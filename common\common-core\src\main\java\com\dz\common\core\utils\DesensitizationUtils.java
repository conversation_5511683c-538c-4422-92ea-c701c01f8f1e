package com.dz.common.core.utils;

/**
 * 数据脱敏工具类
 * @Author: Handy
 * @Date: 2021/9/26 13:48
 */
public class DesensitizationUtils {

    /**
     * 手机号处理
     * @param mobile
     * @return
     */
    public static String mobileSecret(String mobile) {
        if(null == mobile || mobile.length() < 11) {
            return mobile;
        }
        return mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})","$1****$2");
    }

    /**
     * 姓名处理
     * @param name
     * @return
     */
    public static String nameSecret(String name) {
        if(null == name || name.length() < 2) {
            return name;
        }
        String result = name.substring(0,1);
        for (int i = 1; i < name.length(); i++) {
            result += "*";
        }
        return result;
    }

    /**
     * 地址处理
     * @param address
     * @return
     */
    public static String addressSecret(String address) {
        if(null == address || address.length() < 3) {
            return address;
        }
        return address.substring(0,3)+"……";
    }

    public static void main(String[] args) {
    }

}
