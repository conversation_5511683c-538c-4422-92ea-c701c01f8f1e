package com.dz.ms.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.core.dto.user.SysUserDTO;
import com.dz.ms.user.dto.AccountLoginDTO;
import com.dz.ms.user.dto.OmsUserDTO;
import com.dz.ms.user.dto.UpdatePasswordDTO;
import com.dz.ms.user.dto.UserRoleDTO;
import com.dz.ms.user.entity.OmsUser;

/**
 * OMS-系统用户信息接口
 * @author: Handy
 * @date:   2020/01/29 19:15
 */
public interface OmsUserService extends IService<OmsUser> {

    /**
     * 账号密码登录
     * @param param
     * @return
     */
    public String passwordLogin(AccountLoginDTO param);

    /**
     * 获取用户角色ID
     * @param uid
     * @return
     */
    Long getUserRole(Long uid);

    /**
     * 绑定用户角色
     * @param param
     * @param uid
     */
    public void bindRole(UserRoleDTO param, Long uid);

    /**
     * 用户密码确认并发送短信验证码
     * @param param
     * @return
     */
    boolean passwordCheck(AccountLoginDTO param);

    /**
     * 密码加短信验证码登录
     * @param param
     * @return
     */
    OmsUserDTO passwordSmsLogin(AccountLoginDTO param);

    /**
     * 修改密码
     * @param param
     */
    void passwordUpdate(UpdatePasswordDTO param);

}