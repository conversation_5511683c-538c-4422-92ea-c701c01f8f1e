package com.dz.common.core.fegin.basic;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.basic.MpMsgSubscribeUserDTO;
import com.dz.common.core.dto.basic.SubscribeMsgSendApiDTO;
import com.dz.common.core.dto.basic.SubscribeMsgSendDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ServiceConstant.BASIC_SERVICE_NAME, contextId = "MpMsgFeignClient")
public interface MpMsgFeignClient {

    /**
     * 发送小程序订阅消息
     *
     * @param param
     * @param tenantId
     * @return
     */
    @PostMapping(value = "/subscribe_msg/send")
    public Result<Boolean> sendMiniappSubscribeMsg(@RequestBody SubscribeMsgSendDTO param, @RequestParam("tenantId") Long tenantId);

    @PostMapping(value = "/subscribe_msg/send/noCatch")
    public Result<Boolean> sendSubscribeMsgCatch(@RequestBody SubscribeMsgSendDTO param, @RequestParam("tenantId") Long tenantId);

    /**
     * 外部接口发送小程序订阅消息
     *
     * @param param
     * @param tenantId
     * @return
     */
    @PostMapping(value = "/subscribe_msg/send_out")
    public Result<Boolean> sendMiniappSubscribeMsgOut(@RequestBody SubscribeMsgSendApiDTO param, @RequestParam("tenantId") Long tenantId);


    @GetMapping(value = "/subscribe_msg/subscribe_user")
    public Result<List<MpMsgSubscribeUserDTO>> getSubscribeUser(@RequestParam("msgCode") String msgCode);

}
