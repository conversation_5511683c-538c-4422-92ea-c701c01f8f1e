import { defineStore } from "pinia";
import { login as userLogin, getInfoUser, getInfoMenu, logout as logoutUser, strongAuthenticate } from '@/http/index.js'
import { encrypt, decrypt } from '@/utils/jsencrypt'
import { getToken, setToken, removeToken, setCode, removeCode, getCode } from '@/utils/auth'
import router, { customeRoutes } from '../router'
import { nextTick } from 'vue'
import * as myEcharts from 'echarts'
import { filterAsyncRouter } from '@/utils/NavTop'
import { subscribeMsgList } from '@/http/index.js'
// json 后缀不可省略
import customedTheme from './echartTheme.json'
import { set } from "lodash";
myEcharts.registerTheme('customedTheme', customedTheme)
export const useGlobalStore = defineStore('global', {
  state: () => ({
    loading: false, // 全局loading设置
    contentLoading: false, // 内容区域loading 包括搜索区域、按钮区域、列表区域
    token: getToken(),
    userInfo: {},
    name: '',//名字
    avatar: '',

    menus: [],  //菜单信息
    tagList: [], //右侧顶部导航信息
    layout: '',
    permissions: [],// 用户权限
    collapsed: false, // 菜单栏是否收缩
    // echarts 带主题的全局引入
    $myEcharts: myEcharts,
    tenantCode: getCode(),
    subscribeMsgList: [],
    $cdn: 'https://vip-dev.oss-cn-shanghai.aliyuncs.com/muji/muji-mini/pc/',
    LoginSuccessful: false
  }),
  getters: {
    // 获取菜单栏职位
    // getToken() {
    //     return this.token
    // },
  },
  actions: {
    getSubscribeMsgList() {
      if (this.subscribeMsgList.length) {
        return Promise.resolve(this.subscribeMsgList)
      }
      return subscribeMsgList().then(res => {
        this.subscribeMsgList = res.data;
        return res.data
      })
    },
    setLoginSuccessful() {
      this.LoginSuccessful = false
    },
    // 菜单栏是否收缩
    setCollapsed(data) {
      this.collapsed = data
    },
    // 当表单收起展开影响布局时
    setLayout(data) {
      this.layout = data
    },
    // 关闭tag
    closeSelectedTag(info) {
      var flag = this.tagList.findIndex(item => {
        return item.path === info.path;
      })
      // console.log(this.tagList,'删除前')
      this.tagList.splice(flag, 1)
      // console.log(this.tagList,'删除后00000')
    },
    // 设置全局loading
    login(data) {
      // console.log(username, password, uuid, code);

      return new Promise((resolve, reject) => {
        userLogin(data).then(res => {
          // debugger
          if (res.code === 0) {
            this.LoginSuccessful = true
          } else {
            this.LoginSuccessful = false
          }
          // setToken(res.data.token)
          // setCode(res.data.tenantCode)
          // this.tenantCode = res.data.tenantCode

          resolve(res)
        }).catch((err) => {
          this.LoginSuccessful = false
          reject(err)
        })
      })

    },
    setTokenDky(data) {
      return new Promise((resolve, reject) => {

        strongAuthenticate(data).then(res => {
          // debugger

          setToken(res.data.token)
          // setCode(res.data.tenantCode)
          // this.tenantCode = res.data.tenantCode

          resolve(res)
        }).catch((err) => {
          reject(err)
        })
      })
    },
    setLoading(value) {
      this.loading = value
    },
    // 设置内容loading
    setContentLoading(value) {
      this.contentLoading = value
    },
    // 保存token 缓存在localStorage
    setToken(token) {
      localStorage.setItem('token', token)
      this.token = token
    },
    // 保存个人信息
    setUserInfo() {
      return new Promise((resolve, reject) => {
        getInfoUser().then(res => {

          this.name = res.data.username
          // this.avatar = res.data.avatar
          // this.roles = res.data
          this.userInfo = res.data
          this.permissions = res.data.functionCodes

          resolve(res.data)
        }).catch(err => {
          reject(err)
        })
      })
    },
    // 保存省市区数据
    setAreaList(data) {
      this.areaList = data
    },
    // 保存部门列表
    setDeptListt(data) {
      this.deptListt = data
    },
    logout() {//退出
      return new Promise((resolve, reject) => {
        logoutUser().then(res => {
          this.name = ''
          this.avatar = ''
          this.token = ''
          this.userInfo = ''
          this.permissions = []


          // 清空tag
          this.tagList = []

          this.menus = []
          removeToken()
          removeCode()
          localStorage.clear()
          resolve()
          router.push('/login')
        })

        // }).catch((err) => {
        // reject(err)
        // })
      })
    },
    filterTreeAndMarkSecondLevel(tree) {
      // 定义一个递归函数来处理节点
      function processNode(node) {
        // 如果当前节点是隐藏的，则不返回该节点（即过滤掉它）
        if (node.hidden) {
          return null;
        }

        // 过滤子节点并递归处理
        if (node.children) {
          node.children = node.children.filter(child => {
            const processedChild = processNode(child);
            // 如果处理后的子节点不是null（即它不是隐藏的），则返回它
            return processedChild !== null;
          });

          // 在二级节点上添加isShow=true（假设二级是指直接子节点）
          node.children.forEach(child => {
            // 在这里，我们可以认为child就是二级节点，因为我们已经移除了所有隐藏的父节点
            child.isShow = true;
          });
        }

        // 返回处理后的节点
        return node;
      }

      // 对树中的每个根节点进行处理
      return tree.map(rootNode => processNode(rootNode)).filter(node => node !== null);
    },
    // 接口获取生成动态路由和动态菜单
    generateMenus() {
      // this.menus = this.filterTreeAndMarkSecondLevel(filterAsyncRouter(customeRoutes))
      return new Promise((resolve, reject) => {
        getInfoMenu().then(res => {
          console.log("🚀 ~ getInfoMenu ~ res:", res)

          const rewriteRoutes = filterAsyncRouter(res.data)
          rewriteRoutes.push({ path: '/:path(.*)*', redirect: '/404', hidden: true })
          rewriteRoutes.forEach((e) => router.addRoute(e))
          nextTick(() => {
            this.menus = this.filterTreeAndMarkSecondLevel(rewriteRoutes);

          })

          // }
          resolve(rewriteRoutes)
        }).catch(err => {
          reject(err)
        })

      })
    },
    // 路由变化时候添加路由
    addTag(item) {

      if (this.tagList.length == 0) {
        this.tagList.push(item)
      } else {
        // 如果没有存储该路由的话，存起来
        var isHas = this.tagList.findIndex(val => {

          return val.path === item.path;
        })
        if (isHas < 0) {
          this.tagList.push(item)
        }
      }
    },
    // 判断权限
    $hasPermission(value) {

      const permissions = this.permissions
      if (value) {
        const hasPermissions = permissions.includes(value)
        return hasPermissions
      };
      return true
    },
    /**
    * 根据指定的ID值，从选项数组中查找并返回对应的值。
    *
    * @param {any} val - 要查找的ID值。
    * @param {Array} options - 选项数组，每个选项是一个对象。
    * @param {string} key - 在选项对象中用于比较的键名。
    * @param {string} value - 在找到匹配项后，返回该项的指定键对应的值。
    * @returns {string} - 如果找到匹配项，则返回指定键的值；否则返回空字符串。
    */
    $getValueById(val, options, key, value) {
      let result = options.filter(item => {
        return val == item[key]
      })
      if (result.length) {
        return result[0][value]
      }
      return ''
    },

  }
})



