package com.dz.ms.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.core.annotation.CacheEvict;
import com.dz.common.core.annotation.Cacheable;
import com.dz.common.core.constants.CommonConstants;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.user.constants.CacheKeys;
import com.dz.ms.user.dto.OmsPermissionDTO;
import com.dz.ms.user.entity.OmsPermission;
import com.dz.ms.user.mapper.OmsPermissionMapper;
import com.dz.ms.user.service.OmsPermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * OMS-权限功能
 * @author: Handy
 * @date:   2020/01/29 19:15
 */
@Service
public class OmsPermissionServiceImpl extends ServiceImpl<OmsPermissionMapper,OmsPermission> implements OmsPermissionService {

    @Autowired
    private OmsPermissionMapper omsPermissionMapper;

    /**
     * 保存权限功能
     * @param param
     * @return
     */
    @Override
    @Transactional
    @CacheEvict(prefix = CacheKeys.OMS_ALL_PERMIT_CODES)
    public Long saveOmsPermission(OmsPermissionDTO param) {
        OmsPermission getPermission = null;
        if(null != param.getId()) {
            getPermission = omsPermissionMapper.selectById(param.getId());
        }
        OmsPermission omsPermission = new OmsPermission(param.getId(), param.getCode(), param.getParentId(), param.getPermitType(), param.getPermitName(), param.getUrl(), param.getPermitDesc(), param.getDisplaySort(), param.getHasChild());
        if(ParamUtils.isNullOr0Long(omsPermission.getId())) {
            omsPermissionMapper.insert(omsPermission);
        }
        else {
            omsPermissionMapper.updateById(omsPermission);
        }
        long parentId = omsPermission.getParentId();
        if(parentId > 0) {
            omsPermission = new OmsPermission();
            omsPermission.setId(parentId);
            omsPermission.setHasChild(1);
            omsPermissionMapper.updateById(omsPermission);
        }
        if(null != param.getId() && getPermission != null && getPermission.getParentId() != null && !getPermission.getParentId().equals(parentId)) {
            resetParentPermission(getPermission);
        }
        return omsPermission.getId();
    }

    /**
     * 根据ID删除权限功能
     * @param id
     */
    @Override
    @Transactional
    public void deleteOmsPermissionById(Long id) {
        OmsPermission getPermission = omsPermissionMapper.selectById(id);
        if(getPermission.getHasChild().equals(1)) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT,"该权限下有包含子权限请先删除或编辑子权限！");
        }
        omsPermissionMapper.deleteById(id);
        if(!getPermission.getParentId().equals(0L)) {
            resetParentPermission(getPermission);
        }
    }

    /**
     * 将无子权限的hasChild设为0
     * @param getPermission
     */
    private void resetParentPermission(OmsPermission getPermission) {
        LambdaQueryWrapper<OmsPermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OmsPermission :: getParentId,getPermission.getParentId());
        List<OmsPermission> list = omsPermissionMapper.selectList(wrapper);
        if(CollectionUtils.isEmpty(list)) {
            OmsPermission omsPermission = new OmsPermission();
            omsPermission.setId(getPermission.getParentId());
            omsPermission.setHasChild(0);
            omsPermissionMapper.updateById(omsPermission);
        }
    }

    /**
     * 获取所有权限编码
     * @return
     */
    @Override
    @Cacheable(prefix = CacheKeys.OMS_ALL_PERMIT_CODES, expire = CommonConstants.DAY_SECONDS)
    public List<String> getAllPermissionCodes() {
        List<OmsPermission> list = omsPermissionMapper.selectList(new LambdaQueryWrapper<>());
        return list.stream().map(OmsPermission::getCode).collect(Collectors.toList());
    }

    /**
     * 根据权限编号查询权限
     * @param code
     * @return
     */
    @Override
    public OmsPermission getPermissionByCode(String code) {
        LambdaQueryWrapper<OmsPermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OmsPermission :: getCode,code);
        return omsPermissionMapper.selectOne(wrapper);
    }

    /**
     * 获取接口对应权限MAP
     * @return
     */
    @Override
    @Cacheable(prefix = CacheKeys.OMS_PERMIT_URLMAP, expire = CommonConstants.WEEK_SECONDS)
    public Map<String, String> getPermissionUrlMap() {
        List<OmsPermission> list = omsPermissionMapper.getPermissionUrlList();
        Map<String, String> map = new HashMap<>();
        if(null == list) {
            return map;
        }
        for (OmsPermission omsPermission : list) {
            map.put(omsPermission.getUrl(),omsPermission.getCode());
        }
        return map;
    }

}
