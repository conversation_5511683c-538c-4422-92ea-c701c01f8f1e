// 若不加.tab-bar，则在自定义tab-bar中使用的css变量不会生效。
page,
.tab-bar {
  /* 页面左右通用边距 */
  --page-margin: 40rpx;
  /* 线框颜色 */
  --border-black-color: #000;
  /* 主题主色 */
  --primary-color: #7f0019;
  /* 次主题色 */
  --secondary-color: #F4EEDE;
  /* 背景色 */
  --background-color: #EOCEAA;
  --gray-bg: #f5f5f5;
  // --gray-bg: green;
  /* 灰色背景色 */
  --background-gray-color: #f6f6f6;
  /* 文字颜色 */
  --text-white-color: #fff;
  --text-black-color: #3c3c43;
  --text-gray-color: #bbb;
  /* 输入框placeholder颜色 */
  --text-placeholder-color: #888;
  /* 导航文字颜色 */
  --text-nav-color: #000;
  --bg-nav-color: #fff;
  /* radio线框颜色 */
  --radio-border-color: #000;
  /* diglog背景颜色 */
  --dialog-bg: #fff;
  /* list表背景色*/
  --list-bg: #FAFAFA;


  /* 搜索列表里面左上角的活动标识 */
  --card-new-color: #E0CEAA;
  --img-list-card-background-color: #FAFAFA;
  /*按钮禁用颜色*/
  --disabled-button: #bbb;
  --disabled-txt: #fff;
  /* 按钮背景白色 */
  --plain-button-background-color: #fff;
  /* 按钮颜色 */
  --btn-primary: #3c3c3c;
  --btn-gray: #f5f5f5;
  /* 购物车详情线条 */
  --cart-background-color: #fff;
  /* 购物车标题前的渐变色 */
  --cart-font-color: linear-gradient(0deg, #E7CC87 0%, #E4DECD 49%, #ACB4BF 100%);
  --coupon-item-background-color: linear-gradient(90deg, #A4243E 0%, #8A0D25 100%);


  /*//menu 切换颜色背景 */

  --themeBtnColor: #000000;
  /*//按钮的颜色背景*/
  --themeBtnBorderColor: #000000;
  /*//按钮的边框线*/
  --themeTipsColor: linear-gradient(270deg, #CDAB83 0%, #BC915C 100%);
  /*任务中心 按钮悬浮 tips颜色*/
  --themeTipsTextColor: #FFFFFF;
  /*任务中心 按钮悬浮 tips字体颜色*/
  --themeVip1v1Color: linear-gradient(270deg, rgba(195, 155, 107, 0.8) 0%, #BC915C 100%);
  /*任务中心 1v1 详情 侧边栏 会员背景 颜色*/
  --themeVip1v1TextColor: #000;
  /*任务中心 1v1 详情 侧边栏 会员字体 颜色*/
  --themeOneBtnColor: linear-gradient(270deg, rgba(195, 155, 107, 0.8) 0%, #BC915C 100%);
  /*任务中心 1v1 详情 一键发送颜色 颜色*/
  --themeCustmerBtntextLfet: #FFF;
  /*客户列表 转发文字按钮背景 颜色*/
  --themeCustmerBtnBackLeft: linear-gradient(270deg, rgba(195, 155, 107, 0.8) 0%, #BC915C 100%);
  /*客户列表 转发按钮 颜色*/
  --themeCustmerBtntextRight: #FFF;
  /*客户列表 右边转发文字按钮 颜色*/
  --themeCustmerBtnBackRight: #000000;
  /*客户列表 右边转字按钮 背景颜色*/
  --thmemLibraryColor: linear-gradient(270deg, rgba(195, 155, 107, 0.8) 0%, #BC915C 100%);

  /*素材筛选 选中 背景颜色*/
  --thmemGrupBg: linear-gradient(270deg, rgba(195, 155, 107, 0.8) 0%, #BC915C 100%);
  /*侧边栏客户分组一键发送*/
  --thmemStaffbackColor: linear-gradient(225deg, rgba(195, 155, 107, 0.8) 0%, #bc915c 100%);
  /*离职继承接替按钮 选中 背景颜色*/
  --thmemStaffTextColor: #fff;
  /*离职继承接替按钮 字体颜色*/
  --themeInheritBorderColor: #000;
  /*继承接替按钮 边框颜色颜色*/
  --themeInheritTextColor: #000;
  /*立即发送*/
  --themeSendTextColor: #000000;
  /*设置发送内容 取消 边框颜色颜色*/
  --themeSendBorderColor: #BBBBBB;
  /*会员卡片背景色*/
  --themeMemberBg: linear-gradient(270deg, rgba(195, 155, 107, 0.8) 0%, #BC915C 100%);

  /*通用层级*/
  --zIndexFixed: 9999;
  --zIndexFixedAdd1: 10000;
  --zIndexFixedRemove1: 9998;
  //--zIndexFixedAdd1: calc(var(--zIndexFixed) - 1); // css可以生效，但是生成的js里没意义。固采取上述人工计算的方式。
  //--zIndexFixedRemove1: calc(var(--zIndexFixed) - 1); // css可以生效，但是生成的js里没意义。固采取上述人工计算的方式。
  --zIndexMask: 11000;

  /*tabBar高度 - 单位是px*/
  --tabBarHeight: 80;

  --checkboxColor: #3e3d44;
  --homeVipBg: rgba(248, 245, 240, 1);
}
