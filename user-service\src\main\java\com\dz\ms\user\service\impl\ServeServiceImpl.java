package com.dz.ms.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.dto.user.ServeDTO;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;

import com.dz.ms.user.entity.Serve;
import com.dz.ms.user.mapper.ServeMapper;
import com.dz.ms.user.mapper.StoreMapper;
import com.dz.ms.user.service.ServeService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;

/**
 * 
 * @author: yibo
 * @date:   2024/11/19 17:20
 */
@Service
public class ServeServiceImpl extends ServiceImpl<ServeMapper,Serve> implements ServeService {

	@Resource
    private ServeMapper serveMapper;

    @Resource
    private StoreMapper storeMapper;
    @Override
    public List<ServeDTO> getServeList(ServeDTO param) {
        QueryWrapper<Serve> serveQueryWrapper = new QueryWrapper<>();
        if (param.getStatus() != null){
            serveQueryWrapper.eq("status",param.getStatus());
        }
        serveQueryWrapper.orderByAsc("sort");
        List<Serve> list = serveMapper.selectList(serveQueryWrapper);
        return BeanCopierUtils.convertList(list,ServeDTO.class);
    }

    /**
     * 根据ID查询
     * @param id
     * @return ServeDTO
     */
    @Override
    public ServeDTO getServeById(Long id) {
        Serve serve = serveMapper.selectById(id);
        return BeanCopierUtils.convertObject(serve,ServeDTO.class);
    }

    /**
     * 保存
     * @param param
     * @return Long
     */
    @Override
    public Long saveServe(ServeDTO param) {
        CurrentUserDTO currentUser = SecurityContext.getUser();
        Long uid = currentUser.getUid();
        Long tenantId = currentUser.getTenantId();
        Serve serve = new Serve(param.getId(), param.getName(), param.getImage(), param.getSort(), param.getStatus());
        if(ParamUtils.isNullOr0Long(serve.getId())) {
            serve.setTenantId(tenantId);
            serve.setCreator(uid);
            serveMapper.insert(serve);
        }
        else {
            serve.setModifier(uid);
            serveMapper.updateById(serve);
        }
        return serve.getId();
    }

    /**
     * 根据ID删除
     * @param param
     */
    @Override
    public void deleteServeById(IdCodeDTO param) {
        serveMapper.deleteById(param.getId());
    }

    @Override
    public void updateSort(List<ServeDTO> param) {
        CurrentUserDTO currentUser = SecurityContext.getUser();
        Long uid = currentUser.getUid();
        for (ServeDTO serveDTO : param){
            Serve serve = new Serve(serveDTO.getId(), serveDTO.getName(), serveDTO.getImage(), serveDTO.getSort(), serveDTO.getStatus());
            serve.setModifier(uid);
            serveMapper.updateById(serve);
        }
    }

    @Override
    public List<ServeDTO> getAppServeList() {
        QueryWrapper<Serve> serveQueryWrapper = new QueryWrapper<>();
        serveQueryWrapper.eq("status",0);
        serveQueryWrapper.orderByAsc("sort");
        List<Serve> list = serveMapper.selectList(serveQueryWrapper);
        return BeanCopierUtils.convertList(list,ServeDTO.class);
    }

}