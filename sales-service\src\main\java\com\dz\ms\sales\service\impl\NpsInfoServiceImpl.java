package com.dz.ms.sales.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.annotation.CacheEvict;
import com.dz.common.core.annotation.Cacheable;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.IsEnableDTO;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.sales.constants.CacheKeys;
import com.dz.ms.sales.dto.NpsInfoDTO;
import com.dz.ms.sales.dto.NpsQuestionDTO;
import com.dz.ms.sales.dto.NpsQuestionOptionDTO;
import com.dz.ms.sales.entity.NpsInfo;
import com.dz.ms.sales.entity.NpsQuestion;
import com.dz.ms.sales.entity.NpsQuestionOption;
import com.dz.ms.sales.mapper.NpsInfoMapper;
import com.dz.ms.sales.mapper.NpsQuestionMapper;
import com.dz.ms.sales.mapper.NpsQuestionOptionMapper;
import com.dz.ms.sales.service.NpsInfoService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/16
 */
@Service
public class NpsInfoServiceImpl extends ServiceImpl<NpsInfoMapper, NpsInfo> implements NpsInfoService {
    @Resource
    private NpsInfoMapper npsInfoMapper;
    @Resource
    private NpsQuestionMapper npsQuestionMapper;
    @Resource
    private NpsQuestionOptionMapper npsQuestionOptionMapper;

    /**
     * 分页查询NPS问卷信息
     *
     * @param param
     * @return PageInfo<NpsInfoDTO>
     */
    @Override
    public PageInfo<NpsInfoDTO> getNpsInfoList(NpsInfoDTO param) {
        NpsInfo npsInfo = BeanCopierUtils.convertObjectTrim(param, NpsInfo.class);
        npsInfo.setTitle(null);
        npsInfo.setStartTime(null);
        npsInfo.setEndTime(null);
        Date date = new Date();
        LambdaQueryWrapper<NpsInfo> wrapper = new LambdaQueryWrapper<>(npsInfo);
        if (StringUtils.isNotEmpty(param.getTitle())) {
            wrapper.like(NpsInfo::getTitle, param.getTitle());
        }
        if (null != param.getStartTime()) {
            wrapper.le(NpsInfo::getStartTime, param.getStartTime());
        }
        if (null != param.getEndTime()) {
            wrapper.ge(NpsInfo::getEndTime, param.getEndTime());
        }
        int openState = NumberUtils.toInt(param.getOpenState());
        if (openState == 1) {
            wrapper.gt(NpsInfo::getStartTime, date);
        } else if (openState == 2) {
            wrapper.lt(NpsInfo::getStartTime, date);
            wrapper.gt(NpsInfo::getEndTime, date);
        } else if (openState == 3) {
            wrapper.lt(NpsInfo::getEndTime, date);
        }
        wrapper.orderByDesc(NpsInfo::getId);
        IPage<NpsInfo> page = npsInfoMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), wrapper);
        List<NpsInfoDTO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            for (NpsInfo nps : page.getRecords()) {
                NpsInfoDTO npsInfoDTO = BeanCopierUtils.convertObject(nps, NpsInfoDTO.class);
                if (date.before(nps.getStartTime())) {
                    npsInfoDTO.setOpenState("未开始");
                } else if (date.after(nps.getEndTime())) {
                    npsInfoDTO.setOpenState("已结束");
                } else {
                    npsInfoDTO.setOpenState("开放中");
                }
                list.add(npsInfoDTO);
            }
        }
        return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), list);
    }

    /**
     * 根据ID查询NPS问卷信息
     *
     * @param id
     * @return NpsInfoDTO
     */
    @Override
    @Cacheable(prefix = CacheKeys.NPS_INFO, key = "'#id'")
    public NpsInfoDTO getNpsInfoById(Long id) {
        NpsInfo npsInfo = npsInfoMapper.selectById(id);
        NpsInfoDTO npsInfoDTO = BeanCopierUtils.convertObject(npsInfo, NpsInfoDTO.class);
        List<NpsQuestion> questions = npsQuestionMapper.selectList(new LambdaQueryWrapper<NpsQuestion>().eq(NpsQuestion::getNpsId, id).orderByAsc(NpsQuestion::getSort, NpsQuestion::getId));
        List<NpsQuestionDTO> questionDTOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(questions)) {
            List<NpsQuestionOption> questionOptions = npsQuestionOptionMapper.selectList(new LambdaQueryWrapper<NpsQuestionOption>().eq(NpsQuestionOption::getNpsId, id).orderByAsc(NpsQuestionOption::getSort, NpsQuestionOption::getId));
            List<NpsQuestionOptionDTO> questionOptionDTOS = BeanCopierUtils.convertList(questionOptions, NpsQuestionOptionDTO.class);
            Map<Long, List<NpsQuestionOptionDTO>> optionMap = questionOptionDTOS.stream().collect(Collectors.groupingBy(NpsQuestionOptionDTO::getNpsQuestionId));
            for (NpsQuestion question : questions) {
                NpsQuestionDTO npsQuestion = BeanCopierUtils.convertObject(question, NpsQuestionDTO.class);
                npsQuestion.setOptions(optionMap.get(question.getId()));
                questionDTOS.add(npsQuestion);
            }
        }
        npsInfoDTO.setQuestions(questionDTOS);
        return npsInfoDTO;
    }

    /**
     * 保存NPS问卷信息
     *
     * @param param
     * @return Long
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(prefix = CacheKeys.NPS_INFO, key = "'#param.id'")
    public Long saveNpsInfo(NpsInfoDTO param) {
        boolean isAdd = ParamUtils.isNullOr0Long(param.getId());
        NpsInfo npsInfo = new NpsInfo(param.getId(), param.getTitle(), param.getStartTime(), param.getEndTime(), param.getState());
        if (isAdd) {
            npsInfoMapper.insert(npsInfo);
        } else {
            npsInfoMapper.updateById(npsInfo);
        }


        if (!CollectionUtils.isEmpty(param.getQuestions())) {
            List<NpsQuestionOption> addOptions = new ArrayList<>();
            Date now = new Date();
            List<Long> questionIds = new ArrayList<>();
            List<Long> optionIds = new ArrayList<>();
            for (NpsQuestionDTO npsQuestion : param.getQuestions()) {
                NpsQuestion question = BeanCopierUtils.convertObject(npsQuestion, NpsQuestion.class);
                if (ParamUtils.isNullOr0Long(npsQuestion.getId())) {
                    question.setNpsId(npsInfo.getId());
                    npsQuestionMapper.insert(question);
                } else {
                    npsQuestionMapper.updateById(question);
                }
                questionIds.add(question.getId());
                if (CollectionUtils.isEmpty(npsQuestion.getOptions())) {
                    continue;
                }
                for (NpsQuestionOptionDTO npsQuestionOption : npsQuestion.getOptions()) {
                    NpsQuestionOption option = BeanCopierUtils.convertObject(npsQuestionOption, NpsQuestionOption.class);
                    if (ParamUtils.isNullOr0Long(npsQuestionOption.getId())) {
                        option.setNpsId(npsInfo.getId());
                        option.setNpsQuestionId(question.getId());
                        option.setIsDeleted(0);
                        option.setTenantId(SecurityContext.getUser().getTenantId());
                        option.setCreated(now);
                        option.setCreator(SecurityContext.getUser().getUid());
                        option.setModified(now);
                        option.setModifier(SecurityContext.getUser().getUid());
                        addOptions.add(option);
                    } else {
                        optionIds.add(option.getId());
                        npsQuestionOptionMapper.updateById(option);
                    }
                }
            }
            if (questionIds.size() > 0) {
                npsQuestionMapper.delete(new LambdaQueryWrapper<NpsQuestion>().eq(NpsQuestion::getNpsId, npsInfo.getId()).notIn(NpsQuestion::getId, questionIds));
            }
            if (optionIds.size() > 0) {
                npsQuestionOptionMapper.delete(new LambdaQueryWrapper<NpsQuestionOption>().eq(NpsQuestionOption::getNpsId, npsInfo.getId()).notIn(NpsQuestionOption::getId, optionIds));
            }
            if (addOptions.size() > 0) {
                npsQuestionOptionMapper.insertBatchSomeColumn(addOptions);
            }
        }
        return npsInfo.getId();
    }

    /**
     * 根据ID删除NPS问卷信息
     *
     * @param param
     */
    @Override
    @CacheEvict(prefix = CacheKeys.NPS_INFO, key = "'#param.id'")
    public void deleteNpsInfoById(IdCodeDTO param) {
        npsInfoMapper.deleteById(param.getId());
    }

    /**
     * 更新NPS问卷状态
     *
     * @param param
     */
    @Override
    @CacheEvict(prefix = CacheKeys.NPS_INFO, key = "'#param.id'")
    public void updateNpsInfoState(IsEnableDTO param) {
        NpsInfo npsInfo = new NpsInfo();
        npsInfo.setId(param.getId());
        npsInfo.setState(param.getState());
        npsInfoMapper.updateById(npsInfo);
    }

    /**
     * 根据预约ID列表获取关联NPS
     *
     * @param days
     * @param scene
     * @return
     */
    public List<NpsQuestionDTO> getNpsBySignInDays(Integer days, Integer scene) {
        NpsInfo npsInfo = npsInfoMapper.selectOne(new LambdaQueryWrapper<NpsInfo>().eq(NpsInfo::getState, 1).eq(NpsInfo::getScene, scene).eq(NpsInfo::getSceneValue, days).orderByDesc(NpsInfo::getId).last("limit 1"));
        if (ObjectUtils.isEmpty(npsInfo)) {
            return null;
        }

        LambdaQueryWrapper<NpsQuestion> eq = new LambdaQueryWrapper<NpsQuestion>().eq(NpsQuestion::getNpsId, npsInfo.getId());
        eq.orderByAsc(NpsQuestion::getSort);
        eq.orderByDesc(NpsQuestion::getId);
        List<NpsQuestion> npsQuestions = npsQuestionMapper.selectList(eq);
        if (ObjectUtils.isEmpty(npsQuestions)) {
            return null;
        }
        List<NpsQuestionDTO> npsQuestionList = BeanCopierUtils.convertList(npsQuestions, NpsQuestionDTO.class);
        List<Long> questionIds = npsQuestionList.stream().map(NpsQuestionDTO::getId).collect(Collectors.toList());

        npsQuestionOptionMapper.selectList(new LambdaQueryWrapper<NpsQuestionOption>().in(NpsQuestionOption::getNpsQuestionId, questionIds)).forEach(npsQuestionOption -> {
            npsQuestionList.forEach(npsQuestionDTO -> {
                if (npsQuestionDTO.getId().equals(npsQuestionOption.getNpsQuestionId())) {
                    if (ObjectUtils.isEmpty(npsQuestionDTO.getOptions())) {
                        npsQuestionDTO.setOptions(new ArrayList<>());
                    }
                    npsQuestionDTO.getOptions().add(BeanCopierUtils.convertObject(npsQuestionOption, NpsQuestionOptionDTO.class));
                }
            });
        });
        return npsQuestionList;
    }

}