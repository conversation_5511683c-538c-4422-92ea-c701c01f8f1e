package com.dz.ms.sales.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.sales.dto.FittingDTO;
import com.dz.ms.sales.service.FittingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;

@Api(tags="用户试衣照记录表")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class FittingController  {

    @Resource
    private FittingService fittingService;

    /**
     * 分页查询用户试衣照记录表
     * @param param
     * @return result<PageInfo<FittingDTO>>
     */
    @ApiOperation("分页查询用户试衣照记录表")
	@GetMapping(value = "/app/fitting/list")
    public Result<PageInfo<FittingDTO>> getFittingList(@ModelAttribute FittingDTO param) {
        Result<PageInfo<FittingDTO>> result = new Result<>();
		PageInfo<FittingDTO> page = fittingService.getFittingList(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询用户试衣照记录表
     * @param id
     * @return result<FittingDTO>
     */
    @ApiOperation("根据ID查询用户试衣照记录表")
	@GetMapping(value = "/app/fitting/info")
    public Result<FittingDTO> getFittingById(@RequestParam("id") Long id) {
        Result<FittingDTO> result = new Result<>();
        FittingDTO fitting = fittingService.getFittingById(id);
        result.setData(fitting);
        return result;
    }

    /**
     * 新增用户试衣照记录表
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增用户试衣照记录表",type = LogType.OPERATELOG)
    @ApiOperation("新增用户试衣照记录表")
	@PostMapping(value = "/app/fitting/add")
    public Result<Long> addFitting(@RequestBody FittingDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,true);
        Long id = fittingService.saveFitting(param);
        result.setData(id);
        return result;
    }

    /**
    * 更新用户试衣照记录表
    * @param param
    * @return result<Object>
    */
    @SysLog(value = "更新用户试衣照记录表",type = LogType.OPERATELOG)
    @ApiOperation("更新用户试衣照记录表")
    @PostMapping(value = "/fitting/update")
    public Result<Long> updateFitting(@RequestBody FittingDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,false);
        fittingService.saveFitting(param);
        result.setData(param.getId());
        return result;
    }

    /**
    * 验证保存参数
    * @param param
    */
    private void validationSaveParam(FittingDTO param,boolean isAdd) {
        if(isAdd && null != param.getId()) { 
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if(!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
        if(StringUtils.isNotBlank(param.getImgText()) && param.getImgText().length() > 100){
            throw new BusinessException(ErrorCode.BAD_REQUEST, "文字长度不能超过100");
        }
    }

	/**
     * 根据ID删除用户试衣照记录表
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除用户试衣照记录表")
	@PostMapping(value = "/app/fitting/delete")
    public Result<Boolean> deleteFittingById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        fittingService.deleteFittingById(param);
        result.setData(true);
        return result;
    }

}
