package com.dz.ms.product.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 商品货架
 *
 * @author: LiinNs
 * @date: 2024/11/27 15:15
 */
@Getter
@Setter
@NoArgsConstructor
@Table("商品货架")
@TableName(value = "shelf")
@Builder
@AllArgsConstructor
public class Shelf implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR, length = 256, isNull = true, comment = "货架名称", isIndex = true)
    private String name;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = false, comment = "货架上架类型 1永久上架 2时间段")
    private Integer onType;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "上架开始时间")
    private Date onStartTime;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "上架结束时间-下架")
    private Date onEndTime;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, comment = "优先级-值越大显示越优先")
    private Integer priority;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, defaultValue = "0", comment = "是否根据展示 0不限制 1限制")
    private Integer limitShow;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, defaultValue = "0", comment = "人群包ID")
    private Long groupId;
    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = true, comment = "人群包名称")
    private String groupName;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, defaultValue = "1", comment = "货架状态 0禁用 1启用")
    private Integer state;
    @Columns(type = ColumnType.VARCHAR, length = 4000, isNull = true, comment = "库存售罄组件配置")
    private String content;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, defaultValue = "0", comment = "兑礼人数")
    private Integer exchangePeople;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, defaultValue = "0", comment = "兑礼订单数")
    private Integer exchangeOrder;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, defaultValue = "0", comment = "总兑礼件数")
    private Integer exchangeNum;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, defaultValue = "0", comment = "总兑礼积分")
    private Integer exchangePoint;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, defaultValue = "0", comment = "人均兑礼件数")
    private Integer exchangeAvgNum;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "是否删除 0未删除 1已删除")
    @TableLogic
    private Integer isDeleted;

    public Shelf(Long id, String name, Integer onType, Date onStartTime, Date onEndTime, Integer priority, Integer limitShow, Long groupId, String groupName, Integer state, Integer exchangePeople, Integer exchangeOrder, Integer exchangeNum, Integer exchangePoint, Integer exchangeAvgNum) {
        this.id = id;
        this.name = name;
        this.onType = onType;
        this.onStartTime = onStartTime;
        this.onEndTime = onEndTime;
        this.priority = priority;
        this.limitShow = limitShow;
        this.groupId = groupId;
        this.groupName = groupName;
        this.state = state;
        this.exchangePeople = exchangePeople;
        this.exchangeOrder = exchangeOrder;
        this.exchangeNum = exchangeNum;
        this.exchangePoint = exchangePoint;
        this.exchangeAvgNum = exchangeAvgNum;
    }

}
