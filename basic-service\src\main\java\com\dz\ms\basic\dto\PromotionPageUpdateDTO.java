package com.dz.ms.basic.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "重新生成URL")
public class PromotionPageUpdateDTO extends BaseDTO {

    @ApiModelProperty(value = "页面推广ID")
    private Long id;
    @ApiModelProperty(value = "小程序路径")
    private String path;
}
