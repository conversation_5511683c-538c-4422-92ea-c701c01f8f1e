package com.dz.ms.sales.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/16
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "NPS问卷信息")
public class NpsInfoDTO extends BaseDTO {

    @ApiModelProperty(value = "NPS问卷ID")
    private Long id;
    @ApiModelProperty(value = "问卷标题")
    private String title;
    @ApiModelProperty(value = "问卷开始时间")
    private Date startTime;
    @ApiModelProperty(value = "问卷结束时间")
    private Date endTime;
    @ApiModelProperty(value = "问卷触发场景 0其他 1打卡")
    private Integer scene;
    @ApiModelProperty(value = "问卷状态 0停用 1启用")
    private Integer state;
    @ApiModelProperty(value = "完成填写人数")
    private Integer finishNum;
    @ApiModelProperty(value = "问卷开放状态 1未开始 2开放中 3已结束")
    private String openState;
    @ApiModelProperty(value = "问题列表")
    private List<NpsQuestionDTO> questions;

}
