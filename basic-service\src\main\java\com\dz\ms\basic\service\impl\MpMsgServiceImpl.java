package com.dz.ms.basic.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.KeyValueDTO;
import com.dz.common.core.dto.basic.MpMsgSubscribeUserDTO;
import com.dz.common.core.dto.basic.SubscribeMsgSendApiDTO;
import com.dz.common.core.dto.basic.SubscribeMsgSendDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.dto.wechat.mpmsg.SubscribeMsgKeywordDTO;
import com.dz.common.core.dto.wechat.mpmsg.SubscribeMsgTemplateAddDTO;
import com.dz.common.core.dto.wechat.mpmsg.SubscribeMsgTemplateDTO;
import com.dz.common.core.dto.wechat.mpmsg.SubscribeMsgTemplateGetDTO;
import com.dz.common.core.enums.WechatApiEnum;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.user.UserInfoFeginClient;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.service.WechatRequestSevice;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.constants.CacheKeys;
import com.dz.ms.basic.dto.MpMsgDTO;
import com.dz.ms.basic.dto.MpMsgKeywordDTO;
import com.dz.ms.basic.entity.MpMsg;
import com.dz.ms.basic.entity.MpMsgKeyword;
import com.dz.ms.basic.entity.MpMsgSubscribe;
import com.dz.ms.basic.mapper.MpMsgKeywordMapper;
import com.dz.ms.basic.mapper.MpMsgMapper;
import com.dz.ms.basic.mapper.MpMsgSubscribeLogMapper;
import com.dz.ms.basic.mapper.MpMsgSubscribeMapper;
import com.dz.ms.basic.service.MpMsgService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 小程序/公众号模板消息
 * @author: Handy
 * @date:   2023/07/07 14:22
 */
@Service
@Slf4j
public class MpMsgServiceImpl extends ServiceImpl<MpMsgMapper,MpMsg> implements MpMsgService {

	@Resource
    private MpMsgMapper mpMsgMapper;
    @Resource
    private MpMsgKeywordMapper mpMsgKeywordMapper;
    @Resource
    private WechatRequestSevice wechatRequestSevice;
    @Resource
    private RedisService redisService;
    @Resource
    private UserInfoFeginClient userInfoFeginClient;
    @Resource
    private MpMsgSubscribeMapper mpMsgSubscribeMapper;
    @Resource
    private MpMsgSubscribeLogMapper mpMsgSubscribeLogMapper;

    /**
     * 获取小程序订阅消息模板库列表
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public PageInfo<SubscribeMsgTemplateDTO> getSubscribeMsgTemplateList(Integer pageNo, Integer pageSize) {
        Long tenantId = SecurityContext.getUser().getTenantId();
        List<KeyValueDTO> categorys = wechatRequestSevice.requestArray(WechatApiEnum.API_MINAPP_SUBSCRIBE_GET_CATEGORY,tenantId, KeyValueDTO.class,null,null);
        String ids = "";
        for (int i = 0; i < categorys.size(); i++) {
            KeyValueDTO category = categorys.get(i);
            if(i > 0) {
                ids += ",";
            }
            ids += category;
        }
        Integer[] pages = ParamUtils.getPageParam(pageNo,pageSize);
        JSONObject json = wechatRequestSevice.requestParam(WechatApiEnum.API_MINAPP_SUBSCRIBE_GET_PUBTEMPLATETITLES,tenantId, JSONObject.class,ids,pages[0],pages[1]);
        JSONArray array = json.getJSONArray("data");
        List<SubscribeMsgTemplateDTO> list = new ArrayList<>();
        if(null != array) {
            for (int i = 0; i < array.size(); i++) {
                list.add(array.getObject(i,SubscribeMsgTemplateDTO.class));
            }
        }
        return new PageInfo<>(pageNo,pageSize,json.getIntValue("count"),list);
    }

    /**
     * 获取小程序订阅消息模板关键词库
     * @param templateCode
     * @return
     */
    @Override
    public List<SubscribeMsgKeywordDTO> getSubscribeMsgKeyword(String templateCode) {
        Long tenantId = SecurityContext.getUser().getTenantId();
        return wechatRequestSevice.requestArray(WechatApiEnum.API_MINAPP_SUBSCRIBE_GET_PUBTEMPLATEKEYWORDS,tenantId, SubscribeMsgKeywordDTO.class,null,templateCode);
    }

    /**
     * 获取帐号下已添加的模板列表
     * @return
     */
    @Override
    public List<SubscribeMsgTemplateGetDTO> getAddSubscribeMsgTemplateList() {
        Long tenantId = SecurityContext.getUser().getTenantId();
        return wechatRequestSevice.requestArray(WechatApiEnum.API_MINAPP_SUBSCRIBE_GET_TEMPLATE,tenantId, SubscribeMsgTemplateGetDTO.class,null,null);
    }

	/**
     * 分页查询小程序订阅消息模板配置
     * @param param
     * @return PageInfo<MpMsgDTO>
     */
    @Override
    public PageInfo<MpMsgDTO> getSubscribeMsgList(MpMsgDTO param) {
        MpMsg mpMsg = BeanCopierUtils.convertObjectTrim(param,MpMsg.class);
        mpMsg.setMsgType(1);
        IPage<MpMsg> page = mpMsgMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), new QueryWrapper<>(mpMsg));
        return new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),BeanCopierUtils.convertList(page.getRecords(), MpMsgDTO.class));
    }

    @Override
    public List<MpMsgDTO> getSubscribeMsgListAll() {
        List<MpMsg> list = mpMsgMapper.selectList(new LambdaQueryWrapper<MpMsg>().eq(MpMsg::getState,1));
        if (!CollectionUtils.isEmpty(list)) {
            return BeanCopierUtils.convertList(list, MpMsgDTO.class);
        }
        return List.of();
    }

    @Override
    public List<MpMsgSubscribeUserDTO> getSubscribeUser(String msgCode) {
        MpMsgDTO mpMsg = getSubscribeMsgByCode(msgCode);
        List<MpMsgSubscribe> mpMsgSubscribes = mpMsgSubscribeMapper.selectList(new LambdaQueryWrapper<MpMsgSubscribe>().eq(MpMsgSubscribe::getTemplateId, mpMsg.getTemplateId()));
        return BeanCopierUtils.convertList(mpMsgSubscribes, MpMsgSubscribeUserDTO.class);
    }

    /**
     * 保存小程序订阅消息模板配置
     * @param param
     * @return Long
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveSubscribeMsg(MpMsgDTO param) {
        MpMsg mpMsg = new MpMsg(param.getId(), param.getMsgType(), param.getTemplateCode(),param.getTemplateCode(), param.getTemplateId(), param.getTemplateName(), param.getTriggerType(), param.getKeyword(), param.getPagePath(), param.getState());
        mpMsg.setMsgType(1);
        Map<Integer,MpMsgKeyword> keyMap = new HashMap<>();;
        List<Integer> kidList = new ArrayList<>();
        String keywordStr = "";
        for (int i = 0; i < param.getKeywordList().size(); i++) {
            MpMsgKeywordDTO keyword = param.getKeywordList().get(i);
            if(i > 0) {
                keywordStr += ",";
            }
            keywordStr += keyword.getKeyName();
            kidList.add(keyword.getKeyId());
        }
        mpMsg.setKeyword(keywordStr);
        Long tenantId = SecurityContext.getUser().getTenantId();
        boolean isAdd = ParamUtils.isNullOr0Long(mpMsg.getId());
        LambdaQueryWrapper<MpMsg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MpMsg::getTemplateCode,param.getTemplateCode());
        if(!isAdd) {
            queryWrapper.ne(MpMsg::getId,param.getId());
        }
        long count = mpMsgMapper.selectCount(queryWrapper);
        if(isAdd) {
            if(null != param.getCreateToMP() && param.getCreateToMP()) {
                SubscribeMsgTemplateAddDTO template = new SubscribeMsgTemplateAddDTO();
                template.setTid(param.getTemplateCode());
                template.setKidList(kidList);
                String templateId = wechatRequestSevice.request(WechatApiEnum.API_MINAPP_SUBSCRIBE_ADD_TEMPLATE,tenantId,String.class,template);
                mpMsg.setTemplateId(templateId);
            }
            if(count > 0) {
                mpMsg.setMsgCode(param.getTemplateCode()+"-"+count);
            }
            mpMsgMapper.insert(mpMsg);
        }
        else {
            MpMsg getMsg = mpMsgMapper.selectById(param.getId());
            if(!param.getTemplateCode().equals(getMsg.getTemplateCode()) && count > 0) {
                mpMsg.setMsgCode(param.getTemplateCode()+"-"+count);
            }
            mpMsgMapper.updateById(mpMsg);
            List<MpMsgKeyword> keywords = mpMsgKeywordMapper.selectList(new LambdaQueryWrapper<MpMsgKeyword>().eq(MpMsgKeyword::getMsgId,mpMsg.getId()));
            if(!CollectionUtils.isEmpty(keywords)) {
                for (MpMsgKeyword keyword : keywords) {
                    if(!kidList.contains(keyword.getKeyId())) {
                        mpMsgKeywordMapper.deleteById(keyword.getId());
                        continue;
                    }
                    keyMap.put(keyword.getKeyId(),keyword);
                }
                keyMap = keywords.stream().collect(Collectors.toMap(MpMsgKeyword::getKeyId,keyword -> keyword));
            }
            redisService.delAll(CacheKeys.SUBSCRIBE_MSGID_BYSCENE+":"+ tenantId);
        }
        for (MpMsgKeywordDTO keyword : param.getKeywordList()) {
            MpMsgKeyword getKeyword = keyMap.get(keyword.getKeyId());
            MpMsgKeyword mpMsgKeyword = new MpMsgKeyword(null, mpMsg.getId(), keyword.getSort(), keyword.getKeyName(), keyword.getKeyType(), keyword.getKeyId(), keyword.getIsEdit(), keyword.getEditText());
            if(null == getKeyword) {
                mpMsgKeywordMapper.insert(mpMsgKeyword);
            }
            else {
                mpMsgKeyword.setId(getKeyword.getId());
                mpMsgKeywordMapper.updateById(mpMsgKeyword);
            }
        }
        return mpMsg.getId();
    }

    /**
     * 根据ID查询小程序订阅消息模板配置
     * @param id
     * @return MpMsgDTO
     */
    @Override
    public MpMsgDTO getSubscribeMsgById(Long id) {
        MpMsg mpMsg = mpMsgMapper.selectById(id);
        MpMsgDTO mpMsgDTO = BeanCopierUtils.convertObject(mpMsg,MpMsgDTO.class);
        List<MpMsgKeyword> keywords = mpMsgKeywordMapper.selectList(new LambdaQueryWrapper<MpMsgKeyword>().eq(MpMsgKeyword::getMsgId,id).orderByAsc(MpMsgKeyword::getSort));
        mpMsgDTO.setKeywordList(BeanCopierUtils.convertList(keywords,MpMsgKeywordDTO.class));
        return mpMsgDTO;
    }

    /**
     * 根据ID删除小程序订阅消息模板配置
     * @param param
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSubscribeMsgById(IdCodeDTO param) {
        MpMsg mpMsg = mpMsgMapper.selectById(param.getId());
        if(null == mpMsg) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "ID参数错误");
        }
        mpMsgMapper.deleteById(param.getId());
        Long tenantId = SecurityContext.getUser().getTenantId();
        wechatRequestSevice.requestParam(WechatApiEnum.API_MINAPP_SUBSCRIBE_DEL_TEMPLATE,tenantId,null,mpMsg.getTemplateId());
        redisService.delAll(CacheKeys.SUBSCRIBE_MSGID_BYSCENE+":"+ tenantId);
    }

    /**
     * 发送小程序订阅消息不抛异常
     * @param param
     * @param tenantId
     */
    @Override
    public void sendSubscribeMsgCatch(SubscribeMsgSendDTO param, Long tenantId) {
        try {
            sendSubscribeMsg(param,tenantId);
        } catch (Exception e) {
            log.error("发送小程序订阅消息失败",e);
        }
    }

    /**
     * 外部接口发送小程序订阅消息
     * @param param
     * @param tenantId
     */
    public void sendSubscribeMsgOut(SubscribeMsgSendApiDTO param, Long tenantId) {
        sendSubscribeMsg(param.getMsgCode(),null,param.getOpenids(),param.getPageUrl(),null,param.getContent(),tenantId);
    }

    /**
     * 发送小程序订阅消息
     * @param param
     * @param tenantId
     */
    public void sendSubscribeMsg(SubscribeMsgSendDTO param, Long tenantId) {
        sendSubscribeMsg(param.getMsgCode(),param.getUids(),param.getOpenids(),param.getPageUrl(),param.getParam(),param.getContent(),tenantId);
    }

    /**
     * 发送小程序订阅消息
     */
    private void sendSubscribeMsg(String msgCode,List<Long> uids,List<String> openids,String pageUrl,String param,String[] content, Long tenantId) {
        if(CollectionUtils.isEmpty(openids)) {
            if(CollectionUtils.isEmpty(uids)) {
                log.info("发送小程序订阅消息uids:{},openids:{}发送用户为空",uids,openids);
                throw new BusinessException(ErrorCode.BAD_REQUEST,"发送用户不能为空");
            }
            if(uids.size() > 1000) {
                log.info("发送小程序订阅消息uids:{},openids:{}用户数大于1000",uids,openids);
                throw new BusinessException(ErrorCode.BAD_REQUEST,"发送用户数不能大于1000");
            }
        }
        else {
            if(openids.size() > 1000) {
                log.info("发送小程序订阅消息uids:{},openids:{}openid数大于1000",uids,openids);
                throw new BusinessException(ErrorCode.BAD_REQUEST,"发送openid数不能大于1000");
            }
        }
        if(null == content || content.length == 0) {
            log.info("发送小程序订阅消息uids:{},openids:{}模板内容为空",uids,openids);
            throw new BusinessException(ErrorCode.BAD_REQUEST,"模板内容不能为空");
        }
        if(null == msgCode) {
            log.info("发送小程序订阅消息uids:{},openids:{}模板编号为空",uids,openids);
            throw new BusinessException(ErrorCode.BAD_REQUEST,"模板类型不能为空");
        }
        if(null == SecurityContext.getUser() || null == SecurityContext.getUser().getTenantId()) {
            SecurityContext.setUser(new CurrentUserDTO(tenantId));
        }
        MpMsgDTO mpMsg = getSubscribeMsgByCode(msgCode);
        if(null == mpMsg || StringUtils.isBlank(mpMsg.getTemplateId())) {
            log.error("发送小程序订阅消息uids:{},openids:{}获取模板失败",uids,openids);
            throw new BusinessException(ErrorCode.INTERNAL_ERROR,"获取模板失败");
        }
        List<MpMsgKeywordDTO> keywordList = mpMsg.getKeywordList();
        if(null == keywordList || keywordList.size() != content.length) {
            log.error("发送小程序订阅消息uids:{},openids:{}模板内容关键字数量不匹配",uids,openids);
            throw new BusinessException(ErrorCode.INTERNAL_ERROR,"模板内容关键字数量不匹配");
        }
        if(null == openids) {
            Result<List<String>> userResult = userInfoFeginClient.getUserOpenidByIds(uids,tenantId);
            if(null == userResult || CollectionUtils.isEmpty(userResult.getData())) {
                log.error("发送小程序订阅消息uids:{}用户不存在",uids);
                throw new BusinessException(ErrorCode.INVALID_ARGUMENT,"用户不存在");
            }
            openids = userResult.getData();
        }
        Map<String,Object> map = new HashMap<String,Object>();
        for (int i = 0; i < keywordList.size(); i++) {
            MpMsgKeywordDTO keyword = keywordList.get(i);
            String key = keyword.getKeyType()+keyword.getKeyId();
            String value = content[i];
            if(null != keyword.getIsEdit() && keyword.getIsEdit().equals(1) && StringUtils.isNotEmpty(keyword.getEditText())) {
                value = keyword.getEditText();
            }
            if(null == value) {
                value = "";
            }
            if(keyword.getKeyType().equals("thing") && value.length() > 20) {
                value = value.substring(0,19);
            }
            Map<String,String> submap = new HashMap<>();
            submap.put("value",value);
            map.put(key,submap);
        }
        String page = mpMsg.getPagePath();
        if(StringUtils.isBlank(page)) {
            page = pageUrl;
            if(StringUtils.isBlank(page)) {
                page = null;
            }
        }
        else if(StringUtils.isNotBlank(param)) {
            page += (page.contains("?") ? "&" : "?") + param;
        }
        for (String openid : openids) {
            Map<String, Object> data = new HashMap<String, Object>();
            data.put("touser", openid);
            data.put("template_id", mpMsg.getTemplateId());
            if(null != page) {
                data.put("page", page);
            }
            data.put("data", map);
            wechatRequestSevice.request(WechatApiEnum.API_MINAPP_SUBSCRIBE_MESSAGE_SEND,tenantId,null,data);
        }
    }

    /**
     * 根据消息编码获取订阅消息详情
     * @param msgCode
     * @return
     */
    public MpMsgDTO getSubscribeMsgByCode(String msgCode) {
        String key = CacheKeys.SUBSCRIBE_INFO_BYCODE+SecurityContext.getUser().getTenantId()+":"+msgCode;
        Object object = redisService.get(key);
        if(null != object) {
            return (MpMsgDTO) object;
        }
        MpMsg mpMsg = mpMsgMapper.selectOne(new LambdaQueryWrapper<MpMsg>().eq(MpMsg::getMsgCode,msgCode));
        MpMsgDTO mpMsgDTO = BeanCopierUtils.convertObject(mpMsg,MpMsgDTO.class);
        List<MpMsgKeyword> keywords = mpMsgKeywordMapper.selectList(new LambdaQueryWrapper<MpMsgKeyword>().eq(MpMsgKeyword::getMsgId,mpMsg.getId()).orderByAsc(MpMsgKeyword::getSort));
        mpMsgDTO.setKeywordList(BeanCopierUtils.convertList(keywords,MpMsgKeywordDTO.class));
        redisService.set(key,mpMsgDTO);
        return mpMsgDTO;
    }

}