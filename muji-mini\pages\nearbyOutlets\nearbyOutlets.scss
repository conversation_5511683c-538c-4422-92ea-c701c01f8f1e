/* pages/nearbyOutlets/nearbyOutlets.wxss */
.page-content {
  width: 100%;
  position: relative;
  box-sizing: border-box;
  z-index: 0;
  padding-top: 20rpx;
  height: 100vh;
  display: flex;
  flex-direction: column;

  .map-box {
    width: 100%;
    flex: 1;
    position: relative;
    box-sizing: border-box;
    overflow: hidden;
  }

  .list-box {
    padding: 0 40rpx;
    box-sizing: border-box;
    flex: 1;
    position: relative;
    // padding-bottom: env(safe-area-inset-bottom);
    width: 100%;

    .total {
      height: 41rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #888888;
      line-height: 41rpx;
      text-align: justify;
      font-style: normal;
      margin-bottom: 20rpx;
    }


    .list-item {
      margin-top: 30rpx;
    }
  }

}
