package com.dz.ms.product.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 货架商品库存任务
 *
 * @author: LiinNs
 * @date: 2024/11/27 15:13
 */
@Getter
@Setter
@NoArgsConstructor
@Table("货架商品库存任务")
@TableName(value = "product_on_task")
public class ProductOnTask implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "商品任务ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "货架商品ID 因货架可以同时多次添加同一个商品，分开管理库存任务", isIndex = true)
    private Long shelfProductId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "货架ID", isIndex = true)
    private Long shelfId;
    @Columns(type = ColumnType.VARCHAR, length = 256, isNull = true, comment = "货架名称")
    private String shelfName;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "商品ID", isIndex = true)
    private Long productId;
    @Columns(type = ColumnType.VARCHAR, length = 256, isNull = true, comment = "商品名称", isIndex = true)
    private String productName;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, comment = "上架类型 1实时 2单次时间 3周期")
    private Integer onType;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "上架类型为单次时间时时间点")
    private Date onTime;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "上架类型为周期时周期天数")
    private Integer onDays;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, comment = "上架库存数量 正+ 负-")
    private Integer onInventory;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = false, comment = "状态 0待完成 1已完成 2进行中 3待编辑")
    private Integer state;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, defaultValue = "0", comment = "0正常 1删除")
    @TableLogic
    private Integer isDeleted;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, comment = "1货架正常 0货架禁用")
    private Integer shelfState;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    public ProductOnTask(Long id, Long shelfProductId, Long shelfId, String shelfName, Long productId, String productName, Integer onType, Date onTime, Integer onDays, Integer onInventory, Integer state, Integer shelfState) {
        this.id = id;
        this.shelfId = shelfId;
        this.shelfProductId = shelfProductId;
        this.shelfName = shelfName;
        this.productId = productId;
        this.productName = productName;
        this.onType = onType;
        this.onTime = onTime;
        this.onDays = onDays;
        this.onInventory = onInventory;
        this.state = state;
        this.shelfState = shelfState;
    }

}
