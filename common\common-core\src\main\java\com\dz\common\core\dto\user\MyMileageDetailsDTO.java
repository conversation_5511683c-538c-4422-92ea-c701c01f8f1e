package com.dz.common.core.dto.user;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 我的里程详情DTO
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "我的里程详情")
public class MyMileageDetailsDTO extends BaseDTO {

    @ApiModelProperty(value = "获得里程/扣除里程")
    private String pointsNum;
    @ApiModelProperty(value = "入账时间")
    private String obtainTime;
    @ApiModelProperty(value = "获得原因")
    private String obtainDesc;
    @ApiModelProperty(value = "消费时间")
    private String consumptionTime;
    @ApiModelProperty(value = "消费金额")
    private String consumptionAmount;
    @ApiModelProperty(value = "可累计金额")
    private String cumulativeAmount;
    @ApiModelProperty(value = "渠道门店")
    private String channelStore;
    @ApiModelProperty(value = "操作方式 1增加 2减少")
    private Integer changeType;
}
