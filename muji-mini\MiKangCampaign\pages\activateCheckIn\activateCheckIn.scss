/* MiKangCampaign/pages/activateCheckIn/activateCheckIn.wxss */
.page-container {
    // background: #F8F6ED;
    position: relative;

    .uploader {
        .van-uploader__preview-delete {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32rpx;
            height: 32rpx;
            background: #24252899;
            border-radius: 50%;
            top: -5rpx;
            right: -5rpx;
            margin: 15rpx;

            &::after {
                display: none;
            }

            .van-uploader__preview-delete-icon {
                position: relative;
                transform: none;
                font-size: 16rpx;
                line-height: 32rpx;
                text-align: center;
            }
        }

        .uploader-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            //justify-content: center;
            margin-right: 24rpx;
            margin-bottom: 24rpx;
            height: 172rpx;
            width: 172rpx;
            background: #fff;
            box-sizing: border-box;
            border: 2rpx dashed #716552;

            .plus {
                margin-top: 56rpx;
                // margin-bottom: 25rpx;
                width: 60rpx;
                height: 60rpx;
                position: relative;

                image {
                    width: 100%;
                    height: 100%;
                }

                // &:before {
                //   content: "";
                //   position: absolute;
                //   left: 0;
                //   right: 0;
                //   top: 50%;
                //   height: 1rpx;
                //   background: #666;
                // }

                // &:after {
                //   content: "";
                //   position: absolute;
                //   left: 50%;
                //   top: 0;
                //   bottom: 0;
                //   width: 1rpx;
                //   background: #666;
                // }
            }

            .text {
                font-weight: 300;
                font-size: 22rpx;
                color: #3c3c43;
                line-height: 40rpx;
                letter-spacing: 2rpx;
                text-align: center;
                font-style: normal;
                text-transform: none;
            }
        }
    }

    .page-main {
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        z-index: 1;
        width: 100%;
        // height: 100vh;
        overflow-y: auto;
        overflow-x: hidden;
        font-family: MUJIFont2020;

        .page-wrap {
            width: 100%;
            position: relative;

            .page-back {
                width: 100%;
                position: absolute;
                top: 0rpx;
            }

            .activateCheckIn {
                width: 100%;

                .activeRules {
                    height: 1104rpx;
                    // padding-left: 60rpx;
                    // background-color: #FFFFFF;
                    position: relative;
                }

                .bottom {
                    position: relative;

                    .bottom-right {
                        position: absolute;
                        right: 27rpx;
                        top: 90rpx;
                        display: flex;
                        flex-direction: column;
                        align-items: center;

                        .text {
                            width: 35rpx;
                            font-weight: 500;
                            font-size: 32rpx;
                            color: var(--text-black-color);
                            line-height: 43rpx;
                            letter-spacing: 2rpx;
                            text-align: left;
                        }
                    }

                    .upload_file {
                        // padding-top: 40rpx;
                        width: 100%;
                        padding: 0rpx 48rpx;
                        box-sizing: border-box;

                        .title {
                            padding-right: 50rpx;
                            font-family: MUJIFont2020;
                            font-weight: bold;
                            font-size: 28rpx;
                            color: #716552;
                            line-height: 39rpx;
                            text-align: left;
                            padding-bottom: 32rpx;
                            display: block;

                            .text {
                                font-size: 24rpx;
                                font-weight: 400;
                            }

                            &:first-child {
                                padding-top: 0;
                            }
                        }

                        .btn {
                            // margin-bottom: 12rpx;
                        }

                        .avatar-box {
                            .auth-avatar {
                                width: 100rpx;
                                height: 100rpx;
                            }
                        }

                        .type {
                            width: 100%;
                            // height: 219rpx;
                            // white-space: nowrap;
                            box-sizing: border-box;
                            display: flex;
                            flex-wrap: wrap;
                            flex-direction: row;
                            // padding: 0rpx 27rpx;
                            justify-content: space-between;
                            margin-bottom: 8rpx;

                            .type-wrap {
                                display: inline-block;
                                box-sizing: border-box;
                                margin-bottom: 48rpx;
                                margin-right: 30rpx;
                                width: 140rpx;
                                height: 140rpx;
                                line-height: 140rpx;
                                overflow: hidden;
                                color: #bbbbbb;
                                font-weight: 400;
                                position: relative;
                                border-radius: 50%;
                                border: 2rpx solid #bbbbbb;

                                &:nth-child(4n) {
                                    margin-right: 0rpx;
                                }

                                .type-img {
                                    width: 100%;
                                    position: absolute;
                                    top: 0;
                                    left: 0;
                                }

                                .type-item {
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    white-space: pre-wrap;
                                    // height: 100%;
                                    width: 100%;
                                    box-sizing: border-box;
                                    font-family: MUJIFont2020;
                                    font-size: 24rpx;
                                    // line-height: 40rpx;
                                    // letter-spacing: 2rpx;
                                    text-align: center;
                                }
                            }

                            .active {
                                font-weight: 500;
                                color: #756453;
                                border: 2rpx solid #756453;
                            }
                        }

                        .tips {
                            font-family: MUJIFont2020;
                            font-weight: 400;
                            font-size: 25rpx;
                            color: var(--text-black-color);
                            line-height: 45rpx;
                            letter-spacing: 2rpx;
                            text-align: right;
                            padding-right: 49rpx;
                            margin-top: 20rpx;
                        }
                    }
                }
            }
        }
    }

    .check {
        height: 29rpx;
        display: flex;
        align-items: center;
        justify-content: start;
        font-weight: 350;
        font-size: 24rpx;
        color: #3c3c43;
        line-height: 29rpx;
        // text-align: center;
        margin-top: 40rpx;
        margin-bottom: 40rpx;
        padding-left: 48rpx;

        .iconfont {
            font-size: 32rpx;
            color: #3c3c43;
            margin-right: 20rpx;
        }

        .link {
            font-weight: 400;
            font-size: 24rpx;
            color: #3c3c43;
        }

        .radio {
            border-radius: 50%;
            // font-size: 28rpx;
            color: var(--text-black-color);
        }
    }
}

.bottom-box {
    margin-top: 48rpx;
    margin-bottom: env(safe-area-inset-bottom);
    display: flex;
    justify-content: center;
}

.custom-select-action {
    border-radius: 16rpx 16rpx 0 0 !important;
    .van-action-sheet__header {
        font-family: Source Han Sans CN;
        font-weight: 700;
        font-size: 28rpx;
        text-align: center;
        vertical-align: middle;
        color: #3c3c43;
    }
}

.custom-select-action .van-action-sheet__close {
    font-size: 32rpx !important;
    color: #3c3c43;
    font-weight: 300 !important;
}

.select-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 24rpx;
    padding: 40rpx;
}

.select-item {
    width: 206rpx;
    height: 68rpx;
    line-height: 68rpx;
    border-radius: 8rpx;
    background-color: #f7f7f7;
    text-align: center;
}

.button-box {
    padding: 24rpx 40rpx;
}

.select-confirm {
    color: white;
    background-color: #3c3c43;
    border-radius: 8rpx;
    font-weight: 500;
    font-size: 28rpx;
    line-height: 92rpx;
}

.select-confirm-active {
    color: white;
    background-color: #3c3c43;
}

.van-icon-arrow {
    color: #bbbbbb;
}
