<template>
  <layout>
    <template v-slot:header>
      <searchForm :formParams="formParams" @cancel="resetData" @ok="refreshData">
        <a-form-item label="活动名称" name="lotteryName">
          <a-input v-model:value="formParams.lotteryName" placeholder="请输入活动名称" />
        </a-form-item>
      </searchForm>
    </template>
    <template v-slot:topRight>
      <a-button
        :disabled="!$hasPermission('lotteryActivity:add')"
        type="primary"
        @click="addDrawerData.visible = true"
        >新建抽奖活动</a-button
      >
    </template>
    <template v-slot="{ height }">
      <a-table
        :indentSize="20"
        row-key="id"
        :scroll="{ scrollToFirstRowOnChange: true, y: height - 88, x: '100%' }"
        :dataSource="dataSource"
        :columns="tableHeader"
        :pagination="pagination"
        :loading="loading"
        @change="loadData"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === '_showTime'">
            <span v-if="record.showTimeFlag === 2">不限制</span>
            <div v-else>
              <div>{{ record.showStartTime }}</div>
              <div>{{ record.showEndTime }}</div>
            </div>
          </template>
          <template v-else-if="column.dataIndex === '_activeTime'">
            <span v-if="record.validTimeFlag === 2">不限制</span>
            <div v-else>
              <div>{{ record.validStartTime }}</div>
              <div>{{ record.validEndTime }}</div>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'operate'">
            <a-space>
              <a-button
                :disabled="!$hasPermission('lotteryActivity:edit')"
                type="link"
                @click="handleEdit(record)"
                >编辑</a-button
              >
              <a-button
                :disabled="!$hasPermission('lotteryActivity:status')"
                type="link"
                @click="handleStopUsing(record)"
              >
                {{ record.state === 1 ? '停用' : '启用' }}
              </a-button>
              <a-popconfirm
                title="确定删除该抽奖活动？"
                @confirm="handleDelete(record)"
                :disabled="!$hasPermission('lotteryActivity:delete') || record._disableDel"
              >
                <a-button
                  type="link"
                  :disabled="!$hasPermission('lotteryActivity:delete') || record._disableDel"
                  >删除</a-button
                >
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </template>
  </layout>

  <addDraw v-model:visible="addDrawerData" @ok="resetData" />
</template>
<script setup>
import AddDraw from './components/AddDraw.vue'

import { usePagination } from 'vue-request'
import { getLotteryList, enableLottery, delLottery } from '@/http/index.js'
import { message } from 'ant-design-vue'
import { LOTTERY_STATE_OBJ } from './utils/constant'

const formParams = reactive({ lotteryName: '' })

// 分页参数
const pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ['bottomLeft']
  }
})
const total = ref(0)

const tableHeader = [
  { title: '序', dataIndex: '_index', align: 'center', width: 80 },
  {
    title: '抽奖活动名称',
    dataIndex: 'lotteryName',
    width: 200,
    align: 'center',
    ellipsis: true
  },
  { title: '展示时间', dataIndex: '_showTime', align: 'center', width: 165 },
  { title: '活动有效时间', dataIndex: '_activeTime', align: 'center', width: 165 },
  { title: '活动优先级', dataIndex: 'priority', width: 100, align: 'center' },
  { title: '活动奖项数量', dataIndex: 'awardNum', width: 100, align: 'center' },
  { title: '活动奖品数量', dataIndex: 'prizeNum', align: 'center', width: 100 },
  { title: '参与人数', dataIndex: 'joinInNum', align: 'center', width: 100 },
  { title: '参与次数', dataIndex: 'lotteryNum', align: 'center', width: 100 },
  { title: '活动状态', dataIndex: '_lotteryState', align: 'center', width: 100 },
  { title: '启停用状态', dataIndex: '_state', align: 'center', width: 100 },
  { title: '操作', dataIndex: 'operate', align: 'center', width: 200 }
]
function loadData(pag) {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}
function refreshData() {
  run({ pageNum: 1, pageSize: pageSize.value })
}
function resetData() {
  Object.assign(formParams, { lotteryName: '' })
  refreshData()
}

const {
  data: dataSource,
  run,
  loading,
  pageSize,
  current
} = usePagination((param) => getLotteryList({ ...param, ...formParams }), {
  manual: false, // 修改为false,让其自动执行
  pagination: {
    currentKey: 'pageNum', // 通过该值指定接口 当前页数 参数的属性值
    pageSizeKey: 'pageSize', // 通过该值指定接口 每页获取条数 参数的属性值
    totalKey: 'data.data.count' // 指定 data 中 total 属性的路径
  },
  formatResult: (res) => {
    const { count, list } = res.data
    total.value = count
    list.forEach((item, index) => {
      Object.assign(item, {
        _disableDel: item.lotteryState === 1 || item.state === 1,
        _none: '-',
        _index: (current.value - 1) * pageSize.value + 1 + index,
        _state: item.state === 1 ? '启用' : '停用',
        _lotteryState: LOTTERY_STATE_OBJ[item.lotteryState]
      })
    })
    return list
  }
})

// 新建抽奖活动
const addDrawerData = reactive({
  visible: false,
  id: null
})

// 编辑抽奖活动
function handleEdit(record) {
  addDrawerData.visible = true
  addDrawerData.id = record.id
}
// 停用启用活动
async function handleStopUsing(record) {
  const params = { id: record.id, state: record.state === 1 ? 0 : 1 }
  await enableLottery(params)
  message.success(record.state === 1 ? '已停用' : '已启用')
  resetData()
}
// 删除抽奖活动
async function handleDelete(record) {
  const res = await delLottery({ id: record.id })
  message.success(res.msg)
  resetData()
}
</script>
<style scoped lang="scss">
.activeData {
  position: relative;
  display: flex;
  // padding: 20px 0px;
  flex-wrap: wrap;
  justify-content: start;

  .active-item {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    min-width: 175px;

    .label {
      font-size: 14px;
      // padding-bottom: 5px;
      margin-right: 10px;
    }

    .cont {
      display: flex;
      align-items: flex-end;
      margin-right: 30px;

      .value {
        font-size: 28px;
        text-align: right;
      }

      .danwei {
        font-size: 12px;
        padding-bottom: 5px;
      }
    }
  }
}
</style>
