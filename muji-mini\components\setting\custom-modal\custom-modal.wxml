<view>
  <van-popup style="--popup-background-color: transparent;--overlay-background-color: {{info.pageSetting.modalMaskColor}}" lock-scroll closeable="{{false}}" zIndex="1000000" show="{{ show&&info.id }}" position="{{info.pageSetting.modalPos==2?'bottom':'center'}}" close-on-click-overlay="{{info.pageSetting.modalMaskClose}}" safe-area-inset-bottom="{{false}}" bindclose="close">
    <view class="customModal {{info.pageSetting.modalPos==2?'bottom':'center'}}" wx:if="{{info.pageSetting}}">
      <view class="customModal-content {{info.pageSetting.modalPos==2?'bottom':'center'}}" style="--radius:{{info.pageSetting.modalRadius*rpx}}px;width:{{info.pageSetting.pageWidth*rpx}}px;height:{{info.pageSetting.pageHeight*rpx}}px">
        <!-- 背景 -->
        <custom-bg bgSetting="{{info.pageSetting}}" class="bgStyle"></custom-bg>
        <view class="customModal-info">
          <!-- 标题  -->
          <view class="customModal-title" catch:touchmove="touchmove1" wx:if="{{info.pageSetting.modalTitle}}">
            {{info.pageSetting.templateName}}
          </view>
          <!-- 组件 -->
          <view class="customModal-list">
            <scroll-view class="customModal-scroll" scroll-y style="width:100%;height:100%;" catch:touchmove="touchmove1">
              <view id="view"></view>
              <!-- 自定义组件 -->
              <!-- type 1-富文本  2-海报 3-视频 4-轮播 5-悬浮窗 6-滚动区域 7-门店 8-任务 -->
              <block wx:for="{{info.componentSetting}}" wx:key="id">
                <my-rich width="{{info.pageSetting.pageWidth}}" wx:if="{{item.type==1}}" data="{{item}}" id="view{{index}}" bindgoAchor="goAchor" bindgoTop="goTop" bindgoModal="goModal" bindgoShare="goShare"></my-rich>
                <my-poster width="{{info.pageSetting.pageWidth}}" wx:if="{{item.type==2}}" data="{{item}}" id="view{{index}}" bindgoAchor="goAchor" bindgoTop="goTop" bindgoModal="goModal" bindgoShare="goShare"></my-poster>
                <my-video width="{{info.pageSetting.pageWidth}}" wx:if="{{item.type==3}}" data="{{item}}" id="view{{index}}" bindgoAchor="goAchor" bindgoTop="goTop" bindgoModal="goModal" bindgoShare="goShare"></my-video>
                <normal-slider wx:if="{{item.type==4}}" data="{{item}}" id="view{{index}}" bindgoAchor="goAchor" bindgoTop="goTop" bindgoModal="goModal" bindgoShare="goShare"></normal-slider>
                <scroll width="{{info.pageSetting.pageWidth}}" wx:if="{{item.type==6}}" data="{{item}}" id="view{{index}}" bindgoAchor="goAchor" bindgoTop="goTop" bindgoModal="goModal" bindgoShare="goShare"></scroll>
                <custom-text width="{{info.pageSetting.pageWidth}}" wx:if="{{item.type==9}}" data="{{item}}" id="view{{index}}" bindgoAchor="goAchor" bindgoTop="goTop" bindgoModal="goModal" bindgoShare="goShare"></custom-text>
                <my-division width="{{info.pageSetting.pageWidth}}" wx:if="{{item.type==10}}" data="{{item}}" id="view{{index}}" bindgoAchor="goAchor" bindgoTop="goTop" bindgoModal="goModal" bindgoShare="goShare"></my-division>
                <!--
                <store width="{{info.pageSetting.pageWidth}}" wx:if="{{item.type==7}}" data="{{item}}" id="view{{index}}"></store>
            <task width="{{info.pageSetting.pageWidth}}" wx:if="{{item.type==8}}" data="{{item}}" id="view{{index}}"></task>
             -->
              </block>
            </scroll-view>
          </view>
        </view>
      </view>
      <!-- 关闭按钮 -->
      <view class="customModal-close  {{info.pageSetting.modalPos==2?'bottom':'center'}}" catch:touchmove="touchmove1">
        <image class="customModal-closeBox" wx:if="{{info.pageSetting.modalCloseImg}}" src="{{info.pageSetting.modalCloseImg}}" bindtap="close" catch:touchmove="touchmove1" />
        <view class="customModal-closeBox" bindtap="close" wx:else>
          <text class="iconfont icon-a-Turnoff" style="color:#fff;font-size:50rpx;" catch:touchmove="touchmove1" wx:if="{{info.pageSetting.modalPos==1}}"></text>
          <text class="iconfont icon-a-Turnoff" style="color:#bbb;font-size:50rpx;" catch:touchmove="touchmove1" wx:else></text>
        </view>
      </view>
    </view>
  </van-popup>
</view>