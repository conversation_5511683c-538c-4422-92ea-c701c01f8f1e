<template>
  <a-form-item :label="props.label" :name="props.name" class="BaseDateTimeRange">
    <a-range-picker v-model:value="thisFields.value" @change="thisMethods.change" valueFormat="YYYY-MM-DD HH:mm:ss" :show-time="{ defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')] }" v-bind="$attrs" />
  </a-form-item>
</template>

<script setup>
import dayjs from 'dayjs'
import { onMounted, reactive, watch } from 'vue'

const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  name: {
    type: String,
    default: ''
  },
  modelValue: {
    type: Array,
    default: () => []
  }
})
const emits = defineEmits(['update:modelValue', 'change'])

const thisFields = reactive({
  value: []
})
const thisMethods = {
  setValue() {
    if (props.modelValue.length === 2) {
      thisFields.value = [props.modelValue[0], props.modelValue[1]]
    } else {
      thisFields.value = []
    }
  },
  change(e) {
    // console.log('e：', e)
    if (e === null) e = []
    emits('update:modelValue', e)
    emits('change', e)
  }
}

onMounted(() => thisMethods.setValue())
watch(() => props.modelValue, () => thisMethods.setValue())
</script>

<style scoped lang="scss">
.BaseDateTimeRange {
}
</style>
