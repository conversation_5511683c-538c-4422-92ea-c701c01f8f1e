package com.dz.common.core.fegin.product;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;


@FeignClient(name = ServiceConstant.PRODUCT_SERVICE_NAME, contextId = "ProductOnTaskFeignClient")
public interface ProductOnTaskFeignClient {

    /**
     * 执行所有可用货架上下架任务
     *
     * @return
     */
    @PostMapping(value = "/product_on_task/execute_product_on_task_of_all_shelf")
    Result<Void> executeProductOnTaskOfAllShelf();

}

