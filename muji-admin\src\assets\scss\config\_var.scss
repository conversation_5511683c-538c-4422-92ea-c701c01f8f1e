$layoutBorderRadius: 8px; // 是布局使用的边角

$colorPrimary: #6a6bbf; // 主题色

$theme-white: #F6F5FD;
$primary-color: #6a6bbe; //主按键用色
$theme-special-color: #faa968; //部分特殊组件用色
$theme-background-color: #FAFAFB; //背景颜色 一级页面底层背景颜色
$theme-background-panel: #FFFFFF; //面板底色
$table-background-indirect: #F6F5FD; //面板组件用色，列表间接色
$table-background-hover: #F0EFF9; //列表间接hover状态的颜色
$forbidden-color: #D6D6D6; //按键不可用的颜色
// 下面是tab 导航切换的颜色
$theme-nav-line-color: #D8D8D8;
$theme-login-bg-color: linear-gradient(136deg, #F6F5FD 0%, #EBEAF9 100%);

$borderColor: #e2e2e2 !default;
