package com.dz.ms.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.Date;

/**

 */
@Data
@Table("sftp订单商品")
@TableName(value = "ods_order_product")
public class OdsOrderProduct implements Serializable {

    private static final long serialVersionUID = 1L;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "主键id")
    @TableId
    private String id;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "会员编号")
    private String memberCode;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "店铺编号")
    private String shopIdStr;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "店铺名称")
    private String shopNameStr;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "订单编号")
    private String saleIdStr;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "销售类型")
    private String saleTyStr;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "交易日期")
    private String checkoutDtStr;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "POS编号")
    private String posIdStr;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "收银员编号")
    private String staffIdStr;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "原销售编号")
    private String saleidFrStr;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "总数量")
    private String totalQtyStr;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "实收金额")
    private String totalAmtStr;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "商品编号")
    private String itemIdStr;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "商品名称")
    private String itemNameStr;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "商品数量")
    private String qtyStr;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "对应商品行")
    private String upSerialNoStr;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "商品金额")
    private String saleAmtStr;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "实收金额（可能重复，需确认是否两个字段意义不同）")
    private String realAmtStr;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "自选套餐")
    private String promname1Str;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "几件几折")
    private String promname2Str;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "通用套餐")
    private String promname5Str;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "满减满赠")
    private String promdisnameStr;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "用卷")
    private String couponnameStr;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    private Date createTime;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "分区日期")
    private String bizDate;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "租户id")
    private Long tenantId = 1L;
}
