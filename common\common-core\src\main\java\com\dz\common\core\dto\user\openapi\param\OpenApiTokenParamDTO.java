package com.dz.common.core.dto.user.openapi.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * openapi入参数
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "openApi获取token入参")
public class OpenApiTokenParamDTO {
    
    @ApiModelProperty(value = "appId")
    @NotBlank(message = "appId不可为空")
    private String appId;
    @ApiModelProperty(value = "appSecret")
    @NotBlank(message = "appSecret不可为空")
    private String appSecret;

}
