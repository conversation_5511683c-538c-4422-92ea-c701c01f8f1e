package com.dz.ms.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.ms.basic.dto.PromotionChannelDTO;
import com.dz.ms.basic.dto.PromotionPageDTO;
import com.dz.ms.basic.entity.PromotionChannel;
import com.dz.ms.basic.entity.PromotionPage;

public interface PromotionChannelService  extends IService<PromotionChannel> {

    Long savePromotionChannel(PromotionChannelDTO param);

    void deletePromotionChannel(PromotionChannelDTO param);
}
