package com.dz.ms.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 会员权益
 * @author: Handy
 * @date:   2023/08/07 17:44
 */
@Getter
@Setter
@NoArgsConstructor
@Table("会员权益")
@TableName(value = "benefit_info")
public class BenefitInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "权益ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR,length = 64,isNull = true,comment = "权益名称")
    private String benefitName;
    @Columns(type = ColumnType.VARCHAR,length = 256,isNull = true,comment = "权益背景图")
    private String benefitImg;
    @Columns(type = ColumnType.VARCHAR,length = 256,isNull = true,comment = "未激活样式图片")
    private String unActivateImg;
    @Columns(type = ColumnType.VARCHAR,length = 256,isNull = true,comment = "激活样式图片")
    private String activateImg;
    @Columns(type = ColumnType.VARCHAR,length = 256,isNull = true,comment = "弹窗图片")
    private String popupImg;
    @Columns(type = ColumnType.VARCHAR,length = 1024,isNull = true,comment = "权益简介")
    private String details;
    @Columns(type = ColumnType.INT,length = 10,isNull = true,defaultValue = "0",comment = "权益排序")
    private Integer sort;
    @Columns(type = ColumnType.TEXT,length = 0,isNull = true,comment = "跳转链接配置JSON")
    private String jumpLink;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = true,defaultValue = "1",comment = "权益状态 0停用 1启用")
    private Integer state;
    private String code;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = true,comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = false,defaultValue = "0",comment = "是否删除 0未删除 1已删除")
    @TableLogic
    private Integer isDeleted;

    public BenefitInfo(Long id, String benefitName, String benefitImg, String unActivateImg, String popupImg, String details, Integer sort, String jumpLink, Integer state) {
        this.id = id;
        this.benefitName = benefitName;
        this.benefitImg = benefitImg;
        this.unActivateImg = unActivateImg;
        this.popupImg = popupImg;
        this.details = details;
        this.sort = sort;
        this.jumpLink = jumpLink;
        this.state = state;
    }
}
