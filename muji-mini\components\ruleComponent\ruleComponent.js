// components/ruleComponent/ruleComponent.js
const app = getApp()
Component({

	/**
	 * 组件的属性列表
	 */
	properties: {
		height: {
			type: String,
			value: 82
		},
		ruleDesc: {
			type: String,
			value: ``,
			async observer() {
				await this.setData({
					rpx: app.globalData.rpx
				})
				await this.init()
			}
		}
	},

	/**
	 * 组件的初始数据
	 */
	data: {
		isExpanded: false, // 控制展开收起的状态,默认收起
		scroll: false, // 是否超出高度了
		refresh: false,
		rpx: 0,
		textHeight: 0 // 文本实际高度
	},

	/**
	 * 组件的方法列表
	 */
	methods: {
		// 切换展开收起状态
		toggleContent: app.debounce(async function (e) {
			this.setData({
				isExpanded: !this.data.isExpanded
			})
		}, 300),

		async init() {
			let { height, rpx } = this.data;
			// 等待视图更新完成
			await new Promise(resolve => setTimeout(resolve, 50));

			try {
				// 获取文本内容高度
				const textRes = await this.getStyle('rule-list');
				console.log(textRes, '------------------')
				const textHeight = textRes.height;
				console.log(textHeight, textHeight > height * rpx, height * rpx);
				await this.setData({
					textHeight,
					// 如果文本高度超过限制高度,则显示展开按钮
					scroll: textHeight > height * rpx,
				});
				let time = setTimeout(() => {
					clearTimeout(time)
					this.setData({ refresh: true })
				}, 2000);
			} catch (err) {
				console.error('获取文本高度失败:', err);
				this.setData({
					scroll: false
				});
			}
		},

		// 获取指定元素的样式
		getStyle(id) {
			return new Promise((resolve, reject) => {
				const query = wx.createSelectorQuery().in(this);
				query.select('#' + id).fields({
					size: true
				}, function (res) {
					if (res) {
						resolve(res);
					} else {
						reject(new Error('获取元素失败'));
					}
				}).exec();
			});
		}
	}
})