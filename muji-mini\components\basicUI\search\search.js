const app = getApp()

Component({
  properties: {
    clearable: {
      type: Boolean,
      value: false,
    },
    showAction: {
      type: Boolean,
      value: false,
    },
    placeholder: {
      type: String,
      value: '',
    },
    focus: {
      type: Boolean,
      value: false,
    }
  },
  data: {
    showClear: false,
    value: ''
  },
  methods: {
    onInput(event) {
      const {
        value = ''
      } = event.detail || {}
      this.triggerEvent('input', value)
      this.triggerEvent('change', value)
      this.setData({
        value: value,
        showClear: this.getShowClear(value)
      })
    },
    onChange(event) {
      this.setData({
        value: event.detail
      })
      this.triggerEvent('change', event.detail)
    },

    onCancel() {
      this.triggerEvent('cancel')
      this.triggerEvent('change', '')
    },

    onSearch() {
      this.triggerEvent('search', this.data.value)
    },

    onFocus() {
      this.triggerEvent('focus')
      this.focused = true
      this.setData({
        showClear: this.getShowClear()
      })
    },

    onBlur() {
      this.focused = false
      this.triggerEvent('blur')
      this.setData({
        showClear: this.getShowClear()
      })
    },

    getShowClear(value) {
      const valueBoolean = value === undefined ? this.data.value : value
      return this.data.clearable && valueBoolean
    },

    onClear() {
      this.setData({
        value: '',
        showClear: this.getShowClear('')
      })
      this.triggerEvent('input', '')
      this.triggerEvent('change', '')
      this.triggerEvent('clear', '')
      this.triggerEvent('confirm', '')
    },

    onConfirm: app.debounce(function () {
      this.triggerEvent('confirm', this.data.value)
    })
  }
})
