<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.order.mapper.ExchangeOrderMapper">

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
        id,
  	    order_code,
  	    order_type,
  	    shelf_id,
  	    shelf_name,
  	    campaign_id,
  	    campaign_name,
  	    product_point,
  	    product_amount,
  	    order_point,
  	    order_amount,
  	    number,
  	    user_id,
  	    user_phone,
  	    user_crm_code,
  	    user_card_level,
  	    receiver_id,
  	    receiver_name,
  	    receiver_phone,
  	    receiver_address,
  	    receiver_postcode,
  	    express_amount,
  	    order_status,
  	    pay_status,
  	    pay_type,
  	    pay_time,
  	    pay_code,
  	    trade_id,
  	    express_status,
  	    express_id,
  	    express_code,
  	    form_id,
  	    remark,
  	    finish_time,
  	    sync_status,
  	    is_deleted,
  	    tenant_id,
  	    created,
  	    modified
    </sql>

	<!-- 查询示例 -->
	<select id="selectDemo" parameterType="java.lang.Long" resultType="com.dz.ms.order.entity.ExchangeOrder">
		select
		<include refid="Base_Column_List"/>
		from exchange_order
		where id = #{id,jdbcType=BIGINT}
		AND tenant_id = #{tenantId}
	</select>

	<select id="selectPageByParam" resultType="com.dz.ms.order.dto.CrmExchangeOrderListDTO">
		select
		id,
		order_code,
		user_id,
		user_crm_code,
		created,
		order_status,
		express_status,
		number,
		order_point
		from exchange_order
		<where>
			<if test="param.orderCode != null and param.orderCode != ''">
				and order_code = #{param.orderCode}
			</if>
			<if test="param.userCrmCode != null and param.userCrmCode !=''">
				and user_crm_code = #{param.userCrmCode}
			</if>
			<if test="param.userName != null and param.userName != ''">
				<choose>
					<when test="param.userIdList != null and param.userIdList.size > 0">
						and user_id in
						<foreach collection="param.userIdList" item="item" open="(" close=")" separator=",">
							#{item}
						</foreach>
					</when>
					<otherwise>
						and user_id = -1
					</otherwise>
				</choose>
			</if>
			<if test="param.createdStart != null">
				and created >= #{param.createdStart}
			</if>
			<if test="param.createdEnd != null">
				and created &lt;= #{param.createdEnd}
			</if>
			<if test="param.orderStatus != null">
				<choose>
					<when test="param.orderStatus == 4">
						and order_status in (4,8)
					</when>
					<otherwise>
						and order_status = #{param.orderStatus}
					</otherwise>
				</choose>
			</if>
			<if test="param.expressStatus != null">
				and express_status = #{param.expressStatus}
			</if>
		</where>
		order by id desc
	</select>

	<select id="selectListByParam" resultType="com.dz.ms.order.dto.CrmExchangeOrderListDTO">
		select
		id,
		order_code,
		user_id,
		user_crm_code,
		created,
		order_status,
		express_status,
		number,
		order_point
		from exchange_order
		<where>
			<if test="param.orderCode != null and param.orderCode != ''">
				and order_code = #{param.orderCode}
			</if>
			<if test="param.userCrmCode != null and param.userCrmCode !=''">
				and user_crm_code = #{param.userCrmCode}
			</if>
			<if test="param.userName != null and param.userName != ''">
				<choose>
					<when test="param.userIdList != null and param.userIdList.size > 0">
						and user_id in
						<foreach collection="param.userIdList" item="item" open="(" close=")" separator=",">
							#{item}
						</foreach>
					</when>
					<otherwise>
						and user_id = -1
					</otherwise>
				</choose>
			</if>
			<if test="param.createdStart != null">
				and created >= #{param.createdStart}
			</if>
			<if test="param.createdEnd != null">
				and created &lt;= #{param.createdEnd}
			</if>
			<if test="param.orderStatus != null">
				<choose>
					<when test="param.orderStatus == 4">
						and order_status in (4,8)
					</when>
					<otherwise>
						and order_status = #{param.orderStatus}
					</otherwise>
				</choose>
			</if>
			<if test="param.expressStatus != null">
				and express_status = #{param.expressStatus}
			</if>
		</where>
		order by id desc
	</select>

	<select id="listApp" resultType="com.dz.common.core.dto.order.OrderListAppDTO">
		select
		id,
		order_code,
		order_status,
		order_point,
		order_amount,
		number,
		order_status,
		created
		from exchange_order
		<where>
			<if test="param.orderCode != null">
				and order_code = #{param.orderCode}
			</if>
			<if test="param.orderStatus != null">
				<!--<choose>
					<when test="param.orderStatus == 4">
						and order_status in (4,8)
					</when>
					<otherwise>-->
						and order_status = #{param.orderStatus}
					<!--</otherwise>
				</choose>-->
			</if>
			<if test="param.userId != null">
				and user_id = #{param.userId}
			</if>
		</where>
		order by id desc
	</select>

	<select id="selectExchangePeople" resultType="java.lang.Integer">
		select count(distinct user_id)
		from exchange_order
		where shelf_id = #{shelfId}
	</select>

</mapper>
