//package com.dz.gateway.swagger;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//import reactor.core.publisher.Mono;
//
//import java.util.*;
//
//@RestController
//@RequestMapping("/swagger-resources")
//public class SwaggerHandler {
////    @Autowired(required = false)
////    private SecurityConfiguration securityConfiguration;
////    @Autowired(required = false)
////    private UiConfiguration uiConfiguration;
////    private final SwaggerResourcesProvider swaggerResources;
////
////    @Autowired
////    public SwaggerHandler(SwaggerResourcesProvider swaggerResources) {
////        this.swaggerResources = swaggerResources;
////    }
////
////
////    @GetMapping("/configuration/security")
////    public Mono<ResponseEntity<SecurityConfiguration>> securityConfiguration() {
////        return Mono.just(new ResponseEntity<>(
////                Optional.ofNullable(securityConfiguration).orElse(SecurityConfigurationBuilder.builder().build()), HttpStatus.OK));
////    }
////
////    @GetMapping("/configuration/ui")
////    public Mono<ResponseEntity<UiConfiguration>> uiConfiguration() {
////        return Mono.just(new ResponseEntity<>(
////                Optional.ofNullable(uiConfiguration).orElse(UiConfigurationBuilder.builder().build()), HttpStatus.OK));
////    }
////
////    @GetMapping("")
////    public Mono<ResponseEntity> swaggerResources() {
////        return Mono.just((new ResponseEntity<>(swaggerResources.get(), HttpStatus.OK)));
////    }
//
//    @GetMapping("")
//    public ResponseEntity<List<Map<String, String>>> swaggerResources() {
//        List<Map<String, String>> resources = new ArrayList<>();
//        Map<String, String> resource = new HashMap<>();
//        resource.put("name", "default");
//        resource.put("url", "/v3/api-docs");
//        resource.put("swaggerVersion", "3.0");
//        resources.add(resource);
//        return ResponseEntity.ok(resources);
//    }
//}