package com.dz.ms.order.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 预约记录
 * @author: Handy
 * @date:   2024/05/31 16:36
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@Table("预约记录")
@TableName(value = "booking_record")
public class BookingRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR,length = 64,isNull = false,comment = "预约记录编码",isIndex = true)
    private String recordCode;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "预约客户ID",isIndex = true)
    private Long uid;
    @Columns(type = ColumnType.VARCHAR,length = 64,isNull = true,comment = "预约客户姓名")
    private String userName;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = true,defaultValue = "0",comment = "预约客户性别(0未知 1男 2女)")
    private Integer gender;
    @Columns(type = ColumnType.VARCHAR,length = 36,isNull = true,comment = "预约客户手机号")
    private String mobile;
    @Columns(type = ColumnType.VARCHAR,length = 20,isNull = true,comment = "预约客户等级")
    private String cardLevel;
    @Columns(type = ColumnType.BIGINT, length = 11, isNull = false, comment = "预约ID",isIndex = true)
    private Long bookingId;
    @Columns(type = ColumnType.VARCHAR,length = 64,isNull = false,comment = "预约名称")
    private String bookingName;
    @Columns(type = ColumnType.VARCHAR,length = 128,isNull = false,comment = "预约活动主图")
    private String bookingImg;
    @Columns(type = ColumnType.VARCHAR,length = 24,isNull = true,comment = "预约日期(格式yyyy-MM-dd)",isIndex = true)
    private String bookingDate;
    @Columns(type = ColumnType.VARCHAR,length = 32,isNull = true,comment = "预约时间段")
    private String timeSlot;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = true,comment = "预约时间段开始时间",isIndex = true)
    private Date bookingTimeStart;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = true,comment = "预约时间段结束时间")
    private Date bookingTimeEnd;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = true,comment = "核销有效期开始时间")
    private Date verifyTimeStart;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = true,comment = "核销有效期结束时间")
    private Date verifyTimeEnd;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = false, comment = "预约项目类型 2服务 3卡券")
    private Integer itemType;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "预约客户ID",isIndex = true)
    private Long itemId;
    @Columns(type = ColumnType.VARCHAR,length = 64,isNull = true,comment = "预约项目名称")
    private String itemName;
    @Columns(type = ColumnType.VARCHAR,length = 255,isNull = true,comment = "预约项目描述")
    private String itemIntroduction;
    @Columns(type = ColumnType.VARCHAR,length = 36,isNull = false,comment = "预约门店编码")
    private String storeCode;
    @Columns(type = ColumnType.VARCHAR,length = 36,isNull = false,comment = "预约门店名称")
    private String storeName;
    @Columns(type = ColumnType.VARCHAR,length = 36,isNull = true,comment = "预约/分配员工编号")
    private String empCode;
    @Columns(type = ColumnType.VARCHAR,length = 36,isNull = true,comment = "核销门店编码")
    private String verifyStoreCode;
    @Columns(type = ColumnType.VARCHAR,length = 36,isNull = true,comment = "核销门店名称")
    private String verifyStoreName;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, comment = "核销人类型 1后台用户 2导购")
    private Integer verifierType;
    @Columns(type = ColumnType.VARCHAR,length = 36,isNull = true,comment = "核销员工编号")
    private String verifierCode;
    @Columns(type = ColumnType.VARCHAR,length = 50,isNull = true,comment = "核销员工姓名")
    private String verifierName;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = false,defaultValue = "0",comment = "预约状态 1、待核销,2、已核销,3、已过期,4、已取消")
    private Integer state;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = false,defaultValue = "0",comment = "消息发送状态 0预约成功 1预约修改 2预约开始前 3预约取消 4预约过期")
    private Integer msgState;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "状态变更时间",isIndex = true)
    private Date stateTime;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, comment = "已修改次数", defaultValue = "0")
    private Integer updateNum;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = false, comment = "是否代客预约 0否  1是", defaultValue = "0")
    private Integer empBooking;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = true, comment = "预约消耗类型 0无消耗 1积分 2卡券 3权益 4pin_code 5付费",defaultValue = "0")
    private Integer consumeType;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = true,comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    public BookingRecord(Long id, String recordCode, Long uid, String userName, Integer gender, String mobile, String cardLevel, Long bookingId, String bookingName, String bookingImg, String bookingDate, String timeSlot, Date bookingTimeStart, Date bookingTimeEnd, Date verifyTimeStart, Date verifyTimeEnd, Integer itemType, Long itemId, String itemName, String itemIntroduction, String storeCode, String storeName, String empCode, String verifyStoreCode, String verifyStoreName, Integer verifierType, String verifierCode, String verifierName, Integer state, Integer msgState, Date stateTime, Integer updateNum, Integer empBooking, Integer consumeType) {
        this.id = id;
        this.recordCode = recordCode;
        this.uid = uid;
        this.userName = userName;
        this.gender = gender;
        this.mobile = mobile;
        this.cardLevel = cardLevel;
        this.bookingId = bookingId;
        this.bookingName = bookingName;
        this.bookingImg = bookingImg;
        this.bookingDate = bookingDate;
        this.timeSlot = timeSlot;
        this.bookingTimeStart = bookingTimeStart;
        this.bookingTimeEnd = bookingTimeEnd;
        this.verifyTimeStart = verifyTimeStart;
        this.verifyTimeEnd = verifyTimeEnd;
        this.itemType = itemType;
        this.itemId = itemId;
        this.itemName = itemName;
        this.itemIntroduction = itemIntroduction;
        this.storeCode = storeCode;
        this.storeName = storeName;
        this.empCode = empCode;
        this.verifyStoreCode = verifyStoreCode;
        this.verifyStoreName = verifyStoreName;
        this.verifierType = verifierType;
        this.verifierCode = verifierCode;
        this.verifierName = verifierName;
        this.state = state;
        this.msgState = msgState;
        this.stateTime = stateTime;
        this.updateNum = updateNum;
        this.empBooking = empBooking;
        this.consumeType = consumeType;
    }

}
