package com.dz.ms.sales.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.core.annotation.Cacheable;
import com.dz.common.core.annotation.Lock;
import com.dz.common.core.aop.RateLimitAspect;
import com.dz.common.core.constants.CommonConstants;
import com.dz.common.core.dto.basic.MpMsgSubscribeUserDTO;
import com.dz.common.core.dto.basic.SubscribeMsgSendDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.dto.user.UserSimpleDTO;
import com.dz.common.core.dto.user.crm.MuJiMemberOrder;
import com.dz.common.core.enums.SubscribeMsgEnum;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.basic.MpMsgFeignClient;
import com.dz.common.core.fegin.user.MUJIOpenApiFeignClient;
import com.dz.common.core.fegin.user.UserInfoFeginClient;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.DateUtils;
import com.dz.common.core.utils.ThreadPoolUtils;
import com.dz.ms.sales.constants.CacheKeys;
import com.dz.ms.sales.constants.CampaignCodeEnum;
import com.dz.ms.sales.dto.CampaignBuyerDTO;
import com.dz.ms.sales.dto.CampaignDTO;
import com.dz.ms.sales.dto.CampaignSignInConfigDTO;
import com.dz.ms.sales.entity.*;
import com.dz.ms.sales.mapper.*;
import com.dz.ms.sales.service.CampaignService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/30
 */
@Service
@Slf4j
public class CampaignServiceImpl extends ServiceImpl<CampaignMapper, Campaign> implements CampaignService {
    @Resource
    private CampaignMapper campaignMapper;
    @Resource
    private CampaignUserMapper campaignUserMapper;
    @Resource
    private CampaignBuyerConfigMapper campaignBuyerConfigMapper;
    @Resource
    private CampaignSignInConfigMapper campaignSignInConfigMapper;
    @Resource
    private UserInfoFeginClient userInfoFeginClient;
    @Resource
    private MUJIOpenApiFeignClient mujiOpenApiFeignClient;
    @Resource
    private RedisService redisService;
    @Resource
    private CampaignEnrollMapper campaignEnrollMapper;
    @Resource
    private MpMsgFeignClient mpMsgFeignClient;
    @Resource
    private SignInUserMapper signInUserMapper;
    @Resource
    private SignInUserDetailMapper signInUserDetailMapper;
    @Resource
    private UserLotterySummaryMapper userLotterySummaryMapper;
    @Resource
    private UserLotteryPrizesMapper userLotteryPrizesMapper;
    @Resource
    private CampaignBuyerMapper campaignBuyerMapper;


    @Override
    public Boolean editCampaign(CampaignDTO param) {

        Campaign oldCampaign = campaignMapper.selectById(param.getId());

        Campaign campaign = new Campaign();
        campaign.setId(param.getId());
        SecurityContext.setUser(new CurrentUserDTO(oldCampaign.getTenantId(), oldCampaign.getCreator()));

        if (ObjectUtils.isNotEmpty(param.getState())) {
            campaign.setState(param.getState());
        }
        if (StringUtils.isNotBlank(param.getCampaignJoinCode())) {
            campaign.setCampaignJoinCode(param.getCampaignJoinCode());
        }

        campaignMapper.updateById(campaign);
        redisService.del(CacheKeys.CAMPAIGN_INFO_CODE + ":" + oldCampaign.getCampaignCode());
        redisService.del(CacheKeys.CAMPAIGN_INFO_DETAIL + ":" + oldCampaign.getCampaignCode());
        if (param.getCleanCache()) {
            redisService.delAll(CacheKeys.CAMPAIGN_ORDER_MATCH);
        }

        return true;
    }

    @Override
    public List<CampaignDTO> getCampaignList() {
        List<Campaign> campaigns = campaignMapper.selectList(null);
        return BeanCopierUtils.convertList(campaigns, CampaignDTO.class);
    }

    @Override
    @Lock(prefix = "campaign:order:lock", key = "#uid")
    @Transactional(rollbackFor = RuntimeException.class)
    public CampaignDTO getCurrentCampaignOrderMatch(Long uid) {

        CampaignDTO signInCampaign = getCampaignInfoDetailCache(CampaignCodeEnum.CAMPAIGN_SIGN_IN.getCode());
//        if (campaign.getState() != 1) {
//            log.info("活动状态: {}, 活动详情: {}", new String[]{"未开始", "进行中", "已结束"}[campaign.getState()], JSON.toJSONString(campaign));
//            return false;
//        }
        if (signInCampaign.getState() == 3) {
            return signInCampaign;
        }
        UserSimpleDTO currentUserInfo = userInfoFeginClient.getCurrentUserSimpleInfo().getData();
        String cacheKey = CacheKeys.CAMPAIGN_ORDER_MATCH + currentUserInfo.getUnionid();
        CampaignDTO cachedResult = (CampaignDTO) redisService.get(cacheKey);
        if (cachedResult != null) {
            return cachedResult;
        }
//        if (StringUtils.isBlank(currentUserInfo.getCardNo())) {
//            throw new BusinessException(ErrorCode.EXCHANGE_NOT_REGISTER);
//        }
        // 检查用户是否已参与签到活动
        CampaignUser campaignUser = isUserAlreadyParticipated(signInCampaign.getCampaignCode(), currentUserInfo.getMobile());
        if (ObjectUtils.isNotEmpty(campaignUser)) {
            redisService.set(cacheKey, signInCampaign, CommonConstants.HOUR_SECONDS_2);
            return signInCampaign;
        }

        // 限流，根据活动开始时间后延10分钟
//        Date campaignStartTime = signInCampaign.getCampaignStartTime();
//        long time = campaignStartTime.getTime();
//        long time1 = System.currentTimeMillis();
//        if (time1 - time < 10*60*1000) {
//            RateLimitAspect.someApiMethod();
//        }

        // 若没有
        CampaignBuyerDTO campaignBuyer = signInCampaign.getCampaignBuyer();
        // 查询订单信息
        if (ObjectUtils.isNotEmpty(campaignBuyer) && StringUtils.isNotBlank(currentUserInfo.getCardNo()) && StringUtils.isNotBlank(currentUserInfo.getMobile())) {
            List<MuJiMemberOrder> muJiMemberOrderList = fetchUserOrders(currentUserInfo.getCardNo(), campaignBuyer);
            log.info("============muJiMemberOrderList=========" +JSON.toJSON(muJiMemberOrderList));
            // 匹配订单详情
            boolean isOrderMatch = checkIfUserPurchasedRequiredItem(currentUserInfo.getCardNo(), muJiMemberOrderList, campaignBuyer.getSku());
            // 匹配成功, 则参与签到活动
            if (isOrderMatch) {
                campaignUserMapper.insert(new CampaignUser(null, signInCampaign.getCampaignCode(), 1, currentUserInfo.getMobile()));
                redisService.set(cacheKey, signInCampaign, CommonConstants.HOUR_SECONDS_2);
                redisService.del(CacheKeys.CAMPAIGN_USER_COUNT + signInCampaign.getCampaignCode() + ":" + currentUserInfo.getMobile());
                return signInCampaign;
            }
        }

        // 匹配失败，检查是否允许报名参加
        // 不允许
        if (signInCampaign.getIsAllowSignUp() == 0) {
            throw new BusinessException(ErrorCode.FORBIDDEN, "您还未获取资格参与本次活动");
        }

        CampaignDTO enrollCampaign = getCampaignInfoCache(signInCampaign.getCampaignJoinCode());
//        if (enrollCampaign.getCampaignCode().equals(CampaignCodeEnum.CAMPAIGN_ENROLL_1.getCode())) {
//            if (campaignEnrollMapper.selectCount(new LambdaQueryWrapper<CampaignEnroll>().eq(CampaignEnroll::getCampaignCode, enrollCampaign.getCampaignCode()).eq(CampaignEnroll::getUnionid, currentUserInfo.getUnionid()).in(CampaignEnroll::getStatus, 0, 1)) <= 0) {
//                if (enrollCampaign.getState() != 1 && enrollCampaign.getState() != 2) {
//                    enrollCampaign = getCampaignInfoCache(CampaignCodeEnum.CAMPAIGN_ENROLL_2.getCode());
//                }
//            }
//        }

        // 允许
        redisService.set(cacheKey, enrollCampaign, CommonConstants.HOUR_SECONDS_2);
        return enrollCampaign;
    }

    private CampaignUser isUserAlreadyParticipated(String campaignCode, String mobile) {
        return campaignUserMapper.selectOne(new LambdaQueryWrapper<CampaignUser>()
                .eq(CampaignUser::getCampaignCode, campaignCode)
                .eq(CampaignUser::getMobile, mobile));
    }

    @Override
    public List<MuJiMemberOrder> fetchUserOrders(String cardNo, CampaignBuyerDTO campaignBuyer) {
        List<MuJiMemberOrder> muJiMemberOrderList = new ArrayList<>();
        Integer pageNum = 1;
        Integer pageSize = 100;

        while (true) {
            JSONObject data = mujiOpenApiFeignClient.orderList(cardNo, DateUtils.toDayStr(campaignBuyer.getChannelStartTime()), DateUtils.toDayStr(campaignBuyer.getChannelEndTime()), 1, pageNum, pageSize).getData();
            if (data == null) {
                break;
            }
            pageNum++;
            List<MuJiMemberOrder> items = data.getJSONArray("items").toJavaList(MuJiMemberOrder.class);
            muJiMemberOrderList.addAll(items);
            if ("N".equals(data.getString("has_more"))) {
                break;
            }
        }

        return muJiMemberOrderList;
    }


    @Override
    public boolean checkIfUserPurchasedRequiredItem(String cardNo, List<MuJiMemberOrder> muJiMemberOrderList, String requiredSku) {
        if (StringUtils.isBlank(requiredSku)) {
            return false;
        }
        String[] split = requiredSku.split(",");
        for (String s : split) {
            for (MuJiMemberOrder muJiMemberOrder : muJiMemberOrderList) {
                JSONObject ticketDetails = mujiOpenApiFeignClient.memberOrderDetails(cardNo, muJiMemberOrder.getOrderSn()).getData();
                log.info("============ticketDetails=========" +JSON.toJSON(ticketDetails));
                if (ticketDetails != null) {
                    JSONArray ticketOrderList = ticketDetails.getJSONArray("goods");
                    for (Object ticketOrder : ticketOrderList) {

                        JSONObject ticketOrderJsonObject = (JSONObject) JSONObject.toJSON(ticketOrder);
                        if (s.equals(ticketOrderJsonObject.getString("goods_id"))) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }


    @Override
    @Cacheable(prefix = CacheKeys.CAMPAIGN_INFO_CODE, key = "#code", expire = CommonConstants.DAY_SECONDS)
    public CampaignDTO getCampaignInfoCache(String code) {
        Campaign campaign = campaignMapper.selectOne(new LambdaQueryWrapper<Campaign>().eq(Campaign::getCampaignCode, code));
        return BeanCopierUtils.convertObject(campaign, CampaignDTO.class);
    }


    @Override
    @Cacheable(prefix = CacheKeys.CAMPAIGN_INFO_DETAIL, key = "#code", expire = CommonConstants.DAY_SECONDS)
    public CampaignDTO getCampaignInfoDetailCache(String code) {
        CampaignDTO dto = getCampaignInfoCache(code);
        dto.setCampaignBuyer(BeanCopierUtils.convertObject(campaignBuyerConfigMapper.selectOne(new LambdaQueryWrapper<CampaignBuyerConfig>().eq(CampaignBuyerConfig::getCampaignCode, dto.getCampaignCode())), CampaignBuyerDTO.class));
//        dto.setCampaignUser(BeanCopierUtils.convertList(campaignUserMapper.selectList(new LambdaQueryWrapper<CampaignUser>().eq(CampaignUser::getCampaignCode, dto.getCampaignCode())), CampaignUserDTO.class));
        dto.setCampaignSignInConfig(BeanCopierUtils.convertObject(campaignSignInConfigMapper.selectOne(new LambdaQueryWrapper<CampaignSignInConfig>().eq(CampaignSignInConfig::getCampaignCode, dto.getCampaignCode())), CampaignSignInConfigDTO.class));
        return dto;
    }

    @Override
    @Lock(prefix = "campaign:data:del", key = "#uid")
    @Transactional(rollbackFor = RuntimeException.class)
    public Boolean delCurrentUserCampaignData(Long uid) {
        // 删除用户参与活动数据
        UserSimpleDTO userInfo = userInfoFeginClient.getDbUserSimpleInfoList(Arrays.asList(uid)).getData().get(0);

        String key1 = CacheKeys.CAMPAIGN_USER_COUNT + CampaignCodeEnum.CAMPAIGN_ENROLL_1.getCode() + ":" + userInfo.getMobile();
        String key2 = CacheKeys.CAMPAIGN_USER_COUNT + CampaignCodeEnum.CAMPAIGN_ENROLL_2.getCode() + ":" + userInfo.getMobile();
        String key3 = CacheKeys.CAMPAIGN_ORDER_MATCH + userInfo.getUnionid();
        redisService.del(key1, key2, key3);

        // 删除用户报名信息
        campaignEnrollMapper.delete(new LambdaQueryWrapper<CampaignEnroll>().eq(CampaignEnroll::getUid, userInfo.getId()));
        // 删除用户白名单
        campaignUserMapper.delete(new LambdaQueryWrapper<CampaignUser>().eq(CampaignUser::getMobile, userInfo.getMobile()));
        // 删除用户打卡信息
        signInUserMapper.delete(new LambdaQueryWrapper<SignInUser>().eq(SignInUser::getUid, userInfo.getId()));
        signInUserDetailMapper.delete(new LambdaQueryWrapper<SignInUserDetail>().eq(SignInUserDetail::getUid, userInfo.getId()));
        // 删除用户抽奖信息
        userLotterySummaryMapper.delete(new LambdaQueryWrapper<UserLotterySummary>().eq(UserLotterySummary::getUid, userInfo.getId()));
        userLotteryPrizesMapper.delete(new LambdaQueryWrapper<UserLotteryPrizes>().eq(UserLotteryPrizes::getUid, userInfo.getId()));
        return true;
    }

    @Override
    public Long getCampaignUser(String campaignCode, String mobile) {
        Long count = campaignUserMapper.selectCount(new LambdaQueryWrapper<CampaignUser>().eq(CampaignUser::getCampaignCode, campaignCode).eq(CampaignUser::getMobile, mobile));
        String key = CacheKeys.CAMPAIGN_USER_COUNT + campaignCode + ":" + mobile;
        redisService.set(key, count, CommonConstants.DAY_SECONDS);
        return count;
    }

    @Override
    public void CampaignBuyerJob(String campaignCode) {
        List<MpMsgSubscribeUserDTO> data = mpMsgFeignClient.getSubscribeUser(SubscribeMsgEnum.BUYER_SIGN_IN.getMsgCode()).getData();
        if (ObjectUtils.isEmpty(data)) {
            log.info("没有订阅用户");
            return;
        }
        List<String> openids = data.stream().map(MpMsgSubscribeUserDTO::getOpenid).collect(Collectors.toList());

        List<CampaignBuyer> campaignBuyers = campaignBuyerMapper.selectList(new LambdaQueryWrapper<CampaignBuyer>().eq(CampaignBuyer::getCampaignCode, campaignCode));
        if (ObjectUtils.isNotEmpty(campaignBuyers)) {
            List<String> collect = campaignBuyers.stream().map(CampaignBuyer::getOpenid).collect(Collectors.toList());
            // 从openids集合中筛选出不在collect集合中的元素
            openids = openids.stream().filter(openid -> !collect.contains(openid)).collect(Collectors.toList());
        }
        if (ObjectUtils.isEmpty(openids)) {
            log.info("去除已记录在购买的用户，结果为空");
            return;
        }

        List<SignInUser> signInUsers = signInUserMapper.selectList(new LambdaQueryWrapper<SignInUser>().eq(SignInUser::getCampaignCode, campaignCode));
        if (ObjectUtils.isNotEmpty(signInUsers)) {
            List<String> collect = signInUsers.stream().map(SignInUser::getOpenid).collect(Collectors.toList());
            // 从openids集合中筛选出不在collect集合中的元素
            openids = openids.stream().filter(openid -> !collect.contains(openid)).collect(Collectors.toList());
        }
        if (ObjectUtils.isEmpty(openids)) {
            log.info("去除已签到的用户，结果为空");
            return;
        }

        // 把openids分成每1000条一组，然后放入集合
        List<List<String>> openidList = Lists.partition(openids, 1000);
        log.info("openidList:{}", openidList);
        List<UserSimpleDTO> userInfoList = new ArrayList<>();
        openidList.forEach(openid -> {
            List<UserSimpleDTO> data1 = userInfoFeginClient.getUserIdListByOpenids(openid, SecurityContext.getUser().getTenantId()).getData();
            userInfoList.addAll(data1);
        });

        CampaignDTO signInCampaign = getCampaignInfoDetailCache(CampaignCodeEnum.CAMPAIGN_SIGN_IN.getCode());
        CampaignBuyerDTO campaignBuyerDTO = signInCampaign.getCampaignBuyer();
        Date now = new Date();
        List<CampaignBuyer> campaignBuyerList = new ArrayList<>();
        for (UserSimpleDTO userSimpleDTO : userInfoList) {
            if (userSimpleDTO.getCardNo() != null) {
                // 查询订单信息
                // 查询购买
                // 查询订单信息
                List<MuJiMemberOrder> muJiMemberOrderList = fetchUserOrders(userSimpleDTO.getCardNo(), campaignBuyerDTO);
                // 匹配订单详情
                boolean isOrderMatch = checkIfUserPurchasedRequiredItem(userSimpleDTO.getCardNo(), muJiMemberOrderList, campaignBuyerDTO.getSku());
                if (isOrderMatch) {
                    CampaignBuyer campaignBuyer = new CampaignBuyer();
                    campaignBuyer.setCampaignCode(campaignCode);
                    campaignBuyer.setOpenid(userSimpleDTO.getOpenid());
                    campaignBuyer.setPushFlag(0);
                    campaignBuyer.setIsDeleted(0);
                    campaignBuyer.setTenantId(SecurityContext.getUser().getTenantId());
                    campaignBuyer.setCreated(now);
                    campaignBuyer.setCreator(SecurityContext.getUser().getUid());
                    campaignBuyer.setModified(now);
                    campaignBuyer.setModifier(SecurityContext.getUser().getUid());
                    campaignBuyerList.add(campaignBuyer);
                }
            }
        }

        if (ObjectUtils.isNotEmpty(campaignBuyerList)) {
            Lists.partition(campaignBuyerList, 500).forEach(campaignBuyer -> {
                campaignBuyerMapper.insertBatchSomeColumn(campaignBuyer);
            });
        }
    }

    @Override
    public void CampaignBuyerPushMsgJob(String campaignCode) {
        List<CampaignBuyer> campaignBuyers = campaignBuyerMapper.selectList(new LambdaQueryWrapper<CampaignBuyer>().eq(CampaignBuyer::getCampaignCode, campaignCode).eq(CampaignBuyer::getPushFlag, 0));
        if (ObjectUtils.isNotEmpty(campaignBuyers)) {
            List<SubscribeMsgSendDTO> list = new ArrayList<>();
            campaignBuyers.forEach(campaignBuyer -> {
                SubscribeMsgSendDTO msgSend = new SubscribeMsgSendDTO(SubscribeMsgEnum.BUYER_SIGN_IN, campaignBuyer.getOpenid(), null,
                        new String[]{null, null, null});
                msgSend.setMsgCode(SubscribeMsgEnum.BUYER_SIGN_IN.getMsgCode());
                list.add(msgSend);
            });

            Long tenantId = SecurityContext.getUser().getTenantId();
            ThreadPoolUtils.pool.execute(() -> {
                SecurityContext.setUser(new CurrentUserDTO(tenantId));
                for (SubscribeMsgSendDTO msgSend : list) {
                    mpMsgFeignClient.sendSubscribeMsgCatch(msgSend, tenantId);
                }
            });
            campaignBuyers.forEach(campaignBuyer -> {
                campaignBuyer.setPushFlag(1);
                campaignBuyerMapper.updateById(campaignBuyer);
            });
        }
    }
}
