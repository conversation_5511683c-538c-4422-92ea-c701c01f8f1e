package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@Table("页面推广")
@TableName(value = "promotion_page")
public class PromotionPage implements Serializable {
    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "渠道ID")
    private Long pId;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "页面ID")
    private Long tId;
    @Columns(type = ColumnType.VARCHAR,length = 32,isNull = true,comment = "链接")
    private String linkUrl;
    @Columns(type = ColumnType.MEDIUMTEXT,length = 0,isNull = true,comment = "二维码")
    private String qrCode;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    private Date endTime;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = true,comment = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
}
