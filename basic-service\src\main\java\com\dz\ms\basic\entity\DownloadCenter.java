package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 下载中心
 *
 * @author: LiinNs
 * @date: 2024/11/27 15:10
 */
@Getter
@Setter
@NoArgsConstructor
@Table("下载中心")
@TableName(value = "download_center")
public class DownloadCenter implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 0, isNull = false, comment = "ID ")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR, length = 128, isNull = true, comment = "业务报表接口")
    private String queryUrl;
    @Columns(type = ColumnType.VARCHAR, length = 10000, isNull = true, comment = "请求参数")
    private String jsonParam;
    @Columns(type = ColumnType.VARCHAR, length = 16, isNull = true, comment = "请求方法")
    private String method;
    @Columns(type = ColumnType.VARCHAR, length = 128, isNull = true, comment = "表头")
    private String header;
    @Columns(type = ColumnType.VARCHAR, length = 128, isNull = true, comment = "菜单名")
    private String menuName;
    @Columns(type = ColumnType.VARCHAR, length = 128, isNull = true, comment = "模块名")
    private String moduleName;
    @Columns(type = ColumnType.VARCHAR, length = 128, isNull = true, comment = "文件名")
    private String fileName;
    @Columns(type = ColumnType.BIGINT, length = 0, isNull = false, defaultValue = "0", comment = "下载次数")
    private Long downloadNum;
    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = true, comment = "拓展名")
    private String fileExt;
    @Columns(type = ColumnType.VARCHAR, length = 1024, isNull = true, comment = "下载文件路径")
    private String sourceUrl;
    @Columns(type = ColumnType.TINYINT, length = 0, isNull = false, defaultValue = "0", comment = "状态 0-生成中 1-已完成 2-已失败 3-已失效")
    private Integer state;
    @Columns(type = ColumnType.VARCHAR, length = 1024, isNull = true, comment = "失败原因")
    private String errorDesc;
    @Columns(type = ColumnType.TINYINT, length = 0, isNull = false, defaultValue = "0", comment = "是否删除 0-未删除 1-已删除")
    @TableLogic
    private Integer isDeleted;
    @Columns(type = ColumnType.BIGINT, length = 0, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT, length = 0, isNull = false, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;

    public DownloadCenter(Long id, String queryUrl, String jsonParam, String method, String header, String menuName, String moduleName, String fileName, Long downloadNum, String fileExt, String sourceUrl, Integer state, String errorDesc) {
        this.id = id;
        this.queryUrl = queryUrl;
        this.jsonParam = jsonParam;
        this.method = method;
        this.header = header;
        this.menuName = menuName;
        this.moduleName = moduleName;
        this.fileName = fileName;
        this.downloadNum = downloadNum;
        this.fileExt = fileExt;
        this.sourceUrl = sourceUrl;
        this.state = state;
        this.errorDesc = errorDesc;
    }

}
