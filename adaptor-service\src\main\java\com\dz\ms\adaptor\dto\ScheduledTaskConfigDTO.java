package com.dz.ms.adaptor.dto;
import java.util.Date;
import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 定时任务配置表DTO
 * @author: 
 * @date:   2025/03/11 15:01
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "定时任务配置表")
public class ScheduledTaskConfigDTO extends BaseDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "任务名称")
    private String taskName;
    @ApiModelProperty(value = "Cron表达式")
    private String cronExpression;
    
    @ApiModelProperty(value = "参数")
    private String param;
    @ApiModelProperty(value = "类名")
    private String className;
    @ApiModelProperty(value = "方法名")
    private String methodName;
    @ApiModelProperty(value = "上次运行时间")
    private Date lastRunTime;
    @ApiModelProperty(value = "下一次执行时间")
    private Date nextExecutionTime;
    @ApiModelProperty(value = "状态")
    private Integer status;

}
