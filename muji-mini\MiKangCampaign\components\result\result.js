import {
  throttle
} from '../../../utils/util';
import {
  salesEnroll_list
} from "../../api/index.js";
const app = getApp();


Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    isShow: {
      type: Boolean,
      value: false,
    },
    num: {
      type: Number,
      value: 0,
    },
    // 是否显示关闭按钮
    closeable: {
      type: Boolean,
      value: true
    },
  },
  lifetimes: {
    attached() {
      this.setUserInfo()
    },
  },
  pageLifetimes: {
    show() {
      this.setUserInfo()
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    option: [{
        label: "用户ID",
        value: "name",
      },
      // {
      //   label: "会员卡号",
      //   value: "cardNo",
      // },
      {
        label: "手机号码",
        value: "enrollPhone",
      },
    ],
    list: [{
        cardNo: "李四",
        mobile: "11111111111",
      },
      {
        cardNo: "李四",
        mobile: "11111111111",
      },
      {
        cardNo: "李四",
        mobile: "11111111111",
      },
      {
        cardNo: "李四",
        mobile: "11111111111",
      },
      {
        cardNo: "李四",
        mobile: "11111111111",
      },
      {
        cardNo: "李四",
        mobile: "11111111111",
      },
      {
        cardNo: "李四",
        mobile: "11111111111",
      },
      {
        cardNo: "李四",
        mobile: "11111111111",
      },
    ],
    listData: [],
    tabsObj: {
      1: "一",
      2: "二",
      3: "三",
      4: "四",
    },
  },
  /**
   * 组件的方法列表
   */
  methods: {
    // 禁止穿透
    touchmove() {
      return false
    },
    // 禁止穿透
    touchmove1() {
      return true
    },
    setUserInfo() {
      this.setData({
        userInfo: app.globalData.userInfo
      })
      this.getList()
    },
    onClose() {
      this.triggerEvent('close')
    },
    copyTxt(e) {
      const {
        copy
      } = e.currentTarget.dataset;
      wx.setClipboardData({
        data: copy,
      })
    },
    /**
     * 生命周期函数--监听页面隐藏
     */
    getList() {
      let {
        tabsObj
      } = this.data;
      salesEnroll_list().then((res) => {
        let list = [];
        if (res.data && res.data.length > 0) {
          res.data.forEach((item, index) => {
            item.campaignStartTime = this.getDate(
              item.campaignStartTime
            );
            item.campaignEndTime = this.getDate(item.campaignEndTime);
            console.log(tabsObj[index + 1], "tabsObj[index + 1]");
            item.tabsObj = tabsObj[index + 1];
            if (item.enrollRosterList.length > 0) {
              list.push(item);
            }
          });
          list = list.reverse();
          // list = this.data.list.reverse();
          console.log(list, 'list');
        }
        this.setData({
          listData: list,
        });
      });
    },
    // 处理时间
    getDate(time) {
      console.log(time, "time");
      if (typeof time === "string") {
        // ios 解析不出来 年月 2020-05
        if (time.length < 8) {
          time = `${time}-1`;
        }
        time = new Date(
          time.replace(/-/g, "/").replace("T", " ")
        ).getTime();
      }
      let date = new Date(time); // 创建一个日期对象
      let year = date.getFullYear(); // 获取年份
      let month = date.getMonth() + 1; // 获取月份（0-11），需要加1
      let day = date.getDate(); // 获取日

      // 拼接成“2023年1月1日”的格式
      let formattedDate = `${year}年${month}月${day}日`;
      console.log(formattedDate); // 输出：2023年1月1日（假设今天是2023年1月1日）
      return formattedDate;
    },
  }
})
