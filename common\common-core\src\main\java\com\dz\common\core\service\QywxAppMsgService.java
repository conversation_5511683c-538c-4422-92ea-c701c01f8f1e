package com.dz.common.core.service;

import com.dz.common.core.dto.KeyValueDTO;
import com.dz.common.core.dto.basic.QywxConfigDTO;
import com.dz.common.core.dto.wechat.qymsg.TemplateCardMsgDTO;
import com.dz.common.core.dto.wechat.qymsg.TemplateMsgContentDTO;
import com.dz.common.core.enums.WechatApiEnum;
import com.dz.common.core.fegin.basic.TenantInfoFeginClient;
import com.dz.common.core.fegin.basic.QywxConfigFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 企业微信应用消息工具
 * @Author: Handy
 * @Date: 2022/7/28 23:03
 */
@Component
@Slf4j
public class QywxAppMsgService {

    @Resource
    private WechatRequestSevice wechatRequestSevice;
    @Resource
    private QywxConfigFeignClient wechatFeignClient;
    @Resource
    private TenantInfoFeginClient tenantInfoFeginClient;

    /**
     * 发送企微模板卡片消息
     * @param tousers 接收消息员工账号列表
     * @param title 消息主标题
     * @param desc 消息主标题辅助信息
     * @param emphaTitle 核心内容放大显示 ：300
     * @param emphaDesc 核心内容放大显示辅助信息 ：支付金额
     * @param datas 核心内容列表 ：{"订单编号":"1234","支付时间":"2022-07-29 00:00"}
     * @param pagePath 跳转小程序页面链接
     * @param tenantId 租户ID
     */
    public void sendTemplateCardMsg(List<String> tousers, String title, String desc, String emphaTitle, String emphaDesc, List<TemplateMsgContentDTO> datas, String pagePath, Long tenantId) {
        try {
            QywxConfigDTO qywxConfig = wechatFeignClient.getQywxConfigByTenantId(tenantId).getData();
            if(qywxConfig.getState().equals(0)) {
                return;
            }
            KeyValueDTO tenantInfo = tenantInfoFeginClient.getTenantById(tenantId).getData();
            TemplateCardMsgDTO msg = new TemplateCardMsgDTO();
            TemplateCardMsgDTO.TemplateCard templateCard = msg.new TemplateCard();
            templateCard.setCard_type("text_notice");
            //卡片顶部-----
            TemplateCardMsgDTO.Source source = msg.new Source();
            source.setIcon_url(tenantInfo.getValue());
            source.setDesc("CK精品店导购端");
            source.setDesc_color(1);
            templateCard.setSource(source);
            //顶部描述信息
            TemplateCardMsgDTO.Title mainTitle = msg.new Title();
            mainTitle.setTitle(title);
            mainTitle.setDesc(desc);
            templateCard.setMain_title(mainTitle);
            //核心数据
            TemplateCardMsgDTO.Title emphasisContent = msg.new Title();
            emphasisContent.setTitle(emphaTitle);
            emphasisContent.setDesc(emphaDesc);
            templateCard.setEmphasis_content(emphasisContent);
            if (!CollectionUtils.isEmpty(datas)){
                templateCard.setHorizontal_content_list(datas);
            }
            //跳转链接
            TemplateCardMsgDTO.Jump jump = msg.new Jump();
            jump.setType(2);
            jump.setAppid(qywxConfig.getAppid());
            jump.setPagepath(pagePath);
            jump.setTitle("进入小程序");
            List<TemplateCardMsgDTO.Jump> jumpList = new ArrayList<>();
            jumpList.add(jump);
            templateCard.setJump_list(jumpList);
            //卡片跳转链接
            TemplateCardMsgDTO.Jump cardAction = msg.new Jump();
            cardAction.setType(2);
            cardAction.setAppid(qywxConfig.getAppid());
            cardAction.setPagepath(pagePath);
            templateCard.setCard_action(cardAction);

            msg.setMsgtype("template_card");
            msg.setTemplate_card(templateCard);
            msg.setAgentid(qywxConfig.getAgentId());
            msg.setTouser(tousers.stream().map(String::valueOf).collect(Collectors.joining("|")));
            wechatRequestSevice.request(WechatApiEnum.API_SEND_WXCP_APP_MESSAGE,tenantId,null,msg);
        } catch (Exception e) {
            log.error("发送企微模板卡片消息失败",e);
        }
    }

}
