package com.dz.common.core.enums;

import java.util.Objects;

/**
 * Describe：修改核销数据类型
 * Created by 吴蜀黍 on 2023/8/20 8:36
 **/
public enum VerifyRecordsModifyTypeEnum {

    MODIFY_STORE(1, "修改门店"),
    MODIFY_DATE(2, "修改可核销时间段"),
    ;

    private final Integer code;
    private final String value;

    VerifyRecordsModifyTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (VerifyRecordsModifyTypeEnum resultEnum : VerifyRecordsModifyTypeEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum.value;
            }
        }
        return null;
    }
}
