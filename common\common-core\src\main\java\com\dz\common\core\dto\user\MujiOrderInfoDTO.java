package com.dz.common.core.dto.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class MujiOrderInfoDTO {

    @ApiModelProperty(value = "会员编号")
    private String memberCode;
    @ApiModelProperty(value = "订单编号")
    private String orderSn;
    @ApiModelProperty(value = "订单类型")
    private Integer orderType;
    @ApiModelProperty(value = "渠道")
    private String channel;
    @ApiModelProperty(value = "渠道名称")
    private String channelName;
    @ApiModelProperty(value = "交易时间 例如:2023-10-01 10:00:00")
    private String payTime;
    @ApiModelProperty(value = "订单金额 单位/分 例如：1800，-1800")
    private String totalFee;
    @ApiModelProperty(value = "实付金额 单位/分")
    private String buyerFee;
    @ApiModelProperty(value = "可累计权益金额 单位/分")
    private String calculateFee;
    @ApiModelProperty(value = "门店名称")
    private String storeName;
    @ApiModelProperty(value = "本次消费获得积分")
    private String bonus;
    @ApiModelProperty(value = "本次消费获得里程")
    private String mileage;
    @ApiModelProperty(value = "商品列表")
    private List<MujiOrderGoodDTO> goods;

}
