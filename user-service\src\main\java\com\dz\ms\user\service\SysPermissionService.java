package com.dz.ms.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.core.dto.user.SysPermissionDTO;
import com.dz.ms.user.entity.SysPermission;

import java.util.List;
import java.util.Set;

/**
 * 权限功能接口
 * @author: Handy
 * @date:   2022/2/4 23:04
 */
public interface SysPermissionService extends IService<SysPermission> {

    /**
     * 根据权限编号查询权限
     * @param code
     * @return
     */
    public SysPermission getPermissionByCode(String code);

    /**
     * 获取权限树形结构
     * @return
     */
    public List<SysPermissionDTO> getSysPermissionTree();

    /**
     * 获取权限树形结构
     * @return
     */
    public List<SysPermissionDTO> getSysMenuTree(Set<Long> ids,Integer platform);

    /**
     * 保存权限功能
     * @param param
     * @return
     */
    Long saveSysPermission(SysPermissionDTO param);

    /**
     * 根据ID删除权限功能
     * @param id
     */
    void deleteSysPermissionById(Long id);

    /**
     * 获取所有权限编码
     * @return
     */
    List<String> getAllPermissionCodes(Integer platform);

    /**
     * 获取所有功能权限编码
     * @return
     */
    List<String> getFunctionPermissionCodes(Integer platform);

}
