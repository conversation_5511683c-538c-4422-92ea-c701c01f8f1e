<template>
  <div @click="showModal">
    <slot name="default"></slot>
  </div>
  <a-modal :title="title" :width="type==1?1000:600" centered :maskClosable="false" :closable="true" :open="visible" @cancel="visible=false">
    <div class="modal">
      <!-- 图片热区展示区域 -->
      <div class="modal-img" v-if="type==1">
        <div class="modal-box">
          <img class="modal-imgUrl" :src="imgUrl" ref="img" />
          <div v-for="(item, idx) in link" :key="idx" :style="{
            left: `${item.position[0]}%`,
            top: `${item.position[1]}%`,
            width: `${item.width}%`,
              height: `${item.height}%`,
          }" :class="{ 'active-move-block-wrap': idx === currentLinkIndex }" class="move-block-wrap">
            <div :style="{
              width: '100%',
              height: '100%',
            }" class="move-block" @mousedown="(event) => moveBlockDown(event, idx)" />
            <div class="scale-block" @mousedown="(event) => scaleBlockDown(event, idx)" />
          </div>
          <!-- <div v-for="(item, idx) in link" :key="idx" :style="{
            left: `${item.position[0]}%`,
            top: `${item.position[1]}%`,
            width: `${item.width}%`,
              height: `${item.height}%`,
          }" :class="{ 'active-move-block-wrap': idx === currentLinkIndex }" class="move-block move-block-wrap" @mousedown="(event) => moveBlockDown(event, idx)">
            <div class="scale-block" @mousedown="(event) => scaleBlockDown(event, idx)" />
          </div>-->
        </div>

      </div>
      <!-- 跳转操作区域 -->
      <div class="modal-operate">
        <div class="modal-operate-top">
          <!-- type==1 有图片 可以多个热区 -->
          <a-button block @click="addHot" v-if="type==1" :disabled="maxLinkNum&&link.length>=maxLinkNum">+ 添加热区{{ maxLinkNum?`（最多${maxLinkNum}个）`:'' }}</a-button>
          <!-- type==2 只有一个热区 -->
          <a-button block @click="addHot" v-else :disabled="link.length>=1">+ 添加热区（最多1个）</a-button>
        </div>
        <div class="modal-operate-bottom">
          <div class="modal-operate-item" :class="idx==currentLinkIndex?'active':''" v-for="(item, idx) in link" :key="item.id" @click="currentLinkIndex=idx">
            <div class="modal-operate-title">热区<template v-if="type==1">{{ idx+1 }}</template></div>
            <delete-outlined class="modal-operate-delete" size="mini" @click.stop="deleteHot(idx)" />
            <a-form-item label="埋点参数">
              <a-input placeholder="请输入" allow-clear v-model:value="item.code" allowClear></a-input>
            </a-form-item>
            <!-- 事件列表 -->
            <div class="modal-operate-box" v-for="(event,i) in item.events" :key="event.id">
              <div class="modal-operate-title"> 事件{{ i+1 }} </div>
              <delete-outlined class="modal-operate-delete" size="mini" @click.stop="deleteEvnet(idx,i)" />
              <a-form-item label="条件">
                <a-button block @click="addEventCondition(idx,i)">+ 添加条件</a-button>
              </a-form-item>
              <!-- 条件 -->
              <a-form-item :labelCol="{ width:'90px' }" :label="'条件'+(t+1)" class="modal-operate-condition" v-for="(condition,t) in event.conditions" :key="condition.id">
                <a-space>
                  <a-select style="width:150px" placeholder="请选择条件" v-model:value="condition.oneValue" :options="conditionOptions" @change="(value,option)=>changeCondition(value,option,condition)" :getPopupContainer="triggerNode => triggerNode.parentNode"></a-select>
                  <a-select style="width:150px" placeholder="请选择条件值" v-model:value="condition.secondValue" :options="conditionOptions[condition.oneValue-1]?.children||[]" :mode="condition.multiple?'multiple':''" allowClear :getPopupContainer="triggerNode => triggerNode.parentNode"></a-select>
                </a-space>
                <delete-outlined class="modal-operate-delete" size="mini" @click.stop="deleteEventCondition(idx,i,t)" />
              </a-form-item>
              <a-divider />
              <a-form-item label="选择事件">
                <a-select placeholder="请选择" ref="select" v-model:value="event.operateType" :getPopupContainer="triggerNode => triggerNode.parentNode">
                  <a-select-option :value="1">优惠券实时发券</a-select-option>
                  <a-select-option :value="2">订阅消息</a-select-option>
                </a-select>
              </a-form-item>
              <!-- 优惠券 -->
              <template v-if="event.operateType==1">
                <a-form-item label="优惠券类型">
                  <a-select v-model:value="event.receiveType" placeholder="请选择优惠券类型" style="width:100%" allowClear>
                    <a-select-option :value="1">活动券</a-select-option>
                    <a-select-option :value="2">普通券</a-select-option>
                  </a-select>
                </a-form-item>
                <a-form-item label="优惠券" v-if="event.receiveType==1">
                  <a-input v-model:value="event.activityId" placeholder="请输入优惠券编码" maxlength="100"></a-input>
                </a-form-item>
                <a-form-item label="优惠券" v-else-if="event.receiveType==2">
                  <a-input v-model:value="event.couponId" placeholder="请输入优惠券编码" maxlength="100"></a-input>
                </a-form-item>
              </template>
              <!-- 订阅消息 -->
              <template v-else-if="event.operateType==2">
                <a-form-item label="订阅消息">
                  <a-select v-model:value="event.subscribeScene" mode="multiple" placeholder="请选择订阅消息" :getPopupContainer="triggerNode => triggerNode.parentNode">
                    <!-- 订阅消息模板ID  接口获取 -->
                    <a-select-option v-for="item in subscribeMsgList" :value="item.templateId" :key="item.templateId" :disabled="event.subscribeScene?.length>=3&&!event.subscribeScene.includes(item.templateId)">{{item.templateName}} （{{item.keyword}}）</a-select-option>
                  </a-select>
                </a-form-item>
                <!-- <a-form-item label="未开启订阅">
                  <a-radio-group v-model:value="event.modalType">
                    <a-radio :value="1">弹窗提醒开启</a-radio>
                    <a-radio :value="2">跳过</a-radio>
                  </a-radio-group>
                </a-form-item>
                <a-form-item label="订阅次数限制">
                  <a-radio-group v-model:value="event.openType">
                    <a-radio :value="1">不限制</a-radio>
                    <a-radio :value="2">
                      <a-input-number placeholder="请输入" :precision="0" :min="1" :max="999" v-model:value="event.openNumbers" addonBefore="任意模板订阅次数少于次" addonAfter="才弹窗"></a-input-number>
                    </a-radio>
                  </a-radio-group>
                </a-form-item>
                <a-form-item label="订阅频次" :labelCol="{width:'150px'}">
                  <a-radio-group v-model:value="event.frequencyType">
                    <a-radio :value="1">每次打开都订阅</a-radio>
                    <a-radio :value="2">
                      <a-space>
                        <a-space-compact block>
                          <a-input-number placeholder="请输入" :precision="0" :min="1" :max="999" v-model:value="event.openDays" addonBefore="每"></a-input-number>
                          <a-input-number placeholder="请输入" :precision="0" :min="1" :max="999" v-model:value="event.openFrequency" addonBefore="天" addonAfter="次"></a-input-number>
                        </a-space-compact>
                      </a-space>
                    </a-radio>
                  </a-radio-group>
                </a-form-item> -->
              </template>
            </div>
            <a-form-item v-if="isEvent">
              <a-button block @click="addEvent(idx)">+ 添加一个事件</a-button>
            </a-form-item>
            <!-- 行为列表 -->
            <div class="modal-operate-box" v-for="(action,i) in item.actions" :key="action.id">
              <div class="modal-operate-title"> 行为{{ i+1 }} </div>
              <delete-outlined class="modal-operate-delete" size="mini" @click.stop="deleteAction(idx,i)" />
              <a-form-item label="条件">
                <a-button block @click="addActionCondition(idx,i)">+ 添加条件</a-button>
              </a-form-item>
              <!-- 条件 -->
              <a-form-item :labelCol="{ width:'90px' }" :label="'条件'+(t+1)" class="modal-operate-condition" v-for="(condition,t) in action.conditions" :key="condition.id">
                <a-space>
                  <a-select style="width:150px" placeholder="请选择条件" v-model:value="condition.oneValue" :options="conditionOptions" @change="(value,option)=>changeCondition(value,option,condition)" :getPopupContainer="triggerNode => triggerNode.parentNode"></a-select>
                  <a-select style="width:150px" placeholder="请选择条件值" v-model:value="condition.secondValue" :options="conditionOptions[condition.oneValue-1]?.children||[]" :mode="condition.multiple?'multiple':''" allowClear :getPopupContainer="triggerNode => triggerNode.parentNode"></a-select>
                </a-space>
                <delete-outlined class="modal-operate-delete" size="mini" @click.stop="deleteActionCondition(idx,i,t)" />
              </a-form-item>
              <a-divider />
              <a-form-item label="选择行为">
                <a-select placeholder="请选择" ref="select" v-model:value="action.operateType" @change="(value)=>changeType(value,action)" :getPopupContainer="triggerNode => triggerNode.parentNode">
                  <template v-for="item in operateTypes" :key="item.value">
                    <a-select-option :value="item.value" v-if="showType.includes(item.value)">{{ item.label }}</a-select-option>
                  </template>
                </a-select>
              </a-form-item>
              <!-- 跳转页面 -->
              <template v-if="action.operateType==1">
                <a-form-item label="跳转是否重定向" :labelCol="{width:'150px'}">
                  <a-switch :unCheckedValue="0" :checkedValue="1" v-model:checked="action.ifRedirectTo" />
                </a-form-item>
                <a-form-item label="热区链接">
                  <selectPage :link="action" :params="{idx,i}" @ok="changeLink"></selectPage>
                </a-form-item>
              </template>
              <!-- 关闭弹窗 -->
              <template v-else-if="action.operateType==2">
                <a-form-item label="弹窗位置">
                  <a-select placeholder="请选择" v-model:value="action.modalIndex" allowClear :getPopupContainer="triggerNode => triggerNode.parentNode">
                    <a-select-option :value="-1">关闭弹窗本身</a-select-option>
                    <!-- 自定义页面里面才显示 -->
                    <template v-if="showOtherModal">
                      <a-select-option :value="index" v-for="(item,index) in 10" :key="index">弹窗{{ index+1 }}</a-select-option>
                    </template>
                  </a-select>
                </a-form-item>
              </template>
              <!-- 打开弹窗 -->
              <template v-else-if="action.operateType==3">
                <a-form-item label="弹窗位置">
                  <a-select placeholder="请选择" v-model:value="action.modalIndex" allowClear :getPopupContainer="triggerNode => triggerNode.parentNode">
                    <a-select-option :value="index" v-for="(item,index) in 10" :key="index">弹窗{{ index+1 }}</a-select-option>
                  </a-select>
                </a-form-item>
                <!-- 跳转页面 -->
                <a-form-item label="弹窗内容">
                  <selectPage btnTitle="+ 添加弹窗" :link="action" :showList="[4]" :params="{idx,i}" @ok="changeLink"></selectPage>
                </a-form-item>
              </template>
              <!-- 点击复制 -->
              <a-form-item label="复制内容" v-else-if="action.operateType==5">
                <a-textarea v-model:value="action.copyText" placeholder="请输入" :maxlength="1000" />
              </a-form-item>
              <!-- 点击分享 -->
              <template v-else-if="action.operateType==6">
                <pageShare :showShare="false" :disabled="disabled" :addParams="action"></pageShare>
              </template>
              <!-- 锚点跳转 -->
              <a-form-item label="锚点位置" v-else-if="action.operateType==7">
                <a-select placeholder="请选择" v-model:value="action.anchorIndex" allowClear :getPopupContainer="triggerNode => triggerNode.parentNode">
                  <a-select-option :value="index" v-for="(item,index) in components" :key="index">锚点{{ index+1 }}</a-select-option>
                </a-select>
              </a-form-item>
            </div>
            <a-form-item>
              <a-button block @click="addAction(idx)" :disabled="maxCondition&&(item.actions.length>=maxCondition)">+ 添加一个行为</a-button>
            </a-form-item>
          </div>
        </div>

      </div>
    </div>
    <template #footer v-if="!disabled">
      <a-space>
        <a-button @click="visible=false">取消</a-button>
        <a-button type="primary" @click="ok">确定</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router'
import { grade_infoLista, crowdList } from '@/http'
import { message } from "ant-design-vue";
import { cloneDeep } from 'lodash'
import { v4 as uuidv4 } from "uuid";
import { useGlobalStore } from '@/store'
const global = useGlobalStore()
// 组件的公共参数
import { operateTypes } from "@/utils/fixedVariable.js";
const img = ref(null)
const subscribeMsgList = ref([])
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  // 类型 1-热区组件（需要有图片才能使用） 2-添加跳转（不需要热区）默认热区只有一个
  type: {
    type: String || Number,
    default: 1
  },
  // 热区图片
  imgUrl: {
    type: String,
  },
  // 原始热区的数据
  links: {
    type: Array,
    required: true
  },
  // 可添加的最多热区个数 默认10个
  maxLinkNum: {
    type: Number,
    default: 10
  },
  // 可添加的最多行为个数 默认不限制
  maxCondition: {
    type: Number,
    default: 0
  },
  // 是否可以添加事件
  isEvent: {
    type: Boolean,
    default: true
  },
  // 组件配置
  components: {
    type: Array,
    default() {
      return []
    }
  },
  // 显示的行为类型
  showType: {
    type: Array,
    default() {
      return [1, 2, 3, 4, 5, 6, 7, 8, 9]
    }
  }
})


// 获取是否是装饰页面  显示其他的弹窗
const showOtherModal = useRoute().path?.includes('/mini/pageMini')





const title = computed(() => {
  return props.type == 1 ? '设置热区' : '设置行为'
})

const { visible, link, currentLinkIndex, conditionOptions } = toRefs(reactive({
  visible: false,
  link: [],
  currentLinkIndex: 0,
  conditionOptions: [
    {
      value: '1',
      label: '是否会员',
      children: [
        {
          value: '1',
          label: '是',
        },
        {
          value: '2',
          label: '否',
        },
      ],
    },
    {
      value: '2',
      label: '会员等级',
      children: [
        {
          value: '1',
          label: '是',
        },
        {
          value: '2',
          label: '否',
        },
      ],
    },
    {
      value: '3',
      label: '白名单用户(允许)',
      children: [

      ],
    },
    {
      value: '4',
      label: '黑名单用户(排除)',
      children: [

      ],

    },
  ],
}))

// 点击显示弹窗
const showModal = () => {
  if (props.disabled) return
  if (props.type == 1 && !props.imgUrl) {
    message.warning('请先添加热区图片~')
    return
  }
  const route = useRoute();
  const currentPath = route?.path;

  link.value = cloneDeep(props.links)
  visible.value = true
}
watch(() => visible.value, () => {
  console.log("🚀 ~ watch ~ props.visible:", visible.value)

  if (visible.value) {
    // 获取订阅消息模板列表
    global.getSubscribeMsgList().then(data => {
      subscribeMsgList.value = data
      // console.log(data)
    })
    // 获取会员等级数据
    grade_infoLista({ state: 1 }).then(res => {
      conditionOptions.value[1].children = res.data.map(item => {
        return {
          value: item.gradeCode,
          label: item.gradeName
        }
      })
    })
    // 获取人群包数据
    crowdList({ pageNum: 1, pageSize: 10000, state: 1 }).then(res => {
      let data = (res.data.list || []).map(item => {
        return {
          value: item.id,
          label: item.crowdName
        }
      })
      conditionOptions.value[2].children = cloneDeep(data)
      conditionOptions.value[3].children = cloneDeep(data)
    })
  }
})
// 切换行为类型
const changeType = (type, item) => {
  if (type == 2 || type == 3 || type == 1) {
    item.modalIndex = null
    item.linkName = ''
    item.linkUrl = ''
    item.path = ''
    item.url = ''
  }
}

// 获取图片样式
const getElementStyle = () => {
  return img.value.getBoundingClientRect();
};


// 添加热区
const addHot = () => {
  link.value.push({
    id: uuidv4(),
    width: 10,
    height: 10,
    position: [0, 0], // 位置
    code: '',// 埋点参数
    events: [],// 事件
    actions: [],// 行为
  });
};

// 删除热区
const deleteHot = (idx) => {
  link.value.splice(idx, 1)
}

// 切换链接
const changeLink = ({ data, params: { idx, i } }) => {
  link.value[idx].actions[i] = data
}
// 添加事件
const addEvent = (idx) => {
  link.value[idx].events.push({
    id: uuidv4(),
    conditions: [], // 条件
    operateType: null, // 1-优惠券实时发券  2-订阅消息
    receiveType: null,// 券类型 1-活动  2-普通
    activityId: null, // 活动优惠券编码
    couponId: null, // 普通优惠券编码
    subscribeScene: undefined,// 订阅场景
    modalType: 1, // 未开启订阅 1-弹窗 2-跳过
    openType: 1, // 订阅限制 1-不限制 2-限制次数
    openNumbers: 1, // 限制次数  少于的话 订阅  多的话不订阅
    frequencyType: 1, // 订阅频次 1-每次订阅  2-次数限制
    openDays: 1, // 限制天数
    openFrequency: 1 // 限制次数
  })
}

// 删除事件
const deleteEvnet = (idx, i) => {
  link.value[idx].events.splice(i, 1)
}

// 添加行为
const addAction = (idx) => {
  link.value[idx].actions.push({
    id: uuidv4(),
    conditions: [], // 条件
    operateType: null,// 行为类型
    modalIndex: null,// 弹窗下标
    anchorIndex: null,// 锚点下标
    copyText: '',// 复制内容
    // 分享设置
    isShare: 1,
    shareTitle: '',//分享标题
    shareImg: '',//分享图片
    sharePath: '',//分享路径
    // 跳转页面
    ifRedirectTo: 0, // 是否重定向
    linkType: 1,// 页面类型
    linkName: '',// 跳转页面标题
    linkUrl: '',// 页面跳转路径组合的
    appid: '',// 小程序id
    path: '',// 页面跳转链接
    url: '',// H5链接
  })
}

// 删除行为
const deleteAction = (idx, i) => {
  link.value[idx].actions.splice(i, 1)
}

// 添加行为条件
const addActionCondition = (idx, i) => {
  link.value[idx].actions[i].conditions.push({
    id: uuidv4(),
    oneValue: null,// 值
    secondValue: null,
    multiple: false,
    children: []
  })
}
// 添加事件条件
const addEventCondition = (idx, i) => {
  link.value[idx].events[i].conditions.push({
    id: uuidv4(),
    oneValue: null,// 值
    secondValue: null,
    multiple: false,
    children: []
  })
}
// 删除事件条件
const deleteEventCondition = (idx, i, t) => {
  link.value[idx].events[i].conditions.splice(t, 1)
}
// 删除行为条件
const deleteActionCondition = (idx, i, t) => {
  link.value[idx].actions[i].conditions.splice(t, 1)
}

// 行为选择改变
const changeCondition = (value, option, condition) => {
  // console.log(value, option, condition)
  // 修改单选多选
  if (value == 2) {
    condition.multiple = true
    condition.secondValue = []
  } else {
    condition.multiple = false
    condition.secondValue = null
  }
  condition.children = option.children
}
// 缩放方框
const scaleBlockDown = (event, index) => {
  currentLinkIndex.value = index;
  const itemInfo = cloneDeep(link.value[index]);
  const oldWidth = event.clientX;
  const oldHeight = event.clientY;
  const elementStyle = getElementStyle();
  const maxWidth = 100 - itemInfo.position[0];
  const maxHeight = 100 - itemInfo.position[1];

  document.onmousemove = function (e) {
    e.preventDefault();
    let width = (e.clientX - oldWidth) * 100 / elementStyle.width + itemInfo.width;
    let height = (e.clientY - oldHeight) * 100 / elementStyle.height + itemInfo.height;
    width = width < 0 ? 0 : width;
    height = height < 0 ? 0 : height;
    width = width < maxWidth ? width : maxWidth;
    height = height < maxHeight ? height : maxHeight;
    link.value[index].width = width;
    link.value[index].height = height;
  };
  document.onmouseup = function () {
    document.onmousemove = () => { };
    document.onmouseup = () => { };
  };
};
// 移动方框
const moveBlockDown = (event, index) => {
  currentLinkIndex.value = index;
  const itemInfo = cloneDeep(link.value[index]);
  console.log(itemInfo)
  const oldClientX = event.clientX;
  const oldClientY = event.clientY;
  const elementStyle = getElementStyle();
  const maxRight = 100 - itemInfo.width;
  const maxBottom = 100 - itemInfo.height;

  document.onmousemove = function (e) {
    e.preventDefault();
    let clientX = (e.clientX - oldClientX) * 100 / elementStyle.width + itemInfo.position[0];
    let clientY = (e.clientY - oldClientY) * 100 / elementStyle.height + itemInfo.position[1];
    clientX = clientX < 0 ? 0 : clientX;
    clientY = clientY < 0 ? 0 : clientY;
    clientX = clientX > maxRight ? maxRight : clientX;
    clientY = clientY > maxBottom ? maxBottom : clientY;
    link.value[index].position = [clientX, clientY];
  };
  document.onmouseup = function () {
    // 触发数据
    document.onmousemove = () => { };
    document.onmouseup = () => { };
  };
};


// 保存设置
const ok = () => {
  visible.value = false
  emit('ok', link.value)
}
</script>

<style  scoped lang="scss">
.modal {
  display: flex;
  min-height: 500px;
  max-height: 1000px;
  &-img {
    position: relative;
    padding-right: 20px;
    border-right: 2px dashed #d9d9d9;
    margin-right: 20px;
    max-height: 500px;
    overflow-y: auto;
  }
  &-box {
    position: relative;
    width: 375px;
    height: auto;
    overflow: hidden;
  }
  &-imgUrl {
    height: auto;
    pointer-events: none;
    width: 375px;
  }
  &-operate {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    max-height: 500px;
    overflow-y: auto;
    &-top {
      margin-bottom: 20px;
    }
    &-bottom {
      flex: 1;
      overflow-y: auto;
      background: #fff;
    }

    &-item {
      position: relative;
      padding: 10px;
      background: #efefef;
      margin-bottom: 10px;
      border: 3px solid transparent;
      &.active {
        border: 3px solid #6a6bbf;
      }
    }
    &-delete {
      position: absolute;
      padding: 10px;
      top: 0;
      right: 0;
    }
    &-title {
      font-weight: bold;
      margin-bottom: 10px;
    }
    &-box {
      background: #fff;
      position: relative;
      padding: 10px;
      border: 1px solid #d9d9d9;
      margin-bottom: 10px;
    }
    &-condition {
      position: relative;
      padding-left: 80px;
    }
  }
}
.move-block-wrap {
  position: absolute;
  cursor: move;
  border: 1px solid #24baab;
  background: rgba(36, 186, 171, 0.5);
  .scale-block {
    width: 0;
    height: 0;
    position: absolute;
    right: 1px;
    bottom: 1px;
    border-bottom: 5px solid white;
    border-left: 5px solid transparent;
    cursor: nw-resize;
  }
  &.active-move-block-wrap {
    border-color: #e75c45;
    background: rgba(231, 92, 69, 0.5);
  }
}
</style>
