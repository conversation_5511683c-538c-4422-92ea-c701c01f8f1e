package com.dz.ms.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 测试注册DTO
 *
 * @author: Handy
 * @date: 2023/07/09 23:58
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "测试注册DTO")
public class TestRegisterDTO {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "邀请人Id")
    private Long inviteUserId;

    @ApiModelProperty(value = "姓名")
    private String realName;
}
