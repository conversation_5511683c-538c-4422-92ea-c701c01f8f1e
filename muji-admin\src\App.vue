<template>
  <!-- 设置组件大小为large 设置主题色 -->
  <a-config-provider :locale="zhCN" component-size="middle" :theme="{
      token: token,
      algorithm: theme.defaultAlgorithm,
    }">
    <router-view></router-view>
  </a-config-provider>
</template>
<script setup>
import { useGlobalStore } from '@/store'
const global = useGlobalStore()
import token from '@/assets/theme.js'
import { theme } from 'ant-design-vue';
import zhCN from 'ant-design-vue/es/locale/zh_CN';
// 默认语言为 en-US，如果你需要设置其他语言，推荐在入口文件全局设置 locale
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
dayjs.locale('zh-cn');

import setting from '@/config/defaultSettings'
document.title = setting.brand + '小程序管理平台' // 设置网站标题



</script>
<style>
:root {
  --layoutBorderRadius: token.layoutBorderRadius;
}
</style>
