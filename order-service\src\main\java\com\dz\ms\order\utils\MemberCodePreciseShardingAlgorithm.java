//package com.dz.ms.order.utils;
//
//
//
//import org.apache.shardingsphere.sharding.api.sharding.standard.PreciseShardingValue;
//import org.apache.shardingsphere.sharding.api.sharding.standard.RangeShardingValue;
//import org.apache.shardingsphere.sharding.api.sharding.standard.StandardShardingAlgorithm;
//
//import java.util.Collection;
//import java.util.Collections;
//import java.util.List;
//
//public class MemberCodePreciseShardingAlgorithm implements StandardShardingAlgorithm<String> {
//
////    @Override
////    public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<String> shardingValue) {
////        String memberCode = shardingValue.getValue();
////        // 去掉前两位
////        String trimmedMemberCode = memberCode.substring(2);
////        // 取模分表
////        int mod = Integer.parseInt(trimmedMemberCode) % 64;
////        String targetTableName = "ods_order_product_" + mod;
////        if (availableTargetNames.contains(targetTableName)) {
////            return targetTableName;
////        } else {
////            throw new IllegalArgumentException("Cannot find the target table name for member_code: " + memberCode);
////        }
////    }
//
//
//    @Override
//    public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<String> shardingValue) {
//        String memberCode = shardingValue.getValue();
//        // 去掉前两位
//        String trimmedMemberCode = memberCode.substring(2);
//        // 取模分表
//        int mod = Integer.parseInt(trimmedMemberCode) % 64;
//        String targetTableName = "ods_order_product_" + mod;
//        if (availableTargetNames.contains(targetTableName)) {
//            return targetTableName;
//        } else {
//            throw new IllegalArgumentException("Cannot find the target table name for member_code: " + memberCode);
//        }
//    }
//
//    @Override
//    public Collection<String> doSharding(Collection<String> availableTargetNames, RangeShardingValue<String> rangeShardingValue) {
//        return Collections.emptyList();
//    }
//
//    @Override
//    public void init() {
//        // Initialization logic if needed
//    }
//
//    @Override
//    public String getType() {
//        return "MEMBER_CODE_MOD";
//    }
//}
