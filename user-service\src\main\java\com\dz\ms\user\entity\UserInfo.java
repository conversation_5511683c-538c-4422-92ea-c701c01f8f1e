package com.dz.ms.user.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户信息
 * @author: Handy
 * @date:   2022/07/14 17:30
 */
@Getter
@Setter
@NoArgsConstructor
@Table("用户信息")
@TableName(value = "user_info")
public class UserInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "用户ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR,length = 36,isNull = true,comment = "小程序openid",isIndex = true)
    private String openid;
    @Columns(type = ColumnType.VARCHAR,length = 36,isNull = true,comment = "微信unionid",isIndex = true)
    private String unionid;
    @Columns(type = ColumnType.VARCHAR,length = 36,isNull = true,comment = "手机号",isIndex = true)
    private String mobile;
    @Columns(type = ColumnType.VARCHAR,length = 64,isNull = true,comment = "会员卡号")
    private String cardNo;
    @Columns(type = ColumnType.VARCHAR,length = 32,isNull = true,comment = "会员名")
    private String username;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = true,comment = "性别 0未知 1男 2女")
    private Integer gender;
    @Columns(type = ColumnType.VARCHAR,length = 32,isNull = true,comment = "出生日期")
    private String birthday;
    @Columns(type = ColumnType.VARCHAR,length = 32,isNull = true,comment = "会员等级")
    private String cardLevel;
    @Columns(type = ColumnType.VARCHAR,length = 255,isNull = true,comment = "头像")
    private String avatar;
    @Columns(type = ColumnType.VARCHAR,length = 32,isNull = true,comment = "省份")
    private String province;
    @Columns(type = ColumnType.VARCHAR,length = 64,isNull = true,comment = "城市")
    private String city;
    @Columns(type = ColumnType.VARCHAR,length = 64,isNull = true,comment = "区")
    private String area;
    @Columns(type = ColumnType.VARCHAR,length = 500,isNull = true,comment = "详细地址")
    private String address;
    @Columns(type = ColumnType.VARCHAR,length = 32,isNull = true,comment = "授权隐私条款版本")
    private String policyVersion;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = true,comment = "是否勾选接收品牌信息 0勾选 1未勾选")
    private Integer isAgreeBrand;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = true,comment = "是否勾选已满十四岁 0勾选 1未勾选")
    private Integer isAgreeYear;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = true,comment = "注册时间")
    private Date registerTime;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = true,comment = "绑定时间")
    private Date bindTime;
    @Columns(type = ColumnType.VARCHAR,length = 40,isNull = true,comment = "来源渠道")
    private String channel;
    @Columns(type = ColumnType.VARCHAR,length = 50,isNull = true,comment = "广告ID")
    private String gdtVid;
    @Columns(type = ColumnType.VARCHAR,length = 40,isNull = true,comment = "活动编码")
    private String campaignCode;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = true,comment = "邀请人Id")
    private Long inviteUid;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = false,defaultValue = "1",comment = "状态 0删除 1正常 2冻结")
    private Integer state;
    @Columns(type = ColumnType.VARCHAR,length = 512,isNull = true,comment = "unionid MD5加密串")
    private String clientId;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = true,comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.VARCHAR,length = 10,isNull = true,comment = "1不想/不再需要使用，2优惠活动少，3账户出现异常，4担忧隐私及安全问题，5其他（多个英文逗号隔开）")
    private String logOffType;
    @Columns(type = ColumnType.VARCHAR,length = 255,isNull = true,comment = "其他注销原因")
    private String logOffDesc;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "注销时间")
    private Date logOffTime;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = false,defaultValue = "0",comment = "首购任务状态0未开始首购任务，1会小上线后三年前有订单，2完成首购任务")
    private Integer firstOrderStatus;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = true,comment = "完成的首购任务id")
    private Long firstOrderTaskId;
}
