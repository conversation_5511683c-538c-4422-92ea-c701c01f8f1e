package com.dz.ms.sales.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/5/13
 */
@Getter
@Setter
@NoArgsConstructor
@Table("抽奖奖品")
@TableName(value = "lottery_prizes")
public class LotteryPrizes implements Serializable {
    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "id")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = true, comment = "活动标识", isIndex = true)
    private String campaignCode;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, comment = "用户类型 0全部用户 1购买指定商品的用户 2其他导入的用户 3报名中奖的用户", defaultValue = "0")
    private Integer targetType;
    @Columns(type = ColumnType.VARCHAR, length = 255, isNull = true, comment = "奖品名称")
    private String prizesName;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "奖品等级编码 0特等奖 1一等奖 2二等奖 3三等奖 4四等奖 5鼓励奖")
    private Integer prizesLevelCode;
    @Columns(type = ColumnType.VARCHAR, length = 255, isNull = true, comment = "奖品级别名称")
    private String prizesLevel;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, comment = "奖品类型 0空 1积分 2券 3实物")
    private Integer type;
    @Columns(type = ColumnType.VARCHAR, length = 500, isNull = true, comment = "列表图片")
    private String imageUrl;
    @Columns(type = ColumnType.VARCHAR, length = 500, isNull = true, comment = "详情图片")
    private String detailImageUrl;
    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = true, comment = "描述")
    private String description;
    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = true, defaultValue = "", comment = "券code")
    private String couponCode;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, defaultValue = "0", comment = "积分数量")
    private Integer pointsNum;
    @Columns(type = ColumnType.DOUBLE, length = 10, scale = 4, isNull = false, defaultValue = "0", comment = "抽奖概率")
    private Double probability;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, defaultValue = "0", comment = "剩余库存")
    private Integer residueInventory;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = false, defaultValue = "1", comment = "是否展示 0隐藏 1展示 ")
    private Integer isShow;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, defaultValue = "0", comment = "排序")
    private Integer sort;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, defaultValue = "0", comment = "中奖次数限制 -1不限制")
    private Integer countLimit;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, defaultValue = "0", comment = "时间限制 -1不限制 1天 2周 3月 4季度 5活动期间")
    private Integer timeLimit;
    @Columns(type = ColumnType.VARCHAR, length = 255, isNull = true, comment = "备注")
    private String remark;
    @Columns(type = ColumnType.BIGINT, length = 11, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT, length = 11, isNull = false, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT, length = 11, isNull = true, comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
}
