package com.dz.ms.product.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 货架商品库存任务DTO
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:25
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "货架商品库存任务")
public class ProductOnTaskDTO extends BaseDTO {

    @ApiModelProperty(value = "商品任务ID")
    private Long id;
    @ApiModelProperty(value = "货架商品ID 因货架可以同时多次添加同一个商品，分开管理库存任务")
    private Long shelfProductId;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "货架名称")
    private String shelfName;
    @ApiModelProperty(value = "商品ID")
    private Long productId;
    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "上架类型 1实时 2单次时间 3周期")
    private Integer onType;
    @ApiModelProperty(value = "上架类型为单次时间时时间点")
    private Date onTime;
    @ApiModelProperty(value = "上架类型为周期时周期天数")
    private Integer onDays;
    @ApiModelProperty(value = "上架库存数量 正+ 负-")
    private Integer onInventory;
    @ApiModelProperty(value = "状态 0待完成 1已完成 2进行中 3待编辑")
    private Integer state;
    @ApiModelProperty(value = "0正常 1删除")
    private Integer isDeleted;
    @ApiModelProperty(value = "1货架正常 0货架禁用")
    private Integer shelfState;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "修改时间")
    private Date modified;
    @ApiModelProperty(value = "修改人")
    private Long modifier;

}
