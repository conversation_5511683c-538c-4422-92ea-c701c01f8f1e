package com.dz.ms.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.user.entity.SysMenu;
import com.dz.ms.user.vo.MenuInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 菜单表 数据层
 *
 * <AUTHOR>
 */
public interface SysMenuMapper extends BaseMapper<SysMenu> {

    /**
     * 根据用户ID查询菜单
     *
     * @return 菜单列表
     */

    List<SysMenu> selectMenuTreeAll(@Param("tenantId") Long tenantId);

    /**
     * 根据用户ID查询菜单
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<SysMenu> selectMenuTreeByUserId(@Param("userId") Long userId ,@Param("tenantId") Long tenantId);

    /**
     * 根据roleId查询菜单
     *
     * @param roleId roleId
     * @return 菜单列表
     */
    List<SysMenu> selectMenuTreeByRoleId(@Param("roleId") Long roleId ,@Param("tenantId") Long tenantId);

    /**
     * 根据菜单名称获取菜单信息
     * @param menuName 名称
     * @param menuType 类型
     * @return List<MenuInfoVo>
     */
    List<MenuInfoVo> getInfoByMenuName(@Param(value = "menuName") String menuName,
                                       @Param(value = "menuType") String menuType);

    /**
     * 根据菜单ID获取菜单信息
     * @param menuId menuId
     * @return SysMenu
     */
    SysMenu selectByParentId(@Param(value = "menuId") Long menuId);
}
