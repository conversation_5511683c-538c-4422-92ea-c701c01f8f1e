import Decimal from '../../../../utils/decimal.min.js';

Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    dataInfo: {
      type: Object,
      value: {},
      observer(val) {
        this.handleData(val)
      }
    }
  },
  /**
   * 组件的初始数据
   */
  data: {
    width: 50,
    purchaseData: {},
  },
  /**
   * 组件的方法列表
   */
  methods: {
    handleData(val) {
      const {
        totalFee,
        ...purchaseData
      } = val;
      purchaseData.price = new Decimal(totalFee).div(100).toFixed(2);
      this.setData({
        purchaseData,
      })
    },
    handleGo(e) {
      const {
        ordersn,
        ordersource
      } = e.currentTarget.dataset;
      wx.$mp.track({
        event: 'more_consumption_detail_click',
        props: {
          ordersource: ordersource,
          ordersn: ordersn
        }
      })
      switch (ordersource) {
        case 2: // 线下单，走小票详情
          wx.$mp.navigateTo({
            url: `/pages/purchaseDetail/purchaseDetail?orderSn=${ordersn}`
          })
          break
        case 1: // 线上单，走订单详情
          wx.$mp.navigateTo({
            url: `/pages/orderDetail/orderDetail?orderSn=${ordersn}`
          })
          break;
      }
    }
  }
})
