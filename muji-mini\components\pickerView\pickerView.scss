.my-picker-view {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  padding: 0 32rpx 64rpx;

  background: #fff;

  &-title {
    line-height: 66rpx;
    padding-top: 60rpx;
    padding-bottom: 40rpx;
    text-align: center;
    font-size: 40rpx;
    font-weight: 700;
    color: #3C3C43;
    font-family: 'MUJIFont2020', SourceHanSansCN;
  }

  &-content {
    height: 372rpx;
    overflow: hidden;
    position: relative;

    &::after {
      content: '';
      height: 1rpx;
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 1rpx;
      background-color: transparent; // 这里设置上需要的颜色即可
    }

    &::before {
      content: '';
      position: absolute;
      z-index: 1000;
      top: 0;
      left: 0;
      right: 0;
      height: 1rpx;
      background-color: transparent; // 这里设置上需要的颜色即可
    }
  }

  &-column {
    color: #3C3C43;
    font-weight: 400;
    font-size: 28rpx;
    line-height: 124rpx;
    text-align: center;
    font-family: 'MUJIFont2020', SourceHanSansCN;

    &.disabled {
      color: #888888;
      background: linear-gradient(90deg, rgba(245, 245, 245, 0) 0%, rgba(245, 245, 245, 0.7) 19%, rgba(245, 245, 245, 0.7) 82%, rgba(245, 245, 245, 0) 100%);
    }
  }

  &-btn {
    border-radius: 10rpx;
    margin-top: 50rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 686rpx;
    height: 90rpx;
    background: #3c3c3c;
    font-size: 28rpx;
    font-weight: 400;
    color: #ffffff;
    font-family: SourceHanSansCN;
    transition: 0.3s;

    &.disabled {
      background: #eeeeee;
      color: #3C3C43;
    }
  }

  &-closeBox {
    position: absolute;
    right: 20rpx;
    top: 20rpx;
    height: 80rpx;
    width: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.indicator {
  height: 124rpx;

  &::after {
    content: '';
    height: 1rpx;
    width: 100%;
    border-color: transparent; // 这里设置上需要的颜色即可
  }

  &::before {
    content: '';
    height: 1rpx;
    width: 100%;
    border-color: transparent; // 这里设置上需要的颜色即可
  }
}
