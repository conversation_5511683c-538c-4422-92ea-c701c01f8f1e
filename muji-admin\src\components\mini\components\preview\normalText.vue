<template>
  <div class="text" :style="{width:width/2+'px'}">
    <!-- 内容 -->
    <text-setting :data="data" :content="data.content" class="textStyle">
      <!-- 背景 -->
      <customBg :bgSetting="data" class="bgStyle"></customBg>
    </text-setting>
  </div>
</template>
<script setup>
const emit = defineEmits(['ok', 'cancel', 'changeActive'])
import customBg from './customBg.vue'
import textSetting from './textSetting.vue'
const props = defineProps({
  width: {
    type: Number,
    default: 750,
  },
  data: {
    type: Object,
    default() {
      return {}
    }
  },
})

</script>

<style scoped lang="scss">
.text {
  position: relative;
}
</style>
