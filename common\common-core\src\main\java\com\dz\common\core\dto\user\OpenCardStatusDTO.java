package com.dz.common.core.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 开卡状态DTO
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "开卡状态")
public class OpenCardStatusDTO {

    @ApiModelProperty(value = "微信会员卡开卡状态 1:用户已开卡， 2:用户未开卡")
    private Integer isOpenCard;
    @ApiModelProperty(value = "打开卡包会员卡卡模版ID")
    private String wechatCardId;
    @ApiModelProperty(value = "打开卡包服务号appId")
    private String wechatCardAppId;
}
