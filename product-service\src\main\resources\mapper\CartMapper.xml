<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.product.mapper.CartMapper">

	<!-- 查询基础字段 -->
	<sql id="Base_Column_List">
		id,
  	    user_id,
  	    shelf_product_id,
  	    shelf_id,
  	    product_id,
		img_url,
  	    cost_point,
  	    cost_price,
  	    number,
		campaign_id,
		rule_id,
		r_everyone_limit,
		r_purchase_limit,
  	    checked,
  	    tenant_id,
  	    created,
  	    modified
	</sql>

	<update id="checkedAllUserCart">
		update cart
		set checked = #{status}
		where user_id = #{userId}
	</update>

	<select id="getUserCartList" resultType="com.dz.common.core.dto.product.CartProductDTO">
		select c.id,
		c.user_id,
		c.shelf_product_id,
		c.shelf_id,
		c.product_id,
		c.img_url,
		c.cost_point,
		c.cost_price,
		c.number,
		c.campaign_id,
		c.rule_id,
		c.r_everyone_limit,
		c.r_purchase_limit,
		c.checked,
		p.product_name,
		p.pd_type,
		p.vender_id,
		p.cost_point 'pCostPoint',
		p.cost_price 'pCostPrice',
		p.state
		from cart c
		left join product p on p.id = c.product_id
		where c.user_id = #{userId}
		and p.is_deleted = 0
		<if test="cartId != null">
			and c.id = #{cartId}
		</if>
		order by id desc
	</select>

	<select id="getProductCartByShelfProductId" resultType="com.dz.common.core.dto.product.CartProductDTO">
		SELECT sp.id           'shelfProductId',
			   sp.shelf_id,
			   p.id            'productId',
			   p.shelf_Img_Url 'imgUrl',
			   p.cost_point,
			   p.cost_price,
			   p.product_name,
			   p.pd_type,
			   p.vender_id,
			   p.cost_point    'pCostPoint',
			   p.cost_price    'pCostPrice',
			   p.state
		FROM product p
				 left join shelf_product sp on p.id = sp.product_id
		WHERE sp.id = #{shelfProductId}
		  and p.is_deleted = 0
	</select>

	<select id="getUserCartCount" resultType="java.lang.Integer">
		select IFNULL(sum(number), 0)
		from cart
		where user_id = #{userId}
		  and shelf_id = #{shelfId}
		  and status = 1
	</select>

</mapper>
