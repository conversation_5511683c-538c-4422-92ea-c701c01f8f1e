package com.dz.ms.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.basic.MpMsgSubscribeUserDTO;
import com.dz.common.core.dto.basic.SubscribeMsgSendApiDTO;
import com.dz.common.core.dto.basic.SubscribeMsgSendDTO;
import com.dz.common.core.dto.wechat.mpmsg.SubscribeMsgKeywordDTO;
import com.dz.common.core.dto.wechat.mpmsg.SubscribeMsgTemplateDTO;
import com.dz.common.core.dto.wechat.mpmsg.SubscribeMsgTemplateGetDTO;
import com.dz.ms.basic.dto.MpMsgDTO;
import com.dz.ms.basic.entity.MpMsg;

import java.util.List;

/**
 * 小程序/公众号模板消息接口
 * @author: Handy
 * @date:   2023/07/07 14:22
 */
public interface MpMsgService extends IService<MpMsg> {

    /**
     * 获取小程序订阅消息模板库列表
     * @param pageNo
     * @param pageSize
     * @return
     */
    PageInfo<SubscribeMsgTemplateDTO> getSubscribeMsgTemplateList(Integer pageNo, Integer pageSize);

    /**
     * 获取小程序订阅消息模板关键词库
     * @param templateCode
     * @return
     */
    List<SubscribeMsgKeywordDTO> getSubscribeMsgKeyword(String templateCode);

    /**
     * 获取帐号下已添加的模板列表
     * @return
     */
    List<SubscribeMsgTemplateGetDTO> getAddSubscribeMsgTemplateList();

	/**
     * 分页查询小程序订阅消息模板配置
     * @param param
     * @return PageInfo<MpMsgDTO>
     */
    public PageInfo<MpMsgDTO> getSubscribeMsgList(MpMsgDTO param);

    /**
     * 保存小程序订阅消息模板配置
     * @param param
     * @return Long
     */
    public Long saveSubscribeMsg(MpMsgDTO param);

    /**
     * 根据ID查询小程序订阅消息模板配置
     * @param id
     * @return
     */
    public MpMsgDTO getSubscribeMsgById(Long id);

    /**
     * 根据ID删除小程序订阅消息模板配置
     * @param param
     */
    public void deleteSubscribeMsgById(IdCodeDTO param);

    /**
     * 发送小程序订阅消息不抛异常
     * @param param
     * @param tenantId
     */
    public void sendSubscribeMsgCatch(SubscribeMsgSendDTO param, Long tenantId);
    /**
     * 发送小程序订阅消息
     * @param param
     * @param tenantId
     */
    public void sendSubscribeMsg(SubscribeMsgSendDTO param, Long tenantId);

    /**
     * 外部接口发送小程序订阅消息
     * @param param
     * @param tenantId
     */
    public void sendSubscribeMsgOut(SubscribeMsgSendApiDTO param, Long tenantId);

    List<MpMsgDTO> getSubscribeMsgListAll();

    List<MpMsgSubscribeUserDTO> getSubscribeUser(String msgCode);

    public MpMsgDTO getSubscribeMsgByCode(String msgCode);
}
