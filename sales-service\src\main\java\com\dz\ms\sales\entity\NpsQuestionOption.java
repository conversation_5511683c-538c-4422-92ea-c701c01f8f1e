package com.dz.ms.sales.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/16
 */
@Getter
@Setter
@NoArgsConstructor
@Table("NPS问卷问题选项")
@TableName(value = "nps_question_option")
public class NpsQuestionOption implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "选项ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "问卷ID")
    private Long npsId;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "问卷问题ID",isIndex = true)
    private Long npsQuestionId;
    @Columns(type = ColumnType.VARCHAR,length = 200,isNull = false,comment = "选项名称")
    private String title;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, comment = "选项排序",defaultValue = "0")
    private Integer sort;

    @Columns(type = ColumnType.TINYINT,length = 3,isNull = false,defaultValue = "0",comment = "是否删除 0未删除 1已删除")
    @TableLogic
    private Integer isDeleted;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = true,comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    public NpsQuestionOption(Long id, Long npsId, Long npsQuestionId, String title, Integer sort) {
        this.id = id;
        this.npsId = npsId;
        this.npsQuestionId = npsQuestionId;
        this.title = title;
        this.sort = sort;
    }

}
