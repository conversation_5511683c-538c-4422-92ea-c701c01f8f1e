package com.dz.common.core.dto.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 小程序订阅消息订阅记录DTO
 * @author: Handy
 * @date:   2023/07/07 14:22
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "小程序订阅消息订阅记录")
public class MpMsgSubscribeUserDTO {

    @ApiModelProperty(value = "记录ID")
    private Long id;
    @ApiModelProperty(value = "订阅用户ID")
    private Long uid;
    @ApiModelProperty(value = "订阅用户openid")
    private String openid;

}
