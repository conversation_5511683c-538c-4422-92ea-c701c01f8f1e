/* components/setting/normal-slider/normal-slider.wxss */
.slider {
  position: relative;
  height: auto;

  &-visitor {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    z-index: 101;
  }

  &-bg {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
  }

  &-content {
    position: relative;
    z-index: 100;
    width: 100%;
    height: auto;
    box-sizing: border-box;
    position: relative;
  }

  &-roll {}

  &-inner {
    position: relative;
  }

  &-point {
    position: absolute;
    display: flex;

    &.left {
      left: var(--left);
    }

    &.right {
      right: var(--right);
    }

    &.center {
      left: 50%;
      transform: translateX(-50%);
    }

    &-item {
      border-radius: 50%;

      &.active {
        background: var(--selected) !important;
      }
    }
  }

  &-image {
    width: 100%;
  }

  &-show {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 18rpx;
    color: #3C3C43;
    line-height: 24rpx;
    display: inline-flex;
    align-items: center;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 20rpx;

    &-text {
      margin: 0 10rpx;
    }

    &-arrow {
      width: 8rpx;
      height: 10rpx;
    }
  }
}

.bgStyle {
  width: 100%;
  height: 100%;
}

.floatStyle {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 100;
  pointer-events: none;
}