package com.dz.common.core.dto.user;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 任务详情
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "导出任务列表")
public class InteractionTaskExportDTO {

    @ExcelProperty(value = "任务类型", index = 0)
    private String taskType;
    @ExcelProperty(value = "任务名称", index = 1)
    private String taskName;
    @ExcelProperty(value = "任务内容", index = 2)
    private String taskDesc;
    @ExcelProperty(value = "任务展示时间", index = 3)
    private String showTime;
    @ExcelProperty(value = "任务限时时间", index = 4)
    private String restrictTime;
    @ExcelProperty(value = "任务状态", index = 5)
    private String taskStatus;
    @ExcelProperty(value = "完成要求", index = 6)
    private String readyNum;
    @ExcelProperty(value = "奖励类型", index = 7)
    private String rewardType;
    @ExcelProperty(value = "奖励内容/数量", index = 8)
    private String couponIdNames;
    @ExcelProperty(value = "总参与人数", index = 9)
    private Integer joinInNum=0;
    @ExcelProperty(value = "总完成人数", index = 10)
    private Integer finishPersonNum=0;
    @ExcelProperty(value = "总完成次数", index = 11)
    private Integer finishNum=0;
    @ExcelProperty(value = "启停状态", index = 12)
    private String status;
}
