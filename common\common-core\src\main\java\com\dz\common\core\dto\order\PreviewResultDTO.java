package com.dz.common.core.dto.order;

import com.dz.common.core.dto.product.CartProductDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单预览返回数据DTO
 */
@Setter
@Getter
@ToString
public class PreviewResultDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "应付总积分")
    private Integer totalPoint = 0;
    @ApiModelProperty(value = "应付总金额 某些商品设置了积分+金额兑换")
    private BigDecimal totalPrice = new BigDecimal(0);

    @ApiModelProperty("购物车列表")
    private List<CartProductDTO> productList = new ArrayList<>();
}