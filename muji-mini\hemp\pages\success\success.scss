/* hemp/pages/index/index.wxss */
.page {
  height: auto;
  overflow-y: auto;
  position: relative;
  &-screen {
    position: relative;
    width: 750rpx;
    background-position: 0 0;
    background-size: 750rpx 100%;
  }
  &-logo {
    position: absolute;
    top: 7.674756%;
    left: 50%;
    transform: translateX(-50%);
    width: 242rpx;
    height: 100rpx;
  }
  &-text {
    position: absolute;
    top: 21.7797%;
    right: 106rpx;
    width: 85rpx;
    height: 514rpx;
  }
  &-time {
    position: absolute;
    bottom: 14.1741%;
    left: 50%;
    transform: translateX(-50%);
    width: 586rpx;
    height: 135rpx;
  }
  &-down {
    position: absolute;
    bottom: 4.3559%;
    left: 50%;
    transform: translateX(-50%);
    width: 206rpx;
    height: 43rpx;
  }
  &-rule {
    margin-top: -4rpx;
    width: 100%;
    display: block;
  }
  &-sign {
    position: absolute;
    left: 150rpx;
    right: 150rpx;
    height: 150rpx;
    bottom: 60rpx;
  }
}
