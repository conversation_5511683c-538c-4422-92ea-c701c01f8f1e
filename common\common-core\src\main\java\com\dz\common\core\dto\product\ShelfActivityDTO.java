package com.dz.common.core.dto.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 货架营销活动
 *
 * @author: fei
 * @date: 2024/11/25 16:27
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "货架营销活动")
public class ShelfActivityDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "货架名称")
    private String name;
    @ApiModelProperty(value = "活动时间类型 1永久 2时间段")
    private Integer onType;
    @ApiModelProperty(value = "开始时间")
    private Date onStartTime;
    @ApiModelProperty(value = "结束时间")
    private Date onEndTime;
    @ApiModelProperty(value = "活动状态 1待开始/2进行中/3已结束")
    private Integer campaignState;
    @ApiModelProperty(value = "货架商品数量")
    private Long campaignProductNum;

}
