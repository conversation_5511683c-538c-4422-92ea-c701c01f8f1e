package com.dz.common.core.dto.wechat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 类注释
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/5/22 15:30
 */
@Setter
@Getter
@ToString
@ApiModel(value = "企业微信解密用户信息")
public class DecryptEnterpriseUserDTO {

    @ApiModelProperty(value = "用户所属企业的corpid")
    private String corpid;

    @ApiModelProperty(value = "企业成员UserID，对应管理端的帐号")
    private String userid;

    @ApiModelProperty(value = "企业成员姓名")
    private String name;

    @ApiModelProperty(value = "企业成员的性别，0表示未定义或无权限获取，1表示男性，2表示女性。")
    private Integer gender;

}
