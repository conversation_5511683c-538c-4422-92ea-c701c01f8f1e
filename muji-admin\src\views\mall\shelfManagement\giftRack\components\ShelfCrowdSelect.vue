<template>
  <a-form-item :label="props.label" :name="props.name" :rules="props.rules" class="ShelfStatusSelect">
    <a-select :fieldNames="{label:'crowdName',value:'id'}" v-model:value="thisFields.value" show-search :filterOption="thisMethods.filterOption" :allowClear="true" :maxTagCount="1" placeholder="请选择" @change="thisMethods.change" :options="thisFields.options" v-bind="$attrs" />
  </a-form-item>
</template>

<script setup>
import { nextTick, onMounted, reactive, watch } from 'vue'
import { SHELF_STATUS_ARR } from '@/utils/constants.js'
import { crowdAllList } from '@/http/index.js'

const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  name: {
    type: String,
    default: ''
  },
  rules: {
    type: Object,
    default: () => undefined
  },
  modelValue: {
    type: [Array, Number, String],
    default: () => []
  }
})
const emits = defineEmits(['change', 'update:modelValue'])

const attrs = useAttrs()
const thisFields = reactive({
  value: attrs.mode === 'multiple' ? [] : '',
  options: SHELF_STATUS_ARR
})
const thisMethods = {
  filterOption(input, option) {
    return option.crowdName.toLowerCase().indexOf(input.toLowerCase()) >= 0
  },
  async getOptions() {
    const res = await crowdAllList({ crowdStatus: 0 })
    thisFields.options = res.data
  },
  setValue() {
    thisFields.value = props.modelValue
    if (props.modelValue) { // 物理删除 - 无法进行回显 - 单选需要置空被删项 - 多选需要移除被删项
      if (attrs.mode === 'multiple') {
        const arr = []
        props.modelValue.forEach(v2 => {
          if (thisFields.options.findIndex(v => v.id === v2) !== -1) {
            arr.push(v2)
          }
        })
        thisFields.value = arr
        emits('update:modelValue', thisFields.value)
      } else {
        if (thisFields.options.findIndex(v => v.id === +props.modelValue) === -1) {
          thisFields.value = null
          emits('update:modelValue', thisFields.value)
        }
      }
    }
  },
  change(e) {
    emits('update:modelValue', e)
    emits('change', e)
  }
}

onMounted(async () => {
  await thisMethods.getOptions()
  await nextTick(async () => {
    await thisMethods.setValue()
  })
})
watch(() => props.modelValue, () => thisMethods.setValue())
</script>

<style lang="scss" scoped>
.ShelfStatusSelect {
  .ant-select {
    width: 202px;
  }
}
</style>
