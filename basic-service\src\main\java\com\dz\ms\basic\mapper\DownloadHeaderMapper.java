package com.dz.ms.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.basic.entity.DownloadHeader;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 报表表头配置表Mapper
 *
 * @author: hezx
 * @date: 2022/08/16 16:30
 */
@Mapper
public interface DownloadHeaderMapper extends BaseMapper<DownloadHeader> {

    int deleteByPrimaryKey(Long id);

    int insert(DownloadHeader record);

    int insertSelective(DownloadHeader record);

    DownloadHeader selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DownloadHeader record);

    int updateByPrimaryKey(DownloadHeader record);

    List<DownloadHeader> selectByReportCode(@Param("reportCode") String reportCode, @Param("tenantId") Long tenantId);
}