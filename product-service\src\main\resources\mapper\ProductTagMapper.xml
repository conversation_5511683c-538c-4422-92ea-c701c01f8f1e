<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.product.mapper.ProductTagMapper">

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
        id,
  	    product_id,
  	    product_name,
  	    tag_id,
  	    tag_name,
  	    cate,
  	    tenant_id,
  	    creator,
  	    created,
  	    modified,
  	    modifier
    </sql>

    <insert id="insertBatch">
        insert into product_tag (product_id, product_name,
        tag_id,tag_name,cate,tenant_id,creator,created,modified,modifier)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.productId},#{item.productName},#{item.tagId},#{item.tagName},#{item.cate},#{item.tenantId},#{item.creator},now(),now(),#{item.modifier})
        </foreach>
    </insert>

    <!-- 查询示例 -->
    <select id="selectDemo" parameterType="java.lang.Long" resultType="com.dz.ms.product.entity.ProductTag">
        select
        <include refid="Base_Column_List"/>
        from product_tag
        where id = #{id,jdbcType=BIGINT}
        AND tenant_id = #{tenantId}
    </select>

</mapper>
