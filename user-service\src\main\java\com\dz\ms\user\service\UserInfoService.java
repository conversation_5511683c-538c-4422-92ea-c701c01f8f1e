package com.dz.ms.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.user.*;
import com.dz.common.core.dto.wechat.DecryptUserDTO;
import com.dz.ms.user.dto.UserMobileUpdDTO;
import com.dz.ms.user.dto.UserRegisterDTO;
import com.dz.ms.user.dto.UserUpdParamDTO;
import com.dz.ms.user.dto.UserUpdateDTO;
import com.dz.ms.user.dto.WechatLoginDTO;
import com.dz.ms.user.entity.UserInfo;
import com.dz.ms.user.vo.LogOffVo;
import com.dz.ms.user.vo.MyMileageDetailsVo;
import com.dz.ms.user.vo.UserMobileBindVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 用户信息接口
 *
 * @author: Handy
 * @date: 2022/2/4 17:33
 */
public interface UserInfoService extends IService<UserInfo> {

    /**
     * 小程序端微信登录
     * @param param
     * @return
     */
    public UserSimpleDTO wechatLogin(WechatLoginDTO param);

    /**
     * 获取用户简要信息
     * @return
     */
    public UserSimpleDTO getUserSimpleInfo(Long uid);

    /**
     * 获取用户信息
     * @param uid
     * @return
     */
    public UserInfoDTO getUserInfo(Long uid);

    /**
     * 获取用户会员信息
     * @return
     */
    public MemberInfoDTO getUserMemberInfo(Long uid);

    /**
     * 获取当前用户会员接口信息带缓存
     * @return
     */
    public MemberInfoDTO getUserMemberInfoCach(Long tenantId, Long uid);

    /**
     * 根据授权code获取微信手机号
     * @param code
     * @return
     */
    public DecryptUserDTO getWxPhoneByCode(String code);

    /**
     * 用户注册
     *
     * @param param
     * @param uid
     * @param tenantId
     */
    public void userRegister(UserRegisterDTO param, Long uid, Long tenantId);

    /**
     * 更新当前用户信息
     * @param param
     */
    public void updateUserInfo(UserUpdateDTO param, Long uid, Long tenantId);

    /**
     * 当前用户新增clientId
     */
    String addUserClientId(Long uid, Long tenantId);

    /**
     * 注销用户
     * @param param
     */
    public void logOff(LogOffVo param, Long uid, Long tenantId);

    /**
     * 根据用户ID列表获取用户openid列表
     * @param ids
     * @param tenantId
     * @return
     */
    public List<String> getUserOpenidByIds(List<Long> ids, Long tenantId);

    /**
     * 更新授权隐私条款版本号及勾选
     *
     * @param param
     */
    public void updateUserPolicyVersion(UserUpdateDTO param);

        /**
     * 根据授权code获取微信手机号对应会员信息
     *
     * @param code
     * @return
     */
    MemberInfoDTO getWxPhoneMemberByCode(String code);

    /**
     * 获取当前用户简要信息
     * @return
     */
    UserSimpleDTO getCurrentUserSimpleInfo();

    /**
     * 我的积分
     * @return
     */
    MyPointsDTO myPoints();

    /**
     * 我的积分流水
     *
     * @return
     */
    PageInfo<MyPointsRecordsDTO> myPointsRecords(MyMileageDetailsVo param);

    /**
     * 即将过期积分列表
     * @return
     */
    PageInfo<HistoryPointsRecordsDTO> recentExpirePoints(MyMileageDetailsVo param);

    /**
     * 历史过期积分列表
     * @return
     */
    PageInfo<HistoryPointsRecordsDTO> historyExpirePoints(MyMileageDetailsVo param);

    /**
     * 我的积分详情
     *
     * @param bonusSn
     * @return
     */
    MyPointsDetailsDTO myPointsDetails(String bonusSn);

    /**
     * 我的里程流水
     *
     * @return
     */
    PageInfo<MyMileageRecordsDTO> myMileageRecords(MyMileageDetailsVo param);

    /**
     * 我的里程详情
     *
     * @param mileageSn
     * @return
     */
    MyMileageDetailsDTO myMileageDetails(String mileageSn);

    PageInfo<UserSimpleDTO> getUserSimpleInfoList(Integer pageNum, Integer pageSize);

    void sendSmsCode();
    
    String sendSmsCode(String mobile);
    
    String mobileUpdate(UserMobileUpdDTO userMobileUpdDTO);

    String mobileBind(UserMobileBindVo userMobileBindVo);

    /**
     * 从数据库加载用户基本信息
     *
     * @param uid
     * @return
     */
    UserSimpleDTO getDbUserSimpleInfo(Long uid);

    /**
     * 从数据库加载用户基本信息列表
     *
     * @param uidList
     * @return
     */
    List<UserSimpleDTO> getDbUserSimpleInfoList(List<Long> uidList);

    /**
     * 根据用户昵称获取用户ID列表
     *
     * @param userName
     * @return
     */
    List<Long> getUserIdListByName(String userName);

    /**
     * 从数据库加载用户基本信息
     *
     * @param memberCode
     * @return
     */
    List<UserSimpleDTO> getDbUserByMemberCode(List<String> memberCode);

    List<UserSimpleDTO> getUserIdListByOpenids(List<String> openids, Long tenantId);

    void exportToken(HttpServletResponse response);
}
