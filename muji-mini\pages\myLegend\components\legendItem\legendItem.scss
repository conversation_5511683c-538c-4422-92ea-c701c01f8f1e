.legend-item {
  box-sizing: border-box;
  padding-top: 40rpx;
  padding-bottom: 21rpx;
  border-bottom: 1rpx solid #EEEEEE;
  margin: 0 var(--page-margin);

  // &:first-child {
  //   border-top: 1rpx solid #EEEEEE;
  // }

  .item-title {
    view {
      flex: 1;
      height: 40rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28rpx;
      color: #3C3C43;
      line-height: 40rpx;
      font-style: normal;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .flex-box {
    display: flex;
    justify-content: space-between;
  }

  .info {
    margin-top: 20rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #888888;
    line-height: 33rpx;
    text-align: left;
    font-style: normal;

    view:first-child {
      margin-bottom: 10rpx;
    }
  }


}
