<template>

  <div class="login-box">
    <img style="position: absolute;top: 42px;left: 42px;width: 28px;height: 28px;cursor: pointer;" @click="Cancel" src="@/assets/images/fanhui.png" alt="">
    <a-layout class="layout" style="height: 100vh;    display: flex;
    align-items: center;
    justify-content: center;">

      <div class="login-content">
        <div class="login-left login-form">
          <div class="login-left-box">
            <div class="login-left-box-title">{{ typeTitle == 1 ? '忘记' : '修改' }}{{ formTaxt }}</div>

          </div>
          <!-- :items="[
      {
        title: '账号验证',
        }, { title: '重置密码' ,  }, { title: '完成' , }, ]"  status="waiting" -->
          <!-- <a-steps :current="current" class="steps" status="waiting">

            <a-step title="账号验证" :progressDot="true">
              <template v-slot:progressDot="{index, status, title, description, prefixCls, iconDot}">

              </template>

</a-step>
<a-step title="重置密码">
  <template v-slot:progressDot="{index, status, title, description, prefixCls, iconDot}">

                <div style="width: 30px; height: 30px; border-radius: 50%; border: 1px solid;display: flex;align-items: center;justify-content: center;background-color:transparent;">
                  {{index+1}}
                </div>
              </template>
</a-step>
<a-step title="完成">
  <template v-slot:progressDot="{index, status, title, description, prefixCls, iconDot}">

                <div style="width: 30px; height: 30px; border-radius: 50%; border: 1px solid;display: flex;align-items: center;justify-content: center;background-color:transparent;">
                  {{index+1}}
                </div>
              </template>
</a-step>

</a-steps> -->
          <!-- <div class="steps">
            <ul class="steps-container">
              <li class="steps-container-li">
                <div
                  :class="[current + 1 == 1 || current + 1 == 2 || current + 1 == 3 ? 'steps-container-li-active' : '', 'steps-container-li-number']">
                  1
                </div>
                <div
                  :class="[current + 1 == 1 || current + 1 == 2 || current + 1 == 3 ? 'steps-container-li-active' : '', 'steps-container-li-title']"
                  style="width:45px">账号验证</div>

              </li>
              <div
                :class="['steps-container-line', current + 1 == 1 || current + 1 == 2 || current + 1 == 3 ? 'steps-container-line-active' : '',]">
              </div>
              <li class="steps-container-li">
                <div :class="[current + 1 == 2 || current + 1 == 3 ? 'steps-container-li-active' : '', 'steps-container-li-number']">
                  2
                </div>
                <div :class="[current + 1 == 2 || current + 1 == 3 ? 'steps-container-li-active' : '', 'steps-container-li-title']"
                  style="width:45px">重置密码</div>

              </li>
              <div :class="['steps-container-line', current + 1 == 2 || current + 1 == 3 ? 'steps-container-line-active' : '',]">
              </div>
              <li class="steps-container-li">
                <div :class="[current + 1 == 3 ? 'steps-container-li-active' : '', 'steps-container-li-number']">
                  3
                </div>
                <div :class="[current + 1 == 3 ? 'steps-container-li-active' : '', 'steps-container-li-title']">完成</div>
              </li>
            </ul>
          </div> -->
          <!-- <div class="login-left-box-desc" v-show="current === 0">
            <template v-for="(item, index) in typeTitle == 1 ? btnList1 : btnList2" :key="index">
              <a-button type="primary" class="login-btn" @click="changeBtu(item)" :style="{ width: `${item.width}px`, height: '39px', borderRadius: '10px', padding: 0 }" v-if="item.value == currentBtn">{{ item.label }}</a-button>
              <a-button class="login-btn" type="primary" ghost :style="{ width: `${item.width}px`, height: '39px', borderRadius: '10px', padding: 0 }" @click="changeBtu(item)" v-else>{{ item.label }}</a-button>
            </template>

          </div> -->
          <div v-if="current === 0">
            <!-- 验证码和验证密码 -->
            <a-form ref="phoneForm" :model="phoneObj" :rules="currentBtn == 1 ? rulesPhone : rulesEilter">
              <template v-if="currentBtn == 1 || currentBtn == 2">
                <a-form-item label="" name="address">
                  <a-input v-model:value="phoneObj.address" :maxlength="currentBtn == 1 ? 11 : 60" allowClear :placeholder="currentBtn == 1 ? '请输入手机号' : '请输入邮箱'">
                    <template #prefix>

                      <MailOutlined v-if="currentBtn == 2" style="font-size: 14px" />
                      <TabletOutlined v-if="currentBtn == 1" style="font-size: 14px" />
                    </template>

                  </a-input>
                </a-form-item>
                <a-form-item name="code" label="">
                  <div style="display:flex;align-items:center">
                    <a-input v-model:value="phoneObj.code" placeholder="请输入验证码" allowClear :maxlength="6">
                      <template #prefix>
                        <SafetyCertificateOutlined style="font-size: 14px" />
                      </template>
                    </a-input>
                    <a-button class="login-btn" type="primary" style="margin-left: 10px;height: 39px;border-radius: 10px;" :disabled="isVerifying || isSending || countdown > 0" @click="sendCode">
                      <template v-if="countdown > 0">
                        {{ countdown }} 秒后重新发送
                      </template>
                      <template v-else>
                        发送验证码
                      </template>
                    </a-button>
                  </div>
                </a-form-item>
              </template>
              <a-form-item label="" name="oldPassword" v-if="currentBtn == 0">
                <a-input-password v-model:value="phoneObj.oldPassword" :maxlength="30" allowClear :placeholder="'密码'">
                  <template #prefix>
                    <UnlockOutlined style="font-size: 14px" />
                  </template>
                </a-input-password>
              </a-form-item>
              <div style="height: 68px;width:100%" v-if="currentBtn == 0"></div>
              <a-form-item label="">
                <a-button type="primary" :loading="loading" class="login-btn" style="width: 100%;height: 42px; border-radius: 10px;" @click="nextStep">下一步</a-button>
              </a-form-item>
            </a-form>
          </div>
          <div v-if="current === 1">
            <!-- 修改密码 -->
            <a-form ref="passwordForm" :model="passwordObj" :rules="rulesPassword">
              <a-form-item label="" name="oldPassword">
                <a-input-password type="password" autocomplete="off" :maxlength="30" allowClear v-model:value="passwordObj.oldPassword" placeholder="请输入旧密码">
                  <template #prefix>
                    <UnlockOutlined style="font-size: 14px" />
                  </template>

                </a-input-password>
              </a-form-item>
              <a-form-item label="" name="newPassword1">
                <a-input-password type="password" autocomplete="off" :maxlength="30" allowClear v-model:value="passwordObj.newPassword1" placeholder="请输入密码">
                  <template #prefix>
                    <UnlockOutlined style="font-size: 14px" />
                  </template>

                </a-input-password>
              </a-form-item>
              <a-form-item label="" name="newPassword">
                <a-input-password type="password" autocomplete="off" :maxlength="30" allowClear v-model:value="passwordObj.newPassword" placeholder="请再次输入密码">
                  <template #prefix>
                    <UnlockOutlined style="font-size: 14px" />
                  </template>
                </a-input-password>
              </a-form-item>
              <a-form-item label="">
                <a-button type="primary" :loading="loading" class="login-btn" style="width: 100%;height: 42px; border-radius: 10px;" @click="resetPassword">下一步</a-button>
              </a-form-item>
            </a-form>
          </div>
          <!--  -->
          <div class="completion" v-if="current === 2">
            <div class="completion-icon">
              <CheckOutlined style="color: #fff;font-size: 35px;" />
            </div>
            <!-- <CheckOutlined /> -->
            <!-- <CheckCircleOutlined theme="filled" style="font-size: 80px; background-color: #6A6BBF;border-radius: 50%;padding: 20px;margin-top: 100px;" /> -->
            <div class="completion-title">密码已成功重置</div>
            <a-button type="primary" :loading="loading" class="login-btn" style="width: 100%;height: 42px; border-radius: 10px;" @click="handleCancel">登录（{{ logoDown }}）</a-button>
          </div>
        </div>
        <div class="login-right">
          <img src="@/assets/images/denglu.png" alt="">
        </div>
      </div>

    </a-layout>

  </div>

</template>

<script setup>
import { ref, watch } from 'vue';
import { CheckCircleOutlined } from '@ant-design/icons-vue';
import { message, Modal } from "ant-design-vue";
import { password_update } from '@/http/index';
import { checkEmail, checkMobile } from '@/utils/validate'
import { useRoute, useRouter } from 'vue-router'
import { useGlobalStore } from '@/store'
const GuserInfo = useGlobalStore()
import setting from '../../config/defaultSettings'
import { encrypt, decrypt } from '@/utils/jsencrypt'

const $router = useRouter()
const { query } = useRoute()

// const passwordCheck = (rule, value, callBack) => {
//   let pattern = /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\\W_!@#$%^&*]+$)(?![a-z0-9]+$)(?![a-z\\W_!@#$%^&*]+$)(?![0-9\\W_!@#$%^&*]+$)[a-zA-Z0-9\\W_!@#$%^&*]{15,30}$/;
//   /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\\W_!@#$%^&*]+$)(?![a-z0-9]+$)(?![a-z\\W_!@#$%^&*]+$)(?![0-9\\W_!@#$%^&*]+$)[a-zA-Z0-9\\W_!@#$%^&*]{15,30}$/;
//   if (!passwordObj.value.password.length) {
//     return Promise.reject('请输入新的登录密码')
//   } else if (passwordObj.value.password.length < 15) {
//     return Promise.reject('登录密码至少15位')
//   } else if (passwordObj.value.password.length > 30) {
//     return Promise.reject('登录密码最多30位')
//   } else if (!pattern.test(passwordObj.value.password)) {
//     return Promise.reject('密码需要包含大写，小写，数字，特殊字符(_!@#$%^&*)至少三项类型')
//   } else {
//     return Promise.resolve()
//   }
// }
const pwdRegex1 = new RegExp('(?=.*[0-9]).{6,16}')// 纯数字
const pwdRegex2 = new RegExp('(?=.*[A-Za-z]).{6,16}')// 字母
const validatePass1 = (rule, value, callback) => {
  console.log("🚀 ~ validatePass1 ~ value:", value)
  if (/\s/.test(value)) return callback(new Error('密码不能包含空格'))
  if (value === passwordObj.value.oldPassword) return callback(new Error('重置的新密码需不同于原密码!'))
  let pwdCount = 0// 统计密码包含了几种字符
  if (pwdRegex1.test(value)) {
    pwdCount++
  }
  if (pwdRegex2.test(value)) {
    pwdCount++
  }
  // if (pwdRegex3.test(value)) {
  //   pwdCount++
  // }
  if (pwdCount >= 2) {

    callback()
  } else {
    return callback(new Error('新密码必须包含字母及数字且在6-16位'))
  }
}
const passwordCheckAgain = (rule, value, callback) => {
  if (passwordObj.value.newPassword !== passwordObj.value.newPassword1) {
    callback(new Error('两次输入密码不一致!'))
  } else {
    callback()
  }
}
const btnList2 = ref([
  // {
  //   label: '手机号验证',
  //   type: 'primary',
  //   value: 1,
  //   width: 84
  // },
  // {
  //   label: '邮箱验证',
  //   type: 'primary',
  //   value: 2,
  //   width: 84
  // },
  {
    label: '账号验证',
    type: 'primary',
    value: 0,
    width: 84
  },
]);
const btnList1 = ref([
  {
    label: '手机号验证',
    type: 'primary',
    value: 1,
    width: 137
  },
  {
    label: '邮箱验证',
    type: 'primary',
    value: 2,
    width: 137
  },

]);
const loading = ref(false);
const currentBtn = ref(0);
const formTaxt = ref('密码');
const typeTitle = ref(1); //1忘记密码 2修改密码
const changeBtu = (row) => {
  phoneForm.value?.resetFields();
  passwordForm.value?.resetFields();
  resetCountdown();
  currentBtn.value = row.value

}

watch(() => query, (value) => {
  console.log();
  console.log(value, 'valuevalue');
  console.log(value, 'valuevalue');
  if (value && value.typeTitle)
    typeTitle.value = value.typeTitle
}, {
  deep: true,
  immediate: true,
},

)
const rulesEilter = ref({
  address: [
    { required: true, message: '请输入邮箱', trigger: 'blur', },
    { validator: checkEmail, trigger: ['blur', 'change'] }
  ],
  oldPassword: [
    { required: true, message: '请输入旧密码', trigger: ['blur', 'change'] }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: ['blur', 'change'] }
  ]
})
const rulesPhone = ref({
  address: [
    { required: true, message: '请输入手机号', trigger: 'blur', }, //checkMobile
    { validator: checkMobile, trigger: ['blur', 'change'] }
  ],
  oldPassword: [
    { required: true, message: '请输入旧密码', trigger: ['blur', 'change'] }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' }
  ]
})
//   
const rulesPassword = ref({
  oldPassword: [
    { required: true, message: '请输入旧密码', trigger: 'blur' },
  ],
  newPassword1: [
    { required: true, validator: validatePass1, trigger: 'blur' }
  ],
  newPassword: [
    { required: true, validator: passwordCheckAgain, trigger: 'blur' }
  ]
})

const emit = defineEmits(['update:visible']);
const phoneObj = ref({
  address: "",
  code: ""
})
const passwordObj = ref({

  oldPassword: '',
  newPassword: '',
  newPassword1: ''

})
const phoneForm = ref(null);
const passwordForm = ref(null);
const isVerifying = ref(false);
const isSending = ref(false);
const current = ref(1);
const countdown = ref(0);
const logoDown = ref(3)
const intervalId = ref(null);
let timer = null;
// 组件卸载前清除定时器
onUnmounted(() => {
  clearInterval(intervalId.value);

});
const startlogoCountdown = () => {
  clearInterval(intervalId.value);
  intervalId.value = setInterval(() => {
    if (logoDown.value > 0) {
      logoDown.value--;
    } else {
      clearInterval(intervalId.value);
      // 倒计时结束，执行跳转
      jumpToHome();
    }
  }, 1000);
};
function jumpToHome() {
  if (typeTitle.value == 1) {
    $router.back();
  } else {
    GuserInfo.logout()
    //  $router.();
  }
}
function Cancel() {
  $router.back();
}
const handleCancel = () => {
  if (intervalId.value) {
    clearInterval(intervalId.value);
  }

  if (typeTitle.value == 1) {
    $router.back();
  } else {
    GuserInfo.logout()
    //  $router.();
  }

  // emit('update:visible', false);
  resetCountdown();
};



const nextStep = () => {

  phoneForm.value.validate().then(res => {
    loading.value = true;
    let data = {}
    switch (currentBtn.value) {
      case 1:
        data = { address: phoneObj.value.address, type: currentBtn.value, code: phoneObj.value.code }

        break;
      case 2:
        data = { address: phoneObj.value.address, type: currentBtn.value, code: phoneObj.value.code }

        break
      case 0:

        data = { oldPassword: encrypt(phoneObj.value.oldPassword), type: currentBtn.value, loginAccount: GuserInfo.userInfo.loginAccount }
        break
    }
    passwordResetcheckCode(data).then(res => {
      current.value += 1;
    }).catch(err => {

    }).finally(() => {
      loading.value = false;
    })




  }).catch(err => {
    console.log(err, 'www');

  })

};

const resetPassword = () => {// password_update
  passwordForm.value.validate().then(res => {
    console.log(res, '2222222');
    if (passwordObj.value.newPassword === passwordObj.value.newPassword1) {
      let data = {}
      if (currentBtn.value == '0') {
        data = {
          username: GuserInfo.userInfo.usernamae,
          oldPassword: encrypt(passwordObj.value.oldPassword),
          newPassword: encrypt(passwordObj.value.newPassword)
        }
      } else {
        data = {
          address: phoneObj.value.address, type: currentBtn.value, code: phoneObj.value.code,
          password: encrypt(passwordObj.value.password)
        }
      }

      password_update(data).then(res => {

        current.value += 1;
        startlogoCountdown()
      }

      ).catch(err => {
        // message.error('重置密码失败，请稍后再试');
      }).finally(() => {
        loading.value = false;
        // current.value = 2;
      })
    } else {
      // message.error('密码不一致，请重新输入');
    }


  }).catch(err => {
    console.log(err, 'www');

  })

};

const sendCode = async () => {
  switch (currentBtn.value) {
    case 1:
      if (!phoneObj.value.address) {
        message.warning('请输入手机号');
        return;
      }
      break;
    case 2:
      if (!phoneObj.value.address) {
        message.warning('请输入邮箱');
        return;
      }
      break;
    case 0:
      if (!phoneObj.value.oldPassword) {
        message.warning('请输入旧密码');
        return;
      }
      break;
  }

  isSending.value = true;

  try {
    // 模拟发送验证码的后端逻辑
    // await new Promise(resolve => setTimeout(resolve, 1000)); // 假设发送验证码需要1秒
    await passwordResetgetCode({ address: phoneObj.value.address, type: currentBtn.value }).then(res => {
      // 模拟验证码发送成功
      message.success(`验证码已发送`);
      // 启动倒计时
      startCountdown();

    })


  } catch (error) {
    // console.error('发送验证码失败:', error);
    // message.error('发送验证码失败');
  } finally {
    isSending.value = false;
  }
};

const startCountdown = () => {
  countdown.value = 60; // 设置倒计时时长（秒）

  // 启动定时器，每秒更新倒计时
  timer = setInterval(() => {
    countdown.value -= 1;
    if (countdown.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);
};

const resetCountdown = () => {
  clearInterval(timer);
  countdown.value = 0;
};
</script>

<style scoped lang="scss">
.steps {
  margin-bottom: 56px;

  .steps-container {
    display: flex;
    align-items: center;

    .steps-container-li-number {
      width: 22px;
      height: 22px;
      border-radius: 50%;
      font-size: 11px;
      border: 1px solid;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: transparent;
    }

    .steps-container-line {
      width: 26px;
      height: 1px;
      background: rgba(0, 0, 0, 0.5);
      margin: 0 11px;

      &-active {
        background: #6a6bbf;
      }
    }

    .steps-container-li {
      display: flex;
      align-items: center;

      &-active {
        color: #6a6bbf;
      }
    }

    .steps-container-li:last-child {
      width: 85px;
    }

    .steps-container-li-title {
      margin-left: 8px;
      font-size: 11px;
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      line-height: 17px;
      line-height: 24px;
    }
  }
}

:deep(.ant-modal-title) {
  text-align: center;
}

.completion {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding-top: 59px;
}

.completion-icon {
  width: 56px;
  height: 56px;
  background-color: #6a6bbf;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.login-box {
  position: relative;
  height: 100%;
  width: 100%;
}

.login-content {
  background: #ffffff;
  border-radius: 14px;
  overflow: hidden;
  max-height: 462px;
  display: flex;

  .login-left-box-desc {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
  }

  .login-right {
    width: 392px;
    height: 462px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .login-form {
    background: #fff;
    border: 1px solid #fff;
    border-radius: 5px;
    width: 378px;
    padding: 42px 41px 55px 42px;

    .login-title {
      height: 52px;
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 500;
      font-size: 36px;
      color: #3d3d3d;
      line-height: 52px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 80px;
    }

    .login-left-box {
      display: flex;
      margin-bottom: 28px;

      .login-left-box-title {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 500;
        font-size: 22px;
        color: #3d3d3d;
        line-height: 32px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }
  }
}

.statement {
  li {
    margin-left: 50px;
    list-style: none;
    font-size: 12px;
    margin-bottom: 5px;
  }

  a {
    color: #000;
  }
}

:deep(.ant-input-affix-wrapper .ant-input-clear-icon) {
  font-size: 20px;
}

.tips-password {
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 16px !important;
  color: rgba(0, 0, 0, 0.5);
  line-height: 23px;
  padding-top: 8px;
}

:deep(.ant-input-suffix) {
  font-size: 14px !important;
}

:deep(.ant-input-affix-wrapper .ant-input-prefix) {
  margin-inline-end: 6px;
}

.login-btn {
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 14px;
}

.layout {
  background: $theme-login-bg-color;
  background-size: 100% 100%;
}

:deep(.ant-form-item) {
  margin-bottom: 20px;
}

:deep(.ant-form-item:nth-child(2)) {
  margin-bottom: 27px;
}

:deep(.ant-steps .ant-steps-item-wait .ant-steps-item-icon) {
  background-color: transparent !important;
}

.completion-title {
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 500;
  font-size: 14px;
  color: #3d3d3d;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-top: 14px;
  margin-bottom: 36px;
}

:deep(.ant-form-item:nth-child(4)) {
  margin-bottom: 0;
}

:deep(.ant-form-item .ant-form-item-explain-error) {
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 11px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

:deep(.ant-input) {
  line-height: 100%;
  font-size: 11px !important;
}

:deep(.ant-input-affix-wrapper) {
  border-radius: 14px;
}

:deep(.ant-input::placeholder) {
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 11px;
  color: rgba(0, 0, 0, 0.5);
  text-align: left;
  font-style: normal;
  text-transform: none;
}
</style>
