/* signUp/pages/announcement/announcement.wxss */
.title {
  padding-top: 23rpx;
  margin-bottom: 63rpx;
  font-family: MUJIFont2020,
    SourceHanSansCN;
  font-weight: 700;
  font-size: 32rpx;
  color: #3C3C43;
  line-height: 47rpx;
  letter-spacing: 1rpx;
  text-align: center;
  font-style: normal;
  text-transform: none;
}

.content {
  position: relative;
  padding-bottom: 1rpx;


  // margin-left: 51rpx;
  // margin-right: 94rpx;
  .title1 {
    margin-left: 84rpx;
    font-family: MUJIFont2020,
      SourceHanSansCN;
    font-weight: 700;
    font-size: 32rpx;
    color: #3C3C43;
    line-height: 46rpx;
    text-align: left;
    margin-bottom: 20rpx;
  }
}

.bottom {
  flex-shrink: 0;
  height: env(safe-area-inset-bottom);
}

.table_scroll {
  position: relative;
  margin-bottom: 90rpx;

  &:after {
    position: absolute;
    left: 50%;
    bottom: 0;
    z-index: 1;
    content: '';
    // margin-left: -243rpx;
    transform: translateX(-50%);
    width: 549rpx;
    height: 1rpx;
    background-color: #979797;
  }
}

.page-container {
  .announcement {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    background-color: #FBF8F3;

    .box {


      .title-Time {
        margin: 0 86rpx;
        margin-bottom: 30rpx;
        font-family: MUJIFont2020, SourceHanSansCN;
        font-weight: 500;
        font-size: 24rpx;
        color: #3C3C43;
        line-height: 35rpx;
        // text-align: right;
        font-style: normal;
        text-transform: none;
      }

      .title-content {
        margin: 0 86rpx;
        margin-bottom: 10rpx;
        font-family: MUJIFont2020,
          SourceHanSansCN;
        font-weight: 700;
        font-size: 24rpx;
        color: #3C3C43;
        line-height: 35rpx;
        font-style: normal;
        text-transform: none;
      }

      .tips {
        margin: 0 88rpx;
        padding: 0 3rpx;
        margin-bottom: 30rpx;
        font-family: MUJIFont2020, SourceHanSansCN;
        font-weight: 500;
        font-size: 20rpx;
        color: #3C3C43;
        line-height: 35rpx;
        text-align: justify;
        font-style: normal;
        text-transform: none;
      }

      .table {
        // width: 100%;
        margin: 0 78rpx;
        height: auto;
        box-sizing: border-box;
        position: relative;

        // display: inline-block;

        .head {
          position: sticky;
          top: 0;
          z-index: 999;
          display: grid;

          /* display: grid; 网格布局 */
          /* grid-auto-flow 属性控制自动放置的项目如何插入网格中 */
          /* column	通过填充每一列来放置项目 */
          grid-auto-flow: column;

          .table_th {
            display: flex;
            justify-content: center;

            .table_td {
              display: flex;
              align-items: center;
              justify-content: center;
              min-width: 145rpx;
              height: 61rpx;
              // padding: 0rpx 20rpx;
              // width: 200rpx;
              border-top: 1rpx solid #979797;
              border-bottom: 1rpx solid #979797;
              border-left: 1rpx solid #979797;
              background: #FBF8F3;
              // box-sizing: border-box;
              font-weight: 700;
              font-size: 20rpx;
              color: #3C3C43;
              // line-height: 37rpx;

              &:nth-last-child(1),
              &:nth-last-child(2) {
                width: 202rpx;
                border-right: 1rpx solid #979797;
              }

            }
          }
        }

        .tbale_body {

          .table_tr {
            display: flex;
            justify-content: center;

            .table_body_td {
              // padding: 10rpx 20rpx;
              min-width: 145rpx;
              display: flex;
              align-items: center;
              justify-content: center;
              // box-sizing: border-box;
              height: 61rpx;
              border-bottom: 1rpx solid #979797;
              border-left: 1rpx solid #979797;
              font-weight: 350;
              font-size: 20rpx;
              color: #3C3C43;
              // line-height: 37rpx;

              &:nth-last-child(1),
              &:nth-last-child(2) {
                width: 202rpx;
                border-right: 1rpx solid #979797;
              }
            }
          }
        }
      }
    }
  }
}
