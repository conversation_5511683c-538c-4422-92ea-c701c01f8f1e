package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 省份信息
 * @author: Handy
 * @date:   2023/03/15 13:36
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table("省份信息")
@TableName(value = "province_info")
public class ProvinceInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR,length = 36,isNull = true,comment = "省份编码")
    private String provinceCode;
    @Columns(type = ColumnType.VARCHAR,length = 64,isNull = true,comment = "省份名称")
    private String provinceName;

    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true ,defaultValue = "0", comment = "是否有门店 0否 1是")
    private Integer isStore;

    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;

    public ProvinceInfo(Long id, String provinceCode, String provinceName) {
        this.id = id;
        this.provinceCode = provinceCode;
        this.provinceName = provinceName;
    }

}
