<template>
  <div class="all-level">
    <div class="first-level" :class="collapsed ? 'shouqi' : 'zhankai'">
      <div class="title-all" @click="toIndex">

        <SvgIcon :name="logoName" width="54" border-radius="10px" height="54"></SvgIcon>

        <div class="title-text" v-show="!collapsed">
          <p class="title1">{{ settings.brand }}</p>
          <p class="title2">小程序管理平台</p>
        </div>
      </div>
      <!-- <div class="search-all" v-if="!collapsed">
        <SvgIcon class="icon" name="menu-search" width="24" height="24" />
        <a-select class="search-ipt" v-model:value="menuName" show-search placeholder="功能搜索" style="width: 200px" :options="options" :filter-option="false" @search="getMenuSearch" @change="goPage">
        </a-select>
      </div> -->
      <!-- <div class="search1" v-else @click="unfoldMenu">
        <SvgIcon name="fold-search" width="40" height="40" />
      </div> -->
      <!-- 一级菜单循环 -->
      <ul id="first-ul" class="first-ul hide-scrollbar" :style="{ height: `${heightValue}px` }">
        <li v-for="(item, index) in sliderRoute" :key="item.id" class="first-li" :class="index == firstIndex ? 'active' : ''" @click="selectFirst(item, index, 0, 0, 'dianji')">
          <p :class="['first-p', collapsed == true ? 'first-p-active' : '']">
            <a-popover placement="right" v-if="collapsed">
              <template #content>
                <p class="pop-p">{{ item.meta.title }}</p>
              </template>
              <span>
                <SvgIcon :name="item.meta.icon" width="20" :color="index == firstIndex ? '#383838' : '#8d8c91'" height="20">
                </SvgIcon>
              </span>

            </a-popover>
            <SvgIcon v-if="!collapsed" :name="item.meta.icon" width="20" :color="index == firstIndex ? '#383838' : '#8d8c91'" height="20"></SvgIcon>
            <span v-if="!collapsed">{{ item.meta.title }}</span>
          </p>
          <img class="img1" src="@/assets/images/bg1.png" alt="" v-show="index == firstIndex" />
          <img class="img2" src="@/assets/images/bg2.png" alt="" v-show="index == firstIndex" />

        </li>
      </ul>
      <div @click="unfoldMenu" class="up-icon">
        <SvgIcon name="menu-packup" width="40" height="40" v-if="!collapsed" />
        <SvgIcon name="menu-unfold" width="40" height="40" v-else />
      </div>
    </div>
    <!-- v-show="sliderRouteTwo" -->
    <div class="second-level" v-if="sliderRouteTwo.length > 0">
      <ul class="second-ul">
        <!-- 二级菜单循环 -->
        <li v-for="(itemChild, k) in sliderRouteTwo" :key="itemChild.name" class="second-li" :class="((k == secondIndex)) ? 'active' : ''" @click="selectTwo(itemChild, k)">
          <p class="p-li two-li">

            <span class="tit-mete" :title="itemChild.meta.title">{{ itemChild.meta.title }}</span>
            <span v-if="itemChild.isShow" class="icon-meta">
              <SvgIcon name="fold-icon" width="12" height="12" />
            </span>
            <span v-else class="icon-meta">
              <SvgIcon name="unfold-icon" width="12" height="12" />
            </span>
          </p>
          <!-- ((k == secondIndex)) || showAll -->
          <ul class="three-ul" v-show="itemChild.isShow">
            <!-- 三级菜单循环 -->

            <li class="three-li" v-for="(itemChild2, n) in itemChild.children" :key="itemChild2.name" :class="secondIndexFlag == k && n == threeIndex ? 'active' : ''" @click.stop="getMenuInfo(itemChild, itemChild2, k, n)">
              <!-- {{secondIndexFlag }} =={{ k}} -- {{ n }} --{{ threeIndex}} -->
              <p class="p-li">
                <SvgIcon name="select-icon" width="12" height="12" v-if="secondIndexFlag == k && n == threeIndex" />
                <span v-else class="kong"></span>
                <span class="tit-mete" :title="itemChild2.meta.title">{{ itemChild2.meta.title }}</span>
              </p>
              <!-- {{secondIndexFlag}}-{{k}} -- {{n}} --{{threeIndex}} -->
            </li>
          </ul>
        </li>
      </ul>
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue'
import NavTop from '@/components/NavTop/index.vue'
import defaultSettings from '@/config/defaultSettings'
const emit = defineEmits(["widthChange", "menuInfo", "hasSecondChange"]);//定义可调用的父组件方法名
import { getMenuTree } from '@/http/index.js'
// import { debounce } from 'lodash-es';
import { debounce } from '@/utils/common'

// import SvgIcon from "@/components/svg-icon";
// import { ref, reactive, effect, watch, onMounted, nextTick } from 'vue';
// import { useRoute, useRouter } from 'vue-router'
// import { permitsRoutes } from '@/router/index.js'
import router from "@/router/index.js";
// import screenfull from 'screenfull'
// import img from '@/assets/logo.png'
// import { loginout } from '../http/index.js'
import { useGlobalStore } from '@/store'
// import SubMenu from '@/components/subMenu.vue'
// import '@/assets/scss/globle.scss';// 全局全局样式

const global = useGlobalStore()
// const $route = useRoute()
const $router = useRouter()
let sliderRoute = ref([]) // 侧边栏导航数据
let sliderRouteTwo = ref([])
let options = ref([])// 高亮菜单初始化
let openMenu = ref([]) // 侧边栏展开数据
let collapsed = ref(false)  // 默认收起
let menuName = ref(null)
let modeMenu = ref('inline')
let firstIndex = ref(null) // 当前选择的一级菜单
let onePath = ref('')
let twoPath = ref('')
let oneItem = ref({})
let twoItem = ref({})
let secondIndex = ref(null) //当前选择的二级菜单
let filterOption = ref([])
let secondIndexFlag = ref(null)
let oneIndexFlag = ref(null)
let threeIndex = ref(null) //当前选择的三级菜单
let showAll = ref(true)
let settings = ref({
  brand: defaultSettings.brand,
  // 布局类型
  layout: defaultSettings.layout, // 'sidemenu', 'topmenu'
  // CONTENT_WIDTH_TYPE
  // contentWidth: defaultSettings.layout === 'sidemenu' ? CONTENT_WIDTH_TYPE.Fluid : defaultSettings.contentWidth,
  // 主题 'dark' | 'light'
  theme: defaultSettings.navTheme,
  // 主色调
  primaryColor: defaultSettings.primaryColor,
  fixedHeader: defaultSettings.fixedHeader,
  fixSiderbar: defaultSettings.fixSiderbar,
  colorWeak: defaultSettings.colorWeak,
  hideHintAlert: false,
  hideCopyButton: false
})

// 标品
// TODO: 优化项 index.html和这里引入统一文件
const logoName = ref(import.meta.env.VITE_LOGO_NAME)
// console.log('图标', import.meta.env.VITE_LOGO_NAME)
// console.log(logoName.value, '测试图标')

let heightValue = ref(null)

const windowWidth = ref(window.innerWidth);
const windowHeight = ref(window.innerHeight);

let handleResize = () => {
  windowWidth.value = window.innerWidth;
  windowHeight.value = window.innerHeight;
  // 216
  heightValue.value = window.innerHeight - 190
}

const state = reactive({
  data: [],
  value: [],
  fetching: false,
});

let lastFetchId = 0;
onMounted(() => {
  unfoldMenu()
  getMenu()


  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
handleResize()
let currentMenu = ref({})

// watch(() => $router.path, () => getTwoIndex())
// 监听路由选中左侧菜单
watch(
  () => $router.currentRoute.value,
  (newValue) => {
    currentMenu.value = newValue
    var arr = newValue.path.split("/"); // 使用逗号作为分隔符进行分隔
    global.menus.forEach((val, index) => {
      if (val.name.toLowerCase() === arr[1].toLowerCase()) {
        firstIndex.value = index
        oneIndexFlag.value = index
        // 二级菜单重新赋值
        sliderRouteTwo.value = val.children

        if (val.children.length > 0) {
          val.children.forEach((two, key) => {

            if (two.name.toLowerCase() === (arr[2].toLowerCase())) {
              secondIndex.value = key
              secondIndexFlag.value = key
              if (two.children && two.children.length) {
                two.children.forEach((three, m) => {
                  if (three.name.toLowerCase() === (arr[3].toLowerCase())) {
                    threeIndex.value = m

                  }
                })
              }

            }
          })
        }
      }
    });
    // console.log("🚀 ~ global.menus.forEach ~  sliderRouteTwo.value:", sliderRouteTwo.value)
    // console.log(newValue, 'newValue')
    if (newValue.path.includes('index/page')) {
      emit('hasSecondChange', false);
    } else {
      emit('hasSecondChange', true);
    }
  },
  { immediate: true }
)



// // 展开侧边栏
let openChange = (openKeys) => {
  openMenu.value = openKeys;
}


// 宽度变化传给父组件
let unfoldMenu = () => {
  collapsed.value = !collapsed.value
  global.setCollapsed(collapsed.value)
  emit('widthChange', collapsed.value);
}

//当前点击的一级菜单
const selectFirst = (item, idx, idx2, idx3, type) => {

  // const allRoutes = $router.getRoutes();
  // console.log("🚀 ~ selectFirst ~ allRoutes:", allRoutes)

  // console.log(allRoutes, 'allRoutesallRoutes');


  firstIndex.value = idx
  // console.log(item, idx, idx2, idx3, type, 'item, idx, idx2, idx3, type,')
  sliderRouteTwo.value = (sliderRoute.value[idx].children)
  // 默认选择第一个  // 点击一级不选择二三级
  secondIndex.value = idx2
  threeIndex.value = idx3
  onePath.value = item.path
  twoPath.value = item.children
  showAll.value = true

  // 清空搜索
  menuName.value = null
  options.value = []
  let allPAth = getAllIds(item.children, [])
  if (type == 'dianji') {
    if (item.path == '/index') {
      $router.push({ path: '/index/seconedindex/threeindex' })

    } else {
      // console.log(item, 'item666', allPAth[0], allPAth[1])
      // item.path && allPAth[0] &&
      if (allPAth[1]) {
        //item.path + '/' + allPAth[0] + "/" +
        $router.push({ path: allPAth[1] })
        emit('hasSecondChange', true);
        //没有二级菜单
      }


    }

  }

}

function getAllIds(tree, result) {
  //遍历树 获取path数组
  for (const i in tree) {
    result.push(tree[i].path); // 遍历项目满足条件后的操作
    if (tree[i].children) {
      //存在子节点就递归
      getAllIds(tree[i].children, result);
    }
  }
  return result;
}

// 点击二级菜单
const selectTwo = (item, k) => {
  item.isShow = !item.isShow
  // 切换二级时候清空三级选中
  if (secondIndex.value == k) {
    secondIndex.value = null
  } else {
    secondIndex.value = k
  }
  showAll.value = false


  // 清空搜索
  menuName.value = null
  options.value = []
  getTwoIndex()
}


const getTwoIndex = () => {
  //根据路由判断二级选中
  let newValue = $router.currentRoute.value
  let arrStr = newValue.path.split("/"); // 使用逗号作为分隔符进行分隔
  global.menus.forEach((one, idx1) => {
    one.children?.forEach((two, idx2) => {
      if (two.name.toLowerCase() === (arrStr[2].toLowerCase())) {

        secondIndexFlag.value = idx2

        if (two.children && two.children.length) {
          two.children.forEach((three, idx3) => {

            if (three.name === (arrStr[3].toLowerCase())) {

              threeIndex.value = idx3

            }
          })
        }

      }
    })
  })
}


// 当前点击三级的菜单
const getMenuInfo = (path2, path3, k, n) => {
  threeIndex.value = n
  // console.log("🚀 ~ getMenuInfo threeIndex.value :", threeIndex.value)
  // 跳转到相应的路由并传参给父组件
  // onePath.value && path2.path &&
  // console.log(onePath.value,path2.path, path3.path, '-===')
  if (path3.path) {
    // console.log("🚀 ~ getMenuInfo ~ path3:", path3.path)
    $router.push({ path: path3.path })
    secondIndexFlag.value = k
    // console.log("🚀 ~ getMenuInfo ~ k:", secondIndexFlag.value)
    showAll.value = false

    // 清空搜索
    menuName.value = null
    options.value = []
  }

}

// 获取侧边栏数据
let getMenu = async () => {

  sliderRoute.value = global.menus
  // 默认赋值第一个
  defaultMenu()
}

// 显示右侧菜单
let defaultMenu = () => {
  let newValue = $router.currentRoute.value
  var arr = newValue.path.split("/"); // 使用逗号作为分隔符进行分隔
  global.menus.forEach((val, index) => {
    if (val.name === (arr[1].toLowerCase())) {
      oneItem.value = val
      twoItem.value = oneItem.value.children[0]
      firstIndex.value = index
      if (val.children.length > 0) {
        val.children.forEach((two, key) => {
          if (two.name.toLowerCase() === (arr[2].toLowerCase())) {
            secondIndex.value = key
            if (two.children && two.children.length) {
              two.children.forEach((three, m) => {
                if (three.name.toLowerCase() === (arr[3].toLowerCase())) {
                  threeIndex.value = m
                }
              })
            }
          }
        })
      }
    }
  });

  if ((firstIndex.value != '0') || secondIndex.value != '0' || threeIndex.value != '0') {
    selectFirst(oneItem.value, firstIndex.value, secondIndex.value, threeIndex.value, 'shuaxin')
  } else {
    selectFirst(oneItem.value, 0, 0, 0, 'shuaxin')
  }
  showAll.value = true


}

const getMenuSearch = debounce(value => {
  lastFetchId += 1;
  const fetchId = lastFetchId;
  options.value = [];
  state.fetching = true;


  if (value) {
    getMenuTree({ menuName: value, menuType: 'C' })
      .then((res) => {
        if (fetchId !== lastFetchId) {
          return;
        }
        const data = res.data.map(user => ({
          label: `${user.menuName}`,
          value: `${user.parentPath},${user.path}`,
        }));
        options.value = data;
      })
  } else {
    options.value = []
  }

}, 10);

watch(() => menuName.value, () => {
  options.value = [];
  state.fetching = false;
});


const goPage = (val) => {
  let urlStr = val.split(",")
  $router.replace({ path: '/' + urlStr[1] + '/' + urlStr[0] + '/' + urlStr[2] })

}
const addNavTopRef = ref()

const toIndex = () => {
  // console.log(global.menus, '首页路径')
  // 找出 meta.title 为 '首页' 的对象 跳转到首页
  const result = global.menus.filter(item => item.meta && item.meta.title === '首页');
  if (result.length > 0) {
    const firstMatch = result[0];
    if (firstMatch.name) {
      $router.push({ name: firstMatch.name })
    } else {
      // console.error('找不到有效的路由名称');
    }
  } else {
    let ToName = findFirstChildrenName(global.menus)
    // 没有的话默认跳转到这里
    $router.push({ name: ToName })
  }
  emit('hasSecondChange', false);
}
// 递归查找当前数据的第一条子级，并返回最后一级的 name
const findFirstChildrenName = (data) => {
  // 如果当前数据为空，返回 null
  if (!data || data.length === 0) {
    return null;
  }

  const firstChild = data[0]; // 获取第一条子级数据

  // 如果没有第一条子级，返回 null
  if (!firstChild) {
    return null;
  }

  const children = firstChild.children; // 获取第一条子级的子级数据

  // 如果第一条子级没有子级，直接返回第一条子级的名称
  if (!children || children.length === 0) {
    return firstChild.name;
  }

  // 递归查找第一条子级的子级
  return findFirstChildrenName(children);
};

</script>

<style lang="scss" scoped>
::-webkit-scrollbar {
  background-color: #fff;
  display: none;
}

.sider-zhankai {
  width: 400px !important;
  height: 100%;
  min-width: 400px !important;
  max-width: 400px !important;
  flex: 0 0 400px !important;
}

.sider-shouqi {
  width: 280px !important;
  height: 100%;
  min-width: 280px !important;
  max-width: 280px !important;
  flex: 0 0 280px !important;
}

ul,
li {
  padding: 0;
  margin: 0;
}

.shouqi {
  width: 100px !important;

  .up-icon {
    left: 30px;
    bottom: 20px;
  }
}

.zhankai {
  width: 220px;
  background: #f6f5fd;

  .up-icon {
    left: 150px;
    bottom: 20px;
  }
}

.all-level {
  background: #f6f5fd;
  display: flex;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  z-index: 100;
  padding: 0;
  margin: 0;
}

.first-level {
  position: relative;
  background: #f6f5fd;
  top: 0px;
  left: 0;
  width: 220px;
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  z-index: 100;
  padding: 12px 0 0 0;
  margin: 0;

  li {
    caret-color: transparent;
  }
}

.first-ul {
  padding-top: 8px;
  width: 100%;
  overflow-y: auto;
  list-style: none;
}

.first-li {
  .first-p {
    display: flex;
    align-items: center;
    margin-left: 40px;
    cursor: pointer;
    height: 48px;
    color: rgba(0, 0, 0, 0.85);

    span {
      padding-left: 8px;
      font-size: 16px;
    }
  }

  .first-p-active {
    justify-content: center;
    margin-left: 0;
  }
}

.first-li.active {
  background: #fff;
  position: relative;

  .first-p {
    font-weight: 600;
    height: 48px;
  }

  .img1 {
    position: absolute;
    top: -15px;
    right: 0;
    z-index: 101;
  }

  .img2 {
    position: absolute;
    bottom: -15px;
    right: 0;
    z-index: 101;
  }
}

.second-level {
  margin: 0;
  padding: 24px 0 0 0;
  width: 180px;
  height: 100%;
  background: #fff;
  overflow: hidden;
}

.second-ul {
  list-style: none;
  overflow-y: auto;
  height: 100%;

  .second-li {
    margin: 0 0 12px 0;

    .p-li {
      width: 160px;
      height: 50px;
      margin: 3px auto;
      padding: 0 24px;
      display: flex;
      align-items: center;
      // justify-content: space-between;
      background: #f6f5fd;
      border-radius: 14px;
      color: rgba(0, 0, 0, 0.85);
      caret-color: transparent;
      cursor: pointer;
      .tit-mete {
        white-space: nowrap; /* 确保文本在一行内显示 */
        overflow: hidden; /* 超出容器的文本将被隐藏 */
        text-overflow: ellipsis;
      }
      .icon-meta {
        display: flex;
        align-items: center;
      }
      ::before {
        content: "";
        display: table;
        position: absolute;
      }
    }

    .two-li {
      justify-content: space-between;
      font-size: 16px;
    }
  }

  .active {
    .active {
      .p-li {
        font-weight: 600;
        color: #6a6bbf;
      }
    }
  }
}

.three-ul {
  .three-li {
    position: relative;
    height: 58px;
    display: flex;
    align-items: center;
    justify-content: center;
    caret-color: transparent;
    font-size: 16px;

    .p-li {
      display: flex;
      width: 170px;
      height: 50px;
      margin: 3px auto;
      display: flex;
      align-items: center;
      background: #fff;
      color: rgba(0, 0, 0, 0.5);

      span {
        padding-left: 6px;
      }
    }
  }

  .kong {
    width: 12px;
    height: 12px;
    display: block;
  }

  .active {
    .p-li {
      color: #6a6bbf;
    }
  }
}

:deep(.ant-spin-container),
:deep(.ant-spin-nested-loading) {
  height: 100% !important;
}

.up-icon {
  padding: 0;
  position: fixed;
  bottom: 20px;
  left: 30px;
  cursor: pointer;
}

.ant-layout {
  height: 100%;
}

.title1 {
  font-size: 22px;
  font-weight: 500;
  padding: 24px;
  margin: 0;
}

.icon {
  color: #fff;
}

.title-all {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  color: #000;
  cursor: pointer;

  .title-text {
    margin-left: 10px;
  }

  p {
    padding: 0;
    margin: 0;
    line-height: 24px;
  }

  .title2 {
    font-weight: 400;
    font-size: 14px;
    margin-top: 8px;
  }
}

.search-all {
  margin: 8px 31px 0 32px;
  position: relative;
  padding-bottom: 8px;

  .icon {
    position: absolute;
    top: 8px;
    left: 12px;
    z-index: 9999;
  }
}

.search1 {
  display: flex;
  align-items: center;
  justify-content: center;
  // padding-bottom: 20px;
}

:deep(.ant-select) {
  width: 156px !important;

  .ant-select-selector {
    padding: 0 0 0 34px !important;
    height: 40px !important;
  }

  .ant-select-selection-search-input,
  .ant-select-selection-placeholder,
  .ant-select-selection-item {
    height: 40px !important;
    line-height: 40px;
  }

  .ant-select-selection-search {
    padding-left: 22px;
  }

  .ant-select-arrow {
    display: none;
  }
}

// ::-webkit-scrollbar-thumb {
//   background-color: #888;
//   border-radius: 5px;
// }
// #f6f5fd
::-webkit-scrollbar-track {
  background-color: rgba(50, 50, 50, 0);
  // background-color: #f6f5fd; /* 设置滚动条背景的颜色 */
}

.pop-p {
  margin-bottom: 0 !important;
}

/* 当内容不足以触发滚动条时，隐藏滚动条 */
// .hide-scrollbar {
//   overflow: auto;
//   scrollbar-width: none;
// }
// ::-webkit-scrollbar {
//   display: none;
// }

// .hide-scrollbar {
//   -ms-overflow-style: none;
// }
</style>
