package com.dz.common.core.utils;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Data;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.reflections.Reflections;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
public class ModelToSql {

	private static String noManageTables = "shelf_product,shelf_campaign_rule_product";

	public static void modelToSql(String type) throws SQLException {
		List<String> noTenantTables = Arrays.asList(new String[]{"oms_permission","oms_role","oms_role_permission","oms_user","oms_users_role","sys_permission","sys_permission_url","sys_user","tenant_info","job_business_mapping","system_log","alarm_config","qywx_config","third_api_config","tenant_config","default_data"});
		String database = "bb_"+type;
		String jdbcUrl = "*********************************/"+database+"?serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=UTF-8&useSSL=false";
		String username = "daozhi";
		String password = "Daozhi@123";
		modelToSql(type,jdbcUrl,username,password,noTenantTables);
	}

	public static synchronized void modelToSql(String type,String jdbcUrl,String username,String password,List<String> noTenantTables) throws SQLException {
		//String database = "jml_test";
		String database = jdbcUrl.split("\\?")[0];
		String[] urlArray = database.split("/");
		database = urlArray[urlArray.length-1];
		Connection connection = SqlUtils.getConnnection(jdbcUrl,username,password);
		String sql = String.format("SELECT TABLE_NAME name,TABLE_COMMENT comment FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = '%s'",database);
		ResultSet rs = SqlUtils.getResultSet(connection,sql);
		Map<String,String> tableMap = new HashMap<>();
		while(rs.next()){
			tableMap.put(rs.getString("name"),rs.getString("comment"));
		}
//		sql = String.format("SELECT id FROM mall_basic.tenant_info where state = 1",database);
//		rs = SqlUtils.getResultSet(connection,sql);
//		List<Long> tenantIds = new ArrayList<>();
//		while(rs.next()){
//			tenantIds.add(rs.getLong("id"));
//		}
		List<String> r12months = new ArrayList<>();
		SimpleDateFormat monthSdf = new SimpleDateFormat("_yyyyMM");
		Calendar calendar = Calendar.getInstance();
		List<String> noManageTableList = new ArrayList<>();
		if(StringUtils.isNotBlank(noManageTables)) {
			noManageTableList = Arrays.asList(noManageTables.split(","));
		}
		for (int i = 0; i < 12; i++) {
			if(i > 0) {
				calendar.add(Calendar.MONTH,1);
			}
			r12months.add(monthSdf.format(calendar.getTime()));
		}
		String packageName = "com.dz.ms."+type+".entity";
		Reflections reflections = new Reflections(packageName);
		Set<Class<?>> set = reflections.getTypesAnnotatedWith(TableName.class);
		List<String> createList = new ArrayList<>();
		List<String> updateList = new ArrayList<>();
		List<String> deleteColumnList = new ArrayList<>();
		List<String> deleteIndexList = new ArrayList<>();
		for(Class<?> c : set) {
//			boolean hasSplit = true;
			TableName tableName = c.getAnnotation(TableName.class);
			log.info("tableNameJsonStr" + CommonUtils.jsonStr(tableName));
			if(noManageTableList.contains(tableName.value())) {
				continue;
			}
//			if(noTenantTables.contains(tableName.value())) {
//				hasSplit = false;
//			}
//			List<Long> tenants = new ArrayList<>();
//			if(hasSplit) {
//				tenants.addAll(tenantIds);
//			}
//			else {
//				tenants.add(0L);
//			}
//			for (Long tenantId : tenants) {
			Table table = c.getAnnotation(Table.class);
			if(null == table) {
				throw new RuntimeException("表名注释不能为空");
			}
			List<String> splitSuffixs = new ArrayList<>();
			int splitType = table.splitType();
			if(splitType == 1) {
				for (int i = 0; i < table.splitCount(); i++) {
					splitSuffixs.add("_"+i);
				}
			}
			else if(splitType == 2) {
				splitSuffixs.addAll(r12months);
			}
			else {
				splitSuffixs.add("");
			}
			for (String suffix : splitSuffixs) {
				StringBuilder createSb = new StringBuilder();
				StringBuilder updateSb = new StringBuilder();
				String tbname = tableName.value()+suffix;
//				if(!ParamUtils.isNullOr0Long(tenantId)) {
//					tbname = tableName.value()+"_"+tenantId;
//				}
				createSb.append("CREATE TABLE " +database+ ".`"+tbname+"` (");
				String primaryKey = null;
				Set<String> indexs = new HashSet<>();
				Set<String> uniqueKeys = new HashSet<>();
				Map<String,String[]> indexMap = new HashMap<>();
				Map<String,TableColumn> columnMap = new HashMap<>();
				List<String> codeColumns = new ArrayList<>();
			    List<String> uniqueKeysList = new ArrayList<>();
				boolean isCreate = true;
				if(tableMap.containsKey(tbname)) {
					isCreate = false;
					sql = String.format("SELECT COLUMN_NAME columName,DATA_TYPE dataType,COLUMN_COMMENT columnComment,CASE COLUMN_KEY WHEN 'PRI' THEN 1 ELSE 0 END AS isPrimark,CASE EXTRA WHEN 'auto_increment' THEN 1 ELSE 0 END AS isAuto,CASE IS_NULLABLE WHEN 'YES' THEN true ELSE false END AS isNull,IFNULL(NUMERIC_PRECISION,CHARACTER_MAXIMUM_LENGTH) length,NUMERIC_SCALE scale,COLUMN_DEFAULT defaultValue FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '%s' AND TABLE_NAME = '%s' ORDER BY ORDINAL_POSITION",database,tbname);
					rs = SqlUtils.getResultSet(connection,sql);
					while(rs.next()){
						String name = rs.getString("columName");
						String[] strs = name.split("_");
						Integer end = NumberUtils.toInt(strs[strs.length-1],-1);
						if(end >= 0) {
							continue;
						}
						TableColumn column = new TableColumn();
						column.setColumName(name);
						column.setColumnComment(rs.getString("columnComment"));
						column.setDataType(rs.getString("dataType"));
						column.setIsAuto(rs.getInt("isAuto"));
						column.setIsPrimark(rs.getInt("isPrimark"));
						column.setScale(rs.getInt("scale"));
						column.setNull(rs.getBoolean("isNull"));
						column.setLength(rs.getInt("length"));
						column.setDefaultValue(rs.getString("defaultValue"));
						columnMap.put(name,column);
					}
				}
				String lastColumn = null;
				for(Field field : c.getDeclaredFields()) {
					boolean annotationPresent = field.isAnnotationPresent(Columns.class);
					if (!annotationPresent) {
						continue;
					}
					Columns columns = field.getAnnotation(Columns.class);
					String name = StringUtils.isBlank(columns.name()) ? UnderlineToCamelUtils.camelToUnderline(field.getName()) : columns.name();
					codeColumns.add(name);
					int isAuto = 0;
					String primary = null;
					StringBuilder sb = new StringBuilder();
					sb.append("`"+name+"`");
					sb.append(" ");
					sb.append(columns.type().name().toLowerCase());
					if(!ColumnType.noLengthTypes.contains(columns.type().name())) {
						if(ColumnType.decimalTypes.contains(columns.type().name())) {
							int pointNum = columns.scale() > 0 ? columns.scale() : 2;
							sb.append("("+ columns.length()+","+pointNum+")");
						}
						else {
							sb.append("("+ columns.length() +")");
						}
					}
					if(!columns.isNull()) {
						sb.append(" NOT NULL");
					}
					if(StringUtils.isNotBlank(columns.defaultValue())) {
						if(ColumnType.decimalTypes.contains(columns.type().name())) {
							sb.append(" DEFAULT "+columns.defaultValue());
						}
						else {
							sb.append(" DEFAULT '"+columns.defaultValue()+"'");
						}
					}
					else if(columns.isNull()) {
						sb.append(" DEFAULT NULL");
					}
					TableId tableId = field.getAnnotation(TableId.class);
					if(null != tableId) {
						primary = name;
						primaryKey = name;
						if(tableId.type().equals(IdType.AUTO)) {
							sb.append(" AUTO_INCREMENT");
							isAuto = 1;
						}
					}
					sb.append(" COMMENT '"+columns.comment());
					String columnsSql = sb.toString();
					createSb.append("\n\t" + columnsSql+"',");
					if(!columnMap.containsKey(name)) {
						updateSb.append("\n\tADD COLUMN " + columnsSql);
						if(null != lastColumn) {
							updateSb.append("' after " + lastColumn + ",");
						}
						else {
							updateSb.append("',");
						}
					}
					else {
						TableColumn column = columnMap.get(name);
						List<Integer> lengthList = new ArrayList<>();
						Integer length = ParamUtils.Integer2int(column.getLength());
						if(column.getDataType().equalsIgnoreCase(ColumnType.BIGINT.name())) {
							lengthList.add(11);
							lengthList.add(19);
						}
						else if(column.getDataType().equalsIgnoreCase(ColumnType.TINYINT.name())) {
							lengthList.add(1);
							lengthList.add(2);
							lengthList.add(3);
						}
						else if(ColumnType.noLengthTypes.contains(column.getDataType().toUpperCase())) {
							lengthList.add(0);
						}
						else {
							lengthList.add(length);
						}
						int scale = ParamUtils.Integer2int(column.getScale());
						int isPrimary = null == primary ? 0 : 1;
						String defaultValue = null == column.getDefaultValue() ? "" : column.getDefaultValue();
						String defaultValue1 = null == columns.defaultValue() ? "" : columns.defaultValue();
						boolean equalsDefaultValue = true;
						if(ColumnType.decimalTypes.contains(columns.type().name())) {
							log.info("defaultValue" + defaultValue);
							log.info("defaultValue1" + defaultValue1);
							if(!defaultValue1.equals(defaultValue) && new BigDecimal(defaultValue).compareTo(new BigDecimal(defaultValue1)) != 0) {
								equalsDefaultValue = false;
							}
						}
						else {
							if(!defaultValue1.equals(defaultValue)) {
								equalsDefaultValue = false;
							}
						}
						if(!column.getDataType().equals(columns.type().name().toLowerCase())
								|| !lengthList.contains(columns.length())
								|| columns.isNull() != column.isNull()
								|| columns.scale() != scale
								|| !equalsDefaultValue
								|| !columns.comment().equals(column.getColumnComment())
								|| isAuto != column.getIsAuto()
								|| isPrimary != column.getIsPrimark()) {
							updateSb.append("\n\tMODIFY COLUMN " + columnsSql + "',");
						}
					}
					boolean isUniqueKeys = false;
					String[] unionKeys = columns.unionKeys();
					if(null == columns.unionKeys() || columns.unionKeys().length == 0 || StringUtils.isBlank(columns.unionKeys()[0])) {
						unionKeys = columns.uniqueKeys();
						isUniqueKeys = true;
					}
					if(null != unionKeys && unionKeys.length > 0 && StringUtils.isNotBlank(unionKeys[0])) {
						String[] underlineUnionKeys = new String[unionKeys.length];
						for (int i = 0; i < unionKeys.length; i++) {
							underlineUnionKeys[i] = UnderlineToCamelUtils.camelToUnderline(unionKeys[i]);
						}
						if(underlineUnionKeys.length == 1) {
							indexs.add(underlineUnionKeys[0]);
						}
						else {
							indexMap.put(underlineUnionKeys[0],underlineUnionKeys);
							if(isUniqueKeys) {
								uniqueKeysList.add(JSON.toJSONString(underlineUnionKeys));
							}
						}
					}
					if(columns.isUnique() && !indexMap.containsKey(name)) {
						uniqueKeys.add(name);
					}
					if(columns.isIndex() && !indexMap.containsKey(name)) {
						indexs.add(name);
					}
					lastColumn = name;
				}
				String deleteColumns = "";
				Map<String,String> dbIndexMap = new HashMap<>();
				if(!isCreate) {
					for (String column : columnMap.keySet()) {
						if(!codeColumns.contains(column)) {
							deleteColumns += "DROP COLUMN " + column + ",";
						}
					}
					rs = SqlUtils.getResultSet(connection,"show index from " + database +"."+ tbname);
					Map<String,List<String>> unionIndexMap = new HashMap<>();
					while(rs.next()) {
						int notUnique = rs.getInt("Non_unique");
						String keyName = rs.getString("Key_name");
						String columnName = rs.getString("Column_name");
						int seqInIndex = rs.getInt("Seq_in_index");
						if(keyName.equals("PRIMARY")) {
							dbIndexMap.put("PRIMARY KEY (`"+columnName+"`),",keyName);
							continue;
						}
						List<String> columns = null;
						if(!unionIndexMap.containsKey(notUnique+keyName)) {
							columns = new ArrayList<>();
						}
						else {
							columns = unionIndexMap.get(notUnique+keyName);
						}
						columns.add(seqInIndex-1,columnName);
						unionIndexMap.put(notUnique+keyName,columns);
					}
					for (Map.Entry<String,List<String>> entry : unionIndexMap.entrySet()) {
						List<String> columns = entry.getValue();
						int size = columns.size();
						if(size > 1) {
							String unionKeyStr = " idx";
							if(entry.getKey().startsWith("0")) {
								unionKeyStr = " uni";
							}
							String unionKeyColumns = " (";
							for (int i = 0; i < size; i++) {
								String key = columns.get(i);
								unionKeyStr += "_"+key;
								unionKeyColumns += "`"+key+"`";
								if(i < (columns.size() -1)) {
									unionKeyColumns += ",";
								}
							}
							unionKeyColumns += ") ";
							if(entry.getKey().startsWith("0")) {
								dbIndexMap.put("UNIQUE INDEX"+unionKeyStr+unionKeyColumns+"USING BTREE,",entry.getKey().substring(1));
							}
							else {
								dbIndexMap.put("INDEX"+unionKeyStr+unionKeyColumns+"USING BTREE,",entry.getKey().substring(1));
							}
						}
					}
					for (Map.Entry<String,List<String>> entry : unionIndexMap.entrySet()) {
						List<String> columns = entry.getValue();
						if(columns.size() == 1) {
							if(entry.getKey().startsWith("0")) {
								dbIndexMap.put("UNIQUE INDEX uni_"+columns.get(0)+" (`"+columns.get(0)+"`) USING BTREE,",entry.getKey().substring(1));
							}
							else {
								dbIndexMap.put("INDEX idx_"+columns.get(0)+" (`"+columns.get(0)+"`) USING BTREE,",entry.getKey().substring(1));
							}
						}
					}
				}
				List<String> codeIndexList = new ArrayList<>();
				if(null != primaryKey) {
					String primaryIndex = "PRIMARY KEY (`"+primaryKey+"`),";
					codeIndexList.add(primaryIndex);
					createSb.append("\n\t"+primaryIndex);
					if(!dbIndexMap.containsKey(primaryIndex)) {
						updateSb.append("\n\tADD "+primaryIndex);
					}
				}
				for (String uniqueKey : uniqueKeys) {
					String uniqueIndex = "UNIQUE INDEX uni_"+uniqueKey+" (`"+uniqueKey+"`) USING BTREE,";
					codeIndexList.add(uniqueIndex);
					createSb.append("\n\t"+uniqueIndex);
					if(!dbIndexMap.containsKey(uniqueIndex)) {
						updateSb.append("\n\tADD "+uniqueIndex);
					}
				}
				Collection<String[]> unionKeys = indexMap.values();
				for(String[] unionKey : unionKeys) {
					boolean isUnique = uniqueKeysList.size() > 0 && uniqueKeysList.contains(JSON.toJSONString(unionKey));
					String unionKeyStr = " idx";
					if(isUnique) {
						unionKeyStr = " uni";
					}
					String unionKeyColumns = " (";
					for (int i = 0; i < unionKey.length; i++) {
						String key = unionKey[i];
						unionKeyStr += "_"+key;
						unionKeyColumns += "`"+key+"`";
						if(i < (unionKey.length -1)) {
							unionKeyColumns += ",";
						}
					}
					unionKeyColumns += ") ";
					String unionIndex = "INDEX"+unionKeyStr+unionKeyColumns+"USING BTREE,";
					if(isUnique) {
						unionIndex = "UNIQUE " + unionIndex;
					}
					codeIndexList.add(unionIndex);
					createSb.append("\n\t"+unionIndex);
					if(!dbIndexMap.containsKey(unionIndex)) {
						updateSb.append("\n\tADD "+unionIndex);
					}
				}
				for (String key : indexs) {
					String normalIndex = "INDEX idx_"+key+" (`"+key+"`) USING BTREE,";
					codeIndexList.add(normalIndex);
					createSb.append("\n\t"+normalIndex);
					if(!dbIndexMap.containsKey(normalIndex)) {
						updateSb.append("\n\tADD "+normalIndex);
					}
				}
				String createSql = createSb.toString();
				if(isCreate) {
					createSql = createSql.substring(0,createSql.length()-1);
					createSql += "\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='"+table.value()+"';";
					createList.add(createSql);
					continue;
				}
				String updateSql = updateSb.toString();
				if(tableMap.containsKey(tbname) && !tableMap.get(tbname).equals(table.value())) {
					updateSql += "COMMENT = '"+table.value()+"';";
				}
				if(updateSql.length() > 0) {
					if(updateSql.endsWith(",")) {
						updateSql = updateSql.substring(0,updateSql.length()-1) +";";
					}
					updateSql = "ALTER TABLE " +database+ ".`"+tbname+"` " + updateSql;
					updateList.add(updateSql);
				}
				String deleteIndexSql = "";
				for (String indexStr : dbIndexMap.keySet()) {
					if(!codeIndexList.contains(indexStr)) {
						deleteIndexSql += "DROP INDEX `"+dbIndexMap.get(indexStr)+"`,";
					}
				}
				if(deleteIndexSql.length() > 0) {
					deleteIndexSql = "ALTER TABLE " +database+ ".`"+tbname+"` " + deleteIndexSql.substring(0,deleteIndexSql.length()-1) +";";
					deleteIndexList.add(deleteIndexSql);
				}
				if(deleteColumns.length() > 0) {
					deleteColumnList.add("表字段代码中已删除请及时执行 ALTER TABLE " + tbname + " " + deleteColumns.substring(0,deleteColumns.length()-1) +";");
				}
			}
		}
		log.info("建表语句");
		for (String create : createList) {
			log.info(create);
			boolean result = SqlUtils.execute(connection,create);
			log.info("执行结果:"+result);
			if(!result) {
				throw new RuntimeException("执行SQL失败");
			}
		}
		log.info("删除索引语句");
		for (String deleteIndex : deleteIndexList) {
			log.info(deleteIndex);
			boolean result = SqlUtils.execute(connection,deleteIndex);
			log.info("执行结果:"+result);
			if(!result) {
				throw new RuntimeException("执行SQL失败");
			}
		}
		log.info("表更新语句");
		for (String update : updateList) {
			log.info(update);
			boolean result = SqlUtils.execute(connection,update);
			log.info("执行结果:"+result);
			if(!result) {
				throw new RuntimeException("执行SQL失败");
			}
		}
		log.info("删除字段提示");
		for (String deleteColumn : deleteColumnList) {
			log.info(deleteColumn);
		}
	}

	@Data
	private static class TableColumn {
		private String columName;
		private String dataType;
		private String columnComment;
		private int isPrimark;
		private int isAuto;
		private boolean isNull;
		private String defaultValue;
		private int length;
		private Integer scale;
	}

	public static void main(String[] args) throws SQLException {
		String jdbcUrl = "****************************************************************************************************************";
		String username = "root";
		String password = "sensai123";
		String database = "mall_custom";
		Connection connection = SqlUtils.getConnnection(jdbcUrl,username,password);
		String sql = String.format("SELECT TABLE_NAME name,TABLE_COMMENT comment FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = '%s'",database);
		ResultSet rs = SqlUtils.getResultSet(connection,sql);
		while(rs.next()){
			String name = rs.getString("name");
			String[] strs = name.split("_");
			int end = NumberUtils.toInt(strs[strs.length-1]);
			if(end > 3) {
			}
		}

	}

}
