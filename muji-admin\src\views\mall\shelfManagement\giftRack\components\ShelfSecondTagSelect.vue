<template>
  <a-form-item :label="props.label" :name="props.name" :rules="props.rules" class="ShelfStatusSelect">
    <a-select :fieldNames="{label:'tagName',value:'tagId'}" v-model:value="thisFields.value" show-search :filterOption="thisMethods.filterOption" :allowClear="true" :maxTagCount="1" placeholder="请选择" @change="thisMethods.change" :options="thisFields.options" v-bind="$attrs" />
  </a-form-item>
</template>

<script setup>
import { onMounted, reactive, watch } from 'vue'
import { SHELF_STATUS_ARR } from '@/utils/constants.js'
import { tag_info_noPage } from '@/http/index.js'

const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  name: {
    type: String,
    default: ''
  },
  rules: {
    type: Object,
    default: () => undefined
  },
  modelValue: {
    type: Object,
    default: () => ({})
  }
})
const emits = defineEmits(['change', 'update:modelValue'])

const attrs = useAttrs()
const thisFields = reactive({
  value: null,
  options: []
})
const thisMethods = {
  filterOption(input, option) {
    return option.tagName.toLowerCase().indexOf(input.toLowerCase()) >= 0
  },
  async getOptions() {
    const res = await tag_info_noPage({ cate: 2 })
    thisFields.options = res.data.map(v => ({ tagId: v.id, tagName: v.name }))
  },
  setValue() {
    thisFields.value = props.modelValue.tagId || null
  },
  change(e) {
    emits('update:modelValue', thisFields.options.find(v => v.tagId === thisFields.value))
    emits('change', e)
  }
}

onMounted(() => {
  thisMethods.setValue()
  thisMethods.getOptions()
})
watch(() => props.modelValue, () => thisMethods.setValue())
</script>

<style lang="scss" scoped>
.ShelfStatusSelect {
  .ant-select {
    width: 202px;
  }
}
</style>
