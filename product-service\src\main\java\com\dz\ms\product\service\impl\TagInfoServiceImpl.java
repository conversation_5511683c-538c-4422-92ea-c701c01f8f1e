package com.dz.ms.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.constants.NumConstants;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.product.dto.ShelfTagDTO;
import com.dz.ms.product.dto.TagInfoDTO;
import com.dz.ms.product.entity.ProductTag;
import com.dz.ms.product.entity.TagInfo;
import com.dz.ms.product.mapper.ProductTagMapper;
import com.dz.ms.product.mapper.TagInfoMapper;
import com.dz.ms.product.service.ProductTagService;
import com.dz.ms.product.service.ShelfTagService;
import com.dz.ms.product.service.TagInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 商品标签
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:51
 */
@Service
public class TagInfoServiceImpl extends ServiceImpl<TagInfoMapper, TagInfo> implements TagInfoService {

    @Resource
    private TagInfoMapper tagInfoMapper;
    @Resource
    private ProductTagMapper productTagMapper;
    @Resource
    private ProductTagService productTagService;
    @Resource
    private ShelfTagService shelfTagService;

    /**
     * 分页查询商品标签
     *
     * @param param
     * @return PageInfo<TagInfoDTO>
     */
    @Override
    public PageInfo<TagInfoDTO> getTagInfoList(TagInfoDTO param) {
        TagInfo tagInfo = BeanCopierUtils.convertObjectTrim(param, TagInfo.class);
        if (Objects.isNull(param.getCate())) {
            param.setCate(NumConstants.ONE);
        }
        QueryWrapper<TagInfo> queryWrapper = new QueryWrapper<>(tagInfo);
        queryWrapper.orderByDesc("id");
        IPage<TagInfo> page = tagInfoMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), queryWrapper);
        return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), BeanCopierUtils.convertList(page.getRecords(), TagInfoDTO.class));
    }

    @Override
    public List<TagInfoDTO> getNoPageTagInfoList(String name, Integer cate) {
        LambdaQueryWrapper<TagInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(name), TagInfo::getName, name)
                .eq(TagInfo::getCate, cate);
        List<TagInfo> tagInfos = tagInfoMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(tagInfos)) {
            return Collections.emptyList();
        }
        return BeanCopierUtils.convertList(tagInfos, TagInfoDTO.class);
    }

    @Override
    public List<TagInfoDTO> getByTagIdList(List<Long> tagIdList) {
        if (CollectionUtils.isEmpty(tagIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TagInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TagInfo::getId, tagIdList);
        List<TagInfo> tagInfos = tagInfoMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(tagInfos)) {
            return Collections.emptyList();
        }
        return BeanCopierUtils.convertList(tagInfos, TagInfoDTO.class);
    }

    /**
     * 根据ID查询商品标签
     *
     * @param id
     * @return TagInfoDTO
     */
    @Override
    public TagInfoDTO getTagInfoById(Long id) {
        TagInfo tagInfo = tagInfoMapper.selectById(id);
        return BeanCopierUtils.convertObject(tagInfo, TagInfoDTO.class);
    }

    /**
     * 保存商品标签
     *
     * @param param
     * @return Long
     */
    @Override
    public Long saveTagInfo(TagInfoDTO param) {
        TagInfo tagInfo = new TagInfo(param.getId(), param.getName(), param.getCate());
        if (ParamUtils.isNullOr0Long(tagInfo.getId())) {
            LambdaQueryWrapper<TagInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TagInfo::getName, param.getName());
            List<TagInfo> tagInfos = tagInfoMapper.selectList(queryWrapper);
            if (!CollectionUtils.isEmpty(tagInfos)) {
                throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "标签名称已存在");
            }
            tagInfoMapper.insert(tagInfo);
        } else {

            tagInfoMapper.updateById(tagInfo);
        }
        return tagInfo.getId();
    }

    @Override
    @Transactional
    public Long updateTagInfo(TagInfoDTO param) {
        // cate 不允许更新
        TagInfo tagInfo = new TagInfo(param.getId(), param.getName(), null);
        tagInfoMapper.updateById(tagInfo);
        // 更新关联的商品标签信息
        ProductTag updateProductTag = new ProductTag();
        updateProductTag.setTagName(param.getName());
        LambdaUpdateWrapper<ProductTag> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProductTag::getTagId, param.getId());
        productTagMapper.update(updateProductTag, updateWrapper);
        // 更新关联的货架标签名称
        ShelfTagDTO toUpdateShelfTag = new ShelfTagDTO();
        toUpdateShelfTag.setTagId(param.getId());
        toUpdateShelfTag.setTagName(param.getName());
        shelfTagService.updShelfTagName(toUpdateShelfTag);
        return tagInfo.getId();
    }

    /**
     * 根据ID删除商品标签
     *
     * @param param
     */
    @Override
    @Transactional
    public void deleteTagInfoById(IdCodeDTO param) {
        tagInfoMapper.deleteById(param.getId());
        productTagService.deleteShelfTagByTagId(param.getId());
        shelfTagService.deleteShelfTagByTagId(param.getId());
    }

}