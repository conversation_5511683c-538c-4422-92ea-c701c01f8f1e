import {
  throttle
} from '../../../../../utils/util';
const app = getApp();


Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    isShow: {
      type: Boolean,
      value: false,
    },
    num: {
      type: Number,
      value: 0,
    },
    // 是否显示关闭按钮
    closeable: {
      type: Boolean,
      value: true
    },
    info: {
      type: Object,
      value: () => { },
    }
  },
  lifetimes: {
    attached() {
      this.setUserInfo()
    },
  },
  pageLifetimes: {
    show() {
      this.setUserInfo()
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    emailTxt: '<EMAIL>',
    phoneTxt: '4009209299',
    userInfo: {},
    title: '信息使用说明'
  },
  /**
   * 组件的方法列表
   */
  methods: {
    setUserInfo() {
      this.setData({
        userInfo: app.globalData.userInfo
      })
    },
    onClose(e) {
      this.triggerEvent('close', e)
    },
  }
})
