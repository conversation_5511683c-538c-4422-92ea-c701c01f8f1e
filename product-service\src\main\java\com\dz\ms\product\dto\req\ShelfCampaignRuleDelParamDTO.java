package com.dz.ms.product.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 营销活动规则删除入参
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@ApiModel(value = "营销活动规则删除入参")
public class ShelfCampaignRuleDelParamDTO {

    @ApiModelProperty(value = "营销活动ID")
    private Long campaignId;
    @ApiModelProperty(value = "营销活动规则ID")
    private Long ruleId;
    @ApiModelProperty(value = "营销活动规则ID列表")
    private List<Long> ruleIdList;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;

}
