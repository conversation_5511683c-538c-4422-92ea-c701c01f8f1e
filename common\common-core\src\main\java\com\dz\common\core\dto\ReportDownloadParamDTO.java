package com.dz.common.core.dto;

import com.alibaba.fastjson.JSONObject;
import com.dz.common.core.enums.ReportDownloadEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 类注释
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/2/8 15:19
 */
@Getter
@Setter
@ApiModel("报表下载请求参数")
public class ReportDownloadParamDTO {

    /**
     * com.dz.common.core.enums.ReportDownloadEnum.type
     *
     * @see ReportDownloadEnum
     */

    @ApiModelProperty(value = "报表type")
    private Integer type;

    @ApiModelProperty(value = "文件名")
    private String fileName;

    @ApiModelProperty(value = "请求参数")
    private JSONObject param;

}
