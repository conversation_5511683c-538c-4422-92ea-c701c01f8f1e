package com.dz.common.core.dto.basic;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 系统日志DTO
 * @author: Handy
 * @date:   2022/08/04 20:22
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "系统日志")
public class SystemLogDTO extends BaseDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "日志类型 1操作日志 2脚本同步 3接口调用")
    private Integer logType;
    @ApiModelProperty(value = "日志名称")
    private String logName;
    @ApiModelProperty(value = "请求IP")
    private String sourceIP;
    @ApiModelProperty(value = "接口地址")
    private String requestUrl;
    @ApiModelProperty(value = "执行开始时间")
    private Date startTime;
    @ApiModelProperty(value = "执行结束时间")
    private Date endTime;
    @ApiModelProperty(value = "方法入参")
    private String params;
    @ApiModelProperty(value = "执行结果 0异常 1正常")
    private Integer state;
    @ApiModelProperty(value = "告警状态 0未告警 1已告警 2已处理")
    private Integer alarmState;
    @ApiModelProperty(value = "异常信息")
    private String exception;
    @ApiModelProperty(value = "操作人")
    private Long operator;
    @ApiModelProperty(value = "操作人账号")
    private String operatorName;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;

    @ApiModelProperty(value = "列表筛选开始时间")
    private Date createdStart;
    @ApiModelProperty(value = "列表筛选结束时间")
    private Date createdEnd;

}
