package com.dz.ms.order.dto.req;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ToString
public class CrmExchangeOrderParamDTO extends BaseDTO {

    @ApiModelProperty(value = "订单编号")
    private String orderCode;

    @ApiModelProperty(value = "用户昵称")
    private String userName;

    @ApiModelProperty(hidden = true)
    private List<Long> userIdList;

    @ApiModelProperty(value = "用户CRM编码")
    private String userCrmCode;

    @ApiModelProperty(value = "兑换时间区间 开始")
    private Date createdStart;

    @ApiModelProperty(value = "兑换时间区间 结束")
    private Date createdEnd;

    @ApiModelProperty(value = "订单状态 0已完成 1待支付 2待发货 3已发货 4待兑换 5部分兑换 6已兑换 7已取消")
    private Integer orderStatus;

    @ApiModelProperty(value = "发货状态 0待发货 1已发货 2部分发货")
    private Integer expressStatus;
}
