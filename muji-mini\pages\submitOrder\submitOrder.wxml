<my-page loading="{{loading}}" overallModal="{{overallModal}}" catch:overallModalConfirm="overallModalConfirm">
  <view class="page-container">
    <custom-header type="{{1}}" isBackHidden="{{ isBackHidden }}">
      <view slot="content" class="page-title">
        <text>订单确认</text>
      </view>
    </custom-header>

    <basic-tips tipsTxt="积分兑换后不支持退还，请仔细阅读商品兑换须知" />

    <scroll-view class="pages-content" enhanced="{{ true }}" bounces="{{ false }}" show-scrollbar="{{ false }}" scroll-y>
      <view class="cart-container">
        <view class="select-box" wx:for="{{detail.productList}}" wx:key="id">
          <cart-card skuInfo="{{item}}" />
        </view>
        <view class="total-box">
          <view>合计</view>
          <view>{{detail.totalPoint}}积分<block wx:if="{{detail.totalPrice}}"> + {{detail.totalPrice}}元</block>
          </view>
        </view>
      </view>
    </scroll-view>
    <view class="bottom-box">
      <!--loading="{{loadingSubmit}}"-->
      <basic-button width="{{670}}" btnState="primary" size="large" bind:click="onClickSubmit">
        确认兑换
      </basic-button>
    </view>
  </view>

  <my-popup show="{{ err.show }}" closeable="{{false}}" borderRadius="{{0}}" title="兑换失败" confirmText="查看更多商品" cancelText="" content="{{err.msg}}" bindconfirm="onPopConfirm" bindcancel="" data-key="fail" showType="normal"></my-popup>
</my-page>