<template>

  <a-modal :title="title" width="768px" v-if="open" placement="right" :maskClosable="false" :closable="true" :open="open" @cancel="onClose">
    <!-- <a-spin :spinning="loading"> -->

    <a-form class="form" ref="addForm" :model="addParams" :rules="rules" :labelCol="{ style: 'width:160px' }">

      <a-form-item label="请勾选门店服务" name="isCheckedList" v-if="addType1 === 1">
        <a-checkbox-group v-model:value="addParams.isCheckedList" style="width: 100%">
          <a-row>
            <a-col :span="8" v-for="(item,index) in isCheckedOptions" :key="item.id">
              <a-checkbox :value="item.id">
                <div class="check-item">
                  <div class="check-item-img">
                    <a-image :src="item.image" :width="50" :height="50"></a-image>
                  </div>
                  <div class="check-item-tit" :title="item.name">{{item.name}}</div>

                </div>
              </a-checkbox>
            </a-col>
          </a-row>
        </a-checkbox-group>
      </a-form-item>
      <a-form-item label="请上传门店照片" name="images" v-if="addType1 === 2">
        <uploadImg :max="10" :width="100" :height="100" :imgUrl="addParams.images" :form="addParams" path="images" :disabled="disabled" @success="uploadSuccess" />
        <div class="global-tip">
          建议尺寸
        </div>
      </a-form-item>
      <a-form-item label="请上传门店企微二维码" name="weworkImages" v-if="addType1 === 3">
        <uploadImg :max="10" :width="100" :height="100" :imgUrl="addParams.weworkImages" :form="addParams" path="weworkImages" :disabled="disabled" @success="uploadSuccess" />
        <div class="global-tip">
          建议尺寸
        </div>
      </a-form-item>
    </a-form>
    <!--  -->
    <!-- </a-spin> -->
    <template #footer v-if="!disabled">
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="ok" :loading="loading">确定</a-button>
      </a-space>
    </template>

  </a-modal>

</template>
<script setup>
import { updatestore, updateServe, checkstoreServe } from '@/http/index.js'
import { useGlobalStore } from '@/store'
import { message, Modal } from "ant-design-vue";
import _ from "lodash"

const global = useGlobalStore()
const addForm = ref(null)
import { cloneDeep } from 'lodash'
import { onMounted } from 'vue';
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  // 操作抽屉打开关闭
  visible: {
    type: Boolean,
    default: false
  },
  // 数据ID
  id: {
    type: String || Number,
    default: ''
  },
  // 操作类型
  type: {
    type: Number,
    default: 0, // 0-新增 1-编辑  2-查看
  },
  addType: {
    type: String || Number,
    default: 1, // 1 级 2级
  },
  title: {
    type: String || Number,
    default: ''
  },
  itemRow: {
    type: Object,
    default: {}
  }

})
// 置灰
const disabled = computed(() => {
  // console.log(addParams.value, 'addType');

  return props.type == 2
})
let addType1 = computed(() => {


  return props.addType
})
// 标题
const title = computed(() => {
  return props.title
})

const { open, addParams, rules, loading, isCheckedOptions } = toRefs(reactive({
  open: props.visible,
  loading: false,
  isCheckedOptions: [],
  addParams: {
    isCheckedList: [],
    images: '',
    weworkImages: ''
  },
  rules: {
    isCheckedList: [{ required: true, message: '请选择服务', trigger: ['blur', 'change'] }],

    // images: [{ required: true, message: '请上传门店图片', trigger: ['blur', 'change'] }],
    // weworkImages: [{ required: true, message: '请上传企微二维码', trigger: ['blur', 'change'] }],
  }
})
);
// onMounted(() => {
//     initData()
// })
watch(() => props.visible, (value) => {
  // 弹窗操作
  open.value = props.visible
  addForm.value?.resetFields()
  if (open.value) {

    initData()
  }
})

//所有接口调取出
const initData = async () => {
  const promiseArr = []

  if (props.id && addType1.value == '1') {

    promiseArr.push(checkstoreServe({ storeId: props.id }))
  }

  // 如果有新的接口就在这里push
  loading.value = true
  try {
    const [storeServeList] = await Promise.all(promiseArr)
    if (storeServeList) {
      isCheckedOptions.value = storeServeList.data
      if (storeServeList.data) {
        addParams.value.isCheckedList = storeServeList.data.filter(item => {
          if (item.isChecked == 1) {
            return item
          }
        }).map(item => item.id) || []
      }

    } else {
      if (props.itemRow) {
        if (props.itemRow.images && addType1.value == '2') {
          addParams.value.images = props.itemRow.images
        } else {
          addParams.value.images = ''
        }
        if (props.itemRow.weworkImages && addType1.value == '3') {
          addParams.value.weworkImages = props.itemRow.weworkImages
        } else {
          addParams.value.weworkImages = ''
        }

      }
      // console.log(props.itemRow, 'propspropspropsprops');
    }

    loading.value = false
  } catch (error) {
    loading.value = false
    console.error('获取数据失败:', error)
  }
}

const uploadSuccess = async (data) => {
  let { form, path: key, imgUrl } = data;
  // console.log("🚀 ~ uploadSuccess ~ form, path: key, imgUrl:", form, key, imgUrl)
  _.set(form, key.split('.'), imgUrl)
  // 清除校验
  await nextTick()
  addForm.value.validateFields([key.split('.')])
}


// 关闭弹窗
const onClose = () => {
  emit('cancel')
}

// 确定
const ok = () => {

  addForm.value.validate().then(res => {

    let params = cloneDeep(addParams.value)
    // console.log("🚀 ~ addForm.value.validate ~ params:", params)
    isCheckedOptions.value.forEach(option => {
      // 检查 option.id 是否在 isCheckedList 中
      if (params.isCheckedList.includes(option.id)) {
        // 如果在，则设置 isChecked 为 1
        option.isChecked = 1;
      } else {
        option.isChecked = 0;
        // 如果不在，并且您确实想要将未匹配的项的 isChecked 设置为 0（尽管这通常是多余的，因为未修改的项默认就是 0）
        // 则可以显式地设置它，但在这个例子中我们省略这一步，因为未修改的项将保持为 0
        // option.isChecked = 0; // 这行是多余的，因为未找到匹配时 isChecked 已经是 0 或保持原样
      }
    });


    loading.value = true
    if (props.id) {
      if (addType1.value == '1') {
        // console.log('编辑');
        let newIs = isCheckedOptions.value.filter(item => {
          if (item.isChecked == 1) {
            return item
          }
        }).map(item => {
          return {
            serveId: item.id,
            storeId: props.id
          }
        })
        updateServe(newIs).then(res => {
          message.success(res.msg);
          emit('ok', props.id)
        }).finally(() => {
          loading.value = false
        })
      } else {
        let obj = {
          images: params.images, weworkImages: params.weworkImages
        }
        if (addType1.value == 2) {
          delete obj.weworkImages
        } else {
          delete obj.images
        }
        updatestore({ id: props.id, ...obj }).then(res => {
          message.success(res.msg);
          emit('ok', props.id)
        }).finally(() => {
          loading.value = false
        })
      }

    } else {

    }

  })
}



</script>

<style scoped lang="scss">
.form {
}
:deep(.searchForm .ant-form-item) {
  // margin-bottom: 16px;
}

:deep(.anticon-eye-invisible) {
  display: none;
}

.member-role {
  background: #f2f2f2;
  margin-bottom: 20px;
  padding: 5px 10px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &:nth-child(odd) {
    margin-right: 35px;
  }
}
.check-item {
  display: flex;
  align-items: center;
  padding: 10px;
  .check-item-img {
    margin-right: 10px;
    width: 50px;
    height: 50px;
  }
  .check-item-tit {
    flex: 1;
    width: 90px;
    white-space: nowrap; /* 确保文本在一行内显示 */
    overflow: hidden; /* 超出容器的文本将被隐藏 */
    text-overflow: ellipsis;
  }
}
</style>
