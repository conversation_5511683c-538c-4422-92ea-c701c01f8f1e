<my-page loading="{{loading}}" overallModal="{{overallModal}}" catch:overallModalConfirm="overallModalConfirm">
  <view class="page-container">
    <custom-header type="{{1}}" isBackHidden="{{ isBackHidden }}">
      <view slot="content" class="page-title">
        <text>我的兑换</text>
      </view>
    </custom-header>

    <van-tabs color="#3C3C43" title-inactive-color="#888888" title-active-color="#3C3C43" tab-class="tab-item" wrap-class="nav-wrap" line-height="1.6" active="{{currentTab }}" bind:change="onChangeTab" line-width="55">
      <van-tab wx:for="{{tabList}}" name="{{item.id}}" title="{{item.label}}" wx:key="id" />
    </van-tabs>


    <!--lower-threshold="100"-->
    <scroll-view scroll-y="true" bindscrolltolower="onReachBottom" class="page-content">
      <view class="list-box">
        <block wx:if="{{ detail.list.length > 0}}">
          <view wx:for="{{detail.list}}" wx:key="id" class="list-item">
            <exchange-card item="{{item}}" data-item="{{item}}" bind:tap="onTapItem" />
          </view>

          <view class="extra"></view>
        </block>
        <block wx:else>
          <no-data-available top="{{536}}" text="{{tabList[currentIndex].noDataTxt}}" />
          <view wx:if="{{currentTab===4}}" class="go-btn">
            <basic-button bindtap="handleGoto" width="{{240}}" btnState="primary" size="small">
              去积分商城看看
            </basic-button>
          </view>
        </block>
      </view>
    </scroll-view>
  </view>
</my-page>