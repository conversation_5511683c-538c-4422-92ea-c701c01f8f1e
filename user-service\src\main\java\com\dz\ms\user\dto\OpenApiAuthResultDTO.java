package com.dz.ms.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 开放接口认证登录出参DTO
 * @author: Handy
 * @date:   2023/08/10 23:32
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "开放接口认证登录出参")
public class OpenApiAuthResultDTO {

    @ApiModelProperty(value = "接口令牌")
    private String token;
    @ApiModelProperty(value = "过期时间(秒)默认120分钟")
    private Integer expires;

}
