<template>
  <a-drawer :title="title" width="95%" placement="right" :closable="true" :maskClosable="false" :open="open" @close="onClose">
    <a-spin :spinning="loading">
      <div class="form-top-titles-common">门店基础信息</div>
      <div class="form-top-line-common"></div>
      <a-form class="form" ref="addForm" :model="addParams" :rules="rules" :labelCol="{ style: 'width:120px' }">
        <a-form-item label="门店编号" name="storeSn">
          <a-input v-model:value="addParams.storeSn" style="width:400px" placeholder="请输入门店编号" allow-clear show-count :maxlength="20" />
        </a-form-item>
        <a-form-item label="门店名称" name="storeName">
          <a-input placeholder="请输入门店名称" style="width:400px" v-model:value="addParams.storeName" allow-clear show-count :maxlength="20" />
        </a-form-item>
        <a-form-item label="门店类型" name="type">
          <a-select ref="select" v-model:value="addParams.type" allowClear :disabled="disabled" :options="storetypeOptions" style="width:400px" :getPopupContainer="triggerNode => triggerNode.parentNode" :fieldNames="{ label: 'label', value: 'value' }" optionFilterProp="label" showSearch placeholder="请选择门店类型"></a-select>
        </a-form-item>
        <a-form-item label="门店照片 " name="images">
          <uploadImg :max="10" :width="100" :height="100" :imgUrl="addParams.images" :form="addParams" path="images" :disabled="disabled" @success="uploadSuccess" />
          <div class="global-tip">
            建议尺寸 670px * 370px，比例（16:9），上传后，将展示在首页门店展示处
          </div>
        </a-form-item>
        <div class="form-top-titles-common">门店地址信息</div>
        <div class="form-top-line-common"></div>
        <a-form-item label="省份" name="province">
          <a-select ref="select" v-model:value="addParams.province" allowClear :disabled="disabled" :options="provinceOptions" style="width:400px" :getPopupContainer="triggerNode => triggerNode.parentNode" :fieldNames="{ label: 'label', value: 'value' }" optionFilterProp="label" showSearch placeholder="请选择省份"></a-select>
        </a-form-item>
        <a-form-item label="城市" name="city">
          <a-select ref="select" v-model:value="addParams.city" allowClear :disabled="disabled" :options="cityOptions" style="width:400px" :getPopupContainer="triggerNode => triggerNode.parentNode" :fieldNames="{ label: 'label', value: 'value' }" optionFilterProp="label" showSearch placeholder="请选择城市"></a-select>
        </a-form-item>
        <a-form-item label="详细地址" name="storeAddress">
          <a-input placeholder="请输入详细地址" style="width:400px" v-model:value="addParams.storeAddress" allow-clear show-count :maxlength="100" />
        </a-form-item>

        <div style="display:flex;width: 100%;">
          <a-form-item label="经度" name="longitude">
            <a-input style="width:200px" placeholder="请输入经度" v-model:value="addParams.longitude" allow-clear show-count :maxlength="20" />
          </a-form-item>
          <a-form-item label="维度" name="latitude">
            <a-input style="width:200px" placeholder="请输入维度" v-model:value="addParams.latitude" allow-clear show-count :maxlength="20" />
          </a-form-item>
        </div>
        <div class="form-top-titles-common">门店营业时间</div>
        <div class="form-top-line-common"></div>
        <a-form-item label="开业时间" name="openDate">
          <a-date-picker style="width:400px" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" v-model:value="addParams.openDate" />
        </a-form-item>
        <a-form-item label="是否闭店" name="isClose">
          <a-select ref="select" v-model:value="addParams.isClose" allowClear :disabled="disabled" :options="isCloseOptions" style="width:400px" :getPopupContainer="triggerNode => triggerNode.parentNode" :fieldNames="{ label: 'label', value: 'value' }" optionFilterProp="label" showSearch placeholder="请选择门店类型"></a-select>
          <div class="global-tip">
            选择闭店后，不论开业时间如何，该门店小程序附近门店列表不展示
          </div>
        </a-form-item>
        <a-form-item label="门店服务时间1" name="openingHourOne">
          <a-input placeholder="请输入门店服务时间1" style="width:400px" v-model:value="addParams.openingHourOne" allow-clear show-count :maxlength="30" />
          <div class="global-tip">
            示例：周一至周日10:00-22:00
          </div>
        </a-form-item>
        <a-form-item label="门店服务时间2" name="openingHourTwo">
          <a-input placeholder="请输入门店服务时间2" style="width:400px" v-model:value="addParams.openingHourTwo" allow-clear show-count :maxlength="30" />
          <div class="global-tip">
            示例：周一至周日10:00-22:00
          </div>
        </a-form-item>
        <!-- <a-form-item label="请勾选门店服务" name="serveIds">
          <a-checkbox-group v-model:value="addParams.serveIds" style="width: 100%">
            <a-row>
              <a-col :span="8" v-for="(item,index) in isCheckedOptions" :key="item.id">
                <a-checkbox :value="item.id">
                  <div class="check-item">
                    <div class="check-item-img">
                      <a-image :src="item.image" :width="50" :height="50"></a-image>
                    </div>
                    <div class="check-item-tit" :title="item.name">{{item.name}}</div>

                  </div>
                </a-checkbox>
              </a-col>
            </a-row>
          </a-checkbox-group>
        </a-form-item> -->
        <a-form-item label="门店企微二维码" name="weworkImages">
          <uploadImg :max="10" :width="100" :height="100" :imgUrl="addParams.weworkImages" :form="addParams" path="weworkImages" :disabled="disabled" @success="uploadSuccess" />
          <div class="global-tip">
            建议尺寸 300px * 300px，比例 1:1
          </div>
        </a-form-item>
      </a-form>
    </a-spin>
    <template #footer v-if="!disabled">
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="ok" :loading="loading">确定</a-button>
      </a-space>
    </template>

  </a-drawer>
</template>
<script setup>
import { storetypeOptions, isCloseOptions, } from '@/utils/dict-options'
import { checkstoreServe, userStoreInfo, updatestore, storeProvince, storeCity } from '@/http/index.js'
import { useGlobalStore } from '@/store'
import { message, Modal } from "ant-design-vue";
import _ from "lodash"
const $router = useRouter();
const global = useGlobalStore()
const addForm = ref(null)
import { cloneDeep } from 'lodash'
import { onMounted } from 'vue';
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  // 操作抽屉打开关闭
  visible: {
    type: Boolean,
    default: false
  },
  // 数据ID
  id: {
    type: String || Number,
    default: ''
  },
  // 操作类型
  type: {
    type: Number,
    default: 0, // 0-新增 1-编辑  2-查看
  }

})
// 置灰
const disabled = computed(() => {
  return props.type == 2
})

// 标题
const title = computed(() => {
  return ['新建', '编辑', '查看'][props.type] + '门店'
})

const { open, addParams, rules, loading, provinceOptions, cityOptions, isCheckedOptions } = toRefs(reactive({
  open: props.visible,
  provinceOptions: [],
  loading: false,
  cityOptions: [],
  isCheckedOptions: [],
  addParams: {
    storeSn: '',
    storeName: '',
    type: null,
    city: null,
    storeAddress: '',
    serveIds: [],
    openingHourOne: null,
    openingHourTwo: null,
    isClose: '',
    longitude: '',
    latitude: '',
    openDate: '',
    weworkImages: '',
    images: ''

  },
  rules: {
    storeSn: [{ required: true, message: '请输入门店编号', trigger: ['blur', 'change'] }],
    storeName: [{ required: true, message: '请输入门店名称', trigger: ['blur', 'change'] }],
    type: [{ required: true, message: '请选择门店类型', trigger: ['blur', 'change'] }],
    // images: [{ required: true, message: '请输上传', trigger: ['blur', 'change'] }],
    province: [{ required: true, message: '请选择省份', trigger: ['blur', 'change'] }],
    city: [{ required: true, message: '请选择城市', trigger: ['blur', 'change'] }],
    storeAddress: [{ required: true, message: '请输入详细地址', trigger: ['blur', 'change'] }],
    openDate: [{ required: true, message: '请选择日期', trigger: ['blur', 'change'] }],
    isClose: [{ required: true, message: '请选择是否闭店', trigger: ['blur', 'change'] }],
  }
})
);

// 暴露给父组件的属性和方法
defineExpose({
  addParams
})

watch(() => props.visible, (value) => {
  // 弹窗操作
  open.value = props.visible
  addForm.value?.resetFields()
  if (value) {

    initData()
  }
})
//所有接口调取出
const initData = async () => {
  const promiseArr = []
  if (props.id) {//
    promiseArr.push(checkstoreServe({ storeId: props.id }))
    promiseArr.push(userStoreInfo({ id: props.id }))
    promiseArr.push(storeProvince())
    promiseArr.push(storeCity())
  }

  // 如果有新的接口就在这里push
  loading.value = true
  try {
    const [storeServeList, StoreInfo, ProvinceList, CityList,] = await Promise.all(promiseArr)
    if (storeServeList) {
      isCheckedOptions.value = storeServeList.data
      //   if (storeServeList.data) {
      //     addParams.value.isCheckedList = storeServeList.data.filter(item => {
      //       if (item.isChecked == 1) {
      //         return item
      //       }
      //     }).map(item => item.id) || []
      //   }
    }
    if (StoreInfo) {
      if (!StoreInfo.data.openingHourOne) {
        StoreInfo.data.openingHourOne = null
      }
      if (!StoreInfo.data.openingHourTwo) {
        StoreInfo.data.openingHourTwo = null
      }
      addParams.value = {
        ...StoreInfo.data
      }
    }
    if (ProvinceList) {
      provinceOptions.value = ProvinceList.data.map(item => {
        return {
          label: item,
          value: item
        }
      })
    }
    if (CityList) {
      cityOptions.value = CityList.data.map(item => {
        return {
          label: item,
          value: item
        }
      })
    }
    loading.value = false
  } catch (error) {

    console.error('获取数据失败:', error)
  }
}

const uploadSuccess = async (data) => {
  let { form, path: key, imgUrl } = data;
  console.log("🚀 ~ uploadSuccess ~ form, path: key, imgUrl:", form, key, imgUrl)
  _.set(form, key.split('.'), imgUrl)
  // 清除校验
  await nextTick()
  addForm.value.validateFields([key.split('.')])
}

// 关闭弹窗
const onClose = () => {
  emit('cancel')
}

// 确定
const ok = () => {

  addForm.value.validate().then(res => {

    let params = cloneDeep(addParams.value)
    if (!params.openingHourOne) {
      params.openingHourOne = null
    }
    if (!params.openingHourTwo) {
      params.openingHourTwo = null
    }
    loading.value = true
    if (props.id) {
      // console.log('编辑');
      updatestore(params).then(res => {
        message.success(res.msg);
        emit('ok', props.id)
      }).finally(() => {
        loading.value = false
      })
    }

  })
}



</script>

<style scoped lang="scss">
:deep(.searchForm .ant-form-item) {
  margin-bottom: 16px;
}

:deep(.anticon-eye-invisible) {
  display: none;
}

.member-role {
  background: #f2f2f2;
  margin-bottom: 20px;
  padding: 5px 10px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &:nth-child(odd) {
    margin-right: 35px;
  }
}
.check-item {
  display: flex;
  align-items: center;
  padding: 10px;
  .check-item-img {
    margin-right: 10px;
    width: 50px;
    height: 50px;
  }
  .check-item-tit {
    flex: 1;
    width: 90px;
    white-space: nowrap; /* 确保文本在一行内显示 */
    overflow: hidden; /* 超出容器的文本将被隐藏 */
    text-overflow: ellipsis;
  }
}
</style>
