package com.dz.ms.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.constants.NumConstants;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.user.CrowdDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.user.CrowdFeignClient;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.CommonUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.product.dto.ShelfCampaignRuleDTO;
import com.dz.ms.product.dto.req.ShelfCampaignRuleDelParamDTO;
import com.dz.ms.product.dto.req.ShelfCampaignRuleProductSaveDTO;
import com.dz.ms.product.dto.req.ShelfCampaignRuleProductSaveParamDTO;
import com.dz.ms.product.dto.req.ShelfCampaignRuleSaveParamDTO;
import com.dz.ms.product.entity.ShelfCampaign;
import com.dz.ms.product.entity.ShelfCampaignRule;
import com.dz.ms.product.mapper.ShelfCampaignRuleMapper;
import com.dz.ms.product.service.ShelfCampaignRuleProductService;
import com.dz.ms.product.service.ShelfCampaignRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 营销活动规则
 *
 * @author: LiinNs
 * @date: 2024/11/20 11:39
 */
@Service
@Slf4j
public class ShelfCampaignRuleServiceImpl extends ServiceImpl<ShelfCampaignRuleMapper, ShelfCampaignRule> implements ShelfCampaignRuleService {

    @Resource
    private ShelfCampaignRuleMapper shelfCampaignRuleMapper;
    @Resource
    private CrowdFeignClient crowdFeignClient;
    @Resource
    private ShelfCampaignRuleProductService shelfCampaignRuleProductService;
    @Resource
    private RedisService redisService;
    /**
     * 分页查询营销活动规则
     *
     * @param param
     * @return PageInfo<ShelfCampaignRuleDTO>
     */
    @Override
    public PageInfo<ShelfCampaignRuleDTO> getShelfCampaignRuleList(ShelfCampaignRuleDTO param) {
        ShelfCampaignRule shelfCampaignRule = BeanCopierUtils.convertObjectTrim(param, ShelfCampaignRule.class);
        IPage<ShelfCampaignRule> page = shelfCampaignRuleMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), new QueryWrapper<>(shelfCampaignRule));
        return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), BeanCopierUtils.convertList(page.getRecords(), ShelfCampaignRuleDTO.class));
    }

    /**
     * 根据营销活动ID列表查询营销活动规则列表
     * @param campaignIds 营销活动ID列表
     * @param isQryCrowd 是否查询人群包信息
     * @return List<ShelfCampaignRuleDTO>
     */
    @Override
    public List<ShelfCampaignRuleDTO> getRuleListByCampaignIds(List<Long> campaignIds, boolean isQryCrowd) {
        List<ShelfCampaignRuleDTO> list = new ArrayList<>();
        List<ShelfCampaignRule> shelfCampaignRuleList = shelfCampaignRuleMapper.selectList(new LambdaQueryWrapper<ShelfCampaignRule>().in(ShelfCampaignRule::getCampaignId,campaignIds).orderByAsc(ShelfCampaignRule::getId));
        if(!CollectionUtils.isEmpty(shelfCampaignRuleList)){
            list = BeanCopierUtils.convertList(shelfCampaignRuleList, ShelfCampaignRuleDTO.class);
            //查询人群包数据
            if(isQryCrowd){
                for (ShelfCampaignRuleDTO shelfCampaignRuleDTO : list) {
                    Result<CrowdDTO> crowdRes = crowdFeignClient.getCrowdById(shelfCampaignRuleDTO.getGroupId());
                    if(crowdRes.isSuccess() && Objects.nonNull(crowdRes.getData())){
                        shelfCampaignRuleDTO.setCrowdDTO(crowdRes.getData());
                    }
                }
            }
        }
        return list;
    }

    /**
     * 根据营销活动ID列表查询营销活动规则列表
     * @param campaignIds 营销活动ID列表
     * @param isQryCrowd 是否查询人群包信息
     * @return Map<Long, ShelfCampaignRuleDTO>
     */
    @Override
    public Map<Long, ShelfCampaignRuleDTO> getRuleMapByCampaignIds(List<Long> campaignIds, boolean isQryCrowd) {
        Map<Long, ShelfCampaignRuleDTO> map = new HashMap<>();
        List<ShelfCampaignRuleDTO> shelfCampaignRuleList = this.getRuleListByCampaignIds(campaignIds, isQryCrowd);
        if(!CollectionUtils.isEmpty(shelfCampaignRuleList)){
            map = shelfCampaignRuleList.stream().collect(Collectors.toMap(ShelfCampaignRuleDTO::getId, shelfCampaignRuleDTO -> shelfCampaignRuleDTO));
        }
        return map;
    }

    /**
     * 根据ID查询营销活动规则
     * @param id id
     * @param isThrow 1:抛异常
     * @return ShelfCampaignRuleDTO
     */
    @Override
    public ShelfCampaignRuleDTO getShelfCampaignRuleById(Long id,Integer isThrow) {
        ShelfCampaignRule shelfCampaignRule = shelfCampaignRuleMapper.selectById(id);
        if(shelfCampaignRule == null && Objects.equals(isThrow, NumConstants.ONE)){
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[营销活动规则]未查询到此营销活动规则");
        }
        return BeanCopierUtils.convertObject(shelfCampaignRule, ShelfCampaignRuleDTO.class);
    }

    /**
     * 保存营销活动规则
     * @param param 营销活动规则
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveShelfCampaignRule(ShelfCampaign shelfCampaign, ShelfCampaignRuleSaveParamDTO param, boolean isAdd) {
        boolean checkRuleRes = checkSaveParam(param, isAdd);
        if(!checkRuleRes){
            return;
        }
        Long uid = SecurityContext.getUser().getUid();
        //加人群包逻辑
        if(!Objects.equals(param.getGroupType(),NumConstants.ONE)) {
            Result<Long> saveCrowdRes = crowdFeignClient.saveCrowd(param.getCrowdDTO());
            if(saveCrowdRes.isSuccess() && Objects.nonNull(saveCrowdRes.getData())){
                param.setGroupId(saveCrowdRes.getData());
            } else {
                throw new BusinessException("人群包添加失败,请检查人群包条件");
            }
        }
        ShelfCampaignRule shelfCampaignRule = new ShelfCampaignRule(param.getId(), param.getCampaignId(), param.getName(), param.getGroupId(), param.getContent(), param.getShelfId(), param.getRuleType(), param.getCycleCreated(), param.getPeriod(), param.getRuleNum());
        try {
            if (ParamUtils.isNullOr0Long(shelfCampaignRule.getId())) {
                shelfCampaignRuleMapper.insert(shelfCampaignRule);
            } else {
                shelfCampaignRuleMapper.updateById(shelfCampaignRule);
            }
            List<ShelfCampaignRuleProductSaveDTO> ruleProductList = param.getRuleProductList();
            for (ShelfCampaignRuleProductSaveDTO dto : ruleProductList) {
                if(Objects.equals(param.getRuleType(),NumConstants.ONE)){
                    dto.setRuleCreated(shelfCampaign.getOnStartTime());
                }
                if(Objects.equals(param.getRuleType(),NumConstants.TWO)){
                    dto.setRuleCreated(shelfCampaignRule.getCycleCreated());
                }
            }
            ShelfCampaignRuleProductSaveParamDTO saveParamDTO = new ShelfCampaignRuleProductSaveParamDTO();
            saveParamDTO.setShelfId(param.getShelfId());
            saveParamDTO.setCampaignId(param.getCampaignId());
            saveParamDTO.setRuleId(shelfCampaignRule.getId());
            saveParamDTO.setSaveRuleProductList(ruleProductList);
            shelfCampaignRuleProductService.saveShelfCampaignRuleProduct(saveParamDTO,isAdd);
        } catch (BusinessException e) {
            log.error("=================【营销活动规则】 saveShelfCampaignRule接口,uid:{},BusinessException报错:{},入参:{}",uid,e.getMessage(), CommonUtils.jsonStr(param),e);
            throw new BusinessException(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error("=================【营销活动规则】 saveShelfCampaignRule接口】,uid:{},Exception报错:{},入参:{}",uid,e.getMessage(),CommonUtils.jsonStr(param),e);
            throw new BusinessException(ErrorCode.INTERNAL_ERROR);
        }
    }

    private boolean checkSaveParam(ShelfCampaignRuleSaveParamDTO param, boolean isAdd) {
        if(Objects.isNull(param.getRuleType())){
            throw new BusinessException(ErrorCode.BAD_REQUEST, "限购规则设置必填");
        }
        if(Objects.equals(param.getRuleType(),NumConstants.TWO) && Objects.isNull(param.getCycleCreated())){
            throw new BusinessException(ErrorCode.BAD_REQUEST, "限购规则设置为周期时周期开始时间必填");
        }
        if(!isAdd){
            //校验营销活动规则
            List<ShelfCampaignRuleDTO> list = this.getRuleListByCampaignIds(Collections.singletonList(param.getCampaignId()), false);
            if(CollectionUtils.isEmpty(list)){
                return false;
            }
            //校验营销活动规则是否属于此活动
            if(list.stream().noneMatch(s -> Objects.equals(s.getId(),param.getId()))){
                return false;
            }
        }
        return true;
    }

    /**
     * 根据ID删除营销活动规则
     *
     * @param param
     */
    @Override
    public void deleteShelfCampaignRuleById(IdCodeDTO param) {
        shelfCampaignRuleMapper.deleteById(param.getId());
    }

    /**
     * 根据条件删除营销活动规则
     * @param param 删除条件
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRule(ShelfCampaignRuleDelParamDTO param) {
        shelfCampaignRuleMapper.deleteByParam(param);
    }

    /**
     * 根据人群包id,更新货架营销活动关联人群包id为null
     * @param groupId 人群包id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updGroupIdIntoNull(Long groupId) {
        shelfCampaignRuleMapper.updGroupIdIntoNull(groupId);
    }

}
