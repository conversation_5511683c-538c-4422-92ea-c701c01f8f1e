Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    title: {
      type: String,
      value: ''
    },
    // 默认下标
    index: {
      type: Number,
      value: 0,
      observer (value) {
        if (['', null, undefined].includes(value)) return
        this.setData({
          _value: [value],
          _show: false
        }, () => {
          let time = setTimeout(() => {
            clearTimeout(time)
            time = null
            this.setData({
              _show: true
            })
          }, 100)
        })
      }
    },
    range: {
      type: Array,
      value: []
    },
    zIndex: {
      type: Number,
      value: 10
    }
  },

  data: {
    _value: [0], // 选中的下标
    _show: true
  },
  methods: {
    close () {
      this.triggerEvent('close')
    },
    onPickerChange (e) {
      console.log('picker改变', e.detail.value)
      this.setData({
        _value: e.detail.value
      })
    },
    confirm () {
      if (this.data.range[this.data._value[0]].disabled) {
        wx.showToast({
          icon: 'none',
          title: '该选项不可选'
        })
        return
      }
      const index = this.data._value[0]
      this.triggerEvent('confirm', {
        value: index,
        index,
        data: this.data.range[index]
      })
      this.triggerEvent('close')
    }
  }
})
