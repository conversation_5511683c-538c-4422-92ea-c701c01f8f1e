// 所有的命名必须全局唯一
import service from '@/utils/request.js'
import { PRODUCT_TYPE_OBJ } from '@/utils/constants.js'

// 商品新增
export function productAdd(data = {}) {
    return service({
        url: '/crm/product/product/add',
        method: 'post',
        data
    })
}

// 商品编辑
export function producUpdate(data = {}) {
    return service({
        url: '/crm/product/product/update',
        method: 'post',
        data
    })
}

// 商品详情
export function producInfo(data = {}) {
    return service({
        url: '/crm/product/product/info',
        method: 'get',
        data
    })
}

// 商品列表
export function productList(data = {}) {
    return service({
        url: '/crm/product/product/list',
        method: 'post',
        data
    }).then(res => {
      res.data.list.forEach(v => {
        v.pdTypeDesc = PRODUCT_TYPE_OBJ[v.pdType]
        v.tagList = v.tagList || []
        v.exchangeNum = v.exchangeNum || 0
        v.shelfImgList = v.shelfImgList || []
        v.shelfImg = v.shelfImg || v.shelfImgList[0] || []
      })
      return res
    })
}

// 商品删除
export function producDelete(data = {}) {
    return service({
        url: '/crm/product/product/delete',
        method: 'post',
        data
    })
}

// 所处货架
export function shelfList_by_name(data = {}) {
    return service({
        url: 'crm/product/shelf/list_by_name',
        method: 'get',
        data
    })
}

// 启用停用
export function productUpdate_state(data = {}) {
    return service({
        url: '/crm/product/product/update_state',
        method: 'post',
        data
    })
}
