package com.dz.common.core.enums;

import java.util.Objects;

/**
 * 抽奖上限次数周期
 * <AUTHOR>
 */
public enum LimitPeriodEnum {
    DAY(0, "日"),
    WEEK(1, "周"),
    MONTH(2, "月"),
    YEAR(3, "年"),
    ;

    private final Integer code;
    private final String value;

    LimitPeriodEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (LimitPeriodEnum resultEnum : LimitPeriodEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum.value;
            }
        }
        return null;
    }
}
