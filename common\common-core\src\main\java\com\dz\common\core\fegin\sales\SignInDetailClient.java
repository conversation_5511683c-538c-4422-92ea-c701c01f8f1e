package com.dz.common.core.fegin.sales;


import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.DownloadAddParamDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/7
 */
@FeignClient(name = ServiceConstant.SALES_SERVICE_NAME, contextId = "SignInDetailClient")
public interface SignInDetailClient {


    @PostMapping(value = "/sales/sign_in_detail/export_list")
    Result<Void> exportList(DownloadAddParamDTO downloadAddParamDTO);

    /**
     * 签到提醒任务执行完成
     *
     * @param campaignCode
     * @return
     */
    @PostMapping(value = "/sales/sign_in_detail/pushMsg")
    Result<Void> signInPushMsgJob(@RequestParam("campaignCode") String campaignCode);
    /**
     * 用户签到失败任务执行完成
     *
     * @param campaignCode
     * @return
     */
    @PostMapping(value = "/sales/sign_in_detail/fail")
    Result<Void> signInUserFailJob(@RequestParam("campaignCode") String campaignCode);
}
