<template>
  <a-modal v-model:open="thisFields.open" :title="``" @ok="thisMethods.handleOk" @cancel="thisMethods.cancel" :okButtonProps="{disabled:thisFields.loading}" width="800px">
    <a-form class="form" ref="formRef" :model="formFields" :rules="formRules" :labelCol="{ style: 'width:160px' }">
      <BaseProductExpectUpDownShelfTimeRadio label="请选择上下架库周期" name="onTypeTemp" v-model="formFields.onTypeTemp" />
      <a-form-item label="请填写上下架库时间" name="onDaysTemp" v-if="formFields.onTypeTemp===3">
        <a-input placeholder="请输入" style="width:212px;" @blur="e=>{const match=e.target.value.match(/[1-9]\d*/ig)||[];formFields.onDaysTemp=match[0]||'';}" v-model:value="formFields.onDaysTemp">
          <template #prefix>每</template>
          <template #suffix>天</template>
        </a-input>
        <div class="global-tip">第一次设置后，当日的24点会执行一次任务，后续根据周期的天数循环执行，直至删除任务</div>
      </a-form-item>
      <BaseDateTime v-if="formFields.onTypeTemp===2" label="请填写上下架库时间" name="onTimeTemp" v-model="formFields.onTimeTemp" />
    </a-form>
  </a-modal>
</template>

<script setup>
import { onMounted, reactive, watch } from 'vue'
import { cloneDeep } from 'lodash'

const props = defineProps({
  record: {
    type: Object,
    default: () => ({})
  },
  modelValue: {
    type: Boolean,
    default: false
  }
})
const emits = defineEmits(['update:modelValue', 'ok'])

const formRef = ref(null)
const getDefaultFormFields = () => ({
  onTypeTemp: 2,
  onTimeTemp: '',
  onDaysTemp: ''
})
const formFields = reactive(getDefaultFormFields())
const formRules = reactive({
  onTypeTemp: [{ required: true, message: '本项必选' }],
  onTimeTemp: [{ required: true, message: '本项必填' }],
  onDaysTemp: [{ required: true, message: '本项必选' }]
})

const thisFields = reactive({
  loading: false,
  open: false,
  record: {}
})
const thisMethods = {
  setOpen() {
    thisFields.open = props.modelValue
    formRef.value?.resetFields()
    Object.assign(formFields, getDefaultFormFields())
    if (thisFields.open) {
      thisMethods.initData()
    }
  },
  async initData() {
    Object.assign(formFields, props.record)
  },
  cancel() {
    emits('update:modelValue', false)
  },
  async handleOk() {
    await formRef.value.validate()
    let params = cloneDeep(formFields)
    console.log('handleOk：', params)
    emits('ok', params)
    emits('update:modelValue', false)
  }
}

onMounted(() => thisMethods.setOpen())
watch(() => props.modelValue, () => thisMethods.setOpen())
</script>

<style lang="scss" scoped>
.form {
  padding-top: 20px;
}

.addWrap {
  display: flex;
  align-items: center;

  .addButton {
    margin-bottom: 24px;
  }
}

.delWrap {
  display: flex;
  align-items: center;
  cursor: move;

  .delName {
    margin-left: 10px;
    width: 202px;
  }
}
</style>
