<template>

  <a-modal :title="title" width="576px" v-if="open" placement="right" :maskClosable="false" :closable="true" :open="open" @cancel="onClose">
    <!-- <a-spin :spinning="loading"> -->

    <a-form class="form" ref="addForm" :model="addParams" :rules="rules" :labelCol="{ style: 'width:160px' }">

      <a-form-item label="服务名称" name="name">
        <a-input placeholder="请输入" style="width:300px;" v-model:value="addParams.name" show-count :maxlength="20" />
      </a-form-item>
      <a-form-item label="请上传服务图片" name="image">
        <uploadImg :max="10" :width="100" :height="100" :imgUrl="addParams.image" :form="addParams" path="image" :disabled="disabled" @success="uploadSuccess" />
        <div class="global-tip">
          建议尺寸 44px * 44px，比例 1:1
        </div>
      </a-form-item>

    </a-form>
    <!--  -->
    <!-- </a-spin> -->
    <template #footer v-if="!disabled">
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="ok" :loading="loading">确定</a-button>
      </a-space>
    </template>

  </a-modal>

</template>
<script setup>
import { serveSave, } from '@/http/index.js'
import { useGlobalStore } from '@/store'
import { message, Modal } from "ant-design-vue";
import _ from "lodash"
import { cloneDeep } from 'lodash'
const global = useGlobalStore()
const addForm = ref(null)

import { onMounted } from 'vue';
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  // 操作抽屉打开关闭
  visible: {
    type: Boolean,
    default: false
  },
  // 数据ID
  id: {
    type: String || Number,
    default: ''
  },
  // 操作类型
  type: {
    type: Number,
    default: 0, // 0-新增 1-编辑  2-查看
  },
  addType: {
    type: Number,
    default: 1, // 1 级 2级
  },
  title: {
    type: String || Number,
    default: ''
  }

})
// 置灰
const disabled = computed(() => {
  console.log(addParams.value, 'addType');

  return props.type == 2
})
let addType1 = computed(() => {


  return props.addType
})
// 标题
const title = computed(() => {
  return ['新建', '编辑', '查看'][props.type] + '服务'
})

const { open, addParams, rules, loading, } = toRefs(reactive({
  open: props.visible,


  loading: false,

  addParams: {
    name: '',
    image: ''
  },
  rules: {
    name: [{ required: true, message: '请输入服务名称', trigger: ['blur', 'change'] }],
    image: [{ required: true, message: '请上传图片', trigger: ['blur', 'change'] }],
  }
})
);
// onMounted(() => {
//     initData()
// })
watch(() => props.visible, (value) => {
  // 弹窗操作
  open.value = props.visible
  addForm.value?.resetFields()
  if (open.value) {

    initData()
  }
})

//所有接口调取出
const initData = async () => {
  const promiseArr = []

  if (props.id) {

    // promiseArr.push(tag_info({ id: props.id }))
  }

  // 如果有新的接口就在这里push
  loading.value = true
  try {
    // const [] = await Promise.all(promiseArr)


    loading.value = false
  } catch (error) {
    loading.value = false
    console.error('获取数据失败:', error)
  }
}

const uploadSuccess = async (data) => {
  let { form, path: key, imgUrl } = data;
  console.log("🚀 ~ uploadSuccess ~ form, path: key, imgUrl:", form, key, imgUrl)
  _.set(form, key.split('.'), imgUrl)
  // 清除校验
  await nextTick()
  addForm.value.validateFields([key.split('.')])
}


// 关闭弹窗
const onClose = () => {
  emit('cancel')
}

// 确定
const ok = () => {

  addForm.value.validate().then(res => {

    let params = cloneDeep(addParams.value)


    loading.value = true
    if (props.id) {
      // console.log('编辑');
      serveSave(params).then(res => {
        message.success(res.msg);
        emit('ok', props.id)
      }).finally(() => {
        loading.value = false
      })
    } else {
      // console.log(params, 'xin');
      params.status = 0
      serveSave(params).then(res => {
        message.success(res.msg);
        emit('ok', props.id)
      }).finally(() => {
        loading.value = false
      })
    }

  })
}



</script>

<style scoped lang="scss">
.form {
}
:deep(.searchForm .ant-form-item) {
  // margin-bottom: 16px;
}

:deep(.anticon-eye-invisible) {
  display: none;
}

.member-role {
  background: #f2f2f2;
  margin-bottom: 20px;
  padding: 5px 10px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &:nth-child(odd) {
    margin-right: 35px;
  }
}
</style>
