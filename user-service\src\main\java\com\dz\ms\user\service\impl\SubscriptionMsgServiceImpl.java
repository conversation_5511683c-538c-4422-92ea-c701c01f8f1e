package com.dz.ms.user.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.basic.GiftConfigDTO;
import com.dz.common.core.dto.basic.GiftConfigOtherDTO;
import com.dz.common.core.dto.basic.SubscribeMsgSendDTO;
import com.dz.common.core.dto.user.UserInfoDTO;
import com.dz.common.core.enums.SubscribeMsgEnum;
import com.dz.common.core.fegin.basic.DefaultDataFeginClient;
import com.dz.common.core.fegin.basic.MpMsgFeignClient;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.vo.SubscriptionMsgVo;
import com.dz.ms.user.entity.RightsReceiveRecord;
import com.dz.ms.user.entity.SubscriptionMsg;
import com.dz.ms.user.mapper.MyMsgMapper;
import com.dz.ms.user.service.MUJIOpenApiService;
import com.dz.ms.user.service.SubscriptionMsgService;
import com.dz.ms.user.service.UserInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 我的礼券
 */
@Service
@Slf4j
public class SubscriptionMsgServiceImpl implements SubscriptionMsgService {

    @Autowired
    private MyMsgMapper myMsgMapper;
    @Autowired
    private MUJIOpenApiService openApiService;
    @Autowired
    private UserInfoService userInfoService;
    @Autowired
    private MpMsgFeignClient mpMsgFeignClient;
    @Autowired
    private DefaultDataFeginClient defaultDataFeginClient;

    /**
     * expireCoupon（优惠券过期通知）memberReceive（会员权益领取通知）taskEnd（活动即将结束提醒）expirePoints(积分过期提醒)getPoints(积分到账提醒)
     * taskEnd定时任务、expirePoints人群包定时任务、getPoints这三个是不用校发验是否发过，每次调用都发送
     * expireCoupon 一天发一次
     * memberReceive 跟核心礼遇权益匹配，一种权益发一次
     * @param param
     * @throws ParseException
     */
    @Override
    public void add(SubscriptionMsgVo param) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date now=new Date();
        SimpleDateFormat sdfTable = new SimpleDateFormat("yyyyMM");
        String nowDay = sdfTable.format(now);
        subTableList(nowDay);
        //查询用户会员信息
        UserInfoDTO userInfoDTO=userInfoService.getUserInfo(SecurityContext.getUser().getUid());
        for (String code : param.getMsgCode()){
            if (code.equals("expireCoupon")){//优惠券过期通知
                if (null != userInfoDTO.getCardNo()){
                    //查询会员券列表
                    JSONObject memberCouponList=openApiService.memberCouponList(userInfoDTO.getCardNo(),null,1,1,100);
                    if (null != memberCouponList){
                        JSONArray list=memberCouponList.getJSONArray("items");
                        if (!CollectionUtils.isEmpty(list)){
                            for (Object account :list){
                                //将jsonObject转为map
                                JSONObject jsonObject=JSONObject.parseObject(JSONObject.toJSONString(account));
                                //查询当天是否已经有记录
                                List<SubscriptionMsg> myMsgList = myMsgMapper.selectSubscriptionByMsgCode(SecurityContext.getUser().getUid(), code,jsonObject.getString("coupon_code"),nowDay);
                                if (!CollectionUtils.isEmpty(myMsgList)){
                                    continue;
                                }
                                String endTime=jsonObject.getString("end_time");
                                Date endTimeDate=sdf.parse(endTime);
                                //计算endTimeDate-now的小时数
                                long hours = (endTimeDate.getTime() - now.getTime()) / (1000 * 60 * 60);
                                if (hours <= 72){
                                    SubscriptionMsg myMsg = new SubscriptionMsg();
                                    myMsg.setUserId(SecurityContext.getUser().getUid());
                                    myMsg.setCreateAt(String.valueOf(SecurityContext.getUser().getUid()));
                                    myMsg.setTenantId(SecurityContext.getUser().getTenantId());
                                    myMsg.setCreateTime(new Date());
                                    myMsg.setMsgCode(code);
                                    myMsg.setSendDesc(jsonObject.getString("coupon_code"));
                                    myMsgMapper.inserSubscriptionByTable(myMsg, nowDay);
                                    //发送订阅消息
                                    SubscribeMsgSendDTO subscribeMsgSendDTO = new SubscribeMsgSendDTO();
                                    subscribeMsgSendDTO.setMsgCode(SubscribeMsgEnum.COUPON_EXPIRE.getMsgCode());
                                    List<Long> uids = new ArrayList<>();
                                    uids.add(SecurityContext.getUser().getUid());
                                    subscribeMsgSendDTO.setUids(uids);
                                    String[] content = new String[3];
                                    content[0]=jsonObject.getString("name");
                                    content[1]=jsonObject.getString("end_time");
                                    content[2]="您有一张优惠券即将过期,快来使用吧";
                                    subscribeMsgSendDTO.setContent(content);
                                    mpMsgFeignClient.sendMiniappSubscribeMsg(subscribeMsgSendDTO, SecurityContext.getUser().getTenantId());
                                }
                            }
                        }
                    }
                }else{
                    continue;
                }
            }
            if (code.equals("memberReceive")){//会员权益领取通知
                if (null != userInfoDTO.getCardNo()){
                    //查询活动券
                    JSONObject activityCouponList=openApiService.activityCouponList(userInfoDTO.getCardNo());
                    if (null != activityCouponList){
                        JSONArray activityList=activityCouponList.getJSONArray("items");
                        if (!CollectionUtils.isEmpty(activityList)){
                            //查询核心礼遇权益
                            Result<List<GiftConfigDTO>> result=defaultDataFeginClient.getGiftList();
                            List<GiftConfigDTO> giftConfigDTOS=result.getData();
                            List<String> couponIdListAll=new ArrayList<>();
                            for (GiftConfigDTO giftConfigDTO:giftConfigDTOS) {
                                if (giftConfigDTO.getGiftType()==4){
                                    List<GiftConfigOtherDTO> giftConfigOtherDTO=giftConfigDTO.getGiftConfigOtherDTO();
                                    for (GiftConfigOtherDTO giftConfigOtherDTO1:giftConfigOtherDTO){
                                        if (!org.springframework.util.StringUtils.isEmpty(giftConfigOtherDTO1.getActivityId())) {
                                            String[] couponIds = giftConfigOtherDTO1.getActivityId().split(",");
                                            List<String> couponIdList = new ArrayList<>(Arrays.asList(couponIds));
                                            couponIdListAll.addAll(couponIdList);
                                        }
                                    }
                                }else{
                                    if (!org.springframework.util.StringUtils.isEmpty(giftConfigDTO.getCouponId())) {
                                        String[] couponIds = giftConfigDTO.getCouponId().split(",");
                                        List<String> couponIdList = new ArrayList<>(Arrays.asList(couponIds));
                                        couponIdListAll.addAll(couponIdList);
                                    }
                                }
                            }
                            for (Object jsonObject:activityList){
                                //将jsonObject转为map
                                JSONObject activityJsonObject=JSONObject.parseObject(JSONObject.toJSONString(jsonObject));
                                //记录订阅消息发送 只有核心礼遇里面的权益才发订阅消息
                                if (!couponIdListAll.contains(activityJsonObject.getString("activity_id"))){
                                    continue;
                                }
                                //查询是否已经有某个权益提醒记录
                                SubscriptionMsg subscriptionMsg = myMsgMapper.selectSubscriptionByActivityId(SecurityContext.getUser().getUid(), activityJsonObject.getString("activity_id"));
                                if (null != subscriptionMsg){
                                    continue;
                                }
                                SubscriptionMsg myMsg = new SubscriptionMsg();
                                myMsg.setUserId(SecurityContext.getUser().getUid());
                                myMsg.setCreateAt(String.valueOf(SecurityContext.getUser().getUid()));
                                myMsg.setTenantId(SecurityContext.getUser().getTenantId());
                                myMsg.setCreateTime(new Date());
                                myMsg.setMsgCode(activityJsonObject.getString("activity_id"));
                                myMsgMapper.inserSubscriptionByActivityId(myMsg);
                                //发送订阅消息
                                SubscribeMsgSendDTO subscribeMsgSendDTO = new SubscribeMsgSendDTO();
                                subscribeMsgSendDTO.setMsgCode(SubscribeMsgEnum.BENEFIT_RECEIVE.getMsgCode());
                                List<Long> uids = new ArrayList<>();
                                uids.add(SecurityContext.getUser().getUid());
                                subscribeMsgSendDTO.setUids(uids);
                                String[] content = new String[3];
                                content[0]=activityJsonObject.getString("name");
                                String subName="";
                                if (!org.springframework.util.StringUtils.isEmpty(activityJsonObject.getString("sub_name"))){
                                    subName=activityJsonObject.getString("sub_name");
                                }else{
                                    subName=activityJsonObject.getString("name");
                                }
                                content[1]=subName;
                                content[2]="您有待领取的礼物,快去小程序内查看吧~";
                                subscribeMsgSendDTO.setContent(content);
                                mpMsgFeignClient.sendMiniappSubscribeMsg(subscribeMsgSendDTO, SecurityContext.getUser().getTenantId());
                            }
                        }
                    }
                }else{
                    continue;
                }
            }
            if (!code.equals("memberReceive") && !code.equals("expireCoupon")){
                SubscriptionMsg myMsg = new SubscriptionMsg();
                myMsg.setUserId(SecurityContext.getUser().getUid());
                myMsg.setCreateAt(String.valueOf(SecurityContext.getUser().getUid()));
                myMsg.setTenantId(SecurityContext.getUser().getTenantId());
                myMsg.setCreateTime(new Date());
                myMsg.setMsgCode(code);
                myMsg.setSendDesc(param.getSendDesc());
                myMsgMapper.inserSubscriptionByTable(myMsg, nowDay);
            }
        }
    }

    /**
     * 判断当月订阅消息记录表是否存在，不存在则新建
     * @param cdpDay
     */
    private String subTableList(String cdpDay) {
        try {
            List<String> tableList = myMsgMapper.querySubscriptionTableList();
            boolean hasCurrentTable = false;
            if (!CollectionUtils.isEmpty(tableList)) {
                for (String table : tableList) {
                    String newTableName="t_subscription_msg_"+cdpDay;
                    if (newTableName.equals(table)) {
                        hasCurrentTable = true;
                    }
                }
            }
            if (!hasCurrentTable) {
                myMsgMapper.createSubscriptionTable(cdpDay);
            }
            return cdpDay;
        } catch (Exception e) {
            log.error("t_subscription_msg_创建失败", e);
            return null;
        }
    }
}
