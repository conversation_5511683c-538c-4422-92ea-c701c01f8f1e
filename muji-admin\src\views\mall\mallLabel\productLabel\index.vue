<template>

  <layout>
    <template v-slot:headerTop>
      <a-tabs v-model:activeKey="activeKey" @change="tagChange">
        <a-tab-pane v-for="(item, key) in tabs" :key="key" :tab="item">
        </a-tab-pane>
      </a-tabs>
    </template>

    <template v-slot:topRight>
      <a-space>
        <a-button type="primary" :disabled="!$hasPermission('productLabel:add')" @click="addChang">新建标签</a-button>

      </a-space>

    </template>
    <template v-slot="{ height }">
      <a-table :indentSize="20" row-key="id" :scroll="{ scrollToFirstRowOnChange: true, y: height - 88, x: '100%' }" :dataSource="dataSource" :columns="tableHeader" :pagination="pagination" :loading="loading" @change="loadData">
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'index'">
            {{ (current - 1) * pageSize + 1 + index }}
          </template>
          <template v-if="column.key === 'userNum'">
            {{ record.userNum || 0 }}
          </template>
          <template v-if="column.key === 'action'">

            <!-- -->
            <a-button type="link" :disabled="!$hasPermission('productLabel:edit')" @click="EditRole(record)">编辑</a-button>
            <a-divider type="vertical" />
            <a-popconfirm :disabled="!$hasPermission('productLabel:del')" @confirm="handleDelete(record)">
              <template #title>
                <div style="width:200px">该标签正在被商品、货架使用，删除后会会一并失效，无法恢复，是否确认删除？</div>

              </template>
              <a-button :disabled="!$hasPermission('productLabel:del')" type="link">删除</a-button>
            </a-popconfirm>
          </template>
        </template>
      </a-table>
    </template>

  </layout>
  <addLabel :visible="visible" :addType="activeKey" @ok="updateList" @cancel="visible = false" :id="id" :type="type" />
</template>
<script setup>
import addLabel from './components/addLabel.vue';
import { useRoute, useRouter } from 'vue-router'
import { usePagination, useRequest, } from 'vue-request';
import { tag_infoList, tag_infoDelete, productUpdate_state } from '@/http/index.js'
import { message, Modal } from "ant-design-vue";
// 分页参数
let pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ["bottomLeft"],
  };
});
// 表格分页数据获取
const total = ref(0)


const { formParams, tableHeader, visible, id, type, activeKey, tabs } = toRefs(reactive({


  id: '',
  type: 0,// 0-新增 1-编辑  2-查看
  visible: false,
  formParams: {
    cate: ''
  },
  activeKey: '1',
  tabs: {
    1: "一级标签管理",
    2: "二级标签管理",
  },
  tableHeader: [{
    title: '序号',
    key: 'index',
    align: 'center',
    width: 80
  },
  {
    title: '标签名称',
    dataIndex: 'name',
    width: 180,
    align: 'center',
  },

  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    align: 'center',
    width: 150,
    fixed: 'right'
  }]
})
);
const { data: dataSource, run, loading, pageSize, current, refresh } = usePagination((param) => {
  // 这里修改参数
  // return resignationInheritanceList({ ...param, ...getParams() })
  formParams.value['cate'] = activeKey.value
  return tag_infoList({ ...param, ...formParams.value })
},
  {
    manual: false, // 修改为false,让其自动执行
    pagination: {
      currentKey: 'pageNum',  // 通过该值指定接口 当前页数 参数的属性值
      pageSizeKey: 'pageSize',// 通过该值指定接口 每页获取条数 参数的属性值
      totalKey: 'data.data.count',  // 指定 data 中 total 属性的路径
    },
    formatResult: res => { // 返回数据格式化
      total.value = res.data.count
      return res.data.list
    }
  });
function tagChange() {
  formParams.value['cate'] = activeKey.value
  loadData()
}
function updateList(value) {
  visible.value = false;
  // 新增 重置搜索数据
  if (!value) {
    resetData();
  } else {
    // 查看、编辑 不需要清空搜索数据 直接刷新
    refresh();
  }
}
function EditRole(record) {
  visible.value = true
  type.value = 1
  id.value = record.id
  console.log(record, id.value);

}
//删除
const handleDelete = (record) => {
  tag_infoDelete({ id: record.id }).then(res => {
    if (res.code === 0) {
      message.success('删除成功')
      resetData()
    }
  })
}
// 加载数据
const loadData = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}


// 刷新数据
const refreshData = () => {
  run({ pageNum: 1, pageSize: pageSize.value })
};

// 重置数据
const resetData = () => {
  formParams.value = {
    name: '',
  }
  refreshData()
}
function addChang() {
  visible.value = true
  type.value = 0
  id.value = ''
}
</script>
