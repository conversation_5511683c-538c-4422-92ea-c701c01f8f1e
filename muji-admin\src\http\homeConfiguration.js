// 所有的命名必须全局唯一
import service from '@/utils/request.js'

// 配置对象
export function basicgiftmap(data) {
    return service({
        url: '/crm/basic/gift/map',
        method: 'get',
        data
    })
}


// 列表
export function interactionList(data) {
    return service({
        url: '/crm/basic/gift/list',
        method: 'get',
        data
    })
}


// 列表编辑
export function giftconfigUpdate(data) {
    return service({
        url: '/crm/basic/gift/config/udpate',
        method: 'post',
        data
    })
}