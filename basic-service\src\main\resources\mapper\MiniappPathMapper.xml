<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.basic.mapper.MiniappPathMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    id,
  	    `path`,
        `name`,
  	    tenant_id,
  	    page_type,
  	    create_time
    </sql>

    <!-- 查询示例 -->
    <select id="selectByPageType" parameterType="java.lang.Integer" resultType="com.dz.ms.basic.entity.MiniappPath">
        select
        <include refid="Base_Column_List" />
        from miniapp_path
        where page_type = #{pageType}
    </select>

</mapper>
