<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.order.mapper.OrderDetailMapper">

	<!-- 查询基础字段 -->
    <sql id="Base_Column_List">
		id,
  	    order_code,
  	    product_id,
  	    product_name,
  	    pd_type,
  	    vender_id,
		coupon_code,
		stock_id,
  	    img_url,
  	    cost_point,
  	    pre_point,
  	    cost_price,
  	    p_cost_point,
  	    p_pre_point,
  	    p_cost_price,
  	    shelf_product_id,
  	    shelf_id,
  	    shelf_name,
  	    s_cost_point,
  	    s_pre_point,
  	    campaign_id,
  	    campaign_name,
  	    rule_id,
  	    rule_name,
  	    r_cost_point,
  	    r_pre_point,
		r_everyone_limit,
		r_purchase_limit,
  	    number,
  	    real_point,
  	    real_amount,
  	    delivery_number,
  	    status,
  	    send_status,
  	    tenant_id,
  	    created,
  	    modified,
  	    is_deleted
    </sql>
    <insert id="insertBatchSomeColumn">
		INSERT INTO ods_order_product (
		id, biz_date, checkout_dt_str, couponname_str, item_id_str, item_name_str,
		qty_str, sale_amt_str, real_amt_str, promname1_str, promname2_str, promname5_str,
		promdisname_str, up_serial_no_str, total_amt_str, total_qty_str, sale_ty_str,
		member_code, sale_id_str, pos_id_str, staff_id_str, shop_id_str, shop_name_str,
		saleid_fr_str, create_time, tenant_id
		)
		VALUES
		<foreach collection="odsOrderProductList" item="item" separator=",">
			(
			#{item.id}, #{item.bizDate}, #{item.checkoutDtStr}, #{item.couponnameStr}, #{item.itemIdStr}, #{item.itemNameStr},
			#{item.qtyStr}, #{item.saleAmtStr}, #{item.realAmtStr}, #{item.promname1Str}, #{item.promname2Str}, #{item.promname5Str},
			#{item.promdisnameStr}, #{item.upSerialNoStr}, #{item.totalAmtStr}, #{item.totalQtyStr}, #{item.saleTyStr},
			#{item.memberCode}, #{item.saleIdStr}, #{item.posIdStr}, #{item.staffIdStr}, #{item.shopIdStr}, #{item.shopNameStr},
			#{item.saleidFrStr}, #{item.createTime}, #{item.tenantId}
			)
		</foreach>
	</insert>
	<delete id="removeOrder">
		DELETE FROM ${tableName}
		WHERE create_time &lt; #{ninetyDaysAgo}
	</delete>

	<!-- 分批删除操作 -->
	<delete id="removeOrderBatch" parameterType="map">
		DELETE FROM ${tableName}
		WHERE create_time &lt; #{ninetyDaysAgo}
		LIMIT #{offset}, #{batchSize}
	</delete>


	<!-- 查询示例 -->
	<select id="selectDemo" parameterType="java.lang.Long" resultType="com.dz.ms.order.entity.OrderDetail">
		select
		<include refid="Base_Column_List"/>
		from order_detail
		where id = #{id,jdbcType=BIGINT}
		AND tenant_id = #{tenantId}
	</select>

	<select id="selectPurchaseStatic" resultType="com.dz.common.core.dto.order.PurchaseStaticDTO">
		select od.number,
		od.rule_id,
		od.shelf_product_id
		from order_detail od
		left join exchange_order eo on od.order_code = eo.order_code
		where od.shelf_id = #{shelfId}
		and eo.user_id = #{userId}
		<choose>
			<when test="ruleId != null">
				and od.rule_id = #{ruleId}
			</when>
			<otherwise>
				and (od.r_everyone_limit is null or od.r_everyone_limit = 0)
				and (od.r_purchase_limit is null or od.r_purchase_limit = 0)
			</otherwise>
		</choose>
		<if test="periodStart != null">
			and eo.created >= #{periodStart}
		</if>
		<if test="periodEnd != null">
			and eo.created &lt;= #{periodEnd}
		</if>
	</select>

	<select id="selectCpStatic" resultType="com.dz.common.core.dto.product.CpStaticDTO">
		SELECT DISTINCT
		od.shelf_product_id,
		od.product_id,
		od.vender_id 'cpCode',
		SUM(od.number) 'sellAmount',
		SUM(od.real_point) 'totalPointAmount',
		COUNT(distinct eo.user_id) 'sellPeopleAmount'
		FROM order_detail od
		LEFT JOIN exchange_order eo ON od.order_code = eo.order_code
		<where>
			<if test="param.cpCode != null and param.cpCode != ''">
				od.vender_id = #{param.cpCode}
			</if>
			<if test="param.sellDateStart != null">
				and od.created >= #{param.sellDateStart}
			</if>
			<if test="param.sellDateEnd != null">
				and od.created &lt;= #{param.sellDateEnd}
			</if>
		</where>
		GROUP BY od.shelf_product_id,od.product_id,od.vender_id
		ORDER BY od.shelf_product_id ASC
	</select>

	<select id="selectMujiOrder" resultType="com.dz.common.core.dto.MujiOrder">
		SELECT
		odc.id,
		odc.order_code 'exchangeSn',
		eo.user_crm_code 'memberCode',
		od.product_id 'goodsId',
		od.product_name 'goodsName',
		od.vender_id 'stockId',
		eo.out_sn 'bonusSn',
		od.real_point 'bonus',
		odc.coupon_code 'couponCode',
		eo.created 'createdAt',
		eo.modified 'updatedAt',
		DATE_FORMAT( eo.created, "%Y%m" ) 'dt'
		FROM
		order_detail_coupon odc
		LEFT JOIN order_detail od ON odc.order_detail_id = od.id
		LEFT JOIN exchange_order eo ON odc.order_code = eo.order_code
		<where>
			<if test="beginTime != null">
				AND eo.created &gt;= #{beginTime}
			</if>
			<if test="endTime != null">
				AND eo.created &lt;= #{endTime}
			</if>
		</where>
	</select>

	<select id="sftpOrderList" resultType="com.dz.ms.order.entity.OdsOrderProduct">
		SELECT
			sale_id_str,item_id_str
		FROM
			ods_order_product_${mod} odc
		where odc.member_code=#{memberCode} and odc.total_amt_str>1
		<if test="beginTime != null and endTime == null">
			AND odc.checkout_dt_str &gt;= #{beginTime}
		</if>
		<if test="beginTime != null and endTime != null">
			AND odc.checkout_dt_str BETWEEN #{beginTime}
			AND #{endTime}
		</if>
	</select>

</mapper>
