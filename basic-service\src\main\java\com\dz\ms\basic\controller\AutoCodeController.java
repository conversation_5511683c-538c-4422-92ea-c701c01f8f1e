package com.dz.ms.basic.controller;

import com.dz.common.base.vo.Result;
import com.dz.ms.basic.dto.AutoCodeColumnDTO;
import com.dz.ms.basic.dto.AutoCodeTableDTO;
import com.dz.ms.basic.service.AutoCodeService;
import com.dz.ms.basic.utils.DelAllFile;
import com.dz.ms.basic.utils.FileDownload;
import com.dz.ms.basic.utils.FileZip;
import com.dz.ms.basic.utils.Freemarker;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Hidden;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Hidden
@Api(tags="代码生成")
@Controller
@RequestMapping(value="/oms")
public class AutoCodeController {

    @Resource
    private AutoCodeService autoCodeService;

    @ApiOperation("获取数据库列表")
    @ResponseBody
    @GetMapping( value = "/autocode/database")
    public Result<List<String>> getAllDatabase() {
        Result<List<String>> result = new Result<List<String>>();
        result.setData(autoCodeService.getAllDatabase());
        return result;
    }

    @ApiOperation("生成代码")
    @RequestMapping(value="/autocode/create")
    public void autoCode(HttpServletResponse response, HttpServletRequest request) throws Exception{
        String databaseName = request.getParameter("databaseName");
        String tableName = request.getParameter("tableName");
        String author = request.getParameter("author");
        String split = request.getParameter("split");
        if(StringUtils.isBlank(split)) {
            split = "0";
        }
        String typeName = databaseName.split("_")[1];
        String packageName = request.getParameter("packageName");
        String page = request.getParameter("page");
        String serviceName = request.getParameter("serviceName");
        String hasName = request.getParameter("hasName");
        author = null == author ? "" : author;

        boolean byOrder = true;
        boolean isSingle = false;

        if(databaseName == null || tableName == null || typeName == null) {
            return;
        }
        String filePath = "/opt/autocode";						//存放路径
        DelAllFile.delFolder(filePath); //生成代码前,先清空之前生成的代码
        String ftlPath = filePath+"/codeftl";								//ftl路径
        String codePath = filePath+"/code";
        typeName = typeName == null ? "" : typeName;
        String typeNameUpper = typeName == null ? "" : typeName.toUpperCase();
        SimpleDateFormat formatter = new SimpleDateFormat ("yyyy/MM/dd HH:mm");
        String nowDate = formatter.format(new Date());

        List<AutoCodeTableDTO> tableList = autoCodeService.getTableNameBykeyword(databaseName, tableName);
        for (AutoCodeTableDTO table : tableList) {
            String name = table.getName();
            String[] strs = name.split("_");
            Integer end = NumberUtils.toInt(strs[strs.length-1],-1);
            if(end >= 0) {
                continue;
            }
            List<AutoCodeColumnDTO> infoList = autoCodeService.getTableInfoByTableName(databaseName, table.getName());
            boolean hasPrimark = false;
            boolean hasAuto = false;
            boolean hasDate = false;
            boolean hasTimestamp = false;
            boolean hasDecimal = false;
            boolean hasDelete = false;
            boolean hasDeleter = false;
            boolean hasModifier = false;
            boolean hasModified = false;
            boolean hasCreate = false;
            boolean hasCreator = false;
            boolean hasAppId = false;
            String primarkName = "";
            String primarkType = "";
            String primarkMType = "";
            String primarkUpper = "";
            String testString = "";
            String testInteger = "";
            String splitUpper = null;
            String splitType = null;
            List<AutoCodeColumnDTO> pureList = new ArrayList<>();
            for (AutoCodeColumnDTO tableInfo : infoList) {
                boolean isPure = true;
                if(!hasDelete) {
                    if(tableInfo.getColumName().equals("is_deleted")) {
                        hasDelete = true;
                        isPure = false;
                    }
                }
                if(!hasDeleter) {
                    if(tableInfo.getColumName().equals("deleter")) {
                        hasDeleter = true;
                        isPure = false;
                    }
                }
                if(!hasModified) {
                    if(tableInfo.getColumName().equals("modified")) {
                        hasModified = true;
                        isPure = false;
                    }
                }
                if(!hasModifier) {
                    if(tableInfo.getColumName().equals("modifier")) {
                        hasModifier = true;
                        isPure = false;
                    }
                }
                if(!hasCreate) {
                    if(tableInfo.getColumName().equals("created")) {
                        hasCreate = true;
                        isPure = false;
                    }
                }
                if(!hasCreator) {
                    if(tableInfo.getColumName().equals("creator")) {
                        hasCreator = true;
                        isPure = false;
                    }
                }
                if(!hasAppId) {
                    if(tableInfo.getColumName().equals("tenant_id")) {
                        hasAppId = true;
                        isPure = false;
                    }
                }
                tableInfo.setUpperCaseColum(getTuofengString(tableInfo.getColumName(),false,false));
                tableInfo.setFirstUpperCaseColum(getTuofengString(tableInfo.getColumName(),true,false));
                if(tableInfo.getIsPrimark() == 1) {
                    hasPrimark = true;
                    primarkName = tableInfo.getColumName();
                    primarkType = tableInfo.getDataType();
                    primarkUpper = tableInfo.getUpperCaseColum();
                    primarkMType = tableInfo.getModelDataType();
                }
                if(tableInfo.getIsAuto() == 1) {
                    hasAuto = true;
                }
                if(!hasDate) {
                    if(tableInfo.getModelDataType().equals("Date")) {
                        hasDate = true;
                    }
                }
                if(!hasTimestamp) {
                    if(tableInfo.getModelDataType().equals("Timestamp")) {
                        hasTimestamp = true;
                    }
                }
                if(!hasDecimal) {
                    if(tableInfo.getModelDataType().equals("BigDecimal")) {
                        hasDecimal = true;
                    }
                }
                if(testString.equals("")) {
                    if(tableInfo.getModelDataType().equals("String")) {
                        testString = getTuofengString(tableInfo.getColumName(),true,false);
                    }
                }
                if(testInteger.equals("")) {
                    if(tableInfo.getModelDataType().equals("Integer") && tableInfo.getIsPrimark() == 0
                            && !tableInfo.getColumName().equals("tenant_id") && !tableInfo.getColumName().equals("is_delete")) {
                        testInteger = getTuofengString(tableInfo.getColumName(),true,false);
                    }
                }
                if(null == splitType) {
                    if(tableInfo.getColumName().equals(split)) {
                        splitType = tableInfo.getModelDataType();
                        splitUpper = tableInfo.getUpperCaseColum();
                    }
                }
                if(isPure) {
                    pureList.add(tableInfo);
                }
            }
            String objectName = getTuofengString(table.getName(),true,true);
            String varObjectName = getTuofengString(table.getName(),false,true);
            Map<String,Object> root = new HashMap<String,Object>();
            root.put("fieldList", infoList);
            root.put("pureList", pureList);
            root.put("packageName", packageName);                       //包名
            root.put("typeName", typeName);
            root.put("typeNameUpper", typeNameUpper);
            root.put("author", author);
            root.put("hasPrimark", hasPrimark);
            root.put("hasAuto", hasAuto);
            root.put("hasDate", hasDate);
            root.put("hasTimestamp", hasTimestamp);
            root.put("hasDecimal", hasDecimal);
            root.put("hasDelete", hasDelete);
            root.put("hasDeleter", hasDeleter);
            root.put("hasModifier", hasModifier);
            root.put("hasModified", hasModified);
            root.put("hasCreate", hasCreate);
            root.put("hasCreator", hasCreator);
            root.put("hasAppId", hasAppId);
            root.put("byOrder", byOrder);
            root.put("isSingle", isSingle);
            root.put("testString", testString);
            root.put("testInteger", testInteger);
            root.put("primarkName", primarkName);
            root.put("primarkType", primarkType);
            root.put("primarkMType", primarkMType);
            root.put("primarkUpper", primarkUpper);
            root.put("objectName", objectName);                         //类名
            root.put("objectNameUpper", objectName.toUpperCase());
            root.put("varObjectName", varObjectName);
            root.put("tableName", table.getName());
            root.put("tableComment", table.getComment());
            root.put("needpage", page);
            root.put("serviceName", typeName.toUpperCase());
            root.put("hasName", hasName);
            //表前缀
            root.put("nowDate", nowDate);                            //当前日期
            root.put("splitColum", split);
            root.put("splitType", splitType);
            root.put("splitUpper", splitUpper);
            root.put("hasSplit", null != splitType);

            String packageType = "";//typeName.equals("") ? "" : typeName+"/";
            /*生成mybatis xml*/
            Freemarker.printFile("mapperXmlMysql.ftl", root, "/mapper/"+objectName+"Mapper.xml", codePath, ftlPath);

            /*model*/
            Freemarker.printFile("model.ftl", root, "/entity/"+ packageType + objectName+".java", codePath, ftlPath);

            /*model*/
            Freemarker.printFile("mapper.ftl", root, "/dao/"+ packageType + objectName+"Mapper.java", codePath, ftlPath);

            /*interfaces*/
            Freemarker.printFile("interfaces.ftl", root, "/service/"+ packageType + objectName+"Service.java", codePath, ftlPath);

            /*service*/
            Freemarker.printFile("service.ftl", root, "/serviceImpl/"+ packageType + objectName+"ServiceImpl.java", codePath, ftlPath);

            /*dto*/
            Freemarker.printFile("dto.ftl", root, "/dto/"+ packageType + objectName+"DTO.java", codePath, ftlPath);

            /*feignClient*/
            Freemarker.printFile("feignClient.ftl", root, "/feignClient/"+ packageType + objectName+"FeignClient.java", codePath, ftlPath);

            /*dto*/
            Freemarker.printFile("controller.ftl", root, "/controller/"+ packageType + objectName+"Controller.java", codePath, ftlPath);
        }
        /*生成的全部代码压缩成zip文件*/
        FileZip.zip(codePath, filePath+"/code.zip");

        /*下载代码*/
        FileDownload.fileDownload(response, filePath+"/code.zip", "code.zip");

    }

    private String getTuofengString(String colum, boolean isFirst, boolean isTable) {
        if(isTable && colum.startsWith("t_")) {
            colum = colum.substring(2,colum.length());
        }
        String[] array = colum.split("_");
        String name = "";
        for (int i = 0; i < array.length; i++) {
            String str = array[i];
            if(isFirst) {
                name += str.substring(0, 1).toUpperCase()+str.substring(1, str.length());
            }else {
                if(i == 0) {
                    name += str;
                }else {
                    name += str.substring(0, 1).toUpperCase()+str.substring(1, str.length());
                }
            }
        }
        return name;
    }

}
