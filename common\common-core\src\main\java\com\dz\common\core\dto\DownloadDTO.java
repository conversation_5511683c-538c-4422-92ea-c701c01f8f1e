package com.dz.common.core.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 创建下载任务
 *
 * <AUTHOR>
 * @date 2022/8/11 18:39
 */
@Data
public class DownloadDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 任务id
     */
    private Long id;
    /**
     * 菜单名称 eg：订单
     */
    private String menuName;
    /**
     * 所属业务名称 eg：订单列表
     */
    private String moduleName;
    /**
     * 报表名称
     */
    private String fileName;
    /**
     * 文件路径
     */
    private String sourceUrl;

    private Long downloadNum;

    /**
     * 状态 0 生成中 1已完成 2已失败
     */
    private Integer state;
    /**
     * 失败原因
     */
    private String errorDesc;
    /**
     * 是否删除 0未删除 1已删除
     */
    private Integer isDeleted;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 业务方报表表头 ,隔开
     */
    private String header;
}
