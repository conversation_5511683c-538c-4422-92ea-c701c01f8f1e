package com.dz.ms.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.basic.entity.MpMsgCrowd;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MpMsgCrowdMapper extends BaseMapper<MpMsgCrowd> {
    List<MpMsgCrowd> selectListBy(@Param("pageSize") Integer pageSize, @Param("pageNum") Integer pageNum);
}
