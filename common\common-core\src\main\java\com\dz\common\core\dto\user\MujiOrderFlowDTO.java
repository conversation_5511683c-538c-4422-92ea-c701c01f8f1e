package com.dz.common.core.dto.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 积分流水
 */
@Data
public class MujiOrderFlowDTO {

    @ApiModelProperty(value = "订单编号")
    private String orderSn;
    @ApiModelProperty(value = "订单类型")
    private Integer orderType;
    @ApiModelProperty(value = "消费渠道")
    private String channel;
    @ApiModelProperty(value = "消费渠道名称")
    private String channelName;
    @ApiModelProperty(value = "订单金额 单位/分 例如：1800，-1800")
    private Integer totalFee;
    @ApiModelProperty(value = "交易时间 例如:2023-10-01 10:00:00")
    private String payTime;
    @ApiModelProperty(value = "订单来源 1:线上订单 2:线下订单")
    private Integer orderSource;

}
