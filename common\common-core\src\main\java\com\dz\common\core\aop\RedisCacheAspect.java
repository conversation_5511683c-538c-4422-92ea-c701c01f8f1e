package com.dz.common.core.aop;

import com.dz.common.core.annotation.CacheEvict;
import com.dz.common.core.annotation.Cacheable;
import com.dz.common.core.constants.CommonConstants;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.CodeSignature;
import org.aspectj.lang.reflect.MethodSignature;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Aspect
public class RedisCacheAspect {

    @Resource
    private RedisService redisService;

    public RedisCacheAspect() {}


    @Around("@annotation(cacheable)")
    public Object around(ProceedingJoinPoint pjp, Cacheable cacheable) throws Throwable {
        if (this.redisService == null) {
            log.error("未配置redisTemplate,使用@Cacheable失败");
            return pjp.proceed();
        } else {
            String cacheKey = this.getCacheKey(pjp, cacheable.prefix(), cacheable.key());
            log.info("获取缓存数据[key:{}]", cacheKey);
            Object result = null;
            try {
                result = redisService.get(cacheKey);
            } catch (Exception e) {
                log.info("从redis cache获取数据出错.[cacheKey:{}]", cacheKey, e);
                return pjp.proceed();
            }
            if (result == null) {
                log.info("未获取缓存数据[key:{}]", cacheKey);
                result = pjp.proceed();
                try {
                    Class clz = getAdvicedMethod(pjp).getReturnType();
                    if (clz != Void.class && result != null) {
                        log.info("add redis cache.[cacheKey:{}]", cacheKey);
                        if (cacheable.expire() > 0) {
                            log.info("setting cache expire [cacheKey:{},expire:{},unit:{}]", new Object[]{cacheKey, cacheable.expire(), cacheable.timeunit()});
                            redisService.set(cacheKey, result, (long)cacheable.expire());
                        } else {
                            redisService.set(cacheKey, result, CommonConstants.WEEK_SECONDS);
                        }
                    }
                } catch (Exception e) {
                    log.info("设置redis cache出错.[cacheKey:{}]", cacheKey, e);
                }
            } else {
                log.info("从redis cache中获取数据[cacheKey:{}]", cacheKey);
            }
            return result;
        }
    }

    @Before("@annotation(cacheEvict)")
    public void cacheEvict(JoinPoint pjp, CacheEvict cacheEvict) throws Throwable {
        if (this.redisService == null) {
            log.info("未配置redisTemplate,使用@CacheEvict失败.");
        } else {
            String cacheKey = this.getCacheKey(pjp, cacheEvict.prefix(), cacheEvict.key());

            try {
                if (null != cacheKey && !"".equals(cacheKey.trim())) {
                    log.info("删除redis cache [cacheKey:{}]", cacheKey);
                    if (StringUtils.isEmpty(cacheEvict.key())) {
                        redisService.delAll(cacheKey);
                    } else {
                        redisService.del(cacheKey);
                    }
                }
            } catch (Exception e) {
                log.info("删除缓存信息失败.key:{}", cacheKey, e);
            }

        }
    }

    private String getCacheKey(JoinPoint pjp, String cacheName, String keyExpression) throws Exception {
        cacheName = cacheName.replaceAll("'","");
        StringBuffer cacheKey = new StringBuffer();
        cacheKey.append(cacheName).append(":");
        if(StringUtils.isNotBlank(keyExpression)) {
            Map<String, Object> param = new HashMap<>();
            if(keyExpression.contains("#")) {
                Object[] paramValues = pjp.getArgs();
                String[] paramNames = ((CodeSignature)pjp.getSignature()).getParameterNames();
                for (int i = 0; i < paramNames.length; i++) {
                    param.put(paramNames[i], paramValues[i]);
                }
            }
            String[] keys = this.getKeys(keyExpression);
            int length = keys.length;
            for(int i = 0; i < length; ++i) {
                String key = keys[i];
                if(key.length() == 0) {
                    continue;
                }
                if(key.charAt(0) == '#') {
                    key = key.substring(1);
                    int dotIdx = key.indexOf(".");
                    Object value = null;
                    if (dotIdx > 0) {
                        String propertyName = key.substring(0, dotIdx);
                        key = key.substring(dotIdx + 1);
                        value = param.get(propertyName);
                        String getter = "get" + Character.toUpperCase(key.charAt(0)) + key.substring(1);
                        value = value.getClass().getMethod(getter).invoke(value);
                    }
                    else {
                        value = param.get(key);
                    }
                    if(null == value && (("tenantId").equals(key) || ("uid").equals(key) || ("platform").equals(key))) {
                        value = SecurityContext.getUser();
                        String getter = "get" + Character.toUpperCase(key.charAt(0)) + key.substring(1);
                        value = value.getClass().getMethod(getter).invoke(value);
                    }
                    if(null == value) {
                        cacheKey.append("null").append(":");
                    }
                    else {
                        cacheKey.append(value.toString()).append(":");
                    }
                }
                else {
                    cacheKey.append(key).append(":");
                }
            }
        }
        if (cacheKey.charAt(cacheKey.length() - 1) == ':') {
            cacheKey.deleteCharAt(cacheKey.length() - 1);
        }
        return cacheKey.toString();
    }

    private String[] getKeys(String key) {
        if (key.startsWith("'") && key.endsWith("'")) {
            String[] keyArr = key.split("\\+");
            boolean checkError = Arrays.asList(keyArr).stream().anyMatch((it) -> {
                return !it.trim().endsWith("'") || !it.trim().startsWith("'");
            });
            if (checkError) {
                throw new BusinessException("bad cache key " + key);
            } else {
                for(int i = 0; i < keyArr.length; ++i) {
                    String ele = keyArr[i].trim();
                    ele = ele.substring(1);
                    ele = ele.substring(0, ele.length() - 1);
                    keyArr[i] = ele.trim();
                }

                return keyArr;
            }
        } else {
            return new String[]{key};
        }
    }

    private Method getAdvicedMethod(JoinPoint pjp) throws NoSuchMethodException {
        Signature sig = pjp.getSignature();
        MethodSignature msig = null;
        if (!(sig instanceof MethodSignature)) {
            throw new IllegalArgumentException("annotation can only use to method.");
        } else {
            msig = (MethodSignature)sig;
            Object target = pjp.getTarget();
            return target.getClass().getMethod(msig.getName(), msig.getParameterTypes());
        }
    }

}