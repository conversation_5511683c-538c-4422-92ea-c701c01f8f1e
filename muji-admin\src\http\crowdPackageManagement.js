// 所有的命名必须全局唯一
import service from '@/utils/request.js'


// 人群管理新增
export function crowdsave(data = {}) {
    return service({
        url: '/crm/user/crowd/save',
        method: 'post',
        data
    })
}

// 人群管理编辑
export function crowdUpdate(data = {}) {
    return service({
        url: '/crm/user/crowd/update',
        method: 'post',
        data
    })
}

// 人群管理详情
export function crowdInfo(data = {}) {
    return service({
        url: '/crm/user/crowd/info',
        method: 'get',
        data
    })
}


// 人群管理列表
export function crowdList(data = {}) {
    return service({
        url: '/crm/user/crowd/list',
        method: 'get',
        data
    })
}
// 人群管理列表
export function crowdAllList(data = {}) {
    return service({
        url: '/crm/user/crowd/noPageList',
        method: 'get',
        data
    })
}

// 人群管理删除

export function crowdDelete(data = {}) {
    return service({
        url: '/crm/user/crowd/delete',
        method: 'post',
        data
    })
}

// 启用停用
export function crowdUpdate_state(data = {}) {
    return service({
        url: '/crm/user/crowd/updateStatus',
        method: 'post',
        data
    })
}
// 
export function crowdtemplate(data = {}) {
    return service({
        url: '/crm/user/crowd/template',
        method: 'get',
        data,
        responseType: 'blob',
    })
}

// 

export function crowdimport(data = {}) {
    return service({
        url: '/crm/user/crowd/import',
        method: 'post',
        headers: { 'Content-Type': 'multipart/form-data' },
        data
    })
}
