package com.dz.common.core.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 单ID/STATE POST请求通用DTO
 * @author: lzx
 * @date:   2023/7/27 16:33
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
@ApiModel(value = "单ID POST请求通用DTO")
public class IsEnableDTO {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "状态 0禁用 1启用")
    private Integer state;

    public IsEnableDTO(Long id, Integer state) {
        this.id = id;
        this.state = state;
    }

}
