package com.dz.common.core.enums;

import java.util.Objects;

/**
 * 预约记录状态枚举
 * <AUTHOR>
public enum BookingRecordStateEnum {
    TO_MAKE(0, "待核销"),
    PENDING_CANCELED(1, "已核销"),
    ALREADY_CANCELED(2, "已过期"),
    EXPIRED(3, "已取消");
    private final Integer code;
    private final String value;

    BookingRecordStateEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (BookingRecordStateEnum resultEnum : BookingRecordStateEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum.value;
            }
        }
        return null;
    }
}
