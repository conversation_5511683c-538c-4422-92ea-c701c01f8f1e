package com.dz.ms.product.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;


/**
 * 商品搜索字段
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "商品搜索字段")
public class ProductUnionIdParamDTO {

    @ApiModelProperty(value = "商品ID")
    private Long productId;
    @ApiModelProperty(value = "uuid")
    private String uuid;

    @ApiModelProperty(value = "标记")
    private Integer sign;

}
