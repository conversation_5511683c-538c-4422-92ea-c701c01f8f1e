const app = getApp()
Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    isShow: {
      type: Boolean,
      value: false,
    },
    close: {
      type: Boolean,
      value: false
    },
    height: {
      type: Number,
      value: 300,
    },
    title: {
      type: String,
      value: '',
    },
    subTitle: {
      type: String,
      value: ''
    },
    type: {
      type: String,
      value: 'primary'
    },
    showConfirm: {
      type: Boolean,
      value: false,
    },
    confirmTxt: {
      type: String,
      value: ''
    },
    showCancel: {
      type: Boolean,
      value: false,
    },
    cancelTxt: {
      type: String,
      value: ''
    }
  },
  data: {},
  methods: {
    confirm() {
      this.triggerEvent('confirm')
    },
    cancel() {
      this.triggerEvent('cancel')
    },
    close() {
      this.triggerEvent('close')
    }

  }
})
