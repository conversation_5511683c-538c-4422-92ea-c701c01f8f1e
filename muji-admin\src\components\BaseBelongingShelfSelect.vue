<template>
  <a-form-item :label="props.label" :name="props.name" :rules="props.rules" class="ParentWrap">
    <a-select
      :fieldNames="{label:'name',value:'id'}"
      v-model:value="thisFields.value"
      show-search
      :filterOption="thisMethods.filterOption"
      :allowClear="true"
      :maxTagCount="1"
      placeholder="请选择"
      @change="thisMethods.change"
      :options="thisFields.options"
      v-bind="$attrs"
    />
  </a-form-item>
</template>

<script setup>
import { onMounted, reactive, watch } from 'vue'
import { apiGiftRack } from '@/http/index.js'

const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  name: {
    type: String,
    default: ''
  },
  rules: {
    type: Object,
    default: () => undefined
  },
  modelValue: {
    type: [Array, String, Number],
    default: () => []
  }
})
const emits = defineEmits(['change', 'update:modelValue'])

const attrs = useAttrs()
const thisFields = reactive({
  value: attrs.mode === 'multiple' ? [] : '',
  options: []
})
const thisMethods = {
  filterOption (input, option) {
    return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
  },
  async getOptions () {
    const res = await apiGiftRack.getAllPageList({ state: 1 })
    thisFields.options = res.data
  },
  async fillItem () {
    if (props.modelValue) { // 逻辑删除 - 可以进行回显 - 单选需要禁用被删项 - 多选需要禁用被删项
      let noLimitShelfIds = props.modelValue
      if (attrs.mode === 'multiple') {
        noLimitShelfIds = props.modelValue.join(',')
      }
      const res = await apiGiftRack.getAllPageList({ noLimitShelfIds })
      res.data.forEach(v => {
        if ((v.state === 0 || v.isDeleted === 1) && thisFields.options.findIndex(v2 => v2.id === v.id) === -1) {
          v.disabled = true
          thisFields.options.push(v)
        }
      })
    }
  },
  setValue () {
    thisFields.value = props.modelValue
    thisMethods.fillItem()
  },
  change (e) {
    emits('update:modelValue', e)
    emits('change', e)
  }
}

onMounted(() => {
  thisMethods.setValue()
  thisMethods.getOptions()
})
watch(() => props.modelValue, () => thisMethods.setValue())
</script>

<style lang="scss" scoped>
.ParentWrap {
  .ant-select {
    width: 202px;
  }
}
</style>
