package com.dz.common.core.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 创建下载任务
 *
 * <AUTHOR>
 * @date 2022/8/11 18:39
 */
public class DownloadQueryParamDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 菜单名称 eg：订单 参数必填
     */
    private String menuName;
    /**
     * 所属业务名称 eg：订单列表 参数必填
     */
    private String moduleName;
    private String beginTime;


    private String fileName;

    private String endTime;
    /**
     * 租户id
     */
    private Long tenantId;
    @ApiModelProperty(value = "创建人")
    private Long creator;

    public Long getCreator() {
        return creator;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getMenuName() {
        return menuName;
    }

    public void setMenuName(String menuName) {
        this.menuName = menuName;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
}
