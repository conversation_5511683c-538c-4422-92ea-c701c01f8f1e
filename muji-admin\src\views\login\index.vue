<template>
  <div class="login-box">
    <a-layout class="layout" style="height: 100vh;    display: flex;
    align-items: center;
    justify-content: center;">

      <div class="login-content">
        <div class="login-left login-form">
          <div class="login-left-box">
            <SvgIcon :name="logo" width="40" height="40"></SvgIcon>
            <div class="login-left-box-title">{{ setting.brand }}<br />小程序管理平台</div>
          </div>
          <div class="login-title">
            {{ setting.brand }}登录

          </div>
          <a-form ref="loginForm" :model="form" :rules="rules" class="">
            <template v-if="!global.LoginSuccessful">
              <a-form-item label="" name="username" style="width: 297px;">
                <a-input autosize="true" v-model:value="form.username" :maxlength="40" type="text" placeholder="用户名" autocomplete="off">
                  <template #prefix>
                    <user-outlined style="font-size: 14px" />
                  </template>
                </a-input>
              </a-form-item>
              <a-form-item label="" name="password" style="width: 297px;text-align: right;">
                <a-input-password :maxlength="30" v-model:value="form.password" autocomplete="off" placeholder="密码">
                  <template #prefix>
                    <UnlockOutlined style="font-size: 14px" />
                  </template>

                </a-input-password>

              </a-form-item>
              <a-form-item label="" name="imageCode" style="width: 297px;">
                <div style="display:flex; flex:1;">
                  <a-input v-model:value="form.imageCode" allowClear :maxlength="6" placeholder="验证码" type="text" autocomplete="off">
                    <template #prefix>
                      <SafetyCertificateOutlined style="font-size: 14px" />
                    </template>

                  </a-input>
                  <div style="cursor: pointer;border: 1px solid #d9d9d9;height: 39px;margin-left: 10px; " type="text" class="el_button_captcha" @click="initCaptchaHandle">
                    <img style=" height: 100%;" :src="code" alt="验证码" />
                  </div>
                </div>

              </a-form-item>
              <a-form-item style="width: 297px;">
                <a-button type="primary" class="login-btn" @click="onFinish" :loading="loading" html-type="submit" style="width: 100%;height: 39px;border-radius: 10px;">
                  下一步
                </a-button>
              </a-form-item>
            </template>
            <template v-else>
              <a-form-item label="" name="dkeyAmPassword" style="width: 297px;">

                <a-input v-model:value="form.dkeyAmPassword" allowClear :maxlength="6" placeholder="请输入宁盾动态口令" type="text">

                </a-input>

              </a-form-item>
              <a-form-item style="width: 297px;">
                <a-button type="primary" class="login-btn" @click="loginFul" :loading="loading" html-type="submit" style="width: 100%;height: 39px;border-radius: 10px;">
                  登录
                </a-button>
              </a-form-item>
            </template>
            <!-- <div @click="forgotPassword" class="tips-password" style="font-size: 14px;cursor: pointer;">忘记密码</div> -->
          </a-form>
        </div>
        <div class="login-right">
          <img src="@/assets/images/denglu.png" alt="">
        </div>
      </div>

    </a-layout>
  </div>
</template>

<script setup>
import { getimagecode } from '@/http/index.js'
import { reactive, toRefs, onMounted, nextTick } from 'vue';
import { useGlobalStore } from '@/store'
import { useRoute, useRouter } from 'vue-router'
import { message, Modal } from "ant-design-vue";
import setting from '@/config/defaultSettings'
import { encrypt, decrypt } from '@/utils/jsencrypt'
import _ from "lodash";
const global = useGlobalStore()
const $router = useRouter()
const { query } = useRoute()
const logo = ref(import.meta.env.VITE_LOGO_NAME)
const passwordCheck = (rule, value, callBack) => {
  let pattern = /^(?=.*[a-zA-Z])(?=.*[1-9])(?=.*[\W]).{6,}$/;
  if (!form.value.newPassword.length) {
    return Promise.reject('请输入新的确认密码')
  } else if (form.value.newPassword.length < 15) {
    return Promise.reject('登录密码至少15位')
  } else if (!pattern.test(form.value.newPassword)) {
    return Promise.reject('密码需要包含大写，小写，数字，特殊字符四种类型')
  } else {
    return Promise.resolve()
  }
}
const passwordCheckAgain = (rule, value, callBack) => {
  let pattern = /^(?=.*[a-zA-Z])(?=.*[1-9])(?=.*[\W]).{6,}$/;
  if (!form.value.newPasswordConfirm.length) {
    return Promise.reject('请输入新的确认密码')
  } else if (form.value.newPasswordConfirm.length < 15) {
    return Promise.reject('登录密码至少15位')

  } else if (!pattern.test(form.value.newPasswordConfirm)) {
    return Promise.reject('密码需要包含大写，小写，数字，特殊字符四种类型')

  } else if (form.value.newPassword !== form.value.newPasswordConfirm) {
    return Promise.reject('两次输入密码不一致，请重新输入')
  } else {
    return Promise.resolve()
  }
}
//  --------------------------- 定义数据 ---------------------------
const { code, form, loginForm, rules, loading } = toRefs(reactive({
  loading: false,
  redirect: '',
  loginForm: null,
  code: '',
  form: {
    username: '',
    password: '',
    codeKey: '',
    imageCode: '',
    tenantId: 1,
    platform: 1
  },

  rules: {
    username: [
      {
        required: true,
        message: '请输入用户名',
        trigger: ['blur', 'change']
      }
    ],
    password: [
      {
        required: true,
        message: '请输入密码',
        trigger: ['blur', 'change']
      }
    ],
    imageCode: [{
      required: true,
      message: '请输入图形验证码',
      trigger: ['blur', 'change']
    }],
    dkeyAmPassword: [{
      required: true,
      message: '请输入宁盾动态口令',
      trigger: ['blur', 'change']
    }],
    oldPassword: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
    newPassword: [
      { validator: passwordCheck, trigger: 'blur' }
    ],
    newPasswordConfirm: [
      { validator: passwordCheckAgain, trigger: 'blur' }
    ]
  }
}));
onMounted(() => {
  initCaptchaHandle()
})
// 获取验证码
const initCaptchaHandle = async function () {

  const { data } = await getimagecode()
  console.log("🚀 ~ initCaptchaHandle ~ data:", data.imageBase64)
  code.value = data.imageBase64 || ''
  form.value.codeKey = data.codeKey || ''

}
const onFinish = (values) => {//登录
  loginForm.value.validate().then(res => {
    loading.value = true
    let formParams = _.cloneDeep(form.value);
    formParams.username = formParams.username.trim()
    formParams.password = encrypt(formParams.password.trim())
    delete formParams.dkeyAmPassword
    global.login(formParams).then(async res => {
    }).finally(() => {
      initCaptchaHandle()
      loading.value = false
    })
  })

}
function loginFul() {
  loginForm.value.validate().then(res => {
    loading.value = true
    let formParams = _.cloneDeep(form.value);
    formParams.username = formParams.username.trim()
    formParams.password = encrypt(formParams.password.trim())
    delete formParams.imageCode
    delete formParams.codeKey

    global.setTokenDky(formParams).then(async res => {
      await global.setUserInfo()//获取用户信息
      await global.generateMenus()//获取菜单
      if (!global.menus.length) {
        message.warning('该角色没有操作权限，请联系管理员！');
      } else {
        let ToName = findFirstChildrenName(global.menus)
        if (ToName) {
          await $router.push({ name: ToName })
        } else {
          await $router.push({ name: 'Layout' })
        }

      }
      await nextTick(() => {

        global.setLoginSuccessful()
      })
    }).finally(() => {

      loading.value = false
    })
  })
}
// 递归查找当前数据的第一条子级，并返回最后一级的 name
const findFirstChildrenName = (data) => {
  // 如果当前数据为空，返回 null
  if (!data || data.length === 0) {
    return null;
  }

  const firstChild = data[0]; // 获取第一条子级数据

  // 如果没有第一条子级，返回 null
  if (!firstChild) {
    return null;
  }

  const children = firstChild.children; // 获取第一条子级的子级数据

  // 如果第一条子级没有子级，直接返回第一条子级的名称
  if (!children || children.length === 0) {
    return firstChild.name;
  }

  // 递归查找第一条子级的子级
  return findFirstChildrenName(children);
};
const isModalVisible = ref(false);
const forgotPassword = () => {
  console.log("🚀 ~ forgotPassword ~ forgotPassword:",)
  $router.push({ name: 'forgotPasswordModal', query: { typeTitle: 1 } })
  // isModalVisible.value = true;
}
const handleModalVisibility = (visible) => {
  isModalVisible.value = visible;
};
</script>

<style scoped lang="scss">
.login-box {
  height: 100%;
  width: 100%;
}

.login-content {
  background: #ffffff;
  border-radius: 10px;
  overflow: hidden;
  max-height: 462px;
  display: flex;

  .login-right {
    width: 392px;
    height: 462px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .login-form {
    background: #fff;
    border: 1px solid #fff;
    border-radius: 10px;
    width: 378px;
    padding: 42px 42px 28px;

    .login-title {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 500;
      font-size: 20px;
      color: #3d3d3d;
      line-height: 28px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 39px;
      word-break: break-all;
    }

    .login-left-box {
      display: flex;
      align-items: center;
      margin-bottom: 28px;

      .login-left-box-title {
        flex: 1;
        width: 0;
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: 17px;
        color: #3d3d3d;
        line-height: 24px;
        text-align: left;
        font-style: normal;
        text-transform: none;

        margin-left: 15px;
        word-break: break-all;
      }
    }
  }
}

.statement {
  li {
    margin-left: 50px;
    list-style: none;
    font-size: 12px;
    margin-bottom: 5px;
  }

  a {
    color: #000;
  }
}

:deep(.ant-input-affix-wrapper .ant-input-clear-icon) {
  font-size: 14px;
}

:deep(.ant-input-affix-wrapper) {
  border-radius: 10px;
}

.tips-password {
  text-align: center;
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 11px;
  color: rgba(0, 0, 0, 0.5);
  line-height: 16px;

  font-style: normal;
  text-transform: none;
  padding-top: 11px;
}

:deep(.ant-input-suffix) {
  font-size: 14px;
}

:deep(.ant-input-affix-wrapper .ant-input-prefix) {
  margin-inline-end: 6px;
}

.login-btn {
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 20px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}

.layout {
  background: $theme-login-bg-color;
  background-size: 100% 100%;
}

:deep(.ant-form-item) {
  margin-bottom: 20px;
}

:deep(.ant-form-item:nth-child(3)) {
  margin-bottom: 27px;
}

:deep(.ant-form-item:nth-child(4)) {
  margin-bottom: 0;
}

:deep(.ant-form-item .ant-form-item-explain-error) {
  height: 16px;
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 11px;
  color: #ff0000;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

:deep(.ant-input) {
  height: 29px;
  line-height: 100%;
  font-size: 14px !important;
}

:deep(.ant-input::placeholder) {
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 14px !important;
  color: rgba(0, 0, 0, 0.5);
  line-height: 29px;
}
</style>
