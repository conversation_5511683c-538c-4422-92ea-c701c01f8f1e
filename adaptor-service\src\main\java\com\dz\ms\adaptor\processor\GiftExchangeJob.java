package com.dz.ms.adaptor.processor;

import com.dz.common.core.fegin.sales.InteractionTaskFeginClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import javax.annotation.Resource;
import java.text.ParseException;

/**
 * 完成兑礼任务奖励
 */
@Slf4j
@Component
public class GiftExchangeJob implements BasicProcessor {

    @Resource
    private InteractionTaskFeginClient interactionTaskFeginClient;
    @Override
    public ProcessResult process(TaskContext context) throws ParseException {
        interactionTaskFeginClient.taskSuccessRecordByOrder();
        log.info("完成兑礼任务奖励定时任务");
        return new ProcessResult(true, "success");
    }
}