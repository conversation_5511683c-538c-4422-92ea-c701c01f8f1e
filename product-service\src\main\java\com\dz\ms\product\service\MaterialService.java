package com.dz.ms.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.ms.product.dto.MaterialDTO;
import com.dz.ms.product.entity.Material;

/**
 * 商品素材表接口
 *
 * @author: LiinNs
 * @date: 2024/11/18 13:45
 */
public interface MaterialService extends IService<Material> {

    /**
     * 分页查询商品素材表
     *
     * @param param
     * @return PageInfo<MaterialDTO>
     */
    public PageInfo<MaterialDTO> getMaterialList(MaterialDTO param);

    /**
     * 根据ID查询商品素材表
     *
     * @param id
     * @return MaterialDTO
     */
    public MaterialDTO getMaterialById(Long id);

    /**
     * 保存商品素材表
     *
     * @param param
     * @return Long
     */
    public Long saveMaterial(MaterialDTO param);

    /**
     * 根据ID删除商品素材表
     *
     * @param param
     */
    public void deleteMaterialById(IdCodeDTO param);

}
