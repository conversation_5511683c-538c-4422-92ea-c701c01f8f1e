@import "assets/scss/config";

.BaseHomeModuleTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;

  ._title-name {
    color: rgba(0, 0, 0, 1);
    font-size: px2rpx(36);
    font-family: SourceHanSansCN;
    font-weight: bold;
  }

  ._title-right-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  ._title-right-name {
    color: rgba(136, 136, 136, 1);
    font-size: px2rpx(28);
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
  }

  ._title-right-icon {
    font-weight: 300;
    font-size: px2rpx(22);
    color: rgba(136, 136, 136, 0.8);
    margin: px2rpx(3) 0 0 px2rpx(10);
  }
}
