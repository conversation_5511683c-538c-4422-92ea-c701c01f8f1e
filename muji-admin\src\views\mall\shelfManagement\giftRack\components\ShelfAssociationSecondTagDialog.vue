<template>
  <a-modal v-model:open="thisFields.open" :title="`${props.firstTagRecord.tagName}-二级标签筛选配置`" @ok="thisMethods.handleOk" @cancel="thisMethods.cancel" :okButtonProps="{disabled:thisFields.loading}" width="800px">
    <a-form class="form" ref="formRef" :model="formFields" :rules="formRules" :labelCol="{ style: 'width:160px' }">
      <a-form-item label="关联二级筛选标签">
        <div class="addWrap ui-child-form-item-mb0">
          <ShelfSecondTagSelect v-model="formFields.tag" label="" />
          <a-button class="addButton" type="link" @click="thisMethods.handleAdd">+ 添加</a-button>
        </div>
      </a-form-item>
      <a-form-item label="已选择标签" name="tagList">
        <VueDraggable :list="formFields.tagList" item-key="index" :animation="300">
          <template #item="{ element: item, index }">
            <div class="delWrap">
              <HolderOutlined />
              <div class="delName">{{ item.tagName }}</div>
              <a-button type="link" @click="thisMethods.handleDel(index)">- 删除</a-button>
            </div>
          </template>
        </VueDraggable>
        <div v-if="formFields.tagList.length==0" class="global-tip">暂无数据</div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import ShelfSecondTagSelect from '@/views/mall/shelfManagement/giftRack/components/ShelfSecondTagSelect.vue'
import { onMounted, reactive, watch } from 'vue'
import { cloneDeep } from 'lodash'
import { apiShelfTag } from '@/http/index.js'
import { message } from 'ant-design-vue'

const props = defineProps({
  firstTagRecord: {
    type: Object,
    default: () => ({})
  },
  record: {
    type: Object,
    default: () => ({})
  },
  modelValue: {
    type: Boolean,
    default: false
  }
})
const emits = defineEmits(['update:modelValue', 'ok'])

const formRef = ref(null)
const formFields = reactive({
  tag: {},
  tagList: []
})
const formRules = reactive({
  tagList: [{ required: true, message: '请添加二级标签' }]
})

const thisFields = reactive({
  loading: false,
  open: false,
  ShelfAssociationSecondTagDialogOpen: false
})
const thisMethods = {
  setOpen() {
    thisFields.open = props.modelValue
    formRef.value?.resetFields()
    if (thisFields.open) {
      thisMethods.initData()
    }
  },
  async initData() {
    thisFields.loading = true
    const res = await apiShelfTag.getTwoTag({
      shelfId: props.record.id,
      tagId: props.firstTagRecord.tagId
    }).finally(() => thisFields.loading = false)
    formFields.tagList = res.data.map(v => ({ id: v.id, tagId: v.tagId, tagName: v.tagName }))
    formFields.tag = {}
  },
  handleAdd() {
    if (!formFields.tag.tagId) return message.warning('请选择标签')
    if (formFields.tagList.findIndex(v => v.tagId === formFields.tag.tagId) !== -1) return message.warning('请勿重复添加')
    formFields.tagList.push({ tagName: formFields.tag.tagName, tagId: formFields.tag.tagId })
    formRef.value?.validateFields('tagList')
  },
  handleDel(index) {
    formFields.tagList.splice(index, 1)
    formRef.value?.validateFields('tagList')
  },
  cancel() {
    emits('update:modelValue', false)
  },
  async handleOk() {
    await formRef.value.validate()
    let params = cloneDeep(formFields)
    console.log('配置二级筛选标签：', params)
    thisFields.loading = true
    const res = await apiShelfTag.saveTwoTag({
      shelfId: props.record.id,
      oneTagId: props.firstTagRecord.tagId,
      twoTagIdList: formFields.tagList.map(v => v.tagId)
    }).finally(() => thisFields.loading = false)
    message.success(res.msg)
    emits('update:modelValue', false)
  }
}

onMounted(() => thisMethods.setOpen())
watch(() => props.modelValue, () => thisMethods.setOpen())
</script>

<style lang="scss" scoped>
.form {
  padding-top: 20px;
}

.addWrap {
  display: flex;
  align-items: center;

  .addButton {
    //margin-bottom: 24px;
  }
}

.delWrap {
  display: flex;
  align-items: center;
  cursor: move;

  .delName {
    margin-left: 10px;
    width: 202px;
  }
}
</style>
