<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.basic.mapper.MiniappTemplateMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    id,
  	    template_type,
  	    page_type,
  	    template_name,
  	    content,
  	    tenant_id,
  	    creator,
  	    created,
  	    modifier,
  	    modified,
  	    group_id,
        template_code,
        path_id
    </sql>

    <!-- 查询示例 -->
    <select id="selectDemo" parameterType="java.lang.Long" resultType="com.dz.ms.basic.entity.MiniappTemplate">
        select
        <include refid="Base_Column_List" />
        from miniapp_template
        where id = #{id,jdbcType=BIGINT}
        AND tenant_id = #{tenantId}
    </select>
    <select id="getTemplateByType" resultType="com.dz.ms.basic.entity.MiniappTemplate">
        select
        <include refid="Base_Column_List" />
        from miniapp_template
        where page_type = #{pageType}
    </select>
    <!-- 修改原有类型 -->
    <update id="setTemplateTypeByType">
        update miniapp_template
        set page_type = 0,path_id=1
        where page_type = #{pageType}
    </update>

    <!-- 根据类型将模板设置为未发布 -->
    <update id="setUnPublishByType">
        update miniapp_template
        set publish = 0
        where template_type = #{publish}
    </update>

</mapper>
