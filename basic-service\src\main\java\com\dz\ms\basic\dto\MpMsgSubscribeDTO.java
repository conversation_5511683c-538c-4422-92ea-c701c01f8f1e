package com.dz.ms.basic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 小程序订阅消息订阅记录入参DTO
 * @author: Handy
 * @date:   2023/07/07 14:22
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "小程序订阅消息订阅记录入参")
public class MpMsgSubscribeDTO {

    @ApiModelProperty(value = "场景编号")
    private String scene;
    @ApiModelProperty(value = "消息配置ID")
    private Long msgId;
    @ApiModelProperty(value = "消息模板ID")
    private String templateId;
    @ApiModelProperty(value = "触发类型 1自动触发 2手动触发")
    private Integer triggerType;
    @ApiModelProperty(value = "订阅次数")
    private Integer subNumber;

}
