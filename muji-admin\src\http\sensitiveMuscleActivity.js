// 所有的命名必须全局唯一
import service from '@/utils/request.js'
// 活动页面api


// 活动列表
export function CampaignList(data = {}) {
    return service({
        url: '/crm/sales/signIn/user/list',
        method: 'get',
        data
    })
}

export function CampaignDataInfo(data = {}) {
  return service({
      url: '/crm/sales/signIn/user/info',
      method: 'get',
      data
  })
}

// 导出
export function exportCampaignData(data = {}) {
    return service({
        url: '/crm/sales/signIn/user/info',
        method: 'post',
        data
    })
}
