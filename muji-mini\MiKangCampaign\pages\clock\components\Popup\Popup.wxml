<modal-cam show="{{isShow}}" background="{{'#00000000'}}" closeable="{{true}}" borderRadius="{{0}}" bindclose="onClose">
  <view class="clockPopup tab-bar">
    <image class="image" style="width: 610rpx;" src="{{$cdn}}/MiKangCampaign/days5Popup.png" mode="widthFix" />
    <image class="imageLeft" style="width: 34rpx;" src="{{$cdn}}/MiKangCampaign/leftIcon.png" mode="widthFix" />
    <image class="imageRight" style="width: 34rpx;" src="{{$cdn}}/MiKangCampaign/rightIcon.png" mode="widthFix" />
    <view class="goPrizeDraw" data-item="1" bind:click="onClose" catch:tap="onClose"></view>
    <view class="goCoupon" data-item="2" bind:click="onClose" catch:tap="onClose"></view>
    <!-- <swiper class="swiper" indicator-color="rgba(255,255,255,0.3)" indicator-active-color="#ffffff" indicator-dots="{{true}}" autoplay="{{false}}" next-margin='55rpx' previous-margin="55rpx" current="{{current}}" bindchange="changeSwiper">
      <swiper-item class="swiper-item ">
        <view class="clockPopup-content {{current==0?'':'swiper-item-current0'}}" style="background-image:url({{$cdn}}/MiKangCampaign/clockPopup.png);">
          <view class="iconfont iconfontGift" style="background-image:url({{$cdn}}/MiKangCampaign/ticketGiftIcon.png)"></view>
          <view class="title"> <text>{{"恭喜您获得\n抽奖机会"}} <text class="title-text">1</text> 次</text> </view>
          <view class="num"><text>100%中奖，有机会得价值226元米糠正装</text></view>
          <view class="clock-btn">
            <basic-button width="{{380}}" size="large" data-item="1" bind:click="onClose" catch:tap="onClose" data-clickItem="{{true}}">
              去抽奖
            </basic-button>
          </view>
        </view>
      </swiper-item>
      <swiper-item class="swiper-item ">
        <view class="clockPopup-content {{current==1?'':'swiper-item-current0'}}" style="background-image:url({{$cdn}}/MiKangCampaign/clockPopup.png);">
          <view class="iconfont" style="background-image:url({{$cdn}}/MiKangCampaign/ticketIcon.png)"></view>
          <view class="title"> <text>{{"恭喜您获得\n健康美容品类"}} <text class="title-text">85折</text> 券一张</text> </view>
          <view class="num"><text>在【我的】-【我的礼券】中查看</text></view>
          <view class="clock-btn">
            <basic-button width="{{380}}" size="large" data-item="2" bind:click="onClose" catch:tap="onClose" data-clickItem="{{true}}">
              去查看
            </basic-button>
          </view>
        </view>
      </swiper-item>
    </swiper> -->

  </view>
</modal-cam>