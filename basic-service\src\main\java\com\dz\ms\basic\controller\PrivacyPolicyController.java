package com.dz.ms.basic.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.dto.PrivacyPolicyDTO;
import com.dz.ms.basic.service.PrivacyPolicyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags="隐私条款")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class PrivacyPolicyController  {

    @Resource
    private PrivacyPolicyService privacyPolicyService;

    /**
     * 分页查询隐私条款
     * @param param
     * @return result<PageInfo<PrivacyPolicyDTO>>
     */
    @ApiOperation("分页查询隐私条款")
	@GetMapping(value = "/crm/privacy_policy/list")
    public Result<PageInfo<PrivacyPolicyDTO>> getPrivacyPolicyList(@ModelAttribute PrivacyPolicyDTO param) {
        Result<PageInfo<PrivacyPolicyDTO>> result = new Result<>();
		PageInfo<PrivacyPolicyDTO> page = privacyPolicyService.getPrivacyPolicyList(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询隐私条款
     * @param id
     * @return result<PrivacyPolicyDTO>
     */
    @ApiOperation("根据ID查询隐私条款")
	@GetMapping(value = "/crm/privacy_policy/info")
    public Result<PrivacyPolicyDTO> getPrivacyPolicyById(@RequestParam("id") Long id) {
        Result<PrivacyPolicyDTO> result = new Result<>();
        PrivacyPolicyDTO privacyPolicy = privacyPolicyService.getPrivacyPolicyById(id);
        result.setData(privacyPolicy);
        return result;
    }

    /**
     * 新增隐私条款
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增隐私条款",type = LogType.OPERATELOG)
    @ApiOperation("新增隐私条款")
    @PostMapping(value = "/crm/privacy_policy/add")
    public Result<Long> addPrivacyPolicy(@RequestBody PrivacyPolicyDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,true);
        Long id = privacyPolicyService.savePrivacyPolicy(param);
        result.setData(id);
        return result;
    }

    /**
     * 更新隐私条款
     * @param param
     * @return result<Object>
     */
    @SysLog(value = "更新隐私条款",type = LogType.OPERATELOG)
    @ApiOperation("更新隐私条款")
    @PostMapping(value = "/crm/privacy_policy/update")
    public Result<Long> updatePrivacyPolicy(@RequestBody PrivacyPolicyDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,false);
        privacyPolicyService.savePrivacyPolicy(param);
        result.setData(param.getId());
        return result;
    }

    /**
     * 验证保存参数
     * @param param
     */
    private void validationSaveParam(PrivacyPolicyDTO param,boolean isAdd) {
        if(isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if(!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
    }
	
	/**
     * 根据ID删除隐私条款
     * @param param
     * @return result<Boolean>
     */
    @SysLog(value = "删除隐私条款",type = LogType.OPERATELOG)
    @ApiOperation("根据ID删除隐私条款")
	@PostMapping(value = "/crm/privacy_policy/delete")
    public Result<Boolean> deletePrivacyPolicyById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        privacyPolicyService.deletePrivacyPolicyById(param);
        result.setData(true);
        return result;
    }

    @ApiOperation("获取最新隐私条款版本号")
    @GetMapping(value = "/app/privacy_policy/last_version")
    public Result<String> getLastPrivacyPolicyVersion() {
        Result<String> result = new Result<>();
        String version = privacyPolicyService.getLastPrivacyPolicyVersion();
        result.setData(version);
        return result;
    }

    @ApiOperation("获取最新隐私条款内容")
    @GetMapping(value = "/app/privacy_policy/last_info")
    public Result<PrivacyPolicyDTO> getLastPrivacyPolicyInfo() {
        Result<PrivacyPolicyDTO> result = new Result<>();
        PrivacyPolicyDTO privacyPolicy = privacyPolicyService.getLastPrivacyPolicyInfo();
        result.setData(privacyPolicy);
        return result;
    }

}
