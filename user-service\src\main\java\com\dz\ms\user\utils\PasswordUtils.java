package com.dz.ms.user.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.Arrays;
import java.util.Base64;
import java.util.Random;

/**
 * 生成密码
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022/4/1 22:09
 */
@Slf4j
public class PasswordUtils {


    @Value("${pu.tencent.keySecret}")
    private static String keySecret;
    @Value("${pu.tencent.iv}")
    private static String iv1;
    @Value("${pu.tencent.aes}")
    private static String aes;
    /**
     * 大小写字母
     */
    public final static String[] lowWord = {
            "a", "b", "c", "d", "e", "f", "g",
            "h", "j", "k", "m", "n",
            "p", "q", "r", "s", "t",
            "u", "v", "w", "x", "y", "z",
    };

    public final static String[] upWord = {
            "A", "B", "C", "D", "E", "F", "G",
            "H", "J", "K", "M", "N",
            "P", "Q", "R", "S", "T",
            "U", "V", "W", "X", "Y", "Z"
    };

    /**
     * 数字 0-9
     */
    public final static String[] num = {
            "0","1", "2", "3", "4", "5", "6", "7", "8", "9"
    };

    /**
     * 特殊字符
     */
    public final static String[] symbol = {
            "!", "@", "#", "$", "%", "^", "&", "*",
            "(", ")", "{", "}", "[", "]" , ".", "?", "_",
             "-", ",", ";", ":",  "|", "~"
    };

    public final static String[] type = {
            "upWord","lowWord", "num", "symbol"
    };

    /**
     * 随机生成 n 位包含 字母、数字、特殊字符 的密码
     *
     * @return
     */

    public static String randomPW(Integer count) {
        StringBuilder stringBuffer = new StringBuilder();
        Random random = new Random(System.currentTimeMillis());
        String flag = type[random.nextInt(type.length)];
        // 输出长度 12 位
        int length = count;
        for (int i = 0; i < length; i++) {
            switch (flag) {
                case "lowWord":
                    stringBuffer.append(lowWord[random.nextInt(lowWord.length)]);
                    break;
                case "upWord":
                    stringBuffer.append(upWord[random.nextInt(upWord.length)]);
                    break;
                case "num":
                    stringBuffer.append(num[random.nextInt(num.length)]);
                    break;
                case "symbol":
                    stringBuffer.append(symbol[random.nextInt(symbol.length)]);
                    break;
                default:
                    break;
            }
            flag= type[random.nextInt(type.length)];
        }
        return stringBuffer.toString();
    }


    public static String decrypt(String password) {
        try {
            // 1解密
            SecretKeySpec key = new SecretKeySpec(keySecret.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance(aes);
            IvParameterSpec iv = new IvParameterSpec(iv1.getBytes());
            cipher.init(Cipher.DECRYPT_MODE, key, iv);  // 初始化

            byte[] result = cipher.doFinal(Base64.getDecoder().decode(password));
            String resultStr = new String(result);

            return resultStr;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("解密密码异常", e);
        }
        return null;
    }

    public static void main(String[] args) {

    }
}
