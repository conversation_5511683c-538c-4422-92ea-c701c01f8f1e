<view class="filter-box" style="height: {{ hasTabList ? '203rpx' : '103rpx'}};" id="filterBox">
  <view class="filter-container">
    <view class="filter-tab" wx:if="{{hasTabList}}">
      <scroll-view class="tab-list" enhanced="{{ true }}" bounces="{{ false }}" scroll-with-animation="{{true}}" scroll-x show-scrollbar="{{false}}">
        <view class="tab-item {{activeTab===item.value && 'active'}}" wx:for="{{tabList}}" wx:key="value" data-value="{{item.value}}" bind:tap="changeTab" data-filter="tag" data-label="{{item.label}}">
          <view class="tab-text">{{item.label}}</view>
          <block wx:if="{{item.childList.length>0}}">
            <view class="iconfont icon-Pull-down" style="margin-left: 10rpx;" />
          </block>
        </view>
      </scroll-view>
    </view>
    <view class="filter-other">
      <view class="filter-btn black-font">
        <view class="btn-item" bindtap="hanldeToggleFilter" data-filter="sort" data-check="{{1}}" style="margin-right: 36rpx">
          <view class="iconfont icon-Sorting" />
          {{selectedLabel}}
        </view>
        <view class="btn-item" bindtap="hanldeToggleFilter" data-filter="point" data-check="{{2}}">
          <view class="iconfont icon-Screen" />
          按条件筛选
        </view>
      </view>
      <view wx:if="{{showMine}}" class="filter-btn" style="color: #888;">
        <view class="btn-item only-me" bindtap="hanldeToggleFilter" data-filter="isMyExchange">
          <view class="checked-box {{isMyExchange ? 'active' : ''}}">
            <view wx:if="{{isMyExchange}}" class="iconfont icon-Mark" />
          </view>
          只看我能换的
        </view>
      </view>
    </view>
  </view>
  <basic-popup top="{{topHeight}}" bindclose="closeDialog" isShow="{{ showFilterDialog }}" zIndex="{{1}}">
    <view slot="content">
      <view class="dialog-content">
        <view class="filter-diaglog-box">
          <view wx:if="{{currentFilterType==='point'}}" class="dialog-title">
            积分区间
          </view>
          <view class="filter-list-box">
            <view class="dialog-item {{item.selected && 'active'}}" wx:for="{{currentDialogContent}}" wx:key="value" bind:tap="changeFilter" data-label="{{item.label}}" data-value="{{item.value}}">{{item.label}}</view>
          </view>
        </view>
        <view class="btn-box" wx:if="{{currentCheckType===2}}">
          <basic-button bindtap="handleReset" width="{{320}}" btnState="plain" size="large">
            重置
          </basic-button>
          <basic-button bindtap="handleConfirm" width="{{320}}" btnState="primary" size="large">
            确认
          </basic-button>
        </view>
      </view>
    </view>
  </basic-popup>
</view>