<!--MiKangCampaign/pages/restartPage/restartPage.wxml-->
<my-page overallModal="{{overallModal}}">
  <view class="page-container" style="background:#F8F6ED;">
    <custom-header isShare="{{true}}" background="transparent" type="{{1}}" color="white" />
    <scroll-view class="overPage-wrap" enhanced="{{ true }}" bounces="{{ false }}" show-scrollbar="{{ false }}" scroll-y>
      <view class="overPage">
        <view class="overPage-top">
          <image class="enpageBack" src="{{$cdn}}/MiKangCampaign/Clock_in_0.png" mode="widthFix" />

          <view bind:tap="onTapRule" class="page-rule">活动规则</view>
          <view bind:tap="onTapRule1" class="page-zhongjiang">招募结果</view>
          <view class="page-content" style="padding-top: {{navBarHeight+statusBarHeight+'px'}};">
            <view class="image1">
              <view class="text" wx:if="{{isRestar}}"><text class="text1">您未完成体验官打卡任务</text></view>
              <view class="h1" wx:if="{{isRestar}}">
                <text>{{'立即重启打卡'}}</text>
              </view>
              <view class="h1 h2" wx:else>
                <block>
                  <view><text>{{'您未完成体验官'}}</text></view>
                  <view class="h1-text"><text>{{'打卡任务'}}</text></view>
                </block>
              </view>

              <view class="bubble" wx:if="{{isRestar}}">
                <image class="bubble-img" src="{{$cdn}}/MiKangCampaign/triangle.png" mode="" />
                <text>您可在{{endTime}}前随时重启打卡</text>
              </view>
            </view>
            <view class="otherType-bottom">
              <!-- 可以重新开启打卡 -->
              <view class="otherType-bottom-box" wx:if="{{isRestar}}">
                <basic-button width="{{670}}" loading="{{loading}}" size="large" bind:click="restarClick">
                  重新开启打卡体验
                </basic-button>
              </view>

              <view class="pullDownView {{!isRestar?'isRestarNo':''}}">
                <view class="viewIconfont iconfont icon-Pull-down"></view>
                <view class="viewText">下拉查看商品介绍</view>
              </view>
            </view>

          </view>
        </view>
        <view class="picture">
          <image class="img" mode="widthFix" style="width:750rpx;flex-shrink:0" src="{{$cdn}}/MiKangCampaign/overpage2.png" />
          <!-- <image class="img" mode="widthFix" style="width:750rpx;flex-shrink:0" src="{{$cdn}}/MiKangCampaign/overpage3.jpg" /> -->
        </view>
      </view>
    </scroll-view>
    <!-- 拒绝订阅 提示框 -->
    <guidePopup showGuide="{{showContact1}}" bind:finish="closeGuide" />
    <result isShow="{{showAnnouncement}}" bindclose="close" />
  </view>
</my-page>