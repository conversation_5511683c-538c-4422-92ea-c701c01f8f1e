<template>
  <a-drawer v-model:open="thisFields.open" title="推广" width="1200" placement="right" :closable="true" :maskClosable="false" @ok="thisMethods.handleOk" @close="thisMethods.cancel" :okButtonProps="{ hidden: true }" :cancelButtonProps="{ hidden: true }">
    <!-- :okButtonProps="{disabled:thisFields.loading}"  -->
    <div class="statistics-header-box">
      <div class="statistics-header-name">{{ record.templateName ? record.templateName : '--' }}</div>
      <div class="statistics-header-info">
        <div class="statistics-header-info-name">
          创建人：{{ dataSource && dataSource[0]?.creatorName ? dataSource[0].creatorName : '--' }}
        </div>

        <div class="statistics-header-info-line"></div>
        <div class="statistics-header-info-name">
          更新时间：{{ record.modified ? record.modified : '--' }}
        </div>
        <div class="statistics-header-info-line"></div>
        <div class="statistics-header-info-name">
          页面组：{{ record.groupName ? record.groupName : '--' }}
        </div>

      </div>
    </div>
    <layout>
      <template v-slot:topLeft>
        <!-- {{ visibleBatch }} -->
        <a-checkbox v-model:checked="thisFields.isAllowSelect" :indeterminate="indeterminate" @change="changeAllSelect">
          已选 {{ thisFields.selectedRows.length }} 条
        </a-checkbox>

      </template>
      <template v-slot:topRight>
        <a-space>
          <a-button @click="thisMethods.exportData" :disabled="!thisFields.loading && !thisFields.selectedRows.length">批量下载</a-button>
          <a-button type="primary" :disabled="loading && record.pagePath" @click="thisMethods.addEdit('', 0)">新建链接</a-button>
        </a-space>

      </template>
      <template v-slot="{ height }">

        <a-table :indentSize="20" row-key="id" :row-selection="rowSelection" :scroll="{ y: '100%', x: '100%', scrollToFirstRowOnChange: true }" :pagination="false" :dataSource="dataSource" :columns="thisFields.tableHeader" :loading="loading">
          <template v-slot:headerCell="{ column }">
            <span v-if="column.title == '链接'">
              <a-space>
                <span>链接</span><a-tooltip placement="top">
                  <template #title>
                    <span>链接有效期为30天，失效后请点击刷新按钮重新生成新链接</span>
                  </template>
                  <QuestionCircleOutlined />
                </a-tooltip>
              </a-space>
            </span>
            <span v-else>{{ column.title }}</span>
            <!-- 在这里添加自定义的头部内容 -->
          </template>
          <template #bodyCell="{ column, record, index }">

            <template v-if="column.key === 'index'">
              {{ (current - 1) * pageSize + 1 + index }}
            </template>
            <template v-if="column.key === 'qrCode'">
              <a-image :src="'data:image/gif;base64,' + record.qrCode" :width="50" :height="50"></a-image>
            </template>
            <template v-if="column.key === 'pagePath'">
              <div style="display:flex">
                <span :title="record.pagePath" style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                  {{  record.pagePath  }}</span>
                <div style="flex:1">
                  <a-tooltip placement="top">
                    <template #title>
                      <span>复制</span>
                    </template>
                    <copy-outlined class="copy" :data-clipboard-text="record.pagePath" @click="copy" />
                  </a-tooltip>
                </div>
              </div>

            </template>
            <template v-if="column.key === 'linkUrl'">
              <div style="display:flex">
                <span :title="record.linkUrl" style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                  {{ record.linkUrl }}</span>
                <div style="flex:1">
                  <a-tooltip placement="top">
                    <template #title>
                      <span>复制</span>
                    </template>
                    <copy-outlined class="copy" :data-clipboard-text="record.linkUrl" @click="copy" />
                  </a-tooltip>
                  <a-tooltip placement="top">
                    <template #title>
                      <span>重新生成（上一次生成时间：{{ record.endTime || '--' }})</span>
                    </template>
                    <RedoOutlined @click="chongxin(record)" />
                  </a-tooltip>
                </div>
              </div>

            </template>
            <template v-if="column.key === 'action'">

            </template>

          </template>
        </a-table>
      </template>
    </layout>

  </a-drawer>
  <a-modal v-model:open="thisFields.extendShow" title="新建链接" @ok="thisMethods.extendhandleOk" @cancel="thisMethods.extendcancel" :cancelButtonProps="{ loading: thisFields.loading }" :okButtonProps="{ loading: thisFields.loading }" width="800px">
    <a-spin :spinning="thisFields.loading">
      <a-form class="form" ref="addForm" :model="thisFields.addParams" :rules="thisFields.rules" :labelCol="{ style: 'width:160px' }">

        <a-form-item label="请选择渠道" name="channelIds">
          <a-checkbox-group v-model:value="thisFields.addParams.channelIds" style="width: 100%">

            <a-row :gutter="[10,10]">
              <a-col :span="24" v-for="(item, index) in thisFields.ischannelIdsOptions" :key="index">
                <a-button type="link" class="chann-btn" ghost @click="thisMethods.Toggle(index,item.unfold)">
                  <CaretDownOutlined v-if="item.unfold " />
                  <CaretRightOutlined v-else />
                  <!-- <CaretUpOutlined v-else /> -->
                  {{item.channelName}}
                </a-button>
                <!-- style="margin-top:10px" -->
                <a-row :gutter="[0,0]" v-if="item.unfold&&item.twoChannel.length">
                  <a-col :span="8" v-for="(item2,index2) in item.twoChannel" :key="index2">
                    <a-checkbox :value="item2.id" :disabled="item2.disabled">
                      <div class="check-item">
                        <div></div>
                        <div class="check-item-tit" :title="item2.channelName">{{ item2.channelName }}</div>
                      </div>
                    </a-checkbox>
                  </a-col>
                </a-row>
              </a-col>
            </a-row>

          </a-checkbox-group>
        </a-form-item>
      </a-form>
    </a-spin>
    <template #footer>
      <div style="display:flex;justify-content: space-between;align-items: center;">
        <div> 未找到想要的渠道，<span class="global-color" @click="toLink">去创建</span></div>
        <a-space>
          <a-button @click="thisMethods.extendcancel" :loading="thisFields.loading">取消</a-button>
          <a-button type="primary" @click="thisMethods.extendhandleOk" :loading="thisFields.loading">确定</a-button>
        </a-space>
      </div>
    </template>

  </a-modal>
  <addArg :visible="thisFields.visible" ref="addArgRef" @ok="updateList" @cancel="thisFields.visible = false" :id="thisFields.id" :type="thisFields.type" />
</template>

<script setup>
import addArg from '@/views/argument/components/addArg.vue';
import { usePagination, useRequest, } from 'vue-request';
import { onMounted, reactive, watch } from 'vue'
import { cloneDeep } from 'lodash'
import { basicpromotion_pagelist, promotion_channelchannelList, promotion_pageAdd, promotion_pageupdate, promotion_pagegetChannelList } from '@/http/index.js'
import { message } from 'ant-design-vue'
import Clipboard from 'clipboard';
import JSZip from 'jszip'
import FileSaver from 'file-saver'
import { useRoute, useRouter } from 'vue-router'
import { useGlobalStore } from '@/store'
const global = useGlobalStore()
const $router = useRouter()
const emits = defineEmits(['update:value'])
const addForm = ref(null)
const props = defineProps({
  firstTagRecord: {
    type: Object,
    default: () => ({})
  },
  record: {
    type: Object,
    default: () => ({})
  },
  value: {
    type: Boolean,
    default: false
  }
})
const thisFields = reactive({
  visible: false,
  id: '',
  type: 0,
  addParams: {
    channelIds: []
  },
  rules: {
    channelIds: [{ required: true, message: '请选择渠道', trigger: ['blur', 'change'] }],
  },
  ischannelIdsOptions: [],
  total: 0,
  allList: [],
  selectedRows: [],
  isAllowSelect: false,
  extendShow: false,
  loading: false,
  open: false,
  tableHeader: [
    {
      title: '序号',
      key: 'index',
      align: 'center',
      width: 80

    },
    { title: '一级渠道', dataIndex: 'oneChannelName', align: 'center', ellipsis: true, width: 100 },
    { title: '二级渠道', dataIndex: 'towChannelName', align: 'center', ellipsis: true, width: 100 },
    { title: '路径', key: 'pagePath', align: 'center', ellipsis: true, width: 180 },
    { title: '链接', key: 'linkUrl', align: 'center', ellipsis: true, width: 180 },
    { title: '二维码', key: 'qrCode', align: 'center', ellipsis: true, width: 100 },
    { title: '创建人', dataIndex: 'creatorName', align: 'center', ellipsis: true, width: 100 },
    { title: '创建时间', dataIndex: 'createTime', align: 'center', ellipsis: true, width: 120 },

    // { title: '操作', dataIndex: 'action', align: 'center', width: 60, fixed: 'right' }
  ]
})
const { data: dataSource, run, loading, pageSize, current, refresh } = usePagination((param) => {
  // 这里修改参数
  // return resignationInheritanceList({ ...param, ...getParams() })
  return basicpromotion_pagelist({ ...param, templateId: props.record.id })
},
  {
    manual: true, // 修改为false,让其自动执行
    pagination: {
      // currentKey: 'pageNo',  // 通过该值指定接口 当前页数 参数的属性值
      // pageSizeKey: 'pageSize',// 通过该值指定接口 每页获取条数 参数的属性值
      // totalKey: 'data.data.count',  // 指定 data 中 total 属性的路径
    },
    formatResult: res => { // 返回数据格式化

      return res.data
    }
  });

const copy = () => {
  var clipboard = new Clipboard('.copy')
  clipboard.on('success', e => {
    message.success('复制成功')
    // 释放内存
    clipboard.destroy()
  })
  clipboard.on('error', e => {
    // 不支持复制
    message.info('该浏览器不支持自动复制')
    // 释放内存
    clipboard.destroy()
  })
}

// 选中元素的key值
const selectedRowKeys = computed(() => {
  return thisFields.selectedRows.map(item => item.id)
})

// 检测是否一个个的单独选中，若单独全选中  设置全选
watch(() => selectedRowKeys.value, () => {
  if (selectedRowKeys.value.length > 0 && selectedRowKeys.value.length == dataSource.value.length) {
    // console.log("🚀 ~ watch ~ selectedRowKeys.value:", selectedRowKeys.value)
    thisFields.isAllowSelect = true
    // console.log("🚀 ~ watch ~ thisFields.isAllowSelect:", thisFields.isAllowSelect)
  }
})
// 是否半选
const indeterminate = computed(() => {
  return selectedRowKeys.value.length && !thisFields.isAllowSelect
})

function toLink() {
  // $router.push({
  //   name: 'Argument'
  // })
  if (global.$hasPermission("argument:add")) {
    thisFields.visible = true
    thisFields.id = ''
    thisFields.type = 0
  } else {
    message.warning('暂无权限')
  }

}
function updateList() {
  thisFields.visible = false
  thisFields.loading = true
  promotion_channelchannelList().then(res => {
    promotion_pagegetChannelList({ templateId: props.record.id }).then(res2 => {
      // Get array of existing channel IDs
      const existingIds = res2.data.map(item => item)
      // Mark channels as disabled if they already exist
      thisFields.ischannelIdsOptions = res.data.map(channel => ({
        ...channel,
        disabled: existingIds.includes(channel.id)
      }))
      thisFields.loading = false

    })
  })
  // thisFields.extendShow = true
  addForm.value?.resetFields()
}
// 切换选中
const rowSelection = computed(() => {
  return {
    // hideSelectAll: false,// 是否去掉全选
    type: 'checkbox',
    selectedRowKeys: selectedRowKeys.value,
    // 选中单个
    onSelect(record, selected, rows, e) {
      // console.log("🚀 ~ onSelect ~ selected:", selected)
      if (selected) {
        thisFields.selectedRows.push(record)
      } else {
        let index = selectedRowKeys.value.indexOf(record.id)
        if (index > -1) {
          thisFields.selectedRows.splice(index, 1)
        }
        // 取消选中  全选也要手动取消
        thisFields.isAllowSelect = false
      }
    },
    // 全选
    onSelectAll(selected, rows, changeRow) {
      // console.log("🚀 ~ onSelectAll ~ selected:", selected)
      if (selected) {
        changeRow.forEach((item) => {
          thisFields.selectedRows.push(item)
        })
      } else {
        let ids = changeRow.map((item) => item.id)
        thisFields.selectedRows = thisFields.selectedRows.filter((item) => {
          return !ids.includes(item.id)
        })
        // 取消选中  全选也要手动取消
        thisFields.isAllowSelect = false
      }
    },
  }
})
// 全选按钮
const changeAllSelect = (e) => {
  // console.log("🚀 ~ changeAllSelect ~ e:", e)
  // 全选选中 当前列表要都选中
  if (e.target.checked) {
    dataSource.value.forEach(record => {
      // 没有选中就设置成选中
      let index = selectedRowKeys.value.indexOf(record.id)
      if (index < 0) {
        thisFields.selectedRows.push(record)
      }
    })
    thisFields.isAllowSelect = true
  } else {
    thisFields.selectedRows = []
    thisFields.isAllowSelect = false
  }
}
function chongxin(row) {
  promotion_pageupdate({ id: row.id, }).then(res => {
    run({ templateId: props.record.id })
  })
}
const thisMethods = {
  handleOk() {

    thisFields.selectedRows = []
    thisFields.isAllowSelect = false
    selectedRowKeys.value = []
    dataSource.value = []
    emits('update:value', false)
  },
  cancel() {

    thisFields.selectedRows = []
    thisFields.isAllowSelect = false
    selectedRowKeys.value = []
    dataSource.value = []
    thisFields.open = false
    emits('update:value', false)

  },
  extendhandleOk() {

    let params = cloneDeep(thisFields.addParams)
    // console.log(params);
    addForm.value.validate().then(res => {
      thisFields.loading = true
      promotion_pageAdd({ ...params, id: props.record.id }).then(res => {
        message.success(res.msg)
        thisFields.extendShow = false
        thisFields.loading = false
        run({ templateId: props.record.id })
        thisFields.selectedRows = []
        thisFields.isAllowSelect = false
        selectedRowKeys.value = []
        dataSource.value = []
      })


    })

  },
  extendcancel() {
    thisFields.extendShow = false
  },
  Toggle(index, bool) {
    console.log("🚀 ~ Toggle ~ index, bool:", index, bool)
    thisFields.ischannelIdsOptions[index]['unfold'] = !bool
  },
  extendOpen() {
    thisFields.loading = true
    promotion_channelchannelList().then(res => {
      promotion_pagegetChannelList({ templateId: props.record.id }).then(res2 => {
        // Get array of existing channel IDs
        const existingIds = res2.data?.map(item => item)

        // Mark channels as disabled if they already exist
        thisFields.ischannelIdsOptions = res.data?.map(channel => ({
          ...channel,
          twoChannel: channel.twoChannel.map(item => ({ disabled: existingIds.includes(item.id), ...item }))
          ,
          unfold: true
        }))

        thisFields.loading = false
      })
    }).finally(() => {
      thisFields.loading = false
    })

    thisFields.extendShow = true
    addForm.value?.resetFields()
  },
  async setOpen() {
    thisFields.loading = true
    thisFields.open = props.value
    run({ templateId: props.record.id })
    thisFields.loading = false
  },
  exportData() {
    if (!thisFields.selectedRows.length) {
      message.warning('请选择要下载的二维码')
      return
    }

    const zip = new JSZip()
    const folder = zip.folder('qrcodes')

    thisFields.selectedRows.forEach((item, index) => {
      const fileName = `${item.channelName || 'qrcode'}_${index + 1}.png`
      const base64Data = item.qrCode.replace(/^data:image\/(png|jpg|gif);base64,/, '')
      folder.file(fileName, base64Data, { base64: true })
    })

    zip.generateAsync({ type: 'blob' }).then(content => {
      FileSaver.saveAs(content, `${props.record.templateName}.zip`)
    })
  },
  addEdit(row, type) {
    thisMethods.extendOpen()
  }
}
watch(() => props.value, () => {
  console.log("🚀 ~ watch ~ props.visible:", props.value)

  if (props.value) {
    thisMethods.setOpen()
  }
})
</script>


<style lang="scss">
.chann-btn {
  padding: 4px 0 !important;
}
.check-item {
  display: flex;
  align-items: center;
  padding: 10px;
  .check-item-tit {
    flex: 1;
    width: 150px;
    white-space: nowrap; /* 确保文本在一行内显示 */
    overflow: hidden; /* 超出容器的文本将被隐藏 */
    text-overflow: ellipsis;
  }
}
.statistics {
  &-header {
    display: flex;
    align-items: center;
    border-radius: $layoutBorderRadius;
    padding: 10px;
    background-color: #fff;

    &-video {
      position: relative;

      .video-icon {
        position: absolute;
        transform: translate(-50%, -50%);
        left: 50%;
        top: 50%;
        z-index: 999;
        font-size: 50px;
        color: #ffffff;
        cursor: pointer;
      }
    }

    &-box {
      margin-left: 20px;
    }

    &-info {
      margin-top: 20px;
      padding-bottom: 20px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #eff2f9;

      &-name {
        color: #373737;
      }

      &-line {
        height: 16px !important;
        width: 1px;
        border-left: 1px solid #373737;
        margin: 0 15px;
      }
    }
  }
}
</style>
