package com.dz.ms.sales.entity;


import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/30
 */
@Getter
@Setter
@NoArgsConstructor
@Table("活动白名单用户")
@TableName(value = "campaign_user")
public class CampaignUser implements Serializable {
    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "id")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = true, comment = "活动标识", uniqueKeys = {"campaignCode", "mobile"})
    private String campaignCode;

    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, comment = "用户类型 1购买指定商品的用户 2其他导入的用户 3报名中奖的用户")
    private Integer type;

    @Columns(type = ColumnType.VARCHAR, length = 20, isNull = false, comment = "手机号")
    private String mobile;

//    @Columns(type = ColumnType.VARCHAR, length = 50, isNull = false, comment = "unionid")
//    private String unionid;

//    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "是否删除 0未删除 1已删除")
//    @TableLogic
//    private Integer isDeleted;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;

    public CampaignUser(Long id, String campaignCode, Integer type, String mobile) {
        this.id = id;
        this.campaignCode = campaignCode;
        this.type = type;
        this.mobile = mobile;
    }
}
