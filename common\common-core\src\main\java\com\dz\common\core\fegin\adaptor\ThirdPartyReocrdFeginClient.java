package com.dz.common.core.fegin.adaptor;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.adaptor.ThirdPartyRecordVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 第三方接口请求记录FeginClient
 */
@FeignClient(name = ServiceConstant.ADAPTOR_SERVICE_NAME, contextId = "ThirdPartyReocrdFeginClient")
public interface ThirdPartyReocrdFeginClient {

    /**
     * 第三方接口请求记录
     * @return
     */
    @PostMapping(value = "/third/party/record")
    public Result<Boolean> thridPartRecord(@RequestBody ThirdPartyRecordVo param);

    /**
     * 第三方宁盾接口请求记录
     * @return
     */
    @PostMapping(value = "/third/dKeyAm/record")
    Result<Boolean> thirdDKeyAmRecord(@RequestBody ThirdPartyRecordVo param);

}

