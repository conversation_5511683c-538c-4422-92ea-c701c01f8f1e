<!--signUp/pages/registrationSuccessful/registrationSuccessful.wxml-->
<my-page overallModal="{{overallModal}}">
  <view class="page-container" style="background-image:url({{$cdn}}/MiKangCampaign/mk-submit-success-bg.png);">
    <custom-header background="transparent" type="{{1}}" color="black" />
    <view class="registrationSuccessful">
      <view class="activeRules" bindtap="activeRules">
        <view class="activeRules-in">活动规则</view>
      </view>
      <!-- <view class="activeRules2" bindtap="activeRules2">
        <view class="activeRules2-in">体验官公示结果</view>
      </view> -->
      <view class="image">
        <image class="top-success" src="{{$cdn}}/MiKangCampaign/mk-submit-success.png" mode="" />
        <view class="h1">
          提交完成
        </view>
        <view class="p1">
          <view>若您中选成为体验官，我们将于</view>
          <view>
            <text class="focus-text">{{sendSmsTime}}</text>发送中选短信通知
          </view>
          <view>
            并在<text class="focus-text">10个工作日</text>内为您寄送
          </view>
          <view>米糠护肤礼请注意查收</view>
        </view>
      </view>
      <view class="bottom-footer">
        <view class="footer-tips">{{!disabled ? '报名结果将通过消息通知发送，请注意查收':'开启订阅可通过微信收到中选通知'}}</view>
        <view class="bottom-box">
          <basic-button width="{{670}}" loading="{{loading}}" size="large" bind:click="submit">
            {{ !disabled ? "返回首页":"点击订阅通知"}}
          </basic-button>
        </view>
      </view>

    </view>
    <!-- 拒绝订阅 提示框 -->
    <guidePopup showGuide="{{showContact1}}" bind:finish="closeGuide" />
  </view>
</my-page>