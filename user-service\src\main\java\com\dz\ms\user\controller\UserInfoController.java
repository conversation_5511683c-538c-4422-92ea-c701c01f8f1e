package com.dz.ms.user.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.user.*;
import com.dz.common.core.dto.wechat.DecryptUserDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.user.UserInfoFeginClient;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.ms.user.dto.UserMobileUpdDTO;
import com.dz.ms.user.dto.UserRegisterDTO;
import com.dz.ms.user.dto.UserUpdParamDTO;
import com.dz.ms.user.dto.UserUpdateDTO;
import com.dz.ms.user.entity.UserInfo;
import com.dz.ms.user.service.UserInfoService;
import com.dz.ms.user.vo.LogOffVo;
import com.dz.ms.user.vo.MyMileageDetailsVo;
import com.dz.ms.user.vo.UserMobileBindVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

@Api(tags = "用户信息")
@RestController
public class UserInfoController implements UserInfoFeginClient {
    @Value("${spring.profiles.active:}")
    public String profile;
    @Resource
    private UserInfoService userInfoService;

    @ApiOperation("获取当前用户会员信息")
    @GetMapping(value = {"/app/current_member_info", "/current_member_info"})
    public Result<MemberInfoDTO> getCurrentUserMemberInfo() {
        Result<MemberInfoDTO> result = new Result<>();
        result.setData(userInfoService.getUserMemberInfo(null));
        return result;
    }

    @ApiOperation("根据uid获取用户简要信息")
    @GetMapping(value = "/user/simple_info")
    public Result<UserSimpleDTO> getUserSimpleInfo(@RequestParam(value = "uid", required = false) Long uid) {
        Result<UserSimpleDTO> result = new Result<>();
        result.setData(userInfoService.getUserSimpleInfo(uid));
        return result;
    }

    @ApiOperation("从db根据uid获取用户简要信息")
    @GetMapping(value = "/user/db_simple_info")
    public Result<UserSimpleDTO> getDbUserSimpleInfo(@RequestParam(value = "uid") Long uid) {
        Result<UserSimpleDTO> result = new Result<>();
        result.setData(userInfoService.getDbUserSimpleInfo(uid));
        return result;
    }

    /**
     * 从db根据List<uid>获取用户简要信息
     */
    @ApiOperation("从db根据List<uid>获取用户简要信息")
    @PostMapping(value = "/user/db_simple_info_list")
    public Result<List<UserSimpleDTO>> getDbUserSimpleInfoList(@RequestBody List<Long> uidList) {
        Result<List<UserSimpleDTO>> result = new Result<>();
        result.setData(userInfoService.getDbUserSimpleInfoList(uidList));
        return result;
    }

    /**
     * 分页查询所有用户信息
     *
     * @return
     */
    @GetMapping(value = "/user/simple_info/list")
    public Result<PageInfo<UserSimpleDTO>> getUserSimpleInfoList(@RequestParam(value = "pageNum", required = false) Integer pageNum, @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        Result<PageInfo<UserSimpleDTO>> result = new Result<>();
        result.setData(userInfoService.getUserSimpleInfoList(pageNum, pageSize));
        return result;
    }

    @ApiOperation("根据授权code获取微信手机号")
    @GetMapping(value = "/app/wxphone_bycode")
    public Result<DecryptUserDTO> getWxPhoneByCode(@RequestParam("code") String code) {
        Result<DecryptUserDTO> result = new Result<>();
        result.setData(userInfoService.getWxPhoneByCode(code));
        return result;
    }

    @ApiOperation("根据授权code获取微信手机号对应会员信息")
    @GetMapping(value = "/app/wxphone_member_bycode")
    public Result<MemberInfoDTO> getWxPhoneMemberByCode(@RequestParam("code") String code){
        Result<MemberInfoDTO> result = new Result<>();
        result.setData(userInfoService.getWxPhoneMemberByCode(code));
        return result;
    }

    @ApiOperation("用户注册")
    @PostMapping(value = "/app/register")
    public Result<Object> userRegister(@RequestBody UserRegisterDTO param) {
        Result<Object> result = new Result<>();
        /*
        if(StringUtils.isBlank(param.getSurname())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "姓氏不能为空");
        }
        if(StringUtils.isBlank(param.getName())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "名字不能为空");
        }
        */
        /*if(StringUtils.isBlank(param.getRealName())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "姓名不能为空");
        }
        if(null == param.getGender() || (!param.getGender().equals(1) && !param.getGender().equals(2))) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "性别不能为空");
        }*/
        if (StringUtils.isBlank(param.getMobile())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "手机号不能为空");
        }
        /*if(StringUtils.isBlank(param.getBirthday())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "生日不能为空");
        }*/
        CurrentUserDTO user = SecurityContext.getUser();
        userInfoService.userRegister(param, user.getUid(), user.getTenantId());
        return result;
    }

    @ApiOperation("更新当前用户信息")
    @PostMapping(value = "/app/update")
    public Result<Object> updateUserInfo(@RequestBody UserUpdateDTO param) {
        Result<Object> result = new Result<>();
        CurrentUserDTO user = SecurityContext.getUser();
        userInfoService.updateUserInfo(param, user.getUid(), user.getTenantId());
        return result;
    }

    @ApiOperation("当前用户新增clientId")
    @PostMapping(value = "/app/addUserClientId")
    public Result<String> addUserClientId() {
        Result<String> result = new Result<>();
        CurrentUserDTO user = SecurityContext.getUser();
        result.setData(userInfoService.addUserClientId(user.getUid(), user.getTenantId()));
        return result;
    }

    @ApiOperation("注销用户")
    @PostMapping(value = "/app/log/off")
    public Result<Object> logOff(@RequestBody LogOffVo param) {
        Result<Object> result = new Result<>();
        CurrentUserDTO user = SecurityContext.getUser();
        userInfoService.logOff(param, user.getUid(), user.getTenantId());
        return result;
    }

    @ApiOperation("发送短信验证码")
    @GetMapping(value = "/app/send/sms/code")
    public Result<Object> sendSmsCode(){
        Result<Object> result = new Result<>();
        userInfoService.sendSmsCode();
        return result;
    }

    @ApiOperation("发送短信验证码2")
    @PostMapping(value = "/app/send/sms/code2")
    public Result<String> sendSmsCode2(@RequestBody @Valid SmsDTO smsDTO){
        Result<String> result = new Result<>();
        result.setData(userInfoService.sendSmsCode(smsDTO.getMobile()));
        return result;
    }

    @ApiOperation("会员手机号变更")
    @PostMapping(value = "/app/mobile/update")
    public Result<Object> mobileUpdate(@RequestBody UserMobileUpdDTO userMobileUpdDTO){
        Result<Object> result = new Result<>();
        userInfoService.mobileUpdate(userMobileUpdDTO);
        return result;
    }

    @ApiOperation("会员手机号绑定")
    @PostMapping(value = "/app/mobile/bind")
    public Result<Object> mobileBind(@RequestBody UserMobileBindVo userMobileBindVo){
        Result<Object> result = new Result<>();
        userInfoService.mobileBind(userMobileBindVo);
        return result;
    }

    @ApiOperation("更新授权隐私条款版本号及勾选")
    @PostMapping(value = "/app/update/policy_version")
    public Result<Object> updateUserPolicyVersion(@RequestBody UserUpdateDTO param) {
        Result<Object> result = new Result<>();
        userInfoService.updateUserPolicyVersion(param);
        return result;
    }

    /**
     * 根据用户ID列表获取用户openid列表
     *
     * @param ids
     * @param tenantId
     * @return
     */
    @PostMapping(value = "/user/openid_byids")
    public Result<List<String>> getUserOpenidByIds(@RequestBody List<Long> ids, @RequestParam("tenantId") Long tenantId) {
        Result<List<String>> result = new Result<>();
        result.setData(userInfoService.getUserOpenidByIds(ids, tenantId));
        return result;
    }


    @ApiOperation("当前登录用户简要信息")
    @GetMapping(value = "/user/current_simple_info")
    public Result<UserSimpleDTO> getCurrentUserSimpleInfo() {
        Result<UserSimpleDTO> result = new Result<>();
        result.setData(userInfoService.getCurrentUserSimpleInfo());
        return result;
    }

    @ApiOperation("当前登录用户积分信息")
    @GetMapping(value = {"/app/user/my/points", "/user/my/points"})
    public Result<MyPointsDTO> myPoints() {
        Result<MyPointsDTO> result = new Result<>();
        result.setData(userInfoService.myPoints());
        return result;
    }

    /**
     * 根据用户昵称获取用户ID列表
     *
     * @param userName
     * @return
     */
    @GetMapping(value = "/user/id_list_by_name")
    @Override
    public Result<List<Long>> getUserIdListByName(@RequestParam("userName") String userName) {
        Result<List<Long>> ret = new Result<>();
        ret.setData(userInfoService.getUserIdListByName(userName));
        return ret;
    }

    @ApiOperation("当前登录用户积分流水")
    @PostMapping(value = "/app/user/my/points/records")
    public Result<PageInfo<MyPointsRecordsDTO>> myPointsRecords(@RequestBody MyMileageDetailsVo param) {
        Result<PageInfo<MyPointsRecordsDTO>> result = new Result<>();
        result.setData(userInfoService.myPointsRecords(param));
        return result;
    }

    @ApiOperation("即将过期积分列表")
    @PostMapping(value = "/app/user/my/points/recent")
    public Result<PageInfo<HistoryPointsRecordsDTO>> recentExpirePoints(@RequestBody MyMileageDetailsVo param) {
        Result<PageInfo<HistoryPointsRecordsDTO>> result = new Result<>();
        result.setData(userInfoService.recentExpirePoints(param));
        return result;
    }

    @ApiOperation("历史过期积分列表")
    @PostMapping(value = "/app/user/my/points/history")
    public Result<PageInfo<HistoryPointsRecordsDTO>> historyExpirePoints(@RequestBody MyMileageDetailsVo param) {
        Result<PageInfo<HistoryPointsRecordsDTO>> result = new Result<>();
        result.setData(userInfoService.historyExpirePoints(param));
        return result;
    }

    @ApiOperation("当前登录用户积分详情")
    @GetMapping(value = "/app/user/my/points/details")
    public Result<MyPointsDetailsDTO> myPointsDetails(@RequestParam("bonusSn") String bonusSn) {
        Result<MyPointsDetailsDTO> result = new Result<>();
        result.setData(userInfoService.myPointsDetails(bonusSn));
        return result;
    }

    @ApiOperation("当前登录用户里程流水")
    @PostMapping(value = "/app/user/my/mileage/records")
    public Result<PageInfo<MyMileageRecordsDTO>> myMileageRecords(@RequestBody MyMileageDetailsVo param) {
        Result<PageInfo<MyMileageRecordsDTO>> result = new Result<>();
        result.setData(userInfoService.myMileageRecords(param));
        return result;
    }

    @ApiOperation("当前登录用户里程明细")
    @GetMapping(value = "/app/user/my/mileage/details")
    public Result<MyMileageDetailsDTO> myMileageDetails(@RequestParam("mileageSn") String mileageSn) {
        Result<MyMileageDetailsDTO> result = new Result<>();
        result.setData(userInfoService.myMileageDetails(mileageSn));
        return result;
    }

    /**
     * 更新首购任务状态
     * @param firstOrderStatus
     * @return
     */
    @GetMapping(value = "/user/info/update/order/status")
    public Result<Object> updateFirstOrderStatus(@RequestParam("firstOrderStatus") Integer firstOrderStatus,
                                                 @RequestParam(value = "firstOrderTaskId", required = false) Long firstOrderTaskId) {
        Result<Object> result = new Result<>();
        UserInfo userInfo = new UserInfo();
        userInfo.setId(SecurityContext.getUser().getUid());
        userInfo.setFirstOrderStatus(firstOrderStatus);
        if (firstOrderTaskId != null){
            userInfo.setFirstOrderTaskId(firstOrderTaskId);
        }
        userInfoService.updateById(userInfo);
        return result;
    }

    /**
     * 从db根据memberCode获取用户简要信息
     * @param memberCode
     * @return
     */
    @PostMapping(value = "/user/info/by/member")
    public Result<List<UserSimpleDTO>> getDbUserSimpleInfo(@RequestBody List<String> memberCode) {
        Result<List<UserSimpleDTO>> result = new Result<>();
        result.setData(userInfoService.getDbUserByMemberCode(memberCode));
        return result;
    }

    @PostMapping(value = "/user/id_list_by_openid")
    public Result<List<UserSimpleDTO>> getUserIdListByOpenids(@RequestBody List<String> openids, @RequestParam("tenantId") Long tenantId){
        Result<List<UserSimpleDTO>> result = new Result<>();
        result.setData(userInfoService.getUserIdListByOpenids(openids, tenantId));
        return result;
    }



    @GetMapping("/crm/userInfo/tokenExport")
    @ApiOperation("测试用户导出token，不在第三方创建")
    public void exportToken(HttpServletResponse response) {
        if (!"pro".equals(profile)) {
            userInfoService.exportToken(response);
        }
    }
}
