package com.dz.common.base.constant;

/**
 * 接口客户端类型
 * @Author: Handy
 * @Date: 2020/10/11 20:38
 */
public class ClientTypeConstant {

    /** 小程序用户端 */
    public static final int APP = 1;

    /** H5用户端 */
    public static final int WEB = 2;

    /** 小程序导购端 */
    public static final int EMP = 3;

    /** 开放接口openapi端 */
    public static final int OPENAPI = 4;

    /** 后台管理系统端 */
    public static final int CRM = 5;

    /** 运营平台系统端 */
    public static final int OMS = 6;

    public static boolean isApp(int type) {
        return type == APP || type == WEB || type == EMP;
    }

}
