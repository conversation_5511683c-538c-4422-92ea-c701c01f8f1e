package com.dz.ms.user.constants;

/**
 * 我的消息提醒
 */
public enum MyMsgEnum {

    expireCoupon(5,"您有优惠券即将过期，点击查看详情","优惠券即将过期提醒", "您有优惠券即将过期，请于有效期内领取或使用，请至首页-我的礼券查看，点击查看更多", "/pages/myCoupon/myCoupon"),
    couponAccount(3,"您有新的优惠券可领取，点击领取","您有新优惠券可领取", "您有新的优惠券可领取，去查看使用吧，点击查看更多", "/pages/myCoupon/myCoupon"),
    receiveCard(1,"您有一张MUJI会员卡待领取，点击领取","您有一张MUJI会员卡可领取", "请前往“个人中心”点击“会员卡”按钮领取吧", "/pages/more/more"),
    expirePoints(4,"您有一些积分即将过期，点击进行兑礼","积分过期提醒", "您有一些积分即将过期，去兑礼商城看看吧，点击兑礼", "/pages/life/life"),
    ;

    // 消息code
    private final Integer sortNum;
    //首页通知标题
    private final String homeTitle;
    // 标题
    private final String title;
    // 内容
    private final String desc;
    // 跳转链接
    private final String jumpUrl;

    MyMsgEnum(Integer sortNum,String homeTitle,String title, String desc, String jumpUrl) {
        this.sortNum = sortNum;
        this.homeTitle = homeTitle;
        this.title = title;
        this.desc = desc;
        this.jumpUrl = jumpUrl;
    }

    public Integer getSortNum() {
        return sortNum;
    }
    public String getHomeTitle() {
        return homeTitle;
    }
    public String getTitle() {
        return title;
    }

    public String getDesc() {
        return desc;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }
}
