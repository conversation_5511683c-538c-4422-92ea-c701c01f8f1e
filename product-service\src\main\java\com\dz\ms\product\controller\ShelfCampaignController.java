package com.dz.ms.product.controller;

import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.constants.NumConstants;
import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.dto.DownloadCenterDTO;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.IdNumberDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.fegin.basic.ReportDownloadFeignClient;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.ms.product.config.Globals;
import com.dz.ms.product.dto.ShelfCampaignDTO;
import com.dz.ms.product.dto.req.ShelfCampaignParamDTO;
import com.dz.ms.product.dto.req.ShelfCampaignSaveParamDTO;
import com.dz.ms.product.service.ShelfCampaignService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "货架营销活动")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
@Slf4j
public class ShelfCampaignController {

    @Resource
    private ShelfCampaignService shelfCampaignService;
    @Resource
    private ReportDownloadFeignClient reportDownloadFeignClient;

    /**
     * 分页查询货架营销活动
     *
     * @param param
     * @return result<PageInfo < ShelfCampaignDTO>>
     */
    @ApiOperation("分页查询货架营销活动")
    @GetMapping(value = "/crm/shelf_campaign/list")
    public Result<PageInfo<ShelfCampaignDTO>> getShelfCampaignList(@ModelAttribute ShelfCampaignParamDTO param) {
        Result<PageInfo<ShelfCampaignDTO>> result = new Result<>();
        PageInfo<ShelfCampaignDTO> page = shelfCampaignService.getShelfCampaignList(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询货架营销活动
     *
     * @param id
     * @return result<ShelfCampaignDTO>
     */
    @ApiOperation("根据ID查询货架营销活动")
    @GetMapping(value = "/crm/shelf_campaign/info")
    public Result<ShelfCampaignDTO> getShelfCampaignById(@RequestParam("id") Long id) {
        Result<ShelfCampaignDTO> result = new Result<>();
        ShelfCampaignDTO shelfCampaign = shelfCampaignService.getShelfCampaignById(id, NumConstants.ONE);
        result.setData(shelfCampaign);
        return result;
    }

    /**
     * 新增货架营销活动
     *
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增货架营销活动", type = LogType.OPERATELOG)
    @ApiOperation("新增货架营销活动")
    @PostMapping(value = "/crm/shelf_campaign/add")
    public Result<Long> addShelfCampaign(@RequestBody ShelfCampaignSaveParamDTO param) {
        Result<Long> result = new Result<>();
        Long id = shelfCampaignService.saveShelfCampaign(param,true);
        result.setData(id);
        return result;
    }

    /**
     * 更新货架营销活动
     *
     * @param param
     * @return result<Object>
     */
    @SysLog(value = "更新货架营销活动", type = LogType.OPERATELOG)
    @ApiOperation("更新货架营销活动")
    @PostMapping(value = "/crm/shelf_campaign/update")
    public Result<Long> updateShelfCampaign(@RequestBody ShelfCampaignSaveParamDTO param) {
        Result<Long> result = new Result<>();
        shelfCampaignService.saveShelfCampaign(param,false);
        result.setData(param.getId());
        return result;
    }

    /**
     * 根据ID删除货架营销活动
     *
     * @param param
     * @return result<Boolean>
     */
    @SysLog(value = "根据ID删除货架营销活动", type = LogType.OPERATELOG)
    @ApiOperation("根据ID删除货架营销活动")
    @PostMapping(value = "/crm/shelf_campaign/delete")
    public Result<Boolean> deleteShelfCampaignById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        shelfCampaignService.deleteShelfCampaignById(param);
        result.setData(true);
        return result;
    }

    /**
     * 根据ID修改启停状态
     *
     * @param param ID NUMBER 通用DTO
     * @return result<Boolean>
     */
    @SysLog(value = "根据ID修改启停状态", type = LogType.OPERATELOG)
    @ApiOperation("根据ID修改启停状态")
    @PostMapping(value = "/crm/shelf_campaign/update_state")
    public Result<Boolean> updateStateById(@RequestBody IdNumberDTO param) {
        Result<Boolean> result = new Result<>();
        shelfCampaignService.updateStateById(param);
        result.setData(true);
        return result;
    }

    /**
     * 导出货架营销活动
     *
     * @return
     */
    @PostMapping(value = "/shelf_campaign/export_list")
    public Result<Void> exportList(@RequestBody DownloadAddParamDTO exportParam) {
        CurrentUserDTO user = SecurityContext.getUser();
        Globals.downloadThreadPool.execute(() -> {
            try {
                SecurityContext.setUser(user);
                shelfCampaignService.exportList(exportParam);
            } catch (Exception e) {
                log.error("导出错误", e);
                DownloadCenterDTO downloadCenterDTO = new DownloadCenterDTO();
                downloadCenterDTO.setId(exportParam.getDownloadCenterId());
                downloadCenterDTO.setErrorDesc(e.getMessage());
                downloadCenterDTO.setState(2);
                reportDownloadFeignClient.updateDownloadCenter(downloadCenterDTO);
            }
        });
        return new Result<>();
    }

}
