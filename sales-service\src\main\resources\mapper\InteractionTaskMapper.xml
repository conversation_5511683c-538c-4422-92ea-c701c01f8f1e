<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" " http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.sales.mapper.InteractionTaskMapper">

    <!--查出来三天内即将到期的任务-->
    <select id="getInteractionTaskList" resultType="com.dz.ms.sales.entity.InteractionTask">
        SELECT id FROM `t_interaction_task` where `status`=0 and is_time_restrict=1 AND restrict_time_end > NOW()
        AND restrict_time_end &lt;= DATE_ADD(NOW(), INTERVAL 3 DAY)
    </select>
</mapper>
