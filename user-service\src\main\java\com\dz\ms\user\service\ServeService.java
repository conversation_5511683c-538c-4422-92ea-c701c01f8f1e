package com.dz.ms.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.user.ServeDTO;
import com.dz.ms.user.entity.Serve;

import java.util.List;

/**
 * 接口
 * @author: yibo
 * @date:   2024/11/19 17:20
 */
public interface ServeService extends IService<Serve> {

	/**
     * 分页查询
     * @param param
     * @return PageInfo<ServeDTO>
     */
    public List<ServeDTO> getServeList(ServeDTO param);

    /**
     * 根据ID查询
     * @param id
     * @return ServeDTO
     */
    public ServeDTO getServeById(Long id);

    /**
     * 保存
     * @param param
     * @return Long
     */
    public Long saveServe(ServeDTO param);

    /**
     * 根据ID删除
     * @param param
     */
    public void deleteServeById(IdCodeDTO param);

    void updateSort(List<ServeDTO> param);

    List<ServeDTO> getAppServeList();
}
