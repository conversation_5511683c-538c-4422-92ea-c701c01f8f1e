<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.product.mapper.TagInfoMapper">

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
        id,
  	    name,
  	    cate,
  	    is_deleted,
  	    tenant_id,
  	    creator,
  	    created,
  	    modified,
  	    modifier
    </sql>

    <!-- 查询示例 -->
    <select id="selectDemo" parameterType="java.lang.Long" resultType="com.dz.ms.product.entity.TagInfo">
        select
        <include refid="Base_Column_List"/>
        from tag_info
        where id = #{id,jdbcType=BIGINT}
        AND tenant_id = #{tenantId}
    </select>

</mapper>
