package com.dz.ms.product.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.product.dto.ShelfPeopleDTO;
import com.dz.ms.product.service.ShelfPeopleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "货架人群包条件")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class ShelfPeopleController {

    @Resource
    private ShelfPeopleService shelfPeopleService;

    /**
     * 分页查询货架人群包条件
     *
     * @param param
     * @return result<PageInfo < ShelfPeopleDTO>>
     */
    @ApiOperation("分页查询货架人群包条件")
    @GetMapping(value = "/shelf_people/list")
    public Result<PageInfo<ShelfPeopleDTO>> getShelfPeopleList(@ModelAttribute ShelfPeopleDTO param) {
        Result<PageInfo<ShelfPeopleDTO>> result = new Result<>();
        PageInfo<ShelfPeopleDTO> page = shelfPeopleService.getShelfPeopleList(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询货架人群包条件
     *
     * @param id
     * @return result<ShelfPeopleDTO>
     */
    @ApiOperation("根据ID查询货架人群包条件")
    @GetMapping(value = "/shelf_people/info")
    public Result<ShelfPeopleDTO> getShelfPeopleById(@RequestParam("id") Long id) {
        Result<ShelfPeopleDTO> result = new Result<>();
        ShelfPeopleDTO shelfPeople = shelfPeopleService.getShelfPeopleById(id);
        result.setData(shelfPeople);
        return result;
    }

    /**
     * 新增货架人群包条件
     *
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增货架人群包条件", type = LogType.OPERATELOG)
    @ApiOperation("新增货架人群包条件")
    @PostMapping(value = "/shelf_people/add")
    public Result<Long> addShelfPeople(@RequestBody ShelfPeopleDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param, true);
        Long id = shelfPeopleService.saveShelfPeople(param);
        result.setData(id);
        return result;
    }

    /**
     * 更新货架人群包条件
     *
     * @param param
     * @return result<Object>
     */
    @SysLog(value = "更新货架人群包条件", type = LogType.OPERATELOG)
    @ApiOperation("更新货架人群包条件")
    @PostMapping(value = "/shelf_people/update")
    public Result<Long> updateShelfPeople(@RequestBody ShelfPeopleDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param, false);
        shelfPeopleService.saveShelfPeople(param);
        result.setData(param.getId());
        return result;
    }

    /**
     * 验证保存参数
     *
     * @param param
     */
    private void validationSaveParam(ShelfPeopleDTO param, boolean isAdd) {
        if (isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if (!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
    }

    /**
     * 根据ID删除货架人群包条件
     *
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除货架人群包条件")
    @PostMapping(value = "/shelf_people/delete")
    public Result<Boolean> deleteShelfPeopleById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        shelfPeopleService.deleteShelfPeopleById(param);
        result.setData(true);
        return result;
    }

}
