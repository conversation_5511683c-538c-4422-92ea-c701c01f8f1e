package com.dz.ms.user.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.io.Serializable;
import java.util.Date;

/**
 * 人群包条件横向
 * @author: yibo
 * @date:   2024/11/18 13:53
 */
@Getter
@Setter
@NoArgsConstructor
@Table("人群包条件横向")
@TableName(value = "crowd_condition_row")
public class CrowdConditionRow implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "主键id")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "人群包id")
    private Long crowdId;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "人群包条件id(父级)")
    private Long conditionId;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = false,comment = "条件类型 1会员等级")
    private Integer conditionType;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = true,comment = "条件 ")
    private Integer conditionValue;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = true,defaultValue = "0",comment = "条件判断 0首位 1或 2且")
    private Integer conditionJudge;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    private Date createTime;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "修改时间")
    private Date updateTime;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = true,comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "租户id")
    private Long tenantId;
    @Columns(type = ColumnType.INT,length = 11,isNull = true,defaultValue = "0",comment = "排序")
    private Integer sort;
}
