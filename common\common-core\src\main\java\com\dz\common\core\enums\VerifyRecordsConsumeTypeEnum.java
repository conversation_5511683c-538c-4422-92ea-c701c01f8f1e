package com.dz.common.core.enums;

import java.util.Objects;

/**
 * 消耗方式 枚举
 * <AUTHOR>
 * @date 2023-10-02 10:51
 */
public enum VerifyRecordsConsumeTypeEnum {
    POINTS(1, "积分"),
    COUPON(2, "卡券"),
    EQUITY(3, "权益"),
    PIN_CODE(4, "pin_code"),
    BUY(5, "付费"),
    GRADE_GIFT(6, "积分兑礼"),
    TURNTABLE(7, "大转盘"),
    GRATIS(8, "无偿")
    ;
    private final Integer code;
    private final String value;

    VerifyRecordsConsumeTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (VerifyRecordsConsumeTypeEnum resultEnum : VerifyRecordsConsumeTypeEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum.value;
            }
        }
        return null;
    }
}
