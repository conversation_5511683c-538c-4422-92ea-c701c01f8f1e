package com.dz.ms.sales.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.ms.sales.dto.CrmSignInUserDTO;
import com.dz.ms.sales.dto.CrmSignInUserParamDTO;
import com.dz.ms.sales.entity.SignInUser;
import com.dz.ms.sales.entity.SignInUserDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/8
 */
@Repository
public interface SignInUserMapper extends BaseMapper<SignInUser> {


    int signInUserFail(@Param("ids") List<Long> ids, @Param("userId") Long userId);

    int signInUserFailByEndTime(@Param("userId") Long userId);

    List<SignInUser> notSignInByToday(String campaignCode);
}
