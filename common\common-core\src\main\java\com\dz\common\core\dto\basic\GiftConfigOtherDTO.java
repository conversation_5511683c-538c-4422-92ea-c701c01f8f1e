package com.dz.common.core.dto.basic;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 核心礼遇其他配置
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class GiftConfigOtherDTO {

    @ApiModelProperty(value = "ID")
    private Long id;
    @ApiModelProperty(value = "礼遇类型：1新人礼配置，2二回礼配置，3生日礼配置，4升级礼配置，5无以上礼遇可领取且券列表有有效券时，6无礼遇、无可用券时，7无礼遇、有可使用的商品券时")
    private Integer giftType;
    @ApiModelProperty(value = "会员等级编号 1普通会员，2铜级会员,3银级会员,4金级会员")
    private Integer levelId;
    @ApiModelProperty(value = "弹窗配置")
    private String ballImg;
    @ApiModelProperty(value = "跳转链接")
    private String ballJump;
    @ApiModelProperty(value = "关联活动id")
    private String activityId;
}
