package com.dz.common.core.aop;

import com.dz.common.base.constant.ClientTypeConstant;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.threadlocal.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * 导购权限校验切片实现
 * <AUTHOR>
 * @date 2022/2/4 19:14
 */
@Aspect
@Component
@Slf4j
public class EmpAuthAspect {
	public EmpAuthAspect(){}

	/**
	* 所有标注了@ActionLog标签的方法切入点
	*/
	@Pointcut("execution(@com.dz.common.core.annotation.EmpAuth * com.dz.ms.*.controller.*.*(..))")
	private void empAuthController() {}

	@Around("empAuthController()")
	public Object aroundMethods(ProceedingJoinPoint joinPoint) throws Throwable {
		try{
			if(!SecurityContext.getUser().getType().equals(ClientTypeConstant.EMP)) {
				throw new BusinessException(ErrorCode.FORBIDDEN, "无接口权限");
			}
			return joinPoint.proceed();
		}catch(Exception e){
			throw e;
		}

	}

}
