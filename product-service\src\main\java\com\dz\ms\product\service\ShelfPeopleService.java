package com.dz.ms.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.ms.product.dto.ShelfPeopleDTO;
import com.dz.ms.product.entity.ShelfPeople;

/**
 * 货架人群包条件接口
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:27
 */
public interface ShelfPeopleService extends IService<ShelfPeople> {

    /**
     * 分页查询货架人群包条件
     *
     * @param param
     * @return PageInfo<ShelfPeopleDTO>
     */
    public PageInfo<ShelfPeopleDTO> getShelfPeopleList(ShelfPeopleDTO param);

    /**
     * 根据ID查询货架人群包条件
     *
     * @param id
     * @return ShelfPeopleDTO
     */
    public ShelfPeopleDTO getShelfPeopleById(Long id);

    /**
     * 保存货架人群包条件
     *
     * @param param
     * @return Long
     */
    public Long saveShelfPeople(ShelfPeopleDTO param);

    /**
     * 根据ID删除货架人群包条件
     *
     * @param param
     */
    public void deleteShelfPeopleById(IdCodeDTO param);

}
