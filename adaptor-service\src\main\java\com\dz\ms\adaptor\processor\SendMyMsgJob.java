package com.dz.ms.adaptor.processor;

import com.dz.ms.adaptor.service.PointsSubscriptionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;

/**
 * 我的消息发送即将过期消息提醒
 */
@Slf4j
@Component
public class SendMyMsgJob implements BasicProcessor {

    @Resource
    private PointsSubscriptionService pointsSubscriptionService;
    @Override
    public ProcessResult process(TaskContext context) throws IOException, ParseException {
        log.info("我的消息发送即将过期消息提醒开始");
        String jobParams=context.getJobParams();
        pointsSubscriptionService.sendMyMsg(jobParams);
        log.info("我的消息发送即将过期消息提醒结束");
        return new ProcessResult(true, "success");
    }
}