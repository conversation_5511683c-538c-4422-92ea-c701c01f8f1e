package com.dz.ms.sales.service;


import com.dz.ms.sales.entity.UserLotterySummary;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/15
 */
public interface UserLotterySummaryService {

    UserLotterySummary getUserLotterySummary(String campaignCode, String unionid);

    void operation(String campaignCode, String unionid, String operation, int num);

    List<UserLotterySummary> getResidueLotterySummary();

}
