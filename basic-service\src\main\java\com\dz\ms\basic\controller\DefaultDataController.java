package com.dz.ms.basic.controller;

import com.alibaba.fastjson.JSON;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.dto.DefaultDataDTO;
import com.dz.ms.basic.service.DefaultDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Api(tags="默认数据")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class DefaultDataController  {

    @Resource
    private DefaultDataService defaultDataService;

    /**
     * 分页查询默认数据
     * @param param
     * @return result<PageInfo<DefaultDataDTO>>
     */
    @ApiOperation("分页查询默认数据")
	@GetMapping(value = "/default_data/list")
    public Result<PageInfo<DefaultDataDTO>> getDefaultDataList(@ModelAttribute DefaultDataDTO param) {
        Result<PageInfo<DefaultDataDTO>> result = new Result<>();
		PageInfo<DefaultDataDTO> page = defaultDataService.getDefaultDataList(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询默认数据
     * @param id
     * @return result<DefaultDataDTO>
     */
    @ApiOperation("根据ID查询默认数据")
	@GetMapping(value = "/default_data/info")
    public Result<DefaultDataDTO> getDefaultDataById(@RequestParam("id") Long id) {
        Result<DefaultDataDTO> result = new Result<>();
        DefaultDataDTO defaultData = defaultDataService.getDefaultDataById(id);
        result.setData(defaultData);
        return result;
    }

    /**
     * 新增默认数据
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增默认数据",type = LogType.OPERATELOG)
    @ApiOperation("新增默认数据")
	@PostMapping(value = "/default_data/add")
    public Result<Long> addDefaultData(@RequestBody DefaultDataDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,true);
        Long id = defaultDataService.saveDefaultData(param);
        result.setData(id);
        return result;
    }

    /**
    * 更新默认数据
    * @param param
    * @return result<Object>
    */
    @SysLog(value = "更新默认数据",type = LogType.OPERATELOG)
    @ApiOperation("更新默认数据")
    @PostMapping(value = "/default_data/update")
    public Result<Long> updateDefaultData(@RequestBody DefaultDataDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,false);
        defaultDataService.saveDefaultData(param);
        result.setData(param.getId());
        return result;
    }

    /**
    * 验证保存参数
    * @param param
    */
    private void validationSaveParam(DefaultDataDTO param,boolean isAdd) {
        if(isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if(!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
    }

	/**
     * 根据ID删除默认数据
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除默认数据")
	@PostMapping(value = "/default_data/delete")
    public Result<Boolean> deleteDefaultDataById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        defaultDataService.deleteDefaultDataById(param);
        result.setData(true);
        return result;
    }

    @ApiOperation("获取抽签文案")
    @GetMapping(value = "/dict_data/lottery")
    public Result<List<String[]>> getLotteryWords() {
        DefaultDataDTO defaultData = defaultDataService.getDefaultDataByType(2);
        if(null == defaultData || StringUtils.isEmpty(defaultData.getContent())) {
            return Result.success().data(new ArrayList<>());
        }
        return Result.success().data(JSON.parseArray(defaultData.getContent(),String.class));
    }

}
