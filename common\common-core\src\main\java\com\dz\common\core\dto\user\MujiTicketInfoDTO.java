package com.dz.common.core.dto.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class MujiTicketInfoDTO {

    @ApiModelProperty(value = "会员编号")
    private String memberCode;
    @ApiModelProperty(value = "订单编号")
    private String orderSn;
    @ApiModelProperty(value = "找零")
    private String change;
    @ApiModelProperty(value = "交易日期 例如：2022-02-27 15:20:56.0")
    private String checkoutDt;
    @ApiModelProperty(value = "促销优惠")
    private String discount;
    @ApiModelProperty(value = "优惠金额")
    private String discountAmt;
    @ApiModelProperty(value = "是否使用内买券 Y=是 N=否")
    private String internalOrder;
    @ApiModelProperty(value = "内买券订单文案提示")
    private String internalOrderTip;
    @ApiModelProperty(value = "原价金额")
    private String origAmt;
    @ApiModelProperty(value = "数量合计")
    private String qty;
    @ApiModelProperty(value = "实收金额")
    private String realAmt;
    @ApiModelProperty(value = "应收金额")
    private String saleAmt;
    @ApiModelProperty(value = "销售单号")
    private String saleId;
    @ApiModelProperty(value = "门店地址")
    private String shopAddr;
    @ApiModelProperty(value = "门店编号")
    private String shopId;
    @ApiModelProperty(value = "门店名称")
    private String shopName;
    @ApiModelProperty(value = "收银员")
    private String staffId;
    @ApiModelProperty(value = "电话")
    private String telNo;
    @ApiModelProperty(value = "贵宾优惠")
    private String vipDiscount;
    @ApiModelProperty(value = "可累计权益金额 单位/分 例如：28900")
    private String calculateFee;
    @ApiModelProperty(value = "本次消费获得里程 例如：289")
    private String mileage;
    @ApiModelProperty(value = "本次消费获得积分")
    private String bonus;
    @ApiModelProperty(value = "商品明细")
    private List<MujiTicketItemDTO> items;
    @ApiModelProperty(value = "支付明细")
    private List<MujiTicketPayDTO> payList;
}
