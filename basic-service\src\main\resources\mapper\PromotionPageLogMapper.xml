<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.basic.mapper.PromotionPageLogMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    id,
  	    scene,
  	    scene_name,
  	    state,
  	    tenant_id,
  	    creator,
  	    created,
  	    modifier,
  	    modified
    </sql>
    <select id="getPromotionPageList" resultType="com.dz.ms.basic.dto.PromotionPageDTO">
        select p.id,p.link_url,p.qr_code,p.creator,p.create_time,pc.channel_name,p.p_id
        from promotion_page as p
        left join promotion_channel as pc on pc.id = p.p_id
        where p.t_id = #{templateId}
    </select>

</mapper>
