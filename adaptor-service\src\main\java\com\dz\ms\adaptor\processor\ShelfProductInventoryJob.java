package com.dz.ms.adaptor.processor;

import com.dz.common.core.fegin.product.ProductOnTaskFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import javax.annotation.Resource;

/**
 * 货架商品库存任务
 */
@Slf4j
@Component
public class ShelfProductInventoryJob implements BasicProcessor {

    @Resource
    private ProductOnTaskFeignClient productOnTaskFeignClient;

    @Override
    public ProcessResult process(TaskContext context) {
        productOnTaskFeignClient.executeProductOnTaskOfAllShelf();
        log.info("执行所有可用货架上下架任务 完成");
        return new ProcessResult(true, "success");
    }
}