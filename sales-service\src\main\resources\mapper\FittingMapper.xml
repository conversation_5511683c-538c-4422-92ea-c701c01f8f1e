<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.sales.mapper.FittingMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    id,
  	    user_id,
  	    template_id,
  	    img_url,
  	    tenant_id,
  	    created,
  	    modified,
  	    is_deleted
    </sql>

    <!-- 查询示例 -->
    <select id="selectDemo" parameterType="java.lang.Long" resultType="com.dz.ms.sales.entity.Fitting">
        select
        <include refid="Base_Column_List" />
        from fitting
        where id = #{id,jdbcType=BIGINT}
        AND tenant_id = #{tenantId}
    </select>

</mapper>
