<template>
  <!-- 图片上传组件 -->
  <a-upload accept=".png, .jpg, .jpeg," :key="path" :disabled="loading || disabled" :multiple="multiple" :maxCount="1"
    name="avatar" :show-upload-list="false" :custom-request="uploadImg" :before-upload="beforeUpload">
    <!-- 上传区域容器 -->
    <div :style="{
      width: width + 'px !important',
      height: height + 'px !important',
      marginBottom: '10px',
      cursor: loading ? 'not-allowed' : 'pointer'
    }">
      <!-- 已上传图片展示区域 -->
      <div class="upload show" v-if="imgUrl">
        <img style="width:100%;height: 100%;display:block;object-fit:contain" ref="myImageRef" :src="imgUrl"
          @error="e => { e.target.src = imgErr }" alt="avatar" />
        <!-- 预览按钮 -->
        <eye-outlined class="upload-eye" v-if="isImageURL(imgUrl)" @click.stop="setVisible(true)" />
        <!-- 删除按钮 -->
        <delete-outlined class="upload-delete" @click.stop="removeFile(index)" v-if="!disabled && deleted" />
        <!-- 上传中状态 -->
        <div v-if="loading" class="upload-loading">
          <loading-outlined />上传中...
        </div>
      </div>

      <!-- 上传按钮区域 -->
      <div class="upload" v-else>
        <template v-if="loading">
          <loading-outlined />上传中...
        </template>
        <template v-else>
          <plus-outlined />
          上传图片
        </template>
      </div>

      <!-- 图片预览弹窗 -->
      <div style="position:fixed;width:0px;height:0px;">
        <a-image :width="0" :height="0" :style="{ display: 'none' }" :preview="{
          visible: visible,
          onVisibleChange: setVisible,
        }" :src="imgUrl" />
      </div>
    </div>
  </a-upload>
</template>

<script setup>
import { fileUpload } from '@/http/index.js'
import { message } from 'ant-design-vue';
import { reactive, toRefs, ref, onMounted, watch } from 'vue';
import imgErr from '@/assets/images/imgErr.png';

// 定义emit事件
const emit = defineEmits(['success'])

// Props定义
const props = defineProps({
  api: { // 图片上传接口
    type: Function,
    default: fileUpload,
  },
  disabled: { // 是否禁用上传
    type: Boolean,
    default: false
  },
  deleted: { // 是否可删除
    type: Boolean,
    default: true
  },
  height: { // 图片高度
    type: Number || String
  },
  width: { // 图片宽度
    type: Number || String
  },
  imgUrl: { // 默认图片URL
    type: String,
  },
  form: { // 表单对象
    type: Object
  },
  path: { // 表单字段路径
    type: [String, Number],
  },
  max: { // 文件大小限制(MB)
    type: Number || String,
    default: 10
  },
  maxImgPx: { // 图片尺寸限制
    type: Array,
    default: [1024, 1024]
  },
  maxImgPxShow: { // 是否开启图片尺寸校验
    type: Boolean,
    default: false
  },
  specialPicture: { // 是否为特殊图片模式
    type: Boolean,
    default: false
  },
  enforceSquare: { // 是否强制图片为正方形
    type: Boolean,
    default: false
  },
  multiple: { // 是否支持多选
    type: Boolean,
    default: false
  },
  numTotal: { // 已上传图片总数
    type: Number,
    default: 0
  },
})

// 判断URL是否为图片
function isImageURL(url) {
  const regex = /\.(jpeg|jpg|gif|png|bmp)$/i;
  return regex.test(url);
}

// 响应式状态
let { loading, visible, uploadCount } = toRefs(reactive({
  loading: false,
  visible: false,
  uploadCount: 0
}))

// 获取图片尺寸
async function loadImageDimensions(file) {
  return new Promise((resolve, reject) => {
    const image = new Image();
    image.onload = () => {
      resolve({
        width: image.width,
        height: image.height
      });
    };
    image.onerror = reject;
    image.src = window.URL.createObjectURL(file);
  });
}

// 校验图片尺寸
async function handleImageUpload(file, maxWidth, maxHeight) {
  const { width, height } = await loadImageDimensions(file);
  if (width > maxWidth) {
    message.error(`图片宽度超出${maxWidth}像素，请重新选择`);
    return false;
  }
  if (height > maxHeight) {
    message.error(`图片高度超出${maxHeight}像素，请重新选择`);
    return false;
  }
  return true;
}

// 校验图片大小限制
function validateImageSize(file) {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = function () {
      const width = this.width;
      const height = this.height;
      const longSide = Math.max(width, height);
      const shortSide = Math.min(width, height);

      if (longSide <= 10800 && shortSide <= 1080) {
        resolve(true);
      } else {
        message.error('图片尺寸超出企微限制，长边不超过10800像素，短边不超过1080像素!');
        resolve(false);
      }
    };
    img.src = window.URL.createObjectURL(file);
  });
}

// 防抖标记
const shotTaste = ref(false)

// 上传前校验
const beforeUpload = async (file, fileList) => {
  // 校验数量限制
  if (props.multiple && (fileList.length + props.numTotal > 10)) {
    if (!shotTaste.value) {
      shotTaste.value = true;
      if (props.numTotal > 0) {
        message.error(`最多支持上传10张，已上传${props.numTotal}张图片!`);
      } else {
        message.error('最多支持上传10张!');
      }
      setTimeout(() => {
        shotTaste.value = false;
      }, 2000);
    }
    return false;
  }

  // 校验文件类型
  // console.log(file.type)
  const type = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'].includes(file.type);
  if (!type) {
    message.error('图片格式不支持!');
    return false;
  }

  // 校验文件大小
  const isLimit = file.size / 1024 / 1024 < props.max;
  if (!isLimit) {
    message.error(`图片大小超过${props.max}M!`);
    return false;
  }

  // 校验图片比例
  if (props.enforceSquare) {
    const { width, height } = await loadImageDimensions(file);
    if (width !== height) {
      message.error('长宽比需为1:1!');
      return false;
    }
  }

  return true;
}

// 设置预览状态
const setVisible = (value) => {
  visible.value = value
}

// 移除文件
const removeFile = () => {

  emit('success', {
    form: props.form,
    path: props.path,

    imgUrl: props.specialPicture ? null : ''
  })
}

// 正在上传的文件计数
const ongoingUploads = ref(0)

// 上传图片
const uploadImg = ({ file }) => {
  ongoingUploads.value += 1;
  loading.value = true;

  // 上传完成处理
  const onUploadComplete = () => {
    ongoingUploads.value -= 1;
    if (ongoingUploads.value === 0) {
      loading.value = false;
    }
  }

  // 处理上传请求
  const processUpload = () => {
    const formData = new FormData();
    formData.append('file', file);

    return new Promise((resolve, reject) => {
      props.api(formData)
        .then(async res => {
          const { width, height } = await loadImageDimensions(file);
          emit('success', {
            form: props.form,
            path: props.path,
            imgWidth: width, // 图片的原始宽度
            imgHeight: height,// 图片的原始高度
            imgUrl: props.specialPicture ? res.data : res.data,
          });
          resolve(res);
        })
        .catch(reject)
        .finally(onUploadComplete);
    });
  }

  // 根据配置决定是否校验图片尺寸
  if (props.maxImgPxShow) {
    validateImageSize(file).then(res => {
      if (res) {
        processUpload();
      } else {
        onUploadComplete();
      }
    });
  } else {
    processUpload();
  }
}
</script>

<style lang="scss" scoped>
// 上传区域样式
.upload {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  font-size: 14px;
  color: #999;
  outline: 1px dashed #d9d9d9;
  background: rgb(250, 250, 250);

  // 预览按钮
  &-eye {
    font-size: 14px;
    position: absolute;
    padding: 5px;
    top: 0;
    right: 0;
    color: red;
  }

  // 删除按钮
  &-delete {
    font-size: 14px;
    position: absolute;
    padding: 0;
    bottom: 0;
    right: 0;
    color: red;
  }

  // 图片展示背景
  &.show {
    background: rgb(250, 250, 250) url(../assets/images/bg.png) center repeat;
  }

  &:hover {
    border-color: #1890ff;
  }

  // 加载状态遮罩
  &-loading {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
  }
}
</style>
