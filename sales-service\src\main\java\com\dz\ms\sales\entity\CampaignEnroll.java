package com.dz.ms.sales.entity;


import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/19
 */
@Getter
@Setter
@NoArgsConstructor
@Table(value = "活动报名")
@TableName(value = "campaign_enroll")
public class CampaignEnroll implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = true, comment = "活动标识", isIndex = true)
    private String campaignCode;

//    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, comment = "来源")
//    private Integer source;

    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = true, comment = "一级渠道")
    private String channelOne;
    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = true, comment = "二级渠道")
    private String channelTwo;

    @Columns(type = ColumnType.BIGINT, length = 11, isNull = false, comment = "用户ID", isIndex = true)
    private Long uid;

    @Columns(type = ColumnType.VARCHAR, length = 50, isNull = true, comment = "姓名")
    private String name;

    @Columns(type = ColumnType.VARCHAR, length = 50, isNull = false, comment = "openid")
    private String openid;

    @Columns(type = ColumnType.VARCHAR, length = 50, isNull = false, comment = "unionid", isIndex = true)
    private String unionid;

    @Columns(type = ColumnType.VARCHAR,length = 32,isNull = true,comment = "会员名")
    private String username;

    @Columns(type = ColumnType.VARCHAR, length = 36, isNull = false, comment = "手机号码（用户信息获取）")
    private String mobile;

    @Columns(type = ColumnType.VARCHAR, length = 36, isNull = false, comment = "会员卡号")
    private String cardNo;

    @Columns(type = ColumnType.VARCHAR, length = 36, isNull = false, comment = "手机号码（报名时填写的手机号）")
    private String enrollPhone;

    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "皮肤类型 0未知 1皮肤敏感（泛红、刺痛） 2干燥缺水 3、痘痘/粉刺问题 4、油脂分泌过多 5、黑头问题")
    private Integer skinType;

    @Columns(type = ColumnType.VARCHAR, length = 50, isNull = false, comment = "省")
    private String province;

    @Columns(type = ColumnType.VARCHAR, length = 50, isNull = false, comment = "市")
    private String city;

    @Columns(type = ColumnType.VARCHAR, length = 50, isNull = false, comment = "区")
    private String district;

    @Columns(type = ColumnType.VARCHAR, length = 50, isNull = false, comment = "详细地址")
    private String address;

    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "状态 0待审核 1审核通过 2审核拒绝")
    private Integer status;

    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "推送标识 0未推送 1已推送")
    private Integer pushFlag;

    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "是否删除 0未删除 1已删除")
    @TableLogic
    private Integer isDeleted;
    @Columns(type = ColumnType.BIGINT, length = 11, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;


}
