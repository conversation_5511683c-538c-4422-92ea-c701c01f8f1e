package com.dz.ms.product.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.dto.DownloadCenterDTO;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.IdNumberDTO;
import com.dz.common.core.dto.product.CpStaticDTO;
import com.dz.common.core.dto.product.CpStaticParamDTO;
import com.dz.common.core.dto.product.OdsItemDTO;
import com.dz.common.core.dto.product.ProductCouponDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.basic.ReportDownloadFeignClient;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.product.config.Globals;
import com.dz.ms.product.dto.CrmProductListDTO;
import com.dz.ms.product.dto.ProductDTO;
import com.dz.ms.product.dto.req.CrmProductListParamDTO;
import com.dz.ms.product.dto.req.ProductChiefInfoParamDTO;
import com.dz.ms.product.dto.res.ProductChiefInfoDTO;
import com.dz.ms.product.service.ProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Objects;

@Api(tags = "商品信息")
@RestController
@Slf4j
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class ProductController {

    @Resource
    private ProductService productService;
    @Resource
    private ReportDownloadFeignClient reportDownloadFeignClient;

    /**
     * 分页查询商品信息
     *
     * @param param
     * @return result<PageInfo < ProductDTO>>
     */
    @ApiOperation("分页查询商品信息")
    @PostMapping(value = "/crm/product/list")
    public Result<PageInfo<CrmProductListDTO>> getProductList(@RequestBody CrmProductListParamDTO param) {
        Result<PageInfo<CrmProductListDTO>> result = new Result<>();
        PageInfo<CrmProductListDTO> page = productService.getProductList(param);
        result.setData(page);
        return result;
    }

    /**
     * 分页查询商品信息
     *
     * @param param
     * @return result<PageInfo < ProductDTO>>
     */
    @ApiOperation("分页查询商品信息")
    @GetMapping(value = "/crm/product/chief_info_list")
    public Result<PageInfo<ProductChiefInfoDTO>> getProductChiefInfoList(@ModelAttribute ProductChiefInfoParamDTO param) {
        Result<PageInfo<ProductChiefInfoDTO>> result = new Result<>();
        PageInfo<ProductChiefInfoDTO> page = productService.getProductChiefInfoList(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询商品信息
     *
     * @param id
     * @return result<ProductDTO>
     */
    @ApiOperation("根据ID查询商品信息")
    @GetMapping(value = "/crm/product/info")
    public Result<ProductDTO> getProductById(@RequestParam("id") Long id) {
        Result<ProductDTO> result = new Result<>();
        ProductDTO product = productService.getProductById(id);
        result.setData(product);
        return result;
    }

    /**
     * 新增商品信息
     *
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增商品信息", type = LogType.OPERATELOG)
    @ApiOperation("新增商品信息")
    @PostMapping(value = "/crm/product/add")
    public Result<Long> addProduct(@RequestBody ProductDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param, true);
        Long id = productService.saveProduct(param);
        result.setData(id);
        return result;
    }

    /**
     * 更新商品信息
     *
     * @param param
     * @return result<Object>
     */
    @SysLog(value = "更新商品信息", type = LogType.OPERATELOG)
    @ApiOperation("更新商品信息")
    @PostMapping(value = "/crm/product/update")
    public Result<Long> updateProduct(@RequestBody ProductDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param, false);
        productService.saveProduct(param);
        result.setData(param.getId());
        return result;
    }

    /**
     * 验证保存参数
     *
     * @param param
     */
    private void validationSaveParam(ProductDTO param, boolean isAdd) {
        if (isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if (!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
        if (StringUtils.isBlank(param.getProductName())) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "商品名称不能为空");
        }
        if (Objects.isNull(param.getPdType())) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "商品类型不能为空");
        }
        if (StringUtils.isBlank(param.getShelfImgUrl())) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "商品橱窗图不能为空");
        }
        if (Objects.isNull(param.getVenderId())) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "外部数据ID不能为空");
        }
        if (Objects.isNull(param.getCostPoint())) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "商品积分价值不能为空");
        }
    }

    /**
     * 根据ID删除商品信息
     *
     * @param param
     * @return result<Boolean>
     */
    @SysLog(value = "根据ID删除商品信息", type = LogType.OPERATELOG)
    @ApiOperation("根据ID删除商品信息")
    @PostMapping(value = "/crm/product/delete")
    public Result<Boolean> deleteProductById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        productService.deleteProductById(param);
        result.setData(true);
        return result;
    }

    /**
     * 根据ID修改启停状态
     *
     * @param param ID NUMBER 通用DTO
     * @return result<Boolean>
     */
    @SysLog(value = "根据ID修改启停状态", type = LogType.OPERATELOG)
    @ApiOperation("根据ID修改启停状态")
    @PostMapping(value = "/crm/product/update_state")
    public Result<Integer> updateStateById(@RequestBody IdNumberDTO param) {
        Result<Integer> result = new Result<>();
        result.setData(productService.updateStateById(param));
        return result;
    }

    /**
     * 导出商品列表
     *
     * @return
     */
    @PostMapping(value = "/product/export_product_list")
    public Result<Void> exportProductList(@RequestBody DownloadAddParamDTO exportParam) {
        CurrentUserDTO user = SecurityContext.getUser();
        Globals.downloadThreadPool.execute(() -> {
            try {
                SecurityContext.setUser(user);
                productService.exportProductList(exportParam.getJsonParam(), exportParam.getReportCode(), exportParam.getFileName(), exportParam.getFileExt(), exportParam.getDownloadCenterId());
            } catch (Exception e) {
                log.error("导出错误", e);
                DownloadCenterDTO downloadCenterDTO = new DownloadCenterDTO();
                downloadCenterDTO.setId(exportParam.getDownloadCenterId());
                downloadCenterDTO.setErrorDesc(e.getMessage());
                downloadCenterDTO.setState(2);
                reportDownloadFeignClient.updateDownloadCenter(downloadCenterDTO);
            }
        });
        return new Result<>();
    }

    /**
     * 查询所有电子券商品
     */
    @GetMapping(value = "/product/by/type")
    public Result<List<ProductCouponDTO>> getProductListByPdType() {
        Result<List<ProductCouponDTO>> result = new Result<>();
        result.setData(productService.getProductListByPdType());
        return result;
    }

    @GetMapping(value = {"/crm/product/sftp_upload", "/product/sftp_upload"})
    public void uploadMujiGoodsAndCouponsAndOrdersExcel(@RequestParam(value = "afterDate", required = false) String afterDate) throws ParseException {
        productService.uploadMujiGoodsAndCouponsAndOrdersExcel(afterDate);
    }

    /**
     * CP兑换统计
     */
    @GetMapping(value = "/crm/product/cp_static")
    @ApiOperation("CP兑换统计")
    public Result<PageInfo<CpStaticDTO>> cpStatic(@ModelAttribute CpStaticParamDTO param) {
        if (StringUtils.isEmpty(param.getCpCode())
                && Objects.isNull(param.getSellDateStart())
                && Objects.isNull(param.getSellDateEnd())) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "参数不能全为空");
        }

        Result<PageInfo<CpStaticDTO>> result = new Result<>();
        result.setData(productService.cpStatic(param));
        return result;
    }

    /**
     * 导出CP兑换统计
     */
    @PostMapping(value = "/product/export_cp_static")
    public Result<List<CpStaticDTO>> exportCpStatic(@RequestBody DownloadAddParamDTO param) {
        CurrentUserDTO user = SecurityContext.getUser();
        Globals.downloadThreadPool.execute(() -> {
            try {
                SecurityContext.setUser(user);
                productService.exportCpStatic(param);
            } catch (Exception e) {
                log.error("导出错误", e);
                DownloadCenterDTO downloadCenterDTO = new DownloadCenterDTO();
                downloadCenterDTO.setId(param.getDownloadCenterId());
                downloadCenterDTO.setErrorDesc(e.getMessage());
                downloadCenterDTO.setState(2);
                reportDownloadFeignClient.updateDownloadCenter(downloadCenterDTO);
            }
        });
        return new Result<>();
    }

    @ApiOperation("SFTP获取商品数据")
    @PostMapping(value = {"/product/getSftpFile", "/app/product/getSftpFile"})
    public Result<String> getSftpFile() throws IOException {
        Result<String> response = new Result<>();
        productService.getSftpFile();
        return response;
    }

    /**
     * 查询商品详情
     */
    @GetMapping(value = "/product/by/item/str")
    public Result<List<OdsItemDTO>> selectBySftpProductItemId(@RequestParam(value = "itemId") String itemId) {
        Result<List<OdsItemDTO>> result =new Result<>();
        List<OdsItemDTO> odsItemDTOS = productService.selectBySftpProductItemId(itemId);
        result.setData(odsItemDTOS);
        return result;
    }
}
