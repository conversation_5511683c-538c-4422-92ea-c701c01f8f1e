package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.io.Serializable;
import java.util.Date;

/**
 * 企业微信配置
 * @author: Handy
 * @date:   2022/07/20 21:18
 */
@Getter
@Setter
@NoArgsConstructor
@Table("企业微信配置")
@TableName(value = "qywx_config")
public class QywxConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "企业微信配置ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR,length = 36,isNull = true,comment = "企业微信ID")
    private String corpId;
    @Columns(type = ColumnType.VARCHAR,length = 36,isNull = true,comment = "企业微信应用ID")
    private String agentId;
    @Columns(type = ColumnType.VARCHAR,length = 36,isNull = true,comment = "企业微信应用关联小程序APPID")
    private String appid;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "企业微信应用密钥")
    private String corpSecret;
    @Columns(type = ColumnType.VARCHAR,length = 255,isNull = true,comment = "接收事件服务器Token")
    private String token;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "接收事件服务器EncodingAESKey")
    private String encodingAesKey;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = false,comment = "1-企微应用 2-客户联系")
    private Integer type;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = false,comment = "状态 0关闭 1启用",defaultValue = "1")
    private Integer state;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    public QywxConfig(Long id, String corpId, String agentId, String appid, String corpSecret, String token, String encodingAesKey, Integer type, Integer state, Long tenantId) {
        this.id = id;
        this.corpId = corpId;
        this.agentId = agentId;
        this.appid = appid;
        this.corpSecret = corpSecret;
        this.token = token;
        this.encodingAesKey = encodingAesKey;
        this.type = type;
        this.state = state;
        this.tenantId = tenantId;
    }

}
