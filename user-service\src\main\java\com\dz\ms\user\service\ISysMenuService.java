package com.dz.ms.user.service;


import com.dz.ms.user.dto.SysMenuDTO;
import com.dz.ms.user.vo.MenuInfoVo;
import com.dz.ms.user.vo.RouterVo;

import java.util.List;

/**
 * 菜单 业务层
 *
 * <AUTHOR>
 */
public interface ISysMenuService {

    /**
     * 根据用户ID查询菜单树信息
     *
     * @return 菜单列表
     */
    List<SysMenuDTO> selectMenuTreeByUserId();

    /**
     * 构建前端路由所需要的菜单
     *
     * @param menus 菜单列表
     * @return 路由列表
     */
    List<RouterVo> buildMenus(List<SysMenuDTO> menus);
    
    /**
     * 根据菜单名称获取菜单及祖级信息
     * @param menuName 名称
     * @param menuType 类型
     * @return List<MenuInfoVo>
     */
    List<MenuInfoVo> getInfoByMenuName(String menuName, String menuType);

    /**
     * 获取父级PATH
     * @param menuId menuId
     * @param parentPathList parentPath
     * @return List<String>
     */
    List<String> selectByParentId(Long menuId,List<String> parentPathList);
}
