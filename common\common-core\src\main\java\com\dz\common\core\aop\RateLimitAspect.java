package com.dz.common.core.aop;


import com.alibaba.nacos.shaded.com.google.common.util.concurrent.RateLimiter;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/2/27
 */
@Aspect
@Component
@Slf4j
public class RateLimitAspect {

    private static final RateLimiter rateLimiter = RateLimiter.create(200.0); // 每秒5个请求

    @Before("execution(@com.dz.common.core.annotation.RateLimit * com.dz.ms.*.controller.*.*(..))")
    public void rateLimit() {
        if (!rateLimiter.tryAcquire()) {
//            throw new RuntimeException("Too many requests - try again later");
            throw new BusinessException(ErrorCode.INTERNAL_ERROR, "请求太多了，请稍后重试");

        }
    }
    public static void someApiMethod() {
        if (rateLimiter.tryAcquire()) {
            // 处理请求
            System.out.println("Request processed");
        } else {
            // 请求被限流
            System.out.println("Too many requests - try again later");
            throw new BusinessException(ErrorCode.INTERNAL_ERROR, "请求太多了，请稍后重试");
        }
    }


}
