package com.dz.ms.user.controller;

import com.alibaba.excel.EasyExcel;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.user.*;
import com.dz.common.core.utils.ExcelUtils;
import com.dz.ms.user.service.StoreService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

@Api(tags="门店")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class StoreController  {

    @Resource
    private StoreService storeService;

    /**
     * 分页查询门店
     * @param param
     * @return result<PageInfo<StoreDTO>>
     */
    @ApiOperation("分页查询门店")
    @PostMapping(value = "/crm/store/list")
    public Result<PageInfo<StoreDTO>> getStoreList(@RequestBody StoreDTO param) {
        Result<PageInfo<StoreDTO>> result = new Result<>();
        PageInfo<StoreDTO> page = storeService.getStoreList(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询门店
     *
     * @param id
     * @return result<StoreDTO>
     */
    @ApiOperation("根据ID查询门店")
    @GetMapping(value = "/crm/store/info")
    public Result<StoreDTO> getStoreById(@RequestParam("id") Long id) throws ParseException {
        Result<StoreDTO> result = new Result<>();
        StoreDTO store = storeService.getStoreById(id);
        result.setData(store);
        return result;
    }

    /**
     * 保存门店
     *
     * @param param
     * @return result<Long>
     */
    @ApiOperation("编辑门店")
    @PostMapping(value = "/crm/store/update")
    public Result<Boolean> updateStore(@RequestBody StoreDTO param) throws ParseException {
        Result<Boolean> result = new Result<>();
        storeService.updateStore(param);
        result.setData(true);
        return result;
    }
	
	/**
     * 根据ID删除门店
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除门店")
	@PostMapping(value = "/store/delete")
    public Result<Boolean> deleteStoreById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        storeService.deleteStoreById(param);
        result.setData(true);
        return result;
    }

    @ApiOperation("查询门店关联服务")
    @GetMapping(value = "/crm/storeServe/list")
    public Result<List<ServeDTO>> getStoreServeList(@RequestParam Long storeId) {
        Result<List<ServeDTO>> result = new Result<>();
        List<ServeDTO> list = storeService.getStoreServeList(storeId);
        result.setData(list);
        return result;
    }

    @ApiOperation("编辑门店关联服务")
    @PostMapping(value = "/crm/store/updateServe")
    public Result<Boolean> updateStoreServe(@RequestBody List<StoreServeDTO> param) {
        Result<Boolean> result = new Result<>();
        storeService.updateStoreServe(param);
        result.setData(true);
        return result;
    }

    @PostMapping("/crm/storeServe/import")
    @ApiOperation("导入门店服务")
    public Result<Boolean> employeeImport(@RequestParam(value = "file") MultipartFile file) {
        Result<Boolean> result = new Result<>();
        List<StoreServeImportExcelDTO> excelDTOList = ExcelUtils.excelImport(file, StoreServeImportExcelDTO.class);
        storeService.importStoreServe(excelDTOList);
        result.setData(true);
        return result;
    }

    @ApiOperation("下载人群包模板")
    @GetMapping(value = "/crm/store/template")
    public void outputList(HttpServletResponse response) throws IOException {
        List<StoreServeImportExcelDTO> excelDTOS = new ArrayList<>();
        String fileName = URLEncoder.encode("门店类型导入模板", StandardCharsets.UTF_8);
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        response.setHeader("Access-Control-Expose-Headers", "Content-disposition");
        EasyExcel.write(response.getOutputStream(), StoreServeImportExcelDTO.class)
                .sheet("门店类型导入模板")
                .doWrite(excelDTOS);
    }

    @ApiOperation("门店省份下拉框")
    @GetMapping(value = "/crm/store/province")
    public Result<List<String>> getStoreProvince() {
        Result<List<String>> result = new Result<>();
        List<String> list = storeService.getStoreProvince();
        result.setData(list);
        return result;
    }

    @ApiOperation("门店城市下拉框")
    @GetMapping(value = "/crm/store/city")
    public Result<List<String>> getStoreCity() {
        Result<List<String>> result = new Result<>();
        List<String> list = storeService.getStoreCity();
        result.setData(list);
        return result;
    }


//---------------------------------------------------------app-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

    @ApiOperation("小程序分页查询门店")
    @PostMapping(value = "/app/store/list")
    public Result<PageInfo<StoreDTO>> getAppStoreList(@RequestBody StoreDTO param) {
        Result<PageInfo<StoreDTO>> result = new Result<>();
        PageInfo<StoreDTO> page = storeService.getAppStoreList(param);
        result.setData(page);
        return result;
    }

    @ApiOperation("小程序查询附近门店城市new")
    @GetMapping(value = "/app/store/storeCity")
    public Result<StoreCityDTO> cityStore(){
        Result<StoreCityDTO> result = new Result<>();
        result.setData(storeService.cityListNew());
        return result;
    }

    @ApiOperation("小程序查询附近门店城市")
    @GetMapping(value = "/app/store/city")
    public Result<List<String>> city(){
        Result<List<String>> result = new Result<>();
        result.setData(storeService.cityList());
        return result;
    }

    @ApiOperation("根据经纬度查询所在城市")
    @GetMapping(value = "/app/store/cityByLonLat")
    public Result<String> cityByLonLat(@RequestParam("longitude") String longitude,@RequestParam("latitude") String latitude){
        Result<String> result = new Result<>();
        result.setData(storeService.cityByLonLat(longitude,latitude));
        return result;
    }

    @ApiOperation("SFTP获取门店数据")
    @PostMapping(value = {"/store/getSftpFile", "/app/store/getSftpFile"})
    public Result<String> getSftpFile() throws IOException {
        Result<String> response = new Result<>();
        storeService.getSftpFile();
        return response;
    }

    /**
     * 查询所有门店的经纬度
     * @return result<PageInfo<StoreDTO>>
     */
    @PostMapping(value = "/store/longitude/latitude/list")
    public Result<List<StoreDTO>> getLongitudeLatitude() {
        Result<List<StoreDTO>> result = new Result<>();
        List<StoreDTO> page = storeService.getLongitudeLatitude();
        result.setData(page);
        return result;
    }

    /**
     * 根据storeSn查询门店
     *
     * @return result<String>
     */
    @ApiOperation("根据ID查询门店")
    @GetMapping(value = "/store/info/store/sn")
    public Result<String> getStoreByStoreSn(@RequestParam("storeSn") String storeSn){
        Result<String> result = new Result<>();
        String store = storeService.getStoreByStoreSn(storeSn);
        result.setData(store);
        return result;
    }
}
