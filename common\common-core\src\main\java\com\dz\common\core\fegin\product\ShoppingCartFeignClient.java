package com.dz.common.core.fegin.product;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.product.CartResultDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = ServiceConstant.PRODUCT_SERVICE_NAME, contextId = "ShoppingCartFeignClient")
public interface ShoppingCartFeignClient {

    /**
     * 获取订单预览商品列表
     */
    @GetMapping(value = "/cart/order_list")
    @ApiOperation(value = "获取订单预览商品列表", notes = "获取订单预览商品列表")
    Result<CartResultDTO> getPreviewCartOrder();

    /**
     * 单商品购买订单预览
     */
    @GetMapping(value = "/cart/order")
    @ApiOperation(value = "单商品购买订单预览", notes = "单商品购买订单预览")
    Result<CartResultDTO> getCartOrderByShelfProductId(@RequestParam("shelfProductId") Long shelfProductId,
                                                       @RequestParam("num") Integer num);

    /**
     * 清空已下单购物车商品
     *
     * @return
     */
    @DeleteMapping(value = "/cart/order/clean")
    @ApiOperation(value = "清空已下单购物车商品", notes = "清空已下单购物车商品")
    Result<Boolean> cleanUserOrderCart();
}
