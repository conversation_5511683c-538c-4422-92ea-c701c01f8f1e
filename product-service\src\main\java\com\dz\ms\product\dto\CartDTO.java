package com.dz.ms.product.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 购物车DTO
 *
 * @author: LiinNs
 * @date: 2024/12/09 18:21
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "购物车")
public class CartDTO extends BaseDTO {

    @ApiModelProperty(value = "ID")
    private Long id;
    @ApiModelProperty(value = "客户ID")
    private Long userId;
    @ApiModelProperty(value = "货架商品ID")
    private Long shelfProductId;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "商品ID")
    private Long productId;
    @ApiModelProperty(value = "兑换积分，加购时积分")
    private Integer costPoint;
    @ApiModelProperty(value = "兑换金额，加购时单价")
    private BigDecimal costPrice;
    @ApiModelProperty(value = "商品数量")
    private Integer number;
    @ApiModelProperty(value = "选中标示 0未选中 1选中")
    private Integer checked;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "修改时间")
    private Date modified;

}
