package com.dz.ms.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.dto.ExchangeStaticParamDTO;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.IdNumberDTO;
import com.dz.ms.product.dto.ShelfDTO;
import com.dz.ms.product.dto.req.ShelfParamDTO;
import com.dz.ms.product.dto.req.ShelfSaveParamDTO;
import com.dz.ms.product.entity.Shelf;

import java.util.List;
import java.util.Map;

/**
 * 商品货架接口
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:27
 */
public interface ShelfService extends IService<Shelf> {

    /**
     * 分页查询商品货架
     *
     * @param param 入参
     * @return PageInfo<ShelfDTO>
     */
    PageInfo<ShelfDTO> getShelfList(ShelfParamDTO param);

    /**
     * 根据ID查询商品货架
     * @param id id
     * @param isThrow 1:抛异常
     * @return ShelfDTO
     */
    ShelfDTO getShelfById(Long id,Integer isThrow);

    /**
     * 获取所有货架信息
     * 本方法通过查询数据库中的货架表，获取所有货架的相关信息，并将其转换为DTO列表返回
     * 此方法有缓存,对货架进行修改时切记清除对应缓存
     * @return 货架DTO列表，包含所有货架的简化信息
     */
    List<ShelfDTO> getAllShelf();

    /**
     * 根据人群包id列表查询优先级排序后的货架列表
     * @param num 限制返回书架的数量
     * @return ShelfDTO列表
     */
    List<ShelfDTO> getPrioritySortedShelf(Integer num);

    /**
     * 根据人群包id列表查询优先级排序后的货架
     * @return 返回根据优先级排序的货架
     */
    ShelfDTO getPrioritySortedShelfOne();

    /**
     * 查询所有商品货架优先级数据
     * @return List<Integer>
     */
    List<ShelfDTO> getAllPriority();

    /**
     * 保存商品货架
     *
     * @param param 入参
     * @return Long
     */
    Long saveShelf(ShelfSaveParamDTO param, boolean isAdd);

    /**
     * 根据ID删除商品货架
     *
     * @param param 入参
     */
    void deleteShelfById(IdCodeDTO param);

    /**
     * 根据ID修改启停状态
     * @param param ID NUMBER 通用DTO
     */
    void updateStateById(IdNumberDTO param);

    /**
     * 不分页查询货架列表
     *
     * @param param 查询条件
     * @param num   num 1:查询少量字段, 2:查询所有字段
     * @return List<ShelfDTO>
     */
    List<ShelfDTO> getNoPageShelf(ShelfParamDTO param, Integer num);

    /**
     * 不分页查询货架列表
     *
     * @param param 查询条件
     * @param num   num 1:查询少量字段, 2:查询所有字段
     * @return Map<Long, ShelfDTO>
     */
    Map<Long, ShelfDTO> getNoPageShelfMap(ShelfParamDTO param, Integer num);

    /**
     * 导出货架列表
     *
     * @param exportParam
     */
    void exportList(DownloadAddParamDTO exportParam);

    /**
     * 根据人群包id,更新货架,货架营销活动关联人群包id为null
     *
     * @param groupId 人群包id
     */
    void updGroupIdIntoNull(Long groupId);

    /**
     * 更新货架订单统计
     *
     * @param orderStaticParam
     * @return
     */
    int updateStatic(ExchangeStaticParamDTO orderStaticParam);
}
