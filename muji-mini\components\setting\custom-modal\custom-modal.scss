@import "assets/scss/common";
@import "assets/scss/config";

@keyframes show {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.customModal {
  position: relative;
  background: transparent;
  display: flex;
  flex-direction: column;
  align-items: center;

  &.center {
    padding: 110rpx 0;
  }



  &-content {
    position: relative;
    overflow: hidden;
    border-radius: var(--radius);

    &.center {
      animation: show 0.6s forwards;
    }

    &.bottom {
      border-radius: var(--radius) var(--radius) 0 0;
    }
  }

  &-info {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  &-list {
    flex: 1;
    overflow: hidden;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  &-scroll {}

  &-close {
    &.center {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &.bottom {
      position: absolute;
      top: 0;
      right: 30rpx;
      width: 80rpx;
    }
  }

  &-closeBox {
    height: 80rpx;
    width: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-title {
    width: 100%;
    box-sizing: border-box;
    text-align: center;
    padding: 60rpx 60rpx 20rpx 60rpx;
    font-family: NotoSansHans;
    font-weight: 500;
    font-size: 36rpx;
    color: #3C3C43;
    line-height: 54rpx;
    letter-spacing: 1rpx;
  }

}
