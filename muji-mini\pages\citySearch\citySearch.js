// pages/citySearch/citySearch.js
import {
  getCurrentCity,
  getIndexBarCityList
} from '../../api/index.js'
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    latitude: undefined, // 用户经纬度
    longitude: undefined, // 用户经纬度
    userLocation: undefined,
    searchCity: '',
    cityList: {},
    allCities: [],
    currentSearchCities: [],
    searchType: 'indexbar',
    scrollTop: 0,
    currentScrollTop: 0,
    isAutoScroll: false,
    navHeight: app.globalData.navBarHeight + app.globalData.statusBarHeight,
    searchBarHeight: 0,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    const {
      userLatitude
    } = app.globalData;
    if (userLatitude && userLatitude !== 0) {
      this.setData({
        latitude: app.globalData.userLatitude,
        longitude: app.globalData.userLongitude,
      })
      this.getCurrentCityData();
    }
    this.getCityList();
    const that = this;
    wx.createSelectorQuery().select('#searchBar').boundingClientRect(function (rect) {
      console.log('rect', rect)
      that.setData({
        searchBarHeight: rect.height
      })
    }).exec();

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow(options) {
    const {
      userLatitude
    } = app.globalData;
    if (userLatitude && userLatitude !== 0) {
      this.setData({
        latitude: app.globalData.userLatitude,
        longitude: app.globalData.userLongitude,
      })
      this.getCurrentCityData();
    }
    this.getCityList();
    if (options.city) {
      this.setData({
        currentCity: options.city
      })
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },
  async getCityList() {
    const res = await getIndexBarCityList();

    function removeEmptyArrays(cityList) {
      for (let key in cityList) {
        if (Array.isArray(cityList[key]) && cityList[key].length === 0) {
          delete cityList[key]; // 删除空数组的属性
        }
      }
      return cityList;
    }
    const cityList = removeEmptyArrays(res.data.cityMap);
    let allCities = []
    for (let key in cityList) {
      if (cityList.hasOwnProperty(key)) {
        allCities = allCities.concat(cityList[key]);
      }
    }
    this.setData({
      cityList,
      allCities: allCities
    });
  },
  handleChangeCity() {
    this.setData({
      currentCity: '',
    })
    const {
      userLocation
    } = this.data;
    this.handleGoto(userLocation);
  },
  changeCity(v) {
    this.setData({
      currentCity: v.detail.city,
    })
    this.handleGoto(v.detail.city);
  },
  async getCurrentCityData() {
    const {
      latitude,
      longitude,
    } = this.data;
    const res = await getCurrentCity({
      latitude,
      longitude,
    });
    this.setData({
      userLocation: res.data,
    })
  },
  async openLocation(e) {
    // 手动打开授权
    app.openAuthLocation().then((res) => {
      this.setData({
        latitude: res.latitude,
        longitude: res.longitude,
      });
      this.getCurrentCityData();
    }).catch((error) => {
      console.log('error', error);
    });
  },
  handleChange(v) {
    if (v.detail !== '') {
      this.setData({
        currentSearchCities: [],
        searchType: 'indexbar',
      })
    }
  },
  handleSearchCity(v) {
    if (v.detail !== '') {
      const {
        allCities
      } = this.data;
      const currentSearchCities = allCities.filter(item => item.includes(v.detail));
      this.setData({
        currentSearchCities,
        searchType: 'all',
      })
    } else {
      this.setData({
        currentSearchCities: [],
        searchType: 'indexbar',
      })
    }
  },
  changeScroll(v) {
    const {
      currentScrollTop
    } = this.data;
    this.setData({
      scrollTop: v.detail.top + currentScrollTop - 180,
    })
  },
  onScroll(e) {
    const scrollTop = e.detail.scrollTop;
    this.setData({
      currentScrollTop: scrollTop,
    });
  },
  handleGoto(v) {
    let pages = getCurrentPages();
    if (pages.length >= 2) {
      let prevPage = pages[pages.length - 2];
      prevPage.setData({
        filters: {
          city: v
        }
      });
      wx.$mp.navigateBack()
    } else {
      wx.$mp.navigateTo({
        url: `/pages/nearbyOutlets/nearbyOutlets?city=${v}`,
      })
    }
  }
})
