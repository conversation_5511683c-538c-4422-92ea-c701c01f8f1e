import { apiIndex } from '../../api/index'

const app = getApp()
Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    item: {
      type: Object,
      value: {}
    },
    // 游客模式
    visitor: {
      type: <PERSON>olean,
    },
    scrollTop: {
      type: Number,
      value: 0,
      observer (val) {
        if (val > 300) {
          this.setData({ bg: 'bg2' })
        } else {
          this.setData({ bg: 'bg1' })
        }
      }
    }
  },
  data: {
    bg: 'bg1',
    show: false,
    resData: {}
  },
  pageLifetimes: {
    show () {
      this.init()
    }
  },
  attached () {
    this.init()
  },
  methods: {
    async init () {
      const res = await apiIndex.getHomeMessageReminder()
      const resData = res.data
      if (resData.homeTitle) {
        resData.homeTitle = `${resData.homeTitle} >>`
      }
      const isRead = resData.isRead === 1
      const show = !isRead && resData.homeTitle
      this.setData({ resData, show })
    },
    jumpUrl: app.debounce(async function () {
      wx.$mp.navigateTo({ url: this.data.resData.jumpUrl })
      apiIndex.getHomeMessageReminderRead({ id: this.data.resData.id })
      this.setData({ show: false })
    }),
    closeNotice () {
      this.setData({ show: false })
    }
  }
})
