package com.dz.common.core.dto.user;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**

 */
@Data
public class StoreCityDTO {
    private Map<Character, List<String>> cityMap;

    public StoreCityDTO() {
        cityMap = new HashMap<>();
        for (char letter = 'A'; letter <= 'Z'; letter++) {
            cityMap.put(letter, new ArrayList<>());
        }
    }

    public void addCity(char letter, String city) {
        if (cityMap.containsKey(letter)) {
            cityMap.get(letter).add(city);
        } else {
            throw new IllegalArgumentException("Invalid letter: " + letter);
        }
    }

    public List<String> getCityList(char letter) {
        return cityMap.getOrDefault(letter, new ArrayList<>());
    }

    public Map<Character, List<String>> getCityMap() {
        return cityMap;
    }
}
