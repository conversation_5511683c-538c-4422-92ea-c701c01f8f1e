/* pages/editPhone/editPhone.wxss */
.page-content {
  display: flex;
  overflow-y: auto;
  overflow-x: hidden;
  flex-direction: column;
  width: 100%;
  box-sizing: border-box;
  padding: 40rpx;

  .title-box {
    margin-bottom: 100rpx;
    padding-right: 34rpx;

    .title-txt {
      font-family: NotoSansHans;
      font-weight: 700;
      font-size: 36rpx;
      color: #000000;
      line-height: 54rpx;
      text-align: left;
      font-style: normal;
      margin-bottom: 40rpx;
    }

    .title-p {
      font-family: SourceHanSansCN, PingFangSC,
        PingFang SC;
      // font-weight: bold;
      font-size: 24rpx;
      color: #888888;
      line-height: 36rpx;
      letter-spacing: 1px;
      text-align: left;
      font-style: normal;
      margin-bottom: 20rpx;
      display: flex;

    }
  }

  .edit-form {
    .form-item {
      border-bottom: 1rpx solid #dbd8d9;
      // height: 114rpx;
      margin-bottom: 60rpx;
      padding-bottom: 16rpx;

      .item-label {
        color: var(--text-gray-color);
        font-family: PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        line-height: 33rpx;
        text-align: left;
        font-style: normal;
      }

      .item-content {
        margin-top: 20rpx;
        color: var(--text-black-color);
        font-size: 28rpx;
        display: flex;
        justify-content: space-between;

        .item-button {
          position: relative;
          box-sizing: border-box;
          width: 30%;

          .btn-txt {
            height: 36rpx;
            font-family: PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            color: #888888;
            line-height: 36rpx;
            text-align: right;
            font-style: normal;
            position: relative;
            box-sizing: border-box;

            &::after {
              content: '';
              position: absolute;
              background-color: #888;
              width: 120rpx;
              height: 1rpx;
              bottom: 0;
              right: 0;
            }
          }

          .red-font {
            color: #7F0019;

            &::after {
              background-color: #7F0019;
            }
          }
        }
      }
    }
  }


  .bottom-box {
    position: fixed;
    bottom: 0;
    padding-bottom: 60rpx;
    margin-top: var(--page-margin);
    /* 避免被系统底部遮挡 */
    background-color: #fff;
    display: flex;
    justify-content: center;
  }
}
