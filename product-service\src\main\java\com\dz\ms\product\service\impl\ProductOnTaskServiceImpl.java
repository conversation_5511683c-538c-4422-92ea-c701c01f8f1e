package com.dz.ms.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.constants.NumConstants;
import com.dz.common.core.constants.ProductConstants;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.ms.product.dto.CrmProductOnTaskListDTO;
import com.dz.ms.product.dto.ProductOnTaskDTO;
import com.dz.ms.product.dto.ShelfDTO;
import com.dz.ms.product.dto.req.CrmProductOnTaskLisParamDTO;
import com.dz.ms.product.dto.req.ProductOnTaskSaveParamDTO;
import com.dz.ms.product.dto.req.ShelfParamDTO;
import com.dz.ms.product.entity.ProductOnTask;
import com.dz.ms.product.entity.ShelfProduct;
import com.dz.ms.product.entity.TaskLog;
import com.dz.ms.product.mapper.ProductOnTaskMapper;
import com.dz.ms.product.mapper.ShelfProductMapper;
import com.dz.ms.product.mapper.TaskLogMapper;
import com.dz.ms.product.service.ProductOnTaskService;
import com.dz.ms.product.service.ShelfService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 货架商品库存任务
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:25
 */
@Service
@Slf4j
public class ProductOnTaskServiceImpl extends ServiceImpl<ProductOnTaskMapper, ProductOnTask> implements ProductOnTaskService {

    private final SimpleDateFormat yyyyMMddHHmm = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    @Resource
    private ProductOnTaskMapper productOnTaskMapper;
    @Resource
    private ShelfProductMapper shelfProductMapper;
    @Resource
    private ShelfService shelfService;
    @Resource
    private TaskLogMapper taskLogMapper;
    @Resource
    private ApplicationContext applicationContext;


    private static void setTimeToZero(Calendar calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
    }

    /**
     * 根据ID查询货架商品库存任务
     *
     * @param id
     * @return ProductOnTaskDTO
     */
    @Override
    public ProductOnTaskDTO getProductOnTaskById(Long id) {
        ProductOnTask productOnTask = productOnTaskMapper.selectById(id);
        return BeanCopierUtils.convertObject(productOnTask, ProductOnTaskDTO.class);
    }

    private static long getDaysBetween(Calendar cal1, Calendar cal2) {
        setTimeToZero(cal1);
        setTimeToZero(cal2);
        long milliseconds1 = cal1.getTimeInMillis();
        long milliseconds2 = cal2.getTimeInMillis();
        long diff = Math.abs(milliseconds2 - milliseconds1);
        return diff / (24 * 60 * 60 * 1000);
    }

    private static boolean isSeparatedByNDaysOrMultiples(Calendar cal1, Calendar cal2, int n) {
        long daysBetween = getDaysBetween(cal1, cal2);
        return daysBetween % n == 0;
    }

    /**
     * 根据ID删除货架商品库存任务
     *
     * @param param
     */
    @Override
    public void deleteProductOnTaskById(IdCodeDTO param) {
        // 判断状态再删除
        productOnTaskMapper.deleteById(param.getId());
    }

    @Override
    public void executeProductOnTaskOfAllShelf() {
        ShelfParamDTO param = new ShelfParamDTO();
        param.setState(NumConstants.ONE);
        param.setShelfState(ProductConstants.Shelf.STATE_IS_UP);
        List<ShelfDTO> onShelfList = shelfService.getNoPageShelf(param, NumConstants.TWO);
        for (ShelfDTO shelf : onShelfList) {
            // 避免同类内部调用事务方法因为反射代理失效
            ProductOnTaskService proxyBean = applicationContext.getBean(ProductOnTaskService.class);
            proxyBean.executeProductOnTaskOfShelf(shelf);
        }
    }

    /**
     * 分页查询货架商品库存任务
     *
     * @param param
     * @return PageInfo<ProductOnTaskDTO>
     */
    @Override
    public PageInfo<CrmProductOnTaskListDTO> getProductOnTaskList(CrmProductOnTaskLisParamDTO param) {
        LambdaQueryWrapper<ProductOnTask> productOnTaskQueryWrapper = new LambdaQueryWrapper<>();
        // product_name like "%productName%"
        productOnTaskQueryWrapper
                .eq(ProductOnTask::getShelfId, param.getShelfId())
                .like(StringUtils.isNotBlank(param.getProductName()), ProductOnTask::getProductName, param.getProductName())
                // state = #{state}
                .eq(Objects.nonNull(param.getState()), ProductOnTask::getState, param.getState())
                // state != 1
                .ne(Objects.equals(NumConstants.ONE, param.getHideDone()), ProductOnTask::getState, ProductConstants.ProductOnTask.STATE_DONE)
                .orderByDesc(ProductOnTask::getId);
        IPage<ProductOnTask> page = productOnTaskMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), productOnTaskQueryWrapper);
        List<CrmProductOnTaskListDTO> crmProductOnTaskListDTOS = BeanCopierUtils.convertList(page.getRecords(), CrmProductOnTaskListDTO.class);
        if (CollectionUtils.isEmpty(crmProductOnTaskListDTOS)) {
            return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), new ArrayList<>());
        }
        List<Long> pageShelfProductIdList = crmProductOnTaskListDTOS.stream().map(CrmProductOnTaskListDTO::getShelfProductId).collect(Collectors.toList());
        List<ShelfProduct> shelfProductList = shelfProductMapper.selectList(new LambdaQueryWrapper<ShelfProduct>().in(ShelfProduct::getId, pageShelfProductIdList));
        Map<Long, ShelfProduct> shelfProductMap = shelfProductList.stream().collect(Collectors.toMap(ShelfProduct::getId, shelfProduct -> shelfProduct));
        for (CrmProductOnTaskListDTO task : crmProductOnTaskListDTOS) {
            if (shelfProductMap.containsKey(task.getShelfProductId())) {
                task.setCurrentInventory(shelfProductMap.get(task.getShelfProductId()).getCurrentInventory());
            }
            // taskDesc
            if (Objects.equals(ProductConstants.ProductOnTask.ON_TYPE_NOW, task.getOnType())) {
                task.setTaskDesc("实时");
            }
            if (Objects.equals(ProductConstants.ProductOnTask.ON_TYPE_ONCE, task.getOnType())) {
                task.setTaskDesc(yyyyMMddHHmm.format(task.getOnTime()));
            }
            if (Objects.equals(ProductConstants.ProductOnTask.ON_TYPE_PER_PERIOD, task.getOnType())) {
                task.setTaskDesc(String.format("每%s天", task.getOnDays()));
            }
            // canDelete
            if (Objects.equals(ProductConstants.ProductOnTask.STATE_DONE, task.getState())) {
                task.setCanDelete(NumConstants.UN_DELETED);
            } else {
                task.setCanDelete(NumConstants.DELETED);
            }
        }
        return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), crmProductOnTaskListDTOS);
    }

    @Override
    @Transactional
    public Long addProductOnTask(List<ProductOnTaskSaveParamDTO> list) {
        Long count = 0L;
        CurrentUserDTO user = SecurityContext.getUser();
        List<ProductOnTask> addList = new ArrayList<>();
        List<ProductOnTask> nowList = new ArrayList<>();
        for (ProductOnTaskSaveParamDTO param : list) {
            if (Objects.nonNull(param.getId())) {
                continue;
            }
            count++;
            ProductOnTask addItem = BeanCopierUtils.convertObject(param, ProductOnTask.class);
            Date now = new Date();
            addItem.setTenantId(user.getTenantId());
            addItem.setCreated(now);
            addItem.setCreator(user.getUid());
            addItem.setModified(now);
            addItem.setModifier(user.getTenantId());
            addList.add(addItem);
            if (Objects.nonNull(addItem.getOnType()) && Objects.nonNull(addItem.getOnInventory()) && addItem.getOnInventory() != 0) {
                if (Objects.equals(addItem.getOnType(), ProductConstants.ProductOnTask.ON_TYPE_NOW)) {
                    nowList.add(addItem);
                    addItem.setState(ProductConstants.ProductOnTask.STATE_DONE);
                } else if (Objects.equals(addItem.getOnType(), ProductConstants.ProductOnTask.ON_TYPE_ONCE)) {
                    addItem.setState(ProductConstants.ProductOnTask.STATE_PENDING);
                } else if (Objects.equals(addItem.getOnType(), ProductConstants.ProductOnTask.ON_TYPE_PER_PERIOD)) {
                    addItem.setState(ProductConstants.ProductOnTask.STATE_ON_GOING);
                }
            } else {
                addItem.setState(ProductConstants.ProductOnTask.STATE_NEED_SETUP);
            }
        }
        List<List<ProductOnTask>> partition = Lists.partition(addList, 1000);
        for (List<ProductOnTask> part : partition) {
            productOnTaskMapper.insertBatch(part);
        }
        for (ProductOnTask task : nowList) {
            modifyShelfProductInventoryByTask(task);
        }
        return count;
    }

    /**
     * 保存货架商品库存任务
     *
     * @param param
     * @return Long
     */
    @Override
    public ProductOnTask saveProductOnTask(ProductOnTaskDTO param) {
        ProductOnTask task = productOnTaskMapper.selectById(param.getId());
        if (Objects.isNull(task)) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "任务不存在");
        }
        ProductOnTask productOnTask = new ProductOnTask(param.getId(), task.getShelfProductId(), param.getShelfId(), param.getShelfName(), param.getProductId(), param.getProductName(), param.getOnType(), param.getOnTime(), param.getOnDays(), param.getOnInventory(), param.getState(), param.getShelfState());
        if (Objects.nonNull(param.getOnInventory()) && param.getOnInventory() < 0) {
            LambdaQueryWrapper<ShelfProduct> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ShelfProduct::getId, productOnTask.getShelfProductId());
            queryWrapper.eq(ShelfProduct::getShelfId, productOnTask.getShelfId());
            queryWrapper.eq(ShelfProduct::getProductId, productOnTask.getProductId());
            ShelfProduct shelfProduct = shelfProductMapper.selectOne(queryWrapper);
            if (-param.getOnInventory() > shelfProduct.getCurrentInventory()) {
                throw new BusinessException("下架库存不能超过目前库存");
            }
        }
        if (Objects.equals(productOnTask.getOnType(), ProductConstants.ProductOnTask.ON_TYPE_NOW)) {
            productOnTask.setState(ProductConstants.ProductOnTask.STATE_DONE);
        } else if (Objects.equals(productOnTask.getOnType(), ProductConstants.ProductOnTask.ON_TYPE_ONCE)) {
            productOnTask.setState(ProductConstants.ProductOnTask.STATE_PENDING);
        } else if (Objects.equals(productOnTask.getOnType(), ProductConstants.ProductOnTask.ON_TYPE_PER_PERIOD)) {
            productOnTask.setState(ProductConstants.ProductOnTask.STATE_ON_GOING);
        }
        productOnTaskMapper.updateById(productOnTask);
        if (Objects.equals(productOnTask.getOnType(), ProductConstants.ProductOnTask.ON_TYPE_NOW)) {
            if (Objects.nonNull(productOnTask.getOnInventory()) && productOnTask.getOnInventory() != 0) {
                modifyShelfProductInventoryByTask(productOnTask);
            }
        }
        return productOnTask;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void executeProductOnTaskOfShelf(ShelfDTO shelfDTO) {
        // 货架停用
        if (Objects.equals(shelfDTO.getState(), NumConstants.ZERO)) {
            return;
        }
        // 冗余处理货架状态
        //if (Objects.equals(shelfDTO.getOnType(), ProductConstants.Shelf.ON_TYPE_TWO)) {
            Date date = new Date();
            if (shelfDTO.getOnStartTime().after(date)) {
                shelfDTO.setShelfState(ProductConstants.Shelf.STATE_NO_START);
            }
            if (shelfDTO.getOnStartTime().before(date) && shelfDTO.getOnEndTime().after(date)) {
                shelfDTO.setShelfState(ProductConstants.Shelf.STATE_IS_UP);
            }
            if (shelfDTO.getOnEndTime().before(date)) {
                shelfDTO.setShelfState(ProductConstants.Shelf.STATE_IS_END);
            }
        //}
        if (Objects.equals(shelfDTO.getShelfState(), ProductConstants.Shelf.STATE_IS_UP)) {
            // 搜索所有货架上下架商品任务
            LambdaQueryWrapper<ProductOnTask> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProductOnTask::getShelfId, shelfDTO.getId());
            // 上架类型 1实时 2单次时间 3周期, 实时类型任务列表保存时已执行完成
            queryWrapper.in(ProductOnTask::getOnType, List.of(2, 3));
            // 状态 0待完成 1已完成 2进行中
            queryWrapper.in(ProductOnTask::getState, List.of(0, 2));
            List<ProductOnTask> pendingTask = productOnTaskMapper.selectList(queryWrapper);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            for (ProductOnTask task : pendingTask) {
                // 单次时间 任务
                if (task.getOnType().equals(ProductConstants.ProductOnTask.ON_TYPE_ONCE) && task.getOnTime().before(new Date())) {
                    modifyShelfProductInventoryByTask(task);
                }
                // 周期 任务
                if (task.getOnType().equals(ProductConstants.ProductOnTask.ON_TYPE_PER_PERIOD)) {
                    Calendar calBase = Calendar.getInstance();
                    calBase.setTime(task.getCreated());

                    Calendar today = Calendar.getInstance();
                    today.setTime(new Date());
                    boolean separatedByNDaysOrMultiples = isSeparatedByNDaysOrMultiples(calBase, today, task.getOnDays());
                    // 判断是否是周期
                    if (separatedByNDaysOrMultiples) {
                        QueryWrapper<TaskLog> taskLogQueryWrapper = new QueryWrapper<>();
                        taskLogQueryWrapper.eq("task_id", task.getId());
                        taskLogQueryWrapper.eq("DATE_FORMAT(created, '%Y-%m-%d')", sdf.format(new Date()));
                        TaskLog taskLog = taskLogMapper.selectOne(taskLogQueryWrapper);
                        // 每个周期只执行一次
                        if (Objects.isNull(taskLog)) {
                            modifyShelfProductInventoryByTask(task);
                        }
                    }
                }
            }
        }
    }

    private int modifyShelfProductInventoryByTask(ProductOnTask param) {
        // 修改库存
        LambdaQueryWrapper<ShelfProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShelfProduct::getId, param.getShelfProductId());
        queryWrapper.eq(ShelfProduct::getShelfId, param.getShelfId());
        queryWrapper.eq(ShelfProduct::getProductId, param.getProductId());
        ShelfProduct shelfProduct = shelfProductMapper.selectOne(queryWrapper);
        if (Objects.isNull(shelfProduct)) {
            return 0;
        }
        ShelfProduct toUpdate = new ShelfProduct();
        toUpdate.setId(shelfProduct.getId());
        toUpdate.setCurrentInventory(shelfProduct.getCurrentInventory() + param.getOnInventory());
        if (toUpdate.getCurrentInventory() < 0) {
            toUpdate.setCurrentInventory(0);
        }
        int i = shelfProductMapper.updateAddInventory(toUpdate);
        log.info("该货架商品id: {}库存修改状态: {}", shelfProduct.getId(), i);
        if (i > 0) {
            if (Objects.equals(param.getOnType(), ProductConstants.ProductOnTask.ON_TYPE_NOW)) {
                param.setState(ProductConstants.ProductOnTask.STATE_DONE);
            } else if (Objects.equals(param.getOnType(), ProductConstants.ProductOnTask.ON_TYPE_ONCE)) {
                param.setState(ProductConstants.ProductOnTask.STATE_DONE);
            } else if (Objects.equals(param.getOnType(), ProductConstants.ProductOnTask.ON_TYPE_PER_PERIOD)) {
                param.setState(ProductConstants.ProductOnTask.STATE_ON_GOING);
            }
            productOnTaskMapper.updateById(param);
            // 保存修改日志
            taskLogMapper.insert(new TaskLog(null, param.getId(), param.getShelfId(), param.getShelfName(), param.getProductId(), param.getProductName(), param.getOnInventory(), shelfProduct.getCurrentInventory()));
        }
        return i;
    }

}
