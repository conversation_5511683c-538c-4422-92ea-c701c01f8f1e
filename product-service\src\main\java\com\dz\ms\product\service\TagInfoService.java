package com.dz.ms.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.ms.product.dto.TagInfoDTO;
import com.dz.ms.product.entity.TagInfo;

import java.util.List;

/**
 * 标签接口
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:51
 */
public interface TagInfoService extends IService<TagInfo> {

    /**
     * 分页查询标签
     *
     * @param param
     * @return PageInfo<TagInfoDTO>
     */
    public PageInfo<TagInfoDTO> getTagInfoList(TagInfoDTO param);

    /**
     * 查询标签
     */
    List<TagInfoDTO> getNoPageTagInfoList(String name, Integer cate);

    /**
     * 查询标签列表
     *
     * @param tagIdList
     * @return
     */
    List<TagInfoDTO> getByTagIdList(List<Long> tagIdList);

    /**
     * 根据ID查询标签
     *
     * @param id
     * @return TagInfoDTO
     */
    public TagInfoDTO getTagInfoById(Long id);

    /**
     * 保存标签
     *
     * @param param
     * @return Long
     */
    Long saveTagInfo(TagInfoDTO param);

    /**
     * 编辑标签
     */
    Long updateTagInfo(TagInfoDTO param);

    /**
     * 根据ID删除标签
     *
     * @param param
     */
    public void deleteTagInfoById(IdCodeDTO param);
}
