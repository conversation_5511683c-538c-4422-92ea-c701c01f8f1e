package com.dz.ms.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.product.dto.req.ShelfCampaignRuleDelParamDTO;
import com.dz.ms.product.entity.ShelfCampaignRule;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 营销活动规则Mapper
 *
 * @author: LiinNs
 * @date: 2024/11/20 11:39
 */
@Repository
public interface ShelfCampaignRuleMapper extends BaseMapper<ShelfCampaignRule> {

    /**
     * 根据人群包id,更新货架营销活动关联人群包id为null
     * @param groupId 人群包id
     */
    void updGroupIdIntoNull(@Param("groupId") Long groupId);

    /**
     * 根据条件删除营销活动规则
     * @param param 删除条件
     */
    void deleteByParam(@Param("param") ShelfCampaignRuleDelParamDTO param);
    
}
