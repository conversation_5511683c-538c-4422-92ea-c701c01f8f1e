<!--
 * @Author: 提瓦特冒险
 * @Date: 2025-01-22 11:41:33
 * @LastEditTime: 2025-02-27 11:45:38
 * @LastEditors: 提瓦特冒险
 * @Description: 
 * @FilePath: \muji-admin\src\views\sensitiveMuscleActivity\index.vue
 * 可以输入预定的版权声明、个性签名、空行等（同一时间，同一地点，超级牛马，认真上班）
-->
<template>
  <layout>
    <template v-slot:headerTop>
      <div>
        <div class="activeData">
          <div class="active-item">
            <div class="label">报名招募人数</div>
            <div class="cont">
              <div class="value">{{ CampaignData.enrollNum || 0 }}</div>
              <div class="danwei">人</div>
            </div>
          </div>
          <div class="active-item">
            <div class="label">招募人数</div>
            <div class="cont">
              <div class="value">{{ CampaignData.recruitNum || 0 }}</div>
              <div class="danwei">人</div>
            </div>
          </div>
          <div class="active-item">
            <div class="label">购买用户</div>
            <div class="cont">
              <div class="value">{{ CampaignData.purchaseNum || 0 }}</div>
              <div class="danwei">人</div>
            </div>
          </div>
          <div class="active-item">
            <div class="label">参与打卡人数</div>
            <div class="cont">
              <div class="value">{{ CampaignData.signInNum || 0 }}</div>
              <div class="danwei">人</div>
            </div>
          </div>
          <div class="active-item">
            <div class="label">完成7天打卡</div>
            <div class="cont">
              <div class="value">{{ CampaignData.signIn7DaysNum || 0 }}</div>
              <div class="danwei">人</div>
            </div>
          </div>
        </div>
        <div class="activeData">
          <div class="active-item">
            <div class="label">完成1天打卡</div>
            <div class="cont">
              <div class="value">{{ CampaignData.signIn1DaysNum || 0 }}</div>
              <div class="danwei">人</div>
            </div>
          </div>
          <div class="active-item">
            <div class="label">完成2天打卡</div>
            <div class="cont">
              <div class="value">{{ CampaignData.signIn2DaysNum || 0 }}</div>
              <div class="danwei">人</div>
            </div>
          </div>
          <div class="active-item">
            <div class="label">完成3天打卡</div>
            <div class="cont">
              <div class="value">{{ CampaignData.signIn3DaysNum || 0 }}</div>
              <div class="danwei">人</div>
            </div>
          </div>
          <div class="active-item">
            <div class="label">完成4天打卡</div>
            <div class="cont">
              <div class="value">{{ CampaignData.signIn4DaysNum || 0 }}</div>
              <div class="danwei">人</div>
            </div>
          </div>
          <div class="active-item">
            <div class="label">完成5天打卡</div>
            <div class="cont">
              <div class="value">{{ CampaignData.signIn5DaysNum || 0 }}</div>
              <div class="danwei">人</div>
            </div>
          </div>
          <div class="active-item">
            <div class="label">完成6天打卡</div>
            <div class="cont">
              <div class="value">{{ CampaignData.signIn6DaysNum || 0 }}</div>
              <div class="danwei">人</div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template v-slot:header>
      <searchForm
        :formParams="formParams"
        @cancel="resetData"
        @ok="refreshData"
        :disabled="!$hasPermission('sensitiveMuscleActivity:search')"
      >
        <a-form-item label="第几天打卡" name="days">
          <a-select
            ref="select"
            placeholder="请选择"
            v-model:value="formParams.days"
            :options="TypeOptions"
            allowClear
            :fieldNames="{ label: 'label', value: 'value' }"
            optionFilterProp="label"
            showSearch
          >
          </a-select>
        </a-form-item>
      </searchForm>
    </template>
    <template v-slot:topLeft>
      <a-space>
        <!--  -->
        <a-button
          type="primary"
          @click="handleExport"
          :disabled="!$hasPermission('sensitiveMuscleActivity:export')"
          >导出</a-button
        >
      </a-space>
    </template>
    <template v-slot="{ height }">
      <a-table
        :indentSize="20"
        row-key="id"
        :scroll="{ scrollToFirstRowOnChange: true, y: height - 88, x: '100%' }"
        :dataSource="dataSource"
        :columns="tableHeader"
        :pagination="pagination"
        :loading="loading"
        @change="loadData"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'index'">
            {{ (pagination.current - 1) * pagination.pageSize + 1 + index }}
          </template>
          <template v-if="startList.includes(column.key)">
            <span>{{ record[column.key] }}分</span>
          </template>
          <template v-if="column.key === 'materialUrl' && record.materialUrl">
            <div class="image-wrap" style="display: flex">
              <div
                style="margin-right: 10px"
                class="img-item"
                v-for="(item, index) in record.materialUrl.split(',')"
                :key="index"
              >
                <a-image :src="item" :width="50" :height="50"></a-image>
              </div>
            </div>
          </template>
        </template>
      </a-table>
    </template>
  </layout>
</template>
<script setup>
// import { useRoute, useRouter } from 'vue-router'
import { usePagination, useRequest } from "vue-request";
import { downloadTask } from "@/http/index.js";
import {
  CampaignList,
  CampaignDataInfo,
  exportCampaignData,
} from "@/http/index.js";
// import { message, Modal } from "ant-design-vue";
import { downloadExcel } from "@/utils/tools.js";
import dayjs from "dayjs";
import { cloneDeep } from "lodash";
// 分页参数
let pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ["bottomLeft"],
  };
});
// 表格分页数据获取
const total = ref(0);

const { formParams, tableHeader, CampaignData, startList, TypeOptions } =
  toRefs(
    reactive({
      CampaignData: {},
      startList: [
        "comfort",
        "moisturize",
        "absorption",
        "persistence",
        "satisfaction",
        "score",
        "recommend",
        "purchase",
      ],
      id: "",
      type: 0, // 0-新增 1-编辑  2-查看
      visible: false,
      formParams: {
        days: "",
      },
      TypeOptions: [
        { label: 1, value: 1 },
        { label: 2, value: 2 },
        { label: 3, value: 3 },
        { label: 4, value: 4 },
        { label: 5, value: 5 },
        { label: 6, value: 6 },
        { label: 7, value: 7 },
      ],
      tableHeader: [
        {
          title: "序",
          key: "index",
          align: "center",
          width: 80,
        },
        {
          title: "用户昵称",
          dataIndex: "username",
          key: "cardNo",
          width: 200,
          align: "center",
          ellipsis: true,
        },
        {
          title: "会员卡号",
          dataIndex: "cardNo",
          key: "cardNo",
          width: 150,
          align: "center",
          ellipsis: true,
        },
        {
          title: "第几天打卡",
          dataIndex: "days",
          key: "days",
          width: 100,
          align: "center",
        },
        {
          title: "打卡时间",
          dataIndex: "signInTime",
          key: "signInTime",
          align: "center",
          width: 160,
        },
        {
          title: "舒适度",
          dataIndex: "comfort",
          key: "comfort",
          align: "center",
          width: 100,
        },
        {
          title: "保湿度",
          key: "moisturize",
          dataIndex: "moisturize",
          align: "center",
          width: 100,
        },
        {
          title: "吸收速度",
          dataIndex: "absorption",
          key: "absorption",
          align: "center",
          width: 100,
        },
        {
          title: "持久效果",
          dataIndex: "persistence",
          key: "persistence",
          align: "center",
          width: 100,
        },
        {
          title: "整体满意度",
          dataIndex: "satisfaction",
          key: "satisfaction",
          align: "center",
          width: 100,
        },
        {
          title: "使用照片",
          dataIndex: "materialUrl",
          key: "materialUrl",
          align: "left",
          width: 450,
          ellipsis: true,
        },
        {
          title: "7日使用感受",
          dataIndex: "feeling",
          key: "feeling",
          align: "center",
          width: 100,
          ellipsis: true,
        },
        {
          title: "7天后使用改善",
          dataIndex: "improve",
          key: "improve",
          align: "left",
          width: 200,
          ellipsis: true,
        },
        {
          title: "产品满意度",
          dataIndex: "score",
          key: "score",
          align: "center",
          width: 100,
        },
        {
          title: "推荐产品度",
          dataIndex: "recommend",
          key: "recommend",
          align: "center",
          width: 100,
        },
        {
          title: "继续购买度",
          dataIndex: "purchase",
          key: "purchase",
          align: "center",
          width: 100,
        },
      ],
    })
  );
// 处理参数
const getParams = () => {
  let params = { ...formParams.value };
  return params;
};
const {
  data: dataSource,
  run,
  loading,
  pageSize,
  current,
  refresh,
} = usePagination(
  (param) => {
    // 这里修改参数
    return CampaignList({ ...param, ...getParams() });
  },
  {
    manual: false, // 修改为false,让其自动执行
    pagination: {
      currentKey: "pageNum", // 通过该值指定接口 当前页数 参数的属性值
      pageSizeKey: "pageSize", // 通过该值指定接口 每页获取条数 参数的属性值
      totalKey: "data.data.count", // 指定 data 中 total 属性的路径
    },
    formatResult: (res) => {
      // 返回数据格式化
      total.value = res.data.count;
      return res.data.list;
    },
  }
);

initData();
function initData() {
  CampaignDataInfo().then((res) => {
    console.log(res, "CampaignData");
    CampaignData.value = res.data;
  });
}

const handleExport = async () => {
  const param = cloneDeep(getParams());
  downloadTask({ type: 8, fileName: "打卡", param });
};

// 加载数据
const loadData = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize });
};

// 刷新数据
const refreshData = () => {
  run({ pageNum: 1, pageSize: pageSize.value });
};

// 重置数据
const resetData = () => {
  formParams.value = {
    days: "",
  };
  refreshData();
};
</script>
<style scoped lang="scss">
.activeData {
  position: relative;
  display: flex;
  // padding: 20px 0px;
  flex-wrap: wrap;
  justify-content: start;

  .active-item {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    min-width: 175px;

    .label {
      font-size: 14px;
      // padding-bottom: 5px;
      margin-right: 10px;
    }

    .cont {
      display: flex;
      align-items: flex-end;
      margin-right: 30px;

      .value {
        font-size: 28px;
        text-align: right;
      }

      .danwei {
        font-size: 12px;
        padding-bottom: 5px;
      }
    }
  }
}
</style>
