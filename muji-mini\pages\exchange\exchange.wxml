<my-page loading="{{loading}}" expirePointsNum="{{expirePointsNum}}" overallModal="{{overallModal}}">
  <custom-page info="{{info}}" pageSetting="{{info.pageSetting}}" navSetting="{{info.navSetting}}"
    componentSetting="{{info.componentSetting}}" wx:if="{{info.id}}" bindgoShare="goShare">
    <view class="page-container">
      <!-- <view class="page-content"> -->
      <view class="top-content">
        <!-- 会员等级 -->
        <member-level expandState="{{expandState}}" userInfo="{{userInfo}}" />
        <view class="card-solt" style="background-image: url({{$cdn}}/memberCard/solt.png);" />
        <!-- 会员档案 -->
        <member-info bindexpand="expandMemberCard" userInfo="{{userInfo}}" levelsList="{{memberLevel}}"
          expandState="{{expandState}}" />
      </view>
      <!-- 会员权益 -->
      <view class="rights-box">
        <view class="title">
          会员权益
        </view>
        <view class="row">
          <view class="col-left">已解锁{{rightsList[indexMemcer].activateBenefits.length ||0}}项专属权益</view>
          <view class="col-right" catchtap="toMember">
            <view class="go-btn">会员服务使用协议
              <image class="right-jian" src="{{$cdn}}/right-arrow.png" mode="" />
            </view>
            <view class="go-btn" catchtap="memberTo">权益领取记录
              <image class="right-jian" src="{{$cdn}}/right-arrow.png" mode="" />
            </view>
          </view>
        </view>
        <basic-tabs labelKey="gradeName" valueKey="id" indexMer="{{currentNameIndex}}" tabsType="{{true}}"
          buttom="{{44}}" Fbuttom="{{44}}" width="{{100}}" tabs="{{memberLevel}}" active="{{currentLevel}}"
          bindchange="changeLevel" />
        <!-- 权益内容 -->

      </view>
      <view class="rights-content">
        <view class="rights-list">
          <block wx:for="{{rightsList[currentLevel].activateBenefits}}" wx:for-item="stu" wx:for-index="idx"
            wx:key="id">
            <!-- {{!stu.unLock &&'unlock'}} -->
            <view class="rights-item" bindtap="popup" data-item="{{stu}}">
              <view class="icon-box">
                <image src="{{stu.activateImg}}" mode="" />
              </view>
              <view class="item-name">
                {{stu.benefitName}}
              </view>
            </view>
          </block>
          <block wx:for="{{rightsList[currentLevel].unActivateBenefits}}" wx:for-item="stu" wx:for-index="idx"
            wx:key="id">
            <!-- {{!stu.unLock &&'unlock'}} -->
            <view class="rights-item unlock" bindtap="popup" data-item="{{stu}}">
              <view class="icon-box">
                <image src="{{stu.unActivateImg}}" mode="" />
              </view>
              <view class="item-name">
                {{stu.benefitName}}
              </view>
            </view>
          </block>
        </view>
      </view>
    </view>
  </custom-page>
</my-page>

<!-- <jumpModal show="{{jumpDataShow}}" jumpData="{{itemObj}}" bindclose="close" bindconfirm="confirm"
   data-key="jumpData"></jumpModal> -->