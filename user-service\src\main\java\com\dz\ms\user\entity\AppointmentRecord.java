package com.dz.ms.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Data;

import java.util.Date;

@Data
@Table("预约记录")
@TableName(value = "appointment_record")
public class AppointmentRecord {
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "预约记录ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "用户ID")
    private Long userId;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "活动ID")
    private Long appointmentId;

    @Columns(type = ColumnType.VARCHAR, length = 256, isNull = false, comment = "姓名")
    private String name;

    @Columns(type = ColumnType.VARCHAR, length = 20, isNull = false, comment = "电话")
    private String phone;

    @Columns(type = ColumnType.VARCHAR, length = 10, isNull = false, comment = "预约日期 yyyy/MM/dd")
    private String appointmentDate;

    @Columns(type = ColumnType.INT, length = 1, isNull = false, comment = "预约场次")
    private Integer appointmentSession;

    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = false, comment = "预约时段 14:00-16:00")
    private String appointmentSlot;

    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = true, comment = "会员卡号")
    private String memberCode;

    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    private Date createdTime;

    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = false, comment = "状态")
    private String status;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;

    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;

    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, defaultValue = "0", comment = "0正常 1删除")
    @TableLogic
    private Integer isDeleted;
}