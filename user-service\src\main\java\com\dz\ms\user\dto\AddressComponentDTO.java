package com.dz.ms.user.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * 地点搜索DTO
 * 
 * <AUTHOR>
 * @date 2023/03/10 12:26
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class AddressComponentDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "国")
	private String nation;
	@ApiModelProperty(value = "省")
	private String province;
	@ApiModelProperty(value = "市")
	private String city;
	@ApiModelProperty(value = "区")
	private String district;
	@ApiModelProperty(value = "路")
	private String street;
	@ApiModelProperty(value = "号")
	@JSONField(name = "street_number")
	private String streetNumber;

}
