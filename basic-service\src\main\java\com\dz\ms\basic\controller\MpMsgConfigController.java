package com.dz.ms.basic.controller;

import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.enums.LogType;
import com.dz.ms.basic.dto.MpMsgConfigDTO;
import com.dz.ms.basic.dto.StyleConfigDTO;
import com.dz.ms.basic.service.MpMsgConfigService;
import com.dz.ms.basic.service.StyleConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags="小程序订阅消息全局配置")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class MpMsgConfigController {

    @Resource
    private MpMsgConfigService mpMsgConfigService;

    @ApiOperation("获取当前订阅消息配置")
    @GetMapping(value = "/crm/mp_msg_config/get")
    public Result<MpMsgConfigDTO> getStyleConfig() {
        Result<MpMsgConfigDTO> result = new Result<>();
        MpMsgConfigDTO config = mpMsgConfigService.getMpMsgConfigList(1L);
        result.setData(config);
        return result;
    }
    @ApiOperation("根据ID获取当前订阅消息配置")
    @GetMapping(value = "/crm/mp_msg_config/info")
    public Result<MpMsgConfigDTO> getStyleConfigByid(@RequestParam("id") Long id) {
        Result<MpMsgConfigDTO> result = new Result<>();
        MpMsgConfigDTO config = mpMsgConfigService.getMpMsgConfigById(id);
        result.setData(config);
        return result;
    }

    /**
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "保存订阅消息配置",type = LogType.OPERATELOG)
    @ApiOperation("保存订阅消息配置")
    @PostMapping(value = "/crm/mp_msg_config/save")
    public Result<Long> saveStyleConfig(@RequestBody MpMsgConfigDTO param) {
        Result<Long> result = new Result<>();
        Long id = mpMsgConfigService.saveMpMsgConfig(param);
        result.setData(id);
        return result;
    }


    @ApiOperation("小程序中获取当前订阅消息配置")
    @GetMapping(value = "/app/mp_msg_config/get")
    public Result<MpMsgConfigDTO> appGetStyleConfig() {
        Result<MpMsgConfigDTO> result = new Result<>();
        MpMsgConfigDTO config = mpMsgConfigService.getMpMsgConfigList(1L);
        result.setData(config);
        return result;
    }
}
