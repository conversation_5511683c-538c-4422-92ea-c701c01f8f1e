package com.dz.ms.sales.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/2/11
 */
@Data
public class CampaignEnrollRosterListDTO {

    @ApiModelProperty("报名开始时间")
    private Date campaignStartTime;

    @ApiModelProperty("报名结束时间")
    private Date campaignEndTime;

    @ApiModelProperty("活动公示时间")
    private String campaignShowTime;

    @ApiModelProperty("中奖名单")
    private List<CampaignEnrollRosterDTO> enrollRosterList;
}
