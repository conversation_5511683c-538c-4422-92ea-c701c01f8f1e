package com.dz.ms.product.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.annotation.Lock;
import com.dz.common.core.constants.NumConstants;
import com.dz.common.core.constants.ProductConstants;
import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.IdNumberDTO;
import com.dz.common.core.dto.export.CampaignExportDTO;
import com.dz.common.core.dto.user.CrowdDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.service.ExportService;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.CommonUtils;
import com.dz.common.core.utils.DateUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.product.CacheKeys;
import com.dz.ms.product.dto.ShelfCampaignDTO;
import com.dz.ms.product.dto.ShelfCampaignRuleDTO;
import com.dz.ms.product.dto.ShelfDTO;
import com.dz.ms.product.dto.req.*;
import com.dz.ms.product.entity.ShelfCampaign;
import com.dz.ms.product.mapper.ShelfCampaignMapper;
import com.dz.ms.product.service.ShelfCampaignRuleProductService;
import com.dz.ms.product.service.ShelfCampaignRuleService;
import com.dz.ms.product.service.ShelfCampaignService;
import com.dz.ms.product.service.ShelfService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 货架营销活动
 *
 * @author: LiinNs
 * @date: 2024/11/20 11:39
 */
@Service
@Slf4j
public class ShelfCampaignServiceImpl extends ServiceImpl<ShelfCampaignMapper, ShelfCampaign> implements ShelfCampaignService {

    @Resource
    private ShelfCampaignMapper shelfCampaignMapper;
    @Resource
    private ShelfService shelfService;
    @Resource
    private ShelfCampaignRuleService shelfCampaignRuleService;
    @Resource
    private ShelfCampaignRuleProductService shelfCampaignRuleProductService;
    @Resource
    private RedisService redisService;
    @Resource
    private ExportService exportService;

    /**
     * 分页查询货架营销活动
     *
     * @param param 入参
     * @return PageInfo<ShelfCampaignDTO>
     */
    @Override
    public PageInfo<ShelfCampaignDTO> getShelfCampaignList(ShelfCampaignParamDTO param) {
        List<ShelfCampaignDTO> list = new ArrayList<>();
        if (param.getOnStartTime() != null || param.getOnEndTime() != null) {
            if (param.getOnStartTime() == null || param.getOnEndTime() == null) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "开始/结束时间都不能为空");
            }
            if (param.getOnStartTime().after(param.getOnEndTime())) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "开始时间不能在结束时间之后");
            }
        }
        IPage<ShelfCampaign> page = shelfCampaignMapper.selPageList(new Page<>(param.getPageNum(), param.getPageSize()), param);
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            Date date = new Date();
            list = BeanCopierUtils.convertList(page.getRecords(), ShelfCampaignDTO.class);
            Map<Long, ShelfDTO> shelfMap = shelfService.getNoPageShelfMap(ShelfParamDTO.builder().shelfIdList(list.stream().map(ShelfCampaignDTO::getShelfId).collect(Collectors.toList())).build(), NumConstants.ONE);
            for (ShelfCampaignDTO shelfCampaignDTO : list) {
                if (Objects.nonNull(param.getCampaignState())) {
                    shelfCampaignDTO.setCampaignState(param.getCampaignState());
                }
                if (Objects.isNull(param.getCampaignState())) {
                    if (shelfCampaignDTO.getOnStartTime().after(date)) {
                        shelfCampaignDTO.setCampaignState(ProductConstants.ShelfCampaign.STATE_NO_START);
                    }
                    if (shelfCampaignDTO.getOnStartTime().before(date) && shelfCampaignDTO.getOnEndTime().after(date)) {
                        shelfCampaignDTO.setCampaignState(ProductConstants.ShelfCampaign.STATE_IS_UP);
                    }
                    if (shelfCampaignDTO.getOnEndTime().before(date)) {
                        shelfCampaignDTO.setCampaignState(ProductConstants.ShelfCampaign.STATE_IS_END);
                    }
                }
                ShelfDTO shelfDTO = shelfMap.get(shelfCampaignDTO.getShelfId());
                if (Objects.nonNull(shelfDTO)) {
                    shelfCampaignDTO.setShelfName(shelfDTO.getName());
                }
            }
        }
        return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), list);
    }

    /**
     * 根据ID查询货架营销活动
     *
     * @param id        id
     * @param isThrow   1:抛异常
     * @return ShelfCampaignDTO
     */
    @Override
    public ShelfCampaignDTO getShelfCampaignById(Long id, Integer isThrow) {
        ShelfCampaign shelfCampaign = shelfCampaignMapper.selectById(id);
        if (shelfCampaign == null && Objects.equals(isThrow, NumConstants.ONE)) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[货架营销活动]未查询到此营销活动");
        }
        ShelfCampaignDTO shelfCampaignDTO = null;
        if (Objects.nonNull(shelfCampaign)) {
            shelfCampaignDTO = BeanCopierUtils.convertObject(shelfCampaign, ShelfCampaignDTO.class);
            Long shelfId = shelfCampaign.getShelfId();
            Map<Long, ShelfDTO> shelfMap = shelfService.getNoPageShelfMap(ShelfParamDTO.builder().shelfIdList(Collections.singletonList(shelfId)).build(), NumConstants.ONE);
            ShelfDTO shelfDTO = shelfMap.get(shelfId);
            if (Objects.nonNull(shelfDTO)) {
                shelfCampaignDTO.setShelfName(shelfDTO.getName());
            }
        }
        return shelfCampaignDTO;
    }

    /**
     * 根据货架ID列表查询货架营销活动列表
     * @param shelfIds   shelfIds
     * @param isQryProductNum 是否查询商品数量
     * @return List<ShelfCampaignDTO>
     */
    @Override
    public List<ShelfCampaignDTO> getShelfCampaignByShelfIds(List<Long> shelfIds, boolean isQryProductNum) {
        List<ShelfCampaignDTO> shelfCampaignDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(shelfIds)) {
            List<ShelfCampaign> shelfCampaignList = shelfCampaignMapper.selectList(new LambdaQueryWrapper<ShelfCampaign>().in(ShelfCampaign::getShelfId, shelfIds));
            if(!CollectionUtils.isEmpty(shelfCampaignList)){
                Date date = new Date();
                shelfCampaignDTOList = BeanCopierUtils.convertList(shelfCampaignList, ShelfCampaignDTO.class);
                Map<Long, Long> campaignIdsSumMap = new HashMap<>();
                if(isQryProductNum){
                    campaignIdsSumMap = shelfCampaignRuleProductService.getRuleProductNumByCampaignIds(shelfCampaignList.stream().map(ShelfCampaign::getId).collect(Collectors.toList()));
                }
                if(!CollectionUtils.isEmpty(shelfCampaignDTOList)){
                    for (ShelfCampaignDTO shelfCampaignDTO : shelfCampaignDTOList) {
                        //1.活动状态
                        if (shelfCampaignDTO.getOnStartTime().after(date)) {
                            shelfCampaignDTO.setCampaignState(ProductConstants.ShelfCampaign.STATE_NO_START);
                        }
                        if (shelfCampaignDTO.getOnStartTime().before(date) && shelfCampaignDTO.getOnEndTime().after(date)) {
                            shelfCampaignDTO.setCampaignState(ProductConstants.ShelfCampaign.STATE_IS_UP);
                        }
                        if (shelfCampaignDTO.getOnEndTime().before(date)) {
                            shelfCampaignDTO.setCampaignState(ProductConstants.ShelfCampaign.STATE_IS_END);
                        }
                        //2.活动商品数量
                        shelfCampaignDTO.setCampaignProductNum(campaignIdsSumMap.get(shelfCampaignDTO.getId()));
                    }
                }
            }
        }
        return shelfCampaignDTOList;
    }
    /**
     * 根据货架ID列表查询货架营销活动列表
     * @param shelfIds   shelfIds
     * @return Map<Long, List<ShelfCampaignDTO>>
     */
    @Override
    public Map<Long, List<ShelfCampaignDTO>> getShelfCampaignMap(List<Long> shelfIds){
        List<ShelfCampaignDTO> shelfCampaignDTOList = this.getShelfCampaignByShelfIds(shelfIds, true);
        return shelfCampaignDTOList.stream().collect(Collectors.groupingBy(ShelfCampaignDTO::getShelfId));
    }

    /**
     * 保存货架营销活动
     *
     * @param param
     * @return Long
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveShelfCampaign(ShelfCampaignSaveParamDTO param, boolean isAdd) {
        checkSaveParam(param, isAdd);
        Long uid = SecurityContext.getUser().getUid();
        ShelfCampaign shelfCampaign = new ShelfCampaign(param.getId(), param.getName(), param.getOnType(), param.getOnStartTime(), param.getOnEndTime(), param.getShelfId());
        String lockKey = CacheKeys.Locks.CAMPAIGN_ADD + SecurityContext.getUser().getUid();
        if(!isAdd){
            lockKey = CacheKeys.Locks.CAMPAIGN_UPDATE + param.getId();
        }
        boolean lock = redisService.lock(lockKey, NumConstants.THIRTY);
        if (lock) {
            try {
                if (ParamUtils.isNullOr0Long(shelfCampaign.getId())) {
                    shelfCampaignMapper.insert(shelfCampaign);
                } else {
                    shelfCampaignMapper.updateById(shelfCampaign);
                }
                //保存规则数据
                List<ShelfCampaignRuleSaveParamDTO> ruleList = param.getRuleList();
                if (!CollectionUtils.isEmpty(ruleList)) {
                    List<Long> delRuleIdList = new ArrayList<>();
                    List<ShelfCampaignRuleDTO> oldRuleList = shelfCampaignRuleService.getRuleListByCampaignIds(Collections.singletonList(shelfCampaign.getId()), false);
                    List<Long> oldRuleIdList = oldRuleList.stream().map(ShelfCampaignRuleDTO::getId).collect(Collectors.toList());
                    List<ShelfCampaignRuleSaveParamDTO> newUpdList = ruleList.stream().filter(s -> !ParamUtils.isNullOr0Long(s.getId())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(newUpdList)) {
                        List<Long> newUpdIdList = newUpdList.stream().map(ShelfCampaignRuleSaveParamDTO::getId).collect(Collectors.toList());
                        newUpdIdList = newUpdIdList.stream().filter(oldRuleIdList::contains).collect(Collectors.toList());
                        List<Long> finalNewUpdIdList = newUpdIdList;
                        if (!CollectionUtils.isEmpty(oldRuleIdList)) {
                            List<Long> filterDelOldRuleIdList = oldRuleIdList.stream().filter(s -> !finalNewUpdIdList.contains(s)).collect(Collectors.toList());
                            if (!CollectionUtils.isEmpty(filterDelOldRuleIdList)) {
                                delRuleIdList.addAll(filterDelOldRuleIdList);
                            }
                        }
                        ruleList = ruleList.stream().filter(s -> Objects.isNull(s.getId()) || finalNewUpdIdList.contains(s.getId())).collect(Collectors.toList());
                    } else {
                        if (!CollectionUtils.isEmpty(oldRuleIdList)) {
                            delRuleIdList.addAll(oldRuleIdList);
                        }
                    }
                    if (CollectionUtils.isEmpty(ruleList)) {
                        throw new BusinessException(ErrorCode.BAD_REQUEST, "请配置限购规则");
                    }
                    if (!CollectionUtils.isEmpty(delRuleIdList)) {
                        shelfCampaignRuleService.removeBatchByIds(delRuleIdList);
                        shelfCampaignRuleProductService.deleteRuleProduct(ShelfCampaignRuleProductDelParamDTO.builder().ruleIdList(delRuleIdList).build());
                    }
                    for (ShelfCampaignRuleSaveParamDTO dto : ruleList) {
                        dto.setShelfId(param.getShelfId());
                        dto.setCampaignId(shelfCampaign.getId());
                        shelfCampaignRuleService.saveShelfCampaignRule(shelfCampaign, dto, ParamUtils.isNullOr0Long(dto.getId()));
                    }
                }
            } catch (BusinessException e) {
                log.error("=================【货架活动】 saveShelfCampaign接口,uid:{},BusinessException报错:{},入参:{}",uid,e.getMessage(), CommonUtils.jsonStr(param),e);
                throw new BusinessException(e.getCode(),e.getMessage());
            } catch (Exception e) {
                log.error("=================【货架活动】 saveShelfCampaign接口】,uid:{},Exception报错:{},入参:{}",uid,e.getMessage(),CommonUtils.jsonStr(param),e);
                throw new BusinessException(ErrorCode.INTERNAL_ERROR);
            } finally {
                redisService.unlock(lockKey);
            }
        } else {
            if(StringUtils.equals(lockKey,CacheKeys.Locks.CAMPAIGN_ADD + SecurityContext.getUser().getUid())){
                throw new BusinessException(ErrorCode.CONFLICT,"请求中，请稍后");
            }
            throw new BusinessException(ErrorCode.CONFLICT, "请求中，请稍后");
        }
        return shelfCampaign.getId();
    }

    private void checkSaveParam(ShelfCampaignSaveParamDTO param, boolean isAdd) {
        if (isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if (!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
        if(StringUtils.isBlank(param.getName()) || Objects.isNull(param.getOnType())){
            throw new BusinessException(ErrorCode.BAD_REQUEST, "活动名称,活动时间必填");
        }
        if(Objects.equals(param.getOnType(), ProductConstants.Shelf.ON_TYPE_TWO)){
            if(param.getOnStartTime() == null || param.getOnEndTime() == null){
                throw new BusinessException(ErrorCode.BAD_REQUEST, "开始/结束时间都不能为空");
            }
            if(!param.getOnStartTime().before(param.getOnEndTime())){
                throw new BusinessException(ErrorCode.BAD_REQUEST, "开始时间不得晚于结束时间，同时，两者不可完全重合");
            }
        }
        if(Objects.equals(param.getOnType(),ProductConstants.ShelfCampaign.ON_TYPE_ONE)){
            param.setOnStartTime(DateUtils.timeClear("2000-01-01"));
            param.setOnEndTime(DateUtils.timeClear("3000-01-01"));
        }
        //简单校验限购规则
        List<Long> shelfProductIdList = new ArrayList<>();
        List<ShelfCampaignRuleSaveParamDTO> ruleList = param.getRuleList();
        if(CollectionUtils.isEmpty(ruleList)){
            throw new BusinessException(ErrorCode.BAD_REQUEST, "请配置限购规则");
        }
        //校验人群包
        for (ShelfCampaignRuleSaveParamDTO ruleParam : ruleList) {
            CrowdDTO crowdDTO = ruleParam.getCrowdDTO();
            if(Objects.isNull(crowdDTO)){
                throw new BusinessException(ErrorCode.BAD_REQUEST, "人群包不能为空");
            }
            ruleParam.setGroupId(crowdDTO.getCrowdId());
            ruleParam.setGroupType(crowdDTO.getCrowdType());
            if(Objects.isNull(ruleParam.getGroupType())){
                throw new BusinessException(ErrorCode.BAD_REQUEST, "人群包类型不能为空");
            }
            if(ruleParam.getGroupType() < NumConstants.ZERO || ruleParam.getGroupType() > NumConstants.TWO){
                throw new BusinessException(ErrorCode.BAD_REQUEST, "人群包类型不符合要求");
            }
            if(ruleParam.getGroupType() == NumConstants.ONE && Objects.isNull(ruleParam.getGroupId())){
                throw new BusinessException(ErrorCode.BAD_REQUEST, "请选择适用人群包");
            }
            List<ShelfCampaignRuleProductSaveDTO> ruleProductList = ruleParam.getRuleProductList();
            if(!CollectionUtils.isEmpty(ruleProductList)){
                shelfProductIdList.addAll(ruleProductList.stream().map(ShelfCampaignRuleProductSaveDTO::getShelfProductId).collect(Collectors.toList()));
            }
        }
        //校验货架商品是否重复
        if(shelfProductIdList.size() != new HashSet<>(shelfProductIdList).size()){
            throw new BusinessException(ErrorCode.BAD_REQUEST, "货架商品不能重复");
        }
        if(isAdd){
            //校验货架
            shelfService.getShelfById(param.getShelfId(), NumConstants.ONE);
        }
        if(!isAdd){
            //校验营销活动
            ShelfCampaignDTO shelfCampaignDTO = this.getShelfCampaignById(param.getId(), NumConstants.ONE);
            param.setShelfId(shelfCampaignDTO.getShelfId());
        }
        //根据shelfId查询所有数据并校验新增或更新的数据开始结束时间是否有重叠
        List<ShelfCampaign> shelfCampaignList = shelfCampaignMapper.selectList(new LambdaQueryWrapper<ShelfCampaign>().eq(ShelfCampaign::getShelfId, param.getShelfId()));
        if(!CollectionUtils.isEmpty(shelfCampaignList)){
            for (ShelfCampaign dateShelfCampaign : shelfCampaignList) {
                if(param.getId() != null && param.getId().equals(dateShelfCampaign.getId())){
                    continue;
                }
                boolean hasOverlap = (param.getOnStartTime().after(dateShelfCampaign.getOnStartTime()) && param.getOnStartTime().before(dateShelfCampaign.getOnEndTime()))
                        || (param.getOnEndTime().after(dateShelfCampaign.getOnStartTime()) && param.getOnEndTime().before(dateShelfCampaign.getOnEndTime()))
                        || (param.getOnStartTime().before(dateShelfCampaign.getOnStartTime()) && param.getOnEndTime().after(dateShelfCampaign.getOnEndTime()))
                        || param.getOnStartTime().equals(dateShelfCampaign.getOnStartTime())
                        || param.getOnEndTime().equals(dateShelfCampaign.getOnEndTime());
                if(hasOverlap){
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "同一货架活动时间段有重叠，请检查");
                }
            }
        }
    }

    /**
     * 根据ID删除货架营销活动
     * @param param
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock(prefix = CacheKeys.Locks.SHELF_CAMPAIGN_DELETE, key = "'#param.id'")
    public void deleteShelfCampaignById(IdCodeDTO param) {
        ShelfCampaignDTO shelfCampaignDTO = this.getShelfCampaignById(param.getId(), NumConstants.ONE);
        if (Objects.equals(shelfCampaignDTO.getState(), NumConstants.ONE)) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[货架营销活动]启用中营销活动无法删除");
        }
        shelfCampaignMapper.deleteById(param.getId());
        shelfCampaignRuleService.deleteRule(ShelfCampaignRuleDelParamDTO.builder().campaignId(param.getId()).build());
        shelfCampaignRuleProductService.deleteRuleProduct(ShelfCampaignRuleProductDelParamDTO.builder().campaignId(param.getId()).build());
    }

    /**
     * 根据ID修改启停状态
     *
     * @param param ID NUMBER 通用DTO
     */
    @Override
    public void updateStateById(IdNumberDTO param) {
        this.getShelfCampaignById(param.getId(), NumConstants.ONE);
        ParamUtils.checkStateParam(param.getNumber());
        shelfCampaignMapper.updateById(ShelfCampaign.builder().id(param.getId()).state(param.getNumber()).build());
    }

    /**
     * 根据货架id修改关联货架id为null
     *
     * @param shelfId 货架id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updShelfIdIntoNull(Long shelfId) {
        shelfCampaignMapper.updShelfIdIntoNull(shelfId);
    }

    @Override
    public void exportList(DownloadAddParamDTO exportParam) {
        String jsonParam = exportParam.getJsonParam();
        String fileName = exportParam.getFileName();
        String fileExt = exportParam.getFileExt();
        String reportCode = exportParam.getReportCode();
        Long downloadCenterId = exportParam.getDownloadCenterId();
        CurrentUserDTO commonLoginDTO = SecurityContext.getUser();
        ShelfCampaignParamDTO param = JSON.parseObject(jsonParam, ShelfCampaignParamDTO.class);
        List<JSONObject> reList = getCampaignExportDTO(param);
        exportService.writeExportList(fileName, fileExt, reportCode, downloadCenterId, commonLoginDTO, reList);
    }

    private List<JSONObject> getCampaignExportDTO(ShelfCampaignParamDTO param) {
        if (param.getOnStartTime() != null || param.getOnEndTime() != null) {
            if (param.getOnStartTime() == null || param.getOnEndTime() == null) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "开始/结束时间都不能为空");
            }
            if (param.getOnStartTime().after(param.getOnEndTime())) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "开始时间不能在结束时间之后");
            }
        }
        List<ShelfCampaign> list = shelfCampaignMapper.selectListByParam(param);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        Map<Long, ShelfDTO> shelfMap = shelfService.getNoPageShelfMap(ShelfParamDTO.builder().shelfIdList(list.stream().map(ShelfCampaign::getShelfId).collect(Collectors.toList())).build(), NumConstants.ONE);
        List<CampaignExportDTO> exportList = new ArrayList<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (ShelfCampaign shelfCampaign : list) {
            Date date = new Date();

            CampaignExportDTO exportItem = BeanCopierUtils.convertObject(shelfCampaign, CampaignExportDTO.class);
            if (Objects.nonNull(param.getCampaignState())) {
                exportItem.setCampaignState(param.getCampaignState());
            }
            if (Objects.isNull(param.getCampaignState())) {
                if (shelfCampaign.getOnStartTime().after(date)) {
                    exportItem.setCampaignState(ProductConstants.ShelfCampaign.STATE_NO_START);
                }
                if (shelfCampaign.getOnStartTime().before(date) && shelfCampaign.getOnEndTime().after(date)) {
                    exportItem.setCampaignState(ProductConstants.ShelfCampaign.STATE_IS_UP);
                }
                if (shelfCampaign.getOnEndTime().before(date)) {
                    exportItem.setCampaignState(ProductConstants.ShelfCampaign.STATE_IS_END);
                }
            }
            ShelfDTO shelfDTO = shelfMap.get(shelfCampaign.getShelfId());
            if (Objects.nonNull(shelfDTO)) {
                exportItem.setShelfName(shelfDTO.getName());
            }
            if (Objects.equals(ProductConstants.ShelfCampaign.ON_TYPE_ONE, shelfCampaign.getOnType())) {
                exportItem.setCampaignTimeStr("永久上架");
            } else {
                String campaignTimeStr = simpleDateFormat.format(shelfCampaign.getOnStartTime()) + "-" + simpleDateFormat.format(shelfCampaign.getOnEndTime());
                exportItem.setCampaignTimeStr(campaignTimeStr);
            }
            exportList.add(exportItem);
        }
        return new ArrayList<>(JSON.parseArray(JSON.toJSONString(exportList), JSONObject.class));
    }

}
