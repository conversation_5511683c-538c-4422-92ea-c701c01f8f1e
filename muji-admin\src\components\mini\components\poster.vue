<template>
  <div class="header-title">图片内容</div>
  <a-form-item>
    <uploadImg :max="10" :width="300" :height="100" :imgUrl="data.imgUrl" :form="data" path="imgUrl" :disabled="disabled" @success="uploadSuccess" />
    <div class="global-tip">建议长宽750*200</div>
  </a-form-item>
  <div class="header-title">跳转热区</div>
  <!-- 宽度是375-左右边距 -->
  <addLink :imgUrl="data.imgUrl" :links="data.imgLinks" :width="width-data.paddingLeft-data.paddingRight" :components="components" @ok="(link)=>data.imgLinks=link">
    <a-button block>设置热区</a-button>
  </addLink>
  <div class="header-title">背景设置</div>
  <bgSet :addParams="data"></bgSet>
  <div class="header-title">组件样式</div>
  <!-- <a-form-item label="底部空白">
    <a-input-number placeholder="请输入" :precision="0" v-model:value="data.bottom" addon-after="px"></a-input-number>
  </a-form-item> -->
  <a-form-item label="高度设置">
    <a-radio-group v-model:value="data.heightSet">
      <a-radio :value="0">自适应高度</a-radio>
      <a-radio :value="1">一屏高度</a-radio>
    </a-radio-group>
  </a-form-item>
  <a-form-item label="圆角" :labelCol="{width:'50px'}">
    <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.borderRadius" addon-after="px"></a-input-number>
  </a-form-item>
  <a-form-item label="边距" :labelCol="{width:'50px'}">
    <a-space>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingTop" addon-before="上" addon-after="px"></a-input-number>
      </a-form-item>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingBottom" addon-before="下" addon-after="px"></a-input-number>
      </a-form-item>
    </a-space>
    <a-space>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingLeft" addon-before="左" addon-after="px"></a-input-number>
      </a-form-item>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingRight" addon-before="右" addon-after="px"></a-input-number>
      </a-form-item>
    </a-space>
  </a-form-item>

</template>
<script setup>
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  // 所有组件数组
  components: {
    type: Array,
    default() {
      return []
    }
  },
  // 当前组件数据
  data: {
    type: Object,
    default() {
      return {}
    }
  },
  // 组件展示的宽度
  width: {
    type: Number,
    default: 750,
  },

})

// 上传图片 视频
const uploadSuccess = async (data) => {
  let { form, path, imgUrl, imgWidth, imgHeight } = data;
  form[path] = imgUrl
  form.imgWidth = imgWidth
  form.imgHeight = imgHeight
}


// 修改颜色
const changeColor = async (key, color) => {
  props.addParams[key] = color
}

</script>

<style>
</style>
