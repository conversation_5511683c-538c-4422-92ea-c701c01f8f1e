package com.dz.ms.user.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 绑定会员手机号
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "绑定会员手机号")
public class UserMobileBindVo {

    @ApiModelProperty(value = "新手机号")
    private String newMobile;
    @ApiModelProperty(value = "来源渠道1")
    private String channelOne;
    @ApiModelProperty(value = "来源渠道2")
    private String channelTwo;
    
}
