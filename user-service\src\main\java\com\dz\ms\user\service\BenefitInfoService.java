package com.dz.ms.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.ms.user.dto.BenefitInfoDTO;
import com.dz.ms.user.entity.BenefitInfo;

import java.util.List;

/**
 * 会员权益接口
 * @author: Handy
 * @date:   2023/08/08 01:14
 */
public interface BenefitInfoService extends IService<BenefitInfo> {

	/**
     * 分页查询会员权益
     * @param param
     * @return PageInfo<BenefitInfoDTO>
     */
    public PageInfo<BenefitInfoDTO> getBenefitInfoList(BenefitInfoDTO param);

    /**
     * 查询所有会员权益
     * @param param
     * @return
     */
    public List<BenefitInfoDTO> getAllBenefitInfoList(BenefitInfoDTO param);

    /**
     * 根据ID查询会员权益
     * @param id
     * @return BenefitInfoDTO
     */
    public BenefitInfoDTO getBenefitInfoById(Long id);

    /**
     * 保存会员权益
     * @param param
     * @return Long
     */
    public Long saveBenefitInfo(BenefitInfoDTO param);

    /**
     * 根据ID删除会员权益
     * @param param
     */
    public void deleteBenefitInfoById(IdCodeDTO param);

}
