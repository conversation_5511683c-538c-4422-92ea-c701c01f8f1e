// MiKangCampaign/pages/Winning/Winning.js
const util = require("../../../utils/util.js");
import {
    getCampaignType,
    getLotteryUser,
    getReportDay7Two,
    getUserPrizes,
    getqrcode_base64,
    lotteryUserShare,
} from "../../api/index.js";
const app = getApp();
Page({
    /**
     * 页面的初始数据
     */
    data: {
        loading1: false,
        // src: "/MiKangCampaign/posterBack2.jpg",
        // srcOne: "/MiKangCampaign/posterback1.jpg",
        src: "",
        srcOne: "/MiKangCampaign/mk-share-report-test.png",
        showPopup: false, // 或得抽奖机会弹框
        isShow: false,
        showRules: false,
        isShare: 0, // 0-未分享 1-分享过1次 2-分享过2次
        rpx: app.globalData.rpx,
        CampaignData: {},
        campaignCode: wx.getStorageSync("campaignCode"),
        show: false,
        list: [
        ],
        current: 0,
        daysInfo: {},
        surplusCount: "",
        code_base64: "",
        success: false,
        // 弹框id
        modalId: 0,
        id: "",
        duration: 0
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        console.log(options, options?.id ? options.id : 0, "options");
        this.setData({
            id: options?.id ? options.id : 0,
        });
        this.getCampaignData();
        this.get7Days();
        this.getList();
        this.getData();
        // this.getCanvasImg();
    },
    // 获取活动信息
    getCampaignData() {
        getCampaignType().then((res) => {
            wx.setStorageSync("campaignCode", res.data.campaignCode);
            this.setData({
                CampaignData: res.data,
                campaignCode: res.data.campaignCode,
            });
        });
    },
    // 获取中间信息列表
    getList() {
        getUserPrizes({
            campaignCode: this.data.campaignCode,
            pageNum: 1,
            pageSize: 10,
        }).then(({ data }) => {
            console.log(data, "data 抽奖记录");
            let index =
                this.data.id != 0
                    ? data.list.findIndex(
                          (item) => item.prizesId == this.data.id
                      )
                    : this.data.id;
            this.setData({
                list: data.list,
                current: index,
            });
        });
    },
    // 获取用户抽奖信息
    getData(num) {
        getLotteryUser({
            campaignCode: this.data.campaignCode,
        }).then(({ data }) => {
            console.log("页面判断是否有剩余抽奖次数", data);
            this.setData({
                surplusCount: data.surplusCount,
                totalCount: data.totalCount,
                isShare: data.isShare,
            });
            if (num) {
                this.openShowPopup();
            }
        });
    },
    // 获取太阳码
    getCanvasImg() {
        // trial 1：体验版 3：正式版本 上线后改为正式版本
        let linColor = '{ "r": 161, "g": 135, "b": 100 }';
        getqrcode_base64({
            isHyaline: 1,
            page: "MiKangCampaign/pages/index/index",
            scene: "",
            trial: 1,
            width: 112,
            linColor: linColor,
        }).then(({ data }) => {
            // console.log(data, 'data 二维码打印');
            this.setData({
                code_base64: data,
            });
        });
    },
    // 获取第七天打卡报告信息
    get7Days() {
        getReportDay7Two({
            campaignCode: this.data.campaignCode,
        }).then(({ data }) => {
            console.log(data, "第七天打卡报告");
            this.setData({
                daysInfo: data,
            });
        });
    },
    onSwiperChange: function (e) {
        console.log("当前滑块索引:", e.detail.current);
        this.setData({
            current: e.detail.current,
        });
    },
    // 下一张
    goToNext: function () {
        this.setData({
            current: (this.data.current + 1) % this.data.list.length,
        });
    },
    // 上一张
    goToPrev: function () {
        this.setData({
            current:
                (this.data.current - 1 + this.data.list.length) %
                this.data.list.length,
        });
    },
    // 活动规则跳转
    activeRules: app.debounce(async function () {
        wx.$mp.navigateTo({
            url: "/MiKangCampaign/pages/activityRules/activityRules",
        });
    }),
    // 获得抽奖机会弹框
    openShowPopup: app.debounce(async function () {
        console.log(this.data.isShare, " this.data.isShare");
        this.setData({
            showPopup: this.data.isShare == 1,
        });
    }),
    // 关闭获得一次抽奖机会弹框
    closeShowPopup: app.debounce(async function (e) {
        this.setData({
            showPopup: false,
        });
        console.log(e, "11111111111");
        let isClick = e.detail;
        // 点击去抽奖按钮
        // if (isClick) {
        this.click();
        // }
    }),
    closePopup: app.debounce(async function () {
        this.setData({
            isShow: false,
            srcOne: "/MiKangCampaign/posterback1.jpg",
        });
    }),
    // 打开一级海报弹框
    clickPopu: app.debounce(async function () {
        wx.navigateTo({
            url: `/MiKangCampaign/pages/experience-report/experience-report?id=${this.data.id}`,
        });
        return;
        // this.openPopup()
        let { srcOne } = this.data;
        // canvas生成图片
        this.setData({
            loading1: true,
        });
        if (srcOne) {
            await this.createCanvasOne();
        }
        // 生成图片成功
        this.setData({
            loading1: false,
            isShow: true,
        });
    }),
    // 直接保存海报
    shareImg: app.debounce(async function () {
        // 保存海报
        wx.$mp.track({
            event: "prize_share_click",
        });
        // 分享图片
        let that = this;
        let { srcOne } = that.data;
        console.log(srcOne, "srcOne");
        if (srcOne.startsWith("http")) {
            wx.downloadFile({
                url: srcOne,
                success(res) {
                    console.log(res, "res 保存海报");
                    wx.saveImageToPhotosAlbum({
                        filePath: res.tempFilePath,
                        success: () => {
                            // that.success()
                            wx.showToast({
                                title: "保存成功",
                                icon: "none",
                            });
                            that.closePopup();
                        },
                        fail(err) {
                            console.log("err", err);
                            if (
                                err.errMsg ===
                                "saveImageToPhotosAlbum:fail auth deny"
                            ) {
                                // 用户拒绝了相册权限
                                wx.showModal({
                                    title: "权限提示",
                                    content: "请授权访问相册，才能保存图片",
                                    success(res) {
                                        if (res.confirm) {
                                            wx.openSetting(); // 跳转到设置界面
                                        }
                                    },
                                });
                            } else {
                                // 其他错误处理
                                wx.showToast({
                                    title: "保存失败",
                                    icon: "none",
                                });
                            }
                        },
                    });
                },
            });
        } else {
            wx.saveImageToPhotosAlbum({
                filePath: srcOne,
                success: () => {
                    console.log();
                    // that.success()
                    wx.showToast({
                        title: "保存成功",
                        icon: "none",
                    });
                    that.closePopup();
                },
                fail(err) {
                    console.log("err", err);
                    if (
                        err.errMsg === "saveImageToPhotosAlbum:fail auth deny"
                    ) {
                        // 用户拒绝了相册权限
                        wx.showModal({
                            title: "权限提示",
                            content: "请授权访问相册，才能保存图片",
                            success(res) {
                                if (res.confirm) {
                                    wx.openSetting(); // 跳转到设置界面
                                }
                            },
                        });
                    } else {
                        // 其他错误处理
                        wx.showToast({
                            title: "保存失败",
                            icon: "none",
                        });
                    }
                },
            });
        }
    }),
    // 生成一级海报图片
    createCanvasOne() {
        return new Promise((resolve, reject) => {
            this.createSelectorQuery()
                .select("#myCanvasOne")
                .fields({
                    node: true,
                    size: true,
                })
                .exec(async (res) => {
                    console.log(res, "55555555555");
                    let { node: canvas, width, height } = res[0];
                    let { userInfo, $cdn, srcOne, daysInfo } = this.data;
                    console.log(userInfo, "userInfo");
                    let { rpx } = app.globalData;
                    let dpr = 4096 / height - 0.01;
                    const ctx = canvas.getContext("2d");
                    ctx.imageSmoothingEnabled = false;
                    canvas.width = width * dpr;
                    canvas.height = height * dpr;
                    ctx.scale(dpr, dpr);
                    ctx.clearRect(0, 0, width, height);
                    // 以上代码固定
                    // 以下内容根据UI实际书写

                    ctx.fillStyle = "#ffffff";
                    ctx.fillRect(0, 0, width, height);
                    console.log(width, height);
                    ctx.fillStyle = "#000000";
                    // 绘制背景
                    let img1 = await this.loadImage(canvas, $cdn + srcOne);
                    ctx.drawImage(img1, 0, 0, 662 * rpx, 1222 * rpx);

                    // 绘制第一天照片
                    let img2 = await this.loadImage(
                        canvas,
                        daysInfo.materialUrlDay1 ||
                            wx.$config.ossImg + "/MiKangCampaign/today-icon.png"
                    );
                    let scale2 = 0;
                    let img2Width = img2.width;
                    let img2Hidth = img2.height;
                    console.log(
                        img2Width,
                        ":img2Width",
                        img2Hidth,
                        ":img2Hidth"
                    );

                    scale2 = 323 / img2Hidth;
                    let dx1 = (312 + 15 - scale2 * img2Width) / 2; //  图片偏移 x
                    ctx.drawImage(
                        img2,
                        0 * rpx,
                        0 * rpx,
                        img2Width,
                        img2Hidth,
                        dx1 * rpx,
                        95 * rpx,
                        scale2 * img2Width * rpx,
                        scale2 * img2Hidth * rpx
                    );
                    // ctx.drawImage(img2, 15 * rpx, 95 * rpx, 312 * rpx, 323 * rpx)
                    // 绘制文字
                    let daysOne = ["第", "一", "天"];
                    let height1 = 34 * rpx;
                    let y1 = 113 * rpx;
                    daysOne.forEach((item) => {
                        ctx.textBaseline = "top";
                        ctx.textAlign = "left";
                        ctx.fillStyle = "#FFFFFF";
                        ctx.font =
                            "900 " + 32 * rpx + "px SourceHanSansCN-Regular";
                        ctx.fillText(item, 31 * rpx, y1);
                        y1 += height1;
                    });

                    // 绘制第二天照片
                    let img3 = await this.loadImage(
                        canvas,
                        daysInfo.materialUrlDay7 ||
                            wx.$config.ossImg + "/MiKangCampaign/today-icon.png"
                    );
                    let scale = 0;
                    let img3Width = img3.width;
                    let img3Hidth = img3.height;

                    scale = 323 / img3Hidth;
                    let dx = (312 + 15 - scale * img3Width) / 2; //  图片偏移 x
                    ctx.drawImage(
                        img3,
                        0 * rpx,
                        0 * rpx,
                        img3Width,
                        img3Hidth,
                        dx * rpx,
                        425 * rpx,
                        scale * img3Width * rpx,
                        scale * img3Hidth * rpx
                    );
                    // ctx.drawImage(img3, 0 * rpx, 0 * rpx, img3.width, img3Hidth, dx * rpx, 425 * rpx, scale * img3.width * rpx, scale * img3Hidth * rpx, )
                    // 绘制文字
                    let daysTwo = ["第", "七", "天"];
                    let height2 = 34 * rpx;
                    let y2 = 618 * rpx;
                    daysTwo.forEach((item) => {
                        ctx.textBaseline = "top";
                        ctx.textAlign = "left";
                        ctx.fillStyle = "#FFFFFF";
                        ctx.font =
                            "900 " + 32 * rpx + "px SourceHanSansCN-Regular";
                        ctx.fillText(item, 276 * rpx, y2);
                        y2 += height2;
                    });
                    // 绘制雷达图 【保湿效果，吸收速度，持久效果，舒适度】
                    // [2,3,4,5] [daysInfo.moisturizeAvg, daysInfo.absorptionAvg, daysInfo.persistenceAvg, daysInfo.comfortAvg]
                    this.drawChartOne(ctx, [
                        daysInfo.moisturizeAvg,
                        daysInfo.absorptionAvg,
                        daysInfo.persistenceAvg,
                        daysInfo.comfortAvg,
                    ]);
                    let improve = daysInfo.improve
                        ? daysInfo.improve.split(",")
                        : [];
                    // 绘制皮肤状态 改善
                    const skin =
                        improve.length > 5 ? improve.slice(0, 5) : improve;
                    // [
                    //   '敏感现象缓解',
                    //   '皮肤更加水润',
                    //   '暗沉改善',
                    //   '毛孔变得细腻',
                    //   '光泽度提升'
                    // ]
                    this.drawSkinConditionOne(ctx, skin);

                    wx.canvasToTempFilePath({
                        x: 0,
                        y: 0,
                        width: width,
                        height: height,
                        destWidth: width * 4,
                        destHeight: height * 4,
                        canvas: canvas,
                        success: (res) => {
                            this.setData({
                                srcOne: res.tempFilePath,
                            });
                            resolve();
                        },
                        fail(e) {
                            console.log(e, "失败");
                            reject();
                        },
                    });
                });
        });
    },
    // 绘制雷达图 values按照labels的顺序传值
    drawChartOne(ctx, values) {
        const { rpx } = app.globalData;
        const angle = (Math.PI * 2) / 4; // 每个标签的角度
        const labels = [
            {
                text: "保湿效果",
                x: 0,
                y: -135 * rpx,
            },
            {
                text: "吸收速度",
                x: 135 * rpx,
                y: -14 * rpx,
            },
            {
                text: "持久效果",
                x: 0,
                y: 95 * rpx,
            },
            {
                text: "舒适度",
                x: -125 * rpx,
                y: -14 * rpx,
            },
        ];

        ctx.save();
        ctx.translate(450 * rpx, 1019 * rpx); // 移动热力图中心

        // 绘制边框
        ctx.strokeStyle = "#C8B49A";
        ctx.lineWidth = 1 * rpx;
        ctx.beginPath();
        ctx.arc(0, 0, 44.5, 0, Math.PI * 2);
        ctx.stroke();
        // ctx.lineDashOffset = 0
        ctx.setLineDash([10 * rpx, 12 * rpx]);
        [18, 25, 33.25].forEach((r) => {
            ctx.beginPath();
            ctx.arc(0, 0, r, 0, Math.PI * 2);
            ctx.stroke();
        });

        // 绘制文字
        ctx.fillStyle = "#3D3D3D";
        ctx.font = `500 ${20 * rpx}px/1  sans-serif`;
        ctx.textBaseline = "top";
        ctx.textAlign = "center";
        labels.forEach((item, index) => {
            const { text, x, y } = item;
            ctx.fillText(text, x, y);
            ctx.fillText(values[index].toFixed(1), x, y + 22 * rpx);
        });

        // 绘制区域
        ctx.beginPath();
        const maxValue = Math.max(...values); // 获取最大值，用于定义网格的半径
        values.forEach((value, index) => {
            const radius = 81 * rpx * (value / 5); // 根据值计算半径
            const x = radius * Math.cos(index * angle - Math.PI / 2);
            const y = radius * Math.sin(index * angle - Math.PI / 2);

            if (index === 0) {
                ctx.moveTo(x, y); // 第一个点
            } else {
                ctx.lineTo(x, y);
            }
        });
        ctx.closePath();
        ctx.fillStyle = "rgba(200,180,154,0.8)"; // 区域颜色
        ctx.fill();

        ctx.restore();
    },
    // 绘制皮肤状态
    drawSkinConditionOne(ctx, list) {
        const { rpx } = app.globalData;
        const containerHeight = 274 * rpx;
        const rectHeight = 32 * rpx;
        const rectSpacing = 32 * rpx;
        const x = list.length;
        // 计算标签区域的总高度（标签加上间隔）
        const totalHeight = x * rectHeight + (x - 1) * rectSpacing;
        // 如果标签总高度小于容器的高度，则需要分配额外的空白间隔，保持标签纵向均匀分布
        const topOffset = (containerHeight - totalHeight) / 2;

        ctx.save();
        ctx.translate(42 * rpx, 874 * rpx); // 移动热力图中心

        // 绘制标签
        ctx.lineCap = "round";
        ctx.lineWidth = rectHeight;
        ctx.fillStyle = "#fff";
        ctx.strokeStyle = "#C8B49A";
        // 字体
        ctx.font = `500 ${21 * rpx}px/${23 * rpx}  sans-serif`;
        ctx.textBaseline = "top";
        const padding = 15 * rpx;
        for (let i = 0; i < x; i++) {
            const y = topOffset + i * (rectHeight + rectSpacing); // 每个标签的起始y位置
            const text = list[i];
            const textW = ctx.measureText(text).width;

            ctx.beginPath();
            ctx.moveTo(0, y + rectHeight / 4);
            ctx.lineTo(textW + padding * 2, y + rectHeight / 4);
            ctx.stroke();

            ctx.fillText(text, padding, y);
        }

        ctx.restore();
    },
    // 分享
    share: app.debounce(async function () {
        let that = this;
        let { src } = this.data;
        // canvas生成图片
        this.setData({
            loading1: true,
        });
        console.log(src, "99999");
        if (src) {
            await this.createCanvas();
        }
        // 生成图片成功
        this.setData({
            loading1: false,
            show: true,
        });
        this.closePopup();
    }),
    // 跳转到抽奖列表
    click: app.debounce(async function () {
        wx.redirectTo({
            url: `/MiKangCampaign/pages/prizeDraw/prizeDraw`,
        });
    }),
    goLottery: app.debounce(async function () {
        this.click();
    }),
    // canvas加载图片
    loadImage(canvas, src) {
        let that = this;
        return new Promise((resolve) => {
            let img1 = canvas.createImage(img1);
            img1.src = src;
            img1.onload = function () {
                resolve(img1);
                console.log(src, "图片成功");
            };
            img1.onerror = function (e) {
                console.log(src);
                wx.showToast({
                    title: "图片加载失败",
                    icon: "none",
                });
                console.log("图片加载失败");
                that.setData({
                    loading1: false,
                });
            };
        });
    },
    // 头像为圆形的
    circleImg(ctx, img, x, y, r) {
        ctx.save();
        ctx.beginPath();
        var d = 2 * r;
        var cx = x + r;
        var cy = y + r;
        ctx.arc(cx, cy, r, 0, 2 * Math.PI);
        ctx.clip();
        ctx.drawImage(img, x, y, d, d);
        ctx.restore();
    },
    // 生成海报图片
    createCanvas() {
        return new Promise((resolve, reject) => {
            this.createSelectorQuery()
                .select("#myCanvas")
                .fields({
                    node: true,
                    size: true,
                })
                .exec(async (res) => {
                    console.log(res, "55555555555");
                    let { node: canvas, width, height } = res[0];
                    let { userInfo, $cdn, src, daysInfo, code_base64 } =
                        this.data;
                    console.log(userInfo, "userInfo");
                    let { rpx } = app.globalData;
                    let dpr = 4096 / height - 0.01;
                    const ctx = canvas.getContext("2d");
                    canvas.width = width * dpr;
                    canvas.height = height * dpr;
                    ctx.scale(dpr, dpr);
                    ctx.clearRect(0, 0, width, height);
                    // 以上代码固定
                    // 以下内容根据UI实际书写

                    ctx.fillStyle = "#ffffff";
                    ctx.fillRect(0, 0, width, height);
                    console.log(width, height);
                    ctx.fillStyle = "#000000";
                    // 绘制背景
                    let img1 = await this.loadImage(canvas, $cdn + src);
                    ctx.drawImage(img1, 0, 0, 497 * rpx, 1045 * rpx);

                    // 绘制第一天照片
                    let img2 = await this.loadImage(
                        canvas,
                        daysInfo.materialUrlDay1 ||
                            wx.$config.ossImg + "/MiKangCampaign/today-icon.png"
                    );
                    let scale2 = 0;
                    let img2Width = img2.width;
                    let img2Hidth = img2.height;

                    scale2 = 242 / img2Hidth;
                    let dx = (234 + 11 - scale2 * img2Width) / 2; //  图片偏移 x
                    ctx.drawImage(
                        img2,
                        0 * rpx,
                        0 * rpx,
                        img2Width,
                        img2Hidth,
                        dx * rpx,
                        71 * rpx,
                        scale2 * img2Width * rpx,
                        scale2 * img2Hidth * rpx
                    );

                    // ctx.drawImage(img2, 11 * rpx, 71 * rpx, 234 * rpx, 242 * rpx)
                    // 绘制文字
                    let daysOne = ["第", "一", "天"];
                    let height1 = 26 * rpx;
                    let y1 = 85 * rpx;
                    daysOne.forEach((item) => {
                        ctx.textBaseline = "top";
                        ctx.textAlign = "left";
                        ctx.fillStyle = "#FFFFFF";
                        ctx.font =
                            "900 " + 24 * rpx + "px SourceHanSansCN-Regular";
                        ctx.fillText(item, 23 * rpx, y1);
                        y1 += height1;
                    });

                    // 绘制第二天照片
                    let img3 = await this.loadImage(
                        canvas,
                        daysInfo.materialUrlDay7 ||
                            wx.$config.ossImg + "/MiKangCampaign/today-icon.png"
                    );
                    let scale = 0;
                    let img3Width = img3.width;
                    let img3Hidth = img3.height;

                    scale = 242 / img3Hidth;
                    let dx1 = (234 + 11 - scale * img3Width) / 2;
                    //  图片偏移 x
                    ctx.drawImage(
                        img3,
                        0 * rpx,
                        0 * rpx,
                        img3Width,
                        img3Hidth,
                        dx1 * rpx,
                        319 * rpx,
                        scale * img3Width * rpx,
                        scale * img3Hidth * rpx
                    );

                    // ctx.drawImage(img3, 11 * rpx, 319 * rpx, 234 * rpx, 242 * rpx)
                    // 绘制文字
                    let daysTwo = ["第", "七", "天"];
                    let height2 = 26 * rpx;
                    let y2 = 463 * rpx;
                    daysTwo.forEach((item) => {
                        ctx.textBaseline = "top";
                        ctx.textAlign = "left";
                        ctx.fillStyle = "#FFFFFF";
                        ctx.font =
                            "900 " + 24 * rpx + "px SourceHanSansCN-Regular";
                        ctx.fillText(item, 207 * rpx, y2);
                        y2 += height2;
                    });
                    // 绘制雷达图 【保湿效果，吸收速度，持久效果，舒适度】
                    //  [2, 3, 4, 5] [daysInfo.moisturizeAvg, daysInfo.absorptionAvg, daysInfo.persistenceAvg, daysInfo.comfortAvg]
                    this.drawChart(ctx, [
                        daysInfo.moisturizeAvg,
                        daysInfo.absorptionAvg,
                        daysInfo.persistenceAvg,
                        daysInfo.comfortAvg,
                    ]);
                    let improve = daysInfo.improve
                        ? daysInfo.improve.split(",")
                        : [];
                    // 绘制皮肤状态 改善
                    const skin =
                        improve.length > 5 ? improve.slice(0, 5) : improve;
                    this.drawSkinCondition(ctx, skin);

                    // 绘制头像 需要有默认值
                    // let img4 = await this.loadImage(
                    //     canvas,
                    //     userInfo.avatar ||
                    //         this.data.$cdn + "/MiKangCampaign/avatarIcon.png"
                    // );
                    // // 圆形头像
                    // this.circleImg(ctx, img4, 30 * rpx, 928 * rpx, 40 * rpx);
                    // // 用户名
                    // ctx.textBaseline = "top";
                    // ctx.textAlign = "left";
                    // ctx.fillStyle = "#3C3C43";
                    // ctx.font = "500 " + 22 * rpx + "px SourceHanSansCN-Medium";
                    // ctx.fillText(userInfo.username, 122 * rpx, 970 * rpx);
                    // 绘制二维码 需要有默认值
                    // console.log(code_base64, 'code_base64');
                    let img5 = await this.loadImage(
                        canvas,
                        "data:image/png;base64," + code_base64
                    );
                    ctx.drawImage(
                        img5,
                        386 * rpx,
                        909 * rpx,
                        80 * rpx,
                        80 * rpx
                    );

                    wx.canvasToTempFilePath({
                        x: 0,
                        y: 0,
                        width: width,
                        height: height,
                        destWidth: width * 4,
                        destHeight: height * 4,
                        canvas: canvas,
                        success: (res) => {
                            this.setData({
                                src: res.tempFilePath,
                            });
                            resolve();
                        },
                        fail(e) {
                            console.log(e, "失败");
                            reject();
                        },
                    });
                });
        });
    },
    // 绘制雷达图 values按照labels的顺序传值
    drawChart(ctx, values) {
        const { rpx } = app.globalData;
        const angle = (Math.PI * 2) / 4; // 每个标签的角度
        const labels = [
            {
                text: "保湿效果",
                x: 0,
                y: -102 * rpx,
            },
            {
                text: "吸收速度",
                x: 100 * rpx,
                y: -14 * rpx,
            },
            {
                text: "持久效果",
                x: 0,
                y: 70 * rpx,
            },
            {
                text: "舒适度",
                x: -94 * rpx,
                y: -14 * rpx,
            },
        ];

        ctx.save();
        ctx.translate(337 * rpx, 761 * rpx); // 移动热力图中心

        // 绘制边框
        ctx.strokeStyle = "#C8B49A";
        ctx.lineWidth = 1 * rpx;
        ctx.beginPath();
        ctx.arc(0, 0, 32, 0, Math.PI * 2);
        ctx.stroke();
        // ctx.lineDashOffset = 0
        ctx.setLineDash([10 * rpx, 12 * rpx]);
        [9, 18, 25].forEach((r) => {
            ctx.beginPath();
            ctx.arc(0, 0, r, 0, Math.PI * 2);
            ctx.stroke();
        });

        // 绘制文字
        ctx.fillStyle = "#3D3D3D";
        ctx.font = `500 ${15.5 * rpx}px/1  sans-serif`;
        ctx.textBaseline = "top";
        ctx.textAlign = "center";
        labels.forEach((item, index) => {
            const { text, x, y } = item;
            ctx.fillText(text, x, y);
            ctx.fillText(values[index]?.toFixed(1), x, y + 22 * rpx);
        });

        // 绘制区域
        ctx.beginPath();
        const maxValue = Math.max(...values); // 获取最大值，用于定义网格的半径
        values.forEach((value, index) => {
            const radius = 64 * rpx * (value / 5); // 根据值计算半径
            const x = radius * Math.cos(index * angle - Math.PI / 2);
            const y = radius * Math.sin(index * angle - Math.PI / 2);

            if (index === 0) {
                ctx.moveTo(x, y); // 第一个点
            } else {
                ctx.lineTo(x, y);
            }
        });
        ctx.closePath();
        ctx.fillStyle = "rgba(200,180,154,0.8)"; // 区域颜色
        ctx.fill();

        ctx.restore();
    },
    // 绘制皮肤状态
    drawSkinCondition(ctx, list) {
        const { rpx } = app.globalData;
        const containerHeight = 208 * rpx;
        const rectHeight = 24 * rpx;
        const rectSpacing = 22 * rpx;
        const x = list.length;
        // 计算标签区域的总高度（标签加上间隔）
        const totalHeight = x * rectHeight + (x - 1) * rectSpacing;
        // 如果标签总高度小于容器的高度，则需要分配额外的空白间隔，保持标签纵向均匀分布
        const topOffset = (containerHeight - totalHeight) / 2;

        ctx.save();
        ctx.translate(32 * rpx, 654 * rpx); // 移动热力图中心

        // 绘制标签
        ctx.lineCap = "round";
        ctx.lineWidth = rectHeight;
        ctx.fillStyle = "#fff";
        ctx.strokeStyle = "#C8B49A";
        ctx.font = `500 ${15.9 * rpx}px/${23 * rpx}  sans-serif`;
        ctx.textBaseline = "top";
        const padding = 5 * rpx;
        for (let i = 0; i < x; i++) {
            const y = topOffset + i * (rectHeight + rectSpacing); // 每个标签的起始y位置
            const text = list[i];
            const textW = ctx.measureText(text).width;

            ctx.beginPath();
            ctx.moveTo(0, y + rectHeight / 4);
            ctx.lineTo(textW + padding * 2, y + rectHeight / 4);
            ctx.stroke();

            ctx.fillText(text, padding, y);
        }

        ctx.restore();
    },
    // 海报关闭
    close() {
        this.setData({
            show: false,
            src: "/MiKangCampaign/posterBack2.jpg",
        });
    },
    // 海报操作成功后(下载图片成功、分享成功)的逻辑
    success() {
        // 分享抽奖 判断是否分享过 没有分享过走接口 已经分享过走普通分享
        if (!this.data.isShare) {
            // 分享报告获得抽奖
            wx.$mp.track({
                event: "prize_share_lucky_click",
            });
            lotteryUserShare({
                code: this.data.campaignCode,
            }).then((res) => {
                wx.showToast({
                    title: "分享成功",
                    icon: "none",
                });
                this.close();
                this.get7Days();
                this.getList();
                this.getData(1);
            });
        }
        console.log("分享、下载海报、收藏成功了");
    },
    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {
      this.setData({
        duration: 500
      })
    },

    /**
     * 生命周期函数--监听页面显示
     */
    onisShow() {},
    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {},

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {},

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {},
    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {},
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {
        let that = this;
        // 分享抽奖 判断是否分享过 没有分享过走接口 已经分享过走普通分享
        // if (!this.data.isShare) {
        //   lotteryUserShare({
        //     code: this.data.campaignCode,
        //   }).then(res => {
        //     wx.showToast({
        //       title: '分享成功',
        //       icon: 'none',
        //     });
        //     this.get7Days()
        //     this.getList()
        //     this.getData()
        //     this.openShowPopup()
        //   })
        // }
        return {
            title: "和我一起体验「米糠护肤」开启五天打卡活动",
            imageUrl: wx.$config.ossImg + "/MiKangCampaign/mk-share-card-1.png",
            path: "/MiKangCampaign/pages/index/index",
        };
    },
    // 跳转到我的礼券
    goMyCoupon: app.debounce(async function () {
        wx.$mp.navigateTo({
            url: `/pages/myCoupon/myCoupon`,
        });
    }),
    // 跳转到积分商城
    goMylife: app.debounce(async function () {
        console.log(3333333);
        wx.$mp.switchTab({
            url: `/pages/life/life`,
        });
    }),
    // 跳转到积分商城
    goIndex: app.debounce(async function () {
      wx.redirectTo({
        url: "/MiKangCampaign/pages/clock/clock",
      });
    }),
});
