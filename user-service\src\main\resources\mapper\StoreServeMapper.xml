<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.user.mapper.StoreServeMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    id,
  	    store_id,
  	    serve_id,
  	    tenant_id,
  	    created,
  	    creator,
  	    modified,
  	    modifier
    </sql>

    <!-- 查询示例 -->
    <select id="selectDemo" parameterType="java.lang.Long" resultType="com.dz.ms.user.entity.StoreServe">
        select
        <include refid="Base_Column_List" />
        from store_serve
        where id = #{id,jdbcType=BIGINT}
        AND tenant_id = #{tenantId}
    </select>
    <select id="selectStoreIds" resultType="java.lang.Long">
        select store_id from store_serve group by store_id
    </select>
    <select id="selectStoreIdsByServeIds" resultType="java.lang.Long">
        select store_id from store_serve where serve_id in
        <foreach collection="serveIds" item="serveId" open="(" separator="," close=")">
            #{serveId}
        </foreach>
        group by store_id
    </select>
    <select id="selectOkServe" resultType="java.lang.Long">
        select id from serve where status = 0
    </select>

</mapper>
