<template>

  <layout>
    <template v-slot:header>
      <searchForm :formParams="formParams" @cancel="resetData" @ok="refreshData" :disabled="!$hasPermission('giftOrder:search')">
        <a-form-item name="orderCode">
          <a-input placeholder="订单编号" allow-clear v-model:value="formParams.orderCode" allowClear @keyup.enter="refreshData"></a-input>
        </a-form-item>
        <a-form-item name="userName">
          <a-input placeholder="用户昵称" allow-clear v-model:value="formParams.userName" allowClear @keyup.enter="refreshData"></a-input>
        </a-form-item>
        <a-form-item name="userCrmCode">
          <a-input placeholder="用户ID" allow-clear v-model:value="formParams.userCrmCode" allowClear @keyup.enter="refreshData"></a-input>
        </a-form-item>
        <a-form-item name="createTime" label="兑换时间">
          <a-range-picker v-model:value="formParams.createTime" :placeholder="['开始时间', '结束时间']" :presets="$rangePresets" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
        </a-form-item>
        <a-form-item name="orderStatus">
          <a-select ref="select" v-model:value="formParams.orderStatus" allowClear :disabled="disabled" :options="orderStatusOptions" :fieldNames="{ label: 'label', value: 'value' }" optionFilterProp="label" showSearch placeholder="订单状态"></a-select>
        </a-form-item>
        <a-form-item name="expressStatus">
          <a-select ref="select" v-model:value="formParams.expressStatus" allowClear :disabled="disabled" :options="expressStatusOptions" :fieldNames="{ label: 'label', value: 'value' }" optionFilterProp="label" showSearch placeholder="发货状态"></a-select>
        </a-form-item>
      </searchForm>
    </template>
    <template v-slot:topRight>
      <a-space>
        <!-- :disabled="!$hasPermission('open:leaveUser:synchLeaveUser')" 权限标识 -->

        <uploadFile type="primary" :api="exchange_orderimport_delivery" @ok="crowdUploadSuccess" :disabled="!$hasPermission('giftOrder:bulk:delivery')">批量发货
        </uploadFile>
        <downloadFile type="primary" file-name="批量发货模版" file-type="excel" :api="exchange_ordertemplate" :disabled="!$hasPermission('giftOrder:deliver:goods')">批量发货模版
        </downloadFile>

        <a-button type="primary" :disabled="!$hasPermission('giftOrder:derive')" @click="excelOrder">导出订单数据</a-button>
      </a-space>

    </template>
    <template v-slot="{ height }">
      <a-table :indentSize="20" row-key="id" :scroll="{ scrollToFirstRowOnChange: true, y: height - 88, x: '100%' }" :dataSource="dataSource" :columns="tableHeader" :pagination="pagination" :loading="loading" @change="loadData">
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'index'">
            {{ (current - 1) * pageSize + 1 + index }}
          </template>

          <template v-if="column.key === 'action'">
            <a-button type="link" :disabled="!$hasPermission('giftOrder:goods')||record.expressStatus== 1" @click="EditRole(record)">发货</a-button>

          </template>

        </template>
      </a-table>
    </template>

  </layout>
  <addOrder :visible="visible" @ok="updateList" @cancel="visible = false" :id="id" :type="type"></addOrder>
</template>
<script setup>
import addOrder from './components/addOrder.vue'
import { useRoute, useRouter } from 'vue-router'
import { usePagination, useRequest, } from 'vue-request';
import { orderexchange_orderlist, downloadTask, exchange_ordertemplate, exchange_orderimport_delivery } from '@/http/index.js'
import { message, Modal } from "ant-design-vue";
import { expressStatusOptions, orderStatusOptions } from '@/utils/dict-options'
import { cloneDeep } from "lodash";
// 分页参数
let pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ["bottomLeft"],
  };
});
// 表格分页数据获取
const total = ref(0)


const { formParams, tableHeader, visible, id, type, } = toRefs(reactive({


  id: '',
  type: 0,// 0-新增 1-编辑  2-查看
  visible: false,

  formParams: {
    userName: '',
    orderCode: ''
  },

  tableHeader: [{
    title: '序号',
    key: 'index',
    align: 'center',
    width: 80
  },
  {
    title: '订单编号',
    dataIndex: 'orderCode',
    width: 180,
    align: 'center',
  },
  {
    title: '兑换时间',
    dataIndex: 'created',
    align: 'center',
    ellipsis: true
  },
  {
    title: '用户昵称',
    dataIndex: 'userName',
    align: 'center',
    ellipsis: true
  },
  {
    title: '用户ID',
    dataIndex: 'userCrmCode',
    align: 'center',
    ellipsis: true
  },
  {
    title: '订单兑换状态',
    dataIndex: 'orderStatus',
    align: 'center',
    ellipsis: true,
    customRender: (row) => {
      // console.log(row);
      let textLabel = null
      if (typeof row.text == 'number') {
        textLabel = orderStatusOptions.find(item => item.value == row.text)
      }
      console.log(textLabel);
      return textLabel && textLabel.label || '--'; // 如果数据为空，则显示 '----' textLabel.label ||
    }
  },
  {
    title: '发货状态',
    dataIndex: 'expressStatus',
    align: 'center',
    ellipsis: true,
    customRender: (row) => {
      // console.log(row);
      let textLabel = null
      if (typeof row.text == 'number') {
        textLabel = expressStatusOptions.find(item => item.value == row.text)
      }
      console.log(textLabel);
      return textLabel && textLabel.label || '--'; // 如果数据为空，则显示 '----' textLabel.label ||
    }
  },
  {
    title: '商品数量',
    dataIndex: 'number',
    align: 'center',
    ellipsis: true
  },
  {
    title: '订单价值(积分)',
    dataIndex: 'orderPoint',
    align: 'center',
    ellipsis: true
  },

  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    align: 'center',
    width: 150,
    fixed: 'right'
  }]
})
);
const getParams = () => {
  // 拷贝一份 然后处理数据
  let params = cloneDeep(formParams.value);
  params.createdStart = params.createTime
    ? params.createTime[0] + " 00:00:00"
    : null;
  params.createdEnd = params.createTime
    ? params.createTime[1] + " 23:59:59"
    : null;
  delete params.createTime;
  return params;
};
const { data: dataSource, run, loading, pageSize, current, refresh } = usePagination((param) => {
  // 这里修改参数
  // return resignationInheritanceList({ ...param, ...getParams() })
  return orderexchange_orderlist({ ...param, ...getParams() })
},
  {
    manual: false, // 修改为false,让其自动执行
    pagination: {
      currentKey: 'pageNum',  // 通过该值指定接口 当前页数 参数的属性值
      pageSizeKey: 'pageSize',// 通过该值指定接口 每页获取条数 参数的属性值
      totalKey: 'data.data.count',  // 指定 data 中 total 属性的路径
    },
    formatResult: res => { // 返回数据格式化
      total.value = res.data.count
      return res.data.list
    }
  });

function updateList(value) {
  visible.value = false;
  // 新增 重置搜索数据
  if (!value) {
    resetData();
  } else {
    // 查看、编辑 不需要清空搜索数据 直接刷新
    refresh();
  }
}
function EditRole(record) {
  visible.value = true
  type.value = 1
  id.value = record.orderCode
  console.log(record, id.value);

}

// 加载数据
const loadData = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}
function excelOrder() {
  downloadTask({ type: 4, fileName: '订单列表', param: { ...getParams() } })
}

// 刷新数据
const refreshData = () => {
  run({ pageNum: 1, pageSize: pageSize.value })
};

// 重置数据
const resetData = () => {
  formParams.value = {
    orderCode: '',
    userName: ''
  }
  refreshData()
}
function addChang() {
  visible.value = true
  type.value = 0
  id.value = ''
}

function crowdUploadSuccess() {
  loadData()
}

</script>
