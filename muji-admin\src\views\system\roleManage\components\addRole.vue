<template>
  <a-drawer :title="title" width="800" placement="right" :closable="true" :maskClosable="false" :open="open" @close="onClose">
    <a-spin :spinning="loading">

      <a-form class="form" ref="addForm" :model="addParams" :rules="rules" :labelCol="{ style: 'width:100px' }">

        <a-form-item label="角色名称" name="roleName">
          <a-input placeholder="请输入" style="width:400px;" v-model:value="addParams.roleName" show-count :maxlength="10" />
        </a-form-item>
        <a-form-item label="角色描述" name="roleDesc">
          <a-textarea placeholder="请输入" style="width:400px;" v-model:value="addParams.roleDesc" show-count :maxlength="30" />
        </a-form-item>
        <a-form-item label="后台权限信息" name="permitIds">

          <a-tree checkable checked :checked-keys="addParams.permitIds" @check="oncheck" :tree-data="permitionList" :field-names="{
                            children: 'subList',
                            title: 'permitName',
                            key: 'id',
                        }">
            <template #title="{ permitName, code }">
              {{ permitName }}（{{ code }}）<copy-outlined class="copy" :data-clipboard-text="'!$hasPermission(\'' + code + '\')'" @click="copy" />
            </template>
          </a-tree>

        </a-form-item>

      </a-form>
      <!--  -->
    </a-spin>
    <template #footer v-if="!disabled">
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="ok" :loading="loading">确定</a-button>
      </a-space>
    </template>

  </a-drawer>
</template>
<script setup>
import { permissionTree, roleSave, roleInfo, roleUpdate } from '@/http/index.js'
import { useGlobalStore } from '@/store'
import { message, Modal } from "ant-design-vue";
import Clipboard from 'clipboard';
const $router = useRouter();
const global = useGlobalStore()
const addForm = ref(null)
import { cloneDeep } from 'lodash'
import { onMounted } from 'vue';
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  // 操作抽屉打开关闭
  visible: {
    type: Boolean,
    default: false
  },
  // 数据ID
  id: {
    type: String || Number,
    default: ''
  },
  // 操作类型
  type: {
    type: Number,
    default: 0, // 0-新增 1-编辑  2-查看
  }

})
// 置灰
const disabled = computed(() => {
  return props.type == 2
})

// 标题
const title = computed(() => {
  return ['新增', '编辑', '查看'][props.type] + '角色'
})

const { open, addParams, rules, loading, permitionList } = toRefs(reactive({
  open: props.visible,
  permitionList: [],

  loading: false,

  addParams: {
    roleName: '',
    permitIds: [],
    roleDesc: '',
  },
  rules: {
    roleName: [{ required: true, message: '请输入角色名称', trigger: ['blur', 'change'] }],
    permitIds: [{ type: 'array', required: true, message: '请选择页面权限', trigger: ['blur', 'change'] }],
  }
})
);
// onMounted(() => {
//     initData()
// })
watch(() => props.visible, (value) => {
  // 弹窗操作
  open.value = props.visible
  addForm.value?.resetFields()

  if (open.value) {

    initData()
  }
})
const oncheck = async (selectedKeys) => {
  addParams.value.permitIds = selectedKeys;
  await nextTick();
  addForm.value.validateFields(['permitIds'])
}

//所有接口调取出
const initData = async () => {
  const promiseArr = []

  promiseArr.push(permissionTree())
  if (props.id) {

    promiseArr.push(roleInfo({ id: props.id }))
  }

  // 如果有新的接口就在这里push
  loading.value = true
  try {
    const [preTree, roleInfoA] = await Promise.all(promiseArr)
    // console.log(roleInfoA);
    permitionList.value = []
    permitionList.value = preTree.data
    if (roleInfoA) {
      addParams.value = {
        id: roleInfoA.data.id,
        roleName: roleInfoA.data.roleName,
        roleDesc: roleInfoA.data.roleDesc,
        permitIds: roleInfoA.data.permitIds,
      }
    }
    loading.value = false
  } catch (error) {
    loading.value = false
    console.error('获取数据失败:', error)
  }
}

const copy = () => {
  var clipboard = new Clipboard('.copy')
  clipboard.on('success', e => {
    message.success('复制成功')
    // console.log('复制成功')
    // 释放内存
    clipboard.destroy()
  })
  clipboard.on('error', e => {
    // 不支持复制
    // console.log('该浏览器不支持自动复制')
    // 释放内存
    clipboard.destroy()
  })
}




// 关闭弹窗
const onClose = () => {
  emit('cancel')
}

// 确定
const ok = () => {

  addForm.value.validate().then(res => {

    let params = cloneDeep(addParams.value)


    loading.value = true
    if (props.id) {
      console.log('编辑');
      roleUpdate(params).then(res => {
        message.success(res.msg);
        emit('ok', props.id)
      }).finally(() => {
        loading.value = false
      })
    } else {
      roleSave(params).then(res => {
        message.success(res.msg);
        emit('ok', props.id)
      }).finally(() => {
        loading.value = false
      })
    }

  })
}



</script>

<style scoped lang="scss">
:deep(.searchForm .ant-form-item) {
  margin-bottom: 16px;
}

:deep(.anticon-eye-invisible) {
  display: none;
}

.member-role {
  background: #f2f2f2;
  margin-bottom: 20px;
  padding: 5px 10px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &:nth-child(odd) {
    margin-right: 35px;
  }
}
</style>
