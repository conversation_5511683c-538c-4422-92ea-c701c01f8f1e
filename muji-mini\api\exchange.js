// 当前登录用户里程流水
exports.mileageRecords = (data) =>
  wx.$request({
    url: "/app/user/user/my/mileage/records",
    data,
    method: "post",
  });
// 权益记录
exports.rightsreceiverecord = (data) =>
  wx.$request({
    url: "/app/user/rights/receive/record",
    data,
    method: "post",
  });
// 我的礼券列表未使用&待领取
exports.usercouponlist = (data) =>
  wx.$request({
    url: "/app/user/coupon/list",
    data,
    method: "post",
  });

//积分流水列表
exports.mypointsrecords = (data) =>
  wx.$request({
    url: "/app/user/user/my/points/records",
    data,
    method: "post",
  });
//历史记录礼券
exports.couponexlistpire = (data) =>
  wx.$request({
    url: "/app/user/coupon/list/expire",
    data,
    method: "post",
  });
//   礼券详情
exports.usercoupondetail = (data) =>
  wx.$request({
    url: "/app/user/coupon/detail",
    data,
    method: "get",
  });
// /app/user/coupon/receive 我的礼券领取并使用
exports.usercouponreceive = (data) =>
  wx.$request({
    url: "/app/user/coupon/receive",
    data,
    method: "post",
    timeout: 40 * 1000,
  });
