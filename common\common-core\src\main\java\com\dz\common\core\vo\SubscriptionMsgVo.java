package com.dz.common.core.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 订阅消息入参
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SubscriptionMsgVo
{
    @ApiModelProperty(value = "expireCoupon（优惠券过期通知）memberReceive（会员权益领取通知）taskEnd（活动即将结束提醒）expirePoints(积分过期提醒)getPoints(积分到账提醒)")
    private List<String> msgCode;
    private String sendDesc;
}
