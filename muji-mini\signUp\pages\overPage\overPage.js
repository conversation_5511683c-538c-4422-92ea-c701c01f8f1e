// signUp/pages/overPage/overPage.js
const app = getApp()
import {
  getCampaignType,
  getEnrollInfo
} from '../../api/index.js'
import {
  getSubscribeByScene,
} from '../../../api/index.js';
Page({

  /**
   * 页面的初始数据
   */
  data: {
    type: "", // 报名结束：zhaomuOver 打卡结束：signUpOver 活动结束：activeOver
    BMNum: "", // 判断是第一次报名还是第二次报名
    isClu: '',
    CampaignData: {},
    status: '',
    campaignCode: "",
    showContact1: false, // 订阅消息提示弹框
    round: '', // 第几轮报名 轮次
    statusBarHeight: app.globalData.statusBarHeight,
    navBarHeight: app.globalData.navBarHeight,
    EnrollInfo: {},
    EnrollStatus: '', // 报名状态 1：没有报名信息 2 报名未中奖
    activeTitle: '',
    disabled: false,
    str: 'enroll_fail'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log(options, 'options');
    this.setData({
      isClu: ['zhaomuOver', "zhaomuNot"].includes(options.type) ? `${this.data.$cdn}/signUp/signUpback2.jpg` : `${this.data.$cdn}/signUp/signUpback.png`,
      type: options.type,
      BMNum: options.BMNum,
      status: options.status,
    })
    console.log(['zhaomuOver', "zhaomuNot"].includes(options.type) ? 'signUpback2.jpg' : 'signUpback.png', this.data.isClu, 'isClu');
    this.getData()
    this.getSubNum()
  },
  // 查询订阅次数
  getSubNum() {
    getSubscribeByScene({
      scene: this.data.str
    }).then(res => {
      console.log(res.data, '订阅信息');
      console.log(this.data.userSignInfo, '用户报名信息');
      let disabled = false
      // 有小于1的  就需要订阅
      disabled = res.data.some(item => item.subNumber < 1)
      console.log(disabled, 'disabled');
      this.setData({
        disabled
      })
    })
  },
  getData() {
    getCampaignType().then(res => {
      wx.setStorageSync('campaignCode', res.data.campaignCode)
      res.data.campaignStartTime = this.getDate(res.data.campaignStartTime)
      res.data.campaignEndTime = this.getDate(res.data.campaignEndTime)
      this.setData({
        CampaignData: res.data,
        campaignCode: res.data.campaignCode,
        activeTitle: `在${res.data.nextCampaignTime || ''}\n期间参与第二轮体验官招募`
      })
      // 如果返回报名活动
      if (res.data.campaignCode.indexOf('phase') > -1) {
        // 查看报名信息
        getEnrollInfo({
          campaignCode: this.data.campaignCode
        }).then(({
          data: EnrollInfo
        }) => {
          console.log(!EnrollInfo, '!!EnrollInfo');
          let round = this.data.BMNum != 'null' ? this.data.BMNum : res.data.campaignCode.indexOf('1') > -1 ? 1 : 2
          this.setData({
            EnrollInfo,
            EnrollStatus: !EnrollInfo ? 1 : 2,
            // 判断是第一轮报名还是第二轮报名
            round: round,
            str: round == 1 ? 'enroll_fail' : 'new_campaign'

          })
          console.log(this.data.EnrollInfo, 'EnrollInfo 报名信息');
          console.log(this.data.round, 'round 报名信息');
        })
      }

    })
  },
  // 订阅
  //第一批报名体验官未成功页-订阅报名提醒： recruit_failed_recruit Subscription_click
  //第一批报名时间结束页-订阅报名提醒： recruit_recruit_Subscription_click
  //第二批报名体验官未成功页-订阅新活动提醒： recruit_failed2_new_Subscription_click
  submit: app.debounce(async function (e) {
    let {
      str
    } = this.data
    let {
      BMNum,
      status
    } = this.data
    let trackStr = ''
    switch (str) {
      case "enroll_fail":
        //报名信息未中奖
        // if (status == 2) {
        //   trackStr = "recruit_failed_recruit Subscription_click"
        // } else {
        trackStr = "recruit_recruit_Subscription_click"
        // }
        break
      case "buyer_sign_in":
        trackStr = ""
        break
      case "new_campaign":
        trackStr = "recruit_failed2_new_Subscription_click"
        break
    }
    wx.$mp.track({
      event: trackStr,
    })
    let that = this
    this.setData({
      str: str
    })
    if (app.ifRegister() && str != '') {
      app.subscribe2(str).then((res) => {
        setTimeout(() => {
          that.getSubNum()
        }, 3000)
        let templateIds = res?.templateIds || []
        if (templateIds && templateIds.length > 0) {
          let isSubscribe = templateIds.some((item) =>
            res[item] == "accept"
          )
          if (isSubscribe) {
            wx.showToast({
              title: '订阅成功',
              icon: 'none',
            });

          } else {
            wx.showToast({
              title: '拒绝订阅',
              icon: 'none',
            });
          }
        }

      })
    }

  }),
  // 用户点击拒绝授权 展开提示在某个地方开启授权弹框
  openGuide() {
    this.setData({
      showContact1: true
    })
  },
  closeGuide() {
    this.setData({
      showContact1: false
    })
  },
  // 处理时间
  getDate(time) {
    console.log(time, 'time');
    if (typeof time === 'string') {
      // ios 解析不出来 年月 2020-05
      if (time.length < 8) {
        time = `${time}-1`;
      }
      time = new Date(time.replace(/-/g, '/').replace('T', ' ')).getTime();
    }
    let date = new Date(time); // 创建一个日期对象
    let year = date.getFullYear(); // 获取年份
    let month = date.getMonth() + 1; // 获取月份（0-11），需要加1
    let day = date.getDate(); // 获取日

    // 拼接成“2023年1月1日”的格式
    let formattedDate = `${year}.${month}.${day}`;
    console.log(formattedDate); // 输出：2023年1月1日（假设今天是2023年1月1日）
    return formattedDate
  },
  // 跳转到活动规则页面
  activeRules: app.debounce(async function () {
    wx.$mp.navigateTo({
      url: '/signUp/pages/activityRules/activityRules',
    })
  }),
  // 跳转公示结果页
  activeRules2: app.debounce(async function () {
    wx.$mp.navigateTo({
      url: '/signUp/pages/announcement/announcement',
    })
  }),
  // 半屏拉起其他小程序
  goOtherApp() {
    console.log(11111111111);
    // wx.openEmbeddedMiniProgram({
    //   appId: "wx38d029d79dea452e",
    //   path: "/pages/index/index",
    //   extraData: "携带了参数",
    //   success: success => {
    //     console.log("完成", success)
    //   },
    //   fail(fail) {
    //     console.log('点击取消', fail)
    //   }
    // })
  },
  click() {

  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    // 页面分享
    return {
      title: '和我一起体验「极简护肤」开启七天打卡活动',
      imageUrl: wx.$config.ossImg + '/signUp/share.png',
      path: '/signUp/pages/signUp/signUp',
    }
  }
})
