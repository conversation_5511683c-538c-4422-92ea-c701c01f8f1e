package com.dz.ms.sales.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/2/13
 */
@Data
public class SignInReportDTO {

    @ApiModelProperty(value = "素材链接")
    private String materialUrlDay1;

    @ApiModelProperty(value = "素材链接")
    private String materialUrlDay7;

    @ApiModelProperty(value = "改善")
    private String improve;

    @ApiModelProperty(value = "平均舒适度")
    private double comfortAvg;

    @ApiModelProperty(value = "平均保湿效果")
    private double moisturizeAvg;

    @ApiModelProperty(value = "平均吸收速度")
    private double absorptionAvg;

    @ApiModelProperty(value = "平均持久效果")
    private double persistenceAvg;

}
