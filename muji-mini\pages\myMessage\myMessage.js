// pages/myMessage/myMessage.js
import {
  getUserMessage
} from '../../api/index'

const app = getApp()


Page({

  /**
   * 页面的初始数据
   */
  data: {
    pageSize: 10,
    pageNum: 1,
    hasMore: false,
    messageList: [],
    loading: true,
    navHeight: app.globalData.navBarHeight + app.globalData.statusBarHeight,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {},

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.setData({
      messageList: [],
      pageNum: 1,
    })
    this.getList()
  },
  onShareAppMessage() {

  },
  getMore() {
    const {
      hasMore,
      pageNum
    } = this.data;
    const nextPage = pageNum + 1;
    if (hasMore) {
      this.setData({
        pageNum: nextPage
      })
      this.getList()
    }
  },
  // 获取用户消息列表
  async getList() {
    this.setData({
      loading: true,
    })
    const {
      pageNum,
      pageSize,
      messageList
    } = this.data;
    const res = await getUserMessage({
      pageNum,
      pageSize,
    });
    const {
      count
    } = res.data;
    this.setData({
      messageList: messageList.concat(res.data.list),
      hasMore: pageNum * pageSize < count,
      loading: false,
    })
  },
})
