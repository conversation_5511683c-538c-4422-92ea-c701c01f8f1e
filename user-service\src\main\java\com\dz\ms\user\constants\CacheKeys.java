package com.dz.ms.user.constants;

public class CacheKeys {

    /** OMS系统用户密钥 */
    public static final String OMSUSER_SECRET = "omsuser:secret:";
    /** 系统用户密钥 */
    public static final String SYSUSER_SECRET = "sysuser:secret:";
    /** app用户密钥 */
    public static final String USER_SECRET = "user:secret:";
    /** 导购用户密钥 */
    public static final String EMPUSER_SECRET = "empuser:secret:";
    /** 网页用户密钥 */
    public static final String MPUSER_SECRET = "mpuser:secret:";
    /** openapi用户密钥 */
    public static final String OPENUSER_SECRET = "openuser:secret:";
    /** OMS用户角色 */
    public static final String OMS_USER_ROLE = "oms:user:role:";
    /** OMS用户信息 */
    public static final String OMS_USER_INFO = "oms:user:info:";
    /** 系统用户角色 */
    public static final String SYS_USER_ROLE = "sys:user:role:";
    /** 系统用户可用门店 */
    public static final String SYS_USER_STORE = "sys:user:store:";
    /** 系统用户信息 */
    public static final String SYS_USER_INFO = "sys:user:info:";
    /** 加密APP用户信息 */
    public static final String USER_INFO = "user:info:";
    /** 未加密APP用户信息 */
    public static final String SIMPLE_USER_INFO = "simple:user:info:";
    /** 图形验证码 */
    public static final String IMAGE_CODE = "imagecode:";
    /** 短信验证码 */
    public static final String SMS_CODE = "smscode:";
    /** 用户授权手机号 */
    public static final String USER_AUTHMOBILE = "user:authmobile:";
    /** OMS角色权限 */
    public static final String OMS_ROLE_PERMIT_CODES = "oms:role:permitcodes:";
    /** 系统角色权限码列表 */
    public static final String SYS_ROLE_PERMIT_CODES = "sys:role:permitcodes:";
    /** 系统角色功能权限码列表 */
    public static final String SYS_ROLE_FUNCTIONS = "sys:role:functions:";
    /** 系统角色菜单列表 */
    public static final String SYS_ROLE_MENU_TREE = "sys:role:menutree:";
    /** 系统角色权限接口 */
    public static final String SYS_ROLE_PERMIT_URLS = "sys:role:permiturls:";
    /** 收货地址 */
    public static final String RECEIVER_ADDRESS = "user:receiver:address:";
    /** 导购用户信息 */
    public static final String EMPUSER_INFO = "empuser:info:";
    /** 重置密码次数 */
    public static final String REST_PASSWORD_COUNT  = "sysuser:passwordreset:";
    /** 导购session key */
    public static final String QYWX_SESSION_KEY = "qywx:sessionkey:";
    /** 用户访问记录PVUV */
    public static final String VISIT_PVUV = "pvuv:";

    /** 门店 */
    public static final String STORE_ID = "store:id";
    public static final String STORE_CODE = "store:code:";
    
    /** 短信验证码发送间隔 */
    public static final String MOBILE_CODE_INTERVAL_TIME = "mobile:code:interval:time:";

    /** ----------------------------- Cacheable 使用KEY 不用:结尾 --------------------------------*/
    /** OMS权限URL对应关系 */
    public static final String OMS_PERMIT_URLMAP = "oms:permit:urlmap";
    /** OMS所有权限码 */
    public static final String OMS_ALL_PERMIT_CODES = "oms:all:permitcodes";
    /** CRM所有权限码 */
    public static final String SYS_ALL_PERMIT_CODES = "sys:all:permitcodes";
    /** CRM所有功能权限码 */
    public static final String SYS_ALL_FUNCTION_CODES = "sys:all:functioncodes";
    /** 职位列表 */
    public static final String EMPLOYEE_POSITIONS = "employee:positions";
    /** 会员等级详情 */
    public static final String GRADE_INFO = "grade:info";
    /** 第一个会员等级 */
    public static final String GRADE_FIRST = "grade:first";
    /** 会员等级名称CODE MAP */
    public static final String GRADE_NAME_MAP = "grade:namemap";
    /** 会员等级CODE名称 MAP */
    public static final String GRADE_CODE_MAP = "grade:codemap";
    /** 会员信息 */
    public static final String MEMBER_INFO = "member:info";
    /** crm互动任务列表 */
    public static final String CRM_INTERACTION_TASK_List = "crm:interaction:task:list";
    /** app互动任务列表 */
    public static final String APP_INTERACTION_TASK_List = "app:interaction:task:list";

}
