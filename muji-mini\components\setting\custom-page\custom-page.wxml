<view class="customPage {{isTabBarPage?'first':''}}">
  <!-- 固定导航占位 -->
  <my-header-top wx:if="{{navSetting.navType==1||navSetting.navType==4}}" />
  <!-- 内容区 -->
  <view class="customPage-box ">
    <!-- 沉浸导航占位 -->
    <!-- <my-header-top wx:if="{{navSetting.navType!=1}}" />-->
    <view class="customPage-content">
      <custom-bg bgSetting="{{pageSetting}}" class="bgStyle"></custom-bg>
      <scroll-view enhanced="{{true}}" bounces="{{false}}" class="customPage-scroll" scroll-y scroll-with-animation
        srcollTop="{{srcollTop}}" scroll-into-view="{{view}}" bindscroll="scroll">
        <!-- 返回顶部使用 -->
        <view id="top"></view>
        <!-- 自定义内容 -->
        <slot></slot>
        <!-- 自定义组件 -->
        <!-- type 1-富文本  2-海报 3-视频 4-轮播 5-悬浮窗 6-滚动区域 7-门店 8-任务 9-普通文本-->
        <block wx:for="{{componentSetting}}" wx:key="id">
          <my-rich visitor="{{visitor}}" wx:if="{{item.type==1}}" data="{{item}}" id="view{{index}}" bindgoAchor="goAchor" bindgoTop="goTop"
            bindgoModal="goModal" bindgoShare="goShare"></my-rich>
          <my-poster visitor="{{visitor}}" wx:if="{{item.type==2}}" data="{{item}}" id="view{{index}}" bindgoAchor="goAchor" bindgoTop="goTop"
            bindgoModal="goModal" bindgoShare="goShare"></my-poster>
          <my-video visitor="{{visitor}}" wx:if="{{item.type==3}}" data="{{item}}" id="view{{index}}" bindgoAchor="goAchor" bindgoTop="goTop"
            bindgoModal="goModal" bindgoShare="goShare"></my-video>
          <normal-slider visitor="{{visitor}}" wx:if="{{item.type==4}}" data="{{item}}" id="view{{index}}" bindgoAchor="goAchor"
            bindgoTop="goTop" bindgoModal="goModal" bindgoShare="goShare"></normal-slider>
          <scroll visitor="{{visitor}}" wx:if="{{item.type==6}}" data="{{item}}" id="view{{index}}" bindgoAchor="goAchor" bindgoTop="goTop"
            bindgoModal="goModal" bindgoShare="goShare"></scroll>
          <store visitor="{{visitor}}" wx:if="{{item.type==7}}" data="{{item}}" id="view{{index}}" bindgoAchor="goAchor" bindgoTop="goTop"
            bindgoModal="goModal" bindgoShare="goShare"></store>
          <task visitor="{{visitor}}" wx:if="{{item.type==8}}" data="{{item}}" id="view{{index}}" bindgoAchor="goAchor" bindgoTop="goTop"
            bindgoModal="goModal" bindgoShare="goShare"></task>
          <custom-text visitor="{{visitor}}" wx:if="{{item.type==9}}" data="{{item}}" id="view{{index}}" bindgoAchor="goAchor"
            bindgoTop="goTop" bindgoModal="goModal" bindgoShare="goShare"></custom-text>
          <my-division visitor="{{visitor}}" wx:if="{{item.type==10}}" data="{{item}}" id="view{{index}}" bindgoAchor="goAchor"
            bindgoTop="goTop" bindgoModal="goModal" bindgoShare="goShare"></my-division>
        </block>
      </scroll-view>
    </view>

  </view>
  <!-- 跳过按钮 -->
  <view class="openGo" wx:if="{{pageSetting.openGo}}">
    <custom-link data="{{pageSetting.openLinks[0]}}" class="linkStyle" bindgoAchor="goAchor" bindgoTop="goTop"
      bindgoModal="goModal" bindgoShare="goShare">
      <!-- <image style="height:60rpx;width:168rpx" src="{{$cdn}}/goJump.png" bindtap="goJump" /> -->
      <image style="height:40rpx;width:124rpx" src="{{$cdn}}/whiteGoJump.png" bindtap="goJump" />
    </custom-link>
  </view>

  <!-- 定位导航 -->
  <!-- 锚点行为  置顶行为  关闭弹窗行为-->
  <custom-nav templateName="{{pageSetting.templateName}}" navSetting="{{navSetting}}" scrollTop="{{scrollTop}}"
    bindgoAchor="goAchor" bindgoTop="goTop" bindgoModal="goModal" bindgoShare="goShare"></custom-nav>
  <!-- 悬浮窗 -->
  <custom-float data="{{pageSetting.floatSetting}}" width="{{750}}" class="floatStyle"
    wx:if="{{pageSetting.floatSetting}}" bindgoAchor="goAchor" bindgoTop="goTop" bindgoModal="goModal"
    bindgoShare="goShare"></custom-float>
  <!-- 10个弹窗页面 -->
  <block wx:for="{{modal}}" wx:key="index">
    <custom-modal key="{{index}}" modalId="{{item.id}}" show="{{item.show}}" index="{{index}}" bindclose="closeModal"
      bindgoModal="goModal"></custom-modal>
  </block>
</view>