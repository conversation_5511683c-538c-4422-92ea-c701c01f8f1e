package com.dz.ms.user.service;

import com.dz.common.base.vo.PageInfo;
import com.dz.ms.user.dto.AppointmentStatsDTO;
import com.dz.ms.user.entity.AppointmentRecord;
import com.dz.ms.user.vo.AppointmentDetailsVO;

import java.util.List;

public interface AppointmentService {

    /**
     * 获取预约活动详情
     *
     * @return
     */
    AppointmentDetailsVO getAppointmentDetails();

    /**
     * 预约活动
     *
     * @param name            用户姓名
     * @param phone           用户电话
     * @param appointmentDate 预约日期
     * @param appointmentSlot 预约时段
     */
    void bookAppointment(String name, String phone, String appointmentDate, String appointmentSlot);

    /**
     * 获取用户的预约记录
     *
     * @param userId 用户ID
     * @return 预约记录
     */
    AppointmentRecord getUserAppointments(Long appointmentId, Long userId);

    /**
     * 获取活动预约日期列表
     *
     * @return 预约日期列表
     */
    List<String> getAppointmentDates();

    /**
     * 获取活动预约日期列表
     *
     * @return 预约日期列表
     */
    List<AppointmentStatsDTO> getAppointmentDatesStats();

    /**
     * 获取活动的预约场次配置
     *
     * @return 预约场次配置列表
     */
    List<String> getAppointmentSlots(String appointmentDate);

    /**
     * 获取活动的预约场次配置
     *
     * @return 预约场次配置列表
     */
    List<AppointmentStatsDTO> getAppointmentSlotsStats(String appointmentDate);

    /**
     * 根据日期查询预约记录
     *
     * @param appointmentDate 预约日期
     * @return
     */
    PageInfo<AppointmentRecord> listAppointmentsByDate(Integer pageNum, Integer pageSize, String appointmentDate);

    /**
     * 根据日期导出所有预约记录
     *
     * @param appointmentDate 预约日期
     * @return 预约记录列表
     */
    List<AppointmentRecord> exportAppointmentsByDate(String appointmentDate);

    /**
     * 判断场次是否已约满
     *
     * @param appointmentDate 预约日期
     * @param appointmentSlot 预约场次
     * @return 是否已约满
     */
    boolean isAppointmentSlotFull(String appointmentDate, String appointmentSlot);
}