const app = getApp()
Component({
  externalClasses: ['linkStyle'],
  properties: {
    data: {
      type: null,
      value() {
        return {}
      }
    }
  },
  data: {
    ifHaveShare: false, // 是否包含分享按钮
  },
  lifetimes: {
    attached() {
      let actions = this.data.data?.actions || []
      // 6-分享
      this.setData({
        ifHaveShare: actions.some(item => item.operateType == 6)
      })
    }
  },
  methods: {
    // 热区点击事件
    click: app.debounce(async function () {
      // 返回符合条件的行为
      app.clickHot(this.data.data).then(validData => {
        validData.forEach(data => {
          this.clickEvent(data)
        })
      }).catch(() => { })
    }),
    // 行为
    clickEvent(data) {
      clearTimeout(app.globalData.autoGoTimer)
      let {
        operateType,
        copyText,
        modalIndex,
        anchorIndex,
        linkUrl,
        shareTitle,
        shareImg,
        sharePath,
      } = data

      // 行为 // 热区行为
      // operateTypes = [
      //   { value: 1, label: '页面跳转' },
      //   { value: 2, label: '关闭弹窗' },
      //   { value: 3, label: '打开弹窗' },
      //   { value: 4, label: '长按识别二维码' },
      //   { value: 5, label: '点击复制' },
      //   { value: 6, label: '点击分享' },
      //   { value: 7, label: '锚点跳转' },
      //   { value: 8, label: '返回顶部' },
      //   { value: 9, label: '返回上一页' },
      // ]
      // 页面跳转
      if (operateType == 1) {
        app.goLink(data)
      } else if (operateType == 2) { // 关闭弹窗
        this.triggerEvent('goModal', {
          modalIndex, // 弹窗下标
          operateType: 1, //1-关闭  2-打开
        })
      } else if (operateType == 3) { // 打开弹窗
        this.triggerEvent('goModal', {
          modalIndex, // 弹窗下标
          operateType: 2, //1-关闭  2-打开
          id: linkUrl.split('?id=')[1]
        })
      } else if (operateType == 5) { // 复制
        if (!copyText.length) {
          return
        }
        wx.setClipboardData({
          data: copyText,
        })
      } else if (operateType == 6) { // 分享
        this.triggerEvent('goShare', {
          shareTitle,
          shareImg,
          sharePath
        })
      } else if (operateType == 7) { // 锚点跳转
        this.triggerEvent('goAchor', {
          anchorIndex, // 弹窗下标
        })
      } else if (operateType == 8) { // 返回顶部
        this.triggerEvent('goTop')
      } else if (operateType == 9) { // 返回上一页
        var pages = wx.$mp.getCurrentPages();
        if (pages.length > 1) {
          wx.$mp.navigateBack()
        } else {
          wx.$mp.switchTab({
            url: '/pages/index/index',
          })
        }
      }
    }
  }
})
