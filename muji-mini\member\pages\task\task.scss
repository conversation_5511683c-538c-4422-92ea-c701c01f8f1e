@import 'taskTransition.scss';
//@import './taskTransition.scss';

@keyframes animPopupLogo {
  0% {
    transform: translateX(200rpx) scale(1.2);
  }
  100% {
    transform: scale(1.2);
  }
}

@keyframes animPopupTree {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 1;
    transform: scale(2.6);
  }
}

@keyframes animPopupTreeBox {
  0% {
    transform: none;
  }
  20% {
    transform: rotate(-12deg);
  }
  40% {
    transform: rotate(10deg);
  }
  60% {
    transform: rotate(-7deg);
  }
  80% {
    transform: rotate(3deg);
  }
  100% {
    transform: none;
  }
}

@keyframes animPopupStartMask {
  0% {
    opacity: 1;
    //transform: none;
  }
  100% {
    opacity: 0;
    //transform: translateX(-100vw);
  }
}

@keyframes animPopupStartLast {
  0% {
    opacity: 1;
    transform: none;
  }
  100% {
    opacity: 1;
    transform: translateX(-100vw);
  }
}

.lottery-popup {
  //display: none;

  &.show {
    //display: block;

    .lottery-popup-mask {
      opacity: 1;
      pointer-events: auto;
    }

    .lottery-popup-content {
      opacity: 1;
      transform: none;
      pointer-events: auto;
    }
  }

  .lottery-popup-mask {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 50;
    background-color: rgba(0, 0, 0, 0.75);

    opacity: 0;
    pointer-events: none;
    will-change: opacity;
    transition: opacity 0.4s;
  }

  .lottery-popup-content {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 100;

    margin: auto;
    width: 600rpx;
    height: 940rpx;

    transform: scale(0.4);
    opacity: 0;
    pointer-events: none;
    will-change: opacity, transform;
    transition: opacity 0.4s, transform 0.4s;

    .popup-close {
      position: absolute;
      top: 115rpx;
      right: 54rpx;
      z-index: 10;

      width: 48rpx;
      height: 48rpx;
    }

    .popup-title {
      margin-bottom: 20rpx;
      line-height: 54rpx;
      font-size: 36rpx;
      font-weight: 500;
      letter-spacing: normal;
      color: #EEEAE1;
      text-align: center;
    }

    .popup-content {
      position: relative;
      box-sizing: border-box;
      padding-top: 80rpx;
      width: 600rpx;
      height: 600rpx;

      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      will-change: transform;
      transition: transform 0.4s, opacity 0.4s;

      &.loading {
        transform: scale(0.4);
        opacity: 0;
        .popup-transition {
          opacity: 0;
        }
      }

      .popup-transition {
        opacity: 1;
        transition: opacity 0.5s;
        //&.loading {
        //  opacity: 0;
        //}
      }

      .bg {
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        z-index: -1;
        width: 100%;
        height: 100%;
      }

      .popup-content-image {
        display: block;
        margin-top: 32rpx;
        width: 300rpx;
        height: 300rpx;
      }

      .popup-content-header {
        width: 100%;
        height: 170rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .number {
          margin-bottom: -30rpx;
          font-family: MUJI Font 2020;
          font-size: 109.11rpx;
          font-weight: normal;
          line-height: 1;
          letter-spacing: normal;
          color: #94243A;
        }

        .unit {
          margin: 0 4rpx -60rpx 4rpx;
          width: 1em;
          font-family: MUJI Font 2020;
          font-size: 24rpx;
          font-weight: normal;
          line-height: 30rpx;
          letter-spacing: normal;
          color: #94243A
        }
      }

      .popup-content-footer {
        position: relative;

        .desc {
          padding-top: 10rpx;
          font-size: 18rpx;
          font-weight: 350;
          line-height: normal;
          letter-spacing: -0.03em;
          color: #231815;
        }

        &:before {
          content: '';
          position: absolute;
          left: 50%;
          top: 0;
          margin-left: -75rpx;
          width: 150rpx;
          height: 4rpx;
          background: #94243A;
        }
      }
    }

    .popup-footer {
      margin-top: 50rpx;

      .button-d {
        position: relative;
        margin-left: auto;
        margin-right: auto;
        width: 360rpx;
        height: 80rpx;

        display: flex;
        align-items: center;
        justify-content: center;

        font-size: 28rpx;
        font-weight: 500;
        line-height: normal;
        text-align: center;
        letter-spacing: normal;
        color: #231815;

        border-radius: 5rpx;
        background: #fff;

        .button-d-text {
          pointer-events: none;
          position: absolute;
          left: 0;
          top: 0;
          right: 0;
          bottom: 0;
          z-index: 10;
          display: flex;
          align-items: center;
          justify-content: center;

          opacity: 0;
          transition: opacity 0.2s;

          &.show {
            opacity: 1;
          }

          &.disabled {
            color: #888;
          }
        }
      }

      .button-g {
        box-sizing: border-box;
        margin-top: 40rpx;
        margin-left: auto;
        margin-right: auto;
        width: 360rpx;
        height: 80rpx;

        display: flex;
        align-items: center;
        justify-content: center;

        font-size: 28rpx;
        font-weight: 500;
        line-height: normal;
        text-align: center;
        letter-spacing: normal;
        color: #EEEAE1;

        border-radius: 5rpx;
        //border: 1rpx solid #EEEAE1;
        box-shadow: inset 0 0 0 1rpx #EEEAE1;
      }
    }
  }
}

.page-current-loading.page-current-loading.page-current-loading {
  .task-wrapper {
    opacity: 0;
    pointer-events: none;
  }
}

// 状态1
.page-current-start.page-current-start.page-current-start {
  .task-content {
    transform: none;
  }

  .lottery-outer-text {
    transform: translateX(-120rpx);
  }

  .lottery-outer-logo {
    transform: translateX(200rpx) scale(1.2);
  }

  .lottery-tree-wrapper {
    // transition: transform var(--treeDuration);
    opacity: 0;
  }

  .lottery-draw-text {
    opacity: 0;
    transition: opacity var(--treeDuration) calc(-1 * var(--treeDuration) / 2);
  }

  .start-mask {
    transition: opacity 0.8s 4s;
    transform: none;
    opacity: 1;
  }

  .start-image {
    transform: none;
    //transition: transform 0.6s 0s + 1.5s;
  }

  .popup-title {
    opacity: 1;
    transition: opacity 1s 4s;
  }

  .popup-button {
    pointer-events: auto;
    opacity: 1;
    transition: opacity 1s 4s;
  }

  .popup-butterfly.static {
    opacity: 1;
    transition: opacity 1s 4s;
  }
}

// 状态2
.page-current-popup.page-current-popup.page-current-popup {
  .task-content {
    transform: none;
  }

  .lottery-outer-text {
    transform: translateX(-120rpx);
  }

  .lottery-outer-logo {
    transform: translateX(200rpx) scale(1.2);
    animation: animPopupLogo 1s 4.5s forwards;
  }

  .lottery-tree-wrapper {
    opacity: 0;
    transform: none;
    animation: animPopupTree 2s ease 3.2s forwards;
    //opacity: 1;
    //transform: scale(2.6);
    //transition: transform 2s 2s + 1.5s, opacity 0s 2s + 1.5s;

    .lottery-tree-box {
      transform-origin: center 0;
      animation: animPopupTreeBox 2s ease-out 3.8s forwards;
    }
  }

  .lottery-draw-text {
    opacity: 0;
  }

  .lottery-footer {
    transition: transform 1.5s 2s + 1.5s;
    transform: translateY(-320rpx)
  }

  .start-mask {
    opacity: 1;
    //transform: none;
    animation: animPopupStartMask 0.6s 0s + 1.5s forwards;
  }

  .start-image {
    transform: translateX(-100vw);
    transition: transform 0.6s 0s + 1.5s;
  }

  .start-last-image {
    opacity: 1;
    transform: none;
    animation: animPopupStartLast 0.6s 0s + 1.5s forwards;
  }

  .popup-title {
    opacity: 1;
    transform: translateX(-100vw);
    transition: transform 0.6s 0s + 1.5s;
  }

  .popup-button {
    opacity: 1;
    transform: translateX(-100vw);
    transition: transform 0.6s 0s + 1.5s;
  }

  .popup-window {
    transform-origin: 44% 35%;
    animation: animWindow 5s ease 0.5s + 1.5s forwards;
  }
}

.section-start {
  .start-mask {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    background: rgba(0, 0, 0, 0.75);

    opacity: 0;
    will-change: opacity;
    pointer-events: none;
    //transform: translateX(-100vw);
  }

  .start-image {
    position: absolute;
    left: 0;
    top: 50%;
    right: 0;
    z-index: 10;

    margin-top: -440rpx;
    width: 750rpx;
    height: 880rpx;

    //opacity: 0;
    will-change: transform;
    pointer-events: none;
    transform: translateX(-100vw);

    &.start-image-1 {
      margin-top: -812rpx;
      width: 750rpx;
      height: 1624rpx;
    }
  }

  .start-last-image {
    position: absolute;
    left: 0;
    top: 50%;
    right: 0;
    z-index: 10;

    margin-top: -440rpx;
    width: 750rpx;
    height: 880rpx;

    will-change: transform;
    opacity: 0;
    //pointer-events: none;
    transform: translateX(-100vw);

    &.start-last-image-1 {
      margin-top: -812rpx;
      width: 750rpx;
      height: 1624rpx;
    }
  }
}

.section-popup {

  .popup-title {
    pointer-events: none;
    will-change: transform, opacity;
    opacity: 0;

    position: absolute;
    left: 0;
    top: 50%;
    z-index: 10;

    display: flex;
    flex-direction: column;
    align-items: center;

    margin-top: -480rpx;
    width: 100vw;
    height: 53rpx;
    font-weight: 500;
    font-size: 36rpx;
    color: #EEEAE1;
    //color: #231815;
    line-height: 56rpx;
    text-align: center;
    font-style: normal;
    text-transform: none;

    .popup-title-desc {
      margin-top: 8rpx;
      height: 36rpx;
      font-weight: 500;
      font-size: 24rpx;
      color: #EEEAE1;
      //color: #231815;
      line-height: 36rpx;
      text-align: left;
      font-style: normal;
      text-transform: none;

      .link {
        position: relative;
        pointer-events: auto;
        //color: #231815;
        text-decoration: underline;
        text-underline-offset: 4rpx;

        font-size: 28rpx;

        &:before {
          pointer-events: auto;
          content: '';
          position: absolute;
          left: -20rpx;
          top: -20rpx;
          right: -20rpx;
          bottom: -20rpx;
          z-index: 10;
          //background-color: #48C871;
        }
      }
    }
  }

  .popup-button {
    position: absolute;
    left: 50%;
    top: 50%;
    z-index: 10;

    margin-left: -180rpx;
    margin-top: 308rpx;
    width: 360rpx;
    height: 80rpx;
    background: #EEEAE1;
    //background: #231815;
    border-radius: 5rpx;

    display: flex;
    align-items: center;
    justify-content: center;

    font-weight: 500;
    font-size: 28rpx;
    color: #231815;
    //color: #fff;
    line-height: 41rpx;
    font-style: normal;
    text-transform: none;

    will-change: transform, opacity;
    opacity: 0;
    pointer-events: none;
    //transition: opacity ;
  }

  .popup-window {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1;
    width: 100vw;
    height: 100vh;
    object-fit: fill;

    will-change: transform;
    pointer-events: none;
    opacity: 0;
    transform-origin: 44% 35% !important;
  }

  .popup-butterfly {
    pointer-events: none;
    position: fixed;
    left: 0;
    right: 0;
    top: 50%;
    z-index: 100;

    margin-top: -72rpx;
    width: 750rpx;
    height: 604rpx;

    &.anim {
      animation: animButterfly 6s ease forwards;
    }

    &.static {
      opacity: 0;
    }
  }

  .popup-butterfly-preload {
    position: absolute;
    pointer-events: none;
    width: 1rpx;
    height: 1rpx;
    opacity: 0;
  }

}

.task-scroll-wrapper {
  position: relative;

  .task-scroll-shadow {
    position: absolute;
    z-index: 9;
    left: 0;
    top: 0;
    right: 0;
    height: 130rpx;
    background: linear-gradient(180deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
  }
}

.page {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;

  &-canvas {
    width: 750rpx;
    height: 1008rpx;
    position: fixed;
    left: 0;
    top: 200vh;
  }

  &-header {
    position: sticky;
    z-index: 9;
    top: 0;
    width: 750rpx;
    background: #fff;
  }

  &-title {

    margin-top: 60rpx;
    font-weight: 700;
    font-size: 36rpx;
    color: #231815;
    line-height: 52rpx;
    letter-spacing: 1px;
  }

  &-subTitle {
    margin-top: 10rpx;
    font-weight: 350;
    font-size: 28rpx;
    color: #231815;
    line-height: 40rpx;

    opacity: 1;
    will-change: opacity;
    transition: opacity 0.2s;

    &.hide {
      opacity: 0;
    }
  }

  &-task {
    display: flex;
    flex-direction: column;
    width: 100%;

    &-item {
      padding: 40rpx 0 40rpx 0;
      margin: 0 40rpx;
      border-bottom: 1px dashed #D8D8D8;

      &:last-child {
        border: none;
        // padding-bottom: 0;
      }
    }

    &-title {
      display: flex;
      align-items: flex-end;
      font-weight: 700;
      font-size: 32rpx;
      color: #231815;
      line-height: 45rpx;
      // font-family: MUJIFont2020;
      view{
        font-weight: 400;
        font-size: 24rpx;
        color: #2E2E2E;
        line-height: 36rpx;
      }
    }

    &-subTitle {
      // font-family: MUJIFont2020;
      margin-top: 10rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #2E2E2E;
      line-height: 36rpx;
    }

    &-num {
      font-family: MUJIFont2020;
      font-weight: 700;
    }

    &-info {

      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    &-info-box {
      flex: 1;
      overflow-x: auto;
      padding-top: 24rpx;
    }

    &-info-box::-webkit-scrollbar {
      /* Webkit */
      display: none;
    }

    &-show {
      position: relative;
      display: flex;
      align-items: center;
    }

    &-Invite {

      flex-shrink: 0;
      position: relative;
      z-index: 1;
      margin-right: 15rpx;
      border-radius: 50%;
      width: 72rpx;
      height: 72rpx;
      border: 1rpx dotted #231815;
      font-family: MUJIFont2020;
      color: #231815;
      display: flex;
      align-items: center;
      justify-content: center;

      image {
        border-radius: 50%;
        width: 100%;
        height: 100%;
        z-index: 9;
      }

      &.active {
        color: #fff;
        border: 2rpx solid #FFFFFF;

        &::after,
        &::before {
          display: none;
        }
      }
    }

    &-Invite::after {
      content: '';
      position: absolute;
      background-color: #231815;
      left: 50%;
      top: 50%;
      height: 32rpx;
      width: 2rpx;
      /* 调整线条粗细 */
      transform: translate(-50%, -50%);
    }

    &-Invite::before {
      content: '';
      position: absolute;
      background-color: #231815;
      top: 50%;
      left: 50%;
      width: 32rpx;
      height: 2rpx;
      /* 调整线条粗细 */
      transform: translate(-50%, -50%);
    }

    &-point {
      flex-shrink: 0;
      position: relative;
      z-index: 1;
      margin-right: 15rpx;
      border-radius: 50%;
      width: 72rpx;
      height: 72rpx;
      // border: 1rpx dotted #231815;
      font-family: MUJIFont2020;
      color: #231815;
      display: flex;
      align-items: center;
      justify-content: center;

      &.active {
        color: #fff;
        background: #94243A;
        border: 2rpx solid #FFFFFF;
      }
    }

    &-clock {
      flex-shrink: 0;
      position: relative;
      z-index: 1;
      margin-right: 20rpx;
      border-radius: 50%;
      width: 72rpx;
      height: 72rpx;
      border: 1rpx dotted #231815;
      font-family: MUJIFont2020;
      font-weight: 700;
      font-size: 30rpx;
      color: #231815;
      display: flex;
      align-items: center;
      justify-content: center;

      &.active {
        color: #fff;
        background: #94243A;
        border: 2rpx solid #FFFFFF;
      }
    }

    .stack {
      .page-task {
        &-clock {
          &:nth-of-type(1) {
            margin-right: -50rpx;
          }

          &:nth-of-type(2) {
            margin-right: -50rpx;
          }
        }
      }
    }

    &-btn {
      margin-left: 6rpx;

      display: flex;
      justify-content: center;
      align-items: center;
      width: 132rpx;
      height: 58rpx;
      background: #231815;
      border-radius: 5rpx;
      font-weight: 500;
      font-size: 24rpx;
      color: #FFFFFF;

      // font-family: MUJIFont2020;
      &.disabled {
        pointer-events: none;
        background: #F5F5F5;
        color: #231815;
      }
    }
  }
}
