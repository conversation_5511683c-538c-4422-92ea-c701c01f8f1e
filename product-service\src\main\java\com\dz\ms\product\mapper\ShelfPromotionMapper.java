package com.dz.ms.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.ms.product.dto.ShelfPromotionDTO;
import com.dz.ms.product.dto.req.ShelfPromotionParamDTO;
import com.dz.ms.product.entity.ShelfPromotion;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 货架推广活动Mapper
 *
 * @author: LiinNs
 * @date: 2024/11/20 11:36
 */
@Repository
public interface ShelfPromotionMapper extends BaseMapper<ShelfPromotion> {

    IPage<ShelfPromotion> selPageList(Page<Object> objectPage, @Param("param") ShelfPromotionParamDTO param);

    List<ShelfPromotionDTO> selectListByParam(@Param("param") ShelfPromotionParamDTO param);
}
