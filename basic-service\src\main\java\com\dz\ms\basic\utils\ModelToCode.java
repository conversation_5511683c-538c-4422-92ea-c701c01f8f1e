package com.dz.ms.basic.utils;

import com.dz.ms.basic.dto.AutoCodeColumnDTO;
import com.dz.ms.basic.dto.AutoCodeTableDTO;
import org.apache.commons.lang3.math.NumberUtils;

import java.sql.Connection;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ModelToCode {

	private static String getTuofengString(String colum, boolean isFirst, boolean isTable) {
		if(isTable && colum.startsWith("t_")) {
			colum = colum.substring(2,colum.length());
		}
		String[] array = colum.split("_");
		String name = "";
		for (int i = 0; i < array.length; i++) {
			String str = array[i];
			if(isFirst) {
				name += str.substring(0, 1).toUpperCase()+str.substring(1, str.length());
			}else {
				if(i == 0) {
					name += str;
				}else {
					name += str.substring(0, 1).toUpperCase()+str.substring(1, str.length());
				}
			}
		}
		return name;
	}
	
}