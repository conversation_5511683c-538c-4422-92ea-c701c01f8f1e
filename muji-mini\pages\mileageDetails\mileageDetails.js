import {
  mileageDetails
} from '../../api/index'
Page({
  data: {
    isBackHidden: false,
    loading: false,
    details: {},
    mileageSn: ''
  },
  onShow(options) {
    if (options) {
      this.setData({
        mileageSn: options.mileageSn
      })
    }

    this.getDetail()
  },
  getDetail() {
    this.setData({
      loading: true
    })
    const {
      mileageSn,
      loading
    } = this.data
    mileageDetails({
      mileageSn: mileageSn
    }).then(res => {
      if (res.data.consumptionAmount === '0.00') {
        res.data.consumptionAmount = ''
      }
      if (res.data.cumulativeAmount === '0.00') {
        res.data.cumulativeAmount = ''
      }
      this.setData({
        details: res.data
      })

    }).finally(() => {
      this.setData({
        loading: false
      })
    })
  },
  onShareAppMessage() {}
})
