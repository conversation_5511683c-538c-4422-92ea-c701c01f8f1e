package com.dz.ms.adaptor.processor;

import com.dz.common.core.fegin.product.ProductOnTaskFeignClient;
import com.dz.common.core.fegin.user.StoreFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import javax.annotation.Resource;

/**
 * 门店sftp任务
 */
@Slf4j
@Component
public class StoreJob implements BasicProcessor {

    @Resource
    private StoreFeignClient storeFeignClient;

    @Override
    public ProcessResult process(TaskContext context) {
        storeFeignClient.getSftpFile();
        log.info("拉取门店数据 完成");
        return new ProcessResult(true, "success");
    }
}