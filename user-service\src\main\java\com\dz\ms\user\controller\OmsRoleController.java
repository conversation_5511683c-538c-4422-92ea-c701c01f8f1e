package com.dz.ms.user.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.user.dto.OmsRoleDTO;
import com.dz.ms.user.dto.RolePermissionDTO;
import com.dz.ms.user.entity.OmsRole;
import com.dz.ms.user.service.OmsRoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.List;

@Api(tags="OMS-系统角色")
@RestController
@RequestMapping(value="/oms")
public class OmsRoleController {

    @Resource
    private OmsRoleService omsRoleService;

    /**
     * 分页查询OMS-系统角色
     * @param param
     * @return result<PageInfo<OmsRoleDTO>>
     */
    @ApiOperation("分页查询OMS-系统角色")
    @GetMapping(value = "/role/list")
    public Result<PageInfo<OmsRoleDTO>> getOmsRoleList(@ModelAttribute OmsRoleDTO param) {
        Result<PageInfo<OmsRoleDTO>> result = new Result<>();
        OmsRole omsRole = BeanCopierUtils.convertObjectTrim(param,OmsRole.class);
        IPage<OmsRole> page = omsRoleService.page(new Page<>(param.getPageNum(), param.getPageSize()), new QueryWrapper<>(omsRole));
        result.setData(new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),BeanCopierUtils.convertList(page.getRecords(), OmsRoleDTO.class)));
        return result;
    }

    @ApiOperation("查询OMS系统角色列表不分页")
    @GetMapping(value = "/role/list_nopage")
    public Result<List<OmsRoleDTO>> getOmsRoleListNopage(@ModelAttribute OmsRoleDTO param) {
        Result<List<OmsRoleDTO>> result = new Result<>();
        OmsRole omsRole = BeanCopierUtils.convertObjectTrim(param,OmsRole.class);
        List<OmsRole> list = omsRoleService.list(new QueryWrapper<>(omsRole));
        result.setData(BeanCopierUtils.convertList(list,OmsRoleDTO.class));
        return result;
    }

    /**
     * 根据ID查询OMS-系统角色
     * @param id
     * @return result<OmsRoleDTO>
     */
    @ApiOperation("根据ID查询OMS-系统角色")
    @GetMapping(value = "/role/info")
    public Result<OmsRoleDTO> getOmsRoleById(@RequestParam("id") Long id) throws ParseException, InterruptedException {
        Result<OmsRoleDTO> result = new Result<>();
        OmsRole omsRole = omsRoleService.getById(id);
        result.setData(BeanCopierUtils.convertObject(omsRole,OmsRoleDTO.class));
        return result;
    }

    /**
     * 保存OMS-系统角色
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "保存OMS-系统角色",type = LogType.OPERATELOG)
    @ApiOperation("保存OMS-系统角色")
    @PostMapping(value = "/role/save")
    public Result<Long> save(@RequestBody OmsRoleDTO param) {
        Result<Long> result = new Result<>();
        OmsRole omsRole = new OmsRole(param.getId(), param.getRoleName(), param.getRoleDesc());
        if(ParamUtils.isNullOr0Long(omsRole.getId())) {
            omsRoleService.save(omsRole);
        }
        else {
            omsRoleService.updateById(omsRole);
        }
        result.setData(omsRole.getId());
        return result;
    }

    /**
     * 根据ID删除OMS系统角色
     * @param param
     * @return result<Boolean>
     */
    @SysLog(value = "根据ID删除OMS系统角色",type = LogType.OPERATELOG)
    @ApiOperation("根据ID删除OMS系统角色")
    @PostMapping(value = "/role/delete")
    public Result<Boolean> deleteOmsRoleById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        omsRoleService.removeById(param.getId());
        result.setData(true);
        return result;
    }

    @ApiOperation("获取角色权限列表")
    @GetMapping(value = "/role/permits")
    public Result<List<Long>> getRolePermitCodes(@RequestParam("roleId") Long roleId) {
        Result<List<Long>> result = new Result<>();
        List<Long> list = omsRoleService.getRolePermitIds(roleId);
        result.setData(list);
        return result;
    }

    @SysLog(value = "绑定角色权限",type = LogType.OPERATELOG)
    @ApiOperation("绑定角色权限")
    @PostMapping(value = "/role/bind_permit")
    public Result<Boolean> bindPermit(@RequestBody RolePermissionDTO param) {
        Result<Boolean> result = new Result<>();
        omsRoleService.bindPermit(param, SecurityContext.getUser().getUid());
        result.setData(true);
        return result;
    }

}
