// components/productDetails/productDetails.js
Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true,
  },
  /**
   * 组件的属性列表
   */
  properties: {
    list: {
      type: Array,
      value: [],
      observer(val) {
        this.handleData(val);
      }
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    listData: []
  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleData(v) {
      const data = v.filter(item => !!item.content);
      console.log(data, 'datadatadatadata');
      this.setData({
        listData: data,
      })
    }
  }

})