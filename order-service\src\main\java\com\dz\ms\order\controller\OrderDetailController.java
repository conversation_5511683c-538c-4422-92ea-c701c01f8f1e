package com.dz.ms.order.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.order.dto.OrderDetailDTO;
import com.dz.ms.order.service.OrderDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;

@Api(tags = "订单详情信息")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class OrderDetailController {

    @Resource
    private OrderDetailService orderDetailService;

    /**
     * 分页查询订单详情信息
     *
     * @param param
     * @return result<PageInfo < OrderDetailDTO>>
     */
    @ApiOperation("分页查询订单详情信息")
    @GetMapping(value = "/order_detail/list")
    public Result<PageInfo<OrderDetailDTO>> getOrderDetailList(@ModelAttribute OrderDetailDTO param) {
        Result<PageInfo<OrderDetailDTO>> result = new Result<>();
        PageInfo<OrderDetailDTO> page = orderDetailService.getOrderDetailList(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询订单详情信息
     *
     * @param id
     * @return result<OrderDetailDTO>
     */
    @ApiOperation("根据ID查询订单详情信息")
    @GetMapping(value = "/order_detail/info")
    public Result<OrderDetailDTO> getOrderDetailById(@RequestParam("id") Long id) {
        Result<OrderDetailDTO> result = new Result<>();
        OrderDetailDTO orderDetail = orderDetailService.getOrderDetailById(id);
        result.setData(orderDetail);
        return result;
    }

    /**
     * 新增订单详情信息
     *
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增订单详情信息", type = LogType.OPERATELOG)
    @ApiOperation("新增订单详情信息")
    @PostMapping(value = "/order_detail/add")
    public Result<Long> addOrderDetail(@RequestBody OrderDetailDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param, true);
        Long id = orderDetailService.saveOrderDetail(param);
        result.setData(id);
        return result;
    }

    /**
     * 更新订单详情信息
     *
     * @param param
     * @return result<Object>
     */
    @SysLog(value = "更新订单详情信息", type = LogType.OPERATELOG)
    @ApiOperation("更新订单详情信息")
    @PostMapping(value = "/order_detail/update")
    public Result<Long> updateOrderDetail(@RequestBody OrderDetailDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param, false);
        orderDetailService.saveOrderDetail(param);
        result.setData(param.getId());
        return result;
    }

    /**
     * 验证保存参数
     *
     * @param param
     */
    private void validationSaveParam(OrderDetailDTO param, boolean isAdd) {
        if (isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if (!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
    }

    /**
     * 根据ID删除订单详情信息
     *
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除订单详情信息")
    @PostMapping(value = "/order_detail/delete")
    public Result<Boolean> deleteOrderDetailById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        orderDetailService.deleteOrderDetailById(param);
        result.setData(true);
        return result;
    }

    @ApiOperation("SFTP获取订单商品数据")
    @PostMapping(value = {"/order/getSftpFile", "/app/order/getSftpFile"})
    public Result<String> getSftpFile() throws IOException {
        Result<String> response = new Result<>();
        orderDetailService.getSftpFile();
        return response;
    }

    @ApiOperation("删除订单")
    @PostMapping(value = {"/order/removeOrder", "/app/order/removeOrder"})
    public Result<String> removeOrder() {
        Result<String> response = new Result<>();
        orderDetailService.removeOrder();
        return response;
    }

}
