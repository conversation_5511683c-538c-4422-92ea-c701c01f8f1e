package com.dz.ms.basic.controller;

import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.basic.TenantConfigDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.fegin.basic.TenantConfigFeignClient;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.ms.basic.dto.NavigationConfigDTO;
import com.dz.ms.basic.service.NavigationConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import java.util.List;

@Api(tags="小程序导航自定义配置")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class NavigationConfigController {

    @Resource
    private NavigationConfigService navigationConfigService;
    @Resource
    private TenantConfigFeignClient tenantConfigFeignClient;

    /**
     * 分页查询小程序UI自定义配置
     * @param param
     * @return result<PageInfo<UiConfigDTO>>
     */
//    @ApiOperation("分页查询小程序导航自定义配置")
//	@GetMapping(value = "/navigation_config/list")
    public Result<PageInfo<NavigationConfigDTO>> getUiConfigList(@ModelAttribute NavigationConfigDTO param) {
        Result<PageInfo<NavigationConfigDTO>> result = new Result<>();
		PageInfo<NavigationConfigDTO> page = navigationConfigService.getUiConfigList(param);
        result.setData(page);
        return result;
    }

//    @ApiOperation("获取所有小程序导航自定义配置列表")
//    @GetMapping(value = "/crm/navigation_config/all")
    public Result<List<NavigationConfigDTO>> getAllUiConfig() {
        Result<List<NavigationConfigDTO>> result = new Result<>();
        List<NavigationConfigDTO> page = navigationConfigService.getAllUiConfig();
        result.setData(page);
        return result;
    }

    @ApiOperation("获取当前导航配置")
    @GetMapping(value = "/crm/navigation_config/default")
    public Result<NavigationConfigDTO> getCheckedUiConfig() {
        Result<NavigationConfigDTO> result = new Result<>();
        NavigationConfigDTO config = navigationConfigService.getCheckedUiConfig();
        result.setData(config);
        return result;
    }

    /**
     * 根据ID查询小程序UI自定义配置
     * @param id
     * @return result<UiConfigDTO>
     */
//    @ApiOperation("根据ID查询小程序UI自定义配置")
//	@GetMapping(value = "/navigation_config/info")
    public Result<NavigationConfigDTO> getUiConfigById(@RequestParam("id") Long id) {
        Result<NavigationConfigDTO> result = new Result<>();
        NavigationConfigDTO uiConfig = navigationConfigService.getUiConfigById(id);
        result.setData(uiConfig);
        return result;
    }

    /**
     * 保存小程序UI自定义配置
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "保存TabBar配置",type = LogType.OPERATELOG)
    @ApiOperation("保存小程序导航自定义配置")
	@PostMapping(value = "/crm/navigation_config/save")
    public Result<Long> saveUiConfig(@RequestBody NavigationConfigDTO param) {
        Result<Long> result = new Result<>();
        Long id = navigationConfigService.saveUiConfig(param);
        result.setData(id);
        return result;
    }
	
	/**
     * 根据ID删除小程序UI自定义配置
     * @param param
     * @return result<Boolean>
     */
//    @SysLog(value = "删除TabBar配置",type = LogType.OPERATELOG)
//    @ApiOperation("根据ID删除小程序UI自定义配置")
//	@PostMapping(value = "/crm/navigation_config/delete")
    public Result<Boolean> deleteUiConfigById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        navigationConfigService.deleteUiConfigById(param);
        result.setData(true);
        return result;
    }

//    @SysLog(value = "选中TabBar配置",type = LogType.OPERATELOG)
//    @ApiOperation("选中导航配置")
//    @PostMapping(value = "/crm/navigation_config/checked")
    public Result<Boolean> checkedUiConfigById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        navigationConfigService.checkedUiConfigById(param);
        result.setData(true);
        return result;
    }

    @ApiOperation("获取当前的导航配置")
    @GetMapping(value = "/app/white/navigation_config/default")
    public Result<NavigationConfigDTO> getDefaultUiConfig(@RequestParam("tenantId") Long tenantId) {
        Result<NavigationConfigDTO> result = new Result<>();
        SecurityContext.setUser(new CurrentUserDTO(tenantId));
        NavigationConfigDTO config = navigationConfigService.getCheckedUiConfig();
//        TenantConfigDTO tenantConfig = tenantConfigFeignClient.getTenantConfigById(tenantId).getData();
//        config.setTenantConfig(tenantConfig);
        result.setData(config);
        return result;
    }

}
