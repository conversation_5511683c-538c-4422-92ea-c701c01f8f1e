package com.dz.common.core.dto.adaptor;

import com.jcraft.jsch.ChannelSftp;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;

/**

 */
@Getter
@Setter
@NoArgsConstructor
@ToString
public class SftpDTO {
    //目标文件名
    private String fileName;
    //进入路径
    private String filePath;

    private String host;
    //端口
    private Integer port;
    //用户名
    private String userName;
    //密码
    private String pass;
    //sftp连接 用于关
    private ChannelSftp sftp;

    private InputStream in;
}
