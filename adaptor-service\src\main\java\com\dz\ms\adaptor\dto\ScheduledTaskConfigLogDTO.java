package com.dz.ms.adaptor.dto;
import java.util.Date;
import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 定时任务日志表DTO
 * @author: 
 * @date:   2025/03/17 11:26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@ApiModel(value = "定时任务日志表")
public class ScheduledTaskConfigLogDTO extends BaseDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "task配置表id")
    private Long taskConfigId;
    @ApiModelProperty(value = "任务名称")
    private String taskName;
    @ApiModelProperty(value = "Cron表达式")
    private String cronExpression;
    @ApiModelProperty(value = "参数")
    private String param;
    @ApiModelProperty(value = "执行时间")
    private Long runTime;
    @ApiModelProperty(value = "状态(0进行中 1成功 2失败)")
    private Integer status;
    @ApiModelProperty(value = "创建时间")
    private Date createdAt;
    @ApiModelProperty(value = "更新时间")
    private Date updatedAt;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;

}
