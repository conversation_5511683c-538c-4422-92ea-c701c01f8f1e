/*
100 - Thin
200 - Extra Light (Ultra Light)
300 - Light
400 - Regular (Normal、Book、Roman)
500 - Medium
600 - Semi Bold (Demi Bold)
700 - Bold
800 - Extra Bold (Ultra Bold)
900 - Black (Heavy)

Android使用思源黑体：SourceHanSansCN
IOS使用苹果字体：PingFang SC
*/
// @font-face {
//   font-family: 'MUJIFont2020';
//   font-weight: 900;
//   src: url('https://vip-dev.oss-cn-shanghai.aliyuncs.com/muji/muji-mini/fonts/MUJIFont2020-Heavy.otf') format('opentype');
//   unicode-range: U+0000-00FF, U+0100-024F, U+FF00-FFEF;
// }

// @font-face {
//   font-family: 'MUJIFont2020';
//   font-weight: 700;
//   src: url('https://vip-dev.oss-cn-shanghai.aliyuncs.com/muji/muji-mini/fonts/MUJIFont2020-Bold.otf') format('opentype');
//   unicode-range: U+0000-00FF, U+0100-024F, U+FF00-FFEF;
// }

// @font-face {
//   font-family: 'MUJIFont2020';
//   font-weight: 400;
//   src: url('https://vip-dev.oss-cn-shanghai.aliyuncs.com/muji/muji-mini/fonts/MUJIFont2020-Regular.otf') format('opentype');
//   unicode-range: U+0000-00FF, U+0100-024F, U+FF00-FFEF;
// }

// @font-face {
//   font-family: 'MUJIFont2020';
//   font-weight: 300;
//   src: url('https://vip-dev.oss-cn-shanghai.aliyuncs.com/muji/muji-mini/fonts/MUJIFont2020-Light.otf') format('opentype');
//   unicode-range: U+0000-00FF, U+0100-024F, U+FF00-FFEF;
// }

// @font-face {
//   font-family: 'SourceHanSansCN';
//   font-weight: 900;
//   src: url('https://vip-dev.oss-cn-shanghai.aliyuncs.com/muji/muji-mini/fonts/SourceHanSansCN-Heavy_0.otf') format('opentype');
//   unicode-range: U+4E00-9FFF, U+3000-303F, U+FF00-FFEF;
// }

// @font-face {
//   font-family: 'SourceHanSansCN';
//   font-weight: 700;
//   src: url('https://vip-dev.oss-cn-shanghai.aliyuncs.com/muji/muji-mini/fonts/SourceHanSansCN-Bold_0.otf') format('opentype');
//   unicode-range: U+4E00-9FFF, U+3000-303F, U+FF00-FFEF;
// }

// @font-face {
//   font-family: 'SourceHanSansCN';
//   src: url('https://vip-dev.oss-cn-shanghai.aliyuncs.com/muji/muji-mini/fonts/SourceHanSansCN-Medium_0.otf') format('opentype');
//   font-weight: 500;
//   unicode-range: U+4E00-9FFF, U+3000-303F, U+FF00-FFEF;
// }

// @font-face {
//   font-family: 'SourceHanSansCN';
//   font-weight: 400;
//   src: url('https://vip-dev.oss-cn-shanghai.aliyuncs.com/muji/muji-mini/fonts/SourceHanSansCN-Regular_1.otf') format('opentype');
//   unicode-range: U+4E00-9FFF, U+3000-303F, U+FF00-FFEF;
// }

// @font-face {
//   font-family: 'SourceHanSansCN';
//   font-weight: 300;
//   src: url('https://vip-dev.oss-cn-shanghai.aliyuncs.com/muji/muji-mini/fonts/SourceHanSansCN-Normal_0.otf') format('opentype');
//   unicode-range: U+4E00-9FFF, U+3000-303F, U+FF00-FFEF;
// }