package com.dz.ms.adaptor.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Table;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 即将过期积分用户记录按月分表
 */
@Data
@NoArgsConstructor
@Table("即将过期积分用户记录按月分表")
@TableName(value = "t_expire_points_record")
public class ExpirePointsRecord {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     * 用户会员code
     */
    private String memberCode;

    /**
     * 当前可用积分数量
     */
    private Integer bonusAmount;

    /**
     * 即将过期积分
     */
    private Integer expirePoints;


    /**
     * 创建时间
     */
    private Date createTime;


    /**
     * 租户ID
     */
    private Long tenantId;


    /**
     * 是否已发送订阅消息 0否，1是
     */
    private Integer isPointsSubscription;


    /**
     * 是否已发送我的消息 0否，1是
     */
    private Integer isSendMyMsg;
}
