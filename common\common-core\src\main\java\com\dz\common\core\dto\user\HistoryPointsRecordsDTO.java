package com.dz.common.core.dto.user;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 即将过期/历史过期积分列表DTO
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "即将过期/历史过期积分列表")
public class HistoryPointsRecordsDTO extends BaseDTO {

    @ApiModelProperty(value = "时间")
    private String expireTime;
    @ApiModelProperty(value = "积分数量")
    private String pointsNum;

}
