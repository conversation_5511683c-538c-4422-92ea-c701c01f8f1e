package com.dz.ms.user.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.base.dto.BaseDTO;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.basic.SubscribeMsgSendDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.dto.user.InteractionTaskDTO;
import com.dz.common.core.dto.user.MyMsgDTO;
import com.dz.common.core.dto.user.UserInfoDTO;
import com.dz.common.core.enums.SubscribeMsgEnum;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.SftpUtils;
import com.dz.common.core.vo.MyMsgVo;
import com.dz.common.core.vo.SubscriptionMsgVo;
import com.dz.ms.user.constants.MyMsgEnum;
import com.dz.ms.user.entity.MyMsg;
import com.dz.ms.user.entity.Store;
import com.dz.ms.user.mapper.MyMsgMapper;
import com.dz.ms.user.service.MUJIOpenApiService;
import com.dz.ms.user.service.MyMsgService;
import com.dz.ms.user.service.UserInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.input.BOMInputStream;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.net.MalformedURLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

/**
 * 我的礼券
 */
@Service
@Slf4j
public class MyMsgServiceImpl implements MyMsgService {

    @Autowired
    private MyMsgMapper myMsgMapper;
    @Autowired
    private MUJIOpenApiService openApiService;
    @Autowired
    private UserInfoService userInfoService;
    @Autowired
    private RedisService redisService;
    private static final int THREADS = 46;
    private static final ExecutorService executorService = Executors.newFixedThreadPool(THREADS);
    private static final Semaphore semaphore = new Semaphore(THREADS);

    @Override
    public PageInfo<MyMsgDTO> msgList(BaseDTO param) {
        //查询用户会员信息
        UserInfoDTO userInfoDTO=userInfoService.getUserInfo(SecurityContext.getUser().getUid());
        if (null == userInfoDTO.getCardNo()){
            return new PageInfo<>();
        }
        // 取模分表
        int mod = Math.abs(userInfoDTO.getCardNo().hashCode()) % 64;
        String nowDay = mod+"";
        String dayTable = subTableList(nowDay);
        if (!StringUtils.isEmpty(dayTable)){
            Integer num = param.getPageNum() == null ? 1 : param.getPageNum();
            Integer size = param.getPageSize() == null ? 10 : param.getPageSize();
            Page<MyMsg> page = new Page<>(num, size);
            IPage<MyMsg> iPage = myMsgMapper.listByUserId(page, userInfoDTO.getCardNo(), nowDay);
            List<MyMsgDTO> myMsgDTOS = new ArrayList<>();
            if (!CollectionUtils.isEmpty(iPage.getRecords())){
                myMsgDTOS=BeanCopierUtils.convertList(iPage.getRecords(), MyMsgDTO.class);
            }
            myMsgMapper.updateByUserId(userInfoDTO.getCardNo(), nowDay);
            return new PageInfo<>(iPage.getCurrent(),iPage.getSize(),iPage.getTotal(),myMsgDTOS);
        }else{
            return new PageInfo<>();
        }
    }

    @Override
    public MyMsgDTO unReadMsgList() {
        MyMsgDTO myMsgDTO=new MyMsgDTO();
        //查询用户会员信息
        UserInfoDTO userInfoDTO=userInfoService.getUserInfo(SecurityContext.getUser().getUid());
        if (null == userInfoDTO.getCardNo()){
            return myMsgDTO;
        }
        // 取模分表
        int mod = Math.abs(userInfoDTO.getCardNo().hashCode()) % 64;
        String nowDay = mod+"";
        String dayTable = subTableList(nowDay);
        if (!StringUtils.isEmpty(dayTable)){
            List<MyMsg> myMsgList = myMsgMapper.listByUnRead(userInfoDTO.getCardNo(),nowDay);

            if (!CollectionUtils.isEmpty(myMsgList)){
                myMsgDTO=BeanCopierUtils.convertObject(myMsgList.get(0), MyMsgDTO.class);
            }
            return myMsgDTO;
        }else{
            return myMsgDTO;
        }
    }

    @Override
    public Integer msgNum() {
        //查询用户会员信息
        UserInfoDTO userInfoDTO=userInfoService.getUserInfo(SecurityContext.getUser().getUid());
        if (null == userInfoDTO.getCardNo()){
            return 0;
        }
        // 取模分表
        int mod = Math.abs(userInfoDTO.getCardNo().hashCode()) % 64;
        String nowDay = mod+"";
        String dayTable = subTableList(nowDay);
        if (!StringUtils.isEmpty(dayTable)){
            List<MyMsg> myMsgList=myMsgMapper.listByUnReadNum(userInfoDTO.getCardNo(),nowDay);
            if (CollectionUtils.isEmpty(myMsgList)){
                return 0;
            }else{
                return myMsgList.size();
            }
        }else{
            return 0;
        }
    }

    @Override
    public void add(MyMsgVo param) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date now=new Date();
        //查询用户会员信息
        UserInfoDTO userInfoDTO=null;
        if (null != param.getUserId()){
            userInfoDTO=userInfoService.getUserInfo(param.getUserId());
        }else{
            userInfoDTO=userInfoService.getUserInfo(SecurityContext.getUser().getUid());
        }
        if (null == userInfoDTO.getCardNo()){
            return;
        }
        // 取模分表
        int mod = Math.abs(userInfoDTO.getCardNo().hashCode()) % 64;
        String nowDay = mod+"";
        String dayTable = subTableList(nowDay);
        for (String code : param.getMsgCode()){
            if (code.equals("expirePoints")){
                //查询当天是否已经有记录
                List<MyMsg> myMsgList = myMsgMapper.selectByMsgCode(userInfoDTO.getCardNo(), code,nowDay);
                if (!CollectionUtils.isEmpty(myMsgList)){
                    continue;
                }
            }else{
                //查询当天是否已经有记录
                List<MyMsg> myMsgList = myMsgMapper.selectByMsgCode(userInfoDTO.getCardNo(), code,nowDay);
                if (!CollectionUtils.isEmpty(myMsgList)){
                    continue;
                }
            }
            if (code.equals("receiveCard")){//会员卡领取
                if (null != userInfoDTO.getCardNo()){
                    //查询开卡状态
                    JSONObject result = openApiService.openCardStatus(userInfoDTO.getCardNo());
                    if (null != result && result.containsKey("wechat_card_status") && result.getInteger("wechat_card_status") == 1){
                        continue;
                    }
                }else{
                    continue;
                }
            }
            if (code.equals("expireCoupon")){//优惠券过期
                if (null != userInfoDTO.getCardNo()){
                    boolean flag=false;
                    //查询活动券
                    JSONObject activityCouponList=openApiService.activityCouponList(userInfoDTO.getCardNo());
                    if (null != activityCouponList){
                        JSONArray activityList=activityCouponList.getJSONArray("items");
                        if (!CollectionUtils.isEmpty(activityList)){
                            for (Object jsonObject:activityList){
                                //将jsonObject转为map
                                JSONObject activityJsonObject=JSONObject.parseObject(JSONObject.toJSONString(jsonObject));
                                String dateline =activityJsonObject.getString("dateline");
                                Date dateLineDate=sdf.parse(dateline);
                                //计算dateLineDate-now的小时数
                                long hours = (dateLineDate.getTime() - now.getTime()) / (1000 * 60 * 60);
                                if (hours <= 48){
                                    flag=true;
                                    break;
                                }
                            }
                        }
                    }
                    //查询会员券列表
                    JSONObject memberCouponList=openApiService.memberCouponList(userInfoDTO.getCardNo(),null,1,1,100);
                    if (null != memberCouponList){
                        JSONArray list=memberCouponList.getJSONArray("items");
                        if (!CollectionUtils.isEmpty(list)){
                            for (Object account :list){
                                //将jsonObject转为map
                                JSONObject jsonObject=JSONObject.parseObject(JSONObject.toJSONString(account));
                                String endTime=jsonObject.getString("end_time");
                                Date endTimeDate=sdf.parse(endTime);
                                //计算endTimeDate-now的小时数
                                long hours = (endTimeDate.getTime() - now.getTime()) / (1000 * 60 * 60);
                                if (hours <= 48){
                                    flag=true;
                                    break;
                                }
                            }
                        }
                    }
                    if (!flag){
                        continue;
                    }
                }else{
                    continue;
                }
            }
            if (code.equals("couponAccount")){//优惠券待领取
                if (null != userInfoDTO.getCardNo()){
                    boolean flag=false;
                    //查询活动券
                    JSONObject activityCouponList=openApiService.activityCouponList(userInfoDTO.getCardNo());
                    if (null != activityCouponList){
                        JSONArray activityList=activityCouponList.getJSONArray("items");
                        if (!CollectionUtils.isEmpty(activityList)){
                            for (Object jsonObject:activityList){
                                //将jsonObject转为map
                                JSONObject activityJsonObject=JSONObject.parseObject(JSONObject.toJSONString(jsonObject));
                                String dateline =activityJsonObject.getString("dateline");
                                Date dateLineDate=sdf.parse(dateline);
                                //计算dateLineDate-now的小时数
                                long hours = (dateLineDate.getTime() - now.getTime()) / (1000 * 60 * 60);
                                if (hours > 48){
                                    flag=true;
                                    break;
                                }
                            }
                        }
                    }
                    if (!flag){
                        continue;
                    }
                }else{
                    continue;
                }
            }
            if (!StringUtils.isEmpty(dayTable)){
                MyMsg myMsg = new MyMsg();
                myMsg.setUserId(userInfoDTO.getCardNo());
                myMsg.setTitle(MyMsgEnum.valueOf(code).getTitle());
                myMsg.setMsgDesc(MyMsgEnum.valueOf(code).getDesc());
                myMsg.setJumpUrl(MyMsgEnum.valueOf(code).getJumpUrl());
                myMsg.setHomeTitle(MyMsgEnum.valueOf(code).getHomeTitle());
                myMsg.setIsRead(2);
                myMsg.setCreateAt(String.valueOf(SecurityContext.getUser().getUid()));
                myMsg.setTenantId(SecurityContext.getUser().getTenantId());
                myMsg.setCreateTime(new Date());
                myMsg.setMsgCode(code);
                myMsg.setSortNum(MyMsgEnum.valueOf(code).getSortNum());
                myMsgMapper.inserByTable(myMsg, nowDay);
            }
        }
    }

    @Override
    public void read(Long id) {
        //查询用户会员信息
        UserInfoDTO userInfoDTO=userInfoService.getUserInfo(SecurityContext.getUser().getUid());
        if (null == userInfoDTO.getCardNo()){
            return;
        }
        // 取模分表
        int mod = Math.abs(userInfoDTO.getCardNo().hashCode()) % 64;
        String nowDay = mod+"";
        myMsgMapper.updateReadById(id, nowDay);
    }

    @Override
    public void sync(MyMsgVo msgCode, HttpServletResponse response) throws MalformedURLException {
        if (null == msgCode.getUserId()){
            throw new BusinessException(ErrorCode.BAD_REQUEST, "userId不能为空");
        }
        CurrentUserDTO userInfoDTO = new CurrentUserDTO();
        userInfoDTO.setTenantId(1L);
        SecurityContext.setUser(userInfoDTO);
        if (!CollectionUtils.isEmpty(msgCode.getMsgCode())){
            for(String fileName:msgCode.getMsgCode()){
                InputStream inputStream = SftpUtils.downloadMyMsg(response,fileName,"https://vip-dev.oss-cn-shanghai.aliyuncs.com");
                if (null != inputStream){
                    executorService.submit(() -> {
                        try {
                            semaphore.acquire(); // 获取许可证
                            SecurityContext.setUser(userInfoDTO);
                            if (msgCode.getUserId().equals(1L)){
                                syncMyMsgData(inputStream,fileName);
                            }else if (msgCode.getUserId().equals(2L)){
                                syncMyMsgDataNext(inputStream,fileName);
                            }else{
                                syncPointsMyMsgData(inputStream,fileName);
                            }
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        } finally {
                            semaphore.release(); // 任务完成后释放许可证
                        }
                    });
                }
            }
        }
    }
    private void syncMyMsgData(InputStream data,String fileName) {
        Map<Integer,List<MyMsg>> map=new HashMap<>();
        for (int i = 0; i < 64; i++) {
            map.put(i, new ArrayList<>());
        }
        SecurityContext.setUser(new CurrentUserDTO(1L, 1L));
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String spFileName=fileName.replace(".csv","");
        redisService.setString("msg:success:start:"+spFileName,sdf.format(new Date()));
        try (InputStream bomInputStream = new BOMInputStream(data);
             Reader reader = new InputStreamReader(bomInputStream);
             CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT.builder().setHeader().build())) {

            // 调试列名
            List<String> headerNames = csvParser.getHeaderNames();
            log.debug("CSV myMsg Header Names: {}", headerNames);
            for (CSVRecord csvRecord : csvParser) {
                String memberCode = csvRecord.get("member_code");
                if (!memberCode.contains("MJ")){
                    continue;
                }
                String title = csvRecord.get("title");
                String content = csvRecord.get("content");
                String createdAt = csvRecord.get("created_at");
                if (!StringUtils.isEmpty(createdAt)){
                    String[] strTime=createdAt.split(":");
                    if (strTime.length == 1){
                        createdAt+=":00:00";
                    }else if (strTime.length == 2){
                        createdAt+=":00";
                    }
                }
                // 取模分表
                int mod = Math.abs(memberCode.hashCode()) % 64;
                try {
                    List<MyMsg> myMsgList=map.get(mod);
                    MyMsg msg = new MyMsg();
                    msg.setMsgCode("history");
                    msg.setTitle(title);
                    msg.setMsgDesc(content);
                    msg.setCreateAt("1");
                    if (!StringUtils.isEmpty(createdAt)){
                        if (createdAt.contains("/")){
                            msg.setCreateTime(sdf.parse(createdAt));
                        }else{
                            msg.setCreateTime(sdf1.parse(createdAt));
                        }
                    }
                    msg.setTenantId(1L);
                    msg.setUserId(memberCode);
                    msg.setIsRead(1);
                    msg.setMsgType(2);
                    msg.setHomeTitle(fileName);
                    myMsgList.add(msg);
                    if (myMsgList.size()>=3000){
                        String nowDay = mod+"";
                        String dayTable = subTableList(nowDay);
                        if (!StringUtils.isEmpty(dayTable)){
                            myMsgMapper.inserListByTable(myMsgList, nowDay);
                            map.put(mod, new ArrayList<>());
                        }
                    }
                } catch (Exception e) {
                    redisService.setString("msg:file:myMsg_"+mod,spFileName);
                    log.error("myMsg_"+mod+",数据初始化错误：" + e.getMessage());
                }
            }
            for (int i = 0; i < 64; i++) {
                List<MyMsg> myMsgList=map.get(i);
                if (!CollectionUtils.isEmpty(myMsgList)){
                    String nowDay = i+"";
                    String dayTable = subTableList(nowDay);
                    if (!StringUtils.isEmpty(dayTable)){
                        myMsgMapper.inserListByTable(myMsgList, nowDay);
                    }
                }
            }
            redisService.setString("msg:success:end:"+spFileName,sdf.format(new Date()));
            log.info("myMsg user data processed successfully");
        } catch (Exception e) {
            log.error("Error myMsg processing CSV file", e);
            redisService.setString("msg:file:"+spFileName,spFileName);
        }
    }

    private void syncMyMsgDataNext(InputStream data,String fileName) {
        Map<Integer,List<MyMsg>> map=new HashMap<>();
        for (int i = 0; i < 64; i++) {
            map.put(i, new ArrayList<>());
        }
        SecurityContext.setUser(new CurrentUserDTO(1L, 1L));
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String spFileName=fileName.replace(".csv","");
        redisService.setString("msg:success:start:"+spFileName,sdf.format(new Date()));
        try (InputStream bomInputStream = new BOMInputStream(data);
             Reader reader = new InputStreamReader(bomInputStream);
             CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT)) {

            // 调试列名
            List<String> headerNames = csvParser.getHeaderNames();
            log.debug("CSV myMsg Header Names: {}", headerNames);
            for (CSVRecord csvRecord : csvParser) {
                String memberCode = csvRecord.get(0);
                if (!memberCode.contains("MJ")){
                    continue;
                }
                String title = csvRecord.get(5);
                String content = csvRecord.get(6);
                String createdAt = csvRecord.get(9);
                if (!StringUtils.isEmpty(createdAt)){
                    String[] strTime=createdAt.split(":");
                    if (strTime.length == 1){
                        createdAt+=":00:00";
                    }else if (strTime.length == 2){
                        createdAt+=":00";
                    }
                }
                // 取模分表
                int mod = Math.abs(memberCode.hashCode()) % 64;
                try {
                    List<MyMsg> myMsgList=map.get(mod);
                    MyMsg msg = new MyMsg();
                    msg.setMsgCode("history");
                    msg.setTitle(title);
                    msg.setMsgDesc(content);
                    msg.setCreateAt("1");
                    if (!StringUtils.isEmpty(createdAt)){
                        if (createdAt.contains("/")){
                            msg.setCreateTime(sdf.parse(createdAt));
                        }else{
                            msg.setCreateTime(sdf1.parse(createdAt));
                        }
                    }
                    msg.setTenantId(1L);
                    msg.setUserId(memberCode);
                    msg.setIsRead(1);
                    msg.setMsgType(2);
                    msg.setHomeTitle(fileName);
                    myMsgList.add(msg);
                    if (myMsgList.size()>=3000){
                        String nowDay = mod+"";
                        String dayTable = subTableList(nowDay);
                        if (!StringUtils.isEmpty(dayTable)){
                            myMsgMapper.inserListByTable(myMsgList, nowDay);
                            map.put(mod, new ArrayList<>());
                        }
                    }
                } catch (Exception e) {
                    redisService.setString("msg:file:myMsg_"+mod,spFileName);
                    log.error("myMsg_"+mod+",数据初始化错误：" + e.getMessage());
                }
            }
            for (int i = 0; i < 64; i++) {
                List<MyMsg> myMsgList=map.get(i);
                if (!CollectionUtils.isEmpty(myMsgList)){
                    String nowDay = i+"";
                    String dayTable = subTableList(nowDay);
                    if (!StringUtils.isEmpty(dayTable)){
                        myMsgMapper.inserListByTable(myMsgList, nowDay);
                    }
                }
            }
            redisService.setString("msg:success:end:"+spFileName,sdf.format(new Date()));
            log.info("myMsg user data processed successfully");
        } catch (Exception e) {
            log.error("Error myMsg processing CSV file", e);
            redisService.setString("msg:file:"+spFileName,spFileName);
        }
    }

    /**
     * 积分过期提醒临时方案，crm给人群包memberCode  会小导入站内消息发送
     * @param data
     * @param fileName
     */
    private void syncPointsMyMsgData(InputStream data,String fileName) {
        Date now=new Date();
        Map<Integer,List<MyMsg>> map=new HashMap<>();
        for (int i = 0; i < 64; i++) {
            map.put(i, new ArrayList<>());
        }
        SecurityContext.setUser(new CurrentUserDTO(1L, 1L));
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String spFileName=fileName.replace(".csv","");
        redisService.setString("msg:points:success:start:"+spFileName,sdf.format(new Date()));
        try (InputStream bomInputStream = new BOMInputStream(data);
             Reader reader = new InputStreamReader(bomInputStream);
             CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT.builder().setHeader().build())) {

            // 调试列名
            List<String> headerNames = csvParser.getHeaderNames();
            log.debug("CSV myMsg Header Names: {}", headerNames);
            for (CSVRecord csvRecord : csvParser) {
                String memberCode = csvRecord.get("member_code");
                // 取模分表
                int mod = Math.abs(memberCode.hashCode()) % 64;
                try {
                    List<MyMsg> myMsgList=map.get(mod);
                    MyMsg msg = new MyMsg();
                    msg.setMsgCode("expirePoints");
                    msg.setTitle("积分过期提醒");
                    msg.setMsgDesc("您有一些积分即将过期，去兑礼商城看看吧，点击兑礼");
                    msg.setJumpUrl("/pages/life/life");
                    msg.setCreateAt("1");
                    msg.setCreateTime(now);
                    msg.setTenantId(1L);
                    msg.setUserId(memberCode);
                    msg.setIsRead(2);
                    msg.setMsgType(1);
                    msg.setHomeTitle("您有一些积分即将过期，点击进行兑礼");
                    msg.setSortNum(4);
                    myMsgList.add(msg);
                    if (myMsgList.size()>=3000){
                        String nowDay = mod+"";
                        String dayTable = subTableList(nowDay);
                        if (!StringUtils.isEmpty(dayTable)){
                            myMsgMapper.inserListByTable(myMsgList, nowDay);
                            map.put(mod, new ArrayList<>());
                        }
                    }
                } catch (Exception e) {
                    redisService.setString("msg:points:file:myMsg_"+mod,spFileName);
                    log.error("myMsg_"+mod+",数据初始化错误：" + e.getMessage());
                }
            }
            for (int i = 0; i < 64; i++) {
                List<MyMsg> myMsgList=map.get(i);
                if (!CollectionUtils.isEmpty(myMsgList)){
                    String nowDay = i+"";
                    String dayTable = subTableList(nowDay);
                    if (!StringUtils.isEmpty(dayTable)){
                        myMsgMapper.inserListByTable(myMsgList, nowDay);
                    }
                }
            }
            redisService.setString("msg:points:success:end:"+spFileName,sdf.format(new Date()));
            log.info("myMsg user data processed successfully");
        } catch (Exception e) {
            log.error("Error myMsg processing CSV file", e);
            redisService.setString("msg:points:file:"+spFileName,spFileName);
        }
    }
    /**
     * 判断当月消息表是否存在，不存在则新建
     * @param cdpDay
     */
    private String subTableList(String cdpDay) {
        try {
            String cdpDayRedis=redisService.getString("msg:create:table:new:"+cdpDay);
            if (!StringUtils.isEmpty(cdpDayRedis)){
                return cdpDay;
            }
            List<String> tableList = myMsgMapper.querySubTableList();
            boolean hasCurrentTable = false;
            if (!CollectionUtils.isEmpty(tableList)) {
                for (String table : tableList) {
                    String newTableName="t_my_msg_"+cdpDay;
                    if (newTableName.equals(table)) {
                        hasCurrentTable = true;
                        redisService.setString("msg:create:table:new:"+cdpDay,cdpDay);
                    }
                }
            }
            if (!hasCurrentTable) {
                myMsgMapper.createSubTable(cdpDay);
                redisService.setString("msg:create:table:new:"+cdpDay,cdpDay);
            }
            return cdpDay;
        } catch (Exception e) {
            log.error("myMsgTable创建失败", e);
            return null;
        }
    }

    public static void main(String[] args) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        System.out.println(sdf.parse("2024/12/14 15:58:38"));
    }
}
