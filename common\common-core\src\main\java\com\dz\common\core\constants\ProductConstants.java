package com.dz.common.core.constants;

/**
 * PRODUCT常量信息
 * 
 * <AUTHOR>
 */
public class ProductConstants
{
    /**
     * 货架相关状态
     */
    public static class Shelf {
        /*货架状态*/
        /**未开始*/
        public static final int STATE_NO_START = 1;
        /**上架中*/
        public static final int STATE_IS_UP = 2;
        /**已结束*/
        public static final int STATE_IS_END = 3;

        /*货架上架类型*/
        /**永久上架*/
        public static final int ON_TYPE_ONE = 1;
        /**时间段*/
        public static final int ON_TYPE_TWO = 2;

        /*是否根据人群展示*/
        /**不根据人群展示*/
        public static final int NO_LIMIT_SHOW = 0;
        /**根据人群展示*/
        public static final int IS_LIMIT_SHOW = 1;
        
        
    }

    /**
     * 货架营销活动相关状态
     */
    public static class ShelfCampaign {
        /*活动状态*/
        /**未开始*/
        public static final int STATE_NO_START = 1;
        /**上架中*/
        public static final int STATE_IS_UP = 2;
        /**已结束*/
        public static final int STATE_IS_END = 3;

        /*活动时间类型*/
        /**永久启用*/
        public static final int ON_TYPE_ONE = 1;
        /**时间段*/
        public static final int ON_TYPE_TWO = 2;

        /*限购规则*/
        /**活动时间内(时间段)*/
        public static final int TIME_PERIOD = 0;
        /**周期*/
        public static final int CYCLE = 1;


    }

    /**
     * 货架商品相关状态
     */
    public static class ShelfPro {

        /*货架上架类型*/
        /**
         * 永久上架
         */
        public static final int IS_SHOW = 1;
        /**
         * 已结束
         */
        public static final int NO_SHOW = 2;

    }

    /**
     * 货架商品任务相关状态
     */
    public static class ProductOnTask {
        //上架类型 1实时 2单次时间 3周期
        public static final int ON_TYPE_NOW = 1;
        public static final int ON_TYPE_ONCE = 2;
        public static final int ON_TYPE_PER_PERIOD = 3;

        // 任务状态 0待完成 1已完成 2进行中 3待编辑
        public static final int STATE_PENDING = 0;
        public static final int STATE_DONE = 1;
        public static final int STATE_ON_GOING = 2;

        public static final int STATE_NEED_SETUP = 3;
    }


}
