package com.dz.ms.user.controller;

//import com.alibaba.fastjson.JSON;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.dz.common.base.vo.Result;
//import com.dz.common.core.constants.WechatMessageEventConstants;
//import com.dz.common.core.dto.basic.QywxConfigDTO;
//import com.dz.common.core.dto.user.InteractionTaskDTO;
//import com.dz.common.core.fegin.basic.QywxConfigFeignClient;
//import com.dz.common.core.utils.MessageUtil;
//import com.dz.common.core.utils.aes.WXBizMsgCrypt;
//import com.dz.ms.user.constants.WxEeventType;
//import com.dz.ms.user.constants.WxMessageType;
//import com.dz.ms.user.entity.InteractionTask;
//import com.dz.ms.user.entity.UserTaskRecord;
//import com.dz.ms.user.mapper.InteractionTaskMapper;
//import com.dz.ms.user.mapper.UserTaskRecordMapper;
//import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.io.IOUtils;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.http.HttpMethod;
//import org.springframework.util.CollectionUtils;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

//import javax.annotation.Resource;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.io.InputStream;
//import java.util.Date;
//import java.util.List;
//import java.util.Map;

/**
 * 类注释
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/5/24 15:54
 */
@RestController
@Slf4j
public class WxcpMessageController {
//    @Resource
//    private QywxConfigFeignClient wechatFeignClient;
//    @Resource
//    private InteractionTaskMapper interactionTaskMapper;
//    @Resource
//    private UserTaskRecordMapper userTaskRecordMapper;

//    @GetMapping("/openapi/qywx/notify/{tenantId}")
//    @ApiOperation("企微回调认证")
//    public void getWxDecrypMasg(@RequestParam(value = "msg_signature") String msgSignature,
//                                @RequestParam(value = "timestamp") String timestamp,
//                                @RequestParam(value = "nonce") String nonce,
//                                @RequestParam(value = "echostr", required = false) String echostr,
//                                @PathVariable Long tenantId, HttpServletRequest request, HttpServletResponse response) {
//        Result<QywxConfigDTO> config =
//                wechatFeignClient.getQywxConfigByTenantId(tenantId);
//        String corpId = config.getData().getCorpId();
//        String token = config.getData().getToken();
//        String encodingAESKey = config.getData().getEncodingAesKey();
//        try {
//            log.info("接收企微回调，msg_signature: {}, timestamp: {}, nonce: {}, echostr: {}", msgSignature, timestamp, nonce, echostr);
//            InputStream inputStream = request.getInputStream();
//            String postData = IOUtils.toString(inputStream, "UTF-8");
//
//            WXBizMsgCrypt wxBizMsgCrypt = new WXBizMsgCrypt(token, encodingAESKey, corpId);
//            if (HttpMethod.GET.matches(request.getMethod())) {
//                //验证URL
//                String code = wxBizMsgCrypt.verifyUrl(msgSignature, timestamp, nonce, echostr);
//                log.info("验证URL成功, code: {}", code);
//                response.getWriter().print(code);
//                return;
////                return code;
//            }
//
//        } catch (Exception e) {
//            log.error("微信回调出错", e);
//        }
////        return "";
//
//    }

//    @PostMapping("/openapi/qywx/notify/{tenantId}")
//    @ApiOperation("企微回调")
//    public String postWxDecrypMasg(@RequestParam(value = "msg_signature") String msgSignature,
//                                @RequestParam(value = "timestamp") String timestamp,
//                                @RequestParam(value = "nonce") String nonce,
//                                @RequestParam(value = "echostr", required = false) String echostr,
//                                @PathVariable Long tenantId, HttpServletRequest request, HttpServletResponse response) {
//        Result<QywxConfigDTO> config =
//                wechatFeignClient.getQywxConfigByTenantId(tenantId);
//        String corpId = config.getData().getCorpId();
//        String token = config.getData().getToken();
//        String encodingAESKey = config.getData().getEncodingAesKey();
//        try {
//            log.info("接收企微回调，msg_signature: {}, timestamp: {}, nonce: {}, echostr: {}", msgSignature, timestamp, nonce, echostr);
//            InputStream inputStream = request.getInputStream();
//            String postData = IOUtils.toString(inputStream, "UTF-8");
//
//            WXBizMsgCrypt wxBizMsgCrypt = new WXBizMsgCrypt(token, encodingAESKey, corpId);
//            if (HttpMethod.POST.matches(request.getMethod())) {
//                String msg = wxBizMsgCrypt.decryptMsg(msgSignature, timestamp, nonce, postData);
//                //将post数据转换为map
//                Map<String, String> dataMap = MessageUtil.parseXml(msg);
//                log.info("after decrypt msg: {}", JSON.toJSONString(dataMap));
//                if (StringUtils.isNotEmpty(dataMap.get("MsgType")) &&
//                        StringUtils.isNotEmpty(dataMap.get("Event"))) {
//                    String msgType = dataMap.get("MsgType");
//                    String event = dataMap.get("Event");
//                    String changeType = dataMap.get("ChangeType");
//                    String externalUserID = dataMap.get("ExternalUserID");
//                    boolean isSubcribe = WxMessageType.EVENT.getCode().equals(msgType) &&
//                            (WxEeventType.SUBSCRIBE.getCode().equals(event));
//                    // 只处理关注消息
//                    if (isSubcribe) {
//                        log.info("开始处理首次关注公众号互动活动");
//                        LambdaQueryWrapper<InteractionTask> wrapper = new LambdaQueryWrapper<>();
//                        wrapper.eq(InteractionTask::getIsOnlyOne,0);
//                        wrapper.like(InteractionTask::getTaskName,"公众号");
//                        List<InteractionTask> taskList = interactionTaskMapper.selectList(wrapper);
//                        InteractionTask task = taskList.get(0);
//                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
//                        wrapperRecord.eq(UserTaskRecord::getTaskId,task.getId());
////                        wrapperRecord.eq(UserTaskRecord::getUserId,)
//                        List<UserTaskRecord> userTaskRecord = userTaskRecordMapper.selectList(wrapperRecord);
//                        if (CollectionUtils.isEmpty(userTaskRecord)){
//                            //增加积分接口
//
//
//
//
//                            Date now = new Date();
//                            UserTaskRecord userTaskRecord1 = new UserTaskRecord();
////                            userTaskRecord1.setUserId();
//                            userTaskRecord1.setTaskId(task.getId());
////                            userTaskRecord1.setCreateAt();
//                            userTaskRecord1.setCreateTime(now);
////                            userTaskRecord1.setUpdateAt();
//                            userTaskRecord1.setUpdateTime(now);
//                            userTaskRecord1.setTenantId(tenantId);
//                            userTaskRecordMapper.insert(userTaskRecord1);
//                        }
//                    }
//                    boolean isAddUser = WxMessageType.EVENT.getCode().equals(msgType) &&
//                            (WxEeventType.CHANGEEXTERNALCONTACT.getCode().equals(event) &&
//                                    WxEeventType.ADDEXTERNALCONTACT.getCode().equals(changeType));
//                    //只处理首次添加门店企微
//                    if (isAddUser){
//                        log.info("开始处理首次添加门店企微互动活动");
//                        LambdaQueryWrapper<InteractionTask> wrapper = new LambdaQueryWrapper<>();
//                        wrapper.eq(InteractionTask::getIsOnlyOne,0);
//                        wrapper.like(InteractionTask::getTaskName,"门店企微");
//                        List<InteractionTask> taskList = interactionTaskMapper.selectList(wrapper);
//                        InteractionTask task = taskList.get(0);
//                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
//                        wrapperRecord.eq(UserTaskRecord::getTaskId,task.getId());
////                        wrapperRecord.eq(UserTaskRecord::getUserId,)
//                        List<UserTaskRecord> userTaskRecord = userTaskRecordMapper.selectList(wrapperRecord);
//                        if (CollectionUtils.isEmpty(userTaskRecord)){
//                            //增加积分接口
//
//
//
//
//                            Date now = new Date();
//                            UserTaskRecord userTaskRecord1 = new UserTaskRecord();
////                            userTaskRecord1.setUserId();
//                            userTaskRecord1.setTaskId(task.getId());
////                            userTaskRecord1.setCreateAt();
//                            userTaskRecord1.setCreateTime(now);
////                            userTaskRecord1.setUpdateAt();
//                            userTaskRecord1.setUpdateTime(now);
//                            userTaskRecord1.setTenantId(tenantId);
//                            userTaskRecordMapper.insert(userTaskRecord1);
//                        }
//                    }
//                    //还差一个进入企微社区群回调
//                }
//                return "success";
//            }
//
//        } catch (Exception e) {
//            log.error("微信回调出错", e);
//        }
//        return "";
//    }
}
