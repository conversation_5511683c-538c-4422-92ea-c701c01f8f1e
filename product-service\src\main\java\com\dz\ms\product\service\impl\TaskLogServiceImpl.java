package com.dz.ms.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.product.dto.TaskLogDTO;
import com.dz.ms.product.entity.TaskLog;
import com.dz.ms.product.mapper.TaskLogMapper;
import com.dz.ms.product.service.TaskLogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 日志-货架商品库存任务
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:53
 */
@Service
public class TaskLogServiceImpl extends ServiceImpl<TaskLogMapper, TaskLog> implements TaskLogService {

    @Resource
    private TaskLogMapper taskLogMapper;

    /**
     * 分页查询日志-货架商品库存任务
     *
     * @param param
     * @return PageInfo<TaskLogDTO>
     */
    @Override
    public PageInfo<TaskLogDTO> getTaskLogList(TaskLogDTO param) {
        TaskLog taskLog = BeanCopierUtils.convertObjectTrim(param, TaskLog.class);
        IPage<TaskLog> page = taskLogMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), new QueryWrapper<>(taskLog));
        return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), BeanCopierUtils.convertList(page.getRecords(), TaskLogDTO.class));
    }

    /**
     * 根据ID查询日志-货架商品库存任务
     *
     * @param id
     * @return TaskLogDTO
     */
    @Override
    public TaskLogDTO getTaskLogById(Long id) {
        TaskLog taskLog = taskLogMapper.selectById(id);
        return BeanCopierUtils.convertObject(taskLog, TaskLogDTO.class);
    }

    /**
     * 保存日志-货架商品库存任务
     *
     * @param param
     * @return Long
     */
    @Override
    public Long saveTaskLog(TaskLogDTO param) {
        TaskLog taskLog = new TaskLog(param.getId(), param.getTaskId(), param.getShelfId(), param.getShelfName(), param.getProductId(), param.getProductName(), param.getOnInventory(), param.getCurrentInventory());
        if (ParamUtils.isNullOr0Long(taskLog.getId())) {
            taskLogMapper.insert(taskLog);
        } else {
            taskLogMapper.updateById(taskLog);
        }
        return taskLog.getId();
    }

    /**
     * 根据ID删除日志-货架商品库存任务
     *
     * @param param
     */
    @Override
    public void deleteTaskLogById(IdCodeDTO param) {
        taskLogMapper.deleteById(param.getId());
    }

}