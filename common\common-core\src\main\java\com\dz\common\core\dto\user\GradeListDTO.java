package com.dz.common.core.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "会员等级")
public class GradeListDTO {

    @ApiModelProperty(value = "主键id")
    private Long id;
    @ApiModelProperty(value = "会员等级名称")
    private String gradeName;
    @ApiModelProperty(value = "会员等级编码")
    private String gradeCode;
}
