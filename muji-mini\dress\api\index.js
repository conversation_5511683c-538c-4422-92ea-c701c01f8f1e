/**
 * 添加试穿记录
 * @param {Object} data
 * @param {Number} data.templateId 模板id 1-白色 2-黑色
 * @param {String} data.imgUrl 图片地址
 * @returns
 */
export function addFitting(data) {
  return wx.$request({ url: '/app/sales/fitting/add', method: 'post', data })
}

/**
 * 获取试穿记录
 * @param {Object} data
 * @param {Number} data.pageNum 页码
 * @param {Number} data.pageSize 每页数量
 * @returns
 */
export function getFitting(data) {
  return wx.$request({ url: '/app/sales/fitting/list', data })
}

/**
 * 删除试穿记录
 * @param {Object} data
 * @param {Number} data.id 试穿记录id
 * @returns
 */
export function deleteFitting(data) {
  return wx.$request({ url: '/app/sales/fitting/delete', method: 'post', data })
}
