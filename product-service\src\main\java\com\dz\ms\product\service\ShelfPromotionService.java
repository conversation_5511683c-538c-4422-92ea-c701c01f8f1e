package com.dz.ms.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.IdNumberDTO;
import com.dz.ms.product.dto.ShelfPromotionDTO;
import com.dz.ms.product.dto.inreq.ShelfPromotionQueryDTO;
import com.dz.ms.product.dto.req.ShelfPromotionParamDTO;
import com.dz.ms.product.entity.ShelfPromotion;

/**
 * 货架推广活动接口
 *
 * @author: LiinNs
 * @date: 2024/11/20 11:36
 */
public interface ShelfPromotionService extends IService<ShelfPromotion> {

    /**
     * 分页查询货架推广活动
     *
     * @param param
     * @return PageInfo<ShelfPromotionDTO>
     */
    PageInfo<ShelfPromotionDTO> getShelfPromotionList(ShelfPromotionParamDTO param);

    /**
     * 根据货架ID获取有效的货架推广位
     * 该方法用于查询当前时间正在生效的上架的货架推广位，即开始时间早于当前时间且结束时间晚于当前时间且上架的记录
     * 如果存在符合条件的促销信息，则将第一条记录转换为DTO并返回
     * @param shelfId 货架ID
     * @return ShelfPromotionDTO 返回货架推广位的DTO对象，如果找不到符合条件的促销信息，则返回null
     */
    ShelfPromotionDTO getShelfPromotionByShelfId(Long shelfId);

    /**
     * 根据ID查询货架推广活动
     * @param qryDTO 查询条件
     * @return ShelfPromotionDTO
     */
    ShelfPromotionDTO getShelfPromotionById(ShelfPromotionQueryDTO qryDTO);

    /**
     * 保存货架推广活动
     *
     * @param param
     * @return Long
     */
    public Long saveShelfPromotion(ShelfPromotionDTO param);

    /**
     * 根据ID删除货架推广活动
     *
     * @param param
     */
    public void deleteShelfPromotionById(IdCodeDTO param);

    /**
     * 根据ID修改启停状态
     *
     * @param param ID NUMBER 通用DTO
     */
    void updateStateById(IdNumberDTO param);

    /**
     * 导出推广列表
     *
     * @param exportParam
     */
    void exportPromotionList(DownloadAddParamDTO exportParam);
}
