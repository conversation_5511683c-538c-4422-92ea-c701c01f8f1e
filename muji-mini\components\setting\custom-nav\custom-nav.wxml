<view class="custom-nav" style='height:{{navBarHeight+statusBarHeight}}px'>
  <!-- 背景设置 -->
  <!-- bgColorStyle 0-无 1-默认 2-使用自定义的 -->
  <!-- navType 1-固定显示 2-完全沉浸  3-滑动恢复 4-固定滑动 -->
  <!-- 固定滑动之后的背景 -->
  <custom-bg bgSetting="{{navSetting.bgColorStyle==1?defaultNavSetting:navSetting.move}}" class="bgStyle" wx:if="{{navSetting.bgColorStyle&&navSetting.navType==4&&scrollTop>300}}"></custom-bg>
  <!-- 其他背景 -->
  <custom-bg bgSetting="{{navSetting.bgColorStyle==1?defaultNavSetting:navSetting}}" class="bgStyle" wx:elif="{{navSetting.bgColorStyle&&(navSetting.navType!=3||scrollTop>300)}}"></custom-bg>
  <!-- 内容设置 -->

  <!-- 其他内容 -->
  <view class="custom-nav-content {{navSetting.navType==4&&scrollTop>300?'hidden':'show'}}" style="top:{{statusBarHeight}}px">
    <block wx:if="{{navSetting.navType!=3||scrollTop>300}}">
      <!-- 文字标题 -->
      <block wx:if="{{navSetting.isTitle==1}}">
        <!-- 居中 -->
        <view class="custom-nav-title center" style="color:{{navSetting.titleColor}};text-align:center;line-height:{{navBarHeight}}px" wx:if="{{navSetting.titlePos==1}}">
          {{templateName}}
        </view>
        <!-- 居左 -->
        <view class="custom-nav-title left" style="color:{{navSetting.titleColor}};text-align:left;line-height:{{navBarHeight}}px" wx:else>
          {{templateName}}
        </view>
      </block>
      <!-- 图片标题 -->
      <block wx:elif="{{navSetting.isTitle==2}}">
        <view class="custom-nav-titleUrl" style="text-align:left;">
          <view class="custom-nav-titleBox" style="height:{{navBarHeight}}px;">
            <image src="{{navSetting.titleUrl}}" style="display:block;height:{{navBarHeight}}px;width:{{navBarHeight*navSetting.imgWidth/navSetting.imgHeight}}px" />
            <view class="custom-nav-link">
              <view wx:for="{{navSetting.imgLinks}}" wx:key="id" style="position: absolute;top:{{item.position[1]}}%;left:{{item.position[0]}}%;width:{{item.width}}%;height:{{item.height}}%">
                <custom-link data="{{item}}" class="linkStyle" bindgoAchor="goAchor" bindgoTop="goTop" bindgoModal="goModal" bindgoShare="goShare">
                </custom-link>
              </view>
            </view>
          </view>

        </view>
      </block>
    </block>
    <!-- 返回按钮位置-->
    <view class="custom-nav-back" style="--color:{{frontColor}};" bindtap="goBack" wx:if="{{!isTabBarPage&&navSetting.back}}">
      <text class="iconfont icon-back" style="font-size: 36rpx;"></text>
    </view>
  </view>

  <!-- 固定滑动之后的内容 -->
  <view class="custom-nav-content {{navSetting.navType==4&&scrollTop>300?'show':'hidden'}}" style="top:{{statusBarHeight}}px" wx:if="{{navSetting.navType==4}}">
    <block wx:if="{{navSetting.navType!=3||scrollTop>300}}">
      <!-- 文字标题 -->
      <block wx:if="{{navSetting.move.isTitle==1}}">
        <!-- 居中 -->
        <view class="custom-nav-title center" style="color:{{navSetting.move.titleColor}};text-align:center;line-height:{{navBarHeight}}px" wx:if="{{navSetting.move.titlePos==1}}">
          {{templateName}}
        </view>
        <!-- 居左 -->
        <view class="custom-nav-title left" style="color:{{navSetting.move.titleColor}};text-align:left;line-height:{{navBarHeight}}px" wx:else>
          {{templateName}}
        </view>
      </block>
      <!-- 图片标题 -->
      <block wx:elif="{{navSetting.move.isTitle==2}}">
        <view class="custom-nav-titleUrl" style="text-align:left;">
          <view class="custom-nav-titleBox" style="height:{{navBarHeight}}px;">
            <image src="{{navSetting.move.titleUrl}}" style="display:block;height:{{navBarHeight}}px;width:{{navBarHeight*navSetting.move.imgWidth/navSetting.move.imgHeight}}px" />
            <view class="custom-nav-link">
              <view wx:for="{{navSetting.move.imgLinks}}" wx:key="id" style="position: absolute;top:{{item.position[1]}}%;left:{{item.position[0]}}%;width:{{item.width}}%;height:{{item.height}}%">
                <custom-link data="{{item}}" class="linkStyle" bindgoAchor="goAchor" bindgoTop="goTop" bindgoModal="goModal" bindgoShare="goShare">
                </custom-link>
              </view>
            </view>
          </view>
        </view>
      </block>
    </block>
    <!-- 返回按钮位置 -->
    <view class="custom-nav-back" style="--color:{{frontColor}};" bindtap="goBack" wx:if="{{!isTabBarPage&&navSetting.move.back}}">
      <text class="iconfont icon-back" style="padding: 10px 20px 10px 0;font-size: 36rpx;"></text>
    </view>
  </view>

</view>