import crypto from 'crypto-js'
import { message } from 'ant-design-vue';
import dayjs from 'dayjs'


// 密码加密
export const encryKey = function (word) {
    const key = crypto.enc.Utf8.parse('z1ijn$dk89dl#ers')
    const srcs = crypto.enc.Utf8.parse(word)
    const iv = crypto.enc.Utf8.parse('12abtrefgabcdefg')
    const encrypted = crypto.AES.encrypt(srcs, key, {
        mode: crypto.mode.CBC,
        padding: crypto.pad.Pkcs7,
        iv: iv
    })
    return encrypted.toString()
}


/**
 * 下载bolb文件
 * @param {*} blob bolo源数据 必需
 * @param {*} downloadName 下载文件名，需含文件后缀名 必需
 * @param {*} type 文件类型 enum: excel,zip,image
 * @param {*} callback 成功回调
 */
export function downloadBlob(blob, downloadName, type, callback) {
    if (!blob || !downloadName) {
        message.error('文件或文件名不存在，请联系系统管理员')
        throw '文件或文件名不存在，请联系系统管理员'
    }
    if (blob instanceof Blob) {
        const typeDict = {
            excel: 'application/vnd.ms-excel',
            zip: 'application/zip',
            image: 'application/image',
        }
        type && (blob = new Blob([blob], { type: typeDict[type] || type }))
        let url = window.URL.createObjectURL(blob)
        const a = document.createElement('a') // 创建a标签
        a.href = url
        a.download = dayjs().format('YYYY-MM-DD HH-mm-ss-') + downloadName // 下载文件名，不能包含英文 : 冒号
        a.click()
        a.remove()
        URL.revokeObjectURL(url) // 释放内存
        callback && callback()
        message.success('正在下载，请稍后至浏览器下载栏查看')
    } else if (/^(http|data:image)/.test(blob) && type === 'image') {
        let image = new Image()
        image.setAttribute('crossOrigin', 'anonymous')
        image.src = blob
        image.onload = () => {
            let canvas = document.createElement('canvas')
            canvas.width = image.width
            canvas.height = image.height
            let ctx = canvas.getContext('2d')
            ctx.drawImage(image, 0, 0, image.width, image.height)
            canvas.toBlob((blob) => {
                downloadBlob(blob, downloadName, callback)
            })
        }
    } else {
        message.error('blob：文件类型错误')
        throw 'blob：文件类型错误'
    }
}

/**
* UTF16和UTF8转换对照表
* U+00000000 – U+0000007F 	0xxxxxxx
* U+00000080 – U+000007FF 	110xxxxx 10xxxxxx
* U+00000800 – U+0000FFFF 	1110xxxx 10xxxxxx 10xxxxxx
* U+00010000 – U+001FFFFF 	11110xxx 10xxxxxx 10xxxxxx 10xxxxxx
* U+00200000 – U+03FFFFFF 	111110xx 10xxxxxx 10xxxxxx 10xxxxxx 10xxxxxx
* U+04000000 – U+7FFFFFFF 	1111110x 10xxxxxx 10xxxxxx 10xxxxxx 10xxxxxx 10xxxxxx
*/
//外部js引用时这样写：import {Base64} from '/xxx/base64';//路径需要根据实际路径去写
export let Base64 = {
    // 转码表
    tables: [
        'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H',
        'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P',
        'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X',
        'Y', 'Z', 'a', 'b', 'c', 'd', 'e', 'f',
        'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n',
        'o', 'p', 'q', 'r', 's', 't', 'u', 'v',
        'w', 'x', 'y', 'z', '0', '1', '2', '3',
        '4', '5', '6', '7', '8', '9', '+', '/'
    ],
    UTF16ToUTF8: function (str) {
        let results = [], len = str.length;
        for (let i = 0; i < len; i++) {
            let code = str.charCodeAt(i);
            if (code > 0x0000 && code <= 0x007F) {
                /* 一字节，不考虑0x0000，因为是空字节
                   U+00000000 – U+0000007F 	0xxxxxxx
                */
                results.push(str.charAt(i));
            } else if (code >= 0x0080 && code <= 0x07FF) {
                /* 二字节
                   U+00000080 – U+000007FF 	110xxxxx 10xxxxxx
                   110xxxxx
                */
                let byte1 = 0xC0 | ((code >> 6) & 0x1F);
                // 10xxxxxx
                let byte2 = 0x80 | (code & 0x3F);
                results.push(
                    String.fromCharCode(byte1),
                    String.fromCharCode(byte2)
                );
            } else if (code >= 0x0800 && code <= 0xFFFF) {
                /* 三字节
                   U+00000800 – U+0000FFFF 	1110xxxx 10xxxxxx 10xxxxxx
                   1110xxxx
                */
                let byte1 = 0xE0 | ((code >> 12) & 0x0F);
                // 10xxxxxx
                let byte2 = 0x80 | ((code >> 6) & 0x3F);
                // 10xxxxxx
                let byte3 = 0x80 | (code & 0x3F);
                results.push(
                    String.fromCharCode(byte1),
                    String.fromCharCode(byte2),
                    String.fromCharCode(byte3)
                );
            } else if (code >= 0x00010000 && code <= 0x001FFFFF) {
                // 四字节
                // U+00010000 – U+001FFFFF 	11110xxx 10xxxxxx 10xxxxxx 10xxxxxx
            } else if (code >= 0x00200000 && code <= 0x03FFFFFF) {
                // 五字节
                // U+00200000 – U+03FFFFFF 	111110xx 10xxxxxx 10xxxxxx 10xxxxxx 10xxxxxx
            } else /** if (code >= 0x04000000 && code <= 0x7FFFFFFF)*/ {
                // 六字节
                // U+04000000 – U+7FFFFFFF 	1111110x 10xxxxxx 10xxxxxx 10xxxxxx 10xxxxxx 10xxxxxx
            }
        }

        return results.join('');
    },
    UTF8ToUTF16: function (str) {
        let results = [], len = str.length;
        let i = 0;
        for (let i = 0; i < len; i++) {
            let code = str.charCodeAt(i);
            // 第一字节判断
            if (((code >> 7) & 0xFF) == 0x0) {
                // 一字节
                // 0xxxxxxx
                results.push(str.charAt(i));
            } else if (((code >> 5) & 0xFF) == 0x6) {
                // 二字节
                // 110xxxxx 10xxxxxx
                let code2 = str.charCodeAt(++i);
                let byte1 = (code & 0x1F) << 6;
                let byte2 = code2 & 0x3F;
                let utf16 = byte1 | byte2;
                results.push(Sting.fromCharCode(utf16));
            } else if (((code >> 4) & 0xFF) == 0xE) {
                // 三字节
                // 1110xxxx 10xxxxxx 10xxxxxx
                let code2 = str.charCodeAt(++i);
                let code3 = str.charCodeAt(++i);
                let byte1 = (code << 4) | ((code2 >> 2) & 0x0F);
                let byte2 = ((code2 & 0x03) << 6) | (code3 & 0x3F);
                let utf16 = ((byte1 & 0x00FF) << 8) | byte2
                results.push(String.fromCharCode(utf16));
            } else if (((code >> 3) & 0xFF) == 0x1E) {
                // 四字节
                // 11110xxx 10xxxxxx 10xxxxxx 10xxxxxx
            } else if (((code >> 2) & 0xFF) == 0x3E) {
                // 五字节
                // 111110xx 10xxxxxx 10xxxxxx 10xxxxxx 10xxxxxx
            } else /** if (((code >> 1) & 0xFF) == 0x7E)*/ {
                // 六字节
                // 1111110x 10xxxxxx 10xxxxxx 10xxxxxx 10xxxxxx 10xxxxxx
            }
        }

        return results.join('');
    },
    encode: function (str) {
        if (!str) {
            return '';
        }
        let utf8 = this.UTF16ToUTF8(str); // 转成UTF-8
        let i = 0; // 遍历索引
        let len = utf8.length;
        let results = [];
        while (i < len) {
            let c1 = utf8.charCodeAt(i++) & 0xFF;
            results.push(this.tables[c1 >> 2]);
            // 补2个=
            if (i == len) {
                results.push(this.tables[(c1 & 0x3) << 4]);
                results.push('==');
                break;
            }
            let c2 = utf8.charCodeAt(i++);
            // 补1个=
            if (i == len) {
                results.push(this.tables[((c1 & 0x3) << 4) | ((c2 >> 4) & 0x0F)]);
                results.push(this.tables[(c2 & 0x0F) << 2]);
                results.push('=');
                break;
            }
            let c3 = utf8.charCodeAt(i++);
            results.push(this.tables[((c1 & 0x3) << 4) | ((c2 >> 4) & 0x0F)]);
            results.push(this.tables[((c2 & 0x0F) << 2) | ((c3 & 0xC0) >> 6)]);
            results.push(this.tables[c3 & 0x3F]);
        }

        return results.join('');
    },
    decode: function (str) {
        //判断是否为空
        if (!str) {
            return '';
        }

        let len = str.length;
        let i = 0;
        let results = [];
        //循环解出字符数组
        while (i < len) {
            let code1 = this.tables.indexOf(str.charAt(i++));
            let code2 = this.tables.indexOf(str.charAt(i++));
            let code3 = this.tables.indexOf(str.charAt(i++));
            let code4 = this.tables.indexOf(str.charAt(i++));

            let c1 = (code1 << 2) | (code2 >> 4);
            results.push(String.fromCharCode(c1));

            if (code3 != -1) {
                let c2 = ((code2 & 0xF) << 4) | (code3 >> 2);
                results.push(String.fromCharCode(c2));
            }
            if (code4 != -1) {
                let c3 = ((code3 & 0x3) << 6) | code4;
                results.push(String.fromCharCode(c3));
            }

        }

        return this.UTF8ToUTF16(results.join(''));
    }
};
// 导出excel
export const downloadExcel = (content, filename) => {
    let eleLink = document.createElement('a');
    eleLink.download = filename;
    eleLink.style.display = 'none';
    let blob = new Blob([content]);
    eleLink.href = URL.createObjectURL(blob);
    document.body.appendChild(eleLink);
    eleLink.click();
    document.body.removeChild(eleLink);
};
function decodeFilenameFromContentDisposition(contentDisposition) {
    // 查找 filename* 属性  
    const matches = /filename\*=utf-8''([^;]+)/.exec(contentDisposition);
    if (!matches) {
        // 如果没有找到 filename* 属性，则尝试查找旧的 filename 属性（这里不处理）  
        return null; // 或者返回一个默认值  
    }

    // 提取并解码文件名  
    const encodedFilename = matches[1];
    const decodedFilename = decodeURIComponent(encodedFilename.replace(/\+/g, ' ')); // 通常不需要替换 +，但保留以防万一  

    return decodedFilename;
}

// 导出excel
export const downloadExcelName = (aisData) => {//会获取文件名称
    const contentDisposition = aisData.headers.get('Content-Disposition');
    // console.log("🚀 ~ downloadExcelName ~ contentDisposition:", contentDisposition)
    let filename = 'default.xlsx'; // 默认文件名  
    if (contentDisposition) {

        if (decodeFilenameFromContentDisposition(contentDisposition)) {
            filename = decodeFilenameFromContentDisposition(contentDisposition)
        }
    }
    // console.log(decodeFilenameFromContentDisposition(contentDisposition), contentDisposition);
    let eleLink = document.createElement('a');
    eleLink.download = filename;
    eleLink.style.display = 'none';
    let blob = new Blob([aisData.data]);
    eleLink.href = URL.createObjectURL(blob);
    document.body.appendChild(eleLink);
    eleLink.click();
    document.body.removeChild(eleLink);
};
// 导出图片
// downloadImgByUrl('https://wework.qpic.cn/wwpic3az/213391_lOsPzSQqTMeZl9K_1711686809/0','图片.png')
export const downloadImgByUrl = (url, filename) => {
    // console.log("🚀 ~ downloadImgByUrl ~ url, filename:", url, filename);
    let image = new Image();
    image.setAttribute('crossOrigin', 'anonymous');
    image.src = url;

    image.onload = () => {
        let canvas = document.createElement('canvas');
        canvas.width = image.width;
        canvas.height = image.height;
        let ctx = canvas.getContext('2d');
        ctx.drawImage(image, 0, 0, image.width, image.height);
        canvas.toBlob((blob) => {
            if (blob) {
                let url = URL.createObjectURL(blob);
                let eleLink = document.createElement('a');
                eleLink.download = filename;
                eleLink.href = url;
                eleLink.click();
                eleLink.remove();
                URL.revokeObjectURL(url);
                message.success('正在下载，请稍后至浏览器下载栏查看');
            } else {
                message.error('无法生成文件，请检查图片是否有效');
            }
        });
    };

    image.onerror = function (error) {
        console.error("Error loading image:", error);
        message.error('图片加载失败，请检查 URL 是否正确');
    };
};

// base64转blob
const base64ToBlob = (code) => {
    let parts = code.split(';base64,')
    let contentType = parts[0].split(':')[1]
    let raw = window.atob(parts[1]) // 解码base64得到二进制字符串
    let rawLength = raw.length
    let uInt8Array = new Uint8Array(rawLength) // 创建8位无符号整数值的类型化数组
    for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i) // 数组接收二进制字符串
    }
    return new Blob([uInt8Array], { type: contentType })
}

// 导出base64图片
export const downloadBase64 = (fileName, content) => {
    let aLink = document.createElement('a')
    let blob = base64ToBlob(content) // new Blob([content]);
    aLink.download = fileName
    aLink.href = URL.createObjectURL(blob)
    aLink.click()
}

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 */
export function handleTree(data, id, parentId, children) {
    let config = {
        id: id || 'id',
        parentId: parentId || 'parentId',
        childrenList: children || 'children'
    };

    var childrenListMap = {};
    var nodeIds = {};
    var tree = [];

    for (let d of data) {
        let parentId = d[config.parentId];
        if (childrenListMap[parentId] == null) {
            childrenListMap[parentId] = [];
        }
        nodeIds[d[config.id]] = d;
        childrenListMap[parentId].push(d);
    }

    for (let d of data) {
        let parentId = d[config.parentId];
        if (nodeIds[parentId] == null) {
            tree.push(d);
        }
    }

    for (let t of tree) {
        adaptToChildrenList(t);
    }

    function adaptToChildrenList(o) {
        if (childrenListMap[o[config.id]] !== null) {
            o[config.childrenList] = childrenListMap[o[config.id]];
        }
        if (o[config.childrenList]) {
            for (let c of o[config.childrenList]) {
                adaptToChildrenList(c);
            }
        }
    }
    return tree;
}
