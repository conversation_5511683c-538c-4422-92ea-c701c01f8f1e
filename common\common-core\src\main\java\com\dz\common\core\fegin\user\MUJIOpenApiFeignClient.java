package com.dz.common.core.fegin.user;

import com.alibaba.fastjson.JSONObject;
import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.user.ReceiveCouponDTO;
import com.dz.common.core.vo.MyMsgVo;
import com.dz.common.core.vo.ReceiveCouponVo;
import com.dz.common.core.vo.SubscriptionMsgVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;
import java.net.MalformedURLException;
import java.text.ParseException;

/**
 * crm接口
 */
@FeignClient(name = ServiceConstant.USER_SERVICE_NAME, contextId = "MUJIOpenApiFeignClient")
public interface MUJIOpenApiFeignClient {

    /**
     * 活动优惠券列表
     * @param memberCode
     * @return
     */
    @GetMapping(value = "/activity/coupon/list")
    public Result<JSONObject> activityCouponList(@RequestParam(value = "memberCode",required = false)String memberCode);

    /**
     * 优惠券列表
     * @param memberCode
     * @param type
     * @param status
     * @param page
     * @param pageSize
     * @return
     */
    @GetMapping(value = "/member/coupon/list")
    public Result<JSONObject> memberCouponList(@RequestParam(value = "memberCode",required = false) String memberCode,
                                               @RequestParam(value = "type", required = false) Integer type,
                                               @RequestParam("status") Integer status,
                                               @RequestParam("page") Integer page,
                                               @RequestParam("pageSize") Integer pageSize);

    /**
     * 优惠券详情
     *
     * @param stockId
     * @return
     */
    @GetMapping(value = "/member/coupon/stock/id")
    public Result<JSONObject> memberCouponStockId(@RequestParam("stockId") String stockId);

    /**
     * 会员优惠券详情
     */
    @GetMapping(value = "/member/coupon/detail")
    Result<JSONObject> getCouponDetail(@RequestParam("couponId") String couponId,
                                       @RequestParam(value = "memberCode",required = false) String memberCode,
                                       @RequestParam("couponCode") String couponCode);

    /**
     * 优惠券领取
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/member/coupon/receive")
    Result<ReceiveCouponDTO> couponReceive(@RequestBody ReceiveCouponVo param);

    /**
     * 积分增加
     *
     * @param memberCode
     * @param bonusAmount
     * @param reason
     * @return
     */
    @PostMapping(value = "/member/add/points")
    Result<JSONObject> addPoints(@RequestParam(value = "userId", required = false) Long userId,
                                 @RequestParam(value = "memberCode",required = false) String memberCode,
                                 @RequestParam("channel") String channel,
                                 @RequestParam("bonusAmount") Integer bonusAmount,
                                 @RequestParam("reason") String reason);

    /**
     * 积分增加
     *
     * @param memberCode
     * @param bonusAmount
     * @param reason
     * @return
     */
    @PostMapping(value = "/member/add/points_with_sn")
    Result<JSONObject> addPointsWithSn(@RequestParam(value = "memberCode",required = false) String memberCode,
                                       @RequestParam("channel") String channel,
                                       @RequestParam("bonusAmount") Integer bonusAmount,
                                       @RequestParam("reason") String reason,
                                       @RequestParam("outSn") String outSn);

    /**
     * 积分扣减
     *
     * @param memberCode
     * @param bonusAmount
     * @param reason
     * @return
     */
    @PostMapping(value = "/member/deduct/points")
    Result<JSONObject> deductPoints(@RequestParam(value = "memberCode",required = false) String memberCode,
                                    @RequestParam("channel") String channel,
                                    @RequestParam("bonusAmount") Integer bonusAmount,
                                    @RequestParam("reason") String reason);

    /**
     * 积分扣减
     *
     * @param memberCode
     * @param bonusAmount
     * @param reason
     * @return
     */
    @PostMapping(value = "/member/deduct/points_with_sn")
    Result<JSONObject> deductPointsWithSn(@RequestParam(value = "memberCode",required = false) String memberCode,
                                          @RequestParam("channel") String channel,
                                          @RequestParam("bonusAmount") Integer bonusAmount,
                                          @RequestParam("reason") String reason,
                                          @RequestParam("outSn") String outSn);

    /**
     * 订单列表
     * @param memberCode
     * @param start_time
     * @param end_time
     * @param type
     * @param page
     * @param pageSize
     * @return
     */
    @PostMapping(value = "/member/order/list")
    public Result<JSONObject> orderList(@RequestParam(value = "memberCode",required = false)String memberCode,
                                        @RequestParam("start_time")String start_time,
                                        @RequestParam("end_time")String end_time,
                                        @RequestParam("type")Integer type,
                                        @RequestParam("page")Integer page,
                                        @RequestParam("pageSize")Integer pageSize);

    /**
     * 订单详情
     * @param memberCode
     * @param orderSn
     * @return
     */
    @PostMapping(value = "/member/order/details")
    public Result<JSONObject> memberOrderDetails(@RequestParam(value = "memberCode",required = false)String memberCode,
                                            @RequestParam("orderSn")String orderSn);

    /**
     * 小票详情
     * @param memberCode
     * @param orderSn
     * @return
     */
    @PostMapping(value = "/member/order/ticket/details")
    public Result<JSONObject> ticketDetails(@RequestParam(value = "memberCode",required = false)String memberCode,
                                            @RequestParam("orderSn")String orderSn);

    /**
     * 发送订阅消息
     * @param subscriptionMsgVo
     * @return
     */
    @PostMapping(value = "/app/subscription/add")
    public Result<Object> add(@RequestBody SubscriptionMsgVo subscriptionMsgVo) throws ParseException;

    /**
     * 会员积分数据查询
     * @param memberCode
     * @return
     */
    @PostMapping(value = "/member/points/count")
    public Result<JSONObject> memberPointsCount(@RequestParam(value = "memberCode",required = false)String memberCode);


    /**
     * 添加我的消息
     * @param msgCode
     * @return
     * @throws ParseException
     */
    @PostMapping(value = "/app/msg/add")
    public Result<Object> add(@RequestBody MyMsgVo msgCode) throws ParseException;
}

