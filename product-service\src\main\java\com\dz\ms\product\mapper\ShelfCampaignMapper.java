package com.dz.ms.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.ms.product.dto.req.ShelfCampaignParamDTO;
import com.dz.ms.product.entity.ShelfCampaign;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 货架营销活动Mapper
 *
 * @author: LiinNs
 * @date: 2024/11/20 11:39
 */
@Repository
public interface ShelfCampaignMapper extends BaseMapper<ShelfCampaign> {

    IPage<ShelfCampaign> selPageList(Page<Object> objectPage, @Param("param") ShelfCampaignParamDTO param);

    /**
     * 根据货架id修改关联货架id为null
     *
     * @param shelfId 货架id
     */
    void updShelfIdIntoNull(@Param("shelfId") Long shelfId);


    /**
     * 查询营销活动列表
     *
     * @param param
     * @return
     */
    List<ShelfCampaign> selectListByParam(@Param("param") ShelfCampaignParamDTO param);
}
