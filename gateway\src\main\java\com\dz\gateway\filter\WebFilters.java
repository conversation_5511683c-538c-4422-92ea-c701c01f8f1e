package com.dz.gateway.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import java.util.StringTokenizer;

/**
 * description:
 *
 * <AUTHOR>
 * @date 2019/01/02
 */
@Slf4j
@Configuration
public class WebFilters {

    private final static String PREFIXS = "app,emp,openapi,crm,oms,callback";

    @Bean
    public WebFilter corsFilter() {
        return (ServerWebExchange ctx, WebFilterChain chain) -> {
            ServerHttpRequest request = ctx.getRequest();
            ServerHttpResponse response = ctx.getResponse();
            /*if (CorsUtils.isCorsRequest(request)) {
                HttpHeaders requestHeaders = request.getHeaders();
                HttpMethod requestMethod = requestHeaders.getAccessControlRequestMethod();
                HttpHeaders headers = response.getHeaders();
                headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, requestHeaders.getOrigin());
                headers.addAll(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, requestHeaders.getAccessControlRequestHeaders());
                if (requestMethod != null) {
                    headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, requestMethod.name());
                }
                headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_CREDENTIALS, "true");
                headers.add(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "*");
                headers.add(HttpHeaders.ACCESS_CONTROL_MAX_AGE, "18000L");
            }*/
            if (request.getMethod() == HttpMethod.OPTIONS) {
                response.setStatusCode(HttpStatus.OK);
                return Mono.empty();
            }
            String url = request.getURI().getPath();
            log.info("request:{}",url);
            StringTokenizer st = new StringTokenizer(url,"/");
            String prefix = st.nextToken();
            if("api".equals(prefix)) {
                prefix = st.nextToken();
            }
            if(PREFIXS.contains(prefix)) {
                String client = st.nextToken();
                StringBuilder sb = new StringBuilder();
                sb.append("/");
                sb.append(client);
                sb.append("/");
                sb.append(prefix);
                while(st.hasMoreTokens()) {
                    sb.append("/");
                    sb.append(st.nextToken());
                }
                request = request.mutate().path(sb.toString()).header("rpc","0").build();
                return chain.filter(ctx.mutate().request(request).build());
            }
            if(prefix.contains("inner")) {
                return null;
            }
            return chain.filter(ctx);
        };
    }

}
