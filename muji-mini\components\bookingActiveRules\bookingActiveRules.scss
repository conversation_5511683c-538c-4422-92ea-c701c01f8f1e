.clockPopup {
  position: relative;
  background: transparent;
  display: flex;
  flex-direction: column;
  align-items: center;

  &-content {
    overflow: hidden;
    width: 630rpx;

    border-radius: var(--radius);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 60rpx 40rpx 70rpx;
    box-sizing: border-box;

    .clock {
      max-height: 1070rpx;

      .h1 {
        font-weight: 700;
        font-size: 32rpx;
        color: #3C3C43;
        line-height: 54rpx;
        letter-spacing: 1px;
        text-align: center;
        margin-bottom: 40rpx;

        .h1-en {
          font-size: 30rpx;
        }
      }

      .h2 {
        font-weight: 700;
        font-size: 24rpx;
        color: #3C3C43;
        line-height: 44rpx;
        text-align: justify;
        margin-top: 30rpx;
      }

      .h3 {
        font-weight: 400;
        font-size: 20rpx;
        line-height: 36rpx;
        text-align: left;
        color: #888888;
      }
    }
  }

  &-close {
    padding-top: 10rpx;
    height: 80rpx;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-closeBox {
    height: 80rpx;
    width: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.resetBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 60rpx;
}
