package com.dz.common.core.fegin.basic;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.basic.CityDTO;
import com.dz.common.core.dto.basic.ProvinceCityDTO;
import com.dz.common.core.dto.basic.ProvinceDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 省市
 * @Author: zhaomingcong
 * @Date: 2023/9/3
 */
@FeignClient(name = ServiceConstant.BASIC_SERVICE_NAME, contextId = "CityInfoFeginClient")
public interface CityInfoFeginClient {

    /**
     * 获取有门店的省份
     * @return
     */
    @GetMapping(value = "/province_info/list")
    List<ProvinceDTO> getProvinceList();

    /**
     * 获取省份下城市
     * @param provinceCode
     * @return
     */
    @GetMapping(value = "/city_info/getCityList")
    List<CityDTO> getCityList(@RequestParam("provinceCode") String provinceCode);

    /**
     * 根据城市code获取省份城市信息
     * @param cityCode
     * @return
     */
    @GetMapping(value = "/province_city_info/get_info_by_cityCode")
    public ProvinceCityDTO getInfoByCityCode(@RequestParam("cityCode") String cityCode);

    @ApiOperation("根据名称获取省市code")
    @GetMapping(value = "/province_city_info/get_code_by_name")
    public String getCodeByName(@RequestParam("name") String name);

}
