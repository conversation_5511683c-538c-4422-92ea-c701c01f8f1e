package com.dz.ms.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.constants.NumConstants;
import com.dz.common.core.constants.OrderCodeConstants;
import com.dz.common.core.constants.PointChannelConstant;
import com.dz.common.core.dto.*;
import com.dz.common.core.dto.export.OrderExportDTO;
import com.dz.common.core.dto.order.*;
import com.dz.common.core.dto.product.*;
import com.dz.common.core.dto.user.*;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.product.ProductFeignClient;
import com.dz.common.core.fegin.product.ShelfProductFeignClient;
import com.dz.common.core.fegin.product.ShoppingCartFeignClient;
import com.dz.common.core.fegin.sales.InteractionTaskFeginClient;
import com.dz.common.core.fegin.user.MUJIOpenApiFeignClient;
import com.dz.common.core.fegin.user.UserInfoFeginClient;
import com.dz.common.core.service.ExportService;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.*;
import com.dz.common.core.vo.ReceiveCouponVo;
import com.dz.ms.order.config.Globals;
import com.dz.ms.order.constants.MUJICouponStatusConstants;
import com.dz.ms.order.dto.CrmExchangeOrderListDTO;
import com.dz.ms.order.dto.ExchangeOrderDTO;
import com.dz.ms.order.dto.req.CrmExchangeOrderParamDTO;
import com.dz.ms.order.entity.*;
import com.dz.ms.order.mapper.ExchangeOrderMapper;
import com.dz.ms.order.mapper.OrderDetailCouponMapper;
import com.dz.ms.order.mapper.OrderDetailMapper;
import com.dz.ms.order.mapper.OrderSendMapper;
import com.dz.ms.order.service.ExchangeOrderService;
import com.dz.ms.order.service.OrderCreateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单主表
 *
 * @author: LiinNs
 * @date: 2024/12/09 10:38
 */
@Service
@Slf4j
public class ExchangeOrderServiceImpl extends ServiceImpl<ExchangeOrderMapper, ExchangeOrder> implements ExchangeOrderService {

    private static final String PLZ_RETRY_MESSAGE = "下单失败，请重试或联系客服";
    @Resource
    private ExchangeOrderMapper exchangeOrderMapper;
    @Resource
    private OrderDetailMapper orderDetailMapper;
    @Resource
    private OrderDetailCouponMapper orderDetailCouponMapper;
    @Resource
    private ExportService exportService;
    @Resource
    private UserInfoFeginClient userInfoFeginClient;
    @Resource
    private OrderCreateService orderCreateService;
    @Resource
    private ShoppingCartFeignClient shoppingCartClient;
    @Resource
    private MUJIOpenApiFeignClient openApiFeignClient;
    @Resource
    private ShelfProductFeignClient shelfProductFeignClient;
    @Resource
    private OrderSendMapper orderSendMapper;
    @Resource
    private InteractionTaskFeginClient interactionTaskFeginClient;
    @Resource
    private RedisService redisService;
    @Resource
    private ProductFeignClient productFeignClient;

    /**
     * 同一个请求多次调用订单查询时会把第一次解密的数据缓存下来，从而不去查询数据库，会导致解密失败，
     * 因此捕获异常返回源对象即可
     *
     * @param order 订单实体
     */
    private static void decodeOrderInfo(ExchangeOrder order) {
        log.info("decodeOrderInfo order:{}", JSON.toJSONString(order));
        // 解密显示
        if (Objects.nonNull(order)
                && Objects.nonNull(order.getReceiverPhone())
                && order.getReceiverPhone().contains("=")) {
            String decodePhone = singleFieldDecrypt(order.getReceiverPhone());
            String decodeAddress = singleFieldDecrypt(order.getReceiverAddress());
            log.info("decodeOrderInfo decrypt phone:{}, address:{}, encode_phone:{}, encode_address:{}",
                    order.getReceiverPhone(), order.getReceiverAddress(), decodePhone, decodeAddress);
            order.setReceiverPhone(decodePhone);
            order.setReceiverAddress(decodeAddress);
        }
    }

    private static String singleFieldDecrypt(String originalValue) {
        // 解密显示
        String decryptValue = originalValue;
        try {
            decryptValue = AesMysqlUtils.decrypt(originalValue);
        } catch (Exception e) {
            log.error("数据解密失败:{}", originalValue, e);
        }
        return decryptValue;
    }

    @Override
    public PageInfo<OrderListAppDTO> listApp(OrderListAppParamDTO dto) {
        Integer num = dto.getPageNum() == null ? 1 : dto.getPageNum();
        Integer size = dto.getPageSize() == null ? 10 : dto.getPageSize();
        Page<OrderListAppDTO> page = new Page<>(num, size);
        CurrentUserDTO user = SecurityContext.getUser();
        if (ObjectUtils.isEmpty(user.getUid())) {
            throw new BusinessException(ErrorCode.EXCHANGE_NOT_REGISTER);
        }
        dto.setUserId(user.getUid());
        IPage<OrderListAppDTO> iPage = exchangeOrderMapper.listApp(page, dto);
        List<OrderListAppDTO> list = iPage.getRecords();

        for (OrderListAppDTO orderListAppDTO : list) {
            List<OrderInfoProductAppDTO> productList = new ArrayList<>();
            orderListAppDTO.setProductList(productList);
            LambdaQueryWrapper<OrderDetail> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OrderDetail::getOrderCode, orderListAppDTO.getOrderCode());
            List<OrderDetail> listByOrderCode = orderDetailMapper.selectList(queryWrapper);
            Integer productNum = 0;
            for (OrderDetail orderDetail : listByOrderCode) {
                OrderInfoProductAppDTO orderInfoProductAppDTO = BeanCopierUtils.convertObject(orderDetail, OrderInfoProductAppDTO.class);
                productList.add(orderInfoProductAppDTO);
                productNum += orderDetail.getNumber();
            }

            orderListAppDTO.setNumber(productNum);
        }
        return new PageInfo<>(iPage.getCurrent(), iPage.getSize(), iPage.getTotal(), list);
    }

    @Override
    @Transactional
    public CreateOrderResultDTO create(OrderCreateParamDTO dto) {
        //获得当前用户
        CurrentUserDTO user = SecurityContext.getUser();
        CreateOrderResultDTO resultDto = new CreateOrderResultDTO();
        Long tenantId = user.getTenantId();
        String createOrderKey = "create_order_" + user.getUid();
        boolean lock = redisService.lock(createOrderKey, 10);
        if (!lock) {
            throw new BusinessException(ErrorCode.INTERNAL_ERROR, "创建订单中请稍后");
        }
        if (ObjectUtils.isEmpty(user.getUid())) {
            throw new BusinessException(ErrorCode.EXCHANGE_NOT_REGISTER);
        }

        Result<MemberInfoDTO> currentUserMemberInfo = userInfoFeginClient.getCurrentUserMemberInfo();

        if (currentUserMemberInfo == null || currentUserMemberInfo.getData() == null) {
            throw new BusinessException(ErrorCode.EXCHANGE_NOT_REGISTER);
        }

        MemberInfoDTO currentMemberInfo = currentUserMemberInfo.getData();
        if (StringUtils.isEmpty(currentMemberInfo.getCardNo())) {
            throw new BusinessException(ErrorCode.EXCHANGE_NOT_REGISTER);
        }
        //生成订单号
        String orderCode = RandomUtils.generateOrderCode(user.getUid());
        String paramStr = JSON.toJSONString(dto);
        log.info("订单号:{}创建订单接口入参:{}", orderCode, paramStr);


        //组装订单表数据
        ExchangeOrder order = new ExchangeOrder();
        order.setOrderCode(orderCode);
        order.setOrderType(OrderCodeConstants.AbstractOrderType.NORMAL);
        order.setUserId(user.getUid());//客户id
        order.setUserPhone(currentMemberInfo.getMobile());//用户手机号
        order.setUserCrmCode(currentMemberInfo.getCardNo());
        order.setUserCardLevel(currentMemberInfo.getCardLevel());
        List<OrderDetail> details = orderCreateService.createProduct(dto, order);


        this.calculateOrderPrice(order);//计算订单价格
        if (CollectionUtils.isEmpty(details)) {
            throw new BusinessException(ErrorCode.NOT_ACCEPTABLE, PLZ_RETRY_MESSAGE);
        }
        Map<Long, List<OrderDetail>> rules = details.stream()
                .filter(d -> Objects.nonNull(d.getRuleId()))
                .collect(Collectors.groupingBy(OrderDetail::getRuleId));
        // 用户已购买的信息
        if (!CollectionUtils.isEmpty(rules)) {
            for (Map.Entry<Long, List<OrderDetail>> entry : rules.entrySet()) {
                Long ruleId = entry.getKey();
                List<OrderDetail> rulesProductList = entry.getValue();
                OrderDetail detail = rulesProductList.get(0);
                PurchaseStaticParamDTO param = new PurchaseStaticParamDTO();
                param.setShelfId(order.getShelfId());
                param.setUserId(user.getUid());
                param.setRuleId(ruleId);
                param.setRuleType(detail.getRuleType());
                param.setPeriod(detail.getPeriod());
                param.setRCreated(detail.getRCreated());
                param.setCampaignOnStartTime(detail.getCampaignOnStartTime());
                param.setCampaignOnEndTime(detail.getCampaignOnEndTime());
                List<PurchaseStaticDTO> purchaseStaticDTOS = purchaseStatic(param);
                // 总体限购
                Integer myPurchaseNumber = 0;
                if (!CollectionUtils.isEmpty(purchaseStaticDTOS)) {
                    myPurchaseNumber = purchaseStaticDTOS.stream().mapToInt(PurchaseStaticDTO::getNumber).sum();
                }
                int rPurchaseLimit = Optional.ofNullable(detail.getRPurchaseLimit()).orElse(Integer.MAX_VALUE);
                if (rPurchaseLimit > 0) {
                    int sum = rulesProductList.stream().mapToInt(OrderDetail::getNumber).sum() + myPurchaseNumber;
                    if (sum > rPurchaseLimit) {
                        throw new BusinessException(ErrorCode.BAD_REQUEST, "该活动中每人最多兑换" + rPurchaseLimit + "件");
                    }
                }
                // 单商品限购
                Integer myShelfProductIdPurchaseNumber = 0;
                for (OrderDetail rulesProduct : rulesProductList) {
                    int rEveryoneLimit = Optional.ofNullable(rulesProduct.getREveryoneLimit()).orElse(Integer.MAX_VALUE);
                    if (rEveryoneLimit > 0) {
                        if (!CollectionUtils.isEmpty(purchaseStaticDTOS)) {
                            myShelfProductIdPurchaseNumber = purchaseStaticDTOS.stream()
                                    .filter(d -> Objects.equals(d.getShelfProductId(), rulesProduct.getShelfProductId()))
                                    .mapToInt(PurchaseStaticDTO::getNumber)
                                    .sum();
                        }
                        int sum = rulesProduct.getNumber() + myShelfProductIdPurchaseNumber;
                        if (sum > rEveryoneLimit) {
                            log.info("该商品目前每人限购: {}件 item: {}", rEveryoneLimit, rulesProduct);
                            throw new BusinessException(ErrorCode.BAD_REQUEST, shortenProductName(rulesProduct.getProductName()) + "目前每人限购" + rEveryoneLimit + "件");
                        }
                    }
                }
            }
        }

        Result<MyPointsDTO> pointsDTOResult = userInfoFeginClient.myPoints();
        if (pointsDTOResult == null || !pointsDTOResult.isSuccess() || Objects.isNull(pointsDTOResult.getData())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, PLZ_RETRY_MESSAGE);
        }
        if (order.getOrderPoint() > pointsDTOResult.getData().getPointsNum()) {
            int pointGap = order.getOrderPoint() - pointsDTOResult.getData().getPointsNum();
            throw new BusinessException(ErrorCode.EXCHANGE_INSUFFICIENT_POINT, String.format(ErrorCode.EXCHANGE_INSUFFICIENT_POINT.getMessage(), pointGap));
        }

        order.setOrderType(OrderCodeConstants.AbstractOrderType.NORMAL);//订单类型
        // 是否扣除积分
        boolean usePointSuccess = false;
        List<OrderDetail> deduceSuccessList = new ArrayList<>();
        // outSn 使用 单号
        String outSn = orderCode;
        order.setOutSn(outSn);
        try {
            Date now = new Date();
            order.setExpressCode(null);//物流code，发货的时候才有
            order.setExpressId(null);//物流id,发货的时候才有
            order.setExpressStatus(OrderCodeConstants.AbstractOrderExpressStatus.WAITING);//发货状态,默认未发货
            order.setRemark(dto.getRemark());//用户备注
            order.setPayTime(now);
            order.setOrderStatus(OrderCodeConstants.AbstractOrderStatus.PENDING);
            order.setPayStatus(OrderCodeConstants.AbstractOrderPayStatus.PAID);
            order.setSyncStatus(OrderCodeConstants.SyncStatus.UNREADY);
            order.setIsDeleted(OrderCodeConstants.AbstractOrderIsDelete.NORMAL);//订单删除状态，默认正常
            order.setTenantId(tenantId);//租户id
            order.setCreated(now);
            order.setModified(now);
            exchangeOrderMapper.insert(order);
            PurchaseStaticParamDTO purchaseStaticParamDTO = new PurchaseStaticParamDTO();
            purchaseStaticParamDTO.setShelfId(order.getShelfId());
            purchaseStaticParamDTO.setUserId(user.getUid());
            Date monthZero = DateUtils.getMonthZero();
            purchaseStaticParamDTO.setCampaignOnStartTime(monthZero);
            purchaseStaticParamDTO.setCampaignOnEndTime(new Date());
            List<PurchaseStaticDTO> shelfPurchaseStatic = purchaseStatic(purchaseStaticParamDTO);
            for (OrderDetail detail : details) {
                if ((Objects.isNull(detail.getREveryoneLimit()) || detail.getREveryoneLimit() == 0)
                        && (Objects.isNull(detail.getRPurchaseLimit()) || detail.getRPurchaseLimit() == 0)) {
                    if (Objects.nonNull(detail.getLimitNum()) && detail.getLimitNum() > 0) {
                        int myShelfProductIdPurchaseNumber = 0;
                        if (!CollectionUtils.isEmpty(shelfPurchaseStatic)) {
                            myShelfProductIdPurchaseNumber = shelfPurchaseStatic.stream()
                                    .filter(d -> Objects.equals(d.getShelfProductId(), detail.getShelfProductId()))
                                    .mapToInt(PurchaseStaticDTO::getNumber)
                                    .sum();
                        }
                        if (myShelfProductIdPurchaseNumber + detail.getNumber() > detail.getLimitNum()) {
                            // xxx 当月每人限购2件
                            throw new BusinessException(ErrorCode.BAD_REQUEST, shortenProductName(detail.getProductName()) + " 当月每人限购" + detail.getLimitNum() + "件");
                        }
                    }
                }
                // 扣减库存
                InventoryParamDTO param = new InventoryParamDTO(detail.getShelfId(), detail.getShelfProductId(), detail.getNumber(), NumConstants.TWO, detail.getCampaignId(), detail.getRuleId());
                log.info("订单号:{},直接调用扣除库存接口入参: {}", orderCode, JSON.toJSONString(param));
                Result<Void> updateInventory = shelfProductFeignClient.updateInventory(param);
                log.info("订单号:{},直接调用扣除库存接口出参: {}", orderCode, JSON.toJSONString(updateInventory));
                if (Objects.isNull(updateInventory) || !updateInventory.isSuccess()) {
                    log.error("订单号: {}创建订单失败,{}扣除库存出现异常", orderCode, detail.getProductName());
                    throw new BusinessException(ErrorCode.INTERNAL_ERROR, shortenProductName(detail.getProductName()) + "库存不足，请返回重试");
                }
                deduceSuccessList.add(detail);
                orderDetailMapper.insert(detail);
            }
            // 扣减积分
            if (order.getOrderPoint() > 0) {
                //得到details中的商品名称并转为String以英文逗号隔开
//                String productNames = details.stream().map(OrderDetail::getProductName).collect(Collectors.joining(","));
                Result<JSONObject> deductRes = openApiFeignClient.deductPointsWithSn(currentMemberInfo.getCardNo(), PointChannelConstant.MINI_EXCHANGE, order.getOrderPoint(), "积分兑换", outSn);
                // {"member_code":"xxx"}
                if (Objects.nonNull(deductRes)
                        && deductRes.isSuccess()
                        && Objects.nonNull(deductRes.getData())
                        && currentMemberInfo.getCardNo().equals(deductRes.getData().getString("member_code"))) {
                    usePointSuccess = true;
                } else {
                    log.error("订单号 {}, 扣减积分失败", orderCode);
                    throw new BusinessException(ErrorCode.INTERNAL_ERROR, PLZ_RETRY_MESSAGE);
                }
            }
            for (OrderDetail detail : details) {
                for (int i = 0; i < detail.getNumber(); i++) {
                    OrderDetailCoupon orderDetailCoupon = new OrderDetailCoupon(null, orderCode, detail.getId(), detail.getProductId(), detail.getVenderId(), null, null, OrderCodeConstants.AbstractOrderDetailStatus.NORMAL);
                    try {
                        Result<ReceiveCouponDTO> couponReceive = openApiFeignClient.couponReceive(new ReceiveCouponVo(detail.getVenderId(), null, 2));
                        if (Objects.isNull(couponReceive) || !couponReceive.isSuccess() || Objects.isNull(couponReceive.getData())) {
                            log.error("用户: {}, 订单: {}, venderId: {}发送优惠券失败", currentMemberInfo.getCardNo(), orderCode, detail.getVenderId());
                        } else {
                            ReceiveCouponDTO receiveCouponDTO = couponReceive.getData();
                            orderDetailCoupon.setCouponCode(receiveCouponDTO.getCouponCode());
                            orderDetailCoupon.setStockId(receiveCouponDTO.getStockId());
                        }
                    } catch (Exception e) {
                        // 优惠券发送失败不影响主流程
                        log.error("用户: {}, 订单: {}, venderId: {}发送优惠券失败", currentMemberInfo.getCardNo(), orderCode, detail.getVenderId(), e);
                    }
                    orderDetailCouponMapper.insert(orderDetailCoupon);
                }
            }
            //返回订单号
            resultDto.setOrderCode(orderCode);
            resultDto.setOrderCreateTimeStamp(order.getCreated().getTime());
            resultDto.setCreated(order.getCreated());
            resultDto.setOrderPoint(order.getOrderPoint());
            resultDto.setOrderAmount(order.getOrderAmount());
            log.info("订单号: {}创建订单接口出参: {}", orderCode, resultDto);
            log.info("订单号: {}商品明细行{}", orderCode, JSON.toJSONString(details));

        } catch (Exception e) {
            if (usePointSuccess) {
                String reOutSn = RandomUtils.generateOutSn(user.getUid());
                openApiFeignClient.addPointsWithSn(currentMemberInfo.getCardNo(), PointChannelConstant.MINI_EXCHANGE, order.getOrderPoint(), "积分兑换失败返还", reOutSn);
            }
            if (!CollectionUtils.isEmpty(deduceSuccessList)) {
                // 恢复库存
                for (OrderDetail detail : deduceSuccessList) {
                    try {
                        InventoryParamDTO param = new InventoryParamDTO(detail.getShelfId(), detail.getShelfProductId(), detail.getNumber(), NumConstants.ONE, detail.getCampaignId(), detail.getRuleId());
                        log.info("订单号:{},直接调用恢复库存接口入参: {}", orderCode, JSON.toJSONString(param));
                        Result<Void> updateInventory = shelfProductFeignClient.updateInventory(param);
                        log.info("订单号:{},直接调用恢复库存接口出参: {}", orderCode, JSON.toJSONString(updateInventory));
                        if (Objects.isNull(updateInventory) || !updateInventory.isSuccess()) {
                            log.error("订单: {}, venderId: {}创建订单失败,恢复库存出现异常", orderCode, detail.getVenderId(), e);
                        }
                    } catch (Exception ex) {
                        log.error("恢复库存失败", ex);
                    }
                }
            }
            throw e;
        } finally {
            redisService.unlock(createOrderKey);
        }

        try {
            //清空购物车
            if (dto.getSource() != null && dto.getSource() == 1) {
                Result<Boolean> cleanUserOrderCart;
                cleanUserOrderCart = shoppingCartClient.cleanUserOrderCart();
                log.info("订单号:{},调用清除购物车接口出参:{}", order.getOrderCode(), JSON.toJSONString(cleanUserOrderCart));
            }
        } catch (Exception e) {
            log.error("创建订单清除购物车异常 {}", e.getMessage(), e);
        }
        try {
            //完成兑礼任务
            interactionTaskFeginClient.taskSuccessRecordByOrder();
        } catch (Exception e) {
            log.error("完成兑礼任务奖励异常"+e.getMessage());
        }
        // 推送兑换成功统计数据
        Globals.newFixedThreadPool.execute(() -> {
            try {
                // 避免异步统计不到订单，待优化
                Thread.sleep(3000);
                SecurityContext.setUser(user);
                ExchangeStaticParamDTO orderStaticParam = getOrderStaticParam(order, details);
                shelfProductFeignClient.updateStatic(orderStaticParam);
            } catch (Exception e) {
                log.error("订单号: {},推送兑换成功统计数据失败", orderCode, e);
            }
        });
        return resultDto;
    }

    /**
     * 计算订单价格
     */
    private void calculateOrderPrice(ExchangeOrder order) {
        log.info("订单号:{},计算订单价格订单信息：{}", order.getOrderCode(), JSON.toJSONString(order));
        // 商品积分
        order.setProductPoint(order.getProductPoint() == null ? 0 : order.getProductPoint());
        // 商品金额
        order.setProductAmount(order.getProductAmount() == null ? BigDecimal.ZERO : order.getProductAmount());
        // 配送费
        order.setExpressAmount(order.getExpressAmount() == null ? BigDecimal.ZERO : order.getExpressAmount());
        //订单价格=商品价格-优惠立减金额-优惠券
        BigDecimal productAmount = order.getProductAmount();
        if (productAmount.compareTo(BigDecimal.ZERO) < 0) {
            productAmount = BigDecimal.ZERO;
        }
        // 订单价格 += (商品总额+快递费)
        // 结果都是保留1位小数到角，分位无论是多少都舍掉
        BigDecimal orderAmount = productAmount.add(order.getExpressAmount())
                .setScale(1, RoundingMode.DOWN);
        if (orderAmount.compareTo(BigDecimal.ZERO) < 1) {
            orderAmount = BigDecimal.ZERO;
        }
        order.setOrderAmount(orderAmount);
        order.setOrderPoint(order.getProductPoint());
    }

    private ExchangeStaticParamDTO getOrderStaticParam(ExchangeOrder order, List<OrderDetail> details) {
        Integer exchangePeople = exchangeOrderMapper.selectExchangePeople(order.getShelfId());
        return ExchangeStaticParamDTO.builder()
                .orderCode(order.getOrderCode())
                .orderPoint(order.getOrderPoint())
                .orderNum(order.getNumber())
                .shelfId(order.getShelfId())
                .exchangePeople(exchangePeople)
                .itemList(details.stream().map(detail -> StaticItemParamDTO.builder()
                        .productName(detail.getProductName())
                        .productId(detail.getProductId())
                        .shelfProductId(detail.getShelfProductId())
                        .number(detail.getNumber())
                        .build()
                ).collect(Collectors.toList())).build();
    }

    @Override
    public PreviewResultDTO preview(OrderPreviewParamDTO paramDto) {

        //获得当前用户
        CurrentUserDTO user = SecurityContext.getUser();
        log.info("订单预览接口入参:{},user:{}", JSON.toJSONString(paramDto), JSON.toJSONString(user));
        PreviewResultDTO previewResultDTO = new PreviewResultDTO();
        //查询商品列表信息
        this.previewOrdinaryProductList(paramDto, previewResultDTO);
        Result<MyPointsDTO> pointsDTOResult = userInfoFeginClient.myPoints();
        if (pointsDTOResult == null || !pointsDTOResult.isSuccess() || Objects.isNull(pointsDTOResult.getData())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, PLZ_RETRY_MESSAGE);
        }
        if (previewResultDTO.getTotalPoint() > pointsDTOResult.getData().getPointsNum()) {
            // int pointGap = previewResultDTO.getTotalPoint() - pointsDTOResult.getData().getPointsNum();
            throw new BusinessException(ErrorCode.EXCHANGE_NOT_ENOUGH_POINT);
        }
        //判断商品是否展示
        List<CartProductDTO> productList = previewResultDTO.getProductList();
        log.info("orderServiceImpl preview productList {}", JSON.toJSONString(productList));
        log.info("订单预览接口出参:{}", JSON.toJSONString(previewResultDTO, SerializerFeature.DisableCircularReferenceDetect));
        return previewResultDTO;
    }

    @Override
    public OrderInfoAppDTO infoApp(String orderCode) {
        ExchangeOrder order = getOneByCode(orderCode);
        LambdaQueryWrapper<OrderDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderDetail::getOrderCode, orderCode);
        List<OrderDetail> listByOrderCode = orderDetailMapper.selectList(queryWrapper);
        if (order == null || CollectionUtils.isEmpty(listByOrderCode)) {
            log.error("订单号:{}不存在", orderCode);
            throw new BusinessException(ErrorCode.REQUEST_SERVICE_ERROR, "订单号不存在");
        }
        OrderInfoAppDTO orderInfoAppDTO = BeanCopierUtils.convertObject(order, OrderInfoAppDTO.class);
        // 订单详情
        List<OrderInfoProductAppDTO> productList = new ArrayList<>();
        orderInfoAppDTO.setProductList(productList);

        for (OrderDetail orderDetail : listByOrderCode) {
            LambdaQueryWrapper<OrderDetailCoupon> orderDetailCouponLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orderDetailCouponLambdaQueryWrapper.eq(OrderDetailCoupon::getOrderDetailId, orderDetail.getId());
            List<OrderDetailCoupon> orderDetailCoupons = orderDetailCouponMapper.selectList(orderDetailCouponLambdaQueryWrapper);
            if (CollectionUtils.isEmpty(orderDetailCoupons)) {
                OrderInfoProductAppDTO orderInfoProductAppDTO = new OrderInfoProductAppDTO();
                orderInfoProductAppDTO.setProductId(orderDetail.getProductId());
                orderInfoProductAppDTO.setImgUrl(orderDetail.getImgUrl());
                orderInfoProductAppDTO.setProductName(orderDetail.getProductName());
                orderInfoProductAppDTO.setPdType(orderDetail.getPdType());
                orderInfoProductAppDTO.setNumber(orderDetail.getNumber());
                orderInfoProductAppDTO.setStatus(orderDetail.getStatus());
                // 不对外暴露venderId
                //orderInfoProductAppDTO.setVenderId(orderDetail.getVenderId());
                orderInfoProductAppDTO.setCouponCode(orderDetail.getCouponCode());
                orderInfoProductAppDTO.setStockId(orderDetail.getStockId());
                orderInfoProductAppDTO.setRealPoint(orderDetail.getRealPoint());
                orderInfoProductAppDTO.setRealAmount(orderDetail.getRealAmount());
                productList.add(orderInfoProductAppDTO);
            } else {
                for (OrderDetailCoupon orderDetailCoupon : orderDetailCoupons) {
                    OrderInfoProductAppDTO orderInfoProductAppDTO = new OrderInfoProductAppDTO();
                    orderInfoProductAppDTO.setProductId(orderDetail.getProductId());
                    orderInfoProductAppDTO.setImgUrl(orderDetail.getImgUrl());
                    orderInfoProductAppDTO.setProductName(orderDetail.getProductName());
                    orderInfoProductAppDTO.setPdType(orderDetail.getPdType());
                    orderInfoProductAppDTO.setNumber(1);
                    orderInfoProductAppDTO.setStatus(orderDetail.getStatus());
                    // 不对外暴露venderId
                    //orderInfoProductAppDTO.setVenderId(orderDetail.getVenderId());
                    orderInfoProductAppDTO.setCouponCode(orderDetailCoupon.getCouponCode());
                    orderInfoProductAppDTO.setStockId(orderDetailCoupon.getStockId());
                    orderInfoProductAppDTO.setRealPoint(orderDetail.getCostPoint());
                    orderInfoProductAppDTO.setRealAmount(orderDetail.getCostPrice());
                    productList.add(orderInfoProductAppDTO);
                }
            }
        }
        return orderInfoAppDTO;
    }

    @Override
    public void delete(OrderDeleteParamDTO dto) {
        ExchangeOrder order = getOneByCode(dto.getOrderCode());
        if (order == null) {
            throw new BusinessException(ErrorCode.REQUEST_SERVICE_ERROR, "订单不存在");
        }
        CurrentUserDTO currentUserDTO = SecurityContext.getUser();
        if (currentUserDTO.getUid() == null ||
                (!currentUserDTO.getUid().equals(order.getUserId()))) {
            // 当查看人的手机号和订单的会员手机号不一致的时候提示
            throw new BusinessException(ErrorCode.BAD_REQUEST, "仅会员本人才可操作该订单");
        }
        int flag = exchangeOrderMapper.deleteById(order.getId());
        if (flag != 1) {
            throw new BusinessException(ErrorCode.REQUEST_SERVICE_ERROR, "订单删除失败");
        }
    }

    private ExchangeOrder getOneByCode(String orderCode) {
        LambdaQueryWrapper<ExchangeOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExchangeOrder::getOrderCode, orderCode);
        ExchangeOrder order = exchangeOrderMapper.selectOne(queryWrapper);
        decodeOrderInfo(order);
        return order;
    }

    private String shortenProductName(String productName) {
        if (StringUtils.isEmpty(productName)) {
            return "";
        }
        if (productName.length() > 7) {
            return productName.substring(0, 7) + "…";
        }
        return productName;

    }

    private void previewOrdinaryProductList(OrderPreviewParamDTO paramDto, PreviewResultDTO previewResultDTO) {
        List<CartProductDTO> productList = new ArrayList<>();
        previewResultDTO.setProductList(productList); //商品列表
        CartResultDTO cartResultDTO;
        CurrentUserDTO user = SecurityContext.getUser();
        // 获得满减活动后的商品
        if (paramDto.getSource() == 1) {
            // 跳转来源为购物车
            Result<CartResultDTO> previewCartOrder;
            previewCartOrder = shoppingCartClient.getPreviewCartOrder();
            if (ObjectUtils.isEmpty(previewCartOrder) || !previewCartOrder.getCode().equals(0)) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "订单预览|" + previewCartOrder.getMsg());
            }
            log.info("调用获取订单预览商品列表接口出参:{}", JSON.toJSONString(previewCartOrder));
            cartResultDTO = previewCartOrder.getData();
        } else {
            //跳转来源为立即购买
            OrderCreateProductDTO product = paramDto.getProductList().get(0);
            log.info("调用单商品购买订单预览接口入参:shelfProductId:{}, number:{}", product.getShelfProductId(), product.getNumber());
            Result<CartResultDTO> cartOrderBySpecId = shoppingCartClient.getCartOrderByShelfProductId(product.getShelfProductId(), product.getNumber());
            log.info("调用单商品购买订单预览接口出参:{}", JSON.toJSONString(cartOrderBySpecId));
            cartResultDTO = cartOrderBySpecId.getData();
        }
        if (cartResultDTO == null || CollectionUtils.isEmpty(cartResultDTO.getValidCarts())) {
            throw new BusinessException("商品列表异常");
        }
        log.info("购物车列表===:{}", JSON.toJSONString(cartResultDTO));
        CartProductDTO productDTO = cartResultDTO.getValidCarts().get(0);
        PurchaseStaticParamDTO purchaseStaticParamDTO = new PurchaseStaticParamDTO();
        purchaseStaticParamDTO.setShelfId(productDTO.getShelfId());
        purchaseStaticParamDTO.setUserId(user.getUid());
        Date monthZero = DateUtils.getMonthZero();
        purchaseStaticParamDTO.setCampaignOnStartTime(monthZero);
        purchaseStaticParamDTO.setCampaignOnEndTime(new Date());
        List<PurchaseStaticDTO> shelfPurchaseStatic = purchaseStatic(purchaseStaticParamDTO);
        for (CartProductDTO cartProductDTO : cartResultDTO.getValidCarts()) {
            if (cartProductDTO.getChecked() == 0) {
                continue;
            }
            // 校验库存
            InventoryParamDTO param = new InventoryParamDTO(cartProductDTO.getShelfId(), cartProductDTO.getShelfProductId(), cartProductDTO.getNumber(), NumConstants.TWO, cartProductDTO.getCampaignId(), cartProductDTO.getRuleId());
            log.info("订单预览库存校验接口入参: {}", JSON.toJSONString(param));
            Result<Void> updateInventory = shelfProductFeignClient.validateInventory(param);
            log.info("订单预览库存校验接口出参: {}", JSON.toJSONString(updateInventory));
            if (Objects.isNull(updateInventory) || !updateInventory.isSuccess()) {
                log.error("订单预览库存校验product: {}", cartProductDTO.getProductName());
                throw new BusinessException(ErrorCode.INTERNAL_ERROR, shortenProductName(cartProductDTO.getProductName()) + "库存不足，请返回重试");
            }
            if ((Objects.isNull(cartProductDTO.getREveryoneLimit()) || cartProductDTO.getREveryoneLimit() == 0)
                    && (Objects.isNull(cartProductDTO.getRPurchaseLimit()) || cartProductDTO.getRPurchaseLimit() == 0)) {
                if (Objects.nonNull(cartProductDTO.getLimitNum()) && cartProductDTO.getLimitNum() > 0) {
                    int myShelfProductIdPurchaseNumber = 0;
                    if (!CollectionUtils.isEmpty(shelfPurchaseStatic)) {
                        myShelfProductIdPurchaseNumber = shelfPurchaseStatic.stream()
                                .filter(d -> Objects.equals(d.getShelfProductId(), cartProductDTO.getShelfProductId()))
                                .mapToInt(PurchaseStaticDTO::getNumber)
                                .sum();
                    }
                    if (myShelfProductIdPurchaseNumber + cartProductDTO.getNumber() > cartProductDTO.getLimitNum()) {
                        throw new BusinessException(ErrorCode.BAD_REQUEST, shortenProductName(cartProductDTO.getProductName()) + " 当月每人限购" + cartProductDTO.getLimitNum() + "件");
                    }
                }
            }
            productList.add(cartProductDTO);
        }
        List<CartProductDTO> previewList = previewResultDTO.getProductList();
        if (!CollectionUtils.isEmpty(previewList)) {
            CartProductDTO cartProductDTO = previewList.get(0);
            Map<Long, List<CartProductDTO>> rules = previewList.stream().filter(c -> Objects.nonNull(c.getRuleId())).collect(Collectors.groupingBy(CartProductDTO::getRuleId));
            if (!CollectionUtils.isEmpty(rules)) {
                for (Map.Entry<Long, List<CartProductDTO>> entry : rules.entrySet()) {
                    Long ruleId = entry.getKey();
                    List<CartProductDTO> rulesProductList = entry.getValue();

                    CartProductDTO detail = rulesProductList.get(0);
                    PurchaseStaticParamDTO param = new PurchaseStaticParamDTO();
                    param.setShelfId(cartProductDTO.getShelfId());
                    param.setUserId(user.getUid());
                    param.setRuleId(ruleId);
                    param.setRuleType(detail.getRuleType());
                    param.setPeriod(detail.getPeriod());
                    param.setRCreated(detail.getRCreated());
                    param.setCampaignOnStartTime(detail.getCampaignOnStartTime());
                    param.setCampaignOnEndTime(detail.getCampaignOnEndTime());
                    List<PurchaseStaticDTO> purchaseStaticDTOS = purchaseStatic(param);
                    Integer myPurchaseNumber = 0;
                    if (!CollectionUtils.isEmpty(purchaseStaticDTOS)) {
                        myPurchaseNumber = purchaseStaticDTOS.stream().mapToInt(PurchaseStaticDTO::getNumber).sum();
                    }
                    int rPurchaseLimit = Optional.ofNullable(detail.getRPurchaseLimit()).orElse(Integer.MAX_VALUE);
                    if (rPurchaseLimit > 0) {
                        int sum = rulesProductList.stream().mapToInt(CartProductDTO::getNumber).sum() + myPurchaseNumber;
                        if (sum > rPurchaseLimit) {
                            throw new BusinessException(ErrorCode.BAD_REQUEST, "该活动中每人最多兑换" + rPurchaseLimit + "件");
                        }
                    }

                    Integer myShelfProductIdPurchaseNumber = 0;
                    for (CartProductDTO rulesProduct : rulesProductList) {
                        int rEveryoneLimit = Optional.ofNullable(rulesProduct.getREveryoneLimit()).orElse(Integer.MAX_VALUE);
                        if (rEveryoneLimit > 0) {
                            if (!CollectionUtils.isEmpty(purchaseStaticDTOS)) {
                                myShelfProductIdPurchaseNumber = purchaseStaticDTOS.stream()
                                        .filter(d -> Objects.equals(d.getShelfProductId(), rulesProduct.getShelfProductId()))
                                        .mapToInt(PurchaseStaticDTO::getNumber)
                                        .sum();
                            }
                            int sum = rulesProduct.getNumber() + myShelfProductIdPurchaseNumber;
                            if (sum > rEveryoneLimit) {
                                log.info("该商品目前每人限购: {}件 item: {}", rEveryoneLimit, rulesProduct);
                                throw new BusinessException(ErrorCode.BAD_REQUEST, shortenProductName(rulesProduct.getProductName()) + "目前每人限购" + rEveryoneLimit + "件");
                            }
                        }
                    }
                }
            }
        }
        previewResultDTO.setTotalPoint(cartResultDTO.getTotalPoint());
        previewResultDTO.setTotalPrice(cartResultDTO.getTotalPrice());
    }

    /**
     * 分页查询订单主表
     *
     * @param param
     * @return PageInfo<ExchangeOrderDTO>
     */
    @Override
    public PageInfo<CrmExchangeOrderListDTO> getExchangeOrderList(CrmExchangeOrderParamDTO param) {
        if (StringUtils.hasText(param.getUserName())) {
            Result<List<Long>> userIdList = userInfoFeginClient.getUserIdListByName(param.getUserName());
            if (Objects.isNull(userIdList) || !userIdList.isSuccess() || CollectionUtils.isEmpty(userIdList.getData())) {
                param.setUserIdList(new ArrayList<>());
            } else {
                param.setUserIdList(userIdList.getData());
            }
        }
        IPage<CrmExchangeOrderListDTO> page = exchangeOrderMapper.selectPageByParam(new Page<>(param.getPageNum(), param.getPageSize()), param);
        List<CrmExchangeOrderListDTO> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new PageInfo<>();
        }
        List<Long> uidList = records.stream().map(CrmExchangeOrderListDTO::getUserId).collect(Collectors.toList());
        Result<List<UserSimpleDTO>> dbUserSimpleInfoListRes = userInfoFeginClient.getDbUserSimpleInfoList(uidList);
        Map<Long, UserSimpleDTO> userMap = new HashMap<>();
        if (Objects.isNull(dbUserSimpleInfoListRes) || !dbUserSimpleInfoListRes.isSuccess()) {
            log.error("获取用户信息失败");
        } else if (Objects.nonNull(dbUserSimpleInfoListRes.getData())) {
            userMap = dbUserSimpleInfoListRes.getData().stream().collect(Collectors.toMap(UserSimpleDTO::getId, Function.identity()));
        }
        for (CrmExchangeOrderListDTO record : records) {
            UserSimpleDTO userSimpleDTO = userMap.get(record.getUserId());
            if (Objects.isNull(userSimpleDTO)) {
                record.setUserName("用户名称获取失败");
            } else {
                record.setUserName(userSimpleDTO.getUsername());
            }
            if (Objects.equals(record.getOrderStatus(), OrderCodeConstants.AbstractOrderStatus.EXPIRED)) {
                record.setOrderStatus(OrderCodeConstants.AbstractOrderStatus.PENDING);
            }
        }
        return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), records);
    }

    /**
     * 根据ID查询订单主表
     *
     * @param id
     * @return ExchangeOrderDTO
     */
    @Override
    public ExchangeOrderDTO getExchangeOrderById(Long id) {
        ExchangeOrder exchangeOrder = exchangeOrderMapper.selectById(id);
        return BeanCopierUtils.convertObject(exchangeOrder, ExchangeOrderDTO.class);
    }

    /**
     * 保存订单主表
     *
     * @param param
     * @return Long
     */
    @Override
    public Long saveExchangeOrder(ExchangeOrderDTO param) {
        ExchangeOrder exchangeOrder = new ExchangeOrder(param.getId(), param.getOrderCode(), param.getOrderType(), param.getShelfId(), param.getProductPoint(), param.getProductAmount(), param.getOrderPoint(), param.getOrderAmount(), param.getNumber(), param.getUserId(), param.getUserPhone(), param.getUserCrmCode(), param.getUserCardLevel(), param.getReceiverId(), param.getReceiverName(), param.getReceiverPhone(), param.getReceiverAddress(), param.getReceiverPostcode(), param.getExpressAmount(), param.getOrderStatus(), param.getPayStatus(), param.getPayType(), param.getPayTime(), param.getPayCode(), param.getTradeId(), param.getExpressStatus(), param.getExpressId(), param.getExpressCode(), param.getFormId(), param.getRemark(), param.getFinishTime(), param.getSyncStatus());
        if (ParamUtils.isNullOr0Long(exchangeOrder.getId())) {
            exchangeOrderMapper.insert(exchangeOrder);
        } else {
            exchangeOrderMapper.updateById(exchangeOrder);
        }
        return exchangeOrder.getId();
    }

    /**
     * 根据ID删除订单主表
     *
     * @param param
     */
    @Override
    public void deleteExchangeOrderById(IdCodeDTO param) {
        exchangeOrderMapper.deleteById(param.getId());
    }

    @Override
    public void exportOrderList(DownloadAddParamDTO exportParam) {
        String jsonParam = exportParam.getJsonParam();
        String fileName = exportParam.getFileName();
        String fileExt = exportParam.getFileExt();
        String reportCode = exportParam.getReportCode();
        Long downloadCenterId = exportParam.getDownloadCenterId();
        CurrentUserDTO commonLoginDTO = SecurityContext.getUser();
        CrmExchangeOrderParamDTO param = JSON.parseObject(jsonParam, CrmExchangeOrderParamDTO.class);
        List<JSONObject> reList = getOrderExportDTO(param);
        exportService.writeExportList(fileName, fileExt, reportCode, downloadCenterId, commonLoginDTO, reList);
    }

    @Override
    @Transactional
    public int delivery(OrderDeliveryParamDTO param) {
        LambdaQueryWrapper<ExchangeOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExchangeOrder::getOrderCode, param.getOrderCode());
        ExchangeOrder exchangeOrder = exchangeOrderMapper.selectOne(queryWrapper);
        if (Objects.isNull(exchangeOrder)) {
            throw new BusinessException("订单不存在");
        }
        if (Objects.equals(exchangeOrder.getExpressStatus(), OrderCodeConstants.AbstractOrderExpressStatus.SUCCESS)) {
            throw new BusinessException("订单已发货");
        }
        if (StringUtils.isEmpty(param.getExpressCode())) {
            return 0;
        }
        exchangeOrder.setExpressCode(param.getExpressCode());
        exchangeOrder.setExpressStatus(OrderCodeConstants.AbstractOrderExpressStatus.SUCCESS);
        int i = exchangeOrderMapper.updateById(exchangeOrder);
        orderSendMapper.insert(new OrderSend(null, param.getOrderCode(), param.getExpressCode()));
        return i;
    }

    @Override
    @Transactional
    public Boolean importDelivery(List<OrderDeliveryParamDTO> list) {
        for (OrderDeliveryParamDTO dto : list) {
            try {
                delivery(dto);
            } catch (Exception e) {
                log.error("订单: {}, 发货单: {}发货失败", dto.getOrderCode(), dto.getExpressCode(), e);
            }
        }
        return true;
    }

    @Override
    public List<PurchaseStaticDTO> purchaseStatic(PurchaseStaticParamDTO param) {
        if (Objects.equals(param.getRuleType(), NumConstants.TWO) && Objects.nonNull(param.getCampaignOnStartTime())) {
            Date[] currentCycleStartAndEndDate = getCurrentCycleStartAndEndDate(param.getCampaignOnStartTime(), param.getPeriod());
            param.setPeriodStart(currentCycleStartAndEndDate[0]);
            Date periodEnd = currentCycleStartAndEndDate[1];
            if (periodEnd.after(param.getCampaignOnEndTime())) {
                periodEnd = param.getCampaignOnEndTime();
            }
            param.setPeriodEnd(periodEnd);

        } else {
            param.setPeriodStart(param.getCampaignOnStartTime());
            param.setPeriodEnd(param.getCampaignOnEndTime());
        }
        List<PurchaseStaticDTO> purchaseStaticDTOList = orderDetailMapper.selectPurchaseStatic(param);
        if (CollectionUtils.isEmpty(purchaseStaticDTOList)) {
            return new ArrayList<>();
        }
        return purchaseStaticDTOList;
    }

    public static Date[] getCurrentCycleStartAndEndDate(Date rCreated, int cycleDays) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(rCreated);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        Date cycleStartDate = calendar.getTime();

        Calendar now = Calendar.getInstance();
        long diffInMillis = now.getTimeInMillis() - cycleStartDate.getTime();
        int daysPassed = (int) (diffInMillis / (24 * 60 * 60 * 1000));

        int currentCycle = daysPassed / cycleDays;

        calendar.add(Calendar.DAY_OF_MONTH, currentCycle * cycleDays);
        Date currentCycleStartDate = calendar.getTime();

        calendar.add(Calendar.DAY_OF_MONTH, cycleDays);
        Date currentCycleEndDate = calendar.getTime();
        if (currentCycleStartDate.before(rCreated)) {
            currentCycleStartDate = rCreated;
        }
        return new Date[]{currentCycleStartDate, currentCycleEndDate};
    }

    @Override
    public void changeOrderStatusByOrderCode(String orderCode) {
        // 相同订单 5分钟内不重复执行
//        boolean lock = redisService.lock("order_status_change_" + orderCode, 60 * 5);
//        if (!lock) {
//            return;
//        }
        if (StringUtils.hasText(orderCode)) {
            LambdaQueryWrapper<ExchangeOrder> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ExchangeOrder::getOrderCode, orderCode);
            queryWrapper.eq(ExchangeOrder::getOrderStatus, OrderCodeConstants.AbstractOrderStatus.PENDING);
            ExchangeOrder exchangeOrder = exchangeOrderMapper.selectOne(queryWrapper);
            if (Objects.isNull(exchangeOrder)) {
                return;
            }
            LambdaQueryWrapper<OrderDetail> orderDetailLambdaQueryWrapper = new LambdaQueryWrapper<>();
            orderDetailLambdaQueryWrapper.eq(OrderDetail::getOrderCode, orderCode);
            orderDetailLambdaQueryWrapper.eq(OrderDetail::getStatus, OrderCodeConstants.AbstractOrderDetailStatus.NORMAL);
            List<OrderDetail> orderDetails = orderDetailMapper.selectList(orderDetailLambdaQueryWrapper);
            int successCount = 0;
            int expiredCount = 0;
            for (OrderDetail orderDetail : orderDetails) {
                LambdaQueryWrapper<OrderDetailCoupon> orderDetailCouponLambdaQueryWrapper = new LambdaQueryWrapper<>();
                orderDetailCouponLambdaQueryWrapper.eq(OrderDetailCoupon::getOrderDetailId, orderDetail.getId());
                List<OrderDetailCoupon> orderDetailCoupons = orderDetailCouponMapper.selectList(orderDetailCouponLambdaQueryWrapper);
                int successDetailCount = 0;
                int expiredDetailCount = 0;
                if (CollectionUtils.isEmpty(orderDetailCoupons)) {
                    if (Objects.equals(orderDetail.getStatus(), OrderCodeConstants.AbstractOrderDetailStatus.NORMAL)) {
                        if (StringUtils.hasText(orderDetail.getCouponCode())) {
                            Result<JSONObject> couponDetail = openApiFeignClient.getCouponDetail(orderDetail.getStockId(), exchangeOrder.getUserCrmCode(), orderDetail.getCouponCode());
                            // {"use_type":3,"sub_name":"满300元可用","coupon_code":"0O40MK0D3R2MAO1XYQYA553KEW6TPHYD","shop_jump_info":{"appid":"","jump_type":0,"link":""},"use_type_desc":"","name":"双十二 40元测试券","icon":"https://open-static.muji.com.cn/test/crm/resource/coupon_icon/202412/1733305894050_s_1191_1191.png","detail":"1、凭此券与有效MUJI passport会员身份，在有效期内前往中国（港澳台地区除外）無印良品实体零售店铺，单笔消费满300元，享40元优惠；\n\n2、本券使用日期为2024年12月9日至2024年12月12日。优惠券仅限在有效期内使用一次，不可拆分使用；不可兑现；不可找零，过期则无法使用、不予退回；\n\n3、一笔订单仅限使用一张优惠券，本券不可和其他代金券、抵扣券等其他优惠券同时使用；使用后，如发生退货换货，则本券不予退回；\n\n4、使用时，请在结账前前往会员小程序「无印良品MUJI-优惠券」出示本优惠券给到店员扫码或在自助收银机扫码完成使用，截图等券码形式视为无效；本券在餐厅使用时，需联络餐厅工作人员，进行人工核销；\n\n5、本券不适用于以下店铺及设备：無印良品MUJI天猫/京东旗舰店/京东自营旗舰店/无印良品MUJI天猫家居旗舰店、官方网络商城、微信小程序商城、无印良品MUJI美团外卖店/饿了么外卖店、无印良品MUJI拼多多官方奥莱店、MUJI INFILL店铺、MUJI HOTEL、MUJIcom洗衣房、自助饮品机、自动贩卖机、自助点餐平台；\n\n6、本券仅限MUJI passport会员用户本人领取及使用，同一MUJI passport会员号、同一身份证号、同一微信/支付宝账号、同一手机号码、同一设备号，满足以上任一条件均视为同一用户。若发现违规行为（如恶意获取、虚假交易、恶意套取现金、通过自动化手段/刷屏软件或其他手段获得等情况），無印良品有权取消其优惠资格及优惠金额；\n\n7、客服联络：邮箱：<EMAIL>  ；人工：************（普通话服务）；服务时间：周一至周日，9:00-22:01（国家法定节假日除外）。","expired_at":"2024-12-28 23:59:59","type":6,"stock_id":"cp000000000006063","status":1}
                            if (Objects.isNull(couponDetail) || !couponDetail.isSuccess() || Objects.isNull(couponDetail.getData())) {
                                // 已过期
                                expiredCount++;
                                expiredDetailCount++;
                                orderDetail.setStatus(OrderCodeConstants.AbstractOrderDetailStatus.EXPIRED);
                            } else {
                                // status 状态 1:可使用 2:已使用 3:已过期 4:已作废
                                // expired_at 过期时间
                                JSONObject data = couponDetail.getData();
                                if (Objects.equals(data.getInteger("status"), MUJICouponStatusConstants.USABLE)) {
                                    // 可使用
                                    orderDetail.setStatus(OrderCodeConstants.AbstractOrderDetailStatus.NORMAL);
                                } else if (Objects.equals(data.getInteger("status"), MUJICouponStatusConstants.USED)) {
                                    // 已使用
                                    orderDetail.setStatus(OrderCodeConstants.AbstractOrderDetailStatus.SUCCESS);
                                    successCount++;
                                    successDetailCount++;
                                } else if (Objects.equals(data.getInteger("status"), MUJICouponStatusConstants.EXPIRED)) {
                                    // 已过期
                                    expiredCount++;
                                    expiredDetailCount++;
                                    orderDetail.setStatus(OrderCodeConstants.AbstractOrderDetailStatus.EXPIRED);
                                } else {
                                    // 已过期
                                    orderDetail.setStatus(OrderCodeConstants.AbstractOrderDetailStatus.EXPIRED);
                                    expiredCount++;
                                    expiredDetailCount++;
                                }
                            }
                        } else {
                            // 已过期
                            orderDetail.setStatus(OrderCodeConstants.AbstractOrderDetailStatus.EXPIRED);
                            expiredCount++;
                            expiredDetailCount++;
                        }
                    } else if (Objects.equals(orderDetail.getStatus(), OrderCodeConstants.AbstractOrderDetailStatus.SUCCESS)) {
                        successCount++;
                        successDetailCount++;
                    } else {
                        expiredCount++;
                        expiredDetailCount++;
                    }

                    if (successDetailCount == orderDetails.size()) {
                        orderDetail.setStatus(OrderCodeConstants.AbstractOrderDetailStatus.SUCCESS);
                    }
                    if (expiredDetailCount == orderDetails.size()) {
                        orderDetail.setStatus(OrderCodeConstants.AbstractOrderDetailStatus.EXPIRED);
                    }
                    if (orderDetail.getStatus() != OrderCodeConstants.AbstractOrderDetailStatus.NORMAL) {
                        orderDetailMapper.updateById(orderDetail);
                    }
                } else {
                    for (OrderDetailCoupon orderDetailCoupon : orderDetailCoupons) {
                        if (Objects.equals(orderDetailCoupon.getStatus(), OrderCodeConstants.AbstractOrderDetailStatus.NORMAL)) {
                            if (StringUtils.hasText(orderDetailCoupon.getCouponCode())) {
                                Result<JSONObject> couponDetail = openApiFeignClient.getCouponDetail(orderDetailCoupon.getStockId(), exchangeOrder.getUserCrmCode(), orderDetailCoupon.getCouponCode());
                                // {"use_type":3,"sub_name":"满300元可用","coupon_code":"0O40MK0D3R2MAO1XYQYA553KEW6TPHYD","shop_jump_info":{"appid":"","jump_type":0,"link":""},"use_type_desc":"","name":"双十二 40元测试券","icon":"https://open-static.muji.com.cn/test/crm/resource/coupon_icon/202412/1733305894050_s_1191_1191.png","detail":"1、凭此券与有效MUJI passport会员身份，在有效期内前往中国（港澳台地区除外）無印良品实体零售店铺，单笔消费满300元，享40元优惠；\n\n2、本券使用日期为2024年12月9日至2024年12月12日。优惠券仅限在有效期内使用一次，不可拆分使用；不可兑现；不可找零，过期则无法使用、不予退回；\n\n3、一笔订单仅限使用一张优惠券，本券不可和其他代金券、抵扣券等其他优惠券同时使用；使用后，如发生退货换货，则本券不予退回；\n\n4、使用时，请在结账前前往会员小程序「无印良品MUJI-优惠券」出示本优惠券给到店员扫码或在自助收银机扫码完成使用，截图等券码形式视为无效；本券在餐厅使用时，需联络餐厅工作人员，进行人工核销；\n\n5、本券不适用于以下店铺及设备：無印良品MUJI天猫/京东旗舰店/京东自营旗舰店/无印良品MUJI天猫家居旗舰店、官方网络商城、微信小程序商城、无印良品MUJI美团外卖店/饿了么外卖店、无印良品MUJI拼多多官方奥莱店、MUJI INFILL店铺、MUJI HOTEL、MUJIcom洗衣房、自助饮品机、自动贩卖机、自助点餐平台；\n\n6、本券仅限MUJI passport会员用户本人领取及使用，同一MUJI passport会员号、同一身份证号、同一微信/支付宝账号、同一手机号码、同一设备号，满足以上任一条件均视为同一用户。若发现违规行为（如恶意获取、虚假交易、恶意套取现金、通过自动化手段/刷屏软件或其他手段获得等情况），無印良品有权取消其优惠资格及优惠金额；\n\n7、客服联络：邮箱：<EMAIL>  ；人工：************（普通话服务）；服务时间：周一至周日，9:00-22:01（国家法定节假日除外）。","expired_at":"2024-12-28 23:59:59","type":6,"stock_id":"cp000000000006063","status":1}
                                if (Objects.isNull(couponDetail) || !couponDetail.isSuccess() || Objects.isNull(couponDetail.getData())) {
                                    // 已过期
                                    expiredCount++;
                                    expiredDetailCount++;
                                    orderDetailCoupon.setStatus(OrderCodeConstants.AbstractOrderDetailStatus.EXPIRED);
                                } else {
                                    // status 状态 1:可使用 2:已使用 3:已过期 4:已作废
                                    // expired_at 过期时间
                                    JSONObject data = couponDetail.getData();
                                    if (Objects.equals(data.getInteger("status"), 1)) {
                                        // 可使用
                                        orderDetailCoupon.setStatus(OrderCodeConstants.AbstractOrderDetailStatus.NORMAL);
                                    } else if (Objects.equals(data.getInteger("status"), 2)) {
                                        // 已使用
                                        orderDetailCoupon.setStatus(OrderCodeConstants.AbstractOrderDetailStatus.SUCCESS);
                                        successCount++;
                                        successDetailCount++;
                                    } else if (Objects.equals(data.getInteger("status"), 3)) {
                                        // 已过期
                                        expiredCount++;
                                        expiredDetailCount++;
                                        orderDetailCoupon.setStatus(OrderCodeConstants.AbstractOrderDetailStatus.EXPIRED);
                                    } else {
                                        // 已过期
                                        orderDetailCoupon.setStatus(OrderCodeConstants.AbstractOrderDetailStatus.EXPIRED);
                                        expiredCount++;
                                        expiredDetailCount++;
                                    }
                                }

                            } else {
                                expiredCount++;
                                expiredDetailCount++;
                                orderDetailCoupon.setStatus(OrderCodeConstants.AbstractOrderDetailStatus.EXPIRED);
                            }
                        } else if (Objects.equals(orderDetailCoupon.getStatus(), OrderCodeConstants.AbstractOrderDetailStatus.SUCCESS)) {
                            successCount++;
                            successDetailCount++;
                        } else {
                            expiredCount++;
                            expiredDetailCount++;
                        }
                        if (orderDetailCoupon.getStatus() != OrderCodeConstants.AbstractOrderDetailStatus.NORMAL) {
                            orderDetailCouponMapper.updateById(orderDetailCoupon);
                        }
                    }
                    if (successDetailCount == orderDetailCoupons.size()) {
                        orderDetail.setStatus(OrderCodeConstants.AbstractOrderDetailStatus.SUCCESS);
                    }
                    if (expiredDetailCount == orderDetailCoupons.size()) {
                        orderDetail.setStatus(OrderCodeConstants.AbstractOrderDetailStatus.EXPIRED);
                    }
                    if (orderDetail.getStatus() != OrderCodeConstants.AbstractOrderDetailStatus.NORMAL) {
                        orderDetailMapper.updateById(orderDetail);
                    }
                }

            }
            if (successCount == exchangeOrder.getNumber()) {
                exchangeOrder.setOrderStatus(OrderCodeConstants.AbstractOrderStatus.SUCCESS);
            }
            if (expiredCount == exchangeOrder.getNumber()) {
                exchangeOrder.setOrderStatus(OrderCodeConstants.AbstractOrderStatus.EXPIRED);
            }
            if (exchangeOrder.getOrderStatus() != OrderCodeConstants.AbstractOrderStatus.PENDING) {
                exchangeOrderMapper.updateById(exchangeOrder);
            }
        }
    }

    @Override
    public void changeOrderStatus() {
        CurrentUserDTO user = SecurityContext.getUser();
        if (Objects.nonNull(user) && user.getUid() != 0) {
            LambdaQueryWrapper<ExchangeOrder> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ExchangeOrder::getUserId, user.getUid());
            queryWrapper.eq(ExchangeOrder::getOrderStatus, OrderCodeConstants.AbstractOrderStatus.PENDING);
            List<ExchangeOrder> exchangeOrderList = exchangeOrderMapper.selectList(queryWrapper);
            for (ExchangeOrder exchangeOrder : exchangeOrderList) {
                changeOrderStatusByOrderCode(exchangeOrder.getOrderCode());
            }
        }
    }

    @Override
    public void changeAllOrderStatus() {
        LambdaQueryWrapper<ExchangeOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ExchangeOrder::getOrderStatus, OrderCodeConstants.AbstractOrderStatus.PENDING);
        List<ExchangeOrder> exchangeOrderList = exchangeOrderMapper.selectList(queryWrapper);
        for (ExchangeOrder exchangeOrder : exchangeOrderList) {
            changeOrderStatusByOrderCode(exchangeOrder.getOrderCode());
        }
    }

    @Override
    public PageInfo<CpStaticDTO> cpStatic(CpStaticParamDTO param) {
        Page<CpStaticDTO> page = new Page<>(param.getPageNum(), param.getPageSize());
        IPage<CpStaticDTO> iPage = orderDetailMapper.selectCpStatic(page, param);
        return new PageInfo<>(iPage.getCurrent(), iPage.getSize(), iPage.getTotal(), iPage.getRecords());
    }

    @Override
    public List<MujiOrder> selectMujiOrder(Date beginTime, Date endTime) {
        return orderDetailMapper.selectMujiOrder(beginTime, endTime);
    }

    @Override
    public List<String> getUnusedStockId() {
        Long uid = SecurityContext.getUser().getUid();
        return orderDetailCouponMapper.getUnusedStockIdListByUserId(uid);
    }

    @Override
    public List<String> sftpOrderList(String memberCode,
                                      String startTime,
                                      String endTime,
                                      String deptId,
                                      String depaId,
                                      String lineId,
                                      String classId,
                                      String janId) {
        // 取模分表
        int mod = Math.abs(memberCode.hashCode()) % 64;
        List<OdsOrderProduct> odsOrderProductList = orderDetailMapper.sftpOrderList(mod + "", memberCode, startTime, endTime);
        if (CollectionUtils.isEmpty(odsOrderProductList)) {
            return new ArrayList<>();
        }
        List<OdsOrderProductDTO> odsOrderProductDTOList = BeanCopierUtils.convertList(odsOrderProductList, OdsOrderProductDTO.class);
        for (OdsOrderProductDTO odsOrderProduct : odsOrderProductDTOList) {
            Result<List<OdsItemDTO>> result = productFeignClient.selectBySftpProductItemId(odsOrderProduct.getItemIdStr());
            if (null != result && !CollectionUtils.isEmpty(result.getData())) {
                odsOrderProduct.setDeptIdStr(result.getData().get(0).getDeptIdStr());
                odsOrderProduct.setDepaIdStr(result.getData().get(0).getDepaIdStr());
                odsOrderProduct.setLineIdStr(result.getData().get(0).getLineIdStr());
                odsOrderProduct.setClassIdStr(result.getData().get(0).getClassIdStr());
            }
        }
        List<String> deptIdList = new ArrayList<>();
        if (!StringUtils.isEmpty(deptId)){
            deptIdList=List.of(deptId.split(","));
        }
        List<String> depaIdList = new ArrayList<>();
        if (!StringUtils.isEmpty(depaId)){
            depaIdList=List.of(depaId.split(","));
        }
        List<String> lineIdList = new ArrayList<>();
        if (!StringUtils.isEmpty(lineId)){
            lineIdList=List.of(lineId.split(","));
        }
        List<String> classIdList = new ArrayList<>();
        if (!StringUtils.isEmpty(classId)) {
            classIdList = List.of(classId.split(","));
        }
        List<String> janIdList = new ArrayList<>();
        if (!StringUtils.isEmpty(janId)) {
            janIdList = List.of(janId.split(","));
        }
        List<String> resultList = new ArrayList<>();
        for (OdsOrderProductDTO odsOrderProduct : odsOrderProductDTOList) {
            if (!StringUtils.isEmpty(odsOrderProduct.getDeptIdStr()) && deptIdList.contains(odsOrderProduct.getDeptIdStr())) {
                resultList.add("deptId");
                continue;
            }
            if (!StringUtils.isEmpty(odsOrderProduct.getDepaIdStr()) && depaIdList.contains(odsOrderProduct.getDepaIdStr())) {
                resultList.add("depaId");
                continue;
            }
            if (!StringUtils.isEmpty(odsOrderProduct.getLineIdStr()) && lineIdList.contains(odsOrderProduct.getLineIdStr())) {
                resultList.add("lineId");
                continue;
            }
            if (!StringUtils.isEmpty(odsOrderProduct.getClassIdStr()) && classIdList.contains(odsOrderProduct.getClassIdStr())) {
                resultList.add("classId");
                continue;
            }
            if (!StringUtils.isEmpty(odsOrderProduct.getItemIdStr()) && janIdList.contains(odsOrderProduct.getItemIdStr())) {
                resultList.add("janId");
            }
        }
        return resultList;
    }

    @Override
    public Integer selectExchangePeople(Long shelfId) {
        return exchangeOrderMapper.selectExchangePeople(shelfId);
    }

    private List<JSONObject> getOrderExportDTO(CrmExchangeOrderParamDTO param) {
        if (StringUtils.hasText(param.getUserName())) {
            Result<List<Long>> userIdList = userInfoFeginClient.getUserIdListByName(param.getUserName());
            if (Objects.isNull(userIdList) || !userIdList.isSuccess() || CollectionUtils.isEmpty(userIdList.getData())) {
                param.setUserIdList(new ArrayList<>());
            } else {
                param.setUserIdList(userIdList.getData());
            }
        }
        List<CrmExchangeOrderListDTO> orderList = exchangeOrderMapper.selectListByParam(param);
        if (CollectionUtils.isEmpty(orderList)) {
            return new ArrayList<>();
        }
        List<Long> uidList = orderList.stream().map(CrmExchangeOrderListDTO::getUserId).collect(Collectors.toList());
        List<OrderExportDTO> exportList = new ArrayList<>();
        Result<List<UserSimpleDTO>> dbUserSimpleInfoListRes = userInfoFeginClient.getDbUserSimpleInfoList(uidList);
        Map<Long, UserSimpleDTO> userMap = new HashMap<>();
        if (Objects.isNull(dbUserSimpleInfoListRes) || !dbUserSimpleInfoListRes.isSuccess()) {
            log.error("获取用户信息失败");
        } else if (Objects.nonNull(dbUserSimpleInfoListRes.getData())) {
            userMap = dbUserSimpleInfoListRes.getData().stream().collect(Collectors.toMap(UserSimpleDTO::getId, Function.identity()));
        }
        for (CrmExchangeOrderListDTO order : orderList) {
            if (Objects.equals(order.getOrderStatus(), OrderCodeConstants.AbstractOrderStatus.EXPIRED)) {
                order.setOrderStatus(OrderCodeConstants.AbstractOrderStatus.PENDING);
            }
            OrderExportDTO exportItem = BeanCopierUtils.convertObject(order, OrderExportDTO.class);
            UserSimpleDTO userSimpleDTO = userMap.get(exportItem.getUserId());
            if (Objects.isNull(userSimpleDTO)) {
                exportItem.setUserName("用户名称获取失败");
            } else {
                exportItem.setUserName(userSimpleDTO.getUsername());
            }
            exportList.add(exportItem);
        }
        return new ArrayList<>(JSON.parseArray(JSON.toJSONString(exportList), JSONObject.class));
    }
}