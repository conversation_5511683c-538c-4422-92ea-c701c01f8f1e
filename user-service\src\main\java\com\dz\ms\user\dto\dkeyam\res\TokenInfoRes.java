package com.dz.ms.user.dto.dkeyam.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 用户信息
 * @author: fei
 * @date: 2025/01/22 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TokenInfoRes implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("1：时间令牌 2：短信令牌")
    private Integer type;

    @ApiModelProperty(" type=1时：2：硬件令牌4：APP(手机)令牌" +
            "type=2时：1：短信令牌")
    private Integer model;

    @ApiModelProperty("令牌序列号(如果是短信令牌，则是手机号)")
    private String serial;

    @ApiModelProperty("令牌到期时间")
    private Long expireTime;
    
}
