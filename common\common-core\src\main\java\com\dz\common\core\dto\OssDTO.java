package com.dz.common.core.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * oss
 *
 * <AUTHOR>
 * @date 2022/8/12 13:37
 */
@Getter
@Setter
@NoArgsConstructor
public class OssDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 二进制文件流
     */
    private byte[] bytes;
    /**
     * 文件名
     */
    private String key;

}
