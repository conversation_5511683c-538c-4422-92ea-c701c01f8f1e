package com.dz.common.core.filter;

import com.alibaba.fastjson.JSON;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 重写request传递heaader
 * <AUTHOR>
 */
@Slf4j
@Component
public class ModifyRequestInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        HttpServletRequest request = getHttpServletRequest();
        Map<String,String> headers = null;
        String fromUrl = "";
        if(null != request) {
            fromUrl = request.getRequestURI();
            headers = getHeaders(request);
        }
        else {
            headers = new HashMap<>();
        }
        log.info("{} rpc to:{}",fromUrl, requestTemplate.path());
        CurrentUserDTO currentUser = SecurityContext.getUser();
        if(null != currentUser) {
            if(null != currentUser.getTenantId()) {
                headers.put("tenantId",currentUser.getTenantId().toString());
            }
            if(!headers.containsKey("type") && null != currentUser.getType()) {
                headers.put("type",currentUser.getType().toString());
            }
            if(!headers.containsKey("uid") && null != currentUser.getUid()) {
                headers.put("uid",currentUser.getUid().toString());
            }
            if(!headers.containsKey("storeId") && null != currentUser.getStoreId()) {
                headers.put("storeId",currentUser.getStoreId().toString());
            }
        }
        for(String headerName : headers.keySet()){
            if("content-length".equals(headerName)) {
                continue;
            }
            requestTemplate.header(headerName, headers.get(headerName));
        }
        requestTemplate.removeHeader("rpc").header("rpc","1");
    }

    private HttpServletRequest getHttpServletRequest() {
        try {
            return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        } catch (Exception e) {
            //e.printStackTrace();
            return null;
        }
    }

    private Map<String, String> getHeaders(HttpServletRequest request) {
        Map<String, String> map = new LinkedHashMap<>();
        Enumeration<String> enumeration = request.getHeaderNames();
        while (enumeration.hasMoreElements()) {
            String key = enumeration.nextElement();
            String value = request.getHeader(key);
            map.put(key, value);
        }
        return map;
    }
}
