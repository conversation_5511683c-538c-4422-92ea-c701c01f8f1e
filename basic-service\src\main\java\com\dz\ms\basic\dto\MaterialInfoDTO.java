package com.dz.ms.basic.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 素材信息DTO
 * @author: Handy
 * @date:   2022/2/4 17:39
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "素材信息")
public class MaterialInfoDTO extends BaseDTO {

    @ApiModelProperty(value = "素材ID")
    private Long id;
    @ApiModelProperty(value = "素材类型 1图片 2视频 3音频 0已过期")
    private Integer materialType;
    @ApiModelProperty(value = "分组ID")
    private Long groupId;
    @ApiModelProperty(value = "素材名称")
    private String materialName;
    @ApiModelProperty(value = "素材地址")
    private String materialUrl;
    @ApiModelProperty(value = "视频封面")
    private String videoPoster;
    @ApiModelProperty(value = "替换素材地址")
    private String replaceUrl;
    @ApiModelProperty(value = "生效时间")
    private Date effectiveTime;
    @ApiModelProperty(value = "失效时间")
    private Date expireTime;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "素材ID列表")
    private List<Long> ids;
    @ApiModelProperty(value = "有效期")
    private String validity;
    @ApiModelProperty(value = "使用场景")
    private Set<String> useScene;
}
