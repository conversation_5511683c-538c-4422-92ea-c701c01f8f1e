<template>
  <div @click="showModal">
    <slot name="default"></slot>
  </div>
  <a-modal title="弹窗设置" :width="600" centered :maskClosable="false" :closable="true" :open="visible" @cancel="visible=false">
    <a-form ref="addForm" :model="addParams">
      <a-form-item label="添加图片" name="imgUrl" :rules="[{required:false,message:'请上传',trigger:'change'}]">
        <uploadImg :max="10" :width="300" :height="300" :imgUrl="addParams.imgUrl" :form="addParams" path="imgUrl" :disabled="disabled" @success="uploadSuccess" />
      </a-form-item>
      <a-form-item label="添加热区" name="imgLinks">
        <addLink type="2" :isEvent="isEvent" :showType="showType" :links="addParams.imgLinks" @ok="(link)=>addParams.imgLinks=link">
          <a-button>热区设置</a-button>
        </addLink>
      </a-form-item>
    </a-form>
    <template #footer v-if="!disabled">
      <a-space>
        <a-button @click="visible=false">取消</a-button>
        <a-button type="primary" @click="ok">确定</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup>
import { cloneDeep } from 'lodash'
import { nextTick } from 'vue';
const addForm = ref(null)
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  // 弹窗数据
  data: {
    type: Object,
    default() {
      // 字段固定
      return {
        imgUrl: '',
        imgLinks: []
      }
    }
  },
  // 是否可以添加事件
  isEvent: {
    type: Boolean,
    default: false
  },
  // 显示的行为类型
  showType: {
    type: Array,
    default() {
      return [1, 3] // 跳转  关闭弹窗
    }
  }
})


const { visible, addParams } = toRefs(reactive({
  visible: false,
  addParams: {
    imgUrl: '',
    imgLinks: []
  }
}))

// 点击显示弹窗
const showModal = () => {
  if (props.disabled) return
  addParams.value = cloneDeep(props.data)
  console.log("🚀 ~ showModal ~  addParams.value:", addParams.value)
  visible.value = true
}


// 上传图片 视频
const uploadSuccess = async (data) => {
  let { form, path, imgUrl } = data;
  form[path] = imgUrl
  await nextTick()
  addForm.value.validateFields([path])
}

// 保存设置
const ok = () => {
  addForm.value.validate().then(res => {
    visible.value = false
    emit('ok', addParams.value)
  })
}
</script>

<style  scoped lang="scss">
.modal {
  display: flex;
  min-height: 500px;
  max-height: 1000px;
  &-img {
    position: relative;
    padding-right: 20px;
    border-right: 2px dashed #d9d9d9;
    margin-right: 20px;
    max-height: 500px;
    overflow-y: auto;
  }
  &-box {
    position: relative;
    width: 375px;
    height: auto;
    overflow: hidden;
  }
  &-imgUrl {
    height: auto;
    pointer-events: none;
    width: 375px;
  }
  &-operate {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    max-height: 500px;
    overflow-y: auto;
    &-top {
      margin-bottom: 20px;
    }
    &-bottom {
      flex: 1;
      overflow-y: auto;
      background: #fff;
    }

    &-item {
      position: relative;
      padding: 10px;
      background: #efefef;
      margin-bottom: 10px;
      border: 1px solid transparent;
      &.active {
        border: 1px solid red;
      }
    }
    &-delete {
      position: absolute;
      padding: 10px;
      top: 0;
      right: 0;
    }
    &-title {
      font-weight: bold;
      margin-bottom: 10px;
    }
    &-box {
      background: #fff;
      position: relative;
      padding: 10px;
      border: 1px solid #d9d9d9;
      margin-bottom: 10px;
    }
    &-condition {
      position: relative;
      padding-left: 80px;
    }
  }
}
.move-block-wrap {
  position: absolute;
  cursor: move;
  border: 1px solid #24baab;
  background: rgba(36, 186, 171, 0.5);
  .scale-block {
    width: 0;
    height: 0;
    position: absolute;
    right: 1px;
    bottom: 1px;
    border-bottom: 5px solid white;
    border-left: 5px solid transparent;
    cursor: nw-resize;
  }
  &.active-move-block-wrap {
    border-color: #e75c45;
    background: rgba(231, 92, 69, 0.5);
  }
}
</style>
