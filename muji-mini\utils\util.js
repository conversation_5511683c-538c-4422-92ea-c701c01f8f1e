const formatTime = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return `${[year, month, day].map(formatNumber).join('/')} ${[hour, minute, second].map(formatNumber).join(':')}`
}

const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : `0${n}`
}

// 唯一字符串
export function uuid() {
  var s = [];
  var hexDigits = "0123456789abcdef";
  for (var i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }
  s[14] = "4"; // bits 12-15 of the time_hi_and_version field to 0010
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23] = "-";

  var uuid = s.join("");
  return uuid
}

const throttle = function (fn, interval) {
  var _self = fn; //保存需要被延长执行的函数引用
  var timer;
  return function () {
    var args = arguments;
    var _this = this;
    if (!timer) { //如果定时器还在，说明前一次延迟执行还没有完成
      _self.apply(_this, args);
      timer = setTimeout(function () { //延迟一段时间执行
        clearTimeout(timer);
        timer = null;
      }, interval || 2000);
    }
  };
};


const checkMessageSub = (data) => {
  delete data.errMsg;
  const values = Object.values(data);
  // 判断订阅结果
  if (values.length === 0) {
    // 如果对象为空，说明全部订阅失败
    console.log('全部订阅失败');
    // wx.$loading.showErrorMessage('全部订阅失败')
  } else if (values.every(value => value === 'accept')) {
    // 如果所有值都等于'accept'，说明全部订阅成功
    console.log('全部订阅成功');
    // wx.$loading.showErrorMessage('全部订阅成功')
  } else if (values.some(value => value === 'accept')) {
    // 如果存在一个值等于'accept'，说明部分订阅成功
    console.log('部分订阅成功');
    // wx.$loading.showErrorMessage('部分订阅成功')
  } else {
    // 如果所有值都不等于'accept'，说明全部订阅失败
    console.log('全部订阅失败');
    // wx.$loading.showErrorMessage('全部订阅失败')
  }

}


module.exports = {
  formatTime,
  throttle,
  uuid,
  checkMessageSub
}
