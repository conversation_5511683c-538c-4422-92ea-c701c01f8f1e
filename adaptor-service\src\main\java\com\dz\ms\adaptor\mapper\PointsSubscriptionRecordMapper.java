package com.dz.ms.adaptor.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.ms.adaptor.entity.ExpirePointsRecord;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 即将过期积分用户记录按月分表Mapper
 */
@Repository
public interface PointsSubscriptionRecordMapper extends BaseMapper<ExpirePointsRecord> {

    /**
     * 获取分表列表
     * @return
     */
    List<String> queryPointsSubscriptionTableList();

    /**
     * 创建分表
     * @return
     */
    void createPointsSubscriptionTable(@Param("nowDay")String nowDay);

    int insertBatchSomeColumn(@Param("list") List<ExpirePointsRecord> list,@Param("nowDay")String nowDay);

    IPage<ExpirePointsRecord> selectPageByParam(Page<ExpirePointsRecord> page,@Param("nowDay")String nowDay);

    IPage<ExpirePointsRecord> selectPageByParamMsg(Page<ExpirePointsRecord> page,@Param("nowDay")String nowDay);

    List<ExpirePointsRecord> selectPageByParamStatus(@Param("nowDay")String nowDay);

    List<ExpirePointsRecord> selectPageByParamMsgStatus(@Param("nowDay")String nowDay);

    void updateSubscriptionStatusById(@Param("nowDay")String nowDay,@Param("id")Long id);
    void updateSubscriptionStatusByIdFail(@Param("nowDay")String nowDay,@Param("id")Long id);

    void updateSendMyMsgStatusById(@Param("nowDay")String nowDay,@Param("id")Long id);

    void deleteBySevenDayAgo(@Param("nowDay")String nowDay, @Param("sevenDayAgo")String sevenDayAgo);
    
}
