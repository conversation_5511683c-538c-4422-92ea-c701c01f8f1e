import dayjs from 'dayjs'
import service from '@/utils/request.js'
import { PRODUCT_TYPE_OBJ, SHELF_EXTENSION_STATUS_OBJ, SHELF_EXTENSION_SWITCH_STATUS_OBJ } from '@/utils/constants.js'
import { v4 } from 'uuid'

const itemHandlerShelfProduct = (v) => {
  console.log("🚀 ~ itemHandlerShelfProduct ~ v:", v)
  v.uuid = v4()
  v.tagList = v.tagList || []
  v.superscriptIdList = v.superscriptIdList || []
  v.superscriptIdList = v?.superscriptIdList?.filter(v => v)?.map(v => +v)
  v.superscriptCampaign = v?.superscriptCampaignNameList?.join('、') || []
  if (typeof v.pdType == 'number') {
    v.pdTypeDesc = PRODUCT_TYPE_OBJ[v.pdType]
  }
}

export const apiCrowdPurchaseRestriction = {
  async getPageList(data) {
    return await service({ url: '/crm/product/shelf_campaign/list', method: 'get', data }).then(res => {
      res.data.list.forEach(v => {
        if (v.onType === 1) {
          v.onStartTime = ''
          v.onEndTime = '永久上架'
          v.promotionStateDesc = SHELF_EXTENSION_STATUS_OBJ[v.promotionState]
          v.stateDesc = SHELF_EXTENSION_SWITCH_STATUS_OBJ[v.state]
        }
      })
      return res
    })
  },
  async getPageDetail(data) {
    return await service({ url: '/crm/product/shelf_campaign/info', method: 'get', data }).then(res => {
      if (res.data.onType !== 1 && res.data.onStartTime && res.data.onEndTime) {
        res.data.dateTimeRange = [res.data.onStartTime, res.data.onEndTime]
      }
      try {
        res.data.content = JSON.parse(res.data.content || JSON.stringify({}))
      } catch (e) {
        res.data.content = {}
      }
      return res?.data || {}
    })
  },
  async createPage(data) {
    // data.onStartTime = data.dateTimeRange[0]
    // data.onEndTime = data.dateTimeRange[1]
    // data.content = JSON.stringify(data.content)
    return await service({ url: '/crm/product/shelf_campaign/add', method: 'post', data }).then(res => {
      return res
    })
  },
  async updatePage(data) {
    data.onStartTime = data?.dateTimeRange?.[0] || ''
    data.onEndTime = data?.dateTimeRange?.[1] || ''
    data.content = JSON.stringify(data.content)
    return await service({ url: '/crm/product/shelf_campaign/update', method: 'post', data }).then(res => {
      return res
    })
  },
  async updateState(data) {
    data.number = data.state
    return await service({ url: '/crm/product/shelf_campaign/update_state', method: 'post', data }).then(res => {
      return res
    })
  },
  async deletePage(data) {
    return await service({ url: '/crm/product/shelf_campaign/delete', method: 'post', data }).then(res => {
      return res
    })
  },
  async exportPage(data) {
  },

  // 根据营销活动ID查询营销活动规则列表
  async getShelfCampaignRuleListByCampaignId(data) {
    return await service({ url: '/crm/product/shelf_campaign_rule/list', method: 'get', data }).then(res => {
      return res?.data || []
    })
  },

  // 根据营销活动id查询规则上架商品
  async getShelfCampaignRuleProductListByCampaignId(data) {
    return await service({ url: '/crm/product/shelf_campaign_rule_product/list_by_campaignId', method: 'get', data }).then(res => {
      if (res?.data?.length) {
        res.data.forEach(v => itemHandlerShelfProduct(v))
      }
      return res?.data || []
    })
  },

  // 分页查询营销活动规则关联的货架商品
  async getShelfCampaignRuleProductList(data) {
    return await service({ url: '/crm/product/shelf_campaign_rule_product/list', method: 'get', data }).then(res => {
      return res
    })
  },

}
