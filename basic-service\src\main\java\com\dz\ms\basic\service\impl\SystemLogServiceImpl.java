package com.dz.ms.basic.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.core.annotation.CacheEvict;
import com.dz.common.core.annotation.Cacheable;
import com.dz.common.core.dto.basic.EmailSendDTO;
import com.dz.common.core.dto.basic.SystemLogDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.dto.wechat.qymsg.TextMsgDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.enums.WechatApiEnum;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.service.WechatRequestSevice;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.constants.CacheKeys;
import com.dz.ms.basic.dto.AlarmConfigDTO;
import com.dz.ms.basic.entity.AlarmConfig;
import com.dz.ms.basic.entity.SystemCompleteParamLog;
import com.dz.ms.basic.entity.SystemLog;
import com.dz.ms.basic.mapper.AlarmConfigMapper;
import com.dz.ms.basic.mapper.SystemCompleteParamLogMapper;
import com.dz.ms.basic.mapper.SystemLogMapper;
import com.dz.ms.basic.service.EmailSendService;
import com.dz.ms.basic.service.SystemLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.List;

/**
 * 系统日志
 * @author: Handy
 * @date:   2022/08/04 20:22
 */
@Slf4j
@Service
public class SystemLogServiceImpl extends ServiceImpl<SystemLogMapper,SystemLog> implements SystemLogService {

    @Resource
    private SystemLogMapper systemLogMapper;
    @Resource
    private SystemCompleteParamLogMapper systemCompleteParamLogMapper;
    @Resource
    private AlarmConfigMapper alarmConfigMapper;
    @Resource
    private SystemLogService systemLogService;
    @Resource
    private EmailSendService emailSendService;
    @Resource
    private WechatRequestSevice wechatRequestSevice;
    @Resource
    private RedisService redisService;
    @Resource
    private RestTemplate restTemplate;

    @Value("${notify.qywx.agentid:}")
    private String agentid;
    @Value("${notify.qywx.corpid:}")
    private String corpId;
    @Value("${notify.qywx.corpsecret:}")
    private String corpSecret;

    /**
     * 保存系统日志
     * @param param
     */
    @Override
    public void addSystemLog(SystemLogDTO param) {
        SystemLog systemLog = new SystemLog(null, param.getLogType(), param.getLogName(),param.getRequestUrl(), param.getStartTime(), param.getEndTime(), param.getParams(), param.getState(), param.getAlarmState(), param.getException(), param.getOperator(),param.getOperatorName(),param.getTenantId());
        String params = param.getParams();
        boolean b = false;
        if(null != params && params.length() > 1000){
            systemLog.setParams(params.substring(0,1000));
            b = true;
        }
        String exception = param.getException();
        if(null != exception && exception.length() > 500){
            systemLog.setException(exception.substring(0,500));
            b = true;
        }
        systemLogMapper.insert(systemLog);
        extracted(systemLog, params, b, exception);
    }

    private void extracted(SystemLog systemLog, String params, boolean b, String exception) {
        if(b){
            try {
                SystemCompleteParamLog log = new SystemCompleteParamLog();
                log.setSystemLogId(systemLog.getId());
                log.setParams(params);
                log.setException(exception);
                systemCompleteParamLogMapper.insert(log);
            } catch (Exception e){
                log.error("addSystemLog接口extracted报错:systemLogId:{},入参params:{},入参exception:{}",systemLog.getId(),params,exception);
            }
        }
    }

    /**
     * 发送告警通知
     */
    private void sendAlarmNotify(Integer logType, String logName, String url, String params, String message, Long tenantId) {
        SecurityContext.setUser(new CurrentUserDTO(tenantId));
        List<AlarmConfigDTO> list = systemLogService.getAlarmConfigList();
        if(CollectionUtils.isEmpty(list)) {
            return;
        }
        String content = LogType.getNameByType(logType) +":"+ logName + "失败，url:"+ url +"入参:" + params + "错误原因:" + message;
        for (AlarmConfigDTO config : list) {
            if(config.getState().equals(0)) {
                continue;
            }
            int type = config.getAlarmType();
            if(type == 1) {
                EmailSendDTO param = new EmailSendDTO();
                String[] receivers = config.getReceivers().split(",");
                param.setTo(receivers);
                param.setSubject(logName+"告警");
                param.setContent(content);
                emailSendService.sendSimpleMail(param);
            }
            else if(type == 2) {
                String key = CacheKeys.QYWX_ACCESSTOKEN+"system";
                String accesstoken = redisService.getString(key);
                if(null == accesstoken) {
                    JSONObject json = restTemplate.getForObject(WechatApiEnum.API_GET_WXCP_APPTOKEN_BY_SECRET.getUrl() + "?corpid="+corpId+"&corpsecret="+corpSecret, JSONObject.class);
                    log.info("获取企业微信access_token result:"+ (null == json ? "emp" : json.toJSONString()));
                    if(null != json && json.containsKey("access_token")) {
                        accesstoken = json.getString("access_token");
                        int expires = json.getIntValue("expires_in");
                        redisService.setString(key,accesstoken,expires-600);
                    }
                    else {
                        log.info("获取企业微信access_token失败corpid:{}corpsecret:{}",corpId,corpSecret);
                        continue;
                    }
                }
                TextMsgDTO msg = new TextMsgDTO();
                msg.setMsgtype("text");
                msg.setAgentid(agentid);
                msg.setTouser(config.getReceivers().replaceAll(",","|"));
                JSONObject text = new JSONObject();
                text.put("content",content);
                msg.setText(text);
                wechatRequestSevice.request(WechatApiEnum.API_SEND_WXCP_APP_MESSAGE,null,null,msg,accesstoken);
            }
        }
    }

    /**
     * 获取告警配置列表
     * @return
     */
    @Override
    @Cacheable(prefix = CacheKeys.ALARM_CONFIG_LIST,key = "'list'")
    public List<AlarmConfigDTO> getAlarmConfigList() {
        List<AlarmConfig> list = alarmConfigMapper.selectList(new LambdaQueryWrapper<>());
        return BeanCopierUtils.convertList(list,AlarmConfigDTO.class);
    }

    /**
     * 保存告警配置
     * @return
     */
    @Override
    @CacheEvict(prefix = CacheKeys.ALARM_CONFIG_LIST,key = "'list'")
    public Long saveAlarmConfig(AlarmConfigDTO param) {
        AlarmConfig config = new AlarmConfig(param.getId(),param.getAlarmType(),param.getReceivers(),param.getState());
        if(ParamUtils.isNullOr0Long(param.getId())) {
            alarmConfigMapper.insert(config);
        }
        else {
            alarmConfigMapper.updateById(config);
        }
        return config.getId();
    }

    /**
     * 删除告警配置
     * @return
     */
    @Override
    @CacheEvict(prefix = CacheKeys.ALARM_CONFIG_LIST,key = "'list'")
    public void deleteAlarmConfig(Long id) {
        alarmConfigMapper.deleteById(id);
    }

}
