package com.dz.ms.product.dto.req;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 货架营销活动入参
 *
 * @author: zlf
 * @date: 2024/11/27 11:39
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "货架营销活动入参")
public class ShelfCampaignParamDTO extends BaseDTO {

    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "活动开始时间")
    private Date onStartTime;
    @ApiModelProperty(value = "活动结束时间")
    private Date onEndTime;
    @ApiModelProperty(value = "活动状态 1待开始/2进行中/3已结束")
    private Integer campaignState;
    @ApiModelProperty(value = "启停状态 0禁用 1启用")
    private Integer state;

}
