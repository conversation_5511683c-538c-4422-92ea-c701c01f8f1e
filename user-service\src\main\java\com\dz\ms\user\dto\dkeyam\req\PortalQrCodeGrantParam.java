package com.dz.ms.user.dto.dkeyam.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 扫码授权 入参
 * @author: fei
 * @date: 2025/01/22 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PortalQrCodeGrantParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("管理员登录名")
    private String adminLoginName;

    @ApiModelProperty("管理员密码")
    private String adminPassword;

    @ApiModelProperty("会话id（从二维码中获取）")
    private String qrCodeSessionId;

    @ApiModelProperty("登录名（授权人）（需要在认证系统中实际存在）")
    private String grantorLoginName;

    @ApiModelProperty("登录名（被授权人）（任意填写）")
    private String granteeLoginName;

    @ApiModelProperty("授权小时（被授权人）（任意填写）")
    private Integer grantHours;

    @ApiModelProperty("角色（被授权人）（任意填写）")
    private String granteeRoleName;

    @ApiModelProperty("姓名（被授权人）（任意填写）")
    private String granteePersonalName;

    @ApiModelProperty("手机号（被授权人）（任意填写）")
    private String granteeMobile;

    @ApiModelProperty("事由（被授权人）（任意填写）")
    private String granteeSubject;

}
