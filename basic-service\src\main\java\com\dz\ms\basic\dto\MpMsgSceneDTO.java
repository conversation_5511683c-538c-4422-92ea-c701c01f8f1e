package com.dz.ms.basic.dto;
import java.util.Date;
import java.util.List;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.w3c.dom.stylesheets.LinkStyle;

/**
 * 小程序订阅消息场景DTO
 * @author: Handy
 * @date:   2023/07/07 14:22
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "小程序订阅消息场景")
public class MpMsgSceneDTO extends BaseDTO {

    @ApiModelProperty(value = "场景ID")
    private Long id;
    @ApiModelProperty(value = "场景编号")
    private String scene;
    @ApiModelProperty(value = "场景名称")
    private String sceneName;
    @ApiModelProperty(value = "状态 0关闭 1启用")
    private Integer state;
    @ApiModelProperty(value = "更新时间")
    private Date modified;

    @ApiModelProperty(value = "关联订阅消息模板列表")
    List<MpMsgSceneRelationDTO> relationList;

}
