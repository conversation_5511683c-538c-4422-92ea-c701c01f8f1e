package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 地区码表
 */
@Getter
@Setter
@NoArgsConstructor
@Table("地区码表")
@TableName(value = "district")
public class DistRict implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 上级ID
     */
    private Integer pid;
    /**
     * 地区级别
     */
    private Integer level;
    /**
     * 地区代码
     */
    private String code;
    /**
     * 地区名称
     */
    private String name;
    /**
     * 配置状态 0停用 1启用
     */
    private String status;

}
