package com.dz.ms.adaptor.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.constants.NumConstants;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.IdNumberDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.CommonUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.adaptor.constants.CacheKeys;
import com.dz.ms.adaptor.dto.ScheduledTaskConfigDTO;
import com.dz.ms.adaptor.entity.ScheduledTaskConfig;
import com.dz.ms.adaptor.mapper.ScheduledTaskConfigMapper;
import com.dz.ms.adaptor.service.ScheduledTaskConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * 定时任务配置表
 * @author: 
 * @date:   2025/03/11 15:01
 */
@Slf4j
@Service
public class ScheduledTaskConfigServiceImpl extends ServiceImpl<ScheduledTaskConfigMapper, ScheduledTaskConfig> implements ScheduledTaskConfigService {

	@Resource
    private ScheduledTaskConfigMapper scheduledTaskConfigMapper;
    @Resource
    private RedisService redisService;

	/**
     * 分页查询定时任务配置表
     * @param param
     * @return PageInfo<ScheduledTaskConfigDTO>
     */
    @Override
    public PageInfo<ScheduledTaskConfigDTO> getScheduledTaskConfigList(ScheduledTaskConfigDTO param) {
        ScheduledTaskConfig scheduledTaskConfig = BeanCopierUtils.convertObjectTrim(param,ScheduledTaskConfig.class);
        IPage<ScheduledTaskConfig> page = scheduledTaskConfigMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), new QueryWrapper<>(scheduledTaskConfig));
        return new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),BeanCopierUtils.convertList(page.getRecords(), ScheduledTaskConfigDTO.class));
    }

    /**
     * 根据ID查询定时任务配置表
     * @param id id
     * @param isThrow 是否抛出异常
     * @return ScheduledTaskConfigDTO
     */
    @Override
    public ScheduledTaskConfigDTO getScheduledTaskConfigById(Long id,boolean isThrow) {
        ScheduledTaskConfig scheduledTaskConfig = scheduledTaskConfigMapper.selectById(id);
        if(scheduledTaskConfig == null && isThrow){
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[定时任务配置]未查询到此定时任务配置");
        }
        return BeanCopierUtils.convertObject(scheduledTaskConfig,ScheduledTaskConfigDTO.class);
    }

    /**
     * 保存定时任务配置表
     * @param param
     * @return Long
     */
    @Override
    public Long saveScheduledTaskConfig(ScheduledTaskConfigDTO param) {
        if(StringUtils.isBlank(param.getTaskName()) || StringUtils.isBlank(param.getCronExpression()) || StringUtils.isBlank(param.getClassName()) || StringUtils.isBlank(param.getMethodName())){
            throw new BusinessException(ErrorCode.BAD_REQUEST);
        }
        CurrentUserDTO user = SecurityContext.getUser();
        Long uid = user.getUid();
        String lockKey = CacheKeys.Locks.SCHEDULED_TASK_CONFIG_SAVE + uid + ":" + param.getId();
        boolean lock = redisService.lock(lockKey, NumConstants.THIRTY);
        ScheduledTaskConfig scheduledTaskConfig = BeanCopierUtils.convertObject(param,ScheduledTaskConfig.class);
        if (lock) {
            try {
                Date date = new Date();
                if (ParamUtils.isNullOr0Long(scheduledTaskConfig.getId())) {
                    if(Objects.isNull(scheduledTaskConfig.getStatus())){
                        scheduledTaskConfig.setStatus(0);
                    }
                    if(Objects.isNull(scheduledTaskConfig.getCreatedAt())){
                        scheduledTaskConfig.setCreatedAt(date);
                    }
                    if(Objects.isNull(scheduledTaskConfig.getUpdatedAt())){
                        scheduledTaskConfig.setUpdatedAt(date);
                    }
                    scheduledTaskConfigMapper.insert(scheduledTaskConfig);
                } else {
                    this.getScheduledTaskConfigById(param.getId(), true);
                    scheduledTaskConfig.setUpdatedAt(date);
                    scheduledTaskConfigMapper.updateConfig(scheduledTaskConfig);
                }
            } catch (Exception e) {
                log.error("=================【定时任务配置 saveScheduledTaskConfig接口】,uid:{},Exception报错:{},入参:{}", uid, e.getMessage(), CommonUtils.jsonStr(param), e);
                throw new BusinessException(ErrorCode.INTERNAL_ERROR);
            } finally {
                redisService.unlock(lockKey);
            }
        } else {
            throw new BusinessException(ErrorCode.CONFLICT, "请求中，请稍后");
        }
        return scheduledTaskConfig.getId();
    }

    /**
     * 根据ID删除定时任务配置表
     * @param param
     */
    @Override
    public void deleteScheduledTaskConfigById(IdCodeDTO param) {
        this.getScheduledTaskConfigById(param.getId(),true);
        scheduledTaskConfigMapper.deleteById(param.getId());
    }
    
    /**
     * 根据ID修改启停状态
     * @param param ID NUMBER 通用DTO
     */
    @Override
    public void updateStateById(IdNumberDTO param) {
        this.getScheduledTaskConfigById(param.getId(),true);
        ParamUtils.checkStateParam(param.getNumber());
        ScheduledTaskConfig scheduledTaskConfig = new ScheduledTaskConfig();
        scheduledTaskConfig.setId(param.getId());
        scheduledTaskConfig.setStatus(param.getNumber());
        scheduledTaskConfigMapper.updateById(scheduledTaskConfig);
    }

}
