package com.dz.ms.order.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单发货信息
 *
 * @author: LiinNs
 * @date: 2024/12/20 10:54
 */
@Getter
@Setter
@NoArgsConstructor
@Table("订单发货信息")
@TableName(value = "order_send")
public class OrderSend implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 0, isNull = false, comment = "主键")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = true, comment = "订单编号")
    private String orderCode;
    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = true, comment = "快递单号")
    private String expressCode;
    @Columns(type = ColumnType.BIGINT, length = 0, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT, length = 0, isNull = true, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT, length = 0, isNull = true, comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    public OrderSend(Long id, String orderCode, String expressCode) {
        this.id = id;
        this.orderCode = orderCode;
        this.expressCode = expressCode;
    }

}
