package com.dz.ms.product.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 货架营销活动保存入参
 *
 * @author: LiinNs
 * @date: 2024/11/20 11:39
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "货架营销活动保存入参")
public class ShelfCampaignSaveParamDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "活动时间类型 1永久 2时间段")
    private Integer onType;
    @ApiModelProperty(value = "活动开始时间")
    private Date onStartTime;
    @ApiModelProperty(value = "活动结束时间")
    private Date onEndTime;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "规则数据列表")
    private List<ShelfCampaignRuleSaveParamDTO> ruleList;
    

}
