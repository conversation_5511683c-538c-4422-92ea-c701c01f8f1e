package com.dz.common.core.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * ID NUMBER 通用DTO
 * @author: Handy
 * @date:   2022/2/3 16:33
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
@ApiModel(value = "ID NUMBER 通用DTO")
public class IdNumberDTO {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "NUMBER")
    private Integer number;

    public IdNumberDTO(Long id, Integer number) {
        this.id = id;
        this.number = number;
    }

}
