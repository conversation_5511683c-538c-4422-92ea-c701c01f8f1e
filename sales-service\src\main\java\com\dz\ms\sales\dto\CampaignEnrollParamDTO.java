package com.dz.ms.sales.dto;


import com.dz.common.core.annotation.Columns;
import com.dz.common.core.enums.ColumnType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/19
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(description = "敏感肌测试名单新增DTO")
public class CampaignEnrollParamDTO {


    @ApiModelProperty(value = "活动标识", example = "CAMPAIGN_CODE", required = true)
    @NotBlank(message = "活动标识不能为空")
    private String campaignCode;

//    @NotNull(message = "来源不能为空")
//    @ApiModelProperty(value = "来源 0自然流量 1公众号 2企微 3线下门店", example = "5", allowableValues = "0, 1, 2, 3", required = true)
//    private Integer source;

    @ApiModelProperty(value = "一级渠道")
    private String channelOne;
    @ApiModelProperty(value = "二级渠道")
    private String channelTwo;

    @NotBlank(message = "姓名不能为空")
    @Size(max = 50, message = "姓名长度不能超过50个字符")
    @ApiModelProperty(value = "姓名", example = "张三", required = true)
    private String name;

    @NotBlank(message = "昵称不能为空")
    @Size(max = 50, message = "昵称长度不能超过50个字符")
    @ApiModelProperty(value = "昵称", example = "张三", required = true)
    private String username;

    @NotBlank(message = "手机号码不能为空")
    @Size(max = 11, message = "手机号码长度不能超过11个字符")
    @ApiModelProperty(value = "手机号码", example = "13800138000", required = true)
    private String enrollPhone;

    @NotNull(message = "皮肤类型不能为空")
    @ApiModelProperty(value = "皮肤类型 0未知 1皮肤敏感（泛红、刺痛） 2干燥缺水 3、痘痘/粉刺问题 4、油脂分泌过多 5、黑头问题", example = "5", allowableValues = "0, 1, 2, 3, 4, 5", required = true)
    private Integer skinType;

    @NotBlank(message = "省不能为空")
    @Size(max = 50, message = "省长度不能超过50个字符")
    @ApiModelProperty(value = "省", example = "广东省", required = true)
    private String province;

    @NotBlank(message = "市不能为空")
    @Size(max = 50, message = "市长度不能超过50个字符")
    @ApiModelProperty(value = "市", example = "广州市", required = true)
    private String city;

    @NotBlank(message = "区不能为空")
    @Size(max = 50, message = "区长度不能超过50个字符")
    @ApiModelProperty(value = "区", example = "天河区", required = true)
    private String district;

    @NotBlank(message = "详细地址不能为空")
    @Size(max = 50, message = "详细地址长度不能超过50个字符")
    @ApiModelProperty(value = "详细地址", example = "天河区天河路1号", required = true)
    private String address;


}