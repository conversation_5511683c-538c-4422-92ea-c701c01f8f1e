package com.dz.ms.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.user.entity.AppointmentConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface AppointmentConfigMapper extends BaseMapper<AppointmentConfig> {
    @Select("SELECT * FROM appointment_config WHERE id = #{id}")
    AppointmentConfig findById(Long id);
}