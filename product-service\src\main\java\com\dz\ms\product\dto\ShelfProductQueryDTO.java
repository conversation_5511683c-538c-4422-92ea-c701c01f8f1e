package com.dz.ms.product.dto;

import com.dz.common.core.constants.NumConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;


/**
 * 货架商品查询条件DTO
 *
 * @author: fei
 * @date: 2024/12/11 16:25
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@ApiModel(value = "货架商品查询条件DTO")
public class ShelfProductQueryDTO {
    
    @ApiModelProperty(value = "货架ID列表")
    private List<Long> shelfIds;
    @ApiModelProperty(value = "商品ID列表")
    private List<Long> productIds;
    @ApiModelProperty(value = "货架商品id列表")
    private List<Long> shelfProductIds;
    @ApiModelProperty(value = "1:查询少量字段, 2:查询所有字段")
    private Integer num;
    @ApiModelProperty(value = "是否查询角标")
    private Boolean isQrySuperscript;
    @ApiModelProperty(value = "是否查询角标")
    private Boolean isQryImg;
    @ApiModelProperty(value = "是否查询标签")
    private Boolean isQryTag;
    

}
