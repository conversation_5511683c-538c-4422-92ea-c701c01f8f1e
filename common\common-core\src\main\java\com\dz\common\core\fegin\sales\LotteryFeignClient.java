package com.dz.common.core.fegin.sales;


import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/2/7
 */
@FeignClient(name = ServiceConstant.SALES_SERVICE_NAME, contextId = "LotteryFeignClient")
public interface LotteryFeignClient {
    /**
     * 刷新抽奖库存
     *
     * @param jobParams
     * @return
     */
    @PostMapping("/remote/lottery/inventory")
    public Result<Void> refreshLotteryInventory(@RequestParam("jobParams") String jobParams);
}
