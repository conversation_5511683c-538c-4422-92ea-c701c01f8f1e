package com.dz.ms.basic.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.basic.AliCheckRequestDTO;
import com.dz.common.core.fegin.basic.AliCheckFeignClient;
import com.dz.ms.basic.service.AliCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * @Author: txt
 * @Date: 2024-06-25 11:27
 * @Version: 1.0
 */
@Slf4j
@Api(tags = "阿里审核")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class AliCheckController implements AliCheckFeignClient {

    @Resource
    private AliCheckService aliCheckService;


    @ApiOperation("文字内容审核")
    @PostMapping(value = "/app/check_text")
    public Result<Object> textScanRequest1(@RequestBody AliCheckRequestDTO param) {
        Result<Object> result = new Result<>();
        boolean flag = aliCheckService.textScanRequest(param.getContent(), param.getModelType(), param.getModelId(), false);
        if (flag) {
            return result.errorResult(ErrorCode.BAD_REQUEST, "该文案包含敏感词汇，请重新输入");
        }
        return result;
    }


    @ApiOperation("图片内容审核")
    @PostMapping(value = "/app/images_check")
    public Result<Object> imageSyncScanRequest1(@RequestBody AliCheckRequestDTO param) {
        Result<Object> result = new Result<>();
//        Map<String, Integer> stringIntegerMap = aliCheckService.imageSyncScanRequest(param.getUrlList(), param.getModelType(), param.getModelId(), false);
//        if (stringIntegerMap.containsValue(1)) {
//            return result.errorResult(ErrorCode.BAD_REQUEST, "该图片不合规，请重新上传");
//        }
        return result;
    }

    @ApiOperation("文字内容审核")
    @PostMapping(value = "/aliyun/check_text")
    public Result<Boolean> textScanRequest(@RequestBody AliCheckRequestDTO param) {
        Result<Boolean> result = new Result<>();
        Boolean page = aliCheckService.textScanRequest(param.getContent(), param.getModelType(), param.getModelId(), true);
        result.setData(page);
        return result;
    }


    @ApiOperation("图片内容审核")
    @PostMapping(value = "/aliyun/images_check")
    public Result<Map<String, Integer>> imageSyncScanRequest(@RequestBody AliCheckRequestDTO param) {
        Result<Map<String, Integer>> result = new Result<>();
        Map<String, Integer> stringIntegerMap = aliCheckService.imageSyncScanRequest(param.getUrlList(), param.getModelType(), param.getModelId(), true);
//        Map<String, Integer> stringIntegerMap = new HashMap<>();
//        stringIntegerMap.put("ni", 0);
        result.setData(stringIntegerMap);
        return result;
    }


}
