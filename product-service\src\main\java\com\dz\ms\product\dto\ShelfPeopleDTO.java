package com.dz.ms.product.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 货架人群包条件DTO
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:27
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "货架人群包条件")
public class ShelfPeopleDTO extends BaseDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "货架名称")
    private String shelfName;
    @ApiModelProperty(value = "人群包ID")
    private Long groupId;
    @ApiModelProperty(value = "人群包名称")
    private String groupName;
    @ApiModelProperty(value = "限制条件 1等级 2性别")
    private Integer conditionType;
    @ApiModelProperty(value = "条件关系 1且 2或")
    private Integer conditionLogic;
    @ApiModelProperty(value = "条件文字描述 etc. 普通会员 男")
    private String conditionStr;
    @ApiModelProperty(value = "货架状态 0禁用 1启用")
    private Integer state;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "修改时间")
    private Date modified;
    @ApiModelProperty(value = "修改人")
    private Long modifier;

}
