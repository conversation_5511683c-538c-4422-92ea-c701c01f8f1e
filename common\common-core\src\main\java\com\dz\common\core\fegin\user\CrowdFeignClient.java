package com.dz.common.core.fegin.user;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.user.CrowdDTO;
import com.dz.common.core.dto.user.StoreDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 人群包FeignClient
 * @Author: fei
 * @Date: 2024/11/26 23:11
 */
@FeignClient(name = ServiceConstant.USER_SERVICE_NAME, contextId = "CrowdFeignClient")
public interface CrowdFeignClient {

    /**
     * 根据ID查询人群包
     * @return Result<CrowdDTO>
     */
    @GetMapping(value = "/crowd/info")
    Result<CrowdDTO> getCrowdById(@RequestParam("id") Long id);
    /**
     * 保存人群包
     * @return Result<Long>
     */
    @PostMapping(value = "/crowd/save")
    Result<Long> saveCrowd(@RequestBody CrowdDTO param);
    /**
     * 编辑人群包
     * @return Result<Boolean>
     */
    @PostMapping(value = "/crowd/update")
    Result<Boolean> updateCrowd(@RequestBody CrowdDTO param);

    /**
     * 无分页列表
     * @return Result<List<CrowdDTO>>
     */
    @GetMapping(value = "/crowd/noPageList")
    Result<List<CrowdDTO>> getList(@ModelAttribute CrowdDTO param);

    /**
     * 查询用户适用人群包
     * @return Result<List<Long>>
     */
    @GetMapping(value = "/crowd/check")
    Result<List<Long>> userHaveCrowd();
    /**
     * 查询人群包是否存在
     * @return Result<Boolean>
     */
    @GetMapping(value = "/crowd/isHave")
    Result<Boolean> crowdIsHave(@RequestParam Long id);
    /**
     * 查询所有门店的经纬度
     * @return result<PageInfo<StoreDTO>>
     */
    @PostMapping(value = "/store/longitude/latitude/list")
    Result<List<StoreDTO>> getLongitudeLatitude();
}

