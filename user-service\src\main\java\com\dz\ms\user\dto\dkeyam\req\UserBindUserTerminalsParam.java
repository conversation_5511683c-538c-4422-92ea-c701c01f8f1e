package com.dz.ms.user.dto.dkeyam.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 绑定用户终端入参
 * @author: fei
 * @date: 2025/01/22 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserBindUserTerminalsParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("管理员登录名")
    private String adminLoginName;

    @ApiModelProperty("管理员密码")
    private String adminPassword;

    @ApiModelProperty("站点名称")
    private String tenantName;

    @ApiModelProperty("用户源名称 本地用户源：06bc758e-04f2-40b3-9d6c-562b115aeb3c 外部用户源：和创建的外部用户源名称一致")
    private String identityStoreName;

    @ApiModelProperty("用户登录名")
    private String loginName;
    
    @ApiModelProperty("是否解绑用户以前的终端默认为true，绑定前先解绑以前的用户终端")
    private String unbindPrevious;
    
    @ApiModelProperty("绑定的MAC地址列表 示例：[\"D0:25:98:35:C3:62\", \"10:45:F8:35:CC:E1\"]")
    private String macAddresses;

}
