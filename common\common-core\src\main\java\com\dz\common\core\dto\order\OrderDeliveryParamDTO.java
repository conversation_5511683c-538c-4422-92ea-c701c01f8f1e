package com.dz.common.core.dto.order;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ColumnWidth(19)
public class OrderDeliveryParamDTO {

    @NotNull(message = "快递单号不能为空")
    @ApiModelProperty(value = "订单编号")
    @ExcelProperty("orderCode")
    private String orderCode;
    @NotNull(message = "快递单号不能为空")
    @ApiModelProperty(value = "快递编号")
    @ExcelProperty("expressCode")
    private String expressCode;
}
