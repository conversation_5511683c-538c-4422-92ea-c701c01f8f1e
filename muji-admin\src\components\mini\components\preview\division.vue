<template>
  <div class="division" :style="{width:width/2+'px',height:(data.height+data.paddingTop+data.paddingBottom)/2+'px'}">
    <customBg :bgSetting="data" class="bgStyle"></customBg>
    <div class="division-content" :style="{marginLeft:data.paddingLeft/2+'px',marginRight:data.paddingRight/2+'px',margintop:data.paddingTop/2+'px',marginBottom:data.paddingBottom/2+'px',borderBottom:(data.height/2+'px '+ data.lineType+' '+ data.color)}">
    </div>
  </div>

</template>
<script setup>
const emit = defineEmits(['ok', 'cancel', 'changeActive'])
import { cloneDeep } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { computed } from 'vue';
import customBg from './customBg.vue'
const props = defineProps({
  width: {
    type: Number,
    default: 750,
  },
  data: {
    type: Object,
    default() {
      return {}
    }
  },
})


</script>

<style scoped lang="scss">
.division {
  position: relative;

  &-content {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
  }
}

.bgStyle {
  width: 100%;
  height: 100%;
}
</style>
