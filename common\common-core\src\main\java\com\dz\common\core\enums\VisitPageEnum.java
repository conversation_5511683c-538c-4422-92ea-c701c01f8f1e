package com.dz.common.core.enums;

import java.util.Objects;

/**
 * Describe：用户访问页面CODE枚举,pageCode取自下面文档中路径的最后一级
 * <a href="https://hymblpgz7o.feishu.cn/sheets/TJmrsNeVNhuMzqtVjO1cWI05nhg?sheet=emjaFf"></a>
 * Created by 吴蜀黍 on 2023/8/20 8:36
 **/
public enum VisitPageEnum {
    LOTTERY("turntable-page", "大转盘"),
    LOTTERY_CAMPAIGN("campaign-24cny", "抽签campaign"),
    LOTTERY_GRAFFITI("campaign-prizes", "涂鸦抽奖"),
    COURSE("course", "大师课堂"),
    ;

    private final String code;
    private final String value;

    VisitPageEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByCode(String code) {
        if (code == null) {
            return null;
        }
        for (VisitPageEnum resultEnum : VisitPageEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum.value;
            }
        }
        return null;
    }
}
