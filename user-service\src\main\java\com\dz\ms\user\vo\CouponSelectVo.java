package com.dz.ms.user.vo;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 礼券查询入参
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CouponSelectVo extends BaseDTO
{
    @ApiModelProperty(value = "券状态（1未使用（包含已领取和未领取），2已使用，3已过期）")
    private Integer couponStatus;
}
