package com.dz.ms.user.dto.dkeyam.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.io.Serializable;

/**
 * 拉取所有用户的信息入参
 * @author: fei
 * @date: 2025/01/22 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserInfosParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("管理员登录名")
    private String adminLoginName;

    @ApiModelProperty("管理员密码")
    private String adminPassword;

    @ApiModelProperty("站点名称")
    private String tenantName;

    @ApiModelProperty("用户源名称 本地用户源：06bc758e-04f2-40b3-9d6c-562b115aeb3c 外部用户源：和创建的外部用户源名称一致")
    private String identityStoreName;

    @ApiModelProperty("查找方式 all：所有 updateTime：用户更新时间")
    private String type;

    @ApiModelProperty("返回的第一条数据，在数据库中的位置，int类型，最小值0")
    private Integer firstResult;

    @ApiModelProperty("返回的最大条数，int类型")
    private Integer maxResult;

    @ApiModelProperty("用户更新时间，起始时间，时间戳毫秒")
    private Long startUpdateTime;

    @ApiModelProperty("用户更新时间，结束时间，时间戳毫秒")
    private Long endUpdateTime;
}
