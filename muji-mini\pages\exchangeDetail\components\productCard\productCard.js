const app = getApp()
import {
  productTagArray
} from '../../../../utils/contants.js'


Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    skuInfo: {
      type: Object,
      value: {},
      observer(val) {
        this.handleVal(val)
      }
    },
  },
  onLoad(options) {},
  onShow() {},
  /**
   * 组件的初始数据
   */
  data: {
    tagData: undefined
  },

  /**
   * 组件的方法列表
   */
  methods: {
    onTapUse() {
      wx.$mp.track({
        event: 'exchange_detail_use',
      })
      this.triggerEvent('tap-use', this.properties.skuInfo)
    },
    handleVal(val) {
      const {
        superscript
      } = val;
      if (superscript && superscript.length > 0) {
        const v = productTagArray.find(item => item.value === superscript[0]);
        this.setData({
          tagData: v
        })
      }
    }
  }
})
