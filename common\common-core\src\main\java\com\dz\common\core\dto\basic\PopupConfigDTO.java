package com.dz.common.core.dto.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 弹窗配置DTO
 * @author: Handy
 * @date:   2023/11/20 20:40
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "弹窗配置")
public class PopupConfigDTO {

    @ApiModelProperty(value = "弹窗配置ID")
    private Long id;
    @ApiModelProperty(value = "弹窗类型 1预约黑名单")
    private Integer popupType;
    @ApiModelProperty(value = "弹窗图片")
    private String popupImg;
    @ApiModelProperty(value = "跳转链接配置JSON")
    private String jumpLink;

}
