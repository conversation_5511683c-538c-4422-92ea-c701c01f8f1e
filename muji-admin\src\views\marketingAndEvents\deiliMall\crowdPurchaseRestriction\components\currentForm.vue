<template>
  <a-drawer :title="title" width="1200" placement="right" :closable="true" :maskClosable="false" :open="thisFields.open" @close="onClose">
    <a-spin :spinning="thisFields.loading">
      <a-form class="form" ref="formRef" :model="formFields" :rules="formRules" :labelCol="{ style: 'width:160px' }">
        <div class="form-top-titles-common">货架活动信息</div>
        <div class="form-top-line-common"></div>
        <a-form-item label="活动名称" name="name">
          <a-input placeholder="请输入" style="width:300px;" v-model:value="formFields.name" show-count :maxlength="20" />
        </a-form-item>
        <a-form-item label="活动时间" name="onType">
          <a-radio-group v-model:value="formFields.onType" style="margin-bottom: 16px">
            <a-radio-button :value="2">时间段</a-radio-button>
            <a-radio-button :value="1">永久有效</a-radio-button>
          </a-radio-group>
          <template v-if="formFields.onType === 2">
            <BaseDateTimeRange label="" name="dateTimeRange" v-model="formFields.dateTimeRange" />
          </template>
        </a-form-item>
        <div class="form-top-titles-common">活动货架选择</div>
        <div class="form-top-line-common"></div>
        <BaseBelongingShelfSelect :disabled="!!formFields.id" label="活动货架" name="shelfId" v-model="formFields.shelfId" @change="onShelfIdChange" />
        <div class="form-top-titles-common">限购设置</div>
        <div class="form-top-line-common"></div>

        <div v-for="(item, index) in formFields.ruleList" class="form-rule-section" :key="index">
          <a-form-item :label="`限购规则${index+1}`" class="item-bottom" style=" margin-bottom: 0px;">
          </a-form-item>
          <a-form-item label=" " :colon="false" class="form-item-flex">
            <!-- <a-input :name="`ruleList${index}.name`" :rules="[{ required: true, message: '本项必填' }]" placeholder="请输入" style="width:300px;" v-model:value="item.name" show-count :maxlength="4" /> -->

            <div>
              <a-button type="link" @click="thisMethods.handleAddGroup(index)" :disabled="formFields.ruleList.length >= thisFields.conditionGroupMax">+ 新增限购规则</a-button>
              <a-button type="link" @click="thisMethods.handleDelGroup(index)" :disabled="formFields.ruleList.length <= thisFields.conditionGroupMin">- 删除该规则</a-button>

            </div>
          </a-form-item>

          <ShelfCrowdConditionsFormItem v-if="thisFields.open" :timeType="1" :conditionsRuleNamePrefix="['ruleList', index]" :formFields="item" :formRules="formRules['ruleList', index,'crowdDTO']" :formType="type" :formRef="formRef" />

          <a-form-item label="限购时间设置" name="ruleType">
            <a-radio-group v-model:value="item.ruleType" style="margin-bottom: 16px">
              <a-radio-button :value="1">活动时间内</a-radio-button>
              <a-radio-button :value="2">周期</a-radio-button>
            </a-radio-group>
            <template v-if="item.ruleType === 1">
              <div v-if="formFields.onType === 1">
                <a-input disabled value="永久有效" style="width: 160px;" />
              </div>
              <BaseDateTimeRange class="base-date-time-range-inline" v-else-if="formFields.onType === 2" disabled label="" name="dateTimeRange" v-model="formFields.dateTimeRange" />
            </template>
            <a-form-item v-if="item.ruleType === 2" class="hide-required-mark" :hideRequiredMark="false" label=" " :colon="false" :name="['ruleList',index,'cycleCreated']" :rules="[{ required: true, message: '本项必填' }]">
              <a-date-picker :getPopupContainer="(triggerNode) => triggerNode.parentNode" valueFormat="YYYY-MM-DD HH:mm:ss" style="width: 300px" format="YYYY-MM-DD HH:mm:ss" :show-time="{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }" placeholder="请选择周期开始时间" v-model:value="item.cycleCreated" />
            </a-form-item>
            <template v-if="item.ruleType === 2">
              <div style="display: flex;align-items: center;">
                <span class="ui-c-grey mr10">每</span>
                <a-input-number placeholder="请输入" style="width: 66px;" v-model:value="item.period" :min="1" />
                <span class="ui-c-grey ml10">天</span>
              </div>
            </template>
          </a-form-item>

          <a-form-item label="限购数量设置" name="ruleNum">
            <div style="display: flex;align-items: center;">
              <span class="ui-c-grey mr10">每用户可购买</span>
              <a-input-number placeholder="请输入" style="width: 66px;" v-model:value="item.ruleNum" :min="1" />
              <span class="ui-c-grey ml10">件</span>
            </div>
          </a-form-item>

          <a-form-item label="限购规则商品设置">
            <div class="ui-shelf-add-product-and-management-product">
              <div class="_left">
                目前参与活动商品 {{ item.ruleProductList?.length }} 件
              </div>
              <ShelfAddProductAndManagementProduct v-if="thisFields.open" bizType="crowdPurchaseRestriction" :disabled="!formFields.shelfId" :shelfId="formFields.shelfId" :formFields="item" :allSelectedProductList="formFields.allSelectedProductList" productListKey="ruleProductList" @addOk="e => ShelfAddProductAndManagementProductHandlerOk(e, index)" @managementOk="e => ShelfAddProductAndManagementProductHandlerOk(e, index)" />
            </div>

            <div class="global-tip">
              活动商品库存，默认为该商品货架的目前库存，如有需要可在“管理商品”中做逐个调整
            </div>
          </a-form-item>

          <a-form-item label="非人群包用户提示">
            <jumpModal :data="item.content" @ok="data => contentChange(data, index)">
              <a-button>点击配置弹窗</a-button>
            </jumpModal>
          </a-form-item>

        </div>

      </a-form>
    </a-spin>
    <template #footer v-if="!disabled">
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="ok" :loading="thisFields.loading">确定</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>
<script setup>
import CrowdPurchaseRestrictionAssociationShelfSelect from '@/views/marketingAndEvents/deiliMall/crowdPurchaseRestriction/components/CrowdPurchaseRestrictionAssociationShelfSelect.vue'
import { cloneDeep } from 'lodash'
import { useGlobalStore } from '@/store'
import { message } from 'ant-design-vue'
import { apiCrowdPurchaseRestriction } from '@/http/index.js'
import { nextTick, reactive } from 'vue'
import ShelfAddProductAndManagementProduct from '@/views/mall/shelfManagement/giftRack/components/ShelfAddProductAndManagementProduct.vue'
import ShelfCrowdConditionsFormItem
  from '@/views/mall/shelfManagement/giftRack/components/ShelfCrowdConditionsFormItem.vue'
import dayjs from 'dayjs'
const global = useGlobalStore()
const formRef = ref(null)
const emits = defineEmits(['cancel', 'ok', 'update:visible'])
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  id: {
    type: [String, Number],
    default: ''
  },
  type: {
    type: [String, Number],
    default: 0 // 0-新增 1-编辑 2-查看 3-复制
  }
})

const ruleListItemGet = () => {
  return {
    period: '1',
    name: '',
    cycleCreated: null,
    ruleNum: 1,
    ruleType: 1, // 1活动时间内 2周期
    ruleProductList: [],
    content: { // 规则组件配置
      imgUrl: '',
      imgLinks: []
    },
    crowdDTO: {
      timeType: '',
      crowdType: 0,
      crowdConditionList: [
        {
          conditionJudge: 0,
          crowdConditionRowList: [
            {
              conditionJudge: 0,
              conditions: []
            },
            {
              conditionJudge: 1,
              conditions: []
            }
          ]
        },
        {
          conditionJudge: 1,
          crowdConditionRowList: [
            {
              conditionJudge: 0,
              conditions: []
            },
            {
              conditionJudge: 1,
              conditions: []
            }
          ]
        }
      ],
      crowdName: '',
      crowdId: null,
      crowdImportResultDTO: {
        memberCodeList: [],
        fileName: ''
      }
    }

  }
}

const disabled = computed(() => {
  return +props.type === 2
})
const title = computed(() => {
  return ['新增', '编辑', '查看', '复制'][props.type] + '活动'
})
const getDefaultFormFields = () => ({
  name: '', // 名称
  onType: 2, // 1永久 2时间段
  dateTimeRange: [],
  onStartTime: '',
  onEndTime: '',
  shelfId: null, // 货架ID
  allSelectedProductList: [], // 所有已选中的商品（人群限购跨组件）
  ruleList: [{
    period: '1',
    name: '',
    ruleNum: 1,
    cycleCreated: null,
    ruleType: 1, // 1活动时间内 2周期
    ruleProductList: [],
    content: { // 规则组件配置
      imgUrl: '',
      imgLinks: []
    },
  }]
})
const formFields = reactive(getDefaultFormFields())
const formRules = reactive({
  onType: [{ required: true, message: '本项必选' }],
  dateTimeRange: [{ required: true, message: '本项必选' }],
  shelfId: [{ required: true, message: '本项必选' }],
  name: [{ required: true, message: '本项必填' }],
  ruleName: [{ required: true, message: '本项必填' }]
})

function contentChange(data, index) {
  formFields.ruleList[index].content.imgUrl = data.imgUrl
  formFields.ruleList[index].content.imgLinks = data.imgLinks
  console.log("🚀 ~ contentChange ~  formFields.content:", formFields.content)
  nextTick(() => {
    // formRef.value.validateFields(['content', 'imgUrl'])
    // formRef.value.clearValidate(['content', 'imgUrl'])
  })
}

const ShelfAddProductAndManagementProductHandlerOk = (beSelectedProductIdArrayObjectList, index) => {
  if (beSelectedProductIdArrayObjectList) formFields.ruleList[index].ruleProductList = beSelectedProductIdArrayObjectList
  // 更新allSelectedProductList
  formFields.allSelectedProductList = formFields.ruleList.map(v => {
    return v.ruleProductList
  }).flat() || []
}
const thisFields = reactive({
  conditionGroupMin: 1,
  conditionGroupMax: 5, // todo 待确认
  open: props.visible,
  loading: false
})
const thisMethods = {
  handleAddGroup(index) {
    // const formFields = props.formFields
    const newItem = ruleListItemGet()
    if (index > -1) newItem.conditionJudge = 1 //或
    formFields.ruleList.splice(index + 1, 0, newItem)
    formRef.value?.validateFields('ruleList')
  },
  handleDelGroup(index) {
    // const formFields = props.formFields
    formFields.ruleList.splice(index, 1)
    // if (formFields.crowdConditionList[0]) formFields.crowdConditionList[0].conditionJudge = ''
    formRef.value?.validateFields('ruleList')
  }
}

watch(() => props.visible, () => {
  thisFields.open = props.visible
  Object.keys(formFields).forEach(key => (delete formFields[key]))
  Object.assign(formFields, getDefaultFormFields())
  formRef.value?.clearValidate()
  if (thisFields.open) {
    initData()
  }
})
const onShelfIdChange = () => {
  formFields.ruleList.forEach(item => {
    item.ruleProductList = []
  })
}

const initData = async () => {
  if (!props.id) return
  thisFields.loading = true

  // 拼接详情数据
  const pBasic = apiCrowdPurchaseRestriction.getPageDetail({ id: props.id })
  const pBasicRules = apiCrowdPurchaseRestriction.getShelfCampaignRuleListByCampaignId({ campaignId: props.id })
  const pBasicRulesProduct = await apiCrowdPurchaseRestriction.getShelfCampaignRuleProductListByCampaignId({ campaignId: props.id })

  Promise.all([pBasic, pBasicRules, pBasicRulesProduct])
    .then((results) => {
      thisFields.loading = false

      let basic = results[0]
      const basicRules = results[1]
      const basicRulesProduct = results[2]

      // 编辑时需要 shelfProductName
      if (basicRulesProduct?.length) {
        basicRulesProduct.forEach((item, index) => {
          basicRulesProduct[index].shelfProductName = item.productName || ''
        })
      }

      if (basicRules?.length) {
        basicRules.forEach((item, index) => {
          // console.log("🚀 ~ basicRules.forEach ~ item:", item)
          // content 赋值
          if (!item.content) {
            basicRules[index].content = {
              imgUrl: '',
              imgLinks: []
            }
          } else {
            basicRules[index].content = JSON.parse(item.content)
          }
          // console.log(basicRules[index]?.crowdDTO, 'basicRules[index]?.crowdDTO');
          // 编辑时需要赋值 crowdId，后端没有返回


          // basicRules[index].crowdDTO.isUpdate = 0
          // basicRules[index].crowdDTO.crowdType = 1
          if (!basicRules[index].crowdDTO) {
            basicRules[index].crowdDTO = {
              crowdType: 0,
              timeType: '',
              crowdConditionList: [
                {
                  conditionJudge: 0,
                  crowdConditionRowList: [
                    {
                      conditionJudge: 0,
                      conditions: []
                    },
                    {
                      conditionJudge: 1,
                      conditions: []
                    }
                  ]
                },
                {
                  conditionJudge: 1,
                  crowdConditionRowList: [
                    {
                      conditionJudge: 0,
                      conditions: []
                    },
                    {
                      conditionJudge: 1,
                      conditions: []
                    }
                  ]
                }
              ],
              crowdName: '',
              crowdId: null,
              crowdImportResultDTO: {
                memberCodeList: [],
                fileName: ''
              }
            }

            if (basicRules[index].groupId > 0) {
              basicRules[index].crowdDTO.crowdType = 1
              basicRules[index].crowdDTO.crowdId = basicRules[index].groupId
              // console.log(basicRules[index].crowdDTO.crowdId);

              // console.log(basicRules[index].groupId > 0, basicRules[index].groupId, 'basicRules[index].groupId > 0');
            }
          }
          // 拼接
          basicRules[index].ruleProductList = basicRulesProduct.filter(v => v?.ruleId === item?.id)
          // basicRules[index].ruleProductList = cloneDeep(basicRulesProduct.filter(v => v?.ruleId === item?.id) || [])
        })
      }

      // Object.keys(formFields).forEach(key => (delete formFields[key]))
      nextTick(() => {
        Object.assign(formFields, {
          ...basic,
          ruleList: basicRules,
          allSelectedProductList: basicRulesProduct
        })
        if (props.type === 3) {
          formFields.id = undefined
          formFields.name += '（复制）'
        }

      })

      console.log("!!! formFields: ", formFields)

    })
    .catch((error) => {
      console.error('某个 Promise 失败:', error);
    });
}

const onClose = () => {
  emits('cancel')
  emits('update:visible', false)
}
const ok = () => {
  // console.log('ok：', formFields)
  // await formRef.value.validate() async
  formRef.value.validate().then(async (valid) => {
    let params = cloneDeep(formFields)
    let dateTimeRange = params.dateTimeRange || []
    if (dateTimeRange[0] && dateTimeRange[1]) {
      params.onStartTime = dateTimeRange[0]
      params.onEndTime = dateTimeRange[1]
    }
    // delete params.dateTimeRange
    if (params?.ruleList?.length) {
      params.ruleList.forEach((item, index) => {
        console.log(item, 'itemitemitemitemitem');

        if (item.content) {
          params.ruleList[index].content = JSON.stringify(item.content)
        }
        if (item.ruleProductList?.length) {
          item.ruleProductList.forEach(itemR => {
            itemR.shelfProductName = itemR.productName || ''
            console.log(itemR, 'ruleProductList');

          })
        }
      })
    }

    thisFields.loading = true
    const res = await apiCrowdPurchaseRestriction[props.id ? 'updatePage' : 'createPage'](params).finally(() => thisFields.loading = false)
    message.success(res.msg)
    emits('ok', props.id)
  })

}
</script>

<style scoped lang="scss">
:deep(.searchForm .ant-form-item) {
  margin-bottom: 16px;
}

.form-rule-section {
  padding-top: 24px;
  border: 1px dashed #e9e9e9;
  border-radius: 4px;
  background-color: #fafafa;

  & + .form-rule-section {
    margin-top: 20px;
  }
}

:deep(.form-item-flex .ant-form-item-control-input-content) {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.base-date-time-range-inline {
  margin-bottom: 0;
}
.item-bottom {
  margin-bottom: 0;
}
//影藏红的点
.hide-required-mark {
  :deep(.ant-form-item-required) {
    display: none;
  }
}
</style>
