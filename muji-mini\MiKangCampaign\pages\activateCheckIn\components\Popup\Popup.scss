.clockPopup {
    position: relative;
    background: transparent;

    &-content {
        overflow: hidden;
        width: 630rpx;
        height: 790rpx;
        border-radius: var(--radius);
        display: flex;
        flex-direction: column;
        align-items: center;
        background-position: center center;
        box-sizing: border-box;

        .iconfont {
            width: 79rpx;
            height: 79rpx;
            background-size: 100% 100%;
        }

        .title {
            margin-top: 80rpx;
            font-family: MUJIFont2020;
            font-weight: 500;
            font-size: 36rpx;
            color: var(--text-black-color);
            line-height: 67rpx;
            letter-spacing: 0px;
            // margin-top: 80rpx;
            // text-align: center;
            // padding-top: 36rpx;
            display: flex;
            flex-direction: column;
            align-items: center;
            text {
                margin-top: 10rpx;
            }
        }

        .text {
            width: 450rpx;
            font-family: MUJIFont2020;
            font-weight: 400;
            font-size: 24rpx;
            line-height: 40rpx;
            text-align: left;
            color: var(--text-black-color);
            margin-top: 10rpx;
            margin-left: 8rpx;
        }

        .sub-text {
            color: #3c3c43;
            text-decoration: underline;
            font-weight: bolder;
        }

        .clock-btn {
            padding-top: 40rpx;
        }
    }

    &-close {
        padding-top: 10rpx;
        height: 80rpx;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    &-closeBox {
        height: 80rpx;
        width: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.custom-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-position: center;
    background-size: 690rpx 790rpx;
}
