package com.dz.ms.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.core.dto.DownloadDTO;
import com.dz.common.core.dto.DownloadQueryParamDTO;
import com.dz.ms.basic.entity.DownloadCenter;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 下载中心表Mapper
 *
 * <AUTHOR>
 * 2022/08/15 16:00
 */
@Mapper
public interface DownloadCenterMapper extends BaseMapper<DownloadCenter> {

    int deleteByPrimaryKey(Long id);

    int insert(DownloadCenter record);

    int insertSelective(DownloadCenter record);

    DownloadCenter selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DownloadCenter record);

    int updateByPrimaryKey(DownloadCenter record);

    IPage<DownloadCenter> list(Page<DownloadCenter> page, @Param("param") DownloadQueryParamDTO paramDTO);

    /**
     * 超过n天未下载作废
     *
     * @param scarpDays
     * @return
     */
    int updateScarp(@Param("scarpDays") Integer scarpDays);

    List<DownloadDTO> listScarp(@Param("scarpDays") Integer scarpDays);
}