<template>
  <div>
    <slot name="default">
      <div style="display:flex;cursor:pointer" v-if="link.linkName">
        <a-space @click="showModal">
          <span style="margin-right:10px">{{ link.linkName }}</span>
          <EditOutlined />
        </a-space>
        <DeleteOutlined @click="deleteLink" style="margin-left:50px" />
      </div>
      <a-button @click="showModal" :type="btnType" v-else>{{btnTitle}}</a-button>
    </slot>
  </div>
  <a-modal title="选择页面" width="1000" centered :maskClosable="false" :closable="true" :open="visible" @cancel="visible=false">
    <a-form ref="formRef" :model="addParams" style="width:1000px">
      <a-form-item>
        <a-tabs v-model:activeKey="activeKey" type="card" @change="changeKey">
          <a-tab-pane key="1" tab="常规页面" v-if="showList.includes(1)"></a-tab-pane>
          <a-tab-pane key="2" tab="系统页面" v-if="showList.includes(2)"></a-tab-pane>
          <a-tab-pane key="3" tab="代码级页面" v-if="showList.includes(3)"></a-tab-pane>
          <a-tab-pane key="4" tab="弹窗页面" v-if="showList.includes(4)"></a-tab-pane>
          <a-tab-pane key="5" tab="开屏页面" v-if="showList.includes(5)"></a-tab-pane>
          <a-tab-pane key="6" tab="H5页面" v-if="showList.includes(6)"></a-tab-pane>
          <a-tab-pane key="7" tab="自定义链接" v-if="showList.includes(7)"></a-tab-pane>
          <a-tab-pane key="8" tab="货架商品" v-if="showList.includes(8)"></a-tab-pane>

        </a-tabs>
      </a-form-item>
      <div style="height:500px">
        <template v-if="activeKey<=5">
          <a-form-item name="selectedRowKeys" :rules="[{type:'array',  required: true, message: '请选择', trigger: ['blur', 'change'] }]">
            <a-table :row-selection="rowSelection" :indentSize="20" :scroll="{ scrollToFirstRowOnChange: true, y: 400, x: '100%' }" :columns="columns" row-key="id" :data-source="dataSource" :pagination="pagination" :loading="loading" @change="loadData">
              <template #bodyCell="{ index, column}">
                <template v-if="column.dataIndex === 'index'">{{ (current - 1) * pageSize + 1 + index }}</template>
              </template>
            </a-table>
          </a-form-item>
        </template>
        <template v-else-if="activeKey==6">
          <div class="header-title" style="margin-top:0">页面名称</div>
          <a-form-item name="linkName">
            <a-input placeholder="请输入名称" maxlength="100" v-model:value="addParams.linkName" allowClear></a-input>
          </a-form-item>
          <div class="header-title">H5链接&同主题公众号文章</div>
          <a-form-item name="url" :rules="[{  required: true, message: '请输入', trigger: ['blur', 'change'] }]">
            <a-textarea placeholder="请输入目标链接" rows="5" allow-clear v-model:value="addParams.url" allowClear></a-textarea>
            <div class="global-tip">请确保H5域名配置到小程序后台业务域名内，如有问题与技术联系！</div>
          </a-form-item>
        </template>
        <template v-else-if="activeKey==7">
          <div class="header-title" style="margin-top:0">页面名称</div>
          <a-form-item name="linkName">
            <a-input placeholder="请输入名称" maxlength="100" v-model:value="addParams.linkName" allowClear></a-input>
          </a-form-item>
          <div class="header-title">目标小程序的appid</div>
          <a-form-item name="appid">
            <a-input size="large" placeholder="请输入" allow-clear v-model:value="addParams.appid" allowClear></a-input>
          </a-form-item>
          <div class="header-title">有效路径</div>
          <a-form-item name="path" :rules="[{  required: true, message: '请输入', trigger: ['blur', 'change'] }]">
            <a-textarea size="large" placeholder="请输入" allow-clear v-model:value="addParams.path" allowClear></a-textarea>
          </a-form-item>
        </template>
        <!-- && props.shelfId -->
        <template v-if="activeKey==8 ">
          <a-form-item name="selectedRowKeys" :rules="[{type:'array',  required: true, message: '请选择', trigger: ['blur', 'change'] }]">
            <a-table :row-selection="rowSelection" :indentSize="20" :scroll="{ scrollToFirstRowOnChange: true, y: 400, x: '100%' }" :columns="shopColumns" row-key="id" :data-source="dataSource" :pagination="pagination" :loading="loading" @change="loadData">
              <template #bodyCell="{ index, column,record}">
                <template v-if="column.dataIndex === 'index'">{{ (current - 1) * pageSize + 1 + index }}</template>
                <template v-if="column.dataIndex === 'shelfImg'">
                  <a-image :src="record.shelfImg" :width="50" :height="50" />
                </template>
              </template>
            </a-table>
          </a-form-item>
        </template>
      </div>

    </a-form>
    <template #footer v-if="!disabled">
      <a-space>
        <a-button @click="visible=false">取消</a-button>
        <a-button type="primary" @click="ok">确定</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup>
import { message } from "ant-design-vue";
import { templateList } from '@/http/index.js'
import { apiGiftRack } from '@/http/index.js'
import { cloneDeep } from 'lodash'
import { v4 as uuidv4 } from "uuid";
const formRef = ref()
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  // 页面配置
  link: {
    type: Object,
    required: true
  },
  // 额外参数
  params: {
    type: Object,
    default() {
      return {}
    }
  },
  btnType: {
    type: String,
    default: 'default'
  },
  btnTitle: {
    type: String,
    default: '+ 添加链接'
  },
  // 展示的页面数据
  showList: {
    type: Array,
    default() {
      return [1, 2, 3, 5, 6, 7, 8]
    },
  },
  shelfId: {//货架id
    type: [String, Number],
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

watch(() => props.link, (data) => {
  let params = { ...cloneDeep(data) }
  console.log(params)
  let { linkType, linkUrl } = params
  addParams.value = params
  changeKey(props.showList[0])
  if (props.showList[0] <= 5 && linkUrl) {
    params.selectedRowKeys = [Number(linkUrl.split('?id=')[1])]
  } else {
    params.selectedRowKeys = []
  }
})

// templateType	模板类型 1常规页面 2系统页面 3定制页面 4弹窗页面  5开屏页
// pageType	页面类型 0二级自定义页面 1首页 2兑换 3会员码 4生活圈 5更多

const { visible, addParams, params, columns, dataSource, total, pageSize, current, loading, activeKey, shopColumns } = toRefs(reactive({
  visible: false,
  addParams: cloneDeep(props.link),
  params: {},
  loading: false,
  columns: [
    { title: '序号', dataIndex: 'index', align: 'center', width: 80 },
    { title: 'ID', dataIndex: 'id', align: 'center', ellipsis: true, width: 80 },
    { title: '模板编号', dataIndex: 'templateCode', align: 'center', ellipsis: true, width: 100 },
    { title: '页面名称', dataIndex: 'templateName', align: 'center', ellipsis: true, width: 140 },
    {
      title: '页面组', dataIndex: 'groupName', align: 'center', ellipsis: true, width: 140,
      customRender({ text, record }) {
        return text || '--'
      },
    },
  ],
  shopColumns: [
    { title: '序号', dataIndex: 'index', align: 'center', width: 80 },
    { title: 'ID', dataIndex: 'id', align: 'center', ellipsis: true, width: 80 },
    { title: '商品名称', dataIndex: 'productName', align: 'center', ellipsis: true, width: 100 },
    { title: '商品类型', dataIndex: 'pdTypeDesc', align: 'center', ellipsis: true, width: 140 },
    {
      title: '商品主图', dataIndex: 'shelfImg', align: 'center', ellipsis: true, width: 140,
      customRender({ text, record }) {
        return text || '--'
      },
    },
  ],
  dataSource: [],
  total: 1,
  pageSize: 10,
  current: 1,
  activeKey: '1'
}))


// 切换类型
const changeKey = (key) => {
  key = key || '1'
  activeKey.value = String(key)
  console.log(activeKey.value, 'dddddddddddddddd')
  addParams.value.selectedRowKeys = []

  formRef.value?.resetFields()
  current.value = 1;
  if (key <= 5) {
    // templateType	模板类型 1常规页面 2系统页面 3定制页面 4弹窗页面  5开屏页
    params.value = {
      templateName: '',
      templateType: key,
      publish: 1
    }
    loadData({ current: 1, pageSize: pageSize.value })
  } else if (key == 8) {
    params.value = {
      shelfId: props.shelfId,
      beShow: 1
    }
    loadData({ current: 1, pageSize: pageSize.value })
  }
  dataSource.vlaue = []
}

// 分页参数
let pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showTotal: total => `共 ${total} 条`,
    position: ['bottomCenter'],
  }
});



// 切换选中
const rowSelection = computed(() => {
  return {
    hideSelectAll: true,// 去掉全选
    type: 'radio',
    selectedRowKeys: addParams.value.selectedRowKeys,
    onChange: (ids, rows) => {
      console.log(ids, rows)
      // templateType	模板类型 1常规页面 2系统页面 3定制页面 4弹窗页面  5开屏页
      // pageType	页面类型 0二级自定义页面 1首页 2兑换 3会员码 4生活圈 5更多
      addParams.value.selectedRowKeys = ids;
      let { templateName, pagePath, id, productName } = rows[0];
      if (activeKey.value == 8) {
        addParams.value.linkName = productName
        addParams.value.linkUrl = `/pages/integralDetails/integralDetails?productId=${id}`

      } else {
        addParams.value.linkName = templateName
        addParams.value.linkUrl = `${pagePath}?id=${id}`
      }


      console.log(addParams.value)
    }
  }
})

// 加载数据
const loadData = (e) => {
  if (e) {
    current.value = e.current
    pageSize.value = e.pageSize
  }

  loading.value = true
  if (activeKey.value == 8) {
    apiGiftRack.getShelfProductPageListShop({ pageNum: current.value, pageSize: pageSize.value, ...params.value }).then(res => {
      let { count, list } = res.data;
      dataSource.value = list
      total.value = count
    }).finally(() => {
      loading.value = false
    })
  } else {
    templateList({ pageNum: current.value, pageSize: pageSize.value, ...params.value }).then(res => {
      let { count, list } = res.data;
      dataSource.value = list
      total.value = count
    }).finally(() => {
      loading.value = false
    })
  }

}

// 点击显示弹窗
const showModal = () => {
  let params = { ...cloneDeep(props.link) }
  let { linkType, linkUrl } = params
  addParams.value = params
  changeKey(props.showList[0])
  if (props.showList[0] <= 5 && linkUrl) {
    params.selectedRowKeys = [Number(linkUrl.split('?id=')[1])]
  } else if (props.showList[0] == 8 && linkUrl) {
    params.selectedRowKeys = [Number(linkUrl.split('?id=')[1])]
  } else {
    params.selectedRowKeys = []
  }
  if (props.showList.includes(8) && props.shelfId) {
    visible.value = true
  } else if (!props.showList.includes(8)) {
    visible.value = true
  } else {

    message.warning("请选择货架")
  }

}

// 删除链接
const deleteLink = () => {
  addParams.value.linkType = 1
  addParams.value.linkName = ''
  addParams.value.linkUrl = ''
  addParams.value.appid = ''
  addParams.value.path = ''
  addParams.value.url = ''
  let params = cloneDeep(addParams.value)
  delete params.selectedRowKeys
  emit('ok', {
    params: props.params,
    data: params
  })
}

// 保存设置
const ok = () => {
  addParams.value.linkType = activeKey.value
  formRef.value.validate().then(res => {
    // 校验选择
    visible.value = false
    let params = cloneDeep(addParams.value)
    if (params.linkType == 6) {
      params.linkName = params.linkName || "H5页面"
    } else if (params.linkType == 7) {
      params.linkName = params.linkName || "自定义链接"
    }
    console.log(props.params, 'props.paramsprops.paramsprops.paramsprops.params');
    delete params.selectedRowKeys
    emit('ok', {
      params: props.params,
      data: params
    })
  })
}
</script>

<style  scoped lang="scss">
</style>
