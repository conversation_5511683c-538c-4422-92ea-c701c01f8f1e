<template>
  <ul class="tree">
    <li v-for="node in treeData" :key="node.id">
      <span @click="toggle(node)">
        <i v-if="node.children && node.children.length" :class="{ 'fa-plus': !node.expanded, 'fa-minus': node.expanded }"></i>
        {{ node.name }}
      </span>
      <ul v-if="node.children && node.children.length && node.expanded" class="tree">
        <TreeNode v-for="child in node.children" :key="child.id" :node="child"></TreeNode>
      </ul>
    </li>
  </ul>
</template>
 
<script setup>
import { onMounted } from "vue";


const props = defineProps({
  nodes: {
    type: Object,
  },
  firstIndex: {
    type: String,
  },
  secondIndex: {
    type: String,

  },
  threeIndex: {
    type: String,
  },
})

const toggle = (node) => {
  if (node.children) {
    node.expanded = !node.expanded;
  }
}

onMounted(() => {
  // console.log(props.nodes)
})


</script>
 
<style>
.tree {
  list-style-type: none;
}
.tree span {
  cursor: pointer;
}
.tree i {
  margin-right: 5px;
}
</style>