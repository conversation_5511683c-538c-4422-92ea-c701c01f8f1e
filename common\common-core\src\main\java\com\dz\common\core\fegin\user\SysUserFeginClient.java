package com.dz.common.core.fegin.user;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.user.SysUserDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 用户信息FeginClient
 * @Author: Handy
 * @Date: 2022/2/3 23:11
 */
@FeignClient(name = ServiceConstant.USER_SERVICE_NAME, contextId = "SysUserFeginClient")
public interface SysUserFeginClient {

    /**
     * 根据UID查询系统用户信息(带缓存)
     * @return
     */
    @GetMapping(value = "/sys_user/info")
    public Result<SysUserDTO> getSysUserByUid(@RequestParam("uid")Long uid, @RequestParam("tenantId")Long tenantId);
    @GetMapping(value = "/sys_user/getUserByIds")
    public Result<List<SysUserDTO>> getUserByIds(@RequestParam("ids")List<Long> ids);

}

