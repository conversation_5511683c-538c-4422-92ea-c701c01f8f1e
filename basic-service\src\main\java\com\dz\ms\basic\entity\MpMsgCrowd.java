package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
@Getter
@Setter
@NoArgsConstructor
@Table("消息发送人群包")
@TableName(value = "mp_msg_crowd")
public class MpMsgCrowd implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.VARCHAR,length = 36,isNull = true,comment = "微信unionid",isIndex = true)
    private String unionid;
    @Columns(type = ColumnType.VARCHAR,length = 36,isNull = true,comment = "小程序openid",isIndex = true)
    private String openid;
    @Columns(type = ColumnType.VARCHAR,length = 64,isNull = true,comment = "会员卡号")
    private String cardNo;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = true,comment = "是否发送 0否 1是")
    private Integer isPush;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
}
