@import "../config";

// 边距工具类
$direction: (t: top, r: right, b: bottom, l: left);
$spacers: 0, 5, 10, 15, 20, 22, 25, 30, 100;
@include spacing-gen($spacers, $direction);

// 宽度工具类
$widthList: 100;
@include width-gen($widthList);

// iPhoneX适配底部安全区域：使用苹果官方推出的css函数constant()、env()适配（推荐）
.iPhoneXb {
  bottom: constant(safe-area-inset-bottom) !important;
  bottom: env(safe-area-inset-bottom) !important;
}

.iPhoneXh {
  height: constant(safe-area-inset-bottom) !important;
  height: env(safe-area-inset-bottom) !important;
}

.iPhoneXpb {
  padding-bottom: constant(safe-area-inset-bottom) !important;
  padding-bottom: env(safe-area-inset-bottom) !important;
}

.iPhoneXmb {
  margin-bottom: constant(safe-area-inset-bottom) !important;
  margin-bottom: env(safe-area-inset-bottom) !important;
}
