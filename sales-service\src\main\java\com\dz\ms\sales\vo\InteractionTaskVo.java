package com.dz.ms.sales.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 任务新增
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InteractionTaskVo
{
    @ApiModelProperty(value = "任务ID")
    private Long id;
    @ApiModelProperty(value = "任务名称")
    private String taskName;
    @ApiModelProperty(value = "启停状态 0启用，1停用")
    private Integer status;
    @ApiModelProperty(value = "任务展示时间类型：1时间段，2永久展示")
    private Integer showTimeType;
    @ApiModelProperty(value = "任务展示开始时间")
    private Date showTimeStart;
    @ApiModelProperty(value = "任务展示结束时间")
    private Date showTimeEnd;
    @ApiModelProperty(value = "任务展示图片")
    private String showImg;
    @ApiModelProperty(value = "任务类型：1限时，2购物，3互动")
    private Integer taskType;
    @ApiModelProperty(value = "是否限时：1限时，2不限时")
    private Integer isTimeRestrict;
    @ApiModelProperty(value = "限时开始时间")
    private Date restrictTimeStart;
    @ApiModelProperty(value = "限时结束时间")
    private Date restrictTimeEnd;
    @ApiModelProperty(value = "任务内容：1线下打卡，2兑礼任务，3线下消费，4邀请好友，5首次购买")
    private Integer taskDesc;
    @ApiModelProperty(value = "任务完成周期：1一次性，2周期，3周期+阶梯")
    private Integer readyCycle;
    @ApiModelProperty(value = "任务需要完成次数")
    private Integer readyNum;
    @ApiModelProperty(value = "任务总计完成次数")
    private Integer totalReadyNum;
    @ApiModelProperty(value = "readyDay、readyMonth")
    private String reayType;
    @ApiModelProperty(value = "任务完成周期天数")
    private Integer readyDay;
    @ApiModelProperty(value = "任务完成周期月数")
    private Integer readyMonth;
    @ApiModelProperty(value = "奖励列表")
    List<TaskRewardVo> taskRewardList;
    @ApiModelProperty(value = "线下消费商品品类码部门code，多个用英文逗号隔开")
    private String storeProductCodes;
    @ApiModelProperty(value = "线下消费商品品类码depart code，多个用英文逗号隔开")
    private String departProductCodes;
    @ApiModelProperty(value = "线下消费商品品类码line code，多个用英文逗号隔开")
    private String lineProductCodes;
    @ApiModelProperty(value = "线下消费商品品类码class code，多个用英文逗号隔开")
    private String classProductCodes;
    @ApiModelProperty(value = "线下消费商品品类码jan code，多个用英文逗号隔开")
    private String janProductCodes;
    @ApiModelProperty(value = "任务规则图片")
    private String ruleImg;
}
