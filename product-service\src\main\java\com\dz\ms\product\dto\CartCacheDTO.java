package com.dz.ms.product.dto;


import com.dz.common.core.dto.product.CartProductDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 购物车缓存DTO
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
public class CartCacheDTO {

    @ApiModelProperty(value = "购物车商品列表")
    private List<CartProductDTO> productList;
    //是否缓存数据
    private Integer isCache;
}