package com.dz.gateway.swagger;

public class SwaggerResource {

    private String name;
    private String url;
    private String swaggerVersion;

    public SwaggerResource() {
    }

    public SwaggerResource(String name, String url, String swaggerVersion) {
        this.name = name;
        this.url = url;
        this.swaggerVersion = swaggerVersion;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getSwaggerVersion() {
        return swaggerVersion;
    }

    public void setSwaggerVersion(String swaggerVersion) {
        this.swaggerVersion = swaggerVersion;
    }
}
