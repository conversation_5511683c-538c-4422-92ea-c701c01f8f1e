<!--signUp/pages/overPage/overPage.wxml-->
<my-page overallModal="{{overallModal}}">
  <view class="page-container" style="background:#F8F6ED;">
    <custom-header isShare="{{true}}" background="transparent" type="{{1}}" color="black" />
    <scroll-view class="overPage-wrap" enhanced="{{ true }}" bounces="{{ false }}" show-scrollbar="{{ false }}" scroll-y>
      <view class="overPage">
        <view class="overPage-top" style="height: calc(100vh - {{navBarHeight+statusBarHeight+'px'}});">
          <view class="activeRules" bindtap="activeRules">
            <view class="activeRules-in">活动规则</view>
          </view>
          <view class="activeRules2" bindtap="activeRules2">
            <view class="activeRules2-in">中选公示</view>
          </view>
          <!-- 报名未中奖 或者报名时间已结束 未获得体验资格 -->
          <block wx:if="{{type=='zhaomuNot'||type=='zhaomuOver'}}">
            <view class="image1">
              <view class="h1">
                <!-- EnrollStatus 1：没有报名 2：报名了 -->
                <text wx:if="{{EnrollStatus==1}}">{{round==1?'第一轮体验官\n招募已结束':'第二轮体验官\n招募已结束'}}</text>
                <text wx:if="{{EnrollStatus==2}}">{{'很遗憾\n您未成为体验官'}}</text>
              </view>
              <view class="text">
                <text class="text1">您还可以通过以下方式参与打卡活动</text>
              </view>
            </view>
            <view class="otherType">
              <view class="other-box ">
                <image class="Other-img Other-img1" src="{{$cdn}}/signUp/overIcon1.png" mode="" />
                <view class="other-title">购买新品</view>
                <!-- 购买任一敏感肌用系列商品（线上购买需确认收货），次日10:00即可开启7天打卡 -->
                <view class="othet-text"> <text>{{"任购敏感肌用正装\n次日10:00即可开启7天打卡"}}</text> </view>
                <view class="othet-text1"> <text>{{"*线上购买需要确认收货后"}}</text> </view>
              </view>
              <!-- 第一轮报名展示 -->
              <view wx:if="{{round==1}}" class="other-box other-box2">
                <view class="line"></view>
                <image class="Other-img Other-img2" src="{{$cdn}}/signUp/overIcon2.png" mode="" />
                <view class="other-title">报名参加</view>
                <view class="othet-text">
                  <text>{{activeTitle}}</text>
                </view>
              </view>
            </view>
            <view class="otherType-bottom-box">
              <basic-button width="{{670}}" disabled="{{!disabled}}" loading="{{loading}}" size="large" bind:click="submit">
                {{!disabled?'已订阅活动消息':"订阅活动消息"}}
              </basic-button>
            </view>
            <view class="pullDownView">
              <view class="viewIconfont iconfont icon-Pull-down"></view>
              <view class="viewText">下拉查看更多</view>

            </view>
          </block>
          <!-- 打卡结束 未完成打卡 -->
          <block wx:if="{{type=='signUpOver'}}">
            <view class="image1">
              <view class="h1">
                <text>{{"您未完成\n体验官打卡任务"}}</text>
              </view>
              <view class="text">
                <text class="text1">您还可以通过以下方式参与打卡活动</text>
              </view>

            </view>
            <view class="overPage">
              <view class="signUpOver-box">
                <view class="signUpOver-left">
                  <view class="signUpOver_title">完成打卡可获得</view>
                  <view class="signUpOver_text">健康美容品类85折券</view>
                </view>
                <image class="pic2 pic3" src="{{$cdn}}/signUp/overPageIcon.png" mode="" />
              </view>
              <view class="signUpOver-box signUpOver-box1">
                <view class="signUpOver-left">
                  <view class="signUpOver_title signUpOver_title1"><text>完成打卡参与抽奖 </text></view>
                  <view class="signUpOver_title signUpOver_title1"><text>有机会获得 </text></view>

                  <view class="signUpOver_text">健康美容品类85折券</view>
                </view>
                <image class="pic2" src="{{$cdn}}/signUp/signUpIcon2.png" mode="" />
              </view>
              <view class="signUpOver-bottom-box">
                <basic-button width="{{670}}" disabled="{{disabled}}" loading="{{loading}}" size="large" bind:click="resetClick">
                  重新开启打卡体验
                </basic-button>
              </view>
              <view class="pullDownView">
                <view class="viewIconfont iconfont icon-Pull-down"></view>
                <view class="viewText">下拉查看更多</view>

              </view>
            </view>
          </block>
          <!-- 购买也无法打卡 -->
          <block wx:if="{{type=='buyNot'}}">
            <view class="image1">
              <view class="text1">
                报名体验官时间已结束
                <!-- <text bind:tap="submit" class="subscribe-text" data-str="new_campaign">新活动通知我</text>
            <text class="subscribe" style="background-image: url({{$cdn}}/signUp/subscribe.png);" mode="" /> -->
              </view>
              <view class="h1">
                敏感肌用基础补水系列
              </view>

            </view>
            <view class="box-bottom">
              <view class="picture">
                <image class="img" src="{{$cdn}}/signUp/overpage1.png" mode="" />
              </view>
            </view>
          </block>
        </view>
        <!-- <view class="overPage-bottom"> -->
        <!-- <view class="bottom-title">
            <view class="bottom-title1"> <text>{{'敏感肌用\n基础补水系列'}}</text> </view>
            <view class="bottom-title2"> 新品介绍 </view>
          </view> -->
        <view class="picture">
          <image class="img" mode="widthFix" style="width:750rpx;flex-shrink:0;" src="{{$cdn}}/signUp/overpage2.png" />
        </view>
        <!-- </view> -->
      </view>
    </scroll-view>
    <!-- 拒绝订阅 提示框 -->
    <guidePopup showGuide="{{showContact1}}" bind:finish="closeGuide" />
  </view>
</my-page>