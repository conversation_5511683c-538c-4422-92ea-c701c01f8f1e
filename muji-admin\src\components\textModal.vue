<template>
  <div @click="showModal">
    <slot name="default"></slot>
  </div>
  <a-modal title="样式设置" :width="600" centered :maskClosable="false" :closable="true" :open="visible" @cancel="visible=false">
    <div class="modal">
      <textSetting :data="addParams" :disabled="disabled"></textSetting>
    </div>
    <template #footer v-if="!disabled">
      <a-space>
        <a-button @click="visible=false">取消</a-button>
        <a-button type="primary" @click="ok">确定</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
const emit = defineEmits(['ok', 'cancel'])
import { cloneDeep } from 'lodash'
import { v4 as uuidv4 } from "uuid";

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  // 当前组件数据
  data: {
    type: Object,
    default() {
      return {}
    }
  },

})


const { visible, addParams } = toRefs(reactive({
  visible: false,
  addParams: {}
}))
// 点击显示弹窗
const showModal = () => {
  addParams.value = cloneDeep(props.data)
  visible.value = true
}

const ok = () => {
  emit('ok', addParams.value)
  visible.value = false
}
</script>

<style lang="scss" scoped>
.active {
  background: #a6ccf7;
}
</style>
