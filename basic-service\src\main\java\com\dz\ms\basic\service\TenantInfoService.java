package com.dz.ms.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.core.dto.KeyValueDTO;
import com.dz.ms.basic.dto.TenantInfoDTO;
import com.dz.ms.basic.entity.TenantInfo;

/**
 * 租户信息接口
 * @author: Handy
 * @date:   2022/01/28 15:40
 */
public interface TenantInfoService extends IService<TenantInfo> {

    /**
     * 保存租户信息
     * @param param
     */
    Long saveTenant(TenantInfoDTO param);

    /**
     * 根据租户编码获取租户信息
     * @param code
     * @return
     */
    KeyValueDTO getTenantByCode(String code);

    /**
     * 初始化品牌信息
     * @param tenantId
     */
    void initTenant(Long tenantId);

    /**
     * 根据租户ID查询租户信息
     * @param id
     * @return
     */
    KeyValueDTO getTenantById(Long id);

}
