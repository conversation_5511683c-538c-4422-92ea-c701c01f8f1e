// 校验密码 6-18位密码，字母 + 数字 + 符号任意两种组合
export function checkPassword(rule, val, callback) {
  let pattern = /(?!^(\d+|[a-zA-Z]+|[~!@#$%^&*?]+)$)^[\w~!@#$%^&*?]{6,18}$/
  if (val && !pattern.test(val)) {
    return Promise.reject('请输入6-18位密码，字母/数字/特殊符号任意两种组合')
  }
  return Promise.resolve()
}

// 账号 字母或者数组 20个字符
export function checkUserName(rule, val, callback) {
  let pattern = /^[0-9A-Za-z]{1,20}$/
  if (val && !pattern.test(val)) {
    return Promise.reject('请输入字母或数字，20个字符内')
  }
  return Promise.resolve()
}


// 两位小数
export const decimal2 = (val) => /(^-?[1-9](\d+)?(\.\d{1,2})?$)|(^-?0$)|(^-?\d\.\d{1,2}$)/.test(val)

const falsey = (val) => [null, undefined, false, NaN, ''].includes(val)

// 限制最大、最小值(包含小数)
export function limitFloatNumber(rule, value, callback, { min, max }) {
  console.log("🚀 ~ limitFloatNumber ~ value:", value, typeof value)
  if (typeof value == 'number') {
    if (!decimal2(value)) return callback(new Error('请输入数字，小数位最多两位'))
    if (!falsey(min) && value < min) return callback(new Error(`最小值为${min}`))
    if (!falsey(max) && value > max) return callback(new Error(`最大值为${max}`))
    callback()
  } else {
    callback()
  }


}

// 校验手机号
export function checkMobile(rule, val, callback) {
  let pattern = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/
  if (val && !pattern.test(val)) {
    return Promise.reject('请输入正确格式的手机号')
  }
  return Promise.resolve()
}

// 校验邮箱
export function checkEmail(rule, val, callback) {
  let pattern = /^[0-9A-Za-z_]+([-+.][0-9A-Za-z_]+)*@[0-9A-Za-z_]+([-.][0-9A-Za-z_]+)*\.[0-9A-Za-z_]+([-.][0-9A-Za-z_]+)*$/
  if (val && !pattern.test(val)) {
    return Promise.reject('请输入正确格式的邮箱')
  }
  return Promise.resolve()
}

// 是否是手机号
function isPhoneNumber(value) {
  return /^1\d{10}$/.test(value)
}

// 是否是金额
function isMoney(value) {
  return /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/.test(value)
}

// 是否是正整数
function isPositiveInteger(value) {
  return /^[1-9]\d*$/.test(value)
}
// 限制最大、最小值
export function limitNumber(rule, value, callback, { min, max }) {
  if (value === '' || value === null) callback()
  if (!/^[-+]?(0|[1-9]\d*)$/.test(value)) return callback(new Error('请输入整数'))
  if (!falsey(min) && value < min) return callback(new Error(`最小值为${min}`))
  if (!falsey(max) && value > max) return callback(new Error(`最大值为${max}`))
  callback()
}
// 是否是自然数
function isNaturalNumber(value) {
  return value === '0' || isPositiveInteger(value)
}

export const formRulesValidate = {
  // 基础校验
  isPhoneNumber,
  isMoney,
  isPositiveInteger,
  isNaturalNumber,

  // element-ui-form校验
  elMoney(value = '', callback, min = 0.01, max) { // 金额 - 最多保留两位小数
    console.log("🚀 ~ elMoney ~ value:", String(value))
    value = String(value)
    if (!value) {
      return Promise.resolve()
    } else {
      if (isMoney(value)) {
        const val = +value
        if (min !== undefined && val < min) {
          return Promise.reject(new Error(`不能小于${min}`))
        } else if (max !== undefined && val > max) {
          return Promise.reject(new Error(`不能大于${max}`))
        } else {
          return Promise.resolve()
        }
      } else {
        return Promise.reject(new Error('格式有误'))
      }
    }
  },
  elNaturalNumber(value = '', callback, min = 0, max) { // 自然数 - 0到正无穷
    value = String(value)
    if (!value) {
      return Promise.resolve()
    } else {
      if (isNaturalNumber(value)) {
        const val = +value
        if (min !== undefined && val < min) {
          return Promise.reject(new Error(`不能小于${min}`))
        } else if (max !== undefined && val > max) {
          return Promise.reject(new Error(`不能大于${max}`))
        } else {
          return Promise.resolve()
        }
      } else {
        return Promise.reject(new Error('请输入自然数'))
      }
    }
  },
  elPositiveInteger(value = '', callback, min = 1, max) { // 正整数 - 1到正无穷
    value = String(value)
    if (!value) {
      return Promise.resolve()
    } else {
      if (isPositiveInteger(value)) {
        const val = +value
        if (min !== undefined && val < min) {
          return Promise.reject(new Error(`不能小于${min}`))
        } else if (max !== undefined && val > max) {
          return Promise.reject(new Error(`不能大于${max}`))
        } else {
          return Promise.resolve()
        }
      } else {
        return Promise.reject(new Error('请输入正整数'))
      }
    }
  },
  elMinLength(value = '', callback, length = 11) {
    value = String(value)
    if (!value) {
      return Promise.resolve()
    } else {
      if (value.length >= length) {
        return Promise.resolve()
      } else {
        return Promise.reject(new Error(`长度至少为${length}位`))
      }
    }
  }
}
