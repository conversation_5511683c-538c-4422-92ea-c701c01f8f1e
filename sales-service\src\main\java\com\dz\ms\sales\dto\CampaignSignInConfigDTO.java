package com.dz.ms.sales.dto;


import com.dz.common.core.annotation.Columns;
import com.dz.common.core.enums.ColumnType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/18
 */
@Data
public class CampaignSignInConfigDTO {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "活动标识")
    private String campaignCode;

    @ApiModelProperty(value = "签到活动天数")
    private Integer signInDays;

    @ApiModelProperty(value = "签到次数")
    private Integer signInTimes;

    @ApiModelProperty(value = "允许补签的最大次数")
    private Integer repairSignInTimes;


    @ApiModelProperty(value = "补签标识 0不允许 1允许")
    private Integer patchFlag;

    @ApiModelProperty(value = "完成打卡后是否赠送优惠券 0不赠送 1赠送")
    private Integer isSendCoupon;

    @ApiModelProperty(value = "优惠券编码")
    private String couponCode;

    @ApiModelProperty(value = "完成打卡后是否赠送抽奖机会数量")
    private Integer signInSendLotteryNum;

}
