// pages/productSearch.js
import {
  getProductList,
} from '../../api/index.js'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    loadingList: false,
    isBackHidden: false,
    productList: [],
    productName: '',
    searchState: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      searchState: false,
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },
  inputChang(e) {
    console.log(e);
  },
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },
  async handleSearchStore() {
    const {
      productName
    } = this.data;
    wx.$mp.track({
      event: 'search_product',
      props: {
        productName,
      }
    })
    if (productName === '') {
      this.setData({
        productList: []
      })
    } else {
      const res = await getProductList({
        productName
      });
      this.setData({
        productList: res.data?.list || [],
        searchState: true,
      })
    }
  },
  handleChange(e) {
    this.setData({
      productName: e.detail,
    })
  }
})
