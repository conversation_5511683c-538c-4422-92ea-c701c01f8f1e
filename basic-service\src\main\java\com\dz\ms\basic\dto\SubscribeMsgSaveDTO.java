package com.dz.ms.basic.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 小程序订阅消息模板配置保存入参DTO
 * @author: Handy
 * @date:   2023/07/07 14:22
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "小程序订阅消息模板配置保存入参")
public class SubscribeMsgSaveDTO extends BaseDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "模板编号")
    private String templateCode;
    @ApiModelProperty(value = "模板ID")
    private String templateId;
    @ApiModelProperty(value = "模板名称")
    private String templateName;
    @ApiModelProperty(value = "触发类型 1自动触发 2 手动触发")
    private Integer triggerType;
    @ApiModelProperty(value = "关键词逗号隔开")
    private String keyword;
    @ApiModelProperty(value = "跳转路径")
    private String pagePath;
    @ApiModelProperty(value = "状态 0禁用 1启用")
    private Integer state;

    @ApiModelProperty(value = "添加模板关键词列表")
    private List<Integer> kidList;

}
