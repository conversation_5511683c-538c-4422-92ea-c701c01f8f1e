<template>
  <a-modal
    v-model:open="open"
    width="1280px"
    ok-text="确认添加"
    :confirm-loading="confirmLoading"
    @ok="handleOk"
    @cancel="clearHandleData"
  >
    <a-radio-group v-model:value="category" button-style="solid" style="margin-bottom: 20px">
      <a-radio-button value="a">商品</a-radio-button>
      <a-radio-button value="b">积分</a-radio-button>
      <a-radio-button value="c">优惠券</a-radio-button>
    </a-radio-group>
    <template v-if="category === 'a'">
      <searchForm :formParams="searchFields" @cancel="whenClickReset" @ok="whenClickSearch">
        <a-form-item label="商品名称" name="productName">
          <a-input
            placeholder="请输入"
            allow-clear
            v-model:value="searchFields.productName"
            allowClear
            @keyup.enter="whenClickSearch"
          ></a-input>
        </a-form-item>
        <BaseProductTypeSelect label="商品类型" name="pdType" v-model="searchFields.pdType" />
        <BaseBelongingShelfSelect
          label="所处货架"
          name="shelfIdList"
          v-model="searchFields.shelfIdList"
          mode="multiple"
        />
      </searchForm>
      <div class="checkboxWrap">
        <a-checkbox v-model:checked="goodsFields.hideAddedProducts" @change="refresh">
          <span class="ui-c-grey">隐藏已添加商品</span>
        </a-checkbox>
      </div>
      <a-table
        class="shelf-product-management-add-table"
        row-key="id"
        :row-selection="{
          preserveSelectedRowKeys: true,
          selectedRowKeys: goodsFields.selectedRowKeys,
          onChange: onSelectChange
        }"
        :scroll="{ scrollToFirstRowOnChange: true, x: '100%', y: 500 }"
        :dataSource="dataSource"
        :columns="tableHeader"
        :pagination="pagination"
        :loading="loading"
        @change="whenPaginationChange"
      >
        <template #bodyCell="{ text, record, index, column }">
          <template v-if="column.dataIndex === 'shelfImg'">
            <a-image v-if="record.shelfImg" :src="record.shelfImg" :width="50" :height="50" />
            <div v-else>--</div>
          </template>
        </template>
      </a-table>
    </template>
    <template v-else-if="category === 'b'">
      <div>请输入不同奖项奖励的积分数量，一次最多10个不同档位，不输入值则不会添加</div>
      <div class="point-wrap">
        <div v-for="(item, index) in pointData" :key="item._id" class="point-item">
          <span>{{ index + 1 }}</span>
          <a-input-number
            v-model:value="item.pointsNum"
            :max="99999"
            :min="1"
            :parser="inputNumberParserInteger"
          />
          <span>积分</span>
        </div>
      </div>
    </template>
    <template v-else-if="category === 'c'">
      <div>请填写不同档位的优惠券CP号，一次最多10个，不输入值则不会添加</div>
      <div class="coupon-wrap">
        <div v-for="(item, index) in couponData" :key="item._id" class="coupon-item">
          <span class="label">{{ index + 1 }}</span>
          <a-input v-model:value="item.prizesName" placeholder="请输入优惠券名称" />
          <a-input v-model:value="item.couponCode" placeholder="请输入优惠券CP号" class="coupon-input" />
        </div>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { computed, reactive } from 'vue'
import { usePagination } from 'vue-request'
import { productList } from '@/http/index.js'
import { message } from 'ant-design-vue'
import { inputNumberParserInteger } from '../utils/utils'

const open = defineModel('open', { default: false })
const emits = defineEmits(['ok'])

const props = defineProps({
  // 已选择商品的列表
  selectedGoodsList: {
    type: Array,
    default: () => []
  }
})
const confirmLoading = ref(false)

watch(
  () => open.value,
  () => {
    if (open.value) {
      console.log('selectedGoodsList', props.selectedGoodsList)
      whenClickReset()
      // const data = props.selectedGoodsList.reduce(
      //   (data, item) => {
      //     if (item.prizesType === 3) data.point.push({ ...item })
      //     else if (item.prizesType === 4) data.coupon.push({ ...item })
      //     return data
      //   },
      //   { point: [], coupon: [] }
      // )
      pointData.value = getDefaultPointData()
      couponData.value = getDefaultCouponData()
    }
  }
)

// 确定选择商品
function handleOk() {
  console.log('handleOk')
  // 判断是否有输入了优惠券名称没有输入cp号的
  const hasCouponName = couponData.value.findIndex(
    (item) => (item.couponCode && !item.prizesName) || (!item.couponCode && item.prizesName)
  )
  if (hasCouponName !== -1) {
    message.error(`请将第${hasCouponName + 1}项优惠券信息补充完整`)
    return
  }

  // 过滤未输入的积分，并转换为积分数据
  const _pointData = pointData.value
    .filter((item) => item.pointsNum)
    .map((item) => {
      return Object.assign({}, item, { prizesName: `${item.pointsNum}积分` })
    })

  // 过滤未输入的优惠券
  const _couponData = couponData.value
    .filter((item) => item.couponCode)
    .map((item) => ({ ...item }))

  const _goodsList = goodsFields.selectedRowObjs.map((item) => ({
    _id: item.id,
    _awardId: undefined,
    state: 1,
    totalStock: 0,
    crowdId: -1,
    countLimit: 1,
    productId: item.id,
    prizeProbability: '',
    prizesName: item.productName,
    prizesType: item.pdType,
    couponCode: item.venderId,
    imageUrl: null,
    prizesClass: 1
  }))
  const addList = [..._goodsList, ..._pointData, ..._couponData]
  console.log('addList', addList)

  emits('ok', addList)
  open.value = false
  clearHandleData()
}
// 选择类型 商品、积分、优惠券
const category = ref('a')

// 清除已操作数据
function clearHandleData() {
  goodsFields.selectedRowKeys = []
  goodsFields.selectedRowObjs = []
  pointData.value = []
  couponData.value = []
  category.value = 'a'
}

// 商品选择
const searchFields = reactive(getDefaultSearchFields())
const pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ['bottomLeft']
  }
})
const total = ref(0)
const {
  data: dataSource,
  run,
  loading,
  pageSize,
  current,
  refresh
} = usePagination(
  (param) => {
    const data = { ...param, ...searchFields }
    data.state = 1 // 添加商品添加的是启用中的商品
    if (goodsFields.hideAddedProducts) {
      data.hiddenProductIdList = props.selectedGoodsList
        .filter((f) => f.prizesType === 1 || f.prizesType === 2)
        .map((v) => v.productId)
    }
    return productList(data)
  },
  {
    manual: true,
    pagination: { currentKey: 'pageNum', pageSizeKey: 'pageSize', totalKey: 'data.data.count' },
    formatResult: (res) => {
      const { list, count } = res.data
      total.value = count
      list.forEach((v, index) => {
        v._index = (current.value - 1) * pageSize.value + 1 + index
        v._tagList = v.tagList.map((v) => v.name).join('、')
      })
      return list
    }
  }
)
const goodsFields = reactive({
  hideAddedProducts: true,
  selectedRowKeys: [],
  selectedRowObjs: []
})
const tableHeader = [
  { title: '序号', dataIndex: '_index', align: 'center', width: 60 },
  { title: '商品名称', dataIndex: 'productName', align: 'center', ellipsis: true, width: 140 },
  { title: '商品类型', dataIndex: 'pdTypeDesc', align: 'center', width: 80 },
  { title: '创建时间', dataIndex: 'created', align: 'center', width: 140 },
  { title: '商品主图', dataIndex: 'shelfImg', align: 'center', width: 100 },
  { title: '商品标签', dataIndex: '_tagList', align: 'center', ellipsis: true, width: 100 },
  { title: '积分价值', dataIndex: 'costPoint', align: 'center', width: 80 },
  { title: '累计兑换量', dataIndex: 'exchangeNum', align: 'center', width: 80 }
]
function getDefaultSearchFields() {
  return { pdType: undefined, shelfIdList: [], productName: undefined }
}
function whenPaginationChange(pag) {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}
function whenClickSearch() {
  run({ pageNum: 1, pageSize: pageSize.value })
}
function whenClickReset() {
  Object.assign(searchFields, getDefaultSearchFields())
  whenClickSearch()
}
function onSelectChange(e, a) {
  goodsFields.selectedRowKeys = e
  goodsFields.selectedRowObjs = a
}
// 积分数据 固定为10个档位
const pointData = ref([])
// 优惠券数据 固定为10个档位
const couponData = ref([])
function getDefaultCouponData() {
  return Array.from({ length: 10 }, (_, index) => ({
    _id: `coupon_${index + 1}`,
    totalStock: 0,
    state: 1,
    crowdId: -1,
    countLimit: 1,
    prizesType: 4,
    prizeProbability: '',
    couponCode: undefined,
    prizesName: '',
    prizesClass: 4
  }))
}
function getDefaultPointData() {
  return Array.from({ length: 10 }, (_, index) => ({
    _id: `point_${index + 1}`,
    totalStock: 0,
    state: 1,
    crowdId: -1,
    countLimit: 1,
    prizesType: 3,
    prizeProbability: '',
    prizesClass: 3,
    pointsNum: undefined
  }))
}
</script>

<style lang="scss" scoped>
.point-wrap {
  .point-item {
    margin: 18px 44px 18px 0;
    display: inline-flex;
    align-items: center;
  }
  .ant-input-number {
    width: 100px;
    margin: 0 14px;
  }
}
.coupon-wrap {
  .coupon-item {
    margin: 18px 100px 18px 0;
    display: inline-flex;
    align-items: center;
  }
  .label {
    width: 18px;
  }
  .ant-input {
    width: 180px;
    margin: 0 14px;
  }
  .coupon-input {
    width: 200px;
  }
}
</style>
