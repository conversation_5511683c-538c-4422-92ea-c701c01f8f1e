package com.dz.ms.basic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.core.annotation.Cacheable;
import com.dz.common.core.dto.KeyValueDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.constants.CacheKeys;
import com.dz.ms.basic.dto.TenantInfoDTO;
import com.dz.ms.basic.entity.TenantInfo;
import com.dz.ms.basic.mapper.TenantInfoMapper;
import com.dz.ms.basic.service.TenantInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 租户信息
 * @author: Handy
 * @date:   2022/01/28 15:40
 */
@Service
public class TenantInfoServiceImpl extends ServiceImpl<TenantInfoMapper,TenantInfo> implements TenantInfoService {

    @Resource
    private TenantInfoMapper tenantInfoMapper;
    @Resource
    private RedisService redisService;

    /**
     * 保存租户信息
     * @param param
     */
    @Override
    public Long saveTenant(TenantInfoDTO param) {
        TenantInfo getTenant = tenantInfoMapper.selectOne(new LambdaQueryWrapper<TenantInfo>().eq(TenantInfo::getTenantCode,param.getTenantCode()));
        if(null != getTenant && !getTenant.getId().equals(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"品牌编码已存在");
        }
        TenantInfo tenantInfo = new TenantInfo(param.getId(), param.getTenantCode(), param.getCnName(), param.getEnName(), param.getIntroduce(), param.getLogoUrl(), param.getBigLogo(), param.getMobile(), param.getState());
        if(ParamUtils.isNullOr0Long(tenantInfo.getId())) {
            tenantInfoMapper.insert(tenantInfo);
        }
        else {
            tenantInfoMapper.updateById(tenantInfo);
            redisService.del(CacheKeys.TENANT_BY_CODE+":"+param.getTenantCode());
            redisService.del(CacheKeys.TENANT_BY_ID+":"+param.getId());
        }
        return tenantInfo.getId();
    }

    /**
     * 根据租户编码获取租户信息
     * @param code
     * @return
     */
    @Override
    @Cacheable(prefix = CacheKeys.TENANT_BY_CODE,key = "'#code'")
    public KeyValueDTO getTenantByCode(String code) {
        LambdaQueryWrapper<TenantInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TenantInfo :: getTenantCode,code);
        TenantInfo tenantInfo = tenantInfoMapper.selectOne(wrapper);
        if(null == tenantInfo) {
            return null;
        }
        KeyValueDTO keyValue = new KeyValueDTO(tenantInfo.getId(),tenantInfo.getTenantCode(),tenantInfo.getCnName(),tenantInfo.getLogoUrl());
        return keyValue;
    }

    /**
     * 根据租户ID查询租户信息
     * @param id
     * @return
     */
    @Override
    @Cacheable(prefix = CacheKeys.TENANT_BY_ID,key = "'#id'")
    public KeyValueDTO getTenantById(Long id) {
        TenantInfo tenantInfo = tenantInfoMapper.selectById(id);
        KeyValueDTO keyValue = new KeyValueDTO(tenantInfo.getId(),tenantInfo.getTenantCode(),tenantInfo.getCnName(),tenantInfo.getLogoUrl());
        return keyValue;
    }

    /**
     * 初始化品牌信息
     * @param tenantId
     */
    @Override
    public void initTenant(Long tenantId) {
        /*SecurityContext.setUser(new CurrentUserDTO(tenantId));
        TenantInfo getTenant = tenantInfoMapper.selectById(tenantId);
        SysUserDTO sysUser = new SysUserDTO();
        sysUser.setMobile(getTenant.getMobile());
        sysUser.setUsername(getTenant.getMobile());
        sysUser.setTenantId(getTenant.getId());
        sysUser.setRealname(getTenant.getCnName());
        Result<Long> result = userFeginClient.createSuperAdmin(sysUser);
        if(!result.isSuccess()) {
            throw new BusinessException(ErrorCode.INTERNAL_ERROR,"开通品牌账号出错"+result.getMsg());
        }
        Runnable runnable = () -> modelToSql();
        ThreadPoolUtils.pool.execute(runnable);*/
    }

}
