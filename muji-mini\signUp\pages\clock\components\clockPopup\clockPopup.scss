.clockPopup {
  position: relative;
  background: transparent;
  display: flex;
  flex-direction: column;
  align-items: center;

  &-content {
    overflow: hidden;
    width: 630rpx;
    border-radius: var(--radius);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 46rpx;
    padding-bottom: 39rpx;

    .iconfont {
      width: 79rpx;
      height: 79rpx;
      background-size: 100% 100%;
    }

    .title {
      font-family: MUJIFont2020,
            SourceHanSansCN;
      font-weight: 500;
      font-size: 36rpx;
      color: var(--text-black-color);
      line-height: 49rpx;
      letter-spacing: 3px;
      text-align: ceter;
      margin-top: 27rpx;
    }

    .num {
      font-family: MUJIFont2020,
            SourceHanSansCN;
      font-weight: 300;
      font-size: 24rpx;
      color: var(--text-black-color);
      line-height: 32rpx;
      letter-spacing: 2px;
      text-align: left;
      margin-top: 16rpx;
    }

    .clock-btn {
      margin-top: 66rpx;
    }
  }

  &-close {
    padding-top: 10rpx;
    height: 80rpx;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-closeBox {
    height: 80rpx;
    width: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
