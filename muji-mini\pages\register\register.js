const app = getApp();
import { genderList } from "./../../utils/contants";
import {
  register,
  updateUserInfo,
  getTemplateByType,
  homeGiftList,
  memberqrcodereport,
  addUserMessage,
} from "./../../api/index.js";
// import dayjs from '../../utils/dayjs.min'
const dayjs = require("../../utils/dayjs.min");
const requiredFields = ["username", "mobile"];

Page({
  data: {
    backUrl: "",
    menuButtonTop: app.globalData.menuButtonTop,
    menuButtonBottom: app.globalData.menuButtonBottom,
    info: {
      gender: 0,
    },
    editBirthday: false, // 是否可以修改生日
    isComplate: false,
    isBackHidden: false,
    genderList,
    type: "",
    success: false,
    successContent: `欢迎开启MUJI简约生活路程<br>探索生活百种可能`,
    modalId: 0,
    error: false, // 异常报错
    errContent:
      "您填写的手机号已关联了其他微信账户,\n请确认手机号后重新注册开卡。\n\n如有疑问请致电客服\n400-920-9299[86315]",
    qrcodeId: null,
    qrcodeSn: null,
    isShowEditMobileTips: false,
  },
  onLoad(options) {
    // 注册成功后返回的页面
    let backUrl = options.backUrl ? decodeURIComponent(options.backUrl) : "";
    this.data.backUrl = backUrl;
    // 编辑页面 如果用户未注册 自动变成注册页面(清除type值即可)
    if (app.globalData.userInfo.isMember <= 0) {
      options.type = "";
      this.data.options.type = "";
      this.setData({
        options: this.data.options,
      });
    }
    const { type, qrcodeId, qrcodeSn } = options;
    let info = {};
    // 编辑 从个人信息里面取值
    if (type) {
      info = {
        avatar: app.globalData.userInfo.avatar || "",
        username: app.globalData.userInfo.username || "",
        gender: app.globalData.userInfo.gender || "",
        mobile: app.globalData.userInfo.mobile || "",
        birthday: app.globalData.userInfo.birthday
          ? app.globalData.userInfo.birthday.replace(/-/gi, ".")
          : "",
        province: app.globalData.userInfo.province || "",
        city: app.globalData.userInfo.city || "",
        area: app.globalData.userInfo.area || "",
      };
    }
    this.setData({
      type: type || "",
      info,
      editBirthday: !(app.globalData.userInfo.birthday && type),
    });
    if (type) {
      this.validateForm();
    }
    if (qrcodeId && qrcodeSn) {
      this.setData({
        qrcodeSn,
        qrcodeId,
      });
    }
  },
  // 弹窗关闭
  close() {
    this.setData({
      error: false,
      success: false,
    });
  },
  // 关闭成功
  closeSuccess(e) {
    let { closeBtn } = e.detail;
    this.setData({
      error: false,
      success: false,
    });
    // 关闭的弹窗按钮
    if (closeBtn) {
      if (this.data.backUrl) {
        wx.$mp.redirectTo({
          url: this.data.backUrl,
        });
      } else {
        wx.$mp.navigateBack();
      }
    }
  },

  // 更新用户信息
  update() {
    wx.$mp.track({
      event: "register_edit_info",
    });
    // 数据库有生日的不需要检验  没有的需要校验  兼容老数据
    if (!app.globalData.userInfo.birthday) {
      // 年龄校验
      if (!this.validateBirthday()) return;
    }
    this.setData({
      loading: true,
    });
    let {
      avatar,
      username,
      gender,
      province,
      city,
      area,
      birthday,
    } = this.data.info;

    updateUserInfo({
      avatar,
      username,
      gender,
      province,
      city,
      area,
      birthday: birthday ? birthday.replace(/\./gi, "-") : "",
    })
      .then(async (res) => {
        // 获取最新的用户信息
        await app.getUserInfo();
        let pages = getCurrentPages();
        pages.forEach((currentInstance) => {
          console.log(currentInstance, "666666666666666666666666");
          // 修改tabbar的游客状态
          if (typeof currentInstance.getTabBar === "function") {
            console.log("tabbar5555------------------");
            currentInstance.getTabBar((tabbar) => {
              tabbar.setData({
                visitor: app.globalData.visitor,
              });
            });
          }
        });
        wx.showToast({
          title: "提交成功",
        });
        let time = setTimeout(async () => {
          clearTimeout(time);
          time = null;
          wx.$mp.navigateBack();
          this.setData({
            loading: false,
          });
        }, 2000);
      })
      .catch(() => {
        this.setData({
          loading: false,
        });
      });
  },
  // 注册
  register() {
    // 年龄校验
    if (!this.validateBirthday()) return;
    wx.$mp.track({
      event: "register_submit_click",
    });
    let pages = getCurrentPages();

    // 先订阅 在注册
    app.subscribe("register").then(() => {
      const { info, qrcodeId, qrcodeSn } = this.data;
      const { birthday, ...params } = info;

      params.birthday = birthday ? birthday.replace(/\./gi, "-") : "";
      this.setData({
        loading: true,
      });
      // 如果能拿到邀请人id数据
      if (app.globalData.inviteUserId) {
        params.inviteUserId = app.globalData.inviteUserId;
      }
      register({
        ...params,
        channelOne: app.globalData.channelOne, // 渠道1
        channelTwo: app.globalData.channelTwo, // 渠道2
      })
        .then(async (res) => {
          // 获取最新的用户信息
          await app.getUserInfo();
          // 用户信息推送
          addUserMessage({
            msgCode: ["expireCoupon", "couponAccount", "receiveCard"],
          });
          // giftType 1-新人礼
          let {
            data: { giftType },
          } = await homeGiftList({
            type: 1,
          });
          let memberCode = app.globalData.userInfo.cardNo;
          if (qrcodeId && qrcodeSn && memberCode) {
            memberqrcodereport({
              memberCode,
              qrcodeId,
              qrcodeSn,
            }).then((res) => { });
          }
          if (giftType == 1) {
            // 获取注册的弹窗ID
            let {
              data: { id },
            } = await getTemplateByType({
              //  templateType	模板类型 1常规页面 2系统页面 3定制页面 4弹窗页面
              templateType: 2,
              // pageType	页面类型 0二级自定义页面 1首页 2兑换 3会员码 4生活圈 5更多 10-积分规则 12-无印列表 17-注册成功弹窗
              pageType: 17,
            });
            console.log(id);
            // 成功弹窗
            this.setData({
              modalId: id,
              success: true,
              loading: false,
            });
          } else {
            wx.showToast({
              title: "提交成功",
            });
            let time = setTimeout(async () => {
              clearTimeout(time);
              time = null;
              if (this.data.backUrl) {
                wx.$mp.redirectTo({
                  url: this.data.backUrl,
                });
              } else {
                wx.$mp.navigateBack();
              }
              this.setData({
                loading: false,
              });
            }, 2000);
          }
        })
        .catch((res) => {
          console.log("无法捕获异常", res);
          this.setData({
            loading: false,
          });
          // 加入会员信息异常
          if (res && res.code > 1000) {
            this.setData({
              error: true,
            });
          }
        });
    });
  },
  changeGender(e) {
    const { gender } = e.currentTarget.dataset;
    this.setData({
      ["info.gender"]: gender,
    });
    this.validateForm();
  },
  // 授权手机号
  authMobile(e) {
    wx.$authMobile(e).then(({ success, data }) => {
      if (success) {
        this.setData({
          ["info.mobile"]: data,
        });
      } else {
        this.setData({
          ["info.mobile"]: null,
        });
      }
      this.validateForm();
    });
  },
  birthdayChange(e) {
    const { value } = e.detail;
    this.setData({
      ["info.birthday"]: value.replace(/-/gi, "."),
    });
    this.validateBirthday();
    this.validateForm();
  },
  regionChange(e) {
    const { value } = e.detail;
    console.log("value", value);
    this.setData({
      ["info.province"]: value[0],
      ["info.city"]: value[1],
      ["info.area"]: value[2],
    });
    this.validateForm();
  },
  chooseAvatar(e) {
    const { avatarUrl } = e.detail;
    wx.$uploadFile({
      filePath: avatarUrl,
    }).then(({ data }) => {
      this.setData({
        ["info.avatar"]: data,
      });
      this.validateForm();
    });
  },
  // 校验表单里的必填字段是否都填写
  validateForm() {
    const { info } = this.data;
    const allFilled = requiredFields.every((field) => info[field]);
    this.setData({
      isComplate: allFilled,
    });
  },
  // 校验生日 是否满足14岁
  validateBirthday() {
    let { birthday } = this.data.info;
    // 生日格式 1981.02.02
    if (birthday) {
      birthday = birthday.replace(/\./gi, "/");
      let year = dayjs().diff(dayjs(birthday), "year");
      if (year < 14) {
        wx.showToast({
          title: "年满14周岁方可注册会员",
          icon: "none",
        });
        return false;
      }
    }
    return true;
  },
  changeName(e) {
    const { value } = e.detail;
    this.setData({
      ["info.username"]: value,
    });
    this.validateForm();
  },
  showEditMobileTips() {
    let that = this;
    wx.$mp.navigateTo({
      url: "/pages/editPhone/editPhone",
      events: {
        // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
        changePhone: function (data) {
          that.setData({
            ["info.mobile"]: data,
          });
          console.log(data, "ddddddddddddd");
        },
      },
    });
  },
});
