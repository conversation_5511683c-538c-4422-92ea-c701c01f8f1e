package com.dz.ms.basic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 小程序/公众号模板消息关键字DTO
 * @author: Handy
 * @date:   2023/07/13 17:51
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "小程序/公众号模板消息关键字")
public class MpMsgKeywordDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "消息配置ID")
    private Long msgId;
    @ApiModelProperty(value = "关键词排序")
    private Integer sort;
    @ApiModelProperty(value = "关键词名称")
    private String keyName;
    @ApiModelProperty(value = "关键词类型")
    private String keyType;
    @ApiModelProperty(value = "关键词ID")
    private Integer keyId;
    @ApiModelProperty(value = "是否可编辑内容 0否 1是")
    private Integer isEdit;
    @ApiModelProperty(value = "关键词编辑内容")
    private String editText;

}
