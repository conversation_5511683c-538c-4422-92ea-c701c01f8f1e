package com.dz.ms.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.ms.product.dto.SuperscriptDTO;
import com.dz.ms.product.entity.Superscript;

import java.util.List;

/**
 * 商品角标管理接口
 *
 * @author: LiinNs
 * @date: 2024/11/20 11:35
 */
public interface SuperscriptService extends IService<Superscript> {

    /**
     * 分页查询商品角标管理
     *
     * @param param
     * @return PageInfo<SuperscriptDTO>
     */
    public PageInfo<SuperscriptDTO> getSuperscriptList(SuperscriptDTO param);

    /**
     * 查询商品角标
     *
     * @param param
     * @return List < SuperscriptDTO>
     */
    List<SuperscriptDTO> getSuperscriptNoPageList(SuperscriptDTO param);

    /**
     * 根据ID查询商品角标管理
     *
     * @param id
     * @return SuperscriptDTO
     */
    public SuperscriptDTO getSuperscriptById(Long id);

    /**
     * 保存商品角标管理
     *
     * @param param
     * @return Long
     */
    public Long saveSuperscript(SuperscriptDTO param);

    /**
     * 根据ID删除商品角标管理
     *
     * @param param
     */
    public void deleteSuperscriptById(IdCodeDTO param);

}
