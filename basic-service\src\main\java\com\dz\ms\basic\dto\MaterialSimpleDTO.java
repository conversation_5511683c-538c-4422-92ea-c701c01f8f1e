package com.dz.ms.basic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 素材地址信息DTO
 * @author: Handy
 * @date:   2023/05/09 23:39
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "素材地址信息")
public class MaterialSimpleDTO {

    @ApiModelProperty(value = "素材ID")
    private Long id;
    @ApiModelProperty(value = "素材地址")
    private String url;
    @ApiModelProperty(value = "视频封面")
    private String poster;

}
