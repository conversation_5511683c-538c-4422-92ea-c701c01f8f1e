import {
  // getTemplateByType,
  getShelfId,
  getProductList,
  getBannerList,
  getUserPoint,
  getCartCount,
  getShelfTagList,
} from '../../api/index'

// import dayjs from '../../utils/dayjs.min'
const dayjs = require('../../utils/dayjs.min')
const app = getApp()

Page({
  data: {
    rightFixedWrapVisible: false,
    loading: true,
    info: {},
    pageSize: 10,
    pageNum: 1,
    promotionList: [], // 推广位数据
    productList: [],
    currentShelfId: undefined,
    bannerContent: '',
    topHeight: app.globalData.navBarHeight, // 包括导航条高度
    navHeight: app.globalData.navBarHeight + app.globalData.statusBarHeight, // 导航高度+胶囊按钮的高度
    menuBottom: app.globalData.menuBottom,
    filterData: {}, // 筛选数据
    currentPoint: 0,
    count: 0,
    cartCount: 0,
    count: 0,
    showPointsDialog: false, // 积分兑换提醒
    isKeepReminds: false,
    userInfo: app.globalData.userInfo,
    isShowMyPoint: true, // 是否展示我的积分
    creditHeight: 0,
    hasMore: false,
    stickyElement: {
      top: 0
    },
    isFixedTop: false,
    showFilterDialog: false,
    stickyElementScrollTop: 0,
    showIcon: false,
    lastScrollTop: 0,// 添加上次记录用于防抖
  },

  async onLoad(options) {
    this.setData({
      loading: true,
    })

  },
  async onShow() {
  
    this.rightFixedWrapVisibleHandler()
    if (typeof this.getTabBar === 'function' &&
      this.getTabBar()) {
      this.getTabBar().setData({
        selected: 3,
        isBirthday: app.globalData.isBirthday
      })
    }
    this.getCartCount();
    this.getPoint();
    await this.checkShelfIdUpdate();
    this.checkReminder();
    this.getStickyInfo()
    await app.getUserInfo()
    wx.createSelectorQuery().select('#stickyElement').boundingClientRect((rect) => {
      if (rect) {
        this.setData({
          stickyElementScrollTop: rect.top - this.data.navHeight
        })
      }
    }).exec()
  },
  rightFixedWrapVisibleHandler(e) {
    // const prevPagePath = wx.$mp.localStorage.getStorageSync('prevPagePath')
    // this.setData({ rightFixedWrapVisible: prevPagePath.includes('/pages/interactiveTask/interactiveTask') })
    this.setData({
      rightFixedWrapVisible: this.data.options.rightFixedWrapVisible === 'true',
      showIcon: !!this.data.options.showIcon
    })
  },
  goInteractiveTask() {
    wx.$mp.navigateTo({
      url: '/pages/interactiveTask/interactiveTask'
    })
  },
  updateTabList() {
    this.getStickyInfo()
  },
  getStickyInfo() {
    wx.createSelectorQuery().select('#stickyElement').boundingClientRect((rect) => {
      if (rect) {
        // 获取吸顶元素
        this.setData({
          stickyElement: rect,
        })
      }
    }).exec()
  },
  // 跳转购物车
  goCart() {
    if (app.ifRegister()) {
      wx.$mp.navigateTo({
        url: '/pages/cart/cart'
      })
    }
  },
  getList(refreshProductListWhenKeepPageScroll = false) {
    let {
      pageSize,
      currentShelfId,
      pageNum,
      filterData,
      productList
    } = this.data

    if (refreshProductListWhenKeepPageScroll) {
      pageSize = this.data.productList.length
      pageNum = 1
    } else {
      this.setData({
        loading: pageNum === 1 ? true : false
      })
    }
    return getProductList({
      pageSize,
      shelfId: currentShelfId,
      pageNum,
      ...filterData
    }).then((res) => {
      const {
        data
      } = res
      data.list = data.list || []
      if (refreshProductListWhenKeepPageScroll) {
        this.setData({
          count: data?.count || 0,
          productList: data.list
        })
      } else {
        this.setData({
          count: data?.count || 0,
          productList: pageNum === 1 ? data.list : productList.concat(data.list),
          loading: false,
          hasMore: data?.count ? pageNum * pageSize < data.count : false,
          pageNum
        })
      }
      return data
    })
  },
  async handleFilter(e) {
    const {
      filterValue
    } = e.detail
    this.setData({
      filterData: filterValue
    })
    this.data.pageNum = 1
    this.data.productList = []

    await this.getList()
    // 这里重新处理定位的
    // wx.pageScrollTo({
    //   scrollTop: this.data.stickyElement.top - this.data.navHeight,
    //   duration: 300
    // })
  },
  async getPoint() {
    const res = await getUserPoint();
    if (res.code === 0) {
      this.setData({
        currentPoint: res.data.pointsNum,
      })
    }
  },
  getCartCount() {
    getCartCount().then(res => {
      this.setData({
        cartCount: res?.data || 0,
      })
    })
  },
  async getShelfId() {
    const res = await getShelfId();
    let res1
    if (res?.data && res.data.id) {
      this.setData({
        currentShelfId: res.data.id,
      })
      await this.getTab();
      res1 = this.getList();
      this.getPoromotion();
    } else {
      this.setData({
        promotionList: [], // 推广位数据
        productList: [],
        currentShelfId: undefined,
        bannerContent: '',
        loading: false,
        tabList: [],
      })
    }
    await res1
  },
  handleNoRemind() {
    const {
      isKeepReminds
    } = this.data;
    this.setData({
      isKeepReminds: !isKeepReminds,
    })
  },
  closeRemind() {
    const {
      isKeepReminds
    } = this.data;
    const value = {};
    value.date = dayjs().format('YYYY-MM-DD');
    value.isKeepReminds = isKeepReminds;
    wx.setStorageSync('pointsRemind', value)
    this.setData({
      showPointsDialog: false,
    })
  },
  onTapCart: app.debounce(function () {
    wx.$mp.track({
      event: 'shop_cart'
    })
    if (app.ifRegister()) {
      wx.$mp.navigateTo({
        url: '/pages/cart/cart'
      })
    }
  }),
  checkReminder() {
    const remindsData = wx.getStorageSync('pointsRemind');
    const today = dayjs();
    if ((!today.isSame(dayjs(remindsData.date), 'day') && !remindsData.isKeepReminds) || !remindsData) {
      let time = setTimeout(() => {
        clearTimeout(time)
        // 没有全局弹窗的时候弹出
        let hasShow = Object.values(this.data.overallModal).some(item => !!item)
        //  没有全局弹窗
        if (!hasShow) {
          this.setData({
            showPointsDialog: true,
          })
        }
      }, 1000);
    }
  },
  setTodayReminds() {
    const {
      isKeepReminds
    } = this.data;
    const value = {};
    value.date = dayjs().format('YYYY-MM-DD');
    value.isKeepReminds = isKeepReminds;
    wx.setStorageSync('pointsRemind', value)
    this.setData({
      showPointsDialog: false,
    })
  },
  async getPoromotion() {
    const {
      currentShelfId
    } = this.data;
    if (currentShelfId) {
      const res = await getBannerList({
        shelfId: currentShelfId,
      });
      if (res.data) {
        this.setData({
          bannerContent: res.data.content,
        })
      } else {
        this.setData({
          bannerContent: '',
        })
      }
    }
  },
  async bannerChange(e) {
    console.log(e, ';777777');
      if (this.data.tabList.length) {
        let filterData = {}
        // 会员节专享标签  如果是从页面跳转过来默认选中

        let obj = this.data.tabList.find(item => item.tagId === e.detail.tagId)
        filterData = {
          oneTagId: obj?.tagId
        }

        this.setData({
          filterData
        })
        this.data.pageNum = 1
        this.data.productList = []
        await this.getList()
      } 
    
  },
  async getTab() {
    const {
      currentShelfId
    } = this.data;
    if (currentShelfId) {
      const res = await getShelfTagList({
        shelfId: currentShelfId,
      });
      if (res.data) {
        let filterData = {}
        // 会员节专享标签  如果是从页面跳转过来默认选中
        if (this.data.showIcon) {
          let obj = res.data.find(item => item.tagName == '良友节专区')
          filterData = {
            oneTagId: obj?.tagId
          }
        }

        this.setData({
          tabList: res.data,
          filterData
        })
      } else {
        this.setData({
          tabList: [],
        })
      }
    }
  },
  // 滚到顶部展示
  handleScrollUpper() {
    this.setData({
      isShowMyPoint: true,
    });
  },
  // 点击筛选按钮关闭
  handleHiddenPoint() {
    this.setData({
      isShowMyPoint: false
    });
  },
  getMore() {
    const {
      hasMore,
      pageNum
    } = this.data;
    const nextPage = pageNum + 1;
    if (hasMore) {
      this.setData({
        pageNum: nextPage
      })

      this.getList()
    }
  },
  onShareAppMessage(options) {
    return {
      title: '每月会员福利上新，点击查看详情',
      path: `/pages/life/life?inviteUserId=${app.globalData.userInfo.id}`,
      imageUrl: `${wx.$config.ossImg}/share/shop.jpg`
    }
  },
  goSearch() {
    wx.$mp.track({
      event: 'shop_seach'
    })
    wx.$mp.navigateTo({
      url: '/pages/productSearch/productSearch',
    })
  },

  onPageScroll(e) {
    const isSatisfy = parseInt(Math.max(0, e.scrollTop)) > parseInt(this.data.stickyElementScrollTop)
    console.log(parseInt(Math.max(0, e.scrollTop)), parseInt(this.data.stickyElementScrollTop), 'stickyElementScrollTopstickyElementScrollTop');
    if (this.data.isFixedTop === isSatisfy) {
      this.getStickyInfo()
      return
    }
    this.setData({
      isFixedTop: isSatisfy
    })
  },
  async toggleSticky(e) {
    const query = wx.createSelectorQuery();
    const durationTime = 300;
    query.select('#stickyElement') // 获取目标元素
      .boundingClientRect((rect) => {
        // 获取元素的位置
        console.log( rect.top,this.data.navHeight, rect.top - this.data.navHeight,!this.data.isFixedTop, rect.top - this.data.navHeight > 0 && !this.data.isFixedTop, 'rect.top - this.data.navHeight > 0 && !this.data.isFixedTop');
        if ((rect.top - this.data.navHeight) > 0 && !this.data.isFixedTop) {
          // 如果元素没有到达顶部，滚动到元素的位置
          wx.pageScrollTo({
            scrollTop: this.data.stickyElementScrollTop,
            duration: durationTime, // 动画持续时间
            complete: () => {
              let timer = setTimeout(() => {
                clearTimeout(timer);
                this.getStickyInfo();
                this.setData({
                  showFilterDialog: e.detail.showFilter
                });
                this.showFilterHandler(e.detail.showFilter);
              }, durationTime);
              // this.setData({
              //   showFilterDialog: e.detail.showFilter
              // });
              // this.showFilterHandler(e.detail.showFilter)
            }
          })
        } else {
          // 如果元素已经在顶部，则将其设置为吸顶
          this.setData({
            isFixedTop: true,
            showFilterDialog: e.detail.showFilter,
          });
          this.showFilterHandler(e.detail.showFilter)
        }
      }).exec();
  },
  showFilterHandler(showFilter) {
    if (showFilter) {
      this.onPopupShowDisablePageScroll() // 弹窗展示禁止页面滚动
    } else {
      this.onPopupHideEnablePageScroll() // 弹窗隐藏打开页面滚动
    }
  },
  closeFilterDialog() {
    this.setData({
      showFilterDialog: false,
    })
  },

  // 页面触底事件，触发加载更多数据
  onReachBottom: function () {
    this.getMore();
  },
  // 修改方法：检查并更新 shelfId
  async checkShelfIdUpdate() {
    const oldShelfId = this.data.currentShelfId;
    await this.getCurrentShelfId();
    const prevPagePath = wx.$mp.localStorage.getStorageSync('prevPagePath')
    const prevPageIsProductDetailPage = prevPagePath.includes('/pages/integralDetails/integralDetails')
    if (!this.data.currentShelfId || oldShelfId !== this.data.currentShelfId || !prevPageIsProductDetailPage) {
      // 货架id不存在，需要重新获取数据。
      // 货架id变更，需要重新获取数据。
      // 上一页不是商详页，需要重新获取数据。
      this.data.pageNum = 1;
      this.data.productList = [];

      await this.getShelfId();
      if (this.data.scrollTop !== 0 && !this.data.showFilterDialog) {
        wx.pageScrollTo({
          scrollTop: 0,
          duration: 300,
          complete: () => this.getStickyInfo()
        })
      }
    } else {
      // 返回到当前页时，没重新拉取商品，固保留了位置，但是数据可能已经变更，需要把整体页面的商品数据重刷一下。
      // 数据多了，性能会有问题。如果只刷新被点击的那一项的数据，性能会好很多。

      this.getList(true)
    }
  },
  // 新增方法：只获取 shelfId
  async getCurrentShelfId() {
    const res = await getShelfId();
    if (res?.data && res.data.id) {
      this.setData({
        currentShelfId: res.data.id,
      })
    } else {
      this.setData({
        currentShelfId: undefined,
      })
    }
  },
  goGift() {
    wx.$mp.navigateTo({
      url: '/member/pages/lottery/lottery',
    })
  }
})
