package com.dz.common.core.dto.user;
import java.util.Date;
import java.util.List;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 人群包DTO
 * @author: yibo
 * @date:   2024/11/18 13:53
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "人群包")
public class CrowdDTO extends BaseDTO {

    @ApiModelProperty(value = "主键id")
    private Long id;
    @ApiModelProperty(value = "主键id")
    private Long crowdId;
    @ApiModelProperty(value = "人群包名称")
    private String crowdName;
    @ApiModelProperty(value = "人群包类型 0条件规则 1使用人群包 2导入人群包")
    private Integer crowdType;
    @ApiModelProperty(value = "启停状态 0启用 1停用")
    private Integer crowdStatus;
    @ApiModelProperty(value = "类型 0时间段 1永久启用")
    private Integer timeType;
    @ApiModelProperty(value = "人群包可用开始时间  (为空则永久可用)")
    private Date startTime;
    @ApiModelProperty(value = "人群包可用结束时间  (为空则永久可用)")
    private Date endTime;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    @ApiModelProperty(value = "修改人")
    private Long modifier;
    @ApiModelProperty(value = "租户id")
    private Long tenantId;
    @ApiModelProperty(value = "人群包纵向条件")
    private List<CrowdConditionDTO> crowdConditionList;
    @ApiModelProperty(value = "人群包用户")
    private CrowdImportResultDTO crowdImportResultDTO;

    private Integer groupType;
    private Long groupId;
    @ApiModelProperty(value = "是否更新 1:更新")
    private Integer isUpdate;
}
