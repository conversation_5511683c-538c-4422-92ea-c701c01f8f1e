package com.dz.ms.sales.service;


import com.dz.common.core.dto.user.crm.MuJiMemberOrder;
import com.dz.ms.sales.dto.CampaignBuyerDTO;
import com.dz.ms.sales.dto.CampaignDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/30
 */
public interface CampaignService {

    Boolean editCampaign(CampaignDTO param);

    List<CampaignDTO> getCampaignList();

    /**
     * 查询当前用户的消费订单是否匹配活动
     *
     * @param uid
     * @return
     */
    CampaignDTO getCurrentCampaignOrderMatch(Long uid);

    /**
     * 根据活动编码查询活动详情（缓存）
     *
     * @param campaignCode
     * @return
     */
    CampaignDTO getCampaignInfoCache(String campaignCode);

    /**
     * 根据活动编码查询活动明细（缓存）
     *
     * @param campaignCode
     * @return
     */
    CampaignDTO getCampaignInfoDetailCache(String campaignCode);

    /**
     * 删除当前用户的活动数据
     *
     * @param uid
     * @return
     */
    Boolean delCurrentUserCampaignData(Long uid);

    /**
     * 查询是否存在
     *
     * @param mobile
     * @return
     */
    Long getCampaignUser(String campaignCode, String mobile);

    List<MuJiMemberOrder> fetchUserOrders(String cardNo, CampaignBuyerDTO campaignBuyer);

    boolean checkIfUserPurchasedRequiredItem(String cardNo, List<MuJiMemberOrder> muJiMemberOrderList, String requiredSku);


    void CampaignBuyerJob(String campaignCode);

    void CampaignBuyerPushMsgJob(String campaignCode);
}
