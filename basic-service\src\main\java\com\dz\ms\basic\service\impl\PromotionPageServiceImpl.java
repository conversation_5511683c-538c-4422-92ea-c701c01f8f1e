package com.dz.ms.basic.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.annotation.Cacheable;
import com.dz.common.core.dto.user.SysUserDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.user.SysUserFeginClient;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.DateUtils;
import com.dz.ms.basic.dto.*;
import com.dz.ms.basic.entity.*;
import com.dz.ms.basic.mapper.MiniappPathMapper;
import com.dz.ms.basic.mapper.PromotionChannelMapper;
import com.dz.ms.basic.mapper.PromotionPageLogMapper;
import com.dz.ms.basic.mapper.PromotionPageMapper;
import com.dz.ms.basic.service.MiniappTemplateService;
import com.dz.ms.basic.service.PromotionPageService;
import com.dz.ms.basic.service.WechatApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.dz.ms.basic.constants.CacheKeys;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 页面推广
 * @author: Handy
 * @date:   2022/02/04 22:59
 */
@Service
@Slf4j
public class PromotionPageServiceImpl extends ServiceImpl<PromotionPageMapper, PromotionPage> implements PromotionPageService {

    @Resource
    private PromotionPageMapper promotionPageMapper;
    @Resource
    private PromotionPageLogMapper promotionPageLogMapper;
    @Resource
    private MiniappTemplateService miniappTemplateService;
    @Resource
    private MiniappPathMapper miniappPathMapper;
    @Resource
    private PromotionChannelMapper promotionChannelMapper;
    @Autowired
    private WechatApiService wechatApiService;
    @Resource
    private SysUserFeginClient sysUserFeginClient;

    @Override
    public List<PromotionPageDTO> getPromotionPageList(Long templateId) {
        MiniappTemplate miniappTemplate = miniappTemplateService.getById(templateId);
        if(miniappTemplate == null) {
            throw new BusinessException("小程序页面模板不存在");
        }
        MiniappPath path = miniappPathMapper.selectById(miniappTemplate.getPathId());
        if(path == null) {
            throw new BusinessException("小程序页面路径不存在");
        }
        List<PromotionPageDTO> list = promotionPageMapper.getPromotionPageList(templateId);
        if (list == null || list.size() == 0) {
            //创建一条默认记录
            Base64.Encoder encoder = Base64.getEncoder();
            PromotionPage promotionPage = new PromotionPage();
            byte[] qrcode = miniappTemplateService.getMiniappQrcode(path.getPath(),"id="+templateId,280,3,2, null);
            promotionPage.setTId(templateId);
            promotionPage.setQrCode(encoder.encodeToString(qrcode));
            promotionPage.setModified(new Date());
            promotionPage.setCreateTime(new Date());
            UrlLinkDto url= wechatApiService.getMiniappUrlLink(path.getPath(),"id="+templateId,1,"release");
            promotionPage.setLinkUrl(url.getUrl_link());
            promotionPage.setEndTime(new Date(url.getExpire_time()));
            PromotionPageLog log1 = BeanCopierUtils.convertObject(promotionPage, PromotionPageLog.class);
            log1.setCreateTime(new Date());
            promotionPageLogMapper.insert(log1);
            promotionPageMapper.insert(promotionPage);
        }
        List<PromotionPageDTO> pageList = promotionPageMapper.getPromotionPageList(templateId);
        if(!CollectionUtils.isEmpty(pageList)){
            List<Long> ids = pageList.stream().map(PromotionPageDTO::getCreator).distinct().collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
            List<SysUserDTO> creatorUsers = sysUserFeginClient.getUserByIds(ids).getData();
            log.info("用户信息为:{}", JSON.toJSONString(creatorUsers));
            Map<Long,SysUserDTO> creatorMap = creatorUsers.stream().collect(Collectors.toMap(SysUserDTO :: getId, su -> su));
            log.info("转换后用户信息为:{}", JSON.toJSONString(creatorMap));
            pageList.forEach(channel->{
                channel.setCreatorName(creatorMap.containsKey(channel.getCreator()) ? creatorMap.get(channel.getCreator()).getRealname() : "");
            });
            for (PromotionPageDTO promotionPageDTO : pageList) {
                if (promotionPageDTO.getTwopId() == null) {
                    promotionPageDTO.setTowChannelName("默认");
                    promotionPageDTO.setOneChannelName("默认");
                    promotionPageDTO.setPagePath(path.getPath()+"?id=" + templateId);
                }else {
                    PromotionChannel promotionChannel = promotionChannelMapper.selectById(promotionPageDTO.getTwopId());
                    if (promotionChannel == null) {
                        throw new BusinessException("推广渠道不存在");
                    }
                    promotionPageDTO.setTowChannelName(promotionChannel.getChannelName());
                    promotionPageDTO.setTowChannelParam(promotionChannel.getChannelParam());
                    PromotionChannel parentChannel = promotionChannelMapper.selectById(promotionChannel.getParentId());
                    promotionPageDTO.setOneChannelName(parentChannel.getChannelName());
                    promotionPageDTO.setOneChannelName(parentChannel.getChannelParam());

                    promotionPageDTO.setPagePath(path.getPath() + "?chid=" + parentChannel.getChannelParam()+"&cpid="+ promotionChannel.getChannelParam() +"&id=" + templateId);
                }
            }
        }
        return pageList;
    }

    @Override
    public Integer savePromotionPage(PromotionPageAddDTO param) {
        List<Long> ids = param.getChannelIds();
        //根据ID获取页面路径
        MiniappTemplate miniappTemplate = miniappTemplateService.getById(param.getId());
        if(miniappTemplate == null) {
            throw new BusinessException("小程序页面模板不存在");
        }
        MiniappPath path = miniappPathMapper.selectById(miniappTemplate.getPathId());
        if(path == null) {
            throw new BusinessException("小程序页面路径不存在");
        }
        for (Long id : ids) {
            //二级渠道获取
            PromotionChannel promotionChannel = promotionChannelMapper.selectById(id);
            if(promotionChannel == null) {
                throw new BusinessException("推广渠道不存在");
            }
            //获取一级渠道
            PromotionChannel parentChannel = promotionChannelMapper.selectById(promotionChannel.getParentId());
            if(parentChannel == null) {
                throw new BusinessException("推广渠道不存在");
            }
            //获取二维码
            byte[] qrcode = miniappTemplateService.getMiniappQrcode(path.getPath(),"paramid=" + promotionChannel.getId()+"&id="+param.getId(),280,3,2,null);
            Base64.Encoder encoder = Base64.getEncoder();
            PromotionPage promotionPage = new PromotionPage();
            promotionPage.setTId(param.getId());
            promotionPage.setPId(id);
            promotionPage.setQrCode(encoder.encodeToString(qrcode));
            promotionPage.setModified(new Date());
            UrlLinkDto url= wechatApiService.getMiniappUrlLink(path.getPath(),"chid=" + parentChannel.getChannelParam()+"&cpid="+ promotionChannel.getChannelParam()+"&id="+param.getId(),1,"release");
            promotionPage.setLinkUrl(url.getUrl_link());
//            promotionPage.setEndTime(new Date(url.getExpire_time()));
            promotionPage.setEndTime(new Date());
            PromotionPageLog log1 = BeanCopierUtils.convertObject(promotionPage, PromotionPageLog.class);
            log1.setCreateTime(new Date());
            promotionPageLogMapper.insert(log1);
            promotionPage.setCreateTime(new Date());
            promotionPageMapper.insert(promotionPage);
        }
        return 1;
    }

    @Override
    public Long updatePromotionPageUrl(PromotionPageUpdateDTO param) {
        PromotionPage promotionPage = promotionPageMapper.selectById(param.getId());
        if(promotionPage == null) {
            throw new BusinessException("推广页面不存在");
        }
        Long day = DateUtils.getBetween(promotionPage.getEndTime(), new Date());
        if (day < 20) {
            return param.getId();
        }
        //获取页面模版
        MiniappTemplate miniappTemplate = miniappTemplateService.getById(promotionPage.getTId());
        MiniappPath path = miniappPathMapper.selectById(miniappTemplate.getPathId());
        if (path == null) {
            throw new BusinessException("小程序页面路径不存在");
        }
        if (promotionPage.getPId()!= null) {
            PromotionChannel promotionChannel = promotionChannelMapper.selectById(promotionPage.getPId());
            //获取一级渠道
            PromotionChannel parentChannel = promotionChannelMapper.selectById(promotionChannel.getParentId());
            if(parentChannel == null) {
                throw new BusinessException("推广渠道不存在");
            }
            UrlLinkDto url = wechatApiService.getMiniappUrlLink(param.getPath(), "chid=" + parentChannel.getChannelParam()+"&cpid="+ promotionChannel.getChannelParam()+"&id="+param.getId(), 1, "release");
            promotionPage.setLinkUrl(url.getUrl_link());
            promotionPage.setEndTime(new Date(url.getExpire_time()));
            promotionPage.setModified(new Date(url.getExpire_time()));
            PromotionPageLog log1 = BeanCopierUtils.convertObject(promotionPage, PromotionPageLog.class);
            log1.setId(null);
            log1.setCreateTime(new Date());
            promotionPageLogMapper.insert(log1);
            promotionPageMapper.updateById(promotionPage);
            return param.getId();
        }else {
            UrlLinkDto url= wechatApiService.getMiniappUrlLink(path.getPath(),"id="+param.getId(),1,"release");
            promotionPage.setLinkUrl(url.getUrl_link());
            promotionPage.setEndTime(new Date(url.getExpire_time()));
            promotionPage.setModified(new Date(url.getExpire_time()));
            PromotionPageLog log1 = BeanCopierUtils.convertObject(promotionPage, PromotionPageLog.class);
            log1.setId(null);
            log1.setCreateTime(new Date());
            promotionPageLogMapper.insert(log1);
            promotionPageMapper.updateById(promotionPage);
            return param.getId();
        }
    }

    @Override
    public void updatePromotionPage(PromotionPageUpdateDTO param) {
        MiniappTemplate miniappTemplate = miniappTemplateService.getById(param.getId());
        if(miniappTemplate == null) {
            throw new BusinessException("小程序页面模板不存在");
        }
        MiniappPath path = miniappPathMapper.selectById(miniappTemplate.getPathId());
        if(path == null) {
            throw new BusinessException("小程序页面路径不存在");
        }
        List<PromotionPage> list = promotionPageMapper.selectList(new LambdaQueryWrapper<PromotionPage>().eq(PromotionPage::getTId, param.getId()));
        for (PromotionPage page : list) {
            if (page.getPId()== null){
                Base64.Encoder encoder = Base64.getEncoder();
                byte[] qrcode = miniappTemplateService.getMiniappQrcode(path.getPath(),"id="+param.getId(),280,3,2,null);
                page.setQrCode(encoder.encodeToString(qrcode));
                UrlLinkDto url= wechatApiService.getMiniappUrlLink(path.getPath(),"id="+param.getId(),1,"release");
                page.setLinkUrl(url.getUrl_link());
                page.setEndTime(new Date(url.getExpire_time()));
                PromotionPageLog log1 = BeanCopierUtils.convertObject(page, PromotionPageLog.class);
                log1.setCreateTime(new Date());
                log1.setId(null);
                promotionPageLogMapper.insert(log1);
                promotionPageMapper.updateById(page);
            }else {
                PromotionChannel promotionChannel = promotionChannelMapper.selectById(page.getPId());
                //获取一级渠道
                PromotionChannel parentChannel = promotionChannelMapper.selectById(promotionChannel.getParentId());
                if(parentChannel == null) {
                    throw new BusinessException("推广渠道不存在");
                }
                //获取二维码
                byte[] qrcode = miniappTemplateService.getMiniappQrcode(path.getPath(), "paramid=" + promotionChannel.getId()+"&id="+param.getId(), 280, 3, 2,null);
                Base64.Encoder encoder = Base64.getEncoder();
                page.setQrCode(encoder.encodeToString(qrcode));
                UrlLinkDto url = wechatApiService.getMiniappUrlLink(path.getPath(), "chid=" + parentChannel.getChannelParam()+"&cpid="+ promotionChannel.getChannelParam() + "&id=" + param.getId(), 1, "release");
                page.setLinkUrl(url.getUrl_link());
                page.setEndTime(new Date(url.getExpire_time()));
                PromotionPageLog log1 = BeanCopierUtils.convertObject(page, PromotionPageLog.class);
                log1.setId(null);
                log1.setCreateTime(new Date());
                promotionPageLogMapper.insert(log1);
                promotionPageMapper.updateById(page);
            }
        }
    }

    @Override
    public List<Long> getChannelIds(Long templateId) {
        List<PromotionPage> list = promotionPageMapper.selectList(new LambdaQueryWrapper<PromotionPage>().eq(PromotionPage::getTId, templateId));
        if (list != null && list.size() > 0) {
            return list.stream().map(PromotionPage::getPId).collect(Collectors.toList());
        }
        return List.of();
    }

    @Override
    @Cacheable(prefix = CacheKeys.PROMOTION_PARAM_BYID,key = "'#paramId'")
    public String getParamByParamId(Long paramId) {
        PromotionChannel promotionChannel = promotionChannelMapper.selectById(paramId);
        //获取一级渠道
        PromotionChannel parentChannel = promotionChannelMapper.selectById(promotionChannel.getParentId());
        if(parentChannel == null) {
            throw new BusinessException("推广渠道不存在");
        }
        String params = "chid=" + parentChannel.getChannelParam()+"&cpid="+ promotionChannel.getChannelParam();
        return params;
    }
}
