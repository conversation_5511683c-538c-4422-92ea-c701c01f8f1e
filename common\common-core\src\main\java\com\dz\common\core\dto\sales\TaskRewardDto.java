package com.dz.common.core.dto.sales;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * 任务奖励列表
 */
@Getter
@Setter
@NoArgsConstructor
public class TaskRewardDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务id")
    private Long taskId;
    @ApiModelProperty(value = "奖励类型：1优惠券，2积分")
    private Integer rewardType;
    @ApiModelProperty(value = "奖励券列表id，多个用英文逗号隔开")
    private String couponIds;
    @ApiModelProperty(value = "奖励券列表名称")
    private String couponNames;
    @ApiModelProperty(value = "奖励积分数量")
    private Integer pointsNum;
    @ApiModelProperty(value = "奖励顺序")
    private Integer sortNum;
    @ApiModelProperty(value = "渠道")
    private Long tenantId;
}
