package com.dz.ms.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.user.*;
import com.dz.ms.user.entity.Store;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * 门店接口
 * @author: yibo
 * @date:   2024/11/19 17:19
 */
public interface StoreService extends IService<Store> {

	/**
     * 分页查询门店
     * @param param
     * @return PageInfo<StoreDTO>
     */
    public PageInfo<StoreDTO> getStoreList(StoreDTO param);

    /**
     * 根据ID查询门店
     *
     * @param id
     * @return StoreDTO
     */
    public StoreDTO getStoreById(Long id) throws ParseException;

    /**
     * 保存门店
     * @param param
     * @return Long
     */
    public Long saveStore(StoreDTO param);

    /**
     * 根据ID删除门店
     * @param param
     */
    public void deleteStoreById(IdCodeDTO param);

    List<ServeDTO> getStoreServeList(Long storeId);

    void updateStoreServe(List<StoreServeDTO> param);

    void updateStore(StoreDTO param) throws ParseException;

    void importStoreServe(List<StoreServeImportExcelDTO> excelDTOList);

    PageInfo<StoreDTO> getAppStoreList(StoreDTO param);

    StoreCityDTO cityListNew();

    String cityByLonLat(String longitude, String latitude);

    void getSftpFile() throws IOException;

    List<StoreDTO> getLongitudeLatitude();

    List<String> getStoreProvince();

    List<String> getStoreCity();

    List<String> cityList();


    /**
     * 根据ID查询门店
     *
     * @param storeSn
     * @return String
     */
    public String getStoreByStoreSn(String storeSn);
}
