<template>
  <layout>
    <template v-slot:header>
      <searchForm :formParams="searchFields" :disabled="!$hasPermission('normal:search')" @cancel="whenClickReset" @ok="whenClickSearch">
        <a-form-item name="templateName">
          <a-input placeholder="页面名称" v-model:value="searchFields.templateName" allowClear></a-input>
        </a-form-item>
        <a-form-item name="publish">
          <a-select v-model:value="searchFields.publish" placeholder="页面状态" allowClear>
            <a-select-option :value="1">已发布</a-select-option>
            <a-select-option :value="0">未发布</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item name="groupName">
          <a-input placeholder="页面组名称" v-model:value="searchFields.groupName" allowClear></a-input>
        </a-form-item>
      </searchForm>
    </template>
    <template v-slot:topLeft>
      <a-space>
        已选 {{ selectedRowKeys.length }} 条
      </a-space>
    </template>
    <template v-slot:topRight>
      <a-space>
        <!-- <a-button>移动到组</a-button> -->
        <a-button type="primary" @click="addEdit('',0)" :disabled="!$hasPermission('normal:add')">新建页面</a-button>
      </a-space>
    </template>
    <template v-slot="{ height }">
      <a-table :row-selection="rowSelection" :indentSize="20" row-key="id" :scroll="{ scrollToFirstRowOnChange: true, y: height - 88, x: '100%' }" :dataSource="dataSource" :columns="thisFields.tableHeader" :pagination="pagination" :loading="loading" @change="whenPaginationChange">
        <template #bodyCell="{ record, index, column}">
          <template v-if="column.dataIndex === 'index'">{{ (current - 1) * pageSize + 1 + index }}</template>
          <template v-if="column.dataIndex === 'templateName'">
            {{ record.templateName}}
            <div v-if="record.pageType>0">
              <a-tag>{{thisFields.navList[record.pageType-1].text}}</a-tag>
            </div>
          </template>
          <template v-if="column.dataIndex === 'publish'">{{['未发布','已发布'][record.publish]}}</template>
          <template v-if="column.dataIndex === 'action'">
            <a-button type="link" @click="addEdit(record,1)" :disabled="!$hasPermission('normal:edit')">编辑</a-button>
            <template v-if="record.publish&&record.pageType<=0">
              <a-divider type="vertical" />
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <template v-for="(item,index) in thisFields.navList" :key="index+1+''">
                      <!-- 积分商城不能选择 -->
                      <a-menu-item v-if="item.show">
                        <a-popconfirm @confirm="changePath(record,index+1)" :disabled="index==3">
                          <template #title>
                            <div>是否确定将该页面设置为{{item.text}}页面吗？</div>
                            <div>设置后该页面和原{{item.text}}的推广页面路径、</div>
                            <div>链接和二维码会更新？</div>
                          </template>
                          <a-button type="link" :disabled="index==3">{{item.text}}</a-button>
                        </a-popconfirm>
                      </a-menu-item>
                    </template>
                  </a-menu>
                </template>
                <a-button type="link" :disabled="!$hasPermission('normal:nav')">
                  设为导航页
                  <DownOutlined />
                </a-button>
              </a-dropdown>
            </template>
            <template v-if="record.publish">
              <a-divider type="vertical" />
              <a-button @click="extendChange(record)" :disabled="!$hasPermission('normal:extend')" type="link">推广</a-button>
            </template>
            <template v-else>
              <a-divider type="vertical" />
              <a-popconfirm title="是否确定发布该数据？" :disabled="!$hasPermission('normal:publish')" @confirm="publishPage(record)">
                <a-button type="link">发布</a-button>
              </a-popconfirm>
            </template>
            <a-divider type="vertical" />
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1"><a-button type="link" :disabled="!$hasPermission('normal:copy')" @click="addEdit(record,3)">复制</a-button></a-menu-item>
                  <!-- <a-menu-item key="2"><a-button type="link" v-if="record.publish">数据</a-button></a-menu-item> -->
                  <a-menu-item key="3"><a-button type="link" :disabled="!$hasPermission('normal:preview')" @click="preview(record)">预览</a-button></a-menu-item>
                  <a-menu-item key="4">
                    <a-popconfirm title="是否确定删除该数据？" :disabled="!$hasPermission('normal:del')" @confirm="handleDelete(record)">
                      <a-button type="link" :disabled="!$hasPermission('normal:del')">删除</a-button>
                    </a-popconfirm>
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="link">
                更多
                <DownOutlined />
              </a-button>
            </a-dropdown>
          </template>
        </template>
      </a-table>
    </template>
  </layout>
  <miniExtendDialog v-model:value="thisFields.extendVisble" :record="thisFields.extendRecord"></miniExtendDialog>
  <!-- templateType	模板类型 1常规页面 2系统页面 3定制页面 4弹窗页面 -->
  <!-- pageType	页面类型 0默认(二级自定义页面) 1首页 2会员中心 3xx 4 -->
  <!-- isOnly	同类型是否只存在一个已发布 0否 1是 -->
  <!--  pageSetting  页面状态的配置数据
      [
        {key:'1',value:'常规页',},
        {key:'2',value:'加载页',},
        {key:'3',value:'开屏页',},
        {key:'4',value:'弹窗',},
      ]
   -->
  <addMini :id="thisFields.id" :type="thisFields.type" :params="{
    templateType:1,
    pageType:0,
    isOnly:0,
  }" :pageSetting="[
  {key:'1',value:'常规页',},
  {key:'2',value:'加载页',},
  ]" :visible="thisFields.visible" @cancel="thisFields.visible = false" @ok="saveData" />

  <a-modal title="预览二维码" :open="thisFields.previewVisible" width="700px" @cancel="thisFields.previewVisible=false" :footer="null">
    <div style="display:flex;flex-direction:column;align-items:center">
      <img :src="thisFields.codeUrl" alt="">
      <div style="margin-top:20px;">手机扫码预览 <a-button type="link" @click="donwload">下载</a-button></div>
    </div>
  </a-modal>

</template>
<script setup>
import { message } from 'ant-design-vue'
import { templateList, deleteTemplate, miniCode, updateTemplatePath, getNav, publishTemplate } from '@/http/index.js'
import { usePagination } from 'vue-request'
import { cloneDeep } from 'lodash'




// 分页数据
const total = ref(0)
const pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ['bottomLeft']
  }
})

// 搜索数据
const getDefaultSearchFields = () => ({
  templateName: '',
  publish: null,
  groupName: '',
  templateType: 1,//	模板类型 1常规页面 2系统页面 3定制页面 4弹窗页面 5开屏页面
})
const searchFields = reactive(getDefaultSearchFields())
// 搜索数据格式处理
const getParams = () => {
  let params = cloneDeep(searchFields)
  return params
}
// 页面数据
const thisFields = reactive({
  id: '',
  type: 0, // 0-新增 1-编辑 2-查看 3-复制
  visible: false, // 详情
  extendVisble: false,//推广
  extendRecord: {},
  previewVisible: false,// 预览二维码页面
  templateName: '',// 预览模板名称
  codeUrl: '',// 二维码图片
  tableHeader: [
    // { title: '序号', dataIndex: 'index', align: 'center', width: 80 },
    { title: 'ID', dataIndex: 'id', align: 'center', ellipsis: true, width: 80 },
    { title: '模板编号', dataIndex: 'templateCode', align: 'center', ellipsis: true, width: 100 },
    { title: '页面名称', key: 'templateName', dataIndex: 'templateName', align: 'center', ellipsis: true, width: 140 },
    {
      title: '页面组', dataIndex: 'groupName', align: 'center', ellipsis: true, width: 140,
      customRender({ text, record }) {
        return text || '--'
      },
    },
    { title: '页面状态', dataIndex: 'publish', key: 'publish', align: 'center', ellipsis: true, width: 100 },
    { title: '最近更新时间', dataIndex: 'modified', align: 'center', ellipsis: true, width: 160 },
    {
      title: '近30天浏览人数', dataIndex: 'numberView', align: 'center', ellipsis: true, width: 130,
      customRender({ text, record }) {
        return text || '--'
      },
    },
    {
      title: '近30天访客人数', dataIndex: 'numberVisitors', align: 'center', ellipsis: true, width: 130,
      customRender({ text, record }) {
        return text || '--'
      },
    },
    { title: '操作', dataIndex: 'action', align: 'center', width: 300, fixed: 'right' }
  ],
  navList: []
})
// 选中的元素
const selectedRows = ref([])
// 选中数据操作
const rowSelection = computed(() => {
  return {
    type: 'checkbox',
    selectedRowKeys: selectedRowKeys.value,
    // 选中单个
    onSelect(record, selected, rows, e) {
      if (selected) {
        selectedRows.value.push(record)
      } else {
        let index = selectedRowKeys.value.indexOf(record.id)
        if (index > -1) {
          selectedRows.value.splice(index, 1)
        }
      }
    },
    // 全选
    onSelectAll(selected, rows, changeRow) {
      if (selected) {
        changeRow.forEach((item) => {
          selectedRows.value.push(item)
        })
      } else {
        let ids = changeRow.map((item) => item.id)
        selectedRows.value = selectedRows.value.filter((item) => {
          return !ids.includes(item.id)
        })

      }
    },
  }
})
// 选中元素的key值
const selectedRowKeys = computed(() => {
  return selectedRows.value.map(item => item.id)
})


// 获取导航信息
getNav().then(res => {
  if (res.data?.id) {
    let content = JSON.parse(res.data.content)
    thisFields.navList = content.list
  }
})

// 表格数据加载
const { data: dataSource, run, loading, pageSize, current, refresh } = usePagination((param) => {
  return templateList({ ...param, ...getParams() })
}, {
  manual: false, // 修改为false 让其自动执行
  pagination: {
    currentKey: 'pageNum', // 通过该值指定接口 当前页数 参数的属性值
    pageSizeKey: 'pageSize', // 通过该值指定接口 每页获取条数 参数的属性值
    totalKey: 'data.data.count' // 指定 data 中 total 属性的路径
  },
  formatResult: res => { // 返回数据格式化
    total.value = res.data.count
    return res.data.list
  }
})
// 分页变化
const whenPaginationChange = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}
// 搜索
const whenClickSearch = () => {
  run({ pageNum: 1, pageSize: pageSize.value })
}
// 重置
const whenClickReset = () => {
  Object.assign(searchFields, getDefaultSearchFields())
  whenClickSearch()
}

// 删除数据
const handleDelete = (record) => {
  deleteTemplate({ id: record.id }).then(res => {
    message.success('删除成功')
    run({ pageNum: 1, pageSize: pageSize.value })
  })
}

// 添加编辑页面
const addEdit = (record, type) => {
  // type: 0, // 0-新增 1-编辑 2-查看 3-复制
  thisFields.type = type
  thisFields.id = record?.id
  thisFields.visible = true
}

// 切换路径 成为导航页
const changePath = (record, pageType) => {
  updateTemplatePath({ id: record.id, templateType: record.templateType, pageType }).then(res => {
    message.success('设置成功')
    refresh()
  })
}

// 修复发布
const publishPage = (record) => {
  publishTemplate({
    templateType: record.templateType,
    pageType: record.pageType,
    id: record.id,
    publish: 1,
  }).then(res => {
    message.success('发布成功')
    refresh()
  })
}

// 保存数据
const saveData = (flag) => {
  // 只有保存并发布需要关闭弹窗
  if (flag) {
    thisFields.visible = false
  }
  refresh()
}


// 预览页面
const preview = (record) => {
  miniCode({
    page: 'pages/customPreview/customPreview',
    scene: `id=${record.id}`,
    trial: 1, // 1-体验版 0-正式版本
    width: 200,
  }).then((data) => {
    const blob = new Blob([data], { type: "image/png" });
    const objectUrl = URL.createObjectURL(blob);
    thisFields.templateName = record.templateName
    thisFields.previewVisible = true
    thisFields.codeUrl = URL.createObjectURL(blob)

  });
}

// 下载
const donwload = () => {
  const a = document.createElement("a");
  document.body.appendChild(a);
  a.setAttribute("style", "display:none");
  a.setAttribute("href", thisFields.codeUrl);
  a.setAttribute("download", thisFields.templateName + "预览.png");
  a.click();
};
const extendChange = (record) => {

  thisFields.extendVisble = true
  thisFields.extendRecord = record
  console.log("🚀 ~ extendChange ~ record:", thisFields.extendRecord)
}
</script>
