import { before } from 'lodash'
import { notification } from 'ant-design-vue'
import router from './router'
import { useGlobalStore } from './store'
import { getToken } from '@/utils/auth'


// // const whiteList = ['/register'] 白名单
router.beforeEach(async (to, from, next) => {
  const global = useGlobalStore()
  if (to.path === '/login') {
    // console.log(to, from, 'to, from, next')
    next()
  } else if (getToken()) {
    // 第一进入的时候必须获取用户信息
    if (!global.permissions.length) {
      try {
        await global.setUserInfo()
      } catch {

      }

    }
    //  根据role权限生成可访问的路由表
    //  // 动态添加可访问路由表

    if (!global.menus.length) {

      try {
        let res = await global.generateMenus()


        next({ ...to, replace: true })


      } catch {

      }

    }

    next() // hack方法 确保addRoutes已完成

  } else if (to.path === '/forgotPasswordModal') {
    // console.log(to, from, 'to, from, next')
    next()
  } else {
    // next()
    // next({ path: '/login' })
    // 退出到登录
    global.logout().then(() => {
      next({ path: '/login' }) // 否则全部重定向到登录页
    })
  }
})

router.afterEach(() => {
  // NProgress.done()
  const global = useGlobalStore()
  // next()
})
