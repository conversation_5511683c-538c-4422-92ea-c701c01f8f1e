package com.dz.ms.basic.dto;
import java.util.Date;
import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 小程序/公众号模板消息推送任务DTO
 * @author: Handy
 * @date:   2023/07/07 14:22
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "小程序/公众号模板消息推送任务")
public class MpMsgPushDTO extends BaseDTO {

    @ApiModelProperty(value = "推送任务ID")
    private Long id;
    @ApiModelProperty(value = "推送任务名称")
    private String pushName;
    @ApiModelProperty(value = "消息配置ID")
    private Long msgId;
    @ApiModelProperty(value = "模板ID")
    private String templateId;
    @ApiModelProperty(value = "模板名称")
    private String templateName;
    @ApiModelProperty(value = "消息内容")
    private String msgContent;
    @ApiModelProperty(value = "跳转路径")
    private String pagePath;
    @ApiModelProperty(value = "推送类型 1立即推送 2定时推送")
    private Integer pushType;
    @ApiModelProperty(value = "定时推送时间")
    private Date pushTime;
    @ApiModelProperty(value = "推送人数")
    private Integer pushNumber;
    @ApiModelProperty(value = "状态 1未执行 2已执行 3已撤销")
    private Integer state;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "更新人")
    private Long modifier;
    @ApiModelProperty(value = "更新时间")
    private Date modified;

}
