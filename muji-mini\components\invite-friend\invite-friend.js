const app = getApp()
Component({
  properties: {
    // 显示弹窗
    show: {
      type: Boolean,
      value: false,
    },
    src: {
      type: String,
      value: ''
    },
    data: {
      type: [Object, String, Number]
    },
    zIndex: {
      type: Number,
      value: 1000000
    },
    overlayBackground: {
      type: String,
      value: 'rgba(0,0,0,.7)'
    },
  },
  data: {
    tip: false
  },

  methods: {
    cancel() {
      this.setData({
        tip: false
      })
    },
    // 前往开启
    confirm() {
      wx.$mp.openSetting()
      this.setData({
        tip: false
      })
    },
    // 关闭按钮
    close() {
      this.triggerEvent('close')
    },
    // 分享好友
    onShare() {
      this.triggerEvent('share', this.data.data)
      this.close()
    },

    // 下载海报
    onDownload() {
      // 保存图片到相册
      let src = this.data.src;
      let that = this
      // 网络图片需要先转换为本地图片
      if (src.startsWith('http')) {
        wx.downloadFile({
          url: src,
          success(res) {
            that.shareImg(res.tempFilePath)
          }
        })
      } else {
        this.shareImg(src)
      }
    },
    shareImg(src) {
      let that=this
      wx.saveImageToPhotosAlbum({
        filePath: src,
        success: () => {
          wx.showToast({
            title: '保存成功',
          })
          // 权限
          this.close()
        },
        fail(err) {
          console.log('err', err)
          if (err.errMsg === 'saveImageToPhotosAlbum:fail auth deny') {
            // 用户拒绝了相册权限
            that.close()
            that.setData({
              tip: true
            })
          }
        }
      });
    },
  }
})
