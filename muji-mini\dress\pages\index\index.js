const app = getApp()
import { getAgreeRecord, agreeRecord } from '../../../api/index'
Page({
  data: {
    show: false,
    closeable: false,
    agree: false,// 从未点击过同意
  },
  onLoad() {
    this.init()
  },
  onShow() { },
  async init() {
    // agreementId  1-查询试衣镜记录
    let res = await getAgreeRecord({ agreementId: 1 })
    if (!res.data) {
      this.setData({ agree: false, show: true, closeable: false })
    } else {
      this.setData({ agree: true, closeable: true })
    }
  },
  // 关闭按钮
  close() {
    this.setData({ show: false })
  },
  // 确认按钮
  confirm() {
    this.close()
    // 同意规则
    agreeRecord({ agreementId: 1 })
    this.setData({ agree: true, closeable: true })
  },
  // 去试衣 需要注册
  goCamera: app.debounce(async function () {
    if (this.data.userInfo.isMember > 0) {
      wx.$mp.navigateTo({
        url: '/dress/pages/camera/camera',
      })
    } else {
      wx.$mp.navigateTo({
        url: '/pages/register/register',
      })
    }
  }),
  goPhoto: app.debounce(async function () {
    wx.$mp.navigateTo({
      url: '/dress/pages/photo/photo',
    })
  }),
  showModal: app.debounce(async function () {
    this.setData({ show: true })
  }),
  onShareAppMessage() {
    return {
      title: '生成你的「自然有生活」同款海报',
      imageUrl: this.data.$cdn + '/dress/share.png',
      path: '/dress/pages/index/index'
    }
  },

})
