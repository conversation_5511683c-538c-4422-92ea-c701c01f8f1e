const app = getApp()

const checkedIcon = {
  checked: {
    url: `${wx.$config.ossImg}/store/checked.png`,
    width: 46,
    height: 54,
  },
  unChecked: {
    url: `${wx.$config.ossImg}/store/unchecked.png`,
    width: 41,
    height: 48
  }
}

const defaulPosition = {
  latitude: 39.9037,
  longitude: 116.3975,
}

Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    outletsList: {
      type: Array,
      value: [],
      observer(val) {
        this.handleData(val);
      }
    },
    latitude: {
      type: Number,
    }, // 经度
    longitude: {
      type: Number,
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    current: 0,
    markers: [],
    currentLatitude: defaulPosition.latitude,
    currentLongitude: defaulPosition.longitude,
  },
  lifetimes: {
    // attached() {
    //   const {
    //     current,
    //     outletsList,
    //   } = this.data;
    //   const v = outletsList.map((item, index) => ({
    //     id: item.id,
    //     latitude: item.latitude,
    //     longitude: item.longitude,
    //     iconPath: index === current ? checkedIcon.checked.url : checkedIcon.unChecked.url,
    //     width: index === current ? checkedIcon.checked.width : checkedIcon.unChecked.width,
    //     height: index === current ? checkedIcon.checked.height : checkedIcon.unChecked.height,
    //     alpha: 1,
    //     customCallout: {
    //       anchorY: -5,
    //       display: index === current ? 'ALWAYS' : 'BYCLICK',
    //     }
    //   }))
    //   this.setData({
    //     markers: v,
    //     // currentLatitude: v[0].latitude,
    //     // currentLongitude: v[0].latitude,
    //   })
    // },
  },
  observers: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleData(val) {
      if (val.length > 0) {
        const {
          current,
        } = this.data;
        console.log('current', current);
        const v = val.map((item, index) => {
          console.log('index', index);
          console.log('item', item);
          return {
            id: item.id,
            latitude: item.latitude,
            longitude: item.longitude,
            iconPath: index === current ? checkedIcon.checked.url : checkedIcon.unChecked.url,
            width: index === current ? checkedIcon.checked.width : checkedIcon.unChecked.width,
            height: index === current ? checkedIcon.checked.height : checkedIcon.unChecked.height,
            alpha: 1,
            zIndex: index === current ? 999 : 0,
            customCallout: {
              anchorY: -5,
              display: index === current ? 'ALWAYS' : 'BYCLICK',
            }
          }
        })
        this.setData({
          markers: v,
          currentLatitude: v[current].latitude,
          currentLongitude: v[current].longitude,
        })
      } else {

        this.setData({
          markers: [],
          currentLatitude: app.globalData.userLatitude || defaulPosition.latitude,
          currentLongitude: app.globalData.userLongitude || defaulPosition.longitude,
        })
      }

    },
    onMarkerTap(e) {
      const {
        markerId
      } = e.detail;
      this.scrollToItem(markerId)
      // 更新标记的 callout 显示状态，确保气泡显示
      const markers = this.data.markers.map(marker => {
        if (marker.id === markerId) {
          marker.customCallout.display = 'ALWAYS'; // 点击时直接显示气泡
          marker.width = checkedIcon.checked.width;
          marker.height = checkedIcon.checked.height;
          marker.iconPath = checkedIcon.checked.url;
          marker.zIndex = 999;
        } else {
          marker.customCallout.display = 'BYCLICK'; // 其他标记保持为点击时显示
          marker.width = checkedIcon.unChecked.width;
          marker.height = checkedIcon.unChecked.height;
          marker.iconPath = checkedIcon.unChecked.url;
          marker.zIndex = 0;
        }
        return marker;
      });

      const currentMarker = markers.find(item => item.id === markerId);

      this.setData({
        markers: markers,
        current: markerId,
        currentLatitude: currentMarker.latitude,
        currentLongitude: currentMarker.longitude,
      });
    },
    // onMarkerTap(e) {
    //   const {
    //     markers
    //   } = this.data;
    //   const {
    //     markerId
    //   } = e.detail;
    //   // 更新标记的 callout 显示状态，确保气泡显示
    //   const index = markers.findIndex(item => item.id === markerId);
    //   this.setData({
    //     current: index,
    //   });
    // },
    swiperChange(e) {
      const {
        current
      } = e.detail;
      const markers = this.data.markers.map((marker, index) => {
        if (index === current) {
          marker.customCallout.display = 'ALWAYS'; // 点击时直接显示气泡
          marker.width = checkedIcon.checked.width;;
          marker.height = checkedIcon.checked.height;;
          marker.iconPath = checkedIcon.checked.url;
          marker.zIndex = 999;
        } else {
          marker.customCallout.display = 'BYCLICK'; // 其他标记保持为点击时显示
          marker.width = checkedIcon.unChecked.width;;
          marker.height = checkedIcon.unChecked.height;;
          marker.iconPath = checkedIcon.unChecked.url;
          marker.zIndex = 0;
        }
        return marker;
      });
      this.setData({
        current,
        markers,
        currentLatitude: markers[current].latitude,
        currentLongitude: markers[current].longitude,
      });
    },
    scrollToItem(markerId) {
      const {
        markers
      } = this.data;
      const current = markers.findIndex(item => item.id === markerId);
      this.setData({
        current,
      })
    }
  }
})
