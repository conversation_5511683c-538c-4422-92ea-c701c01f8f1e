package com.dz.ms.product.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品信息DTO
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "商品信息")
public class CrmProductInfoDTO {

    @ApiModelProperty(value = "商品ID")
    private Long id;
    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "副标题")
    private String subTitle;
    @ApiModelProperty(value = "商品编号")
    private String productCode;
    @ApiModelProperty(value = "商品类型 1实物商品 2电子券")
    private Integer pdType;
    @ApiModelProperty(value = "是否赠品 0赠品 1正品商品")
    private Integer beGift;
    @ApiModelProperty(value = "外部数据ID")
    private String venderId;
    @ApiModelProperty(value = "场景图片地址列表")
    private List<String> scenceImgList;
    @ApiModelProperty(value = "商品橱窗图片地址列表")
    private List<String> shelfImgList;
    @ApiModelProperty(value = "商品一级标签列表")
    private List<TagInfoDTO> tags1;
    @ApiModelProperty(value = "商品二级标签列表")
    private List<TagInfoDTO> tags2;
    @ApiModelProperty(value = "商品详情")
    private String details;
    @ApiModelProperty(value = "吊牌价")
    private BigDecimal prePrice;
    @ApiModelProperty(value = "销售价")
    private BigDecimal price;
    @ApiModelProperty(value = "购买方式 0积分 1积分+金额")
    private Integer purchaseType;
    @ApiModelProperty(value = "兑换积分")
    private Integer costPoint;
    @ApiModelProperty(value = "1积分+金额时 仍需支付的金额")
    private BigDecimal costPrice;
    @ApiModelProperty(value = "兑换须知类型 1图片 2富文本")
    private Integer exchangeDescType;
    @ApiModelProperty(value = "兑换须知图片")
    private String exchangeDescUrl;
    @ApiModelProperty(value = "兑换须知富文本")
    private String exchangeDescContent;
    @ApiModelProperty(value = "商品详情类型 1图片 2富文本")
    private Integer detailsType;
    @ApiModelProperty(value = "商品详情图片")
    private String detailsUrl;
    @ApiModelProperty(value = "商品详情富文本")
    private String detailsContent;
    @ApiModelProperty(value = "使用说明类型 1图片 2富文本")
    private Integer referType;
    @ApiModelProperty(value = "使用说明图片")
    private String referUrl;
    @ApiModelProperty(value = "使用说明富文本")
    private String referContent;
}
