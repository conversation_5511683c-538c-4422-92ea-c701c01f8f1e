package com.dz.common.core.dto.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MujiOrderGoodDTO {

    @ApiModelProperty(value = "商品单价 单位/分")
    private String amount;
    @ApiModelProperty(value = "用户实付总金额 单位/分")
    private String buyerFee;
    @ApiModelProperty(value = "商品id")
    private String goodsId;
    @ApiModelProperty(value = "商品名")
    private String goodsName;
    @ApiModelProperty(value = "用户购买的数量")
    private Integer quantity;
    @ApiModelProperty(value = "商品总金额 单位/分")
    private String totalFee;
    @ApiModelProperty(value = "对应商品行")
    private String upSerialNo;
}
