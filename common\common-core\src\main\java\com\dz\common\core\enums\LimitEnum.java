package com.dz.common.core.enums;

import java.util.Objects;

/**
 * Describe：限制类型类型枚举
 * Created by 吴蜀黍 on 2023/8/20 8:36
 **/
public enum LimitEnum {

    NOT_LIMIT(0, "不限制"),
    LIMIT(1, "按条件限制"),
    WHITE_LIST(2, "按白名单限制"),
    ;

    private final Integer code;
    private final String value;

    LimitEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (LimitEnum resultEnum : LimitEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum.value;
            }
        }
        return null;
    }
}
