package com.dz.ms.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.basic.dto.MaterialSimpleDTO;
import com.dz.ms.basic.entity.MaterialInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 素材信息Mapper
 * @author: Handy
 * @date:   2022/2/4 17:39
 */
@Repository
public interface MaterialInfoMapper extends BaseMapper<MaterialInfo> {

    /** 根据素材id列表获取素材地址列表 */
    List<MaterialSimpleDTO> getMaterialUrlByIds(@Param("ids") List<Long> ids);

    /** 生效达到生效日期的替换素材 */
    int effectiveReplaceUrl(@Param("date")Date date);

    /** 批量新增素材信息 */
    void insertBatch(@Param("list")List<MaterialInfo> list);

}
