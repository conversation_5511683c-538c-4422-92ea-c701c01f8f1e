package com.dz.ms.product.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.constants.NumConstants;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.CommonUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.product.dto.ShelfCampaignDTO;
import com.dz.ms.product.dto.ShelfCampaignRuleProductDTO;
import com.dz.ms.product.dto.ShelfProductDTO;
import com.dz.ms.product.dto.ShelfProductSuperscriptDTO;
import com.dz.ms.product.dto.req.*;
import com.dz.ms.product.dto.res.ProductChiefInfoDTO;
import com.dz.ms.product.dto.res.ShelfCampaignRuleProductEnhanceDTO;
import com.dz.ms.product.dto.res.ShelfCampaignRuleProductResDTO;
import com.dz.ms.product.entity.ShelfCampaignRuleProduct;
import com.dz.ms.product.mapper.ShelfCampaignRuleProductMapper;
import com.dz.ms.product.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 营销活动规则关联的货架商品
 *
 * @author: LiinNs
 * @date: 2024/11/20 11:39
 */
@Service
@Slf4j
public class ShelfCampaignRuleProductServiceImpl extends ServiceImpl<ShelfCampaignRuleProductMapper, ShelfCampaignRuleProduct> implements ShelfCampaignRuleProductService {

    @Resource
    private ShelfCampaignRuleProductMapper shelfCampaignRuleProductMapper;
    @Resource
    private ProductService productService;
    @Resource
    private ShelfProductService shelfProductService;
    @Resource
    private ShelfCampaignService shelfCampaignService;
    @Resource
    private ShelfProductSuperscriptService shelfProductSuperscriptService;

    /**
     * 分页查询营销活动规则关联的货架商品
     *
     * @param param
     * @return PageInfo<ShelfCampaignRuleProductDTO>
     */
    @Override
    public PageInfo<ShelfCampaignRuleProductResDTO> getShelfCampaignRuleProductList(ShelfCampaignRuleProductParamDTO param) {
        IPage<ShelfCampaignRuleProductResDTO> page = shelfCampaignRuleProductMapper.selPageList(new Page<>(param.getPageNum(), param.getPageSize()), param);
        List<ShelfCampaignRuleProductResDTO> resList = page.getRecords();
        if(!CollectionUtils.isEmpty(resList)){
            ProductChiefInfoParamDTO qryProductParam = new ProductChiefInfoParamDTO();
            qryProductParam.setShowTags(true);
            qryProductParam.setProductIdList(resList.stream().map(ShelfCampaignRuleProductResDTO::getProductId).collect(Collectors.toList()));
            List<ProductChiefInfoDTO> productList = productService.getProductChiefInfoListByIds(qryProductParam);
            if(!CollectionUtils.isEmpty(productList)){
                for (ShelfCampaignRuleProductResDTO resDto : resList) {
                    Optional<ProductChiefInfoDTO> first = productList.stream().filter(s -> Objects.equals(s.getId(), resDto.getProductId())).findFirst();
                    if (first.isPresent()) {
                        ProductChiefInfoDTO productChiefInfoDTO = first.get();
                        resDto.setShelfImg(productChiefInfoDTO.getShelfImg());
                        resDto.setSceneImg(productChiefInfoDTO.getSceneImg());
                        resDto.setTagList(productChiefInfoDTO.getTagList());
                    }
                    if (Objects.nonNull(resDto.getEveryoneLimit()) && Objects.equals(resDto.getEveryoneLimit(), NumConstants.MAX_VALUE)) {
                        resDto.setEveryoneLimit(null);
                    }
                }
            }
        }
        return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), resList);
    }

    /**
     * 据营销活动id列表查询关联商品的数量
     * @param campaignIds 销活动id列表
     * @return Map<Long, Long>
     */
    @Override
    public Map<Long, Long> getRuleProductNumByCampaignIds(List<Long> campaignIds) {
        //据营销活动id列表查询关联商品的数量
        List<ShelfCampaignRuleProductDTO> list = this.getProductByManyIds(campaignIds, null, null, NumConstants.ONE);
        if(!CollectionUtils.isEmpty(list)){
            return list.stream().collect(Collectors.groupingBy(ShelfCampaignRuleProductDTO::getCampaignId, Collectors.counting()));
        }
        return new HashMap<>();
    }

    /**
     * 根据货架ID/货架商品id列表获取启用且进行中的货架活动规则商品信息
     * @param shelfId 货架ID
     * @param shelfProductIds 货架商品id列表
     * @param num 1:查询少量字段, 2:查询所有字段
     * @return 返回一个列表，如果没有找到符合条件的，则返回空列表
     */
    @Override
    public List<ShelfCampaignRuleProductDTO> getShelfCampaignRuleProductByShelfId(Long shelfId, List<Long> shelfProductIds, Integer num) {
        //根据货架ID列表查询货架营销活动列表
        List<ShelfCampaignDTO> shelfCampaignDTOList = shelfCampaignService.getShelfCampaignByShelfIds(Collections.singletonList(shelfId), false);
        if(!CollectionUtils.isEmpty(shelfCampaignDTOList)){
            Optional<ShelfCampaignDTO> first = shelfCampaignDTOList.stream().filter(s -> Objects.equals(s.getState(), NumConstants.ONE) && Objects.equals(s.getCampaignState(), NumConstants.TWO)).findFirst();
            if(first.isPresent()){
                ShelfCampaignDTO shelfCampaignDTO = first.get();
                //据营销活动id列表查询关联商品
                List<ShelfCampaignRuleProductDTO> ruleProductList = this.getProductByManyIds(Collections.singletonList(shelfCampaignDTO.getId()), null, shelfProductIds, num);
                if(!CollectionUtils.isEmpty(ruleProductList)){
                    for (ShelfCampaignRuleProductDTO dto : ruleProductList) {
                        dto.setCampaignOnStartTime(dto.getRuleCreated());
                        dto.setCampaignOnEndTime(shelfCampaignDTO.getOnEndTime());
                    }
                }
                return ruleProductList;
            }
        }
        return new ArrayList<>();
    }
    /**
     * 根据货架ID/货架商品id列表获取进行中的货架活动规则商品信息
     * @param shelfId 货架ID
     * @param shelfProductIds 货架商品id列表
     * @param num 1:查询少量字段, 2:查询所有字段
     * @return 返回一个Map，如果没有找到符合条件的，则返回空Map
     */
    @Override
    public Map<Long, ShelfCampaignRuleProductDTO> getShelfCampaignRuleProductMapByShelfId(Long shelfId, List<Long> shelfProductIds, Integer num) {
        Map<Long, ShelfCampaignRuleProductDTO> map = new HashMap<>();
        List<ShelfCampaignRuleProductDTO> list = this.getShelfCampaignRuleProductByShelfId(shelfId, shelfProductIds, num);
        if(!CollectionUtils.isEmpty(list)){
            map = list.stream().collect(Collectors.toMap(ShelfCampaignRuleProductDTO::getShelfProductId, shelfCampaignRuleProductDTO -> shelfCampaignRuleProductDTO));
        }
        return map;
    }
    

    /**
     * 根据ID查询营销活动规则关联的货架商品
     *
     * @param id
     * @return ShelfCampaignRuleProductDTO
     */
    @Override
    public ShelfCampaignRuleProductDTO getShelfCampaignRuleProductById(Long id) {
        ShelfCampaignRuleProduct shelfCampaignRuleProduct = shelfCampaignRuleProductMapper.selectById(id);
        return BeanCopierUtils.convertObject(shelfCampaignRuleProduct, ShelfCampaignRuleProductDTO.class);
    }

    /**
     * 根据主键ID列表查询规则商品列表
     * @param ids ids
     * @return List<ShelfCampaignRuleProductDTO>
     */
    @Override
    public List<ShelfCampaignRuleProductDTO> selLessListByIds(List<Long> ids) {
        List<ShelfCampaignRuleProductDTO> list = shelfCampaignRuleProductMapper.selLessListByIds(ids);
        if(!CollectionUtils.isEmpty(list)){
            return list;
        }
        return new ArrayList<>();
    }

    /**
     * 根据营销活动id列表/规则ID列表/货架商品id列表查询活动上架商品
     * @param campaignIds 营销活动id列表
     * @param ruleIds 规则ID列表
     * @param shelfProductIds 货架商品id列表
     * @param num num 1:查询少量字段, 2:查询所有字段
     * @return List<ShelfCampaignRuleProductDTO>
     */
    @Override
    public List<ShelfCampaignRuleProductDTO> getProductByManyIds(List<Long> campaignIds, List<Long> ruleIds, List<Long> shelfProductIds, Integer num) {
        List<ShelfCampaignRuleProductDTO> list = new ArrayList<>();
        if(Objects.equals(num, NumConstants.ONE)){
            list = shelfCampaignRuleProductMapper.selLessList(campaignIds,ruleIds,shelfProductIds);
        }
        if(Objects.equals(num,NumConstants.TWO)){
            list = shelfCampaignRuleProductMapper.selAllList(campaignIds,ruleIds,shelfProductIds);
        }
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        return list;
    }

    /**
     * 根据营销活动id列表/规则ID列表查询规则上架商品增强数据(附加图片,标签,角标等数据)
     * @param campaignIds 营销活动id列表
     * @param ruleIds 规则ID列表
     * @param num num 1:查询少量字段, 2:查询所有字段
     * @return List<ShelfCampaignRuleProductEnhanceDTO>
     */
    @Override
    public List<ShelfCampaignRuleProductEnhanceDTO> getProductEnhanceByManyIds(List<Long> campaignIds, List<Long> ruleIds, Integer num) {
        List<ShelfCampaignRuleProductEnhanceDTO> list = new ArrayList<>();
        List<ShelfCampaignRuleProductDTO> basicProductList = this.getProductByManyIds(campaignIds, ruleIds, null, num);
        if(!CollectionUtils.isEmpty(basicProductList)){
            List<ShelfCampaignRuleProductEnhanceDTO> copierRuleProductList = BeanCopierUtils.convertList(basicProductList, ShelfCampaignRuleProductEnhanceDTO.class);
            List<ShelfProductSuperscriptDTO> showSuperscriptList = shelfProductSuperscriptService.getShowSuperscriptList(basicProductList.stream().map(ShelfCampaignRuleProductDTO::getShelfProductId).collect(Collectors.toList()), null);
            ProductChiefInfoParamDTO qryProductParam = new ProductChiefInfoParamDTO();
            qryProductParam.setShowTags(true);
            qryProductParam.setProductIdList(basicProductList.stream().map(ShelfCampaignRuleProductDTO::getProductId).collect(Collectors.toList()));
            List<ProductChiefInfoDTO> productList = productService.getProductChiefInfoListByIds(qryProductParam);
            copierRuleProductList.forEach(s -> {
                if(!CollectionUtils.isEmpty(productList)){
                    Optional<ProductChiefInfoDTO> first = productList.stream().filter(y -> Objects.equals(s.getProductId(), y.getId())).findFirst();
                    if(first.isPresent()){
                        ProductChiefInfoDTO productDTO = first.get();
                        s.setShelfImg(productDTO.getShelfImg());
                        s.setTagList(productDTO.getTagList());
                        s.setProductName(productDTO.getProductName());
                        s.setPdType(productDTO.getPdType());
                        if (Objects.nonNull(productDTO.getState()) && Objects.equals(productDTO.getState(), NumConstants.ZERO)) {
                            s.setBeShow(NumConstants.ZERO);
                        }
                        if (!CollectionUtils.isEmpty(showSuperscriptList)) {
                            List<ShelfProductSuperscriptDTO> filterList = showSuperscriptList.stream().filter(y -> Objects.equals(s.getShelfProductId(), y.getShelfProductId())).collect(Collectors.toList());
                            if (!CollectionUtils.isEmpty(filterList)) {
                                s.setSuperscriptIdList(filterList.stream().map(ShelfProductSuperscriptDTO::getSuperscriptId).collect(Collectors.toList()));
                                s.setSuperscriptNameList(filterList.stream().map(ShelfProductSuperscriptDTO::getSuperscriptName).collect(Collectors.toList()));
                            }
                        }
                        if (Objects.nonNull(s.getEveryoneLimit()) && Objects.equals(s.getEveryoneLimit(), NumConstants.MAX_VALUE)) {
                            s.setEveryoneLimit(null);
                        }
                        list.add(s);
                    }
                }
            });
        }
        return list;
    }

    /**
     * 保存营销活动规则关联的货架商品
     *
     * @param param
     * @return Long
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveShelfCampaignRuleProduct(ShelfCampaignRuleProductSaveParamDTO param, boolean isAdd) {
        Long uid = SecurityContext.getUser().getUid();
        List<ShelfCampaignRuleProductSaveDTO> saveRuleProductList = param.getSaveRuleProductList();
        Long shelfId = param.getShelfId();
        Long campaignId = param.getCampaignId();
        Long ruleId = param.getRuleId();
        List<ShelfCampaignRuleProductSaveDTO> addProductList = new ArrayList<>();
        List<ShelfCampaignRuleProductSaveDTO> updRuleProductList = new ArrayList<>();
        List<Long> delProductIdList = new ArrayList<>();
        //数据校验,整理
        saveRuleProductList = dataDispose(saveRuleProductList,shelfId,campaignId,ruleId);
        try {
            //货架新增
            if(isAdd && !CollectionUtils.isEmpty(saveRuleProductList)){
                addProductList = saveRuleProductList;
                List<ShelfCampaignRuleProduct> saveList = setShelfProductList(addProductList,true);
                this.saveBatch(saveList);
                return;
            }
            //货架管理商品
            //1.数据整理
            List<ShelfCampaignRuleProductDTO> oldRuleProductList = this.getProductByManyIds(null, Collections.singletonList(ruleId), null, NumConstants.ONE);
            List<Long> oldRuleProductIdList = oldRuleProductList.stream().map(ShelfCampaignRuleProductDTO::getId).collect(Collectors.toList());
            if(!isAdd){
                if(!CollectionUtils.isEmpty(saveRuleProductList)){
                    List<ShelfCampaignRuleProductSaveDTO> filterIdIsNullList = saveRuleProductList.stream().filter(s -> ParamUtils.isNullOr0Long(s.getId())).collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(filterIdIsNullList)){
                        addProductList.addAll(filterIdIsNullList);
                    }
                    List<ShelfCampaignRuleProductSaveDTO> filterIdNonNullList = saveRuleProductList.stream().filter(s -> !ParamUtils.isNullOr0Long(s.getId())).collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(filterIdNonNullList)){
                        updRuleProductList.addAll(filterIdNonNullList);
                    }
                    if(!CollectionUtils.isEmpty(updRuleProductList)){
                        List<Long> filterDelList = oldRuleProductIdList.stream().filter(s -> updRuleProductList.stream().noneMatch(y -> Objects.equals(s, y.getId()))).collect(Collectors.toList());
                        if(!CollectionUtils.isEmpty(filterDelList)){
                            delProductIdList = filterDelList;
                        }
                    }
                }
            }
            //2.货架商品,货架营销商品
            //删除商品
            if(!CollectionUtils.isEmpty(oldRuleProductList)){
                if(CollectionUtils.isEmpty(saveRuleProductList) || CollectionUtils.isEmpty(updRuleProductList)){
                    this.removeBatchByIds(oldRuleProductIdList);
                }
            }
            if(!CollectionUtils.isEmpty(delProductIdList)){
                this.removeBatchByIds(delProductIdList);
            }
            //3.新增商品
            if(!CollectionUtils.isEmpty(addProductList)){
                List<ShelfCampaignRuleProduct> saveList = setShelfProductList(addProductList,true);
                this.saveBatch(saveList);
            }
            //4.更新商品
            if(!CollectionUtils.isEmpty(updRuleProductList)){
                List<ShelfCampaignRuleProduct> saveList = setShelfProductList(updRuleProductList,false);
                this.updateBatchById(saveList);
            }
            
        } catch (BusinessException e) {
            log.error("=================[限购规则商品] saveShelfCampaignRuleProduct接口,uid:{},BusinessException报错:{},入参:{}",uid,e.getMessage(), CommonUtils.jsonStr(param),e);
            throw new BusinessException(e.getCode(),e.getMessage());
        } catch (Exception e) {
            log.error("=================[限购规则商品] saveShelfCampaignRuleProduct接口,uid:{},Exception报错:{},入参:{}",uid,e.getMessage(),CommonUtils.jsonStr(param),e);
            throw new BusinessException(ErrorCode.INTERNAL_ERROR);
        }
    }

    private List<ShelfCampaignRuleProductSaveDTO> dataDispose(List<ShelfCampaignRuleProductSaveDTO> saveRuleProductList,Long shelfId,Long campaignId,Long ruleId) {
        List<Long> delShelfProductIdList = new ArrayList<>();
        List<Long> delIdList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(saveRuleProductList)){
            //公共校验
            for (ShelfCampaignRuleProductSaveDTO dto : saveRuleProductList) {
                if (Objects.isNull(dto.getShelfProductId())) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "[限购规则商品]商品:" + dto.getShelfProductName() + "货架商品ID不能为空");
                }
                if(Objects.equals(dto.getInventoryType(),NumConstants.TWO)){
                    if (Objects.isNull(dto.getRuleInventory()) || dto.getRuleInventory() < NumConstants.ZERO) {
                        throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[限购规则商品]商品:" + dto.getShelfProductName() + "活动库存必填且不能小于0");
                    }
                }
                if (Objects.isNull(dto.getCostPoint()) || dto.getCostPoint() <= NumConstants.ZERO) {
                    throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[限购规则商品]商品:" + dto.getShelfProductName() + "积分价值必填且不能小于等于0");
                }
                if (Objects.nonNull(dto.getPrePoint()) && dto.getPrePoint() <= NumConstants.ZERO) {
                    throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[限购规则商品]商品:" + dto.getShelfProductName() + "积分划线价不能小于等于0");
                }
            }
            //筛选更新数据(筛选出更新商品不在此规则下的数据)
            List<ShelfCampaignRuleProductSaveDTO> updList = saveRuleProductList.stream().filter(s -> !ParamUtils.isNullOr0Long(s.getId())).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(updList)){
                List<Long> updIdList = updList.stream().map(ShelfCampaignRuleProductSaveDTO::getId).collect(Collectors.toList());
                //根据更新id列表获取规则商品列表
                List<ShelfCampaignRuleProductDTO> tableRuleProductList = this.selLessListByIds(updIdList);
                //筛选出更新商品不在此规则下的数据
                if(CollectionUtils.isEmpty(tableRuleProductList)){
                    delIdList.addAll(updIdList);
                } else {
                    for (Long id : updIdList) {
                        if(tableRuleProductList.stream().noneMatch(s -> Objects.equals(id,s.getId()))){
                            delIdList.add(id);
                        }
                    }
                    List<ShelfCampaignRuleProductDTO> filterCollect = tableRuleProductList.stream().filter(s -> !Objects.equals(s.getRuleId(), ruleId)).collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(filterCollect)){
                        delIdList.addAll(filterCollect.stream().map(ShelfCampaignRuleProductDTO::getId).collect(Collectors.toList()));
                    }
                }
            }
            //初次筛数据
            saveRuleProductList = saveRuleProductList.stream().filter(s -> Objects.isNull(s.getId()) || !delIdList.contains(s.getId())).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(saveRuleProductList)){
                //筛选出不在货架上的商品,活动库存校验,数据整理
                List<Long> allShelfProductIdList = saveRuleProductList.stream().map(ShelfCampaignRuleProductSaveDTO::getShelfProductId).collect(Collectors.toList());
                List<ShelfProductDTO> shelfProductDTOList = shelfProductService.selLessListByIds(allShelfProductIdList);
                for (ShelfCampaignRuleProductSaveDTO dto : saveRuleProductList) {
                    //筛选出不在货架上的商品
                    Optional<ShelfProductDTO> first = shelfProductDTOList.stream().filter(s -> Objects.equals(s.getId(), dto.getShelfProductId())).findFirst();
                    if (first.isEmpty()) {
                        delShelfProductIdList.add(dto.getShelfProductId());
                    } else {
                        if (Objects.isNull(dto.getInventoryType())) {
                            dto.setInventoryType(NumConstants.TWO);
                        }
                        if(Objects.equals(dto.getInventoryType(),NumConstants.TWO)){
                            if(dto.getRuleInventory() > first.get().getCurrentInventory()){
                                dto.setRuleInventory(first.get().getCurrentInventory());
                            }
                        } else {
                            dto.setRuleInventory(first.get().getCurrentInventory());
                        }
                        dto.setProductId(first.get().getProductId());
                        dto.setShelfId(shelfId);
                        dto.setCampaignId(campaignId);
                        dto.setRuleId(ruleId);
                    }
                }
                //再次筛选
                saveRuleProductList = saveRuleProductList.stream().filter(s -> !delShelfProductIdList.contains(s.getShelfProductId())).collect(Collectors.toList());
            }
        }
        return saveRuleProductList;
    }

    //入库数据校验,对象转换
    private List<ShelfCampaignRuleProduct> setShelfProductList(List<ShelfCampaignRuleProductSaveDTO> list,boolean isAdd) {
        List<ShelfCampaignRuleProduct> resList = BeanCopierUtils.convertList(list,ShelfCampaignRuleProduct.class);
        for (ShelfCampaignRuleProduct ruleProduct : resList) {
            if (Objects.isNull(ruleProduct.getEveryoneLimit())) {
                ruleProduct.setEveryoneLimit(NumConstants.MAX_VALUE);
            }
            if (isAdd) {

            }
            if (!isAdd) {

            }
        }
        return resList;
    }

    /**
     * 根据ID删除营销活动规则关联的货架商品
     *
     * @param param
     */
    @Override
    public void deleteShelfCampaignRuleProductById(IdCodeDTO param) {
        shelfCampaignRuleProductMapper.deleteById(param.getId());
    }

    /**
     * 根据条件删除营销活动规则关联的货架商品
     * @param param 删除条件
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRuleProduct(ShelfCampaignRuleProductDelParamDTO param) {
        shelfCampaignRuleProductMapper.deleteByParam(param);
    }

    /**
     * 扣减或增加活动规则商品库存
     * @param isAdd 是否增加库存，1为增加，2为扣减
     * @param shelfId 货架ID
     * @param shelfProductId 货架商品ID
     * @param num 数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void ruleProductDeductOrAddInventory(Integer isAdd, Long shelfId, Long shelfProductId, Integer num) {
        if(Objects.isNull(isAdd)){
            isAdd = NumConstants.TWO;
        }
        List<ShelfCampaignRuleProductDTO> ruleProductList = this.getShelfCampaignRuleProductByShelfId(shelfId, Collections.singletonList(shelfProductId), NumConstants.ONE);
        if (CollectionUtils.isEmpty(ruleProductList)) {
            throw new BusinessException("该商品已失效，请返回重试");
        }
        ShelfCampaignRuleProductDTO ruleProductDTO = ruleProductList.get(NumConstants.ZERO);
        int result = shelfCampaignRuleProductMapper.updateInventory(isAdd, ruleProductDTO.getId(), num);
        if (result <= 0) {
            throw new BusinessException("商品库存不足");
        }
    }

    @Override
    public void ruleProductValidateInventory(Long shelfId, Long shelfProductId, Integer num) {
        List<ShelfCampaignRuleProductDTO> ruleProductList = this.getShelfCampaignRuleProductByShelfId(shelfId, Collections.singletonList(shelfProductId), NumConstants.ONE);
        if (CollectionUtils.isEmpty(ruleProductList)) {
            throw new BusinessException("该商品已失效，请返回重试");
        }
        ShelfCampaignRuleProductDTO ruleProductDTO = ruleProductList.get(NumConstants.ZERO);
        int result = shelfCampaignRuleProductMapper.validateInventory(ruleProductDTO.getId(), num);
        if (result <= 0) {
            throw new BusinessException("商品库存不足");
        }
    }

}
