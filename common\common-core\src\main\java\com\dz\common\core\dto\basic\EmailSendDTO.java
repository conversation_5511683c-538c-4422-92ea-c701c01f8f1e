package com.dz.common.core.dto.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 邮件发送参数DTO
 * @author: Handy
 * @date:   2022/8/6 17:07
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "邮件发送参数")
public class EmailSendDTO {

    @ApiModelProperty(value = "收件人")
    private String[] to;
    @ApiModelProperty(value = "抄送人")
    private String[] cc;
    @ApiModelProperty(value = "主题")
    private String subject;
    @ApiModelProperty(value = "邮件内容")
    private String content;
}
