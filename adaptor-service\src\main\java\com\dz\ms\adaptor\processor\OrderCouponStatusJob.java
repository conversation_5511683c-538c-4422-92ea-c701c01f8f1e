package com.dz.ms.adaptor.processor;

import com.dz.common.core.fegin.order.ExchangeOrderFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import javax.annotation.Resource;
import java.text.ParseException;

/**
 * 订单兑换状态更新任务
 */
@Slf4j
@Component
public class OrderCouponStatusJob implements BasicProcessor {

    @Resource
    private ExchangeOrderFeignClient exchangeOrderFeignClient;

    @Override
    public ProcessResult process(TaskContext context) throws ParseException {
        exchangeOrderFeignClient.changeAllOrderStatus();
        log.info("订单兑换状态更新任务");
        return new ProcessResult(true, "success");
    }
}