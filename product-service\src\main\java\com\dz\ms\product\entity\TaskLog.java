package com.dz.ms.product.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 日志-货架商品库存任务
 *
 * @author: LiinNs
 * @date: 2024/11/27 15:19
 */
@Getter
@Setter
@NoArgsConstructor
@Table("日志-货架商品库存任务")
@TableName(value = "task_log")
public class TaskLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "商品任务ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "任务ID")
    private Long taskId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "货架ID")
    private Long shelfId;
    @Columns(type = ColumnType.VARCHAR, length = 256, isNull = true, comment = "货架名称")
    private String shelfName;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "商品ID")
    private Long productId;
    @Columns(type = ColumnType.VARCHAR, length = 256, isNull = true, comment = "商品名称")
    private String productName;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, comment = "上架库存数量 正+ 负-")
    private Integer onInventory;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "任务执行后商品当前库存数量")
    private Integer currentInventory;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "任务执行时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;

    public TaskLog(Long id, Long taskId, Long shelfId, String shelfName, Long productId, String productName, Integer onInventory, Integer currentInventory) {
        this.id = id;
        this.taskId = taskId;
        this.shelfId = shelfId;
        this.shelfName = shelfName;
        this.productId = productId;
        this.productName = productName;
        this.onInventory = onInventory;
        this.currentInventory = currentInventory;
    }

}
