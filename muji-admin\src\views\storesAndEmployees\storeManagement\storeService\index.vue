<template>

  <layout>
    <!-- <template v-slot:header>
      <searchForm :formParams="formParams" @cancel="resetData" @ok="refreshData" :disabled="!$hasPermission('setting:permission:searchRole')">

      </searchForm>
    </template> -->
    <template v-slot:topRight>
      <a-space>
        <!-- :disabled="!$hasPermission('open:leaveUser:synchLeaveUser')" 权限标识 -->

        <a-button type="primary" :disabled="!$hasPermission('out:save')" @click="addChang">新建服务</a-button>
        <a-button type="primary" :disabled="!$hasPermission('out:drag')" @click="drawChange">前台服务排序</a-button>
      </a-space>

    </template>
    <template v-slot="{ height }">
      <!-- :pagination="pagination" -->
      <a-table :indentSize="20" row-key="id" :scroll="{ y: height - 88, x: '100%' }" :pagination="false" :dataSource="dataSource" :columns="tableHeader" :loading="loading">
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'index'">
            {{ (current - 1) * pageSize + 1 + index }}
          </template>
          <template v-if="column.key === 'image'">
            <a-image :src="record.image" :width="50" :height="50"></a-image>
          </template>
          <template v-if="column.key === 'status'">
            <a-tag color="red" v-if="record.status == 1">停用中</a-tag>
            <a-tag color="success" v-else>启用中</a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-popconfirm :disabled="!$hasPermission('out:status')" :title="`是否确定${record.status == '1'?'启用':'停用'}？`" @confirm="handEnd(record)">
              <a-button type="link" :disabled="!$hasPermission('out:status')">{{record.status == '1'?'启用':'停用'}}</a-button>
            </a-popconfirm>
            <a-divider type="vertical" />
            <a-popconfirm title="是否确定删除该服务吗？" :disabled="!$hasPermission('out:delete') || record.status != '1'" @confirm="handleDelete(record)">

              <a-button :disabled="!$hasPermission('out:delete') || record.status != '1'" type="link">删除 </a-button>
            </a-popconfirm>
          </template>

        </template>
      </a-table>
    </template>

  </layout>
  <addService :visible="visible" @ok="updateList" @cancel="visible = false" :id="id" :type="type" />
  <drawService :visible="visibleDraw" @ok="updateList" @cancel="visibleDraw = false" :id="id" :type="type" />
</template>
<script setup>
import addService from "./components/addService.vue"
import drawService from "./components/drawService.vue"
import { useRoute, useRouter } from 'vue-router'
import { usePagination, useRequest, } from 'vue-request';
import { serveList, serveDelete, serveSave } from '@/http/index.js'
import { message, Modal } from "ant-design-vue";
// 分页参数
let pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: false,
    showQuickJumper: false,
    showTotal: (total) => `共 ${total} 条`,
    position: ["bottomLeft"],
  };
});
// 表格分页数据获取
const total = ref(0)


const { formParams, tableHeader, visible, id, type, pdTypeOptions, visibleDraw } = toRefs(reactive({

  visibleDraw: false,
  id: '',
  type: 0,// 0-新增 1-编辑  2-查看
  visible: false,
  pdTypeOptions: [],
  formParams: {
    roleName: ''
  },

  tableHeader: [
    {
      title: '序号',
      key: 'index',
      align: 'center',

    },
    {
      title: '服务ID',
      dataIndex: 'id',

      align: 'center',
    },
    {
      title: '服务名称',
      dataIndex: 'name',
      align: 'center',
      ellipsis: true
    },
    {
      title: '服务图片',
      key: 'image',
      align: 'center',
      ellipsis: true
    },
    {
      title: '服务状态',
      key: 'status',
      align: 'center',
      ellipsis: true
    },


    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      width: 150,
      fixed: 'right'
    }
  ]
})
);
const { data: dataSource, run, loading, pageSize, current, refresh } = usePagination((param) => {
  // 这里修改参数
  // return resignationInheritanceList({ ...param, ...getParams() })
  return serveList({ ...param, ...formParams.value })
},
  {
    manual: false, // 修改为false,让其自动执行
    pagination: {
      // currentKey: 'pageNum',  // 通过该值指定接口 当前页数 参数的属性值
      // pageSizeKey: 'pageSize',// 通过该值指定接口 每页获取条数 参数的属性值
      // totalKey: 'data.data.count',  // 指定 data 中 total 属性的路径
    },
    formatResult: res => { // 返回数据格式化
      console.log(res);
      // total.value = res.data.count
      return res.data
    }
  });

function updateList(value) {
  visible.value = false;
  visibleDraw.value = false
  // 新增 重置搜索数据
  // if (!value) {
  resetData();
  // } else {
  //   // 查看、编辑 不需要清空搜索数据 直接刷新
  // refresh();
  // }
}
function drawChange() {
  visibleDraw.value = true

}
function handEnd(record) {
  serveSave({ id: record.id, status: record.status == '0' ? 1 : 0 }).then(res => {
    if (res.code === 0) {
      message.success(`${record.status == '1' ? '启用' : '停用'}成功`)
      resetData()
    }
  })

}
//删除
const handleDelete = (record) => {
  serveDelete({ id: record.id }).then(res => {
    if (res.code === 0) {
      message.success('删除成功')
      resetData()
    }
  })
}
// 加载数据
const loadData = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}


// 刷新数据
const refreshData = () => {
  run({ pageNum: 1, pageSize: pageSize.value })
};

// 重置数据
const resetData = () => {
  formParams.value = {
    // roleName: '',
  }
  refreshData()
}
function addChang() {
  visible.value = true
  type.value = 0
  id.value = ''
}
</script>
