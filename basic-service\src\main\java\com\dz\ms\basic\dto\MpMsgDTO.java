package com.dz.ms.basic.dto;
import java.util.Date;
import java.util.List;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 小程序/公众号模板消息DTO
 * @author: Handy
 * @date:   2023/07/07 14:22
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "小程序/公众号模板消息")
public class MpMsgDTO extends BaseDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "类型 1小程序 2公众号")
    private Integer msgType;
    @ApiModelProperty(value = "模板编号")
    private String templateCode;
    @ApiModelProperty(value = "模板ID")
    private String templateId;
    @ApiModelProperty(value = "模板名称")
    private String templateName;
    @ApiModelProperty(value = "触发类型 1自动触发 2 手动触发")
    private Integer triggerType;
    @ApiModelProperty(value = "关键词逗号隔开")
    private String keyword;
    @ApiModelProperty(value = "跳转路径")
    private String pagePath;
    @ApiModelProperty(value = "状态 0禁用 1启用")
    private Integer state;
    @ApiModelProperty(value = "更新时间")
    private Date modified;

    @ApiModelProperty(value = "模板关键词列表")
    private List<MpMsgKeywordDTO> keywordList;
    @ApiModelProperty(value = "是否创建到小程序后台")
    private Boolean createToMP;

}
