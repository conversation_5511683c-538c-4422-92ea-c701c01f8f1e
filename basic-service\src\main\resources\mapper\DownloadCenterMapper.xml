<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.basic.mapper.DownloadCenterMapper">

    <resultMap id="BaseResultMap" type="com.dz.ms.basic.entity.DownloadCenter">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="query_url" jdbcType="VARCHAR" property="queryUrl"/>
        <result column="json_param" jdbcType="VARCHAR" property="jsonParam"/>
        <result column="method" jdbcType="VARCHAR" property="method"/>
        <result column="menu_name" jdbcType="VARCHAR" property="menuName"/>
        <result column="module_name" jdbcType="VARCHAR" property="moduleName"/>
        <result column="file_name" jdbcType="VARCHAR" property="fileName"/>
        <result column="source_url" jdbcType="VARCHAR" property="sourceUrl"/>
        <result column="download_num" jdbcType="BIGINT" property="downloadNum"/>
        <result column="state" jdbcType="TINYINT" property="state"/>
        <result column="error_desc" jdbcType="VARCHAR" property="errorDesc"/>
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="created" jdbcType="TIMESTAMP" property="created"/>
        <result column="header" jdbcType="VARCHAR" property="header"/>
    </resultMap>

    <resultMap id="PageResultMap" type="com.dz.common.core.dto.DownloadDTO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="menu_name" jdbcType="VARCHAR" property="menuName"/>
        <result column="module_name" jdbcType="VARCHAR" property="moduleName"/>
        <result column="file_name" jdbcType="VARCHAR" property="fileName"/>
        <result column="source_url" jdbcType="VARCHAR" property="sourceUrl"/>
        <result column="download_num" jdbcType="BIGINT" property="downloadNum"/>
        <result column="state" jdbcType="TINYINT" property="state"/>
        <result column="error_desc" jdbcType="VARCHAR" property="errorDesc"/>
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="created" jdbcType="TIMESTAMP" property="created"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="header" jdbcType="VARCHAR" property="header"/>
    </resultMap>

    <sql id="Page_Column_List">
        id, menu_name, module_name, file_name,header,
    source_url, download_num, `state`, error_desc, is_deleted, tenant_id, creator, created
    </sql>

    <sql id="Base_Column_List">
        id, query_url, json_param, `method`, menu_name, module_name, `header`, file_name,
    file_ext, source_url, download_num, `state`, error_desc, is_deleted, tenant_id, creator, created
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from download_center
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from download_center
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.dz.ms.basic.entity.DownloadCenter"
            useGeneratedKeys="true">
        insert into download_center (query_url, json_param, `method`,
                                     menu_name, module_name, `header`,
                                     file_name, file_ext, source_url, download_num,
                                     `state`, error_desc, is_deleted,
                                     tenant_id, creator, created)
        values (#{queryUrl,jdbcType=VARCHAR}, #{jsonParam,jdbcType=VARCHAR}, #{method,jdbcType=VARCHAR},
                #{menuName,jdbcType=VARCHAR}, #{moduleName,jdbcType=VARCHAR}, #{header,jdbcType=VARCHAR},
                #{fileName,jdbcType=VARCHAR}, #{fileExt,jdbcType=VARCHAR}, #{sourceUrl,jdbcType=VARCHAR},
                #{downloadNum,jdbcType=BIGINT},
                #{state,jdbcType=TINYINT}, #{errorDesc,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT},
                #{tenantId,jdbcType=BIGINT}, #{creator,jdbcType=VARCHAR}, #{created,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.dz.ms.basic.entity.DownloadCenter"
            useGeneratedKeys="true">
        insert into download_center
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="queryUrl != null">
                query_url,
            </if>
            <if test="jsonParam != null">
                json_param,
            </if>
            <if test="method != null">
                `method`,
            </if>
            <if test="menuName != null">
                menu_name,
            </if>
            <if test="moduleName != null">
                module_name,
            </if>
            <if test="header != null">
                `header`,
            </if>
            <if test="fileName != null">
                file_name,
            </if>
            <if test="fileExt != null">
                file_ext,
            </if>
            <if test="sourceUrl != null">
                source_url,
            </if>
            <if test="downloadNum != null">
                download_num,
            </if>
            <if test="state != null">
                `state`,
            </if>
            <if test="errorDesc != null">
                error_desc,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="created != null">
                created,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="queryUrl != null">
                #{queryUrl,jdbcType=VARCHAR},
            </if>
            <if test="jsonParam != null">
                #{jsonParam,jdbcType=VARCHAR},
            </if>
            <if test="method != null">
                #{method,jdbcType=VARCHAR},
            </if>
            <if test="menuName != null">
                #{menuName,jdbcType=VARCHAR},
            </if>
            <if test="moduleName != null">
                #{moduleName,jdbcType=VARCHAR},
            </if>
            <if test="header != null">
                #{header,jdbcType=VARCHAR},
            </if>
            <if test="fileName != null">
                #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="fileExt != null">
                #{fileExt,jdbcType=VARCHAR},
            </if>
            <if test="sourceUrl != null">
                #{sourceUrl,jdbcType=VARCHAR},
            </if>
            <if test="downloadNum != null">
                #{downloadNum,jdbcType=BIGINT},
            </if>
            <if test="state != null">
                #{state,jdbcType=TINYINT},
            </if>
            <if test="errorDesc != null">
                #{errorDesc,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="created != null">
                #{created,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.dz.ms.basic.entity.DownloadCenter">
        update download_center
        <set>
            <if test="queryUrl != null">
                query_url = #{queryUrl,jdbcType=VARCHAR},
            </if>
            <if test="jsonParam != null">
                json_param = #{jsonParam,jdbcType=VARCHAR},
            </if>
            <if test="method != null">
                `method` = #{method,jdbcType=VARCHAR},
            </if>
            <if test="menuName != null">
                menu_name = #{menuName,jdbcType=VARCHAR},
            </if>
            <if test="moduleName != null">
                module_name = #{moduleName,jdbcType=VARCHAR},
            </if>
            <if test="header != null">
                `header` = #{header,jdbcType=VARCHAR},
            </if>
            <if test="fileName != null">
                file_name = #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="fileExt != null">
                file_ext = #{fileExt,jdbcType=VARCHAR},
            </if>
            <if test="sourceUrl != null">
                source_url = #{sourceUrl,jdbcType=VARCHAR},
            </if>
            <if test="downloadNum != null">
                download_num = #{downloadNum,jdbcType=BIGINT},
            </if>
            <if test="state != null">
                `state` = #{state,jdbcType=TINYINT},
            </if>
            <if test="errorDesc != null">
                error_desc = #{errorDesc,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="created != null">
                created = #{created,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateScarp" parameterType="java.lang.Integer">
        update download_center
        set `state` =3
        where `state` = 2
          and download_num = 0
          and TIMESTAMPDIFF(day, create_time, NOW()) >= #{scarpDays}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.dz.ms.basic.entity.DownloadCenter">
        update download_center
        set query_url    = #{queryUrl,jdbcType=VARCHAR},
            json_param   = #{jsonParam,jdbcType=VARCHAR},
            `method`     = #{method,jdbcType=VARCHAR},
            menu_name    = #{menuName,jdbcType=VARCHAR},
            module_name  = #{moduleName,jdbcType=VARCHAR},
            `header`     = #{header,jdbcType=VARCHAR},
            file_name    = #{fileName,jdbcType=VARCHAR},
            file_ext     = #{fileExt,jdbcType=VARCHAR},
            source_url   = #{sourceUrl,jdbcType=VARCHAR},
            download_num = #{downloadNum,jdbcType=BIGINT},
            `state`      = #{state,jdbcType=TINYINT},
            error_desc   = #{errorDesc,jdbcType=VARCHAR},
            is_deleted   = #{isDeleted,jdbcType=TINYINT},
            tenant_id    = #{tenantId,jdbcType=BIGINT},
            creator      = #{creator,jdbcType=VARCHAR},
            created      = #{created,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>


    <select id="list" resultMap="PageResultMap">
        select
        <include refid="Page_Column_List"/>
        from download_center
        where is_deleted = 0
        <if test="param.menuName != null">
            and menu_name = #{param.menuName,jdbcType=VARCHAR}
        </if>
        <if test="param.moduleName != null">
            and module_name = #{param.moduleName,jdbcType=VARCHAR}
        </if>
        <if test="param.tenantId != null">
            and tenant_id = #{param.tenantId,jdbcType=BIGINT}
        </if>
        <if test="param.fileName != null and param.fileName != ''">
            and file_name like concat('%',#{param.fileName},'%')
        </if>
        <if test="param.beginTime != null and param.beginTime != ''">
            and created <![CDATA[ >= ]]> #{param.beginTime}
        </if>
        <if test="param.endTime != null and param.endTime != ''">
            and created <![CDATA[ <= ]]> #{param.endTime}
        </if>
        <if test="param.creator != null">
            and creator = #{param.creator,jdbcType=VARCHAR}
        </if>
        order by id desc
    </select>

    <select id="listScarp" resultMap="PageResultMap">
        select
        <include refid="Page_Column_List"/>
        from download_center
        where is_deleted = 0
        and TIMESTAMPDIFF(day,created, NOW()) >= #{scarpDays}
        and source_url!=''
    </select>


</mapper>