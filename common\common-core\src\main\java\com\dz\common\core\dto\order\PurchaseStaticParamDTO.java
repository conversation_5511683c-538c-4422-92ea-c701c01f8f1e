package com.dz.common.core.dto.order;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class PurchaseStaticParamDTO {

    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "用户ID")
    private Long userId;
    @ApiModelProperty(value = "规则ID")
    private Long ruleId;
    @ApiModelProperty(value = "活动规则限购时间设置 1活动时间内 2周期")
    private Integer ruleType;
    @ApiModelProperty(value = "活动规则创建时间")
    private Date rCreated;
    @ApiModelProperty(value = "周期天数")
    private Integer period;
    @ApiModelProperty(value = "活动开始时间")
    private Date campaignOnStartTime;
    @ApiModelProperty(value = "活动结束时间")
    private Date campaignOnEndTime;
    @ApiModelProperty(value = "当前周期开始时间", hidden = true)
    private Date periodStart;
    @ApiModelProperty(value = "当前周期开始时间", hidden = true)
    private Date periodEnd;
}
