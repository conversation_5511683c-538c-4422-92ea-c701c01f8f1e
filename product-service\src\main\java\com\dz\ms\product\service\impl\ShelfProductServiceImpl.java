package com.dz.ms.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.config.RedisDistributedLock;
import com.dz.common.core.constants.NumConstants;
import com.dz.common.core.constants.ProductConstants;
import com.dz.common.core.dto.*;
import com.dz.common.core.dto.product.InventoryParamDTO;
import com.dz.common.core.dto.user.MyPointsDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.user.UserInfoFeginClient;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.CommonUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.product.CacheKeys;
import com.dz.ms.product.dto.*;
import com.dz.ms.product.dto.req.*;
import com.dz.ms.product.dto.res.*;
import com.dz.ms.product.entity.ShelfProduct;
import com.dz.ms.product.mapper.ShelfProductMapper;
import com.dz.ms.product.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 货架商品
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:25
 */
@Service
@Slf4j
public class ShelfProductServiceImpl extends ServiceImpl<ShelfProductMapper, ShelfProduct> implements ShelfProductService {

    @Resource
    private ShelfProductMapper shelfProductMapper;
    @Resource
    private ShelfService shelfService;
    @Resource
    private ProductService productService;
    @Resource
    private RedisService redisService;
    @Resource
    private ShelfCampaignRuleService shelfCampaignRuleService;
    @Resource
    private ShelfCampaignRuleProductService shelfCampaignRuleProductService;
    @Resource
    private ShelfTagService shelfTagService;
    @Resource
    private ShelfProductSuperscriptService shelfProductSuperscriptService;
    @Resource
    private UserInfoFeginClient userInfoFeginClient;
    @Resource
    private RedisDistributedLock redisDistributedLock;

    /**
     * 分页查询货架商品
     *
     * @param param 入参
     * @return PageInfo<ShelfProductDTO>
     */
    @Override
    public PageInfo<ShelfProductDTO> getShelfProductList(ShelfProductParamDTO param) {
        IPage<ShelfProduct> page = shelfProductMapper.selPageList(new Page<>(param.getPageNum(), param.getPageSize()), param);
        List<ShelfProduct> records = page.getRecords();
        List<ShelfProductDTO> resList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(records)){
            resList = BeanCopierUtils.convertList(page.getRecords(), ShelfProductDTO.class);
            List<Long> shelfProductIds = resList.stream().map(ShelfProductDTO::getId).collect(Collectors.toList());
            List<Long> productIdList = resList.stream().map(ShelfProductDTO::getProductId).collect(Collectors.toList());
            ProductChiefInfoParamDTO qryProductParam = new ProductChiefInfoParamDTO();
            qryProductParam.setShowTags(true);
            qryProductParam.setProductIdList(productIdList);
            List<ProductChiefInfoDTO> productList = productService.getProductChiefInfoListByIds(qryProductParam);
            //查询货架商品展示角标列表
            List<ShelfProductSuperscriptDTO> showSuperscriptList = shelfProductSuperscriptService.getShowSuperscriptList(shelfProductIds, null);
            if (!CollectionUtils.isEmpty(productList)) {
                for (ShelfProductDTO resDto : resList) {
                    Optional<ProductChiefInfoDTO> first = productList.stream().filter(s -> Objects.equals(s.getId(), resDto.getProductId())).findFirst();
                    if (first.isPresent()) {
                        ProductChiefInfoDTO productChiefInfoDTO = first.get();
                        resDto.setShelfImg(productChiefInfoDTO.getShelfImg());
                        resDto.setSceneImg(productChiefInfoDTO.getSceneImg());
                        resDto.setShopWindowImg(productChiefInfoDTO.getShopWindowImg());
                        resDto.setTagList(productChiefInfoDTO.getTagList());
                    }
                    List<ShelfProductSuperscriptDTO> superscriptList = showSuperscriptList.stream().filter(s -> Objects.equals(s.getShelfProductId(), resDto.getId())).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(superscriptList)) {
                        resDto.setSuperscriptIdList(superscriptList.stream().map(ShelfProductSuperscriptDTO::getSuperscriptId).collect(Collectors.toList()));
                    }
                }
            }
        }
        return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), resList);
    }

    /**
     * 根据ID查询货架商品
     * @param id id
     * @param isThrow 1:抛异常
     * @return ShelfProductDTO
     */
    @Override
    public ShelfProductDTO getShelfProductById(Long id,Integer isThrow) {
        ShelfProduct info = shelfProductMapper.selectById(id);
        if(info == null && Objects.equals(isThrow,NumConstants.ONE)){
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[货架商品]未查询到此货架商品");
        }
        return BeanCopierUtils.convertObject(info, ShelfProductDTO.class);
    }

    /**
     * 根据主键ID列表查询货架商品列表
     * @param ids ids
     * @return List<ShelfProductDTO>
     */
    @Override
    public List<ShelfProductDTO> selLessListByIds(List<Long> ids) {
        List<ShelfProductDTO> list = shelfProductMapper.selLessListByIds(ids);
        if(!CollectionUtils.isEmpty(list)){
            return list;
        }
        return new ArrayList<>();
    }
    
    /**
     * 根据商品ID查询货架名称列表
     * @param productId 商品id
     * @return List<String>
     */
    @Override
    public List<String> getShelfNamesByProductId(Long productId) {
        List<String> list = shelfProductMapper.selectShelfNamesByProductId(productId);
        if(!CollectionUtils.isEmpty(list)){
            return list;
        }
        return new ArrayList<>();
    }

    /**
     * 根据货架ID列表查询货架上架商品数量列表
     * @param shelfIds 架ID列表
     * @return List<IdNumberDTO>
     */
    @Override
    public Map<Long,Integer> getProductSumByShelfIds(List<Long> shelfIds) {
        Map<Long,Integer> map = new HashMap<>();
        List<IdNumberDTO> list = shelfProductMapper.selectProductSumByShelfIds(shelfIds);
        if(!CollectionUtils.isEmpty(list)){
            map = list.stream().collect(Collectors.toMap(IdNumberDTO::getId,IdNumberDTO::getNumber));
        }
        return map;
    }

    /**
     * 根据货架ID列表/商品ID列表/货架商品id列表等查询货架上架商品
     * @param queryDTO 查询条件
     * @return List<ShelfProductDTO>
     */
    @Override
    public List<ShelfProductDTO> getProductByShelfIds(ShelfProductQueryDTO queryDTO) {
        if(Objects.isNull(queryDTO.getNum())){
            queryDTO.setNum(NumConstants.ONE);
        }
        if(Objects.isNull(queryDTO.getIsQrySuperscript())){
            queryDTO.setIsQrySuperscript(false);
        }
        if(Objects.isNull(queryDTO.getIsQryImg())){
            queryDTO.setIsQryImg(false);
        }
        if(Objects.isNull(queryDTO.getIsQryTag())){
            queryDTO.setIsQryTag(false);
        }
        List<ShelfProductDTO> list = new ArrayList<>();
        if(Objects.equals(queryDTO.getNum(), NumConstants.ONE)){
            list = shelfProductMapper.selLessList(queryDTO.getShelfIds(),queryDTO.getProductIds(),queryDTO.getShelfProductIds());
        }
        if(Objects.equals(queryDTO.getNum(),NumConstants.TWO)){
            list = shelfProductMapper.selAllList(queryDTO.getShelfIds(),queryDTO.getProductIds(),queryDTO.getShelfProductIds());
        }
        if(Objects.equals(queryDTO.getNum(),NumConstants.THREE)){
            list = shelfProductMapper.selPointCorrelationsList(queryDTO.getShelfIds(),queryDTO.getProductIds(),queryDTO.getShelfProductIds());
        }
        if(!CollectionUtils.isEmpty(list)){
            if(queryDTO.getIsQrySuperscript()){
                List<ShelfProductAllSuperscriptResDTO> allSuperscriptList = shelfProductSuperscriptService.getAllProductSuperscriptList(list.stream().map(ShelfProductDTO::getId).collect(Collectors.toList()),queryDTO.getShelfIds().get(NumConstants.ZERO));
                if(!CollectionUtils.isEmpty(allSuperscriptList)){
                    list.forEach(s -> {
                        List<ShelfProductAllSuperscriptResDTO> superscriptList = allSuperscriptList.stream().filter(ss -> Objects.equals(ss.getShelfProductId(), s.getId())).collect(Collectors.toList());
                        if(!CollectionUtils.isEmpty(superscriptList)){
                            ShelfProductAllSuperscriptResDTO allSuperscriptResDTO = superscriptList.get(NumConstants.ZERO);
                            List<ShelfProductSuperscriptDTO> showSuperscriptList = allSuperscriptResDTO.getShelfProductSuperscriptList();
                            if(!CollectionUtils.isEmpty(showSuperscriptList)){
                                s.setSuperscriptIdList(showSuperscriptList.stream().map(ShelfProductSuperscriptDTO::getSuperscriptId).collect(Collectors.toList()));
                                s.setSuperscriptNameList(showSuperscriptList.stream().map(ShelfProductSuperscriptDTO::getSuperscriptName).collect(Collectors.toList()));
                            }
                            String ruleName = allSuperscriptResDTO.getRuleName();
                            if(StringUtils.isNotBlank(ruleName)){
                                s.setSuperscriptCampaignNameList(Collections.singletonList(ruleName));
                            }
                        }
                    });
                }
            }
            if(queryDTO.getIsQryImg() || queryDTO.getIsQryTag()){
                ProductChiefInfoParamDTO qryProductParam = new ProductChiefInfoParamDTO();
                qryProductParam.setShowTags(queryDTO.getIsQryTag());
                qryProductParam.setProductIdList(list.stream().map(ShelfProductDTO::getProductId).collect(Collectors.toList()));
                List<ProductChiefInfoDTO> productList = productService.getProductChiefInfoListByIds(qryProductParam);
                if(!CollectionUtils.isEmpty(productList)){
                    list.forEach(s -> {
                        Optional<ProductChiefInfoDTO> first = productList.stream().filter(y -> Objects.equals(s.getProductId(), y.getId())).findFirst();
                        if(first.isPresent()){
                            ProductChiefInfoDTO productChiefInfoDTO = first.get();
                            s.setShelfImg(productChiefInfoDTO.getShelfImg());
                            s.setTagList(productChiefInfoDTO.getTagList());
                        }
                    });
                }
            }
        }
        //数据处理
        for (ShelfProductDTO shelfProductDTO : list) {
            if(Objects.nonNull(shelfProductDTO.getProductState()) && Objects.equals(shelfProductDTO.getProductState(),NumConstants.ZERO)){
                shelfProductDTO.setBeShow(NumConstants.THREE);
            }
        }
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        return list;
    }

    /**
     * 根据货架ID列表查询货架上架商品
     * @param shelfIds 架ID列表
     * @param num num 1:查询少量字段, 2:查询所有字段
     * @return Map<Long,List<ShelfProductDTO>>
     */
    @Override
    public Map<Long,List<ShelfProductDTO>> getProductMapByShelfIds(List<Long> shelfIds, Integer num) {
        Map<Long,List<ShelfProductDTO>> map = new HashMap<>();
        List<ShelfProductDTO> list = this.getProductByShelfIds(ShelfProductQueryDTO.builder().shelfIds(shelfIds).num(num).build());
        if(!CollectionUtils.isEmpty(list)){
            for (Long shelfId : shelfIds) {
                List<ShelfProductDTO> filterShelfList = list.stream().filter(s -> Objects.equals(s.getShelfId(), shelfId)).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(filterShelfList)){
                    map.put(shelfId,filterShelfList);
                }
            }
        }
        return map;
    }

    /**
     * 根据货架id更新货架名称,根据商品id更新商品名称/商品类型
     * @param param 入参
     */
    @Override
    public void updShelfProductName(ShelfProductDTO param) {
        try {
            if(Objects.nonNull(param.getProductId()) && (StringUtils.isNotBlank(param.getProductName()) || Objects.nonNull(param.getPdType()))){
                shelfProductMapper.updateShelfProduct(ShelfProduct.builder().productId(param.getProductId()).productName(param.getProductName()).pdType(param.getPdType()).build());
            }
        } catch (Exception e) {
            log.error("[货架商品]更新货架商品名称失败,param:{}",param,e);
        }
    }

    /**
     * 保存货架商品
     *
     * @param param 入参
     * @param isAdd 是否新增
     * @param channelNum 渠道值 1:货架保存调用 2:货架商品管理调用
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveShelfProduct(ShelfProductSaveParamDTO param, boolean isAdd, Integer channelNum) {
        Long uid = SecurityContext.getUser().getUid();
        List<ShelfProductSaveDTO> saveShelfProductList = param.getSaveShelfProductList();
        Long shelfId = param.getShelfId();
        String shelfName = param.getShelfName();
        List<ShelfProductSaveDTO> addProductList = new ArrayList<>();
        List<ShelfProductSaveDTO> updShelfProductList = new ArrayList<>();
        List<Long> delProductIdList = new ArrayList<>();
        List<ShelfProductDTO> oldShelfProductList = this.getProductByShelfIds(ShelfProductQueryDTO.builder().shelfIds(Collections.singletonList(shelfId)).num(NumConstants.ONE).build());
        List<Long> oldShelfProductIdList = oldShelfProductList.stream().map(ShelfProductDTO::getId).collect(Collectors.toList());
        //数据校验,整理
        saveShelfProductList = dataDispose(saveShelfProductList,shelfId,shelfName,oldShelfProductList);
        String lockKey = CacheKeys.Locks.SHELF_PRODUCT_UPDATE + param.getShelfId();
        boolean lock = redisService.lock(lockKey, NumConstants.THIRTY);
        if (lock) {
            try {
                List<ShelfProductSuperscriptDTO> shelfProductSuperscriptList = new ArrayList<>();
                //货架新增
                if(isAdd && !CollectionUtils.isEmpty(saveShelfProductList)){
                    addProductList = saveShelfProductList;
                    List<ShelfProductDTO> saveList = setShelfProductList(addProductList,true, channelNum);
                    if(!CollectionUtils.isEmpty(saveList)){
                        for (ShelfProductDTO shelfProductDTO : saveList) {
                            ShelfProduct shelfProduct = BeanCopierUtils.convertObject(shelfProductDTO, ShelfProduct.class);
                            this.save(shelfProduct);
                            if(!CollectionUtils.isEmpty(shelfProductDTO.getSuperscriptIdList())){
                                shelfProductSuperscriptList.addAll(shelfProductDTO.getSuperscriptIdList().stream().map(s -> ShelfProductSuperscriptDTO.builder().shelfId(shelfId).shelfProductId(shelfProduct.getId()).superscriptId(s).build()).collect(Collectors.toList()));
                            }
                        }
                        shelfProductSuperscriptService.saveProductSuperscriptList(shelfProductSuperscriptList,shelfId);
                    }
                    return;
                }
                //货架管理商品
                //1.数据整理
                if(Objects.equals(NumConstants.TWO,channelNum) && !CollectionUtils.isEmpty(saveShelfProductList)){
                    ShelfDTO shelfDTO = shelfService.getShelfById(shelfId, NumConstants.ONE);
                    for (ShelfProductSaveDTO shelfProductSaveDTO : saveShelfProductList) {
                        shelfProductSaveDTO.setShelfId(shelfDTO.getId());
                        shelfProductSaveDTO.setShelfName(shelfDTO.getName());
                    }
                }
                if(!isAdd){
                    if(!CollectionUtils.isEmpty(saveShelfProductList)){
                        List<ShelfProductSaveDTO> filterIdIsNullList = saveShelfProductList.stream().filter(s -> ParamUtils.isNullOr0Long(s.getId())).collect(Collectors.toList());
                        if(!CollectionUtils.isEmpty(filterIdIsNullList)){
                            addProductList.addAll(filterIdIsNullList);
                        }
                        List<ShelfProductSaveDTO> filterIdNonNullList = saveShelfProductList.stream().filter(s -> !ParamUtils.isNullOr0Long(s.getId())).collect(Collectors.toList());
                        if(!CollectionUtils.isEmpty(filterIdNonNullList)){
                            updShelfProductList.addAll(filterIdNonNullList);
                        }
                        if(!CollectionUtils.isEmpty(updShelfProductList)){
                            List<Long> filterDelList = oldShelfProductIdList.stream().filter(s -> updShelfProductList.stream().noneMatch(y -> Objects.equals(s, y.getId()))).collect(Collectors.toList());
                            if(!CollectionUtils.isEmpty(filterDelList)){
                                delProductIdList = filterDelList;
                            }
                        }
                    }
                }
                //2.货架商品,货架营销商品
                //删除商品
                if(!CollectionUtils.isEmpty(oldShelfProductList)){
                    if(CollectionUtils.isEmpty(saveShelfProductList) || CollectionUtils.isEmpty(updShelfProductList)){
                        this.removeBatchByIds(oldShelfProductIdList);
                        //根据货架id,删除货架关联营销规则商品
                        shelfCampaignRuleProductService.deleteRuleProduct(ShelfCampaignRuleProductDelParamDTO.builder().shelfId(shelfId).build());
                    }
                }
                if(!CollectionUtils.isEmpty(delProductIdList)){
                    this.removeBatchByIds(delProductIdList);
                }
                //3.新增商品
                if(!CollectionUtils.isEmpty(addProductList)){
                    List<ShelfProductDTO> saveList = setShelfProductList(addProductList,true, channelNum);
                    if(!CollectionUtils.isEmpty(saveList)){
                        for (ShelfProductDTO shelfProductDTO : saveList) {
                            ShelfProduct shelfProduct = BeanCopierUtils.convertObject(shelfProductDTO, ShelfProduct.class);
                            this.save(shelfProduct);
                            if(!CollectionUtils.isEmpty(shelfProductDTO.getSuperscriptIdList())){
                                shelfProductSuperscriptList.addAll(shelfProductDTO.getSuperscriptIdList().stream().map(s -> ShelfProductSuperscriptDTO.builder().shelfId(shelfId).shelfProductId(shelfProduct.getId()).superscriptId(s).build()).collect(Collectors.toList()));
                            }
                        }
                    }
                }
                //4.更新商品
                if(!CollectionUtils.isEmpty(updShelfProductList)){
                    List<ShelfProductDTO> saveList = setShelfProductList(updShelfProductList,false, channelNum);
                    List<Long> updShelfProductIdList = updShelfProductList.stream().map(ShelfProductSaveDTO::getId).collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(saveList)){
                        this.updateBatchById(BeanCopierUtils.convertList(saveList, ShelfProduct.class));
                        if(!CollectionUtils.isEmpty(saveList)){
                            for (ShelfProductDTO shelfProductDTO : saveList) {
                                if(!CollectionUtils.isEmpty(shelfProductDTO.getSuperscriptIdList())){
                                    shelfProductSuperscriptList.addAll(shelfProductDTO.getSuperscriptIdList().stream().map(s -> ShelfProductSuperscriptDTO.builder().shelfId(shelfId).shelfProductId(shelfProductDTO.getId()).superscriptId(s).build()).collect(Collectors.toList()));
                                }
                            }
                        }
                        //根据货架商品主键id,排除式删除货架关联营销规则商品
                        shelfCampaignRuleProductService.deleteRuleProduct(ShelfCampaignRuleProductDelParamDTO.builder().shelfId(shelfId).shelfProductIdList(updShelfProductIdList).build());
                    }
                }
                //5.保存货架角标列表
                shelfProductSuperscriptService.saveProductSuperscriptList(shelfProductSuperscriptList,shelfId);
            } catch (BusinessException e) {
                log.error("=================【货架商品 saveShelfProduct接口】,uid:{},BusinessException报错:{},入参:{}",uid,e.getMessage(), CommonUtils.jsonStr(param),e);
                throw new BusinessException(e.getCode(),e.getMessage());
            } catch (Exception e) {
                log.error("=================【货架商品 saveShelfProduct接口】,uid:{},Exception报错:{},入参:{}",uid,e.getMessage(),CommonUtils.jsonStr(param),e);
                throw new BusinessException(ErrorCode.INTERNAL_ERROR);
            } finally {
                redisService.unlock(lockKey);
            }
        } else {
            throw new BusinessException(ErrorCode.INTERNAL_ERROR);
        }
    }

    private List<ShelfProductSaveDTO> dataDispose(List<ShelfProductSaveDTO> saveShelfProductList,Long shelfId,String shelfName,List<ShelfProductDTO> oldShelfProductList) {
        List<Long> delProductIdList = new ArrayList<>();
        List<Long> delIdList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(saveShelfProductList)){
            //公共校验
            for (ShelfProductSaveDTO dto : saveShelfProductList) {
                if(Objects.isNull(dto.getShowType())){
                    throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[货架商品]商品:" + dto.getProductName() + "展示方式必填");
                }
                if (Objects.isNull(dto.getOnInventory()) || dto.getOnInventory() < NumConstants.ZERO) {
                    throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[货架商品]商品:" + dto.getProductName() + "上架库存必填且不能小于0");
                }
                if (Objects.isNull(dto.getCostPoint()) || dto.getCostPoint() <= NumConstants.ZERO) {
                    throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[货架商品]商品:" + dto.getProductName() + "积分价值必填且不能小于等于0");
                }
                if (Objects.nonNull(dto.getPrePoint()) && dto.getPrePoint() <= NumConstants.ZERO) {
                    throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[货架商品]商品:" + dto.getProductName() + "积分划线价不能小于等于0");
                }
                if (!CollectionUtils.isEmpty(dto.getSuperscriptIdList()) && dto.getSuperscriptIdList().size() > NumConstants.ONE) {
                    throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[货架商品]商品:" + dto.getProductName() + "角标最多1个");
                }
            }
            //筛选更新数据(筛选出更新id不在货架下的数据)
            List<ShelfProductSaveDTO> updList = saveShelfProductList.stream().filter(s -> !ParamUtils.isNullOr0Long(s.getId())).collect(Collectors.toList());
            List<Long> updIdList;
            List<ShelfProductDTO> shelfProductDTOList;
            if(!CollectionUtils.isEmpty(updList)){
                updIdList = updList.stream().map(ShelfProductSaveDTO::getId).collect(Collectors.toList());
                //根据更新id列表获取货架商品列表
                shelfProductDTOList = this.selLessListByIds(updIdList);
                if(CollectionUtils.isEmpty(shelfProductDTOList)){
                    delIdList.addAll(updIdList);
                }
                for (Long id : updIdList) {
                    if(shelfProductDTOList.stream().noneMatch(s -> Objects.equals(id,s.getId()))){
                        delIdList.add(id);
                    }
                }
                //筛选出货架不是shelfId的数据
                List<ShelfProductDTO> filterCollect = shelfProductDTOList.stream().filter(s -> !Objects.equals(s.getShelfId(), shelfId)).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(filterCollect)){
                    delIdList.addAll(filterCollect.stream().map(ShelfProductDTO::getId).collect(Collectors.toList()));
                }
            }
            //初次筛数据
            saveShelfProductList = saveShelfProductList.stream().filter(s -> Objects.isNull(s.getId()) || !delIdList.contains(s.getId())).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(saveShelfProductList)){
                //筛选被删除的商品id列表
                List<Long> allProductIdList = saveShelfProductList.stream().map(ShelfProductSaveDTO::getProductId).collect(Collectors.toList());
                List<ProductDTO> productDTOList = productService.selLessListByIds(allProductIdList);
                if(!CollectionUtils.isEmpty(productDTOList)){
                    List<Long> filterCollect = allProductIdList.stream().filter(s -> productDTOList.stream().noneMatch(y -> Objects.equals(s, y.getId()))).collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(filterCollect)){
                        delProductIdList.addAll(filterCollect);
                    }
                } else {
                    delProductIdList.addAll(allProductIdList);
                }
                //再次筛选
                saveShelfProductList = saveShelfProductList.stream().filter(s -> !delProductIdList.contains(s.getProductId())).collect(Collectors.toList());
                //数据赋值
                if(!CollectionUtils.isEmpty(saveShelfProductList)){
                    /*List<ShelfProductSaveDTO> filterIdNonNullList = saveShelfProductList.stream().filter(s -> !ParamUtils.isNullOr0Long(s.getId())).collect(Collectors.toList());
                    List<ShelfProductDTO> delShelfProductList = new ArrayList<>();
                    if(!CollectionUtils.isEmpty(filterIdNonNullList)){
                        List<ShelfProductDTO> filterList = delShelfProductList = oldShelfProductList.stream().filter(s -> filterIdNonNullList.stream().noneMatch(y -> Objects.equals(s.getId(), y.getId()))).collect(Collectors.toList());
                        if(!CollectionUtils.isEmpty(filterList)){
                            delShelfProductList.addAll(filterList);
                        }
                    } else if(!CollectionUtils.isEmpty(oldShelfProductList)){
                        delShelfProductList.addAll(oldShelfProductList);
                    }
                    if(!CollectionUtils.isEmpty(delShelfProductList)){
                        for (ShelfProductDTO delDTO : delShelfProductList) {
                            if (Objects.equals(delDTO.getBeShow(),NumConstants.ONE) && Objects.equals(delDTO.getProductState(),NumConstants.ONE)) {
                                throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[货架商品]展示中的商品不能删除");
                            }
                        }
                    }*/
                    for (int i = 0; i < saveShelfProductList.size(); i++) {
                        ShelfProductSaveDTO dto = saveShelfProductList.get(i);
                        Optional<ProductDTO> first = productDTOList.stream().filter(s -> Objects.equals(s.getId(), dto.getProductId())).findFirst();
                        if(first.isPresent()){
                            ProductDTO productDTO = first.get();
                            dto.setProductName(productDTO.getProductName());
                            dto.setPdType(productDTO.getPdType());
                        }
                        dto.setOnShelfIndex(i + NumConstants.ONE);
                        dto.setShelfId(shelfId);
                        dto.setShelfName(shelfName);
                    }
                }
            }
        }
        return saveShelfProductList;
    }

    //入库数据处理,对象转换
    private List<ShelfProductDTO> setShelfProductList(List<ShelfProductSaveDTO> list,boolean isAdd, Integer channelNum) {
        List<ShelfProductDTO> resList = new ArrayList<>();
        for (ShelfProductSaveDTO dto : list) {
            ShelfProductDTO shelfProduct = BeanCopierUtils.convertObject(dto,ShelfProductDTO.class);
            if(isAdd){
                shelfProduct.setCurrentInventory(shelfProduct.getOnInventory());
                resList.add(shelfProduct);
            }
            if(!isAdd){
                if(Objects.equals(channelNum,NumConstants.TWO)){
                    shelfProduct.setShelfName(null);
                }
                if(Objects.nonNull(dto.getBeShow()) && Objects.equals(dto.getBeShow(),NumConstants.THREE)){
                    shelfProduct.setBeShow(null);
                }
                shelfProduct.setOnInventory(null);
                shelfProduct.setCurrentInventory(null);
                resList.add(shelfProduct);
            }
        }
        return resList;
    }

    /**
     * 验证保存参数
     */
    private void validationSaveParam(List<ShelfProduct> shelfProductList, List<Long> addProductIdList, Long shelfId, boolean isAdd) {
        if (Objects.isNull(shelfId) || (isAdd && CollectionUtils.isEmpty(addProductIdList))) {
            throw new BusinessException(ErrorCode.BAD_REQUEST);
        }
        if (isAdd && shelfProductList.stream().anyMatch(s -> Objects.nonNull(s.getId()))) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if (!isAdd && shelfProductList.stream().anyMatch(s -> ParamUtils.isNullOr0Long(s.getId()))) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
    }

    /**
     * 根据货架ID删除货架商品
     * @param param 入参
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByShelfId(IdCodeDTO param) {
        shelfProductMapper.delete(new LambdaQueryWrapper<>(ShelfProduct.builder().shelfId(param.getId()).build()));
    }

    /**
     * 根据商品ID删除货架商品
     * @param param 入参
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByProductId(IdCodeDTO param) {
        shelfProductMapper.delete(new LambdaQueryWrapper<>(ShelfProduct.builder().productId(param.getId()).build()));
    }

    /**
     * 根据主键ID删除货架商品
     * @param param 入参
     */
    @Override
    public void deleteShelfProductById(IdCodeDTO param) {
        ShelfProductDTO shelfProductDTO = this.getShelfProductById(param.getId(),NumConstants.ONE);
        if(Objects.equals(shelfProductDTO.getBeShow(),ProductConstants.ShelfPro.IS_SHOW)){
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[货架商品]展示中的货架商品不能删除");
        }
        shelfProductMapper.deleteById(param.getId());
    }

    /**
     * 根据条件分页查询货架商品列表
     * @param param 货架商品查询参数对象，包含查询所需的各种条件和参数
     * @return 返回一个分页的货架商品信息列表，每个商品信息以ShelfProductMiniResDTO形式表示
     */
    @Override
    public PageInfo<ShelfProductMiniResDTO> getShelfProductListByParam(ShelfProductMiniParamDTO param) {
        List<ShelfProductMiniResDTO> resList = new ArrayList<>();
        //积分区间列表数据处理
        List<ScoreRangeDTO> scoreRangeList = param.getScoreRangeList();
        List<ScoreRangeDTO> filterScoreRangeList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(scoreRangeList)){
            for (ScoreRangeDTO scoreRangeDTO : scoreRangeList) {
                if(Objects.nonNull(scoreRangeDTO.getStartNum()) && Objects.nonNull(scoreRangeDTO.getEndNum())){
                    filterScoreRangeList.add(scoreRangeDTO);
                }
            }
            param.setScoreRangeList(filterScoreRangeList);
        }
        ShelfDTO shelfDTO = this.getUserShelfDTO(true);
        if(Objects.isNull(shelfDTO)){
            return new PageInfo<>();
        }
        param.setShelfId(shelfDTO.getId());
        //获取查询条件的商品标签列表
        if(Objects.nonNull(param.getOneTagId())){
            List<Long> tagIdListParam = new ArrayList<>();
            tagIdListParam.add(param.getOneTagId());
            param.setTagIdList(tagIdListParam);
            List<ShelfTagDTO> tagIdList = shelfTagService.getTwoTag(shelfDTO.getId(),param.getOneTagId());
            if(!CollectionUtils.isEmpty(tagIdList)){
                param.getTagIdList().addAll(tagIdList.stream().map(ShelfTagDTO::getTagId).collect(Collectors.toList()));
            }
        } else if(!CollectionUtils.isEmpty(param.getTwoTagIdList())){
            param.setTagIdList(param.getTwoTagIdList());
        }
        //获取用户积分
        if(Objects.nonNull(param.getIsMyExchange()) && Objects.equals(param.getIsMyExchange(),NumConstants.ONE)){
            Result<MyPointsDTO> myPointsDTOResult = userInfoFeginClient.myPoints();
            if (Objects.isNull(myPointsDTOResult) || !myPointsDTOResult.isSuccess() || Objects.isNull(myPointsDTOResult.getData())) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "数据加载异常，请稍后重试");
            }
            MyPointsDTO myPointsDTO = myPointsDTOResult.getData();
            param.setMyPoint(myPointsDTO.getPointsNum());
            param.setGroupIdList(shelfDTO.getGroupIdList());
        }
        IPage<ShelfProductMiniResDTO> page = shelfProductMapper.selMiniPageList(new Page<>(param.getPageNum(), param.getPageSize()), param);
        List<ShelfProductMiniResDTO> pageRecords = page.getRecords();
        //查询货架商品的活动数据
        if(!CollectionUtils.isEmpty(pageRecords)){
            //查询商品信息
            ProductChiefInfoParamDTO qryProductParam = new ProductChiefInfoParamDTO();
            qryProductParam.setShowTags(false);
            qryProductParam.setProductIdList(pageRecords.stream().map(ShelfProductMiniResDTO::getProductId).collect(Collectors.toList()));
            List<ProductChiefInfoDTO> productList = productService.getProductChiefInfoListByIds(qryProductParam);
            if(!CollectionUtils.isEmpty(productList)) {
                Map<Long, ProductChiefInfoDTO> productMap = productList.stream().collect(Collectors.toMap(ProductChiefInfoDTO::getId, productDTO -> productDTO));
                List<Long> shelfProductIds = pageRecords.stream().map(ShelfProductMiniResDTO::getShelfProductId).collect(Collectors.toList());
                //查询货架商品展示角标列表
                List<ShelfProductSuperscriptDTO> showSuperscriptList = shelfProductSuperscriptService.getShowSuperscriptList(shelfProductIds, null);
                //根据货架ID和货架商品id列表获取进行中的货架活动规则商品信息
                Map<Long, ShelfCampaignRuleProductDTO> ruleProductMap = shelfCampaignRuleProductService.getShelfCampaignRuleProductMapByShelfId(shelfDTO.getId(), shelfProductIds, NumConstants.TWO);
                Map<Long, ShelfCampaignRuleDTO> ruleMap = new HashMap<>();
                if(!CollectionUtils.isEmpty(ruleProductMap)){
                    //获取ruleProductMap的第一个元素
                    Optional<Map.Entry<Long, ShelfCampaignRuleProductDTO>> first = ruleProductMap.entrySet().stream().findFirst();
                    if(first.isPresent()){
                        Long campaignId = first.get().getValue().getCampaignId();
                        ruleMap = shelfCampaignRuleService.getRuleMapByCampaignIds(Collections.singletonList(campaignId), false);
                    }
                }
                for (ShelfProductMiniResDTO dto : pageRecords) {
                    dto.setInventoryContent(shelfDTO.getContent());
                    ShelfProductMiniResDTO resDTO = this.setShelfProductMiniResDTO(shelfDTO, dto, productMap, ruleProductMap, ruleMap, showSuperscriptList);
                    if(Objects.nonNull(resDTO)){
                        resList.add(resDTO);
                    }
                }
            } 
        }
        return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), resList);
    }

    private ShelfDTO getUserShelfDTO(boolean isQryRedis) {
        //获取货架
        ShelfDTO shelfDTO = null;
        if(isQryRedis){
            Object obj = redisService.get(CacheKeys.shelf.SHELF_UID + SecurityContext.getUser().getUid());
            if(Objects.nonNull(obj)){
                shelfDTO = (ShelfDTO) obj;
            }
        }
        if(Objects.isNull(shelfDTO)){
            shelfDTO = shelfService.getPrioritySortedShelfOne();
        }
        return shelfDTO;
    }

    private ShelfProductMiniResDTO setShelfProductMiniResDTO(ShelfDTO shelfDTO, ShelfProductMiniResDTO dto, Map<Long, ProductChiefInfoDTO> productMap, Map<Long, ShelfCampaignRuleProductDTO> ruleProductMap, Map<Long, ShelfCampaignRuleDTO> ruleMap, List<ShelfProductSuperscriptDTO> showSuperscriptList) {
        ShelfProductMiniResDTO resDTO = null;
        Date date = new Date();
        ProductChiefInfoDTO productDTO = productMap.get(dto.getProductId());
        if(Objects.nonNull(productDTO)){
            ShelfCampaignRuleProductDTO ruleProductDTO = ruleProductMap.get(dto.getShelfProductId());
            List<ShelfProductSuperscriptDTO> superscriptList = showSuperscriptList.stream().filter(s -> Objects.equals(s.getShelfProductId(), dto.getShelfProductId())).collect(Collectors.toList());
            dto.setProductName(productDTO.getProductName());
            dto.setCostPrice(productDTO.getCostPrice());
            dto.setCostPriceOnShelf(productDTO.getCostPriceOnShelf());
            dto.setPdType(productDTO.getPdType());
            dto.setShelfImg(productDTO.getShelfImg());
            if(!CollectionUtils.isEmpty(superscriptList)){
                dto.setSuperscript(superscriptList.stream().map(ShelfProductSuperscriptDTO::getSuperscriptName).collect(Collectors.toList()));
            }
            if (Objects.nonNull(ruleProductDTO)) {
                ShelfCampaignRuleDTO ruleDTO = ruleMap.get(ruleProductDTO.getRuleId());
                if (Objects.nonNull(ruleDTO)) {
                    boolean isRuleProduct = isRuleProduct(date, ruleProductDTO);
                    if(isRuleProduct){
                        dto.setCostPoint(ruleProductDTO.getCostPoint());
                        dto.setPrePoint(ruleProductDTO.getPrePoint());
                        if(ruleProductDTO.getRuleInventory() < dto.getCurrentInventory()){
                            dto.setCurrentInventory(ruleProductDTO.getRuleInventory());
                        }
                        dto.setRuleContent(ruleDTO.getContent());
                        dto.setIsCampaign(NumConstants.ONE);
                        if(shelfDTO.getGroupIdList().contains(ruleDTO.getGroupId())){
                            dto.setIsCampaignAuth(NumConstants.ONE);
                        }
                    }
                }
            }
            resDTO = dto;
        }
        return resDTO;
    }

    /**
     * 根据货架商品id查询货架商品详情
     * @param shelfProductId 货架商品id
     * @return 返回一个ShelfProductMiniResDTO对象，其中包含货架商品详情信息
     */
    @Override
    public ProductMiniResDTO getShelfProductByShelfProductId(Long shelfProductId) {
        ShelfDTO shelfDTO = this.getUserShelfDTO(false);
        ProductMiniResDTO productMiniResDTO = null;
        ShelfProductDTO shelfProductDTO = null;
        Date date = new Date();
        if(Objects.nonNull(shelfDTO)){
            shelfProductDTO = this.getShelfProductById(shelfProductId,NumConstants.TWO);
        }
        if(Objects.nonNull(shelfProductDTO) && Objects.equals(shelfProductDTO.getShelfId(),shelfDTO.getId())){
            ProductDTO productDTO = productService.getProductById(shelfProductDTO.getProductId());
            if (Objects.nonNull(productDTO) && Objects.equals(productDTO.getState(),NumConstants.ONE) && Objects.equals(shelfProductDTO.getBeShow(),NumConstants.ONE)){
                productMiniResDTO = BeanCopierUtils.convertObject(productDTO, ProductMiniResDTO.class);
                productMiniResDTO.setInventoryContent(shelfDTO.getContent());
                productMiniResDTO.setShelfId(shelfProductDTO.getShelfId());
                productMiniResDTO.setShelfProductId(shelfProductId);
                productMiniResDTO.setProductId(shelfProductDTO.getProductId());
                productMiniResDTO.setCostPoint(shelfProductDTO.getCostPoint());
                productMiniResDTO.setPrePoint(shelfProductDTO.getPrePoint());
                productMiniResDTO.setDeliveryType(shelfProductDTO.getDeliveryType());
                productMiniResDTO.setShowType(shelfProductDTO.getShowType());
                productMiniResDTO.setCurrentInventory(shelfProductDTO.getCurrentInventory());
                List<ShelfCampaignRuleProductDTO> ruleProductList = shelfCampaignRuleProductService.getShelfCampaignRuleProductByShelfId(shelfProductDTO.getShelfId(), Collections.singletonList(shelfProductId), NumConstants.TWO);
                if(!CollectionUtils.isEmpty(ruleProductList)){
                    ShelfCampaignRuleProductDTO shelfCampaignRuleProductDTO = ruleProductList.get(NumConstants.ZERO);
                    List<ShelfCampaignRuleDTO> ruleList = shelfCampaignRuleService.getRuleListByCampaignIds(Collections.singletonList(shelfCampaignRuleProductDTO.getCampaignId()), false);
                    if(!CollectionUtils.isEmpty(ruleList)){
                        ShelfCampaignRuleDTO ruleDTO = ruleList.get(NumConstants.ZERO);
                        boolean isRuleProduct = isRuleProduct(date, shelfCampaignRuleProductDTO);
                        if(isRuleProduct){
                            if(shelfCampaignRuleProductDTO.getRuleInventory() < productMiniResDTO.getCurrentInventory()){
                                productMiniResDTO.setCurrentInventory(shelfCampaignRuleProductDTO.getRuleInventory());
                            }
                            productMiniResDTO.setCostPoint(shelfCampaignRuleProductDTO.getCostPoint());
                            productMiniResDTO.setPrePoint(shelfCampaignRuleProductDTO.getPrePoint());
                            productMiniResDTO.setIsCampaign(NumConstants.ONE);
                            if(shelfDTO.getGroupIdList().contains(shelfCampaignRuleProductDTO.getGroupId())){
                                productMiniResDTO.setIsCampaignAuth(NumConstants.ONE);
                            }
                            productMiniResDTO.setRuleContent(shelfCampaignRuleProductDTO.getContent());
                        }
                    }
                }
                //查询货架商品展示角标列表
                List<ShelfProductSuperscriptDTO> showSuperscriptList = shelfProductSuperscriptService.getShowSuperscriptList(Collections.singletonList(shelfProductId), null);
                if(!CollectionUtils.isEmpty(showSuperscriptList)){
                    productMiniResDTO.setSuperscript(showSuperscriptList.stream().map(ShelfProductSuperscriptDTO::getSuperscriptName).collect(Collectors.toList()));
                }
            }
        }
        return productMiniResDTO;
    }

    /**
     * 根据货架商品id查询展示中的货架商品简介
     * @param shelfProductId 货架商品id
     * @return 返回一个ShelfProductMiniResDTO对象，其中包含货架商品详情信息
     */
    @Override
    public ProductMiniResDTO getShelfProductIntroByShelfProductId(Long shelfProductId) {
        ShelfDTO shelfDTO = this.getUserShelfDTO(false);
        ProductMiniResDTO productMiniResDTO = null;
        ShelfProductDTO shelfProductDTO = null;
        if(Objects.nonNull(shelfDTO)){
            List<ShelfProductDTO> resList = this.getProductByShelfIds(ShelfProductQueryDTO.builder().shelfProductIds(Collections.singletonList(shelfProductId)).num(NumConstants.ONE).build());
            if(!CollectionUtils.isEmpty(resList)){
                shelfProductDTO = resList.get(NumConstants.ZERO);
            }
        }
        if(Objects.nonNull(shelfProductDTO) && Objects.equals(shelfProductDTO.getShelfId(),shelfDTO.getId())){
            if(Objects.equals(shelfProductDTO.getProductState(),NumConstants.ONE) && Objects.equals(shelfProductDTO.getBeShow(),NumConstants.ONE)){
                productMiniResDTO = BeanCopierUtils.convertObject(shelfProductDTO, ProductMiniResDTO.class);
                productMiniResDTO.setShelfProductId(shelfProductId);
            }
        }
        return productMiniResDTO;
    }

    /**
     * 根据货架ID和货架商品id列表获取商品列表
     * @param shelfId 货架ID
     * @param shelfProductIds 货架商品id列表
     * @return 返回一个包含商品信息的列表，每个商品信息以ShelfProductCartResDTO形式表示
     */
    @Override
    public List<ShelfProductCartResDTO> getShelfProductCartList(Long shelfId, List<Long> shelfProductIds) {
        List<ShelfProductCartResDTO> resList = new ArrayList<>();
        //根据货架ID和货架商品id列表查询货架上架商品
        List<ShelfProductDTO> shelfProductList = this.getProductByShelfIds(ShelfProductQueryDTO.builder().isQrySuperscript(true).shelfIds(Collections.singletonList(shelfId)).shelfProductIds(shelfProductIds).num(NumConstants.THREE).build());
        if(!CollectionUtils.isEmpty(shelfProductList)){
            //查询商品图片等信息
            ProductChiefInfoParamDTO qryProductParam = new ProductChiefInfoParamDTO();
            qryProductParam.setShowTags(false);
            qryProductParam.setProductIdList(shelfProductList.stream().map(ShelfProductDTO::getProductId).collect(Collectors.toList()));
            List<ProductChiefInfoDTO> productList = productService.getProductChiefInfoListByIds(qryProductParam);
            if(!CollectionUtils.isEmpty(productList)){
                Map<Long, ProductChiefInfoDTO> productMap = productList.stream().collect(Collectors.toMap(ProductChiefInfoDTO::getId, productDTO -> productDTO));
                //根据货架ID和货架商品id列表获取进行中的货架活动规则商品信息
                Map<Long, ShelfCampaignRuleProductDTO> ruleProductMap = shelfCampaignRuleProductService.getShelfCampaignRuleProductMapByShelfId(shelfId, shelfProductIds, NumConstants.TWO);
                Map<Long, ShelfCampaignRuleDTO> ruleMap = new HashMap<>();
                if(!CollectionUtils.isEmpty(ruleProductMap)){
                    //获取ruleProductMap的第一个元素
                    Optional<Map.Entry<Long, ShelfCampaignRuleProductDTO>> first = ruleProductMap.entrySet().stream().findFirst();
                    if(first.isPresent()){
                        Long campaignId = first.get().getValue().getCampaignId();
                        ruleMap = shelfCampaignRuleService.getRuleMapByCampaignIds(Collections.singletonList(campaignId), false);
                    }
                }
                for (ShelfProductDTO dto : shelfProductList) {
                    ShelfProductCartResDTO shelfProductCartResDTO = this.getShelfProductCart(dto, productMap, ruleProductMap, ruleMap);
                    if(Objects.nonNull(shelfProductCartResDTO)){
                        resList.add(shelfProductCartResDTO);
                    }
                }
            }
        }
        log.info("[货架商品]getShelfProductCartList:入参shelfId:{},入参:shelfProductIds{},出参:{}",shelfId,CommonUtils.jsonStr(shelfProductIds),CommonUtils.jsonStr(resList));
        return resList;
    }

    private ShelfProductCartResDTO getShelfProductCart(ShelfProductDTO shelfProductDTO, Map<Long, ProductChiefInfoDTO> productMap, Map<Long, ShelfCampaignRuleProductDTO> ruleProductMap, Map<Long, ShelfCampaignRuleDTO> ruleMap) {
        ShelfProductCartResDTO dto = null;
        Date date = new Date();
        ProductChiefInfoDTO productDTO = productMap.get(shelfProductDTO.getProductId());
        ShelfCampaignRuleProductDTO ruleProductDTO = ruleProductMap.get(shelfProductDTO.getId());
        if(Objects.nonNull(productDTO)){
            if((Objects.nonNull(productDTO.getState()) && Objects.equals(productDTO.getState(),NumConstants.ZERO)) || (Objects.nonNull(shelfProductDTO.getBeShow()) && Objects.equals(shelfProductDTO.getBeShow(),NumConstants.THREE))){
                shelfProductDTO.setBeShow(NumConstants.ZERO);
            }
            dto = new ShelfProductCartResDTO();
            dto.setShelfId(shelfProductDTO.getShelfId());
            dto.setShelfProductId(shelfProductDTO.getId());
            dto.setProductId(shelfProductDTO.getProductId());
            dto.setBeShow(shelfProductDTO.getBeShow());
            dto.setProductName(productDTO.getProductName());
            dto.setCostPoint(shelfProductDTO.getCostPoint());
            dto.setCostPrice(productDTO.getCostPrice());
            dto.setPdType(productDTO.getPdType());
            dto.setVenderId(productDTO.getVenderId());
            dto.setImgUrl(productDTO.getShelfImg());
            dto.setState(productDTO.getState());
            dto.setCostPriceOnShelf(productDTO.getCostPriceOnShelf());
            dto.setPCostPoint(productDTO.getCostPoint());
            dto.setPCostPrice(productDTO.getCostPrice());
            dto.setShowType(shelfProductDTO.getShowType());
            dto.setSCostPoint(shelfProductDTO.getCostPoint());
            dto.setSPrePoint(shelfProductDTO.getPrePoint());
            dto.setSCurrentInventory(shelfProductDTO.getCurrentInventory());
            dto.setLimitNum(shelfProductDTO.getLimitNum());
            // 勿删 序列化推断不正确
            if (!CollectionUtils.isEmpty(shelfProductDTO.getSuperscriptIdList())) {
                dto.setSuperscriptIdList(new ArrayList<Long>(shelfProductDTO.getSuperscriptIdList()));
            }
            if (!CollectionUtils.isEmpty(shelfProductDTO.getSuperscriptNameList())) {
                dto.setSuperscriptNameList(new ArrayList<String>(shelfProductDTO.getSuperscriptNameList()));
            }
            if (!CollectionUtils.isEmpty(shelfProductDTO.getSuperscriptCampaignNameList())) {
                dto.setSuperscriptCampaignNameList(new ArrayList<String>(shelfProductDTO.getSuperscriptCampaignNameList()));
            }
            if (Objects.nonNull(ruleProductDTO)) {
                ShelfCampaignRuleDTO ruleDTO = ruleMap.get(ruleProductDTO.getRuleId());
                if (Objects.nonNull(ruleDTO)) {
                    boolean isRuleProduct = isRuleProduct(date, ruleProductDTO);
                    if(isRuleProduct){
                        dto.setCampaignId(ruleDTO.getCampaignId());
                        dto.setRuleId(ruleDTO.getId());
                        dto.setRPurchaseLimit(ruleDTO.getRuleNum());
                        dto.setRuleGroupId(ruleDTO.getGroupId());
                        dto.setRuleType(ruleDTO.getRuleType());
                        dto.setPeriod(ruleDTO.getPeriod());
                        dto.setRCreated(ruleDTO.getCreated());
                        dto.setCostPoint(ruleProductDTO.getCostPoint());
                        dto.setRCostPoint(ruleProductDTO.getCostPoint());
                        dto.setRPrePoint(ruleProductDTO.getPrePoint());
                        dto.setREveryoneLimit(ruleProductDTO.getEveryoneLimit());
                        if(ruleProductDTO.getRuleInventory() > shelfProductDTO.getCurrentInventory()){
                            dto.setRestInventory(shelfProductDTO.getCurrentInventory());
                        } else {
                            dto.setRestInventory(ruleProductDTO.getRuleInventory());
                        }
                        dto.setCampaignOnStartTime(ruleProductDTO.getCampaignOnStartTime());
                        dto.setCampaignOnEndTime(ruleProductDTO.getCampaignOnEndTime());
                    }
                }
            }
        }
        return dto;
    }

    private static boolean isRuleProduct(Date date, ShelfCampaignRuleProductDTO ruleProductDTO) {
        boolean isRuleProduct = false;
        if(ruleProductDTO.getRuleCreated().before(date)){
            isRuleProduct = true;
        }
        return isRuleProduct;
    }

    /**
     * 扣减或增加货架库存
     * @param isAdd 是否增加库存，1为增加，2为扣减
     * @param shelfId 货架ID
     * @param shelfProductId 货架商品ID
     * @param num 数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deductOrAddInventory(Integer isAdd, Long shelfId, Long shelfProductId, Integer num) {
        if (Objects.isNull(isAdd)) {
            isAdd = NumConstants.TWO;
        }
        int result = shelfProductMapper.updateInventory(isAdd, shelfProductId, num);
        if (result <= 0) {
            throw new BusinessException("商品库存不足");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInventory(InventoryParamDTO param) {
        // 避免事务失效
//        ShelfProductService proxyBean = applicationContext.getBean(ShelfProductService.class);
//        proxyBean.deductOrAddInventory(param.getIsAdd(), param.getShelfId(), param.getShelfProductId(), param.getNum());
        Long shelfProductId = param.getShelfProductId();
        String lockKey = "redis_distributed_lock:updateInventory:shelfProduct:" + shelfProductId;
        String requestId = UUID.randomUUID().toString();
        if (redisDistributedLock.tryGetDistributedLockWithRetry(lockKey, requestId, RedisDistributedLock.EXPIRE_TIME, RedisDistributedLock.RETRY_TIMES, RedisDistributedLock.SLEEP_TIME)) {
            try {
                int result = shelfProductMapper.updateInventory(param.getIsAdd(), param.getShelfProductId(), param.getNum());
                if (result <= 0) {
                    throw new BusinessException("商品库存不足");
                }
                if (Objects.nonNull(param.getCampaignId()) && Objects.nonNull(param.getRuleId())) {
                    shelfCampaignRuleProductService.ruleProductDeductOrAddInventory(param.getIsAdd(), param.getShelfId(), param.getShelfProductId(), param.getNum());
                }
            } finally {
                redisDistributedLock.releaseDistributedLock(lockKey, requestId);
            }
        } else {
            // 处理获取锁失败的情况
            throw new BusinessException("获取锁失败");
        }
    }

    @Override
    public void validateInventory(InventoryParamDTO param) {
        int result = shelfProductMapper.validateInventory(param.getShelfProductId(), param.getNum());
        if (result <= 0) {
            throw new BusinessException("商品库存不足");
        }
        if (Objects.nonNull(param.getCampaignId()) && Objects.nonNull(param.getRuleId())) {
            shelfCampaignRuleProductService.ruleProductValidateInventory(param.getShelfId(), param.getShelfProductId(), param.getNum());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatic(ExchangeStaticParamDTO orderStaticParam) {
        log.info("推送兑换成功统计数据 入参: {}", orderStaticParam);
        shelfService.updateStatic(orderStaticParam);
        List<StaticItemParamDTO> itemList = orderStaticParam.getItemList();
        for (StaticItemParamDTO item : itemList) {
            productService.updateStatic(item.getProductId(), item.getNumber());
            updateShelfProductStatic(item.getShelfProductId(), item.getNumber());
        }

    }

    private void updateShelfProductStatic(Long shelfProductId, Integer num) {
        String lockKey = "redis_distributed_lock:shelfProduct:" + shelfProductId;
        String requestId = UUID.randomUUID().toString();
        if (redisDistributedLock.tryGetDistributedLockWithRetry(lockKey, requestId, RedisDistributedLock.EXPIRE_TIME, RedisDistributedLock.RETRY_TIMES, RedisDistributedLock.SLEEP_TIME)) {
            try {
                shelfProductMapper.updateStatic(shelfProductId, num);
            } finally {
                redisDistributedLock.releaseDistributedLock(lockKey, requestId);
            }
        } else {
            // 处理获取锁失败的情况
            throw new BusinessException("获取锁失败");
        }
    }
}
