<!--pages/editPhone/editPhone.wxml-->
<!-- 页面 导航默认配置 -->
<my-page loading="{{loading}}" overallModal="{{overallModal}}">
  <custom-header type="{{1}}" isBackHidden="{{ isBackHidden }}">
    <view slot="content" class="page-title">
    </view>
  </custom-header>
  <view class="page-content">
    <view class="title-box">
      <view class="title-txt">更换手机号</view>
      <view class="title-p">
        <view style="font-weight: 800;">·</view>
        <view>更换后您的会员信息、优惠券和积分等会员权益将转移至新的手机号；</view>
      </view>
      <view class="title-p">
        <view style="font-weight: 800;">·</view>
        <view>原手机号所相关的其他信息将一并移除，请谨慎更换。</view>
      </view>
    </view>
    <view class="edit-form">
      <view class="form-item">
        <view class="item-label">
          当前号码
        </view>
        <view class="item-content">
          <input class="item-input" type="number" placeholder="请输入当前号码" data-key="oldMobile" value="{{ info.oldMobile }}" maxlength="{{15}}" bindconfirm="changeForm" bindblur="changeForm" bindinput="changeForm" />
        </view>
      </view>
      <view class="form-item">
        <view class="item-label">
          新手机号
        </view>
        <view class="item-content">
          <input class="item-input" type="number" placeholder="请输入新手机号" data-key="newMobile" value="{{ info.newMobile }}" maxlength="{{15}}" bindconfirm="changeForm" bindblur="changeForm" bindinput="changeForm" />
        </view>
      </view>
      <view class="form-item">
        <view class="item-label">
          验证码
        </view>
        <view class="item-content">
          <view class="item-text">
            <input class="item-input" type="number" placeholder="请输入验证码" data-key="smsCode" value="{{ info.smsCode }}" bindconfirm="changeForm" maxlength="{{15}}" bindblur="changeForm" bindinput="changeForm" />
          </view>
          <view class="item-button" bindtap="sendCode">
            <view class="btn-txt {{sendDisabled ? 'red-font' : ''}}">
              {{buttonText}}
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="bottom-box">
      <basic-button disabled="{{!isComplate}}" width="{{670}}" loading="{{loading}}" size="large" bind:click="confirm">
        提交更换
      </basic-button>
    </view>
  </view>
  <!-- 会员信息变更失败 -->
  <my-popup show="{{error}}" borderRadius="{{0}}" isCenter="{{false}}" closeable="{{false}}" title="手机号变更失败" confirmText="我知道了" content="{{errContent}}" bindclose="close" bindconfirm="close" bindcancel="close">
  </my-popup>
</my-page>