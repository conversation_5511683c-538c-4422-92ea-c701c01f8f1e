package com.dz.ms.sales.entity;


import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/4
 */
@Getter
@Setter

@Table("用户打卡表")
@TableName(value = "sign_in_user")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SignInUser implements Serializable {


    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = true, comment = "活动标识", isIndex = true)
    private String campaignCode;

    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = true, comment = "一级渠道")
    private String channelOne;
    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = true, comment = "二级渠道")
    private String channelTwo;

    @Columns(type = ColumnType.BIGINT, length = 11, isNull = false, comment = "用户ID", isIndex = true)
    private Long uid;
    @Columns(type = ColumnType.VARCHAR, length = 50, isNull = false, comment = "openid")
    private String openid;
    @Columns(type = ColumnType.VARCHAR, length = 50, isNull = false, comment = "unionid", isIndex = true)
    private String unionid;
    @Columns(type = ColumnType.VARCHAR,length = 32,isNull = true,comment = "会员名")
    private String username;
    @Columns(type = ColumnType.VARCHAR, length = 36, isNull = false, comment = "手机号码")
    private String mobile;



    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "打卡活动开始时间")
    private Date startActivityTime;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "打卡活动结束时间")
    private Date endActivityTime;


    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "打卡开始时间")
    private Date signInStartTime;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "打卡结束时间")
    private Date signInEndTime;

//    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "补卡开始时间")
//    private Date signInReplaceStartTime;
//    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "补卡结束时间")
//    private Date signInReplaceEndTime;



    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "完成打卡后是否发送优惠券 0未发送 1已发送")
    private Integer sendCouponFlag;

    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "完成打卡后是否赠送抽奖机会 0未赠送 1已赠送")
    private Integer sendLotteryFlag;

    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "打卡状态 0未完成 1已完成 2缺卡超出阈值失败")
    private Integer state;

    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "是否删除 0未删除 1已删除")
    @TableLogic
    private Integer isDeleted;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;

    public SignInUser(Long id, String campaignCode, String channelOne, String channelTwo, Long uid, String openid, String unionid,
                      String username, String mobile, Date startActivityTime, Date endActivityTime,
                      Date signInStartTime, Date signInEndTime
//            , Date signInReplaceStartTime, Date signInReplaceEndTime
    ) {
        this.id = id;
        this.campaignCode = campaignCode;
        this.channelOne = channelOne;
        this.channelTwo = channelTwo;
        this.uid = uid;
        this.openid = openid;
        this.unionid = unionid;
        this.username = username;
        this.mobile = mobile;
        this.startActivityTime = startActivityTime;
        this.endActivityTime = endActivityTime;
        this.signInStartTime = signInStartTime;
        this.signInEndTime = signInEndTime;
//        this.signInReplaceStartTime = signInReplaceStartTime;
//        this.signInReplaceEndTime = signInReplaceEndTime;
    }
}
