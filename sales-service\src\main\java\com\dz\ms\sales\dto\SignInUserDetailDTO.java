package com.dz.ms.sales.dto;


import com.dz.common.core.annotation.Columns;
import com.dz.common.core.enums.ColumnType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/8
 */
@Data
public class SignInUserDetailDTO {


    @ApiModelProperty(value = "主键", example = "1")
    private Long id;

    @ApiModelProperty(value = "活动标识", required = true)
    @NotBlank(message = "活动标识不能为空")
    private String campaignCode;

    @ApiModelProperty(value = "一级渠道")
    private String channelOne;
    @ApiModelProperty(value = "二级渠道")
    private String channelTwo;

    @ApiModelProperty(value = "素材链接")
    private String materialUrl;

    @ApiModelProperty(value = "打卡日期(yyyyMMdd)")
    private String signInDate;
    @ApiModelProperty(value = "打卡天数，第几天，开启打卡为第0天")
    private Integer days;

    // 第1-6天
    @ApiModelProperty(value = "舒适度")
    private Integer comfort;
    @ApiModelProperty(value = "保湿效果")
    private Integer moisturize;
    @ApiModelProperty(value = "吸收速度")
    private Integer absorption;
    @ApiModelProperty(value = "持久效果")
    private Integer persistence;
//    @ApiModelProperty(value = "年龄段 1、18~26岁 2、27~36岁")
//    private Integer ageBracket;
    @ApiModelProperty(value = "改善")
    private String improve;
    @ApiModelProperty(value = "整体满意度")
    private Integer satisfaction;

    // 第7天
    @ApiModelProperty(value = "使用感受")
    private String feeling;
//    @ApiModelProperty(value = "提升空间（可多选，逗号分隔）")
//    private String suggestion;
    @ApiModelProperty(value = "评分")
    private Integer score;
    @ApiModelProperty(value = "推荐")
    private Integer recommend;

    @ApiModelProperty(value = "是否购买")
    private Integer purchase;


    @ApiModelProperty(value = "打卡状态 0未打卡 1已打卡 2缺卡")
    private Integer state;
    @ApiModelProperty(value = "打卡时间")
    private Date signInTime;
    @ApiModelProperty(value = "补卡时间")
    private Date supplementTime;



    @ApiModelProperty(value = "头像")
    private String avatar;
    @ApiModelProperty(value = "昵称")
    private String username;

    @ApiModelProperty("参与人数")
    private Integer num;

}
