package com.dz.ms.user.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.user.constants.CacheKeys;
import com.dz.ms.user.dto.SysPermissionUrlDTO;
import com.dz.ms.user.entity.SysPermissionUrl;
import com.dz.ms.user.service.SysPermissionUrlService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags="权限功能接口")
@RestController
public class SysPermissionUrlController  {

    @Resource
    private SysPermissionUrlService sysPermissionUrlService;
    @Resource
    private RedisService redisService;

    /**
     * 分页查询权限功能接口
     * @param param
     * @return result<PageInfo<SysPermissionUrlDTO>>
     */
    @ApiOperation("分页查询权限功能接口")
	@GetMapping(value = "/oms/sys_permission_url/list")
    public Result<PageInfo<SysPermissionUrlDTO>> getSysPermissionUrlList(@ModelAttribute SysPermissionUrlDTO param) {
        Result<PageInfo<SysPermissionUrlDTO>> result = new Result<>();
        SysPermissionUrl sysPermissionUrl = BeanCopierUtils.convertObjectTrim(param,SysPermissionUrl.class);
        IPage<SysPermissionUrl> page = sysPermissionUrlService.page(new Page<>(param.getPageNum(), param.getPageSize()), new QueryWrapper<>(sysPermissionUrl));
        result.setData(new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),BeanCopierUtils.convertList(page.getRecords(), SysPermissionUrlDTO.class)));
        return result;
    }

    /**
     * 根据ID查询权限功能接口
     * @param id
     * @return result<SysPermissionUrlDTO>
     */
    @ApiOperation("根据ID查询权限功能接口")
	@GetMapping(value = "/oms/sys_permission_url/info")
    public Result<SysPermissionUrlDTO> getSysPermissionUrlById(@RequestParam("id") Long id) {
        Result<SysPermissionUrlDTO> result = new Result<>();
        SysPermissionUrl sysPermissionUrl = sysPermissionUrlService.getById(id);
        result.setData(BeanCopierUtils.convertObject(sysPermissionUrl,SysPermissionUrlDTO.class));
        return result;
    }

    /**
     * 保存权限功能接口
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "保存权限功能接口",type = LogType.OPERATELOG)
    @ApiOperation("保存权限功能接口")
	@PostMapping(value = "/oms/sys_permission_url/save")
    public Result<Long> save(@RequestBody SysPermissionUrlDTO param) {
        Result<Long> result = new Result<>();
        SysPermissionUrl sysPermissionUrl = new SysPermissionUrl(param.getId(), param.getPermitId(), param.getPermitName(), param.getUrl());
        if(ParamUtils.isNullOr0Long(sysPermissionUrl.getId())) {
            sysPermissionUrlService.save(sysPermissionUrl);
        }
        else {
            sysPermissionUrlService.updateById(sysPermissionUrl);
        }
        redisService.delAll(CacheKeys.SYS_ROLE_PERMIT_URLS);
        result.setData(sysPermissionUrl.getId());
        return result;
    }
	
	/**
     * 根据ID删除权限功能接口
     * @param param
     * @return result<Boolean>
     */
    @SysLog(value = "根据ID删除权限功能接口",type = LogType.OPERATELOG)
    @ApiOperation("根据ID删除权限功能接口")
	@PostMapping(value = "/oms/sys_permission_url/delete")
    public Result<Boolean> deleteSysPermissionUrlById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        sysPermissionUrlService.removeById(param.getId());
        result.setData(true);
        return result;
    }

}
