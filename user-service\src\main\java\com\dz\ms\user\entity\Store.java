package com.dz.ms.user.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 门店
 * @author: yibo
 * @date:   2024/11/19 17:19
 */
@Getter
@Setter
@NoArgsConstructor
@Table("门店")
@TableName(value = "store")
public class Store implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "主键id")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "门店编号")
    private String storeSn;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "门店名称")
    private String storeName;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = true,comment = "门店类型 1普通店  2旗舰店")
    private Integer type;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = true,comment = "1正常  2删除(暂无用处)")
    private Integer status;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "省份")
    private String province;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "城市")
    private String city;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "区域")
    private String area;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "经度")
    private String longitude;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "维度")
    private String latitude;
    @Columns(type = ColumnType.VARCHAR,length = 255,isNull = true,comment = "联系邮箱")
    private String email;
    @Columns(type = ColumnType.VARCHAR,length = 50,isNull = true,comment = "联系电话")
    private String phone;
    @Columns(type = ColumnType.VARCHAR,length = 255,isNull = true,comment = "门店地址")
    private String storeAddress;
    @Columns(type = ColumnType.VARCHAR,length = 255,isNull = true,comment = "门店图片")
    private String images;
    @Columns(type = ColumnType.VARCHAR,length = 255,isNull = true,comment = "企业微信联系人二维码")
    private String weworkImages;
    @Columns(type = ColumnType.VARCHAR,length = 512,isNull = true,comment = "营业时间1")
    private String openingHourOne;
    @Columns(type = ColumnType.VARCHAR,length = 512,isNull = true,comment = "营业时间2")
    private String openingHourTwo;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "开业日期")
    private String openDate;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = false,defaultValue = "0",comment = "是否处理过 0否 1是")
    private Integer isUpdate;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = false,defaultValue = "0",comment = "是否关店 0根据开业日期判断  1闭店")
    private Integer isClose;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "创建人")
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = true,comment = "修改人")
    private Long modifier;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "租户id")
    private Long tenantId;

}
