package com.dz.common.base.vo;

import com.dz.common.base.enums.ErrorCode;

/**
 * 统一接口返回类型实体
 * @author: Handy
 * @date: 2017/9/10
 */
public class Result<T> {

    /** 返回业务数据 */
    private T data;
    /** 返回结果编码 默认0 */
    private Integer code = 0;
    /** 返回结果提示 默认 成功 */
    private String msg = "成功";
    /** 是否成功 */
    private boolean success;

    public Result<T> errorResult(ErrorCode errorCode) {
        this.code = errorCode.getCode();
        this.msg = errorCode.getMessage();
        return this;
    }

    public Result<T> errorResult(ErrorCode errorCode, String msg) {
        this.code = errorCode.getCode();
        this.msg = msg;
        return this;
    }

    public Result<T> errorResult(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
        return this;
    }

    /**
     * 执行错误返回结果
     * @return
     */
    public Result<T> errorResult(String msg) {
        this.code = ErrorCode.INTERNAL_ERROR.getCode();
        this.msg = msg;
        return this;
    }

    /**
     * 参数错误返回结果
     * @return
     */
    public Result<T> paramErroResult(String msg) {
        this.code = ErrorCode.INVALID_ARGUMENT.getCode();
        this.msg = msg;
        return this;
    }

    /**
     * 未认证返回结果
     * @return
     */
    public Result<T> result401(String msg) {
        this.code = ErrorCode.UNAUTHORIZED.getCode();
        this.msg = msg;
        return this;
    }

    public static Result success(){
        return new Result();
    }

    public Result<T> data(T data) {
        this.data = data;
        return this;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public boolean isSuccess() {
        return null != this.code && this.code.equals(0);
    }
}
