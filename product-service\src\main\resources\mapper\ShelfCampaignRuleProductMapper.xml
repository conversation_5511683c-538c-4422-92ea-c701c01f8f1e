<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.product.mapper.ShelfCampaignRuleProductMapper">

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
        id,
  	    campaign_id,
  	    rule_id,
  	    shelf_id,
  	    shelf_product_id,
  	    product_id,
  	    inventory_type,
  	    rule_inventory,
  	    cost_point,
  	    pre_point,
        everyone_limit,
  	    tenant_id,
  	    creator,
  	    created,
  	    modified,
  	    modifier
    </sql>

    <!-- 查询示例 -->
    <select id="selectDemo" parameterType="java.lang.Long"
            resultType="com.dz.ms.product.entity.ShelfCampaignRuleProduct">
        select
        <include refid="Base_Column_List"/>
        from shelf_campaign_rule_product
        where id = #{id,jdbcType=BIGINT}
        AND tenant_id = #{tenantId}
    </select>

	<select id="selPageList" resultType="com.dz.ms.product.dto.res.ShelfCampaignRuleProductResDTO">
		select distinct 
			ru.id,ru.campaign_id,ru.rule_id,ru.shelf_id,ru.shelf_product_id,ru.product_id,ru.inventory_type,ru.rule_inventory,ru.cost_point,ru.pre_point,ru.everyone_limit,
			sp.on_inventory,sp.current_inventory,sp.be_show,
			p.product_name,p.pd_type
		from shelf_campaign_rule_product ru
		left join shelf_product sp on sp.id = ru.shelf_product_id
		left join product p on p.id = ru.product_id
		<where>
			ru.is_deleted = 0
			and sp.is_deleted = 0
			and p.is_deleted = 0
			<if test=" param.productName != null and param.productName != ''  ">
				and p.product_name like CONCAT('%', #{param.productName},'%')
			</if>
			<if test="param.pdType != null">
				AND p.pd_type = #{param.pdType}
			</if>
		</where>
		ORDER BY ru.id ASC
	</select>
	
    <select id="selLessListByIds" resultType="com.dz.ms.product.dto.ShelfCampaignRuleProductDTO">
		SELECT 
			ru.id,
			ru.campaign_id,
			ru.rule_id,
			ru.shelf_id,
			ru.shelf_product_id,
			ru.product_id
		FROM shelf_campaign_rule_product ru
		left join shelf_product sp on sp.id = ru.shelf_product_id
		left join product p on p.id = ru.product_id
		<where>
			ru.is_deleted = 0
			and sp.is_deleted = 0
			and p.is_deleted = 0
			and ru.id in
			<foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</where>
	</select>
	
	<select id="selLessList" resultType="com.dz.ms.product.dto.ShelfCampaignRuleProductDTO">
		SELECT 
		    ru.id, ru.campaign_id, ru.rule_id, ru.shelf_product_id,
			r.group_id, r.name ruleName, r.content
		FROM shelf_campaign_rule_product ru
		left join shelf_campaign_rule r on r.id = ru.rule_id
		left join shelf_product sp on sp.id = ru.shelf_product_id
		left join product p on p.id = ru.product_id
		<where>
			ru.is_deleted = 0
			and r.is_deleted = 0
			and sp.is_deleted = 0
			and p.is_deleted = 0
			<if test="null != ruleIds and ruleIds.size > 0">
				and ru.rule_id in
				<foreach collection="ruleIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="null != campaignIds and campaignIds.size > 0">
				and ru.campaign_id in
				<foreach collection="campaignIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="null != shelfProductIds and shelfProductIds.size > 0">
				and ru.shelf_product_id in
				<foreach collection="shelfProductIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
		</where>
	</select>
	
	<select id="selAllList" resultType="com.dz.ms.product.dto.ShelfCampaignRuleProductDTO">
		SELECT distinct
			ru.id,ru.campaign_id,ru.rule_id,ru.shelf_id,ru.shelf_product_id,ru.product_id,ru.inventory_type,ru.rule_inventory,ru.cost_point,ru.pre_point,ru.everyone_limit,ru.rule_created,
			ru.tenant_id,ru.creator,ru.created,ru.modified,ru.modifier,
			r.group_id, r.name ruleName, r.content,
			sp.on_inventory,sp.current_inventory,sp.be_show
		FROM shelf_campaign_rule_product ru
		left join shelf_campaign_rule r on r.id = ru.rule_id
		left join shelf_product sp on sp.id = ru.shelf_product_id
		left join product p on p.id = ru.product_id
		<where>
			ru.is_deleted = 0
			and r.is_deleted = 0
			and sp.is_deleted = 0
			and p.is_deleted = 0
			<if test="null != ruleIds and ruleIds.size > 0">
				and ru.rule_id in
				<foreach collection="ruleIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="null != campaignIds and campaignIds.size > 0">
				and ru.campaign_id in
				<foreach collection="campaignIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="null != shelfProductIds and shelfProductIds.size > 0">
				and ru.shelf_product_id in
				<foreach collection="shelfProductIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
		</where>
	</select>

	<select id="validateInventory" resultType="java.lang.Integer">
		select count(id)
		from shelf_campaign_rule_product
		<where>
			id = #{id}
			and is_deleted = 0
			and rule_inventory >= #{num}
		</where>
	</select>

	<update id="deleteByParam">
		update shelf_campaign_rule_product
		set is_deleted = 1
		<where>
			<if test="param.campaignId != null">
				AND campaign_id = #{param.campaignId}
			</if>
			<if test="param.ruleId != null">
				AND rule_id = #{param.ruleId}
			</if>
			<if test="param.ruleIdList != null and param.ruleIdList.size > 0">
				AND rule_id in
				<foreach collection="param.ruleIdList" index="index" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
			<if test="param.shelfId != null">
				AND shelf_id = #{param.shelfId}
			</if>
			<if test="param.productId != null">
				AND product_id = #{param.productId}
			</if>
			<if test="param.shelfProductIdList != null and param.shelfProductIdList.size > 0">
				AND shelf_product_id not in
				<foreach collection="param.shelfProductIdList" index="index" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
		</where>
	</update>

	<update id="updateInventory">
		update shelf_campaign_rule_product
		set
		<if test="isAdd == 1">
			rule_inventory = rule_inventory + #{num}
		</if>
		<if test="isAdd == 2">
			rule_inventory = rule_inventory - #{num}
		</if>
		<where>
			id = #{id}
			and is_deleted = 0
			<if test="isAdd == 2">
				and rule_inventory <![CDATA[ >= ]]> #{num}
			</if>
		</where>
	</update>
	
</mapper>
