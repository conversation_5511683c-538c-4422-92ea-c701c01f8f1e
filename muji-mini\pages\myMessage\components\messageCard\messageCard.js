// import dayjs from '../../../../utils/dayjs.min'
const dayjs = require('../../../../utils/dayjs.min')
// import {
//   readMessage
// } from '../../../../api/index'
const app = getApp();


Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    messageInfo: {
      type: Object,
      value: {},
      observer(val) {
        this.handleData(val)
      }
    }
  },
  /**
   * 组件的初始数据
   */
  data: {
    width: 50,
    message: {}
  },
  /**
   * 组件的方法列表
   */
  methods: {
    handleData(val) {
      const {
        createTime,
        msgDesc,
        msgType,
        ...data
      } = val;
      data.time = dayjs(createTime).format('YYYY.MM.DD');
      data.msgDesc = msgDesc;
      if (msgType !== 2) {
        data.msgDesc = `${msgDesc}>>`
      }
      this.setData({
        message: data,
      })
    },
    async handlegoto(e) {
      const {
        url,
        // state,
        id
      } = e.currentTarget.dataset;
      wx.$mp.track({
        event: 'message_list_click',
        props: {
          id,
          title: this.data.messageInfo.title,
        }
      })
      // if (state === 2) {
      //   await readMessage({
      //     id
      //   })
      // }
      app.goUrl(url)
    }
  }
})
