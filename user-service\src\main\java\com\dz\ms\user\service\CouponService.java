package com.dz.ms.user.service;

import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.user.CouponsDetailDTO;
import com.dz.common.core.dto.user.CouponsListAllDTO;
import com.dz.common.core.dto.user.CouponsListDTO;
import com.dz.common.core.dto.user.ReceiveCouponDTO;
import com.dz.common.core.dto.user.RightsReceiveRecordDTO;
import com.dz.common.core.vo.ReceiveCouponVo;
import com.dz.ms.user.vo.CouponSelectVo;

import java.util.List;

/**
 * 我的礼券
 *
 * @author: Handy
 * @date: 2022/2/4 17:33
 */
public interface CouponService {

    CouponsListAllDTO getCouponList();

    PageInfo<CouponsListDTO> getCouponListExpire(CouponSelectVo param);

    CouponsDetailDTO getCouponDetail(String couponId,String couponCode);

    void couponSend(String couponId);

    ReceiveCouponDTO couponReceive(ReceiveCouponVo param);

    List<RightsReceiveRecordDTO> rightsReceiveRecord();
}
