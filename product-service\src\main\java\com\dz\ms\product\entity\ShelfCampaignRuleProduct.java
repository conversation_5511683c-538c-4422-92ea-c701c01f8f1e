package com.dz.ms.product.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 营销活动规则关联的货架商品
 *
 * @author: LiinNs
 * @date: 2024/11/27 15:15
 * 注意: 此表不受ModelToSql类管理
 */
@Getter
@Setter
@NoArgsConstructor
@Table("营销活动规则关联的货架商品")
@TableName(value = "shelf_campaign_rule_product")
public class ShelfCampaignRuleProduct implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "营销活动ID", isIndex = true)
    private Long campaignId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "营销活动规则ID", isIndex = true)
    private Long ruleId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "货架ID", isIndex = true)
    private Long shelfId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "货架商品ID", isIndex = true)
    private Long shelfProductId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "商品ID", isIndex = true)
    private Long productId;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, defaultValue = "1", comment = "1同当前库存 2活动规则库存")
    private Integer inventoryType;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "活动规则库存")
    private Integer ruleInventory;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, comment = "兑换积分")
    private Integer costPoint;
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "积分划线价")
    private Integer prePoint;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "每人限购数")
    private Integer everyoneLimit;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "规则开始时间")
    private Date ruleCreated;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "是否删除 0未删除 1已删除")
    @TableLogic
    private Integer isDeleted;

    public ShelfCampaignRuleProduct(Long id, Long campaignId, Long ruleId, Long shelfId, Long productId, Integer inventoryType, Integer ruleInventory, Integer costPoint, Integer prePoint) {
        this.id = id;
        this.campaignId = campaignId;
        this.ruleId = ruleId;
        this.shelfId = shelfId;
        this.productId = productId;
        this.inventoryType = inventoryType;
        this.ruleInventory = ruleInventory;
        this.costPoint = costPoint;
        this.prePoint = prePoint;
    }

}
