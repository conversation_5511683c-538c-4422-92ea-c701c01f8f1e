package com.dz.ms.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.ms.product.dto.ShelfCampaignRuleProductDTO;
import com.dz.ms.product.dto.req.ShelfCampaignRuleProductDelParamDTO;
import com.dz.ms.product.dto.req.ShelfCampaignRuleProductParamDTO;
import com.dz.ms.product.dto.res.ShelfCampaignRuleProductResDTO;
import com.dz.ms.product.entity.ShelfCampaignRuleProduct;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 营销活动规则关联的货架商品Mapper
 *
 * @author: LiinNs
 * @date: 2024/11/20 11:39
 */
@Repository
public interface ShelfCampaignRuleProductMapper extends BaseMapper<ShelfCampaignRuleProduct> {

    IPage<ShelfCampaignRuleProductResDTO> selPageList(Page<Object> objectPage, @Param("param") ShelfCampaignRuleProductParamDTO param);
    
    /**
     * 根据ids查询少量字段
     */
    List<ShelfCampaignRuleProductDTO> selLessListByIds(@Param("ids") List<Long> ids);

    /**
     * 查询少量字段
     */
    List<ShelfCampaignRuleProductDTO> selLessList(@Param("campaignIds") List<Long> campaignIds, @Param("ruleIds") List<Long> ruleIds, @Param("shelfProductIds") List<Long> shelfProductIds);

    /**
     * 查询所有字段
     */
    List<ShelfCampaignRuleProductDTO> selAllList(@Param("campaignIds") List<Long> campaignIds, @Param("ruleIds") List<Long> ruleIds, @Param("shelfProductIds") List<Long> shelfProductIds);

    /**
     * 根据条件删除营销活动规则关联的货架商品
     *
     * @param param 删除条件
     */
    void deleteByParam(@Param("param") ShelfCampaignRuleProductDelParamDTO param);

    int updateInventory(@Param("isAdd") Integer isAdd, @Param("id") Long id, @Param("num") Integer num);

    int validateInventory(@Param("id") Long id, @Param("num") Integer num);
}
