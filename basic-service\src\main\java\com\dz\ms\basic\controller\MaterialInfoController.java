package com.dz.ms.basic.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.base.dto.BaseDTO;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.basic.MaterialRelationParamDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.basic.MaterialFeginClient;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.dto.MaterialInfoDTO;
import com.dz.ms.basic.dto.MaterialSimpleDTO;
import com.dz.ms.basic.entity.MaterialInfo;
import com.dz.ms.basic.service.MaterialInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Api(tags="素材信息")
@RestController
public class MaterialInfoController implements MaterialFeginClient {

    @Resource
    private MaterialInfoService materialInfoService;

    /**
     * 分页查询素材信息
     * @param param
     * @return result<PageInfo<MaterialInfoDTO>>
     */
    @ApiOperation("分页查询素材信息")
	@GetMapping(value = "/crm/material/list")
    public Result<PageInfo<MaterialInfoDTO>> getMaterialInfoList(@ModelAttribute MaterialInfoDTO param) {
        Result<PageInfo<MaterialInfoDTO>> result = new Result<>();
        MaterialInfo materialInfo = BeanCopierUtils.convertObjectTrim(param,MaterialInfo.class);
        String name = materialInfo.getMaterialName();
        Integer type = materialInfo.getMaterialType();
        materialInfo.setMaterialName(null);
        if(null != type && type.equals(0)) {
            materialInfo.setMaterialType(null);
        }
        LambdaQueryWrapper<MaterialInfo> wrapper = new LambdaQueryWrapper<>(materialInfo);
        if(StringUtils.isNotEmpty(name)) {
            wrapper.like(MaterialInfo::getMaterialName,name);
        }
        if(null != type && type.equals(0)) {
            wrapper.le(MaterialInfo::getExpireTime,new Date());
        }
        else {
            wrapper.and(wp -> wp.isNull(MaterialInfo::getExpireTime).or().ge(MaterialInfo::getExpireTime,new Date()));
        }
        wrapper.orderByDesc(MaterialInfo::getId);
        IPage<MaterialInfo> page = materialInfoService.page(new Page<>(param.getPageNum(), param.getPageSize()), wrapper);
        result.setData(new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),BeanCopierUtils.convertList(page.getRecords(),MaterialInfoDTO.class)));
        return result;
    }

    /**
     * 新增素材信息
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增素材信息",type = LogType.OPERATELOG)
    @ApiOperation("新增素材信息")
    @PostMapping(value = "/material_info/add")
    public Result<Long> addMaterialInfo(@RequestBody MaterialInfoDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,true);
        MaterialInfo materialInfo = new MaterialInfo(param.getId(), param.getMaterialType(), param.getGroupId(), param.getMaterialName(),param.getMaterialUrl(), param.getReplaceUrl(), param.getVideoPoster(),param.getEffectiveTime(),param.getExpireTime());
        materialInfoService.save(materialInfo);
        result.setData(materialInfo.getId());
        return result;
    }

    /**
     * 更新素材信息
     * @param param
     * @return result<Object>
     */
    @SysLog(value = "更新素材信息",type = LogType.OPERATELOG)
    @ApiOperation("更新素材信息")
    @PostMapping(value = "/material_info/update")
    public Result<Long> updateMaterialInfo(@RequestBody MaterialInfoDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,false);
        MaterialInfo materialInfo = new MaterialInfo(param.getId(), param.getMaterialType(), param.getGroupId(), param.getMaterialName(),param.getMaterialUrl(), param.getReplaceUrl(), param.getVideoPoster(),param.getEffectiveTime(),param.getExpireTime());
        materialInfoService.updateById(materialInfo);
        result.setData(param.getId());
        return result;
    }

    /**
     * 验证保存参数
     * @param param
     */
    private void validationSaveParam(MaterialInfoDTO param,boolean isAdd) {
        if(isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if(!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
    }

    @SysLog(value = "批量新增素材",type = LogType.OPERATELOG)
    @ApiOperation("批量新增素材信息")
    @PostMapping(value = "/crm/material/add_batch")
    public Result<Object> addBatch(@RequestBody List<MaterialInfoDTO> list) {
        Result<Object> result = new Result<>();
        if(CollectionUtils.isEmpty(list)) {
            return result.paramErroResult("素材信息不能为空");
        }
        materialInfoService.addBatch(list);
        return result;
    }

    @SysLog(value = "批量更新素材",type = LogType.OPERATELOG)
    @ApiOperation("批量更新素材信息")
    @PostMapping(value = "/crm/material/update_batch")
    public Result<Object> updateBatch(@RequestBody List<MaterialInfoDTO> list) {
        Result<Object> result = new Result<>();
        if(CollectionUtils.isEmpty(list)) {
            return result.paramErroResult("素材信息不能为空");
        }
        for (MaterialInfoDTO material : list) {
            if(null == material.getId()) {
                return result.paramErroResult("素材ID不能为空");
            }
        }
        materialInfoService.updateBatch(list);
        return result;
    }
	
	/**
     * 根据ID列表删除素材信息
     * @param ids
     * @return result<Boolean>
     */
    @SysLog(value = "删除素材",type = LogType.OPERATELOG)
    @ApiOperation("根据ID列表删除素材信息")
	@PostMapping(value = "/crm/material/delete_byids")
    public Result<Boolean> deleteMaterialInfoByIds(@RequestBody List<Long> ids) {
        Result<Boolean> result = new Result<>();
        if(CollectionUtils.isEmpty(ids)) {
            return result.paramErroResult("素材ID不能为空");
        }
        materialInfoService.deleteMaterialByIds(ids);
        result.setData(true);
        return result;
    }

    /**
     * 根据ID列表更新素材分类
     * @param param
     * @return result<Boolean>
     */
    @SysLog(value = "更新素材分类",type = LogType.OPERATELOG)
    @ApiOperation("根据ID列表更新素材分类")
    @PostMapping(value = "/crm/material/update_group_byids")
    public Result<Boolean> updateMaterialGroupByIds(@RequestBody MaterialInfoDTO param) {
        Result<Boolean> result = new Result<>();
        if(CollectionUtils.isEmpty(param.getIds())) {
            return result.paramErroResult("素材ID不能为空");
        }
        if(ParamUtils.isNullOr0Long(param.getGroupId())) {
            return result.paramErroResult("分组ID不能为空");
        }
        materialInfoService.updateMaterialGroupByIds(param);
        result.setData(true);
        return result;
    }

    @SysLog(value = "更新素材过期时间",type = LogType.OPERATELOG)
    @ApiOperation("根据ID列表更新素材过期时间")
    @PostMapping(value = "/crm/material/update_time_byids")
    public Result<Boolean> updateMaterialTimeByIds(@RequestBody MaterialInfoDTO param) {
        Result<Boolean> result = new Result<>();
        if(CollectionUtils.isEmpty(param.getIds())) {
            return result.paramErroResult("素材ID不能为空");
        }
        if(null == param.getExpireTime()) {
            return result.paramErroResult("失效时间不能为空");
        }
        materialInfoService.updateMaterialTimeByIds(param);
        result.setData(true);
        return result;
    }

    @ApiOperation("分页查询即将过期和已过期素材信息")
    @GetMapping(value = "/crm/material/list_expire")
    public Result<PageInfo<MaterialInfoDTO>> getMaterialExpireList(BaseDTO param) {
        Result<PageInfo<MaterialInfoDTO>> result = new Result<>();
        result.setData(materialInfoService.getMaterialExpireList(param));
        return result;
    }

    @ApiOperation("根据素材id列表获取素材地址列表")
    @PostMapping(value = {"/app/material/list_by_ids","/crm/material/list_by_ids"})
    public Result<List<MaterialSimpleDTO>> getMaterialUrlByIds(@RequestBody List<Long> ids) {
        Result<List<MaterialSimpleDTO>> result = new Result<>();
        result.setData(materialInfoService.getMaterialUrlByIds(ids, SecurityContext.getUser().getTenantId()));
        return result;
    }

    @ApiOperation("保存素材关联业务信息")
    @PostMapping(value = "/material/relation/save")
    public Result<Long> saveMaterialRelation(@RequestBody MaterialRelationParamDTO param) {
        Result<Long> result = new Result<>();
        if(null == param.getRelationEnum() || null == param.getRelationId()) {
            return result.paramErroResult("必填参数不能为空");
        }
        materialInfoService.saveMaterialRelation(param);
        return result;
    }

    @ApiOperation("批量保存素材关联业务信息")
    @PostMapping(value = "/material/relation/save_batch")
    public Result<Long> saveBatchMaterialRelation(@RequestBody List<MaterialRelationParamDTO> list) {
        Result<Long> result = new Result<>();
        if(CollectionUtils.isEmpty(list)) {
            return result.paramErroResult("参数不能为空");
        }
        for (MaterialRelationParamDTO param : list) {
            if(null == param.getRelationEnum() || null == param.getRelationId()) {
                return result.paramErroResult("必填参数不能为空");
            }
        }
        materialInfoService.saveBatchMaterialRelation(list);
        return result;
    }

}
