package com.dz.ms.product.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.dto.DownloadCenterDTO;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.IdNumberDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.basic.ReportDownloadFeignClient;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.product.config.Globals;
import com.dz.ms.product.dto.ShelfPromotionDTO;
import com.dz.ms.product.dto.inreq.ShelfPromotionQueryDTO;
import com.dz.ms.product.dto.req.ShelfPromotionParamDTO;
import com.dz.ms.product.service.ShelfPromotionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Api(tags = "货架推广活动")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
@Slf4j
public class ShelfPromotionController {

    @Resource
    private ShelfPromotionService shelfPromotionService;
    @Resource
    private ReportDownloadFeignClient reportDownloadFeignClient;

    /**
     * 分页查询货架推广活动
     *
     * @param param
     * @return result<PageInfo < ShelfPromotionDTO>>
     */
    @ApiOperation("分页查询货架推广活动")
    @GetMapping(value = "/crm/shelf_promotion/list")
    public Result<PageInfo<ShelfPromotionDTO>> getShelfPromotionList(@ModelAttribute ShelfPromotionParamDTO param) {
        Result<PageInfo<ShelfPromotionDTO>> result = new Result<>();
        PageInfo<ShelfPromotionDTO> page = shelfPromotionService.getShelfPromotionList(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询货架推广活动
     *
     * @param id
     * @return result<ShelfPromotionDTO>
     */
    @ApiOperation("根据ID查询货架推广活动")
    @GetMapping(value = "/crm/shelf_promotion/info")
    public Result<ShelfPromotionDTO> getShelfPromotionById(@RequestParam("id") Long id) {
        Result<ShelfPromotionDTO> result = new Result<>();
        ShelfPromotionDTO shelfPromotion = shelfPromotionService.getShelfPromotionById(ShelfPromotionQueryDTO.builder().id(id).isThrow(true).isQryShelf(true).build());
        result.setData(shelfPromotion);
        return result;
    }

    /**
     * 新增货架推广活动
     *
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增货架推广活动", type = LogType.OPERATELOG)
    @ApiOperation("新增货架推广活动")
    @PostMapping(value = "/crm/shelf_promotion/add")
    public Result<Long> addShelfPromotion(@RequestBody ShelfPromotionDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param, true);
        Long id = shelfPromotionService.saveShelfPromotion(param);
        result.setData(id);
        return result;
    }

    /**
     * 更新货架推广活动
     *
     * @param param
     * @return result<Object>
     */
    @SysLog(value = "更新货架推广活动", type = LogType.OPERATELOG)
    @ApiOperation("更新货架推广活动")
    @PostMapping(value = "/crm/shelf_promotion/update")
    public Result<Long> updateShelfPromotion(@RequestBody ShelfPromotionDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param, false);
        shelfPromotionService.saveShelfPromotion(param);
        result.setData(param.getId());
        return result;
    }

    /**
     * 验证保存参数
     *
     * @param param
     */
    private void validationSaveParam(ShelfPromotionDTO param, boolean isAdd) {
        if (isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if (!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
    }

    /**
     * 根据ID删除货架推广活动
     *
     * @param param
     * @return result<Boolean>
     */
    @SysLog(value = "根据ID删除货架推广活动", type = LogType.OPERATELOG)
    @ApiOperation("根据ID删除货架推广活动")
    @PostMapping(value = "/crm/shelf_promotion/delete")
    public Result<Boolean> deleteShelfPromotionById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        shelfPromotionService.deleteShelfPromotionById(param);
        result.setData(true);
        return result;
    }

    /**
     * 根据ID修改启停状态
     * @param param ID NUMBER 通用DTO
     * @return result<Boolean>
     */
    @SysLog(value = "根据ID修改启停状态", type = LogType.OPERATELOG)
    @ApiOperation("根据ID修改启停状态")
    @PostMapping(value = "/crm/shelf_promotion/update_state")
    public Result<Boolean> updateStateById(@RequestBody IdNumberDTO param) {
        Result<Boolean> result = new Result<>();
        shelfPromotionService.updateStateById(param);
        result.setData(true);
        return result;
    }

    @ApiOperation("根据货架ID查询货架推广位")
    @GetMapping(value = "/app/shelf_promotion/info_by_shelf_id")
    public Result<ShelfPromotionDTO> getShelfPromotionByShelfId(@RequestParam("shelfId") Long shelfId) {
        Result<ShelfPromotionDTO> result = new Result<>();
        ShelfPromotionDTO shelfPromotion = shelfPromotionService.getShelfPromotionByShelfId(shelfId);
        result.setData(shelfPromotion);
        return result;
    }

    /**
     * 导出货架推广列表
     *
     * @return
     */
    @PostMapping(value = "/shelf_promotion/export_promotion_list")
    public Result<Void> exportPromotionList(@RequestBody DownloadAddParamDTO exportParam) {
        CurrentUserDTO user = SecurityContext.getUser();
        Globals.downloadThreadPool.execute(() -> {
            try {
                SecurityContext.setUser(user);
                shelfPromotionService.exportPromotionList(exportParam);
            } catch (Exception e) {
                log.error("导出错误", e);
                DownloadCenterDTO downloadCenterDTO = new DownloadCenterDTO();
                downloadCenterDTO.setId(exportParam.getDownloadCenterId());
                downloadCenterDTO.setErrorDesc(e.getMessage());
                downloadCenterDTO.setState(2);
                reportDownloadFeignClient.updateDownloadCenter(downloadCenterDTO);
            }
        });
        return new Result<>();
    }

}
