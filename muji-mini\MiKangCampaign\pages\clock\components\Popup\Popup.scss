.clockPopup {
    position: relative;
    background: transparent;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    // box-shadow: 0rpx 22rpx 74rpx 0rpx rgba(143, 119, 85, 0.35);

    .image {
        box-shadow: inset 0rpx 0rpx 0rpx 0rpx rgba(143, 119, 85, 0.35);
        // background-color: rgba(143, 119, 85, 0.3);
    }

    .imageLeft {
        position: absolute;
        top: -18rpx;
        left: 32rpx;
    }

    .imageRight {
        position: absolute;
        top: -18rpx;
        right: 32rpx;
    }

    .goPrizeDraw {
        position: absolute;
        top: 546rpx;
        width: 268rpx;
        height: 74rpx;
        // background-color: rgba(64, 224, 208, 0.438);
    }

    .goCoupon {
        position: absolute;
        top: 638rpx;
        width: 192rpx;
        height: 48rpx;
        // background-color: rgba(64, 224, 208, 0.438);
    }

    .swiper {
        width: 100%;
        height: 720rpx;
    }

    .swiper-item {
        height: 100%;
        width: 100%;

        .swiper-item-current0 {
            height: 595rpx;
            margin-top: 53rpx;
        }
    }

    .clockPopup-content {
        overflow: hidden;
        width: 610rpx;
        margin: 0 20rpx;
        height: 648rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        background-size: 100% 100%;
        background-position: center center;
        box-sizing: border-box;

        .iconfont {
            width: 120rpx;
            height: 62rpx;
            background-size: 100% 100%;
            margin-top: 157rpx;
        }

        .iconfontGift {
            width: 76rpx;
            height: 83rpx;
            margin-top: 126rpx;
        }

        .title {
            font-family: MUJIFont2020;
            font-weight: Medium;
            font-size: 36rpx;
            color: 500;
            line-height: 52rpx;
            margin-top: 48rpx;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            font-weight: Bold;

            text {
                // margin-top: 10rpx;
            }

            .title-text {
                color: #756453;
            }
        }

        .text,
        .num {
            width: 450rpx;
            font-family: MUJIFont2020;
            font-weight: 400;
            font-size: 20rpx;
            line-height: 36rpx;
            text-align: center;
            color: #888888;
            margin-top: 24rpx;
            margin-left: 8rpx;
        }

        .sub-text {
            color: #3c3c43;
            text-decoration: underline;
            font-weight: bolder;
        }

        .clock-btn {
            // margin-top: 48rpx;
            margin-bottom: 148rpx;
        }
    }

    .clockPopup-close {
        padding-top: 10rpx;
        height: 80rpx;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .clockPopup-closeBox {
        height: 80rpx;
        width: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.selfModal-closeBox {
    border: 2rpx solid #ffffff;
    border-radius: 50%;
}
