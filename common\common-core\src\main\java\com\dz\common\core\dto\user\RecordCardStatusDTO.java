package com.dz.common.core.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 任务详情
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "打卡状态")
public class RecordCardStatusDTO {

    @ApiModelProperty(value = "打卡状态，1打卡成功，2门店不在百米范围内，3超过门店可打卡次数")
    private Integer cardStatus;
    @ApiModelProperty(value = "每天完成任务限制次数")
    private Integer limitationNum;
    @ApiModelProperty(value = "每家门店限制打卡次数")
    private Integer storeCardNum;
    @ApiModelProperty(value = "当天打卡门店名称")
    private String clocStoreNameNow;
    private String clocStoreSn;
    @ApiModelProperty(value = "已打卡门店次数")
    private Integer clocStoreNumNow;
    private Integer checkStatus;
}
