import { apiBooking } from '../../../api/index'
import { onInput } from '../../../utils/form'

const app = getApp()
const getDataDefault = () => {
  return {
    loading: false,
    appointmentDates: [],
    appointmentDateShow: false,
    appointmentDateIndex: 0,
    appointmentDateIndexBak: -99,
    appointmentSlotShow: false,
    appointmentSlots: [],
    appointmentSlotIndex: 0,
    formFields: {
      appointmentDate: '',
      appointmentSlot: '',
      name: '',
      phone: ''
    },
    resData: {}
  }
}

Page({
  data: getDataDefault(),

  onLoad () {
    this.init()
  },
  async init () {
    this.setData(getDataDefault())
    this.getBookingInfo()
  },
  async setDateVisibleShow () {
    this.setData({ appointmentDateShow: true })
  },
  async setDateVisibleHide () {
    this.setData({ appointmentDateShow: false })
  },
  async getBookingDates () {
    const res = await apiBooking.getBookingDates()
    this.setData({ appointmentDates: res.data })
  },
  async setSlotVisibleShow () {
    this.setData({ appointmentSlotShow: true })
  },
  async setSlotVisibleHide () {
    this.setData({ appointmentSlotShow: false })
  },
  async getBookingSlots (data) {
    const res = await apiBooking.getBookingSlots(data)
    this.setData({ appointmentSlots: res.data })
  },
  async getBookingInfo () {
    this.setData({ loading: true })
    const res = await apiBooking.getBookingInfo().finally(() => {
      this.setData({ loading: false })
    })
    this.data.formFields.phone = res.data.phone || ''
    this.setData({ resData: res.data, formFields: this.data.formFields })
    if (res.data.alreadyBooked === false) {
      this.getBookingDates()
    }
  },
  async bindPickerChangeDate (e) {
    console.log('bindPickerChangeDate：', e)
    this.setData({ appointmentDateIndex: e.detail.value })
    if (this.data.appointmentDateIndexBak === this.data.appointmentDateIndex) return
    this.data.appointmentDateIndexBak = this.data.appointmentDateIndex
    console.log('trigger bindPickerChangeDate：', e)
    const item = this.data.appointmentDates[this.data.appointmentDateIndex]
    const appointmentDate = item.id
    this.data.formFields.appointmentDate = appointmentDate
    this.data.formFields.appointmentSlot = ''
    this.setData({ formFields: this.data.formFields, appointmentSlotIndex: 0 })
    await this.getBookingSlots({ appointmentDate })
  },
  bindPickerChangeSlot (e) {
    console.log('bindPickerChangeSlot：', e)
    this.setData({ appointmentSlotIndex: e.detail.value })
    const item = this.data.appointmentSlots[this.data.appointmentSlotIndex]
    this.data.formFields.appointmentSlot = item.id
    this.setData({ formFields: this.data.formFields })
  },
  onInput,
  async submit () {
    if (!app.ifRegister()) return

    const { appointmentDate, appointmentSlot, name, phone } = this.data.formFields

    if (!appointmentDate) {
      return wx.$mp.showToast({ title: '请选择日期' })
    } else if (!appointmentSlot) {
      return wx.$mp.showToast({ title: '请选择场次' })
    } else if (!name) {
      return wx.$mp.showToast({ title: '请输入姓名' })
    } else if (name.length > 10) {
      return wx.$mp.showToast({ title: '姓名最多十个字' })
    }
    // else if (!phone) {
    //   return wx.$mp.showToast({ title: '请输入手机号' })
    // } else if (!wx.$mp.validate.isPhoneNumber(phone)) {
    //   return wx.$mp.showToast({ title: '请输入正确的手机号' })
    // }

    this.setData({ loading: true })
    await apiBooking.setBookingInfo(this.data.formFields).finally(() => {
      this.setData({ loading: false })
    })
    this.getBookingInfo()
  }
})
