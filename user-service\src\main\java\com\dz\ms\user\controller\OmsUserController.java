package com.dz.ms.user.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.base.utils.MD5;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.constants.CommonConstants;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.user.constants.CacheKeys;
import com.dz.ms.user.dto.OmsUserDTO;
import com.dz.ms.user.dto.UserRoleDTO;
import com.dz.ms.user.entity.OmsUser;
import com.dz.ms.user.service.OmsRoleService;
import com.dz.ms.user.service.OmsUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.dz.ms.user.utils.BCryptPasswordEncoder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;

@Api(tags="OMS-系统用户信息")
@RestController
@RequestMapping(value="/oms")
public class OmsUserController {

    @Resource
    private OmsUserService omsUserService;
    @Resource
    private OmsRoleService omsRoleService;
    @Resource
    private RedisService redisService;

    /**
     * 分页查询OMS-系统用户信息
     * @param param
     * @return result<PageInfo<OmsUserDTO>>
     */
    @ApiOperation("分页查询OMS-系统用户信息")
    @GetMapping(value = "/user/list")
    public Result<PageInfo<OmsUserDTO>> getOmsUserList(@ModelAttribute OmsUserDTO param) {
        Result<PageInfo<OmsUserDTO>> result = new Result<>();
        OmsUser omsUser = BeanCopierUtils.convertObjectTrim(param,OmsUser.class);
        LambdaQueryWrapper<OmsUser> wrapper = new LambdaQueryWrapper<>(omsUser);
        wrapper.ne(OmsUser :: getIsAdmin,"1");
        IPage<OmsUser> page = omsUserService.page(new Page<>(param.getPageNum(), param.getPageSize()),wrapper);
        result.setData(new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),BeanCopierUtils.convertList(page.getRecords(), OmsUserDTO.class)));
        return result;
    }

    /**
     * 根据ID查询OMS-系统用户信息
     * @param id
     * @return result<OmsUserDTO>
     */
    @ApiOperation("根据ID查询OMS-系统用户信息")
    @GetMapping(value = "/user/info")
    public Result<OmsUserDTO> getOmsUserById(@RequestParam("id") Long id) {
        Result<OmsUserDTO> result = new Result<>();
        result.setData(getUserByUid(id));
        return result;
    }

    /**
     * 根据UID查询OMS系统用户信息
     * @return
     */
    private OmsUserDTO getUserByUid(Long uid) {
        OmsUserDTO omsUserDTO = (OmsUserDTO) redisService.get(CacheKeys.OMS_USER_INFO+uid);
        if(null == omsUserDTO) {
            OmsUser omsUser = omsUserService.getById(uid);
            omsUserDTO = BeanCopierUtils.convertObject(omsUser,OmsUserDTO.class);
            redisService.set(CacheKeys.OMS_USER_INFO+uid,omsUserDTO, CommonConstants.DAY_SECONDS);
        }
        return omsUserDTO;
    }

    /**
     * 保存OMS-系统用户信息
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "保存OMS-系统用户信息",type = LogType.OPERATELOG)
    @ApiOperation("保存OMS-系统用户信息")
    @PostMapping(value = "/user/save")
    public Result<Long> save(@RequestBody OmsUserDTO param) {
        Result<Long> result = new Result<>();
        OmsUser omsUser = new OmsUser(param.getId(), param.getUsername(), null, null, param.getRealname(), param.getNickname(), param.getHeadUrl(), param.getMobile(), param.getState(), null, param.getIsAdmin());
        if(ParamUtils.isNullOr0Long(omsUser.getId())) {
            String password = "muji6688";
            String salt = UUID.randomUUID().toString().trim().replaceAll("-","");
            password = MD5.encode(password+salt);
            String encode = new BCryptPasswordEncoder().encode(password);
            omsUser.setPassword(encode);
            omsUser.setSalt(salt);
            omsUser.setIsOnline(2);
            omsUserService.save(omsUser);
        }
        else {
            omsUser.setPassword(null);
            omsUser.setSalt(null);
            omsUserService.updateById(omsUser);
            redisService.del(CacheKeys.OMS_USER_INFO+omsUser.getId());
        }
        if(null != param.getRoleId()) {
            Long userid = null == param.getId() ? omsUser.getId() : param.getId();
            UserRoleDTO userRole = new UserRoleDTO();
            userRole.setUserId(userid);
            userRole.setRoleId(param.getRoleId());
            omsUserService.bindRole(userRole, SecurityContext.getUser().getUid());
        }
        result.setData(omsUser.getId());
        return result;
    }

    /**
     * 根据ID删除OMS系统用户
     * @param param
     * @return result<Boolean>
     */
    @SysLog(value = "根据ID删除OMS系统用户",type = LogType.OPERATELOG)
    @ApiOperation("根据ID删除OMS系统用户")
    @PostMapping(value = "/user/delete")
    public Result<Boolean> deleteOmsUserById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        omsUserService.removeById(param.getId());
        result.setData(true);
        return result;
    }

    @ApiOperation("获取用户角色")
    @GetMapping(value = "/user/role")
    public Result<Long> getUserRole(@RequestParam("uid") Long uid) {
        Result<Long> result = new Result<>();
        Long roleId = omsUserService.getUserRole(uid);
        result.setData(roleId);
        return result;
    }

    @SysLog(value = "绑定用户角色",type = LogType.OPERATELOG)
    @ApiOperation("绑定用户角色")
    @PostMapping(value = "/user/bind_role")
    public Result<Boolean> bindRole(@RequestBody UserRoleDTO param) {
        Result<Boolean> result = new Result<>();
        omsUserService.bindRole(param,SecurityContext.getUser().getUid());
        result.setData(true);
        return result;
    }

    @ApiOperation("获取当前用户权限列表")
    @GetMapping(value = "/user/identity")
    public Result<OmsUserDTO> getUserPermitCodes() {
        Result<OmsUserDTO> result = new Result<>();
        Long uid = SecurityContext.getUser().getUid();
        OmsUserDTO omsUser = getUserByUid(uid);
        Long roleId = 0L;
        if(ParamUtils.Integer2int(omsUser.getIsAdmin()) != 1) {
            roleId = omsUserService.getUserRole(uid);
        }
        List<String> list = omsRoleService.getRolePermitCodes(roleId);
        omsUser.setRoleId(roleId);
        omsUser.setPermits(list);
        result.setData(omsUser);
        return result;
    }

}

