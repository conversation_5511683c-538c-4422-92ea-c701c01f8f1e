package com.dz.ms.sales.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/4
 */
@Data
public class CampaignTaskStateDTO {

    @ApiModelProperty("用户类别 1报名用户 2打卡用户")
    private Integer userType;

    @ApiModelProperty("报名状态 0未报名 1已报名")
    private Integer taskState;

    @ApiModelProperty("打卡状态 0未完成 1已完成")
    private Integer taskState1;

    @ApiModelProperty("活动标识")
    private String campaignCode;

    @ApiModelProperty("活动状态 0未开始 1进行中 2公示期 3已结束")
    private Integer campaignState;

}
