package com.dz.ms.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.core.dto.basic.QywxConfigDTO;
import com.dz.common.core.dto.wechat.DecryptEnterpriseUserDTO;
import com.dz.ms.basic.entity.QywxConfig;

/**
 * 企业微信配置接口
 * @author: Handy
 * @date:   2022/07/20 21:18
 */
public interface QywxConfigService extends IService<QywxConfig> {

    /**
     * 根据租户ID获取企业微信access_token
     * @param type
     * @param tenantId
     * @param cleanCach
     * @return
     */
    String getQywxAccessToken(Integer type, Long tenantId, Boolean cleanCach);

    /**
     * 根据租户ID获取企业微信配置
     * @param tenantId
     * @return
     */
    QywxConfigDTO getQywxConfigByTenantId(Long tenantId);

    DecryptEnterpriseUserDTO decryptEnterpriseUserDTO(String encryptedData, String iv, String sessionKey);
}
