// MiKangCampaign/pages/prizeDraw/prizeDraw.js
const app = getApp();
import {
  throttle
} from "../../../utils/util";
import {
  getCampaignType,
  getLottery,
  getLotteryList,
  getLotteryUser,
  getUserPrizes,
} from "../../api/index.js";
let timer = null,
  timer2 = null;
Page({
  /**
   * 页面的初始数据
   */
  data: {
    giftList: [
      // {
      //   imageUrl: 'prizeAward1.png',
      //   id: 1
      // },
      // {
      //   imageUrl: 'prizeAward2.png',
      //   id: 2
      // },
      // {
      //   imageUrl: 'prizeAward3.png',
      //   id: 3
      // },
      // {
      //   imageUrl: 'prizeAward4.png',
      //   id: 4
      // },
      // {
      //   imageUrl: 'prizeAward5.png',
      //   id: 5
      // },
      // {
      //   imageUrl: 'prizeAward6.png',
      //   id: 6
      // },
      // {
      //   imageUrl: 'prizeAward7.png',
      //   id: 7
      // },
      // {
      //   imageUrl: 'prizeAward8.png',
      //   id: 8
      // },
      // {
      //   imageUrl: 'prizeAward9.png',
      //   id: 9
      // },
      // {
      //   imageUrl: 'prizeAward10.png'
      // },
    ],
    campaignCode: wx.getStorageSync("campaignCode") || "sign_in",
    showPopup: false,
    infoLottery: {},
    checkIn: 0, // 选中的
    surplusCount: "", // 剩余抽奖机会
    totalCount: "", // 总抽奖机会
    list1: [],
    surplusData: undefined,
    isSurplus: false,
    clicked: false,
    animateClass: '',
    frontFadeClass: '',
    backFadeClass: '',
    backImgLoaded: false,
    backImg: '',
    textAnim1: null,
    textAnim2: null,
    isAnimateStart: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 初始化两段文字动画
    const anim1 = wx.createAnimation({
      duration: 0
    });
    anim1.opacity(1).scale(1).step();

    // const anim2 = wx.createAnimation({
    //   duration: 0
    // });
    // anim2.opacity(0).scale(0.1).step();

    this.setData({
      textAnim1: anim1.export(),
      // textAnim2: anim2.export()
    });
    this.getDatas();
  },
  getDatas() {
    getCampaignType().then((res) => {
      wx.setStorageSync("campaignCode", res.data.campaignCode);
      this.setData({
        CampaignData: res.data,
      });
    });
  },
  // 获取抽奖列表
  async getLisdt() {
    let {
      data
    } = await getUserPrizes({
      campaignCode: this.data.campaignCode,
      pageNum: 1,
      pageSize: 10,
    });
    console.log(data, "data 抽奖记录");
    this.setData({
      list1: data.list,
    });
  },
  hideShare() {
    // 禁止直接转发 需要点击补卡 进行转发
    wx.hideShareMenu({
      menus: ["shareAppMessage", "shareTimeline"],
    });
  },
  showShare() {
    wx.showShareMenu({
      menusmenus: ["shareAppMessage"],
    });
  },
  getData() {
    getLotteryUser({
      campaignCode: this.data.campaignCode,
    }).then(({
      data
    }) => {
      console.log("页面判断是否有剩余抽奖次数", data);
      this.setData({
        surplusCount: data.surplusCount,
        totalCount: data.totalCount,
        isShare: data.isShare,
      });
      console.log(
        wx.getStorageSync("isPrizeDialog"),
        "wx.getStorageSync('isPrizeDialog') 打印"
      );
      // 没有分享 弹弹框
      // if (!wx.getStorageSync('isPrizeDialog')) {
      //   wx.setStorageSync('isPrizeDialog', 1)
      //   this.openPopup()
      // }
    });
  },
  getList() {
    getLotteryList({
      campaignCode: this.data.campaignCode,
    }).then(({
      data
    }) => {
      // 默认第一个和最后一个
      data = [
        ...data,
      ];
      console.log(data, "抽奖列表");
      this.setData({
        giftList: data,
      });
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getData();
    this.getList();
    this.getLisdt();
  },
  openPopup() {
    this.setData({
      showPopup: true,
    });
  },
  closePopup: app.debounce(async function () {
    this.setData({
      showPopup: false,
    });
  }),
  openActive() {
    // let randomNum = Math.ceil(Math.random() * 7)
    // this.setData({
    //   checkIn: randomNum
    // })
  },
  printNumbers(n) {
    if (n > 8)
      this.setData({
        checkIn: 2,
      });
    // 终止条件，防止无限递归
    else {
      this.setData({
        checkIn: n + 1,
      });
    }
    // 递归调用，n自增
  },
  closeActive() {},
  // 抽取惊喜礼品
  submit: app.debounce(async function () {
    wx.$mp.track({
      event: "lucky_lottery_click",
    });
    let that = this;
    let {
      totalCount,
      surplusCount
    } = that.data;
    getLottery({
        campaignCode: this.data.campaignCode,
      })
      .then(({
        data
      }) => {
        console.log(data, "抽奖");
        this.setData({
          surplusData: data,
        });
        // wx.redirectTo({
        //     url: `/MiKangCampaign/pages/Winning/Winning?id=${data.id}`,
        // });
      })
      .then(() => {
        console.log(totalCount, "totalCount 总共抽奖次数");
        console.log(surplusCount, "surplusCount 剩余抽奖次数");
        this.getData();
        this.setData({
          isSurplus: true,
        });
      })
      .catch((err) => {});
  }),
  activeRules() {
    wx.navigateTo({
      url: "/MiKangCampaign/pages/activityRules/activityRules",
    });
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    // 页面分享
    return {
      title: "和我一起体验「米糠护肤」开启五天打卡活动",
      imageUrl: wx.$config.ossImg + "/MiKangCampaign/mk-share-card-1.png",
      path: "/MiKangCampaign/pages/index/index",
    };
  },

  /**
   * 前往 我的礼券
   */
  toCoupon: throttle(async function (e) {
    // wx.$mp.track({
    //   event: 'member_coupon_click'
    // })
    if (app.ifRegister()) {
      app.subscribe("coupon").then(() => {
        wx.$mp.navigateTo({
          url: "/pages/myCoupon/myCoupon",
          success: (result) => {},
          fail: (res) => {
            console.log(res);
          },
          complete: (res) => {},
        });
      });
    }
  }),

  /**
   * 前往 积分商城
   */
  toShop: app.debounce(async function (e) {
    // console.log(e);
    wx.$mp.switchTab({
      url: "/pages/life/life",
    });
  }),

  onImageClick() {
    if (this.data.isSurplus) return;
    wx.preloadWebview?.()
    this.setData({
      isSurplus: true
    });

    wx.$mp.track({
      event: "lucky_lottery_click",
    });
    let that = this;
    let {
      totalCount,
      surplusCount
    } = that.data;
    getLottery({
        campaignCode: this.data.campaignCode,
      })
      .then(({
        data
      }) => {
        console.log(data, "抽奖");
        this.setData({
          surplusData: data,
        });

        const newImg = data.imageUrl ? `${this.data.$cdn}/MiKangCampaign/${data.imageUrl}` : `${this.data.$cdn}/MiKangCampaign/mk-gift-ticket-icon.png`;

        // 第一步：旋转到 90°
        const step1 = wx.createAnimation({
          duration: 300,
          timingFunction: 'linear'
        });
        step1.rotateY(-90).step();
        this.setData({
          flipAnim: step1.export(),
        });

        // 在旋转到 45°时淡出
        setTimeout(() => {
          this.setData({
            frontOpacity: 0
          });
        }, 500);
        console.log(newImg, 'newImg')
        // 加载新图片
        this.setData({
          backImg: newImg,
          backOpacity: 1
        });

        // 第二步：旋转到 180°
        const step2 = wx.createAnimation({
          duration: 1000,
          timingFunction: 'linear'
        });
        step2.rotateY(-180).step();
        this.setData({
          flipAnim: step2.export(),
        });
        setTimeout(() => {
          // 当前文字淡出 + 缩小
          const textAnim1 = wx.createAnimation({
            duration: 800,
            timingFunction: 'ease-in-out'
          });
          textAnim1.opacity(0).scale(0.5).step();
          this.setData({
            textAnim1: textAnim1.export(),
          });
          setTimeout(() => {
            this.setData({
              isAnimateStart: true,
            });
            setTimeout(() => {
              const textAnim2 = wx.createAnimation({
                duration: 800,
                timingFunction: 'ease-in-out'
              });
              textAnim2.opacity(1).scale(1).step();
              this.setData({
                textAnim2: textAnim2.export()
              });
              setTimeout(() => {
                wx.redirectTo({
                  url: `/MiKangCampaign/pages/Winning/Winning?id=${data.id}`,
                });
              }, 3000);
            }, 0);
          }, 800);
        }, 1300);
      })
      .then(() => {
        console.log(totalCount, "totalCount 总共抽奖次数");
        console.log(surplusCount, "surplusCount 剩余抽奖次数");
        this.getData();
        this.setData({
          isSurplus: true,
        });
      })
      .catch((err) => {
        this.setData({
          isSurplus: false
        });
      });


  },

  // goWinning: () => {
  //   wx.redirectTo({
  //       url: `/MiKangCampaign/pages/Winning/Winning?`,
  //   });
  // }

});
