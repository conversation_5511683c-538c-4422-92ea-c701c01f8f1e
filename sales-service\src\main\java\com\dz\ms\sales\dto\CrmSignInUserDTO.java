package com.dz.ms.sales.dto;


import com.dz.common.core.annotation.Columns;
import com.dz.common.core.enums.ColumnType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/17
 */
@Data
public class CrmSignInUserDTO {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "用户打卡记录ID")
    private Long signInUserId;

    @ApiModelProperty(value = "日期(yyyyMMdd)")
    private String signInDate;

    @ApiModelProperty(value = "活动标识")
    private String campaignCode;
    @ApiModelProperty(value = "一级渠道")
    private String channelOne;
    @ApiModelProperty(value = "二级渠道")
    private String channelTwo;

    @ApiModelProperty(value = "用户ID")
    private Long uid;
    @ApiModelProperty(value = "openid")
    private String openid;
    @ApiModelProperty(value = "unionid")
    private String unionid;
    @ApiModelProperty(value = "会员名")
    private String username;
    @ApiModelProperty(value = "手机号码")
    private String mobile;
    @ApiModelProperty(value = "会员卡号")
    private String cardNo;

    @ApiModelProperty(value = "打卡天数，第几天，开启打卡为第0天")
    private Integer days;

    @ApiModelProperty(value = "素材链接")
    private String materialUrl;
    @ApiModelProperty(value = "期待")
    private String expectation;

    // 第1-6天
    @ApiModelProperty(value = "舒适度")
    private Integer comfort;
    @ApiModelProperty(value = "保湿效果")
    private Integer moisturize;
    @ApiModelProperty(value = "吸收速度")
    private Integer absorption;
    @ApiModelProperty(value = "持久效果")
    private Integer persistence;
    @ApiModelProperty(value = "改善")
    private String improve;
    @ApiModelProperty(value = "整体满意度")
    private Integer satisfaction;

    // 第7天
    @ApiModelProperty(value = "使用感受")
    private String feeling;
    @ApiModelProperty(value = "是否购买")
    private Integer purchase;
    @ApiModelProperty(value = "推荐")
    private Integer recommend;
    @ApiModelProperty(value = "评分")
    private Integer score;


    @ApiModelProperty(value = "打卡状态 0未打卡 1已打卡 2缺卡")
    private Integer state;
    @ApiModelProperty(value = "是否补卡 0正常打卡 1补卡")
    private Integer supplementFlag;

    @ApiModelProperty(value = "打卡时间")
    private Date signInTime;
    @ApiModelProperty(value = "补卡时间")
    private Date supplementTime;


}
