// MiKangCampaign/pages/Participate/Participate.js
const app = getApp();
import { getCampaignType } from "../../api/index.js";
Page({
    /**
     * 页面的初始数据
     */
    data: {
        title1: "恭喜您成为",
        title2: "极简护肤体验官",
        text: "恭喜您成为体验官\n 即刻体验7日极简护肤吧",
        text1: "完成7日打卡的体验官将获得\n",
        text2: "更有惊喜好礼抽奖机会1次，最高可获得\n",
        text3: "敏感肌水奶套组正装",
        showRules: false,
        showAnnouncement: false,
        isShowTime: false, // 时间判断
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        // 获取当前时间
        const currentTime = new Date();
        const showDate = new Date("2025/03/05 10:00");
        this.setData({
            isShowTime: currentTime.getTime() >= showDate.getTime(),
        });
        this.getData();
    },
    getData() {
        getCampaignType().then((res) => {
            wx.setStorageSync("campaignCode", res.data.campaignCode);
            this.setData({
                CampaignData: res.data,
            });
        });
    },
    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {},

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {},
    // 跳转到活动规则页面
    onTapRule: app.debounce(async function () {
        wx.$mp.navigateTo({
            url: "/MiKangCampaign/pages/activityRules/activityRules",
        });
    }),
    // 跳转公示结果页
    onTapRule1: app.debounce(async function () {
        this.setData({
            showAnnouncement: true,
        });
        // wx.$mp.navigateTo({
        //   url: "/MiKangCampaign/pages/announcement/announcement",
        // });
    }),
    close() {
        this.setData({
            showAnnouncement: false,
        });
    },
    closeRules() {
        this.setData({
            showRules: false,
        });
    },
    // 参与打卡
    submit: app.debounce(async function () {
        wx.$mp.track({
            event: "open_punch_in_click",
        });
        wx.navigateTo({
            url: `/MiKangCampaign/pages/activateCheckIn/activateCheckIn`,
        });
    }),
    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {},

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {},

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {},

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {},

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {
        // 页面分享
        return {
            title: "和我一起体验「米糠护肤」开启五天打卡活动",
            imageUrl: wx.$config.ossImg + "/MiKangCampaign/mk-share-card-1.png",
            path: "/MiKangCampaign/pages/index/index",
        };
    },
});
