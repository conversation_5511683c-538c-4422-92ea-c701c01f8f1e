package com.dz.common.core.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StaticItemParamDTO {

    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "商品ID")
    private Long productId;
    @ApiModelProperty(value = "货架商品ID")
    private Long shelfProductId;
    @ApiModelProperty(value = "商品数量")
    private Integer number;
}
