package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 小程序导航自定义配置
 * @author: Handy
 * @date:   2022/11/21 10:05
 */
@Getter
@Setter
@NoArgsConstructor
@Table("小程序导航自定义配置")
@TableName(value = "navigation_config")
public class NavigationConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR,length = 32,isNull = true,comment = "UI名称")
    private String uiName;
    @Columns(type = ColumnType.VARCHAR,length = 32,isNull = true,comment = "主色值")
    private String color;
    @Columns(type = ColumnType.VARCHAR,length = 32,isNull = true,comment = "背景色")
    private String bgColor;
    @Columns(type = ColumnType.VARCHAR,length = 32,isNull = true,comment = "选中后色值")
    private String selectedColor;
    @Columns(type = ColumnType.TEXT,length = 0,isNull = true,comment = "导航配置内容json")
    private String content;
    @Columns(type = ColumnType.INT,length = 0,isNull = false, defaultValue = "0",comment = "字体大小")
    private Integer fontSize;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = false, defaultValue = "0",comment = "是否选中 0未选中 1选中")
    private Integer checked;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = true,comment = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;

    public NavigationConfig(Long id, String color, String selectedColor, String content, Integer checked) {
        this.id = id;
        this.color = color;
        this.selectedColor = selectedColor;
        this.content = content;
        this.checked = checked;
    }
}
