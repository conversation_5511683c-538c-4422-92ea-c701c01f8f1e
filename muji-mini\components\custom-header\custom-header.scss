.custom-header-wrap {
  &.isFixed {
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 10;
    pointer-events: none;
  }

  .custom-header {
    position: fixed;
    width: 100%;
    height: inherit;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    padding-top: var(--status);
    color: var(--text-nav-color);
    z-index: var(--zIndexFixed);
    transform: translate3d(0, 0, 0);
    background-color: var(--bg);
    box-sizing: border-box;

    .logo {
      height: 40rpx;
      width: 256rpx;
      overflow: hidden;
      margin-left: 40rpx;

      image {
        height: 40rpx;
        width: 256rpx;
      }
    }
  }

  .custom-header-flex {
    width: 100%;
    display: flex;
    font-size: 14px;
    padding: 0 10px;
  }

  .custom-header-content {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 36rpx;
  }

  .custom-header-back-icon {
    // padding: 10px 20px 10px 0;
    width: 60rpx;
    height: 60rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    font-size: 36rpx;
    pointer-events: auto;
  }

}
