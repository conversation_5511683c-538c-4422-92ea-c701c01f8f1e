<template>
  <a-collapse expandIconPosition="end" v-model:activeKey="activeKey" :bordered="false" style="background: rgb(255, 255, 255)">
    <template #expandIcon="{ isActive }">
      <UpOutlined :rotate="isActive ? 180 : 0" />
    </template>
    <a-collapse-panel key="1" header="顶栏设置">
      <a-collapse expandIconPosition="end" class="collapse" v-model:activeKey="activeKey1" default-active-key="1-1" :bordered="false" style="background: rgb(255, 255, 255)">
        <a-collapse-panel key="1-1" header="基本设置">
          <pageNav :disabled="disabled" :addParams="addParams.navSetting" :navSetting="navSetting" :width="addParams.pageSetting.pageWidth" :components="addParams.componentSetting"></pageNav>
        </a-collapse-panel>
      </a-collapse>
    </a-collapse-panel>
  </a-collapse>
</template>
<script setup>
import pageNav from './pageNav.vue'
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  addParams: {
    type: Object,
    default() {
      return {}
    }
  },
  // 页面选择
  pageSetting: {
    type: Array,
    default() {
      return [
        { key: '1', value: '常规页', },
        { key: '2', value: '加载页', },
        { key: '3', value: '开屏页', },
        { key: '4', value: '弹窗', },
      ]
    }
  },
  navSetting: {
    type: Array,
    default() {
      return [
        { value: 1, name: '固定标题' },
        { value: 2, name: '完全沉浸' },
        { value: 3, name: '滑动恢复' },
        { value: 4, name: '固定恢复' },
      ]
    }
  }
})
const { activeKey, activeKey1 } = toRefs(reactive({
  activeKey: ['1'],
  activeKey1: ['1-1']
}))


// 上传图片 视频
const uploadSuccess = async (data) => {
  let { form, path, imgUrl } = data;
  form.pageSetting[path] = imgUrl
}


// 修改颜色
const changeColor = async (key, color) => {
  props.addParams.pageSetting[key] = color
}

// 页面类型变化
const changePageType = (e) => {
  if (e.target.value != 4) {
    props.addParams.pageSetting.pageWidth = 375;
  }
}

</script>

<style>
</style>
