package com.dz.common.core.dto.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 租户设置DTO
 * @author: Handy
 * @date:   2022/08/30 23:04
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "租户设置")
public class TenantConfigDTO {

    @ApiModelProperty(value = "租户设置ID")
    private Long id;
    @ApiModelProperty(value = "用户信息是否依赖CRM 0否 1是")
    private Integer userCrm;

}
