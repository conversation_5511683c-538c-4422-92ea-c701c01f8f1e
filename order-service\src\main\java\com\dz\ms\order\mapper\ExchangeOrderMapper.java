package com.dz.ms.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.core.dto.order.OrderListAppDTO;
import com.dz.common.core.dto.order.OrderListAppParamDTO;
import com.dz.ms.order.dto.CrmExchangeOrderListDTO;
import com.dz.ms.order.dto.req.CrmExchangeOrderParamDTO;
import com.dz.ms.order.entity.ExchangeOrder;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 订单主表Mapper
 *
 * @author: LiinNs
 * @date: 2024/12/09 10:38
 */
@Repository
public interface ExchangeOrderMapper extends BaseMapper<ExchangeOrder> {

    /**
     * 分页查询订单列表
     *
     * @param objectPage
     * @param param
     * @return
     */
    IPage<CrmExchangeOrderListDTO> selectPageByParam(@Param("page") Page<Object> objectPage,
                                                     @Param("param") CrmExchangeOrderParamDTO param);

    List<CrmExchangeOrderListDTO> selectListByParam(@Param("param") CrmExchangeOrderParamDTO param);

    /**
     * 分页查询APP订单列表
     *
     * @param page
     * @param param
     * @return
     */
    IPage<OrderListAppDTO> listApp(Page<OrderListAppDTO> page, @Param("param") OrderListAppParamDTO param);

    /**
     * 获取货架兑换人数
     *
     * @param shelfId
     * @return
     */
    Integer selectExchangePeople(@Param("shelfId") Long shelfId);
}
