package com.dz.ms.user.dto.dkeyam.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 获取单个用户的信息入参
 * @author: fei
 * @date: 2025/01/22 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CallbackGetOneUserParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("用户登录名")
    private String loginName;

    @ApiModelProperty("密钥（认证系统中没有配置，则不会传此参数）")
    private String secretKey;
}
