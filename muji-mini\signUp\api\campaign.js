// 获取当前用户的活动信息 (判断是打卡还是报名)
exports.getCampaignType = data => wx.$request({
  url: '/app/sales/campaign/user/order_match',
  data,
  method: 'get'
})

// 判断是否报名
exports.getEnrollInfo = data => wx.$request({
  url: '/app/sales/enroll/info',
  data,
  method: 'get'
})

// 查询用户是否参与打卡
exports.getSignInOpen = data => wx.$request({
  url: '/app/sales/signIn/user/open',
  data,
  method: 'get'
})

// 填写报名信息
exports.enrollSave = data => wx.$request({
  url: '/app/sales/enroll',
  data,
  method: 'post'
})

// 多个文件上传 /app/basic/file/campaign/upload

// 开启打卡
exports.SignInUserOpen1 = data => wx.$request({
  url: '/app/sales/signIn/user/open/signIn',
  data,
  method: 'post'
})

// 用户打卡
exports.SignInUserOpen = data => wx.$request({
  url: '/app/sales/signIn/user/signIn',
  data,
  method: 'post'
})

// 查询当前打卡进度
exports.getUserSignInSpeed = data => wx.$request({
  url: '/app/sales/signIn/user/speed',
  data,
  method: 'get'
})

//补卡 /app/sales/signIn/user/supplement
exports.signInUserSupplement = data => wx.$request({
  url: '/app/sales/signIn/user/supplement',
  data,
  method: 'POST'
})

// 根据预约ID列表获取关联NPS app/sales/nps/sign_in_days
exports.getQuestionnaireList = data => wx.$request({
  url: '/app/sales/nps/sign_in_days',
  data,
  method: 'get'
})

// 获取奖品列表
exports.getLotteryList = data => wx.$request({
  url: '/app/sales/lottery/list',
  data,
  method: 'get'
})

// 抽奖
exports.getLottery = data => wx.$request({
  url: '/app/sales/lottery',
  data,
  method: 'post'
})

// 获取用户抽奖基础信息 抽奖次数 分享次数
exports.getLotteryUser = data => wx.$request({
  url: '/app/sales/lottery/user/basic',
  data,
  method: 'get'
})

// 抽奖 分享
exports.lotteryUserShare = data => wx.$request({
  url: '/app/sales/lottery/user/share',
  data,
  method: 'post'
})

// 获取第七天的报告
exports.getReportDay7 = data => wx.$request({
  url: '/app/sales/signIn/user/report/day7',
  data,
  method: 'get'
})

// 获取第七天的报告
exports.getReportDay7Two = data => wx.$request({
  url: '/app/sales/signIn/user/reportDays',
  data,
  method: 'get'
})

// 获取用户抽奖记录
exports.getUserPrizes = data => wx.$request({
  url: '/app/sales/lottery/user/get_prizes',
  data,
  method: 'get'
})

// 小程序二维码base64
exports.getqrcode_base64 = data => wx.$request({
  url: '/app/basic/miniapp/qrcode_base64',
  data,
  method: 'get'
})

// 公告 招募结果 公示列表
exports.salesEnroll_list = data => wx.$request({
  url: '/app/sales/enroll/list',
  data,
  method: 'get'
})

// 重启打开
exports.restartSignIn = data => wx.$request({
  url: '/app/sales/signIn/user/restartSignIn',
  data,
  method: 'get'
})
