<view class="tabs" style="--num:{{tabs.length}};--color:#3c3c3c;--buttom:{{buttom}}rpx;--Fbuttom:{{-Fbuttom}}rpx;--fontSize: {{fontSize}}rpx;--lineHeight:{{lineHeight}}rpx;--fontWeight:{{fontWeight}};--AfontWeight:{{AfontWeight}}">
  <!--  style="width:{{tabsType && indexMer < item[valueKey]?131:100}}rpx"  -->
  <!-- {{indexMer}} {{item.gradeCode}} {{indexMer < item.gradeCode}} -->
  <view class="tabs-item" wx:for="{{tabs}}" wx:key="index" catchtap="changeActive" data-index="{{index}}"
    data-value="{{item[valueKey]}}">

    <view class="tabs-item-text {{active == index?'active':''}}">
      <image wx:if="{{tabsType && ( item.gradeCode ? indexMer < item.gradeCode  : true)}}" class="tabs-suo"
        src="{{$cdn}}/equity-lock.png"></image>{{item[labelKey]}}
    </view>
    <!-- <view class="tabs-w"></view> -->
  </view>
  <!-- equity-lock.png -->
  <view class="tabs-line" wx:if="{{lineType}}" />
</view>