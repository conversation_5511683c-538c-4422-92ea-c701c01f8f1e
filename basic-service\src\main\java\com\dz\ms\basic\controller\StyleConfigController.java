package com.dz.ms.basic.controller;

import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.enums.LogType;
import com.dz.ms.basic.dto.NavigationConfigDTO;
import com.dz.ms.basic.dto.StyleConfigDTO;
import com.dz.ms.basic.service.StyleConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags="风格配置")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class StyleConfigController {

    @Resource
    private StyleConfigService styleConfigService;

    @ApiOperation("获取当前风格配置")
    @GetMapping(value = "/crm/style_config/get")
    public Result<StyleConfigDTO> getStyleConfig() {
        Result<StyleConfigDTO> result = new Result<>();
        StyleConfigDTO config = styleConfigService.getStyleConfigList(1L);
        result.setData(config);
        return result;
    }
    @ApiOperation("根据获取当前风格配置")
    @GetMapping(value = "/crm/style_config/info")
    public Result<StyleConfigDTO> getStyleConfigByid(@RequestParam("id") Long id) {
        Result<StyleConfigDTO> result = new Result<>();
        StyleConfigDTO config = styleConfigService.getStyleConfigById(id);
        result.setData(config);
        return result;
    }

    /**
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "保存风格配置",type = LogType.OPERATELOG)
    @ApiOperation("保存风格配置")
    @PostMapping(value = "/crm/style_config/save")
    public Result<Long> saveStyleConfig(@RequestBody StyleConfigDTO param) {
        Result<Long> result = new Result<>();
        Long id = styleConfigService.saveStyleConfig(param);
        result.setData(id);
        return result;
    }


    @ApiOperation("小程序中获取当前风格配置")
    @GetMapping(value = "/app/style_config/get")
    public Result<StyleConfigDTO> appGetStyleConfig() {
        Result<StyleConfigDTO> result = new Result<>();
        StyleConfigDTO config = styleConfigService.getStyleConfigList(1L);
        result.setData(config);
        return result;
    }
}
