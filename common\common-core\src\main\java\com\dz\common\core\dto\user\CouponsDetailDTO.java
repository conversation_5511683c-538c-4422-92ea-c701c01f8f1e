package com.dz.common.core.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 券详情
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "券详情")
public class CouponsDetailDTO {

    @ApiModelProperty(value = "券ID")
    private String couponId;
    @ApiModelProperty(value = "任务名称")
    private String couponName;
    @ApiModelProperty(value = "券描述")
    private String couponDesc;
    @ApiModelProperty(value = "券有效期结束时间（yyyy-MM-dd HH:mm:ss）")
    private String couponEndTime;
    @ApiModelProperty(value = "券码")
    private String couponCode;
    @ApiModelProperty(value = "券状态（1未使用，2已使用，3已过期，4:已作废）")
    private String couponStatus;
    @ApiModelProperty(value = "券使用规则")
    private String ruleDesc;
}
