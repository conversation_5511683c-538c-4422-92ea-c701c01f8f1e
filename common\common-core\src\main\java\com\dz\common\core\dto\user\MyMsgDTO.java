package com.dz.common.core.dto.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 我的消息
 */
@Getter
@Setter
public class MyMsgDTO {

    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "标题")
    private String title;
    @ApiModelProperty(value = "内容")
    private String msgDesc;
    @ApiModelProperty(value = "跳转链接")
    private String jumpUrl;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "是否已读，1是2否")
    private Integer isRead;
    @ApiModelProperty(value = "消息code")
    private String msgCode;
    @ApiModelProperty(value = "首页通知标题")
    private String homeTitle;
    @ApiModelProperty(value = "排序")
    private Integer sortNum;
    @ApiModelProperty(value = "1会小2历史迁移")
    private Integer msgType;

}
