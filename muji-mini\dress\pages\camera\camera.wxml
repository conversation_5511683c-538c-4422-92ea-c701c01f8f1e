<my-page overallModal="{{overallModal}}" loading="{{loading}}">
  <view class="camera-page {{ currentMark }}">
    <dress-nav title="自然有生活" show-back="{{ isCheck }}"></dress-nav>
    <!-- 相机 -->
    <block wx:if="{{step === 1}}">
      <view class="camera-area">
        <!-- 拍照后 -->
        <block wx:if="{{taked}}">
          <image src="{{ chooseImage }}" class="camera" />
          <!-- 相机操作区域 -->
          <view class="camera-operate">
            <view class="camera-operate-item"></view>
            <image src="{{ $cdn }}/dress/camera-taked.png?v=2" class="camera-operate-item take" bindtap="handleSubmitTap" />
            <view class="camera-operate-item"></view>
          </view>
          <!-- 右侧文案 -->
          <view class="camera-operate-text">2025 汉麻</view>
          <!-- 输入 -->
          <view class="input-area">
            <view class="input-area-text">MUJI FRIEND</view>
            <input value="{{ inputText }}" type="text" class="input" placeholder="点击输入文字" placeholder-class="input-placeholder" bindinput="handleInputChange" />
          </view>
          <view class="retake-btn" bindtap="handleRetakeTap">重新上传</view>
        </block>
        <block wx:else>
          <camera wx:if="{{showCamera}}" binderror="handleCameraError" bindinitdone="initCameraDone" device-position="{{ devicePosition }}" class="camera"></camera>
          <!-- 相机操作区域 -->
          <view class="camera-operate">
            <image src="{{ $cdn }}/dress/camera-album.png" class="camera-operate-item" bindtap="handleAlbumTap" />
            <image src="{{ $cdn }}/dress/camera-take.png" class="camera-operate-item take" bindtap="handleTakeTap" />
            <image src="{{ $cdn }}/dress/camera-trans.png" class="camera-operate-item" bindtap="handleTransTap" />
          </view>
        </block>
        <!-- 水印 -->
        <image src="{{ currentMarkImg }}" class="mark-img" />
      </view>
      <!-- 选择水印区域 -->
      <view class="watermark-area">
        <view wx:for="{{marks}}" wx:key="index" class="watermark-item {{ item.name === currentMark ? 'active' : '' }}" bindtap="handleWatermarkItemTap" data-name="{{item.name}}">
          <image src="{{item.sImg}}" class="watermark-item-s" />
        </view>
      </view>
    </block>
    <!-- 提交 -->
    <view wx:if="{{step === 2}}" class="submit-area">
      <image src="{{ drawImage }}" class="submit-area-img" />
      <view class="primary-btn" bindtap="handleSaveShareTap">保存并分享</view>
      <view wx:if="{{ !isCheck }}" class="primary-btn" bindtap="handleEditTap">回到首页</view>
      <view wx:else class="primary-btn" bindtap="handleDeleteTap">删除照片</view>
    </view>
  </view>
  <canvas type="2d" class="draw-canvas"></canvas>
  <share-poster ImgStyle="'v2'" src="{{ drawImage }}" show="{{ showShare }}" bindsuccess="handleShareSuccess" bindclose="handleShareClose"></share-poster>
</my-page>
