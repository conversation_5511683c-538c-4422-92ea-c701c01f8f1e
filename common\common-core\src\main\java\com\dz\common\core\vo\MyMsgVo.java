package com.dz.common.core.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 我的消息入参
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MyMsgVo
{
    @ApiModelProperty(value = "expireCoupon（您有优惠券即将过期）couponAccount（您有新的优惠券到账）receiveCard（您有一张MUJI会员卡待领取）expirePoints（您有一些积分即将过期）")
    private List<String> msgCode;
    @ApiModelProperty(value = "userId")
    private Long userId;
}
