package com.dz.common.core.dto.wechat;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * JSCODE换取的session信息
 * <AUTHOR>
 * @date 2022/01/30 18:10
 */
@Setter
@Getter
@ToString
public class CodeSessionDTO {

    @ApiModelProperty(value = "用户唯一标识的openid")
    private String openid;
    @ApiModelProperty(value = "用户在开放平台的唯一标识符")
    private String unionid;
    @ApiModelProperty(value = "会话密钥")
    @JSONField(name = "session_key")
    private String sessionKey;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "用户所属企业的corpid")
    private String corpid;
    @ApiModelProperty(value = "用户在企业内的UserID，对应管理端的帐号，企业内唯一")
    private String userid;

}
