package com.dz.ms.basic.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.constants.CacheKeys;
import com.dz.ms.basic.dto.PromotionChannelDTO;
import com.dz.ms.basic.dto.PromotionPageDTO;
import com.dz.ms.basic.entity.PromotionChannel;
import com.dz.ms.basic.entity.PromotionPage;
import com.dz.ms.basic.mapper.PromotionChannelMapper;
import com.dz.ms.basic.mapper.PromotionPageMapper;
import com.dz.ms.basic.service.PromotionChannelService;
import com.dz.ms.basic.service.PromotionPageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
@Slf4j
public class PromotionChannelServiceImpl extends ServiceImpl<PromotionChannelMapper, PromotionChannel> implements PromotionChannelService {


    @Resource
    private PromotionChannelMapper promotionChannelMapper;

    @Override
    public Long savePromotionChannel(PromotionChannelDTO param) {
        if (null!= param.getId()){
            PromotionChannel promotionChannel = promotionChannelMapper.selectById(param.getId());
            if (null == promotionChannel){
                throw new BusinessException(ErrorCode.BAD_REQUEST,"推广渠道ID不存在");
            }
        }
        if (param.getChannelParam() != null && !param.getChannelParam().matches("[a-zA-Z0-9]+")){
            throw new BusinessException(ErrorCode.BAD_REQUEST,"只能输入大小写和数字");
        }
        PromotionChannel promotionChannel = new PromotionChannel();
        promotionChannel.setChannelName(param.getChannelName());
        if (param.getChannelParam() != null){
            promotionChannel.setChannelParam(param.getChannelParam());
        }
        promotionChannel.setId(param.getId());
        if (param.getParentId() != null){
            promotionChannel.setParentId(param.getParentId());
        }
        if(ParamUtils.isNullOr0Long(promotionChannel.getId())) {
            promotionChannel.setCreateTime(new Date());
            promotionChannel.setTenantId(SecurityContext.getUser().getTenantId());
            promotionChannel.setCreator(SecurityContext.getUser().getUid());
            promotionChannelMapper.insert(promotionChannel);
        }
        else {
            promotionChannel.setModified(new Date());
            promotionChannel.setModifier(SecurityContext.getUser().getUid());
            promotionChannelMapper.updateById(promotionChannel);
        }
        return promotionChannel.getId();
    }

    @Override
    public void deletePromotionChannel(PromotionChannelDTO param) {
        PromotionChannel promotionChannel = promotionChannelMapper.selectById(param.getId());
        if (null == promotionChannel){
            throw new BusinessException(ErrorCode.BAD_REQUEST,"推广渠道ID不存在");
        }
        PromotionChannel deleteChannel = new PromotionChannel();
        deleteChannel.setIsDeleted(1);
        deleteChannel.setId(param.getId());
        deleteChannel.setModified(new Date());
        log.info("删除渠道数据:{}", JSON.toJSONString(deleteChannel));
        promotionChannelMapper.updateById(deleteChannel);
    }
}
