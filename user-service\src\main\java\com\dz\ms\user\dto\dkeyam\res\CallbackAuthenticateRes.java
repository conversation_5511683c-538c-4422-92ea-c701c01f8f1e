package com.dz.ms.user.dto.dkeyam.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 外部用户认证出参
 * @author: fei
 * @date: 2025/01/22 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CallbackAuthenticateRes implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("姓名")
    private String personalName;

    @ApiModelProperty("角色（会清除以前的角色）")
    private String role;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("手机号")
    private String mobile;
}
