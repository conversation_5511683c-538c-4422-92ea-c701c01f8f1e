package com.dz.ms.basic.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.dto.DownloadDTO;
import com.dz.common.core.dto.DownloadQueryParamDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.fegin.product.ProductFeignClient;
import com.dz.common.core.service.DownloadStrategy;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.ms.basic.entity.DownloadCenter;
import com.dz.ms.basic.mapper.DownloadCenterMapper;
import com.dz.ms.basic.mapper.DownloadHeaderMapper;
import com.dz.ms.basic.service.DownloadCenterService;
import com.dz.ms.basic.service.OssService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 下载中心表
 *
 * <AUTHOR>
 * 2022/08/15 16:00
 */
@Service
@Slf4j
public class DownloadCenterServiceImpl implements DownloadCenterService {

    @Resource
    private DownloadCenterMapper downloadCenterMapper;
    @Resource
    private OssService ossService;
    @Resource
    private ProductFeignClient productFeignClient;
    @Resource
    private DownloadHeaderMapper downloadHeaderMapper;
    @Resource
    private ApplicationContext applicationContext;

    @Override
    public void save(DownloadCenter downloadCenter) {
        downloadCenterMapper.insert(downloadCenter);
    }

    @Override
    public void save(DownloadAddParamDTO downloadAddParamDTO, CurrentUserDTO commonLoginDTO) {
        log.info("add download params:{}", JSON.toJSONString(downloadAddParamDTO));
        Date date = new Date();

        DownloadCenter downloadCenter = new DownloadCenter();
        BeanUtils.copyProperties(downloadAddParamDTO, downloadCenter);
        downloadCenter.setCreator(commonLoginDTO.getUid());
        downloadCenter.setCreated(date);
        downloadCenterMapper.insertSelective(downloadCenter);
        // 增加参数
        downloadAddParamDTO.setDownloadCenterId(downloadCenter.getId());
        log.info("开始下载业务报表[{}]", downloadAddParamDTO.getFileName());
        String beanName = downloadAddParamDTO.getBeanName();
        DownloadStrategy strategy = (DownloadStrategy) applicationContext.getBean(beanName);
        strategy.export(downloadAddParamDTO);
    }

    @Override
    public void delete(Long id) {
        DownloadCenter downloadCenter = downloadCenterMapper.selectByPrimaryKey(id);
        downloadCenterMapper.deleteByPrimaryKey(id);
        //删除oss文件
        ossService.delOSS(downloadCenter.getSourceUrl());
    }

    @Override
    public void mark(Long id) {
        DownloadCenter record = downloadCenterMapper.selectByPrimaryKey(id);
        DownloadCenter downloadCenter = new DownloadCenter();
        downloadCenter.setId(id);
        downloadCenter.setDownloadNum(record.getDownloadNum() + 1);
        downloadCenterMapper.updateByPrimaryKeySelective(downloadCenter);
    }

    @Override
    public PageInfo<DownloadDTO> page(DownloadQueryParamDTO paramDTO) {
        log.info("query download page param:{}", JSON.toJSONString(paramDTO));

        IPage<DownloadCenter> page = downloadCenterMapper.list(new Page<>(paramDTO.getPageNum(), paramDTO.getPageSize()), paramDTO);

        log.info("query download page result:{}", JSON.toJSONString(page));
        return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), BeanCopierUtils.convertList(page.getRecords(), DownloadDTO.class));
    }

    @Override
    public void scarp(Integer scarpDays) {
        // 标记为已过期
        downloadCenterMapper.updateScarp(scarpDays);
    }

    @Override
    public List<DownloadDTO> listScarp(Integer scarpDays) {
        return downloadCenterMapper.listScarp(scarpDays);
    }

    @Override
    public void updateClear(DownloadDTO downloadDTO) {
        // 清除记录
        DownloadCenter downloadCenter = new DownloadCenter();
        downloadCenter.setId(downloadDTO.getId());
        downloadCenter.setSourceUrl("");
        downloadCenterMapper.updateByPrimaryKeySelective(downloadCenter);
        //删除oss文件
        ossService.delOSS(downloadDTO.getSourceUrl());
    }
}