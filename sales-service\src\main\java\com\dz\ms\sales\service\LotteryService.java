package com.dz.ms.sales.service;


import com.dz.common.base.vo.PageInfo;
import com.dz.ms.sales.dto.LotteryPrizesDTO;
import com.dz.ms.sales.dto.UserLotteryBasicDTO;
import com.dz.ms.sales.dto.UserLotteryPrizesDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/9
 */
public interface LotteryService {

    /**
     * 查询奖品列表
     *
     * @return
     */
    List<LotteryPrizesDTO> getPrizes(String campaignCode);


    /**
     * 抽奖
     *
     * @return
     */
    LotteryPrizesDTO lottery(String campaignCode, Long tenantId, Long uid);

    /**
     * 查询用户抽奖基本信息
     *
     * @return
     */
    UserLotteryBasicDTO getUserLotteryBasic(String campaignCode);

    /**
     * 分享
     *
     * @param tenantId
     * @param uid
     * @param campaignCode
     * @return
     */
    Boolean share(Long tenantId, Long uid, String campaignCode);

    /**
     * 获取用户抽奖记录
     *
     * @param campaignCode
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageInfo<UserLotteryPrizesDTO> getUserLotteryPrizes(String campaignCode, Integer pageNum, Integer pageSize);

    /**
     * 刷新抽奖库存
     *
     * @param jobParams
     */
    void refreshLotteryInventory(String jobParams);
}
