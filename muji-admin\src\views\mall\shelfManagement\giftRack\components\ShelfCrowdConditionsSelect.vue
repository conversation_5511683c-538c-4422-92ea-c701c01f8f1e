<template>
  <a-form-item :label="props.label" :name="props.name" :rules="props.rules" class="ShelfStatusSelect">
    <a-cascader v-model:value="thisFields.value" @change="thisMethods.change" :options="thisFields.options"
      :show-search="{ filter: thisMethods.filter }" :changeOnSelect="false" placeholder="请选择" v-bind="$attrs">
      <template #displayRender="{ labels, selectedOptions }">
        {{ labels[labels.length - 1] }}
      </template>
    </a-cascader>
  </a-form-item>
</template>

<script setup>
import { onMounted, reactive, watch } from 'vue'
import { SHELF_STATUS_ARR } from '@/utils/constants.js'

const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  name: {
    type: String,
    default: ''
  },
  rules: {
    type: Object,
    default: () => undefined
  },
  modelValue: {
    type: Array,
    default: () => []
  }
})
const emits = defineEmits(['change', 'update:modelValue'])

const thisFields = reactive({
  value: [],
  options: [
    {
      value: 1,
      label: '会员等级',
      children: [
        {
          value: 1,
          label: '普通会员'
        },
        {
          value: 2,
          label: '铜级会员'
        },
        {
          value: 3,
          label: '银级会员'
        },
        {
          value: 4,
          label: '金级会员'
        }
      ]
    }
    // {
    //   value: 'xingbie',
    //   label: '性别',
    //   children: [
    //     {
    //       value: 'nan',
    //       label: '男'
    //     },
    //     {
    //       value: 'nv',
    //       label: '女'
    //     },
    //     {
    //       value: 'weizhi',
    //       label: '未知'
    //     }
    //   ]
    // }
  ]
})
const thisMethods = {
  filter(inputValue, path) {
    return path.some(option => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1)
  },
  setValue() {
    thisFields.value = props.modelValue
  },
  change(e) {
    emits('update:modelValue', e)
    emits('change', e)
  }
}

onMounted(() => thisMethods.setValue())
watch(() => props.modelValue, () => thisMethods.setValue())
</script>

<style lang="scss" scoped>
.ShelfStatusSelect {
  .ant-select {
    width: 202px;
  }
}
</style>
