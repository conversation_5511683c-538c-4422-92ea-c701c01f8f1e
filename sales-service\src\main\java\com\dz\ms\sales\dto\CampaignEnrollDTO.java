package com.dz.ms.sales.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/19
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(description = "敏感肌测试名单数据传输对象")
public class CampaignEnrollDTO {


    @ApiModelProperty(value = "ID", example = "1", required = true)
    private Long id;

    @ApiModelProperty("活动标识")
    private String campaignCode;

    @ApiModelProperty(value = "用户ID", example = "1001", required = true)
    private Long uid;

    @ApiModelProperty(value = "openid", example = "o6_bmjrPTlm6_2sgVt7hMZOPfL2M", required = true)
    private String openid;

    @ApiModelProperty(value = "unionid", example = "ouZjDjHhB0c6XxbN8pPYEwSgXUuk", required = true)
    private String unionid;

    @ApiModelProperty(value = "姓名", example = "张三", required = true)
    private String name;

    @ApiModelProperty(value = "手机号码", example = "13800138000", required = true)
    private String mobile;

    @ApiModelProperty(value = "皮肤类型 0未知 1皮肤敏感（泛红、刺痛） 2干燥缺水 3、痘痘/粉刺问题 4、油脂分泌过多 5、黑头问题", example = "5", allowableValues = "0, 1, 2, 3, 4, 5", required = true)
    private Integer skinType;

    @ApiModelProperty(value = "省", example = "广东省", required = true)
    private String province;

    @ApiModelProperty(value = "市", example = "广州市", required = true)
    private String city;

    @ApiModelProperty(value = "区", example = "天河区", required = true)
    private String district;

    @ApiModelProperty(value = "详细地址", example = "天河区天河路1号", required = true)
    private String address;

    @ApiModelProperty(value = "状态", example = "1", allowableValues = "0, 1, 2", required = true)
    private Integer status;

    @ApiModelProperty(value = "租户ID", example = "1", required = true)
    private Long tenantId;

    @ApiModelProperty(value = "创建时间", example = "2024-12-19T12:34:56Z", required = true)
    private Date created;

    @NotNull(message = "创建人不能为空")
    @ApiModelProperty(value = "创建人", example = "1001", required = true)
    private Long creator;

    @ApiModelProperty(value = "是否报名成功", example = "true", required = true)
    private Boolean enrollSuccess;

    @ApiModelProperty(value = "招募轮次", example = "1", required = true)
    private Integer enrollCount;


}