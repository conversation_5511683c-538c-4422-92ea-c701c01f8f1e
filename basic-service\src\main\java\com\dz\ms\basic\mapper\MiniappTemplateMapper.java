package com.dz.ms.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.basic.entity.MiniappTemplate;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 小程序页面模板Mapper
 * @author: Handy
 * @date:   2022/02/04 22:59
 */
@Repository
public interface MiniappTemplateMapper extends BaseMapper<MiniappTemplate> {

    /** 根据类型将模板设置为未发布 */
    int setUnPublishByType(@Param("publish") Integer publish);

    MiniappTemplate getTemplateByType(@Param("pageType") Integer pageType);
    int setTemplateTypeByType(@Param("pageType") Integer pageType);

}
