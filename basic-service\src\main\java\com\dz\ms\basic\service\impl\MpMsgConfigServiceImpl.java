package com.dz.ms.basic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.dto.MpMsgConfigDTO;
import com.dz.ms.basic.dto.StyleConfigDTO;
import com.dz.ms.basic.entity.MpMsgConfig;
import com.dz.ms.basic.entity.StyleConfig;
import com.dz.ms.basic.mapper.MpMsgConfigMapper;
import com.dz.ms.basic.mapper.StyleConfigMapper;
import com.dz.ms.basic.service.MpMsgConfigService;
import com.dz.ms.basic.service.MpMsgService;
import com.dz.ms.basic.service.StyleConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 小程序UI自定义配置
 * @author: Handy
 * @date:   2022/11/21 14:54
 */
@Service
public class MpMsgConfigServiceImpl extends ServiceImpl<MpMsgConfigMapper, MpMsgConfig> implements MpMsgConfigService {


    @Resource
    private MpMsgConfigMapper mpMsgConfigMapper;

    @Override
    public MpMsgConfigDTO getMpMsgConfigList(Long tenantId) {
        MpMsgConfig mpMsgConfig = mpMsgConfigMapper.selectOne(new QueryWrapper<MpMsgConfig>().eq("tenant_id", tenantId));
        MpMsgConfigDTO  list = BeanCopierUtils.convertObject(mpMsgConfig, MpMsgConfigDTO.class);
        return list;
    }

    @Override
    public MpMsgConfigDTO getMpMsgConfigById(Long id) {
        MpMsgConfig mpMsgConfig = mpMsgConfigMapper.selectById(id);
        if (mpMsgConfig == null) {
            throw new BusinessException(ErrorCode.INTERNAL_ERROR, "未查询到该样式配置");
        }
        MpMsgConfigDTO styleConfigDTO = BeanCopierUtils.convertObject(mpMsgConfig, MpMsgConfigDTO.class);
        return styleConfigDTO;
    }

    @Override
    public Long saveMpMsgConfig(MpMsgConfigDTO param) {
        MpMsgConfig mpMsgConfig = BeanCopierUtils.convertObject(param, MpMsgConfig.class);
        if (ParamUtils.isNullOr0Long(mpMsgConfig.getId())) {
            mpMsgConfig.setCreator(SecurityContext.getUser().getUid());
            mpMsgConfig.setCreateTime(new Date(System.currentTimeMillis()));
            mpMsgConfigMapper.insert(mpMsgConfig);
        } else {
            mpMsgConfig.setModifier(SecurityContext.getUser().getUid());
            mpMsgConfig.setModified(new Date(System.currentTimeMillis()));
            mpMsgConfigMapper.updateById(mpMsgConfig);
        }
        return mpMsgConfig.getId();
    }
}