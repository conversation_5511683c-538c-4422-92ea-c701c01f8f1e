package com.dz.common.core.constants;

import com.jcraft.jsch.*;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.Properties;

/**
 */
public class SFTPChannel {
    protected final org.slf4j.Logger logger = LoggerFactory.getLogger(this.getClass());
    Session session = null;
    Channel channel = null;

    public ChannelSftp getChannel(Map<String, String> sftpDetails, int timeout) throws JSchException {

        String ftpHost = sftpDetails.get(SFTPConstans.SFTP_REQ_HOST);
        String port = sftpDetails.get(SFTPConstans.SFTP_REQ_PORT);
        String ftpUserName = sftpDetails.get(SFTPConstans.SFTP_REQ_USERNAME);
        String ftpPassword = sftpDetails.get(SFTPConstans.SFTP_REQ_PASSWORD);

        int ftpPort = SFTPConstans.SFTP_DEFAULT_PORT;
        if (port != null && !port.equals("")) {
            ftpPort = Integer.valueOf(port);
        }

        JSch jsch = new JSch();
        session = jsch.getSession(ftpUserName, ftpHost, ftpPort); // 根据用户名，主机ip，端口获取一个Session对象
        if (logger.isDebugEnabled()) {
            logger.debug("Session created.");
        }
        if (ftpPassword != null) {
            session.setPassword(ftpPassword); // 设置密码
        }
        Properties config = new Properties();
        config.put("StrictHostKeyChecking", "no");
        session.setConfig(config); // 为Session对象设置properties
        session.setTimeout(timeout); // 设置timeout时间
        session.connect(); // 通过Session建立链接
        if (logger.isDebugEnabled()) {
            logger.debug("Session connected.");
            logger.debug("Opening Channel.");
        }
        channel = session.openChannel("sftp"); // 打开SFTP通道
        channel.connect(); // 建立SFTP通道的连接
        if (logger.isDebugEnabled()) {
            logger.debug("Connected successfully to ftpHost = " + ftpHost + ",as ftpUserName = " + ftpUserName
                    + ", returning: " + channel);
        }
        return (ChannelSftp) channel;
    }

    public void closeChannel() {
        if (channel != null) {
            channel.disconnect();
        }
        if (session != null) {
            session.disconnect();
        }
    }
}
