package com.dz.ms.basic.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * 租户信息DTO
 * @author: Handy
 * @date:   2022/01/28 15:40
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "租户信息")
public class TenantInfoDTO extends BaseDTO {

    @ApiModelProperty(value = "租户ID")
    private Long id;
    @ApiModelProperty(value = "品牌编码")
    private String tenantCode;
    @ApiModelProperty(value = "品牌中文名称")
    private String cnName;
    @ApiModelProperty(value = "品牌英文名称")
    private String enName;
    @ApiModelProperty(value = "品牌介绍")
    private String introduce;
    @ApiModelProperty(value = "品牌logo URL")
    private String logoUrl;
    @ApiModelProperty(value = "登录页logo")
    private String bigLogo;
    @ApiModelProperty(value = "手机号码")
    private String mobile;
    @ApiModelProperty(value = "租户状态 0关闭 1正常")
    private Integer state;
    @ApiModelProperty(value = "创建时间")
    private Date created;

}
