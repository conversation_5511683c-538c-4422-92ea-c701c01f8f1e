<my-modal show="{{show}}" zIndex="{{zIndex}}" background="{{background}}" overlayClick="{{overlayClick}}" overlayBackground="{{overlayBackground}}" closeable="{{closeable}}" borderRadius="{{borderRadius}}" bindclose="close">
	<view class="popup" id="popup">
		<!-- 标题 -->
		<text class="popup-title" catch:touchmove="touchmove1">{{title}}</text>
		<!-- 内容区域 可以是富文本 -->
		<view class="popup-content">
			<scroll-view class="popup-scroll" style="max-height:{{height}}rpx" scroll-y catch:touchmove="touchmove" wx:if="{{height}}">
				<view style="padding-left:60rpx;padding-right:30rpx;box-sizing:border-content;text-align: {{isCenter?'center':'left'}};">
					<image class="popup-scroll-icon" src="{{$cdn}}{{icon}}" wx:if="{{icon}}" />
					<view class="popup-scroll-pos" wx:if="{{store}}">
						<image src="{{$cdn}}/store/position.png" class="popup-scroll-position" />
						<view>{{store}}</view>
					</view>
				</view>
			</scroll-view>
		</view>
		<view class="popup-bottom" catch:touchmove="touchmove1">
			<!-- 按钮区域 -->
			<view class="popup-button cancel" bindtap="cancel" wx:if="{{cancelText}}">{{cancelText}}</view>
			<view class="popup-button confirm" bindtap="confirm" wx:if="{{confirmText}}">{{confirmText}}</view>
		</view>
		<text class="popup-tip" wx:if="{{tip}}">{{tip}}</text>
		<view style="height:60rpx;width:100%;"></view>
	</view>
</my-modal>