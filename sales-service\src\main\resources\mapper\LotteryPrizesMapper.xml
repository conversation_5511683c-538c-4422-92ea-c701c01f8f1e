<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.sales.mapper.LotteryPrizesMapper">


    <update id="lotteryResidueInventoryById">
        update lottery_prizes
        set residue_inventory = residue_inventory - 1
        where id = #{id} and residue_inventory > 0
    </update>


</mapper>
