// signUp/pages/announcement/announcement.js
import {
  salesEnroll_list
} from '../../api/index.js'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    option: [{
        label: "用户昵称",
        value: 'name'
      },
      {
        label: "会员卡号",
        value: 'cardNo'
      },
      {
        label: "手机号码",
        value: 'mobile'
      }
    ],
    list: [{
        cardNo: "李四",
        mobile: '11111111111'
      },
      {
        cardNo: "李四",
        mobile: '11111111111'
      },
      {
        cardNo: "李四",
        mobile: '11111111111'
      },
      {
        cardNo: "李四",
        mobile: '11111111111'
      },
      {
        cardNo: "李四",
        mobile: '11111111111'
      },
      {
        cardNo: "李四",
        mobile: '11111111111'
      },
      {
        cardNo: "李四",
        mobile: '11111111111'
      },
      {
        cardNo: "李四",
        mobile: '11111111111'
      },
    ],
    listData: [],
    tabsObj: {
      1: '一',
      2: '二',
      3: '三',
      4: '四',
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getList()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  getList() {
    let {
      tabsObj
    } = this.data
    salesEnroll_list().then(res => {
      let list = []
      if (res.data && res.data.length > 0) {

        res.data.forEach((item, index) => {
          item.campaignStartTime = this.getDate(item.campaignStartTime)
          item.campaignEndTime = this.getDate(item.campaignEndTime)
          console.log(tabsObj[index + 1], 'tabsObj[index + 1]');
          item.tabsObj = tabsObj[index + 1]
          if (item.enrollRosterList.length > 0) {
            list.push(item)
          }
        })
        list = list.reverse()

      }
      this.setData({
        listData: list
      })
    })
  },
  // 处理时间
  getDate(time) {
    console.log(time, 'time');
    if (typeof time === 'string') {
      // ios 解析不出来 年月 2020-05
      if (time.length < 8) {
        time = `${time}-1`;
      }
      time = new Date(time.replace(/-/g, '/').replace('T', ' ')).getTime();
    }
    let date = new Date(time); // 创建一个日期对象
    let year = date.getFullYear(); // 获取年份
    let month = date.getMonth() + 1; // 获取月份（0-11），需要加1
    let day = date.getDate(); // 获取日

    // 拼接成“2023年1月1日”的格式
    let formattedDate = `${year}年${month}月${day}日`;
    console.log(formattedDate); // 输出：2023年1月1日（假设今天是2023年1月1日）
    return formattedDate
  },
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    // 页面分享
    return {
      title: '和我一起体验「极简护肤」开启七天打卡活动',
      imageUrl: wx.$config.ossImg + '/signUp/share.png',
      path: '/signUp/pages/signUp/signUp',
    }
  }
})
