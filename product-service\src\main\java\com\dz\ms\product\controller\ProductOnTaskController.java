package com.dz.ms.product.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.constants.ProductConstants;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.product.config.Globals;
import com.dz.ms.product.dto.CrmProductOnTaskListDTO;
import com.dz.ms.product.dto.ProductOnTaskDTO;
import com.dz.ms.product.dto.req.CrmProductOnTaskLisParamDTO;
import com.dz.ms.product.dto.req.ProductOnTaskSaveParamDTO;
import com.dz.ms.product.entity.ProductOnTask;
import com.dz.ms.product.service.ProductOnTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Api(tags = "货架商品库存任务")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
@Slf4j
public class ProductOnTaskController {

    @Resource
    private ProductOnTaskService productOnTaskService;

    /**
     * 分页查询货架商品库存任务
     *
     * @param param
     * @return result<PageInfo < ProductOnTaskDTO>>
     */
    @ApiOperation("分页查询货架商品库存任务")
    @GetMapping(value = "/crm/product_on_task/list")
    public Result<PageInfo<CrmProductOnTaskListDTO>> getProductOnTaskList(@ModelAttribute CrmProductOnTaskLisParamDTO param) {
        Result<PageInfo<CrmProductOnTaskListDTO>> result = new Result<>();
        PageInfo<CrmProductOnTaskListDTO> page = productOnTaskService.getProductOnTaskList(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询货架商品库存任务
     *
     * @param id
     * @return result<ProductOnTaskDTO>
     */
    @ApiOperation("根据ID查询货架商品库存任务")
    @GetMapping(value = "/product_on_task/info")
    public Result<ProductOnTaskDTO> getProductOnTaskById(@RequestParam("id") Long id) {
        Result<ProductOnTaskDTO> result = new Result<>();
        ProductOnTaskDTO productOnTask = productOnTaskService.getProductOnTaskById(id);
        result.setData(productOnTask);
        return result;
    }

    /**
     * 新增货架商品库存任务
     *
     * @return result<Long>
     */
    @SysLog(value = "新增货架商品库存任务", type = LogType.OPERATELOG)
    @ApiOperation("新增货架商品库存任务")
    @PostMapping(value = "/crm/product_on_task/add")
    public Result<Long> addProductOnTask(@RequestBody List<ProductOnTaskSaveParamDTO> list) {
        Result<Long> result = new Result<>();
        for (ProductOnTaskSaveParamDTO param : list) {
            if (Objects.isNull(param.getShelfProductId())) {
                throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "货架商品ID不能为空");
            }
            if (Objects.isNull(param.getShelfId())) {
                throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "货架ID不能为空");
            }
            if (Objects.isNull(param.getProductId())) {
                throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "商品ID不能为空");
            }
            if ((StringUtils.isBlank(param.getProductName()))) {
                throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "商品名称不能为空");
            }
//            if (Objects.isNull(param.getOnType())) {
//                throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "上架类型不能为空");
//            }
            if (Objects.equals(param.getOnType(), ProductConstants.ProductOnTask.ON_TYPE_ONCE)
                    && Objects.isNull(param.getOnTime())) {
                throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "上架类型为单次时间时，上架时间不能为空");
            }
            if (Objects.equals(param.getOnType(), ProductConstants.ProductOnTask.ON_TYPE_PER_PERIOD)
                    && Objects.isNull(param.getOnDays())) {
                throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "上架类型为周期时，上架周期不能为空");
            }
            if (Objects.nonNull(param.getOnType()) && Objects.isNull(param.getOnInventory())) {
                throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "上架库存不能为空");
            }
        }
        Long count = productOnTaskService.addProductOnTask(list);
        result.setData(count);
        return result;
    }

    /**
     * 更新货架商品库存任务
     *
     * @param param
     * @return result<Object>
     */
    @SysLog(value = "更新货架商品库存任务", type = LogType.OPERATELOG)
    @ApiOperation("更新货架商品库存任务")
    @PostMapping(value = "/crm/product_on_task/update")
    public Result<ProductOnTask> updateProductOnTask(@RequestBody ProductOnTaskDTO param) {
        Result<ProductOnTask> result = new Result<>();
        validationSaveParam(param);
        result.setData(productOnTaskService.saveProductOnTask(param));
        return result;
    }

    /**
     * 验证保存参数
     *
     * @param param
     */
    private void validationSaveParam(ProductOnTaskDTO param) {
        if (ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
        if (Objects.isNull(param.getOnType())) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "上架类型不能为空");
        }
        if (Objects.equals(param.getOnType(), ProductConstants.ProductOnTask.ON_TYPE_ONCE)
                && Objects.isNull(param.getOnTime())) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "上架类型为单次时间时，上架时间不能为空");
        }
        if (Objects.equals(param.getOnType(), ProductConstants.ProductOnTask.ON_TYPE_PER_PERIOD)
                && Objects.isNull(param.getOnDays())) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "上架类型为周期时，上架周期不能为空");
        }
        if (Objects.isNull(param.getOnInventory())) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "上架库存不能为空");
        }
    }

    /**
     * 根据ID删除货架商品库存任务
     *
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除货架商品库存任务")
    @PostMapping(value = "/crm/product_on_task/delete")
    public Result<Boolean> deleteProductOnTaskById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        productOnTaskService.deleteProductOnTaskById(param);
        result.setData(true);
        return result;
    }

    /**
     * 执行所有可用货架上下架任务
     *
     * @return
     */
    @ApiOperation("执行所有可用货架上下架任务")
    @PostMapping(value = {"/product_on_task/execute_product_on_task_of_all_shelf", "/crm/product_on_task/execute_product_on_task_of_all_shelf"})
    public Result<Void> executeProductOnTaskOfAllShelf() {
        Globals.fixedThreadPool.execute(() -> {
            try {
                SecurityContext.setUser(new CurrentUserDTO(1L, 999L));
                productOnTaskService.executeProductOnTaskOfAllShelf();
            } catch (Exception e) {
                log.error("执行上下架任务失败", e);
            }
        });
        return new Result<>();
    }

}
