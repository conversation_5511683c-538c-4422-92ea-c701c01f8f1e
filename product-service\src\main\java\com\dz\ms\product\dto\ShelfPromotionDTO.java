package com.dz.ms.product.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 货架推广活动DTO
 *
 * @author: LiinNs
 * @date: 2024/11/20 11:36
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "货架推广活动")
public class ShelfPromotionDTO extends BaseDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "推广时间类型 1永久 2时间段")
    private Integer onType;
    @ApiModelProperty(value = "推广开始时间")
    private Date onStartTime;
    @ApiModelProperty(value = "推广结束时间")
    private Date onEndTime;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "货架名称")
    private String shelfName;
    @ApiModelProperty(value = "推广数量")
    private Integer num;
    @ApiModelProperty(value = "推广组件配置")
    private String content;
    @ApiModelProperty(value = "启停状态 0禁用 1启用")
    private Integer state;
    @ApiModelProperty(value = "推广状态 1待开始/2进行中/3已结束")
    private Integer promotionState;
    
    @ApiModelProperty(value = "0正常 1删除")
    private Integer isDeleted;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "修改时间")
    private Date modified;
    @ApiModelProperty(value = "修改人")
    private Long modifier;

}
