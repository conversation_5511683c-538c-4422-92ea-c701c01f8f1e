.message-card {
  display: flex;
  padding: 60rpx 40rpx;
  width: 100%;
  box-sizing: border-box;
  position: relative;
  gap: 20rpx;
  align-items: center;

  .icon-box {
    flex-basis: 90rpx;
    width: 90rpx;
    height: 90rpx;
    background-color: #3c3c43;
    border-radius: 50%;
    color: #fff;
    text-align: center;
    line-height: 90rpx;
    font-size: 43rpx;
  }

  .info-box {
    flex: 1;
    position: relative;
    width: 100%;

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .txt {
        height: 42rpx;
        font-family: NotoSansHans,
          NotoSansHans;
        font-weight: 500;
        font-size: 28rpx;
        color: #000000;
        line-height: 42rpx;
        text-align: left;
        font-style: normal;
        width: 440rpx;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .time {
        flex-basis: 120rpx;
        width: 120rpx;
        height: 28rpx;
        font-family: PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 20rpx;
        color: #888888;
        line-height: 28rpx;
        text-align: right;
        font-style: normal;
      }

    }

    .desc {
      font-family: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        NotoSansHans;
      font-weight: 400;
      font-size: 24rpx;
      color: #888888;
      line-height: 36rpx;
      text-align: left;
      font-style: normal;
      margin-top: 10rpx;
    }
  }
}
