package com.dz.common.core.fegin.adaptor;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.adaptor.SftpDTO;
import com.dz.common.core.dto.adaptor.ThirdPartyRecordVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.InputStream;

/**
 * Sftp接口FeginClient
 */
@FeignClient(name = ServiceConstant.ADAPTOR_SERVICE_NAME, contextId = "SftpFeginClient")
public interface SftpFeginClient {



}

