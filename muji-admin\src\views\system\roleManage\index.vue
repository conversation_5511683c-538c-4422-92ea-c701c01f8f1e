<template>

  <layout>
    <template v-slot:header>
      <searchForm :formParams="formParams" @cancel="resetData" @ok="refreshData" :disabled="!$hasPermission('setting:permission:searchRole')">
        <a-form-item name="roleName">
          <a-input placeholder="角色名称" allow-clear v-model:value="formParams.roleName" allowClear @keyup.enter="refreshData"></a-input>
        </a-form-item>

      </searchForm>
    </template>
    <template v-slot:topRight>
      <a-space>
        <!-- :disabled="!$hasPermission('open:leaveUser:synchLeaveUser')" 权限标识 -->
        <a-button type="primary" :disabled="!$hasPermission('setting:permission:addRole')" @click="addChang">新建角色</a-button>

      </a-space>

    </template>
    <template v-slot="{ height }">
      <a-table :indentSize="20" row-key="id" :scroll="{ scrollToFirstRowOnChange: true, y: height - 88, x: '100%' }" :dataSource="dataSource" :columns="tableHeader" :pagination="pagination" :loading="loading" @change="loadData">
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'index'">
            {{ (current - 1) * pageSize + 1 + index }}
          </template>
          <template v-if="column.key === 'userNum'">
            {{ record.userNum || 0 }}
          </template>
          <template v-if="column.key === 'action'">
            <a-button type="link" :disabled="!$hasPermission('setting:permission:editRole')" @click="EditRole(record)">编辑</a-button>
            <a-divider type="vertical" />
            <a-popconfirm title="是否确定删除该角色？" @confirm="handleDelete(record)">
              <a-button :disabled="!$hasPermission('setting:permission:delRole')" type="link">删除</a-button>
            </a-popconfirm>
          </template>

        </template>
      </a-table>
    </template>

  </layout>
  <addRole :visible="visible" @ok="updateList" @cancel="visible = false" :id="id" :type="type" />
</template>
<script setup>
import addRole from './components/addRole.vue';
import { useRoute, useRouter } from 'vue-router'
import { usePagination, useRequest, } from 'vue-request';
import { roleList, roleDelete, } from '@/http/index.js'
import { message, Modal } from "ant-design-vue";
// 分页参数
let pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ["bottomLeft"],
  };
});
// 表格分页数据获取
const total = ref(0)


const { formParams, tableHeader, visible, id, type } = toRefs(reactive({


  id: '',
  type: 0,// 0-新增 1-编辑  2-查看
  visible: false,
  formParams: {
    roleName: ''
  },
  tabs: {

    1: "管理员账户列表",
    2: "角色管理列表",
  },
  tableHeader: [{
    title: '序号',
    key: 'index',
    align: 'center',
    width: 80
  },
  {
    title: '角色名称',
    dataIndex: 'roleName',
    width: 180,
    align: 'center',
  },
  {
    title: '角色详情',
    dataIndex: 'roleDesc',
    align: 'center',
    ellipsis: true
  },
  {
    title: '员工数量',
    key: 'userNum',
    align: 'center',
    ellipsis: true
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    align: 'center',
    width: 150,
    fixed: 'right'
  }]
})
);
const { data: dataSource, run, loading, pageSize, current, refresh } = usePagination((param) => {
  // 这里修改参数
  // return resignationInheritanceList({ ...param, ...getParams() })
  return roleList({ ...param, ...formParams.value })
},
  {
    manual: false, // 修改为false,让其自动执行
    pagination: {
      currentKey: 'pageNum',  // 通过该值指定接口 当前页数 参数的属性值
      pageSizeKey: 'pageSize',// 通过该值指定接口 每页获取条数 参数的属性值
      totalKey: 'data.data.count',  // 指定 data 中 total 属性的路径
    },
    formatResult: res => { // 返回数据格式化
      total.value = res.data.count
      return res.data.list
    }
  });

function updateList(value) {
  visible.value = false;
  // 新增 重置搜索数据
  if (!value) {
    resetData();
  } else {
    // 查看、编辑 不需要清空搜索数据 直接刷新
    refresh();
  }
}
function EditRole(record) {
  visible.value = true
  type.value = 1
  id.value = record.id
  console.log(record, id.value);

}
//删除角色
const handleDelete = (record) => {
  roleDelete({ id: record.id }).then(res => {
    if (res.code === 0) {
      message.success('删除成功')
      resetData()
    }
  })
}
// 加载数据
const loadData = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}


// 刷新数据
const refreshData = () => {
  run({ pageNum: 1, pageSize: pageSize.value })
};

// 重置数据
const resetData = () => {
  formParams.value = {
    roleName: '',
  }
  refreshData()
}
function addChang() {
  visible.value = true
  type.value = 0
  id.value = ''
}
</script>
