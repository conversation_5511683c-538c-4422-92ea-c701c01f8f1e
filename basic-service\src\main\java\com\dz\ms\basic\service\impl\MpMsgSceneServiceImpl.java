package com.dz.ms.basic.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.annotation.Cacheable;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.user.UserSimpleDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.user.UserInfoFeginClient;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.constants.CacheKeys;
import com.dz.ms.basic.dto.MpMsgSceneDTO;
import com.dz.ms.basic.dto.MpMsgSceneRelationDTO;
import com.dz.ms.basic.dto.MpMsgSubscribeDTO;
import com.dz.ms.basic.entity.*;
import com.dz.ms.basic.mapper.*;
import com.dz.ms.basic.service.MpMsgSceneService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 小程序订阅消息场景
 * @author: Handy
 * @date:   2023/07/07 14:22
 */
@Service
@Slf4j
public class MpMsgSceneServiceImpl extends ServiceImpl<MpMsgSceneMapper,MpMsgScene> implements MpMsgSceneService {

	@Resource
    private MpMsgSceneMapper mpMsgSceneMapper;
    @Resource
    private MpMsgMapper mpMsgMapper;
    @Resource
    private MpMsgSceneRelationMapper mpMsgSceneRelationMapper;
    @Resource
    private MpMsgSubscribeMapper mpMsgSubscribeMapper;
    @Resource
    private MpMsgSubscribeLogMapper mpMsgSubscribeLogMapper;
    @Resource
    private RedisService redisService;
    @Resource
    private UserInfoFeginClient userInfoFeginClient;

    /**
     * 分页查询小程序订阅消息场景
     * @param param
     * @return PageInfo<MpMsgSceneDTO>
     */
    @Override
    public PageInfo<MpMsgSceneDTO> getMpMsgSceneList(MpMsgSceneDTO param) {
        MpMsgScene mpMsgScene = BeanCopierUtils.convertObjectTrim(param,MpMsgScene.class);
        IPage<MpMsgScene> page = mpMsgSceneMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), new QueryWrapper<>(mpMsgScene));
        List<MpMsgSceneDTO> list = BeanCopierUtils.convertList(page.getRecords(), MpMsgSceneDTO.class);
        if(!CollectionUtils.isEmpty(list)) {
            List<String> sceneList = list.stream().map(MpMsgSceneDTO::getScene).collect(Collectors.toList());
            List<MpMsgSceneRelationDTO> relationList = mpMsgSceneRelationMapper.selectSceneRelationBySceneList(sceneList);
            Map<String,List<MpMsgSceneRelationDTO>> sceneMap = new HashMap<>();
            if(!CollectionUtils.isEmpty(relationList)) {
                for (MpMsgSceneRelationDTO sceneRelation : relationList) {
                    List<MpMsgSceneRelationDTO> relations = sceneMap.get(sceneRelation.getScene());
                    if(null == relations) {
                        relations = new ArrayList<>();
                    }
                    relations.add(sceneRelation);
                    sceneMap.put(sceneRelation.getScene(),relations);
                }
            }
            for (MpMsgSceneDTO mpMsgSceneDTO : list) {
                mpMsgSceneDTO.setRelationList(sceneMap.get(mpMsgSceneDTO.getScene()));
            }
        }
        return new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),list);
    }

    /**
     * 根据ID查询小程序订阅消息场景
     * @param id
     * @return MpMsgSceneDTO
     */
    @Override
    public MpMsgSceneDTO getMpMsgSceneById(Long id) {
        MpMsgScene mpMsgScene = mpMsgSceneMapper.selectById(id);
        return BeanCopierUtils.convertObject(mpMsgScene,MpMsgSceneDTO.class);
    }

    /**
     * 保存小程序订阅消息场景
     * @param param
     * @return Long
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveMpMsgScene(MpMsgSceneDTO param) {
        MpMsgScene mpMsgScene = new MpMsgScene(param.getId(), param.getScene(), param.getSceneName(), param.getState());
        boolean isAdd = ParamUtils.isNullOr0Long(mpMsgScene.getId());
        LambdaQueryWrapper<MpMsgScene> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MpMsgScene::getScene,param.getScene());
        if(!isAdd) {
            queryWrapper.ne(MpMsgScene::getId,param.getId());
        }
        Long count = mpMsgSceneMapper.selectCount(queryWrapper);
        if(count > 0) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "场景编码不能重复");
        }
        if(isAdd) {
            mpMsgSceneMapper.insert(mpMsgScene);
        }
        else {
            MpMsgScene getMpMsgScene = mpMsgSceneMapper.selectById(mpMsgScene.getId());
            mpMsgSceneMapper.updateById(mpMsgScene);
            if(!mpMsgScene.getScene().equals(getMpMsgScene.getScene())) {
                MpMsgSceneRelation mpMsgSceneRelation = new MpMsgSceneRelation();
                mpMsgSceneRelation.setScene(mpMsgScene.getScene());
                mpMsgSceneRelationMapper.update(mpMsgSceneRelation,new LambdaQueryWrapper<MpMsgSceneRelation>().eq(MpMsgSceneRelation::getScene,getMpMsgScene.getScene()));
            }
        }
        if(null == param.getRelationList()) {
            return mpMsgScene.getId();
        }
        List<Long> msgIdList = param.getRelationList().stream().map(MpMsgSceneRelationDTO::getMsgId).collect(Collectors.toList());
        List<Long> hasList = new ArrayList<>();
        if(!isAdd) {
            List<MpMsgSceneRelation> list = mpMsgSceneRelationMapper.selectList(new LambdaQueryWrapper<MpMsgSceneRelation>().eq(MpMsgSceneRelation::getScene,param.getScene()));
            for (MpMsgSceneRelation mpMsgSceneRelation : list) {
                hasList.add(mpMsgSceneRelation.getMsgId());
                if(!msgIdList.contains(mpMsgSceneRelation.getMsgId())) {
                    mpMsgSceneRelationMapper.deleteById(mpMsgSceneRelation.getId());
                }
            }
        }
        for (MpMsgSceneRelationDTO mpMsgSceneRelationDTO : param.getRelationList()) {
            if(hasList.contains(mpMsgSceneRelationDTO.getMsgId())) {
                continue;
            }
            MpMsgSceneRelation mpMsgSceneRelation = new MpMsgSceneRelation(null,mpMsgScene.getScene(),mpMsgSceneRelationDTO.getMsgId());
            mpMsgSceneRelationMapper.insert(mpMsgSceneRelation);
        }
        redisService.del(CacheKeys.SUBSCRIBE_MSGID_BYSCENE+":"+ SecurityContext.getUser().getTenantId()+":"+mpMsgScene.getScene());
        return mpMsgScene.getId();
    }

    /**
     * 根据ID删除小程序订阅消息场景
     * @param param
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMpMsgSceneById(IdCodeDTO param) {
        MpMsgScene getMpMsgScene = mpMsgSceneMapper.selectById(param.getId());
        if(null == getMpMsgScene) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "ID参数错误");
        }
        mpMsgSceneMapper.deleteById(param.getId());
        mpMsgSceneRelationMapper.delete(new LambdaQueryWrapper<MpMsgSceneRelation>().eq(MpMsgSceneRelation::getScene,getMpMsgScene.getScene()));
        redisService.del(CacheKeys.SUBSCRIBE_MSGID_BYSCENE+":"+ SecurityContext.getUser().getTenantId()+":"+getMpMsgScene.getScene());
    }

    /**
     * 根据场景获取小程序订阅消息模板列表
     * @param scene
     * @return
     */
    @Override
//    @Cacheable(prefix = CacheKeys.SUBSCRIBE_MSGID_BYSCENE,key = "'#tenantId'+'#scene'")
    public List<MpMsgSubscribeDTO> getSubscribeMsgIdsByScene(String scene, Long tenantId) {
        List<MpMsgSubscribeDTO> msgSubscribeDTOS = new ArrayList<>();
        //根据场景获取订阅消息ID
        List<MpMsgSceneRelation> mpMsgSceneRelation=mpMsgSceneRelationMapper.selectList(new LambdaQueryWrapper<MpMsgSceneRelation>().eq(MpMsgSceneRelation::getScene,scene));
        //根据订阅消息ID获取订阅消息模板
        List<Long> msgIds = mpMsgSceneRelation.stream().map(MpMsgSceneRelation::getMsgId).collect(Collectors.toList());
        //根据订阅消息模板ID获取订阅消息模板
        List<MpMsg> list = mpMsgMapper.selectList(new LambdaQueryWrapper<MpMsg>().in(MpMsg::getId,msgIds));
        //获取当前客户订阅消息模板的次数
        Long uId = SecurityContext.getUser().getUid();
        //根据Uid和msgId获取订阅消息
        List<MpMsgSubscribe> mpMsgSubscribes = mpMsgSubscribeMapper.selectList(new LambdaQueryWrapper<MpMsgSubscribe>().eq(MpMsgSubscribe::getUid,uId).in(MpMsgSubscribe::getMsgId,msgIds));
        //根据msgId进行分组
        if (!CollectionUtils.isEmpty(mpMsgSubscribes)) {

            Map<Long, List<MpMsgSubscribe>> map = mpMsgSubscribes.stream().collect(Collectors.groupingBy(MpMsgSubscribe::getMsgId));
            for (MpMsg mpMsg : list) {
                MpMsgSubscribeDTO mpMsgSubscribeDTO = new MpMsgSubscribeDTO();
                List<MpMsgSubscribe> mpMsgSubscribes1 = map.get(mpMsg.getId());
                mpMsgSubscribeDTO.setMsgId(mpMsg.getId());
                mpMsgSubscribeDTO.setTemplateId(mpMsg.getTemplateId());
                mpMsgSubscribeDTO.setScene(scene);
                if (!CollectionUtils.isEmpty(mpMsgSubscribes1)) {
                    //统计订阅次数
                    Integer subNumber = mpMsgSubscribes1.stream().mapToInt(MpMsgSubscribe::getSubNumber).sum();
                    mpMsgSubscribeDTO.setSubNumber(subNumber);
                }else {
                    mpMsgSubscribeDTO.setSubNumber(0);
                }
                msgSubscribeDTOS.add(mpMsgSubscribeDTO);
            }
        }else {
            for (MpMsg mpMsg : list) {
                MpMsgSubscribeDTO mpMsgSubscribeDTO = new MpMsgSubscribeDTO();
                mpMsgSubscribeDTO.setMsgId(mpMsg.getId());
                mpMsgSubscribeDTO.setScene(scene);
                mpMsgSubscribeDTO.setTemplateId(mpMsg.getTemplateId());
                mpMsgSubscribeDTO.setSubNumber(0);
                log.info("subNumber数据2444444：{}",mpMsgSubscribeDTO.getSubNumber());
                msgSubscribeDTOS.add(mpMsgSubscribeDTO);
            }
        }
        return msgSubscribeDTOS;
    }
    @Override
    public List<MpMsgSubscribeDTO> getSubscribeMsgIds(List<String> templateIds, Long tenantId) {
        //创建一个返回实体类
        List<MpMsgSubscribeDTO> msgSubscribeDTOS = new ArrayList<>();
        //根据模版ID获取订阅消息模板
        List<MpMsg> list = mpMsgMapper.selectList(new LambdaQueryWrapper<MpMsg>().in(MpMsg::getTemplateId,templateIds));
        //将模版的ID进行解析
        List<Long> msgIds = list.stream().map(MpMsg::getId).collect(Collectors.toList());
        //获取当前客户ID
        Long uId = SecurityContext.getUser().getUid();
        //根据Uid和msgId获取订阅消息
        List<MpMsgSubscribe> mpMsgSubscribes = mpMsgSubscribeMapper.selectList(new LambdaQueryWrapper<MpMsgSubscribe>().eq(MpMsgSubscribe::getUid,uId).in(MpMsgSubscribe::getMsgId,msgIds));
        //判断用户是否订阅
        if (CollectionUtils.isEmpty(mpMsgSubscribes)) {
            for (MpMsg mpMsg : list) {
                MpMsgSubscribeDTO mpMsgSubscribeDTO = new MpMsgSubscribeDTO();
                mpMsgSubscribeDTO.setMsgId(mpMsg.getId());
                mpMsgSubscribeDTO.setTemplateId(mpMsg.getTemplateId());
                mpMsgSubscribeDTO.setSubNumber(0);
                msgSubscribeDTOS.add(mpMsgSubscribeDTO);
            }
        }else {
            //根据msgid进行分组
            Map<Long, List<MpMsgSubscribe>> map = mpMsgSubscribes.stream().collect(Collectors.groupingBy(MpMsgSubscribe::getMsgId));
            //循环msgList
            for (MpMsg mpMsg : list) {
                MpMsgSubscribeDTO mpMsgSubscribeDTO = new MpMsgSubscribeDTO();
                List<MpMsgSubscribe> mpMsgSubscribes1 = map.get(mpMsg.getId());

                mpMsgSubscribeDTO.setMsgId(mpMsg.getId());
                mpMsgSubscribeDTO.setTemplateId(mpMsg.getTemplateId());
                if (!CollectionUtils.isEmpty(mpMsgSubscribes1)) {
                    //统计订阅次数
                    Integer subNumber = mpMsgSubscribes1.stream().mapToInt(MpMsgSubscribe::getSubNumber).sum();
                    mpMsgSubscribeDTO.setSubNumber(subNumber);
                }else {
                    mpMsgSubscribeDTO.setSubNumber(0);
                }
                msgSubscribeDTOS.add(mpMsgSubscribeDTO);
            }
        }

        return msgSubscribeDTOS;
    }
    /*@Override
    public List<MpMsgSubscribeDTO> getSubscribeMsgIds(List<String> templateIds, Long tenantId) {
        List<MpMsgSubscribeDTO> msgSubscribeDTOS = new ArrayList<>();
        List<MpMsg> list = mpMsgMapper.selectList(new LambdaQueryWrapper<MpMsg>().in(MpMsg::getTemplateId,templateIds));
        log.info("MSG查询结果为：{}",JSON.toJSONString(list));
        List<Long> msgIds = list.stream().map(MpMsg::getId).collect(Collectors.toList());
        log.info("MSGID为：{}",JSON.toJSONString(msgIds));
        //获取当前客户订阅消息模板的次数
        Long uId = SecurityContext.getUser().getUid();
        //根据Uid和msgId获取订阅消息
        List<MpMsgSubscribe> mpMsgSubscribes = mpMsgSubscribeMapper.selectList(new LambdaQueryWrapper<MpMsgSubscribe>().eq(MpMsgSubscribe::getUid,uId).in(MpMsgSubscribe::getMsgId,msgIds));
        log.info("MSGSUBSCRIBES查询结果为：{}",JSON.toJSONString(mpMsgSubscribes));
        //根据msgId进行分组
        if (!CollectionUtils.isEmpty(mpMsgSubscribes)) {
            MpMsgSubscribeDTO mpMsgSubscribeDTO = new MpMsgSubscribeDTO();
            Map<Long, List<MpMsgSubscribe>> map = mpMsgSubscribes.stream().collect(Collectors.groupingBy(MpMsgSubscribe::getMsgId));
            for (MpMsg mpMsg : list) {
                List<MpMsgSubscribe> mpMsgSubscribes1 = map.get(mpMsg.getId());
                log.info("匹配msgid是：{}",mpMsg.getId());
                mpMsgSubscribeDTO.setMsgId(mpMsg.getId());
                mpMsgSubscribeDTO.setTemplateId(mpMsg.getTemplateId());
                log.info("出参msgid是：{}",mpMsgSubscribeDTO.getMsgId());
                if (!CollectionUtils.isEmpty(mpMsgSubscribes1)) {
                    log.info("订阅消息模板数据：{}",mpMsgSubscribes1.size());
                    mpMsgSubscribeDTO.setSubNumber(mpMsgSubscribes1.size());
                    log.info("subNumber数据11111111：{}",mpMsgSubscribeDTO.getSubNumber());
                }else {
                    mpMsgSubscribeDTO.setSubNumber(0);
                    log.info("subNumber数据22222：{}",mpMsgSubscribeDTO.getSubNumber());
                }
                msgSubscribeDTOS.add(mpMsgSubscribeDTO);
            }
        }else {
            for (MpMsg mpMsg : list) {
                MpMsgSubscribeDTO mpMsgSubscribeDTO = new MpMsgSubscribeDTO();
                mpMsgSubscribeDTO.setMsgId(mpMsg.getId());
                mpMsgSubscribeDTO.setTemplateId(mpMsg.getTemplateId());
                mpMsgSubscribeDTO.setSubNumber(0);
                log.info("subNumber数据3333333：{}",mpMsgSubscribeDTO.getSubNumber());
                msgSubscribeDTOS.add(mpMsgSubscribeDTO);
            }
        }
        return msgSubscribeDTOS;
    }*/

    /**
     * 添加小程序订阅消息订阅记录
     */
    @Override
    public void addSubscribeMsgRecord(List<String> templateIds) {
        UserSimpleDTO currentUser = userInfoFeginClient.getUserSimpleInfo(null).getData();
        for (String templateId : templateIds) {
            //根据模板ID获取订阅消息模板
            MpMsg mpMsg = mpMsgMapper.selectOne(new LambdaQueryWrapper<MpMsg>().eq(MpMsg::getTemplateId,templateId));
            //根据msgId获取绑定场景
//            MpMsgSceneRelation mpMsgSceneRelation = mpMsgSceneRelationMapper.selectOne(new LambdaQueryWrapper<MpMsgSceneRelation>().eq(MpMsgSceneRelation::getMsgId,mpMsg.getId()));
            MpMsgSubscribe subscribe = new MpMsgSubscribe(null,null,mpMsg.getId(),templateId,currentUser.getId(),currentUser.getOpenid(),0,null);
            MpMsgSubscribeLog subscribeLog = BeanCopierUtils.convertObject(subscribe,MpMsgSubscribeLog.class);
            // 订阅记录
            mpMsgSubscribeLogMapper.insert(subscribeLog);
            //进行判断是否已经订阅，如果订阅则修改订阅次数，否则新增订阅记录
            MpMsgSubscribe getSubscribe = mpMsgSubscribeMapper.selectOne(new LambdaQueryWrapper<MpMsgSubscribe>()
                    .eq(MpMsgSubscribe::getUid,subscribe.getUid()).eq(MpMsgSubscribe::getMsgId,subscribe.getMsgId()));
            if (null != getSubscribe) {
                getSubscribe.setSubNumber(getSubscribe.getSubNumber()+1);
                mpMsgSubscribeMapper.updateById(getSubscribe);
            }else {
                subscribe.setSubNumber(1);
                mpMsgSubscribeMapper.insert(subscribe);
            }

        }
    }
	
}