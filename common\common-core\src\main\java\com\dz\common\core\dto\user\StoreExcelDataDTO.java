package com.dz.common.core.dto.user;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**

 */
@Data
public class StoreExcelDataDTO {
    @ExcelProperty("shop_id_str")
    @ColumnWidth(20)
    private String storeSn;

    @ExcelProperty("shop_name_str")
    @ColumnWidth(30)
    private String storeName;

    @ExcelProperty("address_str")
    @ColumnWidth(50)
    private String storeAddress;

    @ExcelProperty("tel_str")
    @ColumnWidth(20)
    private String phone;

    @ExcelProperty("longitude_str")
    @ColumnWidth(20)
    private String longitude;

    @ExcelProperty("latitude_str")
    @ColumnWidth(20)
    private String latitude;

    @ExcelProperty("province_name_str")
    @ColumnWidth(30)
    private String province;

    @ExcelProperty("city_name_str")
    @ColumnWidth(30)
    private String city;

    @ExcelProperty("business_hours_1")
    @ColumnWidth(30)
    private String openingHourOne;

    @ExcelProperty("business_hours_2")
    @ColumnWidth(30)
    private String openingHourTwo;

    @ExcelProperty("open_date_str")
    @ColumnWidth(20)
    private String openDate;
}
