const app = getApp()

Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    type: {
      type: Number,
      value: 1
    },
    isBackHidden: {
      type: Boolean,
      value: false
    },
    color: {
      type: String,
      value: '#3D3D3D'
    },
    delta: {
      type: Number
    },
    url: {
      type: String
    },
    background: {
      type: String
    },
    isFixed: {
      type: Boolean,
      value: false
    },
    isShare: {
      type: Boolean
    }
  },
  /**
   * 组件的初始数据
   */
  data: {
    isShowHomeIcon: false,
    // 自定义导航栏的高度
    menuButtonTop: app.globalData.menuButtonTop,
    menuButtonHeight: app.globalData.menuButtonHeight,
    statusBarHeight: app.globalData.statusBarHeight,
    navBarHeight: app.globalData.navBarHeight,
    bg: 'transparent'
  },

  attached() {
    if (wx.$mp.isTabBarPage()) {
      this.setData({
        isBackHidden: true
      })
    } else {
      this.setData({
        isShowHomeIcon: wx.$mp.isFirstPage()
      })
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    pageHome() {
      wx.$mp.navigateTo({
        url: '/pages/index/index'
      })
    },
    pageBack() {
      if (this.properties.isShare) {
        wx.$mp.switchTab({
          url: '/pages/index/index'
        })
      } else if (this.properties.url) {
        wx.$mp.switchTab({
          url: this.properties.url
        })
      } else {
        wx.$mp.navigateBack({
          delta: this.properties.delta || 1
        })
      }
    }
  }
})
