package com.dz.ms.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.ms.basic.dto.MpMsgSceneDTO;
import com.dz.ms.basic.dto.MpMsgSubscribeDTO;
import com.dz.ms.basic.entity.MpMsgScene;

import java.util.List;

/**
 * 小程序订阅消息场景接口
 * @author: Handy
 * @date:   2023/07/07 14:22
 */
public interface MpMsgSceneService extends IService<MpMsgScene> {

	/**
     * 分页查询小程序订阅消息场景
     * @param param
     * @return PageInfo<MpMsgSceneDTO>
     */
    public PageInfo<MpMsgSceneDTO> getMpMsgSceneList(MpMsgSceneDTO param);

    /**
     * 根据ID查询小程序订阅消息场景
     * @param id
     * @return MpMsgSceneDTO
     */
    public MpMsgSceneDTO getMpMsgSceneById(Long id);

    /**
     * 保存小程序订阅消息场景
     * @param param
     * @return Long
     */
    public Long saveMpMsgScene(MpMsgSceneDTO param);

    /**
     * 根据ID删除小程序订阅消息场景
     * @param param
     */
    public void deleteMpMsgSceneById(IdCodeDTO param);

    /**
     * 根据场景获取小程序订阅消息模板列表
     * @param scene
     * @return
     */
    List<MpMsgSubscribeDTO> getSubscribeMsgIdsByScene(String scene, Long tenantId);

    /**
     * 添加小程序订阅消息订阅记录
     * @param param
     */
    void addSubscribeMsgRecord(List<String> templateIds);

    List<MpMsgSubscribeDTO> getSubscribeMsgIds(List<String> templateIds, Long tenantId);
}
