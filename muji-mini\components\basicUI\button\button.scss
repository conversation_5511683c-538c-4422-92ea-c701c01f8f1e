.basic-button {
  display: inline-block;

  &.maxlarge {
    height: 90rpx;
    font-size: 28rpx;
    line-height: 90rpx;
    border-radius: 10rpx;
  }

  &.large {
    height: 80rpx;
    /* 大按钮高度 */
    font-size: 28rpx;
    /* 大按钮字体大小 */
    border-radius: 10rpx;
    line-height: 80rpx;
    font-weight: 500;
  }

  &.middle {
    height: 70rpx;
    /* 中按钮高度 */
    font-size: 16rpx;
    /* 中按钮字体大小 */
    border-radius: 5rpx;
    line-height: 70rpx;
  }

  &.small {
    height: 60rpx;
    /* 小按钮高度 */
    font-size: 24rpx;
    /* 小按钮字体大小 */
    border-radius: 5rpx;
    line-height: 60rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-style: normal;
  }

  &.primary {
    background-color: var(--btn-primary);
    color: var(--text-white-color);
  }

  &.plain {
    background-color: var(--plain-button-background-color);
    border: 1rpx solid var(--btn-primary);
    color: var(--btn-primary);
  }

  &.gray {
    background-color: var(--btn-gray);
    // border: 1rpx solid var(--btn-gray);
    color: var(--btn-primary);
  }

  &.disabled {
    opacity: 0.3;
  }
}
