package com.dz.ms.sales.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.core.dto.user.UserSimpleDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.user.UserInfoFeginClient;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.ms.sales.entity.UserLotterySummary;
import com.dz.ms.sales.mapper.UserLotterySummaryMapper;
import com.dz.ms.sales.service.UserLotterySummaryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/5/21
 */
@Service
@Slf4j
public class UserLotterySummaryServiceImpl implements UserLotterySummaryService {
    @Resource
    private UserLotterySummaryMapper userLotterySummaryMapper;
    @Resource
    private UserInfoFeginClient userInfoFeginClient;

    @Override
    public UserLotterySummary getUserLotterySummary(String campaignCode, String unionid) {
        UserLotterySummary userLotterySummary = userLotterySummaryMapper.selectOne(new LambdaQueryWrapper<UserLotterySummary>().eq(UserLotterySummary::getCampaignCode, campaignCode).eq(UserLotterySummary::getUnionid, unionid).orderByDesc(UserLotterySummary::getId).last("limit 1"));
        if (ObjectUtils.isNotEmpty(userLotterySummary)) {
            return userLotterySummary;
        }
        UserSimpleDTO userInfo = userInfoFeginClient.getCurrentUserSimpleInfo().getData();
        userLotterySummaryMapper.insert(new UserLotterySummary(campaignCode, userInfo.getId(), userInfo.getOpenid(), userInfo.getUnionid(), userInfo.getUsername(), userInfo.getMobile()));
        return getUserLotterySummary(campaignCode, unionid);
    }

    @Override
    public void operation(String campaignCode, String unionid, String operation, int num) {
        UserLotterySummary userLotterySummary = getUserLotterySummary(campaignCode, unionid);
        if ("+".equals(operation)) {
            userLotterySummary.setTotalCount(userLotterySummary.getTotalCount() + num);
            userLotterySummary.setSurplusCount(userLotterySummary.getSurplusCount() + num);
            userLotterySummaryMapper.updateById(userLotterySummary);
        }
        if ("-".equals(operation)) {
            if (userLotterySummary.getSurplusCount() <= 0) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "抽奖次数不足");
            }
            userLotterySummary.setSurplusCount(userLotterySummary.getSurplusCount() - num);
            userLotterySummaryMapper.updateById(userLotterySummary);
        }


    }

    @Override
    public List<UserLotterySummary> getResidueLotterySummary() {
        return userLotterySummaryMapper.selectList(new LambdaQueryWrapper<UserLotterySummary>());
//        return userLotterySummaryMapper.selectList(new LambdaQueryWrapper<UserLotterySummary>().gt(UserLotterySummary::getSurplusCount, 0));
    }

}
