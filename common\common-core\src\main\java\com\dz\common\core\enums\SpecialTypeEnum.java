package com.dz.common.core.enums;

import java.util.Objects;

/**
 * @description: 特殊规则类型枚举
 * <AUTHOR>
 * @date: 2023/11/21 20:17
 **/
public enum SpecialTypeEnum {

    CROWD(1, "人群包"),
    GIFT(2, "买赠"),
    ;

    private final Integer type;
    private final String value;

    SpecialTypeEnum(Integer code, String value) {
        this.type = code;
        this.value = value;
    }

    public Integer getType() {
        return type;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SpecialTypeEnum resultEnum : SpecialTypeEnum.values()) {
            if (Objects.equals(resultEnum.getType(), code)) {
                return resultEnum.value;
            }
        }
        return null;
    }
}
