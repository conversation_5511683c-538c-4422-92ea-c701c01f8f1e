package com.dz.common.core.enums;

import java.util.Objects;

/**
 * Describe：任务类型枚举
 * Created by 吴蜀黍 on 2023/8/20 8:36
 **/
public enum TaskEnum {

    REGISTER(1, "注册"),
    IMPROVE_INFO(2, "完善个人信息"),
    ADD_TEACHER(3, "添加专属彩妆师"),
    INVITE_PERSON(4, "邀请新人"),
    SIGN_IN(5, "签到"),
    EMPTY_BOTTLE(6, "空瓶回收"),
    BUY_PRODUCT(7, "购买商品"),
    BOOKING(8, "预约"),
    ;

    private final Integer code;
    private final String value;

    TaskEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TaskEnum resultEnum : TaskEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum.value;
            }
        }
        return null;
    }
}
