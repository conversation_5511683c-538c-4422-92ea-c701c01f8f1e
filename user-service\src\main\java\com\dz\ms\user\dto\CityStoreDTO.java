package com.dz.ms.user.dto;

import com.dz.common.base.dto.BaseDTO;
import com.dz.common.core.dto.user.StoreDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 城市门店信息DTO
 * @author: Handy
 * @date:   2023/07/09 23:58
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "城市门店信息")
public class CityStoreDTO {

    @ApiModelProperty(value = "城市ID")
    private Long id;
    @ApiModelProperty(value = "城市名称")
    private String city;
    @ApiModelProperty(value = "城市门店列表")
    private List<StoreDTO> storeList;
}
