package com.dz.ms.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.order.dto.VerifyRecordDTO;
import com.dz.ms.order.entity.VerifyRecord;
import com.dz.ms.order.mapper.VerifyRecordMapper;
import com.dz.ms.order.service.VerifyRecordService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * 核销记录
 * @author: Handy
 * @date:   2024/06/06 14:47
 */
@Service
public class VerifyRecordServiceImpl extends ServiceImpl<VerifyRecordMapper,VerifyRecord> implements VerifyRecordService {

	@Resource
    private VerifyRecordMapper verifyRecordMapper;

	/**
     * 分页查询核销记录
     * @param param
     * @return PageInfo<VerifyRecordDTO>
     */
    @Override
    public PageInfo<VerifyRecordDTO> getVerifyRecordList(VerifyRecordDTO param) {
        VerifyRecord verifyRecord = BeanCopierUtils.convertObjectTrim(param,VerifyRecord.class);
        IPage<VerifyRecord> page = verifyRecordMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), new QueryWrapper<>(verifyRecord));
        return new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),BeanCopierUtils.convertList(page.getRecords(), VerifyRecordDTO.class));
    }

    /**
     * 根据ID查询核销记录
     * @param id
     * @return VerifyRecordDTO
     */
    @Override
    public VerifyRecordDTO getVerifyRecordById(Long id) {
        VerifyRecord verifyRecord = verifyRecordMapper.selectById(id);
        return BeanCopierUtils.convertObject(verifyRecord,VerifyRecordDTO.class);
    }

    /**
     * 保存核销记录
     * @param param
     * @return Long
     */
    @Override
    public Long saveVerifyRecord(VerifyRecordDTO param) {
        VerifyRecord verifyRecord = new VerifyRecord(param.getId(), param.getVerifyCode(), param.getVerifyId(), param.getVerifyType(), param.getUid(), param.getUserName(), param.getGender(), param.getMobile(), param.getCardLevel(), param.getRecordName(), param.getItemName(), param.getBookingDate(), param.getTimeSlot(), param.getVerifyStartTime(), param.getVerifyEndTime(), param.getStoreCode(), param.getStoreName(), param.getEmpCode(), param.getVerifyStoreCode(), param.getVerifyStoreName(), param.getVerifierType(), param.getVerifierCode(), param.getVerifierName(), param.getCancelerType(), param.getCancelerCode(), param.getCancelerName(), param.getUpdaterType(), param.getUpdaterCode(), param.getUpdaterName(), param.getConsumeType());
        if(ParamUtils.isNullOr0Long(verifyRecord.getId())) {
            verifyRecordMapper.insert(verifyRecord);
        }
        else {
            verifyRecordMapper.updateById(verifyRecord);
        }
        return verifyRecord.getId();
    }

    /**
     * 根据ID删除核销记录
     * @param param
     */
    @Override
    public void deleteVerifyRecordById(IdCodeDTO param) {
        verifyRecordMapper.deleteById(param.getId());
    }
	
}