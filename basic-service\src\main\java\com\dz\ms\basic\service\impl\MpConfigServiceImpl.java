package com.dz.ms.basic.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.core.constants.CommonConstants;
import com.dz.common.core.enums.WechatApiEnum;
import com.dz.common.core.dto.wechat.CodeSessionDTO;
import com.dz.common.core.dto.wechat.DecryptUserDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.service.WechatRequestSevice;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.ms.basic.constants.CacheKeys;
import com.dz.ms.basic.dto.MpConfigSimpleDTO;
import com.dz.ms.basic.entity.MpConfig;
import com.dz.ms.basic.mapper.MpConfigMapper;
import com.dz.ms.basic.service.MpConfigService;
import com.dz.ms.basic.utils.WxDataDecryptUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

/**
 * 公众号/小程序配置
 * @author: Handy
 * @date:   2022/01/28 15:35
 */
@Service
@Slf4j
public class MpConfigServiceImpl extends ServiceImpl<MpConfigMapper,MpConfig> implements MpConfigService {

    @Resource
    private MpConfigMapper mpConfigMapper;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private RedisService redisService;
    @Resource
    private WechatRequestSevice wechatRequestSevice;
    @Value("${wx.config.cipher}")
    private String cipher;
    /**
     * 根据租户ID获取小程序token
     * @param tenantId
     * @param cleanCach
     * @return
     */
    @Override
    public String getMiniappAccessToken(Long tenantId,boolean cleanCach) {
//        MpConfigSimpleDTO mpConfig = getMpConfigByTenantIdAndType(tenantId,1);
//        return getAccessTokenBySecret(mpConfig,cleanCach);
        MpConfigSimpleDTO mpConfig = getMpConfigByTenantIdAndType(tenantId, 1);
        String profile = System.getProperty("spring.profiles.active");
        if ("dev".equals(profile)) {
            return getAccessTokenBySecret(mpConfig, cleanCach);
        } else if ("uat".equals(profile)) {
            return getAccessTokenBySecret(mpConfig, cleanCach);
        } else if ("pro".equals(profile)) {
            return getAccessTokenBySecretPost(mpConfig, cleanCach);
        } else {
            return getAccessTokenBySecretPost(mpConfig, cleanCach);
        }
    }

    /**
     * 根据公众号小程序配置获取AccessToken
     * @param mpConfig
     * @return
     */
    private String getAccessTokenBySecret(MpConfigSimpleDTO mpConfig,boolean cleanCach) {
        String key = CacheKeys.ACCESSTOKEN_BY_SECRET+mpConfig.getAppId();
        String accesstoken = null;
        if(cleanCach) {
            redisService.del(key);
        }
        else {
            accesstoken = redisService.getString(key);
            if(null != accesstoken) {
                return accesstoken;
            }
        }
        JSONObject json = restTemplate.getForObject(WechatApiEnum.API_GET_MP_ACCCESS_TOKEN.getUrl()+"?grant_type=client_credential&appid=" + mpConfig.getAppId() + "&secret=" + mpConfig.getAppSecret(), JSONObject.class);
        log.info("获取access_token result:"+ (null == json ? "emp" : json.toJSONString()));
        if(null != json && json.containsKey("access_token")) {
            accesstoken = json.getString("access_token");
            int expires = json.getIntValue("expires_in");
            redisService.setString(key,accesstoken,expires-600);
        }
        else {
            log.info("获取access_token失败appid:{}appsecret:{}",mpConfig.getAppId(),mpConfig.getAppSecret());
            throw new BusinessException(ErrorCode.INTERNAL_ERROR,"获取access_token失败");
        }
        return accesstoken;
    }
    /**
     * 根据公众号小程序配置获取AccessToken POST接口
     * @param mpConfig
     * @return
     */
    private String getAccessTokenBySecretPost(MpConfigSimpleDTO mpConfig,boolean cleanCach) {
        String key = CacheKeys.ACCESSTOKEN_BY_SECRET_POST+mpConfig.getAppId();
        String accesstoken = null;
        if(cleanCach) {
            redisService.del(key);
        }
        else {
            accesstoken = redisService.getString(key);
            if(null != accesstoken) {
                return accesstoken;
            }
        }
//        JSONObject json = restTemplate.getForObject(WechatApiEnum.API_GET_MP_ACCCESS_TOKEN.getUrl()+"?grant_type=client_credential&appid=" + mpConfig.getAppId() + "&secret=" + mpConfig.getAppSecret(), JSONObject.class);
        Map<String,String> request = new HashMap<>();
        request.put("grant_type","client_credential");
        request.put("appid",mpConfig.getAppId());
        request.put("secret",mpConfig.getAppSecret());
        JSONObject json  = restTemplate.postForObject(WechatApiEnum.API_POST_MP_ACCCESS_TOKEN.getUrl(),request,JSONObject.class);
        log.info("获取access_token result:"+ (null == json ? "emp" : json.toJSONString()));
        if(null != json && json.containsKey("access_token")) {
            accesstoken = json.getString("access_token");
            int expires = json.getIntValue("expires_in");
            redisService.setString(key,accesstoken,expires-600);
        }
        else {
            log.info("获取access_token失败appid:{}appsecret:{}",mpConfig.getAppId(),mpConfig.getAppSecret());
            throw new BusinessException(ErrorCode.INTERNAL_ERROR,"获取access_token失败");
        }
        return accesstoken;
    }

    /**
     * 根据租户ID和类型获取公众号小程序配置信息
     * @param tenantId
     * @param type
     * @return
     */
    public MpConfigSimpleDTO getMpConfigByTenantIdAndType(Long tenantId,Integer type) {
        String key = CacheKeys.MP_CONFIG_SIMPLE + tenantId + ":" + type;
        Object value = redisService.get(key);
        if(null != value) {
            return (MpConfigSimpleDTO) value;
        }
        LambdaQueryWrapper<MpConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MpConfig :: getTenantId,tenantId);
        wrapper.eq(MpConfig :: getAppType,type);
        MpConfig mpConfig = mpConfigMapper.selectOne(wrapper);
        if(null != mpConfig) {
            MpConfigSimpleDTO mpConfigSimple = BeanCopierUtils.convertObject(mpConfig,MpConfigSimpleDTO.class);
            redisService.set(key,mpConfigSimple, CommonConstants.WEEK_SECONDS);
            return mpConfigSimple;
        }
        return null;
    }

    /**
     * 根据appId获取公众号小程序配置信息
     * @param appId
     * @return
     */
    public MpConfigSimpleDTO getSimpleMpConfigByAppId(String appId) {
        String key = CacheKeys.MP_CONFIG_SIMPLE + appId;
        Object value = redisService.get(key);
        if(null != value) {
            return (MpConfigSimpleDTO) value;
        }
        LambdaQueryWrapper<MpConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MpConfig :: getAppId,appId);
        MpConfig mpConfig = mpConfigMapper.selectOne(wrapper);
        if(null != mpConfig) {
            MpConfigSimpleDTO mpConfigSimple = BeanCopierUtils.convertObject(mpConfig,MpConfigSimpleDTO.class);
            redisService.set(key,mpConfigSimple, CommonConstants.WEEK_SECONDS);
            return mpConfigSimple;
        }
        return null;
    }

    /**
     * 小程序登录code换取session
     * @param appId
     * @param code
     * @return
     */
    @Override
    public CodeSessionDTO getMinappSessionByCode(String appId,String code) {
        MpConfigSimpleDTO mpConfigSimple = getSimpleMpConfigByAppId(appId);
        if(null == mpConfigSimple) {
            return null;
        }
        CodeSessionDTO codeSession = wechatRequestSevice.requestParam(WechatApiEnum.API_MINAPP_JSCODE_GETSESSION,mpConfigSimple.getTenantId(),CodeSessionDTO.class,mpConfigSimple.getAppId(),mpConfigSimple.getAppSecret(),code);
        if(null == codeSession) {
            return null;
        }
        redisService.setString(CacheKeys.MP_SESSION_KEY+codeSession.getOpenid(),codeSession.getSessionKey(), CommonConstants.WEEK_SECONDS);
        codeSession.setTenantId(mpConfigSimple.getTenantId());
        return codeSession;
    }

    /**
     * 微信用户数据解密
     * @param encryptedData
     * @param iv
     * @param openId
     * @return
     */
    @Override
    public DecryptUserDTO wxDataDecrypt(String encryptedData, String iv, String openId) {
        String sessionKey = redisService.getString(CacheKeys.MP_SESSION_KEY+openId);
        if(null == sessionKey) {
            throw new BusinessException(ErrorCode.UNAUTHORIZED,"解密数据失败,请重新登录小程序");
        }
        try {
            byte[] resultByte = WxDataDecryptUtil.decrypt(Base64.decodeBase64(encryptedData), Base64.decodeBase64(sessionKey), Base64.decodeBase64(iv),cipher);
            log.info("{}解密微信用户信息完成",openId);
            if(null != resultByte && resultByte.length > 0){
                String resultJson = new String(resultByte, "UTF-8");
                log.info("{}解密微信用户信息结果:{}",openId,resultJson);
                return JSON.parseObject(resultJson, DecryptUserDTO.class);
            }
        } catch (Exception e) {
            log.error("{}解密微信用户信息失败",openId,e);
        }
        log.info("{}解密微信用户信息结果为空",openId);
        throw new BusinessException(ErrorCode.BAD_REQUEST,"解密数据失败");
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
//        byte[] resultByte = WxDataDecryptUtil.decrypt(Base64.decodeBase64("gOBri3k7UzHWAC8i7InMmqKX7gQHbBRuFHLyqQSfFnslHlVxPeFe7Ln1xV4duwx8RlYMnPlRC3zkJYPh4H/KWFLuUrBdC6A0CyuS/xavzAv7KJcIWvk8Xtwj6KnKkDpkz080G8gtt3vGKcXzEapRWMXdEIhRPBS7K3qGjOPkkiJgyCFNs52o4DBlTZrbIzf74oNUe/xz0pAaK5xLwzlOgQ=="), Base64.decodeBase64("z1BsW8XiR3TddrgcRurlWw=="), Base64.decodeBase64("18WRpV8WysHmtKxBEeoZng=="));
//        if(null != resultByte && resultByte.length > 0) {
//            String resultJson = new String(resultByte, "UTF-8");
//            log.info("解密微信用户信息结果:{}", resultJson);
//        }
    }

}
