package com.dz.ms.basic.dto;
import com.dz.common.base.dto.BaseDTO;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.enums.ColumnType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 小程序页面模板DTO
 * @author: Handy
 * @date:   2022/02/04 22:59
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "小程序常规页面模板")
public class MiniappTemplateListDTO extends BaseDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "模板类型 1常规页面 2系统页面 3定制页面 4弹窗页面 5开屏页面")
    private Integer templateType;
    @ApiModelProperty(value = "页面类型 0默认 1首页 2会员中心 3会员码 4生活圈 5更多 6活动开屏 7首页开屏")
    private Integer pageType;
    @ApiModelProperty(value = "模板名称")
    private String templateName;
    @ApiModelProperty(value = "模板编号")
    private String templateCode;
    @ApiModelProperty(value = "是否发布 0否1是")
    private Integer publish;
    @ApiModelProperty(value = "更新时间")
    private Date modified;
    @ApiModelProperty(value = "发布时间")
    private Date pushTime;
    @ApiModelProperty(value = "页面组名称")
    private String groupName;
    @ApiModelProperty(value = "近30天浏览次数")
    private Integer numberView;
    @ApiModelProperty(value = "近30天访客人数")
    private Integer numberVisitors;
    @ApiModelProperty(value = "页面组ID")
    private Integer groupId;
    @ApiModelProperty(value = "页面地址")
    private String pagePath;
    @ApiModelProperty(value = "地址ID")
    private Long pathId;
}
