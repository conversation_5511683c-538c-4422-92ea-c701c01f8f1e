<template>
  <div class="outer" :class="[pageType==4?(addParams.pageSetting.modalPos==1?'center':'bottom'):'top']" :style="{background:pageType==4?addParams.pageSetting.modalMaskColor:'transparent'}">
    <div class="box" :style="{width:pageType==4?(addParams.pageSetting.pageWidth/2+'px'):'375px',
     height:pageType==4?(addParams.pageSetting.pageHeight/2+'px'):'800px',
     borderRadius:pageType==4?(addParams.pageSetting.modalRadius/2+'px'):'0'}">
      <div class="page">
        <div class="page-header" v-if="pageType!=4&&(navSetting.navType==1||navSetting.navType==4)"></div>
        <div class="page-content">
          <customBg :bgSetting="pageSetting" class="bgStyle"></customBg>
        </div>
      </div>
      <div class="page">
        <div class="page-info">
          <div class="page-header" v-if="pageType!=4" id="item-1" :class="currentIndex==-1?'active':''" :style="{position:(navSetting.navType==1||navSetting.navType==4)?'relative':'absolute'}" @click="changeIndex(-1)">
            <!-- navType 1-固定显示 2-完全沉浸  3-滑动恢复 4-固定回复-->
            <template v-if="navSetting.navType==4&&scrollTop>300">
              <customBg :bgSetting="navSetting.move" class="bgStyle" v-if="navSetting.move.bgColorStyle"></customBg>
              <img src="@/assets/images/black.png" class="page-header-status" v-if="navSetting.move.navColor==1">
              <img src="@/assets/images/white.png" class="page-header-status" v-else>
              <div class="page-header-title" :style="{textAlign:navSetting.move.titlePos==1?'center':'left',
      color:navSetting.move.titleColor}" v-if="navSetting.move.isTitle==1">{{pageSetting.templateName}}</div>
              <div class="page-header-title image" v-else-if="navSetting.move.isTitle==2">
                <img class="page-header-titleUrl" :src="navSetting.move.titleUrl" />
              </div>
              <view class="page-header-back" v-if="navSetting.move.back" :style="{color:navSetting.move.navColor==1?'#000':'#fff'}"></view>
            </template>
            <template v-else-if="navSetting.navType!=3||scrollTop>300">
              <customBg :bgSetting="navSetting" class="bgStyle" v-if="navSetting.bgColorStyle"></customBg>
              <img src="@/assets/images/black.png" class="page-header-status" v-if="navSetting.navColor==1">
              <img src="@/assets/images/white.png" class="page-header-status" v-else>
              <div class="page-header-title" :style="{textAlign:navSetting.titlePos==1?'center':'left',
      color:navSetting.titleColor}" v-if="navSetting.isTitle==1">{{pageSetting.templateName}}</div>
              <div class="page-header-title image" v-else-if="navSetting.isTitle==2">
                <img class="page-header-titleUrl" :src="navSetting.titleUrl" />
              </div>
              <view class="page-header-back" v-if="navSetting.back" :style="{color:navSetting.navColor==1?'#000':'#fff'}"></view>
            </template>
          </div>
          <div class="page-header modal" v-else-if="addParams.pageSetting.modalTitle">{{ addParams.pageSetting.templateName }}</div>
          <div class="page-content" @scroll="scrollContent">
            <div class="page-item" :id="'item'+ i" :class="index==i?'active':''" v-for="(item,i) in componentSetting" :key="item.id" @click="changeIndex(i)">
              <!-- 富文本 -->
              <rich v-if="item.type==1" :data="item" :width="pageType==4?addParams.pageSetting.pageWidth:750"></rich>
              <!-- 组件数据 -->
              <poster v-if="item.type==2" :data="item" :width="pageType==4?addParams.pageSetting.pageWidth:750"></poster>
              <!-- 视频组件 -->
              <myVideo v-if="item.type==3" :data="item" :width="pageType==4?addParams.pageSetting.pageWidth:750"></myVideo>
              <!-- 普通轮播组件 -->
              <normalSlider v-if="item.type==4" :data="item" :width="pageType==4?addParams.pageSetting.pageWidth:750"></normalSlider>
              <!-- 滚动组件 -->
              <scroll v-if="item.type==6" :data="item" :width="pageType==4?addParams.pageSetting.pageWidth:750"></scroll>
              <!-- 门店组件 -->
              <store v-if="item.type==7" :data="item" :width="pageType==4?addParams.pageSetting.pageWidth:750"></store>
              <!-- 任务组件 -->
              <task v-if="item.type==8" :data="item" :width="pageType==4?addParams.pageSetting.pageWidth:750"></task>
              <!-- 普通文本组件 -->
              <normalText v-if="item.type==9" :data="item" :width="pageType==4?addParams.pageSetting.pageWidth:750"></normalText>
              <!-- 普通文本组件 -->
              <division v-if="item.type==10" :data="item" :width="pageType==4?addParams.pageSetting.pageWidth:750"></division>
            </div>
          </div>
        </div>
      </div>
      <!-- 悬浮窗 -->
      <float :data="addParams.pageSetting.floatSetting" class="floatStyle"></float>
    </div>
  </div>

</template>
<script setup>
const emit = defineEmits(['ok', 'cancel', 'changeActive'])
import { cloneDeep } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import customBg from './customBg.vue'
import poster from './poster.vue'
import myVideo from './myVideo.vue'
import scroll from './scroll.vue'
import task from './task.vue'
import normalSlider from './normalSlider.vue'
import normalText from './normalText.vue'
import rich from './rich.vue'
import store from './store.vue'
import division from './division.vue'
import float from './float.vue'
import { computed } from 'vue';
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  // 当前激活下标
  index: {
    type: Number,
    default: 0
  },
  // 页面类型  1-常规页 2-加载页 3-开屏页 4-弹窗
  pageType: {
    type: String || Number,
    default: 1,
  },
  // 组件数组
  addParams: {
    type: Object,
    default() {
      return {
        navSetting: {},
        componentSetting: [],
        pageSetting: {},
      }
    }
  },
})
watch(() => props.addParams, (val) => {
  console.log(val)
  data.value = val
}, {
  deep: true
})

watch(() => props.index, (val) => {
  let time = setTimeout(() => {
    clearTimeout(time)
    time = null
    document.getElementById('item' + val).scrollIntoView({
      behavior: 'smooth', // 定义动画过渡效果， "auto"或 "smooth"之一。默认为"auto"。
      block: 'nearest',//定义垂直方向的对齐，"start","center","end", 或 "nearest"之一。默认为 “start”。
      inline: 'start',//定义水平方向的对齐， "start", "center","end", 或 "nearest"之一。默认为 “nearest”。
    })
  }, 500);

})

const changeIndex = (index) => {
  emit('changeActive', index)
}
const navSetting = computed(() => {
  return data.value.navSetting
})

const pageSetting = computed(() => {
  return data.value.pageSetting
})
const componentSetting = computed(() => {
  return data.value.componentSetting
})

const { activeKey, currentIndex, data, scrollTop } = toRefs(reactive({
  activeKey: [1],
  currentIndex: props.index,
  data: props.addParams,
  scrollTop: 0
}))

watch(() => props.index, (value) => {
  currentIndex.value = value
})

const scrollContent = (e) => {
  scrollTop.value = e.target.scrollTop
}

// 操作数组
const list = computed(() => {
  return props.components
})

const active = (i) => {
  emit('changeActive', i)
}
const ok = () => {
  currentIndex.value = -1
  emit('ok', list.value)
}

// 重命名
const reName = (i) => {
  currentIndex.value = i;
}


// 复制
const copy = (i) => {
  list.value.push({
    ...cloneDeep(list.value[i]),
    id: uuidv4(),
  })
  ok()
}

// 删除
const handleDelete = (i) => {
  list.value.splice(i, 1)
  ok()
}

</script>

<style scoped lang="scss">
.outer {
  width: 375px;
  height: 800px;
  display: flex;
  justify-content: center;
  &.top {
    align-items: flex-start;
  }
  &.center {
    align-items: center;
  }
  &.bottom {
    align-items: flex-end;
    .box {
      border-bottom-left-radius: 0 !important;
      border-bottom-right-radius: 0 !important;
    }
  }
}
.box {
  position: relative;
  overflow: hidden;
}
.floatStyle {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 100;
  pointer-events: none;
}
.page {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  &-info {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  &-header {
    width: 100%;
    height: 88px;
    position: relative;
    z-index: 10000;
    flex-shrink: 0;
    &.active {
      &::before {
        content: "";
        position: absolute;
        left: 0;
        bottom: 0;
        top: 0;
        right: 0;
        border: 3px solid #6a6bbf;
        pointer-events: none;
        z-index: 3;
      }
    }
    &-status {
      pointer-events: none;
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 88px;
    }
    &-title {
      position: absolute;
      left: 40px;
      right: 40px;
      bottom: 6px;
      top: 42px;
      line-height: 40px;
      &.image {
        left: 0;
        right: 0;
      }
    }
    &-titleUrl {
      display: block;
      height: 40px;
      width: auto;
    }
    &-back {
      position: absolute;
      top: 42px;
      left: 0;
      height: 40px;
      bottom: 6px;
      width: 50px;
      &::before {
        content: "";
        position: absolute;
        position: absolute;
        top: 13px;
        left: 18px;
        width: 12px;
        height: 12px;
        border-right: 3px solid;
        border-bottom: 3px solid;
        transform: rotate(135deg);
      }
    }
    &.modal {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 18px;
      box-sizing: border-box;
      padding: 30px 30px 10px 30px;
      line-height: 27px;
      letter-spacing: 1px;
      height: auto;
      color: #3c3c43;
    }
  }
  &-content {
    width: 100%;
    flex: 1;
    overflow-x: hidden;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    padding-top: 1px;
    &::-webkit-scrollbar {
      width: 0;
    }
  }
  &-item {
    position: relative;
    width: 100%;
    &.active {
      &::before {
        content: "";
        position: absolute;
        left: 0;
        bottom: 0;
        top: 0;
        right: 0;
        border: 3px solid #6a6bbf;
        pointer-events: none;
        z-index: 3;
      }
    }
  }
}
</style>
