package com.dz.ms.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.basic.entity.MaterialRelation;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 素材关联信息Mapper
 * @author: Handy
 * @date:   2023/05/10 14:31
 */
@Repository
public interface MaterialRelationMapper extends BaseMapper<MaterialRelation> {

    /** 批量插入素材关联信息  */
    int insertBatch(@Param("list") List<MaterialRelation> list);

}
