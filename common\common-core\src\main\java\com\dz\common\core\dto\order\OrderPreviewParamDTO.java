package com.dz.common.core.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 订单预览DTO
 *
 * @author: wuhaidong
 * @date: 2018/10/18 10:17
 */
@Data
public class OrderPreviewParamDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "跳转来源 0-立即购买 1-购物车")
    private Integer source;
    @ApiModelProperty(value = "商品集合")
    private List<OrderCreateProductDTO> productList;
}