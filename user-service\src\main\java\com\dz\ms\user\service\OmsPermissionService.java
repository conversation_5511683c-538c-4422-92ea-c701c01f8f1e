package com.dz.ms.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.ms.user.dto.OmsPermissionDTO;
import com.dz.ms.user.entity.OmsPermission;

import java.util.List;
import java.util.Map;

/**
 * OMS-权限功能接口
 * @author: Handy
 * @date:   2020/01/29 19:15
 */
public interface OmsPermissionService extends IService<OmsPermission> {

    /**
     * 根据权限编号查询权限
     * @param code
     * @return
     */
    public OmsPermission getPermissionByCode(String code);

    /**
     * 获取接口对应权限MAP
     * @return
     */
    Map<String, String> getPermissionUrlMap();

    /**
     * 保存权限功能
     * @param param
     * @return
     */
    Long saveOmsPermission(OmsPermissionDTO param);

    /**
     * 根据ID删除权限功能
     * @param id
     */
    void deleteOmsPermissionById(Long id);

    /**
     * 获取所有权限编码
     * @return
     */
    List<String> getAllPermissionCodes();

}