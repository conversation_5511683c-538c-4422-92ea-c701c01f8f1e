package com.dz.common.core.fegin.basic;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 下载中心
 */

@FeignClient(name = ServiceConstant.BASIC_SERVICE_NAME, contextId = "ReportDownloadFeignClient")
public interface ReportDownloadFeignClient {

    @PostMapping(value = "/download/task")
    Result<Void> downloadTask(@RequestBody ReportDownloadParamDTO reportDownloadParamDTO);

    @PostMapping(value = "/download/task/list")
    Result<PageInfo<ReportDownloadTaskDTO>> taskList(@RequestBody ReportDownloadTaskParamDTO reportDownloadTaskParamDTO);

    @GetMapping(value = "/download/task/category")
    Result<List<ReportDownloadTypeDTO>> category();

    @PostMapping(value = "/download/task/delete")
    Result<Void> delete(@RequestBody ReportDownloadTaskDeleteParamDTO paramDTO);

    @PostMapping(value = "/download/task/mark")
    Result<Void> mark(@RequestBody ReportDownloadTaskDeleteParamDTO paramDTO);

    /**
     * 下载作废
     *
     * @return
     */
    @GetMapping(value = "/download/task/scarp")
    Result<Void> scarp();

    @GetMapping(value = "/download/task/getHeader")
    Result<List<DownloadHeaderDTO>> getHeader(@RequestParam(value = "reportCode") String reportCode, @RequestParam(value = "tenantId") Long tenantId);

    /**
     * OSS文件方式上传
     *
     * @param ossDTO oss上传参数
     * @return String
     */
    @PostMapping(value = "/download/uploadOss")
    String uploadOss(@RequestBody OssDTO ossDTO);

    /**
     * OSS文件删除
     *
     * @param url oss文件路径
     */
    @DeleteMapping(value = "/download/delOSS")
    void delOSS(@RequestParam(value = "url") String url);

    @PostMapping(value = "/download/updateDownloadCenter")
    void updateDownloadCenter(@RequestBody DownloadCenterDTO downloadCenterDTO);
}
