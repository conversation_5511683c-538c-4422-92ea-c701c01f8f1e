package com.dz.ms.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.adaptor.SftpDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.common.core.utils.SftpUtils;
import com.dz.ms.order.dto.OrderDetailDTO;
import com.dz.ms.order.entity.OdsOrderProduct;
import com.dz.ms.order.entity.OrderDetail;
import com.dz.ms.order.mapper.OrderDetailMapper;
import com.dz.ms.order.service.OrderDetailService;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 订单详情信息
 *
 * @author: LiinNs
 * @date: 2024/12/09 10:38
 */
@Service
public class OrderDetailServiceImpl extends ServiceImpl<OrderDetailMapper, OrderDetail> implements OrderDetailService {

    @Resource
    private OrderDetailMapper orderDetailMapper;
    @Value("${sftp.config.host}")
    private String host;
    @Value("${sftp.config.port}")
    private Integer port;
    @Value("${sftp.config.userName}")
    private String userName;
    @Value("${sftp.config.password}")
    private String password;

    /**
     * 分页查询订单详情信息
     *
     * @param param
     * @return PageInfo<OrderDetailDTO>
     */
    @Override
    public PageInfo<OrderDetailDTO> getOrderDetailList(OrderDetailDTO param) {
        OrderDetail orderDetail = BeanCopierUtils.convertObjectTrim(param, OrderDetail.class);
        IPage<OrderDetail> page = orderDetailMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), new QueryWrapper<>(orderDetail));
        return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), BeanCopierUtils.convertList(page.getRecords(), OrderDetailDTO.class));
    }

    /**
     * 根据ID查询订单详情信息
     *
     * @param id
     * @return OrderDetailDTO
     */
    @Override
    public OrderDetailDTO getOrderDetailById(Long id) {
        OrderDetail orderDetail = orderDetailMapper.selectById(id);
        return BeanCopierUtils.convertObject(orderDetail, OrderDetailDTO.class);
    }

    /**
     * 保存订单详情信息
     *
     * @param param
     * @return Long
     */
    @Override
    public Long saveOrderDetail(OrderDetailDTO param) {
        OrderDetail orderDetail = new OrderDetail(param.getId(), param.getOrderCode(), param.getProductId(), param.getProductName(), param.getPdType(), param.getVenderId(), param.getImgUrl(), param.getCostPoint(), param.getCostPrice(), param.getPCostPoint(), param.getPCostPrice(), param.getShelfProductId(), param.getShelfId(), param.getSCostPoint(), param.getSPrePoint(), param.getCampaignId(), param.getRuleId(), param.getRCostPoint(), param.getRPrePoint(), param.getNumber(), param.getRealPoint(), param.getRealAmount(), param.getDeliveryNumber(), param.getStatus(), param.getSendStatus());
        if (ParamUtils.isNullOr0Long(orderDetail.getId())) {
            orderDetailMapper.insert(orderDetail);
        } else {
            orderDetailMapper.updateById(orderDetail);
        }
        return orderDetail.getId();
    }

    /**
     * 根据ID删除订单详情信息
     *
     * @param param
     */
    @Override
    public void deleteOrderDetailById(IdCodeDTO param) {
        orderDetailMapper.deleteById(param.getId());
    }

    @Override
    public void getSftpFile() throws IOException {
//
        Date date = new Date();
        // 指定日期格式
        LocalDate yesterday = LocalDate.now().minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String formattedDate = yesterday.format(formatter);
        String fileName = "order_" + formattedDate + ".csv";
        SftpDTO sftpDTO = new SftpDTO();
        sftpDTO.setFilePath("/order");
        //sftpDTO.setFileName("order_20250101.csv");
        sftpDTO.setFileName(fileName);
        sftpDTO.setHost(host);
        sftpDTO.setPort(port);
        sftpDTO.setUserName(userName);
        sftpDTO.setPass(password);
        SftpDTO sftpFile = SftpUtils.getSftpFile(sftpDTO);
        //获取文件
        if (sftpFile != null) {
            dealSftpOrderData(sftpFile.getIn());
            sftpFile.getIn().close();
            SftpUtils.sessionClose();
            SftpUtils.sftpClose(sftpFile.getSftp());
        }
    }

    @Override
    public void removeOrder() {
        SecurityContext.setUser(new CurrentUserDTO(1L, 999L));
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 计算九十天前的时间
        LocalDateTime ninetyDaysAgoLocalDateTime = now.minusDays(90);
        // 将 LocalDateTime 转换为 Date
        Date ninetyDaysAgo = Date.from(ninetyDaysAgoLocalDateTime.atZone(ZoneId.systemDefault()).toInstant());

        //int batchSize = 1000; // 每批处理1000条记录
        for (int i = 0; i < 64; i++) {
            String tableName = "ods_order_product_" + i;

            // 调用Mapper接口方法
            orderDetailMapper.removeOrder(tableName, ninetyDaysAgo);
        }
    }


    private void dealSftpOrderData(InputStream in) {
        Date date = new Date();
        List<OdsOrderProduct> odsOrderProductList = new ArrayList<>();
        int batchSize = 1000;

        try (BufferedReader fileReader = new BufferedReader(new InputStreamReader(in, StandardCharsets.UTF_8))) {

            CSVFormat csvFormat = CSVFormat.RFC4180.builder()
                    .setHeader()                          // 自动识别首行为 Header
                    .setSkipHeaderRecord(true)            // 跳过 Header 行
                    .setIgnoreHeaderCase(true)            // 忽略 Header 大小写
                    .setTrim(true)                        // 自动去除字段两端的空格
                    .build();

            try (CSVParser csvParser = new CSVParser(fileReader, csvFormat)) {
                for (CSVRecord csvRecord : csvParser) {
                    // 从 CSVRecord 中按列名获取值（不区分大小写）
                    String checkoutDtStr = csvRecord.get("checkout_dt_str");
                    String storeSn = csvRecord.get("shop_id_str");
                    String storeName = csvRecord.get("shop_name_str");
                    String bizDate = csvRecord.get("biz_date");
                    String couponnameStr = csvRecord.get("couponname_str");
                    String itemIdStr = csvRecord.get("item_id_str");
                    String itemNameStr = csvRecord.get("item_name_str");
                    String qtyStr = csvRecord.get("qty_str");
                    String saleidFrStr = csvRecord.get("saleid_fr_str");
                    String saleAmtStr = csvRecord.get("sale_amt_str");
                    String realAmtStr = csvRecord.get("real_amt_str");
                    String promname1Str = csvRecord.get("promname1_str");
                    String promname2Str = csvRecord.get("promname2_str");
                    String promname5Str = csvRecord.get("promname5_str");
                    String promdisnameStr = csvRecord.get("promdisname_str");
                    String upSerialNoStr = csvRecord.get("up_serial_no_str");
                    String totalAmtStr = csvRecord.get("total_amt_str");
                    String totalQtyStr = csvRecord.get("total_qty_str");
                    String saleTyStr = csvRecord.get("sale_ty_str");
                    String memberCode = csvRecord.get("member_code_str");
                    String saleIdStr = csvRecord.get("sale_id_str");
                    String posIdStr = csvRecord.get("pos_id_str");
                    String staffIdStr = csvRecord.get("staff_id_str");
                    // ... 其他字段获取方式相同 ...

                    OdsOrderProduct odsOrderProduct = new OdsOrderProduct();
                    odsOrderProduct.setId(UUID.randomUUID().toString());
                    odsOrderProduct.setBizDate(bizDate);
                    odsOrderProduct.setCheckoutDtStr(checkoutDtStr);
                    odsOrderProduct.setCouponnameStr(couponnameStr);
                    odsOrderProduct.setItemIdStr(itemIdStr);
                    odsOrderProduct.setItemNameStr(itemNameStr);
                    odsOrderProduct.setQtyStr(qtyStr);
                    odsOrderProduct.setSaleAmtStr(saleAmtStr);
                    odsOrderProduct.setRealAmtStr(realAmtStr);
                    odsOrderProduct.setPromname1Str(promname1Str);
                    odsOrderProduct.setPromname2Str(promname2Str);
                    odsOrderProduct.setPromname5Str(promname5Str);
                    odsOrderProduct.setPromdisnameStr(promdisnameStr);
                    odsOrderProduct.setUpSerialNoStr(upSerialNoStr);
                    odsOrderProduct.setTotalAmtStr(totalAmtStr);
                    odsOrderProduct.setTotalQtyStr(totalQtyStr);
                    odsOrderProduct.setSaleTyStr(saleTyStr);
                    odsOrderProduct.setMemberCode(memberCode);
                    odsOrderProduct.setSaleIdStr(saleIdStr);
                    odsOrderProduct.setPosIdStr(posIdStr);
                    odsOrderProduct.setStaffIdStr(staffIdStr);
                    odsOrderProduct.setShopIdStr(storeSn);
                    odsOrderProduct.setShopNameStr(storeName);
                    odsOrderProduct.setSaleidFrStr(saleidFrStr);
                    odsOrderProduct.setCreateTime(new Date());
                    odsOrderProduct.setTenantId(1L);
                    // ... 其他字段赋值 ...

                    odsOrderProductList.add(odsOrderProduct);

                    // 批量插入逻辑
                    if (odsOrderProductList.size() >= batchSize) {
                        orderDetailMapper.insertBatchSomeColumn(odsOrderProductList);
                        odsOrderProductList.clear();
                    }
                }

                // 插入剩余数据
                if (!CollectionUtils.isEmpty(odsOrderProductList)) {
                    orderDetailMapper.insertBatchSomeColumn(odsOrderProductList);
                }
            } catch (IllegalArgumentException e) {
                log.error("CSV 列名不匹配，请检查文件格式", e);
            } catch (IOException e) {
                log.error("CSV 解析异常", e);
            }
        } catch (IOException e) {
            log.error("文件读取失败", e);
        }

    }

}