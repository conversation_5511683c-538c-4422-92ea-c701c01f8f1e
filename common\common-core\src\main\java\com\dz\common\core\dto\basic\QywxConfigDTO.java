package com.dz.common.core.dto.basic;

import com.dz.common.base.dto.BaseDTO;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.enums.ColumnType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 企业微信配置DTO
 * @author: Handy
 * @date:   2022/07/20 21:18
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "企业微信配置")
public class QywxConfigDTO extends BaseDTO {

    @ApiModelProperty(value = "企业微信配置ID")
    private Long id;
    @ApiModelProperty(value = "企业微信ID")
    private String corpId;
    @ApiModelProperty(value = "企业微信应用ID")
    private String agentId;
    @ApiModelProperty("企业微信应用关联小程序APPID")
    private String appid;
    @ApiModelProperty(value = "企业微信应用密钥")
    private String corpSecret;
    @ApiModelProperty(value = "接收事件服务器Token")
    private String token;
    @ApiModelProperty(value = "接收事件服务器EncodingAESKey")
    private String encodingAesKey;
    @ApiModelProperty(value = "1-企微应用 2-客户联系")
    private Integer type;
    @ApiModelProperty(value = "状态 0关闭 1启用")
    private Integer state;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;

}
