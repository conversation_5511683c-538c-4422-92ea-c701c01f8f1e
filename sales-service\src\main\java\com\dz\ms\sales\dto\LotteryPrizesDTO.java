package com.dz.ms.sales.dto;

import com.dz.common.core.annotation.Columns;
import com.dz.common.core.enums.ColumnType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/5/21
 */
@Data
public class LotteryPrizesDTO {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("活动标识")
    private String campaignCode;

//    @ApiModelProperty("unionid")
//    private String unionid;

    @ApiModelProperty("奖品名称")
    private String prizesName;

    @ApiModelProperty("奖品类型 0空 1积分 2券")
    private Integer prizesType;

    @ApiModelProperty("奖品等级")
    private String prizesLevel;

    @ApiModelProperty("绑定奖品id")
    private Long prizesId;

    @ApiModelProperty("抽奖描述信息")
    private String contentJson;

    @ApiModelProperty("积分数量")
    private Integer pointsNum;

    @ApiModelProperty("券code")
    private String couponCode;

    @ApiModelProperty("外部卡券ID")
    private String outCouponId;


    @ApiModelProperty("奖品级别编码")
    private Integer prizesLevelCode;

    @ApiModelProperty("列表图片")
    private String imageUrl;

    @ApiModelProperty("详情图片")
    private String detailImageUrl;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty(value = "创建时间")
    private Date created;

}
