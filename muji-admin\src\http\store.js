// 所有的命名必须全局唯一
import service from '@/utils/request.js'


// 列表
export function storeList(data) {
    return service({
        url: '/crm/user/store/list',
        method: 'post',
        data
    })
}


// 服务列表

export function serveList(data) {
    return service({
        url: '/crm/user/serve/list',
        method: 'get',
        data
    })
}
//服务保存

export function serveSave(data) {
    return service({
        url: '/crm/user/serve/save',
        method: 'post',
        data
    })
}
//服务删除
export function serveDelete(data) {
    return service({
        url: '/crm/user/serve/delete',
        method: 'post',
        data
    })
}

// 排序保存

export function serveSort(data) {
    return service({
        url: '/crm/user/serve/sort',
        method: 'post',
        data
    })
}

// 服务查询

export function checkstoreServe(data) {
    return service({
        url: '/crm/user/storeServe/list',
        method: 'get',
        data
    })
}

// 保存服务列表 
export function updateServe(data) {
    return service({
        url: '/crm/user/store/updateServe',
        method: 'post',
        data
    })
}
// 门店编辑

export function updatestore(data) {
    return service({
        url: '/crm/user/store/update',
        method: 'post',
        data
    })
}
//门店服务导入

export function storeServeimport(data) {
    return service({
        url: '/crm/user/storeServe/import',
        method: 'post',
        headers: { 'Content-Type': 'multipart/form-data' },
        data
    })
}
// 门店服务详情
export function userStoreInfo(data) {
    return service({
        url: '/crm/user/store/info',
        method: 'get',
        data
    })
}
//门店服务模版下载


export function storetemplate(data) {
    return service({
        url: '/crm/user/store/template',
        method: 'get',
        responseType: 'blob',
        data
    })
}
// 省份下拉

export function storeProvince(data) {
    return service({
        url: '/crm/user/store/province',
        method: 'get',
        data
    })
}
//城市下拉

export function storeCity(data) {
    return service({
        url: '/crm/user/store/city',
        method: 'get',
        data
    })
}