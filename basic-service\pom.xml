<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.dz.ms</groupId>
    <artifactId>basic-service</artifactId>
    <version>1.1.6</version> <!-- Updated version -->

    <parent>
        <groupId>com.dz.common</groupId>
        <artifactId>common</artifactId>
        <version>1.1.6</version> <!-- Ensuring the latest version of common -->
    </parent>

    <dependencies>
        <!-- Common Core Dependency -->
        <dependency>
            <groupId>com.dz.common</groupId>
            <artifactId>common-core</artifactId>
            <version>1.1.6</version> <!-- Ensure it is the latest version -->
        </dependency>
        <!--解决Unable to start embedded Tomcat问题 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <version>2.2.220</version>
        </dependency>
        <!-- Updated Freemarker to the latest stable version -->
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.32</version> <!-- Upgraded version -->
        </dependency>

        <!-- Qiniu SDK update -->
        <dependency>
            <groupId>com.qiniu</groupId>
            <artifactId>qiniu-java-sdk</artifactId>
            <version>7.13.0</version> <!-- Upgraded version -->
        </dependency>

        <!-- Tencent COS SDK -->
        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
            <version>5.6.27</version> <!-- Upgraded version -->
        </dependency>

        <!-- Apache Commons Codec -->
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.16.0</version> <!-- Upgraded version -->
        </dependency>

        <!-- Tencent COS STS SDK -->
<!--        <dependency>-->
<!--            <groupId>com.tencent.cloud</groupId>-->
<!--            <artifactId>cos-sts-java</artifactId>-->
<!--            <version>3.0.8</version>-->
<!--        </dependency>-->

        <!-- Azure Data Lake SDK -->
        <dependency>
            <groupId>com.microsoft.azure</groupId>
            <artifactId>azure-data-lake-store-sdk</artifactId>
            <version>2.3.8</version>
        </dependency>

        <!-- Azure Blob Storage SDK -->
        <dependency>
            <groupId>com.microsoft.azure</groupId>
            <artifactId>azure-storage-blob</artifactId>
            <version>11.0.1</version> <!-- Upgraded version -->
        </dependency>
        <dependency>
            <groupId>org.codehaus.jettison</groupId>
            <artifactId>jettison</artifactId>
            <version>1.5.4</version>
        </dependency>
        <!-- Azure Storage SDK -->
        <dependency>
            <groupId>com.microsoft.azure</groupId>
            <artifactId>azure-storage</artifactId>
            <version>8.6.6</version> <!-- Upgraded version -->
        </dependency>

        <!-- Spring Boot Starter for Mail -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <!-- Aliyun OSS SDK -->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.16.2</version> <!-- Upgraded version -->
        </dependency>

        <!-- Aliyun Java SDK Core -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>4.6.3</version> <!-- Upgraded version -->
        </dependency>

        <!-- Aliyun Java SDK Green -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-green</artifactId>
            <version>3.6.6</version> <!-- Upgraded version -->
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <!-- Spring Boot Maven Plugin -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
