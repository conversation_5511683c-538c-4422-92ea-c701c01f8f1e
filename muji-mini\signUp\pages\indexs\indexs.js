// signUp/pages/indexs/indexs.js
const util = require('../../../utils/util.js')
const app = getApp()
import {
  getCampaignType,
} from '../../api/index.js'
import {
  enrollSave,
} from '../../api/index.js'
const requiredFields = ['name', 'skinType', 'enrollPhone', 'province', 'city', 'district', 'address', 'username', ]
// 'agree'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    showModal: true,
    skinTypeList: [{
        label: '皮肤敏感（泛红、刺痛）',
        value: 1
      },
      {
        label: '干燥缺水',
        value: 2
      },
      {
        label: '痘痘/闭口',
        value: 3
      },
      {
        label: '毛孔粗大',
        value: 4
      },
      {
        label: '细纹/初老',
        value: 5
      }
    ],
    isComplate: false,
    loading: false,
    info: {
      name: '',
      enrollPhone: '',
      province: '',
      city: '',
      district: '',
      address: '',
      skinType: '', // 皮肤类型 0未知 1皮肤敏感（泛红、刺痛） 2干燥缺水 3、痘痘/粉刺问题 4、油脂分泌过多 5、黑头问题,
      skinTypeIndex: '',
      campaignCode: wx.getStorageSync('campaignCode'),
      channelTwo: app.globalData.channelTwo,
      channelOne: app.globalData.channelOne,
      agree: false,
      username: ''
    },
    showPopup: false,
    infoLottery: {},
  },
  // 分享
  goShare(e) {
    let {
      shareTitle,
      shareImg,
      sharePath
    } = e.detail
    this.data.shareInfo = {
      shareTitle,
      shareImg,
      sharePath
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log(options, 'options 11111111111111');
    console.log(app.globalData, 'app.globalData.channelOne 2222222222222222222');
    console.log(util, 'util 333333333333');
    this.getData()
  },
  getData() {
    getCampaignType().then(res => {
      wx.setStorageSync('campaignCode', res.data.campaignCode)
      this.setData({
        CampaignData: res.data,
        "info.campaignCode": res.data.campaignCode
      })
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },
  onTapPrivacyCheck() {
    this.setData({
      "info.agree": !this.data.info.agree
    })
    this.validateForm()
  },
  go() {
    this.openPopup()
    console.log("《MUJI隐私协议》");
  },
  changeName(e) {
    console.log("changeName", e);
    this.setData({
      ['info.name']: e.detail.value,
    })
    this.validateForm()
  },
  changeuserName(e) {
    console.log("changeName", e);
    this.setData({
      ['info.username']: e.detail.value,
    })
    this.validateForm()
  },
  // 选择肤质
  bindPickerChange(e) {
    console.log('picker发送选择改变，携带值为', e)

    this.setData({
      ['info.skinType']: this.data.skinTypeList[e.detail.value].value,
      ['info.skinTypeIndex']: e.detail.value,

    })
    this.validateForm()
  },
  // 一键获取微信地址
  getAddress() {
    console.log('点击一键获取微信地址 1111111111111 ************************');
    let that = this
    wx.getSetting({
      success: (res) => {
        console.log(res, '是否授权');
        if (res.authSetting['scope.address']) {
          // 已经授权，可以直接调用 wx.chooseAddress 接口
          wx.chooseAddress({
            success: (res) => {
              console.log(res, '微信地址获取');
              that.setData({
                ['info.enrollPhone']: res.telNumber,
                ['info.province']: res.provinceName,
                ['info.city']: res.cityName,
                ['info.district']: res.countyName,
                ['info.address']: res.detailInfo,
                ['info.name']: res.userName,
              })
              this.validateForm()
              this.phone(res.telNumber)
            }
          })
        } else {
          // 未授权，可以先调用 wx.authorize 接口
          wx.authorize({
            scope: 'scope.address',
            success: () => {
              // 用户已经同意小程序使用地址信息，可以直接调用 wx.chooseAddress 接口
              that.setData({
                ['info.enrollPhone']: res.telNumber,
                ['info.province']: res.provinceName,
                ['info.city']: res.cityName,
                ['info.district']: res.countyName,
                ['info.address']: res.detailInfo,
                ['info.name']: res.userName,
              })
              this.validateForm()
            }
          })
        }
      }
    })
  },
  // 收货人电话
  changeenrollPhone(e) {
    var that = this;
    var enrollPhone = e.detail.value;
    if (enrollPhone && !(/^1[3456789]\d{9}$/.test(enrollPhone))) {
      this.phone(enrollPhone)
    } else {
      this.setData({
        ['info.enrollPhone']: enrollPhone,
      })
      this.validateForm()
    }
  },
  phone(phone) {
    if (phone && !(/^1[3456789]\d{9}$/.test(phone))) {
      wx.showToast({
        title: '手机号码格式不正确，请重新填写！',
        icon: 'none',
        duration: 2000
      })
      this.setData({
        ['info.enrollPhone']: "",
      })
    }
  },
  // 选择区域
  regionChange(e) {
    const {
      value
    } = e.detail;
    console.log('value', value);
    this.setData({
      ['info.province']: value[0],
      ['info.city']: value[1],
      ['info.district']: value[2],
    })
    this.validateForm()
  },
  changeaddress(e) {
    this.setData({
      ['info.address']: e.detail.value,
    })
    this.validateForm()
  },
  // 校验表单里的必填字段是否都填写
  validateForm() {
    const {
      info
    } = this.data;
    const allFilled = requiredFields.every(field => info[field]);
    console.log(info, 'info:');
    console.log(allFilled, 'allFilled:');
    this.setData({
      isComplate: allFilled,
    })
  },
  // 提交报名信息
  submit: app.debounce(async function () {
    wx.$mp.track({
      event: 'recruit_Apply_click',
    })
    this.openPopup()
    // enrollSave(this.data.info).then(res => {
    //   console.log(res.success);
    //   if (res.success) {
    //     wx.showToast({
    //       title: '报名成功',
    //       icon: 'success',
    //     });
    //     this.setData({
    //       loading: false
    //     })
    //     wx.redirectTo({
    //       url: `/signUp/pages/registrationSuccessful/registrationSuccessful`,
    //     })
    //   }
    // })
  }),
  openPopup() {
    this.setData({
      showPopup: true
    })
  },
  closePopup: app.debounce(async function (e) {
    this.setData({
      showPopup: false,
      loading: true

    })
    let isclick = e.detail
    console.log(isclick, 'isclick 555555');
    if (isclick) {
      // this.setData({
      //   ['info.agree']: true,
      // })
      this.validateForm()
      console.log(this.data.info, '报名信息');
      enrollSave(this.data.info).then(res => {
        console.log(res.success);
        if (res.success) {
          wx.showToast({
            title: '报名成功',
            icon: 'success',
          });
          this.setData({
            loading: false
          })
          wx.redirectTo({
            url: `/signUp/pages/registrationSuccessful/registrationSuccessful`,
          })
        }
      })
    }

    // wx.navigateTo({
    //   url: `/signUp/pages/Winning/Winning`,
    // })
  }),
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    // 页面分享
    return {
      title: '和我一起体验「极简护肤」开启七天打卡活动',
      imageUrl: wx.$config.ossImg + '/signUp/share.png',
      path: '/signUp/pages/signUp/signUp',
    }
  }
})
