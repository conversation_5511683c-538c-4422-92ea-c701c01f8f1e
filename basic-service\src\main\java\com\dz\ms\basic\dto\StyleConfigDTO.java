package com.dz.ms.basic.dto;

import com.dz.common.core.dto.basic.TenantConfigDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "小程序风格配置")
public class StyleConfigDTO {
    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "导航配置内容json")
    private String content;
    @ApiModelProperty(value = "类型")
    private Integer styleType;
}
