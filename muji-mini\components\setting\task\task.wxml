<view class="WidgetHomeTaskListWrap" style="width:{{width}}rpx;padding-left:{{data.paddingLeft}}rpx;padding-right:{{data.paddingRight}}rpx;padding-top:{{data.paddingTop}}rpx;padding-bottom:{{data.paddingBottom}}rpx;background:{{data.bgColor}};box-sizing:border-box;">
  <WidgetHomeTaskList item="{{data}}" visitor="{{data.visitor&&visitor}}"/>
  <view class="WidgetHomeTaskListWrap-visitor" wx:if="{{data.visitor&&visitor}}"  catchtap="showPrincy"></view>
</view>