package com.dz.common.core.config;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.BlockAttackInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.DynamicTableNameInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.threadlocal.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;

/**
 * Mybatis-plus配置
 * @author: Handy
 * @date:   2022/6/22 17:14
 */
@Slf4j
public class MybatisPlusConfig {

    @Value("${mybatis-plus.notenant.tables:}")
    private String[] noTenantTables;

    private static List<String> noTenantTablesList = null;

    private static boolean initNoTenantTables = false;

    /**
     * 新多租户插件配置,一缓和二缓遵循mybatis的规则,需要设置 MybatisConfiguration#useDeprecatedExecutor = false 避免缓存万一出现问题
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        /** 按租户ID分表 *//*
        DynamicTableNameInnerInterceptor dynamicTableNameInnerInterceptor = new DynamicTableNameInnerInterceptor();
        dynamicTableNameInnerInterceptor.setTableNameHandler((sql, tableName) -> {
            if(!initNoTenantTables) {
                if(null != noTenantTables && noTenantTables.length > 0) {
                    noTenantTablesList = Arrays.asList(noTenantTables);
                }
                initNoTenantTables = true;
            }
            if(null != noTenantTablesList && noTenantTablesList.contains(tableName)) {
                return tableName;
            }
            CurrentUserDTO currentUser = SecurityContext.getUser();
            if(null != currentUser && null != currentUser.getTenantId()) {
                //return tableName;
                return tableName + "_" + currentUser.getTenantId();
            }
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT,"未获取到品牌ID2");
        });
        interceptor.addInnerInterceptor(dynamicTableNameInnerInterceptor);*/
        /** 填充租户ID */
        interceptor.addInnerInterceptor(new TenantLineInnerInterceptor(new TenantLineHandler() {
            @Override
            public Expression getTenantId() {
                CurrentUserDTO currentUser = SecurityContext.getUser();
                if(null != currentUser && null != currentUser.getTenantId() ) {
                    return new LongValue(currentUser.getTenantId());
                }
                throw new BusinessException(ErrorCode.INVALID_ARGUMENT,"未获取到品牌ID1");
            }

            // 这是 default 方法,默认返回 false 表示所有表都需要拼多租户条件
            @Override
            public boolean ignoreTable(String tableName) {
                if(!initNoTenantTables) {
                    if(null != noTenantTables && noTenantTables.length > 0) {
                        noTenantTablesList = Arrays.asList(noTenantTables);
                    }
                    initNoTenantTables = true;
                }
                if(null == noTenantTablesList) {
                    return false;
                }
                return noTenantTablesList.contains(tableName);
            }
        }));
        /** 分页插件 如果用了分页插件注意先 add TenantLineInnerInterceptor 再 add PaginationInnerInterceptor
        用了分页插件必须设置 MybatisConfiguration#useDeprecatedExecutor = false */
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        /** 防止全表更新删除 */
        interceptor.addInnerInterceptor(new BlockAttackInnerInterceptor());
        return interceptor;
    }

    @Bean
    public NormalSqlInjector normalSqlInjector() {
        return new NormalSqlInjector();
    }

}
