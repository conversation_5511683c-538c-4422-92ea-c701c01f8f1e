package com.dz.common.core.dto.user;
import java.util.List;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 权限功能DTO
 * @author: Handy
 * @date:   2022/2/4 19:14
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "权限功能")
public class SysPermissionDTO extends BaseDTO {

    @ApiModelProperty(value = "权限ID")
    private Long id;
    @ApiModelProperty(value = "权限编号")
    private String code;
    @ApiModelProperty(value = "父节点ID")
    private Long parentId;
    @ApiModelProperty(value = "权限类型 1模块 2页面 3功能")
    private Integer permitType;
    @ApiModelProperty(value = "权限名称")
    private String permitName;
    @ApiModelProperty(value = "页面/接口地址")
    private String url;
    @ApiModelProperty(value = "权限描述")
    private String permitDesc;
    @ApiModelProperty(value = "菜单显示排序")
    private Integer displaySort;
    @ApiModelProperty(value = "是否有子权限")
    private Integer hasChild;
    @ApiModelProperty(value = "平台类型 1会小 2企微 3商城")
    private Integer platform;

    @ApiModelProperty(value = "路由地址")
    private String path;
    @ApiModelProperty(value = "组件路径")
    private String component;
    @ApiModelProperty(value = "是否为外链（0是 1否）")
    private Integer isFrame;
    @ApiModelProperty(value = "类型（M目录 C菜单 F按钮）")
    private String menuType;
    @ApiModelProperty(value = "显示状态（0显示 1隐藏）")
    private String visible;
    @ApiModelProperty(value = "菜单状态（0显示 1隐藏）")
    private String status;
    @ApiModelProperty(value = "菜单图标")
    private String icon;
    @ApiModelProperty(value = "菜单类型 1:小程序 2:PC")
    private Integer type;
    @ApiModelProperty(value = "子权限列表")
    private List<SysPermissionDTO> subList;

}
