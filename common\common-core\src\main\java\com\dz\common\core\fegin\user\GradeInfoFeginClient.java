package com.dz.common.core.fegin.user;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.user.GradeListDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ServiceConstant.USER_SERVICE_NAME, contextId = "GradeInfoFeginClient")
public interface GradeInfoFeginClient {

    @ApiOperation("获取会员等级MAP")
    @GetMapping(value = "/grade_info/code_name_map")
    public Result<List<GradeListDTO>> getCodeToNameGradeMap(@RequestParam("tenantId")Long tenantId);
}
