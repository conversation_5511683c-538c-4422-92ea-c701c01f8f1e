package com.dz.ms.order.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.order.dto.BookingRecordDTO;
import com.dz.ms.order.service.BookingRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;

@Api(tags="预约记录")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class BookingRecordController  {

    @Resource
    private BookingRecordService bookingRecordService;

    /**
     * 分页查询预约记录
     * @param param
     * @return result<PageInfo<BookingRecordDTO>>
     */
    @ApiOperation("分页查询预约记录")
	@GetMapping(value = "/booking_record/list")
    public Result<PageInfo<BookingRecordDTO>> getBookingRecordList(@ModelAttribute BookingRecordDTO param) {
        Result<PageInfo<BookingRecordDTO>> result = new Result<>();
		PageInfo<BookingRecordDTO> page = bookingRecordService.getBookingRecordList(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询预约记录
     * @param id
     * @return result<BookingRecordDTO>
     */
    @ApiOperation("根据ID查询预约记录")
	@GetMapping(value = "/booking_record/info")
    public Result<BookingRecordDTO> getBookingRecordById(@RequestParam("id") Long id) {
        Result<BookingRecordDTO> result = new Result<>();
        BookingRecordDTO bookingRecord = bookingRecordService.getBookingRecordById(id);
        result.setData(bookingRecord);
        return result;
    }

    /**
     * 新增预约记录
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增预约记录",type = LogType.OPERATELOG)
    @ApiOperation("新增预约记录")
	@PostMapping(value = "/booking_record/add")
    public Result<Long> addBookingRecord(@RequestBody BookingRecordDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,true);
        Long id = bookingRecordService.saveBookingRecord(param);
        result.setData(id);
        return result;
    }

    /**
    * 更新预约记录
    * @param param
    * @return result<Object>
    */
    @SysLog(value = "更新预约记录",type = LogType.OPERATELOG)
    @ApiOperation("更新预约记录")
    @PostMapping(value = "/booking_record/update")
    public Result<Long> updateBookingRecord(@RequestBody BookingRecordDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,false);
        bookingRecordService.saveBookingRecord(param);
        result.setData(param.getId());
        return result;
    }

    /**
    * 验证保存参数
    * @param param
    */
    private void validationSaveParam(BookingRecordDTO param,boolean isAdd) {
        if(isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if(!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
    }

	/**
     * 根据ID删除预约记录
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除预约记录")
	@PostMapping(value = "/booking_record/delete")
    public Result<Boolean> deleteBookingRecordById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        bookingRecordService.deleteBookingRecordById(param);
        result.setData(true);
        return result;
    }

}
