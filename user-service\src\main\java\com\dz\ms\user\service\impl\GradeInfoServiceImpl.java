package com.dz.ms.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.core.annotation.Cacheable;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.IsEnableDTO;
import com.dz.common.core.dto.user.GradeListDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.user.constants.CacheKeys;
import com.dz.ms.user.dto.BenefitInfoDTO;
import com.dz.ms.user.dto.GradeInfoDTO;
import com.dz.ms.user.entity.BenefitInfo;
import com.dz.ms.user.entity.GradeBenefit;
import com.dz.ms.user.entity.GradeInfo;
import com.dz.ms.user.mapper.BenefitInfoMapper;
import com.dz.ms.user.mapper.GradeBenefitMapper;
import com.dz.ms.user.mapper.GradeInfoMapper;
import com.dz.ms.user.service.GradeInfoService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会员等级
 * @author: Handy
 * @date:   2023/08/07 17:44
 */
@Service
@Slf4j
public class GradeInfoServiceImpl extends ServiceImpl<GradeInfoMapper, GradeInfo> implements GradeInfoService {

	@Resource
    private GradeInfoMapper gradeInfoMapper;
    @Resource
    private GradeBenefitMapper gradeBenefitMapper;
    @Resource
    private BenefitInfoMapper benefitInfoMapper;
    @Resource
    private RedisService redisService;

    /**
     * 查询会员等级列表
     * @param param 查询条件入参
     * @return PageInfo<GradeInfoDTO>
     */
    @Override
    public List<GradeInfoDTO> getGradeInfoList(GradeInfoDTO param) {
        GradeInfo gradeInfo = BeanCopierUtils.convertObjectTrim(param,GradeInfo.class);
        List<GradeInfo> list = gradeInfoMapper.selectList(new LambdaQueryWrapper<GradeInfo>(gradeInfo).orderByAsc(GradeInfo::getSort));
        List<GradeInfoDTO> dtoList = BeanCopierUtils.convertList(list,GradeInfoDTO.class);
        dtoList.stream().forEach(s -> {
            Long count = gradeBenefitMapper.selectCount(new LambdaQueryWrapper<GradeBenefit>().eq(GradeBenefit::getGradeId,s.getId()).eq(GradeBenefit::getRelationType,1));
            s.setBenefitNum(count);
        });
        return dtoList;
    }

    /**
     * 根据ID/CODE查询会员等级
     * @param param
     * @return GradeInfoDTO
     */
    @Override
    @Cacheable(prefix = CacheKeys.GRADE_INFO,key = "'#tenantId'+'#param.id'+'#param.code'")
    public GradeInfoDTO getGradeInfo(IdCodeDTO param,Long tenantId) {
        GradeInfo gradeInfo = null;
        if(null != param.getId()) {
            gradeInfo = gradeInfoMapper.selectById(param.getId());
        }
        else {
            gradeInfo = gradeInfoMapper.selectOne(new LambdaQueryWrapper<GradeInfo>().eq(GradeInfo::getGradeCode,param.getCode()));
        }
        if(null == gradeInfo) {
            return null;
        }
        GradeInfoDTO grade = BeanCopierUtils.convertObject(gradeInfo,GradeInfoDTO.class);
        List<GradeBenefit> gradeBenefits = gradeBenefitMapper.selectList(new LambdaQueryWrapper<GradeBenefit>().eq(GradeBenefit::getGradeId,gradeInfo.getId()));
        if(CollectionUtils.isEmpty(gradeBenefits)) {
            return grade;
        }
        List<Long> ids = gradeBenefits.stream().map(GradeBenefit::getBenefitId).collect(Collectors.toList());
        List<BenefitInfo> benefits = benefitInfoMapper.selectBatchIdsBySort(param.getId(),ids);
        Map<Long,BenefitInfo> map = benefits.stream().collect(Collectors.toMap(BenefitInfo::getId,benefitInfo -> benefitInfo));
        List<BenefitInfoDTO> activateList = new ArrayList<>();
        List<BenefitInfoDTO> unActivateList = new ArrayList<>();
        for (GradeBenefit gradeBenefit : gradeBenefits) {
            BenefitInfoDTO benefitInfoDTO = BeanCopierUtils.convertObject(map.get(gradeBenefit.getBenefitId()), BenefitInfoDTO.class);
            benefitInfoDTO.setCode(map.get(gradeBenefit.getBenefitId()).getCode()); // Manually set the code field
            log.info("benefitInfoDTO1111111111111111111111111111111111111111111111111111111111:{}",benefitInfoDTO);
            if (gradeBenefit.getRelationType().equals(1)) {
                activateList.add(benefitInfoDTO);
            } else {
                unActivateList.add(benefitInfoDTO);
            }
        }
//        for (GradeBenefit gradeBenefit : gradeBenefits) {
//            if(gradeBenefit.getRelationType().equals(1)) {
//                activateList.add(BeanCopierUtils.convertObject(map.get(gradeBenefit.getBenefitId()),BenefitInfoDTO.class));
//            }
//            else {
//                unActivateList.add(BeanCopierUtils.convertObject(map.get(gradeBenefit.getBenefitId()),BenefitInfoDTO.class));
//            }
//        }
        if (activateList != null && !activateList.isEmpty()) {
            activateList = activateList.stream()
                    .sorted(Comparator.comparing(BenefitInfoDTO::getSort, Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.toList());
        }
        if (unActivateList != null && !unActivateList.isEmpty()) {
            unActivateList = unActivateList.stream()
                    .sorted(Comparator.comparing(BenefitInfoDTO::getSort, Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.toList());
        }

        grade.setActivateBenefits(activateList);
        grade.setUnActivateBenefits(unActivateList);
        return grade;
    }

    /**
     * 保存会员等级
     * @param param 新增或更新入参
     * @return Long
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveGradeInfo(GradeInfoDTO param) {
        GradeInfo gradeInfo = new GradeInfo(param.getId(), param.getGradeCode(), param.getGradeName(), param.getStyleJson(), param.getSort(), param.getExpenseAmount(), param.getState());
        List<GradeInfoDTO> gradeInfoDTOList = gradeInfoMapper.selectByCodeOrName(param);
        Map<Long,GradeBenefit> map = new HashMap<>();
        if(ParamUtils.isNullOr0Long(gradeInfo.getId())) {
            if(!CollectionUtils.isEmpty(gradeInfoDTOList)){
                throw new BusinessException(ErrorCode.BAD_REQUEST, "[等级]会员等级编码或名称不能重复");
            }
            gradeInfoMapper.insert(gradeInfo);
        }else {
            if(gradeInfoDTOList.stream().anyMatch(s -> !s.getId().equals(param.getId()))){
                throw new BusinessException(ErrorCode.BAD_REQUEST, "[等级]会员等级编码或名称不能重复");
            }
            gradeInfoMapper.updateById(gradeInfo);
            List<GradeBenefit> gradeBenefits = gradeBenefitMapper.selectList(new LambdaQueryWrapper<GradeBenefit>().eq(GradeBenefit::getGradeId,gradeInfo.getId()));
            if(!CollectionUtils.isEmpty(gradeBenefits)) {
                map = gradeBenefits.stream().collect(Collectors.toMap(GradeBenefit::getBenefitId,gradeBenefit -> gradeBenefit));
            }
        }
        if(!CollectionUtils.isEmpty(param.getActivateBenefits())) {
            List<Long> newIds = new ArrayList<>();
            for (BenefitInfoDTO benefit : param.getActivateBenefits()) {
                newIds.add(benefit.getId());
//                BenefitInfo benefitInfo = benefitInfoMapper.selectById(benefit.getId());
//                if (benefit.getJumpLink()!= null){
//                    log.info("跳转和ID:{}{}",benefit.getJumpLink(),benefit.getId());
//                    benefitInfo.setJumpLink(benefit.getJumpLink());
//                }
//                if (benefit.getPopupImg()!=null){
//                    log.info("弹窗图片和ID:{}{}",benefit.getPopupImg(),benefit.getId());
//                    benefitInfo.setPopupImg(benefit.getPopupImg());
//                }

//                benefitInfoMapper.updateById(benefitInfo);
                if(map.containsKey(benefit.getId()) && map.get(benefit.getId()).getRelationType().equals(1)) {
                    GradeBenefit gradeBenefit = map.get(benefit.getId());
                    if (benefit.getSort()!=null){
                        gradeBenefit.setSort(benefit.getSort());
                    }
                    if (benefit.getJumpLink()!= null){
                        log.info("跳转和ID:{}{}",benefit.getJumpLink(),benefit.getId());
                        gradeBenefit.setJumpLink(benefit.getJumpLink());
                    }
                    if (benefit.getPopupImg()!=null){
                        log.info("弹窗图片和ID:{}{}",benefit.getPopupImg(),benefit.getId());
                        gradeBenefit.setPopupImg(benefit.getPopupImg());
                    }
                    if (benefit.getCode()!=null){
                        gradeBenefit.setCode(benefit.getCode());
                    }
                    log.info("要修改的gradeBenefit:{}",gradeBenefit);
                    gradeBenefitMapper.updateById(gradeBenefit);
                    continue;
                }
                GradeBenefit gradeBenefit = new GradeBenefit(null,gradeInfo.getId(),benefit.getId(),1);
                if (benefit.getSort()!=null){
                    gradeBenefit.setSort(benefit.getSort());
                }
                if (benefit.getSort()!=null){
                    gradeBenefit.setSort(benefit.getSort());
                }
                if (benefit.getJumpLink()!= null){
                    log.info("跳转和ID:{}{}",benefit.getJumpLink(),benefit.getId());
                    gradeBenefit.setJumpLink(benefit.getJumpLink());
                }
                if (benefit.getPopupImg()!=null){
                    log.info("弹窗图片和ID:{}{}",benefit.getPopupImg(),benefit.getId());
                    gradeBenefit.setPopupImg(benefit.getPopupImg());
                }
                if (benefit.getCode()!=null){
                    gradeBenefit.setCode(benefit.getCode());
                }
                log.info("要添加的gradeBenefit:{}",gradeBenefit);
                gradeBenefitMapper.insert(gradeBenefit);
            }
            for (GradeBenefit gradeBenefit : map.values()) {
                if(gradeBenefit.getRelationType().equals(1) && !newIds.contains(gradeBenefit.getBenefitId())) {
                    gradeBenefitMapper.deleteById(gradeBenefit.getId());
                }
            }
        }else{
            for (GradeBenefit gradeBenefit : map.values()) {
                if(gradeBenefit.getRelationType().equals(1)) {
                    gradeBenefitMapper.deleteById(gradeBenefit.getId());
                }
            }
        }
        if(!CollectionUtils.isEmpty(param.getUnActivateBenefits())) {
            List<Long> idsA=new ArrayList<>();
            if(!CollectionUtils.isEmpty(param.getActivateBenefits())) {
                idsA=param.getActivateBenefits().stream().map(BenefitInfoDTO::getId).collect(Collectors.toList());
            }
            List<Long> idsB=param.getUnActivateBenefits().stream().map(BenefitInfoDTO::getId).collect(Collectors.toList());
            //可激活权益idsA如果包含在未激活idsB中，则视为不可保存
            if (!CollectionUtils.isEmpty(idsA)){
                if (idsA.containsAll(idsB)){
                    throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "已激活权益不可出现在未激活权益中");
                }
            }
            List<Long> newIds = new ArrayList<>();
            for (BenefitInfoDTO benefit : param.getUnActivateBenefits()) {
                newIds.add(benefit.getId());
//                BenefitInfo benefitInfo = benefitInfoMapper.selectById(benefit.getId());
//                if (benefit.getJumpLink()!= null){
//                    log.info("跳转和ID:{}{}",benefit.getJumpLink(),benefit.getId());
//                    benefitInfo.setJumpLink(benefit.getJumpLink());
//                }
//                if (benefit.getPopupImg()!=null){
//                    log.info("弹窗图片和ID:{}{}",benefit.getPopupImg(),benefit.getId());
//                    benefitInfo.setPopupImg(benefit.getPopupImg());
//                }
//                benefitInfoMapper.updateById(benefitInfo);
                if(map.containsKey(benefit.getId()) && map.get(benefit.getId()).getRelationType().equals(2)) {
                    GradeBenefit gradeBenefit = map.get(benefit.getId());
                    if (benefit.getSort()!=null){
                        gradeBenefit.setSort(benefit.getSort());
                    }
                    if (benefit.getSort()!=null){
                        gradeBenefit.setSort(benefit.getSort());
                    }
                    if (benefit.getJumpLink()!= null){
                        log.info("跳转和ID:{}{}",benefit.getJumpLink(),benefit.getId());
                        gradeBenefit.setJumpLink(benefit.getJumpLink());
                    }
                    if (benefit.getPopupImg()!=null){
                        log.info("弹窗图片和ID:{}{}",benefit.getPopupImg(),benefit.getId());
                        gradeBenefit.setPopupImg(benefit.getPopupImg());
                    }
                    if (benefit.getCode()!=null){
                        gradeBenefit.setCode(benefit.getCode());
                    }
                    gradeBenefitMapper.updateById(gradeBenefit);
                    continue;
                }

                GradeBenefit gradeBenefit = new GradeBenefit(null,gradeInfo.getId(),benefit.getId(),2);
                if (benefit.getSort()!=null){
                    gradeBenefit.setSort(benefit.getSort());
                }
                if (benefit.getSort()!=null){
                    gradeBenefit.setSort(benefit.getSort());
                }
                if (benefit.getJumpLink()!= null){
                    log.info("跳转和ID:{}{}",benefit.getJumpLink(),benefit.getId());
                    gradeBenefit.setJumpLink(benefit.getJumpLink());
                }
                if (benefit.getPopupImg()!=null){
                    log.info("弹窗图片和ID:{}{}",benefit.getPopupImg(),benefit.getId());
                    gradeBenefit.setPopupImg(benefit.getPopupImg());
                }
                if (benefit.getCode()!=null){
                    gradeBenefit.setCode(benefit.getCode());
                }
                gradeBenefitMapper.insert(gradeBenefit);
            }
            for (GradeBenefit gradeBenefit : map.values()) {
                if(gradeBenefit.getRelationType().equals(2) && !newIds.contains(gradeBenefit.getBenefitId())) {
                    gradeBenefitMapper.deleteById(gradeBenefit.getId());
                }
            }
        }else{
            for (GradeBenefit gradeBenefit : map.values()) {
                if(gradeBenefit.getRelationType().equals(2)) {
                    gradeBenefitMapper.deleteById(gradeBenefit.getId());
                }
            }
        }
        Long tenantId = SecurityContext.getUser().getTenantId();
        redisService.del(CacheKeys.GRADE_NAME_MAP+":"+tenantId);
        redisService.del(CacheKeys.GRADE_CODE_MAP+":"+tenantId);
        redisService.delAll(CacheKeys.GRADE_INFO+":"+tenantId);
        return gradeInfo.getId();
    }

    /**
     * 根据ID删除会员等级(逻辑删除)
     * @param param 单ID/CODE POST请求通用DTO
     */
    @Override
    public void deleteGradeInfoById(IdCodeDTO param) {
        GradeInfo info = gradeInfoMapper.selectById(param.getId());
        if(info == null || info.getIsDeleted().equals(1)){
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[等级]未查询到此等级或已删除");
        }
        if(info.getState() != 0){
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[等级]此等级未下架不能删除");
        }
        gradeInfoMapper.deleteById(param.getId());
        Long tenantId = SecurityContext.getUser().getTenantId();
        redisService.del(CacheKeys.GRADE_NAME_MAP+":"+tenantId);
        redisService.del(CacheKeys.GRADE_CODE_MAP+":"+tenantId);
        redisService.delAll(CacheKeys.GRADE_INFO+":"+tenantId);
    }

    /**
     * 根据ID修改会员启停状态
     * @param param ID NUMBER 通用DTO
     */
    @Override
    public void updateGradeInfoStateById(IsEnableDTO param) {
        GradeInfo info = gradeInfoMapper.selectById(param.getId());
        if(info == null || info.getIsDeleted() == 1){
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[等级]未查询到此等级");
        }
        GradeInfo gradeInfo = new GradeInfo();
        gradeInfo.setId(param.getId());
        gradeInfo.setState(param.getState());
        gradeInfoMapper.updateById(gradeInfo);
        Long tenantId = SecurityContext.getUser().getTenantId();
        redisService.del(CacheKeys.GRADE_NAME_MAP+":"+tenantId);
        redisService.del(CacheKeys.GRADE_CODE_MAP+":"+tenantId);
    }

    /**
     * 获取会员等级CodeToName MAP
     * @return
     */
    @Override
    @Cacheable(prefix = CacheKeys.GRADE_CODE_MAP,key = "'#tenantId'")
    public List<GradeListDTO> getCodeToNameGradeMap(Long tenantId) {
        LambdaQueryWrapper<GradeInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GradeInfo::getState,1);
        List<GradeInfo> list = gradeInfoMapper.selectList(wrapper);

        if(CollectionUtils.isEmpty(list)) {
            return BeanCopierUtils.convertList(list,GradeListDTO.class);
        }
        return new ArrayList<>();
    }

    /**
     * 获取会员等级NameToCode MAP
     * @return
     */
    @Override
    @Cacheable(prefix = CacheKeys.GRADE_NAME_MAP,key = "'#tenantId'")
    public Map<String,String> getNameToCodeGradeMap(Long tenantId) {
        LambdaQueryWrapper<GradeInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GradeInfo::getState,1);
        List<GradeInfo> list = gradeInfoMapper.selectList(wrapper);
        Map<String,String> map = new HashMap<>();
        if(CollectionUtils.isEmpty(list)) {
            return map;
        }
        for (GradeInfo grade : list) {
            map.put(grade.getGradeName(),grade.getGradeCode());
        }
        return map;
    }

    /**
     * 批量更新会员等级
     * @param list
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateGradeBatch(List<GradeInfoDTO> list) {
        //List<MaterialRelationParamDTO> relationList = new ArrayList<>();
        for (GradeInfoDTO gradeInfo : list) {
            GradeInfo grade = BeanCopierUtils.convertObject(gradeInfo,GradeInfo.class);
            gradeInfoMapper.updateById(grade);
            /*if(null != grade.getLogo() || null != grade.getStyle()) {
                relationList.add(new MaterialRelationParamDTO(MaterialRelationEnum.MEMBER_CARD,grade.getLogo()+","+grade.getStyle(),grade.getId()));
            }*/
        }
        Long tenantId = SecurityContext.getUser().getTenantId();
        redisService.del(CacheKeys.GRADE_NAME_MAP+":"+tenantId);
        redisService.del(CacheKeys.GRADE_CODE_MAP+":"+tenantId);
        redisService.delAll(CacheKeys.GRADE_INFO+":"+tenantId);
        //materialFeginClient.saveBatchMaterialRelation(relationList);
    }

}
