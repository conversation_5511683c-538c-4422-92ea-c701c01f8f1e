package com.dz.ms.basic.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "页面推广")
public class PromotionPageDTO extends BaseDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "一级渠道名称")
    private String oneChannelName;
    @ApiModelProperty(value = "一级渠道参数")
    private String oneChannelParam;
    @ApiModelProperty(value = "二级渠道名称")
    private String towChannelName;
    @ApiModelProperty(value = "二级渠道参数")
    private String towChannelParam;
    @ApiModelProperty(value = "一级渠道ID")
    private Long onepId;
    @ApiModelProperty(value = "二级渠道ID")
    private Long twopId;
    @ApiModelProperty(value = "页面路径")
    private String pagePath;
    @ApiModelProperty(value = "链接 ")
    private String linkUrl;
    @ApiModelProperty(value = "二维码")
    private String qrCode;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "创建名称")
    private String creatorName;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "上一次生成时间")
    private Date endTime;
}
