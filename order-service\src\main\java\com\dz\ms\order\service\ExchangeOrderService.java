package com.dz.ms.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.MujiOrder;
import com.dz.common.core.dto.order.*;
import com.dz.common.core.dto.product.CpStaticDTO;
import com.dz.common.core.dto.product.CpStaticParamDTO;
import com.dz.ms.order.dto.CrmExchangeOrderListDTO;
import com.dz.ms.order.dto.ExchangeOrderDTO;
import com.dz.ms.order.dto.req.CrmExchangeOrderParamDTO;
import com.dz.ms.order.entity.ExchangeOrder;

import java.util.Date;
import java.util.List;

/**
 * 订单主表接口
 *
 * @author: LiinNs
 * @date: 2024/12/09 10:38
 */
public interface ExchangeOrderService extends IService<ExchangeOrder> {

    /**
     * APP端我的订单列表
     */
    PageInfo<OrderListAppDTO> listApp(OrderListAppParamDTO param);

    /**
     * 新增订单
     *
     * @param param
     * @return
     */
    CreateOrderResultDTO create(OrderCreateParamDTO param);

    /**
     * 订单预览
     */
    PreviewResultDTO preview(OrderPreviewParamDTO paramDto);

    /**
     * 订单详情APP端
     */
    OrderInfoAppDTO infoApp(String orderCode);

    /**
     * 订单删除
     */
    void delete(OrderDeleteParamDTO dto);

    /**
     * 分页查询订单主表
     *
     * @param param
     * @return PageInfo<ExchangeOrderDTO>
     */
    PageInfo<CrmExchangeOrderListDTO> getExchangeOrderList(CrmExchangeOrderParamDTO param);

    /**
     * 根据ID查询订单主表
     *
     * @param id
     * @return ExchangeOrderDTO
     */
    public ExchangeOrderDTO getExchangeOrderById(Long id);

    /**
     * 保存订单主表
     *
     * @param param
     * @return Long
     */
    public Long saveExchangeOrder(ExchangeOrderDTO param);

    /**
     * 根据ID删除订单主表
     *
     * @param param
     */
    public void deleteExchangeOrderById(IdCodeDTO param);

    /**
     * 订单列表导出
     *
     * @param exportParam
     */
    void exportOrderList(DownloadAddParamDTO exportParam);

    /**
     * 订单发货
     *
     * @param param
     * @return
     */
    int delivery(OrderDeliveryParamDTO param);

    Boolean importDelivery(List<OrderDeliveryParamDTO> list);

    /**
     * 订单统计
     *
     * @param param
     * @return
     */
    List<PurchaseStaticDTO> purchaseStatic(PurchaseStaticParamDTO param);

    /**
     * 订单状态兑换修改
     *
     * @param orderCode
     */
    void changeOrderStatusByOrderCode(String orderCode);

    /**
     * 订单状态修改
     */
    void changeOrderStatus();

    void changeAllOrderStatus();

    PageInfo<CpStaticDTO> cpStatic(CpStaticParamDTO param);

    List<MujiOrder> selectMujiOrder(Date beginTime, Date endTime);

    List<String> getUnusedStockId();

    List<String> sftpOrderList(String memberCode,
                               String startTime,
                               String endTime,
                               String deptId,
                               String depaId,
                               String lineId,
                               String classId,
                               String janId);

    Integer selectExchangePeople(Long shelfId);
}
