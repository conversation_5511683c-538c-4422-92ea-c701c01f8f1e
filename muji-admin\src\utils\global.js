import { useGlobalStore } from '@/store'
import dayjs from 'dayjs'
import weekday from 'dayjs/plugin/weekday.js';
import localeData from 'dayjs/plugin/localeData.js';
dayjs.extend(weekday);
dayjs.extend(localeData);
import _ from 'lodash'


export default {
    /**
      * 检查当前用户是否有指定的权限。
      *
      * @param {string} value 需要检查的具体权限字符串。
      * @returns {boolean} 如果用户有指定权限或全局权限，则返回 true，否则返回 false。
      */
    $hasPermission(value) {
        const global = useGlobalStore()
        const permissions = global.permissions
        if (value) {
            // 检查用户是否拥有全部权限或者具体权限
            const hasPermissions = permissions.includes(value)
            return hasPermissions
        };
        return true
    },
    // 预设常用的日期范围以提高用户体验
    $rangePresets: [
        {
            label: '最近一周',
            value: [dayjs().add(-7, 'd'), dayjs()],
        },
        {
            label: '最近两周',
            value: [dayjs().add(-14, 'd'), dayjs()],
        },
        {
            label: '最近一个月',
            value: [dayjs().add(-30, 'd'), dayjs()],
        },
        {
            label: '最近三个月',
            value: [dayjs().add(-90, 'd'), dayjs()],
        },
    ],
    // 主题色
    $colorPrimary: '#6a6bbf',
    /**
     * 根据指定的ID值，从选项数组中查找并返回对应的值。
     *
     * @param {any} val - 要查找的ID值。
     * @param {Array} options - 选项数组，每个选项是一个对象。
     * @param {string} key - 在选项对象中用于比较的键名。
     * @param {string} value - 在找到匹配项后，返回该项的指定键对应的值。
     * @returns {string} - 如果找到匹配项，则返回指定键的值；否则返回空字符串。
  */
    $getValueById(val, options, key, value) {
        let result = options.filter(item => {
            return val == item[key]
        })
        if (result.length) {
            return result[0][value]
        }
        return ''
    },
    // 获取指定对象key的值
    $getObjectValue(obj, key) {
        return _.get(obj, key)
    },
}