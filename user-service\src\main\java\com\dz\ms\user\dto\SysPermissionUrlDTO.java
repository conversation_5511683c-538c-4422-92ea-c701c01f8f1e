package com.dz.ms.user.dto;
import java.util.Date;
import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 权限功能接口DTO
 * @author: Handy
 * @date:   2022/07/18 18:24
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "权限功能接口")
public class SysPermissionUrlDTO extends BaseDTO {

    @ApiModelProperty(value = "权限页面ID")
    private Long id;
    @ApiModelProperty(value = "权限ID")
    private Long permitId;
    @ApiModelProperty(value = "权限名称")
    private String permitName;
    @ApiModelProperty(value = "接口地址")
    private String url;

}
