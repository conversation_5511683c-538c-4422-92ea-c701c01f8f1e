package com.dz.ms.user.dto.dkeyam.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 站点列表 入参
 * @author: fei
 * @date: 2025/01/22 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TenantListRes implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("站点id")
    private String id;

    @ApiModelProperty("站点名称")
    private String name;


}
