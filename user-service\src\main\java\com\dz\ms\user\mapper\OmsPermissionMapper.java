package com.dz.ms.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.user.entity.OmsPermission;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * OMS-权限功能Mapper
 * @author: Handy
 * @date:   2020/01/29 19:15
 */
@Repository
public interface OmsPermissionMapper extends BaseMapper<OmsPermission> {

    /**
     * 获取角色权限列表
     * @param roleId
     * @return
     */
    List<OmsPermission> getRolePermitCodes(@Param("roleId") Long roleId);

    /**
     * 获取角色权限ID列表
     * @param roleId
     * @return
     */
    List<Long> getRolePermitIds(@Param("roleId") Long roleId);

    /**
     * 获取接口权限URL对应父节点列表
     * @return
     */
	List<OmsPermission> getPermissionUrlList();

}