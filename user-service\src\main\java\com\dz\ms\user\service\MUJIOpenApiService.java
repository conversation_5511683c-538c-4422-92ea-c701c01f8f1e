package com.dz.ms.user.service;

import com.alibaba.fastjson.JSONObject;
import com.dz.ms.user.dto.UserMobileUpdDTO;

/**
 * mujiOpenApi接口
 *
 * @author: Handy
 * @date: 2022/2/4 17:33
 */
public interface MUJIOpenApiService {

    /**
     * 第三方注册绑定
     * @param mobile
     * @param openId
     * @param unionId
     * @return
     */
    JSONObject register(String mobile, String openId, String unionId,String channel);

    /**
     * 校验手机号是否已注册
     * @param mobile
     * @return
     */
    JSONObject memberCheck(String mobile);

    /**
     * 会员信息查询
     * @param cardNo
     * @return
     */
    JSONObject memberDetailByCardNo(String cardNo);

    /**
     * 会员信息查询
     * @param unionId
     * @return
     */
    JSONObject memberDetailByUnionId(String unionId);

    /**
     * 发送手机验证码
     * @param mobile
     * @return
     */
    JSONObject sendMobileCode(String mobile);

    /**
     * 会员手机号变更
     * @param userMobileUpdDTO 入参
     * @return
     */
    JSONObject mobileUpdate(UserMobileUpdDTO userMobileUpdDTO);

    /**
     * 会员信息更新
     * @param memberCode
     * @param nickName
     * @param birthday 1998-01-01当会员存在生日信息时，该字段忽略
     * @param gender 1男 2女 3未知
     * @param avatar
     * @param province 130000（河北省）
     * @param city 130600（保定市）
     * @return
     */
    JSONObject updateMemberInfo(String memberCode, String nickName, String birthday,Integer gender,
                                String avatar,String province,String city);

    /**
     * 会员账号注销
     * @param memberCode
     * @param smsCode
     * @param cancelReason 1:不想/不再需要使用2:活动优惠少3:账户出现问题4:担忧隐私及安全问题5:其它
     * @param remark
     * @return
     */
    JSONObject cancelMemberInfo(String memberCode, String smsCode,Integer[] cancelReason,String remark);

    /**
     * 获取会员动态码
     * @param memberCode
     * @return
     */
    JSONObject getMemberPrCode(String memberCode);

    /**
     * 会员积分增加
     * @param memberCode
     *  channel 积分渠道，请联系PM获取
     *  outSn 请求流水单号，业务方必须保证全局唯一
     * @param bonusAmount
     *  expiredAt 过期时间 格式:年-月-日 例如：2024-12-31
     * @param reason
     * @return
     */
    JSONObject addMemberPoints(Long userId,String memberCode, String channel, Integer bonusAmount, String reason);

    /**
     * 会员积分增加
     *
     * @param memberCode  channel 积分渠道，请联系PM获取
     * @param outSn       请求流水单号，业务方必须保证全局唯一
     * @param bonusAmount expiredAt 过期时间 格式:年-月-日 例如：2024-12-31
     * @param reason
     * @return
     */
    JSONObject addMemberPoints(String memberCode, String channel, Integer bonusAmount, String reason, String outSn);

    /**
     * 会员积分扣减
     * @param memberCode
     *  channel 积分渠道，请联系PM获取
     *  outSn 请求流水单号，业务方必须保证全局唯一
     * @param bonusAmount
     *  expiredAt 过期时间 格式:年-月-日 例如：2024-12-31
     * @param reason
     * @return
     */
    JSONObject deductMemberPoints(String memberCode, String channel, Integer bonusAmount, String reason);

    JSONObject deductMemberPoints(String memberCode, String channel, Integer bonusAmount, String reason, String outSn);

    /**
     * 会员积分流水列表
     *
     * @param memberCode
     * @param start_time 2024-12-31 23:59:59
     * @param end_time   2024-12-31 23:59:59
     * @param page
     * @param pageSize
     * @return
     */
    JSONObject memberPointsList(String memberCode, String start_time,String end_time,Integer page,
                                Integer pageSize);

    /**
     * 会员积分流水详情
     * @param memberCode 会员编号
     * @param bonusSn 积分流水编号
     * @return JSONObject
     */
    JSONObject memberPointsDetail(String memberCode, String bonusSn);

    /**
     * 会员积分数据查询
     * @param memberCode
     * @return
     */
    JSONObject memberPointsCount(String memberCode);

    /**
     * 会员里程流水列表
     * @param memberCode
     * @param start_time 2024-12-31 23:59:59
     * @param end_time 2024-12-31 23:59:59
     * @param page
     * @param pageSize
     * @return
     */
    JSONObject memberMileageList(String memberCode, String start_time,String end_time,Integer page,
                                Integer pageSize);

    /**
     * 会员里程流水详情
     * @param memberCode 会员编号
     * @param mileageSn 里程流水编号
     * @return JSONObject
     */
    JSONObject memberMileageDetail(String memberCode, String mileageSn);

    /**
     * 【领券活动】活动列表
     * @param memberCode
     *  platform 微信小程序WECHAT、支付宝ALIPAY
     * @return
     */
    JSONObject activityCouponList(String memberCode);

    /**
     * 【领券活动】活动领取
     *
     * @param memberCode
     * @param activityId 活动ID 例如:e1t5s53d1f3s5
     *                   platform 微信小程序WECHAT、支付宝ALIPAY
     * @return
     */
    JSONObject activityCouponReceive(String memberCode, String activityId);

    /**
     * 【扫码活动】扫码上报人群
     *
     * @param memberCode
     * @param qrcodeId   扫码活动ID 例如:4pekK0
     * @param qrcodeSn   二维码编号 例如:0533841302475137
     *                   platform 微信小程序WECHAT、支付宝ALIPAY
     * @return
     */
    JSONObject activityQrCodeReceive(String memberCode, String qrcodeId, String qrcodeSn);

    /**
     * 会员优惠券列表
     *
     * @param memberCode
     * @param type       1:支付宝商家券 2:微信商家券 3:微信代金券 4:费芮电子券 5:三方权益券 6:nec券 7:中台电子券 8:中台内买券
     * @param status     1:未使用(可使用) 2:已使用 3:已过期
     * @param page
     * @param pageSize
     * @return
     */
    JSONObject memberCouponList(String memberCode,Integer type,Integer status,Integer page,Integer pageSize);

    /**
     * 优惠券批次详情
     * @param stockId 券批次号
     * @return
     */
    JSONObject memberCouponDetails(String stockId);

    /**
     * 会员优惠券领取
     * @param memberCode
     * @param stockId 券批次号
     * @return
     */
    JSONObject memberCouponReceive(String memberCode,String stockId);

    /**
     * 会员券码详情
     * @param memberCode
     * @param stockId 券批次号
     * @param couponCode
     * @return
     */
    JSONObject memberCouponCodeDetails(String memberCode,String stockId,String couponCode);

    /**
     * 会员订单列表
     * @param memberCode
     * @param start_time 2024-01-01 00:00:00
     * @param end_time 2024-12-31 23:59:59
     * @param type 1:所有订单 2:线上订单 3：线下订单
     * @param page
     * @param pageSize
     * @return
     */
    JSONObject memberOrderList(String memberCode, String start_time,String end_time,Integer type,Integer page,
                                 Integer pageSize);

    /**
     * 会员订单-小票详情
     * @param memberCode
     * @param orderSn 订单编号
     * @return
     */
    JSONObject memberOrderTicketDetails(String memberCode,String orderSn);

    /**
     * 会员订单-订单详情
     * @param memberCode
     * @param orderSn 订单编号
     * @return
     */
    JSONObject memberOrderDetails(String memberCode,String orderSn);

    /**
     * 查看用户开卡状态
     * @param memberCode
     * @return
     */
    JSONObject openCardStatus(String memberCode);

    /**
     * 即将过期积分列表
     * @param memberCode
     * @param page
     * @param pageSize
     * @return
     */
    JSONObject recentExpirePoints(String memberCode,Integer page,Integer pageSize);

    /**
     * 历史过期积分列表
     * @param memberCode
     * @param startTime
     * @param endTime
     * @param page
     * @param pageSize
     * @return
     */
    JSONObject historyExpirePoints(String memberCode, String startTime,String endTime,Integer page,
                               Integer pageSize);
}
