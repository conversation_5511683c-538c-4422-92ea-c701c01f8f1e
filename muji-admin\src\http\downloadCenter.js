import service from '@/utils/request.js'

// 下载中心列表
export function downloadTaskList(data = {}) {
    return service({
        url: '/crm/basic/report/download/task/list',
        method: 'post',
        data
    })
}
//

export function downloadTaskdelete(data = {}) {
    return service({
        url: '/crm/basic/report/download/task/delete',
        method: 'post',
        data
    })
}

//
export function downloadTaskcategory(data = {}) {
    return service({
        url: '/crm/basic/report/download/task/category',
        method: 'get',
        data
    })
}

export function downloadTaskmark(data = {}) {
    return service({ url: '/crm/basic/report/download/task/mark', method: 'post', data })
}
// cp 商品列表  


export function productCp_static(data = {}) {
    return service({
        url: '/crm/product/product/cp_static',
        method: 'get',
        data
    })
}