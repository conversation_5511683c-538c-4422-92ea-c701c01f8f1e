{"name": "bvl-admin", "private": true, "engines": {"node": "18.17.1", "npm": "9.6.7"}, "version": "0.0.0", "type": "module", "scripts": {"dev": "npm run serve", "serve": "vite --mode dev", "serveUat": "vite --mode uat", "servePro": "vite --mode prod", "build": "vite build --mode dev", "buildUat": "vite build --mode uat", "buildPro": "vite build --mode prod", "devDeploy": "node ./scripts/devDeploy", "masterDeploy": "node ./scripts/masterDeploy", "uatDeploy": "node ./scripts/uatDeploy", "publish": "node node_modules/gh-pages/bin/gh-pages-clean && npm run build && npm run devDeploy", "publishUat": "node node_modules/gh-pages/bin/gh-pages-clean && npm run buildUat && npm run uatDeploy", "publishPro": "node node_modules/gh-pages/bin/gh-pages-clean && npm run buildPro && npm run masterDeploy"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@tinymce/tinymce-vue": "^6.1.0", "@vue/compiler-sfc": "^3.4.19", "ant-design-vue": "^4.1.2", "axios": "^1.6.7", "clipboard": "^2.0.11", "colorpicker-v3": "^2.10.2", "crypto-js": "^4.1.1", "dayjs": "^1.11.13", "echarts": "^5.5.0", "fabric": "^5.3.0", "file-saver": "^2.0.5", "js-cookie": "^3.0.5", "jsencrypt": "^3.3.2", "jszip": "^3.10.1", "lodash": "^4.17.21", "pinia": "^2.1.7", "swiper": "^8.4.7", "tinymce": "^7.5.1", "unplugin-auto-import": "^0.17.5", "uuid": "^9.0.1", "vue": "^3.4.19", "vue-request": "^1.2.5", "vue-router": "^4.3.0", "vue3-video-play": "1.3.1-beta.6", "vuedraggable": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^4.0.0", "fast-glob": "^3.3.2", "gh-pages": "^6.1.1", "sass": "^1.71.1", "sass-loader": "^14.1.1", "vite": "^5.1.4", "vite-plugin-remove-console": "^2.2.0", "vite-plugin-svg-icons": "^2.0.1"}}