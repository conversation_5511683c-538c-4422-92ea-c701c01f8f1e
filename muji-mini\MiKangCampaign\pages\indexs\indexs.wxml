<!--signUp/pages/indexs/indexs.wxml 活动报名分包页面-->
<my-page overallModal="{{overallModal}}">
  <view class="page-container" style="background-image:url({{$cdn}}/MiKangCampaign/mk-sign-up-bg-1.png);">
    <!-- isShare="{{true}}" -->
    <custom-header background="transparent" type="{{1}}" isBackHidden="{{ isBackHidden }}" color="black" />
    <view class="right-lanyard">
        <image style="height: 286rpx; width: 136rpx;" mode="aspectFit" src="{{$cdn}}/MiKangCampaign/mk-sign-up-lanyard.png"></image>
    </view>
    <scroll-view class="index" enhanced="{{ true }}" scroll-y="{{true}}" bounces="{{ false }}" show-scrollbar="{{ false }}">
      <!-- style="margin-top: {{ menuButtonBottom }}px" style="position: relative; height: 100%;" -->
      <view class="content" style="background-image:url({{$cdn}}/MiKangCampaign/mk-add-form-bg.png);">
        <view class="header">体验官报名信息</view>
        <view class="index-form">
          <view class="form-tips form-tips1">报名成功后，将有机会成为体验官。</view>
          <!-- <view class="form-title form-title1">报名信息</view> -->
          <!-- <view class="form-tips form-tips1">*报名成功后，将有机会成为体验官。</view> -->
          <view class="form-item">
            <view class="item-label">
              昵称
            </view>
            <view class="item-content">
              <input type="nickname" class="item-input" placeholder="请输入您的昵称" placeholder-style="color: #BBBBBB;" value="{{ info.username }}" bindchange="changeuserName" />
            </view>
          </view>
          <view class="form-item">
            <view class="item-label">
              您最想解决的肌肤问题
            </view>
            <!-- <picker value="{{info.skinTypeIndex}}" mode="selector" range="{{skinTypeList}}" range-key='label' bindchange="bindPickerChange">
              <view class="item-content flex-center">
                <view class="item-text">
                  <text class="{{info.skinTypeIndex ?'item-value' : 'without-data'}}">
                    <text>{{info.skinTypeIndex ? skinTypeList[info.skinTypeIndex].label:
                      '请选择'}}</text>
                  </text>
                </view>
                <view class="item-button">
                  <van-icon name="arrow" />
                </view>
              </view>
            </picker> -->
            <view class="item-content flex-end" style="margin-top: 0rpx">
              <view class="item-text">
                <view bind:tap="showActionSelect" style="display: flex;color: {{info.skinTypeIndex  !== '' ? '#3C3C43' : '#BBBBBB' }};">{{info.skinTypeIndex !== '' ? skinTypeList[info.skinTypeIndex].label:
                      '请选择'}}<image style="height: 36rpx; width: 36rpx;" src="{{$cdn}}/MiKangCampaign/mk-right-arrow.png" mode=""/></view>
              </view>
            </view>
          </view>
          <!-- <view class="form-title form-title">收货信息</view> -->
          <!-- <view class="form-tips">*若您成为极简护肤体验官，我们会邮寄产品至此地址， 信息提交后不可修改。</view> -->
          <view class="form-item">
            <view class="item-label address">
              收货人姓名
              <!-- <view class="getAddress" bindtap="getAddress">
                <image style="width: 36rpx; height: 36rpx;margin-right: 10rpx; " src="{{$cdn}}/signUp/wx.png"></image>
                <view>一键获取微信地址</view>
              </view> -->
            </view>

            <view class="item-content flex-end" style="margin-top: 0rpx">
              <view class="item-text">
                <input class="item-input" placeholder="请输入收货人姓名" placeholder-style="color: #BBBBBB;" value="{{ info.name }}" bindchange="changeName" />
              </view>
            </view>
          </view>
          <view class="form-item">
            <view class="item-label">
              收货人电话
            </view>
            <view class="item-content">
              <input type="number" class="item-input" placeholder="请输入收货人电话" 	placeholder-style="color: #BBBBBB;" value="{{ info.enrollPhone }}" bindchange="changeenrollPhone" />
            </view>
          </view>
          <view class="form-item">
            <view class="item-label">
              省-市-区
            </view>
            <picker mode="region" bindchange="regionChange"	header-text="请选择所在地">
              <view class="item-content flex-center">
                <view class="item-text">
                  <text class="{{info.province ?'item-value' : 'without-data'}}">
                    <text>{{info.province ? info.province + ' ' + info.city + ' ' + info.district :
                      '请选择省、市、区'}}</text>
                  </text>
                </view>
                <view class="item-button">
                  <van-icon name="arrow" />
                </view>
              </view>
            </picker>
          </view>
          <view class="form-item">
            <view class="item-label">
              详细地址
            </view>
            <view class="item-content">
              <input class="item-input" placeholder="请输入详细地址" placeholder-style="color: #BBBBBB;" value="{{ info.address }}" bindchange="changeaddress" />
            </view>
          </view>
          <view class="form-tips-last">*若您成为米糠护肤体验官，收货信息将用于活动奖品的寄送，提交后不可修改。</view>
          <!-- <view class="check">
            <view bindtap="onTapPrivacyCheck" wx:if="{{!info.agree}}" class=" iconfont icon-danxuan-weigouxuan radio"></view>
            <view bindtap="onTapPrivacyCheck" wx:else class="iconfont icon-xuanzhong radio"></view>
            <view class="text">我已阅读并同意<text catchtap="go"><text class="link">《活动规则》</text></text>
            </view>
          </view> -->
        </view>
      </view>
      <view class="form-footer">
          <!-- <view class="check" bindtap="onTapPrivacyCheck">
            <view wx:if="{{!info.agree}}" class=" iconfont icon-danxuan-weigouxuan radio">
            </view>
            <view wx:else class="iconfont icon-danxuan-yigouxuan radio"></view>
            <view class="text">我已阅读并同意<text catchtap="go"><text class="link">《信息使用说明》</text></text>
            </view>
          </view> -->
      </view>
    </scroll-view>
    <view class="bottom-box">
      <basic-button class="basic-button" disabled="{{!isComplate}}" width="{{660}}" size="large" bind:click="submit">
        提交报名信息
      </basic-button>
    </view>
  </view>
  <Popup isShow="{{showPopup}}" info="{{infoLottery}}" bindclose="closePopup"></Popup>
</my-page>

<van-action-sheet custom-class="custom-select-action" show="{{ showSelect }}" title="最想解决的皮肤问题" bind:close="actionCancel">
  <view class="select-content" >
    <view class="select-item {{subItem.value === activeValue ? 'select-confirm-active' : ''}}" bind:tap="clickItem" data-value="{{subItem.value}}" wx:for="{{skinTypeList}}" wx:key="index" wx:for-item="subItem">{{subItem.label}}</view>
  </view>
  <view class="button-box">
    <button class="select-confirm" bind:tap="confirmSelect">确定</button>
  </view>
</van-action-sheet>