package com.dz.common.core.enums;

import java.util.Objects;

/**
 * 奖品类型
 * <AUTHOR>
 */
public enum PrizesTypeEnum {
    PRODUCT(1, "商品"),
    POINTS(2, "积分"),
    COIN(3, "金币"),
    COUPON(4, "优惠券"),
    SERVICE(5, "服务"),
    NONE(6, "谢谢参与"),
    VIRTUALLY(7, "虚拟商品");

    private final Integer code;
    private final String value;

    PrizesTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByCode(Integer code) {
        PrizesTypeEnum targetTypeEnum = getByCode(code);
        return targetTypeEnum==null ? null : targetTypeEnum.value;
    }

    public static PrizesTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PrizesTypeEnum resultEnum : PrizesTypeEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum;
            }
        }
        return null;
    }
}
