package com.dz.ms.basic.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "页面推广添加")
public class PromotionPageAddDTO extends BaseDTO {

    @ApiModelProperty(value = "模版ID")
    private Long id;
    @ApiModelProperty(value = "渠道ID")
    private List<Long> channelIds;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
