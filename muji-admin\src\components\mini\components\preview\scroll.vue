<template>
  <div class="poster" :style="{width:width/2+'px',height:data.height/2+'px'}">
    <customBg :bgSetting="data" class="bgStyle"></customBg>
    <div class="poster-content" :style="{
      borderRadius:data.borderRadius/2+'px',
      left:data.paddingLeft/2+'px',
      right:data.paddingRight/2+'px',
      top:data.paddingTop/2+'px',
      bottom:data.paddingBottom/2+'px',
    }">
      <div class="poster-info">
        <img v-for="item in newList" :key="item.id" :src="item.imgUrl" class="poster-img" :style="{width:item.posterWidth+'px',height:item.posterHeight+'px'}" />
      </div>
    </div>
  </div>
</template>
<script setup>
const emit = defineEmits(['ok', 'cancel', 'changeActive'])
import { cloneDeep } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { computed } from 'vue';
import customBg from './customBg.vue'
const props = defineProps({
  width: {
    type: Number,
    default: 750,
  },
  data: {
    type: Object,
    default() {
      return {}
    }
  },
})
watch(() => props.data, (val) => {
  calcHeight()
}, {
  deep: true
})
const { posterWidth, posterHeight, height, newList } = toRefs(reactive({
  posterWidth: '',
  posterHeight: '',
  height: '',
  newList: []
}))

const calcHeight = () => {
  let width = props.width;
  console.log(props.data, 'dddddddddddd')
  let {
    list,
    paddingLeft,
    paddingRight,
  } = props.data;
  newList.value = list.map(item => {
    let {
      imgWidth,
      imgHeight,
    } = item
    let posterWidth = width - paddingLeft - paddingRight
    let posterHeight = parseInt(posterWidth * imgHeight / imgWidth)
    return {
      ...item,
      posterWidth: posterWidth / 2,
      posterHeight: posterHeight / 2
    }
  })

}
calcHeight()

</script>

<style scoped lang="scss">
.poster {
  position: relative;

  &-content {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  &-info {
    width: 100%;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    &::-webkit-scrollbar {
      width: 0;
    }
  }

  &-img {
    display: block;
  }
}
</style>
