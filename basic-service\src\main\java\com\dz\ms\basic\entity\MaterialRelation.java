package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * 素材关联信息
 * @author: Handy
 * @date:   2023/05/09 18:05
 */
@Getter
@Setter
@NoArgsConstructor
@Table("素材关联信息")
@TableName(value = "material_relation")
public class MaterialRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "素材ID",isIndex = true)
    private Long materialId;
    @Columns(type = ColumnType.VARCHAR,length = 36,isNull = false,comment = "关联模块字段类型",isIndex = true)
    private String relationType;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,defaultValue = "0",comment = "关联业务ID",isIndex = true)
    private Long relationId;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "租户ID")
    private Long tenantId;

    public MaterialRelation(Long materialId, String relationType, Long relationId, Long tenantId) {
        this.materialId = materialId;
        this.relationType = relationType;
        this.relationId = relationId;
        this.tenantId = tenantId;
    }
}
