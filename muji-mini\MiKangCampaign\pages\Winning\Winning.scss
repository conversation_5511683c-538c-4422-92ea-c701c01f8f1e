/* signUp/pages/Winning/Winning.wxss */
@import "assets/scss/common";
@import "assets/scss/config";

.page-container {
    background-color: rgba(250, 241, 230, $alpha: 1);
    background-size: 100% 100%;

    .prizeDraw1 {
        flex: 1;
        overflow-y: auto;
        position: relative;

        .activeRules {
            position: absolute;
            right: 0rpx;
            top: 40rpx;
            width: 58rpx;
            height: 160rpx;
            background: #ab8e6166;
            font-family: MUJIFont2020;
            font-weight: 700;
            font-size: 28rpx;
            color: #ffffff;
            line-height: 32rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            writing-mode: vertical-rl;
            letter-spacing: 4rpx;
            border-radius: 16rpx 0rpx 0rpx 16rpx;
            z-index: 9999;
        }

        .goLottery {
            position: absolute;
            right: 12rpx;
            bottom: 328rpx;
            width: 166rpx;
            height: 166rpx;
            z-index: 100;

            .lottery-img {
                width: 166rpx;
                height: 166rpx;
            }

            .lottery-title {
                position: absolute;
                bottom: 23rpx;
                left: 50%;
                transform: translateX(-50%);
                font-family: MUJIFont2020;
                font-weight: 700;
                font-size: 22rpx;
                color: #3c3c43;
                line-height: 32rpx;
                text-align: center;
            }
        }

        .MyCoupon {
            position: absolute;
            right: 51rpx;
            top: 25rpx;
            padding: 30rpx;
            font-family: MUJIFont2020;
            font-weight: 500;
            font-size: 24rpx;
            color: #3c3c43;
            line-height: 40rpx;
            letter-spacing: 1rpx;
            text-align: center;
            font-style: normal;
            text-decoration-line: underline;
        }

        .prizeDraw-title1 {
            height: 40rpx;
            font-family: MUJIFont2020;
            font-weight: 500;
            font-size: 24rpx;
            line-height: 40rpx;
            letter-spacing: 1rpx;
            margin-top: 31rpx;
        }

        .title {
            margin-left: 62rpx;
        }

        .prizeDraw-title2 {
            height: 40rpx;
            font-weight: 300;
            font-size: 24rpx;
            line-height: 40rpx;
            letter-spacing: 1rpx;
        }

        .swiper-wrap {
            margin-top: 139rpx;
            position: relative;

            .tips {
                display: flex;
                align-items: center;
                justify-content: center;
                // margin-left: 73rpx;

                .tips-img {
                    width: 74rpx;
                    height: 76rpx;
                    margin-right: 21rpx;

                    image {
                        width: 100%;
                        height: 100%;
                    }
                }

                .prizeDraw-title3 {
                    font-weight: 500;
                    font-size: 36rpx;
                    color: #2e2e2e;
                    line-height: 52rpx;
                    letter-spacing: 1px;
                    text-align: center;
                }

                .prizeDraw-title4 {
                    font-weight: 700;
                    font-size: 56rpx;
                    color: #2e2e2e;
                    margin-top: 12rpx;
                    line-height: 72rpx;
                    letter-spacing: 1px;
                    text-align: center;
                }
            }

            .item-wrap {
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            .picture {
                box-sizing: border-box;
                width: 520rpx;
                height: 603rpx;
                // margin-right: 77rpx;
                // margin-left: 77rpx;
                // border-top: 1rpx dashed #3C3C43;
                // border-bottom: 1rpx dashed #3C3C43;
                margin-top: 90rpx;
                // padding: 2rpx 0;

                image {
                    width: 100%;
                    height: 100%;
                }

                // .slider-point-Left {
                //   width: 37rpx;
                //   height: 37rpx;
                //   position: absolute;
                //   top: 50%;
                //   left: 0rpx;
                //   transform: translateY(-50%);
                //   padding: 20rpx;

                //   .point_left {
                //     width: 0rpx;
                //     height: 0rpx;
                //     border-right: 20rpx solid #C8B49A;
                //     border-left: 20rpx solid transparent;
                //     border-top: 20rpx solid transparent;
                //     border-bottom: 20rpx solid transparent;
                //   }
                // }

                // .slider-point-Right {
                //   width: 37rpx;
                //   height: 37rpx;
                //   position: absolute;
                //   top: 50%;
                //   right: 0rpx;
                //   transform: translateY(-50%);
                //   padding: 20rpx;

                //   .point_right {
                //     width: 0rpx;
                //     height: 0rpx;
                //     border-left: 20rpx solid #C8B49A;
                //     border-right: 20rpx solid transparent;
                //     border-top: 20rpx solid transparent;
                //     border-bottom: 20rpx solid transparent;
                //   }
                // }
            }

            .bottom-tips {
                z-index: 222;
                font-family: MUJIFont2020;
                // height: 80rpx;
                font-weight: 400;
                font-size: 24rpx;
                color: #756453;
                line-height: 40rpx;
                letter-spacing: 1rpx;
                text-align: center;
                margin-top: 102rpx;

                .height-light {
                    font-family: MUJIFont2020;
                    font-weight: 700;
                    font-size: 24rpx;
                    line-height: 40rpx;
                    letter-spacing: 0%;
                    vertical-align: middle;
                }

                .text1 {
                    font-weight: 400;
                    font-size: 20rpx;
                    color: #756453;
                    line-height: 36rpx;
                    letter-spacing: 1rpx;
                    text-align: center;
                    margin-top: 12rpx;
                }

                .text1-MyCoupon {
                    font-family: MUJIFont2020;
                    font-weight: 400;
                    font-size: 24rpx;
                    line-height: 24rpx;
                    letter-spacing: 0%;
                    vertical-align: middle;
                    text-decoration: underline;
                    text-decoration-style: solid;
                    text-decoration-skip-ink: auto;
                    margin-top: 42rpx;
                    text {
                        display: inline-block;
                        text-decoration-line: underline;
                        // border-bottom: 1rpx solid #3C3C43;
                    }
                }
            }

            .bottom-tips1 {
                margin-top: 162rpx;
            }

            .slider-point-wrap {
                position: absolute;
                // bottom: 225rpx;
                bottom: 200rpx;
                left: 50%;
                transform: translateX(-50%);

                .slider-point {
                    display: flex;
                    align-items: center;

                    .slider-point-item {
                        width: 34rpx;
                        height: 2rpx;
                        background: #c8b49a;
                        // border-radius: 50%;
                        // margin-right: 20rpx;

                        &:nth-last-child(1) {
                            margin-right: 0rpx;
                        }
                    }

                    .active {
                        background: #756453;
                    }
                }

                .slider-tips {
                    font-family: MUJIFont2020;
                    font-weight: 400;
                    font-size: 18rpx;
                    color: #000000;
                    line-height: 25rpx;
                    text-align: center;
                    margin-top: 14rpx;
                }
            }
        }
    }

    .bottom-box {
        margin-top: 66rpx;
        margin-bottom: env(safe-area-inset-bottom);
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;

        .isShare {
            font-family: Source Han Sans CN;
            font-weight: 400;
            font-style: Regular;
            font-size: 10px;
            line-height: 18px;
            letter-spacing: 0%;
            text-align: center;
            vertical-align: middle;
            margin-top: 50rpx;
            color: #756453;
        }
    }

    .share_mask {
        width: 100vw;
        height: 100vh;
        background-color: rgba(0, 0, 0, 0.5);
        position: fixed;
        top: 0;
        left: 0;
    }

    .page-canvas {
        position: fixed;
        left: 0;
        top: 200vh;
        width: 497rpx; // canvas按照750的宽度绘制
        height: 1045rpx;
    }

    .page-canvasone {
        position: fixed;
        left: 0;
        top: 200vh;
        width: 662rpx;
        height: 1222rpx;
    }
}

.share-popup {
    .image {
        width: 662rpx;
        height: 1222rpx;
    }

    .clock-btn {
        margin-top: 40rpx;
        display: flex;
        // background-color: #ffffff;
        justify-content: space-between;
    }
}

.prize-once-again {
    width: 110rpx;
    height: 126rpx;
    position: absolute;
    right: 45rpx;
    bottom: -35rpx;
    image {
        width: 110rpx;
        height: 126rpx;
    }
}
