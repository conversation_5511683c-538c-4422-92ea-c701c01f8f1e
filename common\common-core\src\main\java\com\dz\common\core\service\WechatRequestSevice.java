package com.dz.common.core.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.core.constants.CommonConstants;
import com.dz.common.core.constants.NumConstants;
import com.dz.common.core.enums.WechatApiEnum;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.basic.MpConfigFeignClient;
import com.dz.common.core.fegin.basic.QywxConfigFeignClient;
import com.dz.common.core.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 微信接口封装
 * @Author: Handy
 * @Date: 2022/1/28 11:11
 */
@Component
@Slf4j
public class WechatRequestSevice {

    @Resource
    private QywxConfigFeignClient qywxConfigFeignClient;
    @Resource
    private MpConfigFeignClient mpConfigFeignClient;
    @Resource
    private RestTemplate restTemplate;

    /** 微信接口请求-无参 */
    public <T> T request(WechatApiEnum apiEnum,Long tenantId,Class<T> responseClass) {
        return request(apiEnum,tenantId,responseClass,null,null);
    }
    /** 微信接口请求-POST RequestBody入参*/
    public <T> T request(WechatApiEnum apiEnum,Long tenantId,Class<T> responseClass,Object postBody) {
        return request(apiEnum,tenantId,responseClass,postBody,null);
    }
    /** 微信接口请求-RequestParam入参*/
    public <T> T requestParam(WechatApiEnum apiEnum,Long tenantId,Class<T> responseClass,Object... param) {
        return request(apiEnum,tenantId,responseClass,null,null,param);
    }
    /** 微信接口请求-RequestBody + RequestParam入参*/
    public <T> T request(WechatApiEnum apiEnum,Long tenantId,Class<T> responseClass,Object postBody,String accessToken,Object... param) {
        JSONObject json = execute(apiEnum,tenantId,postBody,false,accessToken,param);
        if(apiEnum.isNoReturn()) {
            return null;
        }
        if(responseClass.isInstance(String.class)) {
            return (T) json.getString(apiEnum.getReturnKey());
        }
        else if(responseClass.isInstance(JSONObject.class)) {
            return (T) json;
        }
        else if(null != apiEnum.getReturnKey()) {
            return json.getObject(apiEnum.getReturnKey(),responseClass);
        }
        else {
            return json.toJavaObject(responseClass);
        }
    }
    /** 微信接口请求-RequestBody + RequestParam入参 返回List*/
    public <T> List<T> requestArray(WechatApiEnum apiEnum, Long tenantId, Class<T> responseClass, Object postBody, Object... param) {
        JSONObject json = execute(apiEnum,tenantId,postBody,false,null,param);
        String key = null == apiEnum.getReturnKey() ? "data" : apiEnum.getReturnKey();
        JSONArray array = json.getJSONArray(key);
        List<T> list = new ArrayList<>();
        if(null != array) {
            for (int i = 0; i < array.size(); i++) {
                list.add(array.getObject(i,responseClass));
            }
        }
        return list;
    }

    /**
     * 微信接口请求执行
     * @param apiEnum
     * @param tenantId
     * @param postBody
     * @param isRetry
     * @param accessToken 非必填不填会根据租户ID获取
     * @param param
     * @return
     */
    private JSONObject execute(WechatApiEnum apiEnum,Long tenantId,Object postBody,boolean isRetry,String accessToken,Object... param) {
        String url = apiEnum.getUrl();
        boolean hasToken = null != accessToken;
        if(apiEnum.isCheckToken() && !hasToken) {
            if(url.startsWith(CommonConstants.WX_QYAPI_PREFIX)) {
                accessToken = qywxConfigFeignClient.getQywxAccessToken(1,tenantId,isRetry).getData();
            }
            else {
                accessToken = mpConfigFeignClient.getMiniappAccessToken(tenantId,isRetry).getData();
            }
        }
        if(null != accessToken) {
            url += (url.contains("?") ? "&" : "?") + "access_token=" + accessToken;
        }
        String params = null == postBody ? "{}" : JSON.toJSONString(postBody);
        log.info("wechat api request url:{} postBody:{} urlVariables:{}",url,params,param);
        if(null != apiEnum.getUrlVariables() && null != param) {
            String urlPrams = String.format(apiEnum.getUrlVariables(),param);
            url += (url.contains("?") ? "&" : "?") + urlPrams;
        }
        HttpHeaders headers = new HttpHeaders();
        JSONObject json = null;
        String result = null;
        if(apiEnum.isPost()) {
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            HttpEntity<String> requestEntity = new HttpEntity<String>(params,  headers);
            if(apiEnum.isString()) {
                result = restTemplate.postForObject(url, requestEntity, String.class);
                json = JSON.parseObject(result,JSONObject.class);
            }
            else {
                json = restTemplate.postForObject(url, requestEntity, JSONObject.class);
            }
        }
        else {
            headers.setContentType(MediaType.parseMediaType("application/x-www-form-urlencoded; charset=UTF-8"));
            HttpEntity<String> requestEntity = new HttpEntity<String>(headers);
            if(apiEnum.isString()) {
                result = restTemplate.exchange(url, HttpMethod.GET,requestEntity, String.class).getBody();
                json = JSON.parseObject(result,JSONObject.class);
            }
            else {
                json = restTemplate.exchange(url, HttpMethod.GET,requestEntity, JSONObject.class).getBody();
            }
        }
        log.info("{} result:{}",(StringUtils.isBlank(apiEnum.getDesc()) ? url : apiEnum.getDesc()), CommonUtils.jsonStr(json));
        if(apiEnum.isString() && null != json) {
            return json;
        }
        if (null != json && "ok".equals(json.getString("errmsg"))) {
            return json;
        }
        String message = null == json ? apiEnum.getDesc()+"出错" : apiEnum.getDesc()+":"+json.getString("errmsg");
        log.error(message);
        int errcode = NumConstants.MAX_VALUE;
        if(Objects.nonNull(json)){
            errcode = json.getIntValue("errcode");
        }
        /** token过期重试 */
        if(!isRetry && (errcode == 40001 || errcode == 42001 || errcode == 40014 || errcode == 41001) && !hasToken) {
            return execute(apiEnum,tenantId,postBody,true,null,param);
        }
        if(errcode == 43101) {
            return json;
        }
        throw new BusinessException(ErrorCode.INTERNAL_ERROR, message);
    }

    public static void main(String[] args) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        JSONObject params = new JSONObject();
        params.put("limit",1000);
        HttpEntity<String> requestEntity = new HttpEntity<String>(JSON.toJSONString(params),  headers);
        JSONObject json = new RestTemplate().postForObject("https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=Qwy1GslkElz1BFKBfMla_qASboSuM7oOVAV86BqMpabCwG1QDqJly3030XKAUSDrjN7DnQEtlfdLIutZb7htBPiNlOyhL-uJiN8k6d25Npe3w4UUkkM1eaza5hYZ4s4Rxwc5-rkusPIinPx-y3faaAkqV2bKYGGsaN-IZxRJ7FWTbelskXEtHSvPUMWDPAslIH8356GrY-XrsGG2qonKjg&userid=DaXiong", requestEntity, JSONObject.class);
    }
}
