package com.dz.ms.user.dto.dkeyam.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.io.Serializable;

/**
 * 用户认证策略入参
 * @author: fei
 * @date: 2025/01/22 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StrongPasswordRequirementParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("站点名称")
    private String tenantName;

    @ApiModelProperty("认证设备名称 需要在认证系统上创建认证设备，设备类型为：API调用")
    private String accessServerName;

    @ApiModelProperty("认证设备共享秘钥")
    private String sharedSecret;

    @ApiModelProperty("用户登录名")
    private String loginName;
    
}
