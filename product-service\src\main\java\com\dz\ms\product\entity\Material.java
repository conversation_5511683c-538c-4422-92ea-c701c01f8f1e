package com.dz.ms.product.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 商品素材表
 *
 * @author: LiinNs
 * @date: 2024/11/27 15:17
 */
@Getter
@Setter
@NoArgsConstructor
@Table("商品素材表")
@TableName(value = "material")
public class Material implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "商品素材id")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = false, comment = "类型 1图片 2视频")
    private Integer type;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "商品id 0未绑定", isIndex = true)
    private Long productId;
    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = true, comment = "商品编号")
    private String productCode;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = false, comment = "1场景图 2橱窗图 3兑换须知图 4商品详情图 5使用说明图")
    private Integer usedType;
    @Columns(type = ColumnType.VARCHAR, length = 600, isNull = true, comment = "图片地址")
    private String imgUrl;
    @Columns(type = ColumnType.VARCHAR, length = 600, isNull = true, comment = "图片名称", isIndex = true)
    private String imgName;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = false, defaultValue = "0", comment = "是否删除 0未删除 1已删除")
    @TableLogic
    private Integer isDeleted;

    public Material(Long id, Integer type, Long productId, String productCode, Integer usedType, String imgUrl, String imgName) {
        this.id = id;
        this.type = type;
        this.productId = productId;
        this.productCode = productCode;
        this.usedType = usedType;
        this.imgUrl = imgUrl;
        this.imgName = imgName;
    }

}
