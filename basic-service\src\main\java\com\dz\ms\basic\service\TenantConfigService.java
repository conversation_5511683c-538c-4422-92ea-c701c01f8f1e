package com.dz.ms.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.core.dto.basic.TenantConfigDTO;
import com.dz.ms.basic.entity.TenantConfig;

/**
 * 租户设置接口
 * @author: Handy
 * @date:   2022/08/30 23:04
 */
public interface TenantConfigService extends IService<TenantConfig> {

    /**
     * 根据ID查询租户设置
     * @param id
     * @return
     */
    public TenantConfigDTO getTenantConfigById(Long id);

    /**
     * 保存租户设置
     * @param param
     */
    public void saveTenantConfig(TenantConfigDTO param);

}
