
package com.dz.ms.sales.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/30
 */
@Data
public class CampaignUserDTO {


    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("活动标识")
    private String campaignCode;

    @ApiModelProperty("用户类型 1购买指定商品的用户 2其他导入的用户 3报名中奖的用户")
    private Integer type;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("参与人数")
    private Integer num;

}
