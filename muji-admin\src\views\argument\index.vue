<template>

  <layout>
    <template v-slot:header>
      <searchForm :formParams="formParams" @cancel="resetData" @ok="refreshData" :disabled="!$hasPermission('argument:search')">
        <a-form-item name="channelName">
          <a-input placeholder="渠道名称" allow-clear v-model:value="formParams.channelName" allowClear @keyup.enter="refreshData"></a-input>
        </a-form-item>

      </searchForm>
    </template>
    <template v-slot:topRight>
      <a-space>
        <!-- :disabled="!$hasPermission('open:leaveUser:synchLeaveUser')" 权限标识 -->
        <a-button type="primary" :disabled="!$hasPermission('argument:add')" @click="addChang">新建渠道</a-button>

      </a-space>

    </template>
    <template v-slot="{ height }">
      <a-table :indentSize="20" row-key="id" :scroll="{ scrollToFirstRowOnChange: true, y: height - 88, x: '100%' }" :dataSource="dataSource" :columns="tableHeader" :pagination="pagination" :loading="loading" @change="loadData">
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'index'">
            {{ (current - 1) * pageSize + 1 + index }}
          </template>

          <template v-if="column.key === 'action'">
            <a-button type="link" :disabled="!$hasPermission('argument:edit') || !record.twoId" @click="EditRole(record)">编辑</a-button>
            <a-divider type="vertical" />
            <a-popconfirm title="是否确定删除该数据吗？" :disabled="!$hasPermission('argument:del')" @confirm="handleDelete(record)">
              <a-button :disabled="!$hasPermission('argument:del')" type="link">删除</a-button>
            </a-popconfirm>
          </template>

        </template>
      </a-table>
    </template>

  </layout>
  <addArg :visible="visible" ref="addArgRef" @okAdd="okAdd" @ok="updateList" :item="addItem" @cancel="visible = false" :id="id" :type="type" />
</template>
<script setup>
import addArg from './components/addArg.vue';
import { useRoute, useRouter } from 'vue-router'
import { usePagination, useRequest, } from 'vue-request';
import { promotion_channelList, promotion_channelDel, } from '@/http/index.js'
import { message, Modal } from "ant-design-vue";
// 分页参数
let pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ["bottomLeft"],
  };
});
// 表格分页数据获取
const total = ref(0)
const addArgRef = ref(null)

const { formParams, tableHeader, visible, id, type, addItem } = toRefs(reactive({


  id: '',
  type: 0,// 0-新增 1-编辑  2-查看
  visible: false,
  formParams: {
    channelName: ''
  },
  addItem: {},
  tableHeader: [{
    title: '序号',
    key: 'index',
    align: 'center',
    width: 80
  },
  {
    title: '一级渠道名称',
    dataIndex: 'oneChannelName',
    width: 180,
    align: 'center',
  },
  {
    title: '一级渠道参数',
    dataIndex: 'oneChannelParam',
    align: 'center',
    ellipsis: true,
    width: 180,
  },
  {
    title: '二级渠道名称',
    dataIndex: 'twoChannelName',
    width: 180,
    align: 'center',
    customRender: (row) => {
      // console.log(row);

      return row.text || '--'; // 如果数据为空，则显示 '----'
    }
  },
  {
    title: '二级渠道参数',
    dataIndex: 'twoChannelParam',
    align: 'center',
    ellipsis: true,
    width: 180,
    customRender: (row) => {
      // console.log(row);

      return row.text || '--'; // 如果数据为空，则显示 '----'
    }
  },
  {
    title: '创建人',
    dataIndex: 'creatorName',
    align: 'center',
    ellipsis: true,
    width: 180,
  },

  {
    title: '创建时间',
    dataIndex: 'createTime',
    align: 'center',
    ellipsis: true,
    width: 180,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    align: 'center',
    width: 150,
    fixed: 'right'
  }]
})
);
const { data: dataSource, run, loading, pageSize, current, refresh } = usePagination((param) => {
  // 这里修改参数
  return promotion_channelList({ ...param, ...formParams.value })
},
  {
    manual: false, // 修改为false,让其自动执行
    pagination: {
      currentKey: 'pageNum',  // 通过该值指定接口 当前页数 参数的属性值
      pageSizeKey: 'pageSize',// 通过该值指定接口 每页获取条数 参数的属性值
      totalKey: 'data.data.count',  // 指定 data 中 total 属性的路径
    },
    formatResult: res => { // 返回数据格式化
      total.value = res.data.count
      return res.data.list
    }
  });
function okAdd() {
  resetData();
}
function updateList(value) {
  visible.value = false;
  // 新增 重置搜索数据
  if (!value) {
    resetData();
  } else {
    // 查看、编辑 不需要清空搜索数据 直接刷新
    refresh();
  }
}
function EditRole(record) {
  visible.value = true
  type.value = 1
  id.value = record.twoId
  addItem.value = {
    twoId: record.twoId,
    channelName: record.twoChannelName,
    channelParam: record.twoChannelParam,
    parentId: record.oneId
  }
  //   addArgRef.value.addParams.channelName = record.channelName
  //   addArgRef.value.addParams.channelParam = record.channelParam
  //   addArgRef.value.addParams.id = record.id
  // console.log(record, id.value, addItem.value, addArgRef.value.addParams);

}
//删除
const handleDelete = (record) => {

  promotion_channelDel({ id: record.twoId || record.oneId }).then(res => {
    if (res.code === 0) {
      message.success('删除成功')
      resetData()
    }
  })
}
// 加载数据
const loadData = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}


// 刷新数据
const refreshData = () => {
  run({ pageNum: 1, pageSize: pageSize.value })
};

// 重置数据
const resetData = () => {
  formParams.value = {
    channelName: '',
  }
  refreshData()
}
function addChang() {
  visible.value = true
  type.value = 0
  id.value = ''
  addItem.value = {}
}
</script>
