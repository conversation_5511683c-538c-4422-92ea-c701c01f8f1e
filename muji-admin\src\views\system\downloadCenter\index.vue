<template>

  <layout>
    <template v-slot:header>
      <searchForm :formParams="formParams" @cancel="resetData" @ok="refreshData" :disabled="!$hasPermission('downloadCenter:search')">
        <a-form-item name="type">
          <a-select ref="select" v-model:value="formParams.type" allowClear :disabled="disabled" :options="TypeOptions" :fieldNames="{ label: 'bizModuleName', value: 'type' }" optionFilterProp="bizModuleName" showSearch placeholder="业务模块"></a-select>
        </a-form-item>
        <a-form-item name="createTime" label="创建时间">
          <a-range-picker v-model:value="formParams.createTime" :placeholder="['开始时间', '结束时间']" :presets="$rangePresets" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
        </a-form-item>
        <a-form-item name="fileName">
          <a-input placeholder="报表名称" allow-clear v-model:value="formParams.fileName" allowClear @keyup.enter="refreshData"></a-input>
        </a-form-item>
      </searchForm>
    </template>
    <template v-slot:topRight>
      <a-space>
        <!-- :disabled="!$hasPermission('open:leaveUser:synchLeaveUser')" 权限标识 -->

      </a-space>

    </template>
    <template v-slot="{ height }">
      <a-table :indentSize="20" row-key="id" :scroll="{ scrollToFirstRowOnChange: true, y: height - 88, x: '100%' }" :dataSource="dataSource" :columns="tableHeader" :pagination="pagination" :loading="loading" @change="loadData">
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'index'">
            {{ (current - 1) * pageSize + 1 + index }}
          </template>

          <template v-if="column.key === 'action'">
            <a-button type="link" v-if="record.state === 1" :disabled="!$hasPermission('downloadCenter:download')" @click="EditRole(record)">下载报表</a-button>
            <a-divider type="vertical" v-if="record.state === 1" />
            <a-popconfirm title="是否确定删除该数据吗？" :disabled="!$hasPermission('downloadCenter:del')" @confirm="handleDelete(record)">
              <a-button :disabled="!$hasPermission('downloadCenter:del')" type="link">删除</a-button>
            </a-popconfirm>
          </template>

        </template>
      </a-table>
    </template>

  </layout>

</template>
<script setup>
import { cloneDeep } from "lodash";
import { useRoute, useRouter } from 'vue-router'
import { usePagination, useRequest, } from 'vue-request';
import { downloadTaskList, downloadTaskdelete, downloadTaskcategory, downloadTaskmark } from '@/http/index.js'
import { message, Modal } from "ant-design-vue";
// 分页参数
let pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ["bottomLeft"],
  };
});
// 表格分页数据获取
const total = ref(0)


const { formParams, tableHeader, TypeOptions } = toRefs(reactive({


  TypeOptions: [],
  formParams: {

  },

  tableHeader: [{
    title: '序号',
    key: 'index',
    align: 'center',
    width: 80
  },
  {
    title: '报表名称',
    dataIndex: 'fileName',
    // width: 180,
    align: 'center',
  },
  {
    title: '业务模块',
    dataIndex: 'moduleName',
    align: 'center',
    ellipsis: true
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    align: 'center',
    ellipsis: true,
    customRender: (row) => {

      return row.text ? row.text : '--'
    }
  },
  {
    title: '任务状态',
    dataIndex: 'state',
    align: 'center',
    ellipsis: true, customRender: (row) => {
      let obj = {
        0: '生成中',
        1: '已完成',
        2: '已失败',
        3: '已失效',
      }

      return row.text ? obj[row.text] : '--'
    }
  },


  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    align: 'center',
    width: 150,
    fixed: 'right'
  }]
})
);

downloadTaskcategory().then(res => {
  TypeOptions.value = res.data
})
const getParams = () => {
  // 拷贝一份 然后处理数据
  let params = cloneDeep(formParams.value);
  params.beginTime = params.createTime
    ? params.createTime[0] + " 00:00:00"
    : null;
  params.endTime = params.createTime
    ? params.createTime[1] + " 23:59:59"
    : null;
  delete params.createTime;
  return params;
};
const { data: dataSource, run, loading, pageSize, current, refresh } = usePagination((param) => {
  // 这里修改参数

  return downloadTaskList({ ...param, ...getParams() })
},
  {
    manual: false, // 修改为false,让其自动执行
    pagination: {
      currentKey: 'pageNum',  // 通过该值指定接口 当前页数 参数的属性值
      pageSizeKey: 'pageSize',// 通过该值指定接口 每页获取条数 参数的属性值
      totalKey: 'data.data.count',  // 指定 data 中 total 属性的路径
    },
    formatResult: res => { // 返回数据格式化
      total.value = res.data.count
      return res.data.list
    }
  });


function EditRole({ sourceUrl, state, id }) {
  downloadTaskmark({ id }).then(() => {
    window.open(sourceUrl, '_blank')
    resetData()
  })

}
//删除
const handleDelete = (record) => {
  downloadTaskdelete({ id: record.id }).then(res => {
    if (res.code === 0) {
      message.success('删除成功')
      resetData()
    }
  })
}
// 加载数据
const loadData = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}


// 刷新数据
const refreshData = () => {
  run({ pageNum: 1, pageSize: pageSize.value })
};

// 重置数据
const resetData = () => {
  formParams.value = {
    roleName: '',
  }
  refreshData()
}

</script>
