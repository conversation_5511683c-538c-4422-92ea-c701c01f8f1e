package com.dz.ms.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.user.dto.SysUserRoleDTO;
import com.dz.ms.user.entity.SysRole;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 系统角色Mapper
 * @author: Handy
 * @date:   2022/2/4 23:04
 */
@Repository
public interface SysRoleMapper extends BaseMapper<SysRole> {

    /**
     * 根据系统用户ID列表获取绑定角色列表
     * @param ids
     * @return
     */
    List<SysUserRoleDTO> getSysRoleByUserIds(@Param("ids") List<Long> ids);

}
