.basic-search {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  width: 100%;
  font-size: 24rpx;
  position: relative;
  height: 100%;


  .input-wrap {
    position: relative;
    display: flex;
    flex: 1;
    box-sizing: border-box;
    align-items: center;
    z-index: 100;
  }

  .left-icon {
    height: 48rpx;
    width: 48rpx;
    margin-right: 10rpx;
  }

  .search-input {
    flex: 1;
    // background-color: green;
  }



  .clear-box {
    right: 20rpx;
    z-index: 99999;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: row-reverse;
    width: 100rpx;
    height: 100%;

    .clear-icon {
      width: 32rpx;
      height: 32rpx;
      padding: 0;
      font-weight: bold;
      font-size: 16rpx;
    }
  }

}
