<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.basic.mapper.DownloadHeaderMapper">


    <resultMap id="BaseResultMap" type="com.dz.ms.basic.entity.DownloadHeader">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="report_code" jdbcType="VARCHAR" property="reportCode"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="replace" jdbcType="VARCHAR" property="replace"/>
        <result column="format" jdbcType="VARCHAR" property="format"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, report_code, code, `name`, sort, `replace`, `format`, `tenant_id`
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from download_header
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from download_header
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.dz.ms.basic.entity.DownloadHeader"
            useGeneratedKeys="true">
        insert into download_header (report_code, code, `name`, replace, format,
                                     sort, tenant_id)
        values (#{reportCode,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},
                #{replace,jdbcType=VARCHAR}, #{format,jdbcType=VARCHAR},
                #{sort,jdbcType=INTEGER}, #{tenantId,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.dz.ms.basic.entity.DownloadHeader"
            useGeneratedKeys="true">
        insert into download_header
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="reportCode != null">
                report_code,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="replace != null">
                `replace`,
            </if>
            <if test="format != null">
                `format`,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="reportCode != null">
                #{reportCode,jdbcType=VARCHAR},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="replace != null">
                #{replace,jdbcType=VARCHAR},
            </if>
            <if test="format != null">
                #{format,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=INTEGER},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.dz.ms.basic.entity.DownloadHeader">
        update download_header
        <set>
            <if test="reportCode != null">
                report_code = #{reportCode,jdbcType=VARCHAR},
            </if>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="replace != null">
                `replace` = #{replace,jdbcType=VARCHAR},
            </if>
            <if test="format != null">
                `format` = #{format,jdbcType=VARCHAR},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.dz.ms.basic.entity.DownloadHeader">
        update download_header
        set report_code = #{reportCode,jdbcType=VARCHAR},
            code        = #{code,jdbcType=VARCHAR},
            `name`      = #{name,jdbcType=VARCHAR},
            `replace`   = #{replace,jdbcType=VARCHAR},
            `format`    = #{format,jdbcType=VARCHAR},
            sort        = #{sort,jdbcType=INTEGER},
            tenant_id   = #{tenantId,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByReportCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from download_header where report_code = #{reportCode} and tenant_id = #{tenantId}
    </select>

</mapper>