package com.dz.common.core.fegin.user;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.user.MemberInfoDTO;
import com.dz.common.core.dto.user.MyPointsDTO;
import com.dz.common.core.dto.user.UserSimpleDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 用户信息FeginClient
 *
 * @Author: Handy
 * @Date: 2022/2/3 23:11
 */
@FeignClient(name = ServiceConstant.USER_SERVICE_NAME, contextId = "UserInfoFeginClient")
public interface UserInfoFeginClient {

    /**
     * 获取用户会员信息带缓存
     * @param uid
     * @return
     */
//    @GetMapping(value = "/member_info_cach")
//    public Result<MemberInfoDTO> getUserMemberInfoCach(@RequestParam(value = "uid",required = false) Long uid);

    /**
     * 获取用户简要信息
     *
     * @param uid
     * @return
     */
    @GetMapping(value = "/user/simple_info")
    public Result<UserSimpleDTO> getUserSimpleInfo(@RequestParam(value = "uid", required = false) Long uid);

    /**
     * 从db根据uid获取用户简要信息
     */
    @ApiOperation("从db根据uid获取用户简要信息")
    @GetMapping(value = "/user/db_simple_info")
    Result<UserSimpleDTO> getDbUserSimpleInfo(@RequestParam(value = "uid") Long uid);

    /**
     * 更新首购任务状态
     * @param firstOrderStatus
     * @return
     */
    @GetMapping(value = "/user/info/update/order/status")
    public Result<Object> updateFirstOrderStatus(@RequestParam("firstOrderStatus") Integer firstOrderStatus,
                                                 @RequestParam(value = "firstOrderTaskId", required = false) Long firstOrderTaskId);

    /**
     * 从db根据List<uid>获取用户简要信息
     */
    @ApiOperation("从db根据List<uid>获取用户简要信息")
    @PostMapping(value = "/user/db_simple_info_list")
    Result<List<UserSimpleDTO>> getDbUserSimpleInfoList(@RequestBody List<Long> uidList);

    /**
     * 分页查询所有用户信息
     *
     * @return
     */
    @GetMapping(value = "/user/simple_info/list")
    public Result<PageInfo<UserSimpleDTO>> getUserSimpleInfoList(@RequestParam(value = "pageNum", required = false) Integer pageNum, @RequestParam(value = "pageSize", required = false) Integer pageSize);

    /**
     * 根据用户ID列表获取用户openid列表
     *
     * @param ids
     * @param tenantId
     * @return
     */
    @PostMapping(value = "/user/openid_byids")
    public Result<List<String>> getUserOpenidByIds(@RequestBody List<Long> ids, @RequestParam("tenantId") Long tenantId);

    /**
     * 获取当前用户会员信息
     */
    @GetMapping(value = "/current_member_info")
    Result<MemberInfoDTO> getCurrentUserMemberInfo();

    /**
     * 当前登录用户积分信息
     */
    @GetMapping(value = "/user/my/points")
    Result<MyPointsDTO> myPoints();

    /**
     * 根据用户昵称获取用户ID列表
     *
     * @param userName
     * @return
     */
    @GetMapping(value = "/user/id_list_by_name")
    Result<List<Long>> getUserIdListByName(@RequestParam("userName") String userName);

    /**
     * 根据用户openid获取用户用户列表
     *
     * @param openids
     * @return
     */
    @PostMapping(value = "/user/id_list_by_openid")
    Result<List<UserSimpleDTO>> getUserIdListByOpenids(@RequestBody List<String> openids, @RequestParam("tenantId") Long tenantId);

    /**
     * 获取当前用户简要信息（缓存）
     *
     * @return
     */
    @GetMapping(value = "/user/current_simple_info")
    public Result<UserSimpleDTO> getCurrentUserSimpleInfo();

    /**
     * 从db根据memberCode获取用户简要信息
     * @param memberCode
     * @return
     */
    @PostMapping(value = "/user/info/by/member")
    public Result<List<UserSimpleDTO>> getDbUserSimpleInfo(@RequestBody List<String> memberCode);

}

