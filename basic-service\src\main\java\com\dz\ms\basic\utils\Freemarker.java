package com.dz.ms.basic.utils;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.io.Writer;
import java.util.Locale;
import java.util.Map;

/**
 *
 * 创建人：FH 创建时间：2015年2月8日
 * @version
 */
@Slf4j
public class Freemarker {

	/**
	 * 打印到控制台(测试用)
	 *  @param ftlName
	 */
	public static void print(String ftlName, Map<String,Object> root, String ftlPath) throws Exception{
		try {
			Template temp = getTemplate(ftlName, ftlPath);
			//通过Template可以将模板文件输出到相应的流
			if(null != temp){
				temp.process(root, new PrintWriter(System.out));
			}
		} catch (TemplateException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	/**
	 * 输出到输出到文件
	 * @param ftlName   ftl文件名
	 * @param root		传入的map
	 * @param outFile	输出后的文件全部路径
	 * @param filePath	输出前的文件上部路径
	 */
	public static void printFile(String ftlName, Map<String,Object> root, String outFile, String filePath, String ftlPath) throws Exception{
		Writer out=null;
		try {
			File file = new File(filePath + outFile);
			if(!file.getParentFile().exists()){				//判断有没有父路径，就是判断文件整个路径是否存在
				file.getParentFile().mkdirs();				//不存在就全部创建
			}
			out = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file), "utf-8"));
			Template template = getTemplate(ftlName, ftlPath);
			if(null != template){
				template.process(root, out);					//模版输出
			}
			out.flush();
			out.close();
		} catch (Exception e) {
			if (null != out){
				out.close();
			}
		} finally {
			if (null != out){
				out.close();
			}
		}
	}

	/**
	 * 通过文件名加载模版
	 * @param ftlName
	 */
	public static Template getTemplate(String ftlName, String ftlPath) throws Exception{
		OutputStream outStream = null;
		try {
			@SuppressWarnings("deprecation")
			Configuration cfg = new Configuration();  												//通过Freemaker的Configuration读取相应的ftl
			cfg.setEncoding(Locale.CHINA, "utf-8");
			File file = new File(ftlPath+"/"+ftlName);
			if(!file.exists()){
				if(!file.getParentFile().exists()){
					file.getParentFile().mkdirs();
				}
				ClassPathResource resource = new ClassPathResource("codeftl/"+ftlName);
				InputStream inputStream = resource.getInputStream();
				byte[] buff = new byte[inputStream.available()];
				int read=inputStream.read(buff);
				log.info("通过文件名加载模版..."+read);
				outStream = new FileOutputStream(file);
				outStream.write(buff);
			}
			cfg.setDirectoryForTemplateLoading(new File(ftlPath));		//设定去哪里读取相应的ftl模板文件
			Template temp = cfg.getTemplate(ftlName);												//在模板文件目录中找到名称为name的文件
			return temp;
		} catch (IOException e) {
			if (null != outStream){
				outStream.close();
			}
		}finally {
			if (null != outStream){
				outStream.close();
			}
		}
		return null;
	}
}
