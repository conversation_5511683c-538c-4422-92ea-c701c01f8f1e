<!--MiKangCampaign/pages/clock/clock.wxml-->
<my-page overallModal="{{overallModal}}" catch:overallModalConfirm="overallModalConfirm">
  <view class="page-container" style="background-image:url({{$cdn}}/MiKangCampaign/dakaBack.jpg);">
    <custom-header isShare="{{true}}" background=" transparent" type="{{1}}" isBackHidden="{{ isBackHidden }}" color="white" />
    <view class="page-top-clock">
      <image class="page-top-back" src="{{$cdn}}/MiKangCampaign/dakaBackTop.png" mode="widthFix" />
      <view class="back_title">
        <block wx:if="{{prizeDraw}}">
          <view><text>{{"您已完成五天打卡"}}</text></view>
          <view class="title-view"><text>{{"解锁惊喜好礼"}}</text></view>
        </block>
        <block wx:else>
          <view><text>{{"完成五天打卡"}}</text></view>
          <view class="title-view"><text>{{"解锁惊喜好礼"}}</text></view>
        </block>
      </view>
      <view class="clockList">
        <view class="clockList-back" style="background-image: url({{$cdn}}/MiKangCampaign/Subtract.png);"></view>
        <view class="clockList-in">
          <view class="clock-text1">您已完成<text> {{completeTimes}} </text>天打卡</view>
          <view class="clock-text1 clock-text2" wx:if="{{completeTimes<5}}">再打卡 <text> {{surplusTimes}} </text> 天可抽取惊喜礼品</view>
          <view class="clock-text1 clock-text2" wx:else>获得<text> 1 </text>次惊喜礼品抽奖机会</view>
          <view class="clockList-wrap">
            <view wx:for="{{clockList}}" wx:key="{{index}}" class="clockItem {{item.state==1? 'isSingIn':''}} {{item.state==2?'notSingIn':''}} {{(item.days == days || (days>=5&&item.days>=5)) && !isSignIn?'today':''}}" bind:tap="buqian" data-query="{{item}}">
              <!-- item.days==days && !isSignIn 判断item.days 是否是当天 并且未打卡 显示today样式 -->
              <block wx:if="{{item.days!=5}}">
                <view class="item">
                  <image wx:if="{{item.state==1&&item.days == days}}" class="item-img" src="{{$cdn}}/MiKangCampaign/today-icon.png"> </image>
                  <image wx:elif="{{item.state==1}}" class="item-img" src="{{$cdn}}/MiKangCampaign/wi-icon.png"> </image>
                  <image wx:elif="{{item.days == days}}" class="item-img" src="{{$cdn}}/MiKangCampaign/today-icon.png"> </image>
                  <image wx:elif="{{item.state==2||item.state==0}}" class="item-img" src="{{$cdn}}/MiKangCampaign/tr-icon.png"> </image>
                  <view class="daysText {{item.days==days?'today-daysText':''}}"> {{item.state==2 ? item.days :item.days==days ? '今日': item.days}}</view>
                </view>
              </block>
              <block wx:if="{{item.days==5}}">
                <view class="item item5">
                  <image class="item-img" src="{{$cdn}}/MiKangCampaign/{{item.state==1 ? 'gift-w-icon.png' :item.days == days ? 'today-icon.png' : 'gift-tr-icon.png' }}"> </image>
                  <view class="daysText"> {{item.state==1&&item.days!=5 ? '' :item.days==days &&item.state!=1? '今日': ''}}</view>
                </view>
              </block>

              <view class="icon-wrap">
                <block wx:if="{{item.state == 1}}">
                  <view class="icon-font  icon-state1  ">
                    <image src="{{$cdn}}/MiKangCampaign/Icon-state1.png" mode="" />
                  </view>
                  <view class="{{index!=clockList.length-1&&clockList[index+1].state==1?'line':''}}">
                  </view>
                </block>
                <block wx:if="{{item.state == 2}}">
                  <view class="buka">补卡</view>
                </block>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <scroll-view class="clock-record" scroll-into-view="{{clickId}}" enhanced="{{ true }}" bounces="{{ false }}" show-scrollbar="{{ false }}" scroll-y style="background-image: url({{$cdn}}/MiKangCampaign/clock-record.jpg);">
      <view class="record_wrap" id="record-title">
        <view class="record-title">
          打卡记录
        </view>
        <view class="record" wx:if="{{recordList.length > 0}}">
          <block wx:for="{{recordList}}">
            <view class="record-item" data-index="{{index}}" bind:tap="detail" wx:if="{{!item.supplementTime&& item.state==1 }}">
              <view class="Day">
                <image src="{{$cdn}}/MiKangCampaign/days/day{{item.days}}.png" mode="widthFix" />
              </view>
              <view wx:if="{{item.days!=5}}" class="record-item-overall">
                <view class="title">整体满意度</view>
                <view class="start">
                  <view style="margin-bottom: -6rpx;">
                    <van-rate disabled value="{{item.satisfaction}}" bind:change="changeRate" data-index="{{index}}" size="32rpx" gutter="22rpx" count="{{5}}" icon="{{$cdn}}/MiKangCampaign/start2.png" void-icon="{{$cdn}}/MiKangCampaign/start1.png"></van-rate>
                  </view>
                  <view class="detail">详情</view>
                </view>

              </view>
              <view wx:else class="record-item-overall">
                <view class="title">整体满意度</view>
                <view class="start">
                  <view style="margin-bottom: -6rpx;">
                    <van-rate disabled value="{{item.score}}" bind:change="changeRate" data-index="{{index}}" size="32rpx" gutter="24rpx" count="{{5}}" icon="{{$cdn}}/MiKangCampaign/start2.png" void-icon="{{$cdn}}/MiKangCampaign/start1.png"></van-rate>
                  </view>
                  <view class="detail">详情</view>
                </view>

              </view>
            </view>
          </block>
        </view>
        <view class="noData" wx:if="{{recordList.length ==0}}">暂无打卡记录</view>
      </view>
    </scroll-view>
    <view class="clock-btn">
      <!-- 打卡 -->
      <view class="basic-button">
        <basic-button wx:if="{{!prizeDraw}}" width="{{670}}" size="large" bind:click="submit" disabled="{{isSignIn}}">
          {{btnTitle}}
        </basic-button>
        <!-- 抽奖 ClickPrizeDraw -->
        <basic-button class="basic-button" wx:if="{{prizeDraw}}" width="{{670}}" size="large" bind:click="Clickhaibao" disabled="{{disabled}}">
          <view class="btn" style="display: flex;justify-content: center;align-items: center;">
            <view>查看体验报告</view>
          </view>
        </basic-button>
      </view>

    </view>
    <view class="box_bottom" wx:if="{{prizeDraw}}">
      <image class="box-right" wx:if="{{surplusCount>0}}" src="{{$cdn}}/MiKangCampaign/prizeDraw.png" bind:tap="goPrizeDraw" mode="widthFix" />
      <image class="box-right" wx:else src="{{$cdn}}/MiKangCampaign/prizeDraw1.png" bind:tap="goPrizeDraw1" mode="widthFix" />
    </view>
    <view bind:tap="onTapRule" class="page-rule">活动规则</view>
    <clockPopup num="{{surplusRepairSignInTimes}}" isShow="{{showContact}}" bindclose="closeContact" />
    <!-- 活动规则 -->
    <active-rules isShow="{{showRules}}" bindclose="closeRules" />
    <!-- 分享指引 -->
    <guidePopup showGuide="{{showContact1}}" bind:finish="closeGuide" />
    <!-- 打卡完成 提示弹框 -->
    <Popup isShow="{{showPopup}}" info="{{infoLottery}}" bindclose="closePopup"></Popup>
  </view>
</my-page>