package com.dz.ms.basic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.annotation.CacheEvict;
import com.dz.common.core.annotation.Cacheable;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.common.core.dto.basic.PopupConfigDTO;
import com.dz.ms.basic.constants.CacheKeys;
import com.dz.ms.basic.entity.PopupConfig;
import com.dz.ms.basic.mapper.PopupConfigMapper;
import com.dz.ms.basic.service.PopupConfigService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * 弹窗配置
 * @author: Handy
 * @date:   2023/11/20 20:40
 */
@Service
public class PopupConfigServiceImpl extends ServiceImpl<PopupConfigMapper,PopupConfig> implements PopupConfigService {

	@Resource
    private PopupConfigMapper popupConfigMapper;

	/**
     * 分页查询弹窗配置
     * @param param
     * @return PageInfo<PopupConfigDTO>
     */
    @Override
    public PageInfo<PopupConfigDTO> getPopupConfigList(PopupConfigDTO param) {
        /*PopupConfig popupConfig = BeanCopierUtils.convertObjectTrim(param,PopupConfig.class);
        IPage<PopupConfig> page = popupConfigMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), new QueryWrapper<>(popupConfig));
        return new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),BeanCopierUtils.convertList(page.getRecords(), PopupConfigDTO.class));*/
        return null;
    }

    /**
     * 根据ID查询弹窗配置
     * @param id
     * @return PopupConfigDTO
     */
    @Override
    public PopupConfigDTO getPopupConfigById(Long id) {
        PopupConfig popupConfig = popupConfigMapper.selectById(id);
        return BeanCopierUtils.convertObject(popupConfig,PopupConfigDTO.class);
    }

    /**
     * 保存弹窗配置
     * @param param
     * @return Long
     */
    @Override
    public Long savePopupConfig(PopupConfigDTO param) {
        PopupConfig popupConfig = new PopupConfig(param.getId(), param.getPopupType(), param.getPopupImg(), param.getJumpLink());
        if(ParamUtils.isNullOr0Long(popupConfig.getId())) {
            popupConfigMapper.insert(popupConfig);
        }
        else {
            popupConfigMapper.updateById(popupConfig);
        }
        return popupConfig.getId();
    }

    /**
     * 根据ID删除弹窗配置
     * @param param
     */
    @Override
    public void deletePopupConfigById(IdCodeDTO param) {
        popupConfigMapper.deleteById(param.getId());
    }

    /**
     * 根据类型查询弹窗配置
     *
     * @param type
     * @param tenantId
     * @return
     */
    @Override
    @Cacheable(prefix = CacheKeys.POPUP_CONFIG, key = "'#tenantId'+'#type'")
    public PopupConfigDTO getPopupConfigByType(Integer type, Long tenantId) {
        PopupConfig popupConfig = popupConfigMapper.selectOne(new LambdaQueryWrapper<PopupConfig>().eq(PopupConfig::getPopupType,type).last("limit 1"));
        return BeanCopierUtils.convertObject(popupConfig,PopupConfigDTO.class);
    }

    /**
     * 根据类型更新弹窗配置
     *
     * @param param
     * @param tenantId
     */
    @Override
    @CacheEvict(prefix = CacheKeys.POPUP_CONFIG, key = "'#tenantId'+'#param.popupType'")
    public void savePopupConfigByType(PopupConfigDTO param, Long tenantId) {
        PopupConfig popupConfig = new PopupConfig(null, param.getPopupType(), param.getPopupImg(), param.getJumpLink());
        PopupConfig getPopupConfig = popupConfigMapper.selectOne(new LambdaQueryWrapper<PopupConfig>().eq(PopupConfig::getPopupType,param.getPopupType()));
        if(null == getPopupConfig) {
            popupConfigMapper.insert(popupConfig);
        }
        else {
            popupConfig.setId(getPopupConfig.getId());
            popupConfigMapper.updateById(popupConfig);
        }
    }

}