package com.dz.ms.user.constants;

import org.springframework.http.HttpMethod;

/**
 * 伯俊接口配置信息
 */
public enum MJAPIEnum {

    CRM_MEMBER_BIND("/open/crm/member/bind", "第三方平台注册绑定", HttpMethod.POST,1),
    CRM_MEMBER_CHECK("/open/crm/member/check", "校验手机号是否已注册", HttpMethod.POST,2),
    CRM_MEMBER_DETAIL("/open/crm/member/detail", "会员信息查询", HttpMethod.POST,3),
    SEND_MOBILE_CODE("/open/crm/auth/send_verify_code", "发送手机验证码", HttpMethod.POST,4),
    CRM_MEMBER_CHANGE_MOBILE("/open/crm/member/change-mobile", "会员手机号变更", HttpMethod.POST,5),
    UPDATE_MEMBER_INFO("/open/crm/member/update", "会员信息更新", HttpMethod.POST,6),
    //cancel_reason 1:不想/不再需要使用2:活动优惠少3:账户出现问题4:担忧隐私及安全问题5:其它
    CANCEL_MEMBER_INFO("/open/crm/member/cancel", "会员账号注销", HttpMethod.POST,7),
    GET_MEMBER_PR_CODE("/open/crm/member/get-dynamic-code", "获取会员动态码", HttpMethod.POST,8),
    ADD_MEMBER_POINTS("/open/crm/bonus/recharge", "会员积分增加", HttpMethod.POST,9),
    DEDUCT_MEMBER_POINTS("/open/crm/bonus/deduct", "会员积分扣减", HttpMethod.POST,10),
    MEMBER_POINTS_LIST("/open/crm/bonus/list", "会员积分流水列表", HttpMethod.POST,11),
    MEMBER_POINTS_DETAIL("/open/crm/bonus/detail", "会员积分流水详情", HttpMethod.POST,12),
    MEMBER_POINTS_COUNT("/open/crm/bonus/count", "会员积分数据查询", HttpMethod.POST,13),
    MEMBER_MILEAGE_LIST("/open/crm/mileage/list", "会员里程流水列表", HttpMethod.POST,14),
    MEMBER_MILEAGE_DETAIL("/open/crm/mileage/detail", "会员里程流水详情", HttpMethod.POST,15),
    ACTIVITY_COUPON_LIST("/open/crm/activity/list-all", "【领券活动】活动列表", HttpMethod.POST,16),
    ACTIVITY_COUPON_RECEIVE("/open/crm/activity/receive", "【领券活动】活动领取", HttpMethod.POST,17),
    ACTIVITY_QRCODE_RECEIVE("/open/crm/activity/qrcode/receive", "【扫码活动】扫码上报人群", HttpMethod.POST,18),
    MEMBER_COUPON_LIST("/open/crm/member/coupon/list", "会员优惠券列表", HttpMethod.POST,19),
    MEMBER_COUPON_STOCK_DETAILS("/open/crm/member/coupon/detail", "优惠券批次详情", HttpMethod.POST,20),
    MEMBER_COUPON_RECEIVE("/open/crm/member/coupon/send", "会员优惠券领取", HttpMethod.POST,21),
    MEMBER_COUPON_CODE_DETAILS("/open/crm/member/coupon-code/detail", "会员券码详情", HttpMethod.POST,22),
    MEMBER_ORDER_LIST("/open/crm/order/list", "会员订单列表", HttpMethod.POST,23),
    MEMBER_ORDER_TICKET_DETAILS("/open/crm/order/ticket-detail", "会员订单-小票详情", HttpMethod.POST,24),
    MEMBER_ORDER_DETAILS("/open/crm/order/detail", "会员订单-订单详情", HttpMethod.POST,25),
    OPEN_CARD_STATUS("/open/crm/member/open-card/status", "查看用户开卡状态", HttpMethod.POST,26),

    //宁盾单点登录
    AUTHENTICATE("/am/webauth/1/strong/authenticate", "认证", HttpMethod.POST,27),
    PASSWORD_REQUIREMENT("/am/webauth/1/strong/passwordRequirement", "用户认证策略", HttpMethod.POST,28),
    GET_DYNAMIC_PASSWORD("/am/webauth/1/strong/getDynamicPassword", "发送动态密码", HttpMethod.POST,29),
    //用户中心
    EXTERNAL_USER_AUTHENTICATE("/custom/external/authenticate", "外部用户认证", HttpMethod.POST,30),
    GET_SINGLE_USER_INFO("/custom/external/user/info", "获取单个用户的信息", HttpMethod.POST,31),
    SYNC_ALL_USERS_INFO("/custom/external/users/sync", "同步所有用户的信息", HttpMethod.POST,32),
    INCREMENTAL_SYNC_USERS("/custom/external/user/incSync", "增量同步用户", HttpMethod.POST,33),
    DELETE_USER("/custom/external/user/delete", "删除用户", HttpMethod.POST,34),
    GET_SINGLE_USER_INFO_BY_ADMIN("/custom/external/user/info/admin", "拉取单个用户的信息", HttpMethod.POST,35),
    GET_ALL_USERS_INFO("/custom/external/users/infos", "拉取所有用户的信息", HttpMethod.POST,36),
    UNBIND_USER_TERMINALS("/custom/external/user/unbindUserTerminals", "解绑用户终端", HttpMethod.POST,37),
    BIND_USER_TERMINALS("/custom/external/user/bindUserTerminals", "绑定用户终端", HttpMethod.POST,38),
    APPLY_TEMPORARY_USER("/custom/external/user/applyTemporaryUser", "申请临时用户", HttpMethod.POST,39),
    TOKEN_BINDING_LIST("/custom/external/token/binding/list", "用户和令牌的绑定关系", HttpMethod.POST,40),
    //用户中心-用户终端
    MOBILE_TOKEN_DELIVER("/am/rest/p/1/token/mobileToken/deliver", "派发手机令牌", HttpMethod.POST,41),
    MOBILE_TOKEN_USER_QR_CODE("/am/rest/p/1/token/mobileToken/userQrCode", "获取手机令牌二维码", HttpMethod.POST,42),
    BIND_AND_WRITE_MOBILE_TOKEN_QR_CODE("/am/rest/p/1/token/mobileToken/bindAndWriteMobileTokenQrCode", "绑定并获取手机令牌二维码", HttpMethod.POST,43),
    COMPLETE_MOBILE_TOKEN_BINDING("/am/rest/p/1/token/mobileToken/completeMobileTokenBinding", "完成手机令牌绑定", HttpMethod.POST,44),
    BIND_TIME_TOKEN("/am/rest/p/1/token/bind", "绑定时间型令牌", HttpMethod.POST,45),
    UNBIND_TIME_TOKEN("/am/rest/p/1/token/unbind", "解绑时间型令牌", HttpMethod.POST,46),
    UNBIND_ALL_TIME_TOKENS("/am/rest/p/1/token/unbindAll", "解绑所有时间型令牌", HttpMethod.POST,47),
    GET_MOBILE_TOKEN_DATA("/am/rest/p/1/token/mobileToken/data", "获取手机令牌信息", HttpMethod.POST,48),
    RECENT_EXPIRE_POINTS("/open/crm/bonus/will-expired", "即将过期积分列表", HttpMethod.POST,49),
    HISTORY_EXPIRE_POINTS("/open/crm/bonus/history-expired", "历史过期积分列表", HttpMethod.POST,50)
    ;

    // 接口地址
    private final String uri;
    // 接口描述
    private final String desc;
    // 接口方法
    private final HttpMethod method;
    // 接口分表
    private final Integer num;

    MJAPIEnum(String uri, String desc, HttpMethod method,Integer num) {
        this.uri = uri;
        this.desc = desc;
        this.method = method;
        this.num = num;
    }

    public String getUri() {
        return uri;
    }

    public String getDesc() {
        return desc;
    }

    public HttpMethod getMethod() {
        return method;
    }

    public Integer getNum() {
        return num;
    }
}
