package com.dz.ms.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.constants.NumConstants;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.product.dto.ShelfDTO;
import com.dz.ms.product.dto.ShelfTagDTO;
import com.dz.ms.product.dto.TagInfoDTO;
import com.dz.ms.product.dto.req.ShelfTagSaveParamDTO;
import com.dz.ms.product.dto.res.ShelfTagGroupResDTO;
import com.dz.ms.product.entity.ShelfTag;
import com.dz.ms.product.mapper.ShelfTagMapper;
import com.dz.ms.product.service.ShelfService;
import com.dz.ms.product.service.ShelfTagService;
import com.dz.ms.product.service.TagInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 货架标签表
 *
 * @author: LiinNs
 * @date: 2024/11/20 11:32
 */
@Service
public class ShelfTagServiceImpl extends ServiceImpl<ShelfTagMapper, ShelfTag> implements ShelfTagService {

    @Resource
    private ShelfTagMapper shelfTagMapper;
    @Resource
    private ShelfService shelfService;
    @Resource
    private TagInfoService tagInfoService;

    /**
     * 分页查询货架标签表
     *
     * @param param
     * @return PageInfo<ShelfTagDTO>
     */
    @Override
    public PageInfo<ShelfTagDTO> getShelfTagList(ShelfTagDTO param) {
        ShelfTag shelfTag = BeanCopierUtils.convertObjectTrim(param, ShelfTag.class);
        IPage<ShelfTag> page = shelfTagMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), new QueryWrapper<>(shelfTag));
        return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), BeanCopierUtils.convertList(page.getRecords(), ShelfTagDTO.class));
    }

    /**
     * 根据ID查询货架标签表
     *
     * @param id
     * @return ShelfTagDTO
     */
    @Override
    public ShelfTagDTO getShelfTagById(Long id) {
        ShelfTag shelfTag = shelfTagMapper.selectById(id);
        return BeanCopierUtils.convertObject(shelfTag, ShelfTagDTO.class);
    }

    /**
     * 根据货架ID查询货架标签组
     * @param shelfId 货架ID
     * @return result<List<ShelfTagDTO>
     */
    @Override
    public List<ShelfTagGroupResDTO> getTagGroupList(Long shelfId) {
        List<ShelfTagGroupResDTO> list = new ArrayList<>();
        List<ShelfTagDTO> tagList = this.getTag(shelfId);
        if(!CollectionUtils.isEmpty(tagList)){
            for (ShelfTagDTO shelfTagDTO : tagList) {
                if(Objects.equals(shelfTagDTO.getCate(),NumConstants.ONE)){
                    ShelfTagGroupResDTO dto = BeanCopierUtils.convertObject(shelfTagDTO,ShelfTagGroupResDTO.class);
                    List<ShelfTagDTO> filterTwoTagList = tagList.stream().filter(s -> Objects.equals(shelfTagDTO.getTagId(),s.getParentId()) && Objects.equals(s.getCate(), NumConstants.TWO)).collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(filterTwoTagList)){
                        dto.setChildren(BeanCopierUtils.convertList(filterTwoTagList,ShelfTagGroupResDTO.class));
                    }
                    list.add(dto);
                }
            }
        }
        return list;
    }

    /**
     * 根据标签ID查询货架标签
     * @param tagId 标签ID
     * @return result<List<ShelfTagDTO>
     */
    @Override
    public List<ShelfTagDTO> getTagByTagId(Long tagId) {
        List<ShelfTag> list = shelfTagMapper.selectList(new LambdaQueryWrapper<>(ShelfTag.builder().tagId(tagId).build()));
        if(!CollectionUtils.isEmpty(list)){
            return BeanCopierUtils.convertList(list, ShelfTagDTO.class);
        }
        return new ArrayList<>();
    }

    /**
     * 根据货架ID查询货架标签
     * @param shelfId 货架ID
     * @return result<List<ShelfTagDTO>
     */
    public List<ShelfTagDTO> getTag(Long shelfId) {
        ShelfTagDTO qryDTO = new ShelfTagDTO();
        qryDTO.setShelfId(shelfId);
        List<ShelfTag> list = shelfTagMapper.selectAllList(qryDTO);
        if(!CollectionUtils.isEmpty(list)){
            return BeanCopierUtils.convertList(list, ShelfTagDTO.class);
        }
        return new ArrayList<>();
    }

    /**
     * 根据货架ID查询货架一级标签
     * @param shelfId 货架ID
     * @return result<List<ShelfTagDTO>
     */
    @Override
    public List<ShelfTagDTO> getOneTag(Long shelfId) {
        return this.getTag(shelfId).stream().filter(s -> Objects.equals(s.getCate(),NumConstants.ONE)).collect(Collectors.toList());
    }

    /**
     * 根据货架ID,一级标签ID查询货架二级标签
     * @param shelfId 货架ID
     * @param tagId 一级标签ID
     * @return result<List<ShelfTagDTO>
     */
    @Override
    public List<ShelfTagDTO> getTwoTag(Long shelfId,Long tagId) {
        ShelfTagDTO qryDTO = new ShelfTagDTO();
        qryDTO.setShelfId(shelfId);
        qryDTO.setParentId(tagId);
        qryDTO.setCate(NumConstants.TWO);
        List<ShelfTag> list = shelfTagMapper.selectAllList(qryDTO);
        if(!CollectionUtils.isEmpty(list)){
            return BeanCopierUtils.convertList(list, ShelfTagDTO.class);
        }
        return new ArrayList<>();
    }

    /**
     * 保存货架标签表
     *
     * @param param
     * @return Long
     */
    @Override
    public Long saveShelfTag(ShelfTagDTO param) {
        ShelfTag shelfTag = new ShelfTag(param.getId(), param.getShelfId(), param.getShelfName(), param.getTagId(), param.getTagName(), param.getCate(), param.getParentId());
        if (ParamUtils.isNullOr0Long(shelfTag.getId())) {
            shelfTagMapper.insert(shelfTag);
        } else {
            shelfTagMapper.updateById(shelfTag);
        }
        return shelfTag.getId();
    }

    /**
     * 保存一级货架标签
     * @param param 入参
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOneShelfTag(ShelfTagSaveParamDTO param) {
        Long shelfId = param.getShelfId();
        if(Objects.isNull(shelfId)){
            throw new BusinessException(ErrorCode.BAD_REQUEST, "[货架标签]货架ID不允许为空");
        }
        ShelfDTO shelfDTO = shelfService.getShelfById(shelfId,NumConstants.ONE);
        //删除旧一级标签
        shelfTagMapper.delete(new LambdaQueryWrapper<>(ShelfTag.builder().shelfId(shelfId).cate(NumConstants.ONE).build()));
        //新增新一级标签
        List<Long> newOneTagIdList = param.getOneTagIdList();
        if(!CollectionUtils.isEmpty(newOneTagIdList)){
            //查询标签数据
            List<TagInfoDTO> tagList = tagInfoService.getByTagIdList(newOneTagIdList);
            tagList = tagList.stream().filter(s -> Objects.equals(s.getCate(),NumConstants.ONE)).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(tagList)){
                throw new BusinessException(ErrorCode.BAD_REQUEST, "[货架标签]未查询到一级标签数据");
            }
            //排序
            Map<Long, Integer> tagIdOrderMap = IntStream.range(0, newOneTagIdList.size())
                    .boxed()
                    .collect(Collectors.toMap(newOneTagIdList::get, i -> i));
            tagList = tagList.stream()
                    .sorted(Comparator.comparingInt(obj -> {
                        return tagIdOrderMap.getOrDefault(obj.getId(), Integer.MAX_VALUE); // 如果tagId不在映射中，则返回最大值以确保它排在最后  
                    }))
                    .collect(Collectors.toList());
            //insert数据整理
            List<ShelfTag> addList = tagList.stream().map(s ->
                    ShelfTag.builder().shelfId(shelfDTO.getId()).shelfName(shelfDTO.getName()).tagId(s.getId()).tagName(s.getName()).cate(s.getCate()).parentId(s.getId()).build())
                    .collect(Collectors.toList());
            //新增数据
            this.saveBatch(addList);
            //数据库中二级标签父级非新一级标签数据删除
            List<ShelfTagDTO> twoTagList = this.getTag(shelfId).stream().filter(s -> Objects.equals(s.getCate(),NumConstants.TWO)).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(twoTagList)){
                List<ShelfTagDTO> delTwoTagList = twoTagList.stream().filter(s -> newOneTagIdList.stream().noneMatch(y -> Objects.equals(y, s.getParentId()))).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(delTwoTagList)){
                    this.removeBatchByIds(delTwoTagList.stream().map(ShelfTagDTO::getId).collect(Collectors.toList()));
                }
            }
        }
    }

    /**
     * 保存二级货架标签
     * @param param 入参
     */
    @Override
    public void saveTwoShelfTag(ShelfTagSaveParamDTO param) {
        Long shelfId = param.getShelfId();
        Long oneTagId = param.getOneTagId();
        if(Objects.isNull(shelfId) || Objects.isNull(oneTagId)){
            throw new BusinessException(ErrorCode.BAD_REQUEST, "[货架标签]货架ID/一级标签ID，不允许为空");
        }
        ShelfDTO shelfDTO = shelfService.getShelfById(shelfId,NumConstants.ONE);
        TagInfoDTO oneTagInfoDTO = tagInfoService.getTagInfoById(oneTagId);
        if(Objects.isNull(oneTagInfoDTO) || !Objects.equals(oneTagInfoDTO.getCate(),NumConstants.ONE)){
            throw new BusinessException(ErrorCode.BAD_REQUEST, "[货架标签]未查询到父级标签数据");
        }
        //删除旧二级标签
        shelfTagMapper.delete(new LambdaQueryWrapper<>(ShelfTag.builder().shelfId(shelfId).parentId(oneTagId).cate(NumConstants.TWO).build()));
        //新增新二级标签
        List<Long> newTwoTagIdList = param.getTwoTagIdList();
        if(!CollectionUtils.isEmpty(newTwoTagIdList)){
            //查询标签数据
            List<TagInfoDTO> tagList = tagInfoService.getByTagIdList(newTwoTagIdList);
            tagList = tagList.stream().filter(s -> Objects.equals(s.getCate(),NumConstants.TWO)).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(tagList)){
                throw new BusinessException(ErrorCode.BAD_REQUEST, "[货架标签]未查询到二级标签数据");
            }
            //排序
            Map<Long, Integer> tagIdOrderMap = IntStream.range(0, newTwoTagIdList.size())
                    .boxed()
                    .collect(Collectors.toMap(newTwoTagIdList::get, i -> i));
            tagList = tagList.stream()
                    .sorted(Comparator.comparingInt(obj -> {
                        return tagIdOrderMap.getOrDefault(obj.getId(), Integer.MAX_VALUE); // 如果tagId不在映射中，则返回最大值以确保它排在最后  
                    }))
                    .collect(Collectors.toList());
            //insert数据整理
            List<ShelfTag> addList = tagList.stream().map(s ->
                    ShelfTag.builder().shelfId(shelfDTO.getId()).shelfName(shelfDTO.getName()).tagId(s.getId()).tagName(s.getName()).cate(s.getCate()).parentId(oneTagId).build())
                    .collect(Collectors.toList());
            //新增数据
            this.saveBatch(addList);
        }
    }

    /**
     * 根据标签ID更新货架标签名称
     * @param param 入参
     */
    @Override
    public void updShelfTagName(ShelfTagDTO param) {
        if(Objects.nonNull(param.getTagId()) && StringUtils.isNotBlank(param.getTagName())){
            shelfTagMapper.update(ShelfTag.builder().tagName(param.getTagName()).build(),new LambdaQueryWrapper<>(ShelfTag.builder().tagId(param.getTagId()).build()));
        }
    }


    /**
     * 根据ID删除货架标签表
     *
     * @param param
     */
    @Override
    public void deleteShelfTagById(IdCodeDTO param) {
        shelfTagMapper.deleteById(param.getId());
    }

    /**
     * 根据标签ID删除货架标签
     * @param tagId 标签ID
     */
    @Override
    public void deleteShelfTagByTagId(Long tagId) {
//        List<ShelfTagDTO> list = this.getTagByTagId(tagId);
//        if(!CollectionUtils.isEmpty(list)){
//            throw new BusinessException(ErrorCode.BAD_REQUEST, "[货架标签]此标签有关联货架，不允许删除");
//        }
        shelfTagMapper.delete(new LambdaQueryWrapper<>(ShelfTag.builder().tagId(tagId).build()));
    }

}
