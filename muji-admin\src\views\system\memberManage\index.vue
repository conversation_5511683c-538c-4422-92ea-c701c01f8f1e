<template>

  <layout>
    <!-- <template v-slot:headerTop>
      <a-tabs v-model:activeKey="activeKey" @change="tagChange">
        <a-tab-pane v-for="(item, key) in tabs" :key="key" :tab="item">
        </a-tab-pane>
      </a-tabs>
    </template> -->
    <template v-slot:header>
      <searchForm :formParams="formParams" @cancel="resetData" @ok="refreshData" :disabled="!$hasPermission('setting:user:searchStaff')">
        <a-form-item name="username">
          <a-input placeholder="用户账号" allow-clear v-model:value="formParams.username" allowClear @keyup.enter="refreshData"></a-input>
        </a-form-item>

      </searchForm>
    </template>
    <template v-slot:topRight>
      <a-space>
        <!-- :disabled="!$hasPermission('open:leaveUser:synchLeaveUser')" 权限标识 -->
        <a-button type="primary" :disabled="!$hasPermission('setting:user:addStaff')" @click="addChang">添加用户</a-button>

      </a-space>

    </template>

    <template v-slot="{ height }">
      <a-table :indentSize="20" row-key="id" :scroll="{ scrollToFirstRowOnChange: true, y: height - 88, x: '100%' }" :dataSource="dataSource" :columns="tableHeader" :pagination="pagination" :loading="loading" @change="loadData">
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'index'">
            {{ (current - 1) * pageSize + 1 + index }}
          </template>
          <template v-if="column.key === 'state'">
            <a-tag color="blue" v-if="record.state == 0">正常</a-tag>
            <a-tag color="red" v-else>关闭</a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <!--:disabled="" !$hasPermission('open:leaveUser:leaveUserInfo')  :disabled="!$hasPermission('open:leaveUser:leaveUserList')"-->
            <a-button type="link" :disabled="!$hasPermission('setting:user:editStaff')" @click="editUser(record)">编辑</a-button>
            <a-divider type="vertical" />
            <a-popconfirm title="是否确定删除该用户？" @confirm="handleDelete(record)">
              <a-button :disabled="!$hasPermission('setting:user:deleteStaff')" type="link">删除</a-button>
            </a-popconfirm>
            <a-divider type="vertical" />
            <a-button :disabled="!$hasPermission('setting:user:reset')" type="link" @click="resetPassword(record)">重置密码</a-button>
          </template>
        </template>
      </a-table>
    </template>

  </layout>
  <addMember ref="addMemberRef" :visible="visible" @ok="updateList" @cancel="visible = false" :id="id" :type="type" />
</template>
<script setup>
import addMember from './components/addMember.vue'
import { useRoute, useRouter } from 'vue-router'
import { usePagination, useRequest, } from 'vue-request';
import { userList, userDelete, passwordReset } from '@/http/index'
import { message, Modal } from "ant-design-vue";
const addMemberRef = ref(null)
// 分页参数
let pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ["bottomLeft"],
  };
});
// 表格分页数据获取
const total = ref(0)


const { activeKey, tabs, formParams, tableHeader, id, type, visible } = toRefs(reactive({


  id: '',
  type: 0,// 0-新增 1-编辑  2-查看
  visible: false,
  formParams: {
    username: ''
  },
  tabs: {

    1: "管理员账户列表",
    2: "角色管理列表",
  },
  tableHeader: [{
    title: '序号',
    key: 'index',
    align: 'center',
    width: 80
  },
  {
    title: 'ID',
    dataIndex: 'id',
    align: 'center',
    width: 80
  },
  {
    title: '姓名',
    dataIndex: 'realname',
    align: 'center',
    width: 150,
    ellipsis: true
  },
  {
    title: '账号',
    dataIndex: 'username',
    align: 'center',
    width: 180
  },
  {
    title: '角色名称',
    dataIndex: 'roleName',
    align: 'center',
    width: 180
  },
  {
    title: '联系电话',
    dataIndex: 'mobile',
    align: 'center',
    width: 180
  },
  {
    title: '创建时间',
    dataIndex: 'created',
    align: 'center',
    width: 160
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    align: 'center',
    width: 240,
    fixed: 'right'
  }]
})
);
const { data: dataSource, run, loading, pageSize, current, refresh } = usePagination((param) => {
  // 这里修改参数
  // return resignationInheritanceList({ ...param, ...getParams() })
  return userList({ ...param, ...formParams.value })
},
  {
    manual: false, // 修改为false,让其自动执行
    pagination: {
      currentKey: 'pageNum',  // 通过该值指定接口 当前页数 参数的属性值
      pageSizeKey: 'pageSize',// 通过该值指定接口 每页获取条数 参数的属性值
      totalKey: 'data.data.count',  // 指定 data 中 total 属性的路径
    },
    formatResult: res => { // 返回数据格式化
      total.value = res.data.count
      return res.data.list
    }
  });


function updateList(value) {
  visible.value = false;
  // 新增 重置搜索数据
  if (!value) {
    resetData();
  } else {
    // 查看、编辑 不需要清空搜索数据 直接刷新
    refresh();
  }
}

function editUser(record) {
  visible.value = true
  type.value = 1
  id.value = record.id
  addMemberRef.value.addParams = {
    id: record.id,
    realname: record.realname,
    username: record.username,
    mobile: record.mobile,
    roleId: record.roleId
  }
  // console.log(addMemberRef.value.addParams, record);

}
//删除
const handleDelete = (data) => {
  userDelete({ id: data.id }).then(res => {
    message.success('删除成功')
    resetData()
  })
}
// 重置密码
const resetPassword = (data) => {
  passwordReset({ id: data.id }).then(res => {
    message.success('重置密码成功')
  })
}
// 加载数据
const loadData = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}


// 刷新数据
const refreshData = () => {
  run({ pageNum: 1, pageSize: pageSize.value })
};

// 重置数据
const resetData = () => {
  formParams.value = {
    username: '',
  }
  refreshData()
}
function addChang() {

  visible.value = true
  type.value = 0
  id.value = ''
  addMemberRef.value.addParams = {
    id: '',
    realname: '',
    username: '',
    mobile: '',
    roleId: ''
  }
  // console.log(visible.value);

}
</script>