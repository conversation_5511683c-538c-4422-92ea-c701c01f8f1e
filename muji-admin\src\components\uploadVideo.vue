<template>
  <div :style="{ width: width + 'px !important', height: height + 'px !important', marginBottom: '10px', cursor: loading ? 'not-allowed' : 'pointer' }">
    <!-- {{props.imgUrl}} -->
    <div class="haveUpload" v-if="props.moments?props.imgUrl.length>0:props.imgUrl">
      <videoPlay :width="width + 'px'" :height="height + 'px'" ref="player" v-bind="options" />
      <div class="action-mask" style="height: 30%" @click.self.stop>
        <a-upload accept=".mp4" :disabled="loading" :multiple="false" :max-count="1" name="avatar" :show-upload-list="false" :custom-request="uploadImg" :before-upload="beforeUpload">
          <div v-if="loading">
            <loading-outlined />上传中...
          </div>
          <EditOutlined v-else style="color: #FFF;fontSize:20px" />
          <!-- <a-button class="upload-btn" :disabled="loading || disabled" type="primary" :loading="loading">更换</a-button> -->
        </a-upload>

      </div>
    </div>

    <a-upload accept=".mp4" v-else :disabled="loading" :multiple="false" :max-count="1" name="avatar" :show-upload-list="false" :custom-request="uploadImg" :before-upload="beforeUpload">
      <div class="upload" :style="{ width: width + 'px !important', height: height + 'px !important' }">
        <template v-if="loading">
          <loading-outlined />上传中...
        </template>
        <template v-else>
          <plus-outlined />
          添加视频
        </template>
      </div>
    </a-upload>
  </div>
</template>
<script setup>
import { fileUpload } from '@/http/index.js'
import { message } from 'ant-design-vue';
import { reactive, toRefs, ref, onMounted, watch } from 'vue';
const player = ref();
const emit = defineEmits(['success'])
const props = defineProps({
  // 图片上传接口
  api: {
    type: Object,
    default() {
      return fileUpload
    },
  },
  // 图片的高度
  height: {
    type: Number || String
  },
  // 图片的宽度
  width: {
    type: Number || String
  },
  // 默认视频url
  imgUrl: {
    type: String,
  },
  // 复制的form对象
  form: {
    type: Object
  },
  // form的key值
  path: {
    type: String,
  },
  // 上传最大值
  max: {
    type: Number || String,
    default: 1000000000
  },
  disabled: {
    type: Boolean,
    default: false
  },
  moments: {//朋友圈特殊校验处理
    type: Boolean,
    default: false
  }
})
let { loading, options, optionsVi } = toRefs(reactive({
  loading: false,
  options: {
    // width: '800px', //播放器高度
    // height: '450px', //播放器高度
    color: "#409eff", //主题色
    muted: false, //静音
    webFullScreen: false,
    speedRate: ["0.75", "1.0", "1.25", "1.5", "2.0"], //播放倍速
    autoPlay: false, //自动播放
    loop: false, //循环播放
    mirror: false, //镜像画面
    ligthOff: false,  //关灯模式
    volume: 0.3, //默认音量大小
    control: true, //是否显示控制器
    title: '', //视频名称
    src: props.moments ? props.imgUrl[0]?.url : props.imgUrl, //视频源
    poster: '', //封面
  },

}))
watch(() => props.imgUrl, (src) => {
  // console.log('更换')
  if (props.moments) {
    options.value.src = src[0]['url'];
    // console.log(optionsVi.value);
  } else {

    options.value.src = src;
  }
  // console.log(options.value)
}, {
  deep: true
})

// 视频上传校验
const beforeUpload = (file) => {
  // console.log(file, props.max)
  // 大小
  const isLimit = file.size / 1024 / 1024 < props.max;
  if (!isLimit) {
    message.error(`视频大小超过${props.max}M!`);
  }
  // 格式
  const isFormat = ['video/mp4'].includes(file.type)
  if (!isFormat) {
    message.error(`视频格式不支持!`);
  }

  return isLimit && isFormat;
}

// 视频上传
const uploadImg = ({ file }) => {
  if (loading.value) return
  loading.value = true
  const formData = new FormData()
  formData.append('file', file)
  props.api(formData).then(res => {
    // console.log('上传成功', res)
    loading.value = false
    if (props.moments) {
      const video = document.createElement('video');
      video.src = window.URL.createObjectURL(file);

      video.onloadedmetadata = () => {
        // console.log(video.duration, 'durationdurationdurationdurationduration');
        emit('success', {
          form: props.form,
          path: props.path,
          imgUrl: [{ ...res.data, audioTime: String(Number(video.duration.toFixed(2))) }]
        })
      }

    } else {
      emit('success', {
        form: props.form,
        path: props.path,
        imgUrl: res.data
      })
    }

  }).catch(() => {
    // console.log('上传失败')
    loading.value = false
  }).finally(() => {
    loading.value = false;
  });
}
</script>
<style lang="scss" scoped>
.haveUpload {
  position: relative;

  &:hover {
    .action-mask {
      opacity: 1;
    }
  }
}

.action-mask {
  position: absolute;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0 30px;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  cursor: pointer;
  text-align: center;
  color: #f2f2f2;
  opacity: 0;
  font-size: 20px;
  transition: opacity 0.3s;
  background-color: hsl(0, 0%, 50%);
  z-index: 99;
}

.upload {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  background: rgb(250, 250, 250);
  font-size: 10px;
  color: #999;
  border: 1px dashed #d9d9d9;
  position: relative;

  &:hover {
    border-color: #1890ff;
    // cursor: pointer;
  }

  &-btn {
    position: absolute;
    right: 0;
    bottom: 22px;
    transform: translateX(120%);
  }

  &-loading {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
  }
}
</style>
