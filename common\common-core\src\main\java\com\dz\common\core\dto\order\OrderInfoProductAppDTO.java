package com.dz.common.core.dto.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * APP端订单信息DTO
 *
 * @author: wuhaidong
 * @date: 2018/10/18 10:17
 */
@Data
public class OrderInfoProductAppDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品ID")
    private Long productId;
    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "商品类型 1实物商品 2电子券")
    private Integer pdType;
    @ApiModelProperty(value = "商品图片url")
    private String imgUrl;
    @ApiModelProperty(value = "实际支付总积分")
    private Integer realPoint;
    @ApiModelProperty(value = "实际支付总金额")
    private BigDecimal realAmount;
    @ApiModelProperty(value = "商品数量")
    private Integer number;
    @ApiModelProperty(value = "商品状态:1待兑换 2已兑换 3已过期")
    private Integer status;
    @ApiModelProperty(value = "外部数据ID 优惠券ID需兑换")
    private String venderId;
    @ApiModelProperty(value = "优惠券编码")
    private String couponCode;
    @ApiModelProperty(value = "优惠券stockId/couponId")
    @JsonProperty("couponId")
    private String stockId;

}
