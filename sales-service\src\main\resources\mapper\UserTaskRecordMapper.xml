<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" " http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.sales.mapper.UserTaskRecordMapper">

    <!--不是兑礼任务查询一次性任务完成情况-->
    <select id="onlyOneFinishPersonNumOne" resultType="com.dz.ms.sales.entity.UserTaskRecord">
        select user_id from t_user_task_record
        where task_id=#{taskId} and create_time
        <if test="startTime !=null and endTime != null">
            BETWEEN #{startTime} and #{endTime}
        </if>
        <if test="num !=null">
            group by user_id having count(user_id) &gt;= ${num}
        </if>
    </select>
    <!--是兑礼任务查询一次性任务完成情况-->
    <select id="onlyOneFinishPersonNumTwo" resultType="com.dz.ms.sales.entity.UserTaskRecord">
        select user_id ,FLOOR(count(user_id)/${num}) as finishNum from t_user_task_record
        where task_id=#{taskId} and create_time
        <if test="startTime !=null and endTime != null">
            BETWEEN #{startTime} and #{endTime}
        </if>
        group by user_id having count(user_id) &gt;= ${num}
    </select>
    <!--根据任务idList查询userId-->
    <select id="getUserIdByTaskIdList" resultType="com.dz.ms.sales.entity.UserTaskRecord">
        select user_id,task_id from t_user_task_record where task_id in
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
        group by user_id,task_id
    </select>

    <select id="querySubTableList" resultType="java.lang.String">
        SHOW TABLES LIKE 't_user_task_month_%'
    </select>

    <update id="createSubTable" parameterType="java.lang.String">
        CREATE TABLE `t_user_task_month_${nowDay}`  (
        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
        `task_id` bigint NULL DEFAULT NULL COMMENT '任务id',
        `user_id` bigint NULL DEFAULT NULL COMMENT '用户id',
        `create_time` datetime NOT NULL COMMENT '创建时间',
        `tenant_id` bigint NOT NULL DEFAULT 1 COMMENT '租户id',
        PRIMARY KEY (`id`) USING BTREE,
        INDEX `idx_uid_taskid`(`task_id`, `user_id`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务埋点' ROW_FORMAT = DYNAMIC;
    </update>

    <insert id="inserByTable">
        insert into t_user_task_month_${nowDay}
        (
        task_id,
        user_id,
        create_time,
        tenant_id
        )
        values
        (
        #{userTask.taskId},
        #{userTask.userId},
        #{userTask.createTime},
        #{userTask.tenantId}
        )
    </insert>

    <select id="querySubTableListYear" resultType="java.lang.String">
        SHOW TABLES LIKE 't_user_task_year_%'
    </select>

    <update id="createSubTableYear" parameterType="java.lang.String">
        CREATE TABLE `t_user_task_year_${nowDay}`  (
        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
        `task_id` bigint NULL DEFAULT NULL COMMENT '任务id',
        `user_id` bigint NULL DEFAULT NULL COMMENT '用户id',
        `create_time` datetime NOT NULL COMMENT '创建时间',
        `tenant_id` bigint NOT NULL DEFAULT 1 COMMENT '租户id',
        PRIMARY KEY (`id`) USING BTREE,
        INDEX `idx_uid_taskid`(`task_id`, `user_id`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 112 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务埋点' ROW_FORMAT = DYNAMIC;
    </update>

    <insert id="inserByTableYear">
        insert into t_user_task_year_${nowDay}
        (
        task_id,
        user_id,
        create_time,
        tenant_id
        )
        values
        (
        #{userTask.taskId},
        #{userTask.userId},
        #{userTask.createTime},
        #{userTask.tenantId}
        )
    </insert>
    <select id="isUserTaskExists" resultType="java.lang.Long">
        SELECT id  FROM ${tableName}
        WHERE task_id = #{taskId} AND user_id = #{uid} limit 1
    </select>

    <select id="participateTaskCount" parameterType="java.lang.String" resultType="com.dz.ms.sales.entity.UserTask">
        select count(user_id) userCount, task_id from t_user_task_year_${nowDay}  group by task_id
    </select>
</mapper>
