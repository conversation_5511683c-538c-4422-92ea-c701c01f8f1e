package com.dz.common.core.dto.user;
import java.util.Date;
import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * DTO
 * @author: yibo
 * @date:   2024/11/19 17:20
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "")
public class ServeDTO extends BaseDTO {

    @ApiModelProperty(value = "主键id")
    private Long id;
    @ApiModelProperty(value = "门店服务名称")
    private String name;
    @ApiModelProperty(value = "门店服务图片")
    private String image;
    @ApiModelProperty(value = "排序")
    private Integer sort;
    @ApiModelProperty(value = "是否删除 0启用 1停用")
    private Integer status;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "修改时间")
    private Date modified;
    @ApiModelProperty(value = "修改人")
    private Long modifier;

    @ApiModelProperty(value = "是否删除 0未选 1选中")
    private Integer isChecked;

}
