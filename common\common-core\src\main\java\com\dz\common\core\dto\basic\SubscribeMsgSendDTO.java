package com.dz.common.core.dto.basic;

import com.dz.common.core.enums.SubscribeMsgEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 发送小程序订阅消息参数
 * <AUTHOR>
 * @date 2022/2/3 11:27
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
@ApiModel(value = "小程序订阅消息发送参数")
public class SubscribeMsgSendDTO implements Serializable {

    private static final long serialVersionUID = 1824399519809149429L;

    @ApiModelProperty(value = "消息类型枚举,传参废弃不用，用下面的msgCode")
    private SubscribeMsgEnum subscribeMsgEnum;

    @ApiModelProperty(value = "接收者uid 与openid二选一")
    private List<Long> uids;

    @ApiModelProperty(value = "接收者openid 与uid二选一")
    private List<String> openids;

    @ApiModelProperty(value = "消息code")
    private String msgCode;

    @ApiModelProperty(value = "跳转页面路径+参数，仅限本小程序内的页面")
    private String pageUrl;

    @ApiModelProperty(value = "跳转页面参数 适用于页面路径已在数据库配置")
    private String param;

    @ApiModelProperty(value = "模板内容数组 数组中值可为null但顺序一定要和模板字段顺序一致")
    private String[] content;
    @ApiModelProperty(value = "场景值")
    private String scene;

    public SubscribeMsgSendDTO(SubscribeMsgEnum subscribeMsgEnum, String pageUrl, String[] content) {
        this.subscribeMsgEnum = subscribeMsgEnum;
        this.pageUrl = pageUrl;
        this.content = content;
    }

    public SubscribeMsgSendDTO(SubscribeMsgEnum subscribeMsgEnum, String openid, String param, String[] content) {
        this.subscribeMsgEnum = subscribeMsgEnum;
        List<String> openids = new ArrayList<>();
        openids.add(openid);
        this.openids = openids;
        this.param = param;
        this.content = content;
    }

    public SubscribeMsgSendDTO(SubscribeMsgEnum subscribeMsgEnum, Long uid, String param, String[] content) {
        this.subscribeMsgEnum = subscribeMsgEnum;
        List<Long> uids = new ArrayList<>();
        uids.add(uid);
        this.uids = uids;
        this.param = param;
        this.content = content;
    }

    public SubscribeMsgSendDTO(SubscribeMsgEnum subscribeMsgEnum, Long uid, String[] content) {
        this.subscribeMsgEnum = subscribeMsgEnum;
        List<Long> uids = new ArrayList<>();
        uids.add(uid);
        this.uids = uids;
        this.content = content;
    }

}
