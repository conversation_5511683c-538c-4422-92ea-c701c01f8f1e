package com.dz.common.core.snd;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.util.List;

public class IgnoreSerializer extends JsonSerializer {

    private static final String DELIMITER = ";"; // 定义分隔符，可根据需求修改

    @Override
    public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value == null) {
            gen.writeNull();
            return;
        }
        StringBuilder sb = new StringBuilder();
        if (value instanceof List<?>) {
            List<?> list = (List<?>) value;
            for (int i = 0; i < list.size(); i++) {
                sb.append(list.get(i));
                if (i < list.size() - 1) {
                    sb.append(DELIMITER);
                }
            }
            gen.writeString(sb.toString());
        } else {
            gen.writeNull();
        }
    }
}
