package com.dz.ms.basic.dto;
import java.util.Date;
import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 默认数据DTO
 * @author: Handy
 * @date:   2023/12/19 17:05
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "默认数据")
public class DefaultDataDTO extends BaseDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "数据类型 1默认小程序UI配置 2抽签文案")
    private Integer type;
    @ApiModelProperty(value = "UI配置内容json")
    private String content;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "修改时间")
    private Date modified;

}
