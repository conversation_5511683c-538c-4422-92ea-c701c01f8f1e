package com.dz.ms.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.order.entity.OrderDetailCoupon;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 订单详情-优惠券信息Mapper
 */
@Repository
public interface OrderDetailCouponMapper extends BaseMapper<OrderDetailCoupon> {

    /**
     * 根据用户ID获取未使用的优惠券ID列表
     *
     * @param userId 用户ID
     * @return 优惠券ID列表
     */
    List<String> getUnusedStockIdListByUserId(@Param("userId") Long userId);
}
