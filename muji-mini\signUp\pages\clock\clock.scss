/* signUp/pages/clock/clock.wxss */

.page-container {
  // height: 100vh;
  display: flex;
  // background-color: rgb(218, 220, 212);
  // background: linear-gradient(179deg, rgba(255, 255, 255, 0.28) 1%, #F8F6ED 100%);
  background-size: 100% auto;

  .clock {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;

    .top_img {
      position: relative;
      width: 100%;
      padding-left: 43rpx;
      padding-top: 46rpx;
      box-sizing: border-box;


      .rules {
        width: 74rpx;
        height: 146rpx;
        position: absolute;
        right: 0;
        top: 90rpx;
        z-index: 9;
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .rules-in {
          width: 44rpx;
          height: 146rpx;
          background: #C7B397;
          font-family: MUJIFont2020,
            SourceHanSansCN;
          font-weight: 400;
          font-size: 22rpx;
          color: #FFFFFF;
          line-height: 24rpx;
          text-align: center;
          display: flex;
          justify-content: center;
          align-items: center;
          writing-mode: vertical-rl;
          letter-spacing: 4rpx;
        }
      }

      .title1 {
        height: 141rpx;
        font-family: MUJIFont2020, SourceHanSansCN;
        font-weight: 500;
        font-size: 52rpx;
        color: var(--text-black-color);
        line-height: 70rpx;
        letter-spacing: 5rpx;
        text-align: left;
      }

      .title2 {
        margin-top: 20rpx;
        // width: 443rpx;
        height: 74rpx;
        font-family: MUJIFont2020,
          SourceHanSansCN;
        font-weight: 400;
        font-size: 24rpx;
        color: var(--text-black-color);
        line-height: 38rpx;
        letter-spacing: 4rpx;
        text-align: left;

        .num {
          color: #C7B397;
        }
      }
    }

    .clockList {
      display: flex;
      justify-content: start;
      flex-wrap: wrap;
      margin: 20rpx 43rpx 0 43rpx;

      .clockItem {
        width: 151rpx;
        height: 151rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        color: #333333;
        margin-left: 19rpx;
        margin-top: 20rpx;
        background: white;
        box-sizing: border-box;

        &:nth-child(1) {
          margin-left: 0rpx;
        }

        &:nth-child(5) {
          margin-left: 0rpx;
        }

        &:nth-child(7) {
          width: 321rpx;
          height: 151rpx;
        }

        .item {
          min-width: 91rpx;
          height: 79rpx;
          font-family: MUJIFont2020,
            SourceHanSansCN;
          font-weight: 300;
          font-size: 60rpx;
          color: var(--text-black-color);
          line-height: 86rpx;
          text-align: center;
          display: flex;
          justify-content: center;
          align-items: center;

          .iconfont {
            width: 71rpx;
            height: 71rpx;
            background-size: 100% 100%;
            margin-right: 20rpx;
            margin-top: 10rpx;

            image {
              width: 100%;
              height: 100%;
            }
          }
        }
      }

      .icon-wrap {
        height: 43rpx;
        width: 76rpx;
        display: flex;
        justify-content: center;

        .icon {
          width: 14rpx;
          height: 14rpx;
          border-radius: 50%;
          background: #C8B49A;
          margin-top: 19rpx;
        }

        .iconfont {
          font-size: 28rpx;
          padding-top: 5rpx;
        }
      }

      .isSingIn {
        background: #F8F6ED;

        &::after {
          content: "";
          font-family: MUJIFont2020,
            SourceHanSansCN;
          font-weight: 300;
          font-size: 20rpx;
          text-align: center;
        }
      }

      .notSingIn {
        background: #F8F6ED;
      }

      .today {
        border-bottom: 8rpx solid #C7B397;
        background-color: #F8F6ED;
      }

      .item-notSingIn {
        width: 76rpx;
        height: 43rpx;
        font-family: MUJIFont2020,
          SourceHanSansCN;
        font-weight: 700;
        font-size: 18rpx;
        color: #C8B49A;
        line-height: 47rpx;
        text-align: center;
        background: #F8F6ED;
        text-decoration: underline;
      }

      .item-notSingIn-today {
        width: 76rpx;
        height: 43rpx;
        font-family: MUJIFont2020, SourceHanSansCN;
        font-weight: 700;
        font-size: 18rpx;
        line-height: 47rpx;
        text-align: center;
        color: #C8B49A;
      }
    }

    .clock-btn {
      display: flex;
      justify-content: center;
      margin-top: 40rpx;

      .timeicon {
        width: 39rpx;
        height: 37rpx;
        background-size: 100% 100%;
        margin-right: 13rpx;
      }
    }

    .record_wrap {
      padding: 0 52rpx;
      margin-top: 80rpx;

      .record-title {
        font-family: MUJIFont2020,
          SourceHanSansCN;
        font-weight: 700;
        font-size: 28rpx;
        color: var(--text-black-color);
        line-height: 40rpx;
        letter-spacing: 1rpx;
        text-align: left;
      }

      .noData {
        font-family: MUJIFont2020,
          SourceHanSansCN;
        font-weight: 400;
        font-size: 24rpx;
        line-height: 38rpx;
        letter-spacing: 4rpx;
        text-align: center;
        margin-top: 180rpx;
      }

      .record {
        padding-bottom: 34rpx;

        .record-item {
          width: 100%;
          height: 176rpx;
          background: #FFFFFF;
          margin: 43rpx 0;
          display: flex;
          align-items: center;

          .Day {
            width: 180rpx;
            height: 176rpx;
            background: #F8F6ED;
            font-family: MUJIFont2020;
            font-weight: 300;
            font-size: 60rpx;
            color: var(--text-black-color);
            line-height: 176rpx;
            text-align: center;
          }

          .record-item-time {
            display: flex;
            justify-content: space-between;
          }

          .record-item-overall {
            width: calc(100% - 180rpx);
            font-family: MUJIFont2020,
              SourceHanSansCN;
            font-weight: 400;
            font-size: 22rpx;
            color: var(--text-black-color);
            line-height: 32rpx;
            padding-left: 39rpx;
            margin-bottom: 20rpx;

            .title {
              height: 32rpx;
            }

            .start {
              margin-top: 3rpx;
              display: flex;
              justify-content: space-between;
              padding-bottom: 12rpx;

              .detail {
                display: flex;
                margin-right: 15rpx;
                margin-top: 8rpx;

                .iconfont {
                  transform: scaleX(-1);
                }
              }
            }

          }
        }
      }
    }
  }
}
