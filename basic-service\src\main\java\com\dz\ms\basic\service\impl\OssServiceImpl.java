package com.dz.ms.basic.service.impl;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.internal.OSSHeaders;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.StorageClass;
import com.dz.common.core.dto.OssDTO;
import com.dz.ms.basic.service.OssService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.net.URL;
import java.util.Date;

/**
 * oss
 *
 * <AUTHOR>
 * @date 2022/8/12 13:38
 */
@Service
@Slf4j
public class OssServiceImpl implements OssService {

    @Value("${file.request.domain:}")
    private String requestDomain;
    @Value("${file.upload.access_key:}")
    private String accessKey;
    @Value("${file.upload.secret_key:}")
    private String secretKey;
    @Value("${file.upload.bucket:}")
    private String bucket;
    @Value("${file.upload.region:}")
    private String region;
    @Value("${file.upload.endpoint:}")
    private String endpoint;
    @Value("${file.upload.oss_project:}")
    private String ossProject;

    @Override
    public String uploadOss(OssDTO ossDTO) {
        try {
            OSS ossClient = new OSSClient(endpoint, accessKey, secretKey);
            // 如果需要上传时设置存储类型与访问权限，请参考以下示例代码。
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setHeader(OSSHeaders.OSS_STORAGE_CLASS, StorageClass.Standard.toString());
            metadata.setObjectAcl(CannedAccessControlList.PublicReadWrite);
            // <yourObjectName>表示上传文件到OSS时需要指定包含文件后缀在内的完整路径，例如abc/efg/123.jpg。
            ossClient.putObject(bucket, ossDTO.getKey(), new ByteArrayInputStream(ossDTO.getBytes()), metadata);
            // 关闭OSSClient。
            ossClient.shutdown();
            // 设置URL过期时间为1小时。
            Date expiration = new Date(System.currentTimeMillis() + 3600 * 1000);
            // 生成以GET方法访问的签名URL，访客可以直接通过浏览器访问相关内容。
            URL url = ossClient.generatePresignedUrl(bucket, ossDTO.getKey(), expiration);
            log.info("oss 默认url-->{}", url.toString());
            return requestDomain + "/" + ossDTO.getKey();
        } catch (Exception e) {
            log.error("oss 上传文件失败,exception:{}", e);
            return null;
        }
    }

    @Override
    public void delOSS(String url) {
        try {
            OSS ossClient = new OSSClient(endpoint, accessKey, secretKey);
            // 如果需要上传时设置存储类型与访问权限，请参考以下示例代码。
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setHeader(OSSHeaders.OSS_STORAGE_CLASS, StorageClass.Standard.toString());
            metadata.setObjectAcl(CannedAccessControlList.PublicReadWrite);
            String fileName = url.replaceAll(requestDomain + "/", "");
            ossClient.deleteObject(bucket, fileName);
            // 关闭OSSClient。
            ossClient.shutdown();
        } catch (Exception e) {
            log.error("oss 文件删除失败,exception:{}", e);
        }
    }
}
