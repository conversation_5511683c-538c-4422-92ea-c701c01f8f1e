package com.dz.common.core.dto.user;

import com.dz.common.core.annotation.Columns;
import com.dz.common.core.enums.ColumnType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 用户简要信息DTO
 * @author: Handy
 * @date:   2022/01/30 22:55
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "用户简要信息")
public class UserSimpleDTO {

    @ApiModelProperty(value = "用户ID")
    private Long id;
    @ApiModelProperty(value = "会员名")
    private String username;
    @ApiModelProperty(value = "小程序openid")
    private String openid;
    @ApiModelProperty(value = "微信unionid")
    private String unionid;
    @ApiModelProperty(value = "头像")
    private String avatar;
    @ApiModelProperty(value = "登录token")
    private String token;
    @ApiModelProperty(value = "会员卡号")
    private String cardNo;
    @ApiModelProperty(value = "手机号")
    private String mobile;
    @ApiModelProperty(value = "首购任务状态0未开始首购任务，1会小上线后三年前有订单，2完成首购任务")
    private Integer firstOrderStatus;
    @ApiModelProperty(value = "完成的首购任务id")
    private Long firstOrderTaskId;
    private Long tenantId;

}
