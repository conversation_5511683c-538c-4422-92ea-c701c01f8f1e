package com.dz.common.core.utils;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程池
 * @Author: Handy
 * @Date: 2022/8/6 0:05
 */
public class ThreadPoolUtils {

    public static ExecutorService pool = new ThreadPoolExecutor(
            10, 50, 3, TimeUnit.MILLISECONDS,
            new ArrayBlockingQueue<Runnable>(10000), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());

}
