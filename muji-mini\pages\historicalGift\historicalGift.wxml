<my-page loading="{{loading}}" overallModal="{{overallModal}}">
  <view class="gray-bg">
    <custom-header type="{{1}}" isBackHidden="{{ isBackHidden }}">
      <view slot="content" class="page-title">
        <text>历史礼券</text>
      </view>
    </custom-header>
    <view class="page-nav">
      <basic-tabs labelKey="label" valueKey="value" width="{{100}}" tabs="{{tabList}}" buttom="{{10}}" lineType="{{false}}" Fbuttom="{{12}}" active="{{activeTab}}" AfontWeight="{{600}}" lineHeight="{{42}}" fontWeight="{{400}}" fontSize="{{28}}" bindchange="onChangeTab" />
      <!-- <van-tabs custom-class="nav-container" nav-class="nav-box" offset-top="30" active="{{ activeTab }}"
        bind:change="onChangeTab" color="#3C3C43" line-width="64" line-height="2" title-active-color="#3C3C43"
        title-inactive-color="#888">
        <van-tab wx:for="{{tabList}}" name="{{item.value}}" wx:key="value" title="{{item.label}}"
          title-style="font-size: 16px;" />
      </van-tabs> -->
    </view>
    <scroll-view scroll-y="{{currentList.length>0}}" scroll-top="{{scrollTop}}" class="page-content" bindscrolltolower="onReachBottom">
      <view class="list-box">
        <view class="list-item" wx:for="{{currentList}}" wx:key="couponID">
          <coupon-item item="{{item}}" />
        </view>
        <block wx:if="{{!loading}}">
          <no-data-available top="{{496}}" data="{{currentList}}" text="暂无礼券" />
        </block>
      </view>
    </scroll-view>
  </view>


</my-page>
<!-- <view class="footer-li">
  <view class="footer-text" bindtap="tolishi" >历史礼券<view class="footer-line"></view></view>
</view> -->