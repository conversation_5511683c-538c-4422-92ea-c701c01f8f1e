@import "assets/scss/config";

.WidgetHomeVipUpgrade {
  --curent: 0; // 升级到下一级已拥有的里程数
  --total: 100; // 升级到下一级需要的里程数
  --currentScale: 0%; // 升级到下一级已拥有的里程数 百分比
  --lastScale: 100%; // 升级到下一级还需要的里程数
  --currentRotate: 53.95deg; // min：53.95deg、max：126.2deg 以iPhoneX为基准
  --opacity: 0;
  position: relative;
  background: var(--homeVipBg);

  &-visitor {
    position: absolute;
    z-index: 102;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
  }

  &.isCardLevel4 {
    ._vip-type-left {
      color: #bbbbbb;
    }

    ._vip-progress-wrap ._vip-progress-point-wrap ._vip-progress-point-left {
      border-color: rgba(60, 60, 67, 0.2);
    }
  }

  ._padding-wrap {
    padding: px2rpx(40) px2rpx(50) 0;
  }

  ._statusBarHeight-fixed {
    box-sizing: border-box;
    padding: 0 px2rpx(32) 0 px2rpx(40);
    height: inherit;
    width: 100%;
    position: fixed;
    z-index: 49;
    left: 0;
  }

  ._navBarHeight-fixed {
    box-sizing: border-box;
    padding: 0 px2rpx(32) 0 px2rpx(40);
    height: inherit;
    width: 100%;
    position: fixed;
    z-index: 49;
    left: 0;
    display: flex;
    align-items: center;

    ._logo {
      width: px2rpx(292);
      height: px2rpx(40);
    }
  }

  ._vip-no-login-wrap {
    width: px2rpx(650);
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: px2rpx(59) px2rpx(18) 0 px2rpx(10);

    ._vip-no-login-left {
      color: rgba(60, 60, 67, 1);
      font-size: px2rpx(36);
      font-weight: 600;
      line-height: px2rpx(42);
      margin-top: px2rpx(9);
    }

    ._vip-no-login-right {
      background-color: rgba(60, 60, 67, 1);
      border-radius: px2rpx(5);
      display: flex;
      flex-direction: column;
      padding: px2rpx(16) px2rpx(30) px2rpx(18);
      color: rgba(255, 255, 255, 1);
      font-size: px2rpx(24);
      font-weight: 500;
    }
  }

  ._vip-progress-wrap {
    $width: 560;
    $scale: 1.68;
    position: relative;
    margin: 0 auto px2rpx(40);
    width: px2rpx(560);
    height: px2rpx(94);

    ._vip-progress-point-wrap {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      z-index: 9;

      ._vip-progress-point-left {
        position: absolute;
        left: px2rpx(-12);
        bottom: px2rpx(-12);
        z-index: 9;
        width: px2rpx(18);
        height: px2rpx(18);
        border-radius: 50%;
        border: px2rpx(6) solid transparent;
        background: #3c3c43;
        background-clip: content-box;
      }

      ._vip-progress-point-right {
        position: absolute;
        right: px2rpx(-12);
        bottom: px2rpx(-12);
        z-index: 9;
        width: px2rpx(18);
        height: px2rpx(18);
        border-radius: 50%;
        border: px2rpx(6) solid transparent;
        background: #BBBBBB;
        background-clip: content-box;
      }
    }

    ._vip-progress-line-wrap {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      z-index: 9;

      ._vip-progress-line1-wrap {
        //background: rgba(0, 255, 0, 0.2);
        overflow: hidden;
        position: absolute;
        left: 0;
        top: 0;
        width: var(--currentScale);
        height: 100%;
        transition: 0.6s;

        ._vip-progress-line1 {
          box-sizing: border-box;
          position: absolute;
          left: px2rpx($width/2);
          top: 0;
          transform: translateX(-50%);
          width: px2rpx($width*$scale);
          height: px2rpx($width*$scale);
          border-radius: 50%;
          border: px2rpx(2) solid #3C3C43;
        }
      }

      ._vip-progress-line2-wrap {
        //background: rgba(0, 0, 255, 0.2);
        overflow: hidden;
        position: absolute;
        right: 0;
        top: 0;
        width: var(--lastScale);
        height: 100%;
        transition: 0.6s;

        ._vip-progress-line2 {
          box-sizing: border-box;
          position: absolute;
          right: px2rpx($width/2);
          top: 0;
          transform: translateX(50%);
          width: px2rpx($width*$scale);
          height: px2rpx($width*$scale);
          border-radius: 50%;
          border: px2rpx(2) dashed #bbbbbb;
        }
      }

      ._vip-progress-line3-wrap {
        pointer-events: none;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;

        ._vip-progress-line3 {
          box-sizing: border-box;
          position: absolute;
          left: px2rpx($width/2);
          top: 0;
          transform: translateX(-50%) rotate(var(--currentRotate));
          width: px2rpx($width*$scale);
          height: px2rpx($width*$scale);
          border-radius: 50%;
          transition: 0.6s;
        }

        ._vip-progress-point-move {
          position: absolute;
          left: 0;
          top: 50%;
          width: px2rpx(18);
          height: px2rpx(18);
          border: px2rpx(6) solid rgba(60, 60, 67, 0.2);
          border-radius: 50%;
          transform: translate(-50%, -50%);
          background: #3c3c43;
          background-clip: content-box;
        }
      }
    }
  }

  ._vip-type-wrap {
    display: flex;
    justify-content: space-between;
    margin: px2rpx(40) px2rpx(18) 0 px2rpx(10);
    color: rgba(60, 60, 67, 1);
  }

  ._vip-type-left {
    color: rgba(60, 60, 67, 1);
  }

  ._vip-type-title {
    font-size: px2rpx(40);
    font-weight: 700;
  }

  ._vip-type-subtitle {
    font-size: px2rpx(20);
    height: px2rpx(27);
    line-height: 1;
    margin: px2rpx(17) 0 0 0;
    display: flex;
    align-items: center;
  }

  ._vip-type-right {
    text-align: right;
  }

  ._vip-type-more-icon {
    width: px2rpx(12);
    font-size: px2rpx(12);
    margin: px2rpx(-1) 0 0 px2rpx(5);
    color: #3C3C43;
  }

  ._vip-name-wrap {
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: px2rpx(40) px2rpx(18) 0 px2rpx(10);
  }

  ._vip-name-left {
    color: rgba(60, 60, 67, 1);
    font-size: px2rpx(32);
    font-weight: 700;
    line-height: px2rpx(42);
    max-width: px2rpx(350);
    @include ellipsis1();
  }

  ._vip-name-right {
    display: flex;
    text-align: center;
  }

  ._vip-score {
    position: relative;
    color: rgba(60, 60, 67, 1);

    ._vip-score-title {
      font-size: px2rpx(32);
      font-weight: 700;
      line-height: px2rpx(42);
      align-self: center;
      max-width: px2rpx(120);
      @include ellipsis1();
    }

    ._vip-score-subtitle {
      font-weight: 500;
      font-size: px2rpx(18);
      margin-top: px2rpx(5);
    }

    ._vip-score-red {
      position: absolute;
      top: 0;
      right: -10rpx;
      width: 12rpx;
      height: 12rpx;
      background: #7F0019;
      border-radius: 50%;
    }
  }

  ._vip-score-gap-line {
    width: px2rpx(2);
    height: px2rpx(20);
    background: rgba(0, 0, 0, 1);
    margin: px2rpx(28) px2rpx(41) px2rpx(27) px2rpx(40);
  }

  ._vip-more-info-wrap {
    box-sizing: border-box;
    background-color: rgba(255, 255, 255, 1);
    width: px2rpx(650);
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: px2rpx(30);
    padding: px2rpx(18) px2rpx(30);
    border-radius: px2rpx(8);
    position: relative;

    ._triangle {
      width: 0;
      height: 0;
      border: px2rpx(14) solid transparent;
      border-bottom-color: #fff;
      position: absolute;
      left: px2rpx(36);
      top: px2rpx(-26);
    }

    ._vip-more-info-left {
      width: px2rpx(404);
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    ._vip-more-info-left-icon {
      font-size: px2rpx(36);
    }

    ._vip-more-info-text {
      font-weight: 500;
      color: rgba(60, 60, 67, 1);
      font-size: px2rpx(22);
      line-height: px2rpx(30);
    }

    ._vip-more-info-right-icon {
      font-size: px2rpx(24);
    }
  }

  ._vip-benefit-wrap {
    margin-top: px2rpx(30);
    position: relative;
    padding-bottom: px2rpx(16);



    ._vip-benefit-img {
      position: relative;
      z-index: 2;
      display: block;
      width: px2rpx(612);
      height: px2rpx(236);
      margin: 0 auto;
    }

    ._vip-benefit-bottom-bg {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: px2rpx(46);
    }
  }

  ._placeholder {
    height: px2rpx(20);
    background: #ffffff;
  }
}