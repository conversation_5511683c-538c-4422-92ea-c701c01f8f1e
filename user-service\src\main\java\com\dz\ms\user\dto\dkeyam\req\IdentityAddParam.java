package com.dz.ms.user.dto.dkeyam.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 新增Mac地址黑白名单 入参
 * @author: fei
 * @date: 2025/01/22 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class IdentityAddParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("管理员登录名")
    private String adminLoginName;

    @ApiModelProperty("管理员密码")
    private String adminPassword;

    @ApiModelProperty("站点名称")
    private String tenantName;

    @ApiModelProperty("Mac地址")
    private String macAddress;

    @ApiModelProperty("类型：1：禁止 2：有效")
    private Integer type;

    @ApiModelProperty("有效开始时间(毫秒时间戳)")
    private Long startDate;

    @ApiModelProperty("有效结束时间(毫秒时间戳)")
    private Long endDate;

    @ApiModelProperty("描述")
    private String description;


}
