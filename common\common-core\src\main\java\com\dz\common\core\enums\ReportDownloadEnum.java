package com.dz.common.core.enums;

import com.dz.common.core.exception.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@AllArgsConstructor
@Getter
public enum ReportDownloadEnum {

    PRODUCT_LIST(1, "productListDownLoadStrategy", "product_list", "商品列表", "商品列表", "商品"),
    SHELF_LIST(2, "shelfListDownLoadStrategy", "shelf_list", "货架列表", "货架列表", "商品"),
    SHELF_PROMOTION_LIST(3, "shelfPromotionListDownLoadStrategy", "shelf_promotion_list", "货架推广列表", "货架推广列表", "商品"),
    EXCHANGE_ORDER_LIST(4, "exchangeOrderListDownLoadStrategy", "exchange_order_list", "订单列表", "订单列表", "订单"),
    EXPORT_TASK_LIST(5, "interactionTaskListDownLoadStrategy", "export_task_list", "任务列表", "任务列表", "任务列表"),
    CAMPAIGN_LIST(6, "campaignListDownLoadStrategy", "campaign_list", "人群限购列表", "人群限购列表", "商品"),
    CP_SELL_LIST(7, "cpSellListDownLoadStrategy", "cp_sell_list", "兑礼cp数据列表", "兑礼cp数据列表", "商品"),
    SIGN_IN_DETAIL_LIST(8, "signInDetailListDownLoadStrategy", "sign_in_detail_list", "打卡数据列表", "打卡数据列表", "销售"),


    ;
    private final Integer type;

    private final String beanName;

    private final String reportCode;

    private final String fileName;

    private final String menuName;

    private final String moduleName;

    public static ReportDownloadEnum typeOf(Integer type) {
        return Arrays.stream(ReportDownloadEnum.values()).filter(reportDownloadEnum -> reportDownloadEnum.type.equals(type))
                .findFirst().orElseThrow(() -> new BusinessException("没有对应的报表类型"));
    }

}
