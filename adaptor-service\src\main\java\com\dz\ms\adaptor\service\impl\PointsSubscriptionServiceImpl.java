package com.dz.ms.adaptor.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.Result;
import com.dz.common.core.constants.CommonConstants;
import com.dz.common.core.dto.adaptor.SftpDTO;
import com.dz.common.core.dto.basic.SubscribeMsgSendDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.dto.user.UserSimpleDTO;
import com.dz.common.core.enums.SubscribeMsgEnum;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.basic.MpMsgFeignClient;
import com.dz.common.core.fegin.user.MUJIOpenApiFeignClient;
import com.dz.common.core.fegin.user.UserInfoFeginClient;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.CommonUtils;
import com.dz.common.core.utils.SftpUtils;
import com.dz.common.core.vo.MyMsgVo;
import com.dz.ms.adaptor.constants.CacheKeys;
import com.dz.ms.adaptor.dto.CrmPointsSyncParamDTO;
import com.dz.ms.adaptor.dto.MqCrmPointsSyncMessageDTO;
import com.dz.ms.adaptor.entity.ExpirePointsRecord;
import com.dz.ms.adaptor.mapper.PointsSubscriptionRecordMapper;
import com.dz.ms.adaptor.mapper.ThirdDKeyAmRecordMapper;
import com.dz.ms.adaptor.mapper.ThirdPartyRecordMapper;
import com.dz.ms.adaptor.service.PointsSubscriptionService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**

 */
@Slf4j
@Service
public class PointsSubscriptionServiceImpl implements PointsSubscriptionService {

//    @Value("${sftp.config.host}")
//    private String host;
//    @Value("${sftp.config.port}")
//    private Integer port;
//    @Value("${sftp.config.userName}")
//    private String userName;
//    @Value("${sftp.config.password}")
//    private String password;
    @Autowired
    private PointsSubscriptionRecordMapper pointsSubscriptionRecordMapper;
    @Autowired
    private UserInfoFeginClient userInfoFeginClient;
    @Autowired
    private MUJIOpenApiFeignClient mujiOpenApiFeignClient;
    @Autowired
    private MpMsgFeignClient mpMsgFeignClient;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    @Qualifier("pointsExecutor")
    private ThreadPoolTaskExecutor pointsExecutor;
    private static final Semaphore semaphore = new Semaphore(25);

    private static final String DATE_YYYY_MM = "yyyyMM";
    @Resource
    private RedisService redisService;

    @Override
    public void pointsSubscription(String jobParams) {
        CurrentUserDTO userInfoDTO = new CurrentUserDTO();
        userInfoDTO.setTenantId(1L);
        SecurityContext.setUser(userInfoDTO);
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_YYYY_MM);
        Date now = new Date();
        String nowDay = sdf.format(now);
        // 分页查询ExpirePointsRecord
        Integer pageNum = 0;
        Integer pageSize = 1000;
        List<ExpirePointsRecord> expirePointsRecordList = new ArrayList<>();
        while (true) {
            pageNum+=1;
            IPage<ExpirePointsRecord> page=new Page<>();
            List<ExpirePointsRecord> expiredPointsRecordListAll = new ArrayList<>();
            if (!StringUtils.isEmpty(jobParams)){
                expiredPointsRecordListAll = pointsSubscriptionRecordMapper.selectPageByParamStatus(nowDay);
                page.setRecords(expiredPointsRecordListAll);
            }else{
                page = pointsSubscriptionRecordMapper.selectPageByParam(new Page<>(pageNum, pageSize),nowDay);
            }
            if (!CollectionUtils.isEmpty(page.getRecords())){
                List<SubscribeMsgSendDTO> subscribeMsgSendDTOList = new ArrayList<>();
                List<SubscribeMsgSendDTO> subscribeMsgSendDTOListFail = new ArrayList<>();
                //获取page.getRecords()中memberCode不是null的值并去重的list
                List<String> memberCodeList = page.getRecords().stream().filter(record -> !StringUtils.isEmpty(record.getMemberCode())).map(ExpirePointsRecord::getMemberCode).distinct().collect(Collectors.toList());
                //查询用户信息
                Result<List<UserSimpleDTO>> userSimpleDTOResult =  userInfoFeginClient.getDbUserSimpleInfo(memberCodeList);
                if (null == userSimpleDTOResult || CollectionUtils.isEmpty(userSimpleDTOResult.getData())){
                    continue;
                }
                for (ExpirePointsRecord myRecord : page.getRecords()){
                    //获取userSimpleDTOResult.getData()中cardNo等于myRecord.getMemberCode()的值
                    UserSimpleDTO userSimpleDTO = userSimpleDTOResult.getData().stream().filter(userSimpleDTO1 -> userSimpleDTO1.getCardNo().equals(myRecord.getMemberCode())).findFirst().orElse(null);
                    if (null == userSimpleDTO){
                        continue;
                    }
                    expirePointsRecordList.add(myRecord);
                    //组装订阅消息
                    SubscribeMsgSendDTO subscribeMsgSendDTO = new SubscribeMsgSendDTO();
                    subscribeMsgSendDTO.setMsgCode(SubscribeMsgEnum.POINTS_EXPIRE.getMsgCode());
                    List<Long> uids = new ArrayList<>();
                    uids.add(userSimpleDTO.getId());
                    subscribeMsgSendDTO.setUids(uids);
                    String[] content = new String[4];
                    String nikeName="";
                    if (StringUtils.isEmpty(userSimpleDTO.getUsername())){
                        nikeName="亲爱的良友";
                    }else if(userSimpleDTO.getUsername().equals("无印良品会员")){
                        nikeName="亲爱的良友";
                    }else{
                        nikeName=userSimpleDTO.getUsername();
                    }
                    content[0]=nikeName;
                    if (null != myRecord.getBonusAmount()){
                        content[1]=""+myRecord.getBonusAmount();
                    }else{
                        content[1]="";
                    }
                    content[2]= String.valueOf(myRecord.getExpirePoints());
                    content[3]="您的一些积分即将过期，去积分商城看看吧";
                    subscribeMsgSendDTO.setContent(content);
                    subscribeMsgSendDTOList.add(subscribeMsgSendDTO);
                }
                if (!CollectionUtils.isEmpty(subscribeMsgSendDTOList)){
                    for (SubscribeMsgSendDTO subscribeMsgSendDTO : subscribeMsgSendDTOList){
                        try {
                            Thread.sleep(150);
                            pointsExecutor.execute(() -> {
                                try {
                                    semaphore.acquire();
                                    SecurityContext.setUser(userInfoDTO);
                                    Result<Boolean> flag=mpMsgFeignClient.sendMiniappSubscribeMsg(subscribeMsgSendDTO, SecurityContext.getUser().getTenantId());
                                } catch (Exception e) {
                                    log.info("积分订阅消息发送失败，失败原因："+e.getMessage());
                                }finally {
                                    semaphore.release();
                                }
                            });
                        }catch (Exception e){
                            subscribeMsgSendDTOListFail.add(subscribeMsgSendDTO);
                        }

                    }
                    subscribeMsgSendDTOList.clear();
                }
                for (ExpirePointsRecord expirePointsRecord :expirePointsRecordList){
                    pointsSubscriptionRecordMapper.updateSubscriptionStatusById(nowDay,expirePointsRecord.getId());
                }
                if (!CollectionUtils.isEmpty(subscribeMsgSendDTOListFail)){
                    for (SubscribeMsgSendDTO subscribeMsgSendDTO : subscribeMsgSendDTOListFail){
                        //获取userSimpleDTOResult.getData()中id等于subscribeMsgSendDTO.getuids(0)的值
                        UserSimpleDTO userSimpleDTO = userSimpleDTOResult.getData().stream().filter(userSimpleDTO1 -> userSimpleDTO1.getId().equals(subscribeMsgSendDTO.getUids().get(0))).findFirst().orElse(null);
                        if (null != userSimpleDTO){
                            //获取expirePointsRecordList中memberCode等于userSimpleDTO.getCardNo()的值
                            ExpirePointsRecord expirePointsRecord = expirePointsRecordList.stream().filter(record -> record.getMemberCode().equals(userSimpleDTO.getCardNo())).findFirst().orElse(null);
                            if (null != expirePointsRecord){
                                pointsSubscriptionRecordMapper.updateSubscriptionStatusByIdFail(nowDay,expirePointsRecord.getId());
                            }
                        }
                    }
                }
                subscribeMsgSendDTOListFail.clear();
                expirePointsRecordList.clear();
                if (!StringUtils.isEmpty(jobParams)){//如果是备用捞取失败的记录进行重新发送，一轮结束后则跳出循环
                    log.info("捞取失败的记录进行重新发送订阅消息完成");
                    break;
                }
            }else{
                break;
            }
        }
        log.info("过期积分发送订阅消息完成");
    }
    @Override
    public void sendMyMsg(String jobParams) throws ParseException {
        CurrentUserDTO userInfoDTO = new CurrentUserDTO();
        userInfoDTO.setTenantId(1L);
        SecurityContext.setUser(userInfoDTO);
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_YYYY_MM);
        Date now = new Date();
        String nowDay = sdf.format(now);
        // 分页查询ExpirePointsRecord
        Integer pageNum = 0;
        Integer pageSize = 1000;
        List<ExpirePointsRecord> expirePointsRecordList = new ArrayList<>();
        while (true) {
            pageNum+=1;
            IPage<ExpirePointsRecord> page=new Page<>();
            List<ExpirePointsRecord> expiredPointsRecordListAll = new ArrayList<>();
            if (!StringUtils.isEmpty(jobParams)){
                expiredPointsRecordListAll = pointsSubscriptionRecordMapper.selectPageByParamMsgStatus(nowDay);
                page.setRecords(expiredPointsRecordListAll);
            }else{
                page = pointsSubscriptionRecordMapper.selectPageByParamMsg(new Page<>(pageNum, pageSize),nowDay);
            }
            if (!CollectionUtils.isEmpty(page.getRecords())){
                List<MyMsgVo> myMsgVoList = new ArrayList<>();
                //获取page.getRecords()中memberCode不是null的值并去重的list
                List<String> memberCodeList = page.getRecords().stream().filter(record -> !StringUtils.isEmpty(record.getMemberCode())).map(ExpirePointsRecord::getMemberCode).distinct().collect(Collectors.toList());
                //查询用户信息
                Result<List<UserSimpleDTO>> userSimpleDTOResult =  userInfoFeginClient.getDbUserSimpleInfo(memberCodeList);
                if (null == userSimpleDTOResult || null == userSimpleDTOResult.getData()){
                    continue;
                }
                for (ExpirePointsRecord myRecord : page.getRecords()){
                    //获取userSimpleDTOResult.getData()中cardNo等于myRecord.getMemberCode()的值
                    UserSimpleDTO userSimpleDTO = userSimpleDTOResult.getData().stream().filter(userSimpleDTO1 -> userSimpleDTO1.getCardNo().equals(myRecord.getMemberCode())).findFirst().orElse(null);
                    if (null == userSimpleDTO){
                        continue;
                    }
                    expirePointsRecordList.add(myRecord);
                    //组装我的消息
                    MyMsgVo myMsgVo = new MyMsgVo();
                    myMsgVo.setUserId(userSimpleDTO.getId());
                    List<String> msgCode = new ArrayList<>();
                    msgCode.add("expirePoints");
                    myMsgVo.setMsgCode(msgCode);
                    myMsgVoList.add(myMsgVo);
                }
                if (!CollectionUtils.isEmpty(myMsgVoList)){
                    for (MyMsgVo myMsgVo : myMsgVoList){
                        mujiOpenApiFeignClient.add(myMsgVo);
                    }
                    myMsgVoList.clear();
                }
                for (ExpirePointsRecord expirePointsRecord :expirePointsRecordList){
                    pointsSubscriptionRecordMapper.updateSendMyMsgStatusById(nowDay,expirePointsRecord.getId());
                }
                expirePointsRecordList.clear();
            }else{
                break;
            }
        }
        log.info("过期积分发送我的消息完成");
    }

    /**
     * 废弃
     * @throws IOException
     */
    @Override
    public void getSftpFile() throws IOException {
        //
        Date date = new Date();
        // 指定日期格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        // 格式化为字符串
        String formattedDate = sdf.format(date);
        String fileName = "shop"+formattedDate+".csv";
        SftpDTO sftpDTO = new SftpDTO();
        sftpDTO.setFilePath("/shop");
        sftpDTO.setFileName(fileName);
//        sftpDTO.setHost(host);
//        sftpDTO.setPort(port);
//        sftpDTO.setUserName(userName);
//        sftpDTO.setPass(password);
        SftpDTO sftpFile = SftpUtils.getSftpFile(sftpDTO);
        //获取文件
        if (sftpFile != null) {
            dealSftpPointsData(sftpFile.getIn());
            sftpFile.getIn().close();
            SftpUtils.sessionClose();
            SftpUtils.sftpClose(sftpFile.getSftp());
        }
    }

    @Override
    public void crmPointsSync(List<CrmPointsSyncParamDTO> list) {
        if(CollectionUtils.isEmpty(list)){
            throw new BusinessException(ErrorCode.BAD_REQUEST, "数据不能为空");
        }
        Date date = new Date();
        for (CrmPointsSyncParamDTO dto : list) {
            dto.setCreateTime(date);
            dto.setTenantId(SecurityContext.getUser().getTenantId());
        }
        List<List<CrmPointsSyncParamDTO>> partition = Lists.partition(list, 101);
        for (List<CrmPointsSyncParamDTO> paramDTOList : partition) {
            MqCrmPointsSyncMessageDTO mqDTO = new MqCrmPointsSyncMessageDTO();
            mqDTO.setList(paramDTOList);
            mqDTO.setTenantId(SecurityContext.getUser().getTenantId());
            mqDTO.setCreateTime(date);
            rabbitTemplate.convertAndSend("adaptor.message", "pointsmessage", CommonUtils.jsonStr(mqDTO));
        }
    }

    @Override
    public Integer insertBatchSomeColumn(List<CrmPointsSyncParamDTO> paramDTOList, String nowDay) {
        if(!CollectionUtils.isEmpty(paramDTOList)){
            List<ExpirePointsRecord> list = BeanCopierUtils.convertList(paramDTOList,ExpirePointsRecord.class);
            return pointsSubscriptionRecordMapper.insertBatchSomeColumn(list, nowDay);
        }
        return null;
    }

    /**
     * 废弃
     * @throws IOException
     */
    private void dealSftpPointsData(InputStream data) {
        CurrentUserDTO userInfoDTO = new CurrentUserDTO();
        userInfoDTO.setTenantId(1L);
        SecurityContext.setUser(userInfoDTO);
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_YYYY_MM);
        Date now = new Date();
        String nowDay = sdf.format(now);
        subTableList(nowDay);
        // 收集需要更新的数据
        List<ExpirePointsRecord> pointsToSaveList = new ArrayList<>();
        try (Reader reader = new InputStreamReader(data);
             CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT.withFirstRecordAsHeader())) {
            int i=0;
            for (CSVRecord csvRecord : csvParser) {
                i+=1;
                String memberCode = csvRecord.get("shop_id_str");
                String expiresPoints = csvRecord.get("shop_name_str");
                ExpirePointsRecord expiresPointsRecord = new ExpirePointsRecord();
                expiresPointsRecord.setMemberCode(memberCode);
                expiresPointsRecord.setExpirePoints(Integer.valueOf(expiresPoints));
                expiresPointsRecord.setCreateTime(now);
                expiresPointsRecord.setTenantId(SecurityContext.getUser().getTenantId());
                pointsToSaveList.add(expiresPointsRecord);
                try {
                    if (pointsToSaveList.size() >= 5000){
                        log.info("存入需要发送的积分过期用户批次："+i);
                        Runnable runnable = () -> {
                            SecurityContext.setUser(userInfoDTO);
                            pointsSubscriptionRecordMapper.insertBatchSomeColumn(pointsToSaveList, nowDay);
                        };
//                        executorService.execute(runnable);
                        pointsToSaveList.clear();
                    }
                }catch (Exception e){
                    log.error("Error sftp user expire points CSV file:"+e.getMessage());
                }
            }
            Runnable runnable = () -> {
                SecurityContext.setUser(userInfoDTO);
                pointsSubscriptionRecordMapper.insertBatchSomeColumn(pointsToSaveList, nowDay);
            };
//            executorService.execute(runnable);
            log.info("sftp user expire points data processed successfully");
        } catch (Exception e) {
            log.error("Error sftp user expire points CSV file:"+e.getMessage());
        }
    }

    /**
     * 判断当月消息表是否存在，不存在则新建
     * @param cdpDay
     */
    @Override
    public String subTableList(String cdpDay) {
        String key = CacheKeys.Checks.T_EXPIRE_POINTS_RECORD_MONTH;
        String checkStr = redisService.getString(key);
        if(org.apache.commons.lang3.StringUtils.isNotBlank(checkStr) && org.apache.commons.lang3.StringUtils.equals(checkStr, cdpDay)){
            return cdpDay;
        }
        for (int i = 0; i < 3; i++) {
            try {
                List<String> tableList = pointsSubscriptionRecordMapper.queryPointsSubscriptionTableList();
                boolean hasCurrentTable = false;
                if (!CollectionUtils.isEmpty(tableList)) {
                    for (String table : tableList) {
                        String newTableName="t_expire_points_record_"+cdpDay;
                        if (newTableName.equals(table)) {
                            hasCurrentTable = true;
                            String sevenDayAgo = LocalDate.now().minusDays(6).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                            pointsSubscriptionRecordMapper.deleteBySevenDayAgo(cdpDay,sevenDayAgo);
                            redisService.setString(key,cdpDay, CommonConstants.HOUR_SECONDS);
                        }
                    }
                }
                if (!hasCurrentTable) {
                    pointsSubscriptionRecordMapper.createPointsSubscriptionTable(cdpDay);
                }
                return cdpDay;
            } catch (Exception e) {
                log.error(cdpDay + "创建t_expire_points_record失败,i:" + i, e);
            }
        }
        return null;
    }
}
