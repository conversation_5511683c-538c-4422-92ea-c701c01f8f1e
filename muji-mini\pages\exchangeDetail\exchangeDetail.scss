/* pages/exchangeDetail/exchangeDetail.wxss */
.page-content {
  padding: 0rpx 40rpx 0;
  box-sizing: border-box;
  width: 100%;

  .box {
    margin-top: 40rpx;
    margin-bottom: 60rpx;
  }

  .title-box {
    height: 40rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #000000;
    line-height: 40rpx;
    text-align: left;
    font-style: normal;
    margin-bottom: 40rpx;
  }

  .list-box {
    border-bottom: 1rpx solid #eee;

    .product-item {
      margin-bottom: 60rpx;
    }
  }

  .info-ele {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30rpx;
    align-items: center;

    .ele-label {
      height: 40rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #888888;
      line-height: 40rpx;
      text-align: left;
      font-style: normal;
    }

    .ele-value {
      height: 40rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #000000;
      line-height: 40rpx;
      text-align: right;
      font-style: normal;
    }
  }
}
