package com.dz.ms.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 图形验证码返回参数
 * @author: Handy
 * @date:   2022/1/30 23:04
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "图形验证码返回参数")
public class ImageCodeDTO {

    @ApiModelProperty(value = "图形验证码key")
    private String codeKey;
    @ApiModelProperty(value = "验证码图片Base64")
    private String imageBase64;

}
