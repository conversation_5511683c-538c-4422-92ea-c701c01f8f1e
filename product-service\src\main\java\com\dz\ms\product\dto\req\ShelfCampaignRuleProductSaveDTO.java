package com.dz.ms.product.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;

/**
 * 营销活动规则关联的货架商品保存入参
 * @author: fei
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@ApiModel(value = "营销活动规则关联的货架商品保存入参")
public class ShelfCampaignRuleProductSaveDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "营销活动ID")
    private Long campaignId;
    @ApiModelProperty(value = "营销活动规则ID")
    private Long ruleId;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "货架商品ID")
    private Long shelfProductId;
    @ApiModelProperty(value = "货架商品名称")
    private String shelfProductName;
    @ApiModelProperty(value = "商品ID")
    private Long productId;
    @ApiModelProperty(value = "1同当前库存 2活动规则库存")
    private Integer inventoryType;
    @ApiModelProperty(value = "活动规则库存")
    private Integer ruleInventory;
    @ApiModelProperty(value = "兑换积分")
    private Integer costPoint;
    @ApiModelProperty(value = "积分划线价")
    private Integer prePoint;
    @ApiModelProperty(value = "每人限购数")
    private Integer everyoneLimit;
    @ApiModelProperty(value = "规则开始时间")
    private Date ruleCreated;

}
