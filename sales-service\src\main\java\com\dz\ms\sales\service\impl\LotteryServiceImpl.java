package com.dz.ms.sales.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.annotation.Lock;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.dto.user.UserSimpleDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.user.MUJIOpenApiFeignClient;
import com.dz.common.core.fegin.user.UserInfoFeginClient;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.vo.ReceiveCouponVo;
import com.dz.ms.sales.dto.CampaignDTO;
import com.dz.ms.sales.dto.LotteryPrizesDTO;
import com.dz.ms.sales.dto.UserLotteryBasicDTO;
import com.dz.ms.sales.dto.UserLotteryPrizesDTO;
import com.dz.ms.sales.entity.*;
import com.dz.ms.sales.mapper.CampaignUserMapper;
import com.dz.ms.sales.mapper.CampaignUserShareMapper;
import com.dz.ms.sales.mapper.LotteryPrizesMapper;
import com.dz.ms.sales.mapper.UserLotteryPrizesMapper;
import com.dz.ms.sales.service.CampaignService;
import com.dz.ms.sales.service.LotteryService;
import com.dz.ms.sales.service.UserLotterySummaryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/5/21
 */
@Service
@Slf4j
public class LotteryServiceImpl implements LotteryService {
    @Resource
    private CampaignService campaignService;
    @Resource
    private CampaignUserMapper campaignUserMapper;
    @Resource
    private UserLotteryPrizesMapper userLotteryPrizesMapper;
    @Resource
    private LotteryPrizesMapper lotteryPrizesMapper;
    @Resource
    private UserInfoFeginClient userInfoFeginClient;
    @Resource
    private UserLotterySummaryService userLotterySummaryService;
    @Resource
    private CampaignUserShareMapper campaignUserShareMapper;
    @Resource
    private MUJIOpenApiFeignClient mujiOpenApiFeignClient;

    private final Random random = new Random();
    private static final String miniCampaign="MINI_INTERACT_CAMPAIGN";

    @Override
    public List<LotteryPrizesDTO> getPrizes(String campaignCode) {
        log.info("getPrizes:{}", campaignCode);
        CampaignDTO campaignInfoCache = campaignService.getCampaignInfoCache(campaignCode);
        if (ObjectUtils.isEmpty(campaignInfoCache)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "活动不存在");
        }
        List<LotteryPrizes> lotteryAwardList = lotteryPrizesMapper.selectList(new LambdaQueryWrapper<LotteryPrizes>().eq(LotteryPrizes::getCampaignCode, campaignCode).eq(LotteryPrizes::getIsShow, 1));
        return BeanCopierUtils.convertList(lotteryAwardList, LotteryPrizesDTO.class);
    }

    @Lock(prefix = "lottery:submit", key = "'#tenantId'+'#campaignCode'+'#uid'")
    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public LotteryPrizesDTO lottery(String campaignCode, Long tenantId, Long uid) {
        UserSimpleDTO currentUserInfo = userInfoFeginClient.getCurrentUserSimpleInfo().getData();

        CampaignDTO campaign = campaignService.getCampaignInfoCache(campaignCode);
        if (ObjectUtils.isEmpty(campaign)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "活动不存在");
        }
        if (campaign.getState() != 1) {
            log.info("活动状态: {}, 活动详情: {}", new String[]{"未开始", "进行中", "公示期", "已结束"}[campaign.getState()], JSON.toJSONString(campaign));
            throw new BusinessException(ErrorCode.BAD_REQUEST, "活动" + new String[]{"未开始", "进行中", "公示期", "已结束"}[campaign.getState()]);
        }

        CampaignUser campaignUser = campaignUserMapper.selectOne(new LambdaQueryWrapper<CampaignUser>().eq(CampaignUser::getCampaignCode, campaignCode).eq(CampaignUser::getMobile, currentUserInfo.getMobile()));
        if (ObjectUtils.isEmpty(campaignUser)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "还未获取抽奖资格");
        }


        Date now = new Date();
        // 抽一次奖消耗
        //先查已使用机会
        Long lotteryNum = userLotteryPrizesMapper.selectCount(new LambdaQueryWrapper<UserLotteryPrizes>().eq(UserLotteryPrizes::getCampaignCode, campaign.getCampaignCode()).eq(UserLotteryPrizes::getUnionid, currentUserInfo.getUnionid()).isNotNull(UserLotteryPrizes::getPrizesId));

        UserLotterySummary userLotterySummary = userLotterySummaryService.getUserLotterySummary(campaignCode, currentUserInfo.getUnionid());

        if (lotteryNum >= userLotterySummary.getTotalCount()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "抽奖次数已达上限");
        }


        //获取奖品config
        List<LotteryPrizes> lotteryAwardList = lotteryPrizesMapper.selectList(new LambdaQueryWrapper<LotteryPrizes>().eq(LotteryPrizes::getCampaignCode, campaign.getCampaignCode()).in(LotteryPrizes::getTargetType, 0, campaignUser.getType()).eq(LotteryPrizes::getIsShow, 1));
        if (ObjectUtils.isEmpty(lotteryAwardList)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "获取不到抽奖奖品");
        }
//        // 抽奖规则
        LotteryPrizes lotteryPrizes = lotteryRule(lotteryAwardList, currentUserInfo, campaign);
//
        if (StringUtils.isBlank(currentUserInfo.getCardNo())) {
            if (StringUtils.isBlank(currentUserInfo.getCardNo())) {
                lotteryPrizes = lotteryPrizesMapper.selectOne(new LambdaQueryWrapper<LotteryPrizes>().eq(LotteryPrizes::getPrizesLevelCode, 5).last("limit 1"));
            }
        }

        // 先扣库存
        Boolean isResidue = lotteryPrizesMapper.lotteryResidueInventoryById(lotteryPrizes.getId());
        if (!isResidue) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "奖品已经抽完啦");
        }
        // 扣除抽奖次数
        userLotterySummaryService.operation(campaignCode, currentUserInfo.getUnionid(), "-", 1);

        UserLotteryPrizes userLotteryPrizes = new UserLotteryPrizes(campaign.getCampaignCode(), campaignUser.getType(),
                currentUserInfo.getId(), currentUserInfo.getOpenid(), currentUserInfo.getUnionid(), currentUserInfo.getUsername(), currentUserInfo.getMobile(), currentUserInfo.getCardNo(),
                lotteryPrizes.getId(), lotteryPrizes.getPrizesName(), lotteryPrizes.getType(), lotteryPrizes.getPrizesLevelCode(), lotteryPrizes.getPrizesLevel(), lotteryPrizes.getImageUrl(), lotteryPrizes.getDetailImageUrl(), lotteryPrizes.getDescription(), null, lotteryPrizes.getPointsNum(), lotteryPrizes.getCouponCode());

        //再给奖励
        if (lotteryPrizes.getType() == 0) {
            // 谢谢参与
        } else if (lotteryPrizes.getType() == 1) {
            // 发积分
            mujiOpenApiFeignClient.addPoints(currentUserInfo.getId(), currentUserInfo.getCardNo(), miniCampaign, lotteryPrizes.getPointsNum(), "会员小程序活动奖励积分" + lotteryPrizes.getPointsNum());
        } else if (lotteryPrizes.getType() == 2) {
            // 发券
            String[] split = lotteryPrizes.getCouponCode().split(",");
            for (String couponCode : split) {
                ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo(couponCode, null, 2);
                mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
            }

        }
        //发放成功,数据留存
        userLotteryPrizesMapper.insert(userLotteryPrizes);

        return BeanCopierUtils.convertObject(lotteryPrizes, LotteryPrizesDTO.class);
    }

    @Override
    public UserLotteryBasicDTO getUserLotteryBasic(String campaignCode) {
        UserLotteryBasicDTO dto = new UserLotteryBasicDTO();
        UserSimpleDTO userInfo = userInfoFeginClient.getCurrentUserSimpleInfo().getData();
        UserLotterySummary userLotterySummary = userLotterySummaryService.getUserLotterySummary(campaignCode, userInfo.getUnionid());
        if (ObjectUtils.isNotEmpty(userLotterySummary)) {
            dto.setSurplusCount(userLotterySummary.getSurplusCount());
            dto.setTotalCount(userLotterySummary.getTotalCount());
        }
        dto.setIsShare(campaignUserShareMapper.selectCount(new LambdaQueryWrapper<CampaignUserShare>().eq(CampaignUserShare::getUnionid, userInfo.getUnionid()).eq(CampaignUserShare::getCampaignCode, campaignCode)).intValue());
        return dto;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    @Lock(prefix = "campaign:share", key = "'#tenantId'+'#uid'+'campaignCode'")
    public Boolean share(Long tenantId, Long uid, String campaignCode) {

        CampaignDTO campaign = campaignService.getCampaignInfoCache(campaignCode);
        if (ObjectUtils.isEmpty(campaign)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "活动不存在");
        }
        UserSimpleDTO userInfo = userInfoFeginClient.getCurrentUserSimpleInfo().getData();
        CampaignUserShare share = getBlessing(userInfo, campaignCode);

        if (ObjectUtils.isEmpty(share)) {
            int update = campaignUserShareMapper.insert(new CampaignUserShare(null, campaignCode, userInfo.getId(), userInfo.getUnionid()));
            if (update > 0) {
//                Lottery lottery = lotteryService.selectLottery();
//                if (ObjectUtils.isEmpty(lottery)) {
//                    throw new BusinessException("抽奖活动已结束");
//                }
                userLotterySummaryService.operation(campaignCode, userInfo.getUnionid(), "+", 1);
            }
            return SqlHelper.retBool(update);
        }
        return false;
    }


    @Override
    public PageInfo<UserLotteryPrizesDTO> getUserLotteryPrizes(String campaignCode, Integer pageNum, Integer pageSize) {
        UserSimpleDTO userInfo = userInfoFeginClient.getCurrentUserSimpleInfo().getData();
        LambdaQueryWrapper<UserLotteryPrizes> queryWrapper = new LambdaQueryWrapper<UserLotteryPrizes>();
        queryWrapper.eq(UserLotteryPrizes::getCampaignCode, campaignCode);
        queryWrapper.eq(UserLotteryPrizes::getUnionid, userInfo.getUnionid());
//        queryWrapper.last("order by CASE WHEN prizes_id IS NULL THEN 0 ELSE 1 END, prizes_id, id desc");
        queryWrapper.orderByAsc(UserLotteryPrizes::getId);
        IPage<UserLotteryPrizes> page = userLotteryPrizesMapper.selectPage(new Page<>(pageNum, pageSize), queryWrapper);
        return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), BeanCopierUtils.convertList(page.getRecords(), UserLotteryPrizesDTO.class));

    }

    @Override
    public void refreshLotteryInventory(String jobParams) {
        JSONObject jsonObject = JSONObject.parseObject(jobParams);
        Set<String> keys = jsonObject.keySet();
        for (String key : keys) {
            LotteryPrizes lotteryPrizes = lotteryPrizesMapper.selectById(Long.valueOf(key));
            LotteryPrizes edit = new LotteryPrizes();
            edit.setId(lotteryPrizes.getId());
            edit.setResidueInventory(lotteryPrizes.getResidueInventory() + jsonObject.getInteger(key));
            lotteryPrizesMapper.updateById(edit);
        }
    }


    //    @Cacheable(prefix = CacheKeys.share, key = "'#tenantId'+'campaignCode'+'#uid'")
    public CampaignUserShare getBlessing(UserSimpleDTO userInfo, String campaignCode) {
        CampaignUserShare campaignUserShare = campaignUserShareMapper.selectOne(new LambdaQueryWrapper<CampaignUserShare>().eq(CampaignUserShare::getUnionid, userInfo.getUnionid()).eq(CampaignUserShare::getCampaignCode, campaignCode).orderByDesc(CampaignUserShare::getId).last("limit 1"));
        return campaignUserShare;
    }


    private LotteryPrizes lottery(List<LotteryPrizes> lotteryAwardList) {
        // 计算有效奖品的总概率
        double totalValidProbability = lotteryAwardList.stream()
                .filter(e -> e.getResidueInventory() > 0)
                .mapToDouble(LotteryPrizes::getProbability)
                .sum();

        // 如果所有奖品都无库存，返回null
        if (totalValidProbability == 0) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "奖品已经抽完啦");
        }
        double randomValue = random.nextDouble() * totalValidProbability;

        // 遍历有库存的奖品，根据累积概率确定中奖奖品
        double cumulativeProbability = 0;
        for (LotteryPrizes prize : lotteryAwardList) {
            // 跳过库存不足的奖品
            if (prize.getResidueInventory() <= 0) {
                continue;
            }
            cumulativeProbability += prize.getProbability();
            if (randomValue <= cumulativeProbability) {
                return prize;
            }
        }
        return null;
    }


    private LotteryPrizes lotteryRule(List<LotteryPrizes> lotteryAwardList, UserSimpleDTO currentUserInfo, CampaignDTO campaign) {
        LotteryPrizes lotteryPrizes = lottery(lotteryAwardList);
        if (ObjectUtils.isEmpty(lotteryPrizes)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "奖品已经抽完啦");
        }
        log.info("奖品：" + JSONObject.toJSON(lotteryPrizes));
        // 限制
        if (null != lotteryPrizes && null != lotteryPrizes.getCountLimit() && lotteryPrizes.getCountLimit() != -1) {
            Integer timeLimit = lotteryPrizes.getTimeLimit();
//            if (3 == timeLimit) {
//                Long count = userLotteryPrizesMapper.selectUserLotteryPrizesByMany(currentUserInfo.getId(), lotteryPrizes.getId(), DateUtils.getMonthStart(), DateUtils.getMonthEnd());
//                if (count >= lotteryPrizes.getCountLimit()) {
//                    // 重新抽奖
//                    return lotteryRule(lotteryAwardList, currentUserInfo, campaign);
//                }
//            }
            if (5 == timeLimit) {
                log.info("活动时间{}, ------- {}", campaign.getCampaignStartTime(), campaign.getCampaignEndTime());
                log.info("getUnionid{}, ---getCampaignCode---- {}", currentUserInfo.getUnionid(), campaign.getCampaignCode());
                log.info("getPrizesLevelCode{},", lotteryPrizes.getPrizesLevelCode());

                Long count = userLotteryPrizesMapper.selectUserLotteryPrizesByMany(currentUserInfo.getUnionid(), campaign.getCampaignCode(), lotteryPrizes.getPrizesLevelCode(), campaign.getCampaignStartTime(), campaign.getCampaignEndTime());
                if (count >= lotteryPrizes.getCountLimit()) {
                    // 重新抽奖
                    return lotteryRule(lotteryAwardList, currentUserInfo, campaign);
                }
            }
        }
        log.info("最终奖品：" + JSONObject.toJSON(lotteryPrizes));
        return lotteryPrizes;
    }
}
