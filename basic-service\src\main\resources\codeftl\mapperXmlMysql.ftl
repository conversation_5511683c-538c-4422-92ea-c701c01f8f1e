<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.${typeName}.mapper.${objectName}Mapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
    <#list fieldList as var>
  	    <#if var_index < fieldList?size-1>
  	    ${var.columName},
  	    <#else>
  	    ${var.columName}
  	    </#if>
    </#list>
    </sql>

    <#if hasPrimark>
    <!-- 查询示例 -->
    <select id="selectDemo" parameterType="java.lang.${primarkMType}" resultType="com.dz.ms.${typeName}.entity.${objectName}">
        select
        <include refid="Base_Column_List" />
        from ${tableName}
        where ${primarkName} = ${r"#{"}${primarkUpper},jdbcType=${primarkType}${r"}"}
        <#if hasAppId>
        AND tenant_id = ${r"#{"}tenantId${r"}"}
        </#if>
    </select>
    </#if>

</mapper>
