package com.dz.common.core.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 我的积分详情DTO
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "我的积分详情")
public class MyPointsDetailsDTO {

    @ApiModelProperty(value = "获得积分/扣除积分")
    private String pointsNum;
    @ApiModelProperty(value = "入账时间")
    private String obtainTime;
    @ApiModelProperty(value = "获得原因")
    private String obtainDesc;
    @ApiModelProperty(value = "消费时间")
    private String consumptionTime;
    @ApiModelProperty(value = "消费金额")
    private String consumptionAmount;
    @ApiModelProperty(value = "可累计金额")
    private String cumulativeAmount;
    @ApiModelProperty(value = "渠道门店")
    private String channelStore;
    @ApiModelProperty(value = "变动类型 1增加 2扣减")
    private Integer changeType;
}
