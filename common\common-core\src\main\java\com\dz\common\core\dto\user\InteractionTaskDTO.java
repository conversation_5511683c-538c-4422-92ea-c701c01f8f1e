package com.dz.common.core.dto.user;

import com.dz.common.core.annotation.Columns;
import com.dz.common.core.dto.sales.TaskRewardDto;
import com.dz.common.core.enums.ColumnType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 任务详情
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "任务详情")
public class InteractionTaskDTO {

    @ApiModelProperty(value = "任务ID")
    private Long id;
    @ApiModelProperty(value = "任务名称")
    private String taskName;
    @ApiModelProperty(value = "启停状态 0启用，1停用")
    private Integer status;
    @ApiModelProperty(value = "任务展示时间类型：1时间段，2永久展示")
    private Integer showTimeType;
    @ApiModelProperty(value = "任务展示开始时间")
    private Date showTimeStart;
    @ApiModelProperty(value = "任务展示结束时间")
    private Date showTimeEnd;
    @ApiModelProperty(value = "任务展示图片")
    private String showImg;
    @ApiModelProperty(value = "任务类型：1限时，2购物，3互动")
    private Integer taskType;
    @ApiModelProperty(value = "是否限时：1限时，2不限时")
    private Integer isTimeRestrict;
    @ApiModelProperty(value = "限时开始时间")
    private Date restrictTimeStart;
    @ApiModelProperty(value = "限时结束时间")
    private Date restrictTimeEnd;
    @ApiModelProperty(value = "任务内容：1线下打卡，2兑礼任务，3线下消费，4邀请好友，5首次购买")
    private Integer taskDesc;
    @ApiModelProperty(value = "任务完成周期：1一次性，2周期，3周期+阶梯")
    private Integer readyCycle;
    @ApiModelProperty(value = "任务需要完成次数")
    private Integer readyNum;
    @ApiModelProperty(value = "任务已经完成次数")
    private Integer allReadyNum;
    @ApiModelProperty(value = "任务总计需要完成次数")
    private Integer totalReadyNum;
    @ApiModelProperty(value = "readyDay、readyMonth")
    private String reayType;
    @ApiModelProperty(value = "任务完成周期天数")
    private Integer readyDay;
    @ApiModelProperty(value = "任务完成周期月数")
    private Integer readyMonth;
    @ApiModelProperty(value = "渠道")
    private Long tenantId;
    @ApiModelProperty(value = "任务状态 0待开始，1进行中，2已结束")
    private Integer taskStatus;
    @ApiModelProperty(value = "总参与人数")
    private Integer joinInNum=0;
    @ApiModelProperty(value = "总完成人数")
    private Integer finishPersonNum=0;
    @ApiModelProperty(value = "总完成次数")
    private Integer finishNum=0;
    @ApiModelProperty(value = "奖励列表")
    private List<TaskRewardDto> taskRewardList;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "线下消费商品品类码部门code，多个用英文逗号隔开")
    private String storeProductCodes;
    @ApiModelProperty(value = "线下消费商品品类码depart code，多个用英文逗号隔开")
    private String departProductCodes;
    @ApiModelProperty(value = "线下消费商品品类码line code，多个用英文逗号隔开")
    private String lineProductCodes;
    @ApiModelProperty(value = "线下消费商品品类码class code，多个用英文逗号隔开")
    private String classProductCodes;
    @ApiModelProperty(value = "线下消费商品品类码jan code，多个用英文逗号隔开")
    private String janProductCodes;
    @ApiModelProperty(value = "任务规则图片")
    private String ruleImg;
    @ApiModelProperty(value = "任务完成第几周期")
    private Integer successCycleNum=0;
    @ApiModelProperty(value = "过期展示延长时间")
    private Integer showTimeExtend;
    @ApiModelProperty(value = "每天完成任务限制次数")
    private Integer limitationNum;
    @ApiModelProperty(value = "每家门店限制打卡次数")
    private Integer storeCardNum;
    @ApiModelProperty(value = "当天是否已打卡：1是，2否")
    private Integer checkClocNow;
    @ApiModelProperty(value = "当天打卡门店名称")
    private String clocStoreNameNow;
    @ApiModelProperty(value = "当天已打卡门店次数")
    private Integer clocStoreNumNow;
}
