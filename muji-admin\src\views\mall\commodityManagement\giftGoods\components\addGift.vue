<template>
  <a-drawer :title="title" width="95%" placement="right" :closable="true" :maskClosable="false" :open="open" @close="onClose">
    <a-spin :spinning="loading">

      <a-form class="form" ref="addForm" :model="addParams" :rules="rules" :labelCol="{ style: 'width:150px' }">
        <div class="form-top-titles-common">商品信息基本配置</div>
        <div class="form-top-line-common"></div>
        <a-form-item label="商品名称" name="productName">
          <a-input placeholder="请输入" style="width:300px;" v-model:value="addParams.productName" show-count :maxlength="20" />
        </a-form-item>
        <a-form-item label="副标题" name="subTitle">
          <a-input placeholder="请输入" style="width:300px;" v-model:value="addParams.subTitle" show-count :maxlength="20" />
          <div class="global-tip">
            若小程序无此字段，可不填写
          </div>
        </a-form-item>
        <a-form-item label="商品类型" name="pdType">
          <a-select ref="select" v-model:value="addParams.pdType" allowClear :disabled="disabled" :options="pdTypeOptions" :getPopupContainer="triggerNode => triggerNode.parentNode" style="width:300px;" :fieldNames="{ label: 'label', value: 'value' }" optionFilterProp="label" showSearch placeholder="请选择"></a-select>
        </a-form-item>
        <a-form-item label="场景图" name="scenceImgUrl">
          <uploadMoreImg sort :file-list="addParams.scenceImgUrl" :max="5" :limit="10" key="scenceImgUrl" path="scenceImgUrl" :form="addParams" @success="uploadSuccess1">
          </uploadMoreImg>
          <div class="global-tip">
            建议尺寸 310 * 310 ，比例 1:1，最多 5张，不上传则不展示，顺序根据这里的排列一致
          </div>
        </a-form-item>
        <a-form-item label="商品橱窗图" name="shelfImgUrl">
          <uploadMoreImg sort :file-list="addParams.shelfImgUrl" :max="10" :limit="10" key="shelfImgUrl" path="shelfImgUrl" :form="addParams" @success="uploadSuccess1">
          </uploadMoreImg>
          <div class="global-tip">
            建议尺寸 750 * 750 ，比例 1:1， 最多 10张
            列表页中，橱窗图顺序展示在橱窗图之后，顺序根据这里的排列一致
          </div>
        </a-form-item>
        <div class="form-top-titles-common">商品外部数据关联</div>
        <div class="form-top-line-common"></div>
        <a-form-item label="外部数据ID" name="venderId">
          <a-input placeholder="请输入" style="width:300px;" v-model:value="addParams.venderId" show-count :maxlength="64" />
          <div class="global-tip">
            不填写，则不展示
          </div>
        </a-form-item>
        <div class="form-top-titles-common">商品标签配置</div>
        <div class="form-top-line-common"></div>
        <a-form-item label="一级标签" name="tags1">
          <a-select ref="select" v-model:value="addParams.tags1" mode="multiple" allowClear :disabled="disabled" :options="tags1Options" :getPopupContainer="triggerNode => triggerNode.parentNode" style="width:300px;" :fieldNames="{ label: 'name', value: 'id' }" optionFilterProp="name" showSearch placeholder="请选择"></a-select>
        </a-form-item>
        <a-form-item label="二级标签" name="tags2">
          <a-select ref="select" v-model:value="addParams.tags2" mode="multiple" allowClear :disabled="disabled" :options="tags2Options" :getPopupContainer="triggerNode => triggerNode.parentNode" style="width:300px;" :fieldNames="{ label: 'name', value: 'id' }" optionFilterProp="name" showSearch placeholder="请选择"></a-select>
          <div class="global-tip">
            标签可多选，带有标签的商品，在前台通过快捷筛选时，可以被筛选出
          </div>
        </a-form-item>

        <div class="form-top-titles-common">商品基础价格维护</div>
        <div class="form-top-line-common"></div>

        <a-form-item label="商品积分价值" name="costPoint">
          <!-- <div style="display:flex;align-items: center;"> -->
          <a-input-number style="width:300px;" addon-after="分" placeholder="请输入" v-model:value="addParams.costPoint" :min="1" :max="99999999" />
          <div class="global-tip" style="padding-left:0px">
            基础积分价值，需要打折、限购时，可在“<span @click="toGO" class="global-color">营销活动</span>”管理中，配置对应活动
          </div>
          <!-- </div> -->
        </a-form-item>
        <a-form-item label="需待到店支付金额" name="costPrice">
          <!-- <div style="display:flex;"> -->
          <a-input-number style="width:300px;" placeholder="请输入" addon-after="元" v-model:value="addParams.costPrice" :step="0" :min="0" :max="99999999999" />
          <div style="display:flex;align-items: baseline;padding-left:0px;margin:0;" class="global-tip">
            不填写，则不展示
            <a-form-item style="padding-left:20px;margin: 0 !important;" label="该金额是否展示在货架列表" name="costPriceOnShelf">
              <a-switch v-model:checked="addParams.costPriceOnShelf" :checkedValue="1" :unCheckedValue="0" />
            </a-form-item>

          </div>
          <!-- </div> -->

        </a-form-item>
        <a-form-item label="吊牌价" name="prePrice">
          <!-- <div style="display:flex;align-items: center;"> -->
          <a-input-number style="width:300px;" placeholder="请输入" addon-after="元" v-model:value="addParams.prePrice" :step="0" :min="0" :max="99999999999" />
          <div style="padding-left:0px" class="global-tip">
            不填写，则不展示
          </div>
          <!-- </div> -->

        </a-form-item>
        <a-form-item label="成本价" name="originPrice">
          <!-- <div style="display:flex;align-items: center;"> -->
          <a-input-number style="width:300px;" placeholder="请输入" addon-after="元" v-model:value="addParams.originPrice" :step="0" :min="0" :max="99999999999" />
          <!-- </div> -->

        </a-form-item>

        <div class="form-top-titles-common">商品详情信息配置</div>
        <div class="form-top-line-common"></div>
        <a-form-item label="兑换须知" name="exchangeDescType">
          <a-radio-group v-model:value="addParams.exchangeDescType">
            <template v-for="(item,index) in exchangeDescTypeOptions" :key="index">
              <a-radio-button :value="item.value">{{item.label}}</a-radio-button>
            </template>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="须知内容" name="exchangeDescUrl" v-if="addParams.exchangeDescType == '1'">
          <uploadMoreImg sort :file-list="addParams.exchangeDescUrl" :max="5" :limit="10" key="exchangeDescUrl" path="exchangeDescUrl" :form="addParams" @success="uploadSuccess1">
          </uploadMoreImg>
          <div class="global-tip">
            建议尺寸 670 * 高度不限 ， 最多 5张，不上传则不展示，顺序根据这里的排列一致
          </div>

        </a-form-item>
        <a-form-item v-else label="须知内容" name="exchangeDescContent">
          <richText default-color="#7e8c8d;" :toolbar="toolbarList" :plugins="pluginsList" :menubar="menubarList" v-model:value="addParams.exchangeDescContent"></richText>
        </a-form-item>
        <a-form-item label="商品详情" name="detailsType">
          <a-radio-group v-model:value="addParams.detailsType">
            <template v-for="(item,index) in exchangeDescTypeOptions" :key="index">
              <a-radio-button :value="item.value">{{item.label}}</a-radio-button>
            </template>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="详情内容" name="detailsUrl" v-if="addParams.detailsType == '1'">
          <uploadMoreImg sort :file-list="addParams.detailsUrl" :max="5" :limit="10" key="detailsUrl" path="detailsUrl" :form="addParams" @success="uploadSuccess1">
          </uploadMoreImg>
          <div class="global-tip">
            建议尺寸 670 * 高度不限 ， 最多 5张，不上传则不展示，顺序根据这里的排列一致
          </div>

        </a-form-item>
        <a-form-item v-else label="详情内容" name="detailsContent">
          <richText :toolbar="toolbarList" :plugins="pluginsList" :menubar="menubarList" v-model:value="addParams.detailsContent"></richText>
        </a-form-item>
        <a-form-item label="使用说明" name="referType">
          <a-radio-group v-model:value="addParams.referType">
            <template v-for="(item,index) in exchangeDescTypeOptions" :key="index">
              <a-radio-button :value="item.value">{{item.label}}</a-radio-button>
            </template>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="说明内容" name="referUrl" v-if="addParams.referType == '1'">
          <uploadMoreImg sort :file-list="addParams.referUrl" :max="5" :limit="10" key="referUrl" path="referUrl" :form="addParams" @success="uploadSuccess1">
          </uploadMoreImg>
          <div class="global-tip">
            建议尺寸 670 * 高度不限， 最多 5张，不上传则不展示，顺序根据这里的排列一致
          </div>

        </a-form-item>
        <a-form-item v-else label="说明内容" name="referContent">
          <richText default-color="#7e8c8d;" :toolbar="toolbarList" :plugins="pluginsList" :menubar="menubarList" v-model:value="addParams.referContent"></richText>
        </a-form-item>
      </a-form>

    </a-spin>
    <template #footer v-if="!disabled">
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="ok" :loading="loading">确定</a-button>
      </a-space>
    </template>

  </a-drawer>
</template>
<script setup>
import richText from "@/components/richText/index.vue";
import { tag_info_noPage, productAdd, producUpdate, producInfo } from '@/http/index.js'
import { useGlobalStore } from '@/store'
import { message, Modal } from "ant-design-vue";
import Clipboard from 'clipboard';
import { exchangeDescTypeOptions, pdTypeOptions } from '@/utils/dict-options'
import { formRulesValidate, limitFloatNumber } from '@/utils/validate.js'
const $router = useRouter();
const global = useGlobalStore()
const addForm = ref(null)
import { cloneDeep } from 'lodash'
import { onMounted } from 'vue';
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  // 操作抽屉打开关闭
  visible: {
    type: Boolean,
    default: false
  },
  // 数据ID
  id: {
    type: String || Number,
    default: ''
  },
  // 操作类型
  type: {
    type: Number,
    default: 0, // 0-新增 1-编辑  2-查看
  }

})
// 置灰
const disabled = computed(() => {
  return props.type == 2
})

// 标题
const title = computed(() => {
  return ['新建', '编辑', '查看'][props.type] + '商品'
})

const { open, addParams, rules, loading, tags1Options, tags2Options, toolbarList, menubarList, pluginsList } = toRefs(reactive({
  open: props.visible,

  tags1Options: [],
  tags2Options: [],
  loading: false,
  toolbarList: "undo redo |  accordionremove |  fontfamily  fontsize| bold italic underline strikethrough    | align   | link image |   lineheight outdent indent| forecolor backcolor removeformat | charmap emoticons    | preview",
  pluginsList: "   autolink     fullscreen image link  preview",
  menubarList: " edit view insert  tools   ",// edit view insert  tools
  addParams: {
    productName: '',
    subTitle: '',
    productCode: '', //商品编号
    pdType: null,// "商品类型 1实物商品 2电子券")
    beGift: '',//  "是否赠品 0赠品 1正品商品")
    itemId: '',// "三方商品ID")
    venderId: '', //外部数据I
    scenceImgUrl: [], //"场景图片地址 逗号分隔")
    shelfImgUrl: [],// 商品橱窗图片展示位 逗号分隔
    details: '',//  "商品详情
    prePrice: '',//  "吊牌价")
    price: '',// "销售价")
    purchaseType: '',// 购买方式 0积分 1积分+金额")
    costPoint: '',// y(value = "兑换积分")
    costPrice: '0.1',// value = "1积分+金额时 仍需支付的金额")
    exchangeNum: '',// "商品累计兑换量")
    exchangeDescType: 1,//"兑换须知类型 1图片 2富文本")
    exchangeDescUrl: [],//"兑换须知图片")
    exchangeDescContent: '',//"兑换须知富文本")
    detailsType: 1,//"商品详情类型 1图片 2富文本")
    detailsUrl: [],//"商品详情图片")
    detailsContent: '',//"商品详情富文本")
    referType: 1,//"使用说明类型 1图片 2富文本")
    referUrl: [],//"使用说明图片")
    referContent: '',//"使用说明富文本")
    state: '',//"状态 0禁用 1启用 2删除")
    tags1: [],
    tags2: [],
    costPriceOnShelf: 1,
    originPrice: null

  },
  rules: {
    productName: [{ required: true, message: '请输入商品名称', trigger: ['blur', 'change'] }],
    pdType: [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
    venderId: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
    // scenceImgUrl: [{ required: true, message: '请上传', trigger: ['blur', 'change'] }],
    shelfImgUrl: [{ required: true, message: '请上传', trigger: ['blur', 'change'] }],
    costPoint: [{ required: true, message: '请输入', trigger: ['blur'] }, {
      validator: (rule, value, callback) => formRulesValidate.elPositiveInteger(value, callback,), trigger: ['blur', 'change']
    }],
    // tags2: [{ type: 'array', required: true, message: '请选择二级标签', trigger: ['blur', 'change'] }],
    costPrice: [
      // { required: true, message: '请输入', trigger: ['blur'] },
      { validator: (rule, value, callback) => limitFloatNumber(rule, value, callback, { min: 0.01, max: 99999999999 }), trigger: ['blur', 'change'] }
    ],
    prePrice: [{ validator: (rule, value, callback) => limitFloatNumber(rule, value, callback, { min: 0.01, max: 99999999999 }), trigger: ['blur', 'change'] }],
    originPrice: [{ validator: (rule, value, callback) => limitFloatNumber(rule, value, callback, { min: 0.01, max: 99999999999 }), trigger: ['blur', 'change'] }],
  }
})
);
function toGO() {
  $router.push({ name: 'CrowdPurchaseRestriction' })
}
watch(() => props.visible, (value) => {
  // 弹窗操作
  open.value = props.visible
  addForm.value?.resetFields()
  if (open.value) {

    initData()
  } else {
    for (let i in addParams.value) {
      // console.log("🚀 ~ watch ~ i:", i)
      if ('exchangeDescType' == i || 'detailsType' == i || 'referType' == i) {
        addParams.value[i] = 1
      } else if ('tags1' == i || 'tags2' == i || 'exchangeDescUrl' == i || 'scenceImgUrl' == i || 'shelfImgUrl' == i || 'detailsUrl' == i || 'referUrl' == i) {
        addParams.value[i] = []
      } else {
        addParams.value[i] = ''
      }


    }
  }
  console.log(addParams.value, 'addParamsaaddParamsaddParams');
})

//所有接口调取出
const initData = async () => {
  const promiseArr = []
  promiseArr.push(tag_info_noPage({ cate: 1 }))
  promiseArr.push(tag_info_noPage({ cate: 2 }))

  if (props.id) {

    promiseArr.push(producInfo({ id: props.id }))
  }

  // 如果有新的接口就在这里push
  loading.value = true
  try {
    const [tag_info1, tag_info2, info] = await Promise.all(promiseArr)
    tags1Options.value = tag_info1.data
    tags2Options.value = tag_info2.data
    if (info) {

      if (info.data.scenceImgUrl && info.data.scenceImgUrl.length > 0) {
        info.data.scenceImgUrl = info.data.scenceImgUrl.split(",");
      } else {
        info.data.scenceImgUrl = []
      }
      if (info.data.shelfImgUrl && info.data.shelfImgUrl.length > 0) {
        info.data.shelfImgUrl = info.data.shelfImgUrl.split(",");
      } else {
        info.data.shelfImgUrl = []
      }
      if (info.data.exchangeDescUrl && info.data.exchangeDescUrl.length > 0) {
        info.data.exchangeDescUrl = info.data.exchangeDescUrl.split(",");
      } else {
        info.data.exchangeDescUrl = []
      }
      if (info.data.referUrl && info.data.referUrl.length > 0) {
        info.data.referUrl = info.data.referUrl.split(",");
      } else {
        info.data.referUrl = []
      }
      if (info.data.detailsUrl && info.data.detailsUrl.length > 0) {
        info.data.detailsUrl = info.data.detailsUrl.split(",");
      } else {
        info.data.detailsUrl = []
      }
      if (info.data.tags1 && info.data.tags1.length > 0) {

        info.data.tags1 = info.data.tags1.map(item => item.id)
      } else {
        info.data.tags1 = []
      }
      if (info.data.tags2 && info.data.tags2.length > 0) {
        info.data.tags2 = info.data.tags2.map(item => item.id)
      } else {
        info.data.tags2 = []
      }
      if (!info.data.prePrice) {
        info.data.prePrice = ''
      }
      if (!info.data.price) {
        info.data.price = ''
      }
      // if (info.data.costPriceOnShelf == '0') {
      //   info.data.costPriceOnShelf = false
      // } else {
      //   info.data.costPriceOnShelf = true
      // }
      addParams.value = info.data

      console.log("🚀 ~ initData ~  addParams.value:", addParams.value)



    }
    console.log(info);
    loading.value = false
  } catch (error) {
    loading.value = false
    console.error('获取数据失败:', error)
  }
  if (!props.id) {
    addParams.value.exchangeDescContent = `<p>1、此商品每人每月限兑1件<br>2、使用指定积分兑换会员权益，积分一经兑换不支持退还；<br>3、兑换后，在本券有效期内，可前往券使用说明指定店铺，享受本券所示之优惠/权益，逾期不可使用、延期、补发或退回；<br>4、商品兑换券所兑换的商品/型号/尺码/颜色/口味请以店铺实际库存为准，建议前往店铺现场确认具体商品库存并兑换，非质量问题不予退换；<br>5、券一经使用，非质量问题不支持退货或换货，若因非质量问题产生退货或换货，则无法再次享受该券所示之优惠/权益，且本券及兑换本券所使用的积分无法退回。</p>`
  }
}

// 多图上传成功
const uploadSuccess1 = async (data) => {
  let { form, path: key, imgUrl } = data;
  console.log("🚀 ~ uploadSuccess1 ~ form, path: key, imgUrl:", form, key, imgUrl)
  form[key] = imgUrl;
  // 清除校验
  await nextTick();
  addForm.value.validateFields([key]);
  console.log(addParams.value.materialUrls, "materialUrls");
};



// 关闭弹窗
const onClose = () => {
  emit('cancel')
}
function filterObjectsByIds(idArray, objectArray) {
  // 使用 Set 来提高查找效率
  const idSet = new Set(idArray);

  // 使用 filter 方法过滤对象数组
  return objectArray.filter(obj => idSet.has(obj.id));
}
// 确定
const ok = () => {

  addForm.value.validate().then(res => {

    let params = cloneDeep(addParams.value)
    // if (params.costPriceOnShelf == true) {
    //   params.costPriceOnShelf = 1
    // } else {
    //   params.costPriceOnShelf = 0
    // }
    if (params.exchangeDescType == '1') {
      params.exchangeDescContent = ''
    } else {
      params.exchangeDescUrl = ''
    }
    if (params.detailsType == '1') {
      params.detailsContent = ''
    } else {
      params.detailsUrl = ''
    }
    if (params.referType == '1') {
      params.referContent = ''
    } else {
      params.referUrl = ''
    }
    console.log("🚀 ~ addForm.value.validate ~ params:", params)
    if (params.scenceImgUrl && params.scenceImgUrl.length > 0) {

      params.scenceImgUrl = params.scenceImgUrl.join(',');
    } else {
      params.scenceImgUrl = ''
    }
    if (params.shelfImgUrl && params.shelfImgUrl.length > 0) {

      params.shelfImgUrl = params.shelfImgUrl.join(',');
    } else {
      params.shelfImgUrl = ''
    }
    if (params.exchangeDescUrl && params.exchangeDescUrl.length > 0) {
      params.exchangeDescUrl = params.exchangeDescUrl.join(',');
    } else {
      params.exchangeDescUrl = ''
    }
    if (params.detailsUrl && params.detailsUrl.length > 0) {
      params.detailsUrl = params.detailsUrl.join(',');
    } else {
      params.detailsUrl = ''
    }
    if (params.referUrl && params.referUrl.length > 0) {

      params.referUrl = params.referUrl.join(',');
    } else {
      params.referUrl = ''
    }
    if (params.tags1 && params.tags1.length > 0) {
      params.tags1 = filterObjectsByIds(params.tags1, tags1Options.value).map(item => {
        return {
          name: item.name,
          id: item.id,
          cate: item.cate,
        }
      })
    } else {
      params.tags1 = []
    }
    if (params.tags2 && params.tags2.length > 0) {
      params.tags2 = filterObjectsByIds(params.tags2, tags2Options.value).map(item => {
        return {
          name: item.name,
          id: item.id,
          cate: item.cate,
        }
      })
    } else {
      params.tags2 = []
    }
    loading.value = true
    if (props.id) {
      console.log('编辑');
      producUpdate(params).then(res => {
        message.success(res.msg);
        emit('ok', props.id)
      }).finally(() => {
        loading.value = false
      })
    } else {
      productAdd(params).then(res => {
        message.success(res.msg);
        emit('ok', props.id)
      }).finally(() => {
        loading.value = false
      })
    }

  })
}



</script>

<style scoped lang="scss">
:deep(.searchForm .ant-form-item) {
  margin-bottom: 16px;
}

:deep(.anticon-eye-invisible) {
  display: none;
}

.member-role {
  background: #f2f2f2;
  margin-bottom: 20px;
  padding: 5px 10px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &:nth-child(odd) {
    margin-right: 35px;
  }
}
</style>
