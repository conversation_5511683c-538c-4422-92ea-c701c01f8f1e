<template>

  <a-modal :title="title" width="576px" v-if="open" placement="right" :maskClosable="false" :closable="true" :open="open" @cancel="onClose">
    <!-- <a-spin :spinning="loading"> -->

    <a-form class="form" ref="addForm" :model="addParams" :rules="rules" :labelCol="{ style: 'width:100px' }">

      <a-form-item label="标签名称" name="name">
        <a-input placeholder="请输入" style="width:300px;" v-model:value="addParams.name" show-count :maxlength="20" />
      </a-form-item>

    </a-form>
    <!--  -->
    <!-- </a-spin> -->
    <template #footer v-if="!disabled">
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="ok" :loading="loading">确定</a-button>
      </a-space>
    </template>

  </a-modal>

</template>
<script setup>
import { tag_infoAdd, tag_infoUpdate, tag_info } from '@/http/index.js'
import { useGlobalStore } from '@/store'
import { message, Modal } from "ant-design-vue";


const global = useGlobalStore()
const addForm = ref(null)
import { cloneDeep } from 'lodash'
import { onMounted } from 'vue';
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  // 操作抽屉打开关闭
  visible: {
    type: Boolean,
    default: false
  },
  // 数据ID
  id: {
    type: String || Number,
    default: ''
  },
  // 操作类型
  type: {
    type: Number,
    default: 0, // 0-新增 1-编辑  2-查看
  },
  addType: {
    type: String,
    default: '1', // 1 级 2级
  }

})
// 置灰
const disabled = computed(() => {
  console.log(addParams.value, 'addType');

  return props.type == 2
})

// 标题
const title = computed(() => {
  return ['新建', '编辑', '查看'][props.type] + ['一级', "二级"][Number(props.addType) - 1] + '标签'
})

const { open, addParams, rules, loading, } = toRefs(reactive({
  open: props.visible,


  loading: false,

  addParams: {
    name: '',
    cate: ''
  },
  rules: {
    name: [{ required: true, message: '请输入标签名称', trigger: ['blur', 'change'] }],

  }
})
);
// onMounted(() => {
//     initData()
// })
watch(() => props.visible, (value) => {
  // 弹窗操作
  open.value = props.visible
  addParams.value = {
    name: '',
    cate: props.addType
  },

    addForm.value?.resetFields()
  if (open.value) {

    initData()
  }
})

//所有接口调取出
const initData = async () => {
  const promiseArr = []

  if (props.id) {

    promiseArr.push(tag_info({ id: props.id }))
  }

  // 如果有新的接口就在这里push
  loading.value = true
  try {
    const [tag_info] = await Promise.all(promiseArr)
    if (tag_info) {
      addParams.value = {
        ...tag_info.data
      }
    }

    loading.value = false
  } catch (error) {
    loading.value = false
    console.error('获取数据失败:', error)
  }
}




// 关闭弹窗
const onClose = () => {
  emit('cancel')
}

// 确定
const ok = () => {

  addForm.value.validate().then(res => {

    let params = cloneDeep(addParams.value)
    params.cate = Number(params.cate)
    loading.value = true
    if (props.id) {
      console.log('编辑');
      tag_infoUpdate(params).then(res => {
        message.success(res.msg);
        emit('ok', props.id)
      }).finally(() => {
        loading.value = false
      })
    } else {
      console.log(params, 'xin');
      tag_infoAdd(params).then(res => {
        message.success(res.msg);
        emit('ok', props.id)
      }).finally(() => {
        loading.value = false
      })
    }

  })
}



</script>

<style scoped lang="scss">
.form {
}
:deep(.searchForm .ant-form-item) {
  // margin-bottom: 16px;
}

:deep(.anticon-eye-invisible) {
  display: none;
}

.member-role {
  background: #f2f2f2;
  margin-bottom: 20px;
  padding: 5px 10px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &:nth-child(odd) {
    margin-right: 35px;
  }
}
</style>
