package com.dz.ms.product.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商品列表信息DTO
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "商品列表信息")
public class CrmProductListDTO {

    @ApiModelProperty(value = "商品ID")
    private Long id;
    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "商品类型 1实物商品 2电子券")
    private Integer pdType;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "场景图片地址列表")
    private List<String> scenceImgList;
    @ApiModelProperty(value = "商品橱窗图片地址列表")
    private List<String> shelfImgList;
    @ApiModelProperty(value = "商品标签列表")
    private List<TagInfoDTO> TagList;
    @ApiModelProperty(value = "购买方式 0积分 1积分+金额")
    private Integer purchaseType;
    @ApiModelProperty(value = "商品CP号")
    private String venderId;
    @ApiModelProperty(value = "成本价")
    private BigDecimal originPrice;
    @ApiModelProperty(value = "到店支付金额")
    private BigDecimal costPrice;
    @ApiModelProperty(value = "兑换积分")
    private Integer costPoint;
    @ApiModelProperty(value = "状态 0禁用 1启用")
    private Integer state;
    @ApiModelProperty(value = "商品累计兑换量")
    private Integer exchangeNum;
    @ApiModelProperty(value = "商品所在货架列表")
    private List<ShelfDTO> shelfList;
    @ApiModelProperty(value = "uuid")
    private String uuid;
}
