package com.dz.ms.user.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.user.*;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.ms.user.service.MUJIOpenApiService;
import com.dz.ms.user.service.MujiOrderFlowService;
import com.dz.ms.user.service.UserInfoService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;


/**
 * 我的消费记录
 */
@Service
public class MujiOrderFlowServiceImpl implements MujiOrderFlowService {

    @Resource
    private MUJIOpenApiService mujiOpenApiService;
    @Resource
    private UserInfoService userInfoService;

    @Override
    public PageInfo<MujiOrderFlowDTO> getMujiOrderFlowList(MujiOrderFlowParamDTO param) {
        UserInfoDTO currentUserSimpleInfo = userInfoService.getUserInfo(SecurityContext.getUser().getUid());
        String memberCode = currentUserSimpleInfo.getCardNo();

        int year = Integer.parseInt(param.getYear());
        LocalDate startDate = LocalDate.of(year, 1, 1);
        LocalDateTime startDateTime = startDate.atStartOfDay();
        String startTime = startDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        LocalDate endDate = LocalDate.of(year, 12, 31);
        LocalDateTime endDateTime = endDate.atTime(23, 59, 59);
        String endTime = endDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        JSONObject res = mujiOpenApiService.memberOrderList(memberCode, startTime, endTime, param.getType(), param.getPageNum(), param.getPageSize());
        List<MujiOrderFlowDTO> listDTO = new ArrayList<>();
        String hasMore = "N";
        if (null != res) {
            JSONArray jsonArray = res.getJSONArray("items");
            if (!CollectionUtils.isEmpty(jsonArray)) {
                Iterator<Object> iterator = jsonArray.stream().iterator();
                while (iterator.hasNext()) {
                    JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(iterator.next()));
                    MujiOrderFlowDTO orderFlowDTO = new MujiOrderFlowDTO();
                    orderFlowDTO.setOrderSn(jsonObject.getString("order_sn"));
                    orderFlowDTO.setOrderType(jsonObject.getInteger("order_type"));
                    orderFlowDTO.setChannel(jsonObject.getString("channel"));
                    orderFlowDTO.setChannelName(jsonObject.getString("channel_name"));
                    orderFlowDTO.setTotalFee(jsonObject.getInteger("total_fee"));
                    orderFlowDTO.setPayTime(jsonObject.getString("pay_time"));
                    orderFlowDTO.setOrderSource(jsonObject.getInteger("order_source"));
                    listDTO.add(orderFlowDTO);
                }
            }
            hasMore = res.getString("has_more");
        }
        return new PageInfo<>(param.getPageNum(), param.getPageSize(), "Y".equalsIgnoreCase(hasMore), listDTO);
    }

    @Override
    public MujiOrderInfoDTO getMujiOrderInfo(String orderSn) {
        UserInfoDTO currentUserSimpleInfo = userInfoService.getUserInfo(SecurityContext.getUser().getUid());
        String memberCode = currentUserSimpleInfo.getCardNo();
        JSONObject jsonObject = mujiOpenApiService.memberOrderDetails(memberCode, orderSn);
        if (null != jsonObject) {
            MujiOrderInfoDTO orderInfoDTO = new MujiOrderInfoDTO();
            orderInfoDTO.setMemberCode(memberCode);
            orderInfoDTO.setOrderSn(jsonObject.getString("order_sn"));
            orderInfoDTO.setOrderType(jsonObject.getInteger("order_type"));
            orderInfoDTO.setChannel(jsonObject.getString("channel"));
            orderInfoDTO.setChannelName(jsonObject.getString("channel_name"));
            orderInfoDTO.setPayTime(jsonObject.getString("pay_time"));
            orderInfoDTO.setTotalFee(convertAndFormatFenToYuan(jsonObject.getBigDecimal("total_fee")));
            orderInfoDTO.setBuyerFee(convertAndFormatFenToYuan(jsonObject.getBigDecimal("buyer_fee")));
            BigDecimal calculateFee = jsonObject.getBigDecimal("calculate_fee");
            orderInfoDTO.setCalculateFee(ensurePlusSign(convertAndFormatFenToYuan(calculateFee)));
            orderInfoDTO.setStoreName(jsonObject.getString("store_name"));
            orderInfoDTO.setBonus(ensurePlusSign(jsonObject.getString("bonus")));
            orderInfoDTO.setMileage(ensurePlusSign(jsonObject.getString("mileage")));
            if (null != jsonObject.get("goods")) {
                JSONArray jsonArray = jsonObject.getJSONArray("goods");
                List<MujiOrderGoodDTO> goodsList = new ArrayList<>();
                for (int i = 0; i < jsonArray.size(); i++) {
                    MujiOrderGoodDTO goodDTO = new MujiOrderGoodDTO();
                    goodDTO.setAmount(convertAndFormatFenToYuan(jsonArray.getJSONObject(i).getBigDecimal("amount")));
                    goodDTO.setBuyerFee(convertAndFormatFenToYuan(jsonArray.getJSONObject(i).getBigDecimal("buyer_fee")));
                    goodDTO.setGoodsId(jsonArray.getJSONObject(i).getString("goods_id"));
                    goodDTO.setGoodsName(jsonArray.getJSONObject(i).getString("goods_name"));
                    goodDTO.setQuantity(jsonArray.getJSONObject(i).getInteger("quantity"));
                    goodDTO.setTotalFee(convertAndFormatFenToYuan(jsonArray.getJSONObject(i).getBigDecimal("total_fee")));
                    goodDTO.setUpSerialNo(jsonArray.getJSONObject(i).getString("up_serial_no"));
                    goodsList.add(goodDTO);
                }
                orderInfoDTO.setGoods(goodsList);
            }
//            if ("T202402060003".equals(orderSn.trim())) {
//                orderInfoDTO.setGoods(mockGoods());
//            }
            return orderInfoDTO;
        }
        return null;
    }

    private List<MujiOrderGoodDTO> mockGoods() {
        List<MujiOrderGoodDTO> goodsList = new ArrayList<>();
        MujiOrderGoodDTO goodDTO1 = new MujiOrderGoodDTO();
        goodDTO1.setAmount("18.00");
        goodDTO1.setBuyerFee("18.00");
        goodDTO1.setGoodsId("商品ID *************");
        goodDTO1.setGoodsName("棉花糖（充气糖果）");
        goodDTO1.setQuantity(1);
        goodDTO1.setTotalFee("18.00");
        goodDTO1.setUpSerialNo("对应商品行 ************* 造的数据不确定是不是和商品ID一致");
        goodsList.add(goodDTO1);
        MujiOrderGoodDTO goodDTO2 = new MujiOrderGoodDTO();
        goodDTO2.setAmount("9.00");
        goodDTO2.setBuyerFee("9.00");
        goodDTO2.setGoodsId("商品ID *************");
        goodDTO2.setGoodsName("巧克力味加薪棉花糖");
        goodDTO2.setQuantity(2);
        goodDTO2.setTotalFee("9.00");
        goodDTO2.setUpSerialNo("对应商品行 ************* 造的数据不确定是不是和商品ID一致");
        goodsList.add(goodDTO2);
        MujiOrderGoodDTO goodDTO3 = new MujiOrderGoodDTO();
        goodDTO3.setAmount("9.00");
        goodDTO3.setBuyerFee("9.00");
        goodDTO3.setGoodsId("商品ID *************");
        goodDTO3.setGoodsName("草莓夹心棉花糖");
        goodDTO3.setQuantity(1);
        goodDTO3.setTotalFee("9.00");
        goodDTO3.setUpSerialNo("对应商品行 ************* 造的数据不确定是不是和商品ID一致");
        goodsList.add(goodDTO3);
        return goodsList;
    }

    @Override
    public MujiTicketInfoDTO getMujiOrderTicketInfo(String orderSn) {
        UserInfoDTO currentUserSimpleInfo = userInfoService.getUserInfo(SecurityContext.getUser().getUid());
        String memberCode = currentUserSimpleInfo.getCardNo();
        JSONObject jsonObject = mujiOpenApiService.memberOrderTicketDetails(memberCode, orderSn);
        if (null != jsonObject) {
            MujiTicketInfoDTO ticketInfoDTO = new MujiTicketInfoDTO();
            ticketInfoDTO.setMemberCode(memberCode);
            ticketInfoDTO.setOrderSn(jsonObject.getString("order_sn"));
            ticketInfoDTO.setChange(jsonObject.getString("change"));
            ticketInfoDTO.setCheckoutDt(jsonObject.getString("checkout_dt"));
            ticketInfoDTO.setDiscount(jsonObject.getString("discount"));
            ticketInfoDTO.setDiscountAmt(jsonObject.getString("discount_amt"));
            ticketInfoDTO.setInternalOrder(jsonObject.getString("internal_order"));
            ticketInfoDTO.setInternalOrderTip(jsonObject.getString("internal_order_tip"));
            ticketInfoDTO.setOrigAmt(jsonObject.getString("orig_amt"));
            ticketInfoDTO.setQty(jsonObject.getString("qty"));
            ticketInfoDTO.setRealAmt(jsonObject.getString("real_amt"));
            ticketInfoDTO.setSaleAmt(jsonObject.getString("sale_amt"));
            ticketInfoDTO.setSaleId(jsonObject.getString("sale_id"));
            ticketInfoDTO.setShopAddr(jsonObject.getString("shop_addr"));
            ticketInfoDTO.setShopId(jsonObject.getString("shop_id"));
            ticketInfoDTO.setShopName(jsonObject.getString("shop_name"));
            ticketInfoDTO.setStaffId(jsonObject.getString("staff_id"));
            ticketInfoDTO.setTelNo(jsonObject.getString("tel_no"));
            ticketInfoDTO.setVipDiscount(jsonObject.getString("vip_discount"));
            BigDecimal calculateFee = jsonObject.getBigDecimal("calculate_fee");
            ticketInfoDTO.setCalculateFee(ensurePlusSign(convertAndFormatFenToYuan(calculateFee)));
            ticketInfoDTO.setMileage(ensurePlusSign(jsonObject.getString("mileage")));
            ticketInfoDTO.setBonus(ensurePlusSign(jsonObject.getString("bonus")));
            if (null != jsonObject.get("items")) {
                JSONArray jsonArray = jsonObject.getJSONArray("items");
                List<MujiTicketItemDTO> itemsList = new ArrayList<>();
                for (int i = 0; i < jsonArray.size(); i++) {
                    MujiTicketItemDTO itemDTO = new MujiTicketItemDTO();
                    itemDTO.setDiscountrate(jsonArray.getJSONObject(i).getString("discountrate"));
                    itemDTO.setItemId(jsonArray.getJSONObject(i).getString("item_id"));
                    itemDTO.setItemName(jsonArray.getJSONObject(i).getString("item_name"));
                    itemDTO.setQty(jsonArray.getJSONObject(i).getString("qty"));
                    itemDTO.setRealPrice(jsonArray.getJSONObject(i).getString("real_price"));
                    itemDTO.setSalePrice(jsonArray.getJSONObject(i).getString("sale_price"));
                    itemsList.add(itemDTO);
                }
                ticketInfoDTO.setItems(itemsList);
            }
            if (null != jsonObject.get("pay_list")) {
                JSONArray jsonArray = jsonObject.getJSONArray("pay_list");
                List<MujiTicketPayDTO> payList = new ArrayList<>();
                for (int i = 0; i < jsonArray.size(); i++) {
                    MujiTicketPayDTO payDTO = new MujiTicketPayDTO();
                    payDTO.setPay(jsonArray.getJSONObject(i).getString("pay"));
                    payDTO.setPayName(jsonArray.getJSONObject(i).getString("pay_name"));
                    payList.add(payDTO);
                }
                ticketInfoDTO.setPayList(payList);
            }
            return ticketInfoDTO;
        }
        return null;
    }

    private static String ensurePlusSign(String input) {
        if (input != null && !input.contains("-")) {
            return "+" + input;
        }
        return input;
    }

    private static BigDecimal convertFenToYuan(BigDecimal fen) {
        if (fen == null) {
            return BigDecimal.ZERO;
        }
        // 将分转换为元，并保留两位小数
        return fen.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
    }

    private static String formatYuan(BigDecimal yuan) {
        if (yuan == null) {
            return "0.00";
        }
        // 使用 DecimalFormat 格式化为类似 18.00 的形式
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(yuan);
    }

    private static String convertAndFormatFenToYuan(BigDecimal fen) {
        BigDecimal yuan = convertFenToYuan(fen);
        return formatYuan(yuan);
    }
}
