.inviteFriend {
  height: 100vh;
  width: 750rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: SourceHanSansCN;
  overflow-y: auto;

  &-content {
    flex: auto;
    display: flex;
    align-items: center;
  }

  &-img {
    flex-shrink: 0;
    width: 670rpx;
  }

  &-action {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding-bottom: 22rpx;
    width: 750rpx;
    height: 366rpx;
    background: #FFFFFF;
    border-radius: 16rpx 18rpx 0rpx 0rpx;
  }

  &-bottom {
    display: flex;
    justify-content: space-between;
    padding: 52rpx 176rpx 40rpx;
  }

  &-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-shrink: 0;

    &-icon {
      width: 100rpx;
      height: 100rpx;
      border-radius: 50%;
    }

    &-text {
      font-weight: 400;
      font-size: 24rpx;
      color: #231815;
      line-height: 40rpx;
    }
  }


  &-cancel {
    border-top: 1rpx solid #D8D8D8;

    &-btn {
      padding: 36rpx 0;
      text-align: center;
      font-weight: 400;
      font-size: 28rpx;
      color: #231815;
    }
  }
}
