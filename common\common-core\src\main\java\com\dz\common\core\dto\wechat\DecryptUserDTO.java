package com.dz.common.core.dto.wechat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 解密微信用户信息
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
public class DecryptUserDTO implements Serializable{

	private static final long serialVersionUID = -3975032773821649419L;

	@ApiModelProperty(value = "用户的唯一标识")
	private String openId;
	@ApiModelProperty(value = "用户昵称")
	private String nickName;
	@ApiModelProperty(value = "用户的性别，值为1时是男性，值为2时是女性，值为0时是未知")
	private Integer gender;
	@ApiModelProperty(value = "国家，如中国为CN")
	private String country;
	@ApiModelProperty(value = "用户个人资料填写的省份")
	private String province;
	@ApiModelProperty(value = "普通用户个人资料填写的城市")
	private String city;
	@ApiModelProperty(value = "用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像），用户没有头像时该项为空。若用户更换头像，原有头像URL将失效")
	private String avatarUrl;
	@ApiModelProperty(value = "只有在用户将公众号绑定到微信开放平台帐号后，才会出现该字段。详见：获取用户个人信息（UnionID机制）")
	private String unionId;

	@ApiModelProperty(value = "手机号")
	private String phoneNumber;
	@ApiModelProperty(value = "国家码")
	private String countryCode;

}
