package com.dz.common.core.dto.user;

import com.dz.common.core.annotation.Columns;
import com.dz.common.core.enums.ColumnType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springframework.beans.factory.annotation.Value;

/**
 * 会员信息DTO
 * @author: Handy
 * @date:   2022/01/30 22:55
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "会员信息")
public class MemberInfoDTO {

    @ApiModelProperty(value = "用户ID")
    private Long id;
    @ApiModelProperty(value = "会员ID")
    private Long memberId;
    @ApiModelProperty(value = "手机号")
    private String mobile;
    @ApiModelProperty(value = "小程序openid")
    private String openid;
    @ApiModelProperty(value = "微信unionid")
    private String unionid;
    @ApiModelProperty(value = "会员名")
    private String username;
    @ApiModelProperty(value = "出生日期")
    private String birthday;
    @ApiModelProperty(value = "头像")
    private String avatar;
    @ApiModelProperty(value = "性别 0未知 1男 2女")
    private Integer gender;
    @ApiModelProperty(value = "会员卡号")
    private String cardNo;
    @ApiModelProperty(value = "会员等级编号 1普通会员，2铜级会员,3银级会员,4金级会员")
    private String cardLevel;
    @ApiModelProperty(value = "会员等级名称")
    private String cardLevelName;
    @ApiModelProperty(value = "省份")
    private String province;
    @ApiModelProperty(value = "城市")
    private String city;
    @ApiModelProperty(value = "区")
    private String area;
    @ApiModelProperty(value = "详细地址")
    private String address;
    @ApiModelProperty(value = "会员积分")
    private Long points;
    @ApiModelProperty(value = "即将过期积分")
    private Long expirePoints;
    @ApiModelProperty(value = "积分即将过期日期")
    private String expirePointsTime;
    @ApiModelProperty(value = "积分即将过期天数")
    private Integer expirePointsDays;
    @ApiModelProperty(value = "当前里程")
    private Integer currentMileage;
    @ApiModelProperty(value = "当前等级里程门槛数量")
    private Integer currentLeveLMileage;
    @ApiModelProperty(value = "距离下个等级里程")
    private Integer nextMileage;
    @ApiModelProperty(value = "下一等级里程门槛数量")
    private Integer nextLeveLMileage;
    @ApiModelProperty(value = "距离下个会员等级名称")
    private String nextLevelName;
    @ApiModelProperty(value = "品牌最新隐私版本号")
    private String globalAccessVersion;
    @ApiModelProperty(value = "用户最近更新隐私版本号")
    private String personalAccessVersion;
    @ApiModelProperty(value = "是否需要隐私条款弹出")
    private Boolean needAccessWarn;
    @ApiModelProperty(value = "同意接受条款和隐私政策")
    private Boolean agreeClauseAndPrivacy;
    @ApiModelProperty(value = "授权隐私条款版本")
    private String policyVersion;
    @ApiModelProperty(value = "是否勾选接收品牌信息")
    private Integer isAgreeBrand;
    @ApiModelProperty(value = "是否勾选已满十四岁 0勾选 1未勾选")
    private Integer isAgreeYear;
    @ApiModelProperty(value = "注册入会时间")
    private String registerTime;
    @ApiModelProperty(value = "是否会员 0不是会员，1是会员，2已注销")
    private Integer isMember;
    @ApiModelProperty(value = "会员二维码")
    private String qrCode;
    @ApiModelProperty(value = "会员码")
    private String platformCode;
    @ApiModelProperty(value = "会员过期时间")
    private String memberExpireTime;
    @ApiModelProperty(value = "开卡appId")
    private String createAppId;
    @ApiModelProperty(value = "开卡cartId")
    private String createCardId;
    @ApiModelProperty(value = "开卡activateType")
    private String createType;
    @ApiModelProperty(value = "unionid MD5加密串")
    private String clientId;
    @ApiModelProperty(value = "是否冻结 0否，1是")
    private Integer isFreeze;
}
