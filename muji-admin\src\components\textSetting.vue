<template>
  <a-form-item>
    <a-space>
      <a-tooltip placement="bottom">
        <template #title>
          <span>字体类型</span>
        </template>
        <svg t="1734086790346" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4120" width="24" height="24">
          <path d="M45.91488 98.816Q54.93888 64 64.60288 64.64t22.528 26.432q7.744 12.8 12.8 19.968a30.976 30.976 0 0 0 12.224 10.304 46.72 46.72 0 0 0 16.128 3.84q9.024 0.64 23.168 0.64h498.816q23.168 0 37.376-1.28a77.44 77.44 0 0 0 23.168-5.184 28.992 28.992 0 0 0 13.504-10.944q4.48-7.104 10.944-19.968 11.584-24.512 21.888-24.512 16.768 0 16.768 25.6l-2.56 220.416a33.472 33.472 0 0 1-3.2 16.128 6.912 6.912 0 0 1-9.024 3.84 27.2 27.2 0 0 1-12.224-12.8 126.976 126.976 0 0 1-11.584-36.736q-6.4-25.6-12.8-44.48a75.968 75.968 0 0 0-19.2-30.912 78.592 78.592 0 0 0-34.176-18.048 213.504 213.504 0 0 0-54.784-5.824H471.06688v639.296a87.36 87.36 0 0 0 8.384 45.76 58.112 58.112 0 0 0 35.456 18.688l100.544 21.888v28.352q-33.536-2.56-97.28-3.2t-141.056-0.512q-76.032 0-131.456 0.64a619.84 619.84 0 0 0-70.912 3.2v-28.352l94.144-17.728a74.496 74.496 0 0 0 48.96-22.528 80 80 0 0 0 11.584-47.04V181.312H186.39488a131.2 131.2 0 0 0-38.016 2.56 78.08 78.08 0 0 0-31.552 16.768 274.24 274.24 0 0 0-34.176 35.456q-18.688 22.528-43.2 58.624a137.408 137.408 0 0 1-25.6 30.912q-9.024 6.4-11.584 0.64a46.72 46.72 0 0 1-1.28-20.608 269.76 269.76 0 0 1 5.184-32.896z" p-id="4121"></path>
          <path d="M1024.28288 531.648h-384v-64h384z" p-id="4122"></path>
          <path d="M1024.28288 723.648h-384v-64h384z" p-id="4123"></path>
          <path d="M1024.28288 915.648h-384v-64h384z" p-id="4124"></path>
        </svg>
      </a-tooltip>
      <a-select placeholder="字体类型" v-model:value="data.fontFamily" :options="fontFamily" mode="multiple" style="width:200px" allowClear :getPopupContainer="triggerNode => triggerNode.parentNode"></a-select>
      <div class="global-tip">请按照顺序选择</div>
    </a-space>
  </a-form-item>
  <a-form-item>
    <a-space>
      <a-tooltip placement="bottom">
        <template #title>
          <span>字体大小</span>
        </template>
        <svg t="1734086965674" class="icon" viewBox="0 0 1064 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5136" width="24" height="24">
          <path d="M495.73888 195.91168a30.72 30.72 0 0 0-55.37792 2.2528L139.22304 900.79232a51.2 51.2 0 0 1-94.12608-40.30464L346.23488 157.81888c44.07296-102.8096 187.63776-108.70784 240.0256-9.8304l104.12032 196.68992a51.2 51.2 0 1 1-90.5216 47.9232l-104.12032-196.68992zM779.75552 570.28608c-10.36288-26.46016-47.9232-25.84576-57.50784 0.86016l-131.31776 367.65696a51.2 51.2 0 0 1-96.41984-34.4064l131.31776-367.65696c41.3696-115.87584 204.26752-118.3744 249.2416-3.93216l145.408 370.0736a51.2 51.2 0 1 1-95.31392 37.43744l-145.408-370.03264z" fill="#1D1D1F" p-id="5137"></path>
          <path d="M163.84 593.92c0-28.2624 22.9376-51.2 51.2-51.2h266.24a51.2 51.2 0 1 1 0 102.4h-266.24c-28.2624 0-51.2-22.9376-51.2-51.2zM573.44 778.24c0-28.2624 22.9376-51.2 51.2-51.2h266.24a51.2 51.2 0 1 1 0 102.4h-266.24c-28.2624 0-51.2-22.9376-51.2-51.2z" fill="#1D1D1F" p-id="5138"></path>
        </svg>
      </a-tooltip>
      <a-input-number placeholder="字体大小" :min="12" :precision="0" v-model:value="data.fontSize" addon-after="px">
      </a-input-number>
    </a-space>
  </a-form-item>
  <a-form-item>
    <a-space>
      <a-tooltip placement="bottom">
        <template #title>
          <span>行高大小</span>
        </template>

        <svg width="24" height="24" focusable="false">
          <path d="M21 5a1 1 0 0 1 .1 2H13a1 1 0 0 1-.1-2H21zm0 4a1 1 0 0 1 .1 2H13a1 1 0 0 1-.1-2H21zm0 4a1 1 0 0 1 .1 2H13a1 1 0 0 1-.1-2H21zm0 4a1 1 0 0 1 .1 2H13a1 1 0 0 1-.1-2H21zM7 3.6l3.7 3.7a1 1 0 0 1-1.3 1.5h-.1L8 7.3v9.2l1.3-1.3a1 1 0 0 1 1.3 0h.1c.4.4.4 1 0 1.3v.1L7 20.4l-3.7-3.7a1 1 0 0 1 1.3-1.5h.1L6 16.7V7.4L4.7 8.7a1 1 0 0 1-1.3 0h-.1a1 1 0 0 1 0-1.3v-.1L7 3.6z"></path>
        </svg>
      </a-tooltip>
      <a-input-number placeholder="行高大小" :min="1" :precision="0" v-model:value="data.lineHeight" addon-after="px">
      </a-input-number>
    </a-space>
  </a-form-item>
  <a-form-item>
    <a-space>
      <a-tooltip placement="top">
        <template #title>
          <span>字体颜色</span>
        </template>
        <Color :show="false" color="rgba(0,0,0,1)" :value="data.color" @changeColor="(value)=>setValue('color',value)"></Color>
      </a-tooltip>
      <a-tooltip placement="bottom">
        <template #title>
          <span>加粗</span>
        </template>
        <a-button :class="data.fontWeight?'active':''" @click="changeChooise('fontWeight')">
          <template #icon>
            <svg width="24" height="24" focusable="false">
              <path d="M7.8 19c-.3 0-.5 0-.6-.2l-.2-.5V5.7c0-.2 0-.4.2-.5l.6-.2h5c1.5 0 2.7.3 3.5 1 .7.6 1.1 1.4 1.1 2.5a3 3 0 0 1-.6 1.9c-.4.6-1 1-1.6 *******.9.3 1.3.6s.8.7 1 1.2c.4.4.5 1 .5 1.6 0 1.3-.4 2.3-1.3 3-.8.7-2.1 1-3.8 1H7.8Zm5-8.3c.6 0 1.2-.1 1.6-.5.4-.3.6-.7.6-1.3 0-1.1-.8-1.7-2.3-1.7H9.3v3.5h3.4Zm.5 6c.7 0 1.3-.1 1.7-.4.4-.4.6-.9.6-1.5s-.2-1-.7-1.4c-.4-.3-1-.4-2-.4H9.4v3.8h4Z" fill-rule="evenodd"></path>
            </svg>
          </template>
        </a-button>
      </a-tooltip>
      <a-tooltip placement="bottom">
        <template #title>
          <span>斜体</span>
        </template>
        <a-button :class="data.fontItalic?'active':''" @click="()=>changeChooise('fontItalic')">
          <template #icon>
            <svg width="24" height="24" focusable="false">
              <path d="m16.7 4.7-.1.9h-.3c-.6 0-1 0-1.4.3-.3.3-.4.6-.5 1.1l-2.1 9.8v.6c0 .5.4.8 1.4.8h.2l-.2.8H8l.2-.8h.2c1.1 0 1.8-.5 2-1.5l2-9.8.1-.5c0-.6-.4-.8-1.4-.8h-.3l.2-.9h5.8Z" fill-rule="evenodd"></path>
            </svg>
          </template>
        </a-button>
      </a-tooltip>
      <a-tooltip placement="bottom">
        <template #title>
          <span>下划线</span>
        </template>
        <a-button :class="data.underLine?'active':''" @click="()=>changeChooise('underLine')">
          <template #icon>
            <svg width="24" height="24" focusable="false">
              <path d="M16 5c.6 0 1 .4 1 1v5.5a4 4 0 0 1-.4 1.8l-1 1.4a5.3 5.3 0 0 1-5.5 1 5 5 0 0 1-1.6-1c-.5-.4-.8-.9-1.1-1.4a4 4 0 0 1-.4-1.8V6c0-.6.4-1 1-1s1 .4 1 1v5.5c0 .3 0 .6.2 1l.6.7a3.3 3.3 0 0 0 2.2.8 3.4 3.4 0 0 0 2.2-.8c.3-.2.4-.5.6-.8l.2-.9V6c0-.6.4-1 1-1ZM8 17h8c.6 0 1 .4 1 1s-.4 1-1 1H8a1 1 0 0 1 0-2Z" fill-rule="evenodd"></path>
            </svg>
          </template>
        </a-button>
      </a-tooltip>
      <a-tooltip placement="bottom">
        <template #title>
          <span>删除线</span>
        </template>
        <a-button :class="data.deleteLine?'active':''" @click="changeChooise('deleteLine')">
          <template #icon>
            <svg width="24" height="24" focusable="false">
              <g fill-rule="evenodd">
                <path d="M15.6 8.5c-.5-.7-1-1.1-1.3-1.3-.6-.4-1.3-.6-2-.6-2.7 0-2.8 1.7-2.8 2.1 0 1.6 1.8 2 3.2 2.3 4.4.9 4.6 2.8 4.6 3.9 0 1.4-.7 4.1-5 4.1A6.2 6.2 0 0 1 7 16.4l1.5-1.1c.4.6 1.6 2 3.7 2 1.6 0 2.5-.4 3-1.2.4-.8.3-2-.8-2.6-.7-.4-1.6-.7-2.9-1-1-.2-3.9-.8-3.9-3.6C7.6 6 10.3 5 12.4 5c2.9 0 4.2 1.6 4.7 2.4l-1.5 1.1Z"></path>
                <path d="M5 11h14a1 1 0 0 1 0 2H5a1 1 0 0 1 0-2Z" fill-rule="nonzero"></path>
              </g>
            </svg>
          </template>
        </a-button>
      </a-tooltip>
      <a-space>
        <a-space-compact block>
          <a-tooltip placement="bottom">
            <template #title>
              <span>左</span>
            </template>
            <a-button :class="data.textAlign=='left'?'active':''" @click="setValue('textAlign','left')">
              <template #icon>
                <svg width="24" height="24" focusable="false">
                  <path d="M5 5h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 1 1 0-2Zm0 4h8c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 1 1 0-2Zm0 8h8c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 0 1 0-2Zm0-4h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 0 1 0-2Z" fill-rule="evenodd"></path>
                </svg>
              </template>
            </a-button>
          </a-tooltip>
          <a-tooltip placement="bottom">
            <template #title>
              <span>居中</span>
            </template>
            <a-button :class="data.textAlign=='center'?'active':''" @click="setValue('textAlign','center')">
              <template #icon>
                <svg width="24" height="24" focusable="false">
                  <path d="M5 5h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 1 1 0-2Zm3 4h8c.6 0 1 .4 1 1s-.4 1-1 1H8a1 1 0 1 1 0-2Zm0 8h8c.6 0 1 .4 1 1s-.4 1-1 1H8a1 1 0 0 1 0-2Zm-3-4h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 0 1 0-2Z" fill-rule="evenodd"></path>
                </svg>
              </template>
            </a-button>
          </a-tooltip>
          <a-tooltip placement="bottom">
            <template #title>
              <span>右</span>
            </template>
            <a-button :class="data.textAlign=='right'?'active':''" @click="setValue('textAlign','right')">
              <template #icon>
                <svg width="24" height="24" focusable="false">
                  <path d="M5 5h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 1 1 0-2Zm6 4h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 0 1 0-2Zm0 8h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 0 1 0-2Zm-6-4h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 0 1 0-2Z" fill-rule="evenodd"></path>
                </svg>
              </template>
            </a-button>
          </a-tooltip>
          <a-tooltip placement="bottom">
            <template #title>
              <span>两端对齐</span>
            </template>
            <a-button :class="data.textAlign=='justify'?'active':''" @click="setValue('textAlign','justify')">
              <template #icon>
                <svg width="24" height="24" focusable="false">
                  <path d="M5 5h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 1 1 0-2Zm0 4h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 1 1 0-2Zm0 4h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 0 1 0-2Zm0 4h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 0 1 0-2Z" fill-rule="evenodd"></path>
                </svg>
              </template>
            </a-button>
          </a-tooltip>
        </a-space-compact>
      </a-space>
    </a-space>
  </a-form-item>
  <div class="header-title">组件样式</div>
  <a-form-item label="显示行数">
    <a-radio-group v-model:value="data.lineType" size="small">
      <a-radio-button :value="1">不限行数</a-radio-button>
      <a-radio-button :value="2">固定行数</a-radio-button>
      <a-radio-button :value="3">最大行数</a-radio-button>
    </a-radio-group>
  </a-form-item>
  <a-form-item style="margin-left:80px;" v-if="data.lineType!=1">
    <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.lines" addon-after="行"></a-input-number>
  </a-form-item>
  <a-form-item label="边距" :labelCol="{width:'50px'}">
    <a-space>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingTop" addon-before="上" addon-after="px"></a-input-number>
      </a-form-item>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingBottom" addon-before="下" addon-after="px"></a-input-number>
      </a-form-item>
    </a-space>
    <a-space>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingLeft" addon-before="左" addon-after="px"></a-input-number>
      </a-form-item>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingRight" addon-before="右" addon-after="px"></a-input-number>
      </a-form-item>
    </a-space>
  </a-form-item>
</template>
<script setup>
const emit = defineEmits(['ok', 'cancel'])
import { fontFamily } from "@/utils/fixedVariable.js";
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  // 当前组件数据
  data: {
    type: Object,
    default() {
      return {}
    }
  },

})



const setValue = (key, value) => {
  props.data[key] = value
}
const changeChooise = (key) => {
  props.data[key] = !props.data[key]
}
</script>

<style lang="scss" scoped>
.active {
  background: #a6ccf7;
}
</style>
