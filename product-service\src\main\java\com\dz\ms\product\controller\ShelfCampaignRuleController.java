package com.dz.ms.product.controller;

import com.dz.common.base.vo.Result;
import com.dz.common.core.constants.NumConstants;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.ms.product.dto.ShelfCampaignRuleDTO;
import com.dz.ms.product.service.ShelfCampaignRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "营销活动规则")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class ShelfCampaignRuleController {

    @Resource
    private ShelfCampaignRuleService shelfCampaignRuleService;

    /**
     * 根据营销活动ID查询营销活动规则列表
     * @param campaignId 营销活动ID
     * @return result<List<ShelfCampaignRuleDTO>>
     */
    @ApiOperation("根据营销活动ID查询营销活动规则列表")
    @GetMapping(value = "/crm/shelf_campaign_rule/list")
    public Result<List<ShelfCampaignRuleDTO>> getShelfCampaignRuleList(@RequestParam("campaignId") Long campaignId) {
        Result<List<ShelfCampaignRuleDTO>> result = new Result<>();
        List<ShelfCampaignRuleDTO> list = shelfCampaignRuleService.getRuleListByCampaignIds(Collections.singletonList(campaignId), false);
        result.setData(list);
        return result;
    }

    /**
     * 根据ID查询营销活动规则
     *
     * @param id
     * @return result<ShelfCampaignRuleDTO>
     */
    @ApiOperation("根据ID查询营销活动规则")
    @GetMapping(value = "/shelf_campaign_rule/info")
    public Result<ShelfCampaignRuleDTO> getShelfCampaignRuleById(@RequestParam("id") Long id) {
        Result<ShelfCampaignRuleDTO> result = new Result<>();
        ShelfCampaignRuleDTO shelfCampaignRule = shelfCampaignRuleService.getShelfCampaignRuleById(id, NumConstants.ONE);
        result.setData(shelfCampaignRule);
        return result;
    }

    /**
     * 根据ID删除营销活动规则
     *
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除营销活动规则")
    @PostMapping(value = "/shelf_campaign_rule/delete")
    public Result<Boolean> deleteShelfCampaignRuleById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        shelfCampaignRuleService.deleteShelfCampaignRuleById(param);
        result.setData(true);
        return result;
    }

}
