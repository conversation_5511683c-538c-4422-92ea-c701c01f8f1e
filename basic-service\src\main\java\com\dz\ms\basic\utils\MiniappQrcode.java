package com.dz.ms.basic.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.util.Map;

public class MiniappQrcode {

    public static void main(String[] args) throws IOException {
        String[] strs = new String[]{"67#BB009-杭州大厦","68#BB021-沈阳中兴","69#BB026-上海第一八佰伴","70#BB027-上海久光","71#BB029-哈尔滨远大","72#BB031-北京汉光","73#BB034-深圳茂业","74#BB048-成都王府井","75#BB052-苏州中心","76#BB088-武汉群光","77#BB104-广州天河城","78#BB129-青岛海信","79#BB155-成都万象城","80#BB164-新世界大丸","81#BB168-南宁万象城","82#BB170-西安开元","83#BB190-杭州in77","84#BB228-西安SKP","85#BB233-杭州万象城","86#BB237-西安赛格","87#BB020-杭州银泰","88#BB022-武汉广场","89#BB032-宁波银泰","90#BB054-成都群光","91#BB075-北京SKP","92#BB121-南京德基","93#BB126-沈阳万象城","94#BB140-天津海信","95#BB156-深圳万象天地","96#BB160-合肥银泰","97#BB025-大连麦凯乐","98#BB046-广州友谊","99#BB050-常州泰富","100#BB064-长沙王府井","101#BB066-石家庄北国","102#BB080-济南银座","103#BB087-广州正佳","104#BB089-长春欧亚","105#BB091-昆明百盛","106#BB110-徐州金鹰","107#BB120-重庆新世纪","108#BB123-北京西单大悦城","109#BB130-福州东百","110#BB132-成都王府井二店","111#BB135-兰州国芳","112#BB141-深圳海岸城","113#BB183-郑州正弘城","114#BB200-南京河西金鹰世界","115#BB209-无锡恒隆","116#BB210-太原王府井","117#BB211-厦门万象城","118#BB216-南京中央","119#BB226-天津万象城","120#BB229-南京金鹰B馆","121#BB230-深圳壹方城","122#BB231-乌鲁木齐美美二","123#BB232-郑州大卫城","124#BB234-无锡大东方","125#BB238-南昌百盛","126#BB007-上海百盛","127#BB011-朝北大悦城","128#BB014-北京双安","129#BB018-天津伊势丹","130#BB024-南京金鹰A馆","131#BB028-北京王府井","132#BB030-长春卓展","133#BB037-无锡百盛","134#BB041-苏州美罗城","135#BB042-长沙平和堂","136#BB058-西安金鹰","137#BB062-贵阳国贸","138#BB090-哈尔滨松雷","139#BB100-深圳海雅缤纷城","140#BB102-重庆时代天街","141#BB103-中山公园龙之梦","142#BB105-武汉万象城","143#BB108-南宁百盛","144#BB111-扬州金鹰","145#BB127-成都光华","146#BB128-重庆新光","147#BB139-宜昌国贸","148#BB142-重庆天街","149#BB149-南通文峰","150#BB191-北京Indigo","151#BB193-西安开元二柜","152#BB205-福州万象城","153#BB208-南宁梦之岛水晶城","154#BB212-宁波万象城","155#BB213-佛山王府井","156#BB218-北京荟聚宜家","157#BB224-上海百联又一城","158#BB227-济南万象城","159#BB154-嘉兴八佰伴","160#BB157-海口王府井","161#BB177-厦门中华城FSS","162#BB178-南昌武商mall","163#BB179-郑州正弘汇","164#BB180-成都SKP","165#BB189-昆明金俊","166#BB202-武汉梦时代","167#BB203-上海IFC","168#BB206-广州K11","169#BB214-广州IGC","170#BB215-长沙IFS"};
        for (int i = 0; i < strs.length; i++) {
            String str = strs[i];
            String[] array = str.split("#");

            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl("https://bb-vip.elcapp.cn/api/crm/basic/miniapp/qrcode");
            builder.queryParam("page", "pages/campaign-24cny/campaign-24cny");
            builder.queryParam("scene", "type%3D1%26id%3D"+array[0]);
            //builder.queryParam("trial", "1");
            builder.queryParam("width", "640");
            builder.queryParam("token", "eyJhbGciOiJIUzI1NiJ9.eyJyb2xlIjoxLCJpZCI6MTAsInR5cGUiOjUsImV4cCI6MTcwNDQyMzM1NSwidGlkIjoyfQ.r1IXBcZEyAA-rrxQ5PjzY2OoZrYw6-b9nJxvjUab2_w");

            byte[] bytes = new RestTemplate().getForObject(URI.create(builder.build().toString()), byte[].class);
            InputStream in = new ByteArrayInputStream(bytes);

            // 读取输入流中的图像数据并创建BufferedImage对象
            BufferedImage image = ImageIO.read(in);

            // 将BufferedImage对象写入输出文件
            File outputFile = new File("C:\\Users\\<USER>\\Desktop\\autocode\\imgs\\"+array[0]+"-"+array[1]+".jpg"); // 输出文件的路径和名称
            ImageIO.write(image, "jpg", outputFile);

        }
    }
}
