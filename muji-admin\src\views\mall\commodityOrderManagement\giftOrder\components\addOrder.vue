<template>

  <a-modal :title="title" width="576px" v-if="open" placement="right" :maskClosable="false" :closable="true" :open="open" @cancel="onClose">

    <a-form class="form" ref="addForm" :model="addParams" :rules="rules" :labelCol="{ style: 'width:100px' }">

      <a-form-item label="快递编号" name="expressCode">
        <a-input placeholder="请输入快递编号" v-model:value="addParams.expressCode" show-count :maxlength="50" />
      </a-form-item>
      <!-- <a-form-item label="订单编号" name="orderCode">
        <a-input placeholder="请输入订单编号" v-model:value="addParams.orderCode" show-count :maxlength="50" />
      </a-form-item> -->
    </a-form>

    <template #footer v-if="!disabled">
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="ok" :loading="loading">确定</a-button>
      </a-space>
    </template>

  </a-modal>

</template>
<script setup>
import { exchange_orderdelivery } from '@/http/index.js'
import { useGlobalStore } from '@/store'
import { message, Modal } from "ant-design-vue";


const global = useGlobalStore()
const addForm = ref(null)
import { cloneDeep } from 'lodash'
import { onMounted } from 'vue';
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  // 操作抽屉打开关闭
  visible: {
    type: Boolean,
    default: false
  },
  // 数据ID
  id: {
    type: String || Number,
    default: ''
  },
  // 操作类型
  type: {
    type: Number,
    default: 0, // 0-新增 1-编辑  2-查看
  },
  addType: {
    type: String,
    default: '1', // 1 级 2级
  }

})
// 置灰
const disabled = computed(() => {

  return props.type == 2
})

// 标题
const title = computed(() => {
  return '发货'
})

const { open, addParams, rules, loading, } = toRefs(reactive({
  open: props.visible,


  loading: false,

  addParams: {
    // orderCode: '',
    expressCode: ''
  },
  rules: {
    expressCode: [{ required: true, message: '请输入快递编号', trigger: ['blur', 'change'] }],
    // orderCode: [{ required: true, message: '请输入订单编号', trigger: ['blur', 'change'] }],
  }
})
);
// onMounted(() => {
//     initData()
// })
watch(() => props.visible, (value) => {
  // 弹窗操作
  open.value = props.visible
  addParams.value = {
    // orderCode: '',
    expressCode: ''
  },

    addForm.value?.resetFields()
  if (open.value) {

    initData()
  }
})

//所有接口调取出
const initData = async () => {
  const promiseArr = []

  if (props.id) {


  }

  // 如果有新的接口就在这里push
  loading.value = true
  try {
    const [] = await Promise.all(promiseArr)


    loading.value = false
  } catch (error) {
    loading.value = false
    console.error('获取数据失败:', error)
  }
}




// 关闭弹窗
const onClose = () => {
  emit('cancel')
}

// 确定
const ok = () => {

  addForm.value.validate().then(res => {
    let params = cloneDeep(addParams.value)
    loading.value = true
    params.orderCode = props.id
    exchange_orderdelivery(params).then(res => {
      message.success(res.msg);
      emit('ok',)
    }).finally(() => {
      loading.value = false
    })
    // if (props.id) {
    //   console.log('编辑');
    //   tag_infoUpdate(params).then(res => {
    //     message.success(res.msg);
    //     emit('ok', props.id)
    //   }).finally(() => {
    //     loading.value = false
    //   })
    // } else {
    //   console.log(params, 'xin');
    //   tag_infoAdd(params).then(res => {
    //     message.success(res.msg);
    //     emit('ok', props.id)
    //   }).finally(() => {
    //     loading.value = false
    //   })
    // }

  })
}



</script>

<style scoped lang="scss">
.form {
}
:deep(.searchForm .ant-form-item) {
  // margin-bottom: 16px;
}

:deep(.anticon-eye-invisible) {
  display: none;
}

.member-role {
  background: #f2f2f2;
  margin-bottom: 20px;
  padding: 5px 10px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &:nth-child(odd) {
    margin-right: 35px;
  }
}
</style>
