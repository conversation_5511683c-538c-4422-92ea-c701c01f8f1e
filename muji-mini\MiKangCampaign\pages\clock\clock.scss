/* MiKangCampaign/pages/clock/clock.wxss */
.page-container {
    // height: 100vh;
    display: flex;
    background-size: 100% auto;
    position: relative;

    .page-top-clock {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 2;

        .page-top-back {
            width: 100%;
        }

        .back_title {
            position: absolute;
            left: 48rpx;
            top: 208rpx;
            font-family: MUJIFont2020;
            color: #ffffff;
            font-size: 64rpx;
            line-height: 72rpx;
            font-weight: 700;

            .title-view {
                margin-top: 18rpx;
            }

            text {
                line-height: 72rpx;
            }
        }

        .clockList {
            width: 654rpx;
            height: 328rpx;
            position: absolute;
            bottom: 48rpx;
            margin: 242rpx 48rpx 0rpx;

            .clockList-back {
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0;
                left: 0rpx;
                background-size: 100% auto;
            }

            .clockList-in {
                position: relative;
                box-sizing: border-box;
                width: 654rpx;
                height: 320rpx;
                margin: auto;
                padding-top: 20rpx;

                .clock-text1 {
                    margin-left: 34rpx;
                    margin-top: 28rpx;
                    font-size: 24rpx;
                    font-weight: 400;
                    line-height: 40rpx;
                    color: #756453;
                    font-family: MUJIFont2020;

                    text {
                        font-weight: 900;
                        font-size: 32rpx;
                    }
                }

                .clock-text2 {
                    margin-top: 0rpx;
                }

                .clockList-wrap {
                    box-sizing: border-box;
                    width: 100%;
                    display: flex;
                    justify-content: space-between;
                    flex-wrap: wrap;
                    padding: 0 32rpx;

                    .clockItem {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        flex-direction: column;
                        color: #333333;
                        margin-top: 24rpx;
                        box-sizing: border-box;

                        .item {
                            width: 56rpx;
                            height: 84rpx;
                            font-family: MUJIFont2020;
                            font-weight: bold;
                            font-size: 32rpx;
                            color: var(--text-black-color);
                            line-height: 84rpx;
                            text-align: center;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            position: relative;
                            color: #ffffff;

                            .item-img {
                                width: 56rpx;
                                height: 84rpx;
                                position: absolute;
                                top: 0rpx;
                                left: 0rpx;
                                z-index: 0;
                                // background-size: 100% auto;
                            }

                            .daysText {
                                position: relative;
                                top: 0rpx;
                                left: 0rpx;
                                font-size: 32rpx;
                                font-weight: 900;
                                z-index: 1;
                                writing-mode: vertical-lr;
                                text-orientation: upright;
                                padding-right: 1rpx;
                            }

                            .today-daysText {
                                color: #ffffff;
                                font-size: 24rpx;
                                font-weight: 700;
                            }

                            .iconfont {
                                width: 71rpx;
                                height: 71rpx;
                                background-size: 100% 100%;
                                margin-right: 20rpx;
                                margin-top: 10rpx;

                                image {
                                    width: 100%;
                                    height: 100%;
                                }
                            }
                        }
                    }

                    .icon-wrap {
                        margin-top: 20rpx;
                        height: 36rpx;
                        width: 110rpx;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        position: relative;

                        .icon {
                            width: 14rpx;
                            height: 14rpx;
                            border-radius: 50%;
                            background: #c8b49a;
                            margin-top: 19rpx;
                        }

                        .icon-font {
                            color: #ffffff;
                            width: 100%;
                            height: 100%;
                            position: relative;
                            z-index: 1;
                        }

                        .icon-state1 {
                            width: 40rpx;
                            height: 40rpx;
                            // font-size: 24rpx;
                            box-sizing: border-box;

                            image {
                                width: 100%;
                                height: 100%;
                            }
                        }

                        .line {
                            display: inline-block;
                            position: absolute;
                            left: 50%;
                            width: 100%;
                            height: 1rpx;
                            background-color: #ffffff;
                            z-index: 0;

                            ::before {
                                content: "";
                                position: absolute;
                                background-color: white;
                                /* 横线的颜色 */
                                position: absolute;
                                box-sizing: border-box;
                                content: " ";
                                pointer-events: none;
                                top: -50%;
                                right: -50%;
                                bottom: -50%;
                                left: -50%;
                                border: 0 solid white;
                                -webkit-transform: scale(0.5);
                                transform: scale(0.5);
                            }

                            &::before {
                                right: -64rpx;
                            }
                        }

                        .buka {
                            color: #ffffff;
                            width: 72rpx;
                            height: 36rpx;
                            line-height: 36rpx;
                            border-radius: 8rpx;
                            text-align: center;
                            box-sizing: border-box;
                            border: 2rpx solid #ffffff;
                            font-size: 20rpx;
                        }
                    }

                    .isSingIn {
                        .item {
                            color: #786756;
                        }
                    }

                    .notSingIn {
                        font-size: 24rpx;
                    }

                    .today {
                        .item {
                            line-height: auto;
                            font-size: 24rpx;
                            writing-mode: vertical-lr;
                        }
                    }

                    .item-notSingIn {
                        width: 76rpx;
                        height: 43rpx;
                        font-family: MUJIFont2020;
                        font-weight: 700;
                        font-size: 18rpx;
                        color: #c8b49a;
                        line-height: 47rpx;
                        text-align: center;
                        background: #f8f6ed;
                        text-decoration: underline;
                    }

                    .item-notSingIn-today {
                        width: 76rpx;
                        height: 43rpx;
                        font-family: MUJIFont2020;
                        font-weight: 700;
                        font-size: 18rpx;
                        line-height: 47rpx;
                        text-align: center;
                        color: #c8b49a;
                    }
                }
            }
        }
    }

    .clock-record {
        position: absolute;
        bottom: 0rpx;
        width: 100%;
        height: calc(100% - 802rpx);
        overflow: hidden;
        background-size: 100% auto;
        z-index: 1;

        .record_wrap {
            // height: 100%;
            overflow: auto;
            padding: 0 50rpx;
            padding-bottom: 99rpx;
            // margin-top: 80rpx;

            .record-title {
                font-family: MUJIFont2020;
                font-weight: 700;
                font-size: 36rpx;
                color: #3c3c43;
                line-height: 40rpx;
                letter-spacing: 1rpx;
                text-align: left;
                margin-top: 48rpx;
            }

            .noData {
                font-family: MUJIFont2020;
                font-weight: 400;
                font-size: 24rpx;
                line-height: 38rpx;
                // letter-spacing: 4rpx;
                text-align: center;
                margin-top: 180rpx;
                color: #888888;
            }

            .record {
                padding-bottom: 34rpx;

                .record-item {
                    width: 100%;
                    margin: 43rpx 0;
                    display: flex;
                    align-items: center;
                    box-sizing: border-box;
                    border-bottom: 2rpx solid #d0b99b61;
                    align-items: flex-end;

                    .Day {
                        width: 134rpx;
                        height: 74rpx;
                        font-family: "MUJIFont2020";
                        margin-bottom: 40rpx;

                        image {
                            width: 100%;
                            height: 100%;
                        }
                    }

                    .record-item-time {
                        display: flex;
                        justify-content: space-between;
                    }

                    .record-item-overall {
                        width: calc(100% - 180rpx);
                        font-family: MUJIFont2020;
                        font-weight: 400;
                        font-size: 20rpx;
                        color: #756453;
                        line-height: 36rpx;
                        padding-left: 39rpx;
                        margin-bottom: 40rpx;

                        .title {
                            height: 36rpx;
                            line-height: 36rpx;
                            margin-bottom: 8rpx;
                        }

                        .start {
                            margin-top: 3rpx;
                            display: flex;
                            justify-content: space-between;
                            align-items: flex-end;
                            // padding-bottom : 12rpx;

                            .van-icon__image {
                                width: 32rpx;
                                height: 48rpx;
                            }

                            .detail {
                                width: 112rpx;
                                height: 56rpx;
                                line-height: 54rpx;
                                text-align: center;
                                box-sizing: border-box;
                                border: 2rpx solid #756453;
                                font-size: 24rpx;
                                border-radius: 8rpx;
                            }
                        }
                    }
                }
            }
        }
    }

    .clock-btn {
        display: flex;
        align-items: flex-end;
        justify-content: center;
        width: 100%;
        height: 168rpx;
        margin-top: 40rpx;
        position: fixed;
        width: 100%;
        bottom: 0rpx;
        z-index: 2;

        .basic-button {
            margin-bottom: 76rpx;
        }

        .timeicon {
            width: 39rpx;
            height: 37rpx;
            background-size: 100% 100%;
            margin-right: 13rpx;
        }
    }

    .box_bottom {
        position: fixed;
        right: 0rpx;
        bottom: 168rpx;
        z-index: 3;

        .box-right {
            width: 164rpx;
            height: 196rpx;
        }
    }

    .page-rule {
        //position: absolute;
        z-index: 10;

        position: fixed;
        right: 0rpx;
        top: 208rpx;
        width: 60rpx;
        height: 160rpx;
        background: rgba(236, 216, 186, 0.4);
        font-family: MUJIFont2020;
        font-weight: 700;
        font-size: 28rpx;
        color: #ffffff;
        line-height: 24rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        writing-mode: vertical-rl;
        letter-spacing: 4rpx;
        border-radius: 16rpx 0rpx 0rpx 16rpx;
        margin: 10rpx 0rpx 10rpx 20rpx;
    }
}

/* 在main.wxss中写  main.wxml引入swiper组件 */
/* 修改dot形状 */
.swiper .wx-swiper-dots .wx-swiper-dot {
    width: 86rpx;
    height: 2rpx;
    border-radius: 0rpx;
    margin: 0rpx;
}
