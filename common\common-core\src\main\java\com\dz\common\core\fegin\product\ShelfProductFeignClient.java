package com.dz.common.core.fegin.product;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.ExchangeStaticParamDTO;
import com.dz.common.core.dto.InventoryFlowParamDTO;
import com.dz.common.core.dto.product.InventoryParamDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(name = ServiceConstant.PRODUCT_SERVICE_NAME, contextId = "ProductFeignClient")
public interface ShelfProductFeignClient {


    /**
     * 库存扣除确认接口
     */
    @PostMapping("/shelf_product/update")
    Result<Void> update(@RequestBody InventoryFlowParamDTO param);


    /**
     * 更新货架库存/活动库存
     *
     * @param param
     * @return
     */
    @PostMapping("/shelf_product/update_inventory")
    Result<Void> updateInventory(@RequestBody InventoryParamDTO param);

    /**
     * 校验货架库存/活动库存
     *
     * @param param
     * @return
     */
    @PostMapping("/shelf_product/validate_inventory")
    Result<Void> validateInventory(@RequestBody InventoryParamDTO param);

    /**
     * 推送兑换成功统计数据
     *
     * @param orderStaticParam
     */
    @PostMapping("/shelf_product/update_static")
    void updateStatic(ExchangeStaticParamDTO orderStaticParam);
}

