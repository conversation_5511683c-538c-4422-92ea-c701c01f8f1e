package com.dz.ms.user.utils;

/**
 * <AUTHOR>
 */

public enum PointsTwoType {
    WECHATPOINTS(0, "微信绑定积分"),
    COSMETICSPOINTS(1, "雷达表考核积分"),
    TOILETPOINTS(2, "特殊贡献积分");

    private int code;
    private String message;

    public static String getName(int index) {
        for (PointsTwoType c : PointsTwoType.values()) {
            if (c.getCode() == index) {
                return c.getMessage();
            }
        }
        return null;
    }

    private PointsTwoType(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }
}