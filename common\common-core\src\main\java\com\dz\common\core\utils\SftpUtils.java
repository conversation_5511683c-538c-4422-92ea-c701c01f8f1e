package com.dz.common.core.utils;

import com.dz.common.core.constants.SFTPChannel;
import com.dz.common.core.constants.SFTPConstans;
import com.dz.common.core.dto.adaptor.SftpDTO;
import com.dz.common.core.exception.BusinessException;
import com.jcraft.jsch.*;
import com.opencsv.CSVParser;
import com.opencsv.CSVParserBuilder;
import com.opencsv.CSVReader;
import com.opencsv.CSVReaderBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.util.*;

@Component
@Slf4j
public class SftpUtils {
  private static Session sshSession;



  /**
   * 连接sftp服务器
   *
   * @param host     ftp地址
   * @param port     端口
   * @param userName 账号
   * @param password 密码
   * @return
   */
  public static ChannelSftp sftpConnection(String host, int port, String userName, String password) throws Exception {
    JSch jsch = new JSch();
    ChannelSftp channelSftp;
    try {
      jsch.getSession(userName, host);
      sshSession = jsch.getSession(userName, host);
      sshSession.setPassword(password);
      Properties properties = new Properties();
      properties.put("StrictHostKeyChecking", "no");
      sshSession.setConfig(properties);
      sshSession.connect();
      Channel channel = sshSession.openChannel("sftp");
      channel.connect();
      channelSftp = (ChannelSftp) channel;
    } catch (JSchException e) {
      e.printStackTrace();
      throw new Exception();
    }
    return channelSftp;
  }

  /**
   * @return
   * @description 退出Sftp服务器登录
   **/
  public static void sftpClose(ChannelSftp channelSftp) {
    if (channelSftp != null) {
      if (channelSftp.isConnected()) {
        channelSftp.disconnect();
      }
    }
  }

  /**
   * 关闭session
   */
  public static void sessionClose() {
    if (sshSession != null) {
      if (sshSession.isConnected()) {
        sshSession.disconnect();
        sshSession = null;
      }
    }
  }

  /**
   * sftp服务器
   * <b>将一个IO流解析，转化数组形式的集合<b>
   * csv文件解析
   *
   * @param in 文件inputStream流
   */
  public static List<String[]> sftpCsv(InputStream in) {
    List<String[]> csvList = new ArrayList<String[]>();
    if (null != in) {
      try {
        InputStreamReader is = new InputStreamReader(in, "UTF-8");
        CSVParser csvParser = new CSVParserBuilder().build();
        CSVReader reader = new CSVReaderBuilder(is).withCSVParser(csvParser).build();
        csvList = reader.readAll();
      } catch (UnsupportedEncodingException e) {
        e.printStackTrace();
      } catch (IOException e) {
        e.printStackTrace();
      }catch (Exception e){
        e.printStackTrace();
      }
    }
    return csvList;
  }


  public static SftpDTO getSftpFile(SftpDTO sftpDTO){
    //1-调用SftpUtil获取当日文件
    SftpDTO sftpDTO1 = new SftpDTO();
    log.info("sftp user member come in");
    InputStream inputStream = null;
    SimpleDateFormat sdfFile = new SimpleDateFormat("yyyy_MM_dd");
    try {
      log.info("sftp connect");
      String fileName = sftpDTO.getFileName();
      //1-创建sftp连接
      log.info("用户名sftp get fileName:"+sftpDTO.getUserName());
      ChannelSftp sftp = SftpUtils.sftpConnection(sftpDTO.getHost(), sftpDTO.getPort(), sftpDTO.getUserName(), sftpDTO.getPass());
      log.info("用户sftp connect success");
      //2-进入所在路径
      sftp.cd(sftpDTO.getFilePath());
      //3-获取文件
      inputStream = sftp.get(fileName);
      sftpDTO1.setSftp(sftp);
      sftpDTO1.setIn(inputStream);
      /*SftpUtils.sftpClose(sftp);
      SftpUtils.sessionClose();*/
      //4-处理数据
      if(inputStream == null){
        log.info("未获取到今日文件,请联系上游确认!");
        return null;
      }else {
        return sftpDTO1;
      }

    } catch (Exception e) {
      e.printStackTrace();
      throw new BusinessException("SFTP获取文件异常："+e.getMessage());
    }
  }

  /**
   * 上传文件，并返回最终文件名
   * @param file 文件
   * @param targetDir 目标目录，不存在则创建
   * @param host 上传服务器
   * @param userName 用户名
   * @param pass 密码
   * @param ftpPort 端口
   * @return 最终文件名
   */
  public static String uploadFileForBatchImport(MultipartFile file, String targetDir, String host, String userName, String pass, String ftpPort) {
    Map<String, String> sftpDetails = new HashMap<>(4);
    // 设置主机ip，端口，用户名，密码
    sftpDetails.put(SFTPConstans.SFTP_REQ_HOST, host);
    sftpDetails.put(SFTPConstans.SFTP_REQ_USERNAME, userName);
    sftpDetails.put(SFTPConstans.SFTP_REQ_PASSWORD, pass);
    sftpDetails.put(SFTPConstans.SFTP_REQ_PORT, ftpPort);
    String prefix = System.currentTimeMillis() + "-";
    SFTPChannel sftpChannel = new SFTPChannel();
    ChannelSftp chSftp = null;
    String originalFilename = file.getOriginalFilename();
    try {
      chSftp = sftpChannel.getChannel(sftpDetails, 60000);
      //判断目录是否存在，不存在则创建
      if (dirIsExistAndCreate(chSftp, targetDir)) {
        String dst = targetDir + prefix + originalFilename;
        // 文件流写入远程目录
        chSftp.put(file.getInputStream(), dst, ChannelSftp.OVERWRITE);
      }
    } catch (JSchException | SftpException | IOException e) {
      log.warn("上传文件失败", e);
    } finally {
      if (null != chSftp) {
        chSftp.quit();
      }
      try {
        sftpChannel.closeChannel();
      } catch (Exception e) {
        log.warn("文件流关闭失败", e);
      }
    }
    return prefix + originalFilename;
  }


  public static boolean dirIsExistAndCreate(ChannelSftp chSftp, String dir) {
    try {
      chSftp.cd(dir);
      return true;
    } catch (SftpException e) {
      if(Objects.isNull(dir)){
        return false;
      }
      String[] dirs = dir.split("/");
      String tempPath = "";
      for (String d : dirs) {
        tempPath = tempPath + "/" + d;
        try {
          chSftp.cd(tempPath);
        } catch (SftpException ee) {
          // 文件不存在
          try {
            chSftp.mkdir(tempPath);
          } catch (SftpException e2) {
            log.warn("创建文件失败, {}",  tempPath, e2);
            return false;
          }
        }
      }
      return true;
    }
  }

  public static InputStream downloadMyMsg(HttpServletResponse response, String fileName, String requestDomain) throws MalformedURLException {

    // 下载网络文件
    URL url = new URL(requestDomain+"/muji/my-msg/"+fileName);
    InputStream inStream=null;
    try {
      URLConnection conn = url.openConnection();
      inStream = conn.getInputStream();
    } catch (FileNotFoundException e) {
      e.printStackTrace();
    } catch (IOException e) {
      e.printStackTrace();
    }
    return inStream;
  }

}
