package com.dz.ms.product.dto.req;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * 商品货架查询入参
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:27
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@ApiModel(value = "商品货架查询入参")
public class ShelfParamDTO extends BaseDTO {

    @ApiModelProperty(value = "货架id列表")
    private List<Long> shelfIdList;
    @ApiModelProperty(value = "货架名称")
    private String name;
    @ApiModelProperty(value = "上架开始时间")
    private Date onStartTime;
    @ApiModelProperty(value = "上架结束时间-下架")
    private Date onEndTime;
    @ApiModelProperty(value = "货架状态 1未开始/2上架中/3已结束")
    private Integer shelfState;
    @ApiModelProperty(value = "货架启用状态 0禁用 1启用")
    private Integer state;

    @ApiModelProperty(value = "不限制删除条件的货架id列表")
    private List<Long> noLimitShelfIds;

}
