package com.dz.common.core.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 系统日志日志切片注解
 * <AUTHOR>
 * @date 2022/8/4 19:14
 */
@Retention(RetentionPolicy.RUNTIME) 
@Target(ElementType.METHOD) 
@Documented
public @interface EmpAuth {

	String value() default "";
	
}
