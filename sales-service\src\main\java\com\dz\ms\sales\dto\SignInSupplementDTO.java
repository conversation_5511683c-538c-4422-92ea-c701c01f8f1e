package com.dz.ms.sales.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/2/7
 */
@Data
public class SignInSupplementDTO {


    @ApiModelProperty(value = "剩余补签次数")
    private Integer patchSignInTimes = 0;

    @ApiModelProperty(value = "补签成功状态 0失败 1成功")
    private Integer patchSignInState = 1;

    @ApiModelProperty(value = "补签成功状态说明")
    private String patchSignInStateDesc = "";

}
