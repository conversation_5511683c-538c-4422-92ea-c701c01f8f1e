<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.dz.gateway</groupId>
	<artifactId>gateway</artifactId>
	<version>1.1.6</version> <!-- Upgraded version -->
	<name>gateway</name>
	<description>gateway project for Spring Boot</description>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.7.18</version> <!-- Upgraded to Spring Boot 2.7.x for security and stability -->
	</parent>

	<dependencies>
		<!-- Spring Cloud Alibaba Nacos Discovery -->

		<!-- 适配nacos 3.0.0 -->
		<!-- <dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
			<version>2022.0.0.0</version> 
		</dependency> -->
		
		
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
			<version>2021.0.5.0</version>
		</dependency>

		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
			<version>1.2.13</version> <!-- Updated version to resolve CVE-2023-6378 -->
		</dependency>
		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-core</artifactId>
			<version>1.2.13</version>
		</dependency>
		<!-- Spring Cloud Gateway -->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-gateway</artifactId>
			<version>3.1.8</version> <!-- Upgraded version to ensure compatibility and security -->
			<exclusions>
				<exclusion>
					<groupId>ch.qos.logback</groupId>
					<artifactId>logback-classic</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
			<version>5.3.34</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
			<version>5.3.34</version>
		</dependency>
		<dependency>
			<groupId>org.yaml</groupId>
			<artifactId>snakeyaml</artifactId>
			<version>1.33</version>
		</dependency>
		<!-- Common Base Dependency -->
		<dependency>
			<groupId>com.dz.common</groupId>
			<artifactId>common-base</artifactId>
			<version>1.1.6</version> <!-- No change needed -->
		</dependency>

		<!-- Spring Cloud Loadbalancer -->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-loadbalancer</artifactId>
			<version>3.1.7</version> <!-- Upgraded version -->
		</dependency>

		<!-- Knife4j for Swagger UI -->
		<dependency>
			<groupId>com.github.xiaoymin</groupId>
			<artifactId>knife4j-spring-ui</artifactId>
			<version>2.0.9</version> <!-- Upgraded version -->
		</dependency>

		<!-- Lombok -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>io.springfox</groupId>-->
<!--			<artifactId>springfox-swagger2</artifactId>-->
<!--			<version>2.9.2</version>-->
<!--		</dependency>-->
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-ui</artifactId>
			<version>1.6.14</version>
		</dependency>
		<dependency>
			<groupId>io.swagger</groupId>
			<artifactId>swagger-models</artifactId>
			<version>1.6.1</version>
		</dependency>
		<dependency>
			<groupId>com.github.xiaoymin</groupId>
			<artifactId>swagger-bootstrap-ui</artifactId>
			<version>1.9.6</version>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>11</source> <!-- Upgraded to Java 11, as Spring Boot 2.7.x supports Java 11 and later -->
					<target>11</target> <!-- Set target JVM to 11 -->
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
