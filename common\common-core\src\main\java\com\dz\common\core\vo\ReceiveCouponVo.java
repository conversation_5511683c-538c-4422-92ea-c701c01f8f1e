package com.dz.common.core.vo;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 券领取入参
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReceiveCouponVo extends BaseDTO
{
    @ApiModelProperty(value = "券ID")
    private String couponId;
    @ApiModelProperty(value = "活动ID")
    private String activityId;
    @ApiModelProperty(value = "领券类型，1活动券，2普通券")
    private Integer receiveType;
}
