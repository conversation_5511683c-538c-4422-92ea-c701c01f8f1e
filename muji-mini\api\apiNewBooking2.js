export const apiNewBooking2 = {
  async getBookingInfo (data) {
    return wx.$request({
      url: '/app/sales/plant_trees/my_info',
      data,
      method: 'get'
    }).then(res => {
      res.data = res.data || {}
      return res
    })
  },
  async setBookingInfo (data) {
    return wx.$request({
      url: '/app/sales/plant_trees/add',
      data,
      method: 'post'
    }).then(res => {
      return res
    })
  },
  async getWhitelist (data) {
    return wx.$request({
      url: '/app/sales/plant_trees/config',
      data,
      method: 'get'
    }).then(res => {
      return res
    })
  }
}
