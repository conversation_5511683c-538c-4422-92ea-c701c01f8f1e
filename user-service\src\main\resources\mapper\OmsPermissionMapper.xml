<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.user.mapper.OmsPermissionMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    id,
  	    code,
  	    parent_id,
  	    permit_type,
  	    permit_name,
  	    url,
  	    permit_desc,
  	    display_sort,
  	    has_child,
  	    created,
  	    creator,
  	    modified,
  	    modifier
    </sql>

    <!-- 获取角色权限列表 -->
    <select id="getRolePermitCodes" parameterType="java.lang.Long" resultType="com.dz.ms.user.entity.OmsPermission">
        select
        p.id,p.code,p.parent_id
        from oms_permission p
        <if test="roleId > 0">
            join oms_role_permission rp
            on rp.permit_id = p.id
            where rp.role_id = #{roleId}
        </if>
    </select>

    <!-- 获取角色权限列表 -->
    <select id="getRolePermitIds" parameterType="java.lang.Long" resultType="java.lang.Long">
        select
		p.id
        from oms_role_permission rp
        join oms_permission p
        on rp.permit_id = p.id
        where rp.role_id = #{roleId}
        and p.has_child = 0
    </select>

	<!-- 获取接口权限URL对应父节点列表 -->
	<select id="getPermissionUrlList" resultType="com.dz.ms.user.entity.OmsPermission">
        select
		p.url,p1.code
        from oms_permission p
        join oms_permission p1
        on p.parent_id = p1.id
        where p.permit_type = 3
    </select>

</mapper>
