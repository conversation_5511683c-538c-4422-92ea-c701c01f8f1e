package com.dz.ms.sales.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 任务信息
 */
@Getter
@Setter
@NoArgsConstructor
@Table("用户互动任务记录表")
@TableName(value = "t_user_task_record")
public class UserTaskRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "主键id")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "用户id")
    private Long userId;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "任务id")
    private Long taskId;

    @Columns(type = ColumnType.VARCHAR, length = 255, isNull = true, comment = "创建人")
    private String createAt;

    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "创建时间")
    private Date createTime;

    @Columns(type = ColumnType.VARCHAR, length = 255, isNull = true, comment = "修改人")
    private String updateAt;

    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "修改时间")
    private Date updateTime;

    @Columns(type = ColumnType.BIGINT, length = 11, isNull = false, comment = "租户id")
    private Long tenantId;

    @Columns(type = ColumnType.INT, length = 10,defaultValue = "0", isNull = true, comment = "任务完成次数")
    private Integer finishNum;
}
