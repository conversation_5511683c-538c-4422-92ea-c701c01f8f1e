package com.dz.ms.sales.dto;


import com.dz.common.core.annotation.Columns;
import com.dz.common.core.enums.ColumnType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/30
 */
@Data
public class CampaignDTO {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("活动名称")
    private String campaignName;

    @ApiModelProperty("活动标识")
    private String campaignCode;

    @ApiModelProperty("活动主图")
    private String campaignImg;

    @ApiModelProperty("活动详情图")
    private String campaignDetailImg;

    @ApiModelProperty("活动规则图")
    private String campaignRuleImg;

    @ApiModelProperty("活动描述")
    private String campaignDesc;

    @ApiModelProperty("开始时间")
    private Date campaignStartTime;

    @ApiModelProperty("结束时间")
    private Date campaignEndTime;

    @ApiModelProperty("活动公示时间")
    private String campaignShowTime;

    @ApiModelProperty("下期活动时间")
    private String nextCampaignTime;

    @ApiModelProperty("发送短信时间")
    private String sendSmsTime;

    @ApiModelProperty("campaign内容json")
    private String content;

    @ApiModelProperty("是否允许报名参加 0不允许 1允许")
    private Integer isAllowSignUp;

    @ApiModelProperty("关联活动标识")
    private String campaignJoinCode;

    @ApiModelProperty("活动状态 0未开始 1进行中 2公示期 3已结束")
    private Integer state;

    @ApiModelProperty("活动购买资格配置")
    private CampaignBuyerDTO campaignBuyer;

    @ApiModelProperty("活动签到配置")
    private CampaignSignInConfigDTO campaignSignInConfig;

    private Boolean cleanCache = false;

//    @ApiModelProperty("活动白名单用户")
//    private List<CampaignUserDTO> campaignUser;

}
