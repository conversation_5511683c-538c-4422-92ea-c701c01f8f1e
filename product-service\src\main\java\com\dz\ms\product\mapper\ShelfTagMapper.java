package com.dz.ms.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.product.dto.ShelfTagDTO;
import com.dz.ms.product.entity.ShelfTag;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 货架标签表Mapper
 *
 * @author: LiinNs
 * @date: 2024/11/20 11:32
 */
@Repository
public interface ShelfTagMapper extends BaseMapper<ShelfTag> {

    List<ShelfTag> selectAllList(@Param("param") ShelfTagDTO param);
    
}
