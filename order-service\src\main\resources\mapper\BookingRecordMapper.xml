<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.order.mapper.BookingRecordMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    id,
  	    record_code,
  	    uid,
  	    user_name,
  	    gender,
  	    mobile,
  	    card_level,
  	    booking_id,
  	    booking_name,
  	    booking_img,
  	    booking_date,
  	    time_slot,
  	    booking_time_start,
  	    booking_time_end,
  	    verify_time_start,
  	    verify_time_end,
  	    item_type,
  	    item_id,
  	    item_name,
  	    item_introduction,
  	    store_code,
  	    store_name,
  	    emp_code,
  	    verify_store_code,
  	    verify_store_name,
  	    verifier_type,
  	    verifier_code,
  	    verifier_name,
  	    state,
  	    msg_state,
  	    state_time,
  	    update_num,
  	    emp_booking,
  	    consume_type,
  	    tenant_id,
  	    created,
  	    creator,
  	    modified,
  	    modifier
    </sql>

    <!-- 查询示例 -->
    <select id="selectDemo" parameterType="java.lang.Long" resultType="com.dz.ms.order.entity.BookingRecord">
        select
        <include refid="Base_Column_List" />
        from booking_record
        where id = #{id,jdbcType=BIGINT}
        AND tenant_id = #{tenantId}
    </select>

</mapper>
