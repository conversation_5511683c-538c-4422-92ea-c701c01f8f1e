package com.dz.ms.product.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 货架人群包条件
 *
 * @author: LiinNs
 * @date: 2024/11/27 15:15
 */
@Getter
@Setter
@NoArgsConstructor
@Table("货架人群包条件")
@TableName(value = "shelf_people")
public class ShelfPeople implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "货架ID")
    private Long shelfId;
    @Columns(type = ColumnType.VARCHAR, length = 256, isNull = true, comment = "货架名称")
    private String shelfName;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "人群包ID")
    private Long groupId;
    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = false, comment = "人群包名称")
    private String groupName;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, comment = "限制条件 1等级 2性别")
    private Integer conditionType;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, comment = "条件关系 1且 2或")
    private Integer conditionLogic;
    @Columns(type = ColumnType.VARCHAR, length = 25, isNull = true, comment = "条件文字描述 etc. 普通会员 男")
    private String conditionStr;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, comment = "货架状态 0禁用 1启用")
    private Integer state;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    public ShelfPeople(Long id, Long shelfId, String shelfName, Long groupId, String groupName, Integer conditionType, Integer conditionLogic, String conditionStr, Integer state) {
        this.id = id;
        this.shelfId = shelfId;
        this.shelfName = shelfName;
        this.groupId = groupId;
        this.groupName = groupName;
        this.conditionType = conditionType;
        this.conditionLogic = conditionLogic;
        this.conditionStr = conditionStr;
        this.state = state;
    }

}
