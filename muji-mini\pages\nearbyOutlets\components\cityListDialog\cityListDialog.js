const app = getApp()
import {
  getCityList
} from '../../../../api/index.js'

Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    isShow: {
      type: Boolean,
      value: false,
    },
  },
  lifetimes: {
    attached() {
      this.getList();
    }
  },
  /**
   * 组件的初始数据
   */
  data: {
    cityList: [],
    currentCity: '全国',
  },

  /**
   * 组件的方法列表
   */
  methods: {
    onClose() {
      this.triggerEvent('closeDialog')
    },
    getList() {
      getCityList().then((res) => {
        this.setData({
          cityList: ['全国', ...res.data],
        })
      })
    },
    onChange(e) {
      const {
        value
      } = e.detail;
      this.setData({
        currentCity: value,
      })
    },
    confirm() {
      const {
        currentCity
      } = this.data;
      this.triggerEvent('filter', {
        city: currentCity
      });
      this.triggerEvent('closeDialog')
    }
  },
})
