package com.dz.ms.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.user.entity.AppointmentBanner;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface AppointmentBannerMapper extends BaseMapper<AppointmentBanner> {
    @Select("SELECT * FROM appointment_banner WHERE appointment_id = #{appointmentId}")
    List<AppointmentBanner> findByAppointmentId(Long appointmentId);
}