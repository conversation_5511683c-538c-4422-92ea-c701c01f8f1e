package com.dz.common.core.fegin.sales;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.dto.user.InteractionTaskDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.text.ParseException;
import java.util.List;

/**
 * 任务信息FeginClient
 *
 * @Author: Handy
 * @Date: 2022/2/3 23:11
 */
@FeignClient(name = ServiceConstant.SALES_SERVICE_NAME, contextId = "InteractionTaskFeginClient")
public interface InteractionTaskFeginClient {

    /**
     * 完成邀请好友任务奖励
     * @return
     */
    @ApiOperation("完成邀请好友任务奖励")
    @GetMapping(value = "/interaction/task/friend")
    public Result<Object> taskSuccessRecordFriend(@RequestParam(value = "inviteUserId") Long inviteUserId);

    /**
     * 导出任务列表
     *
     * @return
     */
    @PostMapping(value = "/interaction/export_task_list")
    public Result<Void> exportTaskList(@RequestBody DownloadAddParamDTO exportParam);


    /**
     * 完成兑礼任务奖励
     * @return
     */
    @GetMapping(value = "/interaction/task/order")
    public Result<Object> taskSuccessRecordByOrder() throws ParseException;

    /**
     * 完成线下消费任务奖励
     * @return
     */
    @GetMapping(value = "/interaction/task/product")
    public Result<Object> taskSuccessRecordByProduct() throws ParseException;

    /**
     * 任务三天到期订阅消息提醒
     * @return
     */
    @PostMapping(value = "/subscription/task/expire/three")
    public Result<Object> taskExpireThree();

    /**
     * 查询所有任务名称
     * @return
     */
    @GetMapping(value = "/interaction/info/by/name")
    public Result<List<String>> getInteractionTaskInfo();

}

