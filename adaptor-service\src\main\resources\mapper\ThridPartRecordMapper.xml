<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.adaptor.mapper.ThirdPartyRecordMapper" >

    <!-- 批量插入 -->
    <insert id="inserByTable">
        insert into t_third_party_record_${nowDay}
        (api_desc,
        api_url,
        param,
        result,
        create_time,
        status,
        fail_desc,
        tenant_id,
        request_time
        )
        values
        (
            #{thirdPartyRecord.apiDesc},
            #{thirdPartyRecord.apiUrl},
            #{thirdPartyRecord.param},
            #{thirdPartyRecord.result},
            #{thirdPartyRecord.createTime},
            #{thirdPartyRecord.status},
            #{thirdPartyRecord.failDesc},
            #{thirdPartyRecord.tenantId},
            #{thirdPartyRecord.requestTime}
        )
    </insert>

    <!--获取分表列表-->
    <select id="queryThirdPartyRecordTableList" resultType="java.lang.String">
        SHOW TABLES LIKE 't_third_party_record_${nowDay}'
    </select>

    <!--创建分表-->
    <update id="createThirdPartyRecordTable" parameterType="java.lang.String">
        CREATE TABLE muji_adaptor.t_third_party_record_${nowDay}  (
                                                 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                 `api_desc` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '接口名称',
                                                 `api_url` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '接口路径',
                                                 `param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '参数',
                                                 `result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '返回结果',
                                                 `create_time` datetime NOT NULL COMMENT '创建时间',
                                                 `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 0失败 1成功',
                                                 `fail_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '失败原因',
                                                 `tenant_id` bigint NOT NULL COMMENT '租户ID',
                                                 `request_time` bigint NULL DEFAULT NULL COMMENT '请求耗时',
                                                 PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 551455 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '请求三方接口的记录表' ROW_FORMAT = DYNAMIC;
    </update>
</mapper>
