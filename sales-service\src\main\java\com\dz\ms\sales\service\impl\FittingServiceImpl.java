package com.dz.ms.sales.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.basic.AliCheckRequestDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.sales.dto.FittingDTO;
import com.dz.ms.sales.entity.Fitting;
import com.dz.ms.sales.mapper.FittingMapper;
import com.dz.ms.sales.service.FittingService;
import com.dz.ms.sales.service.SignInUserDetailService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 用户试衣照记录表
 * @author: 
 * @date:   2025/03/21 11:43
 */
@Service
public class FittingServiceImpl extends ServiceImpl<FittingMapper,Fitting> implements FittingService {

    @Resource
    private SignInUserDetailService signInUserDetailService;
	@Resource
    private FittingMapper fittingMapper;

	/**
     * 分页查询用户试衣照记录表
     * @param param FittingDTO
     * @return PageInfo<FittingDTO>
     */
    @Override
    public PageInfo<FittingDTO> getFittingList(FittingDTO param) {
        Long uid = SecurityContext.getUser().getUid();
        Fitting fitting = Fitting.builder().userId(uid).build();
        QueryWrapper<Fitting> fittingQueryWrapper = new QueryWrapper<>(fitting);
        fittingQueryWrapper.orderByDesc("id");
        IPage<Fitting> page = fittingMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), fittingQueryWrapper);
        return new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),BeanCopierUtils.convertList(page.getRecords(), FittingDTO.class));
    }

    /**
     * 根据ID查询用户试衣照记录表
     * @param id id
     * @return FittingDTO
     */
    @Override
    public FittingDTO getFittingById(Long id) {
        Fitting fitting = fittingMapper.selectOne(new LambdaQueryWrapper<>(Fitting.builder().id(id).userId(SecurityContext.getUser().getUid()).build()));
        return BeanCopierUtils.convertObject(fitting,FittingDTO.class);
    }

    /**
     * 保存用户试衣照记录表
     * @param param FittingDTO
     * @return Long
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveFitting(FittingDTO param) {
        if(Objects.isNull(param.getTemplateId()) || StringUtils.isBlank(param.getImgUrl())){
            throw new BusinessException(ErrorCode.BAD_REQUEST);
        }
        Fitting fitting = BeanCopierUtils.convertObject(param,Fitting.class);
        fitting.setUserId(SecurityContext.getUser().getUid());
        if(ParamUtils.isNullOr0Long(fitting.getId())) {
            fittingMapper.insert(fitting);
            signInUserDetailService.verifyImageSafety2(fitting.getImgUrl(),new AliCheckRequestDTO("",null,2,fitting.getId()));
            signInUserDetailService.verifyTextSafety2(fitting.getImgText(),new AliCheckRequestDTO("",null,2,fitting.getId()));
        }
        return fitting.getId();
    }

    /**
     * 根据ID删除用户试衣照记录表
     * @param param IdCodeDTO
     */
    @Override
    public void deleteFittingById(IdCodeDTO param) {
        FittingDTO dto = this.getFittingById(param.getId());
        if(Objects.nonNull(dto)){
            fittingMapper.deleteById(param.getId());
        }
    }
	
}
