const app = getApp();
import { homeGiftList, couponNum } from "../../api/index";

Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true,
  },
  properties: {
    scrollTop: {
      type: Number,
      value: 0,
    },
    item: {
      type: Object,
      value: {},
    },
    // 游客模式
    visitor: {
      type: <PERSON>olean,
    },
    userInfo: {
      type: Object,
      value() {
        return {};
      },
      observer(val) {
        if (val.isMember > 0) {
          this.getUserInfo()
        }
      },
    },
  },
  pageLifetimes: {
    show() {
      // 需要每次展示都更新数据
      this.getUserInfo();
    },
  },
  data: {
    current: 0, // 升级到下一级已拥有的里程数
    total: 100, // 升级到下一级需要的里程数
    currentScale: "0%", // 升级到下一级已拥有的里程数 百分比
    lastScale: "100%", // 升级到下一级还需要的里程数 百分比
    currentRotate: "53.95deg", // min：53.95deg、max：126.2deg 以iPhoneX为基准
    memberLevel: [],
    giftData: {},
    showModal: false,
    couponNumInfo: 0,
    isRed: 2, // 1-是  2-否  是否显示小红点
  },

  attached() {
    this.setScale();
    this.getUserInfo();
  },
  methods: {
    handleGoLegend() {
      wx.$mp.navigateTo({
        url: "/pages/myLegend/myLegend",
      });
    },
    // 获取用户信息
    async getUserInfo() {
      this.getCouponNum();
      this.getGift();
      // await app.getUserInfo()
      const userInfo = app.globalData.userInfo;
      // userInfo.cardLevel = '4'
      let {
        cardLevel,
        currentLeveLMileage,
        currentMileage,
        nextLeveLMileage,
      } = userInfo;
      let total = nextLeveLMileage - currentLeveLMileage;
      let current = currentMileage - currentLeveLMileage;
      if (current === 0 && total === 0) total = 100;
      if (current > total) current = total;
      if (cardLevel === "4") {
        current = 100;
        total = 100;
      }
      this.setData({
        total,
        current,
        userInfo,
      });
      let time = setTimeout(() => {
        clearTimeout(time);
        time = null;
        this.setScale();
      }, 1500);
    },
    // 触发订阅消息
    goSubscribe: app.debounce(async function (e) {
      let { scene } = e.currentTarget.dataset;
      app.subscribe(scene).then(() => {
        let url = "";
        if (scene === "coupon") {
          url = "/pages/myCoupon/myCoupon";
        } else if (scene === "point") {
          url = "/pages/myIntegral/myIntegral";
        }
        wx.$mp.navigateTo({
          url,
        });
      });
    }),
    // 获取券梳理
    getCouponNum() {
      couponNum().then((res) => {
        this.setData({
          couponNumInfo: res.data?.couponNum,
          isRed: res.data?.isRed || 2, // 1-是  2-否  是否显示小红点
        });
      });
    },
    // 获取礼券信息
    getGift() {
      return homeGiftList().then((res) => {
        // giftType 礼遇类型：1新人礼配置，2二回礼配置，3生日礼配置，4升级礼配置，5无礼遇、有可使用的商品券时，6无以上礼遇可领取且券列表有有效券时，7无礼遇、无可用券时）
        res.data.jumpUrl = res.data.jumpUrl ? JSON.parse(res.data.jumpUrl) : [];
        res.data.ballJumpUrl = res.data.ballJumpUrl
          ? JSON.parse(res.data.ballJumpUrl)
          : [];
        // 生日礼特殊处理
        if (res.data.giftType == 3 && !app.globalData.userInfo?.birthday) {
          res.data.jumpData = {
            imgUrl: res.data.reveiveBallImg,
            imgLinks: res.data.ballJumpUrl,
          };
        } else if (res.data.giftType == 4) {
          // 升级礼
          let result = res.data.giftConfigOtherDTO.filter(
            (item) => item.levelId == app.globalData.userInfo.cardLevel
          );
          if (result.length) {
            res.data.jumpData = {
              imgUrl: result[0].ballImg,
              imgLinks: result[0].ballJump
                ? JSON.parse(result[0].ballJump)
                : [],
            };
          }
        } else if (res.data.ballImg) {
          res.data.jumpData = {
            imgUrl: res.data.ballImg,
            imgLinks: res.data.jumpUrl,
          };
        }
        this.setData({
          giftData: res.data,
        });
      });
    },
    // 展示弹窗
    showJumpModal: app.debounce(async function (e) {
      app.subscribe("order").then(() => {
        if (this.data.giftData.jumpData?.imgUrl) {
          this.setData({
            showModal: true,
          });
        }
      });
    }),
    // 关闭弹窗
    closeModal() {
      this.setData({
        showModal: false,
      });
    },
    // 跳转附近门店
    goStore: app.debounce(async function () {
      wx.$mp.navigateTo({
        url: "/pages/nearbyOutlets/nearbyOutlets",
      });
    }),
    // 跳转注册
    goRegister: app.debounce(async function () {
      wx.$mp.track({
        event: "home_register_click",
      });
      wx.$mp.navigateTo({
        url: "/pages/register/register",
      });
    }),
    setScale() {
      let {
        current,
        total,
        currentScale,
        lastScale,
        currentRotate,
      } = this.data;
      const currentScaleNum = (current / total) * 100;
      currentScale = `${currentScaleNum}%`;
      lastScale = `${100 - currentScaleNum}%`;
      let start = 53.95;
      let stop = 126.2;
      const windowWidth = wx.getSystemInfoSync().windowWidth;
      if (windowWidth === 430) {
        // iPhone 14 Pro Max
        // iPhone 15 Pro Max
        start = 54.2;
      }
      currentRotate = `${start + (stop - start) * (currentScaleNum / 100)}deg`;
      this.setData({
        current,
        total,
        currentScale,
        lastScale,
        currentRotate,
      });
    },
    // 游客模式隐私协议
    showPrincy() {
      var currentInstance = wx.$mp.getCurrentPage();
      currentInstance.showOveralModal("secret");
    },
  },
});
