<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>common</artifactId>
    <groupId>com.dz.common</groupId>
    <version>1.1.6</version>
    <packaging>pom</packaging>
    <name>common</name>
    <description>common</description>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
        <relativePath/>
    </parent>

    <modules>
        <module>common-base</module>
        <module>common-core</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>11</java.version>
        <spring-cloud.version>2021.0.8</spring-cloud.version>
        <spring.cloud.alibaba.version>2021.0.5.0</spring.cloud.alibaba.version>
        <common.version>1.1.6</common.version>
        <mybatis.version>3.5.13</mybatis.version> <!-- Updated to latest stable -->
        <mapper.version>4.2.0</mapper.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.dz.common</groupId>
                <artifactId>common-base</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dz.common</groupId>
                <artifactId>common-core</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>3.5.5</version> <!-- Compatible with Spring Boot 2.7.x -->
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.apache.tomcat.embed/tomcat-embed-core -->
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>9.0.98</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.yaml/snakeyaml -->
<!--            <dependency>-->
<!--                <groupId>org.yaml</groupId>-->
<!--                <artifactId>snakeyaml</artifactId>-->
<!--                <version>2.2</version>-->
<!--            </dependency>-->
            <!-- https://mvnrepository.com/artifact/com.h2database/h2 -->
<!--            <dependency>-->
<!--                <groupId>com.h2database</groupId>-->
<!--                <artifactId>h2</artifactId>-->
<!--                <version>2.2.220</version>-->
<!--                <scope>test</scope>-->
<!--            </dependency>-->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>1.26.0</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>3.25.5</version>
            </dependency>

            <!-- MySQL -->
<!--            <dependency>-->
<!--                <groupId>mysql</groupId>-->
<!--                <artifactId>mysql-connector-java</artifactId>-->
<!--                <version>8.0.33</version> &lt;!&ndash; Updated to latest stable &ndash;&gt;-->
<!--            </dependency>-->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>8.3.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>shardingsphere-jdbc-core</artifactId>
                <version>5.3.2</version> <!-- Updated to latest stable -->
                <exclusions>
                    <exclusion>
                        <groupId>org.yaml</groupId>
                        <artifactId>snakeyaml</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.zookeeper</groupId>
                        <artifactId>zookeeper</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.h2database</groupId>
                        <artifactId>h2</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>1.33</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>1.2.13</version> <!-- Updated version to resolve CVE-2023-6378 -->
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>1.2.13</version> <!-- Updated version to resolve CVE-2023-6378 -->
            </dependency>

            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>2.3.1</version> <!-- Updated to latest stable -->
            </dependency>
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>2.0.34</version> <!-- Updated to latest stable -->
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>2.0.34</version> <!-- Updated to latest stable -->
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>2.13.5</version> <!-- Updated to latest stable -->
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>2.13.5</version> <!-- Updated to latest stable -->
            </dependency>

            <!-- Commons and HTTP -->
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.16.0</version> <!-- Updated to latest stable -->
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.14</version> <!-- Updated to latest stable -->
            </dependency>

            <!-- Lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.28</version> <!-- Updated to latest stable -->
            </dependency>
            <!-- Spring Web -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>5.3.39</version> <!-- Updated to resolve CVE-2016-1000027 -->
            </dependency>

            <!-- PostgreSQL -->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>42.3.9</version> <!-- Updated to resolve CVE-2024-1597 -->
            </dependency>
            <!-- Swagger -->
<!--            <dependency>-->
<!--                <groupId>io.springfox</groupId>-->
<!--                <artifactId>springfox-swagger2</artifactId>-->
<!--                <version>2.10.5</version> &lt;!&ndash; Updated to latest stable &ndash;&gt;-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>org.springdoc</groupId>-->
<!--                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>-->
<!--                <version>2.2.0</version> &lt;!&ndash; 版本可根据需要调整 &ndash;&gt;-->
<!--            </dependency>-->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>1.6.14</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>1.6.10</version> <!-- Updated to latest stable -->
            </dependency>
<!--            <dependency>-->
<!--                <groupId>com.github.xiaoymin</groupId>-->
<!--                <artifactId>swagger-bootstrap-ui</artifactId>-->
<!--                <version>1.9.6</version> &lt;!&ndash; No newer version available &ndash;&gt;-->
<!--            </dependency>-->

            <!-- Guava -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>32.1.2-jre</version> <!-- Updated to latest stable -->
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!-- 项目发布管理 -->
    <distributionManagement>
        <repository>
            <id>maven-release</id>
            <name>Nexus Release Repository</name>
            <url>http://nexus.muji.com.cn/repository/memeber-maven-release/</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshot</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://nexus.muji.com.cn/repository/memeber-maven-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>
</project>