<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.user.mapper.SysRoleMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    id,
  	    role_name,
  	    role_desc,
  	    tenant_id,
  	    created,
  	    creator,
  	    modified,
  	    modifier
    </sql>

    <!-- 根据系统用户ID列表获取绑定角色列表 -->
    <select id="getSysRoleByUserIds" resultType="com.dz.ms.user.dto.SysUserRoleDTO">
        select
        sur.uid,
        sur.role_id,
        sr.role_name
        from sys_role sr
        left join sys_users_role sur on sr.id = sur.role_id
        where sur.uid in
        <foreach collection="ids" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

</mapper>
