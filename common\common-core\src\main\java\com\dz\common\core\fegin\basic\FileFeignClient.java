package com.dz.common.core.fegin.basic;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/8/8
 */
@FeignClient(name = ServiceConstant.BASIC_SERVICE_NAME, contextId = "FileFeignClient")
public interface FileFeignClient {


    @PostMapping(value = "/crm/file/upload")
    public Result<String> uploadFile(@RequestParam("file") MultipartFile file);
}
