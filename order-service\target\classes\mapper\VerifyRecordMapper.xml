<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.order.mapper.VerifyRecordMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    id,
  	    verify_code,
  	    verify_id,
  	    verify_type,
  	    uid,
  	    user_name,
  	    gender,
  	    mobile,
  	    card_level,
  	    record_name,
  	    item_name,
  	    booking_date,
  	    time_slot,
  	    verify_start_time,
  	    verify_end_time,
  	    store_code,
  	    store_name,
  	    emp_code,
  	    verify_store_code,
  	    verify_store_name,
  	    verifier_type,
  	    verifier_code,
  	    verifier_name,
  	    canceler_type,
  	    canceler_code,
  	    canceler_name,
  	    updater_type,
  	    updater_code,
  	    updater_name,
  	    consume_type,
  	    tenant_id,
  	    creator,
  	    created,
  	    modifier,
  	    modified
    </sql>

    <!-- 查询示例 -->
    <select id="selectDemo" parameterType="java.lang.Long" resultType="com.dz.ms.order.entity.VerifyRecord">
        select
        <include refid="Base_Column_List" />
        from verify_record
        where id = #{id,jdbcType=BIGINT}
        AND tenant_id = #{tenantId}
    </select>

</mapper>
