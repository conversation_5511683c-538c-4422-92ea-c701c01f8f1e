package com.dz.ms.user.controller;

import com.dz.common.core.constants.NumConstants;
import com.dz.ms.user.dto.dkeyam.req.*;
import com.dz.ms.user.dto.dkeyam.res.CallbackAuthenticateRes;
import com.dz.ms.user.dto.dkeyam.res.CallbackGetAllUserRes;
import com.dz.ms.user.dto.dkeyam.res.CallbackGetOneUserRes;
import com.dz.ms.user.dto.dkeyam.res.DKeyAmPageInfo;
import com.dz.ms.user.dto.dkeyam.res.DKeyAmResult;
import com.dz.ms.user.service.DKeyAmOpenApiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags="宁盾回调接口")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE},value = "/crm")
public class DKeyAmCallbackApiController {

    @Resource
    private DKeyAmOpenApiService dKeyAmOpenApiService;

    @ApiOperation("认证")
    @PostMapping(value = "/dKeyAm/callback/strongAuthenticate")
    public DKeyAmResult<String> strongAuthenticate(@RequestBody StrongAuthenticateParam param) {
        return dKeyAmOpenApiService.strongAuthenticateParam(param);
    }
    
    @ApiOperation("外部用户认证")
    @PostMapping(value = "/dKeyAm/callback/authenticate")
    public DKeyAmResult<CallbackAuthenticateRes> callbackAuthenticate(@RequestBody CallbackAuthenticateParam param) {
        DKeyAmResult<CallbackAuthenticateRes> result = new DKeyAmResult<>();
        result.setSuccess(true);
        CallbackAuthenticateRes req;
        try {
            req = dKeyAmOpenApiService.callbackAuthenticate(param);
            result.setData(req);
            result.setErrorCode(NumConstants.ZERO);
        } catch (Exception e){
            if(StringUtils.equals(e.getMessage(),"未查询到此账号") || StringUtils.equals(e.getMessage(),"密码错误") || StringUtils.equals(e.getMessage(),"账号已停用")){
                result.setErrorCode(-16);
                result.setMessage("用户名或密码错误");
            } else {
                result.setSuccess(false);
            }
        }
        return result;
    }

    @ApiOperation("获取单个用户的信息")
    @PostMapping(value = "/dKeyAm/callback/getOneUser")
    public DKeyAmResult<CallbackGetOneUserRes> callbackGetOneUser(@RequestBody CallbackGetOneUserParam param) {
        DKeyAmResult<CallbackGetOneUserRes> result = new DKeyAmResult<>();
        result.setSuccess(true);
        CallbackGetOneUserRes req;
        try {
            req = dKeyAmOpenApiService.callbackGetOneUser(param);
            result.setData(req);
            result.setErrorCode(NumConstants.ZERO);
        } catch (Exception e){
            if(StringUtils.equals(e.getMessage(),"未查询到此账号") || StringUtils.equals(e.getMessage(),"账号已停用")){
                result.setErrorCode(-1);
                result.setMessage("用户不存在");
            } else {
                result.setSuccess(false);
            }
        }
        return result;
    }

    @ApiOperation("同步所有用户的信息")
    @PostMapping(value = "/dKeyAm/callback/getAllUser")
    public DKeyAmResult<DKeyAmPageInfo<CallbackGetAllUserRes>> callbackGetAllUser(@RequestBody CallbackGetAllUserParam param) {
        DKeyAmResult<DKeyAmPageInfo<CallbackGetAllUserRes>> result = new DKeyAmResult<>();
        result.setSuccess(true);
        DKeyAmPageInfo<CallbackGetAllUserRes> req;
        try {
            req = dKeyAmOpenApiService.callbackGetAllUserReq(param);
            result.setData(req);
            result.setErrorCode(NumConstants.ZERO);
        } catch (Exception e){
            result.setSuccess(false);
        }
        return result;
    }

    @ApiOperation("增量同步用户")
    @PostMapping(value = "/dKeyAm/callback/userIncSync")
    public DKeyAmResult<String> userIncSync(@RequestBody UserIncSyncParam param) {
        DKeyAmResult<String> result = new DKeyAmResult<>();
        result.setSuccess(true);
        try {
            dKeyAmOpenApiService.userIncSync(param);
            result.setErrorCode(NumConstants.ZERO);
        } catch (Exception e){
            result.setSuccess(false);
        }
        return result;
    }
    @ApiOperation("发送动态密码")
    @PostMapping(value = "/dKeyAm/callback/sendDynamicPassword")
    public DKeyAmResult<String> sendDynamicPassword(@RequestBody UserIncSyncParam param) {
        DKeyAmResult<String> result = new DKeyAmResult<>();
        result.setSuccess(true);
        try {
            dKeyAmOpenApiService.userIncSync(param);
            result.setErrorCode(NumConstants.ZERO);
        } catch (Exception e){
            result.setSuccess(false);
        }
        return result;
    }
    @ApiOperation("删除用户")
    @PostMapping(value = "/dKeyAm/callback/deleteUser")
    public DKeyAmResult<String> deleteUser(@RequestBody UserIncSyncParam param) {
        DKeyAmResult<String> result = new DKeyAmResult<>();
        result.setSuccess(true);
        try {
            dKeyAmOpenApiService.userIncSync(param);
            result.setErrorCode(NumConstants.ZERO);
        } catch (Exception e){
            result.setSuccess(false);
        }
        return result;
    }
    @ApiOperation("获取用户认证策略")
    @PostMapping(value = "/dKeyAm/callback/getPasswordPolicy")
    public DKeyAmResult<String> getPasswordPolicy(@RequestBody UserIncSyncParam param) {
        DKeyAmResult<String> result = new DKeyAmResult<>();
        result.setSuccess(true);
        try {
            dKeyAmOpenApiService.userIncSync(param);
            result.setErrorCode(NumConstants.ZERO);
        } catch (Exception e){
            result.setSuccess(false);
        }
        return result;
    }
    @ApiOperation("用户和令牌的绑定关系")
    @PostMapping(value = "/dKeyAm/callback/bindingList")
    public DKeyAmResult<String> bindUserToken(@RequestBody BindingListParam param) {
        DKeyAmResult<String> result = new DKeyAmResult<>();
        result.setSuccess(true);
        try {
            dKeyAmOpenApiService.bindingList(param);
            result.setErrorCode(NumConstants.ZERO);
        } catch (Exception e){
            result.setSuccess(false);
        }
        return result;
    }
    @ApiOperation("绑定并获取手机令牌二维码")
    @PostMapping(value = "/dKeyAm/callback/bindAndWriteMobileTokenQrCode")
    public DKeyAmResult<String> mobileTokenUserQrCode(@RequestBody MobileTokenBindTokenQrCodeParam param) {
        DKeyAmResult<String> result = new DKeyAmResult<>();
        result.setSuccess(true);
        try {
            dKeyAmOpenApiService.bindAndWriteMobileTokenQrCode(param);
            result.setErrorCode(NumConstants.ZERO);
        } catch (Exception e){
            result.setSuccess(false);
        }
        return result;
    }
    @ApiOperation("获取手机令牌二维码")
    @PostMapping(value = "/dKeyAm/callback/mobileTokenUserQrCode")
    public DKeyAmResult<String> bindAndWriteMobileTokenQrCode(@RequestBody MobileTokenUserQrCodeParam param) {
        DKeyAmResult<String> result = new DKeyAmResult<>();
        result.setSuccess(true);
        try {
            dKeyAmOpenApiService.mobileTokenUserQrCode(param);
            result.setErrorCode(NumConstants.ZERO);
        } catch (Exception e){
            result.setSuccess(false);
        }
        return result;
    }
    @ApiOperation("派发时间型令牌")
    @PostMapping(value = "/dKeyAm/callback/mobileTokenDeliver")
    public DKeyAmResult<String> mobileTokenDeliver(@RequestBody MobileTokenDeliverParam param) {
        DKeyAmResult<String> result = new DKeyAmResult<>();
        result.setSuccess(true);
        try {
            dKeyAmOpenApiService.mobileTokenDeliver(param);
            result.setErrorCode(NumConstants.ZERO);
        } catch (Exception e){
            result.setSuccess(false);
        }
        return result;
    }
    @ApiOperation("完成手机令牌绑定")
    @PostMapping(value = "/dKeyAm/callback/completeMobileTokenBinding")
    public DKeyAmResult<String> completeMobileTokenBinding(@RequestBody MobileTokenCompleteTokenQrCodeParam param) {
        DKeyAmResult<String> result = new DKeyAmResult<>();
        result.setSuccess(true);
        try {
            dKeyAmOpenApiService.completeMobileTokenBinding(param);
            result.setErrorCode(NumConstants.ZERO);
        } catch (Exception e){
            result.setSuccess(false);
        }
        return result;
    }


}
