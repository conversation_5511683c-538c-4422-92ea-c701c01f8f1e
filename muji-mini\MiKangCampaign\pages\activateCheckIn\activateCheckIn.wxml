<!--MiKangCampaign/pages/activateCheckIn/activateCheckIn.wxml-->
<my-page overallModal="{{overallModal}}">
  <view class="page-container">
    <custom-header background="transparent" isShare="{{false}}" type="{{1}}" isBackHidden="{{ isBackHidden }}" color="white" />
    <scroll-view class="page-main" enhanced="{{ true }}" bounces="{{ false }}" show-scrollbar="{{ false }}" scroll-y>
      <view class="page-wrap">
        <image class="page-back" src="{{$cdn}}/MiKangCampaign/day0DakaBack.png" mode="widthFix" />
        <view class="activateCheckIn">
          <!-- 顶部空位 -->
          <view class="activeRules" />
          <view class="bottom">
            <view class="upload_file">
              <block wx:for="{{questionList}}">
                <text class="title">{{item.title}}<text class="text">{{item.subTitle?item.subTitle:''}}</text> </text>
                <!-- 图片上传 -->
                <block wx:if="{{item.questionType==5}}">
                  <view class="btn" bind:tap="clickUploader">
                    <van-uploader class="uploader" file-list="{{materialUrl}}" max-count="{{item.maxPhotoNum}}" preview-full-image="{{false}}" preview-size="172rpx" image-fit="aspectFill" use-before-read="{{true}}" bind:after-read="afterRead" bind:before-read="beforeRead" bind:delete="del" disabled="{{!isClick||!showCamera}}" deletable="{{isClick}}">
                      <view class="uploader-content">
                        <view class="plus">
                          <image src="{{$cdn}}/MiKangCampaign/Plus.png" mode="widthFix" />
                        </view>
                        <!-- <view class="text">添加照片</view> -->
                      </view>
                    </van-uploader>
                  </view>
                </block>
                <!-- 选择题 -->
                <block wx:if="{{item.questionType==2}}">
                  <view class="type">
                    <view class="type-wrap {{items.isClick?'active':''}}" wx:for="{{typeList}}" wx:for-index="indexs" wx:for-item="items" bind:tap="clickTap" data-query="{{indexs}}">
                      <block>
                        <!-- <image wx:if="{{items.isClick}}" class="type-img" src="{{$cdn}}/MiKangCampaign/SubtractYes.png" mode="widthFix" />
                        <image wx:else class="type-img" src="{{$cdn}}/MiKangCampaign/SubtractNo.png" mode="widthFix" /> -->
                      </block>

                      <view class="type-item">{{items.label}}</view>
                    </view>
                  </view>
                  <view class="tips" wx:if="{{item.questionTips}}">{{item.questionTips}}</view>
                </block>
              </block>
            </view>
          </view>
          <!-- <view bindtap="onTapPrivacyCheck" class="check">
            <view wx:if="{{!info.agree}}" class=" iconfont icon-danxuan-weigouxuan radio"></view>
            <view wx:else class="iconfont icon-danxuan-yigouxuan radio"></view>
            <view class="text">我已阅读并同意<text catchtap="go"><text class="link">《信息使用说明》</text></text>
            </view>
          </view> -->
          <view class="bottom-box">
            <basic-button disabled="{{!isComplate}}" width="{{670}}" size="large" bind:click="submit">
              开始打卡
            </basic-button>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
  <Popup isShow="{{showPopup}}" info="{{infoLottery}}" bindclose="closePopup"></Popup>
</my-page>