package com.dz.common.core.fegin.user;

import com.alibaba.fastjson.JSONObject;
import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 用户信息FeginClient
 * @Author: Handy
 * @Date: 2022/2/3 23:11
 */
@FeignClient(name = ServiceConstant.USER_SERVICE_NAME, contextId = "LoginFeginClient")
public interface LoginFeginClient {

    /**
     * 检查用户是否登录及是否有权限访问该页面
     * @param token
     * @param url
     * @return
     */
    @PostMapping(value = "/check/login")
    public Result<JSONObject> checkLogin(@RequestParam("token") String token, @RequestParam("url") String url);

}

