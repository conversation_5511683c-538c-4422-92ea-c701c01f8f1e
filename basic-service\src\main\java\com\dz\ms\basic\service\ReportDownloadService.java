package com.dz.ms.basic.service;

import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.*;

import java.util.List;

/**
 * 类注释
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/2/8 16:49
 */
public interface ReportDownloadService {
    void downloadTask(ReportDownloadParamDTO reportDownloadParamDTO);

    PageInfo<ReportDownloadTaskDTO> taskList(ReportDownloadTaskParamDTO reportDownloadTaskParamDTO);

    List<ReportDownloadTypeDTO> category();

    void delete(Long id);

    void mark(Long id);

    /**
     * 下载中心作废
     */
    void scarp();

    void downloadTemplate(ReportDownloadParamDTO downloadParamDTO);

    List<DownloadHeaderDTO> getHeader(String reportCode, Long tenantId);

    void updateHeader(Long id, String errorMessage, Integer status, String url);

    int updateDownloadCenter(DownloadCenterDTO downloadCenterDTO);
}
