package com.dz.ms.sales.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IsEnableDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.sales.dto.NpsInfoDTO;
import com.dz.ms.sales.dto.NpsQuestionDTO;
import com.dz.ms.sales.service.NpsInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "NPS问卷信息")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class NpsInfoController {

    @Resource
    private NpsInfoService npsInfoService;

    /**
     * 分页查询NPS问卷信息
     *
     * @param param
     * @return result<PageInfo < NpsInfoDTO>>
     */
    @ApiOperation("分页查询NPS问卷信息")
    @GetMapping(value = "/crm/nps/list")
    public Result<PageInfo<NpsInfoDTO>> getNpsInfoList(@ModelAttribute NpsInfoDTO param) {
        Result<PageInfo<NpsInfoDTO>> result = new Result<>();
        PageInfo<NpsInfoDTO> page = npsInfoService.getNpsInfoList(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询NPS问卷信息
     *
     * @param id
     * @return result<NpsInfoDTO>
     */
    @ApiOperation("根据ID查询NPS问卷信息")
    @GetMapping(value = {"/crm/nps/info", "/app/nps/info"})
    public Result<NpsInfoDTO> getNpsInfoById(@RequestParam("id") Long id) {
        Result<NpsInfoDTO> result = new Result<>();
        NpsInfoDTO npsInfo = npsInfoService.getNpsInfoById(id);
        result.setData(npsInfo);
        return result;
    }

    /**
     * 新增NPS问卷信息
     *
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增NPS问卷信息", type = LogType.OPERATELOG)
    @ApiOperation("新增NPS问卷信息")
    @PostMapping(value = "/crm/nps/add")
    public Result<Long> addNpsInfo(@RequestBody NpsInfoDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param, true);
        Long id = npsInfoService.saveNpsInfo(param);
        result.setData(id);
        return result;
    }

    /**
     * 更新NPS问卷信息
     *
     * @param param
     * @return result<Object>
     */
    @SysLog(value = "更新NPS问卷信息", type = LogType.OPERATELOG)
    @ApiOperation("更新NPS问卷信息")
    @PostMapping(value = "/crm/nps/update")
    public Result<Long> updateNpsInfo(@RequestBody NpsInfoDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param, false);
        npsInfoService.saveNpsInfo(param);
        result.setData(param.getId());
        return result;
    }

    /**
     * 验证保存参数
     *
     * @param param
     */
    private void validationSaveParam(NpsInfoDTO param, boolean isAdd) {
        if (isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if (!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
        if (StringUtils.isEmpty(param.getTitle())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "问卷名称不能为空");
        }
        if (null == param.getStartTime() || null == param.getEndTime()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "问卷开放时间不能为空");
        }
        if (CollectionUtils.isEmpty(param.getQuestions())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "问题列表不能为空");
        }
        for (NpsQuestionDTO npsQuestion : param.getQuestions()) {
            if (null == npsQuestion.getQuestionType()) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "问题类型不能为空");
            }
            if (StringUtils.isEmpty(npsQuestion.getTitle())) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "问题名称不能为空");
            }
            int type = npsQuestion.getQuestionType();
            if (type == 1 || type == 2) {
                if (CollectionUtils.isEmpty(npsQuestion.getOptions())) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "问题选项不能为空");
                }
            }
        }
    }

    @SysLog(value = "更新NPS问卷状态", type = LogType.OPERATELOG)
    @ApiOperation("更新NPS问卷状态 0停用 1启用")
    @PostMapping(value = "/crm/nps/update_state")
    public Result<Long> updateNpsInfoState(@RequestBody IsEnableDTO param) {
        Result<Long> result = new Result<>();
        npsInfoService.updateNpsInfoState(param);
        result.setData(param.getId());
        return result;
    }

    @ApiOperation("根据预约ID列表获取关联NPS")
    @GetMapping(value = "/app/nps/sign_in_days")
    public Result<List<NpsQuestionDTO>> getNpsBySignInDays(@RequestParam("days") Integer days, @RequestParam("scene") Integer scene) {
        Result<List<NpsQuestionDTO>> result = new Result<>();
        result.setData(npsInfoService.getNpsBySignInDays(days, scene));
        return result;
    }

}
