package com.dz.ms.product.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 货架营销活动DTO
 *
 * @author: LiinNs
 * @date: 2024/11/20 11:39
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "货架营销活动")
public class ShelfCampaignDTO extends BaseDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "活动类型 1人群限购")
    private Integer type;
    @ApiModelProperty(value = "活动时间类型 1永久 2时间段")
    private Integer onType;
    @ApiModelProperty(value = "活动开始时间")
    private Date onStartTime;
    @ApiModelProperty(value = "活动结束时间")
    private Date onEndTime;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "货架名称")
    private String shelfName;
    @ApiModelProperty(value = "启停状态 0禁用 1启用")
    private Integer state;
    @ApiModelProperty(value = "活动状态 1待开始/2进行中/3已结束")
    private Integer campaignState;

    @ApiModelProperty(value = "货架商品数量")
    private Long campaignProductNum;
    
    
    @ApiModelProperty(value = "0正常 1删除")
    private Integer isDeleted;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "创建时间")
    private Date created;

}
