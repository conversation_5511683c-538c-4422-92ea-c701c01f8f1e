## 应用配置
server:
  port: 10203

spring:
  application:
    name: product-service
  profiles:
    active: pro
  mvc:
    date-format: yyyy-MM-dd HH:mm:ss
  jackson:
    joda-date-time-format: yyyy-MM-dd HH:mm:ss
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
mybatis:
  typeAliasesPackage: com.dz.ms.product.entity
  mapperLocations: classpath:mapper/*.xml
  configuration:
    mapUnderscoreToCamelCase: true
management:
  endpoint:
    serviceregistry:
      enabled: true
  endpoints:
    web:
      base-path: /actuator # 访问根路径
      exposure:
        include: "*"
ribbon:
  ConnectTimeout: 6000 #请求连接的超时时间 默认的时间为 1 秒
  ReadTimeout: 8000 #请求处理的超时时间
logging:
  files: /opt/muji/api/logs
---
spring:
  profiles: dev
  cloud:
    nacos:
      discovery:
        server-addr: member-nacos.project-67.svc.scm-prod.local:8848
        username: nacos
        password: nacos
      config:
        server-addr: member-nacos.project-67.svc.scm-prod.local:8848
        extension-configs[0]:
          data-id: application-dev.yml
          group: PRODUCT_SERVICE
        extension-configs[1]:
          data-id: sharding-jdbc-dev.yml
          group: PRODUCT_SERVICE
        username: nacos
        password: nacos
---
spring:
  profiles: uat
  cloud:
    nacos:
      discovery:
        server-addr: nacos-uat.project-70.svc.cluster.local:8848
        group: UAT_GROUP
        username: nacos
        password: nacos
      config:
        server-addr: nacos-uat.project-70.svc.cluster.local:8848
        extension-configs[0]:
          data-id: application-uat.yml
          group: PRODUCT_SERVICE
        extension-configs[1]:
          data-id: sharding-jdbc-uat.yml
          group: PRODUCT_SERVICE
        username: nacos
        password: nacos
---
spring:
  profiles: pro
  cloud:
    nacos:
      discovery:
        server-addr: nacos-prod.project-76.svc.cluster.local:8848
        username: nacos
        password: Muji@123
      config:
        server-addr: nacos-prod.project-76.svc.cluster.local:8848
        extension-configs[0]:
          data-id: application-pro.yml
          group: PRODUCT_SERVICE
        extension-configs[1]:
          data-id: sharding-jdbc-pro.yml
          group: PRODUCT_SERVICE
        username: nacos
        password: Muji@123


