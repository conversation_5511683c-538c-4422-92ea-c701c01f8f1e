package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 完整的系统日志
 * 方法入参或出参转换json串后长度超过1000的存入此表
 * @author: Handy
 * @date:   2022/08/04 10:05
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table("完整的系统日志")
@TableName(value = "system_complete_param_log")
public class SystemCompleteParamLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = true,comment = "systemLogId")
    private Long systemLogId;
    @Columns(type = ColumnType.TEXT,length = 0,isNull = true,comment = "方法入参")
    private String params;
    @Columns(type = ColumnType.TEXT,length = 0,isNull = true,comment = "异常信息")
    private String exception;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = true,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = true,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = true,comment = "租户ID")
    private Long tenantId;

}
