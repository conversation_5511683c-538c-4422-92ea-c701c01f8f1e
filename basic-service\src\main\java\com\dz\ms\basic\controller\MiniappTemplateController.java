package com.dz.ms.basic.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.user.CrowdDTO;
import com.dz.common.core.dto.user.GradeListDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.user.CrowdFeignClient;
import com.dz.common.core.fegin.user.GradeInfoFeginClient;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.dto.MiniappTemplateDTO;
import com.dz.ms.basic.dto.MiniappTemplateGroupDTO;
import com.dz.ms.basic.dto.MiniappTemplateListDTO;
import com.dz.ms.basic.dto.MiniappTemplateUpdatePathDTO;
import com.dz.ms.basic.entity.MiniappTemplate;
import com.dz.ms.basic.service.MiniappTemplateService;
import com.dz.ms.basic.utils.SqlSortUtils;
import com.dz.ms.basic.utils.SqlUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

@Api(tags="小程序页面模板")
@RestController
public class MiniappTemplateController  {

    @Resource
    private MiniappTemplateService miniappTemplateService;

    @Resource
    private CrowdFeignClient crowdFeignClient;

    @Resource
    private GradeInfoFeginClient gradeInfoFeginClient;


    /**
     * 分页查询小程序页面模板
     * @param param
     * @return result<PageInfo<MiniappTemplateDTO>>
     */
    @ApiOperation("分页查询小程序页面模板")
    @GetMapping(value = "/crm/miniapp_template/list")
    public Result<PageInfo<MiniappTemplateListDTO>> getMiniappTemplateList(@ModelAttribute MiniappTemplateListDTO param) {
        Result<PageInfo<MiniappTemplateListDTO>> result = new Result<>();
        MiniappTemplate miniappTemplate = BeanCopierUtils.convertObjectTrim(param, MiniappTemplate.class);
        String name = miniappTemplate.getTemplateName();
        boolean hasName = false;

        if (StringUtils.isNotBlank(name)) {
            miniappTemplate.setTemplateName(null);
            hasName = true;
        }

        // 使用 QueryWrapper 而不是 LambdaQueryWrapper
        QueryWrapper<MiniappTemplate> wrapper = new QueryWrapper<>(miniappTemplate);
        String customSort ;
        wrapper.eq("template_type", param.getTemplateType());
        // 构造排序规则，pageType=0 排到最后，其他数据正常排序
        if (param.getTemplateType() != null &&  param.getTemplateType() == 5) {
            customSort="CASE WHEN page_type = 0 THEN 2147483647 ELSE page_type END DESC, modified DESC";
//            wrapper.orderBy(true, param.getTemplateType() != null && param.getTemplateType() == 5, "CASE WHEN page_type = 0 THEN 2147483647 ELSE page_type END DESC, modified DESC");
        } else {
            customSort="CASE WHEN page_type = 0 THEN 2147483647 ELSE page_type END ASC, modified DESC";
//            wrapper.orderBy(true, param.getTemplateType() != null && param.getTemplateType() != 5, "CASE WHEN page_type = 0 THEN 2147483647 ELSE page_type END ASC, modified DESC");
        }
        wrapper.last("ORDER BY " + customSort);

        if (hasName) {
            wrapper.like("template_name", name);
        }

        // 分页查询
        IPage<MiniappTemplate> page = miniappTemplateService.page(new Page<>(param.getPageNum(), param.getPageSize()), wrapper);
        if (param.getTemplateType() == 2){
            //查询系统页面保存记录
            List<MiniappTemplate> list = page.getRecords();
            if (list.size() > 0){
                list = miniappTemplateService.selectHistory(list);
            }
        }
        // 转换数据
        result.setData(miniappTemplateService.getMiniappTemplatePath(
                new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(),
                        BeanCopierUtils.convertList(page.getRecords(), MiniappTemplateListDTO.class))
        ));

        return result;
    }

/*    public Result<PageInfo<MiniappTemplateListDTO>> getMiniappTemplateList(@ModelAttribute MiniappTemplateListDTO param) {
        Result<PageInfo<MiniappTemplateListDTO>> result = new Result<>();
        MiniappTemplate miniappTemplate = BeanCopierUtils.convertObjectTrim(param, MiniappTemplate.class);
        String name = miniappTemplate.getTemplateName();
        boolean hasName = false;
        if (StringUtils.isNotBlank(name)) {
            miniappTemplate.setTemplateName(null);
            hasName = true;
        }
        LambdaQueryWrapper<MiniappTemplate> wrapper = new LambdaQueryWrapper<>(miniappTemplate);
        if (param.getTemplateType() != null && (param.getTemplateType() == 1 || param.getTemplateType() == 5)) {
            wrapper.orderByDesc(MiniappTemplate::getPageType)
                    .orderByDesc(MiniappTemplate::getModified);
        } else {
            wrapper.orderByDesc(MiniappTemplate::getModified);
        }
        if (hasName) {
            wrapper.like(MiniappTemplate::getTemplateName, name);
        }
        IPage<MiniappTemplate> page = miniappTemplateService.page(new Page<>(param.getPageNum(), param.getPageSize()), wrapper);

        result.setData(miniappTemplateService.getMiniappTemplatePath(new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), BeanCopierUtils.convertList(page.getRecords(), MiniappTemplateListDTO.class))));
        return result;
    }*/

    /**
     * 获取会员等级列表
     */
    @ApiOperation("获取会员等级列表")
    @GetMapping(value = "/crm/miniapp_template/getGradeList")
    public Result<List<GradeListDTO>> getGradeList() {
        Result<List<GradeListDTO>> result = new Result<>();
        result.setData(gradeInfoFeginClient.getCodeToNameGradeMap(1L).getData());
        return result;
    }

    /**
     * 获取人群包列表
     */
    @ApiOperation("获取人群包列表")
    @GetMapping(value = "/crm/miniapp_template/getCrowdList")
    public Result<List<CrowdDTO>> getgetCrowdList() {
        Result<List<CrowdDTO>> result = new Result<>();
        result.setData(crowdFeignClient.getList(new CrowdDTO()).getData());
        return result;
    }

    /**
     * 根据ID查询小程序页面模板
     * @param id
     * @return result<MiniappTemplateDTO>
     */
    @ApiOperation("根据ID查询小程序页面模板")
	@GetMapping(value = "/crm/miniapp_template/info")
    public Result<MiniappTemplateDTO> getMiniappTemplateById(@RequestParam("id") Long id) {
        Result<MiniappTemplateDTO> result = new Result<>();
        MiniappTemplate miniappTemplate = miniappTemplateService.getCrmMiniappTemplateById(id);
        result.setData(BeanCopierUtils.convertObject(miniappTemplate,MiniappTemplateDTO.class));
        return result;
    }

    /**
     * 新增小程序页面模板
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增小程序页面模板",type = LogType.OPERATELOG)
    @ApiOperation("新增小程序页面模板")
    @PostMapping(value = "/crm/miniapp_template/add")
    public Result<Long> addMiniappTemplate(@RequestBody MiniappTemplateDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,true);
        Long id = miniappTemplateService.saveMiniappTemplate(param);
        result.setData(id);
        return result;
    }

    /**
     * 更新小程序页面模板
     * @param param
     * @return result<Object>
     */
    @SysLog(value = "更新小程序页面模板",type = LogType.OPERATELOG)
    @ApiOperation("更新小程序页面模板")
    @PostMapping(value = "/crm/miniapp_template/update")
    public Result<Long> updateMiniappTemplate(@RequestBody MiniappTemplateDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,false);
        miniappTemplateService.saveMiniappTemplate(param);
        result.setData(param.getId());
        return result;
    }
    /**
     * 更新小程序页面模板
     * @param param
     * @return result<Object>
     */
    @SysLog(value = "变更小程序页面路径",type = LogType.OPERATELOG)
    @ApiOperation("变更小程序页面路径")
    @PostMapping(value = "/crm/miniapp_template/updatePath")
    public Result<Long> updateMiniappTemplatePath(@RequestBody MiniappTemplateUpdatePathDTO param) {
        Result<Long> result = new Result<>();
        miniappTemplateService.updateMiniappTemplatePath(param);
        result.setData(param.getId());
        return result;
    }

    @SysLog(value = "发布页面",type = LogType.OPERATELOG)
    @ApiOperation("发布页面")
    @PostMapping(value = "/crm/miniapp_template/publish")
    public Result<Long> updateMiniappTemplatePublish(@RequestBody MiniappTemplateUpdatePathDTO param) {
        Result<Long> result = new Result<>();
        miniappTemplateService.updateMiniappTemplatePublish(param);
        result.setData(param.getId());
        return result;
    }
    /**
     * 更新小程序页面分组
     * @param param
     * @return result<Object>
     */
    @SysLog(value = "更新小程序页面模板",type = LogType.OPERATELOG)
    @ApiOperation("更新小程序页面模板")
    @PostMapping(value = "/crm/miniapp_template/group")
    public Result updateMiniappTemplateGroup(@RequestBody MiniappTemplateGroupDTO param) {
        Result<Long> result = new Result<>();;
        miniappTemplateService.updsateMiniappTemplateGroup(param);
        return result;
    }



    /**
     * 验证保存参数
     * @param param
     */
    private void validationSaveParam(MiniappTemplateDTO param,boolean isAdd) {
        if(isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if(!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
        if(null == param.getTemplateType()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"模板类型不能为空");
        }
        if(StringUtils.isBlank(param.getTemplateName())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"模板名称不能为空");
        }
        if(null != param.getPublish() && param.getPublish().equals(1)) {
            if(StringUtils.isBlank(param.getContent())) {
                throw new BusinessException(ErrorCode.BAD_REQUEST,"模板内容不能为空");
            }
        }
        else {
            if(StringUtils.isBlank(param.getPageJson())) {
                throw new BusinessException(ErrorCode.BAD_REQUEST,"预览内容不能为空");
            }
        }
    }
	
	/**
     * 根据ID删除小程序页面模板
     * @param param
     * @return result<Boolean>
     */
    @SysLog(value = "删除页面模板",type = LogType.OPERATELOG)
    @ApiOperation("根据ID删除小程序页面模板")
	@PostMapping(value = "/crm/miniapp_template/delete")
    public Result<Boolean> deleteMiniappTemplateById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        MiniappTemplate miniappTemplate = miniappTemplateService.getById(param.getId());
        if(null == miniappTemplate) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"模板ID不存在");
        }
//        if(null != miniappTemplate.getPublish() && miniappTemplate.getPublish().equals(1)) {
//            throw new BusinessException(ErrorCode.BAD_REQUEST,"模板正在使用中不能删除");
//        }
        miniappTemplateService.removeById(param.getId());
        result.setData(true);
        return result;
    }

    @ApiOperation("根据模板类型及页面类型查询小程序页面模板")
    @GetMapping(value = "/app/miniapp_template/info_by_type")
    public Result<MiniappTemplateDTO> getMiniappTemplateByType(@RequestParam("templateType") Integer templateType,@RequestParam("pageType") Integer pageType) {
        Result<MiniappTemplateDTO> result = new Result<>();
        MiniappTemplateDTO miniappTemplate = miniappTemplateService.getMiniappTemplateByType(templateType,pageType, SecurityContext.getUser().getTenantId());
        result.setData(miniappTemplate);
        return result;
    }

    @ApiOperation("根据模板ID查询小程序页面模板")
    @GetMapping(value = "/app/miniapp_template/info")
    public Result<MiniappTemplateDTO> getMiniappTemplateInfoById(@RequestParam("id") Long id) {
        Result<MiniappTemplateDTO> result = new Result<>();
        MiniappTemplateDTO miniappTemplate = miniappTemplateService.getMiniappTemplateById(id, SecurityContext.getUser().getTenantId());
        result.setData(miniappTemplate);
        return result;
    }

    @ApiOperation("根据模板ID预览小程序页面模板")
    @GetMapping(value = "/app/miniapp_template/preview")
    public Result<MiniappTemplateDTO> previewMiniappTemplateById(@RequestParam("id") Long id) {
        Result<MiniappTemplateDTO> result = new Result<>();
        MiniappTemplate miniappTemplate = miniappTemplateService.getById(id);
        miniappTemplate.setContent(miniappTemplate.getContent());
        miniappTemplate.setPageJson(miniappTemplate.getPageJson());
        MiniappTemplateDTO miniappTemplateDTO = BeanCopierUtils.convertObject(miniappTemplate,MiniappTemplateDTO.class);
        result.setData(miniappTemplateDTO);
        return result;
    }

    @GetMapping(value = {"/app/miniapp/qrcode","/crm/miniapp/qrcode"})
    @ApiOperation("小程序二维码")
    public byte[] getMiniappQrcode(@RequestParam(value = "page", required = false)String page,@RequestParam(value = "scene", required = false)String scene,
                                   @RequestParam(value = "width",required = false)Integer width, @RequestParam(value = "trial",required = false)Integer trial,
                                   @RequestParam(value = "isHyaline",required = false)Integer isHyaline) {
        return miniappTemplateService.getMiniappQrcode(page,scene,width,trial,isHyaline, null);
    }

    @GetMapping(value = "/app/miniapp/qrcode_base64")
    @ApiOperation("小程序二维码base64")
    public Result<String> getMiniappQrcodeBase64(@RequestParam(value = "page", required = false)String page,@RequestParam(value = "scene", required = false)String scene,
                                                 @RequestParam(value = "width",required = false)Integer width, @RequestParam(value = "trial",required = false)Integer trial,
                                                 @RequestParam(value = "isHyaline",required = false)Integer isHyaline,
                                                 @RequestParam(value = "linColor",required = false)String linColor) {
        byte[] bytes = miniappTemplateService.getMiniappQrcode(page,scene,width,trial,isHyaline, linColor);
        Base64.Encoder encoder = Base64.getEncoder();
        Result<String> result = new Result<>();
        result.setData(encoder.encodeToString(bytes));
        return result;
    }

}
