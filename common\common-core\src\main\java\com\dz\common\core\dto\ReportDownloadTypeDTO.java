package com.dz.common.core.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 下载中心业务模块
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/2/14 17:44
 */
@ApiModel(value = "下载中心业务模块")
@Getter
@Setter
public class ReportDownloadTypeDTO {

    @ApiModelProperty(value = "下载类型")
    private Integer type;

    @ApiModelProperty(value = "业务模块")
    private String bizModuleName;

}
