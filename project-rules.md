# 项目技术架构与编码规范

## 1. 技术架构

### 1.1 后端技术栈

#### 1.1.1 核心框架
- Spring Boot 2.7.x
- Spring Cloud 2021.0.8
- Spring Cloud Alibaba 2021.0.5.0
- MyBatis 3.5.13
- MyBatis-Plus 4.2.0

#### 1.1.2 微服务组件
- Nacos：服务注册与配置中心
- Sentinel：服务限流与熔断
- OpenFeign：服务间调用
- Gateway：API网关
- LoadBalancer：负载均衡

#### 1.1.3 数据存储
- MySQL：主数据库
- Redis：缓存
- PostgreSQL：可选数据库
- H2：测试数据库

#### 1.1.4 工具库
- Lombok：简化代码
- FastJSON：JSON处理
- Jackson：JSON处理
- Hutool：工具集
- Apache Commons：通用工具
- Swagger/OpenAPI：API文档

### 1.2 前端技术栈

#### 1.2.1 核心框架
- Vue.js
- React
- TypeScript

#### 1.2.2 UI组件库
- Element UI
- Ant Design
- Material-UI

#### 1.2.3 状态管理
- Vuex
- Redux
- MobX

#### 1.2.4 工具库
- Axios：HTTP客户端
- Lodash：工具库
- Moment.js：日期处理
- ECharts：图表库

## 2. 项目结构规范

### 2.1 后端项目结构
```
project-root/
├── common/                 # 公共模块
│   ├── common-base/       # 基础模块
│   └── common-core/       # 核心模块
├── gateway/               # API网关
├── service-xxx/          # 业务服务模块
│   ├── src/main/java/com/dz/ms/xxx/
│   │   ├── controller/   # 控制器
│   │   ├── service/      # 服务接口
│   │   │   └── impl/    # 服务实现
│   │   ├── mapper/      # MyBatis映射
│   │   ├── entity/      # 实体类
│   │   ├── dto/         # 数据传输对象
│   │   ├── vo/          # 视图对象
│   │   ├── feign/       # Feign客户端
│   │   └── config/      # 配置类
│   └── src/main/resources/
│       ├── mapper/      # MyBatis XML
│       └── application.yml
└── pom.xml
```

### 2.2 前端项目结构
```
project-root/
├── src/
│   ├── api/             # API接口
│   ├── assets/          # 静态资源
│   ├── components/      # 公共组件
│   ├── layouts/         # 布局组件
│   ├── router/          # 路由配置
│   ├── store/           # 状态管理
│   ├── styles/          # 样式文件
│   ├── utils/           # 工具函数
│   └── views/           # 页面组件
├── public/              # 公共资源
└── package.json
```

## 3. 编码规范

### 3.1 命名规范

#### 3.1.1 后端命名规范
- 类名：大驼峰命名法（PascalCase）
- 方法名：小驼峰命名法（camelCase）
- 变量名：小驼峰命名法（camelCase）
- 常量名：全大写下划线分隔（UPPER_SNAKE_CASE）
- 包名：全小写点分隔（lowercase.dotted）
- 数据库表名：小写下划线分隔（lower_snake_case）
- 数据库字段名：小写下划线分隔（lower_snake_case）

#### 3.1.2 前端命名规范
- 组件名：大驼峰命名法（PascalCase）
- 文件名：小驼峰命名法（camelCase）
- 变量名：小驼峰命名法（camelCase）
- 常量名：全大写下划线分隔（UPPER_SNAKE_CASE）
- CSS类名：小写连字符（kebab-case）
- 组件文件名：大驼峰命名法（PascalCase）

### 3.2 代码规范

#### 3.2.1 后端代码规范
1. 控制器层（Controller）
   - 使用`@RestController`注解
   - 使用`@RequestMapping`指定基础路径
   - 使用`@Api`和`@ApiOperation`添加Swagger文档
   - 统一返回`Result<T>`对象
   - 使用`@Valid`进行参数校验

2. 服务层（Service）
   - 接口和实现类分离
   - 使用`@Service`注解
   - 方法命名要清晰表达业务含义
   - 使用事务注解`@Transactional`

3. 数据访问层（Mapper）
   - 使用`@Mapper`注解
   - 继承`BaseMapper<T>`
   - XML文件放在resources/mapper目录下

4. 实体类（Entity）
   - 使用`@Data`注解
   - 实现`Serializable`接口
   - 使用`@TableName`指定表名
   - 使用`@TableField`指定字段映射

5. DTO/VO
   - 使用`@Data`注解
   - 实现`Serializable`接口
   - 字段添加注释

#### 3.2.2 前端代码规范
1. 组件规范
   - 使用函数式组件
   - 使用TypeScript类型定义
   - 使用Props接口定义属性
   - 使用Hooks管理状态和副作用

2. 样式规范
   - 使用CSS Modules或Styled Components
   - 遵循BEM命名规范
   - 使用变量管理主题

3. 状态管理
   - 使用TypeScript定义状态类型
   - 使用Action Creators创建动作
   - 使用Selectors选择数据

4. API调用
   - 使用Axios实例
   - 统一错误处理
   - 使用TypeScript定义接口类型

### 3.3 注释规范

#### 3.3.1 后端注释规范
1. 类注释
```java
/**
 * 类描述
 * <AUTHOR>
 * @date 创建时间
 * @version 版本号
 */
```

2. 方法注释
```java
/**
 * 方法描述
 * @param param1 参数1说明
 * @param param2 参数2说明
 * @return 返回值说明
 * @throws Exception 异常说明
 */
```

3. 字段注释
```java
/** 字段说明 */
private String field;
```

#### 3.3.2 前端注释规范
1. 组件注释
```typescript
/**
 * 组件描述
 * @component
 * @example
 * <Component prop1="value1" />
 */
```

2. 函数注释
```typescript
/**
 * 函数描述
 * @param {string} param1 - 参数1说明
 * @param {number} param2 - 参数2说明
 * @returns {boolean} 返回值说明
 */
```

3. 接口注释
```typescript
/**
 * 接口描述
 * @interface
 */
interface InterfaceName {
  /** 属性说明 */
  property: type;
}
```

## 4. 安全规范

### 4.1 后端安全规范
1. 密码加密：使用BCrypt加密
2. 接口认证：使用JWT token
3. 参数校验：使用`@Valid`注解
4. SQL注入：使用MyBatis参数绑定
5. XSS防护：使用HTML转义
6. CSRF防护：使用token验证

### 4.2 前端安全规范
1. 敏感信息：不存储在localStorage
2. 密码传输：使用HTTPS
3. 输入验证：前端校验
4. XSS防护：使用转义函数
5. CSRF防护：携带token

## 5. 测试规范

### 5.1 后端测试规范
1. 单元测试：使用JUnit5
2. 集成测试：使用Spring Test
3. 测试覆盖率：不低于80%
4. 测试命名：方法名_条件_预期结果

### 5.2 前端测试规范
1. 单元测试：使用Jest
2. 组件测试：使用React Testing Library
3. E2E测试：使用Cypress
4. 测试覆盖率：不低于80%

## 6. 部署规范

### 6.1 后端部署规范
1. 使用Docker容器化
2. 使用Kubernetes编排
3. 配置中心：使用Nacos
4. 日志收集：使用ELK
5. 监控：使用Prometheus + Grafana

### 6.2 前端部署规范
1. 使用Nginx部署
2. 启用Gzip压缩
3. 配置缓存策略
4. 使用CDN加速
5. 配置HTTPS

## 7. 版本控制规范

### 7.1 Git规范
1. 分支管理
   - master：主分支
   - develop：开发分支
   - feature/*：功能分支
   - hotfix/*：修复分支

2. 提交规范
   - feat：新功能
   - fix：修复bug
   - docs：文档更新
   - style：代码格式
   - refactor：重构
   - test：测试
   - chore：构建过程或辅助工具的变动

3. 版本号规范
   - 主版本号：不兼容的API修改
   - 次版本号：向下兼容的功能性新增
   - 修订号：向下兼容的问题修正

### 7.2 发布规范
1. 版本号管理
2. 更新日志维护
3. 回滚机制
4. 灰度发布
5. 监控告警 