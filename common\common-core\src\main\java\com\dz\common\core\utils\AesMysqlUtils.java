package com.dz.common.core.utils;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;

@Slf4j
public class AesMysqlUtils {

    private static final String CIPHER_ALGORITHM = "QUVTL0VDQi9QS0NTNVBhZGRpbmc=";
    private static final String TRANSFORMATION = new String(Base64.decodeBase64(CIPHER_ALGORITHM));

    private static final String key = "4ba63e25y1e6h9kx";

    private static SecretKeySpec generateMySQLAESKey(final String key, final String encoding) {
        try {
            final byte[] finalKey = new byte[16];
            int i = 0;
            for (byte b : key.getBytes(encoding)) {
                finalKey[i++ % 16] ^= b;
            }
            return new SecretKeySpec(finalKey, "AES");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * @param data   需要进行加密的原文
     * @return String 数据密文，加密后的数据，进行了Base64的编码
     * @desc: AES对称-加密操作
     */
    public static String encrypt(String data) {

        if (StringUtils.isBlank(data)) {
            return data;
        }

        try {
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            SecureRandom secureRandom = new SecureRandom();
            cipher.init(Cipher.ENCRYPT_MODE, generateMySQLAESKey(key, "UTF-8"), secureRandom);
            byte [] result = cipher.doFinal(data.getBytes("UTF-8"));
            return Base64.encodeBase64String(result);
        } catch (Exception e) {
            log.error("加密失败: {}", e.getMessage(), e);
            throw new RuntimeException("加密失败: " + e.getMessage());
        }
    }



    /**
     * @param data
     * @return String 返回解密后的原文
     * @desc: AES对称-解密操作
     */
    public static String decrypt(String data) {

        if (StringUtils.isBlank(data)) {
            return data;
        }

        // 转换密钥
        try {
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            SecureRandom secureRandom = new SecureRandom();
            cipher.init(Cipher.DECRYPT_MODE, generateMySQLAESKey(key, "UTF-8"), secureRandom);
            byte[] result = cipher.doFinal(Base64.decodeBase64(data));
            return new String(result, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("解密失败: {}", e.getMessage(), e);
            throw new RuntimeException("解密失败: " + e.getMessage());
        }
    }

    public static void main(String[] args) {
    }

}
