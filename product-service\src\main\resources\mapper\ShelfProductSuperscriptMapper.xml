<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.product.mapper.ShelfProductSuperscriptMapper">

	<select id="selectNoPageList" resultType="com.dz.ms.product.dto.ShelfProductSuperscriptDTO">
		SELECT distinct 
			sps.id,sps.shelf_id,sps.shelf_product_id,sps.superscript_id,s.name superscriptName
		FROM shelf_product_superscript sps
		LEFT JOIN superscript s ON sps.superscript_id = s.id
		<where>
		    s.is_deleted = 0                           
			<if test="shelfProductIdList != null and shelfProductIdList.size > 0">
				and sps.shelf_product_id in
				<foreach collection="shelfProductIdList" index="index" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
			<if test="shelfId != null">
				and sps.shelf_id = #{shelfId}
			</if>
		</where>
		ORDER BY sps.id Asc
	</select>


</mapper>
