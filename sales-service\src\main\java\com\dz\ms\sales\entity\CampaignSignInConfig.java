package com.dz.ms.sales.entity;


import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/18
 */
@Getter
@Setter
@NoArgsConstructor
@Table(value = "签到配置表")
@TableName(value = "campaign_sign_in_config")
public class CampaignSignInConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = true, comment = "活动标识", isIndex = true)
    private String campaignCode;

    @Columns(type = ColumnType.INT, length = 10, isNull = false, defaultValue = "1", comment = "签到活动天数")
    private Integer signInDays;

    @Columns(type = ColumnType.INT, length = 10, isNull = false, defaultValue = "1", comment = "签到次数")
    private Integer signInTimes;

    @Columns(type = ColumnType.INT, length = 10, isNull = false, defaultValue = "1", comment = "允许补签的最大次数")
    private Integer repairSignInTimes;

    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "1", comment = "补签标识 0不允许 1允许")
    private Integer patchFlag;

    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "完成打卡后是否赠送优惠券 0不赠送 1赠送")
    private Integer isSendCoupon;

    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = true, comment = "优惠券编码")
    private String couponCode;

    @Columns(type = ColumnType.INT, length = 10, isNull = false, defaultValue = "0", comment = "完成打卡后是否赠送抽奖机会数量")
    private Integer signInSendLotteryNum;



    @Columns(type = ColumnType.BIGINT, length = 11, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT, length = 11, isNull = true, comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
}
