package com.dz.ms.sales.dto;


import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/17
 */
@Data
public class CrmSignInUserParamDTO extends BaseDTO {

//    @ApiModelProperty(value = "用户手机号")
//    private String mobile;

    @ApiModelProperty(value = "打卡天数")
    private Integer days;

//    @ApiModelProperty(value = "用户类型 0全部用户 1购买指定商品的用户 2其他导入的用户 3报名中奖的用户")
//    private Integer targetType;

//    @ApiModelProperty(value = "打卡状态 0未开始 1进行中 2已完成 3未完成")
//    private Integer signInSate;


}
