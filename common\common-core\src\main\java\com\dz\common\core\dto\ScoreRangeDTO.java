package com.dz.common.core.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;


/**
 * 数值区间
 *
 * @author: fei
 * @date: 2024/12/12 16:25
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@ApiModel(value = "数值区间")
public class ScoreRangeDTO {

    @ApiModelProperty(value = "开始值")
    private Integer startNum;
    @ApiModelProperty(value = "结束值")
    private Integer endNum;

}
