package com.dz.common.core.dto.export;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CampaignExportDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "活动名称")
    private String name;
    @ApiModelProperty(value = "活动类型 人群限购_1")
    private Integer type;
    @ApiModelProperty(value = "活动时间")
    private String campaignTimeStr;
    @ApiModelProperty(value = "关联货架")
    private String shelfName;
    @ApiModelProperty(value = "活动状态 待开始_1,进行中_2,已结束_3")
    private Integer campaignState;
    @ApiModelProperty(value = "启停状态 禁用_0,启用_1")
    private Integer state;
}
