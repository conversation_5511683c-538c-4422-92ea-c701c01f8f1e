<template>
  <layout>
    <template v-slot:header>
      <!--:disabled="!$hasPermission('mall:permission:searchGiftRack')"-->
      <searchForm :formParams="searchFields" @cancel="whenClickReset" @ok="whenClickSearch" :disabled="!$hasPermission('giftRack:search')">
        <a-form-item label="" name="name">
          <a-input placeholder="货架名称" allow-clear v-model:value="searchFields.name" allowClear @keyup.enter="whenClickSearch"></a-input>
        </a-form-item>
        <BaseDateTimeRange label="货架上架时间" name="dateTimeRange" v-model="searchFields.dateTimeRange" />
        <ShelfStatusSelect label="货架状态" name="shelfState" v-model="searchFields.shelfState" />
        <a-form-item label="" name="state">
          <a-select ref="select" v-model:value="searchFields.state" allowClear :options="SHELF_SWITCH_STATUS_ARR" style="202px;" :fieldNames="{ label: 'label', value: 'value' }" optionFilterProp="label" showSearch placeholder="启停状态"></a-select>
        </a-form-item>
      </searchForm>
    </template>
    <template v-slot:topRight>
      <a-space>
        <!--:disabled="!$hasPermission('mall:permission:addGiftRack')"-->
        <a-button type="primary" @click="handleAdd" :disabled="!$hasPermission('giftRack:add')">新建货架</a-button>
        <a-button type="primary" @click="handleExport" :disabled="!$hasPermission('giftRack:derive')">导出</a-button>
        <!-- <a-button type="primary" :disabled="!$hasPermission('giftRack:mark')" @click="ShelfProductCornerManagementDialogHandler">角标管理</a-button> -->
      </a-space>
    </template>
    <template v-slot="{ height }">
      <a-table :indentSize="20" row-key="id" :scroll="{ scrollToFirstRowOnChange: true, y: height - 88, x: '100%' }" :dataSource="dataSource" :columns="thisFields.tableHeader" :pagination="pagination" :loading="loading" @change="whenPaginationChange">
        <template #bodyCell="{text, record, index, column}">
          <template v-if="column.dataIndex === 'index'">{{ (current - 1) * pageSize + 1 + index }}</template>
          <template v-if="column.dataIndex === 'onStartTime'">
            <div>{{ record.onStartTime }}</div>
            <div>{{ record.onEndTime }}</div>
          </template>
          <template v-if="column.dataIndex === 'isJoinShelfActivity'">
            <template v-if="record.shelfActivityList.length">
              <ShelfLookIsJoinMarketingActivitiesPopover :record="record">
                <template v-for="(item,index) in record.shelfActivityList">
                  <a-button type="link">{{ item.name }}</a-button>
                  <a-divider type="vertical" v-if="index!==record.shelfActivityList.length-1" />
                </template>
              </ShelfLookIsJoinMarketingActivitiesPopover>
            </template>
            <template v-else>--</template>
          </template>
          <template v-if="column.dataIndex === 'stateDesc'">
            <a-tag color="green" v-if="record.state === 1">启用中</a-tag>
            <a-tag color="red" v-else>停用中</a-tag>
          </template>
          <template v-if="column.dataIndex === 'action'">
            <a-button type="link" @click="handleCopy(record)" :disabled="!$hasPermission('giftRack:copy')">复制</a-button>
            <a-divider type="vertical" />
            <!--:disabled="!$hasPermission('mall:permission:editGiftRack')"-->
            <a-button type="link" @click="handleEdit(record)" :disabled="!$hasPermission('giftRack:edit')">编辑</a-button>
            <a-divider type="vertical" />
            <a-button type="link" @click="handleStopUsing(record)" :disabled="!$hasPermission('giftRack:status')" v-if="record.state===1">停用</a-button>
            <a-button type="link" @click="handleStopUsing(record)" :disabled="!$hasPermission('giftRack:status')" v-else>启用</a-button>
            <a-divider type="vertical" />
            <a-popconfirm title="是否确定删除该货架？" @confirm="handleDelete(record)" :disabled="!$hasPermission('giftRack:del')||record.state===1||record.shelfState===2">
              <!--:disabled="!$hasPermission('mall:permission:delGiftRack')"-->
              <a-button type="link" :disabled="!$hasPermission('giftRack:del')||record.state===1||record.shelfState===2">删除</a-button>
            </a-popconfirm>
            <a-divider type="vertical" />
            <a-button type="link" @click="handleQuickFiltering(record)" :disabled="!$hasPermission('giftRack:filter')">快捷筛选</a-button>
            <!--<a-divider type="vertical" />
            <a-button type="link" @click="thisMethods.handleProductManagement(record)">商品管理</a-button>-->
          </template>
        </template>
      </a-table>
    </template>
  </layout>
  <currentForm v-model:visible="thisFields.visible" @ok="whenEditSuccess" :id="thisFields.id" :type="thisFields.type" />
  <ShelfConfigurationFirstTagDialog v-model="thisFields.ShelfConfigurationFirstTagDialogOpen" :record="thisFields.record" />
  <ShelfProductCornerManagementDialog v-model="thisFields.ShelfProductCornerManagementDialogOpen" />
  <ShelfProductManagementDialog :shelfId="thisFields.record.id" v-model="thisFields.ShelfProductManagementDialogOpen" @ok="thisMethods.ShelfProductManagementDialogHandlerOk" :beSelectedProductIdArrayObjectList="thisFields.record.saveShelfProductList" />
</template>
<script setup>
import currentForm from './components/currentForm.vue'
import ShelfProductManagementDialog from '@/views/mall/shelfManagement/giftRack/components/ShelfProductManagementDialog.vue'
import ShelfProductCornerManagementDialog from '@/views/mall/shelfManagement/giftRack/components/ShelfProductCornerManagementDialog.vue'
import ShelfStatusSelect from '@/views/mall/shelfManagement/giftRack/components/ShelfStatusSelect.vue'
import ShelfLookIsJoinMarketingActivitiesPopover from '@/views/mall/shelfManagement/giftRack/components/ShelfLookIsJoinMarketingActivitiesPopover.vue'
import ShelfConfigurationFirstTagDialog from '@/views/mall/shelfManagement/giftRack/components/ShelfConfigurationFirstTagDialog.vue'
import { message } from 'ant-design-vue'
import { usePagination } from 'vue-request'
import { downloadExcel } from '@/utils/tools.js'
import { apiGiftRack, downloadTask } from '@/http/index.js'
import { cloneDeep } from 'lodash'
import { SHELF_SWITCH_STATUS_ARR, } from '@/utils/constants.js'
const pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ['bottomLeft']
  }
})
const total = ref(0)
const getDefaultSearchFields = () => ({
  name: '',
  dateTimeRange: [],
  shelfState: undefined,
  state: null
})
const searchFields = reactive(getDefaultSearchFields())
const thisFields = reactive({
  ShelfProductManagementDialogOpen: false,
  id: '',
  type: 0, // 0-新增 1-编辑 2-查看
  visible: false,
  tableHeader: [
    { title: '序号', dataIndex: 'index', align: 'center', width: 80 },
    { title: '货架名称', dataIndex: 'name', align: 'center', ellipsis: true, width: 180 },
    { title: '货架上架时间', dataIndex: 'onStartTime', align: 'center', ellipsis: true, width: 160 },
    { title: '上架状态', dataIndex: 'shelfStateDesc', align: 'center', ellipsis: true, width: 100 },
    { title: '优先级', dataIndex: 'priority', align: 'center', ellipsis: true, width: 100 },
    { title: '是否参与营销活动', dataIndex: 'isJoinShelfActivity', align: 'center', ellipsis: true, width: 160 },
    { title: '上架商品数量', dataIndex: 'productSum', align: 'center', ellipsis: true, width: 100 },
    { title: '兑礼人数', dataIndex: 'exchangePeople', align: 'center', ellipsis: true, width: 100 },
    { title: '总订单数量', dataIndex: 'exchangeOrder', align: 'center', ellipsis: true, width: 100 },
    { title: '总兑礼件数', dataIndex: 'exchangeNum', align: 'center', ellipsis: true, width: 100 },
    { title: '兑礼总积分', dataIndex: 'exchangePoint', align: 'center', ellipsis: true, width: 100 },
    { title: '人均兑礼件数', dataIndex: 'exchangeAvgNum', align: 'center', ellipsis: true, width: 100 },
    { title: '货架启停状态', dataIndex: 'stateDesc', align: 'center', ellipsis: true, width: 100 },
    { title: '操作', dataIndex: 'action', align: 'center', width: 260, fixed: 'right' }
  ],
  record: {},
  ShelfConfigurationFirstTagDialogOpen: false,
  ShelfProductCornerManagementDialogOpen: false
})
const thisMethods = {
  async handleProductManagement(record) {
    const res2 = await apiGiftRack.getShelfProductAllList({ shelfId: record.id })
    record.saveShelfProductList = res2.data
    thisFields.record = record
    thisFields.ShelfProductManagementDialogOpen = true
  },
  async ShelfProductManagementDialogHandlerOk(beSelectedProductIdArrayObjectList) {
    await apiGiftRack.updateShelfProductList({
      shelfId: thisFields.record.id,
      saveShelfProductList: beSelectedProductIdArrayObjectList
    })
    refresh()
  }
}
const { data: dataSource, run, loading, pageSize, current, refresh } = usePagination((param) => {
  searchFields.onStartTime = searchFields.dateTimeRange[0] || ''
  searchFields.onEndTime = searchFields.dateTimeRange[1] || ''
  return apiGiftRack.getPageList({ ...param, ...searchFields })
}, {
  manual: false, // 修改为false 让其自动执行
  pagination: {
    currentKey: 'pageNum', // 通过该值指定接口 当前页数 参数的属性值
    pageSizeKey: 'pageSize', // 通过该值指定接口 每页获取条数 参数的属性值
    totalKey: 'data.data.count' // 指定 data 中 total 属性的路径
  },
  formatResult: res => { // 返回数据格式化
    total.value = res.data.count
    if (current.value > 1 && res.data.list.length === 0) {
      run({ pageNum: current.value - 1, pageSize: pageSize.value })
    }
    return res.data.list
  }
})

const whenPaginationChange = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}
const whenClickSearch = () => {
  run({ pageNum: 1, pageSize: pageSize.value })
}
const whenClickReset = () => {
  Object.assign(searchFields, getDefaultSearchFields())
  whenClickSearch()
}

const handleAdd = () => {
  thisFields.visible = true
  thisFields.type = 0
  thisFields.id = ''
}
const handleEdit = (record) => {
  // console.log("🚀 ~ handleEdit ~ record:", record)
  thisFields.visible = true
  thisFields.type = 1
  thisFields.id = record.id
}
const handleCopy = (record) => {
  thisFields.visible = true
  thisFields.type = 3
  thisFields.id = record.id
}
const whenEditSuccess = (value) => {
  thisFields.visible = false
  if (!value) { // 新增 重置搜索数据
    whenClickReset()
  } else { // 查看|编辑 不需要清空搜索数据 直接刷新
    refresh()
  }
}
const handleDelete = async (record) => {
  const res = await apiGiftRack.deletePage({ id: record.id })
  message.success(res.msg)
  refresh()
}

const handleStopUsing = async (record) => {
  await apiGiftRack.updateState({ id: record.id, state: record.state === 1 ? 0 : 1 })
  message.success(record.state === 1 ? '已停用' : '已启用')
  refresh()
}
const handleQuickFiltering = async (record) => {
  thisFields.record = record
  thisFields.ShelfConfigurationFirstTagDialogOpen = true
}

const handleExport = async () => {
  const param = cloneDeep(searchFields)
  param.onStartTime = param.dateTimeRange[0] || ''
  param.onEndTime = param.dateTimeRange[1] || ''
  delete param.dateTimeRange
  downloadTask({ type: 2, fileName: '货架列表', param })
}
const ShelfProductCornerManagementDialogHandler = async () => {
  thisFields.ShelfProductCornerManagementDialogOpen = true
}
</script>
