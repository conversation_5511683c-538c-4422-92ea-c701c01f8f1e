// 用户授权手机号
exports.getPhoneNumber = (data) =>
  wx.$request({
    url: "/app/user/wxphone_bycode",
    data,
    method: "get",
  });

exports.loginWechat = (data) =>
  wx.$request({
    url: "/app/user/login/wechat",
    data,
    method: "post",
  });

exports.register = (data) =>
  wx.$request({
    url: "/app/user/register",
    data,
    method: "post",
  });

// 获取当前用户会员信息
exports.getUserInfo = (data) =>
  wx
    .$request({
      url: "/app/user/current_member_info",
      data,
      method: "get",
    })
    .then((res) => {
      res.data.originIsMember = res.data.isMember;
      // 如果隐私协议不是最新的  已注册的 也要把用户信息当做未注册的人
      let visitor = false;

      const app = getApp();
      // if (res.data.isMember > 0) {
      //   if (
      //     res.data?.policyVersion !=
      //       app.globalData.privacyData?.policyVersion ||
      //     app.globalData.needAuthorization
      //   ) {
      //     res.data.isMember = 0;
      //     visitor = true;
      //   }
      // }
      // 用户弹窗隐私协议了
      if (
        res.data?.policyVersion != app.globalData.privacyData?.policyVersion ||
        app.globalData.needAuthorization
      ) {
        // 用户已注册
        if (res.data.isMember > 0) {
          res.data.isMember = 0;
        }
        visitor = true;
      }
      // 游客模式设置
      app.globalData.visitor = visitor;
      console.log("最新的游客模式，", app.globalData.visitor);
      return res;
    });

// 更新用户信息
exports.updateUserInfo = (data) =>
  wx.$request({
    url: "/app/user/update",
    data,
    method: "post",
  });

//获取会员等级列表
exports.grade_infoList = (data) =>
  wx.$request({
    url: "/app/user/grade_info/list",
    data,
    method: "get",
  });
// 获取当前人权益 会员等级
exports.grade_infoInfo = (data) =>
  wx.$request({
    url: "/app/user/grade_info/info",
    data,
    method: "get",
  });
//   积分过期消息提示
exports.usermypoints = (data) =>
  wx.$request({
    url: "/app/user/user/my/points",
    data,
    method: "get",
  });
// 获取最新隐私条款内容
exports.getPrivacyPolicy = (data) =>
  wx.$request({
    url: "/app/basic/privacy_policy/last_info",
    data,
    method: "get",
  });

// 更新授权隐私条款版本号
exports.updatePolicyVersion = (data) =>
  wx.$request({
    url: "/app/user/update/policy_version",
    data,
    method: "post",
  });

// 获取当前用户积分
exports.getUserPoint = (data) =>
  wx.$request({
    url: "/app/user/user/my/points",
    data,
    method: "get",
  });

// 注销用户
exports.writeOffUser = (data) =>
  wx.$request({
    url: "/app/user/log/off",
    data,
    method: "post",
  });

// 请求验证码
exports.sendSmsCode = (data) =>
  wx.$request({
    url: "/app/user/send/sms/code",
    data,
    method: "get",
  });

// 判断是否是会员
exports.isMemember = (data) =>
  wx.$request({
    url: "/app/user/wxphone_member_bycode",
    data,
    method: "get",
  });

// 小程序查询用户适用人群包
exports.checkCrowd = (data) =>
  wx.$request({
    url: "/app/user/crowd/check",
    data,
    method: "get",
  });

// 核心礼遇列表
exports.homeGiftList = (data) =>
  wx.$request({
    url: "/app/basic/gift/config/list",
    data,
    method: "get",
  });

// 获取当前用户的消费记录
exports.getPurchaseRecords = (data) =>
  wx.$request({
    url: "/app/user/muji_order/list",
    data,
    method: "get",
  });
// 用户消费记录详情
exports.getMuji_orderticket_info = (data) =>
  wx.$request({
    url: "/app/user/muji_order/ticket_info",
    data,
    method: "get",
  });
// 获取用户消息
exports.getUserMessage = (data) =>
  wx.$request({
    url: "/app/user/msg/list",
    data,
    method: "post",
  });

// 添加我的消息
exports.addUserMessage = (data) =>
  wx.$request({
    url: "/app/user/msg/add",
    data,
    method: "post",
  });

// 未读消息数量
exports.unreadMessageNum = () =>
  wx.$request({
    url: "/app/user/msg/num",
    method: "get",
  });

// 已读我的消息
exports.readMessage = (data) =>
  wx.$request({
    url: "/app/user/msg/read",
    method: "get",
    data,
  });

//获取核心福利 列表的详情的
exports.basicConfigInfo = (data) =>
  wx.$request({
    url: "/app/basic/gift/config/info",
    data,
    method: "get",
  });

//根据模版ID获取小程序订阅消息模板列表
exports.subscribeMsgIds = (data) =>
  wx.$request({
    url: "/app/basic/subscribe_msg/ids",
    data,
    method: "get",
  });

// 里程明细

exports.mileageDetails = (data) =>
  wx.$request({
    url: "/app/user/user/my/mileage/details",
    data,
    method: "get",
  });
//积分详情
exports.pointsDetails = (data) =>
  wx.$request({
    url: "/app/user/user/my/points/details",
    data,
    method: "get",
  });

//小程序中获取当前订阅消息配置
exports.subscribeConfig = (data) =>
  wx.$request({
    url: "/app/basic/mp_msg_config/get",
    data,
    method: "get",
  });

//发送订阅消息
exports.addAutoSubscribe = (data) =>
  wx.$request({
    url: "/app/user/subscription/add",
    data,
    method: "post",
  });

//我的可使用券数量
exports.couponNum = (data) =>
  wx.$request({
    url: "/app/user/coupon/used/num",
    data,
    method: "post",
  });

//当前用户新增clientId
exports.addUserClientId = (data) =>
  wx.$request({
    url: "/app/user/addUserClientId",
    data,
    method: "post",
  });

//完成线下消费任务奖励
exports.taskProduct = (data) =>
  wx.$request({
    url: "/app/sales/interaction/task/product",
    data,
    method: "get",
  });
//  会员扫码上报
exports.memberqrcodereport = ({ memberCode, qrcodeId, qrcodeSn }) =>
  wx.$request({
    url: `/app/user/member/qrcode/report?memberCode=${memberCode}&qrcodeId=${qrcodeId}&qrcodeSn=${qrcodeSn}`,

    method: "post",
  });

// 获取渠道值
exports.getChannelId = (data) =>
  wx.$request({
    url: "/app/basic/promotion_page/getParam",
    data,
    method: "get",
  });

// 获取即将过期积分
exports.getRecentExpirePoint = (data) =>
  wx.$request({
    url: "/app/user/user/my/points/recent",
    data,
    method: "post",
  });

// 获取已经过期积分
exports.getHistoryExpirePoint = (data) =>
  wx.$request({
    url: "/app/user/user/my/points/history",
    data,
    method: "post",
  });

// 编辑手机号发送验证码接口
exports.sendSmsEditCode = (data) =>
  wx.$request({
    url: "/app/user/send/sms/code2",
    data,
    method: "post",
  });

// 更新手机号接口
exports.updatePhone = (data) =>
  wx.$request({
    url: "/app/user/mobile/update",
    data,
    method: "post",
  });

// 绑定手机号接口
exports.bindPhone = (data) =>
  wx.$request({
    url: "/app/user/mobile/bind",
    data,
    method: "post",
  });

// 小程序二维码base64
exports.getqrcode_base64 = (data) =>
  wx.$request({
    url: "/app/basic/miniapp/qrcode_base64",
    data,
    method: "get",
  });

// 新增用户同意协议记录
exports.agreeRecord = (data) =>
  wx.$request({
    url: "/app/sales/user_agreement_record/add",
    data,
    method: "post",
  });

// 根据agreementId查询用户同意协议记录
exports.getAgreeRecord = (data) =>
  wx.$request({
    url: "/app/sales/user_agreement_record/info_by_agreementId",
    data,
    method: "get",
  });

// 小程序判断是否升级礼
exports.getGiftLeveShow = (data) =>
  wx.$request({
    url: "/app/basic/gift/level/show",
    data,
    method: "get",
  });

// 是否接受营销短信
exports.cdpAgreeSelect = (data) =>
  wx.$request({
    url: "/app/user/cdp/agree/select",
    data,
    method: "get",
  });
