const app = getApp()
import {
  getServeList
} from '../../../../api/index.js'

Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    isShow: {
      type: Boolean,
      value: false,
    }
  },
  lifetimes: {
    attached() {
      this.getList();
    }
  },
  /**
   * 组件的初始数据
   */
  data: {
    categoryList: []
  },

  /**
   * 组件的方法列表
   */
  methods: {
    onClose() {
      this.triggerEvent('closeDialog')
    },
    changeCategory(e) {
      const {
        value
      } = e.target.dataset;
      const {
        categoryList
      } = this.data;
      const newCategoryList = categoryList.map((item) => {
        if (item.id === value) {
          return {
            ...item,
            selected: !item.selected,
          }
        }
        return {
          ...item,
        }
      })
      this.setData({
        categoryList: newCategoryList,
      })
    },
    clearAll() {
      const {
        categoryList
      } = this.data;
      const newCategoryList = categoryList.map((item) => ({
        ...item,
        selected: false,
      }))
      this.setData({
        categoryList: newCategoryList,
      })
      this.triggerEvent("filterStore")
    },
    confirm() {
      const {
        categoryList
      } = this.data;
      const value = categoryList.filter(item => item.selected);
      const serveIds = value.map(item => item.id);
      this.triggerEvent("filterStore", {
        serveIds
      })
      this.triggerEvent('closeDialog')
    },
    getList() {
      getServeList().then((res) => {
        const v = res.data.map(item => ({
          ...item,
          selected: false,
        }))
        this.setData({
          categoryList: v,
        })
      })
    }
  },

})
