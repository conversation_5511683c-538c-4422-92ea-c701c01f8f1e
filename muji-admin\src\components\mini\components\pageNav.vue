<template>
  <a-form-item label="顶部风格">
    <a-radio-group v-model:value="addParams.navType" button-style="solid" size="small">
      <a-radio-button :value="item.value" v-for="item in navSetting" :key="item.value">{{item.name}}</a-radio-button>
      <!-- <a-radio-button :value="2">完全沉浸</a-radio-button>
      <a-radio-button :value="3">滑动恢复</a-radio-button> -->
    </a-radio-group>
  </a-form-item>
  <a-form-item label="滑动类型" v-if="addParams.navType==4">
    <a-radio-group v-model:value="addParams.moveType" button-style="solid" size="small">
      <a-radio-button :value="1">滑动前</a-radio-button>
      <a-radio-button :value="2">滑动后</a-radio-button>
    </a-radio-group>
  </a-form-item>
  <div class="header-title">状态栏设置</div>
  <!-- 前景颜色值，包括按钮、标题、状态栏的颜色，仅支持 #ffffff 和 #000000 -->
  <template v-if="addParams.navType==4&&addParams.moveType==2">
    <a-form-item label="状态栏的颜色" :labelCol="{ width:'130px' }">
      <a-radio-group v-model:value="addParams.move.navColor">
        <a-radio :value="1">黑色</a-radio>
        <a-radio :value="2">白色</a-radio>
      </a-radio-group>
    </a-form-item>
    <div class="header-title">页面标题</div>
    <a-form-item label="标题形式" :labelCol="{ width:'130px' }">
      <a-radio-group v-model:value="addParams.move.isTitle">
        <a-radio :value="0">无</a-radio>
        <a-radio :value="1">文字</a-radio>
        <a-radio :value="2">图片</a-radio>
      </a-radio-group>
    </a-form-item>
    <template v-if="addParams.move.isTitle==1">
      <a-form-item label="标题文字颜色" :labelCol="{ width:'130px' }">
        <Color color="rgba(0,0,0,1)" :value="addParams.move.titleColor" @changeColor="(value)=>changeColor('move.titleColor',value)"></Color>
      </a-form-item>
      <a-form-item label="标题文字位置" :labelCol="{ width:'130px' }">
        <a-radio-group v-model:value="addParams.move.titlePos">
          <a-radio :value="1">居中</a-radio>
          <a-radio :value="2">居左</a-radio>
        </a-radio-group>
      </a-form-item>
    </template>
    <template v-else-if="addParams.move.isTitle==2">
      <a-form-item label="标题图片">
        <uploadImg :max="10" :width="160" :height="30" :imgUrl="addParams.move.titleUrl" :form="addParams" path="move.titleUrl" :disabled="disabled" @success="uploadSuccess1" />
        <div class="global-tip">小程序显示高度固定40px，宽度自适应</div>
      </a-form-item>
      <a-form-item label="标题图片跳转热区" :labelCol="{ width:'130px' }">
        <addLink :imgUrl="addParams.move.titleUrl" :maxLinkNum="3" :showType="[1,3,7,9,6]" :links="addParams.move.imgLinks" :components="components" @ok="(link)=>addParams.move.imgLinks=link">
          <a-button block>设置热区</a-button>
        </addLink>
      </a-form-item>
    </template>
    <a-form-item label="标题返回按钮" :labelCol="{ width:'130px' }">
      <a-switch :unCheckedValue="0" :checkedValue="1" v-model:checked="addParams.move.back" />
    </a-form-item>
    <div class="header-title">页面标题背景</div>
    <a-form-item label="背景颜色设置" :labelCol="{ width:'130px' }">
      <a-radio-group v-model:value="addParams.move.bgColorStyle">
        <a-radio :value="0">无</a-radio>
        <a-radio :value="1"><a-space>默认
            <a-tooltip placement="top">
              <template #title>
                <span>默认指跟随全局页面风格设置</span>
              </template>
              <QuestionCircleFilled />
            </a-tooltip>
          </a-space></a-radio>
        <a-radio :value="2">自定义</a-radio>
      </a-radio-group>
      <div class="global-tip"></div>
    </a-form-item>
    <!-- 自定义设置 -->
    <bgSet :addParams="addParams.move" v-if="addParams.move.bgColorStyle==2"></bgSet>
  </template>
  <template v-else>
    <a-form-item label="状态栏的颜色" :labelCol="{ width:'130px' }">
      <a-radio-group v-model:value="addParams.navColor">
        <a-radio :value="1">黑色</a-radio>
        <a-radio :value="2">白色</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item label="滑动后状态栏" :labelCol="{ width:'130px' }" v-if="addParams.navType==3">
      <a-radio-group v-model:value="addParams.navColor1">
        <a-radio :value="1">黑色</a-radio>
        <a-radio :value="2">白色</a-radio>
      </a-radio-group>
    </a-form-item>
    <div class="header-title">页面标题</div>

    <a-form-item label="标题形式" :labelCol="{ width:'130px' }">
      <a-radio-group v-model:value="addParams.isTitle">
        <a-radio :value="0">无</a-radio>
        <a-radio :value="1">文字</a-radio>
        <a-radio :value="2">图片</a-radio>
      </a-radio-group>
    </a-form-item>
    <template v-if="addParams.isTitle==1">
      <a-form-item label="标题文字颜色" :labelCol="{ width:'130px' }">
        <Color color="rgba(0,0,0,1)" :value="addParams.titleColor" @changeColor="(value)=>changeColor('titleColor',value)"></Color>
      </a-form-item>
      <a-form-item label="标题文字位置" :labelCol="{ width:'130px' }">
        <a-radio-group v-model:value="addParams.titlePos">
          <a-radio :value="1">居中</a-radio>
          <a-radio :value="2">居左</a-radio>
        </a-radio-group>
      </a-form-item>
    </template>
    <template v-else-if="addParams.isTitle==2">
      <a-form-item label="标题图片">
        <uploadImg :max="10" :width="160" :height="30" :imgUrl="addParams.titleUrl" :form="addParams" path="titleUrl" :disabled="disabled" @success="uploadSuccess" />
        <div class="global-tip">小程序显示高度固定40px，宽度自适应</div>
      </a-form-item>
      <a-form-item label="标题图片跳转热区" :labelCol="{ width:'130px' }">
        <addLink :imgUrl="addParams.titleUrl" :maxLinkNum="3" :showType="[1,3,7,9,6]" :links="addParams.imgLinks" :components="components" @ok="(link)=>addParams.imgLinks=link">
          <a-button block>设置热区</a-button>
        </addLink>
      </a-form-item>
    </template>

    <a-form-item label="标题返回按钮" :labelCol="{ width:'130px' }">
      <a-switch :unCheckedValue="0" :checkedValue="1" v-model:checked="addParams.back" />
    </a-form-item>
    <div class="header-title">页面标题背景</div>
    <a-form-item label="背景颜色设置" :labelCol="{ width:'130px' }">
      <a-radio-group v-model:value="addParams.bgColorStyle">
        <a-radio :value="0">无</a-radio>
        <a-radio :value="1"><a-space>默认
            <a-tooltip placement="top">
              <template #title>
                <span>默认指跟随全局页面风格设置</span>
              </template>
              <QuestionCircleFilled />
            </a-tooltip>
          </a-space></a-radio>
        <a-radio :value="2">自定义</a-radio>
      </a-radio-group>
      <div class="global-tip"></div>
    </a-form-item>
    <!-- 自定义设置 -->
    <bgSet :addParams="addParams" v-if="addParams.bgColorStyle==2"></bgSet>
  </template>

</template>
<script setup>
const emit = defineEmits(['ok', 'cancel'])
import { set } from 'lodash'
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  // 页面组件
  components: {
    type: Array,
    default() {
      return []
    }
  },
  // 组件中图片的宽度
  width: {
    type: Number,
    default: 750
  },
  addParams: {
    type: Object,
    default() {
      return {}
    }
  },
  navSetting: {
    type: Array,
    default() {
      return [
        { value: 1, name: '固定标题' },
        { value: 2, name: '完全沉浸' },
        { value: 3, name: '滑动恢复' },
        { value: 4, name: '固定恢复' },
      ]
    }
  }
})

// 上传图片 视频
const uploadSuccess = async (data) => {
  console.log(data)
  let { form, path, imgUrl, imgWidth, imgHeight } = data;
  set(form, path, imgUrl)
  set(form, 'imgWidth', imgWidth)
  set(form, 'imgHeight', imgHeight)
}
// 上传图片 视频
const uploadSuccess1 = async (data) => {
  console.log(data)
  let { form, path, imgUrl, imgWidth, imgHeight } = data;
  set(form, path, imgUrl)
  set(form, 'move.imgWidth', imgWidth)
  set(form, 'move.imgHeight', imgHeight)
}


// 修改颜色
const changeColor = async (key, color) => {
  set(props.addParams, key, color)

}

</script>

<style>
</style>
