// 奖品类型 1实物商品 2电子券 3积分 4优惠券 99谢谢参与
export const PRIZES_TYPE_ARR = [
  { value: 1, label: '实物商品' },
  { value: 2, label: '电子券' },
  { value: 3, label: '积分' },
  { value: 4, label: '优惠券' },
  { value: 99, label: '谢谢参与' }
]
export const PRIZES_TYPE_OBJ = PRIZES_TYPE_ARR.reduce((c, v) => ({ ...c, [v.value]: v.label }), {})

// 奖品状态 1-启用 0-停用
export const PRIZES_STATUS_ARR = [
  { value: 1, label: '启用' },
  { value: 0, label: '停用' }
]
export const PRIZES_STATUS_OBJ = PRIZES_STATUS_ARR.reduce(
  (c, v) => ({ ...c, [v.value]: v.label }),
  {}
)

// 任务内容：1线下打卡，2兑礼任务，3线下消费，4邀请好友，5首次购买，6分享任务
export const TASK_DESC_ARR = [
  { value: 1, label: '线下打卡' },
  { value: 2, label: '兑礼任务' },
  { value: 3, label: '线下消费' },
  { value: 4, label: '邀请好友' },
  { value: 5, label: '首次购买' },
  { value: 6, label: '分享任务' }
]
export const TASK_DESC_OBJ = TASK_DESC_ARR.reduce((c, v) => ({ ...c, [v.value]: v.label }), {})
export const TASK_READY_CYCLE_ARR = [
  { value: 1, label: '一次性' },
  { value: 2, label: '周期' },
  { value: 3, label: '周期+阶梯' }
]
export const TASK_READY_CYCLE_OBJ = TASK_READY_CYCLE_ARR.reduce(
  (c, v) => ({ ...c, [v.value]: v.label }),
  {}
)
export const rewardTypeOptions = [
  { value: 1, label: '优惠券' },
  { value: 2, label: '积分' },
  { value: 3, label: '抽奖次数' }
]
export const rewardTypeOptionsObj = rewardTypeOptions.reduce(
  (c, v) => ({ ...c, [v.value]: v.label }),
  {}
)

export const INVENTORY_TASK_STATE = ['待执行', '已执行', '执行中', '待编辑']

// 活动状态
export const LOTTERY_STATE_ARR = [
  { value: 0, label: '待开始' },
  { value: 1, label: '进行中' },
  { value: 2, label: '已结束' }
]
export const LOTTERY_STATE_OBJ = LOTTERY_STATE_ARR.reduce(
  (c, v) => ({ ...c, [v.value]: v.label }),
  {}
)
