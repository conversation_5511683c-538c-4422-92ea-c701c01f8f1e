<template>
  <a-drawer :bodyStyle="{padding:0}" width="95%" placement="right" :closable="true" :maskClosable="false" :open="open" @close="onClose">
    <template #title>
      <a-space>
        <a-popconfirm title="确认要取消本次修改吗？" @confirm="onClose">
          <LeftOutlined />
        </a-popconfirm>
        <a-input v-if="bordered" placeholder="请输入模板名称" :disabled="!bordered" :bordered="bordered" v-model:value="addParams.pageSetting.templateName" style="width:200px" @focus="focus" @blur="bordered=false" show-count :maxlength="15">
        </a-input>
        <div style="width:200px" v-else>{{addParams.pageSetting.templateName}}</div>
        <a-button type="link" @click="bordered=!bordered">
          <EditOutlined />
        </a-button>
      </a-space>
    </template>
    <template #extra>
      <a-space>
        <a-popconfirm title="确认要取消本次修改吗？" @confirm="onClose">
          <a-button>取消</a-button>
        </a-popconfirm>
        <a-button @click="saveData(0)" :loading="loading">保存</a-button>
        <a-button @click="saveData(1)" :loading="loading" type="primary">保存并发布</a-button>
      </a-space>
    </template>
    <a-form class="form" ref="addForm" :model="addParams" :rules="rules" labelAlign="left" :labelCol="{ style: 'width:80px' }">
      <div class="content">
        <div class="content-left">
          <a-collapse expandIconPosition="end" v-model:activeKey="activeKey" :bordered="false" style="background: rgb(255, 255, 255)">
            <template #expandIcon="{ isActive }">
              <UpOutlined :rotate="isActive ? 180 : 0" />
            </template>
            <a-collapse-panel key="1">
              <template #header>
                <a-radio-group v-model:value="activeType" button-style="solid">
                  <a-radio-button :value="1">组件</a-radio-button>
                  <a-radio-button :value="2">事件</a-radio-button>
                  <a-radio-button :value="3">变量</a-radio-button>
                </a-radio-group>
              </template>
              <!-- 组件 -->
              <div v-if="[0,1].includes(activeType)">
                <!-- <div v-for="item in addParams.componentSetting" :key="item.id">{{ item.text }}</div> -->
                <a-collapse class="collapse" v-model:activeKey="activeKey1" default-active-key="1-0" :bordered="false" style="background: rgb(255, 255, 255)">
                  <template #expandIcon="{ isActive }">
                    <caret-right-outlined :rotate="isActive ? 90 : 0" />
                  </template>
                  <a-collapse-panel class="collapse" :key="'1-'+index" :header="item.type" v-for="(item,index) in components">
                    <div class="components">
                      <div class="components-item" v-for="data in item.components" :key="data.type" @click="addMini(data)">
                        <div class="components-item-img">
                          <img class="components-item-icon" :src="global.$cdn+data.icon" alt="">
                        </div>
                        <div class="components-item-title">{{ data.text }}</div>
                      </div>
                    </div>
                  </a-collapse-panel>
                </a-collapse>
              </div>
              <!-- 变量 -->
              <div v-if="activeType==3">
                <a-collapse class="collapse" v-model:activeKey="activeKey3" default-active-key="1-0" :bordered="false" style="background: rgb(255, 255, 255)">
                  <template #expandIcon="{ isActive }">
                    <caret-right-outlined :rotate="isActive ? 90 : 0" />
                  </template>
                  <a-collapse-panel class="collapse" :key="'1-'+index" :header="item.name" v-for="(item,index) in miniVariable">
                    <a-space wrap>
                      <template v-for="data in item.children" :key="data.id">
                        <a-tag>{{data.name}}</a-tag>
                      </template>
                    </a-space>
                  </a-collapse-panel>
                </a-collapse>
              </div>
            </a-collapse-panel>
          </a-collapse>
        </div>
        <!-- 预览 -->
        <div class="content-box">
          <preview :addParams="addParams" :pageType="addParams.pageSetting.pageType" :index="activeComponentIndex" @ok="updateComponents" @changeActive="changeActive"></preview>
        </div>
        <div class="content-right">
          <a-button :type="operateType==1?'primary':'default'" size="large" class="content-right-icon" @click="operateType=1;listOperate=false;activeComponentIndex=-2">
            <template #icon>
              <SettingOutlined />
            </template>
          </a-button>
          <a-button style="top:70px" :type="listOperate?'primary':'default'" size="large" class="content-right-icon" @click="changeList">
            <template #icon>
              <UnorderedListOutlined />
            </template>
          </a-button>
          <!--组件列表展示  -->
          <div class="content-list" v-if="listOperate">
            <componentList :components="addParams.componentSetting" :pageType="addParams.pageSetting.pageType" :index="activeComponentIndex" @ok="updateComponents" @changeActive="changeActive"></componentList>
          </div>
          <div class="content-info">
            <!-- 页面设置 -->
            <pageSet :disabled="disabled" :addParams="addParams" :pageSetting="pageSetting" :navSetting="navSetting" v-if="operateType==1"></pageSet>
            <!-- 顶栏设置 -->
            <topBar :disabled="disabled" :addParams="addParams" :pageSetting="pageSetting" :navSetting="navSetting" v-else-if="operateType==3"></topBar>
            <!-- 组件设置 -->
            <componentSet :disabled="disabled" :width="addParams.pageSetting.pageWidth" :components="addParams.componentSetting" :index="activeComponentIndex" v-else></componentSet>
          </div>

        </div>
      </div>
    </a-form>
  </a-drawer>
</template>
<script setup>
import { addTemplate, updateTemplate, templateInfo } from '@/http/index.js'
import { useGlobalStore } from '@/store'
import { message, Modal } from "ant-design-vue";
import pageSet from './components/pageSet.vue'
import componentList from './components/componentList.vue'
import componentSet from './components/componentSet.vue'
import topBar from './components/topBar.vue'
import preview from './components/preview/index.vue'
import { v4 as uuidv4 } from 'uuid'
const $router = useRouter();
const global = useGlobalStore()
const addForm = ref(null)
import { cloneDeep } from 'lodash'
// 组件的公共参数
import { pageData, components, typeData, miniVariable } from "@/utils/fixedVariable.js";
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  // 操作抽屉打开关闭
  visible: {
    type: Boolean,
    default: false
  },
  // 数据ID
  id: {
    type: String || Number,
    default: ''
  },
  // 操作类型
  type: {
    type: Number || String,
    default: 0, // 0-新增 1-编辑  2-查看 3-复制
  },
  // 额外配置参数设置
  params: {
    type: Object,
    default() {
      return {}
    }
  },
  // 页面选择
  pageSetting: {
    type: Array,
    default() {
      return [
        { key: '1', value: '常规页', },
        { key: '2', value: '加载页', },
        { key: '3', value: '开屏页', },
        { key: '4', value: '弹窗', },
      ]
    }
  },
  // 导航选择
  navSetting: {
    type: Array,
    default() {
      return [
        { value: 1, name: '固定标题' },
        { value: 2, name: '完全沉浸' },
        { value: 3, name: '滑动恢复' },
        { value: 4, name: '固定恢复' },
      ]
    }
  }

})

const disabled = computed(() => {
  return props.type == 2 ? true : false
})


const { addParams, open, activeType, rules, operateType, loading, bordered, pageSetting1, activeComponentIndex, activeKey, activeKey1, listOperate, activeKey3 } = toRefs(reactive({
  open: props.visible,
  addParams: { ...cloneDeep(pageData), ...props.params },
  activeKey: ['1'],
  pageSetting1: [],
  activeKey1: ['1-0'],
  activeKey3: ['1-0'],
  operateType: 1,// 操作类型  1-页面  2-组件 3-顶栏
  activeType: 1,// 当前激活的类型  1-组件 2-事件 3-变量
  loading: false,
  bordered: false,
  listOperate: false,
  activeComponentIndex: -1,// 组件生效的下标
  rules: {
    roleName: [{ required: true, message: '请输入角色名称', trigger: ['blur', 'change'] }],
    permitIds: [{ type: 'array', required: true, message: '请选择页面权限', trigger: ['blur', 'change'] }],
  }
}));


watch(() => props.visible, (value) => {
  // 弹窗操作
  open.value = props.visible
  addForm.value?.resetFields()
  if (open.value) {
    initData()
  }
})

const getInfo = (id) => {
  // 接口获取
  templateInfo({ id: id }).then(res => {
    let params = res.data;
    let json;
    // 发布
    if (params.publish) {
      json = JSON.parse(params.content || JSON.stringify({}))
    } else { // 未发布
      json = JSON.parse(params.pageJson || JSON.stringify({}))
    }
    params.pageSetting = json?.pageSetting || cloneDeep(pageData.pageSetting)
    params.pageSetting.templateName = params.templateName
    params.navSetting = json?.navSetting || cloneDeep(pageData.navSetting)
    params.actionLinks = json?.actionLinks || cloneDeep(pageData.actionLinks)
    params.componentSetting = json?.componentSetting || cloneDeep(pageData.componentSetting)
    // 复制清数据
    if (props.type == 3) {
      params.id = ''
      params.templateCode = ''
      params.publish = 0
      params.pageType = 0
      params.isOnly = 0
      params.pagePath = ''
      params.pageJson = ''
      params.content = ''
    }
    addParams.value = params
  })
}

//接口调取出
const initData = async () => {
  operateType.value = 1
  listOperate.value = false
  activeComponentIndex.value = -1

  // props.type 0-新增 1-编辑  2-查看 3-复制
  if (!props.type) {
    addParams.value = { ...cloneDeep(pageData), ...props.params }
    // 页面类型默认值
    addParams.value.pageSetting.pageType = props.pageSetting[0].key
    // 导航默认类型
    addParams.value.navSetting.navType = props.navSetting[0].value
  } else {
    getInfo(props.id)
  }
}



// 添加组件 组件数据
const addMini = (item) => {
  addParams.value.componentSetting.push({
    id: uuidv4(),
    ...cloneDeep(typeData[item.type])
  })
  operateType.value = 2
  activeType.value = 1
  activeComponentIndex.value = addParams.value.componentSetting.length - 1
}

// 更新组件数组
const updateComponents = (list) => {
  addParams.value.componentSetting = list
}

// 切换数组列表
const changeList = () => {
  listOperate.value = !listOperate.value
  if (listOperate.value) {
    // 弹窗显示组件
    if (addParams.value.pageSetting.pageType == 4) {
      operateType.value = 2
      activeComponentIndex.value = 0
    } else { // 页面显示导航
      operateType.value = 3
      activeComponentIndex.value = -1
    }
  }
}
// 切换激活组件
const changeActive = (index) => {
  console.log(index)
  if (index >= 0) {
    operateType.value = 2
    activeComponentIndex.value = index
  } else {
    operateType.value = 3
    activeComponentIndex.value = -1
  }
}

// 修改模板名称
const focus = () => {
  operateType.value = 1
  activeComponentIndex.value = -1
}
// 关闭弹窗
const onClose = () => {
  emit('cancel')
}
// 确定  publish//是否发布 0否1是
const saveData = (publish) => {
  addForm.value.validate().then(res => {
    let params = cloneDeep(addParams.value)
    console.log(addParams.value)
    console.log(JSON.stringify(addParams.value.componentSetting))
    console.log(params)
    params.publish = publish
    params.templateName = params.pageSetting.templateName
    if (publish) {
      params.content = JSON.stringify({
        pageSetting: params.pageSetting,
        navSetting: params.navSetting,
        actionLinks: params.actionLinks,
        componentSetting: params.componentSetting,
      })
      params.pageJson = params.content
    } else {
      params.pageJson = JSON.stringify({
        pageSetting: params.pageSetting,
        navSetting: params.navSetting,
        actionLinks: params.actionLinks,
        componentSetting: params.componentSetting,
      })
    }
    delete params.pageSetting;
    delete params.navSetting;
    delete params.actionLinks;
    delete params.componentSetting;
    let api = params.id ? updateTemplate : addTemplate;
    loading.value = true
    api(params).then((res) => {
      message.success('保存成功')
      emit('ok', publish)
      // 保存不发布 更新数据
      if (!publish) {
        getInfo(res.data)
      }
    }).finally(() => {
      loading.value = false
    })
  })
}


</script>

<style scoped lang="scss">
:deep(.ant-collapse-header) {
  border-bottom: 1px solid #d9d9d9;
}
:deep(.collapse .ant-collapse-header) {
  border-bottom: none;
}
:deep(.content-left .ant-collapse-item) {
  border-bottom: none;
}
.form {
  height: 100%;
  overflow: hidden;
}
.content {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  background: #f8f9fb;
  box-sizing: border-box;
  padding-left: 300px;
  padding-right: 600px;
  min-width: 1230px;
  &-left {
    position: absolute;
    left: 0;
    top: 0;
    width: 300px;
    height: 100%;
    overflow-y: auto;
    background: #fff;
  }
  &-right {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    background: #fff;
    display: flex;
    &-icon {
      position: absolute;
      top: 20px;
      left: -60px;
    }
  }
  &-list {
    width: 200px;
    height: 100%;
    overflow: hidden;
    border-right: 1px solid #d9d9d9;
  }
  &-info {
    width: 400px;
    height: 100%;
    overflow-y: auto;
  }
  &-box {
    position: relative;
    top: 50%;
    width: 375px;
    height: 800px;
    background: #fff;
    transform-origin: center center;
    transform: translateY(-50%) scale(0.8);
    box-shadow: 0 0 5px #ccc;
  }
}
.components {
  display: flex;
  flex-wrap: wrap;
  &-item {
    width: 32%;
    margin-right: 2%;
    margin-bottom: 10px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #efefef;
    border-radius: 5px;
    padding: 5px;
    &:nth-child(3n) {
      margin-right: 0;
    }
    &-img {
      border-radius: 5px;
      background: #fff;
    }
    &-icon {
      width: 100%;
    }
    &-title {
      line-height: 2;
    }
  }
}
</style>
