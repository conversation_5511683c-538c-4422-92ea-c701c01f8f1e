package com.dz.ms.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.ms.basic.dto.NavigationConfigDTO;
import com.dz.ms.basic.entity.NavigationConfig;

import java.util.List;

/**
 * 小程序UI自定义配置接口
 * @author: Handy
 * @date:   2022/11/21 15:00
 */
public interface NavigationConfigService extends IService<NavigationConfig> {

	/**
     * 分页查询小程序UI自定义配置
     * @param param
     * @return PageInfo<UiConfigDTO>
     */
    public PageInfo<NavigationConfigDTO> getUiConfigList(NavigationConfigDTO param);

    /**
     * 根据ID查询小程序UI自定义配置
     * @param id
     * @return UiConfigDTO
     */
    public NavigationConfigDTO getUiConfigById(Long id);

    /**
     * 保存小程序UI自定义配置
     * @param param
     * @return Long
     */
    public Long saveUiConfig(NavigationConfigDTO param);

    /**
     * 根据ID删除小程序UI自定义配置
     * @param param
     */
    public void deleteUiConfigById(IdCodeDTO param);

    /**
     * 获取所有小程序UI自定义配置列表
     * @return
     */
    List<NavigationConfigDTO> getAllUiConfig();

    /**
     * 获取当前选中的UI配置
     * @return
     */
    public NavigationConfigDTO getCheckedUiConfig();

    /**
     * 选中UI配置
     * @param param
     */
    void checkedUiConfigById(IdCodeDTO param);

}
