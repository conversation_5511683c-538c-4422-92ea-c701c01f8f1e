package com.dz.ms.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 用户注册信息DTO
 *
 * @author: Handy
 * @date: 2022/01/30 22:55
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "用户注册信息")
public class UserRegisterDTO {

    @ApiModelProperty(value = "手机号")
    private String mobile;
    @ApiModelProperty(value = "会员名")
    private String username;
    @ApiModelProperty(value = "出生日期")
    private String birthday;
    @ApiModelProperty(value = "头像")
    private String avatar;
    @ApiModelProperty(value = "性别 1男 2女 3未知")
    private Integer gender;
    @ApiModelProperty(value = "省份")
    private String province;
    @ApiModelProperty(value = "城市")
    private String city;
    @ApiModelProperty(value = "区")
    private String area;
    @ApiModelProperty(value = "详细地址")
    private String address;
    @ApiModelProperty(value = "授权隐私条款版本号")
    private String personalAccessVersion;
    @ApiModelProperty(value = "同意接受条款和隐私政策")
    private Boolean agreeClauseAndPrivacy;
    @ApiModelProperty(value = "是否愿意沟通品牌")
    private Boolean agreeBrandCommunicate;
    @ApiModelProperty(value = "是否愿意沟通集团")
    private Boolean agreeCorpCommunicate;
    @ApiModelProperty(value = "分享导购ID")
    private Long empId;
    @ApiModelProperty(value = "手机号是否会员")
    private Integer isMember;
    @ApiModelProperty(value = "来源渠道1")
    private String channelOne;
    @ApiModelProperty(value = "来源渠道2")
    private String channelTwo;
    @ApiModelProperty(value = "广告ID")
    private String gdtVid;
    @ApiModelProperty(value = "活动编码")
    private String campaignCode;
    @ApiModelProperty(value = "邀请人Id")
    private Long inviteUserId;
    @ApiModelProperty(value = "邀请任务Id")
    private Long taskId;

}
