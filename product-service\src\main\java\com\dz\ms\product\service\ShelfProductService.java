package com.dz.ms.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.ExchangeStaticParamDTO;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.product.InventoryParamDTO;
import com.dz.ms.product.dto.ShelfProductDTO;
import com.dz.ms.product.dto.ShelfProductQueryDTO;
import com.dz.ms.product.dto.req.ShelfProductMiniParamDTO;
import com.dz.ms.product.dto.req.ShelfProductParamDTO;
import com.dz.ms.product.dto.req.ShelfProductSaveParamDTO;
import com.dz.ms.product.dto.res.ProductMiniResDTO;
import com.dz.ms.product.dto.res.ShelfProductCartResDTO;
import com.dz.ms.product.dto.res.ShelfProductMiniResDTO;
import com.dz.ms.product.entity.ShelfProduct;

import java.util.List;
import java.util.Map;

/**
 * 货架商品接口
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:25
 */
public interface ShelfProductService extends IService<ShelfProduct> {

    /**
     * 分页查询货架商品
     *
     * @param param
     * @return PageInfo<ShelfProductDTO>
     */
    PageInfo<ShelfProductDTO> getShelfProductList(ShelfProductParamDTO param);

    /**
     * 根据ID查询货架商品
     * @param id
     * @param isThrow 1:抛异常
     * @return ShelfProductDTO
     */
    ShelfProductDTO getShelfProductById(Long id,Integer isThrow);

    /**
     * 根据主键ID列表查询货架商品列表
     * @param ids ids
     * @return List<ShelfProductDTO>
     */
    List<ShelfProductDTO> selLessListByIds(List<Long> ids);

    /**
     * 根据商品ID查询货架名称列表
     * @param productId 商品id
     * @return List<String>
     */
    List<String> getShelfNamesByProductId(Long productId);

    /**
     * 根据货架ID列表查询货架上架商品数量列表
     * @param shelfIds 架ID列表
     * @return List<IdNumberDTO>
     */
    Map<Long,Integer> getProductSumByShelfIds(List<Long> shelfIds);

    /**
     * 根据货架ID列表/商品ID列表/货架商品id列表等查询货架上架商品
     * @param queryDTO 查询条件
     * @return List<ShelfProductDTO>
     */
    List<ShelfProductDTO> getProductByShelfIds(ShelfProductQueryDTO queryDTO);

    /**
     * 根据货架ID列表查询货架上架商品
     * @param shelfIds 架ID列表
     * @param num num 1:查询少量字段, 2:查询所有字段
     * @return Map<Long,List<ShelfProductDTO>>
     */
    Map<Long,List<ShelfProductDTO>> getProductMapByShelfIds(List<Long> shelfIds, Integer num);

    /**
     * 根据货架id更新货架名称,根据商品id更新商品名称/商品类型
     * @param param 入参
     */
    void updShelfProductName(ShelfProductDTO param);

    /**
     * 保存货架商品
     *
     * @param param 入参
     * @param isAdd 是否新增
     * @param channelNum 渠道值 1:货架保存调用 2:货架商品管理调用
     */
    void saveShelfProduct(ShelfProductSaveParamDTO param, boolean isAdd, Integer channelNum);

    /**
     * 根据货架ID删除货架商品
     * @param param 入参
     */
    void deleteByShelfId(IdCodeDTO param);
    
    /**
     * 根据商品ID删除货架商品
     * @param param 入参
     */
    void deleteByProductId(IdCodeDTO param);

    /**
     * 根据主键ID删除货架商品
     * @param param 入参
     */
    void deleteShelfProductById(IdCodeDTO param);

    /**
     * 根据条件分页查询货架商品列表
     * @param param 货架商品查询参数对象，包含查询所需的各种条件和参数
     * @return 返回一个分页的货架商品信息列表，每个商品信息以ShelfProductMiniResDTO形式表示
     */
    PageInfo<ShelfProductMiniResDTO> getShelfProductListByParam(ShelfProductMiniParamDTO param);

    /**
     * 根据货架商品id查询货架商品详情
     * @param shelfProductId 货架商品id
     * @return 返回一个ShelfProductMiniResDTO对象，其中包含货架商品详情信息
     */
    ProductMiniResDTO getShelfProductByShelfProductId(Long shelfProductId);

    /**
     * 根据货架商品id查询展示中的货架商品简介
     * @param shelfProductId 货架商品id
     * @return 返回一个ShelfProductMiniResDTO对象，其中包含货架商品详情信息
     */
    ProductMiniResDTO getShelfProductIntroByShelfProductId(Long shelfProductId);

    /**
     * 根据货架ID和货架商品id列表获取商品列表
     * @param shelfId 货架ID
     * @param shelfProductIds 货架商品id列表
     * @return 返回一个包含商品信息的列表，每个商品信息以ShelfProductCartResDTO形式表示
     */
    List<ShelfProductCartResDTO> getShelfProductCartList(Long shelfId, List<Long> shelfProductIds);

    /**
     * 扣减或增加货架库存
     *
     * @param isAdd          是否增加库存，1为增加，2为扣减
     * @param shelfId        货架ID
     * @param shelfProductId 货架商品ID
     * @param num            数量
     */
    void deductOrAddInventory(Integer isAdd, Long shelfId, Long shelfProductId, Integer num);

    /**
     * 更新货架库存/活动库存
     *
     * @param param
     */
    void updateInventory(InventoryParamDTO param);

    /**
     * 推送兑换成功统计数据
     *
     * @param orderStaticParam
     */
    void updateStatic(ExchangeStaticParamDTO orderStaticParam);

    /**
     * 校验货架库存/活动库存
     *
     * @param param
     */
    void validateInventory(InventoryParamDTO param);
}
