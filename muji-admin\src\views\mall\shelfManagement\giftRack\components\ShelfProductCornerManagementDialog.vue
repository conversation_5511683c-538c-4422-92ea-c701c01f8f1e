<template>
  <a-modal v-model:open="thisFields.open" :title="`角标管理`" @ok="thisMethods.handleOk" @cancel="thisMethods.cancel" :okButtonProps="{hidden:true}" :cancelButtonProps="{hidden:true}" width="800px">
    <a-form class="form" ref="formRef" :model="formFields" :rules="formRules" :labelCol="{ style: 'width:160px' }">
      <!-- <a-form-item label="请输入角标名称" name="name">
        <a-input placeholder="请输入" style="width:300px;" v-model:value="formFields.name" show-count :maxlength="4" />
        <a-button class="addButton" type="link" @click="thisMethods.handleAdd" :disabled="thisFields.loading">添加</a-button>
      </a-form-item> -->
      <div class="ui-shelf-add-product-and-management-product">
        <div class="_left">
          <span style="width: 160px;text-align: right;">已有角标：</span>
        </div>
        <div v-if="!thisFields.list.length">
          <div class="ui-c-grey">暂无数据</div>
        </div>
        <div v-else>
          <div class="delWrap" v-for="(item,index) in thisFields.list" style="margin-bottom:5px;">
            <a-input v-if="item.isEdit" placeholder="请输入" style="width:300px;" v-model:value="item.nameTemp" show-count :maxlength="4" />
            <div v-else style="width:300px;" class="delName">{{ item.name }}</div>
            <!-- <a-button type="link" @click="thisMethods.handleEdit(item)" :disabled="item.isEdit">编辑</a-button>
            <a-button type="link" @click="thisMethods.handleEditOk(item)" :disabled="!item.isEdit">保存</a-button>
            <a-button type="link" @click="thisMethods.handleEditCancel(item)" :disabled="!item.isEdit">取消</a-button>
            <a-popconfirm title="是否确定删除该角标？" @confirm="thisMethods.handleDel(item)">
              <a-button type="link">删除</a-button>
            </a-popconfirm> -->
          </div>
        </div>
      </div>
    </a-form>
  </a-modal>
</template>

<script setup>
import { onMounted, reactive, watch } from 'vue'
import { cloneDeep } from 'lodash'
import { apiShelfCrowd } from '@/http/index.js'
import { message } from 'ant-design-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})
const emits = defineEmits(['update:modelValue'])

const formRef = ref(null)
const formFields = reactive({
  name: ''
})
const formRules = reactive({
  // name: [{ required: true, message: '本项必填' }]
})

const thisFields = reactive({
  list: [],
  loading: false,
  open: false
})
const thisMethods = {
  handleEdit(record) {
    record.isEdit = true
  },
  async handleEditOk(record) {
    if (!record.nameTemp) return message.warn('角标名称不能为空')
    if (thisFields.list.filter(v => v.id !== record.id).map(v => v.name).includes(record.nameTemp)) return message.warn('角标名称不能重复')
    const res = await apiShelfCrowd.updatePage({ id: record.id, name: record.nameTemp })
    record.name = record.nameTemp
    message.success(res.msg)
    record.isEdit = false
  },
  handleEditCancel(record) {
    record.nameTemp = record.name
    record.isEdit = false
  },
  setOpen() {
    thisFields.open = props.modelValue
    formRef.value?.resetFields()
    if (thisFields.open) {
      thisMethods.initData()
    }
  },
  async getAllPageList() {
    thisFields.loading = true
    const res = await apiShelfCrowd.getAllPageList().finally(() => thisFields.loading = false)
    thisFields.list = res.data
  },
  async initData() {
    formFields.name = ''
    await thisMethods.getAllPageList()
  },
  async handleAdd() {
    await formRef.value.validate()
    let params = cloneDeep(formFields)
    if (!params.name) return message.warn('请输入角标名称')
    if (thisFields.list.map(v => v.name).includes(params.name)) return message.warn('角标名称不能重复')
    thisFields.loading = true
    const res = await apiShelfCrowd.createPage(params).finally(() => thisFields.loading = false)
    message.success(res.msg)
    await thisMethods.initData()
  },
  async handleDel(record) {
    const res = await apiShelfCrowd.deletePage({ id: record.id })
    message.success(res.msg)
    await thisMethods.getAllPageList()
  },
  cancel() {
    emits('update:modelValue', false)
  },
  handleOk() {
    emits('update:modelValue', false)
  }
}

onMounted(() => thisMethods.setOpen())
watch(() => props.modelValue, () => thisMethods.setOpen())
</script>

<style lang="scss" scoped>
.form {
  padding-top: 20px;
}

.addWrap {
  display: flex;
  align-items: center;

  .addButton {
    margin-bottom: 24px;
  }
}

.delWrap {
  display: flex;
  align-items: center;
  cursor: move;

  .delName {
    width: 202px;
  }
}
</style>
