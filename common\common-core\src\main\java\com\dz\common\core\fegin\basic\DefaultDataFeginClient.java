package com.dz.common.core.fegin.basic;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.basic.GiftConfigDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * 默认数据
 * @Author: Handy
 * @Date: 2023/12/20 20:03
 */
@FeignClient(name = ServiceConstant.BASIC_SERVICE_NAME, contextId = "DefaultDataFeginClient")
public interface DefaultDataFeginClient {

    /**
     * 获取抽签文案
     * @return
     */
    @GetMapping(value = "/dict_data/lottery")
    public Result<List<String>> getLotteryWords();

    /**
     * 获取核心礼遇列表
     * @return
     */
    @GetMapping(value = "/crm/gift/list")
    public Result<List<GiftConfigDTO>> getGiftList();

}
