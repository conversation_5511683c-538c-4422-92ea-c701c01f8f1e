@import "assets/scss/config";

page {
  overflow: hidden;
}

.pageWrap {
  min-height: 100vh;
  position: relative;
  background-size: cover;

  .treeText {
    position: fixed;
    top: 214rpx;
    left: 96rpx;
    background-size: contain;
    width: 407rpx;
    height: 550rpx;
  }

  .treeLogo {
    position: fixed;
    top: 201rpx;
    right: 73rpx;
    background-size: cover;
    width: 109rpx;
    height: 201rpx;
  }

  .treeMujiLogo {
    position: fixed;
    top: 890rpx;
    left: 96rpx;
    background-size: cover;
    width: 108rpx;
    height: 71rpx;
  }

  .treeActivityRulesButton {
    position: fixed;
    top: 811rpx;
    right: 0;
    background-size: cover;
    width: 82rpx;
    height: 208rpx;
  }

  .inputWrap {
    overflow: hidden;
    width: 564rpx;
    margin: 0 auto;
    padding: 1040rpx 4rpx 0;
    font-weight: 800;
    font-size: 25rpx;
    color: #94243A;
    line-height: 42rpx;

    .item {
      margin-bottom: 70rpx;
      padding: 7rpx 0;
      border-bottom: 1px solid #94243A;
      display: flex;

      .label {
        margin-right: 34rpx;
        display: flex;
        align-items: center;

        .gapW {
          width: 25rpx;
        }
      }

      .input {
        flex: 1;
        font-weight: 400;
        font-size: 33rpx;
        color: #000000;
        line-height: 42rpx;
      }

      &:last-of-type {
        margin-bottom: 70rpx;
      }
    }
  }

  .submitButton {
    background-size: cover;
    width: 462rpx;
    height: 80rpx;
    margin: 0 auto;
  }

  .submitHint {
    margin: 14px auto 0;
    width: 445rpx;
    height: 18rpx;
    background-size: cover;
  }
}
