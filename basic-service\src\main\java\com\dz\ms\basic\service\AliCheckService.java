package com.dz.ms.basic.service;

import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: txt
 * @Date: 2022/3/15 16:48
 * @Version: 1.0
 */
public interface AliCheckService {
    /**
     * 文本审核
     *
     * @param content
     * @param modelType 1 打卡
     * @param modelId 打卡id /
     * @return
     */
    boolean textScanRequest(String content, Integer modelType, Long modelId, boolean flag);

    /**
     * 图片审核
     *
     * @param urlList
     * @param modelType
     * @param modelId
     * @return
     */
    Map<String, Integer> imageSyncScanRequest(List<String> urlList, Integer modelType, Long modelId, boolean flag);

    /**
     * 视频异步审核
     *
     * @param url
     * @param dataId
     * @param modelType
     * @param modelId
     * @throws Exception
     */
    void videoAsyncScanRequest(String url, String dataId, Integer modelType, Long modelId) throws Exception;
}
