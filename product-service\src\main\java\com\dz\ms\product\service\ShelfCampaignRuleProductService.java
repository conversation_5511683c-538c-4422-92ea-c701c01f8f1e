package com.dz.ms.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.ms.product.dto.ShelfCampaignRuleProductDTO;
import com.dz.ms.product.dto.req.ShelfCampaignRuleProductDelParamDTO;
import com.dz.ms.product.dto.req.ShelfCampaignRuleProductParamDTO;
import com.dz.ms.product.dto.req.ShelfCampaignRuleProductSaveParamDTO;
import com.dz.ms.product.dto.res.ShelfCampaignRuleProductEnhanceDTO;
import com.dz.ms.product.dto.res.ShelfCampaignRuleProductResDTO;
import com.dz.ms.product.entity.ShelfCampaignRuleProduct;

import java.util.List;
import java.util.Map;

/**
 * 营销活动规则关联的货架商品接口
 *
 * @author: LiinNs
 * @date: 2024/11/20 11:39
 */
public interface ShelfCampaignRuleProductService extends IService<ShelfCampaignRuleProduct> {

    /**
     * 分页查询营销活动规则关联的货架商品
     * @param param 入参
     * @return PageInfo<ShelfCampaignRuleProductResDTO>
     */
    PageInfo<ShelfCampaignRuleProductResDTO> getShelfCampaignRuleProductList(ShelfCampaignRuleProductParamDTO param);

    /**
     * 据营销活动id列表查询关联商品的数量
     * @param campaignIds 销活动id列表
     * @return Map<Long, Long>
     */
    Map<Long, Long> getRuleProductNumByCampaignIds(List<Long> campaignIds);

    /**
     * 根据货架ID/货架商品id列表获取启用且进行中的货架活动规则商品信息
     * @param shelfId 货架ID
     * @param shelfProductIds 货架商品id列表
     * @param num 1:查询少量字段, 2:查询所有字段
     * @return 返回一个列表，如果没有找到符合条件的，则返回空列表
     */
    List<ShelfCampaignRuleProductDTO> getShelfCampaignRuleProductByShelfId(Long shelfId, List<Long> shelfProductIds, Integer num);
    /**
     * 根据货架ID/货架商品id列表获取进行中的货架活动规则商品信息
     * @param shelfId 货架ID
     * @param shelfProductIds 货架商品id列表
     * @param num 1:查询少量字段, 2:查询所有字段
     * @return 返回一个Map，如果没有找到符合条件的，则返回空Map
     */
    Map<Long, ShelfCampaignRuleProductDTO> getShelfCampaignRuleProductMapByShelfId(Long shelfId, List<Long> shelfProductIds, Integer num);

    /**
     * 根据ID查询营销活动规则关联的货架商品
     *
     * @param id
     * @return ShelfCampaignRuleProductDTO
     */
    public ShelfCampaignRuleProductDTO getShelfCampaignRuleProductById(Long id);

    /**
     * 根据主键ID列表查询规则商品列表
     * @param ids ids
     * @return List<ShelfCampaignRuleProductDTO>
     */
    List<ShelfCampaignRuleProductDTO> selLessListByIds(List<Long> ids);

    /**
     * 根据营销活动id列表/规则ID列表/货架商品id列表查询活动上架商品
     * @param campaignIds 营销活动id列表
     * @param ruleIds 规则ID列表
     * @param shelfProductIds 货架商品id列表
     * @param num num 1:查询少量字段, 2:查询所有字段
     * @return List<ShelfCampaignRuleProductDTO>
     */
    List<ShelfCampaignRuleProductDTO> getProductByManyIds(List<Long> campaignIds, List<Long> ruleIds, List<Long> shelfProductIds, Integer num);

    /**
     * 根据营销活动id列表/规则ID列表查询规则上架商品增强数据(附加图片,标签,角标等数据)
     * @param campaignIds 营销活动id列表
     * @param ruleIds 规则ID列表
     * @param num num 1:查询少量字段, 2:查询所有字段
     * @return List<ShelfCampaignRuleProductEnhanceDTO>
     */
    List<ShelfCampaignRuleProductEnhanceDTO> getProductEnhanceByManyIds(List<Long> campaignIds, List<Long> ruleIds, Integer num);

    /**
     * 保存营销活动规则关联的货架商品
     * @param param 入参
     * @param isAdd 是否新增
     */
    void saveShelfCampaignRuleProduct(ShelfCampaignRuleProductSaveParamDTO param, boolean isAdd);

    /**
     * 根据ID删除营销活动规则关联的货架商品
     *
     * @param param
     */
    public void deleteShelfCampaignRuleProductById(IdCodeDTO param);

    /**
     * 根据条件删除营销活动规则关联的货架商品
     * @param param 删除条件
     */
    void deleteRuleProduct(ShelfCampaignRuleProductDelParamDTO param);

    /**
     * 扣减或增加活动规则商品库存
     *
     * @param isAdd          是否增加库存，1为增加，2为扣减
     * @param shelfId        货架ID
     * @param shelfProductId 货架商品ID
     * @param num            数量
     */
    void ruleProductDeductOrAddInventory(Integer isAdd, Long shelfId, Long shelfProductId, Integer num);

    /**
     * 校验货架库存/活动库存
     */
    void ruleProductValidateInventory(Long shelfId, Long shelfProductId, Integer num);
}
