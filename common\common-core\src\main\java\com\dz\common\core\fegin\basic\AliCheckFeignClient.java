package com.dz.common.core.fegin.basic;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.basic.AliCheckRequestDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * @Description:
 * @Author: txt
 * @Date: 2024-06-25 11:38
 * @Version: 1.0
 */
@FeignClient(name = ServiceConstant.BASIC_SERVICE_NAME, contextId = "AliCheckFeignClient")
public interface AliCheckFeignClient {


    @PostMapping(value = "/aliyun/check_text")
    public Result<Boolean> textScanRequest(@RequestBody AliCheckRequestDTO param);


    @PostMapping(value = "/aliyun/images_check")
    public Result<Map<String, Integer>> imageSyncScanRequest(@RequestBody AliCheckRequestDTO param);


}
