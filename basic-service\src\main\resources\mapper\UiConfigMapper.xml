<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.basic.mapper.UiConfigMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    id,
  	    ui_name,
  	    color,
  	    bg_color,
  	    selected_color,
  	    content,
  	    font_size,
  	    checked,
  	    tenant_id,
  	    creator,
  	    created,
  	    modifier,
  	    modified
    </sql>

    <!-- 获取所有小程序UI自定义配置列表 -->
    <select id="getAllUiConfig" resultType="com.dz.ms.basic.dto.NavigationConfigDTO">
        select id,
  	    ui_name,
  	    color,
        selected_color,
  	    checked
        from navigation_config
    </select>

	<!-- 获取当前选中的UI配置 -->
	<select id="getCheckedUiConfig" resultType="com.dz.ms.basic.dto.NavigationConfigDTO">
		select
		<include refid="Base_Column_List" />
        from navigation_config
    </select>

	<!-- 取消选中UI -->
	<update id="uncheckedUiConfig">
        update navigation_config
        set checked = 0
    </update>

</mapper>
