package com.dz.ms.sales.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户任务完成周期记录
 */
@Getter
@Setter
@NoArgsConstructor
@Table("用户任务完成周期记录")
@TableName(value = "t_user_task_success_record")
public class UserTaskSuccessRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "主键id")
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     *任务id
     */
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "任务id")
    private Long taskId;
    /**
     *用户id
     */
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "用户id")
    private Long userId;
    /**
     *任务第几周期
     */
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "任务第几周期")
    private Integer cycleNum;
    /**
     *1未读，2已读
     */
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "1", comment = "1未读，2已读")
    private Integer isRead;
    /**
     *创建人
     */
    @Columns(type = ColumnType.VARCHAR, length = 255, isNull = true, comment = "创建人")
    private String createAt;
    /**
     *创建时间
     */
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "创建时间")
    private Date createTime;
    /**
     *渠道
     */
    @Columns(type = ColumnType.VARCHAR, length = 255, isNull = false, comment = "渠道")
    private Long tenantId;
}
