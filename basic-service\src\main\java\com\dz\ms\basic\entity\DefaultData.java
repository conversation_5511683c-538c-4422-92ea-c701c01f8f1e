package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 默认数据
 * @author: Handy
 */
@Getter
@Setter
@NoArgsConstructor
@Table("默认数据")
@TableName(value = "default_data")
public class DefaultData implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,isUnique = true,comment = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.INT,length = 10,isNull = true,comment = "数据类型 1默认小程序UI配置 2抽签文案")
    private Integer type;
    @Columns(type = ColumnType.TEXT,length = 0,isNull = true,comment = "UI配置内容json")
    private String content;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;

    public DefaultData(Long id, Integer type, String content) {
        this.id = id;
        this.type = type;
        this.content = content;
    }
}
