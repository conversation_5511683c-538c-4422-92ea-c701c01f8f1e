package com.dz.common.core.dto.basic;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 核心礼遇配置DTO
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "核心礼遇配置")
public class GiftConfigDTO extends BaseDTO {

    @ApiModelProperty(value = "ID")
    private Long id;
    @ApiModelProperty(value = "活动ID")
    private String activityId;
    @ApiModelProperty(value = "礼遇名称")
    private String name;
    @ApiModelProperty(value = "礼遇类型：1新人礼配置，2二回礼配置，3生日礼配置，4升级礼配置，5无以上礼遇可领取且券列表有有效券时，6无礼遇、无可用券时，7无礼遇、有可使用的商品券时")
    private Integer giftType;
    @ApiModelProperty(value = "卡片样式")
    private String headImg;
    @ApiModelProperty(value = "弹窗配置")
    private String ballImg;
    @ApiModelProperty(value = "跳转链接")
    private String jumpUrl;
    @ApiModelProperty(value = "关联优惠券id")
    private String couponId;
    @ApiModelProperty(value = "领券类型，1活动券，2普通券")
    private Integer receiveType;
    @ApiModelProperty(value = "弹窗跳转链接")
    private String ballJumpUrl;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "领取弹窗")
    private String reveiveBallImg;
    @ApiModelProperty(value = "其他配置")
    private List<GiftConfigOtherDTO> giftConfigOtherDTO;
}
