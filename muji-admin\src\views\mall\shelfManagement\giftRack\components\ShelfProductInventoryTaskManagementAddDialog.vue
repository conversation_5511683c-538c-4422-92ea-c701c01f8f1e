<template>
  <a-modal v-model:open="thisFields.open" title="" @ok="thisMethods.handleOk" @cancel="thisMethods.cancel" :okButtonProps="{disabled:thisFields.loading}" width="1200px">
    <layout>
      <template v-slot:header>
        <!--:disabled="!$hasPermission('mall:permission:searchGiftGoods')"-->
        <searchForm :formParams="searchFields" @cancel="whenClickReset" @ok="whenClickSearch">
          <a-form-item label="商品名称" name="productName">
            <a-input placeholder="请输入" allow-clear v-model:value="searchFields.productName" allowClear @keyup.enter="whenClickSearch"></a-input>
          </a-form-item>
          <BaseProductTypeSelect label="商品类型" name="pdType" v-model="searchFields.pdType" />
        </searchForm>
      </template>
      <template v-slot="{ height }">
        <a-table
          :row-selection="{ preserveSelectedRowKeys: true, selectedRowKeys: thisFields.selectedRowKeys, onChange: thisMethods.onSelectChange }"
          :indentSize="20" row-key="id" :scroll="{ scrollToFirstRowOnChange: true, x: '100%' }" :dataSource="dataSource" :columns="thisFields.tableHeader" :pagination="pagination" :loading="loading" @change="whenPaginationChange">
          <template #bodyCell="{text, record, index, column}">
            <template v-if="column.dataIndex === 'index'">{{ (current - 1) * pageSize + 1 + index }}</template>
            <template v-if="column.dataIndex === 'shelfImg'">
              <a-image :src="record.shelfImg" :width="50" :height="50" />
            </template>
            <template v-if="column.dataIndex === 'tagList'">
              {{ record.tagList.map(v => v.name).join('、') }}
            </template>
          </template>
        </a-table>
      </template>
    </layout>
  </a-modal>
</template>
<script setup>
import { usePagination } from 'vue-request'
import { apiGiftRack } from '@/http/index.js'
import { onMounted, watch } from 'vue'
import { cloneDeep } from 'lodash'
import { v4 } from 'uuid'

const props = defineProps({
  shelfId: {
    type: [String, Number],
    default: ''
  },
  modelValue: {
    type: Boolean,
    default: false
  }
})
const emits = defineEmits(['update:modelValue', 'ok'])

const pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ['bottomLeft']
  }
})
const total = ref(0)
const getDefaultSearchFields = () => ({
  pdType: undefined,
  productName: undefined
})
const searchFields = reactive(getDefaultSearchFields())
const thisFields = reactive({
  selectedRowKeys: [],
  selectedRowObjs: [],
  loading: false,
  open: false,
  tableHeader: [
    { title: '序号', dataIndex: 'index', align: 'center', width: 80 },
    { title: '商品名称', dataIndex: 'productName', align: 'center', ellipsis: true, width: 100 },
    { title: '商品类型', dataIndex: 'pdTypeDesc', align: 'center', ellipsis: true, width: 100 },
    { title: '创建时间', dataIndex: 'created', align: 'center', ellipsis: true, width: 140 },
    { title: '商品橱窗图', dataIndex: 'shelfImg', align: 'center', ellipsis: true, width: 100 },
    { title: '商品标签', dataIndex: 'tagList', align: 'center', ellipsis: true, width: 100 },
    { title: '积分价值', dataIndex: 'costPoint', align: 'center', ellipsis: true, width: 100 },
    { title: '累计兑换量', dataIndex: 'exchangeNum', align: 'center', ellipsis: true, width: 100 }
  ]
})
const thisMethods = {
  onSelectChange (e, a) {
    // console.log('onSelectChange：', e, a)
    thisFields.selectedRowKeys = e
    thisFields.selectedRowObjs = a
  },
  setOpen () {
    thisFields.open = props.modelValue
    if (thisFields.open) {
      whenClickReset()
    }
  },
  clearSelected () {
    thisFields.selectedRowKeys = []
    thisFields.selectedRowObjs = []
  },
  cancel () {
    emits('update:modelValue', false)
    thisMethods.clearSelected()
  },
  handleOk () {
    emits('update:modelValue', false)
    const list = thisFields.selectedRowObjs.map(v => ({
      ...v,
      id: undefined,
      ptType: v.ptType,
      productId: v.productId,
      shelfProductId: v.id,
      uuid: v4()
    }))
    emits('ok', list)
    thisMethods.clearSelected()
  }
}

const { data: dataSource, run, loading, pageSize, current, refresh } = usePagination((param) => {
  const data = { ...param, ...searchFields }
  data.shelfId = props.shelfId
  return apiGiftRack.getShelfProductPageList(data)
}, {
  manual: true, // 修改为false 让其自动执行
  pagination: {
    currentKey: 'pageNum', // 通过该值指定接口 当前页数 参数的属性值
    pageSizeKey: 'pageSize', // 通过该值指定接口 每页获取条数 参数的属性值
    totalKey: 'data.data.count' // 指定 data 中 total 属性的路径
  },
  formatResult: res => { // 返回数据格式化
    total.value = res.data.count
    return res.data.list
  }
})

const whenPaginationChange = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}
const whenClickSearch = () => {
  run({ pageNum: 1, pageSize: pageSize.value })
}
const whenClickReset = () => {
  Object.assign(searchFields, getDefaultSearchFields())
  whenClickSearch()
}

onMounted(() => thisMethods.setOpen())
watch(() => props.modelValue, () => thisMethods.setOpen())
</script>

<style scoped lang="scss"></style>
