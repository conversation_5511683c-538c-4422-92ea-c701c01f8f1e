## 应用配置
server:
  port: 10207
daozhi.version: 11.30 #检测发版是否成功用提交正式版前请更新
spring:
  application:
    name: sa-service
  profiles:
    active: dev
  mvc:
    date-format: yyyy-MM-dd HH:mm:ss
  jackson:
    joda-date-time-format: yyyy-MM-dd HH:mm:ss
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
  main:
    allow-bean-definition-overriding: true
mybatis:
  typeAliasesPackage: com.dz.ms.sa.entity
  mapperLocations: classpath:mapper/*.xml
  configuration:
    mapUnderscoreToCamelCase: true
database.sharding.enable: true
management:
  endpoint:
    serviceregistry:
      enabled: true
  endpoints:
    web:
      base-path: /actuator # 访问根路径
      exposure:
        include: "*"
ribbon:
  ConnectTimeout: 3000 #请求连接的超时时间 默认的时间为 1 秒
  ReadTimeout: 6000 #请求处理的超时时间
---
spring:
  profiles: dev
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
      config:
        server-addr: 127.0.0.1:8848
        extension-configs[0]:
          data-id: application-dev.yml
          group: SA_SERVICE
        extension-configs[1]:
          data-id: sharding-jdbc-dev.yml
          group: SA_SERVICE
---
spring:
  profiles: uat
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        group: UAT_GROUP
        username: nacos
        password: Daozhi@123
      config:
        server-addr: 127.0.0.1:8848
        extension-configs[0]:
          data-id: application-uat.yml
          group: SA_SERVICE
        extension-configs[1]:
          data-id: sharding-jdbc-uat.yml
          group: SA_SERVICE
        username: nacos
        password: Daozhi@123
---
spring:
  profiles: prod
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        username: nacos
        password: Daozhi@123
      config:
        server-addr: 127.0.0.1:8848
        extension-configs[0]:
          data-id: application-prod.yml
          group: SA_SERVICE
        extension-configs[1]:
          data-id: sharding-jdbc-prod.yml
          group: SA_SERVICE
        username: nacos
        password: Daozhi@123