/* signUp/pages/activateCheckIn/activateCheckIn.wxss */

.page-container {
  background: #F8F6ED;

  .uploader {
    .van-uploader__preview-delete {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32rpx;
      height: 32rpx;
      background: #C7B397;

      &::after {
        display: none;
      }

      .van-uploader__preview-delete-icon {
        position: relative;
        transform: none;
        font-size: 20rpx;
      }
    }

    .uploader-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      //justify-content: center;
      margin-right: 24rpx;
      margin-bottom: 24rpx;
      height: 213rpx;
      width: 213rpx;
      background: #fff;

      .plus {
        margin-top: 66rpx;
        margin-bottom: 25rpx;
        width: 54rpx;
        height: 54rpx;
        position: relative;

        &:before {
          content: "";
          position: absolute;
          left: 0;
          right: 0;
          top: 50%;
          height: 1rpx;
          background: #666;
        }

        &:after {
          content: "";
          position: absolute;
          left: 50%;
          top: 0;
          bottom: 0;
          width: 1rpx;
          background: #666;
        }
      }

      .text {
        font-weight: 300;
        font-size: 22rpx;
        color: #3C3C43;
        line-height: 40rpx;
        letter-spacing: 2rpx;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }

  .activateCheckIn {
    height: 100%;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;

    .activeRules {
      padding-left: 60rpx;
      background-color: #FFFFFF;
      position: relative;

      .top-right {
        position: absolute;
        right: 27rpx;
        top: 163rpx;
        display: flex;
        flex-direction: column;
        align-items: center;

        .text {
          width: 35rpx;
          font-weight: 500;
          font-size: 32rpx;
          color: var(--text-black-color);
          line-height: 43rpx;
          letter-spacing: 2rpx;
          text-align: left;
        }
      }

      .title {
        font-family: MUJIFont2020, SourceHanSansCN;
        font-weight: 700;
        font-size: 52rpx;
        color: var(--text-black-color);
        line-height: 71rpx;
        text-align: left;
        padding-top: 21rpx;
        margin-bottom: -7rpx;
      }

      .rules {
        font-family: MUJIFont2020,
          SourceHanSansCN;
        font-weight: 500;
        font-size: 26rpx;
        color: var(--text-black-color);
        letter-spacing: 2rpx;
        text-align: left;
        display: flex;
        align-items: center;
        margin-top: 53rpx;

        .text {
          font-family: MUJIFont2020;
          font-weight: 900;
          font-size: 87rpx;
          color: #E3D8BB;
          letter-spacing: 6rpx;
          text-align: left;
          min-width: 58rpx;
        }

        .text1 {
          width: 450rpx;
          margin-left: 30rpx;
          font-family: MUJIFont2020, SourceHanSansCN;
          font-weight: 500;
          font-size: 26rpx;
          color: var(--text-black-color);
          line-height: 47rpx;
          letter-spacing: 2rpx;
          text-align: left;
          position: relative;
          top: 4rpx;
        }

        .b {
          font-weight: 700;
          font-size: 26rpx;
          color: var(--text-black-color);
        }
      }
    }

    .picture {
      width: 56rpx;
      height: 69rpx;
      background-size: 100% 100%;
      margin-top: 22rpx;
    }

    .bottom {
      position: relative;

      .bottom-right {
        position: absolute;
        right: 27rpx;
        top: 90rpx;
        display: flex;
        flex-direction: column;
        align-items: center;

        .text {
          width: 35rpx;
          font-weight: 500;
          font-size: 32rpx;
          color: var(--text-black-color);
          line-height: 43rpx;
          letter-spacing: 2rpx;
          text-align: left;
        }
      }

      .upload_file {
        padding-top: 40rpx;
        padding-left: 55rpx;

        .title {
          padding-right: 50rpx;
          font-family: MUJIFont2020, SourceHanSansCN;
          font-weight: 500;
          font-size: 26rpx;
          color: var(--text-black-color);
          line-height: 39rpx;
          letter-spacing: 2rpx;
          text-align: left;
          padding-top: 60rpx;
          padding-bottom: 35rpx;
          display: block;

          &:first-child {
            padding-top: 0;
          }
        }

        .btn {
          // margin-top: 35rpx;
          margin-bottom: 12rpx;
        }


        .avatar-box {
          .auth-avatar {
            width: 100rpx;
            height: 100rpx;
          }
        }

        .type {
          width: 100%;
          height: 219rpx;
          white-space: nowrap;
          display: flex;
          flex-wrap: nowrap;
          flex-direction: row;

          .type-wrap {
            display: inline-block;
            margin-top: 20rpx;
            margin-right: 22rpx;
            width: 199rpx;
            height: 199rpx;
            background: #FFFFFF;
            border-radius: 50%;
            overflow: hidden;
            color: var(--text-black-color);
            font-weight: 300;

            .type-item {
              display: flex;
              align-items: center;
              justify-content: center;
              white-space: pre-wrap;
              height: 100%;
              width: 100%;
              box-sizing: border-box;
              padding: 20rpx;

              font-family: MUJIFont2020,
                SourceHanSansCN;
              font-size: 24rpx;
              line-height: 40rpx;
              letter-spacing: 2rpx;
              text-align: center;

            }

          }

          .active {
            background-color: #C8B49A;
            font-weight: 500;
            color: #FFFFFF;
          }


        }

        .tips {
          font-family: MUJIFont2020,
            SourceHanSansCN;
          font-weight: 400;
          font-size: 25rpx;
          color: var(--text-black-color);
          line-height: 45rpx;
          letter-spacing: 2rpx;
          text-align: right;
          padding-right: 49rpx;
          margin-top: 20rpx;
        }
      }
    }

  }

  .check {
    height: 29rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 350;
    font-size: 20rpx;
    color: #3C3C43;
    line-height: 29rpx;
    text-align: center;

    .iconfont {
      font-size: 20rpx;
      margin-right: 13rpx;
    }

    .link {
      font-weight: 350;
      font-size: 20rpx;
      color: #BFA37D;
    }

    .radio {
      border-radius: 50%;
      // font-size: 28rpx;
      color: var(--text-black-color);
    }
  }
}

.bottom-box {
  margin-top: 11rpx;
  margin-bottom: env(safe-area-inset-bottom);
  display: flex;
  justify-content: center;
}
