package com.dz.ms.basic.service;

import com.dz.ms.basic.dto.AutoCodeColumnDTO;
import com.dz.ms.basic.dto.AutoCodeTableDTO;

import java.util.List;

/**
 * 代码生成
 * @author: Handy
 * @date:   2019/12/5 17:36
 */
public interface AutoCodeService {

    public List<String> getAllDatabase();

    public List<AutoCodeTableDTO> getTableNameBykeyword(String database, String keyword);

    public List<AutoCodeColumnDTO> getTableInfoByTableName(String database, String tableName);

}
