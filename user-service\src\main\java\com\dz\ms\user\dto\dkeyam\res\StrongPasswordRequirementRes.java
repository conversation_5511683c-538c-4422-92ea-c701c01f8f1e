package com.dz.ms.user.dto.dkeyam.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.io.Serializable;

/**
 * 用户认证策略出参
 * @author: fei
 * @date: 2025/01/22 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StrongPasswordRequirementRes implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("是否需要静态密码")
    private Boolean staticPasswordRequired;

    @ApiModelProperty("是否需要动态密码 如果策略为不需要动态密码，值为false" +
            "如果策略为需要动态密码，用户有令牌，值为true" +
            "如果策略为需要动态密码，用户没有令牌，值为false")
    private Boolean dynamicPasswordRequired;

    @ApiModelProperty("是否能获取动态密码 如果为true，调用接口1.3，将发送动态密码给用户")
    private Boolean needsGetDynamicPassword;

    @ApiModelProperty("强制使用动态密码（可以通过动态密码策略策略：用户未使用动态密码，禁止或允许，来进行配置）如果值为true，但是用户登录没有使用令牌，则登录被禁止")
    private Boolean dynamicPasswordEnforced;
}
