const app = getApp();

Page({
  data: {
    loading: true,
  },
  onShow(options) {
    this.init();
  },
  init() {
    let { userInfo, privacyData, needAuthorization } = app.globalData;
    // 在隐私协议弹窗不需要展示的前提下 才能自动跳转
    if (
      userInfo?.policyVersion != privacyData?.policyVersion ||
      needAuthorization
    ) {
      console.log("不执行自动跳转1");
      this.setData({ loading: false });
    } else if (
      userInfo.isFreeze ||
      (userInfo.isMember > 0 && !userInfo.mobile)
    ) {
      // 冻结或者需要绑定手机号
      console.log("不执行自动跳转2");
      this.setData({ loading: false });
    } else {
      // 没有隐私协议等全局弹窗的时候，跳转到H5页面
      // 判断用户是否注册
      if (userInfo.isMember > 0) {
        wx.$mp.redirectTo({
          url: "/report/pages/h5/h5",
        });
      } else {
        const backUrl = encodeURIComponent(`/report/pages/index/index`)
        wx.$mp.redirectTo({
          url: `/pages/register/register?backUrl=${backUrl}`,
        })
      }

    }
  },
  onShareAppMessage() {
    return {
      title: '探索20年和MUJI的旅途，赢经典商品',
      path: '/report/pages/index/index',
      imageUrl: this.data.$cdn + '/member/share.jpg',
    };
  },
});
