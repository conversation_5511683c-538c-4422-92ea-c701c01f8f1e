// 所有的命名必须全局唯一
import service from '@/utils/request.js'


// 隐私新增
export function privacy_policyAdd(data = {}) {
    return service({
        url: '/crm/basic/privacy_policy/add',
        method: 'post',
        data
    })
}

// 隐私编辑
export function privacy_policyUpdate(data = {}) {
    return service({
        url: '/crm/basic/privacy_policy/update',
        method: 'post',
        data
    })
}

// 隐私详情
export function privacy_policyInfo(data = {}) {
    return service({
        url: '/crm/basic/privacy_policy/info',
        method: 'get',
        data
    })
}


// 隐私列表
export function privacy_policyList(data = {}) {
    return service({
        url: '/crm/basic/privacy_policy/list',
        method: 'get',
        data
    })
}

// 隐私删除

export function privacy_policyDelete(data = {}) {
    return service({
        url: '/crm/basic/privacy_policy/delete',
        method: 'post',
        data
    })
}
