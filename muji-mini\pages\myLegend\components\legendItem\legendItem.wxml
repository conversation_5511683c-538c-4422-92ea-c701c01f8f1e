<view class="legend-item" wx:if="{{typeShow == 'Legend'}}" data-typeShow="Legend" 
data-legendData="{{legendData}}"  bindtap="toDetail">
  <view class="item-title flex-box">
    <view >
      <!-- 消费{{legendData.price}}元 -->
      {{legendData.mileageName}}
    </view>
    <view  style="text-align: right;">
      {{legendData.changeType == 1? '+':''}}{{legendData.pointsNum}}
    </view>
  </view>
  <view class="info">
    <view>获取时间：{{legendData.obtainTime}}</view>
    <view>消费渠道：{{legendData.channel}}</view>
  </view>
</view>
<view class="legend-item" wx:if="{{typeShow == 'coupon'}}" bindtap="toDetail" data-typeShow="coupon" 
data-legendData="{{legendData}}">
  <view class="item-title flex-box">
    <view >
      {{legendData.pointsName}}
    </view>
    <view  style="text-align: right;">
      {{legendData.changeType == 1? '+':''}}{{legendData.pointsNum}}
    </view>
  </view>
  <view class="info">
    <view>{{legendData.changeType == 1?'获取时间：':'扣减时间：'}}{{legendData.obtainTime}}</view>
    <view wx:if="{{legendData.changeType == 1}}">有效期至：{{legendData.expireTime}}</view>
  </view>
</view>
