.rule-componets {
  background: #fafafa;
  // border-radius: 20rpx;
  padding: 60rpx 40rpx;

  .rule-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 30rpx;

    .toggle-icon {
      margin-left: 10rpx;
      width: 32rpx;
      height: 32rpx;
    }

    &-left {
      font-family: NotoSansHans;
      font-weight: 500;
      font-size: 28rpx;
      color: #3c3c43;
      line-height: 32rpx;
      letter-spacing: 1px;
      text-align: left;
      font-style: normal;
    }

    &-right {
      display: flex;
      // justify-content: space-between;
      align-items: center;
      font-family: NotoSansHans;
      font-weight: 400;
      font-size: 24rpx;
      color: #888888;
      line-height: 32rpx;
      text-align: right;
      font-style: normal;
      // margin-right: 2rpx;
    }
  }

  .rule-content {
    &-text {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #888888;
      line-height: 40rpx;
      letter-spacing: 1px;
      text-align: left;
      font-style: normal;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 99999;

      &.show {
      }
      &.hide {
        -webkit-line-clamp: 2;
      }
    }
  }
}
