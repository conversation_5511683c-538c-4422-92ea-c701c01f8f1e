<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.basic.mapper.PopupConfigMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    id,
  	    popup_type,
  	    popup_img,
  	    jump_link,
  	    tenant_id,
  	    created,
  	    creator,
  	    modified,
  	    modifier
    </sql>

    <!-- 查询示例 -->
    <select id="selectDemo" parameterType="java.lang.Long" resultType="com.dz.ms.basic.entity.PopupConfig">
        select
        <include refid="Base_Column_List" />
        from popup_config
        where id = #{id,jdbcType=BIGINT}
        AND tenant_id = #{tenantId}
    </select>

</mapper>
