<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.sales.mapper.UserLotteryPrizesMapper">

    <select id="selectUserLotteryPrizesByMany" resultType="long">
        select count(id) from user_lottery_prizes
        <where>
            unionid = #{unionid}
            and campaign_code = #{campaignCode}
            and created >= #{monthStart}
            and created &lt;= #{monthEnd}
            and prizes_level_code = #{prizesLevelCode}
        </where>
    </select>



</mapper>
