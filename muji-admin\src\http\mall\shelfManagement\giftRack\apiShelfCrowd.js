import service from '@/utils/request.js'

const itemHandler = (v) => {
  v.nameTemp = v.nameTemp || v.name
}

export const apiShelfCrowd = {
  async getAllPageList (data) {
    return await service({ url: '/crm/product/superscript/no_page_list', method: 'get', data }).then(res => {
      res.data.forEach(v => itemHandler(v))
      return res
    })
  },
  async getPageList (data) {
    return await service({ url: '/crm/product/superscript/list', method: 'get', data }).then(res => {
      res.data.list.forEach(v => itemHandler(v))
      return res
    })
  },
  async getPageDetail (data) {
    return await service({ url: '/crm/product/superscript/info', method: 'get', data }).then(res => {
      return res
    })
  },
  async createPage (data) {
    return await service({ url: '/crm/product/superscript/add', method: 'post', data }).then(res => {
      return res
    })
  },
  async updatePage (data) {
    return await service({ url: '/crm/product/superscript/update', method: 'post', data }).then(res => {
      return res
    })
  },
  async updateState (data) {
    data.number = data.state
    return await service({ url: '/crm/product/superscript/update_state', method: 'post', data }).then(res => {
      return res
    })
  },
  async deletePage (data) {
    return await service({ url: '/crm/product/superscript/delete', method: 'post', data }).then(res => {
      return res
    })
  },
  async exportPage (data) {
  }
}
