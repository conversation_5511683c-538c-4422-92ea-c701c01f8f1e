
import { h, } from 'vue'
import { RouterView } from 'vue-router'
import Layout from '@/views/layout/index.vue'


// 返回项目路径
export function getNormalPath(p) {
  if (p.length === 0 || !p || p == 'undefined') {
    return p
  };
  let res = p.replace('//', '/')
  if (res[res.length - 1] === '/') {
    return res.slice(0, res.length - 1)
  }
  return res;
}


//下面是菜单的处理方法
let level = 0
// 匹配views里面所有的.vue文件
const modules = import.meta.glob('./../../src/views/**/*.vue')

export const loadView = (route) => {
  let res
  for (const path in modules) {

    const dir = path.split('views/')[1].split('.vue')[0]
    if (dir === route.component) {
      res = modules[path]
    }
  }
  return res
}
export function filterAsyncRouter(asyncRouterMap, parentPath) {

  level++
  let res = asyncRouterMap.filter((route) => {
    route.meta = route.meta || {}
    route.meta.hidden = route.hidden
    // 默认返回的name为大驼峰式的组件名
    // 删除name，避免vue router自动删除重名的路由
    // delete route.name
    if (parentPath) {
      let path = route.path
      route.path = /^(\/|http)/.test(path) ? path : parentPath + '/' + path
    }

    if (route.component) {
      // Layout组件特殊处理
      if (route.redirect === 'noRedirect' && route.children.length) {
        let firstChildPath = route.children[0].path
        route.redirect = firstChildPath.startsWith('/') ? firstChildPath : route.path + '/' + firstChildPath
      }
      if (route.component === 'Layout') {
        if (level != 1) {
          route.component = {
            render: () => h(RouterView),
          }
        } else {
          route.component = Layout
        }
      } else {
        // console.log(loadView(route),'loadView(route)');
        route.component = loadView(route)
      }
    }
    let children = route.children
    if (children && children.length) {
      route.children = filterAsyncRouter(route.children, route.path)
    }
    return true
  })
  level--
  return res
}