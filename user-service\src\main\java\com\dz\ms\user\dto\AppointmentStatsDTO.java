package com.dz.ms.user.dto;

// 预约活动状态

import lombok.Data;
import org.jetbrains.annotations.NotNull;

import java.util.Objects;

@Data
public class AppointmentStatsDTO implements Comparable<AppointmentStatsDTO> {
    private String id;
    private String name;
    private boolean isFull;
    private Integer remainingStock;


    @Override
    public int compareTo(@NotNull AppointmentStatsDTO other) {
        return Objects.compare(this.name, other.name, String::compareTo);
    }
}
