package com.dz.ms.product.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.product.dto.TagInfoDTO;
import com.dz.ms.product.service.TagInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Api(tags = "商品标签")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class TagInfoController {

    @Resource
    private TagInfoService tagInfoService;

    /**
     * 分页查询商品标签
     *
     * @param param
     * @return result<PageInfo < TagInfoDTO>>
     */
    @ApiOperation("分页查询商品标签")
    @GetMapping(value = "/crm/tag_info/list")
    public Result<PageInfo<TagInfoDTO>> getTagInfoList(@ModelAttribute TagInfoDTO param) {
        Result<PageInfo<TagInfoDTO>> result = new Result<>();
        PageInfo<TagInfoDTO> page = tagInfoService.getTagInfoList(param);
        result.setData(page);
        return result;
    }

    /**
     * 查询商品标签
     *
     * @return result<List < TagInfoDTO>>
     */
    @ApiOperation("查询商品标签")
    @GetMapping(value = "/crm/tag_info/no_page_list")
    public Result<List<TagInfoDTO>> getNoPageTagInfoList(@RequestParam(value = "name", required = false) String name,
                                                         @RequestParam(value = "cate") Integer cate) {
        Result<List<TagInfoDTO>> result = new Result<>();
        List<TagInfoDTO> page = tagInfoService.getNoPageTagInfoList(name, cate);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询商品标签
     *
     * @param id
     * @return result<TagInfoDTO>
     */
    @ApiOperation("根据ID查询商品标签")
    @GetMapping(value = "/crm/tag_info/info")
    public Result<TagInfoDTO> getTagInfoById(@RequestParam("id") Long id) {
        Result<TagInfoDTO> result = new Result<>();
        TagInfoDTO tagInfo = tagInfoService.getTagInfoById(id);
        result.setData(tagInfo);
        return result;
    }

    /**
     * 新增商品标签
     *
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增商品标签", type = LogType.OPERATELOG)
    @ApiOperation("新增商品标签")
    @PostMapping(value = "/crm/tag_info/add")
    public Result<Long> addTagInfo(@RequestBody TagInfoDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param, true);
        Long id = tagInfoService.saveTagInfo(param);
        result.setData(id);
        return result;
    }

    /**
     * 更新商品标签
     *
     * @param param
     * @return result<Object>
     */
    @SysLog(value = "更新商品标签", type = LogType.OPERATELOG)
    @ApiOperation("更新商品标签")
    @PostMapping(value = "/crm/tag_info/update")
    public Result<Long> updateTagInfo(@RequestBody TagInfoDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param, false);
        tagInfoService.updateTagInfo(param);
        result.setData(param.getId());
        return result;
    }

    /**
     * 验证保存参数
     *
     * @param param
     */
    private void validationSaveParam(TagInfoDTO param, boolean isAdd) {
        if (Objects.isNull(param.getCate())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "标签级别不能为空");
        }
        if (isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if (!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
    }

    /**
     * 根据ID删除商品标签
     *
     * @param param
     * @return result<Boolean>
     */
    @SysLog(value = "根据ID删除商品标签", type = LogType.OPERATELOG)
    @ApiOperation("根据ID删除商品标签")
    @PostMapping(value = "/crm/tag_info/delete")
    public Result<Boolean> deleteTagInfoById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        tagInfoService.deleteTagInfoById(param);
        result.setData(true);
        return result;
    }

}
