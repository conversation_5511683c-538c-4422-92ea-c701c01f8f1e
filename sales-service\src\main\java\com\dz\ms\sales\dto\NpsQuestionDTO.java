package com.dz.ms.sales.dto;

import com.dz.common.core.annotation.Columns;
import com.dz.common.core.enums.ColumnType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/16
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "NPS问卷问题")
public class NpsQuestionDTO {

    @ApiModelProperty(value = "问题ID")
    private Long id;
    @ApiModelProperty(value = "问卷ID")
    private Long npsId;
    @ApiModelProperty(value = "问题名称")
    private String title;
    @ApiModelProperty(value = "副标题")
    private String subTitle;
    @ApiModelProperty(value = "问题类型 1单选题 2多选题 3问答题 4打分题 5拍照题")
    private Integer questionType;
    @ApiModelProperty(value = "问答题提示")
    private String questionTips;
    @ApiModelProperty(value = "最高分值")
    private Integer maxScore;
    @ApiModelProperty(value = "最小分值文案")
    private String minScoreText;
    @ApiModelProperty(value = "最大分值文案")
    private String maxScoreText;
    @ApiModelProperty(value = "拍照题最小照片数量")
    private Integer minPhotoNum;
    @ApiModelProperty(value = "拍照题最大照片数量")
    private Integer maxPhotoNum;
    @ApiModelProperty(value = "是否必填 0否 1是")
    private Integer isMust;
    @ApiModelProperty(value = "问题排序")
    private Integer sort;

    @ApiModelProperty(value = "别名")
    private String alias;
    @ApiModelProperty(value = "问题选项列表")
    private List<NpsQuestionOptionDTO> options;

}
