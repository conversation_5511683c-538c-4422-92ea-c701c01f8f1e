<template>
  <div ref="form">
    <a-form :colon="false" layout="inline" ref="searchForm" :model="formParams" class="searchForm">
      <div class="searchForm-form" ref="search" :class="{ collapse: more && !expand }">
        <slot></slot>
      </div>
      <a-form-item class="searchForm-btn">
        <a-space>
          <div v-if="more">
            <a @click="showMore(!expand)">
              {{ expand ? '收起' : '展开' }}
              <component :is="expand ? 'UpOutlined' : 'DownOutlined'" />
            </a>
          </div>
          <a-button type="primary" ghost @click="emit('cancel', searchForm)" :disabled="disabled">
            <ReloadOutlined />清空
          </a-button>
          <a-button type="primary" @click="emit('ok')" :disabled="disabled">
            <SearchOutlined />查询
          </a-button>
        </a-space>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import { useGlobalStore } from '@/store'
import { fileUpload } from '@/http/index.js'
import { message } from 'ant-design-vue'
import { v4 as uuidv4 } from 'uuid'
import { reactive, toRefs, ref, onMounted, watch, nextTick, onUnmounted } from 'vue'

const global = useGlobalStore()
const searchForm = ref()
const search = ref()
const form = ref()

const emit = defineEmits(['cancel', 'ok', 'change'])

const props = defineProps({
  formParams: {
    type: Object,
    default: () => ({})
  },
  disabled: {
    type: Boolean,
    default: false
  },
})

const state = reactive({
  expand: false,
  more: false,
})

const { more, expand } = toRefs(state)

const checkLines = () => {
  if (!search.value?.children) return 0
  return Array.from(search.value.children).reduce((count, formItem) => {
    return formItem.offsetLeft <= 20 ? count + 1 : count
  }, 0)
}

const updateFormItems = (shouldExpand = false) => {
  if (!search.value?.children) return

  Array.from(search.value.children).forEach(formItem => {
    const isVisible = shouldExpand ||
      !(formItem.offsetLeft + formItem.offsetWidth > search.value.clientWidth + search.value.offsetLeft)

    formItem.style.opacity = isVisible ? 1 : 0
    formItem.hidden = !isVisible
  })
}

const pageChange = async () => {
  more.value = false
  updateFormItems(true)

  await nextTick()
  const lines = checkLines()
  more.value = lines > 1

  setTimeout(() => {
    updateFormItems(expand.value)
    global.setLayout(uuidv4())
  }, 10)
}

watch(() => search.value, async () => {
  const lines = checkLines()
  more.value = lines > 1
  await pageChange()
})

const showMore = async (value) => {
  expand.value = value
  await pageChange()
}

const handleResize = () => pageChange()
window.addEventListener('resize', handleResize)

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.searchForm {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;

  &-form {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    overflow: hidden;

    &.collapse {
      flex-wrap: nowrap;
    }
  }

  &-btn {
    margin-left: 20px;
    margin-right: 0;

    a {
      user-select: none;
      color: #000;

      &:hover {
        color: #000;
      }
    }
  }
}

:deep(.searchForm .ant-form-item) {
  margin-bottom: 16px;
}

:deep(.searchForm-form .ant-form-item) {
  width: 200px;

  &:has(label) {
    width: auto;
  }
}
</style>
