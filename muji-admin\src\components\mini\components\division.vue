<template>
  <div class="header-title">基本设置</div>
  <a-form-item label="线条颜色">
    <Color color="rgba(0,0,0,1)" :value="data.color" @changeColor="changeColor"></Color>
  </a-form-item>
  <a-form-item label="线条高度">
    <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.height"></a-input-number>
  </a-form-item>
  <a-form-item label="线条类型">
    <a-select placeholder="请选择" v-model:value="data.lineType" :getPopupContainer="triggerNode => triggerNode.parentNode">
      <a-select-option value="solid">实线</a-select-option>
      <a-select-option value="dotted">点状</a-select-option>
      <a-select-option value="double">双线</a-select-option>
      <a-select-option value="dashed">虚线</a-select-option>
    </a-select>
  </a-form-item>

  <div class="header-title">组件样式</div>
  <a-form-item label="边距" :labelCol="{width:'50px'}">
    <a-space>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingTop" addon-before="上" addon-after="px"></a-input-number>
      </a-form-item>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingBottom" addon-before="下" addon-after="px"></a-input-number>
      </a-form-item>
    </a-space>
    <a-space>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingLeft" addon-before="左" addon-after="px"></a-input-number>
      </a-form-item>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingRight" addon-before="右" addon-after="px"></a-input-number>
      </a-form-item>
    </a-space>
  </a-form-item>
  <div class="header-title">背景设置</div>
  <bgSet :addParams="data"></bgSet>
</template>
<script setup>
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  // 所有组件数组
  components: {
    type: Array,
    default() {
      return []
    }
  },
  // 当前组件数据
  data: {
    type: Object,
    default() {
      return {}
    }
  },
  // 组件展示的宽度
  width: {
    type: Number,
    default: 750,
  },

})

// 修改颜色
const changeColor = async (color) => {
  props.data.color = color
}

// 上传图片 视频
const uploadSuccess = async (data) => {
  let { form, path, imgUrl } = data;
  form[path] = imgUrl
}
</script>

<style>
</style>
