package com.dz.ms.user.dto.dkeyam.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 派发手机令牌 入参
 * @author: fei
 * @date: 2025/01/22 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MobileTokenDeliverParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("管理员登录名")
    private String adminLoginName;

    @ApiModelProperty("管理员密码")
    private String adminPassword;

    @ApiModelProperty("站点名称")
    private String tenantName;

    @ApiModelProperty("用户源名称")
    private String identityStoreName;

    @ApiModelProperty("用户登录名")
    private String loginName;

    @ApiModelProperty("派发模式" +
            "0：派发到邮箱" +
            "1：派发到手机短信" +
            "2：仅绑定")
    private int mode;

    @ApiModelProperty("是否解绑以前的令牌，默认值true：解绑")
    private boolean unbindPrevious;

    @ApiModelProperty("激活码接收邮箱。如果不填写，激活码发送到用户默认邮箱中；如果用户没有默认邮箱，则必须填写")
    private String toEmail;

    @ApiModelProperty("激活码接收手机号。如果不填写，激活码发送到用户默认手机号中；如果用户没有默认手机号，则必须填写")
    private String toMobile;

    @ApiModelProperty("激活码有效期（天）。如果不填写，则按照站点名称所对应站点内配置的激活码有效期为准")
    private Integer expireInDays;

    @ApiModelProperty("令牌有效期（天）。如果不填写，则按照站点名称所对应站点内配置的激活码有效期为准")
    private Integer tokenExpireInDays;

    @ApiModelProperty("令牌延迟启用（小时）。如果不填写，则按照站点名称所对应站点内配置的令牌延迟启用为准")
    private Integer tokenBindingStartDelayHours;

}
