/* signUp/pages/prizeDraw/prizeDraw.wxss */
.page-container {
    background: linear-gradient(rgba(255, 255, 255, 0) 1%, #f8f6ed 100%);
    background-size: 100% 100%;

    .prizeDraw {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        position: relative;

        .activeRules {
            position: absolute;
            right: 0rpx;
            top: 40rpx;
            width: 58rpx;
            height: 160rpx;
            background: #ab8e6166;
            font-family: MUJIFont2020;
            font-weight: 700;
            font-size: 28rpx;
            color: #ffffff;
            line-height: 32rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            writing-mode: vertical-rl;
            letter-spacing: 4rpx;
            border-radius: 16rpx 0rpx 0rpx 16rpx;
        }

        .prizeDraw-title1 {
            height: 40rpx;
            font-family: MUJIFont2020;
            font-weight: 500;
            font-size: 24rpx;
            line-height: 40rpx;
            letter-spacing: 1px;
            margin-top: 31rpx;
        }

        .title {
            margin-left: 62rpx;
        }

        .prizeDraw-title2 {
            height: 40rpx;
            font-weight: 300;
            font-size: 24rpx;
            line-height: 40rpx;
            letter-spacing: 1px;
        }

        .tips {
            display: flex;
            align-items: center;
            justify-content: center;
            // margin-left: 70rpx;
            margin-top: 139rpx;

            &-title {
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            .tips-img {
                width: 74rpx;
                height: 76rpx;
                margin-right: 21rpx;

                image {
                    width: 100%;
                    height: 100%;
                }
            }

            .prizeDraw-title3 {
                font-weight: 500;
                font-size: 36rpx;
                color: #2e2e2e;
                line-height: 52rpx;
                letter-spacing: 1px;
                text-align: center;
            }

            .prizeDraw-title4 {
                font-weight: 700;
                font-size: 56rpx;
                color: #2e2e2e;
                margin-top: 12rpx;
                line-height: 72rpx;
                letter-spacing: 1px;
                text-align: center;
            }
        }

        .gift-wrap {
            display: flex;
            align-items: center;
            justify-content: center;
            // flex-direction: column-reverse;
            flex-wrap: wrap;
            // justify-content: end;
            // align-items: flex-end;
            // height: 606rpx;
            margin-top: 88rpx;
            // padding-left: 29rpx;
            // padding-right: 19rpx;

            height: 604rpx;
            // margin-top: 63rpx;
            // padding-left: 29rpx;
            // padding-right: 19rpx;

            .gift-item {
                width: 240rpx;
                height: 280rpx;
                background: #f8f6ef;
                // margin: 10rpx;
                position: relative;

                .active::before {
                    content: "";
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: #c8b49a;
                    /*蒙版颜色和透明度*/
                }

                .img {
                    width: 520rpx;
                    height: 602rpx;
                }
            }
        }
    }

    .gift-desc {
        margin-top: 102rpx;
        display: flex;
        // flex-direction: column-reverse;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
        // height: 192rpx;
        // margin-top: 56rpx;
        width: 100%;

        &-container {
            width: 100%;
            text-align: center;
        }
        &-content {
            width: 100%;
            text-align: center;
            color: #756453;
        }
        &-title {
            font-family: MUJIFont2020;
            font-weight: 400;
            font-size: 24rpx;
            color: #756453;
            line-height: 40rpx;
            letter-spacing: 1rpx;
            text-align: center;
        }

        &-height-light {
            font-family: MUJIFont2020;
            font-weight: 700;
            font-size: 24rpx;
            line-height: 40rpx;
            letter-spacing: 0%;
            vertical-align: middle;
        }

        &-tips {
            font-family: MUJIFont2020;
            font-weight: 400;
            font-size: 20rpx;
            color: #756453;
            line-height: 36rpx;
            letter-spacing: 1rpx;
            text-align: center;
            margin-top: 12rpx;
        }
        &-button {
            font-family: MUJIFont2020;
            font-weight: 400;
            font-size: 24rpx;
            line-height: 24rpx;
            letter-spacing: 0%;
            vertical-align: middle;
            text-decoration: underline;
            text-decoration-style: solid;
            text-decoration-skip-ink: auto;
            margin-top: 42rpx;
        }
        &-button2 {
            font-family: MUJIFont2020;
            font-weight: 400;
            font-size: 24rpx;
            line-height: 24rpx;
            letter-spacing: 0%;
            vertical-align: middle;
            text-decoration: underline;
            text-decoration-style: solid;
            text-decoration-skip-ink: auto;
            margin-top: 32rpx;
        }
    }

    // .bottom-box {
    //   margin-top: 32rpx;
    //   margin-bottom: 115rpx;
    //   display: flex;
    //   justify-content: center;
    // }

    .bottom-box {
        margin-top: 150rpx;
        margin-bottom: 154rpx;
        display: flex;
        justify-content: center;
    }

    .prize-once-again {
        width: 130rpx;
        height: 152rpx;
        position: absolute;
        right: 32rpx;
        image {
            width: 130rpx;
            height: 152rpx;
        }
    }
}

.image-box {
    width: 520rpx;
}

.image-container {
    width: 520rpx;
    height: 603rpx;
    perspective: 1000rpx;
    position: relative;
}

.image-inner {
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    position: absolute;
}

image {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
}

.gift-box {
    position: absolute;
    z-index: 100;
    width: 520rpx;
    height: 603rpx;
}

.muji-logo {
    width: 112rpx;
    height: 72rpx;
    left: 204.9rpx;
    top: 105.4rpx;
}

.fireworks {
    width: 374rpx;
    height: 160rpx;
    bottom: 190rpx;
    left: 77rpx;
}

.gift-item {
    width: 240rpx;
    height: 280rpx;
    left: 142rpx;
    top: 216rpx;
}

.front {
    transform: rotateY(0deg);
}

.back {
    transform: rotateY(-180deg);
}

.text-wrapper {
    opacity: 1;
}

.hide-text-wrapper {
    opacity: 0;
    transform: scale(0.1);
    transform-origin: center;
    // transition: all 0.1s
}
