package com.dz.common.core.fegin.${typeName};

import com.dz.ms.${typeName}.dto.${objectName}DTO;
import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = ServiceConstant.${serviceName}_SERVICE_NAME, contextId = "${objectName}FeignClient")
public interface ${objectName}FeignClient {

    /**
     * 分页查询${tableComment}
     * @param param
     * @return result<PageInfo<${objectName}DTO>>
     */
    @GetMapping(value = "/${tableName}/list")
    public Result<PageInfo<${objectName}DTO>> get${objectName}List(@ModelAttribute ${objectName}DTO param<#if hasAppId>, @RequestParam("tenantId") Long tenantId</#if>);

    /**
     * 根据ID查询${tableComment}
     * @param id
     * @return result<${objectName}DTO>
     */
    @GetMapping(value = "/${tableName}/info")
    public Result<${objectName}DTO> get${objectName}ById(@RequestParam("${primarkUpper}") ${primarkMType} ${primarkUpper}<#if hasAppId>, @RequestParam("tenantId") Long tenantId</#if>);

    /**
     * 保存${tableComment}
     * @param param
     * @return result<${primarkMType}>
     */
    @PostMapping(value = "/${tableName}/save")
    public Result<${primarkMType}> save(@RequestBody ${objectName}DTO param, @RequestParam("uid") Long uid<#if hasAppId>, @RequestParam("tenantId") Long tenantId</#if>);

    /**
    * 根据ID删除${tableComment}
    * @param id
    * @return result<Boolean>
    */
    @PostMapping(value = "/${tableName}/delete")
    public Result<Boolean> delete${objectName}ById(@RequestParam("${primarkUpper}") ${primarkMType} ${primarkUpper}<#if hasAppId></#if>);

}
