package com.dz.ms.basic.service;

import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.dto.DownloadDTO;
import com.dz.common.core.dto.DownloadQueryParamDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.ms.basic.entity.DownloadCenter;

import java.util.List;

/**
 * 下载中心表接口
 *
 * <AUTHOR>
 * 2022/08/15 16:00
 */
public interface DownloadCenterService {

    /**
     * 新增下载中心表
     *
     * @param downloadCenter 业务参数
     */
    void save(DownloadCenter downloadCenter);

    /**
     * 新增下载中心表
     *
     * @param downloadAddParamDTO 业务参数
     */
    void save(DownloadAddParamDTO downloadAddParamDTO, CurrentUserDTO commonLoginDTO);

    /**
     * del
     *
     * @param id 主键id
     */
    void delete(Long id);

    /**
     * 标记
     *
     * @param id
     */
    void mark(Long id);

    /**
     * 分页查询
     *
     * @param paramDTO 查询条件
     * @return DownloadDTO
     */
    PageInfo<DownloadDTO> page(DownloadQueryParamDTO paramDTO);

    /**
     * n天未下载，标记为已过期
     */
    void scarp(Integer scarpDays);

    /**
     * 查询n天之前的数据
     *
     * @param scarpDays
     * @return
     */
    List<DownloadDTO> listScarp(Integer scarpDays);

    /**
     * 清除oss记录
     *
     * @param downloadDTO
     */
    void updateClear(DownloadDTO downloadDTO);
}