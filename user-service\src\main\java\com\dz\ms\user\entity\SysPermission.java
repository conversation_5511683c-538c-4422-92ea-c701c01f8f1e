package com.dz.ms.user.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.io.Serializable;
import java.util.Date;

/**
 * 权限功能
 * @author: Handy
 * @date:   2022/07/25 21:43
 */
@Getter
@Setter
@NoArgsConstructor
@Table("权限功能")
@TableName(value = "sys_permission")
public class SysPermission implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "权限ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = false,comment = "权限编号",isIndex = true)
    private String code;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,defaultValue = "0",comment = "父节点ID",isIndex = true)
    private Long parentId;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = false,comment = "权限类型 1模块 2页面 3功能")
    private Integer permitType;
    @Columns(type = ColumnType.VARCHAR,length = 30,isNull = false,comment = "权限名称")
    private String permitName;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "页面/接口地址")
    private String url;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "权限描述")
    private String permitDesc;
    @Columns(type = ColumnType.INT,length = 10,isNull = true,defaultValue = "0",comment = "菜单显示排序")
    private Integer displaySort;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = false,defaultValue = "0",comment = "是否有子菜单")
    private Integer hasChild;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = false,defaultValue = "1",comment = "平台类型 1会小 2企微 3商城")
    private Integer platform;
    @Columns(type = ColumnType.VARCHAR,length = 200,isNull = true,defaultValue = "",comment = "路由地址")
    private String path;
    @Columns(type = ColumnType.VARCHAR,length = 255,isNull = true,comment = "组件路径")
    private String component;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = false,defaultValue = "1",comment = "是否为外链（0是 1否）")
    private Integer isFrame;
    @Columns(type = ColumnType.VARCHAR,length = 10,isNull = true,defaultValue = "",comment = "类型（M目录 C菜单 F按钮）")
    private String menuType;
    @Columns(type = ColumnType.VARCHAR,length = 10,isNull = true,defaultValue = "0",comment = "显示状态（0显示 1隐藏）")
    private String visible;
    @Columns(type = ColumnType.VARCHAR,length = 10,isNull = true,defaultValue = "0",comment = "菜单状态（0显示 1隐藏）")
    private String status;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,defaultValue = "#",comment = "菜单图标")
    private String icon;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = true,defaultValue = "2",comment = "菜单类型 1:小程序 2:PC")
    private Integer type;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = true,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    public SysPermission(Long id, String code, Long parentId, Integer permitType, String permitName, String url, String permitDesc, Integer displaySort, Integer hasChild, Integer platform) {
        this.id = id;
        this.code = code;
        this.parentId = parentId;
        this.permitType = permitType;
        this.permitName = permitName;
        this.url = url;
        this.permitDesc = permitDesc;
        this.displaySort = displaySort;
        this.hasChild = hasChild;
        this.platform = platform;
    }

}
