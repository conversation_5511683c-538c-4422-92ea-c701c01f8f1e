package com.dz.common.core.dto.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.jetbrains.annotations.NotNull;


@Data
@AllArgsConstructor
public class InventoryParamDTO {

    @NotNull
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @NotNull
    @ApiModelProperty(value = "货架商品ID")
    private Long shelfProductId;
    @NotNull
    @ApiModelProperty(value = "库存数量")
    private Integer num;
    @NotNull
    @ApiModelProperty(value = "是否增加库存 1增加 2减少")
    private Integer isAdd;
    @ApiModelProperty(value = "活动ID")
    private Long campaignId;
    @ApiModelProperty(value = "活动规则ID")
    private Long ruleId;
}
