const app = getApp()
import {
  memberqrcodereport
} from '../../api/index'
Page({

  /**
   * 页面的初始数据
   */
  data: {

  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow(options) {
    let path = Object.keys(options).reduce((total, key) => {
      return total + `&${key}=${options[key]}`
    }, '')
    let result = app.globalData.userInfo.isMember > 0
    let currentLevel = options.couponStatusTab
    if (options.couponStatusTab && options.q) {
      let decodedQ = decodeURIComponent(options.q);
      let qrcodeId = decodedQ.split('id=')[1].split('&')[0];
      let qrcodeSn = decodedQ.split('sn=')[1];
      let memberCode = app.globalData.userInfo.cardNo

      if (result) { //注册用户上报加跳转我的礼券
        if (memberCode && qrcodeId && qrcodeSn) {
          memberqrcodereport({
            memberCode,
            qrcodeId,
            qrcodeSn
          }).then(res => {
            wx.$mp.redirectTo({
              url: `/pages/myCoupon/myCoupon?currentLevel=${currentLevel}&${path}`
            })
          }).catch(err => { //如果上报失败就回到首页
            wx.$mp.navigateTo({
              url: `/pages/index/index`
            })
          })
        }

      } else { //未注册用户
        wx.$mp.navigateTo({
          url: `/pages/register/register?qrcodeId=${qrcodeId}&qrcodeSn=${qrcodeSn}`
        })
      }
    } else {
      //注册用户跳转我的礼券
      if (result) {
        wx.$mp.redirectTo({
          url: `/pages/myCoupon/myCoupon?currentLevel=${currentLevel}&${path}`
        })
      } else {
        wx.$mp.navigateTo({
          url: '/pages/register/register'
        })
      }
    }

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },
})
