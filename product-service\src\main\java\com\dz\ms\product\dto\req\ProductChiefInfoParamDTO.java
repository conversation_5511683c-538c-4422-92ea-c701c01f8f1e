package com.dz.ms.product.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 商品搜索字段
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "商品搜索字段")
public class ProductChiefInfoParamDTO extends CrmProductListParamDTO {

    @ApiModelProperty(value = "商品ID列表")
    private List<Long> productIdList;
    @ApiModelProperty(value = "是否显示标签")
    private Boolean showTags = false;
    @ApiModelProperty(value = "排序:1正序 2倒叙")
    private Integer orderBy;
}
