// components/modal/modal.js
import {
  getTemplateById,
} from '../../../api/index.js'
const app = getApp()
Component({
  properties: {
    // 显示弹窗
    show: {
      type: Boolean,
      value: false,
      observer(value) {
        // 获取弹窗消息
        if (value) {
          this.getInfo()
        }
      }
    },
    modalId: {
      type: String,
      value: '0',
    },
    // 弹窗下标
    index: {
      type: Number
    }
  },
  data: {
    info: {},
    rpx: app.globalData.rpx
  },
  methods: {
    getInfo() {
      getTemplateById({
        id: this.data.modalId
      }).then(res => {
        res.data.content = res.data?.content ? JSON.parse(res.data.content) : {}
        res.data.actionLinks = res.data.content.actionLinks; // 逻辑页面
        res.data.navSetting = res.data.content.navSetting; // 导航
        res.data.pageSetting = res.data.content.pageSetting; // 页面设置
        res.data.componentSetting = res.data.content.componentSetting; // 组件设置
        this.setData({
          info: res.data
        })
      })
    },
    // 返回顶部
    goTop() {
      this.setData({
        view: 'top',
      })
    },
    // scroll 锚点滚动跳转
    goAchor(e) {
      this.setData({
        view: 'view' + e.detail
      })
    },
    // 对弹窗的操作
    goModal(e) {
      console.log(e)
      let {
        modalIndex,
        operateType
      } = e.detail;
      if (modalIndex < 0) {
        this.triggerEvent('close', {
          index: this.data.index,
          closeBtn: false // 不是点击关闭按钮的关闭本弹窗
        })
      } else {
        this.triggerEvent('goModal', {
          modalIndex,
          operateType
        })
      }
    },
    // 分享
    goShare(e) {
      let {
        shareTitle,
        shareImg,
        sharePath
      } = e.detail
      this.triggerEvent('goShare', {
        shareTitle,
        shareImg,
        sharePath
      })
    },
    // 禁止穿透
    touchmove() {
      return false
    },
    // 禁止穿透
    touchmove1() {
      return true
    },
    // 关闭按钮
    close() {
      this.triggerEvent('close', {
        index: this.data.index,
        closeBtn: true // 是点击关闭按钮的关闭本弹窗
      })
    },
  }
})
