// 所有的命名必须全局唯一
import service from '@/utils/request.js'


// 获取图形验证码
export function getimagecode(data = {}) {
  return service({
    url: '/crm/user/login/imagecode',
    method: 'get',
    data
  })
}
//登录
export function login(data) {
  return service({
    url: '/crm/user/login/password',
    method: 'post',
    data: data,
  })
}
export function strongAuthenticate(data) {
  return service({
    url: '/crm/user/login/strongAuthenticate',
    method: 'post',
    data: data,
  })
}
// 获取用户详细信息
export function getInfoUser() {
  return service({
    url: '/crm/user/sys_user/identity',
    method: 'get',

  })
}

// 退出方法
export function logout(data = {}) {
  return service({
    url: '/crm/user/logout',
    method: 'get',
    data
  })
}

// 获取路由信息
export function getInfoMenu() {
  return service({
    url: '/crm/user/sys_role/getRouterList',
    method: 'get',
  })
}

//上传图片
export function fileUpload(data = {}) {
  return service({
    url: '/crm/basic/file/upload',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data
  })
}

// 获取省市区
export function getAreaList() {
  return service({
    url: '/system/area/list',
    method: 'get',
  })
}


// 修改密码

export function password_update(data) {
  return service({
    url: '/crm/user/password_update',
    method: 'post',
    data
  })
}




//  手机号验证码校验
export function passwordResetgetCode(data = {}) {
  return service({
    url: `/auth/passwordReset/getCode`,
    method: 'post',
    data
  })
}
// 校验验证码 密码 邮箱验证码 
export function passwordResetcheckCode(data = {}) {
  return service({
    url: `/auth/passwordReset/checkCode`,
    method: 'post',
    data
  })
}



export function getMenuTree(data = {}) {
  return service({
    url: '/crm/user/sys_user/getInfoByMenuName?menuName=' + data.menuName + '&menuType=' + data.menuType,
    method: 'get',

  })
}
