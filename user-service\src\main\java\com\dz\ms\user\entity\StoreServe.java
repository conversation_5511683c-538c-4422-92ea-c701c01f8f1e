package com.dz.ms.user.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.io.Serializable;
import java.util.Date;

/**
 * 门店服务
 * @author: yibo
 * @date:   2024/11/19 17:20
 */
@Getter
@Setter
@NoArgsConstructor
@Table("门店服务")
@TableName(value = "store_serve")
public class StoreServe implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "主键id")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = true,comment = "门店id")
    private Long storeId;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = true,comment = "品类id")
    private Long serveId;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = true,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = true,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = true,comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    public StoreServe(Long id, Long storeId, Long serveId) {
        this.id = id;
        this.storeId = storeId;
        this.serveId = serveId;
    }

}
