<my-page overallModal="{{overallModal}}" loading="{{loading1}}">
  <view class="page-container" id="view">
    <custom-header background="transparent" type="{{1}}" color="black" />
    <view class="report-container" id="view" style="background-image: url({{$cdn}}/MiKangCampaign/mk-report-bg.png);">
      <image class="header-img" mode="widthFix" src="{{$cdn}}/MiKangCampaign/mk-report-head-text.png"></image>
      <view class="report-image-box">
        <image class="report-image-left" style="height: 488rpx;width: 390rpx;" src="{{$cdn}}/MiKangCampaign/mk-product.png"></image>
        <view class="report-image-right-box">
          <view class="right-box before-box" style="background-image: url({{$cdn}}/MiKangCampaign/mk-user-image-bg.png);">
            <view class="user-photo-box">
              <image class="user-photo" mode="aspectFit" src="{{daysInfo.materialUrlDay1 || ($cdn + '/MiKangCampaign/mk-product.png')}}"></image>
            </view>
            <image class="report-icon" mode="widthFix" src="{{$cdn}}/MiKangCampaign/mk-before-icon.png"></image>
          </view>
          <view class="right-box after-box" style="background-image: url({{$cdn}}/MiKangCampaign/mk-user-image-bg.png);">
            <view class="user-photo-box">
              <image class="user-photo" mode="aspectFit" src="{{daysInfo.materialUrlDay7 || ($cdn + '/MiKangCampaign/mk-product.png')}}"></image>
            </view>
            <image class="report-icon" mode="widthFix" src="{{$cdn}}/MiKangCampaign/mk-after-icon.png"></image>
          </view>
        </view>
      </view>
      <view class="rating-box">
        <view class="rating">
          {{daysReportInfo.satisfaction}}
          <view class="rating-unit">分</view>
        </view>
        <view class="mk-icon-box">
          <block wx:for="{{ratingLight}}" wx:for-item="item" wx:for-index="index" wx:key="index">
            <image class="rating-icon" mode="widthFix" src="{{$cdn}}/MiKangCampaign/mk-rating-ligth.png"></image>
          </block>
          <block wx:if="{{ratingHalf}}">
            <image wx:for="{{ratingHalf}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2" class="rating-icon" mode="widthFix" src="{{$cdn}}/MiKangCampaign/mk-rating-half.png"></image>
          </block>
          <block wx:if="{{ratingDark}}">
            <image wx:for="{{ratingDark}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2" class="rating-icon" mode="widthFix" src="{{$cdn}}/MiKangCampaign/mk-rating-dark.png"></image>
          </block>
        </view>

        <view class="user-common-title">
          <image class="user-common-img" mode="widthFix" src="{{$cdn}}/MiKangCampaign/mk-user-common.png"></image>
        </view>
      </view>
      <view class="user-common-box">
        <image class="rader-img" src="{{raderUrl}}"/>
        <view class="comment-box">
          <image class="comment-icon" src="{{$cdn}}/MiKangCampaign/mk-left-colon.png"></image>
          <text class="comment">{{daysReportInfo.feeling}}</text>
          <image class="comment-icon" src="{{$cdn}}/MiKangCampaign/mk-right-colon.png"></image>
        </view>
      </view>
      <!-- <view class="improve-box">
          <block wx:for="{{daysInfo.improve}}" wx:for-item="improve" wx:for-index="index3" wx:key="index3">
            <view class="improve-card" style="background-image: url({{$cdn}}/MiKangCampaign/mk-note-bg.png);">
              # <text class="improve-text">{{improve}}</text>
            </view>
          </block>
        </view> -->
      <!-- <view class="qr-code">
          <image src="{{'data:image/png;base64,' + code_base64}}"></image>
        </view> -->
      <view class="btn-box">
        <button bindtap="saveImage">保存海报</button>
        <button wx:if="{{surplusCount>0}}" bindtap="goPrize">抽取惊喜礼品</button>
        <button wx:else bindtap="share">{{!isShare?'分享可再次抽奖':'分享好友'}}</button>
      </view>
    </view>

    <canvas class="page-canvas" canvas-id="myCanvas" id="myCanvas" type="2d"></canvas>
    <canvas class="rader-canvas" canvas-id="raderCanvas" id="raderCanvas" type="2d"></canvas>
    <share-poster ImgStyle="{{'v2'}}" src="{{reportUrl}}" show="{{show}}" bindsuccess="success" bindclose="close"></share-poster>
    <view wx:if="{{show}}" class="share_mask"></view>
    <!-- 第一次分享 恭喜或得一次抽奖机会 -->
    <Popup isShow="{{showPopup}}" info="{{infoLottery}}" bindclose="closeShowPopup"></Popup>
  </view>
</my-page>