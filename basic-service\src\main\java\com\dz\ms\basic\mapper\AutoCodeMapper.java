package com.dz.ms.basic.mapper;

import com.dz.ms.basic.dto.AutoCodeColumnDTO;
import com.dz.ms.basic.dto.AutoCodeTableDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AutoCodeMapper {

    List<String> getAllDatabase();

    List<AutoCodeTableDTO> getTableNameBykeyword(@Param("database") String database, @Param("keyword") String keyword);

    List<AutoCodeColumnDTO> getTableInfoByTableName(@Param("database") String database, @Param("tableName") String tableName);

}
