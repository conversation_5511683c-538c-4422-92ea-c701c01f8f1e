package com.dz.common.core.dto.adaptor;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/7
 */
@Data
public class SiftEnrollDTO {

    @ApiModelProperty(value = "活动编号", required = true)
    private String campaignCode;

    @ApiModelProperty(value = "各渠道抽取人数map", required = true)
    private Map<String, Map<String, Integer>> jobParams;

}
