package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 核心礼遇其他配置
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table("核心礼遇其他配置")
@TableName(value = "t_gift_config_other")
public class GiftConfigOther implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 礼遇类型：1新人礼配置，2二回礼配置，3生日礼配置，4升级礼配置，5无以上礼遇可领取且券列表有有效券时，6无礼遇、无可用券时，7无礼遇、有可使用的商品券时
     */
    private Integer giftType;
    /**
     * 会员等级编号 1普通会员，2铜级会员,3银级会员,4金级会员
     */
    private Integer levelId;
    /**
     *弹窗配置
     */
    private String ballImg;
    /**
     *跳转链接
     */
    private String ballJump;
    /**
     *关联活动id
     */
    private String activityId;
    /**
     *创建人
     */
    private String createAt;
    /**
     *创建时间
     */
    private Date createTime;
    /**
     *修改人
     */
    private String updateAt;
    /**
     *修改时间
     */
    private Date updateTime;
    /**
     *渠道
     */
    private Long tenantId;
}
