// 小程序页面路径映射
const HREF_NAME_MAP_PAGE_PATH = {
  home: '/pages/index/index',
  exchange: '/pages/exchange/exchange',
  qrCode: '/pages/qrCode/qrCode',
  life: '/pages/life/life',
  more: '/pages/more/more',
  webView: '/pages/webView/webView',
  productSearch: '/pages/productSearch/productSearch'
}

// tabBar相关
const TAB_BAR = {
  home: HREF_NAME_MAP_PAGE_PATH.home,
  exchange: HREF_NAME_MAP_PAGE_PATH.exchange,
  qrCode: HREF_NAME_MAP_PAGE_PATH.qrCode,
  life: HREF_NAME_MAP_PAGE_PATH.life,
  more: HREF_NAME_MAP_PAGE_PATH.more
}
Object.entries(TAB_BAR).forEach((v) => {
  const key = v[0]
  const val = v[1]
  TAB_BAR[val] = key
})

// 用户信息中的变量参数
// 富文本中的变量参数为 ##{{name}}##
const USER_INFO_VARIABLE = [{
    id: 1,
    name: '微信昵称',
    key: 'username'
  },
  {
    id: 2,
    name: '注册时间',
    key: 'registerTime'
  },
  {
    id: 3,
    name: '手机号',
    key: 'mobile'
  },
  {
    id: 4,
    name: '会员等级',
    key: 'cardLevelName'
  },
  {
    id: 5,
    name: '生日',
    key: 'birthday'
  },
  {
    id: 6,
    name: '会员卡号',
    key: 'cardNo'
  },
  {
    id: 7,
    name: '所在省',
    key: 'province'
  },
  {
    id: 8,
    name: '所在市',
    key: 'city'
  },
  {
    id: 9,
    name: '称谓',
    key: 'gender'
  }
]

wx.$contants = {
  SHARE_DATA_DEFAULT_TITLE: 'MUJI passport会员，畅享购物福利',
  HREF_NAME_MAP_PAGE_PATH,
  TAB_BAR,
  USER_INFO_VARIABLE
}
