<template>
  <!--  <downloadFile
              type="primary"
              file-name="下载文件名称"
              file-type="下载文件类型  excel zip image"
              :api="unionid_template_export" // 封装的接口api
              :disabled="" // 按钮是否置灰
              >下载模板</downloadFile>
             -->
  <a-button :type="type" :disabled="disabled" @click="exportData">
    <slot></slot>
  </a-button>
</template>
<script setup>

import { reactive, toRefs, ref, nextTick, watch, computed } from 'vue';
import { message } from 'ant-design-vue';
import { useGlobalStore } from '@/store'
import { useRoute, useRouter } from 'vue-router'
import { downloadBlob } from '@/utils/tools.js'
import dayjs from 'dayjs'
import _ from "lodash"

// 固定应用
const global = useGlobalStore()
const $route = useRoute()
const $router = useRouter()
const { query } = useRoute()
const formRef = ref()

const emit = defineEmits(["ok"]);
const props = defineProps({
  // 接口
  api: {
    type: Function || Object,
    required: true
  },
  // 接口参数
  params: {
    type: Object || Function,
    default() {
      return {}
    }
  },
  //按钮类型
  type: {
    type: String,
    default: 'primary'
  },
  //下载文件名，需含文件后缀名
  fileName: {
    type: String,
    default: '下载.xlsx',
  },
  //文件类型  excel  zip  image
  fileType: {
    type: String,
    default: 'excel'
  },
  // 按钮是否置灰
  disabled: {
    type: Boolean,
    default: false
  },
})

// 导出数据
const exportData = () => {
  let params;
  if (typeof props.params === 'function') {
    params = props.params()
  } else {
    params = props.params
  }
  props?.api(params).then(res => {
    let downloadName = props.fileName || res.headers['download-name'] || decodeURIComponent(res.headers['content-disposition']?.split('filename=')[1]) || '下载'
    downloadBlob(res.data, downloadName, props.fileType, () => {
      emit('ok')
    })
  })
}

</script>
<style lang="scss" scoped>
</style>
