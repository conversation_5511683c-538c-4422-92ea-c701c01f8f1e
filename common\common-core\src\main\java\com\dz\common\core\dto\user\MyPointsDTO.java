package com.dz.common.core.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 我的积分DTO
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "我的积分")
public class MyPointsDTO {

    @ApiModelProperty(value = "可用积分")
    private Integer pointsNum;
    @ApiModelProperty(value = "本月即将过期积分")
    private Integer expirePointsNum;

}
