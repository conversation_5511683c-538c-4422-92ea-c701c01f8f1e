package com.dz.ms.basic.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 隐私条款DTO
 * @author: Handy
 * @date:   2023/05/17 16:50
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "隐私条款")
public class PrivacyPolicyDTO extends BaseDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "隐私条款名称")
    private String name;
    @ApiModelProperty(value = "隐私条款标题")
    private String policyTitle;
    @ApiModelProperty(value = "更新标题")
    private String newTitle;
    @ApiModelProperty(value = "隐私条款版本")
    private String policyVersion;
    @ApiModelProperty(value = "隐私条款内容")
    private String content;
    @ApiModelProperty(value = "关联自定义")
    private Integer urlSum;
    @ApiModelProperty(value = "发布时间")
    private Date pushTime;
    @ApiModelProperty(value = "发布状态 0待发布 1发布中 2已结束")
    private Integer pushStatus;
    @ApiModelProperty(value = "启停用状态 0启用 1停用")
    private Integer status;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "更新人")
    private Long modifier;
    @ApiModelProperty(value = "更新时间")
    private Date modified;

}
