package com.dz.ms.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 会员等级
 * @author: zlf
 * @date:   2023/02/13 13:36
 */
@Getter
@Setter
@NoArgsConstructor
@Table("会员等级")
@TableName(value = "grade_info")
public class GradeInfo implements Serializable {

    private static final long serialVersionUID = 1L;


    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "等级ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR,length = 64,isNull = true,comment = "会员等级编码")
    private String gradeCode;
    @Columns(type = ColumnType.VARCHAR,length = 64,isNull = true,comment = "会员等级名称")
    private String gradeName;
    @Columns(type = ColumnType.VARCHAR,length = 512,isNull = true,comment = "会员等级样式JSON")
    private String styleJson;
    @Columns(type = ColumnType.INT,length = 10,isNull = true,defaultValue = "0",comment = "会员等级排序")
    private Integer sort;
    @Columns(type = ColumnType.INT,length = 10,isNull = true,comment = "等级消费金额")
    private Integer expenseAmount;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = true,defaultValue = "0",comment = "会员等级状态 0停用 1启用")
    private Integer state;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = true,comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = false,defaultValue = "0",comment = "是否删除 0未删除 1已删除")
    @TableLogic
    private Integer isDeleted;

    public GradeInfo(Long id, String gradeCode, String gradeName, String styleJson, Integer sort, Integer expenseAmount, Integer state) {
        this.id = id;
        this.gradeCode = gradeCode;
        this.gradeName = gradeName;
        this.styleJson = styleJson;
        this.sort = sort;
        this.expenseAmount = expenseAmount;
        this.state = state;
    }

}
