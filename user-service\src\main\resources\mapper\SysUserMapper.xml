<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.user.mapper.SysUserMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    id,
  	    username,
  	    password,
  	    salt,
  	    realname,
  	    mobile,
  	    state,
  	    change_password,
  	    is_admin,
  	    tenant_id,
  	    created,
  	    creator,
  	    modified,
  	    modifier,
  	    is_deleted
    </sql>

	<!-- 根据账号查询 -->
	<select id="getUserByUsername" resultType="com.dz.ms.user.entity.SysUser">
		select
		<include refid="Base_Column_List" />
		from sys_user
		where username = #{username}
		AND tenant_id = #{tenantId}
		AND is_deleted = 0
	</select>

	<!-- 根据userIdList查询系统用户信息 -->
	<select id="selectByIds" resultType="com.dz.ms.user.entity.SysUser">
		select
		<include refid="Base_Column_List" />
		from sys_user
		where id in
		<foreach collection="uids" index="index" item="item" separator="," open="(" close=")">
			#{item}
		</foreach>
		AND tenant_id = #{tenantId}
	</select>
	
	<select id="selPageList" resultType="com.dz.common.core.dto.user.SysUserDTO">
		select
			username,
			realname,
			mobile,
			is_admin,
			created
		from sys_user
		where is_deleted = 0
		AND `state` = 1
		AND tenant_id = #{tenantId}
	</select>

</mapper>
