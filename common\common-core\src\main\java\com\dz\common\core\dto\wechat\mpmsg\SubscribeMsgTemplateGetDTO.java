package com.dz.common.core.dto.wechat.mpmsg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 已添加小程序订阅消息模板信息
 * @author: Handy
 * @date:   2023/07/07 14:22
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "已添加小程序订阅消息模板信息")
public class SubscribeMsgTemplateGetDTO {

    @ApiModelProperty(value = "添加至帐号下的模板ID，发送小程序订阅消息时所需")
    private String priTmplId;
    @ApiModelProperty(value = "模版标题")
    private String title;
    @ApiModelProperty(value = "模版内容")
    private String content;
    @ApiModelProperty(value = "模板内容示例")
    private String example;
    @ApiModelProperty(value = "模版类型 1一次性订阅 2长期订阅")
    private Integer type;

}
