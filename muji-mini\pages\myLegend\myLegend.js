// pages/myLegend/myLegend.ts
// import dayjs from "../../utils/dayjs.min";
const dayjs = require('../../utils/dayjs.min')
import {
  mileageRecords
} from '../../api/index'
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    legendList: [],
    year: new Date().getFullYear(),
    endDate: new Date().getFullYear(),
    startDate: dayjs().subtract(3, 'year').format('YYYY'),
    numberData: 0,
    pageSize: 10,
    pageNum: 1,
    count: 0,
    loading: false,
    hasNextPage: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad() {
    this.setData({
      loading: true,
      // hasNextPage:true
    })
    await app.getUserInfo()
    this.setData({
      numberData: app.globalData.userInfo.currentMileage || 0
    })
    this.getLsit()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {

  },
  getLsit() {
    mileageRecords({
      pageSize: this.data.pageSize,
      pageNum: this.data.pageNum,
      year: this.data.year
    }).then((res) => {
      const newData = res.data.list
      let legendListArr = []
      if (this.data.pageNum == 1) {
        legendListArr = newData;
      } else {
        legendListArr = this.data.legendList.concat(newData);
      }
      this.setData({
        legendList: legendListArr || [],
        count: res.data.count,
        hasNextPage: res.data.hasNextPage
      })
    }).finally(() => {
      this.setData({
        loading: false
      })
    })
  },
  onReachBottom() {
    const {
      legendList,
      hasNextPage
    } = this.data
    console.log(hasNextPage);
    if (!hasNextPage) return
    let pageNum = Math.ceil(legendList.length / 10)
    this.setData({
      'pageNum': pageNum + 1
    })
    this.getLsit()

  },
  birthdayChange(event) {

    const {
      value
    } = event.detail
    console.log(value);
    this.setData({
      year: value
    })
    this.setData({
      pageNum: 1,

    })
    this.getLsit()
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },
})
