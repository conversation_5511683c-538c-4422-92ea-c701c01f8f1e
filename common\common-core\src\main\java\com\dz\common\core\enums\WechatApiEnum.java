package com.dz.common.core.enums;

import com.dz.common.core.constants.CommonConstants;

public enum WechatApiEnum {

    API_AUTHORIZE_CODE(CommonConstants.WX_OPENAPI+ "/connect/oauth2/authorize","获取网页授权code",true),
    API_COMPONENT_ACCESS_TOKEN(CommonConstants.WX_MPAPI+"/cgi-bin/component/api_component_token","获取component_access_token",true),
    API_PRE_AUTH_CODE(CommonConstants.WX_MPAPI+"/cgi-bin/component/api_create_preauthcode","获取pre_auth_code接口",true),
    API_COMPONENT_LOGIN_PAGE("https://mp.weixin.qq.com/cgi-bin/componentloginpage","生成授权二维码",true),
    API_QUERY_AUTH(CommonConstants.WX_MPAPI+"/cgi-bin/component/api_query_auth","换取公众号或小程序的接口调用凭据和授权",true),
    API_GET_AUTH_INFO(CommonConstants.WX_MPAPI+"/cgi-bin/component/api_get_authorizer_info","获取授权公众号或小程序基本信息",true),
    API_AUTH_TOKEN(CommonConstants.WX_MPAPI+"/cgi-bin/component/api_authorizer_token","获取（刷新）授权公众号的接口调用凭据（令牌）",true),
    API_SET_SERVER_DOMAIN(CommonConstants.WX_MPAPI+"/wxa/modify_domain","设置小程序服务器域名",true),
    API_SET_WEBVIEW_DOMAIN(CommonConstants.WX_MPAPI+"/wxa/setwebviewdomain","设置小程序业务域名",true),
    API_MINAPP_CATEGORY(CommonConstants.WX_MPAPI+"/wxa/get_category","获取授权小程序帐号的可选类目",true),
    API_MINAPP_COMMIT_CODE(CommonConstants.WX_MPAPI+"/wxa/commit","为授权的小程序帐号上传小程序代码",true),
    API_MINAPP_LATEST_AUDITSTATUS(CommonConstants.WX_MPAPI+"/wxa/get_latest_auditstatus","查询最新一次提交的审核状态",true),
    API_MINAPP_SUBMIT_AUDIT(CommonConstants.WX_MPAPI+"/wxa/submit_audit","将第三方提交的代码包提交审核",true),
    API_MINAPP_VERSION_RELEASE(CommonConstants.WX_MPAPI+"/wxa/release","发布已通过审核的小程序",true),
    API_MINAPP_UNDO_AUDIT(CommonConstants.WX_MPAPI+"/wxa/undocodeaudit","小程序审核撤回",true),
    API_MINAPP_REVERT_CODERELEASE(CommonConstants.WX_MPAPI+"/wxa/revertcoderelease","小程序版本回退",true),
    API_MINAPP_QUERY_QUOTA(CommonConstants.WX_MPAPI+"/wxa/queryquota","查询服务商的当月提审限额（quota）和加急次数",true),
    API_GET_MINAPP_QRCODE(CommonConstants.WX_MPAPI+"/wxa/getwxacodeunlimit","获取小程序码",true),
    API_GET_MINAPP_SCHEME(CommonConstants.WX_MPAPI+"/wxa/generatescheme","获取URL scheme",true,true,null,"openlink"),
    API_GET_TEMPLATE_DRAFT_LIST(CommonConstants.WX_MPAPI+"/wxa/gettemplatedraftlist","获取草稿箱内的所有临时代码草稿",true),
    API_GET_CODE_TEMPLATE_LIST(CommonConstants.WX_MPAPI+"/wxa/gettemplatelist","获取代码模版库中的所有小程序代码模版",true),
    API_ADD_CODE_TEMPLATE(CommonConstants.WX_MPAPI+"/wxa/addtotemplate","将草稿箱的草稿选为小程序代码模版",true),
    API_DELETE_CODE_TEMPLATE(CommonConstants.WX_MPAPI+"/wxa/deletetemplate","删除指定小程序代码模版",true),
    API_GET_MINIAPP_TEST_QRCODE(CommonConstants.WX_MPAPI+"/wxa/get_qrcode","获取小程序体验二维码",true),
    API_MINAPP_SUBSCRIBE_GET_CATEGORY(CommonConstants.WX_MPAPI+"/wxaapi/newtmpl/getcategory","小程序订阅消息-获取当前帐号所设置的类目信息",true),
    API_MINAPP_SUBSCRIBE_GET_PUBTEMPLATETITLES(CommonConstants.WX_MPAPI+"/wxaapi/newtmpl/getpubtemplatetitles","小程序订阅消息-获取所属类目的公共库模板标题列表",true,"&ids=%s&start=%d&limit=%d"),
    API_MINAPP_SUBSCRIBE_GET_PUBTEMPLATEKEYWORDS(CommonConstants.WX_MPAPI+"/wxaapi/newtmpl/getpubtemplatekeywords","小程序订阅消息-获取模板标题下的关键词库",true,"tid=%s"),
    API_MINAPP_SUBSCRIBE_ADD_TEMPLATE(CommonConstants.WX_MPAPI+"/wxaapi/newtmpl/addtemplate","小程序订阅消息-组合模板并添加到个人模板库",true,true,null,"priTmplId"),
    API_MINAPP_SUBSCRIBE_GET_TEMPLATE(CommonConstants.WX_MPAPI+"/wxaapi/newtmpl/gettemplate","小程序订阅消息-获取帐号下的模板列表",true),
    API_MINAPP_SUBSCRIBE_DEL_TEMPLATE(CommonConstants.WX_MPAPI+"/wxaapi/newtmpl/deltemplate","小程序订阅消息-删除帐号下的某个模板",true,true,"priTmplId=%s"),
    API_MINAPP_SUBSCRIBE_MESSAGE_SEND(CommonConstants.WX_MPAPI+"/cgi-bin/message/subscribe/send","小程序订阅消息-发送订阅消息",true,true,null),
    API_ADD_WECHAT_TEMPLATE_MSG(CommonConstants.WX_MPAPI+"/cgi-bin/template/api_add_template","添加公众号模板消息",true),
    API_GET_WECHAT_TEMPLATE_MSG(CommonConstants.WX_MPAPI+"/cgi-bin/template/get_all_private_template","获取公众号已添加的模板消息列表",true),
    API_DELETE_WECHAT_TEMPLATE_MSG(CommonConstants.WX_MPAPI+"/cgi-bin/template/del_private_template","删除公众号模板消息",true),
    API_SEND_WECHAT_TEMPLATE_MSG(CommonConstants.WX_MPAPI+"/cgi-bin/message/template/send","发送公众号模板消息",true),
    API_SEND_MINAPP_TEMPLATE_MSG(CommonConstants.WX_MPAPI+"/cgi-bin/message/wxopen/template/send","发送小程序模板消息",true),
    API_MINAPP_JSCODE_SESSION(CommonConstants.WX_MPAPI + "/sns/component/jscode2session","托管小程序登录 code 换取 session_key",true),
    API_MINAPP_JSCODE_GETSESSION(CommonConstants.WX_MPAPI + "/sns/jscode2session","小程序登录 code 换取 session_key",false,false,"appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",null,true),
    API_OAUTH2_ACCESS_TOKEN(CommonConstants.WX_MPAPI + "/sns/oauth2/access_token","公众号授权code换取access_token",true),
    API_OAUTH2_COMPONENT_ACCESS_TOKEN(CommonConstants.WX_MPAPI + "/sns/oauth2/component/access_token","公众号授权code换取access_token 第三方平台授权",true),
    API_GET_USER_INFO(CommonConstants.WX_MPAPI+"/cgi-bin/user/info","根据openId和access_token获取用户信息",true),
    API_GET_USERINFO_BY_ACCESS_TOKEN(CommonConstants.WX_MPAPI+"/sns/userinfo","通过网页授权access_token获取用户基本信息",true),
    API_GET_JSAPI_TICKET(CommonConstants.WX_MPAPI+"/cgi-bin/ticket/getticket","获取JSAPI_TICKET接口",true),
    API_SEND_MSG(CommonConstants.WX_MPAPI+"/cgi-bin/message/custom/send","发送消息",true),
    API_UPLOAD_IMG(CommonConstants.WX_MPAPI+"/cgi-bin/media/uploadimg","上传图片到微信，返回微信图片地址",true),
    API_WIDGET_IMPORT_ORDER(CommonConstants.WX_MPAPI + "/mall/importorder?action=add-order","导入已购订单",true),
    API_WIDGET_UPDATE_ORDER(CommonConstants.WX_MPAPI + "/mall/importorder?action=update-order","更新已购订单",true),
    API_WIDGET_DELETE_ORDER(CommonConstants.WX_MPAPI + "/mall/deleteorder","删除已购订单",true),
    API_WIDGET_IMPORT_SHOPPING_LIST(CommonConstants.WX_MPAPI + "/mall/addshoppinglist","导入想买清单",true),
    API_WIDGET_DELETE_SHOPPING_LIST(CommonConstants.WX_MPAPI + "/mall/deleteshoppinglist","删除想买清单",true),
    API_CARD_CREATE(CommonConstants.WX_MPAPI + "/card/create","创建卡券",true),
    API_QRCODE_CARD_CREATE(CommonConstants.WX_MPAPI + "/card/qrcode/create","创建卡券投放二维码接口",true),
    API_CARD_CODE_GET(CommonConstants.WX_MPAPI + "/card/code/get","根据Code查询卡券状态信息",true),
    API_CARD_CODE_CONSUME(CommonConstants.WX_MPAPI + "/card/code/consume","根据Code核销卡券",true),
    API_CARD_UPDATE(CommonConstants.WX_MPAPI + "/card/update","更改卡券信息",true),
    API_CARD_DELETE(CommonConstants.WX_MPAPI + "/card/delete","删除卡券",true),
    API_CARD_MODIFY_STOCK(CommonConstants.WX_MPAPI + "/card/modifystock","修改库存",true),
    API_CARD_CODE_DECRYPT(CommonConstants.WX_MPAPI + "/card/code/decrypt","Code解码接口",true),
    API_CARD_MEMBERCARD_ACTIVATETEMPINFO_GET(CommonConstants.WX_MPAPI + "/card/membercard/activatetempinfo/get","获取用户开卡时提交的信息（跳转型开卡组件）",true),
    API_CARD_MEMBERCARD_ACTIVATE(CommonConstants.WX_MPAPI + "/card/membercard/activate","激活用户领取的会员卡（跳转型开卡组件）",true),
    API_GET_MP_ACCCESS_TOKEN(CommonConstants.WX_MPAPI+"/cgi-bin/token","根据APPID,SECRET获取accesstoken",true,false),
    API_POST_MP_ACCCESS_TOKEN(CommonConstants.WX_MPAPI+"/cgi-bin/stable_token","根据APPID,SECRET获取accesstoken",true,false),
    API_GET_LIVE_LIST(CommonConstants.WX_MPAPI+"/wxa/business/getliveinfo","获取直播房间列表",true),
    API_GET_USER_PHONE(CommonConstants.WX_MPAPI+"/wxa/business/getuserphonenumber","获取手机号",true,true,null,"phone_info"),

    API_WX_SHORT_LINK(CommonConstants.WX_MPAPI+"/wxa/genwxashortlink","shortLink 短链接",true),

    API_WX_SCHEME_LINK(CommonConstants.WX_MPAPI+"/wxa/generatescheme","schemeLink 短链接",true),

    API_WX_URL_LINK(CommonConstants.WX_MPAPI+"/wxa/generate_urllink","urlLink 短链接",true),

    /**
     * 企业微信
     */
    API_GET_WXCP_THIRD_LOGIN_INFO(CommonConstants.WX_QYAPI + "/cgi-bin/service/get_login_info","获取登录用户信息",true),
    API_GET_WXCP_APPTOKEN_BY_SECRET(CommonConstants.WX_QYAPI + "/cgi-bin/gettoken","根据企业ID和应用密钥获取自建应用Accesstoken",false),
    API_GET_WXCP_MINIAPP_CODE_SESSION(CommonConstants.WX_QYAPI + "/cgi-bin/miniprogram/jscode2session","企业微信小程序登录 code 换取 openId",false,true,"js_code=%s&grant_type=authorization_code",null),
    API_SEND_WXCP_APP_MESSAGE(CommonConstants.WX_QYAPI + "/cgi-bin/message/send","发送应用消息",true,true,null),
    API_GET_CORP_TAG_LIST(CommonConstants.WX_QYAPI + "/cgi-bin/externalcontact/get_corp_tag_list","获取企业标签库",false),
    API_ADD_CORP_TAG(CommonConstants.WX_QYAPI + "/cgi-bin/externalcontact/add_corp_tag","添加企业标签组/标签",true),
    API_EDIT_CORP_TAG(CommonConstants.WX_QYAPI + "/cgi-bin/externalcontact/edit_corp_tag","编辑企业标签组/标签",true),
    API_DEL_CORP_TAG(CommonConstants.WX_QYAPI + "/cgi-bin/externalcontact/del_corp_tag","删除企业标签组/标签",true),
    API_MARK_TAG(CommonConstants.WX_QYAPI + "/cgi-bin/externalcontact/mark_tag","编辑客户企业标签",true),
    API_GET_UNASSIGNED_LIST(CommonConstants.WX_QYAPI + "/cgi-bin/externalcontact/get_unassigned_list","获取离职成员的客户列表",false),
    API_TRANSFER(CommonConstants.WX_QYAPI + "/cgi-bin/externalcontact/transfer","离职成员的外部联系人再分配",true),
    API_GROUP_TRANSFER(CommonConstants.WX_QYAPI + "/cgi-bin/externalcontact/groupchat/transfer","离职成员的群再分配",true),
    API_GET_EMPLOYEE(CommonConstants.WX_QYAPI + "/cgi-bin/user/get","读取成员",false,"userid=%s"),
    API_GET_EMPLOYEE_IDS(CommonConstants.WX_QYAPI + "/cgi-bin/user/list_id","获取成员ID列表",true),
    API_CREATE_EMPLOYEE(CommonConstants.WX_QYAPI + "/cgi-bin/user/create","创建成员",true),
    API_UPDATE_EMPLOYEE(CommonConstants.WX_QYAPI + "/cgi-bin/user/update","更新成员",true),
    API_DELETE_EMPLOYEE(CommonConstants.WX_QYAPI + "/cgi-bin/user/delete","删除成员",false),
    API_GET_CLIENTLE(CommonConstants.WX_QYAPI + "/cgi-bin/externalcontact/get","获取客户详情",false, "external_userid=%s"),
    API_SYNC_CRM_LABEL(CommonConstants.WX_QYAPI + "/cgi-bin/externalcontact/mark_tag","同步编辑客户企业标签",true),
    API_GET_USER_BEHAVIOR_DATA(CommonConstants.WX_QYAPI + "/cgi-bin/externalcontact/get_user_behavior_data","获取联系客户统计数据",true),
    API_GET_GROUPCHAT_STATISTIC(CommonConstants.WX_QYAPI + "/cgi-bin/externalcontact/groupchat/statistic","获取客户群统计数据",true),
    GET_CUSTOMER_GROUP_LIST(CommonConstants.WX_QYAPI + "/cgi-bin/externalcontact/groupchat/list","获取客户群",true),
    GET_CUSTOMER_GROUP_INFO(CommonConstants.WX_QYAPI + "/cgi-bin/externalcontact/groupchat/get","获取客户群详情",true),
    API_GET_DEPARTMENT_LIST(CommonConstants.WX_QYAPI + "/cgi-bin/department/list","获取部门列表",false,true,null,"department"),
    API_GET_DEPARTMENTUSERINFO_LIST(CommonConstants.WX_QYAPI + "/cgi-bin/user/list","获取部门成员",false,true,"department_id=%s","userlist"),
    API_CREATE_DEPARTMENT(CommonConstants.WX_QYAPI + "/cgi-bin/department/create","创建部门",true),
    API_UPDATE_DEPARTMENT(CommonConstants.WX_QYAPI + "/cgi-bin/department/update","更新部门",true),
    API_DELETE_DEPARTMENT(CommonConstants.WX_QYAPI + "/cgi-bin/department/delete","删除部门",false),
    API_ADD_MSG_TEMPLATE(CommonConstants.WX_QYAPI + "/cgi-bin/externalcontact/add_msg_template","企业群发任务",true),
    API_GET_GROUP_MSG_RESULT(CommonConstants.WX_QYAPI + "/cgi-bin/externalcontact/get_group_msg_result","企业群发任务查询发送结果",true),
    API_WXCP_UPLOAD_MEDIA(CommonConstants.WX_QYAPI + "/cgi-bin/media/upload","上传临时素材到企业微信",true),
    API_WXCP_BATCH_EXTERNALCONTACT(CommonConstants.WX_QYAPI + "/cgi-binexternalcontact/batch/get_by_user","批量获取客户详情",true),
    API_GET_AGENT_INFO(CommonConstants.WX_QYAPI + "/cgi-bin/agent/get","获取指定的应用详情",false,"agentid=%s"),
    API_GET_EMPLOYEE_BY_TAG(CommonConstants.WX_QYAPI + "/cgi-bin/tag/get","获取标签成员",false,"tagid=%s"),

    API_ADD_CONTACT_WAY(CommonConstants.WX_QYAPI + "/cgi-bin/externalcontact/add_contact_way", "配置客户联系「联系我」方式", true);

    /** 接口URL */
    private String url;
    /** 接口描述 */
    private String desc;
    /** 是否为POST请求 */
    private boolean isPost;
    /** 是否需要传递token */
    private boolean checkToken;
    /** 无需返回结果 */
    private boolean noReturn;
    /** 需要在url上拼接的参数 格式为 "id=%1&name=%2" */
    private String urlVariables;
    /** 返回只需要String类型单个值时对应的字段名 */
    private String returnKey;
    /** 返回结果是否为String类型 */
    private boolean isString;

    WechatApiEnum(String url, String desc, boolean isPost) {
        this.url = url;
        this.desc = desc;
        this.isPost = isPost;
        this.checkToken = true;
        this.returnKey = null;
        this.isString = false;
    }

    WechatApiEnum(String url, String desc, boolean isPost,String urlVariables) {
        this.url = url;
        this.desc = desc;
        this.isPost = isPost;
        this.checkToken = true;
        this.urlVariables = urlVariables;
        this.returnKey = null;
        this.isString = false;
    }

    WechatApiEnum(String url, String desc, boolean isPost, boolean checkToken) {
        this.url = url;
        this.desc = desc;
        this.isPost = isPost;
        this.checkToken = checkToken;
        this.returnKey = null;
        this.isString = false;
    }

    WechatApiEnum(String url, String desc, boolean isPost,boolean noReturn,String urlVariables) {
        this.url = url;
        this.desc = desc;
        this.isPost = isPost;
        this.noReturn = noReturn;
        this.checkToken = true;
        this.returnKey = null;
        this.isString = false;
    }

    WechatApiEnum(String url, String desc, boolean isPost, boolean checkToken,String urlVariables,String returnKey) {
        this.url = url;
        this.desc = desc;
        this.isPost = isPost;
        this.checkToken = checkToken;
        this.urlVariables = urlVariables;
        this.returnKey = returnKey;
        this.isString = false;
    }

    WechatApiEnum(String url, String desc, boolean isPost, boolean checkToken, String urlVariables, String returnKey, boolean isString) {
        this.url = url;
        this.desc = desc;
        this.isPost = isPost;
        this.checkToken = checkToken;
        this.urlVariables = urlVariables;
        this.returnKey = returnKey;
        this.isString = isString;
    }

    public String getUrl() {
        return url;
    }

    public String getDesc() {
        return desc;
    }

    public boolean isPost() {
        return isPost;
    }

    public boolean isCheckToken() {
        return checkToken;
    }

    public boolean isNoReturn() {
        return noReturn;
    }

    public String getUrlVariables() {
        return urlVariables;
    }

    public String getReturnKey() {
        return returnKey;
    }

    public boolean isString() {
        return isString;
    }

}