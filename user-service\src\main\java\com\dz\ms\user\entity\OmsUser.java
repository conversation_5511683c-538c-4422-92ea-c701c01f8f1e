package com.dz.ms.user.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.io.Serializable;
import java.util.Date;

/**
 * OMS-系统用户信息
 * @author: Handy
 * @date:   2022/07/25 21:43
 */
@Getter
@Setter
@NoArgsConstructor
@Table("OMS-系统用户信息")
@TableName(value = "oms_user")
public class OmsUser implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "系统用户ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR,length = 30,isNull = false,comment = "账号",isIndex = true)
    private String username;
    @Columns(type = ColumnType.VARCHAR,length = 70,isNull = false,comment = "密码")
    private String password;
    @Columns(type = ColumnType.VARCHAR,length = 50,isNull = true,comment = "盐值")
    private String salt;
    @Columns(type = ColumnType.VARCHAR,length = 20,isNull = false,comment = "真实姓名")
    private String realname;
    @Columns(type = ColumnType.VARCHAR,length = 30,isNull = true,comment = "昵称")
    private String nickname;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "头像地址")
    private String headUrl;
    @Columns(type = ColumnType.VARCHAR,length = 20,isNull = true,comment = "手机号码",isIndex = true)
    private String mobile;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = false,defaultValue = "1",comment = "用户状态 0离职 1正常")
    private Integer state;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = true,comment = "在线状态 0离线 1在线")
    private Integer isOnline;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = true,defaultValue = "0",comment = "是否超管 1是 0否")
    private Integer isAdmin;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    public OmsUser(Long id, String username, String password, String salt, String realname, String nickname, String headUrl, String mobile, Integer state, Integer isOnline, Integer isAdmin) {
        this.id = id;
        this.username = username;
        this.password = password;
        this.salt = salt;
        this.realname = realname;
        this.nickname = nickname;
        this.headUrl = headUrl;
        this.mobile = mobile;
        this.state = state;
        this.isOnline = isOnline;
        this.isAdmin = isAdmin;
    }

}
