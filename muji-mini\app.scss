@import "assets/scss/common";

page {
  /* 设计字体要求：英文和数字-品牌字体（MUJIFont2020）、中文-思源黑体（SourceHanSansCN）、前两者不支持则使用-苹果字体（PingFang SC） */
  font-family: "MUJIFont2020", SourceHanSansCN, "PingFang SC", "思源黑体",
    "Helvetica Neue", Helvetica, "Hiragino Sans GB", "Microsoft YaHei",
    "微软雅黑", Arial, sans-serif;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #272636;
  font-size: 28rpx;
  line-height: 1;
  word-break: break-all;
}

button {
  padding: 0;
  margin: 0;
  background-color: transparent;
  border: none;
  border-radius: 0;
}

button::after {
  border: none;
}

image {
  height: auto;
}

.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.flex-start {
  align-items: flex-start;
}

.flex-center {
  align-items: center;
}

.flex-end {
  align-items: flex-end;
}

.ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.two-line-ellipsis {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

// .van-popup {
//   padding-top: 0;
//   background-color: transparent !important;
// }
.van-picker__frame {
  border-top: 1px solid #3c3c43;
  border-bottom: 1px solid #3c3c43;
  width: 100%;
  left: 0 !important;
}
