package com.dz.ms.product.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 货架商品DTO
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:25
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@ApiModel(value = "货架商品")
public class ShelfProductDTO extends BaseDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "货架名称")
    private String shelfName;
    @ApiModelProperty(value = "商品ID")
    private Long productId;
    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "商品状态 0禁用 1启用")
    private Integer productState;
    @ApiModelProperty(value = "商品在货架上的位置 越大越靠后")
    private Integer onShelfIndex;
    @ApiModelProperty(value = "商品类型 1实物商品 2电子券")
    private Integer pdType;
    @ApiModelProperty(value = "发货方式 1线下使用 2邮寄")
    private Integer deliveryType;
    @ApiModelProperty(value = "是否展示 0不展示 1展示 3不展示且不能编辑(注意:3是代码处理的状态,数据库实际状态没有变更,使用到的地方还需要根据商品状态判断,如果商品状态是0,此时展示状态是3)")
    private Integer beShow;
    @ApiModelProperty(value = "展示方式 1积分加价购 2商品兑换")
    private Integer showType;
    @ApiModelProperty(value = "上架库存")
    private Integer onInventory;
    @ApiModelProperty(value = "目前库存")
    private Integer currentInventory;
    @ApiModelProperty(value = "当前货架商品累计兑换量")
    private Integer exchangeNum;
    @ApiModelProperty(value = "兑换积分")
    private Integer costPoint;
    @ApiModelProperty(value = "积分划线价")
    private Integer prePoint;
    @ApiModelProperty(value = "默认限购数量/月")
    private Integer limitNum;
    @ApiModelProperty(value = "1积分+金额时 仍需支付的金额")
    private BigDecimal costPrice;
    @ApiModelProperty(value = "角标id列表")
    private List<Long> superscriptIdList;
    @ApiModelProperty(value = "角标名称列表")
    private List<String> superscriptNameList;
    @ApiModelProperty(value = "活动角标字符串")
    private List<String> superscriptCampaignNameList;

    @ApiModelProperty(value = "商品主图")
    private String shelfImg;
    @ApiModelProperty(value = "首张商品场景图")
    private String sceneImg;
    @ApiModelProperty(value = "首张商品橱窗图")
    private String shopWindowImg;
    @ApiModelProperty(value = "商品标签列表")
    private List<TagInfoDTO> tagList;

    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "修改时间")
    private Date modified;
    @ApiModelProperty(value = "修改人")
    private Long modifier;

}
