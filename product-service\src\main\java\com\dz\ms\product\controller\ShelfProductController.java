package com.dz.ms.product.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.constants.NumConstants;
import com.dz.common.core.dto.ExchangeStaticParamDTO;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.product.InventoryParamDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.ms.product.dto.ShelfProductDTO;
import com.dz.ms.product.dto.ShelfProductQueryDTO;
import com.dz.ms.product.dto.req.ShelfProductMiniParamDTO;
import com.dz.ms.product.dto.req.ShelfProductParamDTO;
import com.dz.ms.product.dto.req.ShelfProductSaveParamDTO;
import com.dz.ms.product.dto.res.ProductMiniResDTO;
import com.dz.ms.product.dto.res.ShelfProductMiniResDTO;
import com.dz.ms.product.service.ShelfProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Api(tags = "货架商品")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class ShelfProductController {

    @Resource
    private ShelfProductService shelfProductService;

    /**
     * 分页查询货架商品
     *
     * @param param
     * @return result<PageInfo < ShelfProductDTO>>
     */
    @ApiOperation("分页查询货架商品")
    @PostMapping(value = "/crm/shelf_product/list")
    public Result<PageInfo<ShelfProductDTO>> getShelfProductList(@RequestBody ShelfProductParamDTO param) {
        Result<PageInfo<ShelfProductDTO>> result = new Result<>();
        PageInfo<ShelfProductDTO> page = shelfProductService.getShelfProductList(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询货架商品
     *
     * @param id
     * @return result<ShelfProductDTO>
     */
    @ApiOperation("根据ID查询货架商品")
    @GetMapping(value = "/crm/shelf_product/info")
    public Result<ShelfProductDTO> getShelfProductById(@RequestParam("id") Long id) {
        Result<ShelfProductDTO> result = new Result<>();
        ShelfProductDTO shelfProduct = shelfProductService.getShelfProductById(id,NumConstants.ONE);
        result.setData(shelfProduct);
        return result;
    }

    /**
     * 根据货架ID查询货架上架商品列表
     * @param shelfId 货架ID
     * @return Result<List<ShelfProductDTO>>
     */
    @ApiOperation("根据货架ID查询货架上架商品列表")
    @GetMapping(value = "/crm/shelf_product/product_by_shelf_id")
    public Result<List<ShelfProductDTO>> getProductByShelfId(@RequestParam("shelfId") Long shelfId) {
        Result<List<ShelfProductDTO>> result = new Result<>();
        List<ShelfProductDTO> list = shelfProductService.getProductByShelfIds(ShelfProductQueryDTO.builder().shelfIds(Collections.singletonList(shelfId)).num(NumConstants.TWO).isQrySuperscript(true).isQryImg(true).isQryTag(true).build());
        result.setData(list);
        return result;
    }

    /**
     * 更新货架商品
     *
     * @param param
     * @return result<Object>
     */
    @SysLog(value = "更新货架商品", type = LogType.OPERATELOG)
    @ApiOperation("更新货架商品")
    @PostMapping(value = "/crm/shelf_product/save")
    public Result<Long> saveShelfProduct(@RequestBody ShelfProductSaveParamDTO param) {
        Result<Long> result = new Result<>();
        if(Objects.isNull(param.getShelfId())){
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[货架商品]货架ID不能为空");
        }
        shelfProductService.saveShelfProduct(param,false, NumConstants.TWO);
        return result;
    }

    /**
     * 根据主键ID删除货架商品
     *
     * @param param 入参
     * @return result<Boolean>
     */
    @SysLog(value = "根据主键ID删除货架商品", type = LogType.OPERATELOG)
    @ApiOperation("根据主键ID删除货架商品")
    @PostMapping(value = "/crm/shelf_product/delete")
    public Result<Boolean> deleteShelfProductById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        shelfProductService.deleteShelfProductById(param);
        result.setData(true);
        return result;
    }

    /**
     * 根据条件分页查询货架商品列表
     * @param param 入参
     * @return result<PageInfo<ShelfProductMiniResDTO>>
     */
    @ApiOperation("根据条件分页查询货架商品列表")
    @PostMapping(value = "/app/shelf_product/list_by_param")
    public Result<PageInfo<ShelfProductMiniResDTO>> getShelfProductListByParam(@RequestBody ShelfProductMiniParamDTO param) {
        Result<PageInfo<ShelfProductMiniResDTO>> result = new Result<>();
        PageInfo<ShelfProductMiniResDTO> page = shelfProductService.getShelfProductListByParam(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据货架商品id查询货架商品详情
     * @param shelfProductId 货架商品id
     * @return result<PageInfo < ShelfProductMiniResDTO>>
     */
    @ApiOperation("根据货架商品id查询货架商品详情")
    @GetMapping(value = "/app/shelf_product/shelf_product_id")
    public Result<ProductMiniResDTO> getShelfProductByShelfProductId(@RequestParam("shelfProductId") Long shelfProductId) {
        Result<ProductMiniResDTO> result = new Result<>();
        ProductMiniResDTO page = shelfProductService.getShelfProductByShelfProductId(shelfProductId);
        result.setData(page);
        return result;
    }

    /**
     * 根据货架商品id查询展示中的货架商品简介
     * @param shelfProductId 货架商品id
     * @return result<PageInfo < ShelfProductMiniResDTO>>
     */
    @ApiOperation("根据货架商品id查询展示中的货架商品简介")
    @GetMapping(value = "/app/shelf_product/shelf_product_intro_id")
    public Result<ProductMiniResDTO> getShelfProductIntroByShelfProductId(@RequestParam("shelfProductId") Long shelfProductId) {
        Result<ProductMiniResDTO> result = new Result<>();
        ProductMiniResDTO page = shelfProductService.getShelfProductIntroByShelfProductId(shelfProductId);
        result.setData(page);
        return result;
    }

    /**
     * 库存更新接口
     *
     * @param param
     * @return
     */
    @PostMapping("/shelf_product/update_inventory")
    Result<Void> updateInventory(@RequestBody @Valid InventoryParamDTO param) {
        Result<Void> result = new Result<>();
        shelfProductService.updateInventory(param);
        return result;
    }

    /**
     * 校验货架库存/活动库存
     *
     * @param param
     * @return
     */
    @PostMapping("/shelf_product/validate_inventory")
    Result<Void> validateInventory(@RequestBody InventoryParamDTO param) {
        Result<Void> result = new Result<>();
        shelfProductService.validateInventory(param);
        return result;
    }

    /**
     * 推送兑换成功统计数据
     *
     * @param orderStaticParam
     */
    @PostMapping(value = {"/shelf_product/update_static", "/crm/shelf_product/update_static"})
    void updateStatic(@RequestBody ExchangeStaticParamDTO orderStaticParam) {
        shelfProductService.updateStatic(orderStaticParam);
    }

}
