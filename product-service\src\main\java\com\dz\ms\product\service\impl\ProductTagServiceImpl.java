package com.dz.ms.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.product.dto.ProductTagDTO;
import com.dz.ms.product.entity.ProductTag;
import com.dz.ms.product.mapper.ProductTagMapper;
import com.dz.ms.product.service.ProductTagService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 商品标签表
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:25
 */
@Service
public class ProductTagServiceImpl extends ServiceImpl<ProductTagMapper, ProductTag> implements ProductTagService {

    @Resource
    private ProductTagMapper productTagMapper;

    /**
     * 分页查询商品标签表
     *
     * @param param
     * @return PageInfo<ProductTagDTO>
     */
    @Override
    public PageInfo<ProductTagDTO> getProductTagList(ProductTagDTO param) {
        ProductTag productTag = BeanCopierUtils.convertObjectTrim(param, ProductTag.class);
        IPage<ProductTag> page = productTagMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), new QueryWrapper<>(productTag));
        return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), BeanCopierUtils.convertList(page.getRecords(), ProductTagDTO.class));
    }

    /**
     * 根据ID查询商品标签表
     *
     * @param id
     * @return ProductTagDTO
     */
    @Override
    public ProductTagDTO getProductTagById(Long id) {
        ProductTag productTag = productTagMapper.selectById(id);
        return BeanCopierUtils.convertObject(productTag, ProductTagDTO.class);
    }

    @Override
    public List<ProductTagDTO> getTagByTagId(Long tagId) {
        LambdaQueryWrapper<ProductTag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductTag::getTagId, tagId);
        List<ProductTag> list = productTagMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            return BeanCopierUtils.convertList(list, ProductTagDTO.class);
        }
        return new ArrayList<>();
    }

    /**
     * 保存商品标签表
     *
     * @param param
     * @return Long
     */
    @Override
    public Long saveProductTag(ProductTagDTO param) {
        ProductTag productTag = new ProductTag(param.getId(), param.getProductId(), param.getProductName(), param.getTagId(), param.getTagName(), param.getCate());
        if (ParamUtils.isNullOr0Long(productTag.getId())) {
            productTagMapper.insert(productTag);
        } else {
            productTagMapper.updateById(productTag);
        }
        return productTag.getId();
    }

    /**
     * 根据ID删除商品标签表
     *
     * @param param
     */
    @Override
    public void deleteProductTagById(IdCodeDTO param) {
        productTagMapper.deleteById(param.getId());
    }

    /**
     * 根据标签ID删除商品标签
     *
     * @param tagId 标签ID
     */
    @Override
    public int deleteShelfTagByTagId(Long tagId) {
//        List<ProductTagDTO> list = this.getTagByTagId(tagId);
//        if (!CollectionUtils.isEmpty(list)) {
//            throw new BusinessException(ErrorCode.BAD_REQUEST, "[商品标签]此标签有关联商品，不允许删除");
//        }
        LambdaQueryWrapper<ProductTag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductTag::getTagId, tagId);
        return productTagMapper.delete(queryWrapper);
    }

}