<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.basic.mapper.MpMsgKeywordMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    id,
  	    msg_id,
  	    sort,
  	    key_name,
  	    key_type,
  	    key_id,
  	    is_edit,
  	    tenant_id,
  	    creator,
  	    created,
  	    modifier,
  	    modified
    </sql>

    <!-- 查询示例 -->
    <select id="selectDemo" parameterType="java.lang.Long" resultType="com.dz.ms.basic.entity.MpMsgKeyword">
        select
        <include refid="Base_Column_List" />
        from mp_msg_keyword
        where id = #{id,jdbcType=BIGINT}
        AND tenant_id = #{tenantId}
    </select>

</mapper>
