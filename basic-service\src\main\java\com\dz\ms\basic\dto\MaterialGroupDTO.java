package com.dz.ms.basic.dto;
import java.util.Date;
import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 素材分组DTO
 * @author: Handy
 * @date:   2022/2/4 17:39
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "素材分组")
public class MaterialGroupDTO extends BaseDTO {

    @ApiModelProperty(value = "素材分组ID")
    private Long id;
    @ApiModelProperty(value = "素材分组名称")
    private String groupName;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "更新时间")
    private Date modified;

}
