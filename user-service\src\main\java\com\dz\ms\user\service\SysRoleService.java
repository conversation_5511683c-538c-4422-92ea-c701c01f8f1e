package com.dz.ms.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.core.dto.user.SysUserDTO;
import com.dz.ms.user.dto.RolePermissionDTO;
import com.dz.common.core.dto.user.SysPermissionDTO;
import com.dz.ms.user.dto.SysUserRoleDTO;
import com.dz.ms.user.entity.SysRole;
import com.dz.ms.user.vo.RouterVo;

import java.util.List;

/**
 * 系统角色接口
 * @author: Handy
 * @date:   2022/2/4 23:04
 */
public interface SysRoleService extends IService<SysRole> {

    /**
     * 获取角色权限列表
     * @param roleId
     * @return
     */
    public List<String> getRolePermitCodes(Long roleId,Long tenantId,Integer platform);

    /**
     * 获取角色功能权限码列表
     * @param roleId
     * @return
     */
    public List<String> getRoleFunctionCodes(Long roleId,Long tenantId,Integer platform);

    /**
     * 获取角色菜单树形结构
     * @return
     */
    public List<SysPermissionDTO> getRoleMenuTree(Long roleId,Long tenantId,Integer platform);

    /**
     * 获取角色权限列表
     * @param roleId
     * @return
     */
    public List<String> getRolePermitUrls(Long roleId,Integer platform);

    /**
     * 获取角色权限ID列表
     * @param roleId
     * @return
     */
    public List<Long> getRolePermitIds(Long roleId);

    /**
     * 绑定角色权限
     * @param param
     */
    public void bindPermit(RolePermissionDTO param);

    /**
     * 根据系统用户ID列表获取绑定角色列表
     * @param ids
     * @return
     */
    public List<SysUserRoleDTO> getSysRoleByUserIds(List<Long> ids);

    /**
     * 验证接口权限
     * @param roleId
     * @param url
     * @param tenantId
     */
    boolean checkPermission(Long roleId, String url, Long tenantId, Integer platform);

    List<RouterVo> selectMenuTree(SysUserDTO sysUser);

}
