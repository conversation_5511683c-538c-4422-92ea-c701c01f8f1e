package com.dz.ms.basic.controller;

import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.basic.GiftConfigDTO;
import com.dz.ms.basic.service.GiftConfigService;
import com.dz.ms.basic.vo.GiftConfigVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Api(tags="核心礼遇配置")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class GiftConfigController {

    @Resource
    private GiftConfigService giftConfigService;

    /**
     * 核心礼遇列表
     */
    @ApiOperation("核心礼遇列表")
	@GetMapping(value = "/app/gift/config/list")
    public Result<GiftConfigDTO> getGiftConfigList(@RequestParam(value = "type", required = false) String type) {
        Result<GiftConfigDTO> result = new Result<>();
        result.setData(giftConfigService.getGiftConfigList(type));
        return result;
    }

    /**
     * 核心礼遇详情
     */
    @ApiOperation("核心礼遇详情")
    @GetMapping(value = "/app/gift/config/info")
    public Result<GiftConfigDTO> getGiftConfigInfo(@RequestParam("id")Long id) {
        Result<GiftConfigDTO> result = new Result<>();
        result.setData(giftConfigService.getGiftConfigInfo(id));
        return result;
    }

    /**
     * 升级礼专用判断是否展示升级礼配置弹窗 2铜级会员,3银级会员,4金级会员
     */
    @ApiOperation("升级礼专用判断是否展示升级礼配置弹窗")
    @GetMapping(value = "/app/gift/level/show")
    public Result<Boolean> getGiftLeveShow(@RequestParam("type")Integer type) {
        Result<Boolean> result = new Result<>();
        result.setData(giftConfigService.getGiftLeveShow(type));
        return result;
    }

    @ApiOperation("修改")
    @PostMapping(value = "/crm/gift/config/udpate")
    public Result<Object> updateGiftConfig(@RequestBody GiftConfigVo param) {
        Result<Object> result = new Result<>();
        giftConfigService.updateGiftConfig(param);
        return result;
    }

    @ApiOperation("配置列表")
    @GetMapping(value = "/crm/gift/list")
    public Result<List<GiftConfigDTO>> getGiftList() {
        Result<List<GiftConfigDTO>> result = new Result<>();
        result.setData(giftConfigService.getGiftList());
        return result;
    }

    @ApiOperation("配置对象")
    @GetMapping(value = "/crm/gift/map")
    public Result<Map<String, GiftConfigDTO>> getGiftMap() {
        Result<Map<String, GiftConfigDTO>> result = new Result<>();
        List<GiftConfigDTO> giftList = giftConfigService.getGiftList();
        Map<String, GiftConfigDTO> configDTOMap = giftList.stream().collect(Collectors.toMap(GiftConfigDTO::getName, Function.identity()));
        result.setData(configDTOMap);
        return result;
    }
}
