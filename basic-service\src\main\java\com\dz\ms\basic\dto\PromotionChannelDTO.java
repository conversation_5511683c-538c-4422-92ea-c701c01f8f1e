package com.dz.ms.basic.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "推广渠道添加")
public class PromotionChannelDTO extends BaseDTO {

    @ApiModelProperty(value = "渠道ID")
    private Long id;
    @ApiModelProperty(value = "渠道名称")
    private String channelName;
    @ApiModelProperty(value = "渠道参数")
    private String channelParam;;
    @ApiModelProperty(value = "0正常 1删除")
    private Integer isDeleted;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "创建人姓名")
    private String creatorName;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "父类ID")
    private Long parentId;
}
