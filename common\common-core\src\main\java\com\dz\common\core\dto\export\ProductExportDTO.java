package com.dz.common.core.dto.export;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ProductExportDTO {

    @ApiModelProperty(value = "商品ID")
    private Long id;
    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "商品类型 1实物商品 2电子券")
    private Integer pdType;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "商品场景图")
    private String scenceImgListStr;
    @ApiModelProperty(value = "商品橱窗图")
    private String shelfImgListStr;
    @ApiModelProperty(value = "商品标签")
    private String TagListStr;
    @ApiModelProperty(value = "商品CP号")
    private String venderId;
    @ApiModelProperty(value = "成本价")
    private BigDecimal originPrice;
    @ApiModelProperty(value = "到店支付金额")
    private BigDecimal costPrice;
    @ApiModelProperty(value = "兑换积分")
    private Integer costPoint;
    @ApiModelProperty(value = "目前所处货架")
    private String shelfListStr;
    @ApiModelProperty(value = "状态 0禁用 1启用")
    private Integer state;
    @ApiModelProperty(value = "商品累计兑换量")
    private Integer exchangeNum;
}
