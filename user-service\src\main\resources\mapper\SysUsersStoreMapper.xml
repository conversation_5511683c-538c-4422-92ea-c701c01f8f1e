<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.user.mapper.SysUsersStoreMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    uid,
  	    store_id,
  	    tenant_id
    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch">
        insert into sys_users_store(uid, store_id, tenant_id)
        values
        <foreach collection="storeIds" item="storeId" separator=",">
            (#{uid}, #{storeId}, #{tenantId})
        </foreach>
    </insert>

</mapper>
