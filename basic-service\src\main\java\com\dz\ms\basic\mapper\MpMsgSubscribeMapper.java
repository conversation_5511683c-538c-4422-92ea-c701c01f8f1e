package com.dz.ms.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.common.core.dto.basic.MpMsgSubscribeUserDTO;
import com.dz.ms.basic.entity.MpMsgSubscribe;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 小程序订阅消息订阅记录Mapper
 * @author: Handy
 * @date:   2023/07/07 14:22
 */
@Repository
public interface MpMsgSubscribeMapper extends BaseMapper<MpMsgSubscribe> {

    /** 获取用户有效订阅记录中最早的一条 */
    List<MpMsgSubscribeUserDTO> selectUserSubscribeList(@Param("uids") List<Long> uids);

    /** 根据ID列表将订阅记录更新为已发送 */
    int updateStateByIds(@Param("ids") List<Long> ids);

    void updatePushNumberByIds(@Param("ids") List<String> ids);
}
