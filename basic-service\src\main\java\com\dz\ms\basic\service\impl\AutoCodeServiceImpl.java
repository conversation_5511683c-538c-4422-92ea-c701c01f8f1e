package com.dz.ms.basic.service.impl;

import com.dz.ms.basic.dto.AutoCodeColumnDTO;
import com.dz.ms.basic.dto.AutoCodeTableDTO;
import com.dz.ms.basic.mapper.AutoCodeMapper;
import com.dz.ms.basic.service.AutoCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 代码生成
 * @author: Handy
 * @date:   2019/12/5 17:36
 */
@Service
public class AutoCodeServiceImpl implements AutoCodeService {

    @Autowired
    private AutoCodeMapper autoCodeMapper;

    @Override
    public List<String> getAllDatabase() {
        return autoCodeMapper.getAllDatabase();
    }

    @Override
    public List<AutoCodeTableDTO> getTableNameBykeyword(String database, String keyword) {
        return autoCodeMapper.getTableNameBykeyword(database,keyword);
    }

    @Override
    public List<AutoCodeColumnDTO> getTableInfoByTableName(String database, String tableName) {
        return autoCodeMapper.getTableInfoByTableName(database,tableName);
    }

}
