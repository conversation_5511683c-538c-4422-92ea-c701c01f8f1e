<template>
  <a-button :disabled="disabled" type="link" @click="thisMethods.ShelfProductManagementAddDialogHandler">+ 添加商品</a-button>
  <a-button :disabled="disabled" type="link" @click="thisMethods.ShelfProductManagementDialogHandler">管理商品</a-button>
  <ShelfProductManagementAddDialog :bizType="props.bizType" :shelfId="props.shelfId" v-model="thisFields.ShelfProductManagementAddDialogOpen" @ok="thisMethods.ShelfProductManagementAddDialogHandlerOk" :beSelectedProductIdArrayObjectList="formFields[props.productListKey]" :allSelectedProductList="allSelectedProductList"/>
  <ShelfProductManagementDialog :bizType="props.bizType" :shelfId="props.shelfId" v-model="thisFields.ShelfProductManagementDialogOpen" @ok="thisMethods.ShelfProductManagementDialogHandlerOk" :beSelectedProductIdArrayObjectList="formFields[props.productListKey]" @changeCurrentInventory="changeCurrentInventory" :allSelectedProductList="allSelectedProductList"/>
</template>
<script setup>
import { reactive } from 'vue'
import ShelfProductManagementAddDialog from '@/views/mall/shelfManagement/giftRack/components/ShelfProductManagementAddDialog.vue'
import ShelfProductManagementDialog from '@/views/mall/shelfManagement/giftRack/components/ShelfProductManagementDialog.vue'

const props = defineProps({
  bizType: { // 业务类型：默认-兑礼货架、crowdPurchaseRestriction-人群限购
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  shelfId: {
    type: [String, Number],
    default: ''
  },
  productListKey: {
    type: String,
    default: 'saveShelfProductList'
  },
  formFields: {
    type: Object,
    default: () => ({})
  },
  allSelectedProductList: { // 所有已选中的商品（人群限购跨组件）
    type: Array,
    default: () => ([])
  },
})
const emits = defineEmits(['addOk', 'managementOk', 'changeCurrentInventory'])

const thisFields = reactive({
  ShelfProductManagementAddDialogOpen: false,
  ShelfProductManagementDialogOpen: false
})
const thisMethods = {
  ShelfProductManagementAddDialogHandler() {
    thisFields.ShelfProductManagementAddDialogOpen = true
  },
  ShelfProductManagementAddDialogHandlerOk(e) {
    props.formFields[props.productListKey].push(...e)
    emits('addOk', props.formFields[props.productListKey])
  },
  ShelfProductManagementDialogHandler() {
    thisFields.ShelfProductManagementDialogOpen = true
  },
  ShelfProductManagementDialogHandlerOk(beSelectedProductIdArrayObjectList) {
    emits('managementOk', beSelectedProductIdArrayObjectList)
    console.log("🚀 ~ ShelfProductManagementDialogHandlerOk ~ beSelectedProductIdArrayObjectList:", beSelectedProductIdArrayObjectList)
  },

  setFormFields() {
    const formFields = props.formFields
    formFields[props.productListKey] = formFields[props.productListKey] || []
    // console.log("🚀 ~ setFormFields ~  formFields[props.productListKey]:", formFields)
  }
}
function changeCurrentInventory() {
  emits('changeCurrentInventory')
}
thisMethods.setFormFields()
</script>

<style scoped lang="scss">
</style>
