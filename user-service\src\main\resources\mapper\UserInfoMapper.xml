<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" " http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.user.mapper.UserInfoMapper">

    <!-- 根据openid获取用户信息 -->
    <select id="getUserByOpenid" resultType="com.dz.ms.user.entity.UserInfo">
        select
        *
        from user_info
        where openid = #{openid}
        AND tenant_id = #{tenantId}
        and state = 1
    </select>

    <!-- 根据unionid获取用户信息 -->
    <select id="getNoOpenidUserByUnionid" resultType="com.dz.ms.user.entity.UserInfo">
        select
        *
        from user_info
        where unionid = #{unionid}
        AND tenant_id = #{tenantId}
        and state = 1
        limit 1
    </select>

    <!-- 根据用户ID列表获取用户信息 -->
    <select id="getUserListByIds" resultType="com.dz.common.core.dto.user.UserSimpleDTO">
        select
        *
        from user_info
        where id in
        <foreach collection="ids" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND tenant_id = #{tenantId}
    </select>

    <select id="getUserInfoByUnionId" resultType="com.dz.common.core.dto.user.UserSimpleDTO">
        select
        id, openid, unionid
        from user_info
        where unionid = #{unionid}
        AND tenant_id = #{tenantId}
    </select>

    <select id="getUserInfoListByUnionIds" resultType="com.dz.common.core.dto.user.UserSimpleDTO">
        select
        id, openid, unionid, mobile
        from user_info
        where
        tenant_id = #{tenantId}
        and unionid in
        <foreach collection="unionids" index="index" item="unionid" separator="," open="(" close=")">
            #{unionid}
        </foreach>
    </select>

    <!-- 根据用户ID列表获取用户openid列表 -->
    <select id="getUserOpenidByIds" resultType="java.lang.String">
        select
        openid
        from user_info
        where id in
        <foreach collection="ids" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        AND tenant_id = #{tenantId} and state = 1
    </select>

    <!-- 获取所有员工ID账号列表 -->
    <select id="queryUserIdCodes" resultType="com.dz.common.core.dto.IdCodeDTO">
        select id,qywx_userid code
        from user_info
        where user_type = 2
    </select>

    <select id="getUserInfoListByOpenids" resultType="com.dz.common.core.dto.user.UserSimpleDTO">
        select
        id, openid, unionid, mobile, card_no
        from user_info
        where
        tenant_id = #{tenantId}
        and openid in
        <foreach collection="openids" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>
