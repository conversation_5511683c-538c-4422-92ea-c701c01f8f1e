// components/noDataAvailable/noDataAvailable.js
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    loading: Boolean,
    data: Array, // 列表数据
    // 空状态图片
    url: {
      type: String,
      required:true
    },
    text: {
      type: String,
      value: '暂无数据'
    },
    // 主题 黑色背景还是白色背景 默认白色背景
    black: {
      type: Boolean,
      value: false
    },
    // 按钮文案
    btnText:{
      type: String,
      value: ''
    },
    top:{
      type: [String,Number],
      value: 352
    }
  },
  methods:{
    click(){
      this.triggerEvent("click")
    }
  }

  
})