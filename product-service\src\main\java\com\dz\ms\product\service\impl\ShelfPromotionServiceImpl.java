package com.dz.ms.product.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.constants.NumConstants;
import com.dz.common.core.constants.ProductConstants;
import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.IdNumberDTO;
import com.dz.common.core.dto.export.PromotionExportDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.service.ExportService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.DateUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.product.dto.ShelfDTO;
import com.dz.ms.product.dto.ShelfPromotionDTO;
import com.dz.ms.product.dto.inreq.ShelfPromotionQueryDTO;
import com.dz.ms.product.dto.req.ShelfParamDTO;
import com.dz.ms.product.dto.req.ShelfPromotionParamDTO;
import com.dz.ms.product.entity.ShelfPromotion;
import com.dz.ms.product.mapper.ShelfPromotionMapper;
import com.dz.ms.product.service.ShelfPromotionService;
import com.dz.ms.product.service.ShelfService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 货架推广活动
 *
 * @author: LiinNs
 * @date: 2024/11/20 11:36
 */
@Service
public class ShelfPromotionServiceImpl extends ServiceImpl<ShelfPromotionMapper, ShelfPromotion> implements ShelfPromotionService {

    @Resource
    private ShelfPromotionMapper shelfPromotionMapper;
    @Resource
    private ShelfService shelfService;
    @Resource
    private ExportService exportService;

    /**
     * 分页查询货架推广活动
     *
     * @param param
     * @return PageInfo<ShelfPromotionDTO>
     */
    @Override
    public PageInfo<ShelfPromotionDTO> getShelfPromotionList(ShelfPromotionParamDTO param) {
        List<ShelfPromotionDTO> list = new ArrayList<>();
        if(param.getOnStartTime() != null || param.getOnEndTime() != null){
            if(param.getOnStartTime() == null || param.getOnEndTime() == null){
                throw new BusinessException(ErrorCode.BAD_REQUEST, "上架开始/结束时间都不能为空");
            }
            if(param.getOnStartTime().after(param.getOnEndTime())){
                throw new BusinessException(ErrorCode.BAD_REQUEST, "上架开始时间不能在结束时间之后");
            }
        }
        IPage<ShelfPromotion> page = shelfPromotionMapper.selPageList(new Page<>(param.getPageNum(), param.getPageSize()), param);
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            Date date = new Date();
            list = BeanCopierUtils.convertList(page.getRecords(), ShelfPromotionDTO.class);
            Map<Long, ShelfDTO> shelfMap = shelfService.getNoPageShelfMap(ShelfParamDTO.builder().shelfIdList(list.stream().map(ShelfPromotionDTO::getShelfId).collect(Collectors.toList())).build(), NumConstants.ONE);
            for (ShelfPromotionDTO shelfPromotionDTO : list) {
                if (Objects.nonNull(param.getPromotionState())) {
                    shelfPromotionDTO.setPromotionState(param.getPromotionState());
                }
                if (Objects.isNull(param.getPromotionState())) {
                    if (shelfPromotionDTO.getOnStartTime().after(date)) {
                        shelfPromotionDTO.setPromotionState(ProductConstants.Shelf.STATE_NO_START);
                    }
                    if (shelfPromotionDTO.getOnStartTime().before(date) && shelfPromotionDTO.getOnEndTime().after(date)) {
                        shelfPromotionDTO.setPromotionState(ProductConstants.Shelf.STATE_IS_UP);
                    }
                    if (shelfPromotionDTO.getOnEndTime().before(date)) {
                        shelfPromotionDTO.setPromotionState(ProductConstants.Shelf.STATE_IS_END);
                    }
                }
                ShelfDTO shelfDTO = shelfMap.get(shelfPromotionDTO.getShelfId());
                if (Objects.nonNull(shelfDTO)) {
                    shelfPromotionDTO.setShelfName(shelfDTO.getName());
                }
            }
        }
        return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), list);
    }

    /**
     * 根据货架ID获取有效的货架推广位
     * 该方法用于查询当前时间正在生效的上架的货架推广位，即开始时间早于当前时间且结束时间晚于当前时间且上架的记录
     * 如果存在符合条件的促销信息，则将第一条记录转换为DTO并返回
     * @param shelfId 货架ID
     * @return ShelfPromotionDTO 返回货架推广位的DTO对象，如果找不到符合条件的促销信息，则返回null
     */
    @Override
    public ShelfPromotionDTO getShelfPromotionByShelfId(Long shelfId) {
        ShelfPromotionDTO shelfPromotionDTO = null;
        LambdaQueryWrapper<ShelfPromotion> queryWrapper = new LambdaQueryWrapper<>();
        Date date = new Date();
        queryWrapper.eq(ShelfPromotion::getShelfId, shelfId).eq(ShelfPromotion::getState, NumConstants.ONE)
                .and(i -> i.lt(ShelfPromotion::getOnStartTime, date)
                        .gt(ShelfPromotion::getOnEndTime, date));
        List<ShelfPromotion> shelfPromotionList = shelfPromotionMapper.selectList(queryWrapper);
        if(!CollectionUtils.isEmpty(shelfPromotionList)){
            shelfPromotionDTO = BeanCopierUtils.convertObject(shelfPromotionList.get(0), ShelfPromotionDTO.class);
        }
        return shelfPromotionDTO;
    }

    /**
     * 根据ID查询货架推广活动
     * @param qryDTO 查询条件
     * id id
     * isThrow 是否抛异常
     * isQryShelf 是否查询货架信息               
     * @return ShelfPromotionDTO
     */
    @Override
    public ShelfPromotionDTO getShelfPromotionById(ShelfPromotionQueryDTO qryDTO) {
        Long id = qryDTO.getId();
        boolean isThrow = Objects.nonNull(qryDTO.getIsThrow()) && qryDTO.getIsThrow();
        boolean isQryShelf = Objects.nonNull(qryDTO.getIsQryShelf()) && qryDTO.getIsQryShelf();
        ShelfPromotion shelfPromotion = shelfPromotionMapper.selectById(id);
        if(shelfPromotion == null && isThrow){
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[货架推广活动]未查询到此货架推广活动");
        }
        List<ShelfDTO> shelfDTOList = new ArrayList<>();
        if(isQryShelf && Objects.nonNull(shelfPromotion)){
            shelfDTOList = shelfService.getNoPageShelf(ShelfParamDTO.builder().shelfIdList(Collections.singletonList(shelfPromotion.getShelfId())).build(), NumConstants.ONE);
        }
        ShelfPromotionDTO shelfPromotionDTO = BeanCopierUtils.convertObject(shelfPromotion, ShelfPromotionDTO.class);
        if(Objects.nonNull(shelfPromotionDTO) && !CollectionUtils.isEmpty(shelfDTOList)){
            Optional<ShelfDTO> first = shelfDTOList.stream().filter(s -> Objects.equals(s.getId(), shelfPromotion.getShelfId())).findFirst();
            first.ifPresent(shelfDTO -> shelfPromotionDTO.setShelfName(shelfDTO.getName()));
        }
        return shelfPromotionDTO;
    }

    /**
     * 保存货架推广活动
     *
     * @param param
     * @return Long
     */
    @Override
    public Long saveShelfPromotion(ShelfPromotionDTO param) {
        ShelfPromotion shelfPromotion = new ShelfPromotion(param.getId(), param.getName(), param.getOnType(), param.getOnStartTime(), param.getOnEndTime(), param.getShelfId(), param.getNum(), param.getContent(), param.getState());
        if(Objects.isNull(shelfPromotion.getShelfId())){
            throw new BusinessException(ErrorCode.BAD_REQUEST, "业务繁忙，请稍后重试");
        }
        if(Objects.equals(shelfPromotion.getOnType(), ProductConstants.Shelf.ON_TYPE_ONE)){
            shelfPromotion.setOnStartTime(DateUtils.timeClear("2000-01-01"));
            shelfPromotion.setOnEndTime(DateUtils.timeClear("3000-01-01"));
        }
        if(Objects.equals(shelfPromotion.getOnType(),ProductConstants.Shelf.ON_TYPE_TWO)){
            if(param.getOnStartTime() == null || param.getOnEndTime() == null){
                throw new BusinessException(ErrorCode.BAD_REQUEST, "推广开始/结束时间都不能为空");
            }
            if(!param.getOnStartTime().before(param.getOnEndTime())){
                throw new BusinessException(ErrorCode.BAD_REQUEST, "推广开始时间不得晚于结束时间，同时，两者不可完全重合");
            }
        }
        //根据shelfId查询所有数据并校验新增或更新的数据推广开始结束时间是否有重叠
        List<ShelfPromotion> shelfPromotionList = shelfPromotionMapper.selectList(new LambdaQueryWrapper<ShelfPromotion>().eq(ShelfPromotion::getShelfId, shelfPromotion.getShelfId()));
        if(!CollectionUtils.isEmpty(shelfPromotionList)){
            for (ShelfPromotion dateShelfPromotion : shelfPromotionList) {
                if(shelfPromotion.getId() != null && shelfPromotion.getId().equals(dateShelfPromotion.getId())){
                    continue;
                }
                boolean hasOverlap = (shelfPromotion.getOnStartTime().after(dateShelfPromotion.getOnStartTime()) && shelfPromotion.getOnStartTime().before(dateShelfPromotion.getOnEndTime()))
                        || (shelfPromotion.getOnEndTime().after(dateShelfPromotion.getOnStartTime()) && shelfPromotion.getOnEndTime().before(dateShelfPromotion.getOnEndTime()))
                        || (shelfPromotion.getOnStartTime().before(dateShelfPromotion.getOnStartTime()) && shelfPromotion.getOnEndTime().after(dateShelfPromotion.getOnEndTime()))
                        || shelfPromotion.getOnStartTime().equals(dateShelfPromotion.getOnStartTime()) 
                        || shelfPromotion.getOnEndTime().equals(dateShelfPromotion.getOnEndTime());
                if(hasOverlap){
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "同一货架推广时间段有重叠，请调整");
                }
            }
        }
        if (ParamUtils.isNullOr0Long(shelfPromotion.getId())) {
            shelfPromotionMapper.insert(shelfPromotion);
        } else {
            shelfPromotionMapper.updateById(shelfPromotion);
        }
        return shelfPromotion.getId();
    }

    /**
     * 根据ID删除货架推广活动
     *
     * @param param
     */
    @Override
    public void deleteShelfPromotionById(IdCodeDTO param) {
        this.getShelfPromotionById(ShelfPromotionQueryDTO.builder().id(param.getId()).isThrow(true).build());
        shelfPromotionMapper.deleteById(param.getId());
    }

    /**
     * 根据ID修改启停状态
     * @param param ID NUMBER 通用DTO
     */
    @Override
    public void updateStateById(IdNumberDTO param) {
        this.getShelfPromotionById(ShelfPromotionQueryDTO.builder().id(param.getId()).isThrow(true).build());
        ParamUtils.checkStateParam(param.getNumber());
        shelfPromotionMapper.updateById(ShelfPromotion.builder().id(param.getId()).state(param.getNumber()).build());
    }

    @Override
    public void exportPromotionList(DownloadAddParamDTO exportParam) {
        String jsonParam = exportParam.getJsonParam();
        String fileName = exportParam.getFileName();
        String fileExt = exportParam.getFileExt();
        String reportCode = exportParam.getReportCode();
        Long downloadCenterId = exportParam.getDownloadCenterId();
        CurrentUserDTO commonLoginDTO = SecurityContext.getUser();
        ShelfPromotionParamDTO param = JSON.parseObject(jsonParam, ShelfPromotionParamDTO.class);
        List<JSONObject> reList = getPromotionExportDTO(param);
        exportService.writeExportList(fileName, fileExt, reportCode, downloadCenterId, commonLoginDTO, reList);
    }

    private List<JSONObject> getPromotionExportDTO(ShelfPromotionParamDTO param) {
        List<ShelfPromotionDTO> promotionList = shelfPromotionMapper.selectListByParam(param);
        if (CollectionUtils.isEmpty(promotionList)) {
            return new ArrayList<>();
        }

        List<PromotionExportDTO> exportList = new ArrayList<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date();
        Map<Long, ShelfDTO> shelfMap = shelfService.getNoPageShelfMap(ShelfParamDTO.builder().shelfIdList(promotionList.stream().map(ShelfPromotionDTO::getShelfId).collect(Collectors.toList())).build(), NumConstants.ONE);
        for (ShelfPromotionDTO promotionDTO : promotionList) {
            if (Objects.isNull(promotionDTO)) {
                continue;
            }
            PromotionExportDTO exportItem = BeanCopierUtils.convertObject(promotionDTO, PromotionExportDTO.class);
            if (Objects.nonNull(param.getPromotionState())) {
                exportItem.setPromotionState(param.getPromotionState());
            }
            if (Objects.isNull(param.getPromotionState())) {
                if (Objects.nonNull(promotionDTO.getOnStartTime()) && promotionDTO.getOnStartTime().after(date)) {
                    exportItem.setPromotionState(ProductConstants.Shelf.STATE_NO_START);
                }
                if (Objects.nonNull(promotionDTO.getOnStartTime()) && promotionDTO.getOnStartTime().before(date) && Objects.nonNull(promotionDTO.getOnEndTime()) && promotionDTO.getOnEndTime().after(date)) {
                    exportItem.setPromotionState(ProductConstants.Shelf.STATE_IS_UP);
                }
                if (Objects.nonNull(promotionDTO.getOnStartTime()) && promotionDTO.getOnEndTime().before(date)) {
                    exportItem.setPromotionState(ProductConstants.Shelf.STATE_IS_END);
                }
            }
            if (Objects.equals(NumConstants.ONE, promotionDTO.getOnType())) {
                exportItem.setPromotionTimeStr("永久上架");
            } else {
                String onShelfTimeStr = simpleDateFormat.format(promotionDTO.getOnStartTime()) + "-" + simpleDateFormat.format(promotionDTO.getOnEndTime());
                exportItem.setPromotionTimeStr(onShelfTimeStr);
            }
            ShelfDTO shelfDTO = shelfMap.get(promotionDTO.getShelfId());
            if (Objects.nonNull(shelfDTO)) {
                exportItem.setShelfName(shelfDTO.getName());
            }
            exportList.add(exportItem);
        }
        return new ArrayList<>(JSON.parseArray(JSON.toJSONString(exportList), JSONObject.class));
    }

}
