package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 小程序/公众号模板消息
 * @author: Handy
 * @date:   2023/07/06 18:05
 */
@Getter
@Setter
@NoArgsConstructor
@Table("小程序/公众号模板消息")
@TableName(value = "mp_msg")
public class MpMsg implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = true,defaultValue = "0",comment = "类型 1小程序 2公众号")
    private Integer msgType;
    @Columns(type = ColumnType.VARCHAR,length = 20,isNull = false,comment = "消息唯一编码")
    private String msgCode;
    @Columns(type = ColumnType.VARCHAR,length = 20,isNull = false,comment = "模板编号")
    private String templateCode;
    @Columns(type = ColumnType.VARCHAR,length = 64,isNull = false,comment = "模板ID")
    private String templateId;
    @Columns(type = ColumnType.VARCHAR,length = 20,isNull = true,comment = "模板名称")
    private String templateName;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = false,comment = "触发类型 1自动触发 2 手动触发")
    private Integer triggerType;
    @Columns(type = ColumnType.VARCHAR,length = 128,isNull = true,comment = "关键词逗号隔开")
    private String keyword;
    @Columns(type = ColumnType.VARCHAR,length = 128,isNull = true,comment = "跳转路径")
    private String pagePath;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = false,comment = "状态 0禁用 1启用",defaultValue = "1")
    private Integer state;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = true,comment = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;

    public MpMsg(Long id, Integer msgType, String msgCode, String templateCode, String templateId, String templateName, Integer triggerType, String keyword, String pagePath, Integer state) {
        this.id = id;
        this.msgType = msgType;
        this.msgCode = msgCode;
        this.templateCode = templateCode;
        this.templateId = templateId;
        this.templateName = templateName;
        this.triggerType = triggerType;
        this.keyword = keyword;
        this.pagePath = pagePath;
        this.state = state;
    }
}
