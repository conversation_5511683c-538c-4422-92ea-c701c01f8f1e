<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.product.mapper.ShelfMapper">

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
        id,
  	    `name`,
  	    on_type,
  	    on_start_time,
  	    on_end_time,
  	    priority,
  	    limit_show,
  	    group_id,
  	    group_name,
  	    `state`,
        content,
  	    exchange_people,
  	    exchange_order,
  	    exchange_num,
  	    exchange_point,
  	    exchange_avg_num,
  	    tenant_id,
  	    creator,
  	    created,
  	    modified,
  	    modifier
    </sql>

    <!-- 查询示例 -->
    <select id="selectDemo" parameterType="java.lang.Long" resultType="com.dz.ms.product.entity.Shelf">
        select
        <include refid="Base_Column_List"/>
        from shelf
        where id = #{id,jdbcType=BIGINT}
        AND tenant_id = #{tenantId}
    </select>
	
	<select id="selPageList" resultType="com.dz.ms.product.entity.Shelf">
		select
		<include refid="Base_Column_List"/>
		from shelf
		<where>
			is_deleted = 0
			<if test=" param.name != null and param.name != ''  ">
				and `name` like CONCAT('%', #{param.name},'%')
			</if>
			<if test="param.onStartTime != null">
				AND on_start_time <![CDATA[ >= ]]> #{param.onStartTime}
			</if>
			<if test="param.onEndTime != null">
				AND on_end_time <![CDATA[ <= ]]> #{param.onEndTime}
			</if>
			<if test="param.shelfState != null and param.shelfState == 1">
				and on_start_time <![CDATA[ > ]]> now()
			</if>
			<if test="param.shelfState != null and param.shelfState == 2">
                and on_start_time <![CDATA[ <= ]]> now() and on_end_time <![CDATA[ >= ]]> now()
            </if>
			<if test="param.shelfState != null and param.shelfState == 3">
				and on_end_time <![CDATA[ < ]]> now()
			</if>
			<if test="param.state != null">
				and `state` = #{param.state}
			</if>
		</where>
		order by id desc
	</select>

	<select id="selectListByParam" resultType="com.dz.ms.product.entity.Shelf">
		select
		<include refid="Base_Column_List"/>
		from shelf
		<where>
			is_deleted = 0
			<if test=" param.name != null and param.name != ''  ">
				and `name` like CONCAT('%', #{param.name},'%')
			</if>
			<if test="param.onStartTime != null">
				AND on_start_time <![CDATA[ >= ]]> #{param.onStartTime}
			</if>
			<if test="param.onEndTime != null">
				AND on_end_time <![CDATA[ <= ]]> #{param.onEndTime}
			</if>
			<if test="param.shelfState != null and param.shelfState == 1">
				and on_start_time <![CDATA[ > ]]> now()
			</if>
			<if test="param.shelfState != null and param.shelfState == 2">
				and now() between on_start_time and on_end_time
			</if>
			<if test="param.shelfState != null and param.shelfState == 3">
				and on_end_time <![CDATA[ < ]]> now()
			</if>
		</where>
	</select>

	<select id="selLessList" resultType="com.dz.ms.product.entity.Shelf">
		select
		id,
		`name`
		from `shelf`
		<where>
			is_deleted = 0
			<if test=" param.name != null and param.name != ''  ">
				and `name` like CONCAT('%', #{param.name},'%')
			</if>
			<if test="param.onStartTime != null">
				AND on_start_time <![CDATA[ >= ]]> #{param.onStartTime}
			</if>
			<if test="param.onEndTime != null">
				AND on_end_time <![CDATA[ <= ]]> #{param.onEndTime}
			</if>
			<if test="param.shelfState != null and param.shelfState == 1">
				and on_start_time <![CDATA[ > ]]> now()
            </if>
            <if test="param.shelfState != null and param.shelfState == 2">
                and on_start_time <![CDATA[ <= ]]> now() and on_end_time <![CDATA[ >= ]]> now()
            </if>
            <if test="param.shelfState != null and param.shelfState == 3">
                and on_end_time <![CDATA[ < ]]> now()
            </if>
            <if test=" param.state != null">
                and state = #{param.state}
            </if>
            <if test="null != param.shelfIdList and param.shelfIdList.size > 0">
                and id in
                <foreach collection="param.shelfIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
	</select>
	
	<select id="selAllList" resultType="com.dz.ms.product.entity.Shelf">
		select
			<include refid="Base_Column_List"/>
		from shelf
		<where>
			is_deleted = 0
			<if test=" param.name != null and param.name != ''  ">
				and `name` like CONCAT('%', #{param.name},'%')
			</if>
			<if test="param.onStartTime != null">
				AND on_start_time <![CDATA[ >= ]]> #{param.onStartTime}
			</if>
			<if test="param.onEndTime != null">
				AND on_end_time <![CDATA[ <= ]]> #{param.onEndTime}
			</if>
			<if test="param.shelfState != null and param.shelfState == 1">
                and on_start_time <![CDATA[ > ]]> now()
            </if>
            <if test="param.shelfState != null and param.shelfState == 2">
                and on_start_time <![CDATA[ <= ]]> now() and on_end_time <![CDATA[ >= ]]> now()
            </if>
            <if test="param.shelfState != null and param.shelfState == 3">
                and on_end_time <![CDATA[ < ]]> now()
            </if>
            <if test="param.state != null">
                and state = #{param.state}
            </if>
            <if test="null != param.shelfIdList and param.shelfIdList.size > 0">
                and id in
                <foreach collection="param.shelfIdList" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
		</where>
	</select>
	<select id="selNoLimitList" resultType="com.dz.ms.product.entity.Shelf">
		select
			id,
			`name`,
			`state`,
			is_deleted
		from `shelf`
		<where>
			<if test=" param.name != null and param.name != ''  ">
				and `name` like CONCAT('%', #{param.name},'%')
			</if>
			<if test="param.onStartTime != null">
				AND on_start_time <![CDATA[ >= ]]> #{param.onStartTime}
			</if>
			<if test="param.onEndTime != null">
				AND on_end_time <![CDATA[ <= ]]> #{param.onEndTime}
			</if>
			<if test="param.shelfState != null and param.shelfState == 1">
				and on_start_time <![CDATA[ > ]]> now()
			</if>
			<if test="param.shelfState != null and param.shelfState == 2">
				and on_start_time <![CDATA[ <= ]]> now() and on_end_time <![CDATA[ >= ]]> now()
			</if>
			<if test="param.shelfState != null and param.shelfState == 3">
				and on_end_time <![CDATA[ < ]]> now()
			</if>
			<if test="null != param.noLimitShelfIds and param.noLimitShelfIds.size > 0">
				and id in
				<foreach collection="param.noLimitShelfIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
		</where>
	</select>

	<update id="updGroupIdIntoNull">
		update shelf
		set group_id = null
		where group_id = #{groupId}
	</update>

	<update id="updateStatic">
		update shelf
		set exchange_people  = #{exchangePeople},
			exchange_order   = exchange_order + 1,
			exchange_num     = exchange_num + #{orderNum},
			exchange_point   = exchange_point + #{orderPoint},
			exchange_avg_num = (exchange_num / exchange_people)
		where id = #{shelfId}
	</update>
</mapper>
