package com.dz.common.core.enums;

import java.util.Objects;

/**
 * 抽奖奖品白名单类行枚举
 * <AUTHOR>
 */
public enum PrizesTargetTypeEnum {
    NONE(-1, "不设置白名单"),
    FILTER(1, "按条件选择"),
    CROWDS(2, "导入人群包");

    private final Integer code;
    private final String value;

    PrizesTargetTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByCode(Integer code) {
        PrizesTargetTypeEnum targetTypeEnum = getByCode(code);
        return targetTypeEnum==null ? null : targetTypeEnum.value;
    }

    public static PrizesTargetTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PrizesTargetTypeEnum resultEnum : PrizesTargetTypeEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum;
            }
        }
        return null;
    }
}
