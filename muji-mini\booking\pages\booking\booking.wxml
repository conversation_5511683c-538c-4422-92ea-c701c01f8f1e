<my-page overallModal="{{overallModal}}" loading="{{loading}}">
  <view class="pageWrap isNotBooked" wx:if="{{resData.alreadyBooked===false}}">
    <custom-header background="transparent" type="{{1}}" isBackHidden="{{ isBackHidden }}" isFixed />
    <image class="mainImage" src="{{$cdn}}/booking/booking/mainImage.png" mode="widthFix" />
    <block wx:if="{{resData.expired}}">
      <view class="noPermission">
        <image class="icon" src="{{$cdn}}/booking/booking/expired.png" mode="widthFix" />
        <view class="text">活动已结束</view>
      </view>
    </block>
    <block wx:elif="{{!resData.inWhitelist}}">
      <view class="noPermission">
        <image class="icon" src="{{$cdn}}/booking/booking/noInWhitelist.png" mode="widthFix" />
        <view class="text">限特邀会员参与，感谢您的关注</view>
      </view>
    </block>
    <block wx:else>
      <view class="mainContent">
        <!--<view class="title">金级、银级会员专享沙龙</view>-->
        <view class="subtitle">时间：3月15日-3月16日</view>
        <view class="subtitle">地址：上海市长宁区哈密路166号红坊2楼</view>
        <view class="subtitle">受邀会员参展可领取伴手礼1份。</view>
        <view class="subtitle">诚邀共赴，与美好相遇。</view>
        <view class="form">
          <view class="formItem">
            <view class="formSelectWrap" catch:tap="setDateVisibleShow">
              <view>{{formFields.appointmentDate ? appointmentDates[appointmentDateIndex].name : '请选择日期'}}</view>
              <view class="arrow"></view>
            </view>
            <view class="formSelectWrap {{!formFields.appointmentDate?'isDisabled':''}}" catch:tap="setSlotVisibleShow">
              <view>{{formFields.appointmentSlot ? appointmentSlots[appointmentSlotIndex].name : '请选择场次'}}</view>
              <view class="arrow"></view>
            </view>
          </view>
          <view class="formItem">
            <view class="formInputWrap">
              <view class="formInputLabel">姓名*</view>
              <input
                class="formInputValue" placeholder="请输入您的姓名" maxlength="10"
                catch:input="onInput" data-form-name="formFields" data-form-fields="{{formFields}}"
                data-field-name="name" value="{{formFields.name}}"
              />
            </view>
          </view>
          <view class="formItem">
            <view class="formInputWrap">
              <view class="formInputLabel">手机号码*</view>
              <input
                class="formInputValue" placeholder="请输入您的手机号码" maxlength="11"
                catch:input="onInput" data-form-name="formFields" data-form-fields="{{formFields}}"
                data-field-name="phone" value="{{formFields.phone}}"
              />
            </view>
          </view>
        </view>
      </view>
      <view class="resetBtn">
        <basic-button width="{{670}}" size="large" catch:tap="submit">确定预约</basic-button>
      </view>
      <view class="btnDesc">*届时您可携带一位亲友一同前往参加</view>
    </block>
  </view>
  <view class="pageWrap isAlreadyBooked" wx:if="{{resData.alreadyBooked===true}}">
    <custom-header background="transparent" type="{{1}}" isBackHidden="{{ isBackHidden }}" />
    <view class="mainWrap">
      <view class="mainContent">
        <view class="logoWrap">
          <image class="logo" src="{{$cdn}}/booking/booking/mainImage.png" mode="widthFix" />
        </view>
        <view class="itemWrap">
          <view class="item">
            <view class="title">预约成功</view>
            <view class="subtitle">为了您的体验，建议您于活动开始前10分钟到达。</view>
          </view>
          <view class="item isGrey">
            <view class="title2">2025年春夏商品展示会</view>
            <view class="subtitle2">上海市长宁区哈密路166号红坊2楼</view>
          </view>
          <view class="item isGrey">
            <view class="title3">预约日期</view>
            <view class="subtitle3">{{resData.userAppointment.appointmentDate}} {{resData.userAppointment.appointmentTime}}</view>
            <view class="title3">姓名</view>
            <view class="subtitle3">{{resData.userAppointment.userName}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
  <my-picker-view
    show="{{appointmentDateShow}}" title="请选择日期"
    index="{{appointmentDateIndex}}" range="{{appointmentDates}}"
    catch:close="setDateVisibleHide" catch:confirm="bindPickerChangeDate"
  />
  <my-picker-view
    show="{{appointmentSlotShow}}" title="请选择场次"
    index="{{appointmentSlotIndex}}" range="{{appointmentSlots}}"
    catch:close="setSlotVisibleHide" catch:confirm="bindPickerChangeSlot"
  />
</my-page>
