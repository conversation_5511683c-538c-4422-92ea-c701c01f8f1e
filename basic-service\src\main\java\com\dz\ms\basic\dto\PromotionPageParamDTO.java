package com.dz.ms.basic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "页面推广参数获取")
public class PromotionPageParamDTO {
    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "一级渠道名称")
    private String oneChannelName;
    @ApiModelProperty(value = "一级渠道参数")
    private String oneChannelParam;
    @ApiModelProperty(value = "二级渠道名称")
    private String towChannelName;
    @ApiModelProperty(value = "二级渠道参数")
    private String towChannelParam;
}
