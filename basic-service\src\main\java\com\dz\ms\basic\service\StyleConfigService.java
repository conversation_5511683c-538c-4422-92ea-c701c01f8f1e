package com.dz.ms.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.ms.basic.dto.NavigationConfigDTO;
import com.dz.ms.basic.dto.StyleConfigDTO;
import com.dz.ms.basic.entity.NavigationConfig;
import com.dz.ms.basic.entity.StyleConfig;

import java.util.List;

/**
 * 小程序UI自定义配置接口
 * @author: Handy
 * @date:   2022/11/21 15:00
 */
public interface StyleConfigService extends IService<StyleConfig> {


    StyleConfigDTO getStyleConfigList(Long tenantId);

    Long saveStyleConfig(StyleConfigDTO param);

    StyleConfigDTO getStyleConfigById(Long id);
}
