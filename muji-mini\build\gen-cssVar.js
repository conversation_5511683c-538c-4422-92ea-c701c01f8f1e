const fs = require('fs')

let content = fs.readFileSync(`${__dirname}/../assets/scss/common/_cssVar.scss`, 'utf-8')
content = content.replace(/\/\*([\s\S]*?)\*\//ig, '') // 移除多行注释
content = content.replace(/\/\/.*/ig, '') // 移除单行注释
const arr = content.match(/--.*:.*;/ig).map(v => {
  v = v.replace(';', '')
  v = v.split(/:\s*/)
  return v
})
const obj = arr.reduce((r, v, i, a) => {
  return {
    ...r,
    [v[0]]: v[1]
  }
}, {})
const fileContent = `wx.$cssVar = ${JSON.stringify(obj, null, '\t')}\n`
fs.writeFileSync(`${__dirname}/../utils/wx/cssVar.js`, fileContent, 'utf-8')
