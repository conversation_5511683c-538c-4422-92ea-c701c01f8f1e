package com.dz.common.core.enums;

import java.util.Objects;

/**
 * Describe：判定关系类型枚举
 * Created by 吴蜀黍 on 2023/8/20 8:36
 **/
public enum RelationshipEnum {
    JUST(0, "且"),
    PERHAPS(1, "或"),
    ;

    private final Integer code;
    private final String value;

    RelationshipEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RelationshipEnum resultEnum : RelationshipEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum.value;
            }
        }
        return null;
    }
}
