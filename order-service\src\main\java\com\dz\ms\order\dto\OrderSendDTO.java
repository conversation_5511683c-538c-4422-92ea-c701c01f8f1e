package com.dz.ms.order.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 订单发货信息DTO
 *
 * @author: LiinNs
 * @date: 2024/12/20 10:54
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "订单发货信息")
public class OrderSendDTO extends BaseDTO {

    @ApiModelProperty(value = "主键")
    private Long id;
    @ApiModelProperty(value = "订单编号")
    private String orderCode;
    @ApiModelProperty(value = "快递单号")
    private String expressCode;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "修改时间")
    private Date modified;
    @ApiModelProperty(value = "修改人")
    private Long modifier;

}
