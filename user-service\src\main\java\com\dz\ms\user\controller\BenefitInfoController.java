package com.dz.ms.user.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.user.dto.BenefitInfoDTO;
import com.dz.ms.user.service.BenefitInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags="会员权益")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class BenefitInfoController {

    @Resource
    private BenefitInfoService benefitInfoService;

    /**
     * 分页查询会员权益
     * @param param
     * @return result<PageInfo<BenefitInfoDTO>>
     */
    @ApiOperation("分页查询会员权益")
	@GetMapping(value = "/crm/benefit/list")
    public Result<PageInfo<BenefitInfoDTO>> getBenefitInfoList(@ModelAttribute BenefitInfoDTO param) {
        Result<PageInfo<BenefitInfoDTO>> result = new Result<>();
		PageInfo<BenefitInfoDTO> page = benefitInfoService.getBenefitInfoList(param);
        result.setData(page);
        return result;
    }

    @ApiOperation("查询所有会员权益")
    @GetMapping(value = "/crm/benefit/list_all")
    public Result<List<BenefitInfoDTO>> getAllBenefitInfoList(@ModelAttribute BenefitInfoDTO param) {
        Result<List<BenefitInfoDTO>> result = new Result<>();
        List<BenefitInfoDTO> list = benefitInfoService.getAllBenefitInfoList(param);
        result.setData(list);
        return result;
    }

    /**
     * 根据ID查询会员权益
     * @param id
     * @return result<BenefitInfoDTO>
     */
    @ApiOperation("根据ID查询会员权益")
	@GetMapping(value = "/crm/benefit/info")
    public Result<BenefitInfoDTO> getBenefitInfoById(@RequestParam("id") Long id) {
        Result<BenefitInfoDTO> result = new Result<>();
        BenefitInfoDTO benefitInfo = benefitInfoService.getBenefitInfoById(id);
        result.setData(benefitInfo);
        return result;
    }

    /**
     * 新增会员权益
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增会员权益",type = LogType.OPERATELOG)
    @ApiOperation("新增会员权益")
	@PostMapping(value = "/crm/benefit/add")
    public Result<Long> addBenefitInfo(@RequestBody BenefitInfoDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,true);
        Long id = benefitInfoService.saveBenefitInfo(param);
        result.setData(id);
        return result;
    }

    /**
    * 更新会员权益
    * @param param
    * @return result<Object>
    */
    @SysLog(value = "更新会员权益",type = LogType.OPERATELOG)
    @ApiOperation("更新会员权益")
    @PostMapping(value = "/crm/benefit/update")
    public Result<Long> updateBenefitInfo(@RequestBody BenefitInfoDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,false);
        benefitInfoService.saveBenefitInfo(param);
        result.setData(param.getId());
        return result;
    }

    /**
    * 验证保存参数
    * @param param
    */
    private void validationSaveParam(BenefitInfoDTO param,boolean isAdd) {
        if(isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if(!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
        if(isAdd && StringUtils.isBlank(param.getBenefitName())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "权益名称不能为空");
        }
    }

	/**
     * 根据ID删除会员权益
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除会员权益")
	@PostMapping(value = "/crm/benefit/delete")
    public Result<Boolean> deleteBenefitInfoById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        benefitInfoService.deleteBenefitInfoById(param);
        result.setData(true);
        return result;
    }

}
