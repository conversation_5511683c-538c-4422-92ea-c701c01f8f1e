package com.dz.ms.product.dto;

import com.dz.common.base.dto.BaseDTO;
import com.dz.common.core.dto.user.CrowdDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 营销活动规则DTO
 *
 * @author: LiinNs
 * @date: 2024/11/20 11:39
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "营销活动规则")
public class ShelfCampaignRuleDTO extends BaseDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "营销活动ID")
    private Long campaignId;
    @ApiModelProperty(value = "规则名称 展示位活动角标")
    private String name;
    @ApiModelProperty(value = "人群包ID")
    private Long groupId;
    @ApiModelProperty(value = "规则组件配置")
    private String content;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "1活动时间内 2周期")
    private Integer ruleType;
    @ApiModelProperty(value = "周期开始时间")
    private Date cycleCreated;
    @ApiModelProperty(value = "周期天数")
    private Integer period;
    @ApiModelProperty(value = "规则范围内每个用户限购数量")
    private Integer ruleNum;

    @ApiModelProperty(value = "人群包")
    private CrowdDTO crowdDTO;
    
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "创建时间")
    private Date created;

}
