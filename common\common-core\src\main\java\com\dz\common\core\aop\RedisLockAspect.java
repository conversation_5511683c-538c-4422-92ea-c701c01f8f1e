package com.dz.common.core.aop;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.core.annotation.Lock;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.CodeSignature;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Aspect
public class RedisLockAspect {

    @Resource
    private RedisService redisService;

    public RedisLockAspect() {}


    @Around("@annotation(locK)")
    public Object around(ProceedingJoinPoint pjp, Lock locK) throws Throwable {
        if (this.redisService == null) {
            log.error("未配置redisTemplate,使用@Lock失败");
            return pjp.proceed();
        } else {
            String cacheKey = this.getCacheKey(pjp, locK.prefix(), locK.key());
            log.info("获取缓存锁key:{}", cacheKey);
            boolean isLock = redisService.lock(cacheKey,locK.expire());
            if(!isLock) {
                throw new BusinessException(ErrorCode.INTERNAL_ERROR, "请求中请稍后");
            }
            try {
                return pjp.proceed();
            } catch (Throwable throwable) {
                throw throwable;
            } finally {
                redisService.unlock(cacheKey);
            }
        }
    }

    private String getCacheKey(JoinPoint pjp, String cacheName, String keyExpression) throws Exception {
        cacheName = cacheName.replaceAll("'","");
        StringBuffer cacheKey = new StringBuffer();
        cacheKey.append(cacheName).append(":");
        if(StringUtils.isNotBlank(keyExpression)) {
            Map<String, Object> param = new HashMap<>();
            if(keyExpression.contains("#")) {
                Object[] paramValues = pjp.getArgs();
                String[] paramNames = ((CodeSignature)pjp.getSignature()).getParameterNames();
                for (int i = 0; i < paramNames.length; i++) {
                    param.put(paramNames[i], paramValues[i]);
                }
            }
            String[] keys = this.getKeys(keyExpression);
            int length = keys.length;
            for(int i = 0; i < length; ++i) {
                String key = keys[i];
                if(key.length() == 0) {
                    continue;
                }
                if(key.charAt(0) == '#') {
                    key = key.substring(1);
                    int dotIdx = key.indexOf(".");
                    Object value = null;
                    if (dotIdx > 0) {
                        String propertyName = key.substring(0, dotIdx);
                        key = key.substring(dotIdx + 1);
                        value = param.get(propertyName);
                        String getter = "get" + Character.toUpperCase(key.charAt(0)) + key.substring(1);
                        value = value.getClass().getMethod(getter).invoke(value);
                    }
                    else {
                        value = param.get(key);
                    }
                    if(null == value && (("tenantId").equals(key) || ("uid").equals(key))) {
                        value = SecurityContext.getUser();
                        String getter = "get" + Character.toUpperCase(key.charAt(0)) + key.substring(1);
                        value = value.getClass().getMethod(getter).invoke(value);
                    }
                    if(null == value) {
                        cacheKey.append("null").append(":");
                    }
                    else {
                        cacheKey.append(value.toString()).append(":");
                    }
                }
                else {
                    cacheKey.append(key).append(":");
                }
            }
        }
        if (cacheKey.charAt(cacheKey.length() - 1) == ':') {
            cacheKey.deleteCharAt(cacheKey.length() - 1);
        }
        return cacheKey.toString();
    }

    private String[] getKeys(String key) {
        if (key.startsWith("'") && key.endsWith("'")) {
            String[] keyArr = key.split("\\+");
            boolean checkError = Arrays.asList(keyArr).stream().anyMatch((it) -> {
                return !it.trim().endsWith("'") || !it.trim().startsWith("'");
            });
            if (checkError) {
                throw new BusinessException("bad cache key " + key);
            } else {
                for(int i = 0; i < keyArr.length; ++i) {
                    String ele = keyArr[i].trim();
                    ele = ele.substring(1);
                    ele = ele.substring(0, ele.length() - 1);
                    keyArr[i] = ele.trim();
                }

                return keyArr;
            }
        } else {
            return new String[]{key};
        }
    }

}