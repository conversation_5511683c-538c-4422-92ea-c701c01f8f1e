package com.dz.ms.product.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.product.dto.TaskLogDTO;
import com.dz.ms.product.service.TaskLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "日志-货架商品库存任务")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class TaskLogController {

    @Resource
    private TaskLogService taskLogService;

    /**
     * 分页查询日志-货架商品库存任务
     *
     * @param param
     * @return result<PageInfo < TaskLogDTO>>
     */
    @ApiOperation("分页查询日志-货架商品库存任务")
    @GetMapping(value = "/task_log/list")
    public Result<PageInfo<TaskLogDTO>> getTaskLogList(@ModelAttribute TaskLogDTO param) {
        Result<PageInfo<TaskLogDTO>> result = new Result<>();
        PageInfo<TaskLogDTO> page = taskLogService.getTaskLogList(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询日志-货架商品库存任务
     *
     * @param id
     * @return result<TaskLogDTO>
     */
    @ApiOperation("根据ID查询日志-货架商品库存任务")
    @GetMapping(value = "/task_log/info")
    public Result<TaskLogDTO> getTaskLogById(@RequestParam("id") Long id) {
        Result<TaskLogDTO> result = new Result<>();
        TaskLogDTO taskLog = taskLogService.getTaskLogById(id);
        result.setData(taskLog);
        return result;
    }

    /**
     * 新增日志-货架商品库存任务
     *
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增日志-货架商品库存任务", type = LogType.OPERATELOG)
    @ApiOperation("新增日志-货架商品库存任务")
    @PostMapping(value = "/task_log/add")
    public Result<Long> addTaskLog(@RequestBody TaskLogDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param, true);
        Long id = taskLogService.saveTaskLog(param);
        result.setData(id);
        return result;
    }

    /**
     * 更新日志-货架商品库存任务
     *
     * @param param
     * @return result<Object>
     */
    @SysLog(value = "更新日志-货架商品库存任务", type = LogType.OPERATELOG)
    @ApiOperation("更新日志-货架商品库存任务")
    @PostMapping(value = "/task_log/update")
    public Result<Long> updateTaskLog(@RequestBody TaskLogDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param, false);
        taskLogService.saveTaskLog(param);
        result.setData(param.getId());
        return result;
    }

    /**
     * 验证保存参数
     *
     * @param param
     */
    private void validationSaveParam(TaskLogDTO param, boolean isAdd) {
        if (isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if (!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
    }

    /**
     * 根据ID删除日志-货架商品库存任务
     *
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除日志-货架商品库存任务")
    @PostMapping(value = "/task_log/delete")
    public Result<Boolean> deleteTaskLogById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        taskLogService.deleteTaskLogById(param);
        result.setData(true);
        return result;
    }

}
