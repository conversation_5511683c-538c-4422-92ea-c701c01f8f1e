package com.dz.ms.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.basic.dto.NavigationConfigDTO;
import com.dz.ms.basic.entity.NavigationConfig;
import com.dz.ms.basic.entity.StyleConfig;
import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 小程序UI自定义配置Mapper
 * @author: Handy
 * @date:   2022/11/21 11:17
 */
@Repository
public interface StyleConfigMapper extends BaseMapper<StyleConfig> {


    StyleConfig selectStyleConfigList(@Param("tenantId") Long tenantId);
}
