package com.dz.ms.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.constant.ClientTypeConstant;
import com.dz.common.core.constants.CommonConstants;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.dto.user.openapi.param.OpenApiTokenParamDTO;
import com.dz.common.core.dto.user.openapi.response.OpenApiDataDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.ms.user.constants.CacheKeys;
import com.dz.ms.user.entity.OpenApiAccount;
import com.dz.ms.user.mapper.OpenApiAccountMapper;
import com.dz.ms.user.service.OpenApiAccountService;
import com.dz.ms.user.utils.JwtTokenUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * OpenApi账号(OpenApiAccount)表服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
public class OpenApiAccountServiceImpl extends ServiceImpl<OpenApiAccountMapper, OpenApiAccount> implements OpenApiAccountService {

    @Resource
    private OpenApiAccountMapper openApiAccountMapper;
    @Resource
    private RedisService redisService;

    @Override
    public OpenApiDataDTO getToken(OpenApiTokenParamDTO param) {
        SecurityContext.setUser(new CurrentUserDTO(null,null,1L,null));
        if(StringUtils.isBlank(param.getAppId())){
            throw new BusinessException("请输入AppId");
        }
        if(StringUtils.isBlank(param.getAppSecret())){
            throw new BusinessException("请选择AppKey");
        }
        LambdaQueryWrapper<OpenApiAccount> openApiAccountQuery = new LambdaQueryWrapper<>();
        openApiAccountQuery.eq(OpenApiAccount::getAppId, param.getAppId());
        openApiAccountQuery.eq(OpenApiAccount::getAppSecret, param.getAppSecret());
        List<OpenApiAccount> openApiAccountList = openApiAccountMapper.selectList(openApiAccountQuery);
        if(CollectionUtils.isEmpty(openApiAccountList)){
            throw new BusinessException("请选择AppKey");
        }
        OpenApiAccount openApiAccount = openApiAccountList.get(0);

        SecurityContext.setUser(new CurrentUserDTO(ClientTypeConstant.OPENAPI,openApiAccount.getId(),openApiAccount.getTenantId(),null));
        String uuid = UUID.randomUUID().toString();
        Map<String, Object> claims = new HashMap<>();
        claims.put("id", openApiAccount.getId());
        claims.put("type", ClientTypeConstant.OPENAPI);
        claims.put("tid", openApiAccount.getTenantId());
        claims.put("key", openApiAccount.getAppId());
        String token = JwtTokenUtils.generatorToken(claims,uuid,24);
        OpenApiDataDTO openApiDTO = new OpenApiDataDTO();
        openApiDTO.setToken(token);
        openApiDTO.setExpireTime(CommonConstants.DAY_SECONDS);
        redisService.setString(CacheKeys.OPENUSER_SECRET + openApiAccount.getAppId(),uuid, CommonConstants.DAY_SECONDS);
        log.info("OpenApi账号获取token,账号:{},token:{}",openApiAccount.getAppId(),token);
        return openApiDTO;
    }


}
