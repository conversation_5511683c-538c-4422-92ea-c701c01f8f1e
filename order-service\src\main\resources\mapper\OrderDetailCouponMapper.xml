<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.order.mapper.OrderDetailCouponMapper">

	<!-- 查询基础字段 -->
	<sql id="Base_Column_List">
		id,
  	    order_code,
  	    order_detail_id,
  	    product_id,
  	    vender_id,
		coupon_code,
		stock_id,
  	    status,
  	    tenant_id,
  	    created,
  	    modified,
  	    is_deleted
	</sql>

	<select id="getUnusedStockIdListByUserId" resultType="java.lang.String">
		SELECT DISTINCT odc.stock_id
		FROM order_detail_coupon odc
				 LEFT JOIN exchange_order eo ON odc.order_code = eo.order_code
		WHERE odc.status = 1
		  AND eo.user_id = #{userId}
	</select>

</mapper>
