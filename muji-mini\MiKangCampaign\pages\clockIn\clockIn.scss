/* MiKangCampaign/pages/clockIn/clockIn.wxss */
.page-container {
    // background-color: rgba(255, 235, 205, 0.534);
    background-size: 100% 100%;
    overflow: hidden;

    .custom-header-more {
        color: #ffffff;
    }

    .right-lanyard {
        position: relative;
        display: inline-block;
        height: 0;
        z-index: 999;
        text-align: left;

        image {
            position: absolute;
            left: -24rpx;
            top: -186rpx;
            transform: rotateY(180deg);
        }
    }

    .page-content {
        padding-top: 34rpx;
        padding-bottom: 100rpx;
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
    }

    .clockIn-title {
        height: 132rpx;
        // background-color: #F8F6ED;
        display: flex;
        justify-content: flex-end;
        font-family: "MUJIFont2020";
        // font-weight: 400;
        font-size: 40rpx;
        color: #3c3c43;
        // line-height: 47rpx;
        text-align: right;
        font-style: normal;
        text-transform: none;
        // padding-right: 40rpx;
        align-items: center;
        border-bottom: 2rpx dashed #71655299;
        margin: 0 40rpx;

        &-wrap {
            height: 79rpx;
            line-height: 79rpx;
            display: flex;
            align-items: flex-end;
            margin-left: 30rpx;
        }

        .num {
            // width: 91rpx;
            // height: 79rpx;
            font-family: MUJIFont2020;
            font-weight: 700;
            font-size: 56rpx;
            color: #3c3c43;
            line-height: 86rpx;
            text-align: center;
            font-style: normal;
            text-transform: none;
            margin-left: 20rpx;
        }
    }

    .clockIn {
        flex-shrink: 0;
        width: 670rpx;
        height: 100%;
        position: relative;
        box-sizing: border-box;
        background-size: 100% 100%;
        margin: 0 40rpx 0 40rpx;

        .clockIn-wrap {
            // flex: 1;
            height: 100%;
            width: 100%;
            box-sizing: border-box;
            padding: 0 40rpx;
        }

        .clockIn-scroll {
            width: 100%;
            height: calc(100% - 132rpx);
        }

        // .clockIn-title {
        //   height: 132rpx;
        //   // background-color: #F8F6ED;
        //   display: flex;
        //   justify-content: flex-end;
        //   font-family: "MUJIFont2020";
        //   font-weight: 400;
        //   font-size: 40rpx;
        //   color: #3C3C43;
        //   // line-height: 47rpx;
        //   text-align: right;
        //   font-style: normal;
        //   text-transform: none;
        //   padding-right: 40rpx;
        //   align-items: center;

        //   &-wrap {
        //     height: 79rpx;
        //     line-height: 79rpx;
        //     display: flex;
        //     align-items: flex-end;
        //     margin-left: 30rpx;
        //   }

        //   .num {
        //     // width: 91rpx;
        //     // height: 79rpx;
        //     font-family: "MUJIFont2020",
        //       "SourceHanSansCN";
        //     font-weight: 700;
        //     font-size: 48rpx;
        //     color: #3C3C43;
        //     line-height: 86rpx;
        //     text-align: center;
        //     font-style: normal;
        //     text-transform: none;
        //     margin-left: 20rpx;
        //   }

        // }

        .clockIn-icon {
            width: 14rpx;
            height: 14rpx;
            background: #c8b49a;
            border-radius: 50%;
            margin-top: 40rpx;
        }

        .score {
            // min-height: calc(100% - 173rpx);
            // background-color: white;
            // margin: 0 40rpx;
            padding-top: 36rpx;
            padding-bottom: 40rpx;

            .van-uploader {
                .van-uploader__wrapper {
                    .van-uploader__preview {
                        &:nth-child(3n) {
                            margin-right: 0rpx;
                        }
                    }
                }

                .van-uploader__preview-delete {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    text-align: center;
                    width: 32rpx;
                    height: 32rpx;
                    background: #24252899;
                    border-radius: 50%;
                    top: -5rpx;
                    right: -5rpx;
                    margin: 15rpx;

                    &::after {
                        display: none;
                    }

                    .van-uploader__preview-delete-icon {
                        position: relative;
                        transform: none;
                        font-size: 16rpx;
                        line-height: 32rpx;
                        text-align: center;
                    }
                }
            }

            .picture {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                width: 172rpx;
                height: 172rpx;
                background: #ffffff;
                font-weight: 700;
                font-size: 22rpx;
                line-height: 25rpx;
                text-align: center;
                border: 2rpx dashed #756453;
                margin-top: 20rpx;
                margin-bottom: 18rpx;

                .img {
                    width: 60rpx;
                    height: 60rpx;
                }

                .img-text {
                    font-weight: 700;
                    font-size: 22rpx;
                    margin-top: 14rpx;
                }
            }

            .textarea {
                width: 590rpx;
                position: relative;
                // padding-bottom: 41rpx;
                box-sizing: border-box;
                margin-bottom: 18rpx;
                font-size: 24rpx;
                line-height: 40rpx;

                .answer-textarea {
                    width: 100%;
                    height: 324rpx;
                    border: 2rpx solid #c8b49a;
                    box-sizing: border-box;
                    margin-top: 20rpx;

                    textarea {
                        width: calc(100% - 48rpx);
                        box-sizing: border-box;
                        margin: 24rpx;
                        font-size: 24rpx;
                        color: #3c3c43;
                        line-height: 29rpx;
                        letter-spacing: 2rpx;
                        text-align: left;
                    }
                }

                .textNum {
                    position: absolute;
                    right: 24rpx;
                    bottom: 24rpx;
                    font-weight: 400;
                    font-size: 20rpx;
                    color: #e0ceaa;
                    line-height: 29rpx;
                    letter-spacing: 3rpx;
                    text-align: right;
                }
            }

            .disabledFalse {
                border-top: 2rpx solid #c8b49a;

                .disabledFalse-textarea {
                    font-family: MUJIFont2020;
                    font-size: 32rpx;
                    font-weight: 700;
                    line-height: 48rpx;
                    color: #3c3c43;
                    margin-top: 48rpx;
                    margin-bottom: 20rpx;
                }
            }

            .prompt {
                font-family: MUJIFont2020;
                font-weight: 400;
                font-size: 24rpx;
                color: #3c3c43;
                line-height: 52rpx;
                margin-bottom: 36rpx;
            }

            .skin {
                display: flex;
                flex-wrap: wrap;
                margin-top: 20rpx;

                .skin-item {
                    width: 180.66rpx;
                    height: 64rpx;
                    box-sizing: border-box;
                    border-radius: 8rpx;
                    border: 2rpx solid #756453;
                    font-family: MUJIFont2020;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #756453;
                    line-height: 62rpx;
                    text-align: center;
                    margin-right: 24rpx;
                    margin-bottom: 24rpx;

                    &:nth-child(3n) {
                        margin-right: 0rpx;
                    }
                }

                .active {
                    background: #756453;
                    color: #ffffff;
                }
            }

            .answer {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                margin-bottom: 38rpx;
                box-sizing: border-box;

                .answer-title {
                    font-family: MUJIFont2020;
                    font-weight: 700;
                    font-size: 32rpx;
                    color: #3c3c43;
                    line-height: 48rpx;
                    margin-bottom: 4rpx;
                    display: flex;
                    flex-direction: column;
                    justify-content: start;

                    .text {
                        font-size: 24rpx;
                        font-weight: 400;
                    }
                }

                .answer-title3 {
                    display: flex;
                    flex-direction: row;
                    justify-content: start;
                    align-items: center;

                    .answer-text {
                        padding-left: 5rpx;
                    }
                }

                .answer-title4 {
                    // font-weight: 400;
                    // font-size: 24rpx;
                    // line-height: 40rpx;
                    // color: #756453;
                    // display: flex;
                    // flex-direction: row;
                    // justify-content: start;
                }

                .answer-text {
                    height: 32rpx;
                    font-family: MUJIFont2020;
                    font-weight: 400;
                    font-size: 20rpx;
                    color: #888888;
                    line-height: 36rpx;
                    // letter-spacing: 1rpx;
                    text-align: left;
                }

                .answer-star {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    width: 100%;

                    .van-icon__image {
                        width: 32rpx;
                        height: 48rpx;
                    }

                    .num {
                        font-family: MUJIFont2020;
                        font-weight: 400;
                        font-size: 32rpx;
                        color: #756453;
                        line-height: 40rpx;
                        letter-spacing: 19%;
                        text-align: center;
                        // margin-left: 40rpx;

                        .key {
                            font-family: MUJIFont2020;
                            font-weight: 400;
                            font-size: 24rpx;
                            color: #756453b2;
                            letter-spacing: 19%;
                        }
                    }
                }
            }
            .answer-detail {
                margin-bottom: 48rpx;
            }

            // .answer1 {
            // border-bottom: 1rpx solid #D8D8D8;
            // }
        }
    }

    .bottom-box {
        display: flex;
        justify-content: center;
    }
}
