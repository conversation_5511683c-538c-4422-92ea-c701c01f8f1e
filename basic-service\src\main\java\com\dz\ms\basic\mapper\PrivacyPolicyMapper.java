package com.dz.ms.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.basic.dto.PrivacyPolicyDTO;
import com.dz.ms.basic.entity.PrivacyPolicy;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;

/**
 * 隐私条款Mapper
 * @author: Handy
 * @date:   2023/05/17 16:50
 */
@Repository
public interface PrivacyPolicyMapper extends BaseMapper<PrivacyPolicy> {

    /** 获取最新隐私条款版本号 */
    String selectLastPrivacyPolicyVersion(@Param("currentTime") Date currentTime);

    /** 获取最新隐私条款内容 */
    PrivacyPolicyDTO selectLastPrivacyPolicyInfo(@Param("currentTime") Date currentTime);

}
