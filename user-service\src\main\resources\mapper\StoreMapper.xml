<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.user.mapper.StoreMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    id,
  	    store_sn,
  	    store_name,
  	    type,
  	    status,
  	    province,
  	    city,
  	    area,
  	    longitude,
  	    latitude,
  	    email,
  	    phone,
  	    store_address,
  	    images,
  	    wework_images,
  	    opening_hour,
  	    is_default,
  	    create_time,
  	    creator,
  	    update_time,
  	    modifier,
  	    tenant_id
    </sql>
	<insert id="insertBatchSomeColumn">
			INSERT INTO store (
			store_sn, store_name, store_address, phone,
			longitude, latitude, province, city,
			opening_hour_one, opening_hour_two, open_date,
			creator, create_time, update_time, tenant_id
			) VALUES
			<foreach collection="storeToSave" item="store" separator=",">
				(
				#{store.storeSn}, #{store.storeName}, #{store.storeAddress}, #{store.phone},
				#{store.longitude}, #{store.latitude}, #{store.province}, #{store.city},
				#{store.openingHourOne}, #{store.openingHourTwo}, #{store.openDate},
				#{store.creator}, #{store.createTime}, #{store.updateTime}, #{store.tenantId}
				)
			</foreach>
	</insert>
	<update id="updateBatchById" parameterType="java.util.List">
		<foreach collection="storesToUpdate" item="store" separator=";">
			UPDATE store
			SET
			store_name = #{store.storeName},
			store_address = #{store.storeAddress},
			phone = #{store.phone},
			longitude = #{store.longitude},
			latitude = #{store.latitude},
			province = #{store.province},
			city = #{store.city},
			opening_hour_one = #{store.openingHourOne},
			opening_hour_two = #{store.openingHourTwo},
			open_date = #{store.openDate},
			update_time = #{store.updateTime}
			WHERE id = #{store.id}
		</foreach>
	</update>


	<!-- 查询示例 -->
    <select id="selectDemo" parameterType="java.lang.Long" resultType="com.dz.ms.user.entity.Store">
        select
        <include refid="Base_Column_List" />
        from store
        where id = #{id,jdbcType=BIGINT}
        AND tenant_id = #{tenantId}
    </select>

    <select id="getLongitudeLatitude" resultType="com.dz.ms.user.entity.Store">
		select *
		from store where longitude is not null and latitude is not null
					 and province != '廃止店' and is_close=0 and city != '-' and city != '其他'
    </select>
    <select id="selectStoreProvince" resultType="java.lang.String">
        select province
        from store
        where province IS NOT NULL
          AND province != ''
        group by province
    </select>
    <select id="selectProvince" resultType="java.lang.String">
        select province
        from provincial
    </select>
    <select id="selectStoreCity" resultType="java.lang.String">
        select city
        from store
        where city IS NOT NULL
          AND city != ''
        group by city
    </select>
    <select id="selectCity" resultType="java.lang.String">
        select city
        from city
    </select>
    <select id="selectOneNew" resultType="com.dz.ms.user.entity.Store">
		select
		<include refid="Base_Column_List" />
		from store
		where store_sn = #{storeSn}
		AND tenant_id = 1
	</select>

</mapper>
