<template>

  <layout>
    <template v-slot:header>
      <searchForm :formParams="formParams" @cancel="resetData" @ok="refreshData" :disabled="!$hasPermission('store:list:search')">
        <a-form-item name="storeSn">
          <a-input placeholder="门店编号" allow-clear v-model:value="formParams.storeSn" allowClear @keyup.enter="refreshData"></a-input>
        </a-form-item>
        <a-form-item name="storeName">
          <a-input placeholder="门店名称" allow-clear v-model:value="formParams.storeName" allowClear @keyup.enter="refreshData"></a-input>
        </a-form-item>
        <a-form-item name="serveIds">
          <a-select ref="select" :max-tag-count="1" v-model:value="formParams.serveIds" allowClear :disabled="disabled" :options="serveIdsOptions" :fieldNames="{ label: 'name', value: 'id' }" :maxTagTextLength="5" optionFilterProp="name" mode="tags" showSearch placeholder="关联服务门店">

            <template #maxTagPlaceholder="omittedValues">
              <span :title="TreeStr" :max="changeStr(omittedValues)">{{changeStr(omittedValues)}}</span>
            </template>
          </a-select>
        </a-form-item>
        <a-form-item name="isUpdate" label="">
          <a-select ref="select" v-model:value="formParams.isUpdate" allowClear :disabled="disabled" :options="isUpdateServeOptions" :fieldNames="{ label: 'label', value: 'value' }" optionFilterProp="label" showSearch placeholder="是否手动编辑过"></a-select>
        </a-form-item>
        <a-form-item name="type" label="">
          <a-select ref="select" v-model:value="formParams.type" allowClear :disabled="disabled" :options="storetypeOptions" :fieldNames="{ label: 'label', value: 'value' }" optionFilterProp="label" showSearch placeholder="门店类型"></a-select>
        </a-form-item>
        <a-form-item name="isStoreServe" label="">
          <a-select ref="select" v-model:value="formParams.isStoreServe" allowClear :disabled="disabled" :options="isStoreServeOptions" :fieldNames="{ label: 'label', value: 'value' }" optionFilterProp="label" showSearch placeholder="是否关联门店服务"></a-select>
        </a-form-item>
        <a-form-item name="isStoreImage">
          <a-select ref="select" v-model:value="formParams.isStoreImage" allowClear :disabled="disabled" :options="isStoreImageOptions" :fieldNames="{ label: 'label', value: 'value' }" optionFilterProp="label" showSearch placeholder="是否关联门店照片"></a-select>
        </a-form-item>
        <a-form-item name="isWeWorkImage">
          <a-select ref="select" v-model:value="formParams.isWeWorkImage" allowClear :disabled="disabled" :options="isWeWorkImageOptions" :fieldNames="{ label: 'label', value: 'value' }" optionFilterProp="label" showSearch placeholder="是否配置企微二维码"></a-select>
        </a-form-item>
        <a-form-item name="isOpen" label="">
          <a-select ref="select" v-model:value="formParams.isOpen" allowClear :disabled="disabled" :options="isOpenServeOptions" :fieldNames="{ label: 'label', value: 'value' }" optionFilterProp="label" showSearch placeholder="开业状态"></a-select>
        </a-form-item>
      </searchForm>
    </template>
    <template v-slot:topRight>
      <a-space>
        <!-- :disabled="!$hasPermission('open:leaveUser:synchLeaveUser')" 权限标识 -->

        <downloadFile type="primary" :disabled="!$hasPermission('store:list:import')" file-name="门店类型模版" file-type="excel" :api="storetemplate">导入门店类型模版下载
        </downloadFile>

        <uploadFile type="primary" :api="storeServeimport" @ok="crowdUploadSuccess" :disabled="!$hasPermission('store:list:derive')">导入门店类型
        </uploadFile>
        <!-- <a-button type="primary" >导入服务模版下载</a-button>
        <a-button type="primary" >批量导入服务</a-button> -->
      </a-space>

    </template>
    <template v-slot="{ height }">
      <a-table :indentSize="20" row-key="id" :scroll="{ scrollToFirstRowOnChange: true, y: height - 88, x: '100%' }" :dataSource="dataSource" :columns="tableHeader" :pagination="pagination" :loading="loading" @change="loadData">
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'index'">
            {{ (current - 1) * pageSize + 1 + index }}
          </template>
          <template v-if="column.key === 'serveNum'">
            <!-- :disabled="!$hasPermission('store:serve')" @click="handleAdd(record,1)" -->
            <div class=""> {{record.serveNum}}</div>
          </template>
          <template v-if="column.key === 'isStoreImage'">
            <!--  :disabled="!$hasPermission('store:update:image')" @click="handleAdd(record,2)" -->
            <div class="">{{record.isStoreImage == 2?'已关联':'未关联'}}</div>
          </template>
          <template v-if="column.key === 'isWeWorkImage'">
            <!-- :disabled="!$hasPermission('store:update:wework')" @click="handleAdd(record,3)" -->
            <div class=""> {{record.isWeWorkImage  == 2?'已配置':'未配置'}}</div>
          </template>

          <template v-if="column.key === 'action'">
            <a-button type="link" @click="storeEdit(record)" :disabled="!$hasPermission('store:list:edit')">编辑</a-button>
          </template>

        </template>
      </a-table>
    </template>

  </layout>
  <addStore :visible="visible" :addType="addType" :itemRow="itemRow" :title="title" @ok="updateList" @cancel="visible = false" :id="id" :type="type" />
  <editStore :visible="visibleEdit" @ok="updateList" @cancel="visibleEdit = false" :id="id" :type="type"></editStore>
</template>
<script setup>
import addStore from "./components/addStore.vue"
import editStore from './components/editStore.vue'
import { useRoute, useRouter } from 'vue-router'
import { usePagination, useRequest, } from 'vue-request';
import { storeList, serveList, storeServeimport, storetemplate } from '@/http/index.js'
import { cloneDeep } from 'lodash'
import { message, Modal } from "ant-design-vue";
import { isStoreServeOptions, isWeWorkImageOptions, isStoreImageOptions, storetypeOptions, isUpdateServeOptions, isOpenServeOptions } from '@/utils/dict-options'
// 分页参数
let pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ["bottomLeft"],
  };
});

function truncateText(text) {
  if (text.length > 5) {
    return text.slice(0, 5) + "...";
  } else {
    return text;
  }
}
const TreeStr = ref('')
const changeStr = (data) => {
  // console.log(data);
  let newArr = data.map(item => item.label)
  TreeStr.value = newArr.join("、")
  // console.log(TreeStr.value);
  return '+' + data.length
}

// 表格分页数据获取
const total = ref(0)

const { formParams, tableHeader, visible, id, type, serveIdsOptions, addType, title, itemRow, visibleEdit } = toRefs(reactive({

  title: '',
  id: '',
  type: 0,// 0-新增 1-编辑  2-查看
  visible: false,
  addType: 0,
  visibleEdit: false,
  serveIdsOptions: [],
  itemRow: {},
  formParams: {
    storeSn: '',
    storeName: '',
    serveIds: []
  },

  tableHeader: [{
    title: '序号',
    key: 'index',
    align: 'center',
    width: 80
  },
  {
    title: '门店编号',
    dataIndex: 'storeSn',
    width: 180,
    align: 'center',
  },
  {
    title: '门店名称',
    dataIndex: 'storeName',
    align: 'center',
    ellipsis: true,
    width: 180,
  },
  {
    title: '省市区',
    dataIndex: 'province',
    align: 'center',
    ellipsis: true,
    width: 180,
    customRender: (row) => {
      return row.text || '--'
    }
  },
  {
    title: '城市',
    dataIndex: 'city',
    align: 'center',
    ellipsis: true,
    width: 180,
    customRender: (row) => {
      return row.text || '--'
    }
  },
  {
    title: '门店地址',
    dataIndex: 'storeAddress',
    align: 'center',
    ellipsis: true,
    width: 200,

    customRender: (row) => {
      return row.text || '--'
    }
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
    align: 'center',
    ellipsis: true,
    width: 180,

    customRender: (row) => {
      return row.text || '--'
    }
  },
  {
    title: '门店类型',
    dataIndex: 'type',
    align: 'center',
    ellipsis: true,
    customRender: (row) => {
      // console.log(row);
      let textLabel = null
      if (typeof row.text == 'number') {
        textLabel = storetypeOptions.find(item => item.value == row.text)
      }
      // console.log(textLabel);
      return textLabel && textLabel.label || '--'; // 如果数据为空，则显示 '----' textLabel.label ||
    }
  },
  {
    title: '开业状态',
    dataIndex: 'isOpen',
    align: 'center',
    ellipsis: true,
    customRender: (row) => {
      // console.log(row);
      let textLabel = null
      if (typeof row.text == 'number') {
        textLabel = isOpenServeOptions.find(item => item.value == row.text)
      }
      // console.log(textLabel);
      return textLabel && textLabel.label || '--'; // 如果数据为空，则显示 '----' textLabel.label ||
    }
  },
  // {
  //   title: '门店服务',
  //   key: 'serveNum',
  //   align: 'center',
  //   ellipsis: true,
  //   width: 100,
  // },
  {
    title: '门店照片',
    key: 'isStoreImage',
    align: 'center',
    ellipsis: true,
    width: 100,
  },
  {
    title: '是否配置企微二维码',
    key: 'isWeWorkImage',
    align: 'center',
    ellipsis: true,
    width: 180,
  },


  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    align: 'center',
    width: 150,
    fixed: 'right'
  }
  ]
})
);
const getParams = () => {
  // 拷贝一份 然后处理数据
  let params = cloneDeep(formParams.value);
  // params.serveIds = params.serveIds.join(',')
  return params;
};
const { data: dataSource, run, loading, pageSize, current, refresh } = usePagination((param) => {
  // 这里修改参数
  // return resignationInheritanceList({ ...param, ...getParams() })
  return storeList({ ...param, ...getParams() })
},
  {
    manual: false, // 修改为false,让其自动执行
    pagination: {
      currentKey: 'pageNum',  // 通过该值指定接口 当前页数 参数的属性值
      pageSizeKey: 'pageSize',// 通过该值指定接口 每页获取条数 参数的属性值
      totalKey: 'data.count',  // 指定 data 中 total 属性的路径
    },
    formatResult: res => { // 返回数据格式化
      total.value = res.data.count
      return res.data.list
    }
  });
//服务列表
serveList({ status: 0 }).then(res => {
  serveIdsOptions.value = res.data
})
function updateList(value) {
  visible.value = false;
  visibleEdit.value = false
  // 新增 重置搜索数据
  if (!value) {
    resetData();
  } else {
    // 查看、编辑 不需要清空搜索数据 直接刷新
    refresh();
  }
}
function handleAdd(record, num) {
  visible.value = true
  type.value = 0
  id.value = record.id
  addType.value = num
  title.value = record.storeName
  itemRow.value = record
  console.log(record, id.value);

}

// 加载数据
const loadData = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}


// 刷新数据
const refreshData = () => {
  run({ pageNum: 1, pageSize: pageSize.value })
};

// 重置数据
const resetData = () => {
  formParams.value = {
    serveIds: []
  }
  refreshData()
}
function storeEdit(row) {
  visibleEdit.value = true
  id.value = row.id
  type.value = 1
}
function crowdUploadSuccess() {
  resetData()
}
</script>
