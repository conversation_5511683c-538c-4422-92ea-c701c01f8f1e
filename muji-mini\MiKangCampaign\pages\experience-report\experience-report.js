// MiKangCampaign/pages/experience-report/experience-report.js
import {
  getCampaignType,
  getLotteryUser,
  getReportDay7,
  getReportDay7Two,
  postqrcode_base64,
  lotteryUserShare,
} from "../../api/index.js";
const app = getApp();
Page({
  /**
   * 页面的初始数据
   */
  data: {
    isShare: false,
    src: "/MiKangCampaign/mk-report-bg.png",
    show: false,
    reportUrl: undefined,
    raderUrl: undefined,
    daysReportInfo: {
      satisfaction: 5,
      feeling: "",
    },
    daysInfo: {
      improve: [
        "暗沉改善",
        "紧致度提升",
        "光滑润泽",
        "滋润保湿",
        "去红血丝",
      ],
    },
    code_base64: "",
    ratingLight: 5,
    ratingHalf: 0,
    ratingDark: 0,
    campaignCode: wx.getStorageSync("campaignCode"),
    id: "",
    isShare: 0, // 0-未分享 1-分享过1次 2-分享过2次
    totalCount: 0,
    surplusCount: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log(options, options?.id ? options.id : 0, "options");
    this.setData({
      id: options?.id ? options.id : 0,
    });
    this.getCampaignData();
    this.getData();
    this.get5DaysReportInfo();
    this.get5Days();
    this.getCanvasImg();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    // 页面分享
    return {
      title: "和我一起体验「米糠护肤」开启五天打卡活动",
      imageUrl: wx.$config.ossImg + "/MiKangCampaign/mk-share-card-1.png",
      path: "/MiKangCampaign/pages/index/index",
    };
  },

  // 获取活动信息
  getCampaignData() {
    getCampaignType().then((res) => {
      wx.setStorageSync("campaignCode", res.data.campaignCode);
      this.setData({
        CampaignData: res.data,
        campaignCode: res.data.campaignCode,
      });
    });
  },

  // 获取第5天打卡报告信息
  get5DaysReportInfo() {
    getReportDay7({
      campaignCode: this.data.campaignCode,
    }).then(({
      data
    }) => {

    });
  },

  // 获取用户抽奖信息
  getData(num) {
    getLotteryUser({
      campaignCode: this.data.campaignCode,
    }).then(({
      data
    }) => {
      console.log("页面判断是否有剩余抽奖次数", data);
      this.setData({
        surplusCount: data.surplusCount,
        totalCount: data.totalCount,
        isShare: data.isShare,
      });
      if (num) {
        this.openShowPopup();
      }
    });
  },

  // 获取第5天打卡报告信息
  get5Days() {
    getReportDay7Two({
      campaignCode: this.data.campaignCode,
    }).then(async ({
      data
    }) => {
      console.log(data, "第五天打卡报告基础信息");
      const satisfaction = this.calculateAverage(data);
      console.log(satisfaction, 'satisfaction')
      const ratingLight = parseInt(satisfaction) ?? 0;
      const decimal = satisfaction % 1 || 0;
      const ratingHelf = decimal < 0.5 ? 0 : 1;
      this.setData({
        ratingLight: ratingLight,
        ratingHalf: ratingHelf,
        ratingDark: 5 - ratingLight - ratingHelf,
        daysReportInfo: {
          ...data,
          satisfaction
        }
      });
      this.setData({
        daysInfo: data,
      });
      await this.creatRaderImg();
    });
  },

  // 获取太阳码
  getCanvasImg() {
    // trial 1：体验版 3：正式版本 上线后改为正式版本
    let linColor = `{"r":220,"g":220,"b":220}`;
    postqrcode_base64({
      isHyaline: 1,
      page: "MiKangCampaign/pages/index/index",
      scene: "",
      trial: 1,
      width: 112,
      linColor: linColor,
    }).then(({
      data
    }) => {
      // console.log(data, 'data 二维码打印');
      this.setData({
        code_base64: data,
      });
    });
  },

  createCanvasImg() {
    let _this = this;
    return new Promise((resolve, reject) => {
      this.createSelectorQuery()
        .select("#myCanvas")
        .fields({
          node: true,
          size: true,
        })
        .exec(async (res) => {
          console.log(res, "55555555555");
          let {
            node: canvas,
            width,
            height
          } = res[0];
          let {
            userInfo,
            $cdn,
            src,
            daysInfo,
            code_base64,
            raderUrl,
          } = this.data;
          console.log(userInfo, "userInfo");
          console.log(res[0], "canvas");

          let {
            rpx
          } = app.globalData;
          let dpr = 4096 / height - 0.01;
          const ctx = canvas.getContext("2d");
          ctx.imageSmoothingEnabled = false;
          canvas.width = width * dpr;
          canvas.height = height * dpr;
          ctx.scale(dpr, dpr);
          ctx.clearRect(0, 0, width, height);
          // 以上代码固定
          // 以下内容根据UI实际书写

          ctx.fillStyle = "#ffffff";
          ctx.fillRect(0, 0, width, height);
          console.log(width, height);
          ctx.fillStyle = "#000000";
          // 绘制背景
          let img1 = await this.loadImage(canvas, $cdn + src);
          ctx.drawImage(img1, 0, 0, 560 * rpx, 1128 * rpx);

          // 绘制header部分
          let headerImg = await this.loadImage(
            canvas,
            wx.$config.ossImg +
            "/MiKangCampaign/mk-report-head-text.png"
          );
          let headerImgWidth = 448;
          let headerImgHidth = 95;
          console.log(
            headerImgWidth,
            ":headerImgWidth",
            headerImgHidth,
            ":headerImgHidth"
          );
          ctx.drawImage(
            headerImg,
            0 * rpx,
            0 * rpx,
            headerImgWidth,
            headerImgHidth,
            48 * rpx,
            42 * rpx,
            448 * rpx,
            95 * rpx
          );

          // 绘制产品图
          const productImg = await this.loadImage(
            canvas,
            wx.$config.ossImg + "/MiKangCampaign/mk-product.png"
          );
          const productImgWidth = productImg.width;
          const productImgHidth = productImg.height;
          let productScale = 0.72;
          console.log(
            productImgWidth,
            ":productImgWidth",
            productImgHidth,
            ":productImgHidth"
          );
          ctx.drawImage(
            productImg,
            0 * rpx,
            0 * rpx,
            productImgWidth,
            productImgHidth,
            50 * rpx,
            76.91 * 2 * rpx,
            productScale * productImgWidth * rpx,
            productScale * productImgHidth * rpx
          );

          // 绘制用户图片背景样式
          const beforeBg = await this.loadImage(
            canvas,
            wx.$config.ossImg +
            "/MiKangCampaign/mk-user-image-bg.png"
          );
          const beforeBgWidth = beforeBg.width;
          const beforeBgHeight = beforeBg.height;
          let beforeScale = 0.95;
          ctx.drawImage(
            beforeBg,
            0 * rpx,
            0 * rpx,
            beforeBgWidth,
            beforeBgHeight,
            355 * rpx,
            76.91 * 2 * rpx,
            beforeScale * beforeBgWidth * rpx,
            beforeScale * beforeBgHeight * rpx
          );

          // 绘制第before用户照片
          const beforeImg = await this.loadImage(
            canvas,
            daysInfo.materialUrlDay1 ||
            wx.$config.ossImg + "/MiKangCampaign/mk-product.png"
          );
          const beforeImgWidth = beforeImg.width;
          const beforeImgHeight = beforeImg.height;
          // let beforeImgScaleW = 86.88 / beforeImgWidth;
          let beforeImgScale = 0.7;
          // const dx1 = 366 + beforeImgWidth * beforeImgScaleW / 4.5;
          ctx.drawImage(
            beforeImg,
            0 * rpx,
            0 * rpx,
            beforeImgWidth,
            beforeImgHeight,
            366 * rpx,
            (81.91 + 4) * 2 * rpx,
            86.88 * 2 * beforeImgScale * rpx,
            86.88 * 2 * beforeImgScale * rpx
          );

          // 绘制before tag
          const beforeTagImg = await this.loadImage(
            canvas,
            wx.$config.ossImg + "/MiKangCampaign/mk-before-icon.png"
          );
          const beforeTagImgWidth = beforeTagImg.width;
          const beforeTagImgHeight = beforeTagImg.height;
          let beforeTagImgScale = 1;
          ctx.drawImage(
            beforeTagImg,
            0 * rpx,
            0 * rpx,
            beforeTagImgWidth,
            beforeTagImgHeight,
            (363 + 3) * rpx,
            (76.91 + 62) * 2 * rpx,
            60 * beforeTagImgScale * rpx,
            32.5 * beforeTagImgScale * rpx
          );

          // 绘制用户图片背景样式2
          const afterBg = await this.loadImage(
            canvas,
            wx.$config.ossImg +
            "/MiKangCampaign/mk-user-image-bg.png"
          );
          const afterBgWidth = afterBg.width;
          const afterBgHeight = afterBg.height;
          let afterBgScale = 0.95;
          ctx.drawImage(
            afterBg,
            0 * rpx,
            0 * rpx,
            afterBgWidth,
            afterBgHeight,
            355 * rpx,
            170.97 * 2 * rpx,
            afterBgScale * afterBgWidth * rpx,
            afterBgScale * afterBgHeight * rpx
          );

          // 绘制第after用户照片
          const afterImg = await this.loadImage(
            canvas,
            daysInfo.materialUrlDay7 ||
            wx.$config.ossImg + "/MiKangCampaign/mk-product.png"
          );
          const afterImgWidth = afterImg.width;
          const afterImgHeight = afterImg.height;
          let afterImgScale = 0.7;
          // const dx2 = 366 + afterImgWidth * afterImgScaleW / 4.5;
          ctx.drawImage(
            afterImg,
            0 * rpx,
            0 * rpx,
            afterImgWidth,
            afterImgHeight,
            366 * rpx,
            (178.97 + 2) * 2 * rpx,
            // afterImgWidth * afterImgScaleW * rpx,
            // afterImgHeight * afterImgScaleW * rpx
            86.88 * 2 * afterImgScale * rpx,
            86.88 * 2 * afterImgScale * rpx,
          );

          // 绘制after tag
          const afterTagImg = await this.loadImage(
            canvas,
            wx.$config.ossImg + "/MiKangCampaign/mk-after-icon.png"
          );
          const afterTagImgWidth = afterTagImg.width;
          const afterTagImgHeight = afterTagImg.height;
          let afterTagImgScale = 1;
          ctx.drawImage(
            afterTagImg,
            0 * rpx,
            0 * rpx,
            afterTagImgWidth,
            afterTagImgHeight,
            (363 + 3) * rpx,
            (173.97 + 60) * 2 * rpx,
            60 * afterTagImgScale * rpx,
            32.5 * afterTagImgScale * rpx
          );

          // 绘制文字
          let rating = [this.data.daysReportInfo.satisfaction, "分"];
          let ratingWidth = 0;
          let w1 = 25;
          rating.forEach((item, index) => {
            // ctx.textBaseline = 'top'
            ctx.textAlign = "left";
            ctx.fillStyle = "#FFFFFF";
            let metrics = 0;
            if (index === 0) {
              metrics = ctx.measureText(item);
              console.log(metrics.width, "metrics");
              ratingWidth += metrics.width * 2 + metrics.width / 3 + 1;
              ctx.font =
                "800 " + 50 * rpx + "px Arial, sans-serif";
            } else {
              ctx.font =
                "400 " + 16 * rpx + "px Arial, sans-serif";
            }
            ctx.fillText(item, w1 * 2 * rpx, 289.55 * 2 * rpx);
            w1 += ratingWidth;
          });

          console.log(w1, ratingWidth, '宽度')

          // 绘制评分米糠
          let ratingScale = 1;
          let ratingX = w1 + 50;
          const ratingXGap = 37;
          const {
            ratingLight,
            ratingHalf,
            ratingDark
          } = this.data;
          if (ratingLight) {
            const ratingLightImg = await this.loadImage(
              canvas,
              wx.$config.ossImg +
              "/MiKangCampaign/mk-rating-ligth.png"
            );
            const ratingLightWidth = ratingLightImg.width;
            const ratingLightHeight = ratingLightImg.height;
            for (let i = 0; i < ratingLight; i++) {
              ctx.drawImage(
                ratingLightImg,
                0 * rpx,
                0 * rpx,
                ratingLightWidth,
                ratingLightHeight,
                ratingX * rpx,
                269.55 * 2 * rpx,
                13 * 2 * ratingScale * rpx,
                21 * 2 * ratingScale * rpx
              );
              ratingX += ratingXGap;
            }
          }
          if (ratingHalf) {
            const ratingHalfImg = await this.loadImage(
              canvas,
              wx.$config.ossImg +
              "/MiKangCampaign/mk-rating-half.png"
            );
            const ratingHalfWidth = ratingHalfImg.width;
            const ratingHalfHeight = ratingHalfImg.height;
            for (let h = 0; h < ratingHalf; h++) {
              ctx.drawImage(
                ratingHalfImg,
                0 * rpx,
                0 * rpx,
                ratingHalfWidth,
                ratingHalfHeight,
                ratingX * rpx,
                269.55 * 2 * rpx,
                13 * 2 * ratingScale * rpx,
                21 * 2 * ratingScale * rpx
              );
              ratingX += ratingXGap;
            }
          }
          if (ratingDark) {
            const ratingDarkImg = await this.loadImage(
              canvas,
              wx.$config.ossImg +
              "/MiKangCampaign/mk-rating-dark.png"
            );
            const ratingDarkWidth = ratingDarkImg.width;
            const ratingDarkHeight = ratingDarkImg.height;
            for (let d = 0; d < ratingDark; d++) {
              ctx.drawImage(
                ratingDarkImg,
                0 * rpx,
                0 * rpx,
                ratingDarkWidth,
                ratingDarkHeight,
                ratingX * rpx,
                269.55 * 2 * rpx,
                13 * 2 * ratingScale * rpx,
                21 * 2 * ratingScale * rpx
              );
              ratingX += ratingXGap;
            }
          }

          // 绘制用户评价图片
          const userCommonImg = await this.loadImage(
            canvas,
            wx.$config.ossImg + "/MiKangCampaign/mk-user-common.png"
          );
          const userCommonImgWidth = userCommonImg.width;
          const userCommonImgHeight = userCommonImg.height;
          let userCommonImgScale = 1;
          ctx.drawImage(
            userCommonImg,
            0 * rpx,
            0 * rpx,
            userCommonImgWidth,
            userCommonImgHeight,
            180 * 2 * rpx,
            260 * 2 * rpx,
            160 * userCommonImgScale * rpx,
            100 * userCommonImgScale * rpx
          );

          // 绘制雷达图
          const raderImg = await this.loadImage(canvas, raderUrl);
          const raderImgWidth = raderImg.width;
          const raderImgHeight = raderImg.height;
          let raderImgScale = 1;
          ctx.drawImage(
            raderImg,
            0 * rpx,
            0 * rpx,
            raderImgWidth,
            raderImgHeight,
            26 * rpx,
            332.85 * 2 * rpx,
            244 * raderImgScale * rpx,
            212 * raderImgScale * rpx
          );

          // 绘制左引号
          const leftColonImg = await this.loadImage(
            canvas,
            wx.$config.ossImg + "/MiKangCampaign/mk-left-colon.png"
          );
          const leftColonImgWidth = leftColonImg.width;
          const leftColonImgHeight = leftColonImg.height;
          let leftColonImgScale = 1;
          ctx.drawImage(
            leftColonImg,
            0 * rpx,
            0 * rpx,
            leftColonImgWidth,
            leftColonImgHeight,
            270 * rpx,
            312.85 * 2 * rpx,
            18 * leftColonImgScale * rpx,
            16 * leftColonImgScale * rpx
          );

          // 绘制中间长文本
          // 配置绘制参数（单位为rpx）
          const config = {
            x: 145 * 2 * rpx, // 起始 x 坐标 (rpx)
            y: 320.26 * 2 * rpx, // 起始 y 坐标 (rpx)
            maxWidth: 224 * rpx, // 最大宽度 (rpx)
            lineHeight: 11 * 2 * rpx, // 行高 (rpx)
          };

          let feelingHeigth = 0;
          ctx.font = `${14 * rpx}px Arial, sans-serif`; // 设置字体（根据rpx调整字体大小）
          ctx.fillStyle = "white"; // 设置颜色

          // 调用封装的绘制函数
          const lines = this.drawLongText(
            ctx,
            this.data.daysReportInfo.feeling,
            config,
            feelingHeigth
          );
          console.log(lines, "feelingHeigth");
          // 绘制右引号
          const rightColonImg = await this.loadImage(
            canvas,
            wx.$config.ossImg + "/MiKangCampaign/mk-right-colon.png"
          );
          const rightColonImgWidth = rightColonImg.width;
          const rightColonImgHeight = rightColonImg.height;
          let rightColonImgScale = 1;
          ctx.drawImage(
            rightColonImg,
            0 * rpx,
            0 * rpx,
            rightColonImgWidth,
            rightColonImgHeight,
            495 * rpx,
            (lines * 22 + 305.26 * 2 + 10) * rpx,
            18 * rightColonImgScale * rpx,
            16 * rightColonImgScale * rpx
          );

          // 设置虚线样式（点线）
          // ctx.setLineDash([2, 2]); // [实线长度, 间隙长度]
          // ctx.lineWidth = 1; // 线条宽度
          // ctx.strokeStyle = 'white'; // 线条颜色

          // 绘制水平线
          // ctx.beginPath();
          // ctx.moveTo(25, feelingHeigth + 50); // 起点 (x, y)
          // ctx.lineTo(25+232.73, feelingHeigth + 50); // 终点 (x, y)
          // ctx.stroke();
          // console.log(daysInfo.improve);
          // let improve = daysInfo.improve ? daysInfo.improve instanceof Array ? daysInfo.improve: daysInfo.improve.split(',') : []
          // // 绘制皮肤状态 改善
          // const skin = improve.length > 5 ? improve.slice(0, 5) : improve;
          // const tagBgImg = await this.loadImage(canvas,  wx.$config.ossImg + '/MiKangCampaign/mk-note-bg.png');

          // this.drawTags(tagBgImg, ctx, skin, {
          //   maxPerRow: 3,
          //   tagWidth: 70,
          //   fillColor: '#e0e0e0'
          // });

          // 绘制太阳图
          if (code_base64) {
            let qrCode = await this.loadImage(
              canvas,
              "data:image/png;base64," + code_base64
            );

            const qrCodeWidth = qrCode.width;
            const qrCodeHeight = qrCode.height;
            let qrCodeScale = 2;

            // 保存Canvas状态
            ctx.save();
            // 定义圆形底的位置和大小
            const qrBgX = 135.5 * 2 * rpx; // 圆心x坐标
            const qrBgY = 520 * 2 * rpx; // 圆心y坐标
            const diameter = 48 * 2 * rpx; // 圆形直径
            const radius = diameter / 2;
            // 绘制白色圆形底
            ctx.beginPath();
            ctx.arc(qrBgX, qrBgY, radius, 0, Math.PI * 2);
            ctx.fillStyle = "white";
            ctx.fill();

            // 设置圆形裁剪区域
            ctx.beginPath();
            ctx.arc(qrBgX, qrBgY, radius, 0, Math.PI * 2);
            ctx.clip();

            // 绘制图片，适应圆形区域
            ctx.drawImage(
              qrCode,
              qrBgX - radius,
              qrBgY - radius,
              diameter,
              diameter
            );

            // 恢复Canvas状态
            ctx.restore();
          }

          const b64Data = canvas.toDataURL('image/png').split(',')[1]
          const filePath = `${wx.env.USER_DATA_PATH}/temp_${Date.now()}.png`

          // writeFile 直接支持 base64 写入
          wx.getFileSystemManager().writeFile({
            filePath,
            data: b64Data,
            encoding: 'base64', // 新方式，替代 base64ToArrayBuffer
            success: () => {
              _this.setData({
                reportUrl: filePath,
              });
              resolve();
            },
            fail: (e) => {
              console.log(canvas, e, '绘制海报失败');
              _this.setData({
                loading1: false,
              });
              reject();
            }
          })

          // setTimeout(() => {
          //   wx.canvasToTempFilePath({
          //     x: 0,
          //     y: 0,
          //     width: width,
          //     height: height,
          //     destWidth: width * 4,
          //     destHeight: height * 4,
          //     canvas: canvas,
          //     success: (res) => {
          //       this.setData({
          //         reportUrl: res.tempFilePath,
          //       });
          //       resolve();
          //     },
          //     fail(e) {
          //       console.log(canvas, e, '绘制海报失败');
          //       _this.setData({
          //         loading1: false,
          //       });
          //       reject();
          //     },
          //   });
          // }, 100);
        });
    });
  },
  // canvas加载图片
  loadImage(canvas, src) {
    let that = this;
    return new Promise((resolve) => {
      let img1 = canvas.createImage(img1);
      img1.src = src;
      img1.onload = function () {
        resolve(img1);
        // console.log(src, "图片成功");
      };
      img1.onerror = function (e) {
        console.log(src);
        wx.showToast({
          title: "图片加载失败",
          icon: "none",
        });
        console.log("图片加载失败");
        that.setData({
          loading1: false,
        });
      };
    });
  },

  // 封装的绘制长文本函数（直接使用 px）
  drawLongText(ctx, text, config, textHeight) {
    const {
      x,
      y,
      maxWidth,
      lineHeight
    } = config;
    let currentLine = "";
    let lines = [];
    let currentY = y;

    // 自动换行逻辑
    for (let i = 0; i < text?.length; i++) {
      currentLine += text[i];
      const metrics = ctx.measureText(currentLine);

      // 如果当前行宽度超过最大宽度，换行
      if (metrics.width > maxWidth) {
        lines.push(currentLine.slice(0, -1)); // 保存当前行（去掉最后一个字符）
        currentLine = text[i]; // 新行从当前字符开始
      }
    }

    // 保存最后一行
    if (currentLine) {
      lines.push(currentLine);
    }

    // 绘制每一行
    lines.forEach((line, index) => {
      textHeight = currentY + index * lineHeight;
      ctx.fillText(line, x, currentY + index * lineHeight);
    });

    return lines?.length ?? 0;
  },
  saveImage: app.debounce(async function () {
    // this.openPopup()
    let {
      reportUrl
    } = this.data;
    // canvas生成图片
    this.setData({
      loading1: true,
    });
    if (!reportUrl) {
      await this.createCanvasImg();
    }
    // 生成图片成功
    this.setData({
      loading1: false,
      isShow: true,
    });
    this.shareImg();
  }),

  // 直接保存海报
  shareImg: app.debounce(async function () {
    // 保存海报
    wx.$mp.track({
      event: "prize_share_click",
    });
    // 分享图片
    let that = this;
    let {
      reportUrl
    } = that.data;
    if (reportUrl.startsWith("http")) {
      wx.downloadFile({
        url: reportUrl,
        success(res) {
          wx.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              // that.success()
              wx.showToast({
                title: "保存成功",
                icon: "none",
              });
            },
            fail(err) {
              console.log("err", err);
              if (
                err.errMsg ===
                "saveImageToPhotosAlbum:fail auth deny"
              ) {
                // 用户拒绝了相册权限
                wx.showModal({
                  title: "权限提示",
                  content: "请授权访问相册，才能保存图片",
                  success(res) {
                    if (res.confirm) {
                      wx.openSetting(); // 跳转到设置界面
                    }
                  },
                });
              } else {
                // 其他错误处理
                wx.showToast({
                  title: "保存失败",
                  icon: "none",
                });
              }
            },
          });
        },
      });
    } else {
      wx.saveImageToPhotosAlbum({
        filePath: reportUrl,
        success: () => {
          console.log();
          // that.success()
          wx.showToast({
            title: "保存成功",
            icon: "none",
          });
        },
        fail(err) {
          console.log("err", err);
          if (
            err.errMsg === "saveImageToPhotosAlbum:fail auth deny"
          ) {
            // 用户拒绝了相册权限
            wx.showModal({
              title: "权限提示",
              content: "请授权访问相册，才能保存图片",
              success(res) {
                if (res.confirm) {
                  wx.openSetting(); // 跳转到设置界面
                }
              },
            });
          } else {
            // 其他错误处理
            wx.showToast({
              title: "保存失败",
              icon: "none",
            });
          }
        },
      });
    }
  }),

  // 绘制动态tag
  drawTags(tagBgImg, ctx, tags, options = {}) {
    // 默认参数
    const defaults = {
      maxPerRow: 3,
      tagHeight: 22.5,
      gap: 12,
      startX: 25,
      startY: 450,
      rowHeight: 36.5,
      fillColor: "#f0f0f0",
      textColor: "#3C3C43",
      font: "9px Arial, sans-serif",
    };

    const settings = {
      ...defaults,
      ...options,
    };

    // 绘制标签
    for (let i = 0; i < tags.length; i++) {
      const row = Math.floor(i / settings.maxPerRow);
      const col = i % settings.maxPerRow;
      const x =
        settings.startX + col * (settings.tagWidth + settings.gap);
      const y = settings.startY + row * settings.rowHeight;

      // 绘制图片作为背景
      ctx.drawImage(
        tagBgImg,
        x,
        y,
        settings.tagWidth,
        settings.tagHeight
      );

      // 绘制文字
      ctx.fillStyle = settings.textColor;
      ctx.font = settings.font;
      ctx.fillText("# " + tags[i], x + 8, y + 15);
    }
  },

  // 获得抽奖机会弹框
  openShowPopup: app.debounce(async function () {
    console.log(this.data.isShare, ' this.data.isShare');
    this.setData({
      showPopup: this.data.isShare == 1
    })
  }),

  // 关闭获得一次抽奖机会弹框
  closeShowPopup: app.debounce(async function (e) {
    this.setData({
      showPopup: false,
    });
  }),
  // 跳转到抽奖列表
  click: app.debounce(async function () {
    wx.redirectTo({
      url: `/MiKangCampaign/pages/prizeDraw/prizeDraw`,
    });
  }),
  // 海报关闭
  close() {
    this.setData({
      show: false,
    });
    // console.log(this.data.isShare, 'this.data.isShare')
    // if (this.data.isShare !== 0) {
    //   return;
    // }
    // this.setData({
    //   isShare: true,
    //   showPopup: true
    // });
  },
  // 分享
  share: app.debounce(async function () {
    let {
      reportUrl
    } = this.data;
    // canvas生成图片
    this.setData({
      loading1: true,
    });
    if (!reportUrl) {
      await this.createCanvasImg();
    }
    // 生成图片成功
    this.setData({
      loading1: false,
      show: true,
    });
  }),

  // 海报操作成功后(下载图片成功、分享成功)的逻辑
  success() {
    // 分享抽奖 判断是否分享过 没有分享过走接口 已经分享过走普通分享
    console.log(this.data.campaignCode, "campaignCode");
    if (!this.data.isShare) {
      // 分享报告获得抽奖
      wx.$mp.track({
        event: "prize_share_lucky_click",
      });
      lotteryUserShare({
        campaignCode: this.data.campaignCode,
      }).then((res) => {
        // wx.showToast({
        //   title: "分享成功",
        //   icon: "none",
        // });
        this.close();
        this.get5Days();
        this.getData(1);
      });
    }
    console.log("分享、下载海报、收藏成功了");
  },

  creatRaderImg() {
    let _this = this;
    return new Promise((resolve, reject) => {
      this.createSelectorQuery()
        .select("#raderCanvas")
        .fields({
          node: true,
          size: true,
        })
        .exec(async (res) => {
          let {
            node: canvas,
            width,
            height
          } = res[0];
          let {
            userInfo,
            $cdn,
            src,
            daysInfo,
            code_base64
          } =
          this.data;
          console.log(userInfo, "raderInfo");
          console.log(res[0], "raderCanvas");

          let {
            rpx
          } = app.globalData;
          let dpr = 4096 / height - 0.01;
          const ctx = canvas.getContext("2d");
          ctx.imageSmoothingEnabled = false;
          canvas.width = width * dpr;
          canvas.height = height * dpr;
          ctx.scale(dpr, dpr);
          ctx.clearRect(0, 0, width, height);
          // 以上代码固定
          // 以下内容根据UI实际书写

          ctx.fillStyle = "rgba(0, 0, 0, 0)"; // 或 ctx.fillStyle = 'transparent';
          ctx.fillRect(0, 0, width, height);
          // 绘制背景
          // let img1 = await this.loadImage(canvas, $cdn + src)
          // ctx.drawImage(img1, 0, 0, 560 * rpx, 1128 * rpx)

          // 绘制雷达图 【保湿效果，吸收速度，持久效果，舒适度】
          // [2,3,4,5] [daysInfo.moisturizeAvg, daysInfo.absorptionAvg, daysInfo.persistenceAvg, daysInfo.comfortAvg]
          console.log(daysInfo, "daysInfo");
          this.drawChartOne(ctx, [
            daysInfo.moisturizeAvg ?? 5,
            daysInfo.absorptionAvg ?? 5,
            daysInfo.persistenceAvg ?? 5,
            daysInfo.comfortAvg ?? 5,
          ]);


          const b64Data = canvas.toDataURL('image/png').split(',')[1]
          const filePath = `${wx.env.USER_DATA_PATH}/temp_${Date.now()}.png`

          // writeFile 直接支持 base64 写入
          wx.getFileSystemManager().writeFile({
            filePath,
            data: b64Data,
            encoding: 'base64', // 新方式，替代 base64ToArrayBuffer
            success: () => {
              _this.setData({
                raderUrl: filePath,
              });
            },
            fail: (err) => {

            }
          })

          // setTimeout(() => {
          //   wx.canvasToTempFilePath({
          //     x: 0,
          //     y: 0,
          //     width: width,
          //     height: height,
          //     destWidth: width * 4,
          //     destHeight: height * 4,
          //     canvas: canvas,
          //     success: (res) => {
          //       this.setData({
          //         raderUrl: res.tempFilePath,
          //       });
          //       resolve();
          //     },
          //     fail(e) {
          //       console.log(canvas, e,'绘制雷达图失败');
          //       _this.setData({
          //         loading1: false,
          //       });
          //       reject();
          //     },
          //   });
          // }, 100);
        });
    });
  },

  // 绘制雷达图 values按照labels的顺序传值
  drawChartOne(ctx, values) {
    const {
      rpx
    } = app.globalData;
    const angle = (Math.PI * 2) / 4; // 每个标签的角度
    const labels = [{
        text: "保湿效果",
        x: 0,
        y: -135 * rpx,
      },
      {
        text: "吸收速度",
        x: 135 * rpx,
        y: -14 * rpx,
      },
      {
        text: "持久效果",
        x: 0,
        y: 95 * rpx,
      },
      {
        text: "舒适度",
        x: -122 * rpx,
        y: -14 * rpx,
      },
    ];

    ctx.save();
    ctx.translate(151 * rpx, 140 * rpx); // 移动热力图中心

    // 绘制边框
    ctx.strokeStyle = "#ffffff";
    ctx.lineWidth = 1 * rpx;
    ctx.beginPath();
    ctx.arc(0, 0, 44.5, 0, Math.PI * 2);
    ctx.stroke();
    // ctx.lineDashOffset = 0
    ctx.setLineDash([10 * rpx, 12 * rpx]);
    [18, 25, 33.25].forEach((r) => {
      ctx.beginPath();
      ctx.arc(0, 0, r, 0, Math.PI * 2);
      ctx.stroke();
    });

    // 绘制十字线
    ctx.setLineDash([]); // 移除虚线以绘制实线十字
    ctx.strokeStyle = "#ffffff"; // 保持与边框一致的颜色
    ctx.lineWidth = 1 * rpx; // 保持线条粗细一致
    ctx.beginPath();
    // 水平线（x轴）
    ctx.moveTo(-44.5, 0); // 从最外圆的左边缘开始
    ctx.lineTo(44.5, 0); // 到最外圆的右边缘
    // 垂直线（y轴）
    ctx.moveTo(0, -44.5); // 从最外圆的上边缘开始
    ctx.lineTo(0, 44.5); // 到最外圆的下边缘
    ctx.stroke();

    // 绘制文字
    ctx.fillStyle = "#ffffff";
    ctx.textBaseline = "top";
    ctx.textAlign = "center";
    labels.forEach((item, index) => {
      const {
        text,
        x,
        y
      } = item;
      ctx.font = `400 ${20 * rpx}px/1  sans-serif`;
      ctx.fillText(text, x, y);
      ctx.font = `bolder ${24 * rpx}px/1  sans-serif`;
      ctx.fillText(values[index].toFixed(1), x, y + 22 * rpx);
    });

    // 绘制区域
    ctx.beginPath();
    const maxValue = Math.max(...values); // 获取最大值，用于定义网格的半径
    values.forEach((value, index) => {
      const radius = 81 * rpx * (value / 5); // 根据值计算半径
      const x = radius * Math.cos(index * angle - Math.PI / 2);
      const y = radius * Math.sin(index * angle - Math.PI / 2);

      if (index === 0) {
        ctx.moveTo(x, y); // 第一个点
      } else {
        ctx.lineTo(x, y);
      }
    });
    ctx.closePath();
    ctx.fillStyle = "rgba(255,255,255,0.5)"; // 区域颜色
    ctx.fill();

    ctx.restore();
  },

  calculateAverage({
    moisturizeAvg,
    absorptionAvg,
    persistenceAvg,
    comfortAvg
  }) {
    // 将 null 或 undefined 转换为 0
    const m = Number(moisturizeAvg) || 0;
    const a = Number(absorptionAvg) || 0;
    const p = Number(persistenceAvg) || 0;
    const c = Number(comfortAvg) || 0;

    // 计算平均值
    const avg = (m + a + p + c) / 4;
    return Number(avg.toFixed(1));
  },
  goPrize() {
    wx.redirectTo({
      url: `/MiKangCampaign/pages/prizeDraw/prizeDraw`,
    });
  }
});
