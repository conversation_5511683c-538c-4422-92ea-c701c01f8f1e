package com.dz.ms.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.product.dto.SuperscriptDTO;
import com.dz.ms.product.entity.Superscript;
import com.dz.ms.product.mapper.SuperscriptMapper;
import com.dz.ms.product.service.ShelfProductSuperscriptService;
import com.dz.ms.product.service.SuperscriptService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 商品角标管理
 *
 * @author: LiinNs
 * @date: 2024/11/20 11:35
 */
@Service
@Slf4j
public class SuperscriptServiceImpl extends ServiceImpl<SuperscriptMapper, Superscript> implements SuperscriptService {

    @Resource
    private SuperscriptMapper superscriptMapper;
    @Resource
    private ShelfProductSuperscriptService shelfProductSuperscriptService;

    /**
     * 分页查询商品角标管理
     *
     * @param param
     * @return PageInfo<SuperscriptDTO>
     */
    @Override
    public PageInfo<SuperscriptDTO> getSuperscriptList(SuperscriptDTO param) {
        Superscript superscript = BeanCopierUtils.convertObjectTrim(param, Superscript.class);
        QueryWrapper<Superscript> queryWrapper = new QueryWrapper<>(superscript);
        queryWrapper.orderByDesc("id");
        IPage<Superscript> page = superscriptMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), queryWrapper);
        return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), BeanCopierUtils.convertList(page.getRecords(), SuperscriptDTO.class));
    }

    /**
     * 查询商品角标
     *
     * @param param
     * @return List < SuperscriptDTO>
     */
    @Override
    public List<SuperscriptDTO> getSuperscriptNoPageList(SuperscriptDTO param) {
        LambdaQueryWrapper<Superscript> superscriptQueryWrapper = new LambdaQueryWrapper<>();
        superscriptQueryWrapper.like(StringUtils.isNotBlank(param.getName()), Superscript::getName, param.getName());
        superscriptQueryWrapper.orderByDesc(Superscript::getId);
        List<Superscript> superscripts = superscriptMapper.selectList(superscriptQueryWrapper);
        return BeanCopierUtils.convertList(superscripts, SuperscriptDTO.class);
    }

    /**
     * 根据ID查询商品角标管理
     *
     * @param id
     * @return SuperscriptDTO
     */
    @Override
    public SuperscriptDTO getSuperscriptById(Long id) {
        Superscript superscript = superscriptMapper.selectById(id);
        return BeanCopierUtils.convertObject(superscript, SuperscriptDTO.class);
    }

    /**
     * 保存商品角标管理
     *
     * @param param
     * @return Long
     */
    @Override
    public Long saveSuperscript(SuperscriptDTO param) {
        Superscript superscript = new Superscript(param.getId(), param.getName());
        if (ParamUtils.isNullOr0Long(superscript.getId())) {
            superscriptMapper.insert(superscript);
        } else {
            superscriptMapper.updateById(superscript);
        }
        return superscript.getId();
    }

    /**
     * 根据ID删除商品角标管理，接口暂时不删除关联数据，需留意
     *
     * @param param
     */
    @Override
    @Transactional
    public void deleteSuperscriptById(IdCodeDTO param) {
        superscriptMapper.deleteById(param.getId());
        shelfProductSuperscriptService.delProductSuperscriptBySuperscriptId(param.getId());
    }

}