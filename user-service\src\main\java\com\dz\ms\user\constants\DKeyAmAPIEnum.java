package com.dz.ms.user.constants;

import org.springframework.http.HttpMethod;

/**
 * 宁盾接口配置信息
 */
public enum DKeyAmAPIEnum {

    STRONG_AUTHENTICATE("/am/webauth/1/strong/authenticate", "认证", HttpMethod.POST),
    STRONG_PASSWORD_REQUIREMENT("/am/webauth/1/strong/passwordRequirement", "用户认证策略", HttpMethod.POST),
    STRONG_GET_DYNAMIC_PASSWORD("/am/webauth/1/strong/getDynamicPassword", "发送动态密码", HttpMethod.POST),
    USER_INC_SYNC("/am/rest/p/1/identity/user/incSync", "增量同步用户", HttpMethod.GET),
    USER_DELETE("/am/rest/p/1/identity/user/delete", "删除用户", HttpMethod.POST),
    USER_INFO("/am/rest/p/1/identity/user/info", "拉取单个用户的信息", HttpMethod.POST),
    USER_INFOS("/am/rest/p/1/identity/user/infos", "拉取所有用户的信息", HttpMethod.POST),
    USER_UNBIND_USER_TERMINALS("/am/rest/p/1/identity/user/unbindUserTerminals", "解绑用户终端", HttpMethod.POST),
    BINDING_LIST("/am/rest/p/1/token/binding/list", "用户和令牌的绑定关系", HttpMethod.POST),
    MOBILE_TOKEN_DELIVER("/am/rest/p/1/token/mobileToken/deliver", "派发手机令牌", HttpMethod.POST),
    MOBILE_TOKEN_USER_QR_CODE("/am/rest/p/1/token/mobileToken/userQrCode", "获取手机令牌二维码", HttpMethod.POST),
    MOBILE_TOKEN_BIND_AND_WRITE_MOBILE_TOKEN_QR_CODE("/am/rest/p/1/token/mobileToken/bindAndWriteMobileTokenQrCode", "绑定并获取手机令牌二维码", HttpMethod.POST),
    MOBILE_TOKEN_COMPLETE_MOBILE_TOKEN_BINDING("/am/rest/p/1/token/mobileToken/completeMobileTokenBinding", "完成手机令牌绑定", HttpMethod.POST),
    TOKEN_BIND("/am/rest/p/1/token/bind", "绑定时间型令牌", HttpMethod.POST),
    TOKEN_UNBIND("/am/rest/p/1/token/unbind", "解绑时间型令牌", HttpMethod.POST),
    TOKEN_UNBIND_ALL("/am/rest/p/1/token/unbindAll", "解绑所有时间型令牌", HttpMethod.POST),
    MOBILE_TOKEN_DATA("/am/rest/p/1/token/mobileToken/data", "获取手机令牌信息", HttpMethod.POST),
    DISCONNECT("/am/webauth/1/disconnect", "断开在线用户", HttpMethod.POST),
    PORTAL_QR_CODE_GRANT("/am/rest/p/1/portal/qrCode/grant", "扫码授权", HttpMethod.POST),
    PORTAL_WEI_XIN_INC_SYNC_USER("/am/rest/p/1/portal/weixin/incSyncUser", "增量更新用户信息（微信）", HttpMethod.POST),
    TENANT_LIST("/am/rest/p/1/tenant/list", "站点列表", HttpMethod.POST),
    BLACK_WHITE_MAC_ADDRESS_LIST_ADD("/am/rest/p/1/identity/blackWhiteMacAddressList/add", "新增Mac地址黑白名单", HttpMethod.POST),
    BLACK_WHITE_MAC_ADDRESS_LIST_LIST_REPLACE("/am/rest/p/1/identity/blackWhiteMacAddressList/list/replace", "Mac地址黑白名单", HttpMethod.POST),
    ;

    // 接口地址
    private final String uri;
    // 接口描述
    private final String desc;
    // 接口方法
    private final HttpMethod method;

    DKeyAmAPIEnum(String uri, String desc, HttpMethod method) {
        this.uri = uri;
        this.desc = desc;
        this.method = method;
    }

    public String getUri() {
        return uri;
    }

    public String getDesc() {
        return desc;
    }

    public HttpMethod getMethod() {
        return method;
    }
}
