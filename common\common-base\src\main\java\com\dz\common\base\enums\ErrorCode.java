package com.dz.common.base.enums;

public enum ErrorCode {
    REDIRECT(302, "重定向"),
    BAD_REQUEST(400, "请求参数个数或格式不符合要求"),
    INVALID_ARGUMENT(400, "非法参数"),
    UNAUTHORIZED(401, "无权访问"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "请求的地址不正确"),
    METHOD_NOT_ALLOWED(405, "不支持的HTTP请求方法"),
    NOT_ACCEPTABLE(406, "不接受的请求"),
    CONFLICT(409, "资源冲突"),
    UNSUPPORTED_MEDIA_TYPE(415, "不支持的Media Type"),
    POINYTS_ACTIVITY_LIMIT(420, "积分特殊活动商品限制"),
    POINYTS_ACTIVITY_REGULAR_LIMIT(430, "积分活动固定商品限制"),
    INTERNAL_ERROR(500, "业务繁忙，请稍后重试"),
    REQUEST_SERVICE_ERROR(500, "请求服务失败"),
    SERVICE_UNAVAILABLE(500, "服务不可用"),
    GATEWAY_TIMEOUT(500, "请求服务超时"),

    ADD_CART_INSUFFICIENT_POINT(1001, "您的积分不足以兑换购物车的全部商品 "),
    // ADD_CART_INVENTORY_SHORTAGE(1002, "商品已售罄"), toast
    EXCHANGE_NOT_ENOUGH_POINT(1003, "您的积分不足以兑换选择的商品"),
    EXCHANGE_NOT_REGISTER(1004, "还未注册，请先注册会员！"),
    EXCHANGE_INSUFFICIENT_POINT(1005, "积分不足，还差%d积分"),
    USER_REGISTER_ERROR(1020, "绑定手机号unionId失败"),
    USER_LOGOFF_ERROR(1021, "注销会员失败");

    private int code;
    private String message;

    private ErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }
}
