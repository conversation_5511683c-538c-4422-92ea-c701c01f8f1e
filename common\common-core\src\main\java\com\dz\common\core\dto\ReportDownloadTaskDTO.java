package com.dz.common.core.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 下载任务列表
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/2/14 17:18
 */
@ApiModel("下载中心下载任务")
@Getter
@Setter
public class ReportDownloadTaskDTO {

    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 菜单名称 eg：订单
     */
    @ApiModelProperty(value = "菜单名称 eg：订单")
    private String menuName;
    /**
     * 所属业务名称 eg：订单列表
     */
    @ApiModelProperty(value = "所属业务名称 eg：订单列表")
    private String moduleName;
    /**
     * 报表名称
     */
    @ApiModelProperty(value = "报表名称")
    private String fileName;
    /**
     * 文件路径
     */
    @ApiModelProperty(value = "文件路径")
    private String sourceUrl;
    @ApiModelProperty(value = "下载次数")
    private Long downloadNum;
    /**
     * 状态 0 生成中 1已完成 2已失败
     */
    @ApiModelProperty(value = "状态 0 生成中 1已完成 2已失败 3已失效")
    private Integer state;
    /**
     * 失败原因
     */
    @ApiModelProperty(value = "失败原因")
    private String errorDesc;
    /**
     * 是否删除 0未删除 1已删除
     */
    @ApiModelProperty(value = "是否删除 0未删除 1已删除")
    private Integer isDeleted;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @JsonProperty("createAt")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonProperty("createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date created;

    /**
     * 业务方报表表头 ,隔开
     */
    @ApiModelProperty(value = "创建人名称")
    private String header;
}
