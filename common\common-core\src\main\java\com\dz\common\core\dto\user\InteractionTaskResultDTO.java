package com.dz.common.core.dto.user;

import com.dz.common.core.dto.sales.TaskRewardDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 任务详情
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "任务详情")
public class InteractionTaskResultDTO {

    @ApiModelProperty(value = "任务列表")
    private List<InteractionTaskDTO> data;
    @ApiModelProperty(value = "是否显示新，1显示，2不显示")
    private Integer showNew=2;
}
