package com.dz.common.core.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 订单创建商品DTO
 */
@Setter
@Getter
@ToString
public class OrderCreateProductDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品ID")
    private Long productId;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "货架商品ID")
    private Long shelfProductId;
    @ApiModelProperty(value = "商品数量")
    private Integer number;
    @ApiModelProperty(value = "商品是否存在 0不存在 1存在")
    private Integer exist;
}