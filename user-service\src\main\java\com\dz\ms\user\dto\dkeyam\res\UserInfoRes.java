package com.dz.ms.user.dto.dkeyam.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.io.Serializable;
import java.util.List;

/**
 * 用户信息
 * @author: fei
 * @date: 2025/01/22 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserInfoRes implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("用户ID")
    private String userId;

    @ApiModelProperty("姓名")
    private String personalName;

    @ApiModelProperty("登录名")
    private String loginName;

    @ApiModelProperty("手机号")
    private List<String> mobiles;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("用户组，组与组织间使用/分隔")
    private String groupPath;

    @ApiModelProperty("角色")
    private List<String> roles;

    @ApiModelProperty("是否启用")
    private Boolean enabled;

    @ApiModelProperty("有效期，起始时间")
    private Long startTime;

    @ApiModelProperty("有效期，结束时间")
    private Long endTime;

    @ApiModelProperty("工号")
    private String number;

    @ApiModelProperty("身份证号")
    private String idCardNumber;

    @ApiModelProperty("部门")
    private String department;

    @ApiModelProperty("公司")
    private String company;

    @ApiModelProperty("自定义属性")
    private List<CustomAttribute> customAttributes;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class CustomAttribute {
        @ApiModelProperty("属性名称")
        private String name;

        @ApiModelProperty("属性值")
        private String value;
    }
}
