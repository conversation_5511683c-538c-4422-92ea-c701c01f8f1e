package com.dz.ms.adaptor.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.adaptor.entity.ThirdPartyRecord;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 人群包Mapper
 * @author: yibo
 * @date:   2024/11/18 13:53
 */
@Repository
public interface ThirdPartyRecordMapper extends BaseMapper<ThirdPartyRecord> {

    int inserByTable(@Param("nowDay")String nowDay, @Param("thirdPartyRecord") ThirdPartyRecord thirdPartyRecord);

    /**
     * 获取分表列表
     * @return
     */
    List<String> queryThirdPartyRecordTableList(@Param("nowDay")String nowDay);

    /**
     * 创建分表
     * @return
     */
    int createThirdPartyRecordTable(@Param("nowDay")String nowDay);
}
