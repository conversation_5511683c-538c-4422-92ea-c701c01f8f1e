package com.dz.ms.basic.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.dto.MaterialGroupDTO;
import com.dz.ms.basic.entity.MaterialGroup;
import com.dz.ms.basic.service.MaterialGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags="素材分组")
@RestController
public class MaterialGroupController  {

    @Resource
    private MaterialGroupService materialGroupService;

    /**
     * 分页查询素材分组
     * @param param
     * @return result<PageInfo<MaterialGroupDTO>>
     */
    @ApiOperation("分页查询素材分组")
	@GetMapping(value = "/crm/material_group/list")
    public Result<PageInfo<MaterialGroupDTO>> getMaterialGroupList(@ModelAttribute MaterialGroupDTO param) {
        Result<PageInfo<MaterialGroupDTO>> result = new Result<>();
        MaterialGroup materialGroup = BeanCopierUtils.convertObjectTrim(param,MaterialGroup.class);
        IPage<MaterialGroup> page = materialGroupService.page(new Page<>(param.getPageNum(), param.getPageSize()), new LambdaQueryWrapper<>(materialGroup).orderByDesc(MaterialGroup::getModified));
        result.setData(new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),BeanCopierUtils.convertList(page.getRecords(), MaterialGroupDTO.class)));
        return result;
    }

    /**
     * 素材分组列表不分页
     * @param param
     * @return result<List<MaterialGroupDTO>>
     */
    @ApiOperation("素材分组列表不分页")
    @GetMapping(value = "/crm/material_group/list_nopage")
    public Result<List<MaterialGroupDTO>> getMaterialGroupListNopage(@ModelAttribute MaterialGroupDTO param) {
        Result<List<MaterialGroupDTO>> result = new Result<>();
        MaterialGroup materialGroup = BeanCopierUtils.convertObjectTrim(param,MaterialGroup.class);
        List<MaterialGroup> list = materialGroupService.list(new LambdaQueryWrapper<>(materialGroup).orderByDesc(MaterialGroup::getModified));
        result.setData(BeanCopierUtils.convertList(list,MaterialGroupDTO.class));
        return result;
    }

    /**
     * 根据ID查询素材分组
     * @param id
     * @return result<MaterialGroupDTO>
     */
    @ApiOperation("根据ID查询素材分组")
	@GetMapping(value = "/crm/material_group/info")
    public Result<MaterialGroupDTO> getMaterialGroupById(@RequestParam("id") Long id) {
        Result<MaterialGroupDTO> result = new Result<>();
        MaterialGroup materialGroup = materialGroupService.getById(id);
        result.setData(BeanCopierUtils.convertObject(materialGroup,MaterialGroupDTO.class));
        return result;
    }

    /**
     * 新增素材分组
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增素材分组",type = LogType.OPERATELOG)
    @ApiOperation("新增素材分组")
    @PostMapping(value = "/crm/material_group/add")
    public Result<Long> addMaterialGroup(@RequestBody MaterialGroupDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,true);
        MaterialGroup materialGroup = new MaterialGroup(param.getId(), param.getGroupName());
        materialGroupService.save(materialGroup);
        result.setData(materialGroup.getId());
        return result;
    }

    /**
     * 更新素材分组
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "更新素材分组",type = LogType.OPERATELOG)
    @ApiOperation("更新素材分组")
    @PostMapping(value = "/crm/material_group/update")
    public Result<Long> updateMaterialGroup(@RequestBody MaterialGroupDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,false);
        MaterialGroup materialGroup = new MaterialGroup(param.getId(), param.getGroupName());
        materialGroupService.updateById(materialGroup);
        result.setData(param.getId());
        return result;
    }

    /**
     * 验证保存参数
     * @param param
     */
    private void validationSaveParam(MaterialGroupDTO param,boolean isAdd) {
        if(isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if(!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
        if(null == param.getGroupName()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "分类名称不能为空");
        }
        LambdaQueryWrapper<MaterialGroup> wrapper = new LambdaQueryWrapper();
        wrapper.eq(MaterialGroup::getGroupName,param.getGroupName());
        boolean isNew = ParamUtils.isNullOr0Long(param.getId());
        if(!isNew) {
            wrapper.ne(MaterialGroup::getId,param.getId());
        }
        long count = materialGroupService.count(wrapper);
        if(count > 0) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "分类名称不能重复");
        }
    }
	
	/**
     * 根据ID删除素材分组
     * @param param
     * @return result<Boolean>
     */
    @SysLog(value = "删除素材分组",type = LogType.OPERATELOG)
    @ApiOperation("根据ID删除素材分组")
	@PostMapping(value = "/crm/material_group/delete")
    public Result<Boolean> deleteMaterialGroupById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        materialGroupService.deleteMaterialGroup(param.getId(), SecurityContext.getUser().getTenantId());
        result.setData(true);
        return result;
    }

}
