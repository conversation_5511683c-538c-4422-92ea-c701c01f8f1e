<view class="cart-card">
  <view class="cart-img">
    <image class="img" mode="aspectFill" src="{{skuInfo.imgUrl}}" />
  </view>
  <view class="cart-info">
    <view class="cart-title">
      {{skuInfo.productName}}
    </view>

    <view class="cart-price">
      {{skuInfo.realPoint}}积分<block wx:if="{{skuInfo.realAmount>0}}">+{{skuInfo.realAmount}}元</block>
    </view>

    <view class="sku-num">
      数量：{{skuInfo.number}}
    </view>
    <view wx:if="{{skuInfo.couponId && skuInfo.couponCode}}" class="use-btn" bind:tap="onTapUse">
      {{skuInfo.buttonText}}
    </view>
  </view>
</view>