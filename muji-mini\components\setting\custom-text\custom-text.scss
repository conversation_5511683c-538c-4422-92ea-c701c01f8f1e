/* components/setting/text/text.wxss */
.bgStyle {
  width: 100%;
  height: auto;
}

.linkStyle {
  width: 100%;
  height: 100%;
}

.text {
  position: relative;
  width: 100%;
  height: auto;

  &-content {
    position: absolute;
    overflow: hidden;
  }

  &-img {
    display: block;
  }

  &-link {
    position: absolute;
    left: 0;
    bottom: 0;
    top: 0;
    right: 0;
    // pointer-events: none;
  }
}

.textStyle {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}
