package com.dz.common.core.enums;

import java.util.Objects;

/**
 * 抽奖记录状态枚举
 * <AUTHOR>
 * @date 2023-09-02 10:51:22
 */
public enum LotteryRecordStateEnum {
    PENDING_TAKE(1, "待领取"),
    BOOKED(2, "已预约"),
    TAKES(3, "已领取"),
    EXPIRED(4, "已过期"),
    ;
    private final Integer code;
    private final String value;

    LotteryRecordStateEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (LotteryRecordStateEnum resultEnum : LotteryRecordStateEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum.value;
            }
        }
        return null;
    }
}
