package com.dz.ms.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 更新用户信息入参DTO
 * @author: fei
 * @date:   2025/01/22 11:10
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "更新用户信息入参")
public class UserUpdParamDTO {

    @ApiModelProperty(value = "unionid MD5加密串")
    private String clientId;
    
    
}
