package com.dz.common.core.dto.user.openapi.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * openapi返回值
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "openapi返回值")
public class OpenApiDataDTO {

    @ApiModelProperty(value = "token")
    private String token;
    @ApiModelProperty(value = "token有效时间, 单位秒, 一般为一天")
    private Integer expireTime;

}
