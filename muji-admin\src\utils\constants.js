// 货架状态
export const SHELF_STATUS_ARR = [
  { value: 1, label: '未开始' },
  { value: 2, label: '上架中' },
  { value: 3, label: '已结束' }
]
export const SHELF_STATUS_OBJ = SHELF_STATUS_ARR.reduce((c, v) => ({ ...c, [v.value]: v.label }), {})

// 货架启停状态
export const SHELF_SWITCH_STATUS_ARR = [
  { value: 1, label: '启用' },
  { value: 0, label: '停用' }
]
export const SHELF_SWITCH_STATUS_OBJ = SHELF_SWITCH_STATUS_ARR.reduce((c, v) => ({ ...c, [v.value]: v.label }), {})

// 货架推广状态
export const SHELF_EXTENSION_STATUS_ARR = [
  { value: '1', label: '待开始' },
  { value: '2', label: '进行中' },
  { value: '3', label: '已结束' }
]
export const SHELF_EXTENSION_STATUS_OBJ = SHELF_EXTENSION_STATUS_ARR.reduce((c, v) => ({ ...c, [v.value]: v.label }), {})

// 货架推广启停状态
export const SHELF_EXTENSION_SWITCH_STATUS_ARR = [
  { value: '1', label: '启用中' },
  { value: '2', label: '已停用' }
]
export const SHELF_EXTENSION_SWITCH_STATUS_OBJ = SHELF_EXTENSION_SWITCH_STATUS_ARR.reduce((c, v) => ({ ...c, [v.value]: v.label }), {})

// 人群限购活动状态
export const CROWD_PURCHASE_RESTRICTION_STATUS_ARR = [
  { value: '1', label: '待开始' },
  { value: '2', label: '进行中' },
  { value: '3', label: '已结束' }
]
// 人群限购活动类型
export const CROWD_PURCHASE_RESTRICTION_TYPE = [
  { value: '1', label: '人群限购' },
]

// AND OR
export const AND_OR_ARR = [
  { value: 2, label: '且' },
  { value: 1, label: '或' }
]
export const AND_OR_OBJ = AND_OR_ARR.reduce((c, v) => ({ ...c, [v.value]: v.label }), {})

// 商品类型
export const PRODUCT_TYPE_ARR = [
  { value: 1, label: '实物商品' },
  { value: 2, label: '电子券' }
]
export const PRODUCT_TYPE_OBJ = PRODUCT_TYPE_ARR.reduce((c, v) => ({ ...c, [v.value]: v.label }), {})

// 商品展示状态
export const PRODUCT_DISPLAY_STATUS_ARR = [
  // { value: 3, label: '未展示' }, // 3未展示且不可编辑 需要使用beShowDisabled字段实现
  { value: 1, label: '展示中' },
  { value: 0, label: '未展示' }
]
export const PRODUCT_DISPLAY_STATUS_OBJ = PRODUCT_DISPLAY_STATUS_ARR.reduce((c, v) => ({ ...c, [v.value]: v.label }), {})

// 商品展示方式
export const PRODUCT_DISPLAY_METHODS_ARR = [
  { value: 1, label: '积分加价购' },
  { value: 2, label: '商品兑换' }
]
export const PRODUCT_DISPLAY_METHODS_OBJ = PRODUCT_DISPLAY_METHODS_ARR.reduce((c, v) => ({ ...c, [v.value]: v.label }), {})

// 商品发货方式
export const PRODUCT_DELIVERY_METHODS_ARR = [
  { value: 1, label: '线下使用' },
  { value: 2, label: '线上使用' }
]
export const PRODUCT_DELIVERY_METHODS_OBJ = PRODUCT_DELIVERY_METHODS_ARR.reduce((c, v) => ({ ...c, [v.value]: v.label }), {})

// 商品期望上下架时间
export const PRODUCT_EXPECT_UP_DOWN_SHELF_TIME_ARR = [
  { value: 1, label: '实时' },
  { value: 2, label: '单次时间' },
  { value: 3, label: '周期' }
]
export const PRODUCT_EXPECT_UP_DOWN_SHELF_TIME_OBJ = PRODUCT_EXPECT_UP_DOWN_SHELF_TIME_ARR.reduce((c, v) => ({ ...c, [v.value]: v.label }), {})
