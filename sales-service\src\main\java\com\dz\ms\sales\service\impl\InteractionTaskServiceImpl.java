package com.dz.ms.sales.service.impl;

import com.alibaba.excel.event.Order;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.basic.GiftConfigOtherDTO;
import com.dz.common.core.dto.basic.SubscribeMsgSendDTO;
import com.dz.common.core.dto.sales.TaskRewardDto;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.dto.user.InteractionTaskDTO;
import com.dz.common.core.dto.user.InteractionTaskExportDTO;
import com.dz.common.core.dto.user.StoreDTO;
import com.dz.common.core.dto.user.UserInfoDTO;
import com.dz.common.core.dto.user.UserSimpleDTO;
import com.dz.common.core.enums.SubscribeMsgEnum;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.basic.MpMsgFeignClient;
import com.dz.common.core.fegin.order.ExchangeOrderFeignClient;
import com.dz.common.core.fegin.user.CrowdFeignClient;
import com.dz.common.core.fegin.user.MUJIOpenApiFeignClient;
import com.dz.common.core.fegin.user.UserInfoFeginClient;
import com.dz.common.core.service.ExportService;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.DateUtils;
import com.dz.common.core.utils.ExcelUtils;
import com.dz.common.core.vo.ReceiveCouponVo;
import com.dz.common.core.vo.SubscriptionMsgVo;
import com.dz.ms.sales.entity.*;
import com.dz.ms.sales.mapper.InteractionTaskMapper;
import com.dz.ms.sales.mapper.TaskRewardMapper;
import com.dz.ms.sales.mapper.UserTaskRecordMapper;
import com.dz.ms.sales.service.InteractionTaskService;
import com.dz.ms.sales.vo.InteractionTaskSelectVo;
import com.dz.ms.sales.vo.InteractionTaskVo;
import com.dz.ms.sales.vo.TaskRewardVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.sql.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 用户信息
 *
 * @author: Handy
 * @date: 2022/2/4 17:33
 */
@Service
@Slf4j
public class InteractionTaskServiceImpl extends ServiceImpl<InteractionTaskMapper, InteractionTask> implements InteractionTaskService {

    @Resource
    private InteractionTaskMapper interactionTaskMapper;
    @Resource
    private UserTaskRecordMapper userTaskRecordMapper;
    @Resource
    private UserInfoFeginClient userInfoFeginClient;
    @Resource
    private TaskRewardMapper taskRewardMapper;
    @Resource
    private MUJIOpenApiFeignClient mujiOpenApiFeignClient;
    @Resource
    private ExportService exportService;
    @Resource
    private CrowdFeignClient crowdFeignClient;

    @Resource
    private ExchangeOrderFeignClient exchangeOrderFeignClient;
    // 地球半径取平均值：6371.393千米
    private static double EARTH_RADIUS = 6371.393;
    private static String miniTask="MINI_INTERACT_TASK";
    private static final ExecutorService executorService = Executors.newFixedThreadPool(20);
    private static final Semaphore semaphore = new Semaphore(10);
    private SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static String readyDay="readyDay";
    @Resource
    private MpMsgFeignClient mpMsgFeignClient;

    @Value("${order.time.first}")
    private String orderTimeFirst;
    @Resource
    private RedisService redisService;

    @Override
    public PageInfo<InteractionTaskDTO> getInteractionTaskList(InteractionTaskSelectVo param) throws ParseException {

        List<InteractionTaskDTO> listDTO=new ArrayList<>();
        LambdaQueryWrapper<InteractionTask> wrapper = new LambdaQueryWrapper<>();
        if (!StringUtils.isEmpty(param.getTaskName())){
            wrapper.like(InteractionTask::getTaskName,param.getTaskName());
        }
        if (null != param.getShowTimeStart() && null != param.getShowTimeEnd()){
            wrapper.and(wq->wq.between(InteractionTask::getShowTimeStart,param.getShowTimeStart(),param.getShowTimeEnd())
                    .or().between(InteractionTask::getShowTimeEnd,param.getShowTimeStart(),param.getShowTimeEnd())
                    .or().eq(InteractionTask::getShowTimeType,2));
        }
        if (null != param.getTaskType()){
            wrapper.eq(InteractionTask::getTaskType,param.getTaskType());
        }
        Date nowTime=new Date();
        if (null != param.getTaskStatus()){
            if (param.getTaskStatus().equals(0)){
                wrapper.gt(InteractionTask::getRestrictTimeStart,nowTime);
                wrapper.eq(InteractionTask::getIsTimeRestrict,1);
            }
            if (param.getTaskStatus().equals(1)){
                wrapper.and(wq->wq.lt(InteractionTask::getRestrictTimeStart,nowTime).gt(InteractionTask::getRestrictTimeEnd,nowTime)
                        .or().eq(InteractionTask::getIsTimeRestrict,2));
            }
            if (param.getTaskStatus().equals(2)){
                wrapper.lt(InteractionTask::getRestrictTimeEnd,nowTime);
            }
        }
        wrapper.orderByDesc(InteractionTask::getCreateTime);
        IPage<InteractionTask> page  = interactionTaskMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()),wrapper);
        List<InteractionTask> list=page.getRecords();
        if (CollectionUtils.isEmpty(list)){
            return new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),listDTO);
        }
        listDTO = BeanCopierUtils.convertList(list,InteractionTaskDTO.class);
        Date now = new Date();
        //当前时间在限时开始时间之前taskStatus=0，当前时间在限时开始时间之后限时结束时间之前taskStatus=1，当前时间在限时结束时间之后taskStatus=2
        for (InteractionTaskDTO taskDTO : listDTO) {
            //查询任务埋点
            Map<Long,Integer> userTaskMap=participateTaskCount();
            if (null != userTaskMap){
                taskDTO.setJoinInNum(userTaskMap.get(taskDTO.getId()));
            }
            if (taskDTO.getIsTimeRestrict()==1){
                if (taskDTO.getRestrictTimeStart().after(now)){
                    taskDTO.setTaskStatus(0);
                }else if (taskDTO.getRestrictTimeStart().before(now) && taskDTO.getRestrictTimeEnd().after(now)){
                    taskDTO.setTaskStatus(1);
                }else if (taskDTO.getRestrictTimeEnd().before(now)){
                    taskDTO.setTaskStatus(2);
                }
            }else{
                taskDTO.setTaskStatus(1);
            }
            List<TaskReward> taskRewards = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId, taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
            List<TaskRewardDto> taskRewardDtos = BeanCopierUtils.convertList(taskRewards, TaskRewardDto.class);
            for (TaskRewardDto taskRewardDto : taskRewardDtos){
                if (taskRewardDto.getRewardType()==1){//优惠券
                    String[] couponIds=taskRewardDto.getCouponIds().split(",");
                    String couponNamesStr="";
                    for (int i = 0; i < couponIds.length; i++){
                        //差券详情查询接口
                        Result<JSONObject> couponDetail=mujiOpenApiFeignClient.memberCouponStockId(couponIds[i]);
                        String couponName=couponDetail.getData().getString("name");
                        if (i<couponIds.length-1){
                            couponNamesStr=couponNamesStr+couponName+",";
                        }else{
                            couponNamesStr=couponNamesStr+couponName;
                        }
                    }
                    taskRewardDto.setCouponNames(couponNamesStr);
                }
            }
            taskDTO.setTaskRewardList(taskRewardDtos);
            //1限时任务，否则不限时任务
            if (taskDTO.getIsTimeRestrict()==1){
                //1一次性任务，2周期性任务，3周期+阶梯
                if (taskDTO.getReadyCycle().equals(1)){
                    List<Long> userIds=new ArrayList<>();
                    //是兑礼任务，每下readyNum次订单算完成一次任务
//                    if (taskDTO.getTaskDesc().equals(2)){
//                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.onlyOneFinishPersonNumTwo(taskDTO.getId(),taskDTO.getRestrictTimeStart(),taskDTO.getRestrictTimeEnd(),taskDTO.getReadyNum());
//                        if (!CollectionUtils.isEmpty(userTaskRecords)){
//                            //取出userTaskRecords中的userId
//                            userIds.addAll(userTaskRecords.stream().map(UserTaskRecord::getUserId).collect(Collectors.toList()));
//                            //计算userTaskRecords中finishNum之和
//                            for (UserTaskRecord userTaskRecord : userTaskRecords) {
//                                taskDTO.setFinishNum(taskDTO.getFinishNum()+userTaskRecord.getFinishNum());//
//                            }
//                            //获取userIds去重后的数量
//                            taskDTO.setFinishPersonNum((int) userIds.stream().distinct().count());
//                        }
//                    }else{
                    //不是兑礼任务，完成一次任务即任务结束
                    List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.onlyOneFinishPersonNumOne(taskDTO.getId(),taskDTO.getRestrictTimeStart(),taskDTO.getRestrictTimeEnd(),taskDTO.getReadyNum());
                    if (!CollectionUtils.isEmpty(userTaskRecords)){
                        //取出userTaskRecords中的userId
                        userIds.addAll(userTaskRecords.stream().map(UserTaskRecord::getUserId).collect(Collectors.toList()));
                        taskDTO.setFinishNum(userTaskRecords.size());
                        //获取userIds去重后的数量
                        taskDTO.setFinishPersonNum((int) userIds.stream().distinct().count());
                    }
//                    }
                }else if (taskDTO.getReadyCycle().equals(2)){
                    if (taskDTO.getReayType().equals(readyDay)){
                        //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                        long day = (taskDTO.getRestrictTimeEnd().getTime() - taskDTO.getRestrictTimeStart().getTime()) / (1000 * 60 * 60 * 24);
                        //计算day除以readyDay取整，得到实际周期次数
                        int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                        Date startTime = taskDTO.getRestrictTimeStart();
                        List<Long> userIds=new ArrayList<>();
                        for (int i = 0; i < actualCycle; i++) {
                            //计算taskDTO.getRestrictTimeStart()加上readyDay天后的日期
                            Date endTime= null;
                            if (i==actualCycle-1){
                                endTime=taskDTO.getRestrictTimeEnd();
                            }else{
                                endTime= DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                            }
                            List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.onlyOneFinishPersonNumOne(taskDTO.getId(),startTime,endTime,taskDTO.getReadyNum());
                            //取出userTaskRecords中的userId
                            userIds.addAll(userTaskRecords.stream().map(UserTaskRecord::getUserId).collect(Collectors.toList()));
                            if (!CollectionUtils.isEmpty(userTaskRecords)){
                                taskDTO.setFinishNum(taskDTO.getFinishNum()+userTaskRecords.size());
                            }
                            startTime=endTime;
                        }
                        //获取userIds去重后的数量
                        taskDTO.setFinishPersonNum((int) userIds.stream().distinct().count());
                    }else{
                        String str=sdf.format(taskDTO.getCreateTime());
                        String firstYear = str.substring(0,4);
                        String firstMonth = str.substring(5,7);
                        String lastStr=sdf.format(taskDTO.getRestrictTimeEnd());
                        String lastYear = lastStr.substring(0,4);
                        String lastMonth = lastStr.substring(5,7);
                        String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                        Date firstDate = sdf.parse(firstDay);
                        Date startTime=null;
                        if (taskDTO.getRestrictTimeStart().after(firstDate)){
                            startTime=taskDTO.getRestrictTimeStart();
                        }else{
                            startTime=firstDate;
                        }
                        List<Long> userIds=new ArrayList<>();
                        //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                        int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                        //计算monthNum除以readymonth取整，得到实际周期次数
                        int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                        for (int i = 0; i < actualCycle; i++) {
                            Date endTime=null;
                            if (i==actualCycle-1){
                                endTime=taskDTO.getRestrictTimeEnd();
                            }else{
                                //得到startTime日期+taskDTO.getReadyMonth()的最后一天
                                endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                                String strEndTime=sdf.format(endTime);
                                String endTimeYear = strEndTime.substring(0,4);
                                String endTimeMonth = strEndTime.substring(5,7);
                                String endTimeDay = strEndTime.substring(8,10);
                                //当月最后一天
                                String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                                endTime=sdf.parse(endTimeStr);
                            }
                            List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.onlyOneFinishPersonNumOne(taskDTO.getId(),startTime,endTime,taskDTO.getReadyNum());
                            //取出userTaskRecords中的userId
                            userIds.addAll(userTaskRecords.stream().map(UserTaskRecord::getUserId).collect(Collectors.toList()));
                            if (!CollectionUtils.isEmpty(userTaskRecords)){
                                taskDTO.setFinishNum(taskDTO.getFinishNum()+userTaskRecords.size());
                            }
                            //在endTime加一个月
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(endTime);
                            calendar.add(Calendar.MONTH, 1);
                            String startTimeStr =sdf.format(calendar.getTime());
                            String startTimeYear = startTimeStr.substring(0,4);
                            String startTimeMonth = startTimeStr.substring(5,7);
                            //当月最后一天
                            startTimeStr=startTimeYear+"-"+startTimeMonth+"-01 00:00:00";
                            startTime=sdf.parse(startTimeStr);
                        }
                        taskDTO.setFinishPersonNum((int) userIds.stream().distinct().count());
                    }
                }else{
                    log.info("===================================任务类型："+taskDTO.getTaskDesc());
                    if (taskDTO.getTaskDesc().equals(3)){//线下消费
                        if (taskDTO.getReayType().equals(readyDay)){
                            //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                            long day = (taskDTO.getRestrictTimeEnd().getTime() - taskDTO.getRestrictTimeStart().getTime()) / (1000 * 60 * 60 * 24);
                            //计算day除以readyDay取整，得到实际周期次数
                            int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                            Date startTime = taskDTO.getRestrictTimeStart();
                            List<Long> userIds=new ArrayList<>();
                            for (int i = 0; i < actualCycle; i++) {
                                //计算taskDTO.getRestrictTimeStart()加上readyDay天后的日期
                                Date endTime= null;
                                if (i==actualCycle-1){
                                    endTime=taskDTO.getRestrictTimeEnd();
                                }else{
                                    endTime=DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                                }
                                List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.onlyOneFinishPersonNumOne(taskDTO.getId(),startTime,endTime,taskDTO.getReadyNum());
                                //取出userTaskRecords中的userId
                                userIds.addAll(userTaskRecords.stream().map(UserTaskRecord::getUserId).collect(Collectors.toList()));
                                if (!CollectionUtils.isEmpty(userTaskRecords)){
                                    taskDTO.setFinishNum(taskDTO.getFinishNum()+userTaskRecords.size());
                                }
                                startTime=endTime;
                            }
                            //获取userIds去重后的数量
                            taskDTO.setFinishPersonNum((int) userIds.stream().distinct().count());
                        }else{
                            String str=sdf.format(taskDTO.getCreateTime());
                            String firstYear = str.substring(0,4);
                            String firstMonth = str.substring(5,7);
                            String lastStr=sdf.format(taskDTO.getRestrictTimeEnd());
                            String lastYear = lastStr.substring(0,4);
                            String lastMonth = lastStr.substring(5,7);
                            String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                            Date firstDate = sdf.parse(firstDay);
                            Date startTime=null;
                            if (taskDTO.getRestrictTimeStart().after(firstDate)){
                                startTime=taskDTO.getRestrictTimeStart();
                            }else{
                                startTime=firstDate;
                            }
                            List<Long> userIds=new ArrayList<>();
                            //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                            int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                            //计算monthNum除以readymonth取整，得到实际周期次数
                            int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                            for (int i = 0; i < actualCycle; i++) {
                                Date endTime=null;
                                if (i==actualCycle-1){
                                    endTime=taskDTO.getRestrictTimeEnd();
                                }else{
                                    //得到startTime日期+taskDTO.getReadyMonth()的最后一天
                                    endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                                    String strEndTime=sdf.format(endTime);
                                    String endTimeYear = strEndTime.substring(0,4);
                                    String endTimeMonth = strEndTime.substring(5,7);
                                    String endTimeDay = strEndTime.substring(8,10);
                                    //当月最后一天
                                    String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                                    endTime=sdf.parse(endTimeStr);
                                }
                                List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.onlyOneFinishPersonNumOne(taskDTO.getId(),startTime,endTime,taskDTO.getReadyNum());
                                if (!CollectionUtils.isEmpty(userTaskRecords)){
                                    //取出userTaskRecords中的userId
                                    userIds.addAll(userTaskRecords.stream().map(UserTaskRecord::getUserId).collect(Collectors.toList()));
                                    taskDTO.setFinishNum(taskDTO.getFinishNum()+userTaskRecords.size());
                                }
                                //在endTime加一个月
                                Calendar calendar = Calendar.getInstance();
                                calendar.setTime(endTime);
                                calendar.add(Calendar.MONTH, 1);
                                String startTimeStr =sdf.format(calendar.getTime());
                                String startTimeYear = startTimeStr.substring(0,4);
                                String startTimeMonth = startTimeStr.substring(5,7);
                                //当月最后一天
                                startTimeStr=startTimeYear+"-"+startTimeMonth+"-01 00:00:00";
                                startTime=sdf.parse(startTimeStr);
                            }
                            //获取userIds去重后的数量
                            taskDTO.setFinishPersonNum((int) userIds.stream().distinct().count());
                        }
                    }else{
                        if (taskDTO.getReayType().equals(readyDay)){
                            //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                            long day = (taskDTO.getRestrictTimeEnd().getTime() - taskDTO.getRestrictTimeStart().getTime()) / (1000 * 60 * 60 * 24);
                            //计算day除以readyDay取整，得到实际周期次数
                            int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                            Date startTime = taskDTO.getRestrictTimeStart();
                            List<Long> userIds=new ArrayList<>();
                            for (int i = 0; i < actualCycle; i++) {
                                //计算taskDTO.getRestrictTimeStart()加上readyDay天后的日期
                                Date endTime= null;
                                if (i==actualCycle-1){
                                    endTime=taskDTO.getRestrictTimeEnd();
                                }else{
                                    endTime=DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                                }
                                List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.onlyOneFinishPersonNumOne(taskDTO.getId(),startTime,endTime,null);
                                //取出userTaskRecords中的userId
                                userIds.addAll(userTaskRecords.stream().map(UserTaskRecord::getUserId).collect(Collectors.toList()));
                                if (!CollectionUtils.isEmpty(userTaskRecords)){
                                    taskDTO.setFinishNum(taskDTO.getFinishNum()+userTaskRecords.size());
                                }
                                startTime=endTime;
                            }
                            //获取userIds去重后的数量
                            taskDTO.setFinishPersonNum((int) userIds.stream().distinct().count());
                        }else{
                            String str=sdf.format(taskDTO.getCreateTime());
                            String firstYear = str.substring(0,4);
                            String firstMonth = str.substring(5,7);
                            String lastStr=sdf.format(taskDTO.getRestrictTimeEnd());
                            String lastYear = lastStr.substring(0,4);
                            String lastMonth = lastStr.substring(5,7);
                            String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                            Date firstDate = sdf.parse(firstDay);
                            Date startTime=null;
                            if (taskDTO.getRestrictTimeStart().after(firstDate)){
                                startTime=taskDTO.getRestrictTimeStart();
                            }else{
                                startTime=firstDate;
                            }
                            List<Long> userIds=new ArrayList<>();
                            //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                            int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                            //计算monthNum除以readymonth取整，得到实际周期次数
                            int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                            for (int i = 0; i < actualCycle; i++) {
                                Date endTime=null;
                                if (i==actualCycle-1){
                                    endTime=taskDTO.getRestrictTimeEnd();
                                }else{
                                    //得到startTime日期+taskDTO.getReadyMonth()的最后一天
                                    endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                                    String strEndTime=sdf.format(endTime);
                                    String endTimeYear = strEndTime.substring(0,4);
                                    String endTimeMonth = strEndTime.substring(5,7);
                                    String endTimeDay = strEndTime.substring(8,10);
                                    //当月最后一天
                                    String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                                    endTime=sdf.parse(endTimeStr);
                                }
                                List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.onlyOneFinishPersonNumOne(taskDTO.getId(),startTime,endTime,null);
                                if (!CollectionUtils.isEmpty(userTaskRecords)){
                                    //取出userTaskRecords中的userId
                                    userIds.addAll(userTaskRecords.stream().map(UserTaskRecord::getUserId).collect(Collectors.toList()));
                                    taskDTO.setFinishNum(taskDTO.getFinishNum()+userTaskRecords.size());
                                }
                                //在endTime加一个月
                                Calendar calendar = Calendar.getInstance();
                                calendar.setTime(endTime);
                                calendar.add(Calendar.MONTH, 1);
                                String startTimeStr =sdf.format(calendar.getTime());
                                String startTimeYear = startTimeStr.substring(0,4);
                                String startTimeMonth = startTimeStr.substring(5,7);
                                //当月最后一天
                                startTimeStr=startTimeYear+"-"+startTimeMonth+"-01 00:00:00";
                                startTime=sdf.parse(startTimeStr);
                            }
                            //获取userIds去重后的数量
                            taskDTO.setFinishPersonNum((int) userIds.stream().distinct().count());
                        }
                    }
                }
            }else{
                //1一次性任务，2周期性任务，3周期+阶梯
                if (taskDTO.getReadyCycle().equals(1)){
                    List<Long> userIds=new ArrayList<>();
                    //是兑礼任务，每下readyNum次订单算完成一次任务
//                    if (taskDTO.getTaskDesc().equals(2)){
//                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.onlyOneFinishPersonNumTwo(taskDTO.getId(),null,null,taskDTO.getReadyNum());
//                        if (!CollectionUtils.isEmpty(userTaskRecords)){
//                            //取出userTaskRecords中的userId
//                            userIds.addAll(userTaskRecords.stream().map(UserTaskRecord::getUserId).collect(Collectors.toList()));
//                            //计算userTaskRecords中finishNum之和
//                            for (UserTaskRecord userTaskRecord : userTaskRecords) {
//                                taskDTO.setFinishNum(taskDTO.getFinishNum()+userTaskRecord.getFinishNum());//
//                            }
//                            //获取userIds去重后的数量
//                            taskDTO.setFinishPersonNum((int) userIds.stream().distinct().count());
//                        }
//                    }else{
                    //不是兑礼任务，完成一次任务即任务结束
                    List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.onlyOneFinishPersonNumOne(taskDTO.getId(),null,null,taskDTO.getReadyNum());
                    if (!CollectionUtils.isEmpty(userTaskRecords)){
                        //取出userTaskRecords中的userId
                        userIds.addAll(userTaskRecords.stream().map(UserTaskRecord::getUserId).collect(Collectors.toList()));
                        taskDTO.setFinishNum(userTaskRecords.size());
                        //获取userIds去重后的数量
                        taskDTO.setFinishPersonNum((int) userIds.stream().distinct().count());
                    }
//                    }
                }else if (taskDTO.getReadyCycle().equals(2)){
                    if (taskDTO.getReayType().equals(readyDay)){
                        List<Long> userIds=new ArrayList<>();
                        //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                        String str=sdf.format(taskDTO.getCreateTime());
                        String firstYear = str.substring(0,4);
                        String firstMonth = str.substring(5,7);
                        String firstDay = str.substring(8,10);
                        String startTimeStr=firstYear+"-"+firstMonth+"-"+firstDay+" 00:00:00";
                        Date startTime=sdf.parse(startTimeStr);
                        long day = (new Date().getTime() - startTime.getTime()) / (1000 * 60 * 60 * 24);
                        //计算day除以readyDay取整，得到实际周期次数
                        int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                        for (int i = 0; i < actualCycle; i++) {
                            //计算taskDTO.getRestrictTimeStart()加上readyDay天后的日期
                            Date endTime=DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                            List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.onlyOneFinishPersonNumOne(taskDTO.getId(),startTime,endTime,taskDTO.getReadyNum());
                            if (!CollectionUtils.isEmpty(userTaskRecords)){
                                //取出userTaskRecords中的userId
                                userIds.addAll(userTaskRecords.stream().map(UserTaskRecord::getUserId).collect(Collectors.toList()));
                                taskDTO.setFinishNum(taskDTO.getFinishNum()+userTaskRecords.size());
                            }
                            startTime=endTime;
                        }
                        //获取userIds去重后的数量
                        taskDTO.setFinishPersonNum((int) userIds.stream().distinct().count());
                    }else{
                        String str=sdf.format(taskDTO.getCreateTime());
                        String firstYear = str.substring(0,4);
                        String firstMonth = str.substring(5,7);
                        String LastStr=sdf.format(new Date());
                        String lastYear = LastStr.substring(0,4);
                        String lastMonth = LastStr.substring(5,7);
                        String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                        Date firstDate = sdf.parse(firstDay);
                        Date startTime=firstDate;
                        List<Long> userIds=new ArrayList<>();
                        //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                        int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                        //计算monthNum除以readymonth取整，得到实际周期次数
                        int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                        for (int i = 0; i < actualCycle; i++) {
                            //得到startTime日期的最后一天
                            Date endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                            String strEndTime=sdf.format(endTime);
                            String endTimeYear = strEndTime.substring(0,4);
                            String endTimeMonth = strEndTime.substring(5,7);
                            String endTimeDay = strEndTime.substring(8,10);
                            //当月最后一天
                            String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                            endTime=sdf.parse(endTimeStr);
                            List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.onlyOneFinishPersonNumOne(taskDTO.getId(),startTime,endTime,taskDTO.getReadyNum());
                            //取出userTaskRecords中的userId
                            userIds.addAll(userTaskRecords.stream().map(UserTaskRecord::getUserId).collect(Collectors.toList()));
                            if (!CollectionUtils.isEmpty(userTaskRecords)){
                                taskDTO.setFinishNum(taskDTO.getFinishNum()+userTaskRecords.size());
                            }
                            //在endTime加一个月
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(endTime);
                            calendar.add(Calendar.MONTH, 1);
                            String startTimeStr =sdf.format(calendar.getTime());
                            String startTimeYear = startTimeStr.substring(0,4);
                            String startTimeMonth = startTimeStr.substring(5,7);
                            //当月最后一天
                            startTimeStr=startTimeYear+"-"+startTimeMonth+"-01 00:00:00";
                            startTime=sdf.parse(startTimeStr);
                        }
                        taskDTO.setFinishPersonNum((int) userIds.stream().distinct().count());
                    }
                }else{
                    log.info("===================================任务类型："+taskDTO.getTaskDesc());
                    if (taskDTO.getTaskDesc().equals(3)){//线下消费
                        if (taskDTO.getReayType().equals(readyDay)){
                            //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                            String str=sdf.format(taskDTO.getCreateTime());
                            String firstYear = str.substring(0,4);
                            String firstMonth = str.substring(5,7);
                            String firstDay = str.substring(8,10);
                            String startTimeStr=firstYear+"-"+firstMonth+"-"+firstDay+" 00:00:00";
                            Date startTime=sdf.parse(startTimeStr);
                            long day = (new Date().getTime() - startTime.getTime()) / (1000 * 60 * 60 * 24);
                            //计算day除以readyDay取整，得到实际周期次数
                            int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                            List<Long> userIds=new ArrayList<>();
                            for (int i = 0; i < actualCycle; i++) {
                                //计算taskDTO.getRestrictTimeStart()加上readyDay天后的日期
                                Date endTime=DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                                List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.onlyOneFinishPersonNumOne(taskDTO.getId(),startTime,endTime,taskDTO.getReadyNum());
                                //取出userTaskRecords中的userId
                                userIds.addAll(userTaskRecords.stream().map(UserTaskRecord::getUserId).collect(Collectors.toList()));
                                if (!CollectionUtils.isEmpty(userTaskRecords)){
                                    taskDTO.setFinishNum(taskDTO.getFinishNum()+userTaskRecords.size());
                                }
                                startTime=endTime;
                            }
                            //获取userIds去重后的数量
                            taskDTO.setFinishPersonNum((int) userIds.stream().distinct().count());
                        }else{
                            String str=sdf.format(taskDTO.getCreateTime());
                            String firstYear = str.substring(0,4);
                            String firstMonth = str.substring(5,7);
                            String LastStr=sdf.format(new Date());
                            String lastYear = LastStr.substring(0,4);
                            String lastMonth = LastStr.substring(5,7);
                            String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                            Date firstDate = sdf.parse(firstDay);
                            Date startTime=firstDate;
                            List<Long> userIds=new ArrayList<>();
                            //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                            int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                            //计算monthNum除以readymonth取整，得到实际周期次数
                            int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                            for (int i = 0; i < actualCycle; i++) {
                                //得到startTime日期的最后一天
                                Date endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                                String strEndTime=sdf.format(endTime);
                                String endTimeYear = strEndTime.substring(0,4);
                                String endTimeMonth = strEndTime.substring(5,7);
                                String endTimeDay = strEndTime.substring(8,10);
                                //当月最后一天
                                String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                                endTime=sdf.parse(endTimeStr);
                                List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.onlyOneFinishPersonNumOne(taskDTO.getId(),startTime,endTime,taskDTO.getReadyNum());
                                if (!CollectionUtils.isEmpty(userTaskRecords)){
                                    //取出userTaskRecords中的userId
                                    userIds.addAll(userTaskRecords.stream().map(UserTaskRecord::getUserId).collect(Collectors.toList()));
                                    taskDTO.setFinishNum(taskDTO.getFinishNum()+userTaskRecords.size());
                                }
                                //在endTime加一个月
                                Calendar calendar = Calendar.getInstance();
                                calendar.setTime(endTime);
                                calendar.add(Calendar.MONTH, 1);
                                String startTimeStr =sdf.format(calendar.getTime());
                                String startTimeYear = startTimeStr.substring(0,4);
                                String startTimeMonth = startTimeStr.substring(5,7);
                                //当月最后一天
                                startTimeStr=startTimeYear+"-"+startTimeMonth+"-01 00:00:00";
                                startTime=sdf.parse(startTimeStr);
                            }
                            //获取userIds去重后的数量
                            taskDTO.setFinishPersonNum((int) userIds.stream().distinct().count());
                        }
                    }else{
                        if (taskDTO.getReayType().equals(readyDay)){
                            //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                            String str=sdf.format(taskDTO.getCreateTime());
                            String firstYear = str.substring(0,4);
                            String firstMonth = str.substring(5,7);
                            String firstDay = str.substring(8,10);
                            String startTimeStr=firstYear+"-"+firstMonth+"-"+firstDay+" 00:00:00";
                            Date startTime=sdf.parse(startTimeStr);
                            long day = (new Date().getTime() - startTime.getTime()) / (1000 * 60 * 60 * 24);
                            //计算day除以readyDay取整，得到实际周期次数
                            int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                            List<Long> userIds=new ArrayList<>();
                            for (int i = 0; i < actualCycle; i++) {
                                //计算taskDTO.getRestrictTimeStart()加上readyDay天后的日期
                                Date endTime=DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                                List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.onlyOneFinishPersonNumOne(taskDTO.getId(),startTime,endTime,null);
                                //取出userTaskRecords中的userId
                                userIds.addAll(userTaskRecords.stream().map(UserTaskRecord::getUserId).collect(Collectors.toList()));
                                if (!CollectionUtils.isEmpty(userTaskRecords)){
                                    taskDTO.setFinishNum(taskDTO.getFinishNum()+userTaskRecords.size());
                                }
                                startTime=endTime;
                            }
                            //获取userIds去重后的数量
                            taskDTO.setFinishPersonNum((int) userIds.stream().distinct().count());
                        }else{
                            String str=sdf.format(taskDTO.getCreateTime());
                            String firstYear = str.substring(0,4);
                            String firstMonth = str.substring(5,7);
                            String LastStr=sdf.format(new Date());
                            String lastYear = LastStr.substring(0,4);
                            String lastMonth = LastStr.substring(5,7);
                            String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                            Date firstDate = sdf.parse(firstDay);
                            Date startTime=firstDate;
                            List<Long> userIds=new ArrayList<>();
                            //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                            int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                            //计算monthNum除以readymonth取整，得到实际周期次数
                            int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                            for (int i = 0; i < actualCycle; i++) {
                                //得到startTime日期的最后一天
                                Date endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                                String strEndTime=sdf.format(endTime);
                                String endTimeYear = strEndTime.substring(0,4);
                                String endTimeMonth = strEndTime.substring(5,7);
                                String endTimeDay = strEndTime.substring(8,10);
                                //当月最后一天
                                String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                                endTime=sdf.parse(endTimeStr);
                                List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.onlyOneFinishPersonNumOne(taskDTO.getId(),startTime,endTime,null);
                                if (!CollectionUtils.isEmpty(userTaskRecords)){
                                    //取出userTaskRecords中的userId
                                    userIds.addAll(userTaskRecords.stream().map(UserTaskRecord::getUserId).collect(Collectors.toList()));
                                    taskDTO.setFinishNum(taskDTO.getFinishNum()+userTaskRecords.size());
                                }
                                //在endTime加一个月
                                Calendar calendar = Calendar.getInstance();
                                calendar.setTime(endTime);
                                calendar.add(Calendar.MONTH, 1);
                                String startTimeStr =sdf.format(calendar.getTime());
                                String startTimeYear = startTimeStr.substring(0,4);
                                String startTimeMonth = startTimeStr.substring(5,7);
                                //当月最后一天
                                startTimeStr=startTimeYear+"-"+startTimeMonth+"-01 00:00:00";
                                startTime=sdf.parse(startTimeStr);
                            }
                            //获取userIds去重后的数量
                            taskDTO.setFinishPersonNum((int) userIds.stream().distinct().count());
                        }
                    }
                }
            }
            if (null == taskDTO.getJoinInNum()){
                taskDTO.setJoinInNum(0);
            }
        }
        return new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),listDTO);
    }

    /**
     * 导出任务列表
     *
     * @param jsonParam
     * @param reportCode
     * @param fileName
     * @param fileExt
     * @param downloadCenterId
     */
    @Override
    public void exportTaskList(String jsonParam, String reportCode, String fileName, String fileExt, Long downloadCenterId) throws ParseException {
        CurrentUserDTO commonLoginDTO = SecurityContext.getUser();
        InteractionTaskSelectVo interactionTaskSelectVo = JSON.parseObject(jsonParam, InteractionTaskSelectVo.class);
        interactionTaskSelectVo.setPageNum(1);
        interactionTaskSelectVo.setPageSize(99999);
        PageInfo<InteractionTaskDTO> listDTO = getInteractionTaskList(interactionTaskSelectVo);
        List<InteractionTaskDTO> list = listDTO.getList();
        if (CollectionUtils.isEmpty(list)){
            return;
        }
        List<InteractionTaskExportDTO> dtoList = new ArrayList<>();
        for (InteractionTaskDTO taskDTO : list){
            InteractionTaskExportDTO dto = new InteractionTaskExportDTO();
            dto.setTaskType(taskDTO.getTaskType().equals(1)?"限时":taskDTO.getTaskType().equals(2)?"购物":"互动");
            dto.setTaskName(taskDTO.getTaskName());
            dto.setTaskDesc(taskDTO.getTaskDesc().equals(1)?"线下打卡":taskDTO.getTaskDesc().equals(2)?"兑礼任务":taskDTO.getTaskDesc().equals(3)?"线下消费":taskDTO.getTaskDesc().equals(4)?"邀请好友":"首次购买");
            if (taskDTO.getShowTimeType()==1){
                dto.setShowTime(sdf.format(taskDTO.getShowTimeStart())+"-"+sdf.format(taskDTO.getShowTimeEnd()));
            }else{
                dto.setShowTime("永久展示");
            }
            if (taskDTO.getIsTimeRestrict()==1){
                dto.setRestrictTime(sdf.format(taskDTO.getRestrictTimeStart())+"-"+sdf.format(taskDTO.getRestrictTimeEnd()));
            }else{
                dto.setRestrictTime("不限时");
            }
            dto.setTaskStatus(taskDTO.getTaskStatus()==0?"待开始":taskDTO.getTaskStatus()==1?"进行中":"已结束");
            if (taskDTO.getReadyCycle().equals(1)){
                dto.setReadyNum(taskDTO.getReadyNum()+"次");
            }else if (taskDTO.getReadyCycle().equals(2)){
                if (taskDTO.getReayType().equals(readyDay)){
                    dto.setReadyNum(taskDTO.getReadyNum()+"次 / "+taskDTO.getReadyDay()+"天");
                }else{
                    dto.setReadyNum(taskDTO.getReadyNum()+"次 / "+taskDTO.getReadyMonth()+"月");
                }
            }else{
                if (taskDTO.getTaskDesc()==3){
                    if (taskDTO.getReayType().equals(readyDay)){
                        dto.setReadyNum(taskDTO.getReadyNum()+"次 / "+taskDTO.getReadyDay()+"天");
                    }else{
                        dto.setReadyNum(taskDTO.getReadyNum()+"次 / "+taskDTO.getReadyMonth()+"月");
                    }
                }else{
                    if (taskDTO.getReayType().equals(readyDay)){
                        dto.setReadyNum(taskDTO.getTotalReadyNum()+"次 / "+taskDTO.getReadyDay()+"天");
                    }else{
                        dto.setReadyNum(taskDTO.getTotalReadyNum()+"次 / "+taskDTO.getReadyMonth()+"月");
                    }
                }
            }
            //查询奖励
            List<TaskReward> taskRewards = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId, taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
            List<TaskRewardDto> taskRewardDtos=BeanCopierUtils.convertList(taskRewards,TaskRewardDto.class);
            String couponNamesAll="";
            for (int i = 0; i < taskRewardDtos.size(); i++){
                TaskRewardDto taskRewardDto = taskRewardDtos.get(i);
                if (taskDTO.getReadyCycle().equals(3)){
                    dto.setRewardType("组合");
                }else{
                    dto.setRewardType(taskRewardDto.getRewardType()==1?"优惠券":"积分");
                }
                if (taskRewardDto.getRewardType()==1){
                    //查询券信息
                    String[] couponIds=taskRewardDto.getCouponIds().split(",");
                    String couponNameStr="";
                    for (int j = 0; j < couponIds.length; j++){
                        //差券详情查询接口
                        Result<JSONObject> couponDetail=mujiOpenApiFeignClient.memberCouponStockId(couponIds[j]);
                        String couponName=couponDetail.getData().getString("name");
                        if (j<couponIds.length-1){
                            couponNameStr=couponNameStr+couponName+",";
                        }else{
                            couponNameStr=couponNameStr+couponName;
                        }
                    }
                    if (i<taskRewardDtos.size()-1){
                        couponNamesAll=couponNamesAll+couponNameStr+",";
                    }else{
                        couponNamesAll=couponNamesAll+couponNameStr;
                    }
                }else{
                    if (i<taskRewardDtos.size()-1){
                        couponNamesAll=couponNamesAll+taskRewardDto.getPointsNum()+",";
                    }else{
                        couponNamesAll=couponNamesAll+taskRewardDto.getPointsNum();
                    }
                }
            }
            dto.setCouponIdNames(couponNamesAll);
            dto.setJoinInNum(taskDTO.getJoinInNum());
            dto.setFinishPersonNum(taskDTO.getFinishPersonNum());
            dto.setFinishNum(taskDTO.getFinishNum());
            dto.setStatus(taskDTO.getStatus()==0?"启用":"停用");
            dtoList.add(dto);
        }

        List<JSONObject> reList = new ArrayList<>(JSON.parseArray(JSON.toJSONString(dtoList), JSONObject.class));
        exportService.writeExportList(fileName, fileExt, reportCode, downloadCenterId, commonLoginDTO, reList);
    }

    @Override
    public InteractionTaskDTO getInteractionTaskInfo(Long id) {
        InteractionTask interactionTask = interactionTaskMapper.selectById(id);
        List<TaskReward> taskRewards = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId, interactionTask.getId()).orderByAsc(TaskReward::getSortNum));
        List<TaskRewardDto> taskRewardDtos = BeanCopierUtils.convertList(taskRewards, TaskRewardDto.class);
        InteractionTaskDTO interactionTaskDTO = BeanCopierUtils.convertObject(interactionTask, InteractionTaskDTO.class);
        interactionTaskDTO.setTaskRewardList(taskRewardDtos);
        return interactionTaskDTO;
    }

    @Override
    @Transactional
    public void addInteractionTaskInfo(InteractionTaskVo param) {
        InteractionTask interactionTask = BeanCopierUtils.convertObject(param, InteractionTask.class);
        Date now=new Date();
        interactionTask.setCreateAt(SecurityContext.getUser().getUid().toString());
        interactionTask.setCreateTime(now);
        interactionTask.setUpdateAt(SecurityContext.getUser().getUid().toString());
        interactionTask.setUpdateTime(now);
        interactionTask.setTenantId(SecurityContext.getUser().getTenantId());
        interactionTask.setStatus(0);
        log.info("interactionTask:"+ JSONObject.toJSONString(interactionTask));
        interactionTaskMapper.insert(interactionTask);
        if (param.getTaskRewardList()!=null&&param.getTaskRewardList().size()>0){
            for (TaskRewardVo taskRewardVo : param.getTaskRewardList()){
                if (taskRewardVo.getRewardType()==1){
                    String[] couponIds=taskRewardVo.getCouponIds().split(",");
                    for (int i = 0; i < couponIds.length; i++){
                        //差券详情查询接口
                        Result<JSONObject> couponDetail=mujiOpenApiFeignClient.memberCouponStockId(couponIds[i]);
                        if (null == couponDetail.getData() || !couponDetail.getData().containsKey("name")){
                            throw new BusinessException(ErrorCode.BAD_REQUEST, "券批次号不正确");
                        }
                    }
                }
                TaskReward taskReward = BeanCopierUtils.convertObject(taskRewardVo, TaskReward.class);
                taskReward.setTaskId(interactionTask.getId());
                taskReward.setCreateAt(SecurityContext.getUser().getUid().toString());
                taskReward.setCreateTime(now);
                taskReward.setUpdateAt(SecurityContext.getUser().getUid().toString());
                taskReward.setUpdateTime(now);
                taskReward.setTenantId(SecurityContext.getUser().getTenantId());
                taskRewardMapper.insert(taskReward);
            }
        }
    }

    @Override
    @Transactional
    public void updateInteractionTaskInfo(InteractionTaskVo param,Integer type) {
        InteractionTask interactionTask = BeanCopierUtils.convertObject(param, InteractionTask.class);
        Date now=new Date();
        interactionTask.setUpdateAt(SecurityContext.getUser().getUid().toString());
        interactionTask.setUpdateTime(now);
        interactionTaskMapper.updateById(interactionTask);
        if (type==1){
            taskRewardMapper.delete(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId, interactionTask.getId()));
            if (param.getTaskRewardList()!=null&&param.getTaskRewardList().size()>0){
                for (TaskRewardVo taskRewardVo : param.getTaskRewardList()){
                    if (taskRewardVo.getRewardType()==1){
                        String[] couponIds=taskRewardVo.getCouponIds().split(",");
                        String couponNamesStr="";
                        for (int i = 0; i < couponIds.length; i++){
                            //差券详情查询接口
                            Result<JSONObject> couponDetail=mujiOpenApiFeignClient.memberCouponStockId(couponIds[i]);
                            if (null == couponDetail.getData() || !couponDetail.getData().containsKey("name")){
                                throw new BusinessException(ErrorCode.BAD_REQUEST, "券批次号不正确");
                            }
                        }
                    }
                    TaskReward taskReward = BeanCopierUtils.convertObject(taskRewardVo, TaskReward.class);
                    taskReward.setTaskId(interactionTask.getId());
                    taskReward.setCreateAt(SecurityContext.getUser().getUid().toString());
                    taskReward.setCreateTime(now);
                    taskReward.setUpdateAt(SecurityContext.getUser().getUid().toString());
                    taskReward.setUpdateTime(now);
                    taskReward.setTenantId(SecurityContext.getUser().getTenantId());
                    taskRewardMapper.insert(taskReward);
                }
            }
        }
    }

    @Override
    @Transactional
    public void deleteInteractionTaskInfo(InteractionTaskVo param) {
        interactionTaskMapper.deleteById(param.getId());
        taskRewardMapper.delete(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId, param.getId()));
    }

    @Override
    public List<InteractionTaskDTO> getInteractionTaskListApp(Long userId) throws ParseException {
        List<InteractionTaskDTO> resultListDTO=new ArrayList<>();
        Date now=new Date();
        LambdaQueryWrapper<InteractionTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InteractionTask::getStatus, 0);
        wrapper.and(wq->wq.eq(InteractionTask::getShowTimeType,2)
                .or().eq(InteractionTask::getShowTimeType,1).lt(InteractionTask::getShowTimeStart,now).gt(InteractionTask::getShowTimeEnd,now));
        wrapper.orderByAsc(InteractionTask::getTaskType);
        wrapper.orderByDesc(InteractionTask::getCreateTime);
        List<InteractionTask> list=interactionTaskMapper.selectList(wrapper);
        List<InteractionTaskDTO> listDTO = BeanCopierUtils.convertList(list, InteractionTaskDTO.class);
        for (InteractionTaskDTO taskDTO : listDTO) {
//            if (taskDTO.getTaskDesc()==5){
                //查询用户会员信息
//                Result<UserSimpleDTO> userInfoDTO = userInfoFeginClient.getDbUserSimpleInfo(userId);
//                if (userInfoDTO.getData().getFirstOrderStatus()==2){
//                    if (!userInfoDTO.getData().getFirstOrderTaskId().equals(taskDTO.getId())){
//                        continue;
//                    }
//                }else{
//                    if (userInfoDTO.getData().getFirstOrderStatus()==1){
//                        continue;
//                    }
//                    boolean flagCheck=checkNewOrder(userInfoDTO.getData().getCardNo());
//                    if (flagCheck){
//                        userInfoFeginClient.updateFirstOrderStatus(1,null);
//                        continue;
//                    }
//                }
//            }
            List<TaskReward> taskRewards = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId, taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
            List<TaskRewardDto> taskRewardDtos = BeanCopierUtils.convertList(taskRewards, TaskRewardDto.class);
            for (TaskRewardDto taskRewardDto : taskRewardDtos){
                if (taskRewardDto.getRewardType()==1){//优惠券
                    String[] couponIds=taskRewardDto.getCouponIds().split(",");
                    String couponNamesStr="";
                    for (int i = 0; i < couponIds.length; i++){
                        //差券详情查询接口
                        Result<JSONObject> couponDetail=mujiOpenApiFeignClient.memberCouponStockId(couponIds[i]);
                        String couponName="";
                        if (null != couponDetail && couponDetail.getData().containsKey("name")){
                            couponName=couponDetail.getData().getString("name");
                        }
                        if (i<couponIds.length-1){
                            couponNamesStr=couponNamesStr+couponName+",";
                        }else{
                            couponNamesStr=couponNamesStr+couponName;
                        }
                    }
                    taskRewardDto.setCouponNames(couponNamesStr);
                }
            }
            taskDTO.setTaskRewardList(taskRewardDtos);
            //1限时任务，否则不限时任务
            if (taskDTO.getIsTimeRestrict()==1){
                //1一次性任务，2周期性任务,3阶梯任务
                if (taskDTO.getReadyCycle().equals(1)){
                    LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                    wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                    wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                    wrapperRecord.gt(UserTaskRecord::getCreateTime,taskDTO.getRestrictTimeStart());
                    wrapperRecord.lt(UserTaskRecord::getCreateTime,taskDTO.getRestrictTimeEnd());

                    List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                    if (!CollectionUtils.isEmpty(userTaskRecords)){
                        taskDTO.setAllReadyNum(userTaskRecords.size());
                    }
                }else if (taskDTO.getReadyCycle().equals(2)){
                    if (taskDTO.getReayType().equals(readyDay)){
                        //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                        long day = (taskDTO.getRestrictTimeEnd().getTime() - taskDTO.getRestrictTimeStart().getTime()) / (1000 * 60 * 60 * 24);
                        //计算day除以readyDay取整，得到实际周期次数
                        int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                        Date startTime = taskDTO.getRestrictTimeStart();
                        for (int i = 0; i < actualCycle; i++) {
                            //计算taskDTO.getRestrictTimeStart()加上readyDay天后的日期
                            Date endTime= null;
                            if (i==actualCycle-1){
                                endTime=taskDTO.getRestrictTimeEnd();
                            }else{
                                endTime=DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                            }
                            //判断now是否在startTime和endTime之间
                            if (now.after(startTime) && now.before(endTime)){
                                LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);

                                List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                if (!CollectionUtils.isEmpty(userTaskRecords)){
                                    taskDTO.setAllReadyNum(userTaskRecords.size());
                                }
                                taskDTO.setSuccessCycleNum(i+1);
                                break;
                            }
                            startTime=endTime;
                        }
                    }else{
                        String str=sdf.format(taskDTO.getCreateTime());
                        String firstYear = str.substring(0,4);
                        String firstMonth = str.substring(5,7);
                        String LastStr=sdf.format(taskDTO.getRestrictTimeEnd());
                        String lastYear = LastStr.substring(0,4);
                        String lastMonth = LastStr.substring(5,7);
                        String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                        Date firstDate = sdf.parse(firstDay);
                        Date startTime=null;
                        if (taskDTO.getRestrictTimeStart().after(firstDate)){
                            startTime=taskDTO.getRestrictTimeStart();
                        }else{
                            startTime=firstDate;
                        }
                        //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                        int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                        //计算monthNum除以readymonth取整，得到实际周期次数
                        int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                        for (int i = 0; i < actualCycle; i++) {
                            //得到startTime日期的最后一天
                            Date endTime= null;
                            if (i==actualCycle-1){
                                endTime=taskDTO.getRestrictTimeEnd();
                            }else{
                                //得到startTime日期+taskDTO.getReadyMonth()的最后一天
                                endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                                String strEndTime=sdf.format(endTime);
                                String endTimeYear = strEndTime.substring(0,4);
                                String endTimeMonth = strEndTime.substring(5,7);
                                String endTimeDay = strEndTime.substring(8,10);
                                //当月最后一天
                                String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                                endTime=sdf.parse(endTimeStr);
                            }
                            //判断now是否在startTime和endTime之间
                            if (now.after(startTime) && now.before(endTime)){
                                LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);

                                List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                if (!CollectionUtils.isEmpty(userTaskRecords)){
                                    taskDTO.setAllReadyNum(userTaskRecords.size());
                                }
                                taskDTO.setSuccessCycleNum(i+1);
                                break;
                            }
                            //在endTime加一个月
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(endTime);
                            calendar.add(Calendar.MONTH, 1);
                            String startTimeStr =sdf.format(calendar.getTime());
                            String startTimeYear = startTimeStr.substring(0,4);
                            String startTimeMonth = startTimeStr.substring(5,7);
                            //当月最后一天
                            startTimeStr=startTimeYear+"-"+startTimeMonth+"-01 00:00:00";
                            startTime=sdf.parse(startTimeStr);
                        }
                    }
                }else{
                    if (taskDTO.getReayType().equals(readyDay)){
                        //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                        long day = (taskDTO.getRestrictTimeEnd().getTime() - taskDTO.getRestrictTimeStart().getTime()) / (1000 * 60 * 60 * 24);
                        //计算day除以readyDay取整，得到实际周期次数
                        int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                        Date startTime = taskDTO.getRestrictTimeStart();
                        for (int i = 0; i < actualCycle; i++) {
                            //计算taskDTO.getRestrictTimeStart()加上readyDay天后的日期
                            Date endTime= null;
                            if (i==actualCycle-1){
                                endTime=taskDTO.getRestrictTimeEnd();
                            }else{
                                endTime=DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                            }
                            //判断now是否在startTime和endTime之间
                            if (now.after(startTime) && now.before(endTime)){
                                LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);

                                List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                if (!CollectionUtils.isEmpty(userTaskRecords)){
                                    taskDTO.setAllReadyNum(userTaskRecords.size());
                                }
                                taskDTO.setSuccessCycleNum(i+1);
                                break;
                            }
                            startTime=endTime;
                        }
                    }else{
                        String str=sdf.format(taskDTO.getCreateTime());
                        String firstYear = str.substring(0,4);
                        String firstMonth = str.substring(5,7);
                        String LastStr=sdf.format(taskDTO.getRestrictTimeEnd());
                        String lastYear = LastStr.substring(0,4);
                        String lastMonth = LastStr.substring(5,7);
                        String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                        Date firstDate = sdf.parse(firstDay);
                        Date startTime=null;
                        if (taskDTO.getRestrictTimeStart().after(firstDate)){
                            startTime=taskDTO.getRestrictTimeStart();
                        }else{
                            startTime=firstDate;
                        }
                        //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                        int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                        //计算monthNum除以readymonth取整，得到实际周期次数
                        int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                        for (int i = 0; i < actualCycle; i++) {
                            //得到startTime日期的最后一天
                            Date endTime= null;
                            if (i==actualCycle-1){
                                endTime=taskDTO.getRestrictTimeEnd();
                            }else{
                                //得到startTime日期+taskDTO.getReadyMonth()的最后一天
                                endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                                String strEndTime=sdf.format(endTime);
                                String endTimeYear = strEndTime.substring(0,4);
                                String endTimeMonth = strEndTime.substring(5,7);
                                String endTimeDay = strEndTime.substring(8,10);
                                //当月最后一天
                                String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                                endTime=sdf.parse(endTimeStr);
                            }
                            //判断当月是否在任务月内
                            if (now.after(startTime) && now.before(endTime)){
                                LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);

                                List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                if (!CollectionUtils.isEmpty(userTaskRecords)){
                                    if (taskDTO.getTotalReadyNum()!=null){
                                        if (taskDTO.getReadyNum()!=null){
                                            taskDTO.setAllReadyNum((userTaskRecords.size() / taskDTO.getReadyNum()));
                                        }else{
                                            taskDTO.setAllReadyNum(userTaskRecords.size());
                                        }
                                    }else{
                                        taskDTO.setAllReadyNum(userTaskRecords.size());
                                    }
                                }
                                taskDTO.setSuccessCycleNum(i+1);
                                break;
                            }
                            //在endTime加一个月
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(endTime);
                            calendar.add(Calendar.MONTH, 1);
                            String startTimeStr =sdf.format(calendar.getTime());
                            String startTimeYear = startTimeStr.substring(0,4);
                            String startTimeMonth = startTimeStr.substring(5,7);
                            //当月最后一天
                            startTimeStr=startTimeYear+"-"+startTimeMonth+"-01 00:00:00";
                            startTime=sdf.parse(startTimeStr);
                        }
                    }
                }
            }else{
                //1一次性任务，2周期性任务
                if (taskDTO.getReadyCycle().equals(1)){
                    LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                    wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                    wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());

                    List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                    if (!CollectionUtils.isEmpty(userTaskRecords)){
                        taskDTO.setAllReadyNum(userTaskRecords.size());
                    }
                }else if (taskDTO.getReadyCycle().equals(2)){
                    if (taskDTO.getReayType().equals(readyDay)){
                        //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                        String str=sdf.format(taskDTO.getCreateTime());
                        String firstYear = str.substring(0,4);
                        String firstMonth = str.substring(5,7);
                        String firstDay = str.substring(8,10);
                        String startTimeStr=firstYear+"-"+firstMonth+"-"+firstDay+" 00:00:00";
                        Date startTime=sdf.parse(startTimeStr);
                        long day = (new Date().getTime() - startTime.getTime()) / (1000 * 60 * 60 * 24);
                        //计算day除以readyDay取整，得到实际周期次数
                        int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                        for (int i = 0; i < actualCycle; i++) {
                            //计算taskDTO.getRestrictTimeStart()加上readyDay天后的日期
                            Date endTime=DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                            //判断now是否在startTime和endTime之间
                            if (now.after(startTime) && now.before(endTime)){
                                LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);

                                List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                if (!CollectionUtils.isEmpty(userTaskRecords)){
                                    taskDTO.setAllReadyNum(userTaskRecords.size());
                                }
                                taskDTO.setSuccessCycleNum(i+1);
                                break;
                            }
                            startTime=endTime;
                        }
                    }else{
                        String str=sdf.format(taskDTO.getCreateTime());
                        String firstYear = str.substring(0,4);
                        String firstMonth = str.substring(5,7);
                        String LastStr=sdf.format(now);
                        String lastYear = LastStr.substring(0,4);
                        String lastMonth = LastStr.substring(5,7);
                        String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                        Date firstDate = sdf.parse(firstDay);
                        Date startTime=firstDate;
                        //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                        int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                        //计算monthNum除以readymonth取整，得到实际周期次数
                        int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                        for (int i = 0; i < actualCycle; i++) {
                            //得到startTime日期的最后一天
                            Date endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                            String strEndTime=sdf.format(endTime);
                            String endTimeYear = strEndTime.substring(0,4);
                            String endTimeMonth = strEndTime.substring(5,7);
                            String endTimeDay = strEndTime.substring(8,10);
                            //当月最后一天
                            String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                            endTime=sdf.parse(endTimeStr);
                            //判断now是否在startTime和endTime之间
                            if (now.after(startTime) && now.before(endTime)){
                                LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);

                                List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                if (!CollectionUtils.isEmpty(userTaskRecords)){
                                    taskDTO.setAllReadyNum(userTaskRecords.size());
                                }
                                taskDTO.setSuccessCycleNum(i+1);
                                break;
                            }
                            //在endTime加一个月
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(endTime);
                            calendar.add(Calendar.MONTH, 1);
                            String startTimeStr =sdf.format(calendar.getTime());
                            String startTimeYear = startTimeStr.substring(0,4);
                            String startTimeMonth = startTimeStr.substring(5,7);
                            //当月最后一天
                            startTimeStr=startTimeYear+"-"+startTimeMonth+"-01 00:00:00";
                            startTime=sdf.parse(startTimeStr);
                        }
                    }
                }else{
                    if (taskDTO.getReayType().equals(readyDay)){
                        //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                        String str=sdf.format(taskDTO.getCreateTime());
                        String firstYear = str.substring(0,4);
                        String firstMonth = str.substring(5,7);
                        String firstDay = str.substring(8,10);
                        String startTimeStr=firstYear+"-"+firstMonth+"-"+firstDay+" 00:00:00";
                        Date startTime=sdf.parse(startTimeStr);
                        long day = (new Date().getTime() - startTime.getTime()) / (1000 * 60 * 60 * 24);
                        //计算day除以readyDay取整，得到实际周期次数
                        int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                        for (int i = 0; i < actualCycle; i++) {
                            //计算taskDTO.getRestrictTimeStart()加上readyDay天后的日期
                            Date endTime=DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                            //判断now是否在startTime和endTime之间
                            if (now.after(startTime) && now.before(endTime)){
                                LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);

                                List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                if (!CollectionUtils.isEmpty(userTaskRecords)){
                                    taskDTO.setAllReadyNum(userTaskRecords.size());
                                }
                                taskDTO.setSuccessCycleNum(i+1);
                                break;
                            }
                            startTime=endTime;
                        }
                    }else{
                        String str=sdf.format(taskDTO.getCreateTime());
                        String firstYear = str.substring(0,4);
                        String firstMonth = str.substring(5,7);
                        String LastStr=sdf.format(now);
                        String lastYear = LastStr.substring(0,4);
                        String lastMonth = LastStr.substring(5,7);
                        String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                        Date firstDate = sdf.parse(firstDay);
                        Date startTime=firstDate;
                        //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                        int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                        //计算monthNum除以readymonth取整，得到实际周期次数
                        int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                        for (int i = 0; i < actualCycle; i++) {
                            //得到startTime日期的最后一天
                            Date endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                            String strEndTime=sdf.format(endTime);
                            String endTimeYear = strEndTime.substring(0,4);
                            String endTimeMonth = strEndTime.substring(5,7);
                            String endTimeDay = strEndTime.substring(8,10);
                            //当月最后一天
                            String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                            endTime=sdf.parse(endTimeStr);
                            //判断当月是否在任务月内
                            if (now.after(startTime) && now.before(endTime)){
                                LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);

                                List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                if (!CollectionUtils.isEmpty(userTaskRecords)){
                                    if (taskDTO.getTotalReadyNum()!=null){
                                        if (taskDTO.getReadyNum()!=null){
                                            taskDTO.setAllReadyNum((userTaskRecords.size() / taskDTO.getReadyNum()));
                                        }else{
                                            taskDTO.setAllReadyNum(userTaskRecords.size());
                                        }
                                    }else{
                                        taskDTO.setAllReadyNum(userTaskRecords.size());
                                    }
                                }
                                taskDTO.setSuccessCycleNum(i+1);
                                break;
                            }
                            //在endTime加一个月
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(endTime);
                            calendar.add(Calendar.MONTH, 1);
                            String startTimeStr =sdf.format(calendar.getTime());
                            String startTimeYear = startTimeStr.substring(0,4);
                            String startTimeMonth = startTimeStr.substring(5,7);
                            //当月最后一天
                            startTimeStr=startTimeYear+"-"+startTimeMonth+"-01 00:00:00";
                            startTime=sdf.parse(startTimeStr);
                        }
                    }
                }
            }
            resultListDTO.add(taskDTO);
        }
        return resultListDTO;
    }

    @Override
    public void taskSuccessRecordCard(String longitude,String latitude) throws ParseException {
        //查询所有门店信息
        Result<List<StoreDTO>> storeList=crowdFeignClient.getLongitudeLatitude();
        if (CollectionUtils.isEmpty(storeList.getData())){
            return;
        }
        boolean isHave=false;
        for (StoreDTO storeDTO : storeList.getData()) {
            //判断门店是否在范围内
            if (checkLongitude(Double.parseDouble(longitude),Double.parseDouble(latitude),
                    Double.parseDouble(storeDTO.getLongitude()),Double.parseDouble(storeDTO.getLatitude()))){
                isHave=true;
            }
        }
        if (!isHave){
            return;
        }
        SimpleDateFormat sdfMonth=new SimpleDateFormat("yyyy-MM");
        Long userId=SecurityContext.getUser().getUid();
        String taskCard = "task_card_" + userId;
        boolean lock = redisService.lock(taskCard, 3);
        if (!lock) {
            return;
        }
        try{
            //查询用户会员信息
            Result<UserSimpleDTO> userInfoDTO = userInfoFeginClient.getUserSimpleInfo(userId);
            Date now=new Date();
            LambdaQueryWrapper<InteractionTask> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(InteractionTask::getStatus, 0);
            wrapper.eq(InteractionTask::getTaskDesc, 1);
            wrapper.and(wq->wq.eq(InteractionTask::getIsTimeRestrict,2)
                    .or().eq(InteractionTask::getIsTimeRestrict,1).lt(InteractionTask::getRestrictTimeStart,now).gt(InteractionTask::getRestrictTimeEnd,now));
            //查询所有在启动的任务
            List<InteractionTask> list=interactionTaskMapper.selectList(wrapper);
            if (!CollectionUtils.isEmpty(list)){
                for (InteractionTask taskDTO : list) {
                    //1限时任务，否则不限时任务
                    if (taskDTO.getIsTimeRestrict()==1){
                        //1一次性任务，2周期性任务，3周期+阶梯
                        if (taskDTO.getReadyCycle().equals(1)){
                            LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                            wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                            wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                            wrapperRecord.gt(UserTaskRecord::getCreateTime,taskDTO.getRestrictTimeStart());
                            wrapperRecord.lt(UserTaskRecord::getCreateTime,taskDTO.getRestrictTimeEnd());
                            List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                            if (!CollectionUtils.isEmpty(userTaskRecords)){
                                if (userTaskRecords.size()>=taskDTO.getReadyNum()){
                                    continue;
                                }else{
                                    UserTaskRecord userTaskRecord = new UserTaskRecord();
                                    userTaskRecord.setUserId(userId);
                                    userTaskRecord.setTaskId(taskDTO.getId());
                                    userTaskRecord.setCreateTime(now);
                                    userTaskRecord.setCreateAt(String.valueOf(userId));
                                    userTaskRecord.setUpdateTime(now);
                                    userTaskRecord.setUpdateAt(String.valueOf(userId));
                                    userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                    userTaskRecordMapper.insert(userTaskRecord);
                                    if (taskDTO.getReadyNum()==userTaskRecords.size()+1){
                                        //查询奖励
                                        List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                        for (TaskReward taskReward : taskRewardList){
                                            if (taskReward.getRewardType()==1){//送优惠券
                                                //发券
                                                String[] couponIds=taskReward.getCouponIds().split(",");
                                                for (String couponId : couponIds){
                                                    ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                    receiveCouponVo.setReceiveType(2);
                                                    receiveCouponVo.setCouponId(couponId);
                                                    mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                }
                                            }else{//送积分
                                                //加积分
                                                mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                            }
                                        }
                                    }
                                }
                            }else{
                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                userTaskRecord.setUserId(userId);
                                userTaskRecord.setTaskId(taskDTO.getId());
                                userTaskRecord.setCreateTime(now);
                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                userTaskRecord.setUpdateTime(now);
                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                userTaskRecordMapper.insert(userTaskRecord);
                                if (taskDTO.getReadyNum()==1){
                                    //查询奖励
                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                    for (TaskReward taskReward : taskRewardList){
                                        if (taskReward.getRewardType()==1){//送优惠券
                                            //发券
                                            String[] couponIds=taskReward.getCouponIds().split(",");
                                            for (String couponId : couponIds){
                                                ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                receiveCouponVo.setReceiveType(2);
                                                receiveCouponVo.setCouponId(couponId);
                                                mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                            }
                                        }else{//送积分
                                            //加积分
                                            mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                        }
                                    }
                                }
                            }
                        }else if (taskDTO.getReadyCycle().equals(2)){
                            if (taskDTO.getReayType().equals(readyDay)){
                                //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                                long day = (taskDTO.getRestrictTimeEnd().getTime() - taskDTO.getRestrictTimeStart().getTime()) / (1000 * 60 * 60 * 24);
                                //计算day除以readyDay取整，得到实际周期次数
                                int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                                Date startTime = taskDTO.getRestrictTimeStart();
                                for (int i = 0; i < actualCycle; i++) {
                                    //计算taskDTO.getRestrictTimeStart()加上readyDay天后的日期
                                    Date endTime= null;
                                    if (i==actualCycle-1){
                                        endTime=taskDTO.getRestrictTimeEnd();
                                    }else{
                                        endTime=DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                                    }
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getReadyNum()){
                                                break;
                                            }else{
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                if (taskDTO.getReadyNum()==userTaskRecords.size()+1){
                                                    //查询奖励
                                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                    for (TaskReward taskReward : taskRewardList){
                                                        if (taskReward.getRewardType()==1){//送优惠券
                                                            //发券
                                                            String[] couponIds=taskReward.getCouponIds().split(",");
                                                            for (String couponId : couponIds){
                                                                ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                                receiveCouponVo.setReceiveType(2);
                                                                receiveCouponVo.setCouponId(couponId);
                                                                mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                            }
                                                        }else{//送积分
                                                            //加积分
                                                            mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                        }
                                                    }
                                                }
                                            }
                                        }else{
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            if (taskDTO.getReadyNum()==1){
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                for (TaskReward taskReward : taskRewardList){
                                                    if (taskReward.getRewardType()==1){//送优惠券
                                                        //发券
                                                        String[] couponIds=taskReward.getCouponIds().split(",");
                                                        for (String couponId : couponIds){
                                                            ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                            receiveCouponVo.setReceiveType(2);
                                                            receiveCouponVo.setCouponId(couponId);
                                                            mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                        }
                                                    }else{//送积分
                                                        //加积分
                                                        mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                    }
                                                }
                                            }
                                        }
                                        break;
                                    }
                                    startTime=endTime;
                                }
                            }else{
                                String str=sdf.format(taskDTO.getCreateTime());
                                String firstYear = str.substring(0,4);
                                String firstMonth = str.substring(5,7);
                                String LastStr=sdf.format(taskDTO.getRestrictTimeEnd());
                                String lastYear = LastStr.substring(0,4);
                                String lastMonth = LastStr.substring(5,7);
                                String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                                Date firstDate = sdf.parse(firstDay);
                                Date startTime=null;
                                if (taskDTO.getRestrictTimeStart().after(firstDate)){
                                    startTime=taskDTO.getRestrictTimeStart();
                                }else{
                                    startTime=firstDate;
                                }
                                //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                                int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                                //计算monthNum除以readymonth取整，得到实际周期次数
                                int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                                for (int i = 0; i < actualCycle; i++) {
                                    //得到startTime日期的最后一天
                                    Date endTime= null;
                                    if (i==actualCycle-1){
                                        endTime=taskDTO.getRestrictTimeEnd();
                                    }else{
                                        //得到startTime日期+taskDTO.getReadyMonth()的最后一天
                                        endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                                        String strEndTime=sdf.format(endTime);
                                        String endTimeYear = strEndTime.substring(0,4);
                                        String endTimeMonth = strEndTime.substring(5,7);
                                        String endTimeDay = strEndTime.substring(8,10);
                                        //当月最后一天
                                        String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                                        endTime=sdf.parse(endTimeStr);
                                    }
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getReadyNum()){
                                                break;
                                            }else{
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                if (taskDTO.getReadyNum()==userTaskRecords.size()+1){
                                                    //查询奖励
                                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                    for (TaskReward taskReward : taskRewardList){
                                                        if (taskReward.getRewardType()==1){//送优惠券
                                                            //发券
                                                            String[] couponIds=taskReward.getCouponIds().split(",");
                                                            for (String couponId : couponIds){
                                                                ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                                receiveCouponVo.setReceiveType(2);
                                                                receiveCouponVo.setCouponId(couponId);
                                                                mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                            }
                                                        }else{//送积分
                                                            //加积分
                                                            mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                        }
                                                    }
                                                }
                                            }
                                        }else{
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            if (taskDTO.getReadyNum()==1){
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                for (TaskReward taskReward : taskRewardList){
                                                    if (taskReward.getRewardType()==1){//送优惠券
                                                        //发券
                                                        String[] couponIds=taskReward.getCouponIds().split(",");
                                                        for (String couponId : couponIds){
                                                            ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                            receiveCouponVo.setReceiveType(2);
                                                            receiveCouponVo.setCouponId(couponId);
                                                            mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                        }
                                                    }else{//送积分
                                                        //加积分
                                                        mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                    }
                                                }
                                            }
                                        }
                                        break;
                                    }
                                    //endTime+一个月
                                    endTime= DateUtils.getPlusMonthLastDay(endTime,1);
                                    String nextTime=sdf.format(endTime);
                                    String nextTimeYear = nextTime.substring(0,4);
                                    String nextTimeMonth = nextTime.substring(5,7);
                                    //得到下个月第一天日期
                                    String endTimeNext=nextTimeYear+"-"+nextTimeMonth+"-01 00:00:00";
                                    endTime=sdf.parse(endTimeNext);
                                    startTime=endTime;
                                }
                            }
                        }else{
                            if (taskDTO.getReayType().equals(readyDay)){
                                //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                                long day = (taskDTO.getRestrictTimeEnd().getTime() - taskDTO.getRestrictTimeStart().getTime()) / (1000 * 60 * 60 * 24);
                                //计算day除以readyDay取整，得到实际周期次数
                                int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                                Date startTime = taskDTO.getRestrictTimeStart();
                                for (int i = 0; i < actualCycle; i++) {
                                    //计算taskDTO.getRestrictTimeStart()加上getReadyDay天后的日期
                                    Date endTime= null;
                                    if (i==actualCycle-1){
                                        endTime=taskDTO.getRestrictTimeEnd();
                                    }else{
                                        endTime=DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                                    }
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getTotalReadyNum()){
                                                break;
                                            }else{
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                //获取taskRewardList中第userTaskRecords.size()个奖励
                                                TaskReward taskReward = taskRewardList.get(userTaskRecords.size());
                                                if (taskReward.getRewardType()==1){//送优惠券
                                                    //发券
                                                    String[] couponIds=taskReward.getCouponIds().split(",");
                                                    for (String couponId : couponIds){
                                                        ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                        receiveCouponVo.setReceiveType(2);
                                                        receiveCouponVo.setCouponId(couponId);
                                                        mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                    }
                                                }else{//送积分
                                                    //加积分
                                                    mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                }
                                            }
                                        }else{
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            //按sortNum正序查询奖励
                                            List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                            TaskReward taskReward = taskRewardList.get(0);
                                            if (taskReward.getRewardType()==1){//送优惠券
                                                //发券
                                                String[] couponIds=taskReward.getCouponIds().split(",");
                                                for (String couponId : couponIds){
                                                    ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                    receiveCouponVo.setReceiveType(2);
                                                    receiveCouponVo.setCouponId(couponId);
                                                    mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                }
                                            }else{//送积分
                                                //加积分
                                                mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                            }
                                        }
                                        break;
                                    }
                                    startTime=endTime;
                                }
                            }else{
                                String str=sdf.format(taskDTO.getCreateTime());
                                String firstYear = str.substring(0,4);
                                String firstMonth = str.substring(5,7);
                                String LastStr=sdf.format(taskDTO.getRestrictTimeEnd());
                                String lastYear = LastStr.substring(0,4);
                                String lastMonth = LastStr.substring(5,7);
                                String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                                Date firstDate = sdf.parse(firstDay);
                                Date startTime=null;
                                if (taskDTO.getRestrictTimeStart().after(firstDate)){
                                    startTime=taskDTO.getRestrictTimeStart();
                                }else{
                                    startTime=firstDate;
                                }
                                //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                                int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                                //计算monthNum除以readymonth取整，得到实际周期次数
                                int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                                for (int i = 0; i < actualCycle; i++) {
                                    //得到startTime日期的最后一天
                                    Date endTime= null;
                                    if (i==actualCycle-1){
                                        endTime=taskDTO.getRestrictTimeEnd();
                                    }else{
                                        //得到startTime日期+taskDTO.getReadyMonth()的最后一天
                                        endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                                        String strEndTime=sdf.format(endTime);
                                        String endTimeYear = strEndTime.substring(0,4);
                                        String endTimeMonth = strEndTime.substring(5,7);
                                        String endTimeDay = strEndTime.substring(8,10);
                                        //当月最后一天
                                        String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                                        endTime=sdf.parse(endTimeStr);
                                    }
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getTotalReadyNum()){
                                                break;
                                            }else{
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                //获取taskRewardList中第userTaskRecords.size()个奖励
                                                TaskReward taskReward = taskRewardList.get(userTaskRecords.size());
                                                if (taskReward.getRewardType()==1){//送优惠券
                                                    //发券
                                                    String[] couponIds=taskReward.getCouponIds().split(",");
                                                    for (String couponId : couponIds){
                                                        ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                        receiveCouponVo.setReceiveType(2);
                                                        receiveCouponVo.setCouponId(couponId);
                                                        mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                    }
                                                }else{//送积分
                                                    //加积分
                                                    mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                }
                                            }
                                        }else{
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            //按sortNum正序查询奖励
                                            List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                            TaskReward taskReward = taskRewardList.get(0);
                                            if (taskReward.getRewardType()==1){//送优惠券
                                                //发券
                                                String[] couponIds=taskReward.getCouponIds().split(",");
                                                for (String couponId : couponIds){
                                                    ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                    receiveCouponVo.setReceiveType(2);
                                                    receiveCouponVo.setCouponId(couponId);
                                                    mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                }
                                            }else{//送积分
                                                //加积分
                                                mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                            }
                                        }
                                        break;
                                    }
                                    //endTime+一个月
                                    endTime= DateUtils.getPlusMonthLastDay(endTime,1);
                                    String nextTime=sdf.format(endTime);
                                    String nextTimeYear = nextTime.substring(0,4);
                                    String nextTimeMonth = nextTime.substring(5,7);
                                    //得到下个月第一天日期
                                    String endTimeNext=nextTimeYear+"-"+nextTimeMonth+"-01 00:00:00";
                                    endTime=sdf.parse(endTimeNext);
                                    startTime=endTime;
                                }
                            }
                        }
                    }else{
                        //1一次性任务，2周期性任务
                        if (taskDTO.getReadyCycle().equals(1)){
                            LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                            wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                            wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                            List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                            if (!CollectionUtils.isEmpty(userTaskRecords)){
                                if (userTaskRecords.size()>=taskDTO.getReadyNum()){
                                    continue;
                                }else{
                                    UserTaskRecord userTaskRecord = new UserTaskRecord();
                                    userTaskRecord.setUserId(userId);
                                    userTaskRecord.setTaskId(taskDTO.getId());
                                    userTaskRecord.setCreateTime(now);
                                    userTaskRecord.setCreateAt(String.valueOf(userId));
                                    userTaskRecord.setUpdateTime(now);
                                    userTaskRecord.setUpdateAt(String.valueOf(userId));
                                    userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                    userTaskRecordMapper.insert(userTaskRecord);
                                    if (taskDTO.getReadyNum()==userTaskRecords.size()+1){
                                        //查询奖励
                                        List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                        for (TaskReward taskReward : taskRewardList){
                                            if (taskReward.getRewardType()==1){//送优惠券
                                                //发券
                                                String[] couponIds=taskReward.getCouponIds().split(",");
                                                for (String couponId : couponIds){
                                                    ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                    receiveCouponVo.setReceiveType(2);
                                                    receiveCouponVo.setCouponId(couponId);
                                                    mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                }
                                            }else{//送积分
                                                //加积分
                                                mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                            }
                                        }
                                    }
                                }
                            }else{
                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                userTaskRecord.setUserId(userId);
                                userTaskRecord.setTaskId(taskDTO.getId());
                                userTaskRecord.setCreateTime(now);
                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                userTaskRecord.setUpdateTime(now);
                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                userTaskRecordMapper.insert(userTaskRecord);
                                if (taskDTO.getReadyNum()==1){
                                    //查询奖励
                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                    for (TaskReward taskReward : taskRewardList){
                                        if (taskReward.getRewardType()==1){//送优惠券
                                            //发券
                                            String[] couponIds=taskReward.getCouponIds().split(",");
                                            for (String couponId : couponIds){
                                                ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                receiveCouponVo.setReceiveType(2);
                                                receiveCouponVo.setCouponId(couponId);
                                                mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                            }
                                        }else{//送积分
                                            //加积分
                                            mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                        }
                                    }
                                }
                            }
                        }else if (taskDTO.getReadyCycle().equals(2)){
                            if (taskDTO.getReayType().equals(readyDay)){
                                //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                                String str=sdf.format(taskDTO.getCreateTime());
                                String firstYear = str.substring(0,4);
                                String firstMonth = str.substring(5,7);
                                String firstDay = str.substring(8,10);
                                String startTimeStr=firstYear+"-"+firstMonth+"-"+firstDay+" 00:00:00";
                                Date startTime=sdf.parse(startTimeStr);
                                long day = (new Date().getTime() - startTime.getTime()) / (1000 * 60 * 60 * 24);
                                //计算day除以readyDay取整，得到实际周期次数
                                int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                                for (int i = 0; i < actualCycle; i++) {
                                    //计算taskDTO.getRestrictTimeStart()加上readyDay天后的日期
                                    Date endTime=DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getReadyNum()){
                                                break;
                                            }else{
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                if (taskDTO.getReadyNum()==userTaskRecords.size()+1){
                                                    //查询奖励
                                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                    for (TaskReward taskReward : taskRewardList){
                                                        if (taskReward.getRewardType()==1){//送优惠券
                                                            //发券
                                                            String[] couponIds=taskReward.getCouponIds().split(",");
                                                            for (String couponId : couponIds){
                                                                ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                                receiveCouponVo.setReceiveType(2);
                                                                receiveCouponVo.setCouponId(couponId);
                                                                mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                            }
                                                        }else{//送积分
                                                            //加积分
                                                            mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                        }
                                                    }
                                                }
                                            }
                                        }else{
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            if (taskDTO.getReadyNum()==1){
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                for (TaskReward taskReward : taskRewardList){
                                                    if (taskReward.getRewardType()==1){//送优惠券
                                                        //发券
                                                        String[] couponIds=taskReward.getCouponIds().split(",");
                                                        for (String couponId : couponIds){
                                                            ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                            receiveCouponVo.setReceiveType(2);
                                                            receiveCouponVo.setCouponId(couponId);
                                                            mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                        }
                                                    }else{//送积分
                                                        //加积分
                                                        mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                    }
                                                }
                                            }
                                        }
                                        break;
                                    }
                                    startTime=endTime;
                                }
                            }else{
                                String str=sdf.format(taskDTO.getCreateTime());
                                String firstYear = str.substring(0,4);
                                String firstMonth = str.substring(5,7);
                                String LastStr=sdf.format(now);
                                String lastYear = LastStr.substring(0,4);
                                String lastMonth = LastStr.substring(5,7);
                                String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                                Date firstDate = sdf.parse(firstDay);
                                Date startTime=firstDate;
                                //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                                int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                                //计算monthNum除以readymonth取整，得到实际周期次数
                                int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                                for (int i = 0; i < actualCycle; i++) {
                                    //得到startTime日期+taskDTO.getReadyMonth()的最后一天
                                    Date endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                                    String strEndTime=sdf.format(endTime);
                                    String endTimeYear = strEndTime.substring(0,4);
                                    String endTimeMonth = strEndTime.substring(5,7);
                                    String endTimeDay = strEndTime.substring(8,10);
                                    //当月最后一天
                                    String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                                    endTime=sdf.parse(endTimeStr);
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getReadyNum()){
                                                break;
                                            }else{
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                if (taskDTO.getReadyNum()==userTaskRecords.size()+1){
                                                    //查询奖励
                                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                    for (TaskReward taskReward : taskRewardList){
                                                        if (taskReward.getRewardType()==1){//送优惠券
                                                            //发券
                                                            String[] couponIds=taskReward.getCouponIds().split(",");
                                                            for (String couponId : couponIds){
                                                                ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                                receiveCouponVo.setReceiveType(2);
                                                                receiveCouponVo.setCouponId(couponId);
                                                                mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                            }
                                                        }else{//送积分
                                                            //加积分
                                                            mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                        }
                                                    }
                                                }
                                            }
                                        }else{
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            if (taskDTO.getReadyNum()==1){
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                for (TaskReward taskReward : taskRewardList){
                                                    if (taskReward.getRewardType()==1){//送优惠券
                                                        //发券
                                                        String[] couponIds=taskReward.getCouponIds().split(",");
                                                        for (String couponId : couponIds){
                                                            ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                            receiveCouponVo.setReceiveType(2);
                                                            receiveCouponVo.setCouponId(couponId);
                                                            mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                        }
                                                    }else{//送积分
                                                        //加积分
                                                        mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                    }
                                                }
                                            }
                                        }
                                        break;
                                    }
                                    //endTime+一个月
                                    endTime= DateUtils.getPlusMonthLastDay(endTime,1);
                                    String nextTime=sdf.format(endTime);
                                    String nextTimeYear = nextTime.substring(0,4);
                                    String nextTimeMonth = nextTime.substring(5,7);
                                    //得到下个月第一天日期
                                    String endTimeNext=nextTimeYear+"-"+nextTimeMonth+"-01 00:00:00";
                                    endTime=sdf.parse(endTimeNext);
                                    startTime=endTime;
                                }
                            }
                        }else{
                            if (taskDTO.getReayType().equals(readyDay)){
                                //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                                String str=sdf.format(taskDTO.getCreateTime());
                                String firstYear = str.substring(0,4);
                                String firstMonth = str.substring(5,7);
                                String firstDay = str.substring(8,10);
                                String startTimeStr=firstYear+"-"+firstMonth+"-"+firstDay+" 00:00:00";
                                Date startTime=sdf.parse(startTimeStr);
                                long day = (new Date().getTime() - startTime.getTime()) / (1000 * 60 * 60 * 24);
                                //计算day除以readyDay取整，得到实际周期次数
                                int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                                for (int i = 0; i < actualCycle; i++) {
                                    //计算taskDTO.getRestrictTimeStart()加上readyDay天后的日期
                                    Date endTime=DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getTotalReadyNum()){
                                                break;
                                            }else{
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                //获取taskRewardList中第userTaskRecords.size()个奖励
                                                TaskReward taskReward = taskRewardList.get(userTaskRecords.size());
                                                if (taskReward.getRewardType()==1){//送优惠券
                                                    //发券
                                                    String[] couponIds=taskReward.getCouponIds().split(",");
                                                    for (String couponId : couponIds){
                                                        ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                        receiveCouponVo.setReceiveType(2);
                                                        receiveCouponVo.setCouponId(couponId);
                                                        mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                    }
                                                }else{//送积分
                                                    //加积分
                                                    mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                }
                                            }
                                        }else{
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            //按sortNum正序查询奖励
                                            List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                            TaskReward taskReward = taskRewardList.get(0);
                                            if (taskReward.getRewardType()==1){//送优惠券
                                                //发券
                                                String[] couponIds=taskReward.getCouponIds().split(",");
                                                for (String couponId : couponIds){
                                                    ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                    receiveCouponVo.setReceiveType(2);
                                                    receiveCouponVo.setCouponId(couponId);
                                                    mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                }
                                            }else{//送积分
                                                //加积分
                                                mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                            }
                                        }
                                        break;
                                    }
                                    startTime=endTime;
                                }
                            }else{
                                String str=sdf.format(taskDTO.getCreateTime());
                                String firstYear = str.substring(0,4);
                                String firstMonth = str.substring(5,7);
                                String LastStr=sdf.format(now);
                                String lastYear = LastStr.substring(0,4);
                                String lastMonth = LastStr.substring(5,7);
                                String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                                Date firstDate = sdf.parse(firstDay);
                                Date startTime=firstDate;
                                //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                                int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                                //计算monthNum除以readymonth取整，得到实际周期次数
                                int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                                for (int i = 0; i < actualCycle; i++) {
                                    //得到startTime日期+taskDTO.getReadyMonth()的最后一天
                                    Date endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                                    String strEndTime=sdf.format(endTime);
                                    String endTimeYear = strEndTime.substring(0,4);
                                    String endTimeMonth = strEndTime.substring(5,7);
                                    String endTimeDay = strEndTime.substring(8,10);
                                    //当月最后一天
                                    String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                                    endTime=sdf.parse(endTimeStr);
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getTotalReadyNum()){
                                                break;
                                            }else{
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                //获取taskRewardList中第userTaskRecords.size()个奖励
                                                TaskReward taskReward = taskRewardList.get(userTaskRecords.size());
                                                if (taskReward.getRewardType()==1){//送优惠券
                                                    //发券
                                                    String[] couponIds=taskReward.getCouponIds().split(",");
                                                    for (String couponId : couponIds){
                                                        ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                        receiveCouponVo.setReceiveType(2);
                                                        receiveCouponVo.setCouponId(couponId);
                                                        mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                    }
                                                }else{//送积分
                                                    //加积分
                                                    mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                }
                                            }
                                        }else{
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            //按sortNum正序查询奖励
                                            List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                            TaskReward taskReward = taskRewardList.get(0);
                                            if (taskReward.getRewardType()==1){//送优惠券
                                                //发券
                                                String[] couponIds=taskReward.getCouponIds().split(",");
                                                for (String couponId : couponIds){
                                                    ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                    receiveCouponVo.setReceiveType(2);
                                                    receiveCouponVo.setCouponId(couponId);
                                                    mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                }
                                            }else{//送积分
                                                //加积分
                                                mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                            }
                                        }
                                        break;
                                    }
                                    //endTime+一个月
                                    endTime= DateUtils.getPlusMonthLastDay(endTime,1);
                                    String nextTime=sdf.format(endTime);
                                    String nextTimeYear = nextTime.substring(0,4);
                                    String nextTimeMonth = nextTime.substring(5,7);
                                    //得到下个月第一天日期
                                    String endTimeNext=nextTimeYear+"-"+nextTimeMonth+"-01 00:00:00";
                                    endTime=sdf.parse(endTimeNext);
                                    startTime=endTime;
                                }
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            log.info("打卡任务失败");
        }finally {
            redisService.unlock(taskCard);
        }
    }

    @Override
    public void taskSuccessRecordByOrder() throws ParseException {
        SimpleDateFormat sdfMonth=new SimpleDateFormat("yyyy-MM");
        Long userId=SecurityContext.getUser().getUid();
        String taskOrder = "task_order_" + userId;
        boolean lock = redisService.lock(taskOrder, 3);
        if (!lock) {
            return;
        }
        try{
            //查询用户会员信息
            Result<UserSimpleDTO> userInfoDTO = userInfoFeginClient.getUserSimpleInfo(userId);
            Date now=new Date();
            LambdaQueryWrapper<InteractionTask> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(InteractionTask::getStatus, 0);
            wrapper.eq(InteractionTask::getTaskDesc, 2);
            wrapper.and(wq->wq.eq(InteractionTask::getIsTimeRestrict,2)
                    .or().eq(InteractionTask::getIsTimeRestrict,1).lt(InteractionTask::getRestrictTimeStart,now).gt(InteractionTask::getRestrictTimeEnd,now));
            //查询所有在启动的任务
            List<InteractionTask> list=interactionTaskMapper.selectList(wrapper);
            if (!CollectionUtils.isEmpty(list)){
                for (InteractionTask taskDTO : list) {
                    //1限时任务，否则不限时任务
                    if (taskDTO.getIsTimeRestrict()==1){
                        //1一次性任务，2周期性任务
                        if (taskDTO.getReadyCycle().equals(1)){
                            LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                            wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                            wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                            wrapperRecord.gt(UserTaskRecord::getCreateTime,taskDTO.getRestrictTimeStart());
                            wrapperRecord.lt(UserTaskRecord::getCreateTime,taskDTO.getRestrictTimeEnd());
                            List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                            if (!CollectionUtils.isEmpty(userTaskRecords)){
                                if (userTaskRecords.size()>=taskDTO.getReadyNum()){
                                    continue;
                                }else{
                                    UserTaskRecord userTaskRecord = new UserTaskRecord();
                                    userTaskRecord.setUserId(userId);
                                    userTaskRecord.setTaskId(taskDTO.getId());
                                    userTaskRecord.setCreateTime(now);
                                    userTaskRecord.setCreateAt(String.valueOf(userId));
                                    userTaskRecord.setUpdateTime(now);
                                    userTaskRecord.setUpdateAt(String.valueOf(userId));
                                    userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                    userTaskRecordMapper.insert(userTaskRecord);
                                    if (taskDTO.getReadyNum()==userTaskRecords.size()+1){
                                        //查询奖励
                                        List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                        for (TaskReward taskReward : taskRewardList){
                                            if (taskReward.getRewardType()==1){//送优惠券
                                                //发券
                                                String[] couponIds=taskReward.getCouponIds().split(",");
                                                for (String couponId : couponIds){
                                                    ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                    receiveCouponVo.setReceiveType(2);
                                                    receiveCouponVo.setCouponId(couponId);
                                                    mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                }
                                            }else{//送积分
                                                //加积分
                                                mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                            }
                                        }
                                    }
                                }
                            }else{
                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                userTaskRecord.setUserId(userId);
                                userTaskRecord.setTaskId(taskDTO.getId());
                                userTaskRecord.setCreateTime(now);
                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                userTaskRecord.setUpdateTime(now);
                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                userTaskRecordMapper.insert(userTaskRecord);
                                if (taskDTO.getReadyNum()==1){
                                    //查询奖励
                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                    for (TaskReward taskReward : taskRewardList){
                                        if (taskReward.getRewardType()==1){//送优惠券
                                            //发券
                                            String[] couponIds=taskReward.getCouponIds().split(",");
                                            for (String couponId : couponIds){
                                                ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                receiveCouponVo.setReceiveType(2);
                                                receiveCouponVo.setCouponId(couponId);
                                                mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                            }
                                        }else{//送积分
                                            //加积分
                                            mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                        }
                                    }
                                }
                            }
                        }else if (taskDTO.getReadyCycle().equals(2)){
                            if (taskDTO.getReayType().equals(readyDay)){
                                //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                                long day = (taskDTO.getRestrictTimeEnd().getTime() - taskDTO.getRestrictTimeStart().getTime()) / (1000 * 60 * 60 * 24);
                                //计算day除以readyDay取整，得到实际周期次数
                                int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                                Date startTime = taskDTO.getRestrictTimeStart();
                                for (int i = 0; i < actualCycle; i++) {
                                    //计算taskDTO.getRestrictTimeStart()加上readyDay天后的日期
                                    Date endTime=DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getReadyNum()){
                                                break;
                                            }else{
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                if (taskDTO.getReadyNum()==userTaskRecords.size()+1){
                                                    //查询奖励
                                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                    for (TaskReward taskReward : taskRewardList){
                                                        if (taskReward.getRewardType()==1){//送优惠券
                                                            //发券
                                                            String[] couponIds=taskReward.getCouponIds().split(",");
                                                            for (String couponId : couponIds){
                                                                ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                                receiveCouponVo.setReceiveType(2);
                                                                receiveCouponVo.setCouponId(couponId);
                                                                mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                            }
                                                        }else{//送积分
                                                            //加积分
                                                            mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                        }
                                                    }
                                                }
                                            }
                                        }else{
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            if (taskDTO.getReadyNum()==1){
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                for (TaskReward taskReward : taskRewardList){
                                                    if (taskReward.getRewardType()==1){//送优惠券
                                                        //发券
                                                        String[] couponIds=taskReward.getCouponIds().split(",");
                                                        for (String couponId : couponIds){
                                                            ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                            receiveCouponVo.setReceiveType(2);
                                                            receiveCouponVo.setCouponId(couponId);
                                                            mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                        }
                                                    }else{//送积分
                                                        //加积分
                                                        mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                    }
                                                }
                                            }
                                        }
                                        break;
                                    }
                                    startTime=endTime;
                                }
                            }else{
                                String str=sdf.format(taskDTO.getCreateTime());
                                String firstYear = str.substring(0,4);
                                String firstMonth = str.substring(5,7);
                                String LastStr=sdf.format(taskDTO.getRestrictTimeEnd());
                                String lastYear = LastStr.substring(0,4);
                                String lastMonth = LastStr.substring(5,7);
                                String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                                Date firstDate = sdf.parse(firstDay);
                                Date startTime=null;
                                if (taskDTO.getRestrictTimeStart().after(firstDate)){
                                    startTime=taskDTO.getRestrictTimeStart();
                                }else{
                                    startTime=firstDate;
                                }
                                //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                                int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                                //计算monthNum除以readymonth取整，得到实际周期次数
                                int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                                for (int i = 0; i < actualCycle; i++) {
                                    //得到startTime日期的最后一天
                                    Date endTime= null;
                                    if (i==actualCycle-1){
                                        endTime=taskDTO.getRestrictTimeEnd();
                                    }else{
                                        //得到startTime日期+taskDTO.getReadyMonth()的最后一天
                                        endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                                        String strEndTime=sdf.format(endTime);
                                        String endTimeYear = strEndTime.substring(0,4);
                                        String endTimeMonth = strEndTime.substring(5,7);
                                        String endTimeDay = strEndTime.substring(8,10);
                                        //当月最后一天
                                        String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                                        endTime=sdf.parse(endTimeStr);
                                    }
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getReadyNum()){
                                                break;
                                            }else{
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                if (taskDTO.getReadyNum()==userTaskRecords.size()+1){
                                                    //查询奖励
                                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                    for (TaskReward taskReward : taskRewardList){
                                                        if (taskReward.getRewardType()==1){//送优惠券
                                                            //发券
                                                            String[] couponIds=taskReward.getCouponIds().split(",");
                                                            for (String couponId : couponIds){
                                                                ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                                receiveCouponVo.setReceiveType(2);
                                                                receiveCouponVo.setCouponId(couponId);
                                                                mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                            }
                                                        }else{//送积分
                                                            //加积分
                                                            mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                        }
                                                    }
                                                }
                                            }
                                        }else{
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            if (taskDTO.getReadyNum()==1){
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                for (TaskReward taskReward : taskRewardList){
                                                    if (taskReward.getRewardType()==1){//送优惠券
                                                        //发券
                                                        String[] couponIds=taskReward.getCouponIds().split(",");
                                                        for (String couponId : couponIds){
                                                            ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                            receiveCouponVo.setReceiveType(2);
                                                            receiveCouponVo.setCouponId(couponId);
                                                            mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                        }
                                                    }else{//送积分
                                                        //加积分
                                                        mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                    }
                                                }
                                            }
                                        }
                                        break;
                                    }
                                    //endTime+一个月
                                    endTime= DateUtils.getPlusMonthLastDay(endTime,1);
                                    String nextTime=sdf.format(endTime);
                                    String nextTimeYear = nextTime.substring(0,4);
                                    String nextTimeMonth = nextTime.substring(5,7);
                                    //得到下个月第一天日期
                                    String endTimeNext=nextTimeYear+"-"+nextTimeMonth+"-01 00:00:00";
                                    endTime=sdf.parse(endTimeNext);
                                    startTime=endTime;
                                }
                            }
                        }else{
                            if (taskDTO.getReayType().equals(readyDay)){
                                //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                                long day = (taskDTO.getRestrictTimeEnd().getTime() - taskDTO.getRestrictTimeStart().getTime()) / (1000 * 60 * 60 * 24);
                                //计算day除以readyDay取整，得到实际周期次数
                                int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                                Date startTime = taskDTO.getRestrictTimeStart();
                                for (int i = 0; i < actualCycle; i++) {
                                    //得到startTime日期的最后一天
                                    Date endTime= null;
                                    if (i==actualCycle-1){
                                        endTime=taskDTO.getRestrictTimeEnd();
                                    }else{
                                        endTime=DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                                    }
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getTotalReadyNum()){
                                                break;
                                            }else{
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                //获取taskRewardList中第userTaskRecords.size()个奖励
                                                TaskReward taskReward = taskRewardList.get(userTaskRecords.size());
                                                if (taskReward.getRewardType()==1){//送优惠券
                                                    //发券
                                                    String[] couponIds=taskReward.getCouponIds().split(",");
                                                    for (String couponId : couponIds){
                                                        ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                        receiveCouponVo.setReceiveType(2);
                                                        receiveCouponVo.setCouponId(couponId);
                                                        mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                    }
                                                }else{//送积分
                                                    //加积分
                                                    mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                }
                                            }
                                        }else{
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            //按sortNum正序查询奖励
                                            List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                            TaskReward taskReward = taskRewardList.get(0);
                                            if (taskReward.getRewardType()==1){//送优惠券
                                                //发券
                                                String[] couponIds=taskReward.getCouponIds().split(",");
                                                for (String couponId : couponIds){
                                                    ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                    receiveCouponVo.setReceiveType(2);
                                                    receiveCouponVo.setCouponId(couponId);
                                                    mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                }
                                            }else{//送积分
                                                //加积分
                                                mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                            }
                                        }
                                        break;
                                    }
                                    startTime=endTime;
                                }
                            }else{
                                String str=sdf.format(taskDTO.getCreateTime());
                                String firstYear = str.substring(0,4);
                                String firstMonth = str.substring(5,7);
                                String LastStr=sdf.format(taskDTO.getRestrictTimeEnd());
                                String lastYear = LastStr.substring(0,4);
                                String lastMonth = LastStr.substring(5,7);
                                String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                                Date firstDate = sdf.parse(firstDay);
                                Date startTime=null;
                                if (taskDTO.getRestrictTimeStart().after(firstDate)){
                                    startTime=taskDTO.getRestrictTimeStart();
                                }else{
                                    startTime=firstDate;
                                }
                                //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                                int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                                //计算monthNum除以readymonth取整，得到实际周期次数
                                int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                                for (int i = 0; i < actualCycle; i++) {
                                    //得到startTime日期的最后一天
                                    Date endTime= null;
                                    if (i==actualCycle-1){
                                        endTime=taskDTO.getRestrictTimeEnd();
                                    }else{
                                        //得到startTime日期+taskDTO.getReadyMonth()的最后一天
                                        endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                                        String strEndTime=sdf.format(endTime);
                                        String endTimeYear = strEndTime.substring(0,4);
                                        String endTimeMonth = strEndTime.substring(5,7);
                                        String endTimeDay = strEndTime.substring(8,10);
                                        //当月最后一天
                                        String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                                        endTime=sdf.parse(endTimeStr);
                                    }
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getTotalReadyNum()){
                                                break;
                                            }else{
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                //获取taskRewardList中第userTaskRecords.size()个奖励
                                                TaskReward taskReward = taskRewardList.get(userTaskRecords.size());
                                                if (taskReward.getRewardType()==1){//送优惠券
                                                    //发券
                                                    String[] couponIds=taskReward.getCouponIds().split(",");
                                                    for (String couponId : couponIds){
                                                        ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                        receiveCouponVo.setReceiveType(2);
                                                        receiveCouponVo.setCouponId(couponId);
                                                        mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                    }
                                                }else{//送积分
                                                    //加积分
                                                    mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                }
                                            }
                                        }else{
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            //按sortNum正序查询奖励
                                            List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                            TaskReward taskReward = taskRewardList.get(0);
                                            if (taskReward.getRewardType()==1){//送优惠券
                                                //发券
                                                String[] couponIds=taskReward.getCouponIds().split(",");
                                                for (String couponId : couponIds){
                                                    ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                    receiveCouponVo.setReceiveType(2);
                                                    receiveCouponVo.setCouponId(couponId);
                                                    mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                }
                                            }else{//送积分
                                                //加积分
                                                mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                            }
                                        }
                                        break;
                                    }
                                    //endTime+一个月
                                    endTime= DateUtils.getPlusMonthLastDay(endTime,1);
                                    String nextTime=sdf.format(endTime);
                                    String nextTimeYear = nextTime.substring(0,4);
                                    String nextTimeMonth = nextTime.substring(5,7);
                                    //得到下个月第一天日期
                                    String endTimeNext=nextTimeYear+"-"+nextTimeMonth+"-01 00:00:00";
                                    endTime=sdf.parse(endTimeNext);
                                    startTime=endTime;
                                }
                            }
                        }
                    }else{
                        //1一次性任务，2周期性任务
                        if (taskDTO.getReadyCycle().equals(1)){
                            LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                            wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                            wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                            List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                            if (!CollectionUtils.isEmpty(userTaskRecords)){
                                if (userTaskRecords.size()>=taskDTO.getReadyNum()){
                                    continue;
                                }else{
                                    UserTaskRecord userTaskRecord = new UserTaskRecord();
                                    userTaskRecord.setUserId(userId);
                                    userTaskRecord.setTaskId(taskDTO.getId());
                                    userTaskRecord.setCreateTime(now);
                                    userTaskRecord.setCreateAt(String.valueOf(userId));
                                    userTaskRecord.setUpdateTime(now);
                                    userTaskRecord.setUpdateAt(String.valueOf(userId));
                                    userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                    userTaskRecordMapper.insert(userTaskRecord);
                                    if (taskDTO.getReadyNum()==userTaskRecords.size()+1){
                                        //查询奖励
                                        List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                        for (TaskReward taskReward : taskRewardList){
                                            if (taskReward.getRewardType()==1){//送优惠券
                                                //发券
                                                String[] couponIds=taskReward.getCouponIds().split(",");
                                                for (String couponId : couponIds){
                                                    ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                    receiveCouponVo.setReceiveType(2);
                                                    receiveCouponVo.setCouponId(couponId);
                                                    mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                }
                                            }else{//送积分
                                                //加积分
                                                mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                            }
                                        }
                                    }
                                }
                            }else{
                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                userTaskRecord.setUserId(userId);
                                userTaskRecord.setTaskId(taskDTO.getId());
                                userTaskRecord.setCreateTime(now);
                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                userTaskRecord.setUpdateTime(now);
                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                userTaskRecordMapper.insert(userTaskRecord);
                                if (taskDTO.getReadyNum()==1){
                                    //查询奖励
                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                    for (TaskReward taskReward : taskRewardList){
                                        if (taskReward.getRewardType()==1){//送优惠券
                                            //发券
                                            String[] couponIds=taskReward.getCouponIds().split(",");
                                            for (String couponId : couponIds){
                                                ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                receiveCouponVo.setReceiveType(2);
                                                receiveCouponVo.setCouponId(couponId);
                                                mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                            }
                                        }else{//送积分
                                            //加积分
                                            mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                        }
                                    }
                                }
                            }
                        }else if (taskDTO.getReadyCycle().equals(2)){
                            if (taskDTO.getReayType().equals(readyDay)){
                                //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                                String str=sdf.format(taskDTO.getCreateTime());
                                String firstYear = str.substring(0,4);
                                String firstMonth = str.substring(5,7);
                                String firstDay = str.substring(8,10);
                                String startTimeStr=firstYear+"-"+firstMonth+"-"+firstDay+" 00:00:00";
                                Date startTime=sdf.parse(startTimeStr);
                                long day = (new Date().getTime() - startTime.getTime()) / (1000 * 60 * 60 * 24);
                                //计算day除以readyDay取整，得到实际周期次数
                                int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                                for (int i = 0; i < actualCycle; i++) {
                                    //计算taskDTO.getRestrictTimeStart()加上readyDay天后的日期
                                    Date endTime=DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getReadyNum()){
                                                break;
                                            }else{
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                if (taskDTO.getReadyNum()==userTaskRecords.size()+1){
                                                    //查询奖励
                                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                    for (TaskReward taskReward : taskRewardList){
                                                        if (taskReward.getRewardType()==1){//送优惠券
                                                            //发券
                                                            String[] couponIds=taskReward.getCouponIds().split(",");
                                                            for (String couponId : couponIds){
                                                                ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                                receiveCouponVo.setReceiveType(2);
                                                                receiveCouponVo.setCouponId(couponId);
                                                                mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                            }
                                                        }else{//送积分
                                                            //加积分
                                                            mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                        }
                                                    }
                                                }
                                            }
                                        }else{
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            if (taskDTO.getReadyNum()==1){
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                for (TaskReward taskReward : taskRewardList){
                                                    if (taskReward.getRewardType()==1){//送优惠券
                                                        //发券
                                                        String[] couponIds=taskReward.getCouponIds().split(",");
                                                        for (String couponId : couponIds){
                                                            ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                            receiveCouponVo.setReceiveType(2);
                                                            receiveCouponVo.setCouponId(couponId);
                                                            mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                        }
                                                    }else{//送积分
                                                        //加积分
                                                        mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                    }
                                                }
                                            }
                                        }
                                        break;
                                    }
                                    startTime=endTime;
                                }
                            }else{
                                String str=sdf.format(taskDTO.getCreateTime());
                                String firstYear = str.substring(0,4);
                                String firstMonth = str.substring(5,7);
                                String LastStr=sdf.format(now);
                                String lastYear = LastStr.substring(0,4);
                                String lastMonth = LastStr.substring(5,7);
                                String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                                Date firstDate = sdf.parse(firstDay);
                                Date startTime=firstDate;
                                //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                                int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                                //计算monthNum除以readymonth取整，得到实际周期次数
                                int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                                for (int i = 0; i < actualCycle; i++) {
                                    //得到startTime日期+taskDTO.getReadyMonth()的最后一天
                                    Date endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                                    String strEndTime=sdf.format(endTime);
                                    String endTimeYear = strEndTime.substring(0,4);
                                    String endTimeMonth = strEndTime.substring(5,7);
                                    String endTimeDay = strEndTime.substring(8,10);
                                    //当月最后一天
                                    String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                                    endTime=sdf.parse(endTimeStr);
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getReadyNum()){
                                                break;
                                            }else{
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                if (taskDTO.getReadyNum()==userTaskRecords.size()+1){
                                                    //查询奖励
                                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                    for (TaskReward taskReward : taskRewardList){
                                                        if (taskReward.getRewardType()==1){//送优惠券
                                                            //发券
                                                            String[] couponIds=taskReward.getCouponIds().split(",");
                                                            for (String couponId : couponIds){
                                                                ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                                receiveCouponVo.setReceiveType(2);
                                                                receiveCouponVo.setCouponId(couponId);
                                                                mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                            }
                                                        }else{//送积分
                                                            //加积分
                                                            mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                        }
                                                    }
                                                }
                                            }
                                        }else{
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            if (taskDTO.getReadyNum()==1){
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                for (TaskReward taskReward : taskRewardList){
                                                    if (taskReward.getRewardType()==1){//送优惠券
                                                        //发券
                                                        String[] couponIds=taskReward.getCouponIds().split(",");
                                                        for (String couponId : couponIds){
                                                            ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                            receiveCouponVo.setReceiveType(2);
                                                            receiveCouponVo.setCouponId(couponId);
                                                            mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                        }
                                                    }else{//送积分
                                                        //加积分
                                                        mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                    }
                                                }
                                            }
                                        }
                                        break;
                                    }
                                    //endTime+一个月
                                    endTime= DateUtils.getPlusMonthLastDay(endTime,1);
                                    String nextTime=sdf.format(endTime);
                                    String nextTimeYear = nextTime.substring(0,4);
                                    String nextTimeMonth = nextTime.substring(5,7);
                                    //得到下个月第一天日期
                                    String endTimeNext=nextTimeYear+"-"+nextTimeMonth+"-01 00:00:00";
                                    endTime=sdf.parse(endTimeNext);
                                    startTime=endTime;
                                }
                            }
                        }else{
                            if (taskDTO.getReayType().equals(readyDay)){
                                //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                                String str=sdf.format(taskDTO.getCreateTime());
                                String firstYear = str.substring(0,4);
                                String firstMonth = str.substring(5,7);
                                String firstDay = str.substring(8,10);
                                String startTimeStr=firstYear+"-"+firstMonth+"-"+firstDay+" 00:00:00";
                                Date startTime=sdf.parse(startTimeStr);
                                long day = (new Date().getTime() - startTime.getTime()) / (1000 * 60 * 60 * 24);
                                //计算day除以readyDay取整，得到实际周期次数
                                int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                                for (int i = 0; i < actualCycle; i++) {
                                    //得到startTime日期+taskDTO.getReadyMonth()的最后一天
                                    Date endTime=DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getTotalReadyNum()){
                                                break;
                                            }else{
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                //获取taskRewardList中第userTaskRecords.size()个奖励
                                                TaskReward taskReward = taskRewardList.get(userTaskRecords.size());
                                                if (taskReward.getRewardType()==1){//送优惠券
                                                    //发券
                                                    String[] couponIds=taskReward.getCouponIds().split(",");
                                                    for (String couponId : couponIds){
                                                        ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                        receiveCouponVo.setReceiveType(2);
                                                        receiveCouponVo.setCouponId(couponId);
                                                        mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                    }
                                                }else{//送积分
                                                    //加积分
                                                    mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                }
                                            }
                                        }else{
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            //按sortNum正序查询奖励
                                            List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                            TaskReward taskReward = taskRewardList.get(0);
                                            if (taskReward.getRewardType()==1){//送优惠券
                                                //发券
                                                String[] couponIds=taskReward.getCouponIds().split(",");
                                                for (String couponId : couponIds){
                                                    ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                    receiveCouponVo.setReceiveType(2);
                                                    receiveCouponVo.setCouponId(couponId);
                                                    mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                }
                                            }else{//送积分
                                                //加积分
                                                mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                            }
                                        }
                                        break;
                                    }
                                    startTime=endTime;
                                }
                            }else{
                                String str=sdf.format(taskDTO.getCreateTime());
                                String firstYear = str.substring(0,4);
                                String firstMonth = str.substring(5,7);
                                String LastStr=sdf.format(now);
                                String lastYear = LastStr.substring(0,4);
                                String lastMonth = LastStr.substring(5,7);
                                String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                                Date firstDate = sdf.parse(firstDay);
                                Date startTime=firstDate;
                                //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                                int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                                //计算monthNum除以readymonth取整，得到实际周期次数
                                int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                                for (int i = 0; i < actualCycle; i++) {
                                    //得到startTime日期+taskDTO.getReadyMonth()的最后一天
                                    Date endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                                    String strEndTime=sdf.format(endTime);
                                    String endTimeYear = strEndTime.substring(0,4);
                                    String endTimeMonth = strEndTime.substring(5,7);
                                    String endTimeDay = strEndTime.substring(8,10);
                                    //当月最后一天
                                    String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                                    endTime=sdf.parse(endTimeStr);
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getTotalReadyNum()){
                                                return;
                                            }else{
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                //获取taskRewardList中第userTaskRecords.size()个奖励
                                                TaskReward taskReward = taskRewardList.get(userTaskRecords.size());
                                                if (taskReward.getRewardType()==1){//送优惠券
                                                    //发券
                                                    String[] couponIds=taskReward.getCouponIds().split(",");
                                                    for (String couponId : couponIds){
                                                        ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                        receiveCouponVo.setReceiveType(2);
                                                        receiveCouponVo.setCouponId(couponId);
                                                        mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                    }
                                                }else{//送积分
                                                    //加积分
                                                    mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                }
                                            }
                                        }else{
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            //按sortNum正序查询奖励
                                            List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                            TaskReward taskReward = taskRewardList.get(0);
                                            if (taskReward.getRewardType()==1){//送优惠券
                                                //发券
                                                String[] couponIds=taskReward.getCouponIds().split(",");
                                                for (String couponId : couponIds){
                                                    ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                    receiveCouponVo.setReceiveType(2);
                                                    receiveCouponVo.setCouponId(couponId);
                                                    mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                }
                                            }else{//送积分
                                                //加积分
                                                mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                            }
                                        }
                                        break;
                                    }
                                    //endTime+一个月
                                    endTime= DateUtils.getPlusMonthLastDay(endTime,1);
                                    String nextTime=sdf.format(endTime);
                                    String nextTimeYear = nextTime.substring(0,4);
                                    String nextTimeMonth = nextTime.substring(5,7);
                                    //得到下个月第一天日期
                                    String endTimeNext=nextTimeYear+"-"+nextTimeMonth+"-01 00:00:00";
                                    endTime=sdf.parse(endTimeNext);
                                    startTime=endTime;
                                }
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            log.info("兑礼任务失败");
        }finally {
            redisService.unlock(taskOrder);
        }
    }

    @Override
    public void taskSuccessRecordByProduct() throws ParseException {
        SimpleDateFormat sdfMonth=new SimpleDateFormat("yyyy-MM-dd");
        Date now=new Date();
        LambdaQueryWrapper<InteractionTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InteractionTask::getStatus, 0);
        wrapper.eq(InteractionTask::getTaskDesc, 3);
        wrapper.and(wq->wq.eq(InteractionTask::getIsTimeRestrict,2)
                .or().eq(InteractionTask::getIsTimeRestrict,1).lt(InteractionTask::getRestrictTimeStart,now).gt(InteractionTask::getRestrictTimeEnd,now));
        //查询所有在启动的任务
        List<InteractionTask> list=interactionTaskMapper.selectList(wrapper);
        Long userId=SecurityContext.getUser().getUid();
        String taskProduct = "task_product_" + userId;
        boolean lock = redisService.lock(taskProduct, 3);
        if (!lock) {
            return;
        }
        try{
            if (!CollectionUtils.isEmpty(list)){
                //查询用户会员信息
                Result<UserSimpleDTO> userInfoDTO = userInfoFeginClient.getDbUserSimpleInfo(userId);
                if (null == userInfoDTO || null == userInfoDTO.getData() || StringUtils.isEmpty(userInfoDTO.getData().getCardNo())){
                    return;
                }
                for (InteractionTask taskDTO : list) {
                    //1限时任务，否则不限时任务
                    if (taskDTO.getIsTimeRestrict()==1){
                        //1一次性任务，2周期性任务,3周期+阶梯
                        if (taskDTO.getReadyCycle().equals(1)){
                            Integer orderNum=orderByProduct(userInfoDTO.getData().getCardNo(),
                                    sdf.format(taskDTO.getRestrictTimeStart()),
                                    sdf.format(taskDTO.getRestrictTimeEnd()),
                                    taskDTO.getStoreProductCodes(),
                                    taskDTO.getDepartProductCodes(),
                                    taskDTO.getLineProductCodes(),
                                    taskDTO.getClassProductCodes(),
                                    taskDTO.getJanProductCodes());
                            if (orderNum<=0){
                                continue;
                            }
                            LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                            wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                            wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                            wrapperRecord.gt(UserTaskRecord::getCreateTime,taskDTO.getRestrictTimeStart());
                            wrapperRecord.lt(UserTaskRecord::getCreateTime,taskDTO.getRestrictTimeEnd());
                            List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                            if (!CollectionUtils.isEmpty(userTaskRecords)){
                                if (userTaskRecords.size()>=taskDTO.getReadyNum()){
                                    continue;
                                }else{
                                    if (orderNum>=taskDTO.getReadyNum()){
                                        for (int i = 0; i < orderNum; i++) {
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            List<UserTaskRecord> userTaskRecordsTwo =userTaskRecordMapper.selectList(wrapperRecord);
                                            if (userTaskRecordsTwo.size()>=taskDTO.getReadyNum()){
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                for (TaskReward taskReward : taskRewardList){
                                                    if (taskReward.getRewardType()==1){//送优惠券
                                                        //发券
                                                        String[] couponIds=taskReward.getCouponIds().split(",");
                                                        for (String couponId : couponIds){
                                                            ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                            receiveCouponVo.setReceiveType(2);
                                                            receiveCouponVo.setCouponId(couponId);
                                                            mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                        }
                                                    }else{//送积分
                                                        //加积分
                                                        mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                    }
                                                }
                                                break;
                                            }
                                        }
                                    }else{
                                        //计算出来已有订单数量减去已完成记录，剩余订单数量为当前继续完成的订单数量
                                        Integer num=orderNum-userTaskRecords.size();
                                        for (int i = 0; i < num; i++) {
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                        }
                                    }
                                }
                            }else{
                                if (orderNum>=taskDTO.getReadyNum()){
                                    for (int i = 0; i < orderNum; i++) {
                                        UserTaskRecord userTaskRecord = new UserTaskRecord();
                                        userTaskRecord.setUserId(userId);
                                        userTaskRecord.setTaskId(taskDTO.getId());
                                        userTaskRecord.setCreateTime(now);
                                        userTaskRecord.setCreateAt(String.valueOf(userId));
                                        userTaskRecord.setUpdateTime(now);
                                        userTaskRecord.setUpdateAt(String.valueOf(userId));
                                        userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                        userTaskRecordMapper.insert(userTaskRecord);
                                        List<UserTaskRecord> userTaskRecordsTwo =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (userTaskRecordsTwo.size()>=taskDTO.getReadyNum()){
                                            //查询奖励
                                            List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                            for (TaskReward taskReward : taskRewardList){
                                                if (taskReward.getRewardType()==1){//送优惠券
                                                    //发券
                                                    String[] couponIds=taskReward.getCouponIds().split(",");
                                                    for (String couponId : couponIds){
                                                        ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                        receiveCouponVo.setReceiveType(2);
                                                        receiveCouponVo.setCouponId(couponId);
                                                        mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                    }
                                                }else{//送积分
                                                    //加积分
                                                    mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                }
                                            }
                                            break;
                                        }
                                    }
                                }else{
                                    //剩余订单数量为当前继续完成的订单数量
                                    for (int i = 0; i < orderNum; i++) {
                                        UserTaskRecord userTaskRecord = new UserTaskRecord();
                                        userTaskRecord.setUserId(userId);
                                        userTaskRecord.setTaskId(taskDTO.getId());
                                        userTaskRecord.setCreateTime(now);
                                        userTaskRecord.setCreateAt(String.valueOf(userId));
                                        userTaskRecord.setUpdateTime(now);
                                        userTaskRecord.setUpdateAt(String.valueOf(userId));
                                        userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                        userTaskRecordMapper.insert(userTaskRecord);
                                    }
                                }
                            }
                        }else if (taskDTO.getReadyCycle().equals(2)){
                            if (taskDTO.getReayType().equals(readyDay)){
                                //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                                long day = (taskDTO.getRestrictTimeEnd().getTime() - taskDTO.getRestrictTimeStart().getTime()) / (1000 * 60 * 60 * 24);
                                //计算day除以readyDay取整，得到实际周期次数
                                int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                                Date startTime = taskDTO.getRestrictTimeStart();
                                for (int i = 0; i < actualCycle; i++) {
                                    //计算taskDTO.getRestrictTimeStart()加上readyDay天后的日期
                                    Date endTime=DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                                    Integer orderNumTwo=orderByProduct(userInfoDTO.getData().getCardNo(),
                                            sdf.format(startTime),
                                            sdf.format(endTime),
                                            taskDTO.getStoreProductCodes(),
                                            taskDTO.getDepartProductCodes(),
                                            taskDTO.getLineProductCodes(),
                                            taskDTO.getClassProductCodes(),
                                            taskDTO.getJanProductCodes());
                                    if (orderNumTwo<=0){
                                        startTime=endTime;
                                        continue;
                                    }
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getReadyNum()){
                                                break;
                                            }else{
                                                if (orderNumTwo>=taskDTO.getReadyNum()){
                                                    for (int j = 0; j < orderNumTwo; j++) {
                                                        UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                        userTaskRecord.setUserId(userId);
                                                        userTaskRecord.setTaskId(taskDTO.getId());
                                                        userTaskRecord.setCreateTime(now);
                                                        userTaskRecord.setCreateAt(String.valueOf(userId));
                                                        userTaskRecord.setUpdateTime(now);
                                                        userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                        userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                        userTaskRecordMapper.insert(userTaskRecord);
                                                        List<UserTaskRecord> userTaskRecordsTwo =userTaskRecordMapper.selectList(wrapperRecord);
                                                        if (userTaskRecordsTwo.size()>=taskDTO.getReadyNum()){
                                                            //查询奖励
                                                            List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                            for (TaskReward taskReward : taskRewardList){
                                                                if (taskReward.getRewardType()==1){//送优惠券
                                                                    //发券
                                                                    String[] couponIds=taskReward.getCouponIds().split(",");
                                                                    for (String couponId : couponIds){
                                                                        ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                                        receiveCouponVo.setReceiveType(2);
                                                                        receiveCouponVo.setCouponId(couponId);
                                                                        mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                                    }
                                                                }else{//送积分
                                                                    //加积分
                                                                    mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                                }
                                                            }
                                                            break;
                                                        }
                                                    }
                                                }else{
                                                    //计算出来已有订单数量减去已完成记录，剩余订单数量为当前继续完成的订单数量
                                                    Integer num=orderNumTwo-userTaskRecords.size();
                                                    for (int j = 0; j < num; j++) {
                                                        UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                        userTaskRecord.setUserId(userId);
                                                        userTaskRecord.setTaskId(taskDTO.getId());
                                                        userTaskRecord.setCreateTime(now);
                                                        userTaskRecord.setCreateAt(String.valueOf(userId));
                                                        userTaskRecord.setUpdateTime(now);
                                                        userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                        userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                        userTaskRecordMapper.insert(userTaskRecord);
                                                    }
                                                }
                                            }
                                        }else{
                                            if (orderNumTwo>=taskDTO.getReadyNum()){
                                                for (int j = 0; j < orderNumTwo; j++) {
                                                    UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                    userTaskRecord.setUserId(userId);
                                                    userTaskRecord.setTaskId(taskDTO.getId());
                                                    userTaskRecord.setCreateTime(now);
                                                    userTaskRecord.setCreateAt(String.valueOf(userId));
                                                    userTaskRecord.setUpdateTime(now);
                                                    userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                    userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                    userTaskRecordMapper.insert(userTaskRecord);
                                                    List<UserTaskRecord> userTaskRecordsTwo =userTaskRecordMapper.selectList(wrapperRecord);
                                                    if (userTaskRecordsTwo.size()>=taskDTO.getReadyNum()){
                                                        //查询奖励
                                                        List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                        for (TaskReward taskReward : taskRewardList){
                                                            if (taskReward.getRewardType()==1){//送优惠券
                                                                //发券
                                                                String[] couponIds=taskReward.getCouponIds().split(",");
                                                                for (String couponId : couponIds){
                                                                    ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                                    receiveCouponVo.setReceiveType(2);
                                                                    receiveCouponVo.setCouponId(couponId);
                                                                    mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                                }
                                                            }else{//送积分
                                                                //加积分
                                                                mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                            }
                                                        }
                                                        break;
                                                    }
                                                }
                                            }else{
                                                //剩余订单数量为当前继续完成的订单数量
                                                for (int j = 0; j < orderNumTwo; j++) {
                                                    UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                    userTaskRecord.setUserId(userId);
                                                    userTaskRecord.setTaskId(taskDTO.getId());
                                                    userTaskRecord.setCreateTime(now);
                                                    userTaskRecord.setCreateAt(String.valueOf(userId));
                                                    userTaskRecord.setUpdateTime(now);
                                                    userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                    userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                    userTaskRecordMapper.insert(userTaskRecord);
                                                }
                                            }
                                        }
                                        break;
                                    }
                                    startTime=endTime;
                                }
                            }else{
                                String str=sdf.format(taskDTO.getRestrictTimeStart());
                                String firstYear = str.substring(0,4);
                                String firstMonth = str.substring(5,7);
                                String LastStr=sdf.format(taskDTO.getRestrictTimeEnd());
                                String lastYear = LastStr.substring(0,4);
                                String lastMonth = LastStr.substring(5,7);
                                String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                                Date firstDate = sdf.parse(firstDay);
                                Date startTime=null;
                                if (taskDTO.getRestrictTimeStart().after(firstDate)){
                                    startTime=taskDTO.getRestrictTimeStart();
                                }else{
                                    startTime=firstDate;
                                }
                                //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                                int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                                //计算monthNum除以readymonth取整，得到实际周期次数
                                int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                                for (int i = 0; i < actualCycle; i++) {
                                    //得到startTime日期的最后一天
                                    Date endTime= null;
                                    if (i==actualCycle-1){
                                        endTime=taskDTO.getRestrictTimeEnd();
                                    }else{
                                        //得到startTime日期+taskDTO.getReadyMonth()的最后一天
                                        endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                                        String strEndTime=sdf.format(endTime);
                                        String endTimeYear = strEndTime.substring(0,4);
                                        String endTimeMonth = strEndTime.substring(5,7);
                                        String endTimeDay = strEndTime.substring(8,10);
                                        //当月最后一天
                                        String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                                        endTime=sdf.parse(endTimeStr);
                                    }
                                    Integer orderNumTwo=orderByProduct(userInfoDTO.getData().getCardNo(),
                                            sdf.format(startTime),
                                            sdf.format(endTime),
                                            taskDTO.getStoreProductCodes(),
                                            taskDTO.getDepartProductCodes(),
                                            taskDTO.getLineProductCodes(),
                                            taskDTO.getClassProductCodes(),
                                            taskDTO.getJanProductCodes());
                                    if (orderNumTwo<=0){
                                        //endTime+一个月
                                        endTime= DateUtils.getPlusMonthLastDay(endTime,1);
                                        String nextTime=sdf.format(endTime);
                                        String nextTimeYear = nextTime.substring(0,4);
                                        String nextTimeMonth = nextTime.substring(5,7);
                                        //得到下个月第一天日期
                                        String endTimeNext=nextTimeYear+"-"+nextTimeMonth+"-01 00:00:00";
                                        endTime=sdf.parse(endTimeNext);
                                        startTime=endTime;
                                        continue;
                                    }
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getReadyNum()){
                                                break;
                                            }else{
                                                if (orderNumTwo>=taskDTO.getReadyNum()){
                                                    for (int j = 0; j < orderNumTwo; j++) {
                                                        UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                        userTaskRecord.setUserId(userId);
                                                        userTaskRecord.setTaskId(taskDTO.getId());
                                                        userTaskRecord.setCreateTime(now);
                                                        userTaskRecord.setCreateAt(String.valueOf(userId));
                                                        userTaskRecord.setUpdateTime(now);
                                                        userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                        userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                        userTaskRecordMapper.insert(userTaskRecord);
                                                        List<UserTaskRecord> userTaskRecordsTwo =userTaskRecordMapper.selectList(wrapperRecord);
                                                        if (userTaskRecordsTwo.size()>=taskDTO.getReadyNum()){
                                                            //查询奖励
                                                            List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                            for (TaskReward taskReward : taskRewardList){
                                                                if (taskReward.getRewardType()==1){//送优惠券
                                                                    //发券
                                                                    String[] couponIds=taskReward.getCouponIds().split(",");
                                                                    for (String couponId : couponIds){
                                                                        ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                                        receiveCouponVo.setReceiveType(2);
                                                                        receiveCouponVo.setCouponId(couponId);
                                                                        mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                                    }
                                                                }else{//送积分
                                                                    //加积分
                                                                    mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                                }
                                                            }
                                                            break;
                                                        }
                                                    }
                                                }else{
                                                    //计算出来已有订单数量减去已完成记录，剩余订单数量为当前继续完成的订单数量
                                                    Integer num=orderNumTwo-userTaskRecords.size();
                                                    for (int j = 0; j < num; j++) {
                                                        UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                        userTaskRecord.setUserId(userId);
                                                        userTaskRecord.setTaskId(taskDTO.getId());
                                                        userTaskRecord.setCreateTime(now);
                                                        userTaskRecord.setCreateAt(String.valueOf(userId));
                                                        userTaskRecord.setUpdateTime(now);
                                                        userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                        userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                        userTaskRecordMapper.insert(userTaskRecord);
                                                    }
                                                }

                                            }
                                        }else{
                                            if (orderNumTwo>=taskDTO.getReadyNum()){
                                                for (int j = 0; j < orderNumTwo; j++) {
                                                    UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                    userTaskRecord.setUserId(userId);
                                                    userTaskRecord.setTaskId(taskDTO.getId());
                                                    userTaskRecord.setCreateTime(now);
                                                    userTaskRecord.setCreateAt(String.valueOf(userId));
                                                    userTaskRecord.setUpdateTime(now);
                                                    userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                    userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                    userTaskRecordMapper.insert(userTaskRecord);
                                                    List<UserTaskRecord> userTaskRecordsTwo =userTaskRecordMapper.selectList(wrapperRecord);
                                                    if (userTaskRecordsTwo.size()>=taskDTO.getReadyNum()){
                                                        //查询奖励
                                                        List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                        for (TaskReward taskReward : taskRewardList){
                                                            if (taskReward.getRewardType()==1){//送优惠券
                                                                //发券
                                                                String[] couponIds=taskReward.getCouponIds().split(",");
                                                                for (String couponId : couponIds){
                                                                    ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                                    receiveCouponVo.setReceiveType(2);
                                                                    receiveCouponVo.setCouponId(couponId);
                                                                    mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                                }
                                                            }else{//送积分
                                                                //加积分
                                                                mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                            }
                                                        }
                                                        break;
                                                    }
                                                }
                                            }else{
                                                //剩余订单数量为当前继续完成的订单数量
                                                for (int j = 0; j < orderNumTwo; j++) {
                                                    UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                    userTaskRecord.setUserId(userId);
                                                    userTaskRecord.setTaskId(taskDTO.getId());
                                                    userTaskRecord.setCreateTime(now);
                                                    userTaskRecord.setCreateAt(String.valueOf(userId));
                                                    userTaskRecord.setUpdateTime(now);
                                                    userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                    userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                    userTaskRecordMapper.insert(userTaskRecord);
                                                }
                                            }
                                        }
                                        break;
                                    }
                                    //endTime+一个月
                                    endTime= DateUtils.getPlusMonthLastDay(endTime,1);
                                    String nextTime=sdf.format(endTime);
                                    String nextTimeYear = nextTime.substring(0,4);
                                    String nextTimeMonth = nextTime.substring(5,7);
                                    //得到下个月第一天日期
                                    String endTimeNext=nextTimeYear+"-"+nextTimeMonth+"-01 00:00:00";
                                    endTime=sdf.parse(endTimeNext);
                                    startTime=endTime;
                                }
                            }
                        }else{
                            if (taskDTO.getReayType().equals(readyDay)){
                                //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                                long day = (taskDTO.getRestrictTimeEnd().getTime() - taskDTO.getRestrictTimeStart().getTime()) / (1000 * 60 * 60 * 24);
                                //计算day除以readyDay取整，得到实际周期次数
                                int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                                Date startTime = taskDTO.getRestrictTimeStart();
                                for (int i = 0; i < actualCycle; i++) {
                                    //得到startTime日期的最后一天
                                    Date endTime= null;
                                    if (i==actualCycle-1){
                                        endTime=taskDTO.getRestrictTimeEnd();
                                    }else{
                                        endTime=DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                                    }
                                    Integer orderNumTwo=orderByProduct(userInfoDTO.getData().getCardNo(),
                                            sdf.format(startTime),
                                            sdf.format(endTime),
                                            taskDTO.getStoreProductCodes(),
                                            taskDTO.getDepartProductCodes(),
                                            taskDTO.getLineProductCodes(),
                                            taskDTO.getClassProductCodes(),
                                            taskDTO.getJanProductCodes());
                                    if (orderNumTwo<=0){
                                        startTime=endTime;
                                        continue;
                                    }
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        wrapperRecord.orderByAsc(UserTaskRecord::getCreateTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getReadyNum()*taskDTO.getTotalReadyNum()){//  首购任务没有加不限时
                                                break;
                                            }
                                            //计算出来已有订单数量减去已完成记录，剩余订单数量为当前继续完成的订单数量
                                            Integer num=orderNumTwo-userTaskRecords.size();
                                            if (num<=0){
                                                break;
                                            }
                                            for (int j = 0; j < num; j++) {
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                List<UserTaskRecord> userTaskRecordsTwo =userTaskRecordMapper.selectList(wrapperRecord);
                                                //判断(userTaskRecords.size()+1)/taskDTO.getReadyNum()有没有余数
                                                int userReadySuccessNum=userTaskRecordsTwo.size()/taskDTO.getReadyNum();
                                                if (userReadySuccessNum == userTaskRecordsTwo.get(0).getFinishNum()){//可能会重复发奖励
                                                    if (userReadySuccessNum<taskDTO.getTotalReadyNum()){
                                                        continue;
                                                    }
                                                    break;
                                                }else{
                                                    int allReadySuccessNum=userTaskRecordsTwo.size()/taskDTO.getReadyNum();
                                                    //查询奖励
                                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                    //获取taskRewardList中第actualCycle个奖励
                                                    TaskReward taskReward = taskRewardList.get(allReadySuccessNum-1);
                                                    if (taskReward.getRewardType()==1){//送优惠券
                                                        //发券
                                                        String[] couponIds=taskReward.getCouponIds().split(",");
                                                        for (String couponId : couponIds){
                                                            ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                            receiveCouponVo.setReceiveType(2);
                                                            receiveCouponVo.setCouponId(couponId);
                                                            mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                        }
                                                    }else{//送积分
                                                        //加积分
                                                        mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                    }
                                                    UserTaskRecord userTaskRecordUpdate = new UserTaskRecord();
                                                    userTaskRecordUpdate.setFinishNum(allReadySuccessNum);
                                                    LambdaQueryWrapper<UserTaskRecord> wrapperRecordUpdate = new LambdaQueryWrapper<>();
                                                    wrapperRecordUpdate.eq(UserTaskRecord::getUserId,userId);
                                                    wrapperRecordUpdate.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                                    userTaskRecordMapper.update(userTaskRecordUpdate,wrapperRecordUpdate);
                                                    if (allReadySuccessNum>=taskDTO.getTotalReadyNum()){
                                                        break;
                                                    }
                                                }
                                            }
                                        }else{
                                            for (int j = 0; j < orderNumTwo; j++) {
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                List<UserTaskRecord> userTaskRecordsTwo =userTaskRecordMapper.selectList(wrapperRecord);
                                                //判断(userTaskRecords.size()+1)/taskDTO.getReadyNum()有没有余数
                                                int userReadySuccessNum=userTaskRecordsTwo.size()/taskDTO.getReadyNum();
                                                if (userReadySuccessNum == userTaskRecordsTwo.get(0).getFinishNum()){//可能会重复发奖励
                                                    if (userReadySuccessNum<taskDTO.getTotalReadyNum()){
                                                        continue;
                                                    }
                                                    break;
                                                }else{
                                                    int allReadySuccessNum=userTaskRecordsTwo.size()/taskDTO.getReadyNum();
                                                    //查询奖励
                                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                    //获取taskRewardList中第actualCycle个奖励
                                                    TaskReward taskReward = taskRewardList.get(allReadySuccessNum-1);
                                                    if (taskReward.getRewardType()==1){//送优惠券
                                                        //发券
                                                        String[] couponIds=taskReward.getCouponIds().split(",");
                                                        for (String couponId : couponIds){
                                                            ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                            receiveCouponVo.setReceiveType(2);
                                                            receiveCouponVo.setCouponId(couponId);
                                                            mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                        }
                                                    }else{//送积分
                                                        //加积分
                                                        mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                    }
                                                    UserTaskRecord userTaskRecordUpdate = new UserTaskRecord();
                                                    userTaskRecordUpdate.setFinishNum(allReadySuccessNum);
                                                    LambdaQueryWrapper<UserTaskRecord> wrapperRecordUpdate = new LambdaQueryWrapper<>();
                                                    wrapperRecordUpdate.eq(UserTaskRecord::getUserId,userId);
                                                    wrapperRecordUpdate.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                                    userTaskRecordMapper.update(userTaskRecordUpdate,wrapperRecordUpdate);
                                                    if (allReadySuccessNum>=taskDTO.getTotalReadyNum()){
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                        break;
                                    }
                                    startTime=endTime;
                                }
                            }else{
                                String str=sdf.format(taskDTO.getRestrictTimeStart());
                                String firstYear = str.substring(0,4);
                                String firstMonth = str.substring(5,7);
                                String LastStr=sdf.format(taskDTO.getRestrictTimeEnd());
                                String lastYear = LastStr.substring(0,4);
                                String lastMonth = LastStr.substring(5,7);
                                String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                                Date firstDate = sdf.parse(firstDay);
                                Date startTime=null;
                                if (taskDTO.getRestrictTimeStart().after(firstDate)){
                                    startTime=taskDTO.getRestrictTimeStart();
                                }else{
                                    startTime=firstDate;
                                }
                                //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                                int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                                //计算monthNum除以readymonth取整，得到实际周期次数
                                int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                                for (int i = 0; i < actualCycle; i++) {
                                    //得到startTime日期的最后一天
                                    Date endTime= null;
                                    if (i==actualCycle-1){
                                        endTime=taskDTO.getRestrictTimeEnd();
                                    }else{
                                        //得到startTime日期+taskDTO.getReadyMonth()的最后一天
                                        endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                                        String strEndTime=sdf.format(endTime);
                                        String endTimeYear = strEndTime.substring(0,4);
                                        String endTimeMonth = strEndTime.substring(5,7);
                                        String endTimeDay = strEndTime.substring(8,10);
                                        //当月最后一天
                                        String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                                        endTime=sdf.parse(endTimeStr);
                                    }
                                    Integer orderNumTwo=orderByProduct(userInfoDTO.getData().getCardNo(),
                                            sdf.format(startTime),
                                            sdf.format(endTime),
                                            taskDTO.getStoreProductCodes(),
                                            taskDTO.getDepartProductCodes(),
                                            taskDTO.getLineProductCodes(),
                                            taskDTO.getClassProductCodes(),
                                            taskDTO.getJanProductCodes());
                                    if (orderNumTwo<=0){
                                        //endTime+一个月
                                        endTime= DateUtils.getPlusMonthLastDay(endTime,1);
                                        String nextTime=sdf.format(endTime);
                                        String nextTimeYear = nextTime.substring(0,4);
                                        String nextTimeMonth = nextTime.substring(5,7);
                                        //得到下个月第一天日期
                                        String endTimeNext=nextTimeYear+"-"+nextTimeMonth+"-01 00:00:00";
                                        endTime=sdf.parse(endTimeNext);
                                        startTime=endTime;
                                        continue;
                                    }
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        wrapperRecord.orderByAsc(UserTaskRecord::getCreateTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getReadyNum()*taskDTO.getTotalReadyNum()){
                                                break;
                                            }
                                            //计算出来已有订单数量减去已完成记录，剩余订单数量为当前继续完成的订单数量
                                            Integer num=orderNumTwo-userTaskRecords.size();
                                            if (num<=0){
                                                break;
                                            }
                                            for (int j = 0; j < num; j++) {
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                List<UserTaskRecord> userTaskRecordsTwo =userTaskRecordMapper.selectList(wrapperRecord);
                                                //判断(userTaskRecords.size()+1)/taskDTO.getReadyNum()有没有余数
                                                int userReadySuccessNum=userTaskRecordsTwo.size()/taskDTO.getReadyNum();
                                                if (userReadySuccessNum == userTaskRecordsTwo.get(0).getFinishNum()){//可能会重复发奖励
                                                    if (userReadySuccessNum<taskDTO.getTotalReadyNum()){
                                                        continue;
                                                    }
                                                    break;
                                                }else{
                                                    int allReadySuccessNum=userTaskRecordsTwo.size()/taskDTO.getReadyNum();
                                                    //查询奖励
                                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                    //获取taskRewardList中第actualCycle个奖励
                                                    TaskReward taskReward = taskRewardList.get(allReadySuccessNum-1);
                                                    if (taskReward.getRewardType()==1){//送优惠券
                                                        //发券
                                                        String[] couponIds=taskReward.getCouponIds().split(",");
                                                        for (String couponId : couponIds){
                                                            ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                            receiveCouponVo.setReceiveType(2);
                                                            receiveCouponVo.setCouponId(couponId);
                                                            mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                        }
                                                    }else{//送积分
                                                        //加积分
                                                        mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                    }
                                                    UserTaskRecord userTaskRecordUpdate = new UserTaskRecord();
                                                    userTaskRecordUpdate.setFinishNum(allReadySuccessNum);
                                                    LambdaQueryWrapper<UserTaskRecord> wrapperRecordUpdate = new LambdaQueryWrapper<>();
                                                    wrapperRecordUpdate.eq(UserTaskRecord::getUserId,userId);
                                                    wrapperRecordUpdate.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                                    userTaskRecordMapper.update(userTaskRecordUpdate,wrapperRecordUpdate);
                                                    if (allReadySuccessNum>=taskDTO.getTotalReadyNum()){
                                                        break;
                                                    }
                                                }
                                            }
                                        }else{
                                            for (int j = 0; j < orderNumTwo; j++) {
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                List<UserTaskRecord> userTaskRecordsTwo =userTaskRecordMapper.selectList(wrapperRecord);
                                                //判断(userTaskRecords.size()+1)/taskDTO.getReadyNum()有没有余数
                                                int userReadySuccessNum=userTaskRecordsTwo.size()/taskDTO.getReadyNum();
                                                if (userReadySuccessNum == userTaskRecordsTwo.get(0).getFinishNum()){//可能会重复发奖励
                                                    if (userReadySuccessNum<taskDTO.getTotalReadyNum()){
                                                        continue;
                                                    }
                                                    break;
                                                }else{
                                                    int allReadySuccessNum=userTaskRecordsTwo.size()/taskDTO.getReadyNum();
                                                    //查询奖励
                                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                    //获取taskRewardList中第actualCycle个奖励
                                                    TaskReward taskReward = taskRewardList.get(allReadySuccessNum-1);
                                                    if (taskReward.getRewardType()==1){//送优惠券
                                                        //发券
                                                        String[] couponIds=taskReward.getCouponIds().split(",");
                                                        for (String couponId : couponIds){
                                                            ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                            receiveCouponVo.setReceiveType(2);
                                                            receiveCouponVo.setCouponId(couponId);
                                                            mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                        }
                                                    }else{//送积分
                                                        //加积分
                                                        mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                    }
                                                    UserTaskRecord userTaskRecordUpdate = new UserTaskRecord();
                                                    userTaskRecordUpdate.setFinishNum(allReadySuccessNum);
                                                    LambdaQueryWrapper<UserTaskRecord> wrapperRecordUpdate = new LambdaQueryWrapper<>();
                                                    wrapperRecordUpdate.eq(UserTaskRecord::getUserId,userId);
                                                    wrapperRecordUpdate.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                                    userTaskRecordMapper.update(userTaskRecordUpdate,wrapperRecordUpdate);
                                                    if (allReadySuccessNum>=taskDTO.getTotalReadyNum()){
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                        break;
                                    }
                                    //endTime+一个月
                                    endTime= DateUtils.getPlusMonthLastDay(endTime,1);
                                    String nextTime=sdf.format(endTime);
                                    String nextTimeYear = nextTime.substring(0,4);
                                    String nextTimeMonth = nextTime.substring(5,7);
                                    //得到下个月第一天日期
                                    String endTimeNext=nextTimeYear+"-"+nextTimeMonth+"-01 00:00:00";
                                    endTime=sdf.parse(endTimeNext);
                                    startTime=endTime;
                                }
                            }
                        }
                    }else{
                        //1一次性任务，2周期性任务，3周期+阶梯
                        if (taskDTO.getReadyCycle().equals(1)){
                            Integer orderNum=orderByProduct(userInfoDTO.getData().getCardNo(),
                                    sdf.format(taskDTO.getCreateTime()),
                                    null,
                                    taskDTO.getStoreProductCodes(),
                                    taskDTO.getDepartProductCodes(),
                                    taskDTO.getLineProductCodes(),
                                    taskDTO.getClassProductCodes(),
                                    taskDTO.getJanProductCodes());
                            if (orderNum<=0){
                                continue;
                            }
                            LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                            wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                            wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                            List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                            if (!CollectionUtils.isEmpty(userTaskRecords)){
                                if (userTaskRecords.size()>=taskDTO.getReadyNum()){
                                    continue;
                                }else{
                                    if (orderNum>=taskDTO.getReadyNum()){
                                        for (int i = 0; i < orderNum; i++) {
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            List<UserTaskRecord> userTaskRecordsTwo =userTaskRecordMapper.selectList(wrapperRecord);
                                            if (userTaskRecordsTwo.size()>=taskDTO.getReadyNum()){
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                for (TaskReward taskReward : taskRewardList){
                                                    if (taskReward.getRewardType()==1){//送优惠券
                                                        //发券
                                                        String[] couponIds=taskReward.getCouponIds().split(",");
                                                        for (String couponId : couponIds){
                                                            ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                            receiveCouponVo.setReceiveType(2);
                                                            receiveCouponVo.setCouponId(couponId);
                                                            mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                        }
                                                    }else{//送积分
                                                        //加积分
                                                        mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                    }
                                                }
                                                break;
                                            }
                                        }
                                    }else{
                                        //计算出来已有订单数量减去已完成记录，剩余订单数量为当前继续完成的订单数量
                                        Integer num=orderNum-userTaskRecords.size();
                                        for (int i = 0; i < num; i++) {
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                        }
                                    }
                                }
                            }else{
                                if (orderNum>=taskDTO.getReadyNum()){
                                    for (int i = 0; i < orderNum; i++) {
                                        UserTaskRecord userTaskRecord = new UserTaskRecord();
                                        userTaskRecord.setUserId(userId);
                                        userTaskRecord.setTaskId(taskDTO.getId());
                                        userTaskRecord.setCreateTime(now);
                                        userTaskRecord.setCreateAt(String.valueOf(userId));
                                        userTaskRecord.setUpdateTime(now);
                                        userTaskRecord.setUpdateAt(String.valueOf(userId));
                                        userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                        userTaskRecordMapper.insert(userTaskRecord);
                                        List<UserTaskRecord> userTaskRecordsTwo =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (userTaskRecordsTwo.size()>=taskDTO.getReadyNum()){
                                            //查询奖励
                                            List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                            for (TaskReward taskReward : taskRewardList){
                                                if (taskReward.getRewardType()==1){//送优惠券
                                                    //发券
                                                    String[] couponIds=taskReward.getCouponIds().split(",");
                                                    for (String couponId : couponIds){
                                                        ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                        receiveCouponVo.setReceiveType(2);
                                                        receiveCouponVo.setCouponId(couponId);
                                                        mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                    }
                                                }else{//送积分
                                                    //加积分
                                                    mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                }
                                            }
                                            break;
                                        }
                                    }
                                }else{
                                    //剩余订单数量为当前继续完成的订单数量
                                    for (int i = 0; i < orderNum; i++) {
                                        UserTaskRecord userTaskRecord = new UserTaskRecord();
                                        userTaskRecord.setUserId(userId);
                                        userTaskRecord.setTaskId(taskDTO.getId());
                                        userTaskRecord.setCreateTime(now);
                                        userTaskRecord.setCreateAt(String.valueOf(userId));
                                        userTaskRecord.setUpdateTime(now);
                                        userTaskRecord.setUpdateAt(String.valueOf(userId));
                                        userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                        userTaskRecordMapper.insert(userTaskRecord);
                                    }
                                }
                            }
                        }else if (taskDTO.getReadyCycle().equals(2)){
                            if (taskDTO.getReayType().equals(readyDay)){
                                //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                                String str=sdf.format(taskDTO.getCreateTime());
                                String firstYear = str.substring(0,4);
                                String firstMonth = str.substring(5,7);
                                String firstDay = str.substring(8,10);
                                String startTimeStr=firstYear+"-"+firstMonth+"-"+firstDay+" 00:00:00";
                                Date startTime=sdf.parse(startTimeStr);
                                long day = (new Date().getTime() - startTime.getTime()) / (1000 * 60 * 60 * 24);
                                //计算day除以readyDay取整，得到实际周期次数
                                int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                                for (int i = 0; i < actualCycle; i++) {
                                    //计算taskDTO.getRestrictTimeStart()加上readyDay天后的日期
                                    Date endTime=DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                                    Integer orderNumTwo=orderByProduct(userInfoDTO.getData().getCardNo(),
                                            sdf.format(startTime),
                                            sdf.format(endTime),
                                            taskDTO.getStoreProductCodes(),
                                            taskDTO.getDepartProductCodes(),
                                            taskDTO.getLineProductCodes(),
                                            taskDTO.getClassProductCodes(),
                                            taskDTO.getJanProductCodes());
                                    if (orderNumTwo<=0){
                                        startTime=endTime;
                                        continue;
                                    }
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getReadyNum()){
                                                break;
                                            }else{
                                                if (orderNumTwo>= taskDTO.getReadyNum()){
                                                    for (int j = 0; j < orderNumTwo; j++) {
                                                        UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                        userTaskRecord.setUserId(userId);
                                                        userTaskRecord.setTaskId(taskDTO.getId());
                                                        userTaskRecord.setCreateTime(now);
                                                        userTaskRecord.setCreateAt(String.valueOf(userId));
                                                        userTaskRecord.setUpdateTime(now);
                                                        userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                        userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                        userTaskRecordMapper.insert(userTaskRecord);
                                                        List<UserTaskRecord> userTaskRecordsTwo =userTaskRecordMapper.selectList(wrapperRecord);
                                                        if (userTaskRecordsTwo.size()>=taskDTO.getReadyNum()){
                                                            //查询奖励
                                                            List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                            for (TaskReward taskReward : taskRewardList){
                                                                if (taskReward.getRewardType()==1){//送优惠券
                                                                    //发券
                                                                    String[] couponIds=taskReward.getCouponIds().split(",");
                                                                    for (String couponId : couponIds){
                                                                        ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                                        receiveCouponVo.setReceiveType(2);
                                                                        receiveCouponVo.setCouponId(couponId);
                                                                        mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                                    }
                                                                }else{//送积分
                                                                    //加积分
                                                                    mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                                }
                                                            }
                                                            break;
                                                        }
                                                    }
                                                }else{
                                                    //计算出来已有订单数量减去已完成记录，剩余订单数量为当前继续完成的订单数量
                                                    Integer num=orderNumTwo-userTaskRecords.size();
                                                    for (int j = 0; j < num; j++) {
                                                        UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                        userTaskRecord.setUserId(userId);
                                                        userTaskRecord.setTaskId(taskDTO.getId());
                                                        userTaskRecord.setCreateTime(now);
                                                        userTaskRecord.setCreateAt(String.valueOf(userId));
                                                        userTaskRecord.setUpdateTime(now);
                                                        userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                        userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                        userTaskRecordMapper.insert(userTaskRecord);
                                                    }
                                                }
                                            }
                                        }else{
                                            if (orderNumTwo>= taskDTO.getReadyNum()){
                                                for (int j = 0; j < orderNumTwo; j++) {
                                                    UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                    userTaskRecord.setUserId(userId);
                                                    userTaskRecord.setTaskId(taskDTO.getId());
                                                    userTaskRecord.setCreateTime(now);
                                                    userTaskRecord.setCreateAt(String.valueOf(userId));
                                                    userTaskRecord.setUpdateTime(now);
                                                    userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                    userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                    userTaskRecordMapper.insert(userTaskRecord);
                                                    List<UserTaskRecord> userTaskRecordsTwo =userTaskRecordMapper.selectList(wrapperRecord);
                                                    if (userTaskRecordsTwo.size()>=taskDTO.getReadyNum()){
                                                        //查询奖励
                                                        List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                        for (TaskReward taskReward : taskRewardList){
                                                            if (taskReward.getRewardType()==1){//送优惠券
                                                                //发券
                                                                String[] couponIds=taskReward.getCouponIds().split(",");
                                                                for (String couponId : couponIds){
                                                                    ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                                    receiveCouponVo.setReceiveType(2);
                                                                    receiveCouponVo.setCouponId(couponId);
                                                                    mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                                }
                                                            }else{//送积分
                                                                //加积分
                                                                mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                            }
                                                        }
                                                        break;
                                                    }
                                                }
                                            }else{
                                                //剩余订单数量为当前继续完成的订单数量
                                                for (int j = 0; j < orderNumTwo; j++) {
                                                    UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                    userTaskRecord.setUserId(userId);
                                                    userTaskRecord.setTaskId(taskDTO.getId());
                                                    userTaskRecord.setCreateTime(now);
                                                    userTaskRecord.setCreateAt(String.valueOf(userId));
                                                    userTaskRecord.setUpdateTime(now);
                                                    userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                    userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                    userTaskRecordMapper.insert(userTaskRecord);
                                                }
                                            }
                                        }
                                        break;
                                    }
                                    startTime=endTime;
                                }
                            }else{
                                String str=sdf.format(taskDTO.getCreateTime());
                                String firstYear = str.substring(0,4);
                                String firstMonth = str.substring(5,7);
                                String LastStr=sdf.format(now);
                                String lastYear = LastStr.substring(0,4);
                                String lastMonth = LastStr.substring(5,7);
                                String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                                Date firstDate = sdf.parse(firstDay);
                                Date startTime=firstDate;
                                //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                                int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                                //计算monthNum除以readymonth取整，得到实际周期次数
                                int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                                for (int i = 0; i < actualCycle; i++) {
                                    ////得到startTime日期+taskDTO.getReadyMonth()的最后一天
                                    Date endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                                    String strEndTime=sdf.format(endTime);
                                    String endTimeYear = strEndTime.substring(0,4);
                                    String endTimeMonth = strEndTime.substring(5,7);
                                    String endTimeDay = strEndTime.substring(8,10);
                                    //当月最后一天
                                    String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                                    endTime=sdf.parse(endTimeStr);
                                    Integer orderNumTwo=orderByProduct(userInfoDTO.getData().getCardNo(),
                                            sdf.format(startTime),
                                            sdf.format(endTime),
                                            taskDTO.getStoreProductCodes(),
                                            taskDTO.getDepartProductCodes(),
                                            taskDTO.getLineProductCodes(),
                                            taskDTO.getClassProductCodes(),
                                            taskDTO.getJanProductCodes());
                                    if (orderNumTwo<=0){
                                        //endTime+一个月
                                        endTime= DateUtils.getPlusMonthLastDay(endTime,1);
                                        String nextTime=sdf.format(endTime);
                                        String nextTimeYear = nextTime.substring(0,4);
                                        String nextTimeMonth = nextTime.substring(5,7);
                                        //得到下个月第一天日期
                                        String endTimeNext=nextTimeYear+"-"+nextTimeMonth+"-01 00:00:00";
                                        endTime=sdf.parse(endTimeNext);
                                        startTime=endTime;
                                        continue;
                                    }
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getReadyNum()){
                                                break;
                                            }else{
                                                if (orderNumTwo>= taskDTO.getReadyNum()){
                                                    for (int j = 0; j < orderNumTwo; j++) {
                                                        UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                        userTaskRecord.setUserId(userId);
                                                        userTaskRecord.setTaskId(taskDTO.getId());
                                                        userTaskRecord.setCreateTime(now);
                                                        userTaskRecord.setCreateAt(String.valueOf(userId));
                                                        userTaskRecord.setUpdateTime(now);
                                                        userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                        userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                        userTaskRecordMapper.insert(userTaskRecord);
                                                        List<UserTaskRecord> userTaskRecordsTwo =userTaskRecordMapper.selectList(wrapperRecord);
                                                        if (userTaskRecordsTwo.size()>=taskDTO.getReadyNum()){
                                                            //查询奖励
                                                            List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                            for (TaskReward taskReward : taskRewardList){
                                                                if (taskReward.getRewardType()==1){//送优惠券
                                                                    //发券
                                                                    String[] couponIds=taskReward.getCouponIds().split(",");
                                                                    for (String couponId : couponIds){
                                                                        ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                                        receiveCouponVo.setReceiveType(2);
                                                                        receiveCouponVo.setCouponId(couponId);
                                                                        mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                                    }
                                                                }else{//送积分
                                                                    //加积分
                                                                    mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                                }
                                                            }
                                                            break;
                                                        }
                                                    }
                                                }else{
                                                    //计算出来已有订单数量减去已完成记录，剩余订单数量为当前继续完成的订单数量
                                                    Integer num=orderNumTwo-userTaskRecords.size();
                                                    for (int j = 0; j < num; j++) {
                                                        UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                        userTaskRecord.setUserId(userId);
                                                        userTaskRecord.setTaskId(taskDTO.getId());
                                                        userTaskRecord.setCreateTime(now);
                                                        userTaskRecord.setCreateAt(String.valueOf(userId));
                                                        userTaskRecord.setUpdateTime(now);
                                                        userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                        userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                        userTaskRecordMapper.insert(userTaskRecord);
                                                    }
                                                }
                                            }
                                        }else{
                                            if (orderNumTwo>= taskDTO.getReadyNum()){
                                                for (int j = 0; j < orderNumTwo; j++) {
                                                    UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                    userTaskRecord.setUserId(userId);
                                                    userTaskRecord.setTaskId(taskDTO.getId());
                                                    userTaskRecord.setCreateTime(now);
                                                    userTaskRecord.setCreateAt(String.valueOf(userId));
                                                    userTaskRecord.setUpdateTime(now);
                                                    userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                    userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                    userTaskRecordMapper.insert(userTaskRecord);
                                                    List<UserTaskRecord> userTaskRecordsTwo =userTaskRecordMapper.selectList(wrapperRecord);
                                                    if (userTaskRecordsTwo.size()>=taskDTO.getReadyNum()){
                                                        //查询奖励
                                                        List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                        for (TaskReward taskReward : taskRewardList){
                                                            if (taskReward.getRewardType()==1){//送优惠券
                                                                //发券
                                                                String[] couponIds=taskReward.getCouponIds().split(",");
                                                                for (String couponId : couponIds){
                                                                    ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                                    receiveCouponVo.setReceiveType(2);
                                                                    receiveCouponVo.setCouponId(couponId);
                                                                    mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                                }
                                                            }else{//送积分
                                                                //加积分
                                                                mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                            }
                                                        }
                                                        break;
                                                    }
                                                }
                                            }else{
                                                //剩余订单数量为当前继续完成的订单数量
                                                for (int j = 0; j < orderNumTwo; j++) {
                                                    UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                    userTaskRecord.setUserId(userId);
                                                    userTaskRecord.setTaskId(taskDTO.getId());
                                                    userTaskRecord.setCreateTime(now);
                                                    userTaskRecord.setCreateAt(String.valueOf(userId));
                                                    userTaskRecord.setUpdateTime(now);
                                                    userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                    userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                    userTaskRecordMapper.insert(userTaskRecord);
                                                }
                                            }
                                        }
                                        break;
                                    }
                                    //endTime+一个月
                                    endTime= DateUtils.getPlusMonthLastDay(endTime,1);
                                    String nextTime=sdf.format(endTime);
                                    String nextTimeYear = nextTime.substring(0,4);
                                    String nextTimeMonth = nextTime.substring(5,7);
                                    //得到下个月第一天日期
                                    String endTimeNext=nextTimeYear+"-"+nextTimeMonth+"-01 00:00:00";
                                    endTime=sdf.parse(endTimeNext);
                                    startTime=endTime;
                                }
                            }
                        }else{
                            if (taskDTO.getReayType().equals(readyDay)){
                                //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                                String str=sdf.format(taskDTO.getCreateTime());
                                String firstYear = str.substring(0,4);
                                String firstMonth = str.substring(5,7);
                                String firstDay = str.substring(8,10);
                                String startTimeStr=firstYear+"-"+firstMonth+"-"+firstDay+" 00:00:00";
                                Date startTime=sdf.parse(startTimeStr);
                                long day = (new Date().getTime() - startTime.getTime()) / (1000 * 60 * 60 * 24);
                                //计算day除以readyDay取整，得到实际周期次数
                                int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                                for (int i = 0; i < actualCycle; i++) {
                                    ////得到startTime日期+taskDTO.getReadyMonth()的最后一天
                                    Date endTime=DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                                    Integer orderNumTwo=orderByProduct(userInfoDTO.getData().getCardNo(),
                                            sdf.format(startTime),
                                            sdf.format(endTime),
                                            taskDTO.getStoreProductCodes(),
                                            taskDTO.getDepartProductCodes(),
                                            taskDTO.getLineProductCodes(),
                                            taskDTO.getClassProductCodes(),
                                            taskDTO.getJanProductCodes());
                                    if (orderNumTwo<=0){
                                        startTime=endTime;
                                        continue;
                                    }
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        wrapperRecord.orderByAsc(UserTaskRecord::getCreateTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getReadyNum()*taskDTO.getTotalReadyNum()){
                                                break;
                                            }
                                            //计算出来已有订单数量减去已完成记录，剩余订单数量为当前继续完成的订单数量
                                            Integer num=orderNumTwo-userTaskRecords.size();
                                            if (num<=0){
                                                break;
                                            }
                                            for (int j = 0; j < num; j++) {
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                List<UserTaskRecord> userTaskRecordsTwo =userTaskRecordMapper.selectList(wrapperRecord);
                                                //判断(userTaskRecords.size()+1)/taskDTO.getReadyNum()有没有余数
                                                int userReadySuccessNum=userTaskRecordsTwo.size()/taskDTO.getReadyNum();
                                                if (userReadySuccessNum == userTaskRecordsTwo.get(0).getFinishNum()){//可能会重复发奖励
                                                    if (userReadySuccessNum<taskDTO.getTotalReadyNum()){
                                                        continue;
                                                    }
                                                    break;
                                                }else{
                                                    int allReadySuccessNum=userTaskRecordsTwo.size()/taskDTO.getReadyNum();
                                                    //查询奖励
                                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                    //获取taskRewardList中第actualCycle个奖励
                                                    TaskReward taskReward = taskRewardList.get(allReadySuccessNum-1);
                                                    if (taskReward.getRewardType()==1){//送优惠券
                                                        //发券
                                                        String[] couponIds=taskReward.getCouponIds().split(",");
                                                        for (String couponId : couponIds){
                                                            ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                            receiveCouponVo.setReceiveType(2);
                                                            receiveCouponVo.setCouponId(couponId);
                                                            mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                        }
                                                    }else{//送积分
                                                        //加积分
                                                        mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                    }
                                                    UserTaskRecord userTaskRecordUpdate = new UserTaskRecord();
                                                    userTaskRecordUpdate.setFinishNum(allReadySuccessNum);
                                                    LambdaQueryWrapper<UserTaskRecord> wrapperRecordUpdate = new LambdaQueryWrapper<>();
                                                    wrapperRecordUpdate.eq(UserTaskRecord::getUserId,userId);
                                                    wrapperRecordUpdate.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                                    userTaskRecordMapper.update(userTaskRecordUpdate,wrapperRecordUpdate);
                                                    if (allReadySuccessNum>=taskDTO.getTotalReadyNum()){
                                                        break;
                                                    }
                                                }
                                            }
                                        }else{
                                            for (int j = 0; j < orderNumTwo; j++) {
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                List<UserTaskRecord> userTaskRecordsTwo =userTaskRecordMapper.selectList(wrapperRecord);
                                                //判断(userTaskRecords.size()+1)/taskDTO.getReadyNum()有没有余数
                                                int userReadySuccessNum=userTaskRecordsTwo.size()/taskDTO.getReadyNum();
                                                if (userReadySuccessNum == userTaskRecordsTwo.get(0).getFinishNum()){//可能会重复发奖励
                                                    if (userReadySuccessNum<taskDTO.getTotalReadyNum()){
                                                        continue;
                                                    }
                                                    break;
                                                }else{
                                                    int allReadySuccessNum=userTaskRecordsTwo.size()/taskDTO.getReadyNum();
                                                    //查询奖励
                                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                    //获取taskRewardList中第actualCycle个奖励
                                                    TaskReward taskReward = taskRewardList.get(allReadySuccessNum-1);
                                                    if (taskReward.getRewardType()==1){//送优惠券
                                                        //发券
                                                        String[] couponIds=taskReward.getCouponIds().split(",");
                                                        for (String couponId : couponIds){
                                                            ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                            receiveCouponVo.setReceiveType(2);
                                                            receiveCouponVo.setCouponId(couponId);
                                                            mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                        }
                                                    }else{//送积分
                                                        //加积分
                                                        mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                    }
                                                    UserTaskRecord userTaskRecordUpdate = new UserTaskRecord();
                                                    userTaskRecordUpdate.setFinishNum(allReadySuccessNum);
                                                    LambdaQueryWrapper<UserTaskRecord> wrapperRecordUpdate = new LambdaQueryWrapper<>();
                                                    wrapperRecordUpdate.eq(UserTaskRecord::getUserId,userId);
                                                    wrapperRecordUpdate.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                                    userTaskRecordMapper.update(userTaskRecordUpdate,wrapperRecordUpdate);
                                                    if (allReadySuccessNum>=taskDTO.getTotalReadyNum()){
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                        break;
                                    }
                                    startTime=endTime;
                                }
                            }else{
                                String str=sdf.format(taskDTO.getCreateTime());
                                String firstYear = str.substring(0,4);
                                String firstMonth = str.substring(5,7);
                                String LastStr=sdf.format(now);
                                String lastYear = LastStr.substring(0,4);
                                String lastMonth = LastStr.substring(5,7);
                                String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                                Date firstDate = sdf.parse(firstDay);
                                Date startTime=firstDate;
                                //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                                int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                                //计算monthNum除以readymonth取整，得到实际周期次数
                                int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                                for (int i = 0; i < actualCycle; i++) {
                                    ////得到startTime日期+taskDTO.getReadyMonth()的最后一天
                                    Date endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                                    String strEndTime=sdf.format(endTime);
                                    String endTimeYear = strEndTime.substring(0,4);
                                    String endTimeMonth = strEndTime.substring(5,7);
                                    String endTimeDay = strEndTime.substring(8,10);
                                    //当月最后一天
                                    String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                                    endTime=sdf.parse(endTimeStr);
                                    Integer orderNumTwo=orderByProduct(userInfoDTO.getData().getCardNo(),
                                            sdf.format(startTime),
                                            sdf.format(endTime),
                                            taskDTO.getStoreProductCodes(),
                                            taskDTO.getDepartProductCodes(),
                                            taskDTO.getLineProductCodes(),
                                            taskDTO.getClassProductCodes(),
                                            taskDTO.getJanProductCodes());
                                    if (orderNumTwo<=0){
                                        //endTime+一个月
                                        endTime= DateUtils.getPlusMonthLastDay(endTime,1);
                                        String nextTime=sdf.format(endTime);
                                        String nextTimeYear = nextTime.substring(0,4);
                                        String nextTimeMonth = nextTime.substring(5,7);
                                        //得到下个月第一天日期
                                        String endTimeNext=nextTimeYear+"-"+nextTimeMonth+"-01 00:00:00";
                                        endTime=sdf.parse(endTimeNext);
                                        startTime=endTime;
                                        continue;
                                    }
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        wrapperRecord.orderByAsc(UserTaskRecord::getCreateTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getReadyNum()*taskDTO.getTotalReadyNum()){
                                                break;
                                            }
                                            //计算出来已有订单数量减去已完成记录，剩余订单数量为当前继续完成的订单数量
                                            Integer num=orderNumTwo-userTaskRecords.size();
                                            if (num<=0){
                                                break;
                                            }
                                            for (int j = 0; j < num; j++) {
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                List<UserTaskRecord> userTaskRecordsTwo =userTaskRecordMapper.selectList(wrapperRecord);
                                                //判断(userTaskRecords.size()+1)/taskDTO.getReadyNum()有没有余数
                                                int userReadySuccessNum=userTaskRecordsTwo.size()/taskDTO.getReadyNum();
                                                if (userReadySuccessNum == userTaskRecordsTwo.get(0).getFinishNum()){//可能会重复发奖励
                                                    if (userReadySuccessNum<taskDTO.getTotalReadyNum()){
                                                        continue;
                                                    }
                                                    break;
                                                }else{
                                                    int allReadySuccessNum=userTaskRecordsTwo.size()/taskDTO.getReadyNum();
                                                    //查询奖励
                                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                    //获取taskRewardList中第actualCycle个奖励
                                                    TaskReward taskReward = taskRewardList.get(allReadySuccessNum-1);
                                                    if (taskReward.getRewardType()==1){//送优惠券
                                                        //发券
                                                        String[] couponIds=taskReward.getCouponIds().split(",");
                                                        for (String couponId : couponIds){
                                                            ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                            receiveCouponVo.setReceiveType(2);
                                                            receiveCouponVo.setCouponId(couponId);
                                                            mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                        }
                                                    }else{//送积分
                                                        //加积分
                                                        mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                    }
                                                    UserTaskRecord userTaskRecordUpdate = new UserTaskRecord();
                                                    userTaskRecordUpdate.setFinishNum(allReadySuccessNum);
                                                    LambdaQueryWrapper<UserTaskRecord> wrapperRecordUpdate = new LambdaQueryWrapper<>();
                                                    wrapperRecordUpdate.eq(UserTaskRecord::getUserId,userId);
                                                    wrapperRecordUpdate.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                                    userTaskRecordMapper.update(userTaskRecordUpdate,wrapperRecordUpdate);
                                                    if (allReadySuccessNum>=taskDTO.getTotalReadyNum()){
                                                        break;
                                                    }
                                                }
                                            }
                                        }else{
                                            for (int j = 0; j < orderNumTwo; j++) {
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                List<UserTaskRecord> userTaskRecordsTwo =userTaskRecordMapper.selectList(wrapperRecord);
                                                //判断(userTaskRecords.size()+1)/taskDTO.getReadyNum()有没有余数
                                                int userReadySuccessNum=userTaskRecordsTwo.size()/taskDTO.getReadyNum();
                                                if (userReadySuccessNum == userTaskRecordsTwo.get(0).getFinishNum()){//可能会重复发奖励
                                                    if (userReadySuccessNum<taskDTO.getTotalReadyNum()){
                                                        continue;
                                                    }
                                                    break;
                                                }else{
                                                    int allReadySuccessNum=userTaskRecordsTwo.size()/taskDTO.getReadyNum();
                                                    //查询奖励
                                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                    //获取taskRewardList中第actualCycle个奖励
                                                    TaskReward taskReward = taskRewardList.get(allReadySuccessNum-1);
                                                    if (taskReward.getRewardType()==1){//送优惠券
                                                        //发券
                                                        String[] couponIds=taskReward.getCouponIds().split(",");
                                                        for (String couponId : couponIds){
                                                            ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                            receiveCouponVo.setReceiveType(2);
                                                            receiveCouponVo.setCouponId(couponId);
                                                            mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                        }
                                                    }else{//送积分
                                                        //加积分
                                                        mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                    }
                                                    UserTaskRecord userTaskRecordUpdate = new UserTaskRecord();
                                                    userTaskRecordUpdate.setFinishNum(allReadySuccessNum);
                                                    LambdaQueryWrapper<UserTaskRecord> wrapperRecordUpdate = new LambdaQueryWrapper<>();
                                                    wrapperRecordUpdate.eq(UserTaskRecord::getUserId,userId);
                                                    wrapperRecordUpdate.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                                    userTaskRecordMapper.update(userTaskRecordUpdate,wrapperRecordUpdate);
                                                    if (allReadySuccessNum>=taskDTO.getTotalReadyNum()){
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                        break;
                                    }
                                    //endTime+一个月
                                    endTime= DateUtils.getPlusMonthLastDay(endTime,1);
                                    String nextTime=sdf.format(endTime);
                                    String nextTimeYear = nextTime.substring(0,4);
                                    String nextTimeMonth = nextTime.substring(5,7);
                                    //得到下个月第一天日期
                                    String endTimeNext=nextTimeYear+"-"+nextTimeMonth+"-01 00:00:00";
                                    endTime=sdf.parse(endTimeNext);
                                    startTime=endTime;
                                }
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            log.info("线下消费任务失败");
        }finally {
            redisService.unlock(taskProduct);
        }
    }

    @Override
    public void taskSuccessRecordFriend(Long inviteUserId) throws ParseException {
        SimpleDateFormat sdfMonth=new SimpleDateFormat("yyyy-MM");
        Long userId=inviteUserId;
        String taskFriend = "task_friend_" + userId;
        boolean lock = redisService.lock(taskFriend, 3);
        if (!lock) {
            return;
        }
        try{
            //查询用户会员信息
            Result<UserSimpleDTO> userInfoDTO = userInfoFeginClient.getUserSimpleInfo(userId);
            Date now=new Date();
            LambdaQueryWrapper<InteractionTask> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(InteractionTask::getStatus, 0);
            wrapper.eq(InteractionTask::getTaskDesc, 4);
            wrapper.and(wq->wq.eq(InteractionTask::getIsTimeRestrict,2)
                    .or().eq(InteractionTask::getIsTimeRestrict,1).lt(InteractionTask::getRestrictTimeStart,now).gt(InteractionTask::getRestrictTimeEnd,now));
            //查询所有在启动的任务
            List<InteractionTask> list=interactionTaskMapper.selectList(wrapper);
            if (!CollectionUtils.isEmpty(list)){
                for (InteractionTask taskDTO : list) {
                    //1限时任务，否则不限时任务
                    if (taskDTO.getIsTimeRestrict()==1){
                        //1一次性任务，2周期性任务
                        if (taskDTO.getReadyCycle().equals(1)){
                            LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                            wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                            wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                            wrapperRecord.gt(UserTaskRecord::getCreateTime,taskDTO.getRestrictTimeStart());
                            wrapperRecord.lt(UserTaskRecord::getCreateTime,taskDTO.getRestrictTimeEnd());
                            List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                            if (!CollectionUtils.isEmpty(userTaskRecords)){
                                if (userTaskRecords.size()>=taskDTO.getReadyNum()){
                                    continue;
                                }else{
                                    UserTaskRecord userTaskRecord = new UserTaskRecord();
                                    userTaskRecord.setUserId(userId);
                                    userTaskRecord.setTaskId(taskDTO.getId());
                                    userTaskRecord.setCreateTime(now);
                                    userTaskRecord.setCreateAt(String.valueOf(userId));
                                    userTaskRecord.setUpdateTime(now);
                                    userTaskRecord.setUpdateAt(String.valueOf(userId));
                                    userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                    userTaskRecordMapper.insert(userTaskRecord);
                                    if (taskDTO.getReadyNum()==userTaskRecords.size()+1){
                                        //查询奖励
                                        List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                        for (TaskReward taskReward : taskRewardList){
                                            if (taskReward.getRewardType()==1){//送优惠券
                                                //发券
                                                String[] couponIds=taskReward.getCouponIds().split(",");
                                                for (String couponId : couponIds){
                                                    ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                    receiveCouponVo.setReceiveType(2);
                                                    receiveCouponVo.setCouponId(couponId);
                                                    mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                }
                                            }else{//送积分
                                                //加积分
                                                mujiOpenApiFeignClient.addPoints(inviteUserId,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                            }
                                        }
                                    }
                                }
                            }else{
                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                userTaskRecord.setUserId(userId);
                                userTaskRecord.setTaskId(taskDTO.getId());
                                userTaskRecord.setCreateTime(now);
                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                userTaskRecord.setUpdateTime(now);
                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                userTaskRecordMapper.insert(userTaskRecord);
                                if (taskDTO.getReadyNum()==1){
                                    //查询奖励
                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                    for (TaskReward taskReward : taskRewardList){
                                        if (taskReward.getRewardType()==1){//送优惠券
                                            //发券
                                            String[] couponIds=taskReward.getCouponIds().split(",");
                                            for (String couponId : couponIds){
                                                ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                receiveCouponVo.setReceiveType(2);
                                                receiveCouponVo.setCouponId(couponId);
                                                mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                            }
                                        }else{//送积分
                                            //加积分
                                            mujiOpenApiFeignClient.addPoints(inviteUserId,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                        }
                                    }
                                }
                            }
                        }else if (taskDTO.getReadyCycle().equals(2)){
                            if (taskDTO.getReayType().equals(readyDay)){
                                //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                                long day = (taskDTO.getRestrictTimeEnd().getTime() - taskDTO.getRestrictTimeStart().getTime()) / (1000 * 60 * 60 * 24);
                                //计算day除以readyDay取整，得到实际周期次数
                                int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                                Date startTime = taskDTO.getRestrictTimeStart();
                                for (int i = 0; i < actualCycle; i++) {
                                    //计算taskDTO.getRestrictTimeStart()加上readyDay天后的日期
                                    Date endTime=DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getReadyNum()){
                                                break;
                                            }else{
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                if (taskDTO.getReadyNum()==userTaskRecords.size()+1){
                                                    //查询奖励
                                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                    for (TaskReward taskReward : taskRewardList){
                                                        if (taskReward.getRewardType()==1){//送优惠券
                                                            //发券
                                                            String[] couponIds=taskReward.getCouponIds().split(",");
                                                            for (String couponId : couponIds){
                                                                ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                                receiveCouponVo.setReceiveType(2);
                                                                receiveCouponVo.setCouponId(couponId);
                                                                mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                            }
                                                        }else{//送积分
                                                            //加积分
                                                            mujiOpenApiFeignClient.addPoints(inviteUserId,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                        }
                                                    }
                                                }
                                            }
                                        }else{
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            if (taskDTO.getReadyNum()==1){
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                for (TaskReward taskReward : taskRewardList){
                                                    if (taskReward.getRewardType()==1){//送优惠券
                                                        //发券
                                                        String[] couponIds=taskReward.getCouponIds().split(",");
                                                        for (String couponId : couponIds){
                                                            ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                            receiveCouponVo.setReceiveType(2);
                                                            receiveCouponVo.setCouponId(couponId);
                                                            mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                        }
                                                    }else{//送积分
                                                        //加积分
                                                        mujiOpenApiFeignClient.addPoints(inviteUserId,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                    }
                                                }
                                            }
                                        }
                                        break;
                                    }
                                    startTime=endTime;
                                }
                            }else{
                                String str=sdf.format(taskDTO.getCreateTime());
                                String firstYear = str.substring(0,4);
                                String firstMonth = str.substring(5,7);
                                String LastStr=sdf.format(taskDTO.getRestrictTimeEnd());
                                String lastYear = LastStr.substring(0,4);
                                String lastMonth = LastStr.substring(5,7);
                                String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                                Date firstDate = sdf.parse(firstDay);
                                Date startTime=null;
                                if (taskDTO.getRestrictTimeStart().after(firstDate)){
                                    startTime=taskDTO.getRestrictTimeStart();
                                }else{
                                    startTime=firstDate;
                                }
                                //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                                int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                                //计算monthNum除以readymonth取整，得到实际周期次数
                                int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                                for (int i = 0; i < actualCycle; i++) {
                                    //得到startTime日期的最后一天
                                    Date endTime= null;
                                    if (i==actualCycle-1){
                                        endTime=taskDTO.getRestrictTimeEnd();
                                    }else{
                                        //得到startTime日期+taskDTO.getReadyMonth()的最后一天
                                        endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                                        String strEndTime=sdf.format(endTime);
                                        String endTimeYear = strEndTime.substring(0,4);
                                        String endTimeMonth = strEndTime.substring(5,7);
                                        String endTimeDay = strEndTime.substring(8,10);
                                        //当月最后一天
                                        String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                                        endTime=sdf.parse(endTimeStr);
                                    }
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getReadyNum()){
                                                break;
                                            }else{
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                if (taskDTO.getReadyNum()==userTaskRecords.size()+1){
                                                    //查询奖励
                                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                    for (TaskReward taskReward : taskRewardList){
                                                        if (taskReward.getRewardType()==1){//送优惠券
                                                            //发券
                                                            String[] couponIds=taskReward.getCouponIds().split(",");
                                                            for (String couponId : couponIds){
                                                                ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                                receiveCouponVo.setReceiveType(2);
                                                                receiveCouponVo.setCouponId(couponId);
                                                                mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                            }
                                                        }else{//送积分
                                                            //加积分
                                                            mujiOpenApiFeignClient.addPoints(inviteUserId,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                        }
                                                    }
                                                }
                                            }
                                        }else{
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            if (taskDTO.getReadyNum()==1){
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                for (TaskReward taskReward : taskRewardList){
                                                    if (taskReward.getRewardType()==1){//送优惠券
                                                        //发券
                                                        String[] couponIds=taskReward.getCouponIds().split(",");
                                                        for (String couponId : couponIds){
                                                            ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                            receiveCouponVo.setReceiveType(2);
                                                            receiveCouponVo.setCouponId(couponId);
                                                            mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                        }
                                                    }else{//送积分
                                                        //加积分
                                                        mujiOpenApiFeignClient.addPoints(inviteUserId,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                    }
                                                }
                                            }
                                        }
                                        break;
                                    }
                                    //endTime+一个月
                                    endTime= DateUtils.getPlusMonthLastDay(endTime,1);
                                    String nextTime=sdf.format(endTime);
                                    String nextTimeYear = nextTime.substring(0,4);
                                    String nextTimeMonth = nextTime.substring(5,7);
                                    //得到下个月第一天日期
                                    String endTimeNext=nextTimeYear+"-"+nextTimeMonth+"-01 00:00:00";
                                    endTime=sdf.parse(endTimeNext);
                                    startTime=endTime;
                                }
                            }
                        }else{
                            if (taskDTO.getReayType().equals(readyDay)){
                                //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                                long day = (taskDTO.getRestrictTimeEnd().getTime() - taskDTO.getRestrictTimeStart().getTime()) / (1000 * 60 * 60 * 24);
                                //计算day除以readyDay取整，得到实际周期次数
                                int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                                Date startTime = taskDTO.getRestrictTimeStart();
                                for (int i = 0; i < actualCycle; i++) {
                                    //得到startTime日期的最后一天
                                    Date endTime= null;
                                    if (i==actualCycle-1){
                                        endTime=taskDTO.getRestrictTimeEnd();
                                    }else{
                                        endTime=DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                                    }
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getTotalReadyNum()){
                                                break;
                                            }else{
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                //获取taskRewardList中第userTaskRecords.size()个奖励
                                                TaskReward taskReward = taskRewardList.get(userTaskRecords.size());
                                                if (taskReward.getRewardType()==1){//送优惠券
                                                    //发券
                                                    String[] couponIds=taskReward.getCouponIds().split(",");
                                                    for (String couponId : couponIds){
                                                        ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                        receiveCouponVo.setReceiveType(2);
                                                        receiveCouponVo.setCouponId(couponId);
                                                        mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                    }
                                                }else{//送积分
                                                    //加积分
                                                    mujiOpenApiFeignClient.addPoints(inviteUserId,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                }
                                            }
                                        }else{
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            //按sortNum正序查询奖励
                                            List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                            TaskReward taskReward = taskRewardList.get(0);
                                            if (taskReward.getRewardType()==1){//送优惠券
                                                //发券
                                                String[] couponIds=taskReward.getCouponIds().split(",");
                                                for (String couponId : couponIds){
                                                    ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                    receiveCouponVo.setReceiveType(2);
                                                    receiveCouponVo.setCouponId(couponId);
                                                    mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                }
                                            }else{//送积分
                                                //加积分
                                                mujiOpenApiFeignClient.addPoints(inviteUserId,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                            }
                                        }
                                        break;
                                    }
                                    startTime=endTime;
                                }
                            }else{
                                String str=sdf.format(taskDTO.getCreateTime());
                                String firstYear = str.substring(0,4);
                                String firstMonth = str.substring(5,7);
                                String LastStr=sdf.format(taskDTO.getRestrictTimeEnd());
                                String lastYear = LastStr.substring(0,4);
                                String lastMonth = LastStr.substring(5,7);
                                String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                                Date firstDate = sdf.parse(firstDay);
                                Date startTime=null;
                                if (taskDTO.getRestrictTimeStart().after(firstDate)){
                                    startTime=taskDTO.getRestrictTimeStart();
                                }else{
                                    startTime=firstDate;
                                }
                                //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                                int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                                //计算monthNum除以readymonth取整，得到实际周期次数
                                int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                                for (int i = 0; i < actualCycle; i++) {
                                    //得到startTime日期的最后一天
                                    Date endTime= null;
                                    if (i==actualCycle-1){
                                        endTime=taskDTO.getRestrictTimeEnd();
                                    }else{
                                        //得到startTime日期+taskDTO.getReadyMonth()的最后一天
                                        endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                                        String strEndTime=sdf.format(endTime);
                                        String endTimeYear = strEndTime.substring(0,4);
                                        String endTimeMonth = strEndTime.substring(5,7);
                                        String endTimeDay = strEndTime.substring(8,10);
                                        //当月最后一天
                                        String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                                        endTime=sdf.parse(endTimeStr);
                                    }
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getTotalReadyNum()){
                                                break;
                                            }else{
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                //获取taskRewardList中第userTaskRecords.size()个奖励
                                                TaskReward taskReward = taskRewardList.get(userTaskRecords.size());
                                                if (taskReward.getRewardType()==1){//送优惠券
                                                    //发券
                                                    String[] couponIds=taskReward.getCouponIds().split(",");
                                                    for (String couponId : couponIds){
                                                        ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                        receiveCouponVo.setReceiveType(2);
                                                        receiveCouponVo.setCouponId(couponId);
                                                        mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                    }
                                                }else{//送积分
                                                    //加积分
                                                    mujiOpenApiFeignClient.addPoints(inviteUserId,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                }
                                            }
                                        }else{
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            //按sortNum正序查询奖励
                                            List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                            TaskReward taskReward = taskRewardList.get(0);
                                            if (taskReward.getRewardType()==1){//送优惠券
                                                //发券
                                                String[] couponIds=taskReward.getCouponIds().split(",");
                                                for (String couponId : couponIds){
                                                    ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                    receiveCouponVo.setReceiveType(2);
                                                    receiveCouponVo.setCouponId(couponId);
                                                    mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                }
                                            }else{//送积分
                                                //加积分
                                                mujiOpenApiFeignClient.addPoints(inviteUserId,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                            }
                                        }
                                        break;
                                    }
                                    //endTime+一个月
                                    endTime= DateUtils.getPlusMonthLastDay(endTime,1);
                                    String nextTime=sdf.format(endTime);
                                    String nextTimeYear = nextTime.substring(0,4);
                                    String nextTimeMonth = nextTime.substring(5,7);
                                    //得到下个月第一天日期
                                    String endTimeNext=nextTimeYear+"-"+nextTimeMonth+"-01 00:00:00";
                                    endTime=sdf.parse(endTimeNext);
                                    startTime=endTime;
                                }
                            }
                        }
                    }else{
                        //1一次性任务，2周期性任务，3周期+阶梯
                        if (taskDTO.getReadyCycle().equals(1)){
                            LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                            wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                            wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                            List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                            if (!CollectionUtils.isEmpty(userTaskRecords)){
                                if (userTaskRecords.size()>=taskDTO.getReadyNum()){
                                    continue;
                                }else{
                                    UserTaskRecord userTaskRecord = new UserTaskRecord();
                                    userTaskRecord.setUserId(userId);
                                    userTaskRecord.setTaskId(taskDTO.getId());
                                    userTaskRecord.setCreateTime(now);
                                    userTaskRecord.setCreateAt(String.valueOf(userId));
                                    userTaskRecord.setUpdateTime(now);
                                    userTaskRecord.setUpdateAt(String.valueOf(userId));
                                    userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                    userTaskRecordMapper.insert(userTaskRecord);
                                    if (taskDTO.getReadyNum()==userTaskRecords.size()+1){
                                        //查询奖励
                                        List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                        for (TaskReward taskReward : taskRewardList){
                                            if (taskReward.getRewardType()==1){//送优惠券
                                                //发券
                                                String[] couponIds=taskReward.getCouponIds().split(",");
                                                for (String couponId : couponIds){
                                                    ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                    receiveCouponVo.setReceiveType(2);
                                                    receiveCouponVo.setCouponId(couponId);
                                                    mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                }
                                            }else{//送积分
                                                //加积分
                                                mujiOpenApiFeignClient.addPoints(inviteUserId,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                            }
                                        }
                                    }
                                }
                            }else{
                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                userTaskRecord.setUserId(userId);
                                userTaskRecord.setTaskId(taskDTO.getId());
                                userTaskRecord.setCreateTime(now);
                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                userTaskRecord.setUpdateTime(now);
                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                userTaskRecordMapper.insert(userTaskRecord);
                                if (taskDTO.getReadyNum()==1){
                                    //查询奖励
                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                    for (TaskReward taskReward : taskRewardList){
                                        if (taskReward.getRewardType()==1){//送优惠券
                                            //发券
                                            String[] couponIds=taskReward.getCouponIds().split(",");
                                            for (String couponId : couponIds){
                                                ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                receiveCouponVo.setReceiveType(2);
                                                receiveCouponVo.setCouponId(couponId);
                                                mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                            }
                                        }else{//送积分
                                            //加积分
                                            mujiOpenApiFeignClient.addPoints(inviteUserId,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                        }
                                    }
                                }
                            }
                        }else if (taskDTO.getReadyCycle().equals(2)){
                            if (taskDTO.getReayType().equals(readyDay)){
                                //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                                String str=sdf.format(taskDTO.getCreateTime());
                                String firstYear = str.substring(0,4);
                                String firstMonth = str.substring(5,7);
                                String firstDay = str.substring(8,10);
                                String startTimeStr=firstYear+"-"+firstMonth+"-"+firstDay+" 00:00:00";
                                Date startTime=sdf.parse(startTimeStr);
                                long day = (new Date().getTime() - startTime.getTime()) / (1000 * 60 * 60 * 24);
                                //计算day除以readyDay取整，得到实际周期次数
                                int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                                for (int i = 0; i < actualCycle; i++) {
                                    //计算taskDTO.getRestrictTimeStart()加上readyDay天后的日期
                                    Date endTime=DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getReadyNum()){
                                                break;
                                            }else{
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                if (taskDTO.getReadyNum()==userTaskRecords.size()+1){
                                                    //查询奖励
                                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                    for (TaskReward taskReward : taskRewardList){
                                                        if (taskReward.getRewardType()==1){//送优惠券
                                                            //发券
                                                            String[] couponIds=taskReward.getCouponIds().split(",");
                                                            for (String couponId : couponIds){
                                                                ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                                receiveCouponVo.setReceiveType(2);
                                                                receiveCouponVo.setCouponId(couponId);
                                                                mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                            }
                                                        }else{//送积分
                                                            //加积分
                                                            mujiOpenApiFeignClient.addPoints(inviteUserId,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                        }
                                                    }
                                                }
                                            }
                                        }else{
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            if (taskDTO.getReadyNum()==1){
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                for (TaskReward taskReward : taskRewardList){
                                                    if (taskReward.getRewardType()==1){//送优惠券
                                                        //发券
                                                        String[] couponIds=taskReward.getCouponIds().split(",");
                                                        for (String couponId : couponIds){
                                                            ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                            receiveCouponVo.setReceiveType(2);
                                                            receiveCouponVo.setCouponId(couponId);
                                                            mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                        }
                                                    }else{//送积分
                                                        //加积分
                                                        mujiOpenApiFeignClient.addPoints(inviteUserId,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                    }
                                                }
                                            }
                                        }
                                        break;
                                    }
                                    startTime=endTime;
                                }
                            }else{
                                String str=sdf.format(taskDTO.getCreateTime());
                                String firstYear = str.substring(0,4);
                                String firstMonth = str.substring(5,7);
                                String LastStr=sdf.format(now);
                                String lastYear = LastStr.substring(0,4);
                                String lastMonth = LastStr.substring(5,7);
                                String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                                Date firstDate = sdf.parse(firstDay);
                                Date startTime=firstDate;
                                //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                                int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                                //计算monthNum除以readymonth取整，得到实际周期次数
                                int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                                for (int i = 0; i < actualCycle; i++) {
                                    //得到startTime日期+taskDTO.getReadyMonth()的最后一天
                                    Date endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                                    String strEndTime=sdf.format(endTime);
                                    String endTimeYear = strEndTime.substring(0,4);
                                    String endTimeMonth = strEndTime.substring(5,7);
                                    String endTimeDay = strEndTime.substring(8,10);
                                    //当月最后一天
                                    String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                                    endTime=sdf.parse(endTimeStr);
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getReadyNum()){
                                                break;
                                            }else{
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                if (taskDTO.getReadyNum()==userTaskRecords.size()+1){
                                                    //查询奖励
                                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                    for (TaskReward taskReward : taskRewardList){
                                                        if (taskReward.getRewardType()==1){//送优惠券
                                                            //发券
                                                            String[] couponIds=taskReward.getCouponIds().split(",");
                                                            for (String couponId : couponIds){
                                                                ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                                receiveCouponVo.setReceiveType(2);
                                                                receiveCouponVo.setCouponId(couponId);
                                                                mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                            }
                                                        }else{//送积分
                                                            //加积分
                                                            mujiOpenApiFeignClient.addPoints(inviteUserId,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                        }
                                                    }
                                                }
                                            }
                                        }else{
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            if (taskDTO.getReadyNum()==1){
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                for (TaskReward taskReward : taskRewardList){
                                                    if (taskReward.getRewardType()==1){//送优惠券
                                                        //发券
                                                        String[] couponIds=taskReward.getCouponIds().split(",");
                                                        for (String couponId : couponIds){
                                                            ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                            receiveCouponVo.setReceiveType(2);
                                                            receiveCouponVo.setCouponId(couponId);
                                                            mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                        }
                                                    }else{//送积分
                                                        //加积分
                                                        mujiOpenApiFeignClient.addPoints(inviteUserId,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                    }
                                                }
                                            }
                                        }
                                        break;
                                    }
                                    //endTime+一个月
                                    endTime= DateUtils.getPlusMonthLastDay(endTime,1);
                                    String nextTime=sdf.format(endTime);
                                    String nextTimeYear = nextTime.substring(0,4);
                                    String nextTimeMonth = nextTime.substring(5,7);
                                    //得到下个月第一天日期
                                    String endTimeNext=nextTimeYear+"-"+nextTimeMonth+"-01 00:00:00";
                                    endTime=sdf.parse(endTimeNext);
                                    startTime=endTime;
                                }
                            }
                        }else{
                            if (taskDTO.getReayType().equals(readyDay)){
                                //计算taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间的天数
                                String str=sdf.format(taskDTO.getCreateTime());
                                String firstYear = str.substring(0,4);
                                String firstMonth = str.substring(5,7);
                                String firstDay = str.substring(8,10);
                                String startTimeStr=firstYear+"-"+firstMonth+"-"+firstDay+" 00:00:00";
                                Date startTime=sdf.parse(startTimeStr);
                                long day = (new Date().getTime() - startTime.getTime()) / (1000 * 60 * 60 * 24);
                                //计算day除以readyDay取整，得到实际周期次数
                                int actualCycle = (int) (day / taskDTO.getReadyDay())+1;
                                for (int i = 0; i < actualCycle; i++) {
                                    //得到startTime日期+taskDTO.getReadyMonth()的最后一天
                                    Date endTime=DateUtils.getPlusDays(startTime,taskDTO.getReadyDay());
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getTotalReadyNum()){
                                                break;
                                            }else{
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                //获取taskRewardList中第userTaskRecords.size()个奖励
                                                TaskReward taskReward = taskRewardList.get(userTaskRecords.size());
                                                if (taskReward.getRewardType()==1){//送优惠券
                                                    //发券
                                                    String[] couponIds=taskReward.getCouponIds().split(",");
                                                    for (String couponId : couponIds){
                                                        ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                        receiveCouponVo.setReceiveType(2);
                                                        receiveCouponVo.setCouponId(couponId);
                                                        mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                    }
                                                }else{//送积分
                                                    //加积分
                                                    mujiOpenApiFeignClient.addPoints(inviteUserId,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                }
                                            }
                                        }else{
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            //按sortNum正序查询奖励
                                            List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                            TaskReward taskReward = taskRewardList.get(0);
                                            if (taskReward.getRewardType()==1){//送优惠券
                                                //发券
                                                String[] couponIds=taskReward.getCouponIds().split(",");
                                                for (String couponId : couponIds){
                                                    ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                    receiveCouponVo.setReceiveType(2);
                                                    receiveCouponVo.setCouponId(couponId);
                                                    mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                }
                                            }else{//送积分
                                                //加积分
                                                mujiOpenApiFeignClient.addPoints(inviteUserId,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                            }
                                        }
                                        break;
                                    }
                                    //endTime+一个月
                                    endTime= DateUtils.getPlusMonthLastDay(endTime,1);
                                    String nextTime=sdf.format(endTime);
                                    String nextTimeYear = nextTime.substring(0,4);
                                    String nextTimeMonth = nextTime.substring(5,7);
                                    //得到下个月第一天日期
                                    String endTimeNext=nextTimeYear+"-"+nextTimeMonth+"-01 00:00:00";
                                    endTime=sdf.parse(endTimeNext);
                                    startTime=endTime;
                                }
                            }else{
                                String str=sdf.format(taskDTO.getCreateTime());
                                String firstYear = str.substring(0,4);
                                String firstMonth = str.substring(5,7);
                                String LastStr=sdf.format(now);
                                String lastYear = LastStr.substring(0,4);
                                String lastMonth = LastStr.substring(5,7);
                                String firstDay = firstYear+"-"+firstMonth+"-01 00:00:00";
                                Date firstDate = sdf.parse(firstDay);
                                Date startTime=firstDate;
                                //获取taskDTO.getRestrictTimeStart()和taskDTO.getRestrictTimeEnd()之间有几个自然月
                                int monthNum = (Integer.parseInt(lastYear)-Integer.parseInt(firstYear))*12+Integer.parseInt(lastMonth)-Integer.parseInt(firstMonth)+1;
                                //计算monthNum除以readymonth取整，得到实际周期次数
                                int actualCycle = (monthNum / taskDTO.getReadyMonth())+1;
                                for (int i = 0; i < actualCycle; i++) {
                                    //得到startTime日期+taskDTO.getReadyMonth()的最后一天
                                    Date endTime= DateUtils.getLastDayOfMonth(startTime.getYear()+1900,startTime.getMonth()+taskDTO.getReadyMonth());
                                    String strEndTime=sdf.format(endTime);
                                    String endTimeYear = strEndTime.substring(0,4);
                                    String endTimeMonth = strEndTime.substring(5,7);
                                    String endTimeDay = strEndTime.substring(8,10);
                                    //当月最后一天
                                    String endTimeStr=endTimeYear+"-"+endTimeMonth+"-"+endTimeDay+" 23:59:59";
                                    endTime=sdf.parse(endTimeStr);
                                    //判断now是否在startTime和endTime之间
                                    if (now.after(startTime) && now.before(endTime)){
                                        LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                                        wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                                        wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                                        wrapperRecord.gt(UserTaskRecord::getCreateTime,startTime);
                                        wrapperRecord.lt(UserTaskRecord::getCreateTime,endTime);
                                        List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                                        if (!CollectionUtils.isEmpty(userTaskRecords)){
                                            if (userTaskRecords.size()>=taskDTO.getTotalReadyNum()){
                                                break;
                                            }else{
                                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                                userTaskRecord.setUserId(userId);
                                                userTaskRecord.setTaskId(taskDTO.getId());
                                                userTaskRecord.setCreateTime(now);
                                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                                userTaskRecord.setUpdateTime(now);
                                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                                userTaskRecordMapper.insert(userTaskRecord);
                                                //查询奖励
                                                List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                                //获取taskRewardList中第userTaskRecords.size()个奖励
                                                TaskReward taskReward = taskRewardList.get(userTaskRecords.size());
                                                if (taskReward.getRewardType()==1){//送优惠券
                                                    //发券
                                                    String[] couponIds=taskReward.getCouponIds().split(",");
                                                    for (String couponId : couponIds){
                                                        ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                        receiveCouponVo.setReceiveType(2);
                                                        receiveCouponVo.setCouponId(couponId);
                                                        mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                    }
                                                }else{//送积分
                                                    //加积分
                                                    mujiOpenApiFeignClient.addPoints(inviteUserId,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                                }
                                            }
                                        }else{
                                            UserTaskRecord userTaskRecord = new UserTaskRecord();
                                            userTaskRecord.setUserId(userId);
                                            userTaskRecord.setTaskId(taskDTO.getId());
                                            userTaskRecord.setCreateTime(now);
                                            userTaskRecord.setCreateAt(String.valueOf(userId));
                                            userTaskRecord.setUpdateTime(now);
                                            userTaskRecord.setUpdateAt(String.valueOf(userId));
                                            userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                            userTaskRecordMapper.insert(userTaskRecord);
                                            //按sortNum正序查询奖励
                                            List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                            TaskReward taskReward = taskRewardList.get(0);
                                            if (taskReward.getRewardType()==1){//送优惠券
                                                //发券
                                                String[] couponIds=taskReward.getCouponIds().split(",");
                                                for (String couponId : couponIds){
                                                    ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                    receiveCouponVo.setReceiveType(2);
                                                    receiveCouponVo.setCouponId(couponId);
                                                    mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                }
                                            }else{//送积分
                                                //加积分
                                                mujiOpenApiFeignClient.addPoints(inviteUserId,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                            }
                                        }
                                        break;
                                    }
                                    //endTime+一个月
                                    endTime= DateUtils.getPlusMonthLastDay(endTime,1);
                                    String nextTime=sdf.format(endTime);
                                    String nextTimeYear = nextTime.substring(0,4);
                                    String nextTimeMonth = nextTime.substring(5,7);
                                    //得到下个月第一天日期
                                    String endTimeNext=nextTimeYear+"-"+nextTimeMonth+"-01 00:00:00";
                                    endTime=sdf.parse(endTimeNext);
                                    startTime=endTime;
                                }
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            log.info("邀请好友任务失败");
        }finally {
            redisService.unlock(taskFriend);
        }
    }

    @Override
    public void threeFirstOrder() throws ParseException {
        Long userId = SecurityContext.getUser().getUid();
        String taskFrist = "task_frist_" + userId;
        boolean lock = redisService.lock(taskFrist, 3);
        if (!lock) {
            return;
        }
        try{
            Date now=new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            LambdaQueryWrapper<InteractionTask> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(InteractionTask::getStatus, 0);
            wrapper.eq(InteractionTask::getTaskDesc, 5);
            wrapper.and(wq->wq.eq(InteractionTask::getIsTimeRestrict,2)
                    .or().eq(InteractionTask::getIsTimeRestrict,1).lt(InteractionTask::getRestrictTimeStart,now).gt(InteractionTask::getRestrictTimeEnd,now));
            //查询所有在启动的任务
            List<InteractionTask> list=interactionTaskMapper.selectList(wrapper);
            if (!CollectionUtils.isEmpty(list)){
                //查询用户会员信息
                Result<UserSimpleDTO> userInfoDTO = userInfoFeginClient.getDbUserSimpleInfo(userId);
//            if (userInfoDTO.getData().getFirstOrderStatus()==1||userInfoDTO.getData().getFirstOrderStatus()==2){
//                return;
//            }
//            boolean flagCheck=checkNewOrder(userInfoDTO.getData().getCardNo());
//            if (flagCheck){
//                userInfoFeginClient.updateFirstOrderStatus(1,null);
//                return;
//            }
                for (InteractionTask taskDTO : list) {
                    Integer orderNum=threeOrderList(userInfoDTO.getData().getCardNo(),taskDTO.getRestrictTimeStart(),taskDTO.getRestrictTimeEnd());
                    if (orderNum<=0){
                        continue;
                    }
                    //先查询是否已经完成首购任务，如果完成，则不再进行后面的逻辑
                    LambdaQueryWrapper<UserTaskRecord> wrapperRecord = new LambdaQueryWrapper<>();
                    wrapperRecord.eq(UserTaskRecord::getUserId,userId);
                    wrapperRecord.eq(UserTaskRecord::getTaskId,taskDTO.getId());
                    wrapperRecord.gt(UserTaskRecord::getCreateTime,taskDTO.getRestrictTimeStart());
                    wrapperRecord.lt(UserTaskRecord::getCreateTime,taskDTO.getRestrictTimeEnd());
                    List<UserTaskRecord> userTaskRecords =userTaskRecordMapper.selectList(wrapperRecord);
                    if (!CollectionUtils.isEmpty(userTaskRecords)){
                        if (userTaskRecords.size()>=taskDTO.getReadyNum()){
//                        userInfoFeginClient.updateFirstOrderStatus(2,taskDTO.getId());
                            continue;
                        }else{
                            if (orderNum>=taskDTO.getReadyNum()){
                                for (int i = 0; i < orderNum; i++) {
                                    UserTaskRecord userTaskRecord = new UserTaskRecord();
                                    userTaskRecord.setUserId(userId);
                                    userTaskRecord.setTaskId(taskDTO.getId());
                                    userTaskRecord.setCreateTime(now);
                                    userTaskRecord.setCreateAt(String.valueOf(userId));
                                    userTaskRecord.setUpdateTime(now);
                                    userTaskRecord.setUpdateAt(String.valueOf(userId));
                                    userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                    userTaskRecordMapper.insert(userTaskRecord);
                                    List<UserTaskRecord> userTaskRecordsTwo =userTaskRecordMapper.selectList(wrapperRecord);
                                    if (userTaskRecordsTwo.size()>=taskDTO.getReadyNum()){
                                        //查询奖励
                                        List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                        for (TaskReward taskReward : taskRewardList){
                                            if (taskReward.getRewardType()==1){//送优惠券
                                                //发券
                                                String[] couponIds=taskReward.getCouponIds().split(",");
                                                for (String couponId : couponIds){
                                                    ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                    receiveCouponVo.setReceiveType(2);
                                                    receiveCouponVo.setCouponId(couponId);
                                                    mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                                }
                                            }else{//送积分
                                                //加积分
                                                mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                            }
                                        }
//                                    userInfoFeginClient.updateFirstOrderStatus(2,taskDTO.getId());
                                        break;
                                    }
                                }
                            }else{
                                //计算出来已有订单数量减去已完成记录，剩余订单数量为当前继续完成的订单数量
                                Integer num=orderNum-userTaskRecords.size();
                                for (int i = 0; i < num; i++) {
                                    UserTaskRecord userTaskRecord = new UserTaskRecord();
                                    userTaskRecord.setUserId(userId);
                                    userTaskRecord.setTaskId(taskDTO.getId());
                                    userTaskRecord.setCreateTime(now);
                                    userTaskRecord.setCreateAt(String.valueOf(userId));
                                    userTaskRecord.setUpdateTime(now);
                                    userTaskRecord.setUpdateAt(String.valueOf(userId));
                                    userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                    userTaskRecordMapper.insert(userTaskRecord);
                                }
                            }
                        }
                    }else{
                        if (orderNum>=taskDTO.getReadyNum()){
                            for (int i = 0; i < orderNum; i++) {
                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                userTaskRecord.setUserId(userId);
                                userTaskRecord.setTaskId(taskDTO.getId());
                                userTaskRecord.setCreateTime(now);
                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                userTaskRecord.setUpdateTime(now);
                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                userTaskRecordMapper.insert(userTaskRecord);
                                List<UserTaskRecord> userTaskRecordsTwo =userTaskRecordMapper.selectList(wrapperRecord);
                                if (userTaskRecordsTwo.size()>=taskDTO.getReadyNum()){
                                    //查询奖励
                                    List<TaskReward> taskRewardList = taskRewardMapper.selectList(new LambdaQueryWrapper<TaskReward>().eq(TaskReward::getTaskId,taskDTO.getId()).orderByAsc(TaskReward::getSortNum));
                                    for (TaskReward taskReward : taskRewardList){
                                        if (taskReward.getRewardType()==1){//送优惠券
                                            //发券
                                            String[] couponIds=taskReward.getCouponIds().split(",");
                                            for (String couponId : couponIds){
                                                ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo();
                                                receiveCouponVo.setReceiveType(2);
                                                receiveCouponVo.setCouponId(couponId);
                                                mujiOpenApiFeignClient.couponReceive(receiveCouponVo);
                                            }
                                        }else{//送积分
                                            //加积分
                                            mujiOpenApiFeignClient.addPoints(null,userInfoDTO.getData().getCardNo(),miniTask,taskReward.getPointsNum(),taskDTO.getTaskName());
                                        }
                                    }
                                    break;
                                }
                            }
                        }else{
                            //剩余订单数量为当前继续完成的订单数量
                            for (int i = 0; i < orderNum; i++) {
                                UserTaskRecord userTaskRecord = new UserTaskRecord();
                                userTaskRecord.setUserId(userId);
                                userTaskRecord.setTaskId(taskDTO.getId());
                                userTaskRecord.setCreateTime(now);
                                userTaskRecord.setCreateAt(String.valueOf(userId));
                                userTaskRecord.setUpdateTime(now);
                                userTaskRecord.setUpdateAt(String.valueOf(userId));
                                userTaskRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                userTaskRecordMapper.insert(userTaskRecord);
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            log.info("首购任务失败");
        }finally {
            redisService.unlock(taskFrist);
        }
    }

    @Override
    public void taskExpireThree() {
        CurrentUserDTO userInfoDTO = new CurrentUserDTO();
        userInfoDTO.setTenantId(1L);
        SecurityContext.setUser(userInfoDTO);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
        Date now=new Date();
        //直接查出来三天内即将到期的任务
        List<InteractionTask> list=interactionTaskMapper.getInteractionTaskList();
        if (CollectionUtils.isEmpty(list)){
            return;
        }
        List<Long> taskIds=list.stream().map(InteractionTask::getId).collect(Collectors.toList());
        List<UserTaskRecord> userTaskRecordList=userTaskRecordMapper.getUserIdByTaskIdList(taskIds);
        if (CollectionUtils.isEmpty(userTaskRecordList)){
            return;
        }
        List<Long> userIds=userTaskRecordList.stream().map(UserTaskRecord::getUserId).collect(Collectors.toList());
        //去重userIds
        userIds=userIds.stream().distinct().collect(Collectors.toList());
        List<Long> finalUserIds = userIds;
        executorService.submit(() -> {
            try {
                semaphore.acquire(); // 获取许可证
                SecurityContext.setUser(userInfoDTO);
                for (Long userId : finalUserIds){
                    SecurityContext.setUser(userInfoDTO);
                    List<InteractionTaskDTO> interactionTaskDTOS = null;
                    try {
                        interactionTaskDTOS = getInteractionTaskListApp(userId);
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                    //未完成列表
                    List<InteractionTaskDTO> incompleteList=new ArrayList<>();
                    if (!CollectionUtils.isEmpty(interactionTaskDTOS)){
                        for (InteractionTaskDTO task : interactionTaskDTOS) {
                            //是否是限时任务
                            if (null != task.getRestrictTimeStart() && null != task.getRestrictTimeEnd()){
                                //判断now是否在限时开始时间和限时结束时间之间
                                if (now.getTime()<=task.getRestrictTimeEnd().getTime()){
                                    if (null != task.getAllReadyNum()){
                                        if (null != task.getReadyNum()){
                                            if (!task.getAllReadyNum().equals(task.getReadyNum())){
                                                incompleteList.add(task);
                                            }
                                        }else{
                                            if (!task.getAllReadyNum().equals(task.getTotalReadyNum())){
                                                incompleteList.add(task);
                                            }
                                        }
                                    }else{
                                        incompleteList.add(task);
                                    }
                                }
                            }else{
                                if (null != task.getAllReadyNum()){
                                    if (null != task.getReadyNum()){
                                        if (!task.getAllReadyNum().equals(task.getReadyNum())){
                                            incompleteList.add(task);
                                        }
                                    }else{
                                        if (!task.getAllReadyNum().equals(task.getTotalReadyNum())){
                                            incompleteList.add(task);
                                        }
                                    }
                                }else{
                                    incompleteList.add(task);
                                }
                            }
                        }
                    }
                    if (CollectionUtils.isEmpty(incompleteList)){
                        continue;
                    }
                    for (InteractionTaskDTO task : incompleteList) {
                        for (InteractionTask task1:list){
                            if (task.getId().equals(task1.getId())){
                                //发送订阅消息
                                SubscribeMsgSendDTO subscribeMsgSendDTO = new SubscribeMsgSendDTO();
                                subscribeMsgSendDTO.setMsgCode(SubscribeMsgEnum.TASK_EXPIRE.getMsgCode());
                                List<Long> uids = new ArrayList<>();
                                uids.add(userId);
                                subscribeMsgSendDTO.setUids(uids);
                                String[] content = new String[3];
                                content[0]=task.getTaskName();
                                content[1]=sdf.format(task.getRestrictTimeEnd());
                                content[2]="完成任务可获得限时礼遇噢";
                                subscribeMsgSendDTO.setContent(content);
                                mpMsgFeignClient.sendMiniappSubscribeMsg(subscribeMsgSendDTO, SecurityContext.getUser().getTenantId());
                                //记录订阅消息
                                SubscriptionMsgVo subscriptionMsgVo = new SubscriptionMsgVo();
                                List<String> msgCode = new ArrayList<>();
                                msgCode.add("taskEnd");
                                subscriptionMsgVo.setMsgCode(msgCode);
                                subscriptionMsgVo.setSendDesc(task.getTaskName()+"，本期活动即将于3日后结束订阅消息提醒");
                                try {
                                    mujiOpenApiFeignClient.add(subscriptionMsgVo);
                                }catch (Exception e){
                                    log.error("记录订阅消息失败"+e.getMessage());
                                }
                                break;
                            }
                        }
                    }
                }
                TimeUnit.MILLISECONDS.sleep(25);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                semaphore.release(); // 任务完成后释放许可证
            }
        });
    }



    @Override
    public void addUserTask(Long taskId, Long uid, Long tenantId) {


        Date now = new Date();
        SimpleDateFormat sdfTable = new SimpleDateFormat("yyyyMM");
        SimpleDateFormat sdfTableYear = new SimpleDateFormat("yyyy");
        String nowDay = sdfTable.format(now);
        String nowYear = sdfTableYear.format(now);
        String dayTable = subTableList(nowDay);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(dayTable)){
            UserTask userTask = new UserTask();
            userTask.setUserId(uid);
            userTask.setTaskId(taskId);
            userTask.setCreateTime(new Date());
            userTask.setTenantId(tenantId);
            userTaskRecordMapper.inserByTable(userTask, nowDay);
        }

        String yearTable = subTableListYear(nowYear);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(yearTable)){
            if (!isUserTaskExistsInAllYearTables(taskId, uid)) {
                UserTask userTask = new UserTask();
                userTask.setUserId(uid);
                userTask.setTaskId(taskId);
                userTask.setCreateTime(new Date());
                userTask.setTenantId(tenantId);
                userTaskRecordMapper.inserByTableYear(userTask, nowYear);
            }
        }

    }

    private String subTableList(String cdpDay) {
        try {
            List<String> tableList = userTaskRecordMapper.querySubTableList();
            boolean hasCurrentTable = false;
            if (!CollectionUtils.isEmpty(tableList)) {
                for (String table : tableList) {
                    String newTableName="t_user_task_month_"+cdpDay;
                    if (newTableName.equals(table)) {
                        hasCurrentTable = true;
                    }
                }
            }
            if (!hasCurrentTable) {
                userTaskRecordMapper.createSubTable(cdpDay);
            }
            return cdpDay;
        } catch (Exception e) {
            log.error("userTaskTable创建失败", e);
            return null;
        }
    }
    private String subTableListYear(String cdpDay) {
        try {
            List<String> tableList = userTaskRecordMapper.querySubTableListYear();
            boolean hasCurrentTable = false;
            if (!CollectionUtils.isEmpty(tableList)) {
                for (String table : tableList) {
                    String newTableName="t_user_task_year_"+cdpDay;
                    if (newTableName.equals(table)) {
                        hasCurrentTable = true;
                    }
                }
            }
            if (!hasCurrentTable) {
                userTaskRecordMapper.createSubTableYear(cdpDay);
            }
            return cdpDay;
        } catch (Exception e) {
            log.error("userTaskTable创建失败", e);
            return null;
        }
    }

    private boolean isUserTaskExistsInAllYearTables(Long taskId, Long uid) {
        int currentYear = Calendar.getInstance().get(Calendar.YEAR);
        for (int year = 2025; year <= currentYear; year++) {
            String yearStr = String.valueOf(year);
            String tableName = "t_user_task_year_" + yearStr;
            Long userTaskExists = userTaskRecordMapper.isUserTaskExists(tableName, taskId, uid);
            if (userTaskExists != null) {
                return true;
            }
        }
        return false;
    }


    public boolean checkLongitude(double longitude1,double latitude1,double longitude2,double latitude2){
        double lambda1 = longitude1 * Math.PI / 180;
        double phi1 = latitude1 * Math.PI / 180;
        double lambda2 = longitude2 * Math.PI / 180;
        double phi2 = latitude2 * Math.PI / 180;

        // Haversine公式
        double a = Math.pow(Math.sin((phi2 - phi1) / 2), 2)
                + Math.cos(phi1) * Math.cos(phi2) * Math.pow(Math.sin((lambda2 - lambda1) / 2), 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        double distance =EARTH_RADIUS * c * 1000;
        if (distance<100){
            return true;
        }
        return false;
    }

    private Map<Long,Integer> participateTaskCount() {
        Map<Long,Integer> userTaskMap = null;
        List<String> tableList = userTaskRecordMapper.querySubTableListYear();
        if (!CollectionUtils.isEmpty(tableList)) {
            List<UserTask> userTaskAll = new ArrayList<>();
            for (String table : tableList) {
                String[] split = table.split("_");
                List<UserTask> userTask = userTaskRecordMapper.participateTaskCount(split[split.length-1]);
                userTaskAll.addAll(userTask);
            }
            if (!CollectionUtils.isEmpty(userTaskAll)){
                userTaskMap=new HashMap<>();
                for (UserTask userTask : userTaskAll){
                    if (userTaskMap.containsKey(userTask.getTaskId())){
                        userTaskMap.put(userTask.getTaskId(),userTaskMap.get(userTask.getTaskId())+userTask.getUserCount());
                    }else {
                        userTaskMap.put(userTask.getTaskId(),userTask.getUserCount());
                    }
                }
            }
        }
        return userTaskMap;
    }

    /**
     * 校验上线后有没有新订单
     * @param cardNo
     * @return
     */
    private Integer threeOrderList(String cardNo,Date startDate,Date endDate) throws ParseException {
        String startTime=sdf.format(startDate);
        String endTime=sdf.format(endDate);
        JSONObject orderListJsonObject =mujiOpenApiFeignClient.orderList(cardNo,
                startTime,endTime,1,1,100).getData();
        Integer orderNum=0;
        if (null != orderListJsonObject && orderListJsonObject.containsKey("items")){
            JSONArray orderList=orderListJsonObject.getJSONArray("items");
            if (!CollectionUtils.isEmpty(orderList)){
                for (Object jsonObject:orderList) {
                    //将jsonObject转为map
                    JSONObject orderJsonObject = JSONObject.parseObject(JSONObject.toJSONString(jsonObject));
                    if (orderJsonObject.getIntValue("order_type")==1 && orderJsonObject.getIntValue("total_fee")>100){
                        orderNum+=1;
                    }
                }
            }
            if(orderListJsonObject.getString("has_more").equals("Y")){
                JSONObject orderListJsonObjectY =mujiOpenApiFeignClient.orderList(cardNo,
                        startTime,endTime,1,2,100).getData();
                if (null != orderListJsonObjectY && orderListJsonObjectY.containsKey("items")){
                    JSONArray orderListY=orderListJsonObjectY.getJSONArray("items");
                    if (!CollectionUtils.isEmpty(orderListY)){
                        for (Object jsonObject:orderListY) {
                            //将jsonObject转为map
                            JSONObject orderJsonObject = JSONObject.parseObject(JSONObject.toJSONString(jsonObject));
                            if (orderJsonObject.getIntValue("order_type")==1 && orderJsonObject.getIntValue("total_fee")>100){
                                orderNum+=1;
                            }
                        }
                    }
                }
            }
        }
        return orderNum;
    }

    /**
     * 校验用户从上线起三年前有没有订单
     * @param cardNo
     * @return
     */
    private boolean checkNewOrder(String cardNo) throws ParseException {
        Date now=sdf.parse(orderTimeFirst);
        //根据用户会员卡号查询三年内订单流水
        String nowStr=sdf.format(now);
        //获取now日期减一天的时间
        Date endTimeOne=DateUtils.getPlusDays(now,-1);
        String lastDayStr = sdf.format(endTimeOne);
        String lastDay=lastDayStr.substring(8,10);
        String year=nowStr.substring(0,4);
        String month=nowStr.substring(5,7);
        String day=nowStr.substring(8,10);
        boolean flag=false;
        configFirst:for (int i = 1; i <= 3; i++) {
            String startTime=Integer.valueOf(year)-i+"-"+month+"-"+lastDay+" 00:00:00";
            String endTime="";
            if (i==1){
                endTime=nowStr;
            }else{
                endTime=Integer.valueOf(year)-i+1+"-"+month+"-"+day+" 23:59:59";
            }
            JSONObject orderListJsonObject =mujiOpenApiFeignClient.orderList(cardNo,
                    startTime,endTime,1,1,100).getData();
            if (null != orderListJsonObject && orderListJsonObject.containsKey("items")){
                JSONArray orderList=orderListJsonObject.getJSONArray("items");
                if (!CollectionUtils.isEmpty(orderList)){
                    for (Object jsonObject:orderList) {
                        //将jsonObject转为map
                        JSONObject orderJsonObject = JSONObject.parseObject(JSONObject.toJSONString(jsonObject));
                        if (orderJsonObject.getInteger("order_type")==1){
                            flag = true;
                            break configFirst;
                        }
                    }
                }
                if(orderListJsonObject.getString("has_more").equals("Y")){
                    JSONObject orderListJsonObjectY =mujiOpenApiFeignClient.orderList(cardNo,
                            startTime,endTime,1,2,100).getData();
                    if (null != orderListJsonObjectY && orderListJsonObjectY.containsKey("items")){
                        JSONArray orderListY=orderListJsonObjectY.getJSONArray("items");
                        if (!CollectionUtils.isEmpty(orderListY)){
                            for (Object jsonObject:orderListY) {
                                //将jsonObject转为map
                                JSONObject orderJsonObject = JSONObject.parseObject(JSONObject.toJSONString(jsonObject));
                                if (orderJsonObject.getInteger("order_type")==1){
                                    flag = true;
                                    break configFirst;
                                }
                            }
                        }
                    }
                }
            }
        }
        return flag;
    }

    /**
     * 查询任务期限内有没有购买指定商品订单
     * @return
     */
    private Integer orderByProduct(String memberCode,
                                   String startTime,
                                   String endTime,
                                   String deptId,
                                   String depaId,
                                   String lineId,
                                   String classId,
                                   String janId) {
        List<String> orderList= exchangeOrderFeignClient.sftpOrderList(memberCode,startTime,endTime,deptId,depaId,lineId,classId,janId);
        Integer orderNum=0;
        if (!CollectionUtils.isEmpty(orderList)){
            orderNum=orderList.size();
        }
        return orderNum;
    }
}
