
.van-icon-arrow {
  color: #BBBBBB;
}

.privacy-custom-container {
  position: relative;
  // flex: 1;
  height: 950rpx;
  // overflow-y: auto;
  // overflow-x: hidden;

  margin: 91rpx 49rpx 0;
  // width: calc(100% - 98rpx);
  width: auto;
}

.custom-content {
  width: 100%;
  min-height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  display: flex;
  flex-direction: column; /* 内容上下排列 */
  align-items: center;
  justify-content: center;
  // position: absolute;
  margin-top: -152rpx;
  padding-bottom: env(safe-area-inset-bottom);
  // z-index: -1;
}

.custom-select-action.van-popup {
  background-color: transparent !important;
}

/* 固定底部按钮 */
.button-box {
  flex-shrink: 0;
  position: sticky;
  bottom: 0;
  left: 0;
  padding: 24rpx 40rpx 20+env(safe-area-inset-bottom);
  width: 100%;
  box-sizing: border-box;
  background: transparent; /* 背景透出 */
}

.select-confirm {
  width: 100%; /* 让按钮自己也占满容器宽度 */
  color: white;
  background-color: #3C3C43;
  border-radius: 8rpx;
}

.privacy-info-image {
  height: 2800rpx;
  width: 750rpx;
}