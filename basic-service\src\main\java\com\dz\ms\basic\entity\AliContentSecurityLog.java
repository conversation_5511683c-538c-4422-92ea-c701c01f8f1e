package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description:
 * @Author: txt
 * @Date: 2022/3/15 16:41
 * @Version: 1.0
 */
@Getter
@Setter
@NoArgsConstructor
@Table("阿里内容安全日志")
@TableName(value = "ali_content_security_log")
public class AliContentSecurityLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "配置ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Columns(type = ColumnType.TINYINT, length = 3, defaultValue = "0", isNull = false, comment = "类型 1文字 2图片 3视频")
    private Integer taskType;

    @Columns(type = ColumnType.TINYINT, length = 3, defaultValue = "0", isNull = false, comment = "多态模型类型 1、打卡")
    private Integer modelType;

    @Columns(type = ColumnType.BIGINT, length = 19, defaultValue = "0", isNull = false, comment = "多态模型ID 1、打卡 ID")
    private Long modelId;

    @Columns(type = ColumnType.TEXT, length = 0, isNull = true, comment = "入参")
    private String inputParameter;

    @Columns(type = ColumnType.TEXT, length = 0, isNull = true, comment = "出参")
    private String outputParameter;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "租户ID")
    private Long tenantId;

    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;

    public AliContentSecurityLog(Integer taskType, Integer modelType, Long modelId, String inputParameter, String outputParameter) {
        this.taskType = taskType;
        this.modelType = modelType;
        this.modelId = modelId;
        this.inputParameter = inputParameter;
        this.outputParameter = outputParameter;
    }
}
