package com.dz.ms.user.controller;

import com.dz.common.base.dto.BaseDTO;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.user.CouponsDetailDTO;
import com.dz.common.core.dto.user.CouponsListAllDTO;
import com.dz.common.core.dto.user.CouponsListDTO;
import com.dz.common.core.dto.user.MyMsgDTO;
import com.dz.common.core.dto.user.ReceiveCouponDTO;
import com.dz.common.core.dto.user.RightsReceiveRecordDTO;
import com.dz.common.core.vo.MyMsgVo;
import com.dz.ms.user.service.CouponService;
import com.dz.ms.user.service.MyMsgService;
import com.dz.ms.user.vo.CouponSelectVo;
import com.dz.ms.user.vo.ReceiveCouponVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.net.MalformedURLException;
import java.text.ParseException;
import java.util.List;

@Api(tags = "我的消息")
@RestController
public class MyMsgController {

    @Resource
    private MyMsgService myMsgService;


    @ApiOperation("我的所有消息列表")
    @PostMapping(value = "/app/msg/list")
    public Result<PageInfo<MyMsgDTO>> msgList(@RequestBody BaseDTO param) {
        Result<PageInfo<MyMsgDTO>> result = new Result<>();
        result.setData(myMsgService.msgList(param));
        return result;
    }

    @ApiOperation("首页置顶消息")
    @GetMapping(value = "/app/un/read/msg/list")
    public Result<MyMsgDTO> unReadMsgList() {
        Result<MyMsgDTO> result = new Result<>();
        result.setData(myMsgService.unReadMsgList());
        return result;
    }

    @ApiOperation("我的消息列表未读角标数量")
    @GetMapping(value = "/app/msg/num")
    public Result<Integer> msgNum() {
        Result<Integer> result = new Result<>();
        result.setData(myMsgService.msgNum());
        return result;
    }

    @ApiOperation("添加我的消息")
    @PostMapping(value = "/app/msg/add")
    public Result<Object> add(@RequestBody MyMsgVo msgCode) throws ParseException {
        Result<Object> result = new Result<>();
        myMsgService.add(msgCode);
        return result;
    }

    @ApiOperation("已读我的消息")
    @GetMapping(value = "/app/msg/read")
    public Result<Object> read(@RequestParam("id") Long id) {
        Result<Object> result = new Result<>();
        myMsgService.read(id);
        return result;
    }

    @ApiOperation("同步历史消息")
    @PostMapping(value = "/app/msg/sync")
    public Result<Object> sync(@RequestBody MyMsgVo msgCode, HttpServletResponse response) throws MalformedURLException {
        Result<Object> result = new Result<>();
        myMsgService.sync(msgCode,response);
        return result;
    }
}
