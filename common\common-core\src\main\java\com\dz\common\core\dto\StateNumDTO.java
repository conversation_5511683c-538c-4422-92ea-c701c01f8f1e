package com.dz.common.core.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/8/8
 */
@Data
@ApiModel("状态、数量通用DTO")
@NoArgsConstructor
@AllArgsConstructor
public class StateNumDTO {

    @ApiModelProperty("状态")
    private Integer state;

    @ApiModelProperty(value = "是否需要预约0否 1是", hidden = true)
    private Integer isBooking;

    @ApiModelProperty("数量")
    private Integer number;
}
