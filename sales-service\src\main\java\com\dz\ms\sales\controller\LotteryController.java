package com.dz.ms.sales.controller;


import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.fegin.sales.LotteryFeignClient;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.ms.sales.dto.LotteryPrizesDTO;
import com.dz.ms.sales.dto.UserLotteryBasicDTO;
import com.dz.ms.sales.dto.UserLotteryPrizesDTO;
import com.dz.ms.sales.service.LotteryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/29
 */
@Api(tags = "奖品信息")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class LotteryController implements LotteryFeignClient {

    @Resource
    private LotteryService lotteryService;

    @ApiOperation("获取奖品列表")
    @GetMapping(value = "/app/lottery/list")
    public Result<List<LotteryPrizesDTO>> getPrizes(@RequestParam("campaignCode") String campaignCode) {
        Result<List<LotteryPrizesDTO>> result = new Result<>();
        List<LotteryPrizesDTO> prizes = lotteryService.getPrizes(campaignCode);
        result.setData(prizes);
        return result;
    }


    @ApiOperation("抽奖")
    @PostMapping(value = "/app/lottery")
    public Result<LotteryPrizesDTO> lottery(@RequestBody LotteryPrizesDTO param) {
        Result<LotteryPrizesDTO> result = new Result<>();
        CurrentUserDTO user = SecurityContext.getUser();
        LotteryPrizesDTO prizes = lotteryService.lottery(param.getCampaignCode(), user.getTenantId(), user.getUid());
        result.setData(prizes);
        return result;
    }


    @GetMapping(value = "/app/lottery/user/basic")
    @ApiOperation("获取用户抽奖基础信息")
    public Result<UserLotteryBasicDTO> getUserLotteryBasic(@RequestParam("campaignCode") String campaignCode) {
        Result<UserLotteryBasicDTO> result = new Result<>();
        UserLotteryBasicDTO dto = lotteryService.getUserLotteryBasic(campaignCode);
        result.setData(dto);
        return result;
    }

    @GetMapping(value = "/app/lottery/user/get_prizes")
    @ApiOperation("获取用户抽奖记录")
    public Result<PageInfo<UserLotteryPrizesDTO>> getUserLotteryPrizes(@RequestParam("campaignCode") String campaignCode, @RequestParam("pageNum") Integer pageNum, @RequestParam("pageSize") Integer pageSize) {
        Result<PageInfo<UserLotteryPrizesDTO>> result = new Result<>();

        PageInfo<UserLotteryPrizesDTO> list = lotteryService.getUserLotteryPrizes(campaignCode, pageNum == null ? 1 : pageNum, pageSize == null ? 10 : pageSize);
        result.setData(list);
        return result;
    }

    @ApiOperation("分享")
    @PostMapping("/app/lottery/user/share")
    public Result<Boolean> share(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        CurrentUserDTO user = SecurityContext.getUser();
        Boolean flag = lotteryService.share(user.getTenantId(), user.getUid(), param.getCode());
        result.setData(flag);
        return result;
    }

    @ApiOperation("刷新抽奖库存")
    @PostMapping("/remote/lottery/inventory")
    public Result<Void> refreshLotteryInventory(@RequestParam("jobParams") String jobParams) {
        lotteryService.refreshLotteryInventory(jobParams);
        return new Result<>();
    }

}
