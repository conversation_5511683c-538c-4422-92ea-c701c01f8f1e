package com.dz.common.core.dto.user;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 我的积分流水DTO
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "我的积分流水")
public class MyPointsRecordsDTO extends BaseDTO {

    @ApiModelProperty(value = "积分增/减名称（打卡门店得积分/积分兑换）")
    private String pointsName;
    @ApiModelProperty(value = "获取积分时间/消费积分时间")
    private String obtainTime;
    @ApiModelProperty(value = "积分过期时间")
    private String expireTime;
    @ApiModelProperty(value = "积分增减数量")
    private String pointsNum;
    @ApiModelProperty(value = "变动类型 1增加 2扣减")
    private Integer changeType;
    @ApiModelProperty(value = "积分流水编号")
    private String bonusSn;
    @ApiModelProperty(value = "积分类型 1任务 2非任务")
    private Integer pointsType=2;

}
