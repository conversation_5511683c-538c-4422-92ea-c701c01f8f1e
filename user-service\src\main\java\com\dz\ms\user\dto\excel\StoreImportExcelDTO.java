package com.dz.ms.user.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.dz.ms.user.utils.StoreStateConverter;
import com.dz.ms.user.utils.StoreStatusConverter;
import com.dz.ms.user.utils.StoreTypeConverter;
import lombok.Data;

/**
 * 门店导入信息
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/5/9 10:36
 */
@Data
public class StoreImportExcelDTO {

    @ExcelProperty("专柜编码")
    private String storeCode;
    @ExcelProperty("专柜名称")
    private String storeName;
    @ExcelProperty("店铺联系方式")
    private String contactPhone;
    @ExcelProperty(value = "门店类型", converter = StoreTypeConverter.class)
    private Integer type;
    @ExcelProperty("营业时间")
    private String businessHours;
    @ExcelProperty(value = "营业状态", converter = StoreStateConverter.class)
    private Integer state;
    @ExcelProperty("专柜地址")
    private String address;
    @ExcelProperty("城市")
    private String city;
    @ExcelProperty("服务人数")
    private Integer serveNum;

}
