<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.sales.mapper.SignInUserDetailMapper">

    <select id="selectByMany" resultType="com.dz.ms.sales.dto.CrmSignInUserDTO">

        select
        id, sign_in_user_id, sign_in_date, campaign_code, channel_one,
        channel_two, uid, openid, unionid, username, mobile, card_no, days, material_url, expectation,
        comfort, moisturize, absorption, persistence, age_bracket, improve, satisfaction, feeling, score,
        purchase, recommend, state, supplement_flag, sign_in_time, supplement_time, is_deleted,
        tenant_id, creator, created, modifier, modified

        from sign_in_user_detail
        <where>
            state = 1 and supplement_flag = 0 and is_deleted = 0 and fail_flag = 0
            <if test="param.days != null">
                and days = #{param.days}
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectSignInCountByDays" resultType="com.dz.ms.sales.dto.SignInUserDetailDTO">
        select days, count(1) as num from sign_in_user_detail where is_deleted = 0 group by days
    </select>


    <select id="missingCardSignInUserDetail" resultType="com.dz.ms.sales.entity.SignInUserDetail">
        SELECT sud.uid, sud.sign_in_user_id,
        DATEDIFF( CURDATE(), su.sign_in_start_time )  AS total_days,
        IFNULL(COUNT( sud.id ), 0) AS actual_days,
        SUM( CASE WHEN sud.supplement_Flag = 1 THEN 1 ELSE 0 END ) AS supplement_count
        FROM sign_in_user su
        LEFT JOIN sign_in_user_detail sud ON su.id = sud.sign_in_user_id
        AND sud.sign_in_date &lt;= CURDATE()
        WHERE su.is_deleted = 0 and su.state = 0 and sud.is_deleted = 0 and sud.fail_flag = 0 AND su.campaign_code = #{campaignCode}
        GROUP BY sud.uid
        HAVING
        <!--        加一是为了去掉开启打卡的数据，days=0       -(actual_days-1) = - actual_days + 1        -->
        ((case when total_days > #{signInTimes} then #{signInTimes} else total_days end) + 1 - actual_days) + supplement_count > #{repairSignInTimes}
    </select>

    <update id="signInUserDetailFail">
        update sign_in_user_detail set fail_flag = 1, modified = now(), modifier = #{userId} where sign_in_user_id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
