package com.dz.common.core.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 创建订单DTO
 */
@Data
public class OrderCreateParamDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "跳转来源 0-立即购买 1-普通购物车")
    private Integer source;

    @ApiModelProperty(value = "用户备注")
    private String remark;

    @ApiModelProperty(value = "商品集合")
    private List<OrderCreateProductDTO> productList;

}
