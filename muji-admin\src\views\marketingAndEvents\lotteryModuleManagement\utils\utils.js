// a-input-number 小数转换
export function inputNumberFormatter(value) {
  return `${value}`.replace(/(?!\.)(\D)/g, '').replace(/^(\d*\.\d{0,2}).*$/, '$1')
}
export function inputNumberParser(value) {
  return value.replace(/\$\s?|(,*)/g, '')
}
// a-input-number parser 整数
export function inputNumberParserInteger(value, d = '') {
  let n = parseInt(value)
  return typeof n === 'number' && !isNaN(n) ? n : d
}
