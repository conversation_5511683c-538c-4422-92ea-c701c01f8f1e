<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.product.mapper.MaterialMapper">

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
        id,
  	    type,
  	    product_id,
  	    product_code,
  	    used_type,
  	    img_url,
  	    img_name,
  	    tenant_id,
  	    creator,
  	    created,
  	    modified,
  	    modifier,
  	    is_deleted
	</sql>

	<insert id="insertBatch">
		insert into material (type,
		product_id,product_code,used_type,img_url,img_name,tenant_id,creator,created,modified,modifier)
		values
		<foreach collection="list" item="item" separator=",">
			(#{item.type},#{item.productId},#{item.productCode},#{item.usedType},#{item.imgUrl},#{item.imgName},#{item.tenantId},#{item.creator},now(),now(),#{item.modifier})
		</foreach>
	</insert>

	<!-- 查询示例 -->
	<select id="selectDemo" parameterType="java.lang.Long" resultType="com.dz.ms.product.entity.Material">
		select
		<include refid="Base_Column_List"/>
		from material
		where id = #{id,jdbcType=BIGINT}
		AND tenant_id = #{tenantId}
	</select>

</mapper>
