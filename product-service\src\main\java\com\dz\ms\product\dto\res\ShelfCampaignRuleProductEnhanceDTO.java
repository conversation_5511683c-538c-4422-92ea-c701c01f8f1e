package com.dz.ms.product.dto.res;

import com.dz.ms.product.dto.ShelfCampaignRuleProductDTO;
import com.dz.ms.product.dto.TagInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 营销活动规则关联的货架商品增强数据
 *
 * @author: fei
 * @date: 2024/12/09 11:39
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "营销活动规则关联的货架商品增强数据")
public class ShelfCampaignRuleProductEnhanceDTO extends ShelfCampaignRuleProductDTO {

    @ApiModelProperty(value = "商品主图")
    private String shelfImg;
    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "商品类型 1实物商品 2电子券")
    private Integer pdType;
    @ApiModelProperty(value = "商品标签列表")
    private List<TagInfoDTO> tagList;
    @ApiModelProperty(value = "角标名称列表")
    private List<Long> superscriptIdList;
    @ApiModelProperty(value = "角标名称列表")
    private List<String> superscriptNameList;

}
