<template>
  <div class="header-title">富文本内容</div>
  <a-form-item>
    <richText v-model:value="data.content" heightContent="max-height: 500px;overflow-y: scroll !important;"></richText>
  </a-form-item>
  <div class="header-title">颜色设置</div>
  <a-form-item label="背景色">
    <Color color="rgba(0,0,0,1)" :value="data.bgColor" @changeColor="changeColor"></Color>
  </a-form-item>
  <!-- <a-form-item label="字体颜色">
    <Color color="rgba(0,0,0,1)" :value="data.fontColor" @changeColor="changeColor1"></Color>
  </a-form-item> -->
  <div class="header-title">组件样式</div>
  <a-form-item label="边距" :labelCol="{ width: '50px' }">
    <a-space>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingTop" addon-before="上"
          addon-after="px"></a-input-number>
      </a-form-item>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingBottom" addon-before="下"
          addon-after="px"></a-input-number>
      </a-form-item>
    </a-space>
    <a-space>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingLeft" addon-before="左"
          addon-after="px"></a-input-number>
      </a-form-item>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingRight" addon-before="右"
          addon-after="px"></a-input-number>
      </a-form-item>
    </a-space>
  </a-form-item>
</template>
<script setup>
import richText from "@/components/richText/index.vue";

const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  // 所有组件数组
  components: {
    type: Array,
    default() {
      return []
    }
  },
  // 当前组件数据
  data: {
    type: Object,
    default() {
      return {}
    }
  },
  // 组件展示的宽度
  width: {
    type: Number,
    default: 750,
  },

})

// 修改颜色
const changeColor = async (color) => {
  props.data.bgColor = color
}

const changeColor1 = async (color) => {
  props.data.fontColor = color
}

</script>

<style></style>
