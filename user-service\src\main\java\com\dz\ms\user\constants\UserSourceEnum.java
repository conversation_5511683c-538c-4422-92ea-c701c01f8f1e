package com.dz.ms.user.constants;

/**
 * 用户注册来源
 * @Author: Handy
 * @Date: 2022/2/2 17:15
 */
public enum UserSourceEnum {
    TMALL(1,"天猫"),
    WEMALL(2,"微商城"),
    STORE(3,"门店"),
    MEMBER(4,"会员中心");

    /** 来源ID */
    private Integer id;
    /** 来源名称 */
    private String name;

    UserSourceEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }
}
