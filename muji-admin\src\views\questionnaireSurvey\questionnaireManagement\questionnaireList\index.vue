<template>

  <layout>
    <template v-slot:header>
      <searchForm :formParams="formParams" @cancel="resetData" @ok="refreshData" :disabled="!$hasPermission('questionnaireList:search')">
        <a-form-item label="问卷名称">
          <a-input placeholder="请输入" v-model:value="formParams.title" allow-clear></a-input>
        </a-form-item>
        <a-form-item label="开启时间">
          <a-range-picker v-model:value="formParams.time" style="width: 350px" :show-time="{ defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')] }" format="YYYY-MM-DD HH:mm:ss" valueFormat="YYYY-MM-DD HH:mm:ss" />
        </a-form-item>
        <!-- <a-form-item label="前台展示状态">
          <a-select v-model:value="formParams.state" style="width: 180px" placeholder="请选择" allow-clear>
            <a-select-option value="">全部</a-select-option>
            <a-select-option :value="0">不展示</a-select-option>
            <a-select-option :value="1">展示中</a-select-option>
          </a-select>
        </a-form-item> -->
        <a-form-item label="问卷状态">
          <a-select v-model:value="formParams.state" style="width: 180px" placeholder="请选择" allow-clear>
            <a-select-option :value="0">停用中</a-select-option>
            <a-select-option :value="1">启用中</a-select-option>
          </a-select>
        </a-form-item>

      </searchForm>
    </template>
    <template v-slot:topRight>
      <a-space>
        <a-button type="primary" :disabled="!$hasPermission('questionnaireList:add')" @click="handleAdd"> + 新增问卷</a-button>
      </a-space>

    </template>
    <template v-slot="{ height }">
      <a-table :indentSize="20" row-key="id" :scroll="{ scrollToFirstRowOnChange: true, y: height - 88, x: '100%' }" :dataSource="dataSource" :columns="tableHeader" :pagination="pagination" :loading="loading" @change="loadData">
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'index'">
            {{(pagination.current-1)*pagination.pageSize+1+index}}
          </template>
          <template v-if="column.key === 'time'">
            <span>{{ record.startTime }}<br />{{ record.endTime }}</span>
          </template>
          <template v-if="column.key === 'scene'">
            <span>{{['其他','预约成功后','核销成功后'][record.scene]}}</span>
          </template>
          <template v-if="column.key === 'state'">
            <a-tag v-if="record.state==0" color="red">停用中</a-tag>
            <a-tag v-else color="green">启用中</a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-button type="link" @click="updateState(record,record.state?0:1)" style="width:60px" :disabled="!$hasPermission('questionnaireList:state')">{{record.state?'停用':'启用'}}</a-button>
            <a-divider type="vertical" />
            <a-button type="link" @click="handlelook(record)" :disabled="!$hasPermission('questionnaireList:info')">详情</a-button>
            <a-divider type="vertical" v-if="record.state == 0"/>
            <a-button type="link" @click="handleEdit(record)" v-if="record.state == 0" :disabled="!$hasPermission('questionnaireList:edit')">编辑</a-button>
            <a-divider type="vertical" />
            <a-button type="link" @click="handleCopy(record)" :disabled="!$hasPermission('questionnaireList:copy')">复制</a-button>
          </template>
        </template>
      </a-table>
    </template>
  </layout>
  <addQuestionPackage :visible="visible" @ok="updateList" @cancel="visible = false" :id="id" :type="type" />
</template>
<script setup>
import addQuestionPackage from './components/addQuestionPackage.vue'
import { useRoute, useRouter } from 'vue-router'
import { usePagination, useRequest, } from 'vue-request';
import { npsList,npsUpdateState,  crowdDelete, crowdUpdate_state } from '@/http/index.js'
import { message, Modal } from "ant-design-vue";
import { downloadExcel } from '@/utils/tools.js'
import dayjs from 'dayjs'
// 分页参数
let pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ["bottomLeft"],
  };
});
// 表格分页数据获取
const total = ref(0)


const { formParams, tableHeader, visible, id, type } = toRefs(reactive({


  id: '',
  type: 0,// 0-新增 1-编辑  2-查看
  visible: false,
  formParams: {
    title: '',
    time: [],
    openState: null,
    scene: null,
    state: null
  },

  tableHeader: [
    {
      title: '序',
      key: 'index',
      align: 'center',
      width: 80
    },
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      align: 'center',
    },
    {
      title: '问卷名称',
      dataIndex: 'title',
      width: 150,
      align: 'center',
      ellipsis: true
    },
    {
      title: '问卷开放时间段',
      key: 'time',
      width: 220,
      align: 'center',
    },
    // {
    //   title: '完成填写人数',
    //   dataIndex: 'finishNum',
    //   align: 'center',
    //   width: 90,
    // },
    {
      title: '问卷状态',
      key: 'state',
      dataIndex: 'state',
      align: 'center',
      width: 80,
    },
    // {
    //   title: '前台展示状态',
    //   key: 'state1',
    //   align: 'center',
    //   width: 100,
    // },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      align: 'left',
      width: 200,
      fixed: 'right'
    },
  ]
})
);
// 处理参数
const getParams = () => {
  let params = { ...formParams.value }
  params.startTime = params.time ? params.time[0] : ''
  params.endTime = params.time ? params.time[1] : ''
  delete params.time
  return params
}
const { data: dataSource, run, loading, pageSize, current, refresh } = usePagination((param) => {
  // 这里修改参数
  // return resignationInheritanceList({ ...param, ...getParams() })
  return npsList({ ...param, ...getParams() })
},
  {
    manual: false, // 修改为false,让其自动执行
    pagination: {
      currentKey: 'pageNum',  // 通过该值指定接口 当前页数 参数的属性值
      pageSizeKey: 'pageSize',// 通过该值指定接口 每页获取条数 参数的属性值
      totalKey: 'data.data.count',  // 指定 data 中 total 属性的路径
    },
    formatResult: res => { // 返回数据格式化
      total.value = res.data.count
      return res.data.list
    }
  });

function updateList(value) {
  visible.value = false;
  // 新增 重置搜索数据
  if (!value) {
    resetData();
  } else {
    // 查看、编辑 不需要清空搜索数据 直接刷新
    refresh();
  }
}
const handleAdd = () => {
  visible.value = true
  type.value = 0
  id.value = ''
}
const handleEdit = (record) => {
  visible.value = true
  type.value = 2
  id.value = record.id
}
const handleCopy = (record) => {
  visible.value = true
  type.value = 3
  id.value = record.id
}
const handlelook = (record) => {
  visible.value = true
  type.value = 1
  id.value = record.id
}
const addQuestionnaire = (record, type) => {
  visible.value = true
  type.value = type
  id.value =  record?.id
}
//导出
function exportData (record) {
  // nps_export({ id: record.id }).then(res => {
  //   downloadExcel(res.data, res.name)
  // })
}
function updateState (record, state) {
  npsUpdateState({ id: record.id, state }).then(res => {
    if (res.code === 0) {
      message.success('更新状态成功')
      resetData()
    }
  })
}
// 加载数据
const loadData = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}


// 刷新数据
const refreshData = () => {
  run({ pageNum: 1, pageSize: pageSize.value })
};

// 重置数据
const resetData = () => {
  formParams.value = {
    title: '',
    time: [],
    openState: null,
    scene: null,
    state: null
  }
  refreshData()
}
</script>
