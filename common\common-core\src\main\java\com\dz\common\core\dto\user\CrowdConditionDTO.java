package com.dz.common.core.dto.user;
import java.util.Date;
import java.util.List;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 人群包条件DTO
 * @author: yibo
 * @date:   2024/11/18 13:53
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "人群包条件")
public class CrowdConditionDTO extends BaseDTO {

    @ApiModelProperty(value = "主键id")
    private Long id;
    @ApiModelProperty(value = "人群包id(父级)")
    private Long crowdId;
    @ApiModelProperty(value = "条件判断 0首位 1或 2且")
    private Integer conditionJudge;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    @ApiModelProperty(value = "修改人")
    private Long modifier;
    @ApiModelProperty(value = "租户id")
    private Long tenantId;
    @ApiModelProperty(value = "排序")
    private Integer sort;
    @ApiModelProperty(value = "人群包横向条件")
    private List<CrowdConditionRowDTO> crowdConditionRowList;
}
