package com.dz.ms.adaptor.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Table;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 定时任务运行记录表
 * @author: 
 * @date:   2025/03/17 11:26
 */
@Data
@NoArgsConstructor
@ToString
@Table("定时任务运行记录表")
@TableName(value = "scheduled_task_config_log")
public class ScheduledTaskConfigLog {
    
    private static final long serialVersionUID = 1L;
    
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * task配置表id
     */
    private Long taskConfigId;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * Cron表达式
     */
    private String cronExpression;
    /**
     * 参数
     */
    private String param;
    /**
     * 执行时间
     */
    private Long runTime;
    /**
     * 状态(0进行中 1成功 2失败)
     */
    private Integer status;
    /**
     * 创建时间
     */
    private Date createdAt;
    /**
     * 更新时间
     */
    private Date updatedAt;
    /**
     * 租户ID
     */
    private Long tenantId;

}
