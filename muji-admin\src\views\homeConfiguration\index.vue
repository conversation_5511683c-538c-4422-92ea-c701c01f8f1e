<template>
  <layout>
    <template v-slot="{ height }">
      <a-spin :spinning="loading" style="height:100%;">
        <div :style="{ height: `${height + 'px'}`, overflowY: 'auto !important', }">
          <a-form class="form" ref="addFormRef1" :model="addParams1" :rules="rules" :labelCol="{ style: 'width:220px' }">
            <div class="form-top-titles-common">新人礼配置</div>
            <div class="form-top-line-common"></div>
            <a-form-item label="首页卡片样式" name="headImg">
              <uploadImg :max="10" :width="100" :height="100" :imgUrl="addParams1.headImg" :form="addParams1" path="headImg" :disabled="disabled" @success="(data) => uploadSuccess(data, 1)" />
              <div class="global-tip">
                建议尺寸 614px * 236px，比例2.5:1
              </div>
            </a-form-item>
            <a-form-item label="新人礼弹窗配置" name="ballImg">
              <uploadImg :max="10" :width="100" :height="100" :imgUrl="addParams1.ballImg" :form="addParams1" path="ballImg" :disabled="disabled" @success="(data) => uploadSuccess(data, 1)" />
              <div class="global-tip">
                建议尺寸 670px * 1000px
              </div>
            </a-form-item>

            <a-form-item label=" " class="hide-required-mark" name="jumpUrl" :hideRequiredMark="false" :colon="false">
              <!-- <selectPage :link="addParams1.jumpUrl" btnType="link" btnTitle="配置跳转链接" @ok="(obj)=>changeLink(obj,1)"></selectPage> -->
              <addLink :disabled="disabled" type="2" :showType="[1, 2]" :links="addParams1.jumpUrl" @ok="(link) => changeLink(link, 1)" :maxLinkNum="1" :isEvent="false">
                <a-button type="link">配置跳转链接</a-button>
                <div class="global-tip">
                  无特殊情况，设置为优惠券列表（全部）页即可，可填写多个，通过逗号
                </div>
              </addLink>

            </a-form-item>
            <a-form-item label="关联优惠券活动" name="couponId">
              <a-input placeholder="请输入" style="width:300px;" allow-clear v-model:value="addParams1.couponId" show-count :maxlength="100" />

            </a-form-item>
            <a-form-item label="注册后跳转弹窗">
              <a-button type="primary" @click="goRegister">去配置</a-button>
            </a-form-item>
            <a-form-item label=" " class="hide-required-mark" :hideRequiredMark="false" :colon="false">
              <a-button @click="save1" type="primary" :disabled="!$hasPermission('homeConfiguration:newPerson')" :loading="loading">保存并发布</a-button>
              <div class="global-tip">
                保存后，小程序页面将根据最新样式做展示
              </div>
            </a-form-item>
          </a-form>

          <!-- <a-form class="form" ref="addFormRef2" :model="addParams2" :rules="rules" :labelCol="{ style: 'width:220px' }">
            <div class="form-top-titles-common">二回礼（购物津贴）</div>
            <div class="form-top-line-common"></div>
            <a-form-item label="首页卡片样式" name="headImg">
              <uploadImg :max="10" :width="100" :height="100" :imgUrl="addParams2.headImg" :form="addParams2" path="headImg" :disabled="disabled" @success="(data)=>uploadSuccess(data,2)" />
              <div class="global-tip">
               建议尺寸 922px*362px，比例2.5:1
              </div>
            </a-form-item>

            <a-form-item label=" " class="hide-required-mark" name="jumpUrl" :hideRequiredMark="false" :colon="false">

              <addLink :disabled="disabled" type="2" :showType="[1,2]" :links="addParams2.jumpUrl" @ok="(link)=>changeLink(link,2)" :maxLinkNum="1" >
                <a-button type="link">配置跳转链接</a-button>
              </addLink>
              <div class="global-tip">
                无特殊情况，设置为优惠券列表（全部）页即可
              </div>
            </a-form-item>
            <a-form-item label="关联优惠卷" name="couponId">
              <a-input placeholder="请输入" style="width:300px;" allow-clear v-model:value="addParams2.couponId" show-count :maxlength="50" />
            </a-form-item>
            <a-form-item label=" " class="hide-required-mark" :hideRequiredMark="false" :colon="false">
              <a-button @click="save2" type="primary" :loading="loading">保存并发布</a-button>
              <div class="global-tip">
                保存后，小程序页面将根据最新样式做展示
              </div>
            </a-form-item>
          </a-form> -->

          <a-form class="form" ref="addFormRef3" :model="addParams3" :rules="rules" :labelCol="{ style: 'width:220px' }">
            <div class="form-top-titles-common">生日礼配置</div>
            <div class="form-top-line-common"></div>
            <a-form-item label="首页卡片样式" name="headImg">
              <uploadImg :max="10" :width="100" :height="100" :imgUrl="addParams3.headImg" :form="addParams3" path="headImg" :disabled="disabled" @success="(data) => uploadSuccess(data, 3)" />
              <div class="global-tip">
                建议尺寸 614px * 236px，比例2.5:1
              </div>
            </a-form-item>
            <a-form-item label="领取弹窗样式" name="ballImg">
              <uploadImg :max="10" :width="100" :height="100" :imgUrl="addParams3.ballImg" :form="addParams3" path="ballImg" :disabled="disabled" @success="(data) => uploadSuccess(data, 3)" />
              <div class="global-tip">
                建议尺寸
              </div>
            </a-form-item>
            <a-form-item label=" " class="hide-required-mark" name="jumpUrl" :hideRequiredMark="false" :colon="false">
              <addLink :disabled="disabled" type="2" :showType="[1, 2]" :links="addParams3.jumpUrl" @ok="(link) => changeLink(link, 3)" :maxLinkNum="1">
                <a-button type="link">配置跳转链接</a-button>
              </addLink>
              <div class="global-tip">
                无特殊情况，设置为优惠券列表（全部）页即可，可填写多个，通过逗号
              </div>
            </a-form-item>
            <a-form-item label="关联优惠券活动" name="couponId">
              <a-input placeholder="请输入" allow-clear style="width:300px;" v-model:value="addParams3.couponId" show-count :maxlength="100" />
            </a-form-item>
            <a-form-item label="邀请补充生日信息弹窗样式" name="reveiveBallImg">
              <uploadImg :max="10" :width="100" :height="100" :imgUrl="addParams3.reveiveBallImg" :form="addParams3" path="reveiveBallImg" :disabled="disabled" @success="(data) => uploadSuccess(data, 3)" />
              <div class="global-tip">
                建议尺寸 670px * 1000px， 在用户未填生日信息时，进入会员权益页面时，会弹窗邀请用户填写
              </div>
            </a-form-item>
            <a-form-item label=" " class="hide-required-mark" name="ballJumpUrl" :hideRequiredMark="false" :colon="false">
              <addLink :disabled="disabled" type="2" :showType="[1, 2]" :links="addParams3.ballJumpUrl" @ok="(link) => changeLinkType(link, 3)" :maxLinkNum="1">
                <a-button type="link">配置跳转链接</a-button>
              </addLink>
              <div class="global-tip">
                无特殊情况，设置为编辑注册信息页即可
              </div>
            </a-form-item>
            <a-form-item label=" " class="hide-required-mark" :hideRequiredMark="false" :colon="false">
              <a-button @click="save3" type="primary" :disabled="!$hasPermission('homeConfiguration:birthday')" :loading="loading">保存并发布</a-button>
              <div class="global-tip">
                保存后，小程序页面将根据最新样式做展示
              </div>
            </a-form-item>
          </a-form>

          <a-form class="form" ref="addFormRef4" :model="addParams4" :rules="rules" :labelCol="{ style: 'width:220px' }">
            <div class="form-top-titles-common">升级礼配置</div>
            <div class="form-top-line-common"></div>
            <a-form-item label="首页卡片样式" name="headImg">
              <uploadImg :max="10" :width="100" :height="100" :imgUrl="addParams4.headImg" :form="addParams4" path="headImg" :disabled="disabled" @success="(data) => uploadSuccess(data, 4)" />
              <div class="global-tip">
                建议尺寸 614px * 236px，比例2.5:1
              </div>
            </a-form-item>
            <template v-for="(item, index) in addParams4.giftConfigOtherDTO" :key="index">
              <div style="display:flex;">
                <a-form-item :label="['铜级会员', '银级会员', '金级会员'][index]" :name="['giftConfigOtherDTO', String(index), 'ballImg']" :rules="[{ required: true, message: '请上传' }]">
                  <uploadImg :max="10" :width="100" :height="100" :imgUrl="item.ballImg" :form="addParams4" :path="'giftConfigOtherDTO' + '.' + index + '.' + 'ballImg'" @success="(data) => uploadSuccess(data, 4)" />
                  <div class="global-tip">
                    建议尺寸 670px * 1000px
                  </div>
                </a-form-item>

                <a-form-item label=" " class="hide-required-mark" :hideRequiredMark="false" :colon="false" :name="['giftConfigOtherDTO', String(index), 'ballJump']" :rules="[{ required: true, message: '请配置跳转链接' }]">
                  <!-- <selectPage :link="addParams4.jumpUrl" btnType="link" btnTitle="配置跳转链接" @ok="(obj)=>changeLink(obj,4)"></selectPage> -->
                  <addLink type="2" :showType="[1, 2]" :links="item.ballJump" @ok="(link) => addParams4.giftConfigOtherDTO[index].ballJump = link" :maxLinkNum="1">
                    <a-button type="link">配置跳转链接</a-button>
                  </addLink>
                  <div class="global-tip">
                    无特殊情况，设置为优惠券列表（全部）页即可，可填写多个，通过逗号
                  </div>
                </a-form-item>
                <a-form-item label="关联优惠券活动" :name="['giftConfigOtherDTO', index, 'activityId']" :rules="[{ required: true, message: '请输入优惠券活动ID' }]">
                  <a-input placeholder="请输入" allow-clear style="width:300px;" v-model:value="item.activityId" show-count :maxlength="100" />
                </a-form-item>
              </div>
            </template>

            <a-form-item label=" " class="hide-required-mark" :hideRequiredMark="false" :colon="false">
              <a-button @click="save4" type="primary" :disabled="!$hasPermission('homeConfiguration:upgrades')" :loading="loading">保存并发布</a-button>
              <div class="global-tip">
                保存后，小程序页面将根据最新样式做展示
              </div>
            </a-form-item>
          </a-form>
          <a-form class="form" ref="addFormRef7" :model="addParams7" :rules="rules" :labelCol="{ style: 'width:220px' }">
            <div class="form-top-titles-common">无礼遇、有可使用的商品券时</div>
            <div class="form-top-line-common"></div>
            <a-form-item label="首页卡片样式" name="headImg">
              <uploadImg :max="10" :width="100" :height="100" :imgUrl="addParams7.headImg" :form="addParams7" path="headImg" :disabled="disabled" @success="(data) => uploadSuccess(data, 7)" />
              <div class="global-tip">
                建议尺寸 614px * 236px，比例2.5:1
              </div>
            </a-form-item>

            <a-form-item label=" " class="hide-required-mark" name="jumpUrl" :hideRequiredMark="false" :colon="false">
              <addLink :disabled="disabled" type="2" :showType="[1, 2]" :links="addParams7.jumpUrl" @ok="(link) => changeLink(link, 7)" :maxLinkNum="1">
                <a-button type="link">配置跳转链接</a-button>
              </addLink>
              <div class="global-tip">
                无特殊情况，设置为优惠券列表（商品券）页即可
              </div>
            </a-form-item>

            <a-form-item label=" " class="hide-required-mark" :hideRequiredMark="false" :colon="false">
              <a-button @click="save7" type="primary" :disabled="!$hasPermission('homeConfiguration:product')" :loading="loading">保存并发布</a-button>
              <div class="global-tip">
                保存后，小程序页面将根据最新样式做展示
              </div>
            </a-form-item>
          </a-form>
          <a-form class="form" ref="addFormRef5" :model="addParams5" :rules="rules" :labelCol="{ style: 'width:220px' }">
            <div class="form-top-titles-common">无以上礼遇可领取且券列表有有效券时</div>
            <div class="form-top-line-common"></div>
            <a-form-item label="首页卡片样式" name="headImg">
              <uploadImg :max="10" :width="100" :height="100" :imgUrl="addParams5.headImg" :form="addParams5" path="headImg" :disabled="disabled" @success="(data) => uploadSuccess(data, 5)" />
              <div class="global-tip">
                建议尺寸 614px * 236px，比例2.5:1
              </div>
            </a-form-item>

            <a-form-item label=" " class="hide-required-mark" name="jumpUrl" :hideRequiredMark="false" :colon="false">
              <!-- <selectPage :link="addParams5.jumpUrl" btnType="link" btnTitle="配置跳转链接" @ok="(obj)=>changeLink(obj,5)"></selectPage> -->
              <addLink :disabled="disabled" type="2" :showType="[1, 2]" :links="addParams5.jumpUrl" @ok="(link) => changeLink(link, 5)" :maxLinkNum="1">
                <a-button type="link">配置跳转链接</a-button>
              </addLink>
              <div class="global-tip">
                无特殊情况，设置为优惠券列表（全部）页即可
              </div>
            </a-form-item>

            <a-form-item label=" " class="hide-required-mark" :hideRequiredMark="false" :colon="false">
              <a-button @click="save5" :disabled="!$hasPermission('homeConfiguration:no')" type="primary" :loading="loading">保存并发布</a-button>
              <div class="global-tip">
                保存后，小程序页面将根据最新样式做展示
              </div>
            </a-form-item>
          </a-form>

          <a-form class="form" ref="addFormRef6" :model="addParams6" :rules="rules" :labelCol="{ style: 'width:220px' }">
            <div class="form-top-titles-common">无礼遇、无可用券时</div>
            <div class="form-top-line-common"></div>
            <a-form-item label="首页卡片样式" name="headImg">
              <uploadImg :max="10" :width="100" :height="100" :imgUrl="addParams6.headImg" :form="addParams6" path="headImg" :disabled="disabled" @success="(data) => uploadSuccess(data, 6)" />
              <div class="global-tip">
                建议尺寸 614px * 236px，比例2.5:1
              </div>
            </a-form-item>

            <a-form-item label=" " class="hide-required-mark" name="jumpUrl" :hideRequiredMark="false" :colon="false">
              <!-- <selectPage :link="addParams6.jumpUrl" btnType="link" btnTitle="配置跳转链接" @ok="(obj)=>changeLink(obj,6)"></selectPage> -->
              <addLink :disabled="disabled" type="2" :showType="[1, 2]" :links="addParams6.jumpUrl" @ok="(link) => changeLink(link, 6)" :maxLinkNum="1">
                <a-button type="link">配置跳转链接</a-button>
              </addLink>
              <div class="global-tip">
                无特殊情况，设置为会员中心页即可
              </div>
            </a-form-item>

            <a-form-item label=" " class="hide-required-mark" :hideRequiredMark="false" :colon="false">
              <a-button @click="save6" type="primary" :disabled="!$hasPermission('homeConfiguration:allNo')" :loading="loading">保存并发布</a-button>
              <div class="global-tip">
                保存后，小程序页面将根据最新样式做展示
              </div>
            </a-form-item>
          </a-form>

        </div>

      </a-spin>
    </template>
  </layout>
</template>
<script setup>
import { interactionList, giftconfigUpdate, basicgiftmap } from '@/http/index.js'
// import { useGlobalStore } from '@/store'
import { message, Modal } from "ant-design-vue";
import { useRouter } from 'vue-router'
const $router = useRouter()
import _ from "lodash"
import { v4 as uuidv4 } from "uuid";
// const global = useGlobalStore()
const addFormRef1 = ref(null)
const addFormRef2 = ref(null)
const addFormRef3 = ref(null)
const addFormRef4 = ref(null)
const addFormRef5 = ref(null)
const addFormRef6 = ref(null)
const addFormRef7 = ref(null)
import { cloneDeep } from 'lodash'
import { onMounted } from 'vue';

const { open, addParams1, addParams2, addParams3, addParams4, addParams5, addParams6, rules, loading, disabled, addParams7 } = toRefs(reactive({
  // open: props.visible,


  loading: false,
  disabled: false,
  addParams1: {
    couponId: '',
    headImg: '',
    ballImg: '',
    giftType: 1,
    name: 'additionalProperties1',
    jumpUrl: []
  },
  addParams2: {
    couponId: '',
    headImg: '',
    ballImg: '',
    giftType: 2,
    name: 'additionalProperties2',
    jumpUrl: []
  },
  addParams3: {
    couponId: '',
    headImg: '',
    ballImg: '',
    giftType: 3,
    name: 'additionalProperties3',
    jumpUrl: [{

      id: uuidv4(),
      width: 10,
      height: 10,
      position: [0, 0], // 位置
      code: '',// 埋点参数
      events: [],// 事件
      actions: [],// 行为
    }],
    ballJumpUrl: [],
  },
  addParams4: {
    couponId: '',
    headImg: '',
    ballImg: '',
    giftType: 4,
    name: 'additionalProperties4',
    jumpUrl: [],
    giftConfigOtherDTO: [
      {

        levelId: 2,
        ballImg: '',
        ballJump: [],
        activityId: null
      },
      {

        levelId: 3,
        ballImg: '',
        ballJump: [],
        activityId: null

      },
      {

        levelId: 4,
        ballImg: '',
        ballJump: [],
        activityId: null

      }
    ]
  },
  addParams5: {
    couponId: '',
    headImg: '',
    ballImg: '',
    giftType: 5,
    name: 'additionalProperties5',
    jumpUrl: []
  },
  addParams6: {
    couponId: '',
    headImg: '',
    ballImg: '',
    giftType: 6,
    name: 'additionalProperties6',
    jumpUrl: []
  },
  addParams7: {
    couponId: '',
    headImg: '',
    ballImg: '',
    giftType: 7,
    name: 'additionalProperties7',
    jumpUrl: []
  }
  ,
  rules: {
    couponId: [{ required: true, message: '请输入优惠券ID', trigger: ['blur', 'change'] }],
    headImg: [{ required: true, message: '请上传卡片样式', trigger: ['blur', 'change'] }],
    ballImg: [{ required: true, message: '请上传弹窗配置', trigger: ['blur', 'change'] }],
    jumpUrl: [{ required: true, message: '请选择跳转链接', trigger: ['change'] },
    { validator: (rule, value, callback) => jumpLInk(rule, value, callback), trigger: ['change'] }
    ],
    ballJumpUrl: [{ required: true, message: '请选择跳转链接', trigger: ['change'] },
    { validator: (rule, value, callback) => ballJumpUrl(rule, value, callback), trigger: ['change'] }
    ],
    reveiveBallImg: [{ required: true, message: '请上传领取配置', trigger: ['blur', 'change'] }],
  }
})
);
function ballJumpUrl(rule, value, callback) {
  console.log("🚀 ~ ballJumpUrl ~ value:", value,)
  if (value[0].actions.length > 0) {

    return Promise.resolve()
  } else {
    // return Promise.reject('重复后台账号')
    return Promise.reject('请选择链接')
  }
}
function jumpLInk(rule, value, callback) {
  console.log("🚀 ~ jumpLInk ~ value:", value,)
  if (value[0].actions.length > 0) {

    return Promise.resolve()
  } else {
    // return Promise.reject('重复后台账号')
    return Promise.reject('请选择链接')
  }
}
onMounted(() => {
  getList()
})
function getList() {
  loading.value = true
  basicgiftmap().then(res => {
    console.log(res.data.length, 'resresresresresres');
    if (res.data && Object.keys(res.data).length > 0) {
      for (let keyMap in res.data) {
        console.log(res.data[keyMap].giftType);
        if (res.data[keyMap].giftType != 4) {
          if (!res.data[keyMap].jumpUrl) {
            res.data[keyMap].jumpUrl = []
          } else {
            res.data[keyMap].jumpUrl = JSON.parse(res.data[keyMap].jumpUrl)
          }
          if (res.data[keyMap].giftType == 3) {
            if (!res.data[keyMap].ballJumpUrl) {
              res.data[keyMap].ballJumpUrl = []
            } else {
              res.data[keyMap].ballJumpUrl = JSON.parse(res.data[keyMap].ballJumpUrl)
            }
          }

        } else {
          if (res.data[keyMap].giftType === 4 && !res.data[keyMap].giftConfigOtherDTO) {
            addParams4.giftConfigOtherDTO = [{

              levelId: 2,
              ballImg: '',
              ballJump: [],
              activityId: null
            },
            {

              levelId: 3,
              ballImg: '',
              ballJump: [],
              activityId: null
            },
            {

              levelId: 4,
              ballImg: '',
              ballJump: [],
              activityId: null
            }]
          } else {
            res.data[keyMap].giftConfigOtherDTO.forEach(item => {
              if (item.ballJump) {
                item.ballJump = JSON.parse(item.ballJump)
              }

            })
          }
        }


      }
      addParams1.value = res.data['additionalProperties1'] || {
        couponId: '',
        headImg: '',
        ballImg: '',
        giftType: 1,
        name: 'additionalProperties1',
        jumpUrl: []
      }
      addParams2.value = res.data['additionalProperties2'] || {
        couponId: '',
        headImg: '',
        ballImg: '',
        giftType: 2,
        name: 'additionalProperties2',
        jumpUrl: []
      }
      addParams3.value = res.data['additionalProperties3'] || {
        couponId: '',
        headImg: '',
        ballImg: '',
        giftType: 3,
        name: 'additionalProperties3',
        jumpUrl: []
      }
      addParams4.value = res.data['additionalProperties4'] || {
        couponId: '',
        headImg: '',
        ballImg: '',
        giftType: 4,
        name: 'additionalProperties4',
        jumpUrl: [],
        giftConfigOtherDTO: [
          {

            levelId: 2,
            ballImg: '',
            ballJump: [],
            activityId: null
          },
          {

            levelId: 3,
            ballImg: '',
            ballJump: [],
            activityId: null

          },
          {

            levelId: 4,
            ballImg: '',
            ballJump: [],
            activityId: null

          }
        ]
      }
      addParams5.value = res.data['additionalProperties5'] || {
        couponId: '',
        headImg: '',
        ballImg: '',
        giftType: 5,
        name: 'additionalProperties5',
        jumpUrl: []
      }
      addParams6.value = res.data['additionalProperties6'] || {
        couponId: '',
        headImg: '',
        ballImg: '',
        giftType: 6,
        name: 'additionalProperties6',
        jumpUrl: []
      }
      addParams7.value = res.data['additionalProperties7'] || {
        couponId: '',
        headImg: '',
        ballImg: '',
        giftType: 7,
        name: 'additionalProperties7',
        jumpUrl: []
      }
    }

    console.log(res.data);

    loading.value = false
  }).catch(err => {
    loading.value = false
  })

}

const uploadSuccess = async (data, type) => {
  let { form, path: key, imgUrl } = data;
  console.log("🚀 ~ uploadSuccess ~ form, path: key, imgUrl:", form, key, imgUrl)
  _.set(form, key.split('.'), imgUrl)

  // 清除校验
  await nextTick()
  switch (type) {
    case 1:
      addFormRef1.value.validateFields([key.split('.')])
      break
    case 2:
      addFormRef2.value.validateFields([key.split('.')])
      break
    case 3:
      addFormRef3.value.validateFields([key.split('.')])
      break
    case 4:
      console.log([key.split('.')]);
      addFormRef4.value.validateFields([key.split('.')])
      break
    case 5:
      addFormRef5.value.validateFields([key.split('.')])
      break
    case 6:
      addFormRef6.value.validateFields([key.split('.')])
      break
    case 7:
      addFormRef7.value.validateFields([key.split('.')])
      break
  }

}

async function changeLinkType(data, type) {
  switch (type) {

    case 3:
      addParams3.value.ballJumpUrl = data
      await nextTick()
      addFormRef3.value.validateFields(['ballJumpUrl'])
      break
  }
}
async function changeLink(data, type) {
  console.log("🚀 ~ changeLink ~ data:", data)
  switch (type) {
    case 1:
      addParams1.value.jumpUrl = data
      await nextTick()
      addFormRef1.value.validateFields(['jumpUrl'])
      break
    case 2:
      addParams2.value.jumpUrl = data
      await nextTick()
      addFormRef2.value.validateFields(['jumpUrl'])
      break
    case 3:
      addParams3.value.jumpUrl = data
      await nextTick()
      addFormRef3.value.validateFields(['jumpUrl'])
      break
    case 4:
      addParams4.value.jumpUrl = data
      await nextTick()
      addFormRef4.value.validateFields(['jumpUrl'])
      break
    case 5:
      addParams5.value.jumpUrl = data
      await nextTick()
      addFormRef5.value.validateFields(['jumpUrl'])
      break
    case 6:
      addParams6.value.jumpUrl = data
      await nextTick()
      addFormRef6.value.validateFields(['jumpUrl'])
      break
    case 7:
      addParams7.value.jumpUrl = data
      await nextTick()
      addFormRef7.value.validateFields(['jumpUrl'])
      break
  }
}
function save1() {
  addFormRef1.value.validate().then(validate => {
    let params = cloneDeep(addParams1.value)
    saveChange(params)
  })
}
function save2() {
  addFormRef2.value.validate().then(validate => {
    let params = cloneDeep(addParams2.value)
    saveChange(params)
  })
}
function save3() {
  addFormRef3.value.validate().then(validate => {
    let params = cloneDeep(addParams3.value)
    saveChange(params)

  })
}
function save4() {
  addFormRef4.value.validate().then(validate => {
    let params = cloneDeep(addParams4.value)
    params.giftConfigOtherVoList = params.giftConfigOtherDTO
    saveChange(params)
  })
}
function save5() {
  addFormRef5.value.validate().then(validate => {
    let params = cloneDeep(addParams5.value)
    saveChange(params)
  })
}
function save6() {
  addFormRef6.value.validate().then(validate => {
    let params = cloneDeep(addParams6.value)
    saveChange(params)
  })
}
function save7() {
  addFormRef7.value.validate().then(validate => {
    let params = cloneDeep(addParams7.value)
    saveChange(params)
  })
}

function saveChange(params) {
  console.log("🚀 ~ saveChange ~ params:", params)
  if (params.giftType != 4) {
    if (params.jumpUrl[0].actions.length > 0) {
      params.jumpUrl = JSON.stringify(params.jumpUrl)
    } else {
      params.jumpUrl = ''
    }
    if (params.giftType == 3) {
      if (params.ballJumpUrl[0].actions.length > 0) {
        params.ballJumpUrl = JSON.stringify(params.ballJumpUrl)
      } else {
        params.ballJumpUrl = ''
      }

    }

  } else {
    if (params.jumpUrl) {
      params.jumpUrl = JSON.stringify(params.jumpUrl)
    } else {
      params.jumpUrl = ''
    }

    if (params.giftConfigOtherDTO) {
      params.giftConfigOtherDTO.forEach(item => {
        if (item.ballJump) {
          item.ballJump = JSON.stringify(item.ballJump)
        }
      })
    }
  }

  giftconfigUpdate(params).then(res => {
    message.success(res.msg);
    getList()
  })
}


// 去配置注册后弹窗
const goRegister = () => {
  const routeUrl = $router.resolve({
    name: 'SystemPage', query: { name: '注册成功后弹窗' }
  })
  window.open(routeUrl.href, '_blank')
}
</script>
<style lang="scss" scoped>
//影藏红的点
.hide-required-mark {
  :deep(.ant-form-item-required) {
    display: none;
  }
}
</style>
