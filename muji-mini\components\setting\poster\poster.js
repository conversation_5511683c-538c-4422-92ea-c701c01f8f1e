const app = getApp();
Component({
  properties: {
    // 组件数据
    data: {
      type: Object,
      value() {
        return {};
      },
    },
    // 游客模式
    visitor: {
      type: Boolean,
    },
    // 组件宽度
    width: {
      type: Number,
      value: 750,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    posterWidth: "", // 图片宽度
    posterHeight: "", // 图片高度
    height: "", // 组件高度
    rpx: app.globalData.rpx,
  },
  lifetimes: {
    attached() {
      let {
        width,
        data: {
          imgWidth,
          imgHeight,
          paddingBottom,
          paddingLeft,
          paddingRight,
          paddingTop,
        },
      } = this.data;
      let posterWidth = width - paddingLeft - paddingRight;
      let posterHeight = parseInt((posterWidth * imgHeight) / imgWidth);
      let height = posterHeight + paddingBottom + paddingTop;
      this.setData({
        paddingTop,
        paddingRight,
        paddingBottom,
        paddingLeft,
        posterWidth,
        posterHeight,
        height,
      });
    },
  },
  methods: {
    // 返回顶部
    goTop() {
      this.triggerEvent("goTop");
    },
    // scroll 锚点滚动跳转
    goAchor(e) {
      this.triggerEvent("goAchor", e.detail);
    },
    // 对弹窗的操作
    goModal(e) {
      this.triggerEvent("goModal", e.detail);
    },
    // 分享
    goShare(e) {
      let { shareTitle, shareImg, sharePath } = e.detail;
      this.triggerEvent("goShare", {
        shareTitle,
        shareImg,
        sharePath,
      });
    },
  },
});
