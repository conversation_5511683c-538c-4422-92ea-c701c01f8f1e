package com.dz.ms.product.dto.req;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 货架商品保存入参
 *
 * @author: fei
 * @date: 2024/11/25 16:25
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@ApiModel(value = "货架商品保存入参")
public class ShelfProductSaveParamDTO {

    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "货架名称")
    private String shelfName;
    @ApiModelProperty(value = "货架商品列表")
    private List<ShelfProductSaveDTO> saveShelfProductList;
    

}
