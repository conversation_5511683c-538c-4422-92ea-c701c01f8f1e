package com.dz.common.core.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 系统用户简要信息DTO
 * @author: Handy
 * @date:   2022/2/4 23:04
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "系统用户简要信息")
public class SysUserSimpleDTO {

    @ApiModelProperty(value = "系统用户ID")
    private Long id;
    @ApiModelProperty(value = "账号")
    private String username;
    @ApiModelProperty(value = "真实姓名")
    private String realname;
    @ApiModelProperty(value = "手机号码")
    private String mobile;

}
