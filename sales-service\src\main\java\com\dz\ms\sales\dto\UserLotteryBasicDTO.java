package com.dz.ms.sales.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/20
 */
@Data
public class UserLotteryBasicDTO {

    @ApiModelProperty(value = "获取的抽奖总次数")
    private Integer totalCount = 0;

    @ApiModelProperty(value = "剩余的抽奖次数")
    private Integer surplusCount = 0;

    @ApiModelProperty("是否分享")
    private Integer isShare;

}
