/* signUp/pages/registrationSuccessful/registrationSuccessful.wxss */
.page-container {
  // background-color: #a0a2a3;
  background-size: 100% 100%;

  .registrationSuccessful {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;

    .activeRules {
      position: absolute;
      right: 0rpx;
      top: 90rpx;
      width: 70rpx;
      height: 146rpx;
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .activeRules-in {
        width: 44rpx;
        height: 146rpx;
        background: #C7B397;
        font-family: MUJIFont2020,
          SourceHanSansCN;
        font-weight: 400;
        font-size: 22rpx;
        color: #FFFFFF;
        line-height: 24rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        writing-mode: vertical-rl;
        letter-spacing: 4rpx;
      }

    }

    .activeRules2 {
      position: absolute;
      right: 0rpx;
      top: 267rpx;
      width: 70rpx;
      height: 216rpx;
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .activeRules2-in {
        width: 44rpx;
        height: 216rpx;
        background: #C7B397;
        font-family: MUJIFont2020,
          SourceHanSansCN;
        font-weight: 400;
        font-size: 22rpx;
        color: #FFFFFF;
        line-height: 24rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        writing-mode: vertical-rl;
      }
    }

    .image {
      text-align: center;
      // height: 195rpx;
      width: 100%;
      // background-color: #4b4e51;
      padding-top: 256rpx;

      .top-success {
        width: 92rpx;
        height: 92rpx;
      }

      .h1 {
        font-family: MUJIFont2020,
          SourceHanSansCN;
        font-weight: 700;
        font-size: 42rpx;
        color: #3C3C43;
        // line-height: 47rpx;
        letter-spacing: 1rpx;
        margin-top: 18rpx;
        // text-align: left;
        // margin-left: 61rpx;
      }

      .p1 {
        margin-top: 50rpx;
        font-family: MUJIFont2020,
          SourceHanSansCN;
        font-weight: 500;
        font-size: 32rpx;
        color: #3C3C43;
        line-height: 47rpx;
        letter-spacing: 1rpx;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }

    .footer-tips {
      text-align: center;
      font-family: MUJIFont2020,
        SourceHanSansCN;
      font-weight: 400;
      font-size: 22rpx;
      color: #3C3C43;
      line-height: 44rpx;
      letter-spacing: 1px;

      font-style: normal;
      text-transform: none;
    }

    // .content_text {
    //   // min-height: calc(100% - 633rpx);
    //   box-sizing: border-box;
    //   background: linear-gradient(#FFFFFF 1%, #F8F6ED 109%);

    //   .cosnten-text-wrap {
    //     display: flex;
    //     flex-direction: column;
    //     position: relative;
    //     // padding-bottom: 77rpx;

    //     .content-title {
    //       width: 346rpx;
    //       height: 57rpx;
    //       background: #E3D8BB;
    //       border-radius: 29rpx 29rpx 29rpx 29rpx;
    //       position: relative;
    //       font-family: MUJIFont2020,
    //         SourceHanSansCN;
    //       font-weight: 700;
    //       font-weight: 700;
    //       font-size: 28rpx;
    //       color: var(--text-black-color);
    //       line-height: 57rpx;
    //       letter-spacing: 2rpx;
    //       text-align: left;
    //       margin-left: 87rpx;
    //       margin-top: 61rpx;
    //       padding-left: 55rpx;
    //       box-sizing: border-box;

    //       .icon {
    //         position: absolute;
    //         top: -19rpx;
    //         left: -54rpx;
    //         width: 90rpx;
    //         height: 90rpx;
    //         background: #E3D8BB;
    //         border-radius: 50%;
    //         background-size: 100% 100%;
    //         display: flex;
    //         justify-content: center;
    //         align-items: center;

    //         .img {
    //           width: 63rpx;
    //           height: 63rpx;
    //         }
    //       }
    //     }

    //     .content-title1 {
    //       width: 360rpx;
    //       height: 57rpx;
    //       background: #E3D8BB;
    //       border-radius: 29rpx 29rpx 29rpx 29rpx;
    //     }

    //     .p {
    //       font-family: MUJIFont2020, SourceHanSansCN;
    //       font-weight: 350;
    //       font-size: 26rpx;
    //       line-height: 21rpx;
    //       letter-spacing: 1px;
    //       text-align: left;
    //       color: var(--text-black-color);
    //       text-align: left;
    //       margin-left: 104rpx;
    //       margin-top: 25rpx;


    //     }

    //     .p2 {
    //       margin-left: 104rpx;
    //       // margin-top: 10rpx;
    //       font-family: MUJIFont2020, SourceHanSansCN;
    //       font-weight: 700;
    //       font-size: 26rpx;
    //       color: var(--text-black-color);
    //       line-height: 52rpx;
    //       letter-spacing: 1px;
    //       text-align: left;

    //       .spe {
    //         font-family: PingFang;
    //         font-weight: 700;
    //         font-size: 28rpx;
    //         color: var(--text-black-color);
    //         line-height: 40rpx;
    //         letter-spacing: 2px;
    //         text-align: left;
    //       }
    //     }

    //     .p3 {
    //       margin-left: 104rpx;
    //       width: 368rpx;
    //       height: 36rpx;
    //       font-family: MUJIFont2020,
    //         SourceHanSansCN;
    //       font-weight: 400;
    //       font-size: 14rpx;
    //       color: #979797;
    //       line-height: 18rpx;
    //       letter-spacing: 1px;
    //       text-align: left;
    //     }

    //     .pic1 {
    //       width: 102rpx;
    //       height: 150rpx;
    //       position: absolute;
    //       right: 84rpx;
    //       top: 119rpx;
    //     }

    //     .pic2 {
    //       width: 170rpx;
    //       height: 240rpx;
    //       position: absolute;
    //       right: 48rpx;
    //       bottom: -10rpx;
    //     }

    //     .subscribe {
    //       position: absolute;
    //       right: 131rpx;
    //       top: 50rpx;
    //       padding: 20rpx;
    //       font-weight: 900;
    //       font-size: 22rpx;
    //       color: #3C3C43;
    //       line-height: 32rpx;
    //       letter-spacing: 2rpx;
    //       text-align: left;
    //       text-decoration: underline;

    //       .subscribe-icon {
    //         position: absolute;
    //         top: 11rpx;
    //         bottom: -20rpx;
    //         right: -62rpx;
    //         width: 49rpx;
    //         height: 54rpx;
    //         margin-left: 20rpx;
    //         padding: 20rpx;
    //       }
    //     }
    //   }
    // }
  }

  .bottom-footer {
    position: absolute;
    bottom: env(safe-area-inset-bottom);
    left: 50%;
    transform: translateX(-50%);
  }

  .bottom-box {
    margin-top: 18rpx;
    margin-bottom: 81rpx;
    display: flex;
    justify-content: center;
  }


}
