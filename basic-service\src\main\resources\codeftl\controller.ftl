package com.dz.ms.${typeName}.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.${typeName}.dto.${objectName}DTO;
import com.dz.ms.${typeName}.service.${objectName}Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;

@Api(tags="${tableComment}")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class ${objectName}Controller  {

    @Resource
    private ${objectName}Service ${varObjectName}Service;

    /**
     * 分页查询${tableComment}
     * @param param
     * @return result<PageInfo<${objectName}DTO>>
     */
    @ApiOperation("分页查询${tableComment}")
	@GetMapping(value = "/${tableName}/list")
    public Result<PageInfo<${objectName}DTO>> get${objectName}List(@ModelAttribute ${objectName}DTO param) {
        Result<PageInfo<${objectName}DTO>> result = new Result<>();
		PageInfo<${objectName}DTO> page = ${varObjectName}Service.get${objectName}List(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询${tableComment}
     * @param ${primarkUpper}
     * @return result<${objectName}DTO>
     */
    @ApiOperation("根据ID查询${tableComment}")
	@GetMapping(value = "/${tableName}/info")
    public Result<${objectName}DTO> get${objectName}ById(@RequestParam("${primarkUpper}") ${primarkMType} ${primarkUpper}) {
        Result<${objectName}DTO> result = new Result<>();
        ${objectName}DTO ${varObjectName} = ${varObjectName}Service.get${objectName}ById(${primarkUpper});
        result.setData(${varObjectName});
        return result;
    }

    /**
     * 新增${tableComment}
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增${tableComment}",type = LogType.OPERATELOG)
    @ApiOperation("新增${tableComment}")
	@PostMapping(value = "/${tableName}/add")
    public Result<Long> add${objectName}(@RequestBody ${objectName}DTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,true);
        Long id = ${varObjectName}Service.save${objectName}(param);
        result.setData(id);
        return result;
    }

    /**
    * 更新${tableComment}
    * @param param
    * @return result<Object>
    */
    @SysLog(value = "更新${tableComment}",type = LogType.OPERATELOG)
    @ApiOperation("更新${tableComment}")
    @PostMapping(value = "/${tableName}/update")
    public Result<Long> update${objectName}(@RequestBody ${objectName}DTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,false);
        ${varObjectName}Service.save${objectName}(param);
        result.setData(param.getId());
        return result;
    }

    /**
    * 验证保存参数
    * @param param
    */
    private void validationSaveParam(${objectName}DTO param,boolean isAdd) {
        if(isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if(!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
    }

	/**
     * 根据ID删除${tableComment}
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除${tableComment}")
	@PostMapping(value = "/${tableName}/delete")
    public Result<Boolean> delete${objectName}ById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        ${varObjectName}Service.delete${objectName}ById(param);
        result.setData(true);
        return result;
    }

}
