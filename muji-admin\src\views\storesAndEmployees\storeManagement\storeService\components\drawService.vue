<template>
  <a-drawer :title="title" width="800" placement="right" :closable="true" :maskClosable="false" :open="open" @close="onClose">

    <table class="draggble-table">
      <thead>
        <tr class="draggble-tr">
          <th class="draggble-th w200">{{""}} </th>
          <th class="draggble-th">服务ID</th>
          <th class="draggble-th">服务名称</th>
          <th class="draggble-th">服务状态</th>
        </tr>
      </thead>
      <tbody class="draggble-tbody">
        <VueDraggable :sort="!disabled" class="service-list" :list="serviceList" item-key="uuid" :drag-selector="'.icon'" :filter="'.disabled'" animation="300">
          <template #item="{ element,index}">

            <tr class="draggble-tr" :key="index">
              <td class="draggble-td w200">
                <SvgIcon name="drag-dot-vertical" class="icon" width="20px" height="20px" />
              </td>
              <td class="draggble-td disabled">
                <div class="line-div ">{{element.id}}</div>
              </td>
              <td class="draggble-td disabled">
                <div class="line-div "> <img class="service-picurl" :src="element.image" alt="">
                  <div class="service-name" :title="element.name">{{element.name}}</div>
                </div>
              </td>
              <td class="draggble-td disabled">
                <a-tag color="red" v-if="element.status == 1">停用中</a-tag>
                <a-tag color="success" v-else>启用中</a-tag>
              </td>
            </tr>

          </template>
        </VueDraggable>
        <a-empty v-if="!serviceList.length" style="margin-top:100px" />
      </tbody>
    </table>
    <!-- </a-spin> -->
    <template #footer v-if="!disabled">
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="ok" :loading="loading">确定</a-button>
      </a-space>
    </template>

  </a-drawer>
</template>
<script setup>
import { serveList, serveSort } from '@/http/index.js'
import { useGlobalStore } from '@/store'
import { message, Modal } from "ant-design-vue";
import _ from "lodash"
import { v4 as uuidv4 } from 'uuid'
const global = useGlobalStore()
const addForm = ref(null)
import { cloneDeep } from 'lodash'
import { onMounted } from 'vue';
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  // 操作抽屉打开关闭
  visible: {
    type: Boolean,
    default: false
  },
  // 数据ID
  id: {
    type: String || Number,
    default: ''
  },
  // 操作类型
  type: {
    type: Number,
    default: 0, // 0-新增 1-编辑  2-查看
  },
  addType: {
    type: Number,
    default: 1, // 1 级 2级
  },
  title: {
    type: String || Number,
    default: ''
  }

})
// 置灰
const disabled = computed(() => {

  return props.type == 2
})
let addType1 = computed(() => {


  return props.addType
})
// 标题
const title = computed(() => {
  return '前台服务排序'
})

const { open, addParams, rules, loading, serviceList } = toRefs(reactive({
  open: props.visible,
  serviceList: [],

  loading: false,


})
);
// onMounted(() => {
//     initData()
// })
watch(() => props.visible, (value) => {
  // 弹窗操作
  open.value = props.visible
  addForm.value?.resetFields()
  if (open.value) {

    initData()
  }
})

//所有接口调取出
const initData = async () => {
  const promiseArr = []
  promiseArr.push(serveList())

  // 如果有新的接口就在这里push
  loading.value = true
  try {
    const [serveList] = await Promise.all(promiseArr)
    serviceList.value = serveList.data


    loading.value = false
  } catch (error) {
    loading.value = false
    console.error('获取数据失败:', error)
  }
}

// 关闭弹窗
const onClose = () => {
  emit('cancel')
}

// 确定
const ok = () => {

  let params = cloneDeep(serviceList.value)

  params.forEach((item, index) => {
    item.sort = index + 1
  })
  loading.value = true

  serveSort(params).then(res => {
    message.success(res.msg);
    emit('ok',)
  }).finally(() => {
    loading.value = false
  })

}



</script>
<style lang="scss" scoped>
.line-div {
  width: 100%;
  display: flex;
  // justify-content: center;
  align-items: center;
}
.w200 {
  width: 200px !important;
}
.icon {
  cursor: move;
}
.draggble {
  &-table {
    width: 100%;
  }
  &-tbody {
    width: 100%;
  }
  &-th {
    display: flex;
    // justify-content: center;
    align-items: center;
    width: 100%;
    padding: 10px;
  }
  &-tr {
    display: flex;
    width: 100%;
    align-items: center;
    margin-bottom: 10px;

    // justify-content: center;
  }
  &-td {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 10px;
    // justify-content: center;
  }
}
.service-picurl {
  width: 50px;
  height: 50px;
  margin-right: 10px;
}
.service-name {
  flex: 1;
  width: 0;
  // width: 200px;
  white-space: nowrap; /* 确保文本在一行内显示 */
  overflow: hidden; /* 超出容器的文本将被隐藏 */
  text-overflow: ellipsis;
}
</style>
