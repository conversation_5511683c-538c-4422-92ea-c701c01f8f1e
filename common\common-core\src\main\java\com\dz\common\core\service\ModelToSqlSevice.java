package com.dz.common.core.service;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.PropertyKeyConst;
import com.alibaba.nacos.api.config.ConfigService;
import com.dz.common.core.constants.ApplicationConstant;
import com.dz.common.core.utils.ModelToSql;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.yaml.snakeyaml.Yaml;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Properties;

/**
 * 根据model操作数据库表
 * @Author: Handy
 * @Date: 2022/8/25 11:11
 */
@Slf4j
@Component
public class ModelToSqlSevice {

    @Autowired
    private ApplicationConstant applicationConstant;
    @Value("${spring.cloud.nacos.config.server-addr: }")
    private String nacosConfigAddr;
    @Value("${spring.cloud.nacos.config.username: }")
    private String username;
    @Value("${spring.cloud.nacos.config.password: }")
    private String password;
    @Value("${spring.cloud.nacos.config.extension-configs[1].group: }")
    private String nacosConfigGroup;
    @Value("${mybatis-plus.notenant.tables:}")
    private String[] noTenantTables;
    @Resource
    private RedisService redisService;

    public void modelToSql(boolean isLock) {
        log.info("modelToSql进入islock:{}",isLock);

        String type = applicationConstant.applicationName.split("-")[0];
        log.info("modelToSql开始执行,type:{},profile:{},singleService:{}",type,applicationConstant.profile,applicationConstant.singleService);
        if(isLock) {
            String key = type+":started:before:lock";
            boolean lock = redisService.lock(key, 300);
            if(!lock) {
                return;
            }
        }
        try {
            log.info("modelToSql开始执行");
            LinkedHashMap<String, Object> obj = null;
            Yaml yaml = new Yaml();
            if(applicationConstant.singleService) {
                ClassPathResource resource = new ClassPathResource("config/sharding-jdbc-"+applicationConstant.profile+".yml");
                InputStream inputStream = resource.getInputStream();
                obj = yaml.load(inputStream);
            }
            else {
                Properties properties = new Properties();
                properties.put(PropertyKeyConst.SERVER_ADDR, nacosConfigAddr);
                if(StringUtils.isNotEmpty(username)) {
                    properties.put(PropertyKeyConst.USERNAME, username);
                    properties.put(PropertyKeyConst.PASSWORD, password);
                }
                ConfigService configService = NacosFactory.createConfigService(properties);
                String content = configService.getConfig("sharding-jdbc-"+applicationConstant.profile+".yml", nacosConfigGroup, 5000);
                log.info("modelToSql sharding config:{}",content);
                obj = yaml.load(content);
            }
            LinkedHashMap<String, Object> dataSource = (LinkedHashMap<String, Object>) obj.get("dataSources");
            HikariDataSource master = null;
            if(applicationConstant.openSharding) {
                master = (HikariDataSource) dataSource.get("ds0");
            }
            else {
                master = (HikariDataSource) dataSource.get("db_master");
            }
            String jdbcUrl = master.getJdbcUrl();
            String username = master.getUsername();
            String password = master.getPassword();
            List<String> list = new ArrayList<>();
            if(null != noTenantTables && noTenantTables.length > 0) {
                list = Arrays.asList(noTenantTables);
            }
            //log.info("modelToSql type:{}, jdbcUrl:{},username:{},password:{},noTenantTables:{}",type,jdbcUrl,username,password,noTenantTables);
            ModelToSql.modelToSql(type,jdbcUrl,username,password,list);
        } catch (Exception e) {
            throw new RuntimeException("初始化数据库表失败",e);
        }
    }

}
