package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.io.Serializable;
import java.util.Date;

/**
 * 公众号/小程序配置
 * @author: Handy
 * @date:   2022/07/26 18:05
 */
@Getter
@Setter
@NoArgsConstructor
@Table("公众号/小程序配置")
@TableName(value = "mp_config")
public class MpConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "公众号/小程序配置ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = false,comment = "类型 1小程序 2公众号")
    private Integer appType;
    @Columns(type = ColumnType.VARCHAR,length = 20,isNull = true,comment = "公众号/小程序名称")
    private String appName;
    @Columns(type = ColumnType.VARCHAR,length = 64,isNull = true,comment = "公众号/小程序appID",isIndex = true)
    private String appId;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "公众号/小程序秘钥")
    private String appSecret;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "行业标签 最多10个，空格隔开")
    private String label;
    @Columns(type = ColumnType.VARCHAR,length = 128,isNull = true,comment = "接口调用凭据刷新令牌")
    private String refreshToken;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = true,comment = "授权方认证类型,-1代表未认证，0代表微信认证")
    private Integer verifyTypeInfo;
    @Columns(type = ColumnType.VARCHAR,length = 64,isNull = true,comment = "小程序的原始ID")
    private String userName;
    @Columns(type = ColumnType.VARCHAR,length = 50,isNull = true,comment = "小程序授权给开发者的权限集列表")
    private String funcInfo;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = false,defaultValue = "0",comment = "授权状态 0未授权 1已授权 2解除授权")
    private Integer authStatus;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = false,defaultValue = "0",comment = "是否独立发版 0否 1是")
    private Integer alone;
    @Columns(type = ColumnType.VARCHAR,length = 20,isNull = true,comment = "代码版本号")
    private String version;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "版本简介")
    private String versionDesc;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = false,defaultValue = "0",comment = "代码上传状态 0未提交 1已提交 2提交失败 3审核成功 4审核失败 5已发布")
    private Integer codeStatus;
    @Columns(type = ColumnType.VARCHAR,length = 500,isNull = true,comment = "审核失败原因")
    private String failReason;

    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = true,comment = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;

    public MpConfig(Long id, Integer appType, String appName, String appId, String appSecret, String label, String refreshToken, Integer verifyTypeInfo, String userName, String funcInfo, Integer authStatus, Integer alone, String version, String versionDesc, Integer codeStatus, String failReason) {
        this.id = id;
        this.appType = appType;
        this.appName = appName;
        this.appId = appId;
        this.appSecret = appSecret;
        this.label = label;
        this.refreshToken = refreshToken;
        this.verifyTypeInfo = verifyTypeInfo;
        this.userName = userName;
        this.funcInfo = funcInfo;
        this.authStatus = authStatus;
        this.alone = alone;
        this.version = version;
        this.versionDesc = versionDesc;
        this.codeStatus = codeStatus;
        this.failReason = failReason;
    }

}
