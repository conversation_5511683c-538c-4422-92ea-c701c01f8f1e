package com.dz.common.core.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2023/6/9 16:29
 * es log
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
@Documented
public @interface EsLog {
    /**
     * 日志类型 1：订单 2 用户
     * */
    int type() default 1;

    String orderCode();
}
