package com.dz.common.core.dto.user.crm;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/3
 */
@Data
public class MuJiMemberOrder {
    /**
     * 订单编号 例如：X5000331432411200217
     */
    @JSONField(name = "order_sn")
    private String orderSn;

    /**
     * 订单类型 1:下单 2:退单
     */
    @JSONField(name = "order_type")
    private Integer orderType;

    /**
     * 消费渠道
     */
    private String channel;

    /**
     * 消费渠道名称
     */
    @JSONField(name = "channel_name")
    private String channelName;

    /**
     * 订单金额 单位/分 例如：1800，-1800
     */
    @JSONField(name = "total_fee")
    private Integer totalFee;

    /**
     * 交易时间 例如:2023-10-01 10:00:00
     */
    @JSONField(name = "pay_time", format = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /**
     * 订单来源 1:线上订单 2:线下订单
     */
    @JSONField(name = "order_source")
    private Integer orderSource;


}
