package com.dz.ms.user.dto.dkeyam.res;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 分页实体
 * @author: fei
 * @date: 2025/01/22 15:15
 */
public class DKeyAmPageInfo<E> {

    //当前页
    private Long startRow;
    //每页数量
    private Long limit;
    //总数量
    private Long totalCount;
    //数据列表
    private List<E> data;

    public DKeyAmPageInfo() {}

    public DKeyAmPageInfo(Long startRow, Long limit, Long totalCount, List<E> data) {
        this.startRow = startRow;
        this.limit = limit;
        this.totalCount = totalCount;
        this.data = data;
    }

    public DKeyAmPageInfo(Long startRow, Long limit, List<E> data) {
        this.startRow = startRow;
        this.limit = limit;
        this.data = data;
    }

    public DKeyAmPageInfo(String str, Class<E> tClass) {
        if(StringUtils.isNotBlank(str)){
            JSONObject jsonObject = JSONObject.parseObject(str);
            this.startRow = StringUtils.isBlank(jsonObject.getString("startRow")) ? null : Long.parseLong(jsonObject.getString("startRow"));
            this.limit = StringUtils.isBlank(jsonObject.getString("limit")) ? null : Long.parseLong(jsonObject.getString("limit"));
            this.totalCount = StringUtils.isBlank(jsonObject.getString("totalCount")) ? null : Long.parseLong(jsonObject.getString("totalCount"));
            this.data = StringUtils.isBlank(jsonObject.getString("data")) ? null : JSONObject.parseArray(jsonObject.getString("data"), tClass);
        }
    }

    public Long getStartRow() {
        return startRow;
    }

    public void setStartRow(Long startRow) {
        this.startRow = startRow;
    }

    public Long getLimit() {
        return limit;
    }

    public void setLimit(Long limit) {
        this.limit = limit;
    }

    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    public List<E> getData() {
        return data;
    }

    public void setData(List<E> data) {
        this.data = data;
    }

}

