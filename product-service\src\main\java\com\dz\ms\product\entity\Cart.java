package com.dz.ms.product.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 购物车
 *
 * @author: LiinNs
 * @date: 2024/12/09 18:21
 */
@Getter
@Setter
@NoArgsConstructor
@Table("购物车")
@TableName(value = "cart")
public class Cart implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "客户ID", isIndex = true)
    private Long userId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "货架商品ID")
    private Long shelfProductId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "货架ID")
    private Long shelfId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "商品ID")
    private Long productId;
    @Columns(type = ColumnType.VARCHAR, length = 255, isNull = false, comment = "商品图片")
    private String imgUrl;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, comment = "兑换积分，加购时积分")
    private Integer costPoint;
    @Columns(type = ColumnType.DECIMAL, length = 19, scale = 2, isNull = true, defaultValue = "0", comment = "兑换金额，加购时单价")
    private BigDecimal costPrice;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "商品数量")
    private Integer number;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "营销活动ID")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long campaignId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "营销规则ID")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long ruleId;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "每人限购数")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer rEveryoneLimit;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "活动限购数量")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer rPurchaseLimit;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, defaultValue = "0", comment = "选中标示 0未选中 1选中")
    private Integer checked;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, defaultValue = "1", comment = "状态 1正常 0失效")
    private Integer status;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "租户ID", isIndex = true)
    private Long tenantId;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;

    public Cart(Long id, Long userId, Long shelfProductId, Long shelfId, Long productId, String imgUrl, Integer costPoint, BigDecimal costPrice, Integer number, Integer checked) {
        this.id = id;
        this.userId = userId;
        this.shelfProductId = shelfProductId;
        this.shelfId = shelfId;
        this.productId = productId;
        this.imgUrl = imgUrl;
        this.costPoint = costPoint;
        this.costPrice = costPrice;
        this.number = number;
        this.checked = checked;
    }

}
