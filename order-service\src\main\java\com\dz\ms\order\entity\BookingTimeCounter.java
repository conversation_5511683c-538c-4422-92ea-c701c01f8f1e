package com.dz.ms.order.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 预约时间段计数器
 * @author: Handy
 * @date:   2024/05/31 16:36
 */
@Getter
@Setter
@NoArgsConstructor
@Table("预约时间段计数器")
@TableName(value = "booking_time_counter")
public class BookingTimeCounter implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.BIGINT, length = 11, isNull = false, comment = "预约ID",uniqueKeys = {"bookingId","storeId","bookingDate","timeSlot"})
    private Long bookingId;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "门店ID")
    private Long storeId;
    @Columns(type = ColumnType.INT,length = 10,isNull = false,comment = "预约日期数字(格式yyyyMMdd)")
    private Integer bookingDate;
    @Columns(type = ColumnType.VARCHAR,length = 32,isNull = false,comment = "预约时间段")
    private String timeSlot;
    @Columns(type = ColumnType.INT,length = 10,isNull = false,comment = "预约数量")
    private Integer bookingNumber;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = true,comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    public BookingTimeCounter(Long id, Long bookingId, Long storeId, Integer bookingDate, String timeSlot, Integer bookingNumber) {
        this.id = id;
        this.bookingId = bookingId;
        this.storeId = storeId;
        this.bookingDate = bookingDate;
        this.timeSlot = timeSlot;
        this.bookingNumber = bookingNumber;
    }

}
