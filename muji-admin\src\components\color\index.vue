<template>
  <div style="display:flex;align-items:center">
    <!-- standard-color 标准色  theme-color主题色
    const standardClolor = [
        '#c21401',
        '#ff1e02',
        '#ffc12a',
        '#ffff3a',
        '#90cf5b',
        '#00af57',
        '#00afee',
        '#0071be',
        '#00215f',
        '#72349d',
      ]
 -->
    <ColorPicker @change="changeColor" v-model:rgba="current" />
    <span style="margin-left:10px" v-if="show">{{current}}</span>
  </div>
</template>
<script setup>
import { reactive, toRefs, watch } from 'vue';
import ColorPicker from 'colorpicker-v3'  // 注册组件
import 'colorpicker-v3/style.css' // 引入样式文件

const emit = defineEmits(["changeColor"]);
const props = defineProps({
  value: {
    type: String
  },
  // 显示文字
  show: {
    type: Boolean,
    default: true
  },
  keyValue: {
    type: String,
    default: 'rgba'
  }
})



// 定义数据
const { current } = toRefs(reactive({
  current: props.value,
}))
watch(() => props.value, () => {
  current.value = props.value
})
const changeColor = (e) => {
  // console.log(e)
  current.value = e[props.keyValue];
  emit('changeColor', current.value)
}
</script>

<style lang="scss" scoped>
</style>
