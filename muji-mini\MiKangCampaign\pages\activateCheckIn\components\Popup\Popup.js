import {
  throttle
} from '../../../../../utils/util';
const app = getApp();


Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    isShow: {
      type: Boolean,
      value: false,
    },
    num: {
      type: Number,
      value: 0,
    },
    // 是否显示关闭按钮
    closeable: {
      type: Boolean,
      value: true
    },
    info: {
      type: Object,
      value: () => {},
    },
    disabled: {
      type: Boolean,
      value: true
    },
  },
  lifetimes: {
    attached() {
      this.setUserInfo()
    },
  },
  pageLifetimes: {
    show() {
      this.setUserInfo()
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    emailTxt: '<EMAIL>',
    phoneTxt: '4009209299',
    userInfo: {},
    title: '信息使用说明',
  },
  /**
   * 组件的方法列表
   */
  methods: {
    setUserInfo() {
      this.setData({
        userInfo: app.globalData.userInfo,
      })
    },
    // 禁止穿透
    touchmove() {
      return false
    },
    // 禁止穿透
    touchmove1() {
      return true
    },
    onClose(e) {
      let isclick = e.currentTarget.dataset.isclick
      console.log(isclick, "isclick");
      this.triggerEvent('close', isclick)
      this.setData({
        disabled: true
      })
    },
    bindscrolltolower() {
      console.log("滚动到最下方");
      this.setData({
        disabled: false
      })
    }
  }
})
