package com.dz.ms.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.ms.product.dto.CrmProductOnTaskListDTO;
import com.dz.ms.product.dto.ProductOnTaskDTO;
import com.dz.ms.product.dto.ShelfDTO;
import com.dz.ms.product.dto.req.CrmProductOnTaskLisParamDTO;
import com.dz.ms.product.dto.req.ProductOnTaskSaveParamDTO;
import com.dz.ms.product.entity.ProductOnTask;

import java.util.List;

/**
 * 货架商品库存任务接口
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:25
 */
public interface ProductOnTaskService extends IService<ProductOnTask> {

    /**
     * 分页查询货架商品库存任务
     *
     * @param param
     * @return PageInfo<ProductOnTaskDTO>
     */
    PageInfo<CrmProductOnTaskListDTO> getProductOnTaskList(CrmProductOnTaskLisParamDTO param);

    /**
     * 根据ID查询货架商品库存任务
     *
     * @param id
     * @return ProductOnTaskDTO
     */
    public ProductOnTaskDTO getProductOnTaskById(Long id);

    /**
     * 保存货架商品库存任务
     *
     * @param param
     * @return Long
     */
    ProductOnTask saveProductOnTask(ProductOnTaskDTO param);

    /**
     * 根据ID删除货架商品库存任务
     *
     * @param param
     */
    void deleteProductOnTaskById(IdCodeDTO param);

    /**
     * 执行所有货架库存上下架任务
     */
    void executeProductOnTaskOfAllShelf();

    /**
     * 执行货架库存上下架任务
     *
     * @param shelfDTO
     */
    void executeProductOnTaskOfShelf(ShelfDTO shelfDTO);

    /**
     * 添加货架上下架任务
     *
     * @param list
     * @return
     */
    Long addProductOnTask(List<ProductOnTaskSaveParamDTO> list);
}
