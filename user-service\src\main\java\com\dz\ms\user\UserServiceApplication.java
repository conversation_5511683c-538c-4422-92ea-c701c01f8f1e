package com.dz.ms.user;

import com.dz.common.core.config.CommonConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.Import;

@EnableDiscoveryClient
@SpringBootApplication(exclude = RestTemplateAutoConfiguration.class)
@Import(CommonConfig.class)
public class UserServiceApplication {

    /*@Bean
    @RefreshScope
    public Globals globals(){
        return new Globals();
    }*/

    public static void main(String[] args) {
        SpringApplication.run(UserServiceApplication.class, args);
    }
}
