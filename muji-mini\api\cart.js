// 添加购物车
exports.postCartAdd = data => wx.$request({
  url: '/app/product/cart/add',
  data,
  method: 'post'
})

// 编辑购物车
exports.postCartEdit = data => wx.$request({
  url: '/app/product/cart/edit',
  data,
  method: 'post'
})

// 删除购物车商品
exports.delCartItem = ids => wx.$request({
  url: `/app/product/cart/delete?ids=${ids}`,
  // data,
  method: 'delete'
})

// 删除购物车失效商品
exports.delCartDisabled = ids => wx.$request({
  url: `/app/product/cart/clean_disabled_product?ids=${ids}`,
  // data,
  method: 'delete'
})

// 获取购物车列表
exports.getCartList = data => wx.$request({
  url: '/app/product/cart/list',
  data,
  method: 'get'
})

// 获取购物车商品总数
exports.getCartCount = data => wx.$request({
  url: '/app/product/cart/count',
  data,
  method: 'get'
})

// 全选购物车
exports.getCartCheckAll = data => wx.$request({
  url: '/app/product/cart/checkedall',
  data,
  method: 'get'
})
