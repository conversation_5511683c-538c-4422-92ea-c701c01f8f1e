<map class="map-content" longitude="{{currentLongitude}}" latitude="{{currentLatitude}}" scale="15" markers="{{markers}}" show-location="{{true}}" bindmarkertap="onMarkerTap">
  <swiper current="{{current}}" next-margin="80rpx" previous-margin="17rpx" class="swiper bottom-position" bindchange="swiperChange">
    <swiper-item wx:for="{{outletsList}}" wx:key="id">
      <map-card outletData="{{item}}" />
    </swiper-item>
  </swiper>
  <cover-view slot="callout">
    <cover-view class="customer-pop" marker-id="{{item.id}}" wx:for="{{outletsList}}" wx:key="id">
      {{item.storeName}}
    </cover-view>
  </cover-view>
</map>