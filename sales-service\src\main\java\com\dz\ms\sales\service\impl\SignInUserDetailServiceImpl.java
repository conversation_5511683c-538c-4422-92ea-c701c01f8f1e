package com.dz.ms.sales.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.Lock;
import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.dto.basic.AliCheckRequestDTO;
import com.dz.common.core.dto.basic.SubscribeMsgSendDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.dto.user.UserSimpleDTO;
import com.dz.common.core.enums.SubscribeMsgEnum;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.basic.AliCheckFeignClient;
import com.dz.common.core.fegin.basic.MpMsgFeignClient;
import com.dz.common.core.fegin.user.MUJIOpenApiFeignClient;
import com.dz.common.core.fegin.user.UserInfoFeginClient;
import com.dz.common.core.service.ExportService;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.DateUtils;
import com.dz.common.core.utils.ThreadPoolUtils;
import com.dz.common.core.vo.ReceiveCouponVo;
import com.dz.ms.sales.dto.*;
import com.dz.ms.sales.dto.excel.SignInUserDetailExportDTO;
import com.dz.ms.sales.entity.CampaignUser;
import com.dz.ms.sales.entity.SignInUser;
import com.dz.ms.sales.entity.SignInUserDetail;
import com.dz.ms.sales.entity.UserLotteryPrizes;
import com.dz.ms.sales.mapper.*;
import com.dz.ms.sales.service.CampaignService;
import com.dz.ms.sales.service.LotteryService;
import com.dz.ms.sales.service.SignInUserDetailService;
import com.dz.ms.sales.service.UserLotterySummaryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/8
 */
@Service
@Slf4j
public class SignInUserDetailServiceImpl implements SignInUserDetailService {
    @Resource
    private SignInUserDetailMapper signInUserDetailMapper;
    @Resource
    private SignInUserMapper signInUserMapper;
    @Resource
    private CampaignService campaignService;
    @Resource
    private CampaignUserMapper campaignUserMapper;
    @Resource
    private UserInfoFeginClient userInfoFeginClient;
    @Resource
    private UserLotterySummaryService userLotterySummaryService;
    @Resource
    private RedisService redisService;
    @Resource
    private MUJIOpenApiFeignClient mujiOpenApiFeignClient;
    @Resource
    private CampaignEnrollMapper campaignEnrollMapper;
    @Resource
    private ExportService exportService;
    @Resource
    private AliCheckFeignClient aliCheckFeignClient;
    @Resource
    private MpMsgFeignClient mpMsgFeignClient;
    @Resource
    private UserLotteryPrizesMapper userLotteryPrizesMapper;


    @Override
    public CrmSignInUserInfoDTO getSignInUserInfo() {
        CrmSignInUserInfoDTO dto = new CrmSignInUserInfoDTO();

        int enrollNum = campaignEnrollMapper.selectCount(new LambdaQueryWrapper<>()).intValue();
        dto.setEnrollNum(enrollNum);

        List<CampaignUserDTO> campaignUserList = campaignUserMapper.selectByType();
        if (ObjectUtils.isNotEmpty(campaignUserList)) {
            dto.setRecruitNum(campaignUserList.stream().filter(item -> item.getType() == 3).mapToInt(CampaignUserDTO::getNum).sum());
            dto.setPurchaseNum(campaignUserList.stream().filter(item -> item.getType() == 1).mapToInt(CampaignUserDTO::getNum).sum());
            dto.setImportNum(campaignUserList.stream().filter(item -> item.getType() == 2).mapToInt(CampaignUserDTO::getNum).sum());
        }

        List<SignInUserDetailDTO> signInUserDetailList = signInUserDetailMapper.selectSignInCountByDays();
        if (ObjectUtils.isNotEmpty(signInUserDetailList)) {
            dto.setSignInNum(signInUserDetailList.stream().filter(item -> item.getDays() == 0).mapToInt(SignInUserDetailDTO::getNum).sum());
            dto.setSignIn1DaysNum(signInUserDetailList.stream().filter(item -> item.getDays() == 1).mapToInt(SignInUserDetailDTO::getNum).sum());
            dto.setSignIn2DaysNum(signInUserDetailList.stream().filter(item -> item.getDays() == 2).mapToInt(SignInUserDetailDTO::getNum).sum());
            dto.setSignIn3DaysNum(signInUserDetailList.stream().filter(item -> item.getDays() == 3).mapToInt(SignInUserDetailDTO::getNum).sum());
            dto.setSignIn4DaysNum(signInUserDetailList.stream().filter(item -> item.getDays() == 4).mapToInt(SignInUserDetailDTO::getNum).sum());
            dto.setSignIn5DaysNum(signInUserDetailList.stream().filter(item -> item.getDays() == 5).mapToInt(SignInUserDetailDTO::getNum).sum());
            dto.setSignIn6DaysNum(signInUserDetailList.stream().filter(item -> item.getDays() == 6).mapToInt(SignInUserDetailDTO::getNum).sum());
            dto.setSignIn7DaysNum(signInUserDetailList.stream().filter(item -> item.getDays() == 7).mapToInt(SignInUserDetailDTO::getNum).sum());
        }
        return dto;
    }

    @Override
    public PageInfo<CrmSignInUserDTO> getSignInUserList(CrmSignInUserParamDTO param) {
        Page<CrmSignInUserDTO> page = new Page<>(param.getPageNum(), param.getPageSize());
        IPage<CrmSignInUserDTO> pages = signInUserDetailMapper.selectByMany(page, param);
        return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), BeanCopierUtils.convertList(pages.getRecords(), CrmSignInUserDTO.class));
    }

    @Override
    public void exportSignInDetailList(DownloadAddParamDTO exportParam) {
        String jsonParam = exportParam.getJsonParam();
        String fileName = exportParam.getFileName();
        String fileExt = exportParam.getFileExt();
        String reportCode = exportParam.getReportCode();
        Long downloadCenterId = exportParam.getDownloadCenterId();
        CurrentUserDTO commonLoginDTO = SecurityContext.getUser();
        CrmSignInUserParamDTO param = JSON.parseObject(jsonParam, CrmSignInUserParamDTO.class);
        List<JSONObject> reList = getSignInDetailExportDTO(param);
        exportService.writeExportList(fileName, fileExt, reportCode, downloadCenterId, commonLoginDTO, reList);
    }

    private List<JSONObject> getSignInDetailExportDTO(CrmSignInUserParamDTO param) {
        LambdaQueryWrapper<SignInUserDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ObjectUtils.isNotEmpty(param.getDays()), SignInUserDetail::getDays, param.getDays());
        wrapper.eq(SignInUserDetail::getSupplementFlag, 0);
        wrapper.eq(SignInUserDetail::getState, 1);
        wrapper.eq(SignInUserDetail::getFailFlag, 0);
        List<SignInUserDetail> signInUserDetailList = signInUserDetailMapper.selectList(wrapper);

        if (CollectionUtils.isEmpty(signInUserDetailList)) {
            return new ArrayList<>();
        }
        List<SignInUserDetailExportDTO> exportList = new ArrayList<>();
//        exportList.add(exportItem);

        for (SignInUserDetail signInUserDetail : signInUserDetailList) {
            SignInUserDetailExportDTO dto = new SignInUserDetailExportDTO();
            dto.setUsername(signInUserDetail.getUsername());
            dto.setCardNo(signInUserDetail.getCardNo());
            dto.setDays("第" + signInUserDetail.getDays() + "天");
            dto.setSignInTime(DateUtils.toDayString(signInUserDetail.getSignInTime(), "yyyy-MM-dd HH:mm:ss"));
            dto.setComfort(signInUserDetail.getComfort() + "分");
            dto.setMoisturize(signInUserDetail.getMoisturize() + "分");
            dto.setAbsorption(signInUserDetail.getAbsorption() + "分");
            dto.setPersistence(signInUserDetail.getPersistence() + "分");
            dto.setSatisfaction(signInUserDetail.getSatisfaction() + "分");
            dto.setMaterialUrl(signInUserDetail.getMaterialUrl());
            dto.setFeeling(signInUserDetail.getFeeling());
            dto.setImprove(signInUserDetail.getImprove());
            dto.setScore(signInUserDetail.getScore() + "分");
            dto.setRecommend(signInUserDetail.getRecommend() + "分");
            dto.setPurchase(signInUserDetail.getPurchase() + "分");
            exportList.add(dto);
        }

        return new ArrayList<>(JSON.parseArray(JSON.toJSONString(exportList), JSONObject.class));

    }


    @Override
    public OpenSignInDTO isOpenSignIn(String campaignCode, Long uid) {
        OpenSignInDTO dto = new OpenSignInDTO();
        UserSimpleDTO userInfo = userInfoFeginClient.getCurrentUserSimpleInfo().getData();
        CampaignDTO campaignInfoCache = campaignService.getCampaignInfoDetailCache(campaignCode);
        if (ObjectUtils.isEmpty(campaignInfoCache)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "活动不存在");
        }
//        if (campaignInfoCache.getState() != 1 && campaignInfoCache.getState() != 2) {
//            log.info("活动状态: {}, 活动详情: {}", new String[]{"未开始", "进行中", "公示期", "已结束"}[campaignInfoCache.getState()], JSON.toJSONString(campaignInfoCache));
//            throw new BusinessException(ErrorCode.BAD_REQUEST, "活动" + new String[]{"未开始", "进行中", "公示期", "已结束"}[campaignInfoCache.getState()]);
//        }
        if (campaignService.getCampaignUser(campaignCode, userInfoFeginClient.getCurrentUserSimpleInfo().getData().getMobile()) <= 0) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "您没有获取参与资格");
        }

        dto.setIsOpen(signInUserMapper.selectCount(new LambdaQueryWrapper<SignInUser>().eq(SignInUser::getCampaignCode, campaignCode).eq(SignInUser::getUnionid, userInfo.getUnionid()).ne(SignInUser::getState, 2)) > 0);
        dto.setRestartCount(signInUserMapper.selectCount(new LambdaQueryWrapper<SignInUser>().eq(SignInUser::getCampaignCode, campaignCode).eq(SignInUser::getUnionid, userInfo.getUnionid()).eq(SignInUser::getState, 2)).intValue());


        SignInUser signInUser = signInUserMapper.selectOne(new LambdaQueryWrapper<SignInUser>().eq(SignInUser::getCampaignCode, campaignCode).eq(SignInUser::getUnionid, userInfo.getUnionid()).orderByDesc(SignInUser::getId).last("limit 1"));

        if (ObjectUtils.isNotEmpty(signInUser)) {
            dto.setStartActivityTime(signInUser.getStartActivityTime());
            dto.setEndActivityTime(signInUser.getEndActivityTime());
            dto.setSignInStartTime(signInUser.getSignInStartTime());
            dto.setSignInEndTime(signInUser.getSignInEndTime());
            dto.setState(signInUser.getState());
        }

        CampaignUser campaignUser = campaignUserMapper.selectOne(new LambdaQueryWrapper<CampaignUser>().eq(CampaignUser::getCampaignCode, campaignInfoCache.getCampaignCode()).eq(CampaignUser::getMobile, userInfo.getMobile()).orderByDesc(CampaignUser::getId).last("limit 1"));
        if (ObjectUtils.isNotEmpty(campaignUser)) {
            dto.setCouponUserType(campaignUser.getType());
        }

        return dto;
    }

    @Override
    @Lock(prefix = "lock:signInUserDetail:openSignIn", key = "'#param.campaignCode'+'#uid'")
    public Long openSignIn(Long uid, SignInUserOpenDTO param) {
        UserSimpleDTO userInfo = userInfoFeginClient.getCurrentUserSimpleInfo().getData();
        // 检查活动状态
        checkCampaignState(param.getCampaignCode());
        // 检查用户参与资格
        checkUserParticipation(param.getCampaignCode(), userInfo.getUnionid());

        CampaignDTO campaignInfoCache = campaignService.getCampaignInfoDetailCache(param.getCampaignCode());
        CampaignSignInConfigDTO campaignSignInConfig = campaignInfoCache.getCampaignSignInConfig();

        // 校验打卡时间范围
        if (DateUtils.getEndOfDayPlusDaysAsDate(campaignSignInConfig.getSignInTimes() - 1).after(campaignInfoCache.getCampaignEndTime())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "T+" + campaignSignInConfig.getSignInTimes() + "，最晚不超过活动结束时间");
        }


//        SignInUser signInUser = new SignInUser(null, param.getCampaignCode(), param.getSource(), uid, userInfo.getOpenid(), userInfo.getUnionid(), userInfo.getUsername(), userInfo.getMobile(), DateUtils.getStartOfDayAsDate(0), DateUtils.getEndOfDayPlusDaysAsDate(campaignSignInConfig.getSignInDays()), DateUtils.getStartOfDayAsDate(1), DateUtils.getEndOfDayPlusDaysAsDate(campaignSignInConfig.getSignInTimes() + 1), DateUtils.getStartOfDayAsDate(campaignSignInConfig.getSignInTimes() + 2), DateUtils.getEndOfDayPlusDaysAsDate(campaignSignInConfig.getSignInDays()));
        SignInUser signInUser = new SignInUser(null, param.getCampaignCode(), param.getChannelOne(), param.getChannelTwo(), uid,
                userInfo.getOpenid(), userInfo.getUnionid(), userInfo.getUsername(), userInfo.getMobile(),
                DateUtils.getStartOfDayAsDate(0), DateUtils.getEndOfDayPlusDaysAsDate(campaignSignInConfig.getSignInDays() - 1),
                DateUtils.getStartOfDayAsDate(0), DateUtils.getEndOfDayPlusDaysAsDate(campaignSignInConfig.getSignInTimes() - 1));
        signInUserMapper.insert(signInUser);


        // 开启打卡，默认打开第0天
        SignInUserDetail openSignInUserDetail = new SignInUserDetail(signInUser.getId(), DateUtils.getEndOfDayPlusDaysAsStr(signInUser.getStartActivityTime(), 0), param.getCampaignCode(), param.getChannelOne(), param.getChannelTwo(), uid, userInfo.getOpenid(), userInfo.getUnionid(), userInfo.getUsername(), userInfo.getMobile(), userInfo.getCardNo(), 0, param.getMaterialUrl(), param.getExpectation());
        signInUserDetailMapper.insert(openSignInUserDetail);

        // 图片安全审核
        verifyImageSafety(param.getMaterialUrl(), openSignInUserDetail.getId());

        // 初始化打开数据
//        List<SignInUserDetail> signInUserDetailList = new ArrayList<>();
//        for (Integer i = 1; i <= campaignSignInConfig.getSignInTimes(); i++) {
//            SignInUserDetail signInUserDetail = new SignInUserDetail(signInUser.getId(), DateUtils.getEndOfDayPlusDaysAsStr(i), param.getCampaignCode(), param.getSource(), uid, userInfo.getOpenid(), userInfo.getUnionid(), userInfo.getUsername(), userInfo.getMobile(), i);
//            signInUserDetailList.add(signInUserDetail);
//        }
//        signInUserDetailMapper.insertBatchSomeColumn(signInUserDetailList);

        return openSignInUserDetail.getId();
    }

    /**
     * 查询当前打卡进度
     *
     * @param campaignCode
     * @param uid
     * @return
     */
    @Override
    public SignInRecordDTO querySignInSpeed(String campaignCode, Long uid) {
        UserSimpleDTO userInfo = userInfoFeginClient.getCurrentUserSimpleInfo().getData();

        SignInRecordDTO dto = new SignInRecordDTO();
        LambdaQueryWrapper<SignInUserDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SignInUserDetail::getCampaignCode, campaignCode);
        wrapper.eq(SignInUserDetail::getUnionid, userInfo.getUnionid());
        wrapper.gt(SignInUserDetail::getDays, 0);
        wrapper.eq(SignInUserDetail::getFailFlag, 0);
        wrapper.orderByAsc(SignInUserDetail::getDays);
        List<SignInUserDetail> signInUserDetailList = signInUserDetailMapper.selectList(wrapper);
        List<SignInUserDetailDTO> signInUserDetailDTOS = BeanCopierUtils.convertList(signInUserDetailList, SignInUserDetailDTO.class);

        CampaignDTO campaignInfoCache = campaignService.getCampaignInfoDetailCache(campaignCode);
        if (ObjectUtils.isEmpty(campaignInfoCache)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "活动不存在");
        }

        CampaignSignInConfigDTO campaignSignInConfig = campaignInfoCache.getCampaignSignInConfig();

        Date now = new Date();
        SignInUser signInUser = signInUserMapper.selectOne(new LambdaQueryWrapper<SignInUser>().eq(SignInUser::getCampaignCode, campaignCode).eq(SignInUser::getUnionid, userInfo.getUnionid()).ne(SignInUser::getState, 2));
        int days = 0;
        if (ObjectUtils.isNotEmpty(signInUser)) {
            dto.setStartActivityTime(signInUser.getStartActivityTime());
            dto.setEndActivityTime(signInUser.getEndActivityTime());
            days = (DateUtils.daysBetween(signInUser.getStartActivityTime(), now).intValue() + 1) <= campaignSignInConfig.getSignInTimes() ? DateUtils.daysBetween(signInUser.getStartActivityTime(), now).intValue() + 1 : campaignSignInConfig.getSignInTimes();
            if (ObjectUtils.isNotEmpty(signInUserDetailDTOS)) {
                // 判断当前时间为第几天打卡
                // 补充缺卡数据
                List<Integer> collect = signInUserDetailDTOS.stream().map(SignInUserDetailDTO::getDays).collect(Collectors.toList());
                for (int i = 1; i < days; i++) {
                    if (!collect.contains(i)) {
                        SignInUserDetailDTO signInUserDetailDTO = new SignInUserDetailDTO();
                        signInUserDetailDTO.setDays(i);
//                    signInUserDetailDTO.setSignInDate(DateUtils.getEndOfDayPlusDaysAsStr(i - 1));
                        if (i == campaignSignInConfig.getSignInTimes()) {
                            signInUserDetailDTO.setState(0);
                        } else {
                            signInUserDetailDTO.setState(2);
                        }
                        signInUserDetailDTOS.add(signInUserDetailDTO);
                    }
                }
                if (days < campaignSignInConfig.getSignInTimes()) {
                    dto.setIsSignIn(signInUserDetailDTOS.stream().filter(e -> e.getState() == 1).filter(e -> e.getSignInDate().equals(DateUtils.getCurrDate())).count() == 1);
                } else {
                    int day = days - 1;
                    dto.setIsSignIn(signInUserDetailDTOS.stream().filter(e -> e.getState() == 1).filter(e -> e.getSignInDate().equals(DateUtils.getEndOfDayPlusDaysAsStr(signInUser.getStartActivityTime(), day))).count() == 1);
                }
                dto.setList(signInUserDetailDTOS);
            }
        }
        dto.setDays(days);

        if (ObjectUtils.isNotEmpty(campaignSignInConfig)) {
            long signInTimes = 0;
            long supplementTimes = 0;
            if (ObjectUtils.isNotEmpty(signInUserDetailList)) {
                signInTimes = signInUserDetailList.stream().filter(e -> e.getState() == 1).count();
                supplementTimes = signInUserDetailList.stream().filter(e -> e.getSupplementFlag() == 1).count();
            }
            dto.setCompleteTimes(signInTimes);
            dto.setSurplusTimes(campaignSignInConfig.getSignInTimes().longValue() - signInTimes);
            dto.setSurplusRepairSignInTimes(campaignSignInConfig.getRepairSignInTimes() - supplementTimes);
        }
        return dto;
    }


    /**
     * 打卡
     *
     * @param uid
     * @param param
     * @return
     */
    @Override
    @Lock(prefix = "lock:signInUserDetail:signIn", key = "'#param.campaignCode'+'#uid'")
    @Transactional(rollbackFor = RuntimeException.class)
    public Long signIn(Long uid, SignInUserDetailDTO param) {
//        SignInUserDetail signInUserDetail = signInUserDetailMapper.selectById(param.getId());
//        CampaignDTO campaignInfoCache = campaignService.getCampaignInfoDetailCache(param.getCampaignCode());

        // 检查活动状态
        checkCampaignState(param.getCampaignCode());

        UserSimpleDTO userInfo = userInfoFeginClient.getCurrentUserSimpleInfo().getData();
        if (signInUserMapper.selectCount(new LambdaQueryWrapper<SignInUser>().eq(SignInUser::getCampaignCode, param.getCampaignCode()).eq(SignInUser::getUnionid, userInfo.getUnionid()).ne(SignInUser::getState, 2)) <= 0) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "还未开启打开");
        }

        CampaignDTO campaignInfoCache = campaignService.getCampaignInfoDetailCache(param.getCampaignCode());
        CampaignSignInConfigDTO campaignSignInConfig = campaignInfoCache.getCampaignSignInConfig();


        // 打卡规则
        SignInUser signInUser = signInUserMapper.selectOne(new LambdaQueryWrapper<SignInUser>().eq(SignInUser::getCampaignCode, param.getCampaignCode()).eq(SignInUser::getUnionid, userInfo.getUnionid()).ne(SignInUser::getState, 2));
        // 判断打卡时间是否在打卡时间范围内(打卡开始时间，打卡结束时间)
        Date now = new Date();


        // 判断当前时间为第几天打卡
//        int days = DateUtils.daysBetween(signInUser.getSignInStartTime(), now).intValue() + 1;
        int days = (DateUtils.daysBetween(signInUser.getStartActivityTime(), now).intValue() + 1) <= campaignSignInConfig.getSignInTimes() ? DateUtils.daysBetween(signInUser.getStartActivityTime(), now).intValue() + 1 : campaignSignInConfig.getSignInTimes();
        if (days < campaignSignInConfig.getSignInTimes()) {
            if (signInUser.getSignInStartTime().after(now) || signInUser.getSignInEndTime().before(now)) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "当前打卡不在有效时间范围内");
            }
        } else {
            if (signInUser.getStartActivityTime().after(now) || signInUser.getEndActivityTime().before(now)) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "当前打卡不在有效时间范围内");
            }
        }
        // 判断当天是否已经打卡，请勿重复打卡
        if (signInUserDetailMapper.selectCount(new LambdaQueryWrapper<SignInUserDetail>().eq(SignInUserDetail::getCampaignCode, param.getCampaignCode()).eq(SignInUserDetail::getUnionid, userInfo.getUnionid()).eq(SignInUserDetail::getDays, days).eq(SignInUserDetail::getFailFlag, 0)) > 0) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "当天已经打卡");
        }


        // 打卡，校验数据
        verifyData(days, param);
        // 插入
        SignInUserDetail signInUserDetail = new SignInUserDetail();
        // 1-6天构造
        if (days <= 6) {
            signInUserDetail = new SignInUserDetail(signInUser.getId(), DateUtils.getCurrDate(), param.getCampaignCode(), param.getChannelOne(), param.getChannelTwo(),
                    uid, userInfo.getOpenid(), userInfo.getUnionid(), userInfo.getUsername(), userInfo.getMobile(), userInfo.getCardNo(), days, param.getComfort(),
                    param.getMoisturize(), param.getAbsorption(), param.getPersistence(), param.getSatisfaction(),
                    1, 0, now);
        }
        // 第7天构造
        if (days >= 7) {
            signInUserDetail = new SignInUserDetail(signInUser.getId(), DateUtils.getEndOfDayPlusDaysAsStr(signInUser.getStartActivityTime(), days - 1),
                    param.getCampaignCode(), param.getChannelOne(), param.getChannelTwo(),
                    uid, userInfo.getOpenid(), userInfo.getUnionid(), userInfo.getUsername(), userInfo.getMobile(), userInfo.getCardNo(), days, param.getMaterialUrl(),
                    param.getFeeling(), param.getScore(), param.getImprove(), param.getPurchase(), param.getRecommend(), param.getSatisfaction(), 1, 0, now);

        }
        signInUserDetailMapper.insert(signInUserDetail);

        // 文本安全审核
        verifyTextSafety(param.getFeeling() + param.getImprove(), signInUserDetail.getId());
        // 图片安全审核
        verifyImageSafety(param.getMaterialUrl(), signInUserDetail.getId());
        // 发放权益
        sendCoupon(param.getCampaignCode(), userInfo, campaignSignInConfig);
        return signInUserDetail.getId();
    }

    /**
     * 补卡
     *
     * @param uid
     * @param param
     * @return
     */
    @Override
    @Lock(prefix = "lock:signInUserDetail:supplement", key = "'#param.campaignCode'+'#uid'")
    @Transactional(rollbackFor = RuntimeException.class)
    public SignInSupplementDTO supplement(Long uid, SignInUserDetailDTO param) {

        SignInSupplementDTO dto = new SignInSupplementDTO();
        dto.setPatchSignInState(1);
        dto.setPatchSignInStateDesc("补签成功");

        UserSimpleDTO userInfo = userInfoFeginClient.getCurrentUserSimpleInfo().getData();
        // 检查活动状态
        checkCampaignState(param.getCampaignCode());

        if (signInUserMapper.selectCount(new LambdaQueryWrapper<SignInUser>().eq(SignInUser::getCampaignCode, param.getCampaignCode()).eq(SignInUser::getUnionid, userInfo.getUnionid()).ne(SignInUser::getState, 2)) <= 0) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "还未开启打开");
        }

        CampaignDTO campaignInfoCache = campaignService.getCampaignInfoDetailCache(param.getCampaignCode());
        CampaignSignInConfigDTO campaignSignInConfig = campaignInfoCache.getCampaignSignInConfig();

        if (campaignSignInConfig.getPatchFlag() != 1 || campaignSignInConfig.getRepairSignInTimes() <= 0) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "活动不允许补签");
        }

        // 校验数据
        Integer days = param.getDays();
        if (ObjectUtils.isEmpty(days)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "补签天数不能为空");
        }
        if (days < 1 || days > 6) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "补签天数不能大于第6天");
        }

        // 打卡规则
        SignInUser signInUser = signInUserMapper.selectOne(new LambdaQueryWrapper<SignInUser>().eq(SignInUser::getCampaignCode, param.getCampaignCode()).eq(SignInUser::getUnionid, userInfo.getUnionid()).ne(SignInUser::getState, 2));

        Date now = new Date();
        if (!(signInUser.getStartActivityTime().before(now) && signInUser.getEndActivityTime().after(now))) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "当前补卡不在有效时间范围内");
        }

        long supplementCount = 0;
        // 判断如果没有打卡，可以直接补前面的天数
        if (signInUserDetailMapper.selectCount(new LambdaQueryWrapper<SignInUserDetail>().eq(SignInUserDetail::getCampaignCode, param.getCampaignCode()).eq(SignInUserDetail::getUnionid, userInfo.getUnionid())) >= 0) {

            List<SignInUserDetail> signInUserDetailList = signInUserDetailMapper.selectList(new LambdaQueryWrapper<SignInUserDetail>().eq(SignInUserDetail::getCampaignCode, param.getCampaignCode()).eq(SignInUserDetail::getUnionid, userInfo.getUnionid()).orderByDesc(SignInUserDetail::getDays));

            supplementCount = signInUserDetailList.stream().filter(e -> e.getSupplementFlag() == 1).count();
            if (supplementCount >= campaignSignInConfig.getRepairSignInTimes()) {
//                throw new BusinessException(ErrorCode.BAD_REQUEST, "补卡失败，你已没有补卡机会");
                dto.setPatchSignInState(0);
                dto.setPatchSignInStateDesc("补卡失败，你已没有补卡机会");
                return dto;
            }

            long count = signInUserDetailList.stream().filter(e -> e.getDays().equals(param.getDays())).count();
            if (count > 0) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "当天已经打卡");
            }

            // 根据supplementTime补卡时间与当前时间比对，判断今天是否补过卡

//            if (signInUserDetailList.stream().filter(e -> e.getSupplementFlag() == 1).filter(e -> DateUtils.toDayString(e.getSupplementTime()).equals(DateUtils.toDayString(now))).count() > 0) {
////                throw new BusinessException(ErrorCode.BAD_REQUEST, "补卡失败，你今天已经补卡过了");
//
//                dto.setPatchSignInState(0);
//                dto.setPatchSignInStateDesc("补卡失败，你今天已经补卡过了");
//                return dto;
//            }

            // 判断当前天数是否大于补签的天数
            // 获取当前应该是第N天打卡
            int currDays = DateUtils.daysBetween(signInUser.getStartActivityTime(), now).intValue() + 1;
            if (param.getDays() >= currDays) {
//                throw new BusinessException(ErrorCode.BAD_REQUEST, "补卡失败，补卡的天数不能大于当前打卡的天数");
                dto.setPatchSignInState(0);
                dto.setPatchSignInStateDesc("补卡失败，补卡的天数不能大于当前打卡的天数");
                return dto;
            }

        }


        SignInUserDetail signInUserDetail = new SignInUserDetail(signInUser.getId(), DateUtils.getEndOfDayPlusDaysAsStr(signInUser.getStartActivityTime(),
                param.getDays() - 1), param.getCampaignCode(), param.getChannelOne(), param.getChannelTwo(), uid, userInfo.getOpenid(), userInfo.getUnionid(),
                userInfo.getUsername(), userInfo.getMobile(), userInfo.getCardNo(), param.getDays());
        signInUserDetail.setState(1);
        signInUserDetail.setSupplementFlag(1);
        signInUserDetail.setSignInTime(now);
        signInUserDetail.setSupplementTime(now);
        signInUserDetailMapper.insert(signInUserDetail);

        // 发放权益
        sendCoupon(param.getCampaignCode(), userInfo, campaignSignInConfig);
        dto.setPatchSignInTimes(campaignSignInConfig.getRepairSignInTimes() - (int) supplementCount - 1);
        return dto;
    }

    @Override
    public SignInUserDetailDTO getReport(Long uid, int days) {
        UserSimpleDTO userInfo = userInfoFeginClient.getCurrentUserSimpleInfo().getData();
        SignInUserDetail signInUserDetail = signInUserDetailMapper.selectOne(new LambdaQueryWrapper<SignInUserDetail>().eq(SignInUserDetail::getUnionid, userInfo.getUnionid()).eq(SignInUserDetail::getDays, days).eq(SignInUserDetail::getFailFlag, 0));
        if (ObjectUtils.isEmpty(signInUserDetail)) {
            return null;
        }
        SignInUserDetailDTO dto = BeanCopierUtils.convertObject(signInUserDetail, SignInUserDetailDTO.class);
        dto.setUsername(userInfo.getUsername());
        dto.setAvatar(userInfo.getAvatar());
        return dto;
    }

    @Override
    public SignInReportDTO getReportDays(String campaignCode, Long uid) {

        // 检查活动状态
        checkCampaignState(campaignCode);

        CampaignDTO campaignInfoCache = campaignService.getCampaignInfoDetailCache(campaignCode);

        UserSimpleDTO userInfo = userInfoFeginClient.getCurrentUserSimpleInfo().getData();
        SignInUser signInUser = signInUserMapper.selectOne(new LambdaQueryWrapper<SignInUser>().eq(SignInUser::getCampaignCode, campaignInfoCache.getCampaignCode()).eq(SignInUser::getUnionid, userInfo.getUnionid()).eq(SignInUser::getState, 1).orderByDesc(SignInUser::getId).last("limit 1"));
        if (ObjectUtils.isEmpty(signInUser)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "您还未完成打卡");
        }

        List<SignInUserDetail> signInUserDetailList = signInUserDetailMapper.selectList(new LambdaQueryWrapper<SignInUserDetail>().eq(SignInUserDetail::getCampaignCode, campaignInfoCache.getCampaignCode()).eq(SignInUserDetail::getSignInUserId, signInUser.getId()).eq(SignInUserDetail::getUnionid, userInfo.getUnionid()).ge(SignInUserDetail::getDays, 0).eq(SignInUserDetail::getFailFlag, 0));
        CampaignSignInConfigDTO campaignSignInConfig = campaignInfoCache.getCampaignSignInConfig();
        if (ObjectUtils.isEmpty(signInUserDetailList) || signInUserDetailList.size() != campaignSignInConfig.getSignInTimes()+1) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "您还未完成打卡");
        }
        Map<Integer, SignInUserDetail> signInUserDetailMap = new HashMap<>();
        for (SignInUserDetail signInUserDetail : signInUserDetailList) {
            signInUserDetailMap.put(signInUserDetail.getDays(), signInUserDetail);
        }

        SignInReportDTO dto = new SignInReportDTO();
        dto.setMaterialUrlDay1(signInUserDetailMap.get(0).getMaterialUrl());
        dto.setMaterialUrlDay7(signInUserDetailMap.get(7).getMaterialUrl());
        dto.setImprove(signInUserDetailMap.get(7).getImprove());
        // 晒出第0天开启打卡的数据
        List<SignInUserDetail> newSignInDetailList = signInUserDetailList.stream().filter(e -> e.getDays() > 0 && e.getDays() < 7 && e.getSupplementFlag() == 0).collect(Collectors.toList());
        dto.setComfortAvg(newSignInDetailList.stream().mapToInt(SignInUserDetail::getComfort).average().orElse(0));
        dto.setMoisturizeAvg(newSignInDetailList.stream().mapToInt(SignInUserDetail::getMoisturize).average().orElse(0));
        dto.setAbsorptionAvg(newSignInDetailList.stream().mapToInt(SignInUserDetail::getAbsorption).average().orElse(0));
        dto.setPersistenceAvg(newSignInDetailList.stream().mapToInt(SignInUserDetail::getPersistence).average().orElse(0));

        return dto;
    }

    @Override
    public void signInPushMsgJob(String campaignCode) {
        List<SignInUser> signInUserList = signInUserMapper.notSignInByToday(campaignCode);
        List<SubscribeMsgSendDTO> list = new ArrayList<>();

        signInUserList.forEach(e -> {
            SubscribeMsgSendDTO msgSend = new SubscribeMsgSendDTO(SubscribeMsgEnum.SIGN_IN, e.getOpenid(), null,
                    new String[]{null, null});
            msgSend.setMsgCode(SubscribeMsgEnum.SIGN_IN.getMsgCode());
            list.add(msgSend);

        });
        Long tenantId = SecurityContext.getUser().getTenantId();
        ThreadPoolUtils.pool.execute(() -> {
            SecurityContext.setUser(new CurrentUserDTO(tenantId));
            for (SubscribeMsgSendDTO msgSend : list) {
                mpMsgFeignClient.sendSubscribeMsgCatch(msgSend, tenantId);
            }
        });
    }

    @Override
    public void signInUserFailJob(String campaignCode) {
        CampaignDTO campaignInfoCache = campaignService.getCampaignInfoDetailCache(campaignCode);
        if (ObjectUtils.isEmpty(campaignInfoCache)) {
            return;
        }
        signInUserMapper.signInUserFailByEndTime(SecurityContext.getUser().getUid());

        CampaignSignInConfigDTO campaignSignInConfig = campaignInfoCache.getCampaignSignInConfig();
        List<SignInUserDetail> signInUserDetailList = signInUserDetailMapper.missingCardSignInUserDetail(campaignCode, campaignSignInConfig.getSignInTimes(), campaignSignInConfig.getRepairSignInTimes());
        if (ObjectUtils.isEmpty(signInUserDetailList)) {
            return;
        }
        List<Long> ids = signInUserDetailList.stream().map(SignInUserDetail::getSignInUserId).collect(Collectors.toList());
        signInUserMapper.signInUserFail(ids, SecurityContext.getUser().getUid());
        signInUserDetailMapper.signInUserDetailFail(ids, SecurityContext.getUser().getUid());
    }

    @Override
    public Boolean restartSignIn(String campaignCode, Long uid) {
        // 检查活动状态
        checkCampaignState(campaignCode);
        CampaignDTO campaignInfoCache = campaignService.getCampaignInfoDetailCache(campaignCode);
        UserSimpleDTO userInfo = userInfoFeginClient.getCurrentUserSimpleInfo().getData();
        Long signInUserCount = signInUserMapper.selectCount(new LambdaQueryWrapper<SignInUser>().eq(SignInUser::getCampaignCode, campaignInfoCache.getCampaignCode()).eq(SignInUser::getUnionid, userInfo.getUnionid()).in(SignInUser::getState, 0, 1));
        if (signInUserCount > 0) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "您未满足重启打卡");
        }
        Long count = signInUserMapper.selectCount(new LambdaQueryWrapper<SignInUser>().eq(SignInUser::getCampaignCode, campaignInfoCache.getCampaignCode()).eq(SignInUser::getUnionid, userInfo.getUnionid()).eq(SignInUser::getState, 2));
        if (count <= 0) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "您未满足重启打卡");
        }
//        signInUserMapper.signInUserFailByEndTime(SecurityContext.getUser().getUid());
        return true;
    }


    private void checkCampaignState(String campaignCode) {
        CampaignDTO campaignInfoCache = campaignService.getCampaignInfoDetailCache(campaignCode);
        if (ObjectUtils.isEmpty(campaignInfoCache)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "活动不存在");
        }
        if (campaignInfoCache.getState() != 1 && campaignInfoCache.getState() != 2) {
            log.info("活动状态: {}, 活动详情: {}", new String[]{"未开始", "进行中", "公示期", "已结束"}[campaignInfoCache.getState()], JSON.toJSONString(campaignInfoCache));
            throw new BusinessException(ErrorCode.BAD_REQUEST, "活动" + new String[]{"未开始", "进行中", "公示期", "已结束"}[campaignInfoCache.getState()]);
        }
    }

    private void checkUserParticipation(String campaignCode, String unionid) {
        UserSimpleDTO userInfo = userInfoFeginClient.getCurrentUserSimpleInfo().getData();
        if (signInUserMapper.selectCount(new LambdaQueryWrapper<SignInUser>().eq(SignInUser::getCampaignCode, campaignCode).eq(SignInUser::getUnionid, unionid).ne(SignInUser::getState, 2)) > 0) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "已开启打卡");
        }
        if (campaignService.getCampaignUser(campaignCode, userInfo.getMobile()) <= 0) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "您没有获取参与资格");
        }
    }


    private void verifyImageSafety(String materialUrl, Long openSignInUserDetailId) {
        // 实现图片安全审核逻辑
        if (StringUtils.isNotBlank(materialUrl)) {
            List<String> urlList = new ArrayList<>();
            if (materialUrl.contains(",")) {
                urlList = Arrays.asList(materialUrl.split(","));
            } else {
                urlList.add(materialUrl);
            }

            Map<String, Integer> map = aliCheckFeignClient.imageSyncScanRequest(new AliCheckRequestDTO(null, urlList, 1, openSignInUserDetailId)).getData();
            if (map.containsValue(1)) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "该图片不合规，请重新上传");
            }
        }
    }

    @Override
    public void verifyImageSafety2(String materialUrl, AliCheckRequestDTO aliCheckRequestDTO) {
        // 实现图片安全审核逻辑
        if (StringUtils.isNotBlank(materialUrl)) {
            List<String> urlList = new ArrayList<>();
            if (materialUrl.contains(",")) {
                urlList = Arrays.asList(materialUrl.split(","));
            } else {
                urlList.add(materialUrl);
            }
            Result<Map<String, Integer>> mapResult = aliCheckFeignClient.imageSyncScanRequest(new AliCheckRequestDTO(null, urlList, aliCheckRequestDTO.getModelType(), aliCheckRequestDTO.getModelId()));
            if(!mapResult.isSuccess()){
                throw new BusinessException(ErrorCode.INTERNAL_ERROR);
            }
            Map<String, Integer> map = mapResult.getData();
            if (map.containsValue(1)) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "该图片不合规，请重新上传");
            }
        }
    }

    private void verifyTextSafety(String text, Long openSignInUserDetailId) {
        // 实现文本安全审核逻辑
        if (StringUtils.isNotBlank(text)) {
            boolean isTextScanRequest = aliCheckFeignClient.textScanRequest(new AliCheckRequestDTO(text, null, 1, openSignInUserDetailId)).getData();
            log.info("textScanRequest:" + isTextScanRequest);
            if (isTextScanRequest) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "存在违规内容，请检查后重新填写");
            }
        }
    }
    @Override
    public void verifyTextSafety2(String text, AliCheckRequestDTO aliCheckRequestDTO) {
        // 实现文本安全审核逻辑
        if (StringUtils.isNotBlank(text)) {
            boolean isTextScanRequest = aliCheckFeignClient.textScanRequest(new AliCheckRequestDTO(text, null, aliCheckRequestDTO.getModelType(), aliCheckRequestDTO.getModelId())).getData();
            log.info("textScanRequest:" + isTextScanRequest);
            if (isTextScanRequest) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "存在违规内容，请检查后重新填写");
            }
        }
    }

    private void verifyData(int days, SignInUserDetailDTO param) {

        switch (days) {
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
                if (param.getComfort() == null || param.getComfort() == 0) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "舒适度还未评分");
                }
                if (param.getMoisturize() == null || param.getMoisturize() == 0) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "保湿效果还未评分");
                }
                if (param.getAbsorption() == null || param.getAbsorption() == 0) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "吸收速度还未评分");
                }
                if (param.getPersistence() == null || param.getPersistence() == 0) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "持久效果还未评分");
                }
//                if (param.getAgeBracket() == null || (param.getAgeBracket() != 1 && param.getAgeBracket() != 2)) {
//                    throw new BusinessException(ErrorCode.BAD_REQUEST, "请选择您的年龄段");
//                }
//                if (param.getImprove() == null || param.getImprove().isBlank()) {
//                    throw new BusinessException(ErrorCode.BAD_REQUEST, "使用产品后，您得到了什么改善");
//                }
                if (param.getSatisfaction() == null || param.getSatisfaction() == 0) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "整体满意度还未评分");
                }

                break;
            case 7:
                if (param.getFeeling() == null || param.getFeeling().isBlank()) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "请填写使用感受");
                }
                if (param.getMaterialUrl() == null || param.getMaterialUrl().isBlank()) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "请上传你的肌肤照片");
                }
                if (param.getMaterialUrl().split(",").length > 9) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "上传超限，最多上传9张");
                }
//                if (param.getAgeBracket() == null || (param.getAgeBracket() != 1 && param.getAgeBracket() != 2)) {
//                    throw new BusinessException(ErrorCode.BAD_REQUEST, "请选择您的年龄段");
//                }
//                if (param.getSuggestion() == null || param.getSuggestion().isBlank()) {
//                    throw new BusinessException(ErrorCode.BAD_REQUEST, "提升空间还未评分");
//                }
                if (param.getRecommend() == null || param.getRecommend() == 0) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "您是否愿意推荐该产品给他人？");
                }
                if (param.getScore() == null || param.getScore() == 0) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "您对产品的总体满意度评分？");
                }
                if (param.getPurchase() == null || param.getPurchase() == 0) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "您是否会继续购买该产品");
                }
                break;
            default:
                break;
        }
    }


    /**
     * 如果用户完成了7天打卡，则需要实现以下功能：
     * 1. 发送优惠券：通过调用优惠券服务接口，将优惠券发放给用户。
     * 3. 记录用户领取优惠券的时间：将用户领取优惠券的时间记录到数据库中，以便后续统计和奖励。
     * 4. 赠送抽奖机会
     *
     * @param userInfo
     */

    private void sendCoupon(String campaignCode, UserSimpleDTO userInfo, CampaignSignInConfigDTO config) {
        // 如果用户完成了7天打卡
        Long count = signInUserDetailMapper.selectCount(new LambdaQueryWrapper<SignInUserDetail>().eq(SignInUserDetail::getCampaignCode, campaignCode).eq(SignInUserDetail::getUnionid, userInfo.getUnionid()).eq(SignInUserDetail::getState, 1).eq(SignInUserDetail::getFailFlag, 0));
        SignInUser signInUser = signInUserMapper.selectOne(new LambdaQueryWrapper<SignInUser>().eq(SignInUser::getCampaignCode, campaignCode).eq(SignInUser::getUnionid, userInfo.getUnionid()).ne(SignInUser::getState, 2));
        if (count.intValue() == 7) {
            if (signInUser.getSendCouponFlag() == 0) {
                // 调用抽奖服务接口，将抽奖机会发放给用户
                log.info("用户{}完成了7天打卡，赠送优惠券", userInfo.getUnionid());
                signInUser.setSendCouponFlag(1);
                ReceiveCouponVo receiveCouponVo = new ReceiveCouponVo(config.getCouponCode(), null, 2);
                mujiOpenApiFeignClient.couponReceive(receiveCouponVo);

                UserLotteryPrizes userLotteryPrizes = new UserLotteryPrizes(campaignCode, 0,
                        userInfo.getId(), userInfo.getOpenid(), userInfo.getUnionid(), userInfo.getUsername(), userInfo.getMobile(), userInfo.getCardNo(),
                        null, "健康美容85折券一张", 2, 6, "签到完成即赠", "", "prizeAward11-de.png", null, null, null, config.getCouponCode());
                userLotteryPrizesMapper.insert(userLotteryPrizes);

            }

            if (signInUser.getSendLotteryFlag() == 0) {
                // 调用抽奖服务接口，将抽奖机会发放给用户
                log.info("用户{}完成了7天打卡，赠送抽奖机会", userInfo.getUnionid());
                signInUser.setSendLotteryFlag(1);
                // 调用抽奖服务接口，将抽奖机会发放给用户
                userLotterySummaryService.operation(campaignCode, userInfo.getUnionid(), "+", 1);

            }
            signInUser.setState(1);
            signInUserMapper.updateById(signInUser);
        }


    }


}
