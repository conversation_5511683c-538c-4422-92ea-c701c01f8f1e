package com.dz.common.core.fegin.basic;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.wechat.CodeSessionDTO;
import com.dz.common.core.dto.wechat.DecryptUserDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = ServiceConstant.BASIC_SERVICE_NAME, contextId = "MpConfigFeignClient")
public interface MpConfigFeignClient {

    /**
     * 根据租户ID获取小程序token
     * @param tenantId
     * @param cleanCach
     * @return
     */
    @GetMapping(value = "/mp_config/miniapp/accesstoken")
    public Result<String> getMiniappAccessToken(@RequestParam("tenantId") Long tenantId,@RequestParam(value = "cleanCach",required = false) Boolean cleanCach);

    /**
     * 小程序登录code换取session
     * @param appId
     * @return
     */
    @PostMapping(value = "/mp_config/code_session")
    public Result<CodeSessionDTO> getMinappSessionByCode(@RequestParam("appId") String appId,@RequestParam("code") String code);

    /**
     * 根据租户ID获取小程序appid
     * @param tenantId
     * @return
     */
    @GetMapping(value = "/mp_config/appid/by_tenant")
    public Result<String> getMiniappidByTenantId(@RequestParam("tenantId") Long tenantId);

    /**
     * 微信用户数据解密
     * @param encryptedData
     * @param iv
     * @param openId
     * @return
     */
    @PostMapping(value = "/mp_config/wxdata_decode")
    public Result<DecryptUserDTO> wxDataDecrypt(@RequestParam("encryptedData")String encryptedData, @RequestParam("iv")String iv, @RequestParam("openId")String openId);

}
