.city-content {
  display: flex;
  flex-direction: column;
  padding: 60rpx 40rpx 0;
  box-sizing: border-box;
  width: 100%;

  .title {
    height: 36rpx;
    font-family: NotoSansHans,
      NotoSansHans;
    font-weight: 500;
    font-size: 36rpx;
    color: #3C3C43;
    letter-spacing: 1px;
    text-align: center;
    font-style: normal;
    margin-bottom: 60rpx;
  }

  .picker-box {
    border-top: 1rpx solid #eee;
    border-bottom: 1rpx solid #eee;
    margin-bottom: 60rpx;
  }


  .city-item {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 28rpx;
  }

  .btn-box {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .btn-item {
      width: 100%;
      height: 80rpx;
      border-radius: 5rpx;
      background: #3C3C43;
      line-height: 80rpx;
      text-align: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #fff;
      font-style: normal;

    }
  }
}
