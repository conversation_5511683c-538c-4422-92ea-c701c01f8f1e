<view class="member-info" style="bottom: {{expandState ? '-148rpx' : '34rpx'}}">
  <view bindtap="handleExpand" wx:if="{{!expandState}}" class="expand-btn top-position" style="opacity: {{ expandState ? 0 : 1}};">收起我的MUJI档案
    <image bindtap="handleExpand" class="top-right-up" src="{{$cdn}}/member/right-d.png" mode="" />
  </view>
  <view class="member-bg" style="background-image: url({{$cdn}}/memberCard/bg.png);" bindtap="handleExpand">
    <view wx:if="{{expandState}}" class="expand-btn bottom-position" style="opacity: {{ expandState ? 1 : 0}};">查看我的MUJI档案
      <image class="top-right-up" bindtap="handleExpand" src="{{$cdn}}/member/right-up.png" mode="" />
    </view>
    <view class="member-content" style="opacity: {{ expandState ? 0 : 1}};">
      <view class="level-tag ellipsis" wx:if="{{userInfo.cardLevelName}}">{{userInfo.cardLevelName}}</view>
      <view class="member-avatar">
        <image src="{{userInfo.avatar?userInfo.avatar:$cdn+'/default-avatar.png'}}" mode="" />
      </view>
      <view class="member-desc">
        <view class="row">
          <view class="member-item1">
            <view class="item-label">昵称</view>
            <view style="margin-top: 5rpx;" class="item-value">{{userInfo.username}}</view>
          </view>
        </view>
        <view class="row">
          <view class="member-item" catchtap="toLegend">
            <view class="item-label">当前里程
              <image class="right-jian" src="{{$cdn}}/right-arrow.png" mode="" />
            </view>
            <view class="item-num">{{userInfo.currentMileage||0}}</view>
          </view>
          <view class="member-item" catchtap="toPotion">
            <view class="item-label">当前积分
              <image class="right-jian" src="{{$cdn}}/right-arrow.png" mode="" />
            </view>
            <view class="item-num">{{userInfo.points || 0}}</view>
          </view>
          <view class="member-item" catchtap="toCoupon">
            <view class="item-label">我的礼券
              <image class="right-jian" src="{{$cdn}}/right-arrow.png" mode="" />
            </view>
            <view class="item-num">{{couponNumInfo||0}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>