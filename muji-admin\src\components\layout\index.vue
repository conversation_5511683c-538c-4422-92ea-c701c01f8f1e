<template>
  <div class="layout">
    <div class="layout-header-top" v-if="$slots.headerTop">
      <slot name="headerTop"></slot>
    </div>
    <div class="layout-container">
      <div class="layout-container-left" :class="open?'':'close'" v-if="$slots.containerLeft" ref="containerLeft">
        <slot name="containerLeft"></slot>
        <div class="layout-container-open" @click="changeOpen">
          <LeftOutlined v-if="open" />
          <RightOutlined v-else />
        </div>
      </div>
      <div class="layout-container-right">
        <div class="layout-header" v-if="$slots.header">
          <slot name="header"></slot>
        </div>
        <div class="layout-top" v-if="$slots.topLeft || $slots.topRight">
          <slot name="topLeft">
            <div></div>
          </slot>
          <slot name="topRight">
            <div></div>
          </slot>
        </div>
        <div class="layout-content">
          <div class="layout-content-search" v-if="$slots.search">
            <slot name="search"></slot>
          </div>
          <div class="layout-content-data">
            <div ref="content" style="flex:1;overflow:hidden;">
              <slot :height="height"></slot>
            </div>
            <div class="layout-content-footer" v-if="$slots.footer">
              <slot name="footer"></slot>
            </div>
          </div>

        </div>
      </div>
    </div>

  </div>
</template>
<script setup>
import { useGlobalStore } from '@/store'
const global = useGlobalStore()
import { reactive, toRefs, ref, onMounted, computed, nextTick } from 'vue';
let content = ref(null)
let height = ref(0)
let containerLeft = ref(null)
// console.log(containerLeft)
const { open } = toRefs(reactive({
  open: true
}))


// 缩放
const changeOpen = async () => {
  containerLeft.value.style.width = open.value ? '0' : 'auto'
  let children = containerLeft.value?.children || []
  children.forEach(element => {
    element.style.opacity = 1
  });
  // 除了最后一个元素都设置为visible
  if (open.value) {
    children.forEach((element, index) => {
      index != children.length - 1 && (element.style.opacity = 0)
    });
  }
  await nextTick()
  open.value = !open.value
}

// 计算表格高度
onMounted(() => {
  height.value = content.value?.clientHeight
})
window.onresize = function () {
  let temp = content.value?.clientHeight
  if (temp != height.value) {
    height.value = temp
  }
}


// 页面搜索导致布局改变重新计算高度
const pageChange = () => {
  let temp = content.value?.clientHeight
  if (temp != height.value) {
    height.value = temp
  }
}

watch(() => global.layout, () => {
  pageChange()
})
</script>
<style lang="scss" scoped>
// $border: layoutBorderRadius;
.layout {
  height: 100%;
  display: flex;
  flex-direction: column;

  &-header-top {
    min-height: 46px;
    background: #ffffff;
    border-radius: 8px;
    padding: 0px 24px;
    margin-bottom: 12px;
  }

  &-container {
    height: 100%;
    flex: 1;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
    &-left {
      flex-shrink: 0;
      position: relative;
      margin-right: 20px;
      height: 100%;
      background: #fff;
      padding: 18px 20px 48px 20px;
      border-radius: $layoutBorderRadius;
      transition: all 0.2s;
    }
    &-open {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      height: 48px;
      background: #fafafa;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #3d3d3d;
    }

    &-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }
  }
  &-header {
    margin-bottom: 12px;
    padding: 18px 20px 2px;
    background: #ffffff;
    box-shadow: 0px 0px 25px 0px rgba(13, 11, 13, 0.06);
    border-radius: $layoutBorderRadius;
  }

  &-top {
    margin-bottom: 12px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
  }

  &-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    background: #ffffff;
    border-radius: $layoutBorderRadius;
    padding: 20px;

    &-search {
      flex-shrink: 0;
      position: relative;
      padding-right: 20px;
      margin-right: 20px;

      &::after {
        content: "";
        position: absolute;
        top: -20px;
        bottom: -20px;
        right: 0;
        width: 6px;
        background: #fafafb;
      }
    }

    &-data {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    &-footer {
      padding: 20px 20px 0;
    }
  }
}
</style>
