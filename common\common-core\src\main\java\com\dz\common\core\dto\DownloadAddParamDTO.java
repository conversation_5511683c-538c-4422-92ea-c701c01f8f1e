package com.dz.common.core.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 创建下载任务
 *
 * <AUTHOR>
 * @date 2022/8/11 18:39
 */
@Data
public class DownloadAddParamDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 业务报表类 bean
     * 参数必填
     */
    private String beanName;
    /**
     * 业务方生成报表接口post参数
     * post请求参数必填
     * get请求参数需要带在queryUrl
     */
    private String jsonParam;
    /**
     * 报表编码 eg:order
     * 参数必填
     */
    private String reportCode;
    /**
     * 菜单名称
     * eg：订单
     * 参数必填
     */
    private String menuName;
    /**
     * 所属业务名称
     * eg：订单列表
     * 参数必填
     */
    private String moduleName;
    /**
     * 报表名称
     * 参数必填
     */
    private String fileName;
    /**
     * 报表拓展名
     */
    private String fileExt;
    /**
     * 下载中心ID
     */
    private Long downloadCenterId;

    /**
     * 租户id 无租户概念传 1
     * 参数必填
     */
    private Long tenantId;

    /**
     * 创建人
     */
    private String createAt;

    /**
     * 业务方报表表头 ,隔开
     */
    private String header;
}
