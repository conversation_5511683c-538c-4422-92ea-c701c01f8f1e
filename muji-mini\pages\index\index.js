const util = require("../../utils/util.js");
const app = getApp();
import {
	apiIndex,
	getTemplateByType,
	getAgreeRecord,
	agreeRecord,
} from "../../api/index";

Page({
	data: {
		showDialog: false,
		widgets: [],
		info: {},
		pageSetting: null,
		open: true,
		loading: false,
	},
	async onLoad() {
		console.log("%c 首页onload", "color: red", this.data.open);
		this.setData({
			loading: true,
		});
		Promise.all([
			// 跳转开屏页
			this.ifOpen().then((res) => {
				if (!res) {
					// let time = setTimeout(() => {
					//   clearTimeout(time);
					//   time = null;
					this.setData({
						open: false,
					});
					// }, 4000);
				} else {
					this.setData({
						open: false,
					});
				}
				return res;
			}).catch((err) => {
				this.setData({
					open: false,
				});
				return true
			}),
			// 页面信息
			this.getInfo(),
		]).then((res) => {
			this.setData({
				loading: false,
			});
			// 不跳转开屏页  延迟弹窗
			if (res[0]) {
				this.modalDelay(10);
			}
			let {
				info: { pageSetting },
			} = this.data;
			// 禁止分享
			if (!pageSetting.isShare) {
				wx.hideShareMenu({
					menus: ["shareAppMessage", "shareTimeline"],
				});
			}
		});
	},
	async onShow() {

		if (typeof this.getTabBar === "function" && this.getTabBar()) {
			this.getTabBar().setData({
				selected: 0,
				isBirthday: app.globalData.isBirthday,
				visitor: app.globalData.visitor, // 游客模式
			});
		}
		// 开屏回来  弹窗延迟
		if (!this.data.open) {
			this.modalDelay(0);
		}
	},
	onHide() {
		console.log("%c 首页onHide", "color: red");
		console.log(this.selectAllComponents())
	},
	onUnload() {
		console.log("%c 首页onUnload", "color: red");
	},
	// 首页自动弹窗延迟
	modalDelay(delayTime) {
		if (!this.data.pageSetting) {
			let time = setTimeout(() => {
				clearInterval(time);
				time = null;
				this.setData({
					pageSetting: this.data.info.pageSetting,
				});
			}, delayTime);
		}
	},
	// 启动页开启判断
	ifOpen() {
		return new Promise(async (resolve, reject) => {
			// 判断是否是第一个页面是首页 不是首页不跳转开屏页
			let obj = wx.getLaunchOptionsSync()
			console.log('第一次进入的数据', obj, app.globalData.isOpen, '----------------------------')
			if (obj.path != 'pages/index/index') {
				reject()
				return
			}
			// 如果已经播放过开屏页了 就不在播放了
			if (app.globalData.isOpen) {
				reject()
				return
			}
			// 设置启动页开启
			let {
				state,
				openFrequency,
				openDays,
				openNumer,
			} = app.globalData.styleSetting.openStyle;
			let goOpen = false;
			if (state) {
				// 判断开屏频率
				if (openFrequency == 1) {
					goOpen = true;
				} else if (openFrequency == 2) {
					if (!app.getTodaySub("openModal")) {
						app.setTodaySub("openModal", 1);
						goOpen = true;
					}
				} else if (openFrequency == 3) {
					app.setDaysSub("openMore");
					if (app.getDaysSub("openMore", openDays, openNumer)) {
						goOpen = true;
					}
				}
			}
			if (goOpen) {
				let that = this;
				// 第一次进入open1  其他开屏进入open
				let { data: ifFirstOpen } = await getAgreeRecord({ agreementId: 2 });
				if (!ifFirstOpen) {
					agreeRecord({ agreementId: 2 });
				}
				let url = !ifFirstOpen ? "/pages/open1/open1" : "/pages/open/open";
				app.globalData.isOpen = true
				wx.$mp.navigateTo({
					url,
					events: {
						// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
						success() {
							that.setData({
								open: false,
							});
						},
					},
				});
				resolve(false);
			} else {
				resolve(true);
			}
		});
	},
	// 获取开屏数据
	getOpenData() {
		return getTemplateByType({
			//  templateType	模板类型 1常规页面 2系统页面 3定制页面 4弹窗页面 5代码级页面
			templateType: 2,
			// pageType	页面类型 0二级自定义页面 1首页 2兑换 3会员码 4生活圈 5更多 7-启动页 18开屏页
			pageType: 18,
		}).then((res) => {
			res.data.content = res.data?.content ? JSON.parse(res.data.content) : {};
			res.data.actionLinks = res.data.content.actionLinks; // 逻辑页面
			res.data.navSetting = res.data.content.navSetting; // 导航
			res.data.pageSetting = res.data.content.pageSetting; // 页面设置
			res.data.componentSetting = res.data.content.componentSetting; // 组件设置
			console.log("开票res.data", res.data);
			return res.data;
		});
	},
	// 获取模板数据
	getInfo() {
		return getTemplateByType({
			//  templateType	模板类型 1常规页面 2系统页面 3定制页面 4弹窗页面
			templateType: 1,
			// pageType	页面类型 0二级自定义页面 1首页 2兑换 3会员码 4生活圈 5更多
			pageType: 1,
		}).then((res) => {
			res.data.content = res.data?.content ? JSON.parse(res.data.content) : {};
			res.data.actionLinks = res.data.content.actionLinks; // 逻辑页面
			res.data.navSetting = res.data.content.navSetting; // 导航
			res.data.pageSetting = res.data.content.pageSetting; // 页面设置
			res.data.componentSetting = res.data.content.componentSetting; // 组件设置
			this.setData({
				info: res.data,
			});
		});
	},

	handleConfirm() {
		console.log("确定");
	},
	handleCancel() {
		console.log("取消");
	},
	handleClose() {
		console.log("关闭");
	},
	onShareAppMessage() {
		return {
			title: "MUJI passport会员，畅享购物福利",
			path: `/pages/index/index?inviteUserId=${app.globalData.userInfo.id}`,
			imageUrl: `${wx.$config.ossImg}/share/normal.jpg`,
		};
	},
});
