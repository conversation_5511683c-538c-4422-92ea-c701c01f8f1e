
// 所有的命名必须全局唯一
import service from '@/utils/request.js'

//



// 列表
export function benefitLista(data) {
    return service({
        url: '/crm/user/benefit/list',
        method: 'get',
        data
    })
}
// 列表 新增
export function benefitAdd(data) {
    return service({
        url: '/crm/user/benefit/add',
        method: 'post',
        data
    })
}

// 列表编辑
export function benefitUpdate(data) {
    return service({
        url: '/crm/user/benefit/update',
        method: 'post',
        data
    })
}
// 列表 删除
export function benefitDelete(data) {
    return service({
        url: '/crm/user/benefit/delete',
        method: 'post',
        data
    })
}

export function benefitInfo(data) {
    return service({
        url: '/crm/user/benefit/info',
        method: 'get',
        data
    })
}
//不带分页的查询 权益列表

export function benefitList_all(data) {
    return service({
        url: '/crm/user/benefit/list_all',
        method: 'get',
        data
    })
}