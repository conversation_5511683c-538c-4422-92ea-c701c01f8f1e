package com.dz.ms.${typeName}.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.ms.${typeName}.dto.${objectName}DTO;
import com.dz.ms.${typeName}.entity.${objectName};

/**
 * ${tableComment}接口
 * @author: ${author}
 * @date:   ${nowDate}
 */
public interface ${objectName}Service extends IService<${objectName}> {

	/**
     * 分页查询${tableComment}
     * @param param
     * @return PageInfo<${objectName}DTO>
     */
    public PageInfo<${objectName}DTO> get${objectName}List(${objectName}DTO param);

    /**
     * 根据ID查询${tableComment}
     * @param ${primarkUpper}
     * @return ${objectName}DTO
     */
    public ${objectName}DTO get${objectName}ById(${primarkMType} ${primarkUpper});

    /**
     * 保存${tableComment}
     * @param param
     * @return Long
     */
    public Long save${objectName}(${objectName}DTO param);

    /**
     * 根据ID删除${tableComment}
     * @param param
     */
    public void delete${objectName}ById(IdCodeDTO param);

}
