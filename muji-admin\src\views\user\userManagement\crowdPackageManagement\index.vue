<template>

  <layout>
    <template v-slot:header>
      <searchForm :formParams="formParams" @cancel="resetData" @ok="refreshData" :disabled="!$hasPermission('crowdPackageManagement:search')">
        <a-form-item name="crowdName">
          <a-input placeholder="人群包名称" allow-clear v-model:value="formParams.crowdName" allowClear @keyup.enter="refreshData"></a-input>
        </a-form-item>

      </searchForm>
    </template>
    <template v-slot:topRight>
      <a-space>
        <!-- :disabled="!$hasPermission('open:leaveUser:synchLeaveUser')" 权限标识 -->
        <a-button type="primary" :disabled="!$hasPermission('crowdPackageManagement:save')" @click="addChang">新建人群包</a-button>

      </a-space>

    </template>
    <template v-slot="{ height }">
      <a-table :indentSize="20" row-key="id" :scroll="{ scrollToFirstRowOnChange: true, y: height - 88, x: '100%' }" :dataSource="dataSource" :columns="tableHeader" :pagination="pagination" :loading="loading" @change="loadData">
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'index'">
            {{ (current - 1) * pageSize + 1 + index }}
          </template>
          <template v-if="column.key === 'startTime'">
            <div v-if="record.timeType == '0'">
              <template v-if="record.startTime && record.endTime">
                <div>{{record.startTime}}</div>
                <div>{{record.endTime}}</div>
              </template>
              <div v-else>永久可用</div>
            </div>
            <div v-else>永久可用</div>
          </template>
          <template v-if="column.key === 'crowdStatus'">
            <a-tag color="red" v-if="record.crowdStatus == 1">停用中</a-tag>
            <a-tag color="success" v-else>启用中</a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-button type="link" :disabled="!$hasPermission('crowdPackageManagement:update')" @click="EditRole(record)">编辑</a-button>
            <a-divider type="vertical" />

            <a-popconfirm :title="`确定要${record.crowdStatus == '1'?'启用':'停用'}吗？`" @confirm="handleEnable(record)" :disabled="!$hasPermission('crowdPackageManagement:status')">
              <a-button :disabled="!$hasPermission('crowdPackageManagement:status')" type="link">{{record.crowdStatus == '1'?'启用':'停用'}}</a-button>
            </a-popconfirm>
            <!-- <a-popconfirm title="确定要停用吗？" @confirm="handleEnable(record)">
              <a-button :disabled="!$hasPermission('crowdPackageManagement:update')|| record.crowdStatus == '1'" type="link">停用</a-button>
            </a-popconfirm>
            <a-divider type="vertical" />
            <a-popconfirm title="确定要启用吗？" @confirm="handleEnable(record)">
              <a-button :disabled="!$hasPermission('crowdPackageManagement:update') ||record.crowdStatus == '0'" type="link">启用</a-button>
            </a-popconfirm> -->
            <template v-if="record.crowdStatus == '1'">
              <a-divider type="vertical" />
              <a-popconfirm title="是否确定删除该人群包吗？" @confirm="handleDelete(record)">
                <a-button :disabled="!$hasPermission('crowdPackageManagement:delete')" type="link">删除</a-button>
              </a-popconfirm>
            </template>

          </template>

        </template>
      </a-table>
    </template>
  </layout>
  <addCrowdPackage :visible="visible" @ok="updateList" @cancel="visible = false" :id="id" :type="type" />

</template>
<script setup>
import addCrowdPackage from './components/addCrowdPackage.vue'
import { useRoute, useRouter } from 'vue-router'
import { usePagination, useRequest, } from 'vue-request';
import { crowdList, crowdDelete, crowdUpdate_state } from '@/http/index.js'
import { message, Modal } from "ant-design-vue";
import { crowdTypeOptions } from '@/utils/dict-options'
// 分页参数
let pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ["bottomLeft"],
  };
});
// 表格分页数据获取
const total = ref(0)


const { formParams, tableHeader, visible, id, type, pdTypeOptions } = toRefs(reactive({


  id: '',
  type: 0,// 0-新增 1-编辑  2-查看
  visible: false,
  pdTypeOptions: [],
  formParams: {
    crowdName: ''
  },

  tableHeader: [{
    title: '序号',
    key: 'index',
    align: 'center',
    width: 80
  },
  {
    title: '人群包名称',
    dataIndex: 'crowdName',
    width: 180,
    align: 'center',

    customRender: (row) => {
      // console.log(row);

      return row.text || '--'; // 如果数据为空，则显示 '----'
    }
  },
  {
    title: '人群包可用时间',
    key: 'startTime',
    align: 'center',
    ellipsis: true,


  },
  {
    title: '人群包类型',
    dataIndex: 'crowdType',
    align: 'center',
    ellipsis: true,
    customRender: (row) => {
      //   console.log(row);
      let textLabel = null
      if (typeof row.text == 'number') {
        textLabel = crowdTypeOptions.find(item => item.value == row.text)
      }
      //   console.log(textLabel);
      return textLabel && textLabel.label || '--'; // 如果数据为空，则显示 '----' textLabel.label ||
    }

  },
  {
    title: '启停状态',
    key: 'crowdStatus',
    align: 'center',
    ellipsis: true,



  },


  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    align: 'center',
    width: 180,
    fixed: 'right'
  }]
})
);
const { data: dataSource, run, loading, pageSize, current, refresh } = usePagination((param) => {
  // 这里修改参数
  // return resignationInheritanceList({ ...param, ...getParams() })
  return crowdList({ ...param, ...formParams.value })
},
  {
    manual: false, // 修改为false,让其自动执行
    pagination: {
      currentKey: 'pageNum',  // 通过该值指定接口 当前页数 参数的属性值
      pageSizeKey: 'pageSize',// 通过该值指定接口 每页获取条数 参数的属性值
      totalKey: 'data.data.count',  // 指定 data 中 total 属性的路径
    },
    formatResult: res => { // 返回数据格式化
      total.value = res.data.count
      return res.data.list
    }
  });

function updateList(value) {
  visible.value = false;
  // 新增 重置搜索数据
  if (!value) {
    resetData();
  } else {
    // 查看、编辑 不需要清空搜索数据 直接刷新
    refresh();
  }
}
function handleEnable(record) {
  crowdUpdate_state({ id: record.id, crowdStatus: record.crowdStatus == '0' ? 1 : 0 }).then(res => {
    if (res.code === 0) {
      message.success(`${record.state == '0' ? '启用' : '停用'}成功`)
      resetData()
    }
  })
}
function EditRole(record) {
  visible.value = true
  type.value = 1
  id.value = record.id
  // console.log(record, id.value);

}
//删除
const handleDelete = (record) => {
  crowdDelete({ id: record.id }).then(res => {
    if (res.code === 0) {
      message.success('删除成功')
      resetData()
    }
  })
}
// 加载数据
const loadData = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}


// 刷新数据
const refreshData = () => {
  run({ pageNum: 1, pageSize: pageSize.value })
};

// 重置数据
const resetData = () => {
  formParams.value = {
    crowdName: '',
  }
  refreshData()
}
function addChang() {
  visible.value = true
  type.value = 0
  id.value = ''
}
</script>
