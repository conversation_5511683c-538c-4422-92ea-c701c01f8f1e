<template>
  <a-modal v-model:open="thisFields.open" title="" @ok="thisMethods.handleOk" @cancel="thisMethods.cancel" :okButtonProps="{ disabled: thisFields.loading }" width="1200px">
    <layout>
      <template v-slot:header>
        <!--:disabled="!$hasPermission('mall:permission:searchGiftRack')"-->
        <searchForm :formParams="searchFields" @cancel="whenClickReset" @ok="whenClickSearch">
          <a-form-item label="商品名称" name="productName">
            <a-input placeholder="请输入" allow-clear v-model:value="searchFields.productName" allowClear @keyup.enter="whenClickSearch"></a-input>
          </a-form-item>
          <BaseProductTypeSelect label="商品类型" name="pdType" v-model="searchFields.pdType" />
          <BaseProductDisplayStatusSelect label="展示状态" name="beShow" v-model="searchFields.beShow" />
          <BaseProductCornerSelect :options="thisFields.productCornerList" label="角标" name="superscriptId" v-model="searchFields.superscriptId" />
          <a-form-item label="商品库存小于" name="inventoryLess">
            <a-input-number placeholder="请输入" allow-clear v-model:value="searchFields.inventoryLess" allowClear @keyup.enter="whenClickSearch" style="width: 202px;"></a-input-number>
          </a-form-item>
        </searchForm>
      </template>
      <template v-slot:topRight>
        <div class="batch-wrap">
          <a-popconfirm :disabled="!thisFields.selectedRowObjs.length" title="是否确定要批量删除？" @confirm="thisMethods.batchDel">
            <a-button :disabled="!thisFields.selectedRowObjs.length" type="link">批量删除</a-button>
          </a-popconfirm>
          <template v-if="bizType === ''">
            <a-popconfirm :disabled="!thisFields.selectedRowObjs.length" title="是否确定要批量不展示？" @confirm="thisMethods.batchHide">
              <a-button :disabled="!thisFields.selectedRowObjs.length" type="link">批量不展示</a-button>
            </a-popconfirm>
            <a-popconfirm :disabled="!thisFields.selectedRowObjs.length" title="是否确定要批量展示？" @confirm="thisMethods.batchShow">
              <a-button :disabled="!thisFields.selectedRowObjs.length" type="link">批量展示</a-button>
            </a-popconfirm>
            <div style="width: 15px;height: 1px;"></div>
            <a-button type="primary" @click="thisMethods.ProductInventoryTaskManagementDialogHandler" :disabled="!props.shelfId">
              库存任务管理
            </a-button>
          </template>
        </div>
      </template>
      <template v-slot="{ height }">
        <a-table class="shelf-product-management-table" :indentSize="20" row-key="uuid" :row-selection="{
          preserveSelectedRowKeys: true,
          selectedRowKeys: thisFields.selectedRowKeys,
          onChange: thisMethods.onSelectChange,
          getCheckboxProps: thisMethods.getCheckboxProps
        }" :scroll="{ scrollToFirstRowOnChange: true, x: '100%' }" :dataSource="dataSource" :columns="thisFields.tableHeader" :pagination="pagination" :loading="loading" @change="whenPaginationChange">
          <template #headerCell="{ text, column }">
            <template v-if="column.dataIndex === 'superscriptIdList'">
              <a-popover>
                <template #content>
                  <div>一个商品最多展示1个角标</div>
                  <!-- <div>先展示商品角标</div>
                  <div>后展示活动角标</div> -->
                </template>
                <div class="iconWrap">
                  <span class="mr5">展示角标</span>
                  <ExclamationCircleOutlined />
                </div>
              </a-popover>
            </template>
            <!-- <template v-if="column.dataIndex === 'superscriptCampaign'">
              <a-popover>
                <template #content>
                  <div>活动角标根据运营活动配置产生</div>
                </template>
                <div class="iconWrap">
                  <span class="mr5">活动角标</span>
                  <ExclamationCircleOutlined />
                </div>
              </a-popover>
            </template> -->
          </template>
          <template #bodyCell="{ text, record, index, column }">
            <template v-if="column.dataIndex === 'index'">{{ (current - 1) * pageSize + 1 + index }}</template>
            <template v-if="column.dataIndex === 'onShelfIndex'">
              <a-button type="link" @click="thisMethods.moveUp(record, index)" :disabled="index === 0 && current === 1 || thisFields.disabledMove">
                上移
              </a-button>
              <a-button class="ml5" type="link" @click="thisMethods.moveDown(record, index)" :disabled="index === (dataSource.length - 1) && current === totalPage || thisFields.disabledMove">
                下移
              </a-button>
            </template>
            <template v-if="column.dataIndex === 'deliveryType'">
              <BaseProductDeliveryMethodsSelect :allowClear="false" style="width: 100%;" label="" name="deliveryType" v-model="record.shelfProductItem.deliveryType" class="resetChildAntFormItem" />
            </template>
            <template v-if="column.dataIndex === 'beShow'">
              <BaseProductDisplayStatusSelect :allowClear="false" :disabled="props.bizType === 'crowdPurchaseRestriction' || record.shelfProductItem.beShowDisabled" style="width: 100%;" label="" name="beShow" v-model="record.shelfProductItem.beShow" class="resetChildAntFormItem" />
            </template>
            <template v-if="column.dataIndex === 'showType'">
              <BaseProductDisplayMethodsSelect :allowClear="false" style="width: 100%;" label="" name="showType" v-model="record.shelfProductItem.showType" class="resetChildAntFormItem" />
            </template>
            <template v-if="column.dataIndex === 'onInventory'">
              <template v-if="props.bizType === 'crowdPurchaseRestriction'">
                {{ record.shelfProductItem.onInventory }}
              </template>
              <a-form-item v-else label="" name="onInventory" class="resetChildAntFormItem">
                <a-input :disabled="!!record.shelfProductItem.id" :allowClear="true" placeholder="请输入" style="width: 100%;" @blur="e => { const match = e.target.value.match(/[1-9]\d*/ig) || []; record.shelfProductItem.onInventory = match[0] || ''; }" v-model:value="record.shelfProductItem.onInventory" />
              </a-form-item>
            </template>
            <template v-if="column.dataIndex === 'ruleInventory'">
              <a-form-item label="" name="ruleInventory" class="resetChildAntFormItem">
                <a-input-number :disabled="!!record.shelfProductItem.id && bizType === ''" :allowClear="true" :max="record.shelfProductItem.currentInventory" :min="0" placeholder="请输入" style="width: 100%;" @blur="e => { const match = e.target.value.match(/[1-9]\d*/ig) || []; record.shelfProductItem.ruleInventory = match[0] || ''; }" v-model:value="record.shelfProductItem.ruleInventory" />
              </a-form-item>
            </template>
            <template v-if="column.dataIndex === 'shelfImg'">
              <a-image :src="record.shelfImg" :width="50" :height="50" />
            </template>
            <template v-if="column.dataIndex === 'tagList'">
              <div v-if="!record?.tagList?.length">-</div>
              <div v-else :title="record?.tagList?.map(v => v.name)?.join('、')" class="tagListWrap">{{
                record?.tagList?.map(v => v.name)?.join('、') }}</div>
            </template>
            <template v-if="column.dataIndex === 'costPoint'">
              <a-form-item label="" name="costPoint" class="resetChildAntFormItem">
                <a-input :allowClear="true" placeholder="请输入" style="width: 100%;" @blur="e => { const match = e.target.value.match(/[1-9]\d*/ig) || []; record.shelfProductItem.costPoint = match[0] || ''; }" v-model:value="record.shelfProductItem.costPoint" />
              </a-form-item>
            </template>
            <template v-if="column.dataIndex === 'prePoint'">
              <a-form-item label="" name="prePoint" class="resetChildAntFormItem">
                <a-input :allowClear="true" placeholder="请输入" style="width: 100%;" @blur="e => { const match = e.target.value.match(/[1-9]\d*/ig) || []; record.shelfProductItem.prePoint = match[0] || ''; }" v-model:value="record.shelfProductItem.prePoint" />
              </a-form-item>
            </template>
            <template v-if="column.dataIndex === 'limitNum'">
              <a-form-item label="" name="limitNum" class="resetChildAntFormItem">
                <a-input :allowClear="true" placeholder="请输入" style="width: 100%;" @blur="e => { const match = e.target.value.match(/[1-9]\d*/ig) || []; record.shelfProductItem. limitNum = match[0] || ''; }" v-model:value="record.shelfProductItem. limitNum" />
              </a-form-item>
            </template>
            <template v-if="column.dataIndex === 'everyoneLimit'">
              <a-form-item label="" name="everyoneLimit" class="resetChildAntFormItem">
                <a-input :allowClear="true" placeholder="请输入" style="width: 100%;" @blur="e => { const match = e.target.value.match(/[1-9]\d*/ig) || []; record.shelfProductItem.everyoneLimit = match[0] || ''; }" v-model:value="record.shelfProductItem.everyoneLimit" />
              </a-form-item>
            </template>
            <template v-if="column.dataIndex === 'superscriptIdList'">
              <div v-if="bizType === 'crowdPurchaseRestriction' && !record.shelfProductItem.superscriptIdList?.length">-
              </div>
              <BaseProductCornerSelect v-else :maxTagCount="props.bizType === 'crowdPurchaseRestriction' ? 100 : 1" :disabled="props.bizType === 'crowdPurchaseRestriction'" :options="thisFields.productCornerList" style="width: 100%;" label="" name="superscriptIdList" v-model="record.shelfProductItem.superscriptIdList" mode="multiple" class="resetChildAntFormItem" multipleMax="1" />
            </template>
            <!-- <template v-if="column.dataIndex === 'superscriptCampaign'">
              <div v-if="record.shelfProductItem.superscriptCampaign.length>0"> {{ record.shelfProductItem.superscriptCampaign }}</div>
              <div v-else>--</div>
            </template> -->
            <template v-if="column.dataIndex === 'action'">
              <a-button class="ml5" type="link" :disabled="record.shelfProductItem.beShow === 1 && props.bizType === ''" @click="thisMethods.handleDel(record)">删除
              </a-button>
            </template>
          </template>
        </a-table>
      </template>
    </layout>
  </a-modal>
  <!--
  注意：不能使用props.beSelectedProductIdArrayObjectList[0]?.shelfId去获取shelfId
  因为：编辑时props.beSelectedProductIdArrayObjectList里的项可以被全部删除
  -->
  <ShelfProductInventoryTaskManagementDialog @changeCurrentInventory="changeCurrentInventory" v-model="thisFields.ShelfProductInventoryTaskManagementDialogOpen" :shelfId="props.shelfId" />
</template>
<script setup>
import ShelfProductInventoryTaskManagementDialog from '@/views/mall/shelfManagement/giftRack/components/ShelfProductInventoryTaskManagementDialog.vue'
import { usePagination } from 'vue-request'
import { nextTick, onMounted, watch } from 'vue'
import { cloneDeep } from 'lodash'
import BaseProductDisplayStatusSelect from '@/components/BaseProductDisplayStatusSelect.vue'
import { message } from 'ant-design-vue'
import { apiShelfCrowd } from '@/http/index.js'

const props = defineProps({
  bizType: { // 业务类型：默认-兑礼货架、crowdPurchaseRestriction-人群限购
    type: String,
    default: ''
  },
  shelfId: {
    type: [String, Number],
    default: ''
  },
  beSelectedProductIdArrayObjectList: {
    type: Array,
    default: () => ([])
  },
  modelValue: {
    type: Boolean,
    default: false
  }
})
const emits = defineEmits(['update:modelValue', 'ok', 'changeCurrentInventory'])

const pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    pageSizeOptions: ['10', '20', '50'],
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ['bottomLeft']
  }
})
const total = ref(0)
const totalPage = ref(0)
const getDefaultSearchFields = () => ({
  productName: undefined,
  pdType: undefined,
  beShow: undefined,
  superscriptId: undefined,
  inventoryLess: null
})
const searchFields = reactive(getDefaultSearchFields())
const thisFields = reactive({
  selectedRowKeys: [],
  selectedRowObjs: [],
  productCornerList: [],
  beSelectedProductIdArrayObjectList: [],
  disabledMove: false,
  ShelfProductInventoryTaskManagementDialogOpen: false,
  loading: false,
  open: false,
  tableHeader: [
    { title: '序号', dataIndex: 'index', align: 'center', width: 80 },
    { title: '商品在货架上的位置', dataIndex: 'onShelfIndex', align: 'center', ellipsis: true, width: 180 },
    { title: '商品名称', dataIndex: 'productName', align: 'center', ellipsis: true, width: 100 },
    { title: '商品货架ID', dataIndex: 'id', align: 'center', ellipsis: true, width: 100 },
    { title: '商品类型', dataIndex: 'pdTypeDesc', align: 'center', ellipsis: true, width: 100 },
    { title: '发货方式', dataIndex: 'deliveryType', align: 'center', ellipsis: true, width: 120 },
    { title: '展示状态', dataIndex: 'beShow', align: 'center', ellipsis: true, width: 120 },
    { title: '展示方式', dataIndex: 'showType', align: 'center', ellipsis: true, width: 120 },
    { title: '上架库存', dataIndex: 'onInventory', align: 'center', ellipsis: true, width: 120 },
    { title: '目前库存', dataIndex: 'currentInventory', align: 'center', ellipsis: true, width: 100 },
    { title: '累计兑换量', dataIndex: 'exchangeNum', align: 'center', ellipsis: true, width: 100 },
    { title: '商品主图', dataIndex: 'shelfImg', align: 'center', ellipsis: true, width: 100 },
    { title: '商品标签', dataIndex: 'tagList', align: 'center', ellipsis: true, width: 100 },
    { title: '积分价值', dataIndex: 'costPoint', align: 'center', ellipsis: true, width: 120 },
    { title: '积分划线价', dataIndex: 'prePoint', align: 'center', ellipsis: true, width: 120 },
    { title: '默认限购数/月', dataIndex: 'limitNum', align: 'center', ellipsis: true, width: 140 },
    { title: '展示角标', dataIndex: 'superscriptIdList', align: 'center', ellipsis: true, width: 200 },
    // { title: '活动角标', dataIndex: 'superscriptCampaign', align: 'center', ellipsis: true, width: 160 },
    { title: '操作', dataIndex: 'action', align: 'center', width: 60, fixed: 'right' }
  ]
})
const thisMethods = {
  batchDel() {
    let hasShownProduct = false;
    thisFields.selectedRowObjs.forEach(v => {
      if (props.bizType === '') {
        if (v.shelfProductItem.beShow === 0) {
          thisMethods.handleDel(v)
        } else {
          hasShownProduct = true;
        }
      } else {
        thisMethods.handleDel(v)
      }
    })
    if (hasShownProduct) {
      message.info('请把商品设置不展示')
    }
    thisMethods.clearSelected()
  },
  batchHide() {
    thisFields.selectedRowObjs.forEach(v => {
      v.shelfProductItem.beShow = 0
    })
    // thisMethods.clearSelected()
  },
  batchShow() {
    thisFields.selectedRowObjs.forEach(v => {
      v.shelfProductItem.beShow = 1
    })
    // thisMethods.clearSelected()
  },
  onSelectChange(e, a) {
    // console.log('onSelectChange：', e, a)
    thisFields.selectedRowKeys = e
    thisFields.selectedRowObjs = a
  },
  clearSelected() {
    thisFields.selectedRowKeys = []
    thisFields.selectedRowObjs = []
  },
  getCheckboxProps(record) {
    let disabled = false
    return { disabled }
  },
  ProductInventoryTaskManagementDialogHandler() {
    thisFields.ShelfProductInventoryTaskManagementDialogOpen = true
  },
  moveUp(record, index) {
    index = (current.value - 1) * pageSize.value + index
    const cur = thisFields.beSelectedProductIdArrayObjectList.splice(index, 1)
    thisFields.beSelectedProductIdArrayObjectList.splice(index - 1, 0, ...cur)
    // console.log('moveUp index, cur', index, cur)
    refresh()
  },
  moveDown(record, index) {
    index = (current.value - 1) * pageSize.value + index
    const cur = thisFields.beSelectedProductIdArrayObjectList.splice(index, 1)
    thisFields.beSelectedProductIdArrayObjectList.splice(index + 1, 0, ...cur)
    // console.log('moveDown index, cur', index, cur)
    refresh()
  },
  handleDel(record) {
    console.log("🚀 ~ handleDel ~ record:", record)
    // 需要根据uuid进行匹配，因为筛选后，index错位，会导致匹配不上。
    const idx = thisFields.beSelectedProductIdArrayObjectList.findIndex(v => v.uuid === record.uuid)
    const cur = thisFields.beSelectedProductIdArrayObjectList.splice(idx, 1)
    // console.log('handleDel index, cur', idx, cur)
    refresh()
  },
  async setOpen() {
    thisFields.open = props.modelValue
    console.log('🚀 ~ setOpen ~ thisFields.open:', thisFields.open)
    if (thisFields.open) {
      // console.log("🚀 ~ setOpen ~ thisFields.open:", thisFields.open)
      thisFields.beSelectedProductIdArrayObjectList = cloneDeep(props.beSelectedProductIdArrayObjectList)
      whenClickReset()
    }
    if (!thisFields.productCornerList.length) {
      const res = await apiShelfCrowd.getAllPageList({ state: 1 })
      thisFields.productCornerList = res.data
    }
  },
  cancel() {
    emits('update:modelValue', false)
    thisMethods.clearSelected()
  },
  handleOk() {
    // console.log('dataSource.value：', dataSource.value)
    // console.log('thisFields.beSelectedProductIdArrayObjectList：', thisFields.beSelectedProductIdArrayObjectList)
    let index = -1
    let name = ''
    let text = ''
    try {
      thisFields.beSelectedProductIdArrayObjectList.forEach((v, i) => {
        if (props.bizType === '') {
          if (!v.showType) {
            name = '展示方式'
            index = i
            throw new Error('校验未通过')
          }
        }
        if (!v.onInventory) {
          name = '上架库存'
          index = i
          throw new Error('校验未通过')
        }
        if (props.bizType === 'crowdPurchaseRestriction') {
          if (v.ruleInventory > v.currentInventory) {
            text = '活动库存不能大于目前库存'
            index = i
            throw new Error('校验未通过')
          }
        }
        if (!v.costPoint) {
          name = '积分价值'
          index = i
          throw new Error('校验未通过')
        }
      })
    } catch (e) {
    }
    if (index !== -1) {
      const idx = index + 1
      const pageNo = Math.ceil(idx / pageSize.value)
      const currentIndexInPageNo = idx % pageSize.value
      pagination.current = pageNo
      // 跳入到对应的分页让用户填写信息时，不能附加搜索条件。否则匹配不上。
      Object.assign(searchFields, getDefaultSearchFields())
      run({ pageNum: pageNo, pageSize: pageSize.value })
      const prefixText = `第${pageNo}页第${currentIndexInPageNo}项`
      let allText = `${prefixText}${name}必填`
      if (text) allText = `${prefixText}${text}`
      return message.warning(`${allText}`)
    }
    emits('update:modelValue', false)
    emits('ok', cloneDeep(thisFields.beSelectedProductIdArrayObjectList))
    thisMethods.clearSelected()
  }
}

function changeCurrentInventory() {
  emits('changeCurrentInventory')
}

// 获取商品管理入库值以及默认值
const setProductManagementDefaultFields = (shelfProductItem = {}) => {
  shelfProductItem.id = shelfProductItem.id ?? undefined
  shelfProductItem.productId = shelfProductItem.productId ?? undefined
  shelfProductItem.deliveryType = shelfProductItem.deliveryType ?? 1
  shelfProductItem.beShow = shelfProductItem.beShow ?? 1
  if (shelfProductItem.beShow === 3) {
    shelfProductItem.beShow = 0
    shelfProductItem.beShowDisabled = true
  }
  shelfProductItem.showType = shelfProductItem.showType ?? undefined
  shelfProductItem.onInventory = shelfProductItem.onInventory ?? ''
  shelfProductItem.costPoint = shelfProductItem.costPoint ?? ''
  shelfProductItem.prePoint = shelfProductItem.prePoint ?? ''
  if (shelfProductItem.id) {
    shelfProductItem.limitNum = shelfProductItem.limitNum ?? ''
  } else {
    shelfProductItem.limitNum = shelfProductItem.limitNum ?? '3'
  }
  shelfProductItem.superscriptIdList = shelfProductItem.superscriptIdList ?? []
  shelfProductItem.superscriptCampaign = shelfProductItem.superscriptCampaign ?? []
  // console.log('shelfProductItem：', shelfProductItem)
}
const productNoDistinctList = async (query) => {
  const db = cloneDeep(thisFields.beSelectedProductIdArrayObjectList)
  // console.log('query：', query, db)
  const pageNum = query.pageNum
  const pageSize = query.pageSize
  const totalList = db.filter(v => {
    let isPass = true
    if (searchFields.productName) {
      if (!v.productName.includes(searchFields.productName)) {
        isPass = false
      }
    }
    if (searchFields.pdType !== undefined) {
      if (v.pdType !== searchFields.pdType) {
        isPass = false
      }
    }
    if (searchFields.beShow !== undefined) {
      if (v.beShow !== searchFields.beShow) {
        isPass = false
      }
    }
    if (searchFields.superscriptId !== undefined) {
      if (!v.superscriptIdList.includes(searchFields.superscriptId)) {
        isPass = false
      }
    }
    if (searchFields.inventoryLess !== null) {
      if (v.id) {
        if (+v.currentInventory >= +searchFields.inventoryLess) {
          isPass = false
        }
      } else {
        if (+v.onInventory >= +searchFields.inventoryLess) {
          isPass = false
        }
      }
    }
    return isPass
  })
  const count = totalList.length
  const list = totalList.filter((v, i) => {
    const start = (pageNum - 1) * pageSize
    const end = pageNum * pageSize
    return i >= start && i < end
  })
  return {
    data: {
      count,
      pageNum,
      pageSize,
      list
    }
  }
}
const { data: dataSource, run, loading, pageSize, current, refresh } = usePagination((param) => {
  const data = { ...param, ...searchFields }
  // 此处的uuid入参，和下面列表处的uuid比对，是神来之笔，保障了被重复添加的商品也有唯一标识。
  // 但是筛选依然会成为难题，假设根据商品名称筛选，筛选之后，在商品明细中，进行uuid的匹配会变的困难。
  // 后端筛选时，重复添加商品和筛选功能若同时存在，实现起来相对复杂，需要根据查询结果，筛选入参，并配合分页逻辑，艰难的匹配uuid。
  // 能被编辑的数据，后端就没有办法进行筛选，还是需要前端来筛选，例如根据展示状态进行筛选。
  // 筛选的最佳方案是：纯前端筛选。纯前端筛选，需要能拿到所有的商品数据，而不能仅仅只是拿到商品id。
  // data.productIdUuidSortList = thisFields.beSelectedProductIdArrayObjectList.map(v => ({ productId: v.productId, uuid: v.uuid }))
  let disabledMove = false
  if (searchFields.productName) {
    disabledMove = true
  } else if (searchFields.pdType !== undefined) {
    disabledMove = true
  } else if (searchFields.beShow !== undefined) {
    disabledMove = true
  } else if (searchFields.superscriptId !== undefined) {
    disabledMove = true
  } else if (searchFields.inventoryLess !== null) {
    disabledMove = true
  }
  // 如果筛选了数据，就禁用掉上移下移。因为此时进行上移和下移，其位置在原始数据中会变化很大，可能会横跨多个位置，变的让人难以理解。
  thisFields.disabledMove = disabledMove
  return productNoDistinctList(data)
}, {
  manual: true, // 修改为false 让其自动执行
  pagination: {
    currentKey: 'pageNum', // 通过该值指定接口 当前页数 参数的属性值
    pageSizeKey: 'pageSize', // 通过该值指定接口 每页获取条数 参数的属性值
    totalKey: 'data.data.count' // 指定 data 中 total 属性的路径
  },
  formatResult: res => { // 返回数据格式化
    res.data.list.forEach((v, i) => {
      const shelfProductItem = thisFields.beSelectedProductIdArrayObjectList.find(v2 => v2.uuid === v.uuid) || {}
      shelfProductItem.costPoint = shelfProductItem.costPoint ?? v.costPoint
      setProductManagementDefaultFields(shelfProductItem)
      // 接口返回的数据一定是被选中的商品数据，一定能匹配上，一定会存在引用关系，所以不用担心数据不会同步变化。
      v.shelfProductItem = shelfProductItem
      v.currentInventory = shelfProductItem.currentInventory ?? 0
    })
    total.value = res.data.count
    totalPage.value = Math.ceil(res.data.count / pageSize.value)
    if (current.value > 1 && res.data.list.length === 0) {
      run({ pageNum: current.value - 1, pageSize: pageSize.value })
    }
    return res.data.list
  }
})

const whenPaginationChange = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}
const whenClickSearch = () => {
  if (!thisFields.beSelectedProductIdArrayObjectList.length) return
  run({ pageNum: 1, pageSize: pageSize.value })
}
const whenClickReset = () => {
  Object.assign(searchFields, getDefaultSearchFields())
  whenClickSearch()
}

const setTableHeader = () => {
  if (props.bizType === 'crowdPurchaseRestriction') {
    thisFields.tableHeader = [
      { title: '序号', dataIndex: 'index', align: 'center', width: 80 },
      { title: '商品名称', dataIndex: 'productName', align: 'center', ellipsis: true, width: 100 },
      { title: '商品类型', dataIndex: 'pdTypeDesc', align: 'center', ellipsis: true, width: 100 },
      // { title: '发货方式', dataIndex: 'deliveryType', align: 'center', ellipsis: true, width: 120 },
      { title: '展示状态', dataIndex: 'beShow', align: 'center', ellipsis: true, width: 120 },
      // { title: '展示方式', dataIndex: 'showType', align: 'center', ellipsis: true, width: 120 },
      { title: '上架库存', dataIndex: 'onInventory', align: 'center', ellipsis: true, width: 120 },
      { title: '目前库存', dataIndex: 'currentInventory', align: 'center', ellipsis: true, width: 100 },
      { title: '活动库存', dataIndex: 'ruleInventory', align: 'center', ellipsis: true, width: 100 },
      // { title: '累计兑换量', dataIndex: 'exchangeNum', align: 'center', ellipsis: true, width: 100 },
      { title: '商品主图', dataIndex: 'shelfImg', align: 'center', ellipsis: true, width: 100 },
      { title: '商品标签', dataIndex: 'tagList', align: 'center', ellipsis: true, width: 100 },
      { title: '积分价值', dataIndex: 'costPoint', align: 'center', ellipsis: true, width: 120 },
      { title: '积分划线价', dataIndex: 'prePoint', align: 'center', ellipsis: true, width: 120 },
      { title: '每人限购数', dataIndex: 'everyoneLimit', align: 'center', ellipsis: true, width: 120 },
      { title: '展示角标', dataIndex: 'superscriptIdList', align: 'center', ellipsis: true, width: 200 },
      // { title: '活动角标', dataIndex: 'superscriptCampaign', align: 'center', ellipsis: true, width: 160 },
      { title: '操作', dataIndex: 'action', align: 'center', width: 60, fixed: 'right' }
    ]
  }
}

onMounted(() => {
  thisMethods.setOpen()
  setTableHeader()
})
watch(() => props.modelValue, () => thisMethods.setOpen())
watch(() => props.beSelectedProductIdArrayObjectList, (val) => {
  thisFields.beSelectedProductIdArrayObjectList = cloneDeep(props.beSelectedProductIdArrayObjectList)
  whenClickReset()

})
</script>

<style scoped lang="scss">
:deep(.shelf-product-management-table .ant-pagination-options-size-changer) {
  width: auto !important;
}

.checkboxWrap {
  margin-left: 13px;
  margin-bottom: 5px;
}

.resetChildAntFormItem {
  &.ant-form-item {
    margin-bottom: 0;
  }
}

.batch-wrap {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.tagListWrap {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
