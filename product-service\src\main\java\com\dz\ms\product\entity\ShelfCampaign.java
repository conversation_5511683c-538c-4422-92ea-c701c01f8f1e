package com.dz.ms.product.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 货架营销活动
 *
 * @author: LiinNs
 * @date: 2024/11/27 15:15
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table("货架营销活动")
@TableName(value = "shelf_campaign")
public class ShelfCampaign implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR, length = 256, isNull = true, comment = "名称", isIndex = true)
    private String name;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = false, defaultValue = "1", comment = "活动类型 1人群限购")
    private Integer type;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = false, comment = "活动时间类型 1永久 2时间段")
    private Integer onType;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "活动开始时间")
    private Date onStartTime;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "活动结束时间")
    private Date onEndTime;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "货架ID", isIndex = true)
    private Long shelfId;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, defaultValue = "1", comment = "启停状态 0禁用 1启用")
    private Integer state;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, defaultValue = "0", comment = "0正常 1删除")
    @TableLogic
    private Integer isDeleted;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    public ShelfCampaign(Long id, String name, Integer onType, Date onStartTime, Date onEndTime, Long shelfId) {
        this.id = id;
        this.name = name;
        this.onType = onType;
        this.onStartTime = onStartTime;
        this.onEndTime = onEndTime;
        this.shelfId = shelfId;
    }

}
