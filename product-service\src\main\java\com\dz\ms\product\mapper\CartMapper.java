package com.dz.ms.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.common.core.dto.product.CartProductDTO;
import com.dz.ms.product.entity.Cart;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 购物车Mapper
 *
 * @author: LiinNs
 * @date: 2024/12/09 10:30
 */
@Repository
public interface CartMapper extends BaseMapper<Cart> {

    /**
     * 用户购物车商品列表
     *
     * @param userId
     * @return
     */
    List<CartProductDTO> getUserCartList(@Param("userId") Long userId, @Param("cartId") Long cartId);

    /**
     * 单商品构造购物车商品
     *
     * @param productId
     * @return
     */
    CartProductDTO getProductCartByShelfProductId(@Param("shelfProductId") Long productId);

    /**
     * 用户购物车数量
     *
     * @param userId
     * @return
     */
    int getUserCartCount(@Param("userId") Long userId, @Param("shelfId") Long shelfId);

    /**
     * 购物车全选
     *
     * @param userId
     * @param status
     * @return
     */
    int checkedAllUserCart(@Param("userId") Long userId, @Param("status") Integer status);
}
