package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.io.Serializable;
import java.util.Date;

/**
 * 素材信息
 * @author: Handy
 * @date:   2022/07/26 18:05
 */
@Getter
@Setter
@NoArgsConstructor
@Table("素材信息")
@TableName(value = "material_info")
public class MaterialInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "素材ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = false,comment = "素材类型 1图片 2视频 3音频")
    private Integer materialType;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,defaultValue = "0",comment = "分组ID",isIndex = true)
    private Long groupId;
    @Columns(type = ColumnType.VARCHAR,length = 64,isNull = false,comment = "素材名称",isIndex = true)
    private String materialName;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = false,comment = "素材地址")
    private String materialUrl;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "替换素材地址",isIndex = true)
    private String replaceUrl;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "视频封面")
    private String videoPoster;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = true,comment = "生效时间",isIndex = true)
    private Date effectiveTime;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = true,comment = "失效时间",isIndex = true)
    private Date expireTime;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = true,comment = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = false,defaultValue = "0",comment = "是否删除 0未删除 1已删除")
    @TableLogic
    private Integer isDeleted;

    public MaterialInfo(Long id, Integer materialType, Long groupId, String materialName, String materialUrl, String replaceUrl, String videoPoster, Date effectiveTime, Date expireTime) {
        this.id = id;
        this.materialType = materialType;
        this.groupId = groupId;
        this.materialName = materialName;
        this.materialUrl = materialUrl;
        this.replaceUrl = replaceUrl;
        this.videoPoster = videoPoster;
        this.effectiveTime = effectiveTime;
        this.expireTime = expireTime;
    }

}
