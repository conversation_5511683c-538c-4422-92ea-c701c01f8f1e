@import "assets/scss/config";

.popup {
  max-height: 1070rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  // padding: 60rpx 30rpx 60rpx 60rpx;
  overflow: hidden;

  &-title {
    display: block;
    width: 100%;
    box-sizing: border-box;
    text-align: center;
    padding: 60rpx 60rpx 40rpx 60rpx;
    font-family: NotoSansHans;
    font-weight: 500;
    font-size: 36rpx;
    color: #3c3c43;
    line-height: 54rpx;
    letter-spacing: 1rpx;
  }

  &-content {
    flex: 1;
    overflow: hidden;
    transition: all 0.5s;
    width: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;

    .normal-content {
      display: block;
      width: 510rpx;
      font-family: MUJIFont2020, PingFangSC;
      font-weight: 400;
      font-size: 28rpx;
      color: #3c3c43;
      line-height: 42rpx;
      font-style: normal;
      box-sizing: border-box;
    }
  }

  &-bottom {
    width: 100%;
    padding-left: 60rpx;
    padding-top: 60rpx;
    padding-right: 60rpx;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
  }

  &-other {
  }
  &-scroll {
    height: 100%;
    width: 100%;
    overflow: auto;
    box-sizing: border-box;
    padding-right: 30rpx;
    &-icon {
      width: 180rpx;
      height: 180rpx;
    }
    &-pos {
      margin-top: 17rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: NotoSansHans, NotoSansHans;
      font-weight: 500;
      font-size: 28rpx;
      color: #3c3c43;
      line-height: 42rpx;
    }
    &-position {
      width: 36rpx;
      height: 36rpx;
      margin-right: 10rpx;
    }
  }
  &-button {
    flex-shrink: 0;
    box-sizing: border-box;
    margin: 0 auto;
    flex: 1;
    &:nth-child(2) {
      margin-left: 30rpx;
    }

    &.confirm {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 80rpx;
      background: #3c3c43;
      border-radius: 5rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28rpx;
      color: #ffffff;
      border: 1rpx solid #3c3c43;
    }

    &.cancel {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 80rpx;
      background: #fff;
      border-radius: 5rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28rpx;
      color: #000;
      border: 1rpx solid #3c3c43;
    }
  }
  &-tip {
    font-family: NotoSansHans, NotoSansHans;
    font-weight: normal;
    font-size: 20rpx;
    color: #888888;
    line-height: 30rpx;
    margin-top: 40rpx;
    text-align: center;
  }
}
