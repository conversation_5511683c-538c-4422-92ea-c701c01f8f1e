package com.dz.ms.product.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 货架商品角标
 *
 * @author: fei
 * @date: 2024/12/09 15:18
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@ApiModel(value = "商品标签表")
public class ShelfProductSuperscriptDTO {

    @ApiModelProperty(value = "ID")
    private Long id;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "货架商品ID")
    private Long shelfProductId;
    @ApiModelProperty(value = "角标ID")
    private Long superscriptId;
    @ApiModelProperty(value = "角标名称")
    private String superscriptName;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "修改时间")
    private Date modified;
    @ApiModelProperty(value = "修改人")
    private Long modifier;

}
