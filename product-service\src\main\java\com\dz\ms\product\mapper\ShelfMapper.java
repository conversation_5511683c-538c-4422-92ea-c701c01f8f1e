package com.dz.ms.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.core.dto.ExchangeStaticParamDTO;
import com.dz.ms.product.dto.req.ShelfParamDTO;
import com.dz.ms.product.entity.Shelf;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 商品货架Mapper
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:27
 */
@Repository
public interface ShelfMapper extends BaseMapper<Shelf> {

    IPage<Shelf> selPageList(Page<Object> objectPage, @Param("param") ShelfParamDTO param);

    List<Shelf> selectListByParam(@Param("param") ShelfParamDTO param);

    /**
     * 查询少量字段
     */
    List<Shelf> selLessList(@Param("param") ShelfParamDTO param);

    /**
     * 查询所有字段
     */
    List<Shelf> selAllList(@Param("param") ShelfParamDTO param);

    List<Shelf> selNoLimitList(@Param("param") ShelfParamDTO param);

    void updGroupIdIntoNull(@Param("groupId") Long groupId);

    int updateStatic(ExchangeStaticParamDTO orderStaticParam);
}
