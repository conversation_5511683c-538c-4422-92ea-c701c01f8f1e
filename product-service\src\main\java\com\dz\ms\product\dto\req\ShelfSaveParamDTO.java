package com.dz.ms.product.dto.req;

import com.dz.common.core.dto.user.CrowdDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 商品货架保存入参
 *
 * @author: fei
 * @date: 2024/11/26 23:11
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@ApiModel(value = "商品货架保存入参")
public class ShelfSaveParamDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "货架名称")
    private String name;
    @ApiModelProperty(value = "货架上架类型 1永久上架 2时间段")
    private Integer onType;
    @ApiModelProperty(value = "上架开始时间")
    private Date onStartTime;
    @ApiModelProperty(value = "上架结束时间-下架")
    private Date onEndTime;
    @ApiModelProperty(value = "优先级 值越大显示越优先")
    private Integer priority;
    @ApiModelProperty(value = "是否根据展示 0不限制 1限制")
    private Integer limitShow;
    @ApiModelProperty(value = "人群包ID")
    private Long groupId;
    @ApiModelProperty(value = "人群包类型 0:新建规则 1:适用人群包 2:导入人群包")
    private Integer groupType;
    @ApiModelProperty(value = "货架状态 0禁用 1启用")
    private Integer state;
    @ApiModelProperty(value = "库存售罄组件配置")
    private String content;
    @ApiModelProperty(value = "保存货架商品列表")
    private List<ShelfProductSaveDTO> saveShelfProductList;
    @ApiModelProperty(value = "人群包")
    private CrowdDTO crowdDTO;
    
    
}
