package com.dz.ms.user.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.user.CouponsDetailDTO;
import com.dz.common.core.dto.user.CouponsListAllDTO;
import com.dz.common.core.dto.user.CouponsListDTO;
import com.dz.common.core.dto.user.ReceiveCouponDTO;
import com.dz.common.core.dto.user.RightsReceiveRecordDTO;
import com.dz.common.core.dto.user.UserSimpleDTO;
import com.dz.common.core.dto.user.couponNumDTO;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.vo.ReceiveCouponVo;
import com.dz.ms.user.service.CouponService;
import com.dz.ms.user.service.MUJIOpenApiService;
import com.dz.ms.user.service.UserInfoService;
import com.dz.ms.user.vo.CouponSelectVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "我的礼券")
@RestController
public class CouponController {

    @Resource
    private CouponService couponService;
    @Resource
    private MUJIOpenApiService mujiApiService;
    @Resource
    private UserInfoService userInfoService;

    @ApiOperation("我的礼券列表未使用&待领取")
    @PostMapping(value = "/app/coupon/list")
    public Result<CouponsListAllDTO> getCouponList() {
        Result<CouponsListAllDTO> result = new Result<>();
        result.setData(couponService.getCouponList());
        return result;
    }

    @ApiOperation("我的礼券列表已使用&已过期")
    @PostMapping(value = "/app/coupon/list/expire")
    public Result<PageInfo<CouponsListDTO>> getCouponListExpire(@RequestBody CouponSelectVo param) {
        Result<PageInfo<CouponsListDTO>> result = new Result<>();
        result.setData(couponService.getCouponListExpire(param));
        return result;
    }

    @ApiOperation("券详情")
    @GetMapping(value = "/app/coupon/detail")
    public Result<CouponsDetailDTO> getCouponDetail(@RequestParam("couponId") String couponId,@RequestParam("couponCode") String couponCode) {
        Result<CouponsDetailDTO> result = new Result<>();
        result.setData(couponService.getCouponDetail(couponId,couponCode));
        return result;
    }

    @GetMapping(value = "/app/coupon/send")
    public Result<Object> couponSend(@RequestParam("couponId") String couponId) {
        Result<Object> result = new Result<>();
        couponService.couponSend(couponId);
        return result;
    }

    @ApiOperation("领券")
    @PostMapping(value = "/app/coupon/receive")
    public Result<ReceiveCouponDTO> couponReceive(@RequestBody ReceiveCouponVo param) {
        Result<ReceiveCouponDTO> result = new Result<>();
        result.setData(couponService.couponReceive(param));
        return result;
    }

    @ApiOperation("权益领取记录")
    @PostMapping(value = "/app/rights/receive/record")
    public Result<List<RightsReceiveRecordDTO>> rightsReceiveRecord() {
        Result<List<RightsReceiveRecordDTO>> result = new Result<>();
        result.setData(couponService.rightsReceiveRecord());
        return result;
    }

    @ApiOperation("我的可使用券数量")
    @PostMapping(value = "/app/coupon/used/num")
    public Result<couponNumDTO> couponUsedNum() {
        Result<couponNumDTO> result = new Result<>();
        couponNumDTO numDTO = new couponNumDTO();
        Integer num = 0;
        //获取当前登录用户信息
        UserSimpleDTO userInfo = userInfoService.getDbUserSimpleInfo(SecurityContext.getUser().getUid());
        //            查询我的券列表
        JSONObject couponJson=mujiApiService.memberCouponList(userInfo.getCardNo(), null, 1, 1, 100);
        if (null != couponJson && couponJson.containsKey("items")){
            JSONArray couponList=couponJson.getJSONArray("items");
            if (!CollectionUtils.isEmpty(couponList)){
                num+=couponList.size();
            }
        }
        //查询活动券
        JSONObject activityCouponList=mujiApiService.activityCouponList(userInfo.getCardNo());
        if (null != activityCouponList && activityCouponList.containsKey("items")){
            JSONArray activityList=activityCouponList.getJSONArray("items");
            if (!CollectionUtils.isEmpty(activityList)){
                num+=activityList.size();
                numDTO.setIsRed(1);
            }
        }
        numDTO.setCouponNum(num);
        result.setData(numDTO);
        return result;
    }
}
