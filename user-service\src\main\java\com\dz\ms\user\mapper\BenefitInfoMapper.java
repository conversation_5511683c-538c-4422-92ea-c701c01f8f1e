package com.dz.ms.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.user.entity.BenefitInfo;
import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 会员权益Mapper
 * @author: Handy
 * @date:   2023/08/08 01:14
 */
@Repository
public interface BenefitInfoMapper extends BaseMapper<BenefitInfo> {

    List<BenefitInfo> selectBatchIdsBySort(@Param("gradeId")Long gradeId,@Param("ids") List<Long> ids);
}
