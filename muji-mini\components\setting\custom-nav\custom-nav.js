// components/setting/my-header/my-header.js
const app = getApp()
Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    navSetting: {
      type: null,
      value() {
        return {}
      },
    },
    templateName: {
      type: String,
      value: ''
    },
    scrollTop: {
      type: Number,
      value: 0,
      observer(val) {
        // 滑动恢复
        if (this.data.navSetting.navType == 3 || this.data.navSetting.navType == 4) {
          if (val > 300) {
            if (this.data.navSetting.navType == 3 && this.data.currentNavColor != this.data.navSetting.navColor1) {
              this.changeNavigationBarColor(this.data.navSetting.navColor1)
            } else if (this.data.navSetting.navType == 4 && this.data.currentNavColor != this.data.navSetting.move.navColor) {
              this.changeNavigationBarColor(this.data.navSetting.move.navColor)
            }
          } else if (this.data.currentNavColor != this.data.navSetting.navColor) {
            this.changeNavigationBarColor(this.data.navSetting.navColor)
          }
        }
      }
    }
  },
  data: {
    isTabBarPage: false,
    frontColor: '', // 返回箭头颜色
    defaultNavSetting: app.globalData.styleSetting.pageStyle, // 默认的风格设置
    statusBarHeight: app.globalData.statusBarHeight, // 自定义导航栏的高度
    navBarHeight: app.globalData.navBarHeight,
    rpx: app.globalData.rpx,
    currentNavColor: -1
  },
  attached() {
    this.setData({
      isTabBarPage: wx.$mp.isTabBarPage(),
      defaultNavSetting: app.globalData.styleSetting.pageStyle, // 默认的风格设置
    })
    this.changeNavigationBarColor(this.data.navSetting.navColor)
  },
  methods: {
    // 返回上一页 首页
    goBack: app.debounce(async function () {
      wx.$mp.navigateBack()
    }),
    //设置页面导航条颜色
    changeNavigationBarColor(navColor) {
      this.data.currentNavColor = navColor
      // navColor 1-黑色  2-白色
      wx.setNavigationBarColor({
        frontColor: navColor == 1 ? '#000000' : '#ffffff',
        backgroundColor: "#ffffff"
      })
      this.setData({
        frontColor: navColor == 1 ? '#000000' : '#ffffff'
      })
    },
    // 返回顶部
    goTop() {
      this.triggerEvent('goTop')
    },
    // scroll 锚点滚动跳转
    goAchor(e) {
      this.triggerEvent('goAchor', e.detail)
    },
    // 对弹窗的操作
    goModal(e) {
      this.triggerEvent('goModal', e.detail)
    },
    // 分享
    goShare(e) {
      let {
        shareTitle,
        shareImg,
        sharePath
      } = e.detail
      this.triggerEvent('goShare', {
        shareTitle,
        shareImg,
        sharePath
      })
    },
  }
})
