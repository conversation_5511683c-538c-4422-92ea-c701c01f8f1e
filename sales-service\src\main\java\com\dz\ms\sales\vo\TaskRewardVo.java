package com.dz.ms.sales.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * 任务奖励列表
 */
@Getter
@Setter
@NoArgsConstructor
public class TaskRewardVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "奖励类型：1优惠券，2积分")
    private Integer rewardType;
    @ApiModelProperty(value = "奖励券列表，多个用英文逗号隔开")
    private String couponIds;
    @ApiModelProperty(value = "奖励积分数量")
    private Integer pointsNum;
    @ApiModelProperty(value = "奖励顺序")
    private Integer sortNum;
}
