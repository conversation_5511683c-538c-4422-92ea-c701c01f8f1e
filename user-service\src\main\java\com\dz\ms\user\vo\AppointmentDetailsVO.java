package com.dz.ms.user.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AppointmentDetailsVO {
    @ApiModelProperty("活动名称")
    private String name;
    @ApiModelProperty("活动banner 多张图时逗号,分隔")
    private String bannerUrl;
    @ApiModelProperty("用户是否在活动白名单")
    private boolean isInWhitelist;
    @ApiModelProperty("用户是否已经预约")
    private boolean isAlreadyBooked;
    @ApiModelProperty("用户手机号")
    private String phone;
    @ApiModelProperty("活动是否已过期")
    private boolean expired;
    @ApiModelProperty("用户预约信息")
    private UserAppointmentVO userAppointment;

    @Data
    public static class UserAppointmentVO {
        @ApiModelProperty("用户姓名")
        private String userName;
        @ApiModelProperty("用户id")
        private Long userId;
        @ApiModelProperty("会员卡号")
        private String memberCode;
        @ApiModelProperty("预约场次信息")
        private String sessionInfo;
        @ApiModelProperty("预约日期 yyyy/MM/dd")
        private String appointmentDate;
        @ApiModelProperty("预约时间场次 14:00-16:00")
        private String appointmentTime;
    }
}