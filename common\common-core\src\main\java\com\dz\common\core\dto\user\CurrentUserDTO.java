package com.dz.common.core.dto.user;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 当前请求用户信息
 * @Author: Handy
 * @Date: 2022/6/22 14:11
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
public class CurrentUserDTO {

    /** 当前用户类型 */
    private Integer type;
    /** 当前用户ID */
    private Long uid;
    /** 当前租户ID */
    private Long tenantId;
    /** 当前平台 1会小 2企微 3商城 */
    private Integer platform;
    /** 当前门店ID */
    private Long storeId;

    public CurrentUserDTO(Long tenantId) {
        this.tenantId = tenantId;
    }

    public CurrentUserDTO(Long tenantId,Long uid) {
        this.tenantId = tenantId;
        this.uid = uid;
    }

    public CurrentUserDTO(Integer type, Long uid, Long tenantId, Long storeId) {
        this.type = type;
        this.uid = uid;
        this.tenantId = tenantId;
        this.storeId = storeId;
    }

}
