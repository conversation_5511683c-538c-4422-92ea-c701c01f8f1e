package com.dz.ms.product.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.product.CartDTO;
import com.dz.common.core.dto.product.CartResultDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.ms.product.service.CartService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 购物车相关接口
 */
@RestController
@Api(tags = "购物车相关接口")
@Slf4j
public class ShoppingCartController {

    @Resource
    private CartService cartService;

    /**
     * 获取用户购物车列表
     *
     * @return
     */
    @GetMapping(value = {"/app/cart/list", "/cart/list"})
    @ApiOperation(value = "获取用户购物车列表", notes = "获取用户购物车列表")
    public Result<CartResultDTO> getUserCartList() {
        Result<CartResultDTO> result = new Result<CartResultDTO>();
        CurrentUserDTO user = SecurityContext.getUser();
        if (ObjectUtils.isEmpty(user)) {
            throw new BusinessException(ErrorCode.UNAUTHORIZED, "登录异常");
        }
        result.setData(cartService.getUserCartList(true));
        return result;
    }


    @PostMapping(value = {"/app/cart/add", "/cart/add"})
    @ApiOperation(value = "添加购物车", notes = "添加购物车")
    public Result<Boolean> addUserCart(@RequestBody CartDTO cartDTO) {
        Result<Boolean> result = new Result<>();
        cartService.addUserCart(cartDTO);
        result.setData(true);
        return result;
    }

    @PostMapping(value = "/app/cart/edit")
    @ApiOperation(value = "修改购物车信息", notes = "修改购物车信息")
    public Result<Boolean> editUserCart(@RequestBody CartDTO cartDTO) {
        Result<Boolean> result = new Result<Boolean>();
        if (null == cartDTO.getId() || ((null == cartDTO.getNumber() || cartDTO.getNumber() < 1) && null == cartDTO.getChecked())) {
            result.errorResult(ErrorCode.BAD_REQUEST, "必填参数为空");
            return result;
        }
        cartService.editUserCart(cartDTO);
        result.setData(true);
        return result;
    }

    @DeleteMapping(value = {"/app/cart/delete", "/cart/delete"})
    @ApiOperation(value = "删除购物车商品", notes = "删除购物车商品")
    public Result<Boolean> deleteUserCart(@RequestParam("ids") String ids) {
        Result<Boolean> result = new Result<Boolean>();
        if (StringUtils.isBlank(ids)) {
            result.errorResult(ErrorCode.BAD_REQUEST, "必填参数为空");
            return result;
        }
        cartService.deleteUserCart(ids);
        result.setData(true);
        return result;
    }

    /**
     * 清空已下单购物车商品
     *
     * @return
     */
    @DeleteMapping(value = "/cart/order/clean")
    @ApiOperation(value = "清空已下单购物车商品", notes = "清空已下单购物车商品")
    public Result<Boolean> cleanUserOrderCart() {
        Result<Boolean> result = new Result<Boolean>();
        cartService.cleanUserOrderCart();
        result.setData(true);
        return result;
    }

    @GetMapping(value = "/app/cart/count")
    @ApiOperation(value = "获取用户购物车商品总数", notes = "获取用户购物车商品总数")
    public Result<Integer> getUserCartCount() {
        Result<Integer> result = new Result<Integer>();
        result.setData(cartService.getUserCartCount());
        return result;
    }

    /**
     * 购物车全选
     */
    @GetMapping(value = "/app/cart/checkedall")
    @ApiOperation(value = "购物车全选", notes = "购物车全选")
    public Result<Boolean> checkedAllUserCart(@RequestParam("status") Integer status) {
        Result<Boolean> result = new Result<>();
        if (null == status || (!status.equals(0) && !status.equals(1))) {
            result.errorResult(ErrorCode.BAD_REQUEST, "选择状态不能为空");
            return result;
        }
        cartService.checkedAllUserCart(status);
        result.setData(true);
        return result;
    }

    /**
     * 获取订单预览商品列表
     */
    @GetMapping(value = "/cart/order_list")
    @ApiOperation(value = "获取订单预览商品列表", notes = "获取订单预览商品列表")
    public Result<CartResultDTO> getPreviewCartOrder() {
        Result<CartResultDTO> result = new Result<CartResultDTO>();
        try {
            CartResultDTO cartResultDTO = cartService.getPreviewCartOrder();
            result.setData(cartResultDTO);
            return result;
        } catch (Exception e) {
            log.info("获取订单预览商品列表");
            log.error(e.getMessage(), e);
            throw new BusinessException(ErrorCode.BAD_REQUEST, e.getMessage());
        }
    }

    /**
     * 单商品购买订单预览
     */
    @GetMapping(value = "/cart/order")
    @ApiOperation(value = "单商品购买订单预览", notes = "单商品购买订单预览")
    public Result<CartResultDTO> getCartOrderByShelfProductId(@RequestParam("shelfProductId") Long shelfProductId,
                                                              @RequestParam("num") Integer num) {
        Result<CartResultDTO> result = new Result<CartResultDTO>();
        try {
            CartResultDTO cartResultDTO = cartService.getCartOrderByShelfProductId(shelfProductId, num);
            result.setData(cartResultDTO);
            return result;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 清空购物车失效商品
     */
    @DeleteMapping(value = "/app/cart/clean_disabled_product")
    @ApiOperation(value = "清空购物车失效商品", notes = "清空购物车失效商品")
    public Result<Object> cleanDisabledProduct() {
        cartService.cleanDisabledProduct();
        Result<Object> result = new Result<>();
        return result;
    }
}
