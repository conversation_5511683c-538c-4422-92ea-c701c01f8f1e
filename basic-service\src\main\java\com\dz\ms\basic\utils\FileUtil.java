package com.dz.ms.basic.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.RandomAccessFile;
import java.nio.ByteBuffer;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.channels.FileChannel.MapMode;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class FileUtil {

	/**
	 * 图片类型白名单.jpg,.png,.bmp,.tiff,.jpeg,.gif,.svg
	 */
	public static final String[] IMGWHITELIST = new String[]{"^[J|j][P|p][G|g]$", "^[P|p][N|n][G|g]$", "^[B|b][M|m][P|p]$",
			"^[T|t][I|i][F|f][F|f]$", "^[J|j][P|p][E|e][G|g]$", "^[G|g][I|i][F|f]$", "^[S|s][V|v][G|g]$", "^[W|w][E|e][B|b][P|p]$"
	};

	/**
	 * 视频/音频类型白名单.mp4,.3gp,.m3u8,.mov,.mpg,.avi,.flv,.mod,.wav,.mp3,.m4a,.aac
	 */
	public static final String[] VIDEOWHITELIST = new String[]{"^[M|m][P|p]4$", "^3[G|g][P|p]$", "^[M|m]3[U|u]8$","^[M|m][O|o][V|v]$", "^[M|m][P|p][G|g]$",
			"^[M|m][O|o][D|d]$", "^[W|w][A|a][V|v]$","^[A|a][V|v][I|i]$", "^[F|f][L|l][V|v]$", "^[M|m][P|p]3$", "^[M|m]4[A|a]$", "^[A|a][A|a][C|c]$"
	};

	/**
	 * 文件类型白名单.51o,.doc,.docx,.pdf,.xls,.xlsx,.zip,.rar,.txt
	 */
	public static final String[] FILEWHITELIST = new String[]{"^51[O|o]$","^[D|d][O|o][C|c]$", "^[D|d][O|o][C|c][X|x]$", "^[P|p][D|d][F|f]$",
			"^[C|c][S|s][V|v]$","^[X|x][L|l][S|s]$", "^[X|x][L|l][S|s][X|x]$", "^[Z|z][I|i][P|p]$", "^[R|r][A|a][R|r]$", "^[T|t][X|x][T|t]$"
	};

	/**
	 * 文件类型黑名单
	 */
	public static final String [] BLACKLIST = new String[] {"^.*<[O|o][B|b][J|j][E|e][C|c][T|t]\\s.*$",
			"^.*<[E|e][M|m][B|b][E|e][D|d]\\s.*$",
			"^.*[D|d][O|o][C|c][U|u][M|m][E|e][N|n][T|t].*$",
			"^.*[A|a][L|l][E|e][R|r][T|t]\\s*\\(.*\\).*$",
			"^.*[W|w][I|i][N|n][D|d][O|o][W|w]\\.[L|l][O|o][C|c][A|a][T|t][I|i][O|o][N|n]\\s*=.*$",
			"^.*[S|s][T|t][Y|y][L|l][E|e]\\s*=.*[X|x]:[E|e][X|x].*[P|p][R|r][E|e][S|s]{1,2}[I|i][O|o][N|n]\\s*\\(.*\\).*$",
			"^.*[D|d][O|o][C|c][U|u][M|m][E|e][N|n][T|t]\\.[C|c][O|o]{2}[K|k][I|i][E|e].*$",
			"^.*[E|e][V|v][A|a][L|l]\\s*\\(.*\\).*$",
			"^.*[U|u][N|n][E|e][S|s][C|c][A|a][P|p][E|e]\\s*\\(.*\\).*$",
			"^.*[E|e][X|x][E|e][C|c][S|s][C|c][R|r][I|i][P|p][T|t]\\s*\\(.*\\).*$",
			"^.*[M|m][S|s][G|g][B|b][O|o][X|x]\\s*\\(.*\\).*$",
			"^.*[C|c][O|o][N|n][F|f][I|i][R|r][M|m]\\s*\\(.*\\).*$",
			"^.*[P|p][R|r][O|o][M|m][P|p][T|t]\\s*\\(.*\\).*$",
			"^.*[S|s][C|c][R|r][I|i][P|p][T|t]>.*</[S|s][C|c][R|r][I|i][P|p][T|t]*.*$",
			"^[.&[^\"]]*\"[.&[^\"]]*$",
			"^[.&[^']]*'[.&[^']]*$",
			"^[[.&[^a]]|[|a|\n|\r\n|\r|\u0085|\u2028|\u2029]]*<[S|s][C|c][R|r][I|i][P|p][T|t]>.*</[S|s][C|c][R|r][I|i][P|p][T|t]>[[.&[^a]]|[|a|\n|\r\n|\r|\u0085|\u2028|\u2029]]*$"
	};

	/**
	 * 创建目录
	 * 
	 * @param destDirName
	 *            目标目录名
	 * @return 目录创建成功返回true，否则返回false
	 */
	public static boolean createDir(String destDirName) {
		File dir = new File(destDirName);
		if (dir.exists()) {
			return false;
		}
		if (!destDirName.endsWith(File.separator)) {
			destDirName = destDirName + File.separator;
		}
		// 创建单个目录
		if (dir.mkdirs()) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 删除文件
	 * @param filePathAndName String 文件路径及名称 如c:/fqf.txt
	 */
	public static void delFile(String filePathAndName) {
		try {
			String filePath = filePathAndName;
			filePath = filePath.toString();
			File myDelFile = new File(filePath);
			if (!myDelFile.delete()){
				log.info("删除文件失败");
			}

		} catch (Exception e) {
			e.printStackTrace();

		}

	}

	/**
	 * 读取到字节数组0
	 * @param filePath //路径
	 * @throws IOException
	 */
	@SuppressWarnings("resource")
    public static byte[] getContent(String filePath) throws IOException {
		File file = new File(filePath);
		long fileSize = file.length();
		if (fileSize > Integer.MAX_VALUE) {
			return null;
		}
		FileInputStream fi = new FileInputStream(file);
		byte[] buffer = new byte[(int) fileSize];
		int offset = 0;
		int numRead = 0;
		while (offset < buffer.length
				&& (numRead = fi.read(buffer, offset, buffer.length - offset)) >= 0) {
			offset += numRead;
		}
		// 确保所有数据均被读取
		if (offset != buffer.length) {
			throw new IOException("Could not completely read file " + file.getName());
		}
		fi.close();
		return buffer;
	}

	/**
	 * 读取到字节数组1
	 * 
	 * @param filePath
	 * @return
	 * @throws IOException
	 */
	public static byte[] toByteArray(String filePath) throws IOException {

		File f = new File(filePath);
		if (!f.exists()) {
			throw new FileNotFoundException(filePath);
		}
		ByteArrayOutputStream bos = new ByteArrayOutputStream((int) f.length());
		BufferedInputStream in = null;
		try {
			in = new BufferedInputStream(new FileInputStream(f));
			int buf_size = 1024;
			byte[] buffer = new byte[buf_size];
			int len = 0;
			while (-1 != (len = in.read(buffer, 0, buf_size))) {
				bos.write(buffer, 0, len);
			}
			return bos.toByteArray();
		} catch (IOException e) {
			e.printStackTrace();
			throw e;
		} finally {
			try {
				if (null != in){
					in.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
			bos.close();
		}
	}

	/**
	 * 读取到字节数组2
	 * 
	 * @param filePath
	 * @return
	 * @throws IOException
	 */
	public static byte[] toByteArray2(String filePath) throws IOException {

		File f = new File(filePath);
		if (!f.exists()) {
			throw new FileNotFoundException(filePath);
		}

		FileChannel channel = null;
		FileInputStream fs = null;
		try {
			fs = new FileInputStream(f);
			channel = fs.getChannel();
			ByteBuffer byteBuffer = ByteBuffer.allocate((int) channel.size());
			while ((channel.read(byteBuffer)) > 0) {
				// do nothing
			}
			return byteBuffer.array();
		} catch (IOException e) {
			log.info("读取文件内容出错"+e.getMessage());
			throw e;
		} finally {
			try {
				if (null != channel){
					channel.close();
				}
			} catch (IOException e) {
				log.info("读取文件内容出错"+e.getMessage());
			}
			try {
				if (null != fs){
					fs.close();
				}
			} catch (IOException e) {
				log.info("读取文件内容出错"+e.getMessage());
			}
		}
	}

	/**
	 * Mapped File way MappedByteBuffer 可以在处理大文件时，提升性能
	 * @param filePath
	 * @throws IOException
	 */
	public static byte[] toByteArray3(String filePath) throws IOException {

		FileChannel fc = null;
		RandomAccessFile rf = null;
		try {
			rf = new RandomAccessFile(filePath, "r");
			fc = rf.getChannel();
			MappedByteBuffer byteBuffer = fc.map(MapMode.READ_ONLY, 0,
					fc.size()).load();
			byte[] result = new byte[(int) fc.size()];
			if (byteBuffer.remaining() > 0) {
				byteBuffer.get(result, 0, byteBuffer.remaining());
			}
			return result;
		} catch (IOException e) {
			e.printStackTrace();
			throw e;
		} finally {
			try {
				if (null != rf){
					rf.close();
				}
				if (null != fc){
					fc.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	/**
	 * @notes:判断文件是否为图片
	 * @param file
	 * @return true 是 | false 否
	 * <AUTHOR> 2014-7-31 上午11:02:38
	 */
	public static final boolean isImage(File file) {
		boolean flag = false;
		try {
			BufferedImage bufreader = ImageIO.read(file);
			int width = bufreader.getWidth();
			int height = bufreader.getHeight();
			if (width > 0 && height > 0) {
				return true;
			}
		} catch (Exception e) {
			flag = false;
		}
		return flag;
	}

	/**
	 * 检查是否是合法的文件名
	 * @param fileName
	 * @return boolean 是否合法
	 */
	public static final int checkFileName(String fileName) {
		if (StringUtils.isBlank(fileName)) {
			return 0;
		}

		Pattern pattern = null;
		Matcher mc = null;

		// 文件名非法校验
		for (String black : BLACKLIST) {
			pattern = Pattern.compile(black);
			mc = pattern.matcher(fileName);
			if (mc.find()) {
				return 0;
			}
		}
		String extFile = StringUtils.substringAfterLast(fileName, ".");
		// 图片文件白名单
		for (String allow : IMGWHITELIST) {
			mc = Pattern.compile(allow).matcher(extFile);
			if (mc.find()) {
				return 1;
			}
		}
		// 视频音频文件白名单
		for (String allow : VIDEOWHITELIST) {
			mc = Pattern.compile(allow).matcher(extFile);
			if (mc.find()) {
				return 2;
			}
		}
		// 文件类型白名单
		for (String allow : FILEWHITELIST) {
			mc = Pattern.compile(allow).matcher(extFile);
			if (mc.find()) {
				return 3;
			}
		}
		return 0;
	}

	/**
	 * inputStreamToFile
	 * @param inputStream
	 * @param file
	 * @throws IOException
	 */
	public static void copyInputStreamToFile(InputStream inputStream, File file)
			throws IOException {

		try (FileOutputStream outputStream = new FileOutputStream(file)) {

			int read;
			byte[] bytes = new byte[1024];

			while ((read = inputStream.read(bytes)) != -1) {
				outputStream.write(bytes, 0, read);
			}
		}

	}

	public static String bytesToHexString(byte[] b){
		StringBuilder stringBuilder = new StringBuilder();
		if (b == null || b.length <= 0) {
			return null;
		}
		for (int i = 0; i < b.length; i++) {
			int v = b[i] & 0xFF;
			String hv = Integer.toHexString(v);
			if (hv.length() < 2) {
				stringBuilder.append(0);
			}
			stringBuilder.append(hv);
		}
		return stringBuilder.toString();
	}

	public static final String[] validFileHexApp = new String[]{
			"89504e47",//png
			"ffd8ff",//jpg,jpeg
			"424d",// bmp
	};

	public static final String[] validFileHex = new String[]{
			"89504e47",//png
			"ffd8ff",//jpg,jpeg
			"47494638",//gif
			"424d",// bmp
			"49492a00",//tif,tiff
			"52494646",//webp
			"3c3f786d",//svg

			"000000",//mp4 3gp
			"23455854",//m3u8
			"000001ba",//mpg
			"52494646",//wav avi
			"464c5601",//flv f4v
			"49443303"//mp3
	};
	public static String checkFileType(String x) {
		switch (x) {
			case "255044462d312e360d25":
				return "pdf";
			case "e69292e58f91e8bebee6":
				return "txt";
			case "d0cf11e0a1b11ae10000":
				return "doc,ppt,xls";
			case "504b0304140006000800":
				return "docx,xlsx";
			case "504b03040a0000000000":
				return "pptx,jar";
			case "89504e470d0a1a0a0000":
				return "png";
			case "ffd8ffe000104a464946":
				return "jpg,jpeg";
			case "47494638396126026f01":
				return "gif";
			case "49492a00227105008037":
				return "tif,tiff";
			//16位色图
			case "424d228c010000000000":
				return "bmp";
			//24位色图
			case "424d8240090000000000":
				return "bmp";
			//256位色图
			case "424d8e1be30000000000":
				return "bmp";
			case "3c21444f435459504520":
				return "html";
			case "526172211a0700cf9073":
				return "rar";
			case "504b0304140000000800":
				return "zip";
			case "235468697320636f6e66":
				return "ini";
			case "4d5a9000030000000400":
				return "exe";
			case "49443303000000002176":
				return "mp3";
			case "49443303000000034839":
				return "mp3";
			case "00000020667479706973":
				return "mp4";
			case "000001ba210001000180":
				return "mpg";
			case "3026b2758e66cf11a6d9":
				return "wmv,asf";
			case "52494646d07d60074156":
				return "avi";
			case "464c5601050000000900":
				return "flv,f4v";
			case "4d546864000000060001":
				return "mid,midi";
			default:
				return "0000";
		}
	}

}