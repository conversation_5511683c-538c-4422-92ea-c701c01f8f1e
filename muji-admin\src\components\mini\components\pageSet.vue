<template>
  <a-collapse expandIconPosition="end" v-model:activeKey="activeKey" :bordered="false" style="background: rgb(255, 255, 255)">
    <template #expandIcon="{ isActive }">
      <UpOutlined :rotate="isActive ? 180 : 0" />
    </template>
    <a-collapse-panel key="1" header="页面设置">
      <a-collapse expandIconPosition="end" class="collapse" v-model:activeKey="activeKey1" default-active-key="1-1" :bordered="false" style="background: rgb(255, 255, 255)">
        <a-collapse-panel key="1-1" header="基本设置">
          <a-form-item label="页面类型" :name="['pageSetting','pageType']">
            <a-radio-group :disabled="addParams.pageSetting.id" v-model:value="addParams.pageSetting.pageType" @change="changePageType">
              <a-radio :key="item.key" :value="item.key" v-for="item in pageSetting">{{item.value}}</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item>
            <a-input placeholder="请输入" v-model:value="addParams.pageSetting.templateName" show-count :maxlength="15" />
          </a-form-item>
          <template v-if="addParams.pageSetting.pageType==3">
            <div class="header-title">开屏设置</div>
            <a-form-item label="展示频次">
              <a-radio-group v-model:value="addParams.pageSetting.openFrequency">
                <a-radio :value="1">每次打开都展示</a-radio>
                <a-radio :value="2">每天仅展示1次</a-radio>
                <a-radio :value="3">
                  <a-space>每 <a-input-number placeholder="请输入" :min="1" :precision="0" v-model:value="addParams.pageSetting.openDays"></a-input-number>天<a-input-number placeholder="请输入" :min="1" :precision="0" v-model:value="addParams.pageSetting.openNumer"></a-input-number>次</a-space>
                </a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="自动跳转">
              <a-switch :unCheckedValue="0" :checkedValue="1" v-model:checked="addParams.pageSetting.autoGo" />
            </a-form-item>
            <template v-if="addParams.pageSetting.autoGo">
              <a-form-item label="自动跳转时间" :labelCol="{width:'150px'}">
                <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="addParams.pageSetting.autoTime" addon-after="ms"></a-input-number>
              </a-form-item>
              <a-form-item>
                <addLink type="2" :showType="[1,9]" :maxCondition="1" :links="addParams.pageSetting.imgLinks" :components="addParams.components" @ok="(link)=>addParams.pageSetting.imgLinks=link">
                  <a-button block>自动跳转设置</a-button>
                </addLink>
              </a-form-item>
            </template>
            <a-form-item label="跳转按钮">
              <a-switch :unCheckedValue="0" :checkedValue="1" v-model:checked="addParams.pageSetting.openGo" />
            </a-form-item>
            <template v-if="addParams.pageSetting.openGo">
              <a-form-item>
                <addLink type="2" :showType="[1,9]" :maxCondition="1" :links="addParams.pageSetting.openLinks" :components="addParams.components" @ok="(link)=>addParams.pageSetting.openLinks=link">
                  <a-button block>跳转按钮设置</a-button>
                </addLink>
              </a-form-item>
            </template>
          </template>
          <!-- 弹窗设置 -->
          <template v-if="addParams.pageSetting.pageType==4">
            <div class="header-title">尺寸设置</div>
            <a-space>
              <a-form-item label="宽" :labelCol="{width:'50px'}">
                <a-input-number placeholder="请输入" :min="1" :max="750" :precision="0" v-model:value="addParams.pageSetting.pageWidth" addon-after="px"></a-input-number>
              </a-form-item>
              <a-form-item label="高" :labelCol="{width:'50px'}">
                <a-input-number placeholder="请输入" :min="1" :precision="0" v-model:value="addParams.pageSetting.pageHeight" addon-after="px"></a-input-number>
              </a-form-item>
            </a-space>
            <div class="header-title">弹窗设置</div>
            <a-form-item label="弹出位置">
              <a-radio-group v-model:value="addParams.pageSetting.modalPos">
                <a-radio :value="1">居中</a-radio>
                <a-radio :value="2">居下</a-radio>
              </a-radio-group>
            </a-form-item>
            <!-- 通过配置加标题 -->
            <!-- <a-form-item label="显示弹窗标题" :labelCol="{width:'200px'}">
              <a-switch :unCheckedValue="0" :checkedValue="1" v-model:checked="addParams.pageSetting.modalTitle" />
            </a-form-item> -->
            <a-form-item label="圆角" :labelCol="{width:'50px'}">
              <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="addParams.pageSetting.modalRadius" addon-after="px"></a-input-number>
            </a-form-item>
            <a-form-item label="关闭按钮">
              <uploadImg :max="10" :width="50" :height="50" :imgUrl="addParams.pageSetting.modalCloseImg" :form="addParams" path="modalCloseImg" :disabled="disabled" @success="uploadSuccess" />
              <div class="global-tip">小程序显示宽高为80*80</div>
            </a-form-item>
            <a-form-item label="关闭弹窗">
              <a-checkbox v-model:checked="addParams.pageSetting.modalMaskClose">点击弹窗外关闭弹窗</a-checkbox>
            </a-form-item>
            <a-form-item label="遮罩层">
              <Color color="rgba(0,0,0,1)" :value="addParams.pageSetting.modalMaskColor" @changeColor="(value)=>changeColor('modalMaskColor',value)"></Color>
            </a-form-item>
          </template>

        </a-collapse-panel>
        <a-collapse-panel key="1-2" header="背景设置">
          <bgSet :disabled="disabled" :addParams="addParams.pageSetting"></bgSet>
        </a-collapse-panel>
        <template v-if="addParams.pageSetting.pageType!=4">
          <!-- <a-collapse-panel key="1-3" header="标题设置">
            <pageNav :disabled="disabled" :addParams="addParams.navSetting" :navSetting="navSetting" :width="addParams.pageSetting.pageWidth" :components="addParams.components"></pageNav>
          </a-collapse-panel> -->
          <a-collapse-panel key="1-4" header="分享设置" v-if="addParams.pageSetting.pageType!=2">
            <pageShare :disabled="disabled" :addParams="addParams.pageSetting"></pageShare>
          </a-collapse-panel>
        </template>
        <template v-if="['1'].includes(addParams.pageSetting.pageType)">
          <a-collapse-panel key="1-5" header="弹窗设置">
            <addModal :disabled="disabled" :addParams="addParams.pageSetting"></addModal>
          </a-collapse-panel>
          <a-collapse-panel key="1-6" header="悬浮窗">
            <floatSetting :disabled="disabled" :addParams="addParams.pageSetting.floatSetting" :components="addParams.componentSetting" @ok="(data)=>addParams.pageSetting.floatSetting=data"></floatSetting>
          </a-collapse-panel>
        </template>
      </a-collapse>
    </a-collapse-panel>
    <a-collapse-panel key="2" header="加载逻辑" v-if="addParams.pageSetting.pageType==2">
      <addLink :disabled="disabled" type="2" :showType="[1]" :links="addParams.actionLinks" :maxLinkNum="1" :isEvent="false" @ok="(link)=>addParams.actionLinks=link">
        <a-button block>设置逻辑行为</a-button>
      </addLink>
    </a-collapse-panel>
  </a-collapse>
</template>
<script setup>
import pageNav from './pageNav.vue'
const emit = defineEmits(['ok', 'cancel'])
import addModal from './addModal.vue'
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  addParams: {
    type: Object,
    default() {
      return {}
    }
  },
  // 页面选择
  pageSetting: {
    type: Array,
    default() {
      return [
        { key: '1', value: '常规页', },
        { key: '2', value: '加载页', },
        { key: '3', value: '开屏页', },
        { key: '4', value: '弹窗', },
      ]
    }
  },
  navSetting: {
    type: Array,
    default() {
      return [
        { value: 1, name: '固定标题' },
        { value: 2, name: '完全沉浸' },
        { value: 3, name: '滑动恢复' },
        { value: 4, name: '固定恢复' },
      ]
    }
  }
})
const { activeKey, activeKey1 } = toRefs(reactive({
  activeKey: ['1'],
  activeKey1: ['1-1']
}))


// 上传图片 视频
const uploadSuccess = async (data) => {
  let { form, path, imgUrl } = data;
  form.pageSetting[path] = imgUrl
}


// 修改颜色
const changeColor = async (key, color) => {
  props.addParams.pageSetting[key] = color
}

// 页面类型变化
const changePageType = (e) => {
  if (e.target.value != 4) {
    props.addParams.pageSetting.pageWidth = 750;
  }
}

</script>

<style>
</style>
