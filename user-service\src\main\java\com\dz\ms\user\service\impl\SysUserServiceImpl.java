package com.dz.ms.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.constant.ClientTypeConstant;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.utils.MD5;
import com.dz.common.core.constants.CommonConstants;
import com.dz.common.core.dto.KeyValueDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.dto.user.SysUserDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.basic.TenantInfoFeginClient;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.CommonUtils;
import com.dz.common.core.utils.IpAdrressUtil;
import com.dz.common.core.utils.ParamUtils;
import com.dz.common.core.utils.RSAUtil;
import com.dz.common.core.utils.RandomUtils;
import com.dz.ms.user.constants.CacheKeys;
import com.dz.ms.user.dto.AccountLoginDTO;
import com.dz.common.core.dto.user.SysPermissionDTO;
import com.dz.ms.user.dto.SysRoleDTO;
import com.dz.ms.user.dto.UpdatePasswordDTO;
import com.dz.ms.user.dto.UserRoleDTO;
import com.dz.ms.user.dto.dkeyam.req.CallbackGetAllUserParam;
import com.dz.ms.user.dto.dkeyam.req.StrongAuthenticateParam;
import com.dz.ms.user.dto.dkeyam.res.DKeyAmResult;
import com.dz.ms.user.entity.SysUser;
import com.dz.ms.user.entity.SysUsersRole;
import com.dz.ms.user.entity.SysUsersStore;
import com.dz.ms.user.mapper.SysUserMapper;
import com.dz.ms.user.mapper.SysUsersRoleMapper;
import com.dz.ms.user.mapper.SysUsersStoreMapper;
import com.dz.ms.user.service.DKeyAmOpenApiService;
import com.dz.ms.user.service.SysRoleService;
import com.dz.ms.user.service.SysUserService;
import com.dz.ms.user.utils.JwtTokenUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import com.dz.ms.user.utils.BCryptPasswordEncoder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 系统用户信息
 * @author: Handy
 * @date:   2022/2/4 23:04
 */
@Slf4j
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper,SysUser> implements SysUserService {

    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private SysUsersRoleMapper sysUsersRoleMapper;
    @Resource
    private SysUsersStoreMapper sysUsersStoreMapper;
    @Resource
    private SysRoleService sysRoleService;
    @Resource
    private RedisService redisService;
    @Resource
    private TenantInfoFeginClient tenantInfoFeginClient;
    @Value("${sys.user.password.privateKey:}")
    private String privateKey;
    @Value("${dkeyam.api.whiteList:}")
    private String[] whiteList;
    @Value("${rsa.type.transformation}")
    private String transformation;
    @Resource
    private DKeyAmOpenApiService dKeyAmOpenApiService;
    
    /**
     * 保存系统用户
     * @param param
     * @return
     */
    @Override
    public Long saveSysUser(SysUserDTO param) {
        SysUser sysUser = sysUserMapper.getUserByUsername(param.getUsername(),param.getTenantId());
        if(null != sysUser && null != sysUser.getId() && (null == param.getId() || !sysUser.getId().equals(param.getId()))) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"账号已存在");
        }
        sysUser = new SysUser(param.getId(), param.getUsername(), null, null, param.getRealname(), param.getMobile(), param.getState(), param.getIsAdmin(),0);
        sysUser.setTenantId(param.getTenantId());
        String sms = null;
        if(ParamUtils.isNullOr0Long(sysUser.getId())) {
            String password = "muji6688";
            String salt = UUID.randomUUID().toString().trim().replaceAll("-","");
            String encode = MD5.encode(MD5.encodeLowerCase(password)+salt);
            encode = new BCryptPasswordEncoder().encode(encode);
            sysUser.setPassword(encode);
            sysUser.setSalt(salt);
            sysUser.setChangePassword(1);
            //sms = "您的登录账号："+param.getUsername()+"，初始密码："+password+"，首次登录可修改初始密码";
            sysUserMapper.insert(sysUser);
        }
        else {
            sysUser.setPassword(null);
            sysUser.setSalt(null);
            sysUser.setUsername(null);
            sysUserMapper.updateById(sysUser);
            redisService.del(CacheKeys.SYS_USER_INFO + param.getTenantId() +":"+ sysUser.getId());
        }
        if(null != param.getRoleId()) {
            Long userid = ParamUtils.isNullOr0Long(param.getId()) ? sysUser.getId() : param.getId();
            UserRoleDTO userRole = new UserRoleDTO();
            userRole.setUserId(userid);
            userRole.setRoleId(param.getRoleId());
            bindRole(userRole);
        }
        return sysUser.getId();
    }

    /**
     * 用户密码确认并发送短信验证码
     * @param param
     * @return
     */
    @Override
    public boolean passwordCheck(AccountLoginDTO param) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String logmsg = "CRM用户密码确认并发送短信验证码,ip:"+ IpAdrressUtil.getIpAdrress(request)+",tenantId:"+param.getTenantId()+",username:"+param.getUsername()+",";
        log.info(logmsg);
        String imageCode = redisService.getString(CacheKeys.IMAGE_CODE + param.getCodeKey());
        if (!param.getImageCode().equalsIgnoreCase(imageCode)) {
            log.info(logmsg+"验证码错误");
            throw new BusinessException("验证码错误");
        }
        redisService.del(CacheKeys.IMAGE_CODE + param.getCodeKey());
        SysUser sysUser = sysUserMapper.getUserByUsername(param.getUsername(),param.getTenantId());
        if(null == sysUser) {
            log.info(logmsg+"账号不存在");
            throw new BusinessException(ErrorCode.BAD_REQUEST,"账号不存在");
        }
        //String password = PasswordUtils.decrypt();
        String encode = MD5.encode(MD5.encodeLowerCase(param.getPassword())+sysUser.getSalt());
        if(!new BCryptPasswordEncoder().matches(encode,sysUser.getPassword())) {
            log.info(logmsg+"用户名或密码错误");
            throw new BusinessException(ErrorCode.BAD_REQUEST,"用户名或密码错误");
        }
        if(sysUser.getState().equals(0)) {
            log.info(logmsg+"该账号已停用");
            throw new BusinessException(ErrorCode.BAD_REQUEST,"该账号已停用");
        }
        if(StringUtils.isBlank(sysUser.getMobile())) {
            log.info(logmsg+"该账号未绑定手机号码");
            throw new BusinessException(ErrorCode.BAD_REQUEST,"该账号未绑定手机号码，请联系管理员绑定");
        }
        if(null != sysUser.getChangePassword() && sysUser.getChangePassword().equals(1)) {
            log.info(logmsg+"首次登录需要修改密码");
            return true;
        }
        String laskKey = CacheKeys.SMS_CODE + ClientTypeConstant.CRM + ":last:" + sysUser.getMobile();
        String lastCode = redisService.getString(laskKey);
        if(StringUtils.isNotBlank(lastCode)) {
            log.info(logmsg+"验证码发送频率过快");
            throw new BusinessException(ErrorCode.BAD_REQUEST,"验证码发送频率过快，请稍后再发");
        }
        String countKey = CacheKeys.SMS_CODE + ClientTypeConstant.CRM + ":count:" + sysUser.getMobile();
        String codeStr = redisService.getString(countKey);
        int count = NumberUtils.toInt(codeStr);
        if(count >= 15) {
            log.info(logmsg+"发送验证码次数超过限制");
            throw new BusinessException(ErrorCode.BAD_REQUEST,"发送验证码次数超过限制");
        }
        count ++;
        String smsCode = RandomUtils.genNumberCode(6);
        redisService.setString(laskKey, smsCode, 60);
        redisService.setString(CacheKeys.SMS_CODE + ClientTypeConstant.CRM + ":code:" + sysUser.getMobile(), smsCode, 300);
        redisService.set(countKey, count, CommonConstants.DAY_SECONDS);
        /** 发送短信验证码 */
        log.info("{}发送短信验证码:{}",sysUser.getMobile(),smsCode);
        /*Result<Boolean> result = smsFeginClient.sendSmsMessage(sysUser.getMobile(), "登录验证码："+smsCode,param.getTenantId());
        if(!result.isSuccess()) {
            log.info(logmsg+"发送短信验证码失败");
            throw new BusinessException(ErrorCode.BAD_REQUEST,"发送短信验证码失败请稍后再试");
        }*/
        log.info(logmsg+"发送成功");
        return false;
    }

    /**
     * 密码加短信验证码登录
     * @param param
     * @return
     */
    @Override
    public SysUserDTO passwordSmsLogin(AccountLoginDTO param) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String logmsg = "CRM密码短信验证码登录,ip:"+ IpAdrressUtil.getIpAdrress(request)+",tenantId:"+param.getTenantId()+",username:"+param.getUsername()+",";
        log.info(logmsg);
        SysUser sysUser = sysUserMapper.getUserByUsername(param.getUsername(),param.getTenantId());
        if(null == sysUser) {
            log.info(logmsg+"账号不存在");
            throw new BusinessException(ErrorCode.BAD_REQUEST,"账号不存在");
        }
        //String password = PasswordUtils.decrypt(param.getPassword());
        String encode = MD5.encode(MD5.encodeLowerCase(param.getPassword())+sysUser.getSalt());
        if(!new BCryptPasswordEncoder().matches(encode,sysUser.getPassword())) {
            log.info(logmsg+"用户名或密码错误");
            throw new BusinessException(ErrorCode.BAD_REQUEST,"用户名或密码错误");
        }
        String inputKey = CacheKeys.SMS_CODE + ClientTypeConstant.CRM + ":input:" + sysUser.getMobile();
        String countStr = redisService.getString(inputKey);
        int count = NumberUtils.toInt(countStr);
        if(count >= 5) {
            log.info(logmsg+"短信验证码输入次数超限{}",count);
            throw new BusinessException(ErrorCode.BAD_REQUEST,"短信验证码输入次数超限，请重新登录");
        }
        count ++;
        String smsCode = redisService.getString(CacheKeys.SMS_CODE + ClientTypeConstant.CRM + ":code:" + sysUser.getMobile());
        if(null == smsCode) {
            log.info(logmsg+"短信验证码已过期");
            throw new BusinessException(ErrorCode.BAD_REQUEST,"短信验证码已过期");
        }
        if(!smsCode.equals(param.getSmsCode())) {
            redisService.set(inputKey,count,300);
            log.info(logmsg+"短信验证码错误");
            throw new BusinessException(ErrorCode.BAD_REQUEST,"短信验证码错误");
        }
        redisService.del(inputKey);
        SecurityContext.setUser(new CurrentUserDTO(ClientTypeConstant.CRM,sysUser.getId(),param.getTenantId(),null));
        Long roleId = 0L;
        if(ParamUtils.Integer2int(sysUser.getIsAdmin()) != 1) {
            roleId = getUserRole(sysUser.getId(), sysUser.getTenantId());
        }
        String uuid = UUID.randomUUID().toString();
        Map<String, Object> claims = new HashMap<>();
        claims.put("id",sysUser.getId());
        claims.put("type", ClientTypeConstant.CRM);
        claims.put("tid", sysUser.getTenantId());
        claims.put("role", roleId);
        claims.put("pt", param.getPlatform());
        String token = JwtTokenUtils.generatorToken(claims,uuid,24);
        redisService.set(CacheKeys.SYSUSER_SECRET + sysUser.getTenantId() +":"+ sysUser.getId(),uuid, CommonConstants.DAY_SECONDS);
        SysUserDTO sysUserDTO = BeanCopierUtils.convertObject(sysUser,SysUserDTO.class);
        redisService.set(CacheKeys.SYS_USER_INFO + sysUser.getTenantId() +":"+ sysUser.getId(),sysUserDTO, CommonConstants.DAY_SECONDS);
        List<String> functionCodes = sysRoleService.getRoleFunctionCodes(roleId,param.getTenantId(),param.getPlatform());
        List<SysPermissionDTO> menuList = sysRoleService.getRoleMenuTree(roleId,param.getTenantId(),param.getPlatform());
        sysUserDTO.setRoleId(roleId);
        sysUserDTO.setFunctionCodes(functionCodes);
        sysUserDTO.setMenuList(menuList);
        sysUserDTO.setToken(token);
        sysUser.setChangePassword(0);
        sysUserMapper.updateById(sysUser);
        log.info(logmsg+"登录成功");
        return sysUserDTO;
    }

    /**
     * 账号密码登录
     * @param param
     * @return
     */
    @Override
    public SysUserDTO passwordLogin(AccountLoginDTO param) {
        SysUser sysUser = sysUserMapper.getUserByUsername(param.getUsername(),param.getTenantId());
        if(null == sysUser) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"用户名或密码错误");
        }
        try {
            param.setPassword(RSAUtil.decrypt(param.getPassword(),privateKey,transformation));
        } catch (Exception e){
            log.error("账号密码登录RSA解密异常param:{}", CommonUtils.jsonStr(param),e);
            throw new BusinessException(ErrorCode.BAD_REQUEST,"用户名或密码错误");
        }
        String encode = MD5.encode(MD5.encodeLowerCase(param.getPassword())+sysUser.getSalt());
        if(!new BCryptPasswordEncoder().matches(encode,sysUser.getPassword())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"用户名或密码错误");
        }
        if(sysUser.getState().equals(0)) {
            log.info("该账号已停用");
            throw new BusinessException(ErrorCode.BAD_REQUEST,"用户名或密码错误");
        }
        return null;
    }

    /**
     * CRM后台宁盾认证
     * @param param 入参
     * @return SysUserDTO
     */
    @Override
    public SysUserDTO strongAuthenticate(AccountLoginDTO param) {
        SysUser sysUser = sysUserMapper.getUserByUsername(param.getUsername(),param.getTenantId());
        if(null == sysUser) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"用户名或密码错误");
        }
        try {
            param.setPassword(RSAUtil.decrypt(param.getPassword(),privateKey,transformation));
        } catch (Exception e){
            log.error("账号密码登录RSA解密异常param:{}", CommonUtils.jsonStr(param),e);
            throw new BusinessException(ErrorCode.BAD_REQUEST,"用户名或密码错误");
        }
        String encode = MD5.encode(MD5.encodeLowerCase(param.getPassword())+sysUser.getSalt());
        if(!new BCryptPasswordEncoder().matches(encode,sysUser.getPassword())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"用户名或密码错误");
        }
        if(sysUser.getState().equals(0)) {
            log.info("该账号已停用");
            throw new BusinessException(ErrorCode.BAD_REQUEST,"用户名或密码错误");
        }
        StrongAuthenticateParam strongAuthenticateParam = StrongAuthenticateParam.builder().loginName(param.getUsername()).password(param.getUsername() + param.getDkeyAmPassword()).build();
        log.info("========================whiteList:{}",CommonUtils.jsonStr(whiteList));
        if (isArrayEmpty(whiteList) || arrayNotContains(whiteList, param.getUsername())) {
            DKeyAmResult<String> dKeyAmResult = dKeyAmOpenApiService.strongAuthenticateParam(strongAuthenticateParam);
            if(dKeyAmResult.isFail()){
                throw new BusinessException(ErrorCode.BAD_REQUEST,"动态口令错误");
            }
        }
        SecurityContext.setUser(new CurrentUserDTO(ClientTypeConstant.CRM,sysUser.getId(),param.getTenantId(),null));
        Long roleId = 0L;
        if(ParamUtils.Integer2int(sysUser.getIsAdmin()) != 1) {
            roleId = getUserRole(sysUser.getId(), sysUser.getTenantId());
        }
        String uuid = UUID.randomUUID().toString();
        Map<String, Object> claims = new HashMap<>();
        claims.put("id",sysUser.getId());
        claims.put("type", ClientTypeConstant.CRM);
        claims.put("tid", sysUser.getTenantId());
        claims.put("role", roleId);
        claims.put("pt", param.getPlatform());
        String token = JwtTokenUtils.generatorToken(claims,uuid,24);
        redisService.set(CacheKeys.SYSUSER_SECRET + sysUser.getTenantId() +":"+ sysUser.getId(),uuid, CommonConstants.DAY_SECONDS);
        SysUserDTO sysUserDTO = BeanCopierUtils.convertObject(sysUser,SysUserDTO.class);
        redisService.set(CacheKeys.SYS_USER_INFO + sysUser.getTenantId() +":"+ sysUser.getId(),sysUserDTO, CommonConstants.DAY_SECONDS);
        List<String> functionCodes = sysRoleService.getRoleFunctionCodes(roleId,param.getTenantId(),param.getPlatform());
        List<SysPermissionDTO> menuList = sysRoleService.getRoleMenuTree(roleId,param.getTenantId(),param.getPlatform());
        sysUserDTO.setRoleId(roleId);
        sysUserDTO.setFunctionCodes(functionCodes);
        sysUserDTO.setMenuList(menuList);
        sysUserDTO.setToken(token);
        sysUser.setChangePassword(0);
        sysUserMapper.updateById(sysUser);
        log.info("登录成功");
        return sysUserDTO;
    }

    /**
     * 判断数组是否为空
     */
    public static boolean isArrayEmpty(String[] array) {
        return array == null || array.length == 0;
    }
    /**
     * 判断数组是否不包含目标字符串
     */
    public static boolean arrayNotContains(String[] array, String target) {
        return !(Arrays.asList(array).contains(target));
    }

    /**
     * 获取用户角色ID
     *
     * @param uid
     * @param tenantId
     * @return
     */
    @Override
    public Long getUserRole(Long uid, Long tenantId) {
        String key = CacheKeys.SYS_USER_ROLE + tenantId +":"+ uid;
        String id = redisService.getString(key);
        if(null != id) {
            return NumberUtils.toLong(id);
        }
        LambdaQueryWrapper<SysUsersRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUsersRole :: getUid,uid);
        SysUsersRole sysUsersRole = sysUsersRoleMapper.selectOne(wrapper);
        if(null == sysUsersRole) {
            return null;
        }
        redisService.set(key,sysUsersRole.getRoleId(),CommonConstants.WEEK_SECONDS);
        return sysUsersRole.getRoleId();
    }

    /**
     * 获取用户可用门店ID列表
     * @param uid
     * @param tenantId
     * @return
     */
    @Override
    public List<Long> getUserStore(Long uid, Long tenantId) {
        String key = CacheKeys.SYS_USER_STORE + tenantId +":"+ uid;
        List<Long> list = (List<Long>) redisService.get(key);
        if(null != list) {
            return list;
        }
        LambdaQueryWrapper<SysUsersStore> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUsersStore :: getUid,uid);
        List<SysUsersStore> storeList = sysUsersStoreMapper.selectList(wrapper);
        if(CollectionUtils.isEmpty(storeList)) {
            list = new ArrayList<>();
        }
        else {
            list = storeList.stream().map(SysUsersStore::getStoreId).collect(Collectors.toList());
        }
        redisService.set(key,list,CommonConstants.WEEK_SECONDS);
        return list;
    }

    /**
     * 绑定用户角色
     * @param param
     */
    @Override
    public void bindRole(UserRoleDTO param) {
        LambdaQueryWrapper<SysUsersRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUsersRole :: getUid,param.getUserId());
        SysUsersRole sysUsersRole = sysUsersRoleMapper.selectOne(wrapper);
        if(null == sysUsersRole || null == sysUsersRole.getId()) {
            sysUsersRole = new SysUsersRole();
            sysUsersRole.setUid(param.getUserId());
            sysUsersRole.setRoleId(param.getRoleId());
            sysUsersRoleMapper.insert(sysUsersRole);
        }
        else if(!sysUsersRole.getRoleId().equals(param.getRoleId())) {
            SysUsersRole updateSysUsersRole = new SysUsersRole();
            updateSysUsersRole.setId(sysUsersRole.getId());
            updateSysUsersRole.setRoleId(param.getRoleId());
            sysUsersRoleMapper.updateById(updateSysUsersRole);
            redisService.del(CacheKeys.SYS_USER_ROLE + SecurityContext.getUser().getTenantId() +":"+ param.getUserId());
        }
    }

    /**
     * 根据账号获取租户信息
     * @param username
     * @return
     */
    @Override
    public List<KeyValueDTO> getTenantsByUsername(String username) {
        List<SysUser> list = sysUserMapper.selectList(new LambdaQueryWrapper<SysUser>().eq(SysUser :: getUsername,username));
        if(CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<Long> ids = list.stream().map(SysUser::getTenantId).collect(Collectors.toList());
        return tenantInfoFeginClient.getTenantInfoByIds(ids).getData();
    }

    /**
     * 根据角色Id列表获取角色对应绑定用户数量
     * @param roleIds
     * @return
     */
    @Override
    public Map<Long,Integer> getRoleUserCountByRoleIds(List<Long> roleIds) {
        List<SysRoleDTO> list = sysUsersRoleMapper.getRoleUserCountByRoleIds(roleIds);
        Map<Long, Integer> map = list.stream().collect(Collectors.toMap(SysRoleDTO :: getId,so-> so.getUserNum()));
        return map;
    }

    /**
     * 重置密码
     * @param uid
     */
    @Override
    public void passwordReset(Long uid) {
        String key = CacheKeys.REST_PASSWORD_COUNT+SecurityContext.getUser().getTenantId()+":"+uid;
        Object num = redisService.get(key);
        if(null == num) {
            redisService.set(key,1,CommonConstants.DAY_SECONDS);
        }
        else {
            int count = (int) num;
            if(count >= 3) {
                throw new BusinessException(ErrorCode.BAD_REQUEST,"重置密码次数超出限制");
            }
            redisService.incr(key,1,CommonConstants.DAY_SECONDS);
        }
        SysUser sysUser = sysUserMapper.selectById(uid);
        if(null == sysUser) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"用户不存在");
        }
        String password = "muji6688";
        String salt = sysUser.getSalt();
        String encode = MD5.encode(MD5.encodeLowerCase(password)+salt);
        encode = new BCryptPasswordEncoder().encode(encode);
        sysUser.setPassword(encode);
        String sms = "您的登录账号："+sysUser.getUsername()+"，初始密码："+password+"，首次登录可修改初始密码";
        sysUser.setTenantId(SecurityContext.getUser().getTenantId());
        sysUser.setChangePassword(1);
        sysUserMapper.updateById(sysUser);
        /*Result<Boolean> smsResult = smsFeginClient.sendSmsMessage(sysUser.getMobile(),sms,sysUser.getTenantId());
        if(!smsResult.isSuccess()) {
            throw new BusinessException(ErrorCode.INTERNAL_ERROR,"发送短信验证码错误");
        }*/
    }

    /**
     * 修改密码
     * @param param
     */
    @Override
    public void passwordUpdate(UpdatePasswordDTO param) {
        /*Pattern pattern = Pattern.compile("^(?=.*[A-Z])(?=.*[a-z])(?=.*\\d)(?=.*[@#$%^&+=!]).{15,}$");
        String newPassword = PasswordUtils.decrypt(param.getNewPassword());
        Matcher matcher = pattern.matcher(newPassword);
        if (!matcher.matches()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"密码必须同时包含大写、小写、数字和特殊字符其中三项且至少15位");
        }*/
        SysUser sysUser = null;
        if(null == param.getTenantId()) {
            param.setTenantId(SecurityContext.getUser().getTenantId());
            sysUser = sysUserMapper.selectById(SecurityContext.getUser().getUid());
        }
        else {
            sysUser = sysUserMapper.getUserByUsername(param.getUsername(),param.getTenantId());
        }
        if(null == sysUser) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"账号不存在");
        }
        String salt = sysUser.getSalt();
        String oldPassword;
        String newPassword;
        try {
            oldPassword = RSAUtil.decrypt(param.getOldPassword(),privateKey,transformation);
            newPassword = RSAUtil.decrypt(param.getNewPassword(),privateKey,transformation);
        } catch (Exception e){
            log.error("修改密码RSA解密异常param:{}", CommonUtils.jsonStr(param),e);
            throw new BusinessException(ErrorCode.BAD_REQUEST,"密码错误");
        }
        //String oldPassword = PasswordUtils.decrypt(param.getOldPassword());
        String encode = MD5.encode(MD5.encodeLowerCase(oldPassword)+salt);
        if(!new BCryptPasswordEncoder().matches(encode,sysUser.getPassword())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"原密码错误");
        }
        encode = MD5.encode(MD5.encodeLowerCase(newPassword)+salt);
        encode = new BCryptPasswordEncoder().encode(encode);
        sysUser.setPassword(encode);
        sysUser.setChangePassword(0);
        sysUser.setTenantId(param.getTenantId());
        sysUserMapper.updateById(sysUser);
    }

    /**
     * 登出
     */
    @Override
    public void logout() {
        CurrentUserDTO currentUser = SecurityContext.getUser();
        redisService.del(CacheKeys.SYSUSER_SECRET +currentUser.getTenantId() +":"+ currentUser.getUid());
    }

    /**
     * 根据userIdList查询系统用户信息
     *
     * @param userIds
     * @return
     */
    @Override
    public List<SysUser> getByIds(List<Long> userIds,Long tenantId) {
        List<SysUser> userList = sysUserMapper.selectByIds(userIds,tenantId);
        return userList;
    }

    /**
     * 根据ID删除系统用户信息
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSysUserById(Long id) {
        sysUserMapper.deleteById(id);
        sysUsersRoleMapper.delete(new LambdaQueryWrapper<SysUsersRole>().eq(SysUsersRole::getUid,id));
    }

    /**
     * 根据UID查询系统用户信息
     * @return
     */
    @Override
    public SysUserDTO getUserByUid(Long uid, Long tenantId) {
        SysUserDTO sysUserDTO = (SysUserDTO) redisService.get(CacheKeys.SYS_USER_INFO + tenantId +":"+ uid);
        if(null == sysUserDTO) {
            SysUser sysUser = this.getById(uid);
            if(sysUser != null){
                sysUserDTO = BeanCopierUtils.convertObject(sysUser,SysUserDTO.class);
                redisService.set(CacheKeys.SYS_USER_INFO + tenantId +":"+ uid,sysUserDTO, CommonConstants.DAY_SECONDS);
            }
        }
        return sysUserDTO;
    }

    /**
     * 根据UID查询系统用户信息(包含角色id)
     * @return SysUserDTO
     */
    @Override
    public SysUserDTO getUserContainRootIdByUid(Long uid, Long tenantId) {
        SysUserDTO sysUserDTO = this.getUserByUid(uid, tenantId);
        if(sysUserDTO != null){
            sysUserDTO.setRoleId(this.getUserRole(uid, tenantId));
        }
        return sysUserDTO;
    }

    /**
     * 查询当前登录系统用户信息(包含角色id)
     * @return SysUserDTO
     */
    @Override
    public SysUserDTO getLoginUserContainRootId() {
        CurrentUserDTO currentUser = SecurityContext.getUser();
        return this.getUserContainRootIdByUid(currentUser.getUid(),currentUser.getTenantId());
    }

    /**
     * 宁盾外部用户认证
     * @param param 入参
     * @return SysUserDTO
     */
    @Override
    public SysUserDTO dKeyAmCallbackAuthenticate(AccountLoginDTO param) {
        SysUser sysUser = sysUserMapper.getUserByUsername(param.getUsername(),param.getTenantId());
        if(null == sysUser) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"未查询到此账号");
        }
        String encode = MD5.encode(MD5.encodeLowerCase(param.getPassword())+sysUser.getSalt());
        if(!new BCryptPasswordEncoder().matches(encode,sysUser.getPassword())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"密码错误");
        }
        if(sysUser.getState().equals(0)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"账号已停用");
        }
        return BeanCopierUtils.convertObject(sysUser,SysUserDTO.class);
    }

    /**
     * 宁盾获取单个用户的信息
     * @param param 入参
     * @return SysUserDTO
     */
    @Override
    public SysUserDTO dKeyAmCallbackGetOneUser(AccountLoginDTO param) {
        SysUser sysUser = sysUserMapper.getUserByUsername(param.getUsername(),param.getTenantId());
        if(null == sysUser) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"未查询到此账号");
        }
        if(sysUser.getState().equals(0)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"账号已停用");
        }
        return BeanCopierUtils.convertObject(sysUser,SysUserDTO.class);
    }

    /**
     * 宁盾同步所有用户的信息
     * @param param 入参
     * @return IPage<SysUserDTO>
     */
    @Override
    public IPage<SysUserDTO> dKeyAmCallbackGetAllUserReq(CallbackGetAllUserParam param) {
        return sysUserMapper.selPageList(new Page<>(param.getStartRow(), param.getLimit()),1L);
    }

    public static void main(String[] args) {
        String password = "muji6688";
        String encode = MD5.encode(MD5.encodeLowerCase(password)+"217693941c594b5fa4e111035b368ff5");
        if(!new BCryptPasswordEncoder().matches(encode,"$2a$10$zmiNwsNxz30vIxCfPNqowu4c9rjuHAhDJiODOcisDYIq6G2phaxvO")) {

        }

        /*String password = "muji6688";
        String salt = "217693941c594b5fa4e111035b368ff5";
        String encode = MD5.encode(MD5.encodeLowerCase(password)+salt);
        encode = new BCryptPasswordEncoder().encode(encode);
        */
    }

}
