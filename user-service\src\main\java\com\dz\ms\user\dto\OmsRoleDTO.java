package com.dz.ms.user.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * OMS-系统角色DTO
 * @author: Handy
 * @date:   2020/01/29 19:15
 */
@Data
@ApiModel(value = "OMS-系统角色")
public class OmsRoleDTO extends BaseDTO {
    @ApiModelProperty(value = "角色ID")
    private Long id;
    @ApiModelProperty(value = "角色名称")
    private String roleName;
    @ApiModelProperty(value = "角色描述")
    private String roleDesc;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "修改时间")
    private Date modified;
    @ApiModelProperty(value = "修改人")
    private Long modifier;

}