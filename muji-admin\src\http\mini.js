// 所有的命名必须全局唯一
import service from '@/utils/request.js'

// 分页查询小程序页面模板
export function templateList(data) {
  return service({
    url: '/crm/basic/miniapp_template/list',
    method: 'get',
    data,
  })
}


// 新增小程序页面模板
export function addTemplate(data) {
  return service({
    url: '/crm/basic/miniapp_template/add',
    method: 'post',
    data,
  })
}

// 更新小程序页面模板
export function updateTemplate(data) {
  return service({
    url: '/crm/basic/miniapp_template/update',
    method: 'post',
    data,
  })
}


// 根据ID查询小程序页面模板
export function templateInfo(data) {
  return service({
    url: '/crm/basic/miniapp_template/info',
    method: 'get',
    data,
  })
}




// 根据ID删除小程序页面模板
export function deleteTemplate(data) {
  return service({
    url: '/crm/basic/miniapp_template/delete',
    method: 'post',
    data,
  })
}

// 变更小程序路径
export function updateTemplatePath(data) {
  return service({
    url: '/crm/basic/miniapp_template/updatePath',
    method: 'post',
    data,
  })
}

// 小程序二维码
export function miniCode(data) {
  return service({
    url: '/crm/basic/miniapp/qrcode',
    method: 'get',
    data,
    responseType: 'arraybuffer'
  })
}



// 保存小程序导航自定义配置
export function navSave(data) {
  return service({
    url: '/crm/basic/navigation_config/save',
    method: 'post',
    data,
  })
}


// 获取当前导航配置
export function getNav(data) {
  return service({
    url: '/crm/basic/navigation_config/default',
    method: 'get',
    data,
  })
}

// 保存当前风格配置
export function styleSave(data) {
  return service({
    url: '/crm/basic/style_config/save',
    method: 'post',
    data,
  })
}


// 获取当前风格配置
export function getStyle(data) {
  return service({
    url: '/crm/basic/style_config/get',
    method: 'get',
    data,
  })
}


// 发布页面
export function publishTemplate(data) {
  return service({
    url: '/crm/basic/miniapp_template/publish',
    method: 'post',
    data,
  })
}

// 不分页查询小程序订阅消息模板配置
export function subscribeMsgList(data) {
  return service({
    url: '/crm/basic/subscribe_msg/listAll',
    method: 'get',
    data,
  })
}

// 获取当前订阅消息配置
export function msgConfigInfo(data) {
  return service({
    url: '/crm/basic/mp_msg_config/get',
    method: 'get',
    data,
  })
}

// 保存当前订阅消息配置
export function msgConfigSave(data) {
  return service({
    url: '/crm/basic/mp_msg_config/save',
    method: 'post',
    data,
  })
}
