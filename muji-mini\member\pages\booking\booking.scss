@import "assets/scss/config";

.page {
  height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;


  &-header {
    flex-shrink: 0;
    width: 100%;
    height: 701rpx;
  }

  &-bgCard {
    flex-shrink: 0;
    width: 670rpx;
    height: 588rpx
  }

  &-noPermission {
    text-align: center;

    &-icon {
      width: 120rpx;
      height: 120rpx;
      // margin-top: 200rpx;
      margin-top: 170rpx;
    }

    &-text {
      margin-top: 20rpx;
      // font-weight: 700;
      font-weight: 400;
      font-size: 33rpx;
      color: #888888;
      line-height: 50rpx;
    }
  }

  &-content {
    flex: 1;
    box-sizing: border-box;
    background: #ffffff;
    padding: 5rpx 40rpx 0;
    position: relative;
  }

  &-title {
    font-family: MUJIFont2020;
    font-weight: 700;
    font-size: 34rpx;
    color: #231815;
    line-height: 50rpx;
  }

  &-subTitle {
    font-family: SourceHanSansCN, MUJIFont2020;
    font-weight: 500;
    font-size: 26rpx;
    color: #2E2E2E;
    line-height: 59rpx;
  }

  &-tip {
    font-family: SourceHanSansCN, MUJIFont2020;
    font-weight: 400;
    font-size: 22rpx;
    color: #888888;
    line-height: 32rpx;
  }

  &-card::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    color: transparent;
  }

  &-card {
    display: flex;
    margin-top: 40rpx;
    overflow-y: scroll;

    // margin-bottom: 140rpx;
    &-footer {
      margin-top: 37rpx;
      font-family: Source Han Sans;
      font-size: 18rpx;
      font-weight: normal;
      line-height: 25rpx;
      letter-spacing: 0.04em;
      /* 正文色/正文辅助色 */
      color: #888888;
      margin-bottom: 200rpx;
    }

    &-box {

      display: flex;
      justify-content: space-between;
    }

    &-li {
      // margin-bottom: 20rpx;
      margin-right: 40rpx;
      text-align: center;
      width: 315rpx;
      height: 506rpx;
      box-sizing: border-box;
      background: linear-gradient( 90deg, rgba(245,245,245,0) 0%, rgba(245,245,245,0.7) 19%, rgba(245,245,245,0.7) 82%, rgba(245,245,245,0) 100%);
      border-radius: 20rpx 20rpx 20rpx 20rpx;

      border: 2rpx dashed #888888;
      &-img {
        width: 100%;
        height: 100%;
      }

      &.active {
        background: rgba(238, 234, 225, 0.4);
        border: 2rpx solid #94243A;
      }

    }

    &-li:last-child {
      margin-right: 0rpx;
    }

    &-title {
      margin-top: 36rpx;
      font-family: SourceHanSansCN, MUJIFont2020;
      font-weight: 700;
      font-size: 28rpx;
      color: #231815;
      line-height: 46rpx;
      letter-spacing: 7rpx;
      text-align: center;
      &.active {
        // background: rgba(238, 234, 225, 0.4);
        color: #94243A;
      }
    }

    &-desc {
      margin-top: 10rpx;
      // width: 276rpx;
      font-family: MUJIFont2020;
      font-weight: 400;
      font-size: 18rpx;
      color: #888888;
      line-height: 30rpx;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }

    &-icon {
      margin-top: 36rpx;
    }

    &-muji {
      margin-top: 4rpx;
      font-family:  MUJIFont2020;
      font-weight: 400;
      font-size: 20rpx;
      color: #231815;
      line-height: 33rpx;
      // line-height: 41rpx;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }

    &-dress {
      margin-top: 10rpx;
      // width: 194rpx;
      // height: 48rpx;
      display: flex;
      justify-content: center;
      font-family: SourceHanSansCN, MUJIFont2020;
      font-weight: 400;
      font-size: 17rpx;
      color: #888888;
      line-height: 24rpx;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }

    &-time-icon {
      margin-top: 30rpx;
    }

    &-time {
      margin-top: 4rpx;
      font-family:  MUJIFont2020;
      font-weight: 400;
      font-size: 20rpx;
      color: #231815;
      line-height: 33rpx;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }


  }

  &-form {
    margin-top: 50rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #3C3C43;

    &-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 40rpx;

      &:last-of-type {
        margin-bottom: 0;
      }
    }

    &-box {
      border-radius: 5rpx 5rpx 5rpx 5rpx;
      border: 2rpx solid #D8D8D9;
      background: #FFFFFF;
      width: 315rpx;
      height: 90rpx;
      box-sizing: border-box;
      padding: 27rpx 30rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &.isDisabled {
        pointer-events: none;
        color: #d8d8d8;
      }
    }

    &-arrow {
      width: 0;
      height: 0;
      border: 13rpx solid transparent;
      border-bottom: none;
      border-top-color: currentColor;
    }

    &-box1 {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 5rpx 5rpx 5rpx 5rpx;
      border: 2rpx solid #D8D8D9;
      background: #FFFFFF;
      height: 90rpx;
      flex: 1;
      box-sizing: border-box;
      padding: 27rpx 30rpx;

      &.isDisabled {
        pointer-events: none;
        color: #d8d8d8;
      }
    }

    &-label {
      width: 160rpx;
    }

    &-value {
      flex: 1;
      color: inherit;
    }

    &-icon {
      width: 30rpx;
      height: 30rpx;
      border: 2rpx solid #3C3C43;
      border-radius: 50%;
      line-height: 30rpx;
      font-size: 18rpx;
      text-align: center;
      margin-right: 10rpx;

      &.active {
        background-color: #3C3C43;
        color: #fff;
      }
    }

    &-tip {
      font-family: SourceHanSansCN, MUJIFont2020;
      font-weight: 400;
      font-size: 22rpx;
      color: #3C3C43;
    }

    &-underline {
      text-decoration: underline;
      text-underline-offset: 6rpx;
    }
  }

  &-button {
    position: fixed;
    width: 100%;
    bottom: 0;
    left: 50%;
    transform: translate(-50%, 0);
    text-align: center;
    padding-bottom: 60rpx;

    &-disabled {
      position: fixed;
      width: 100%;
      bottom: 0;
      left: 50%;
      transform: translate(-50%, 0);
      text-align: center;
      margin-bottom: 60rpx;
      width: 670rpx;
      background: #EEEEEE;
      height: 80rpx;
      font-size: 28rpx;
      border-radius: 5rpx;
      line-height: 80rpx;
      font-weight: 400;

    }
  }

  &-time {
    position: absolute;
    left: 80rpx;
    top: 570rpx;
    font-family: 'MUJIFont2020';
    font-weight: 400;
    font-size: 24rpx;
    color: #231815;

  }

  &-name {
    position: absolute;
    left: 80rpx;
    top: 642rpx;
    font-family: SourceHanSansCN;
    font-weight: 700;
    font-size: 24rpx;
    color: #231815;
  }

  &-canvas {
    position: fixed;
    left: 0;
    top: 200vh;
    width: 750rpx;
    height: 1624rpx;
  }
}
