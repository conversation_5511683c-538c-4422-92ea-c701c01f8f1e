const app = getApp();
Component({
  properties: {
    // 组件数据
    data: {
      type: Object,
      value() {
        return {};
      },
    },
    // 游客模式
    visitor: {
      type: Boolean,
    },
    // 组件宽度
    width: {
      type: Number,
      value: 750,
    },
  },
  data: {
    show: false,
    current: 0,
    delayTime: 0,
    rpx: app.globalData.rpx,
  },
  lifetimes: {
    attached() {
      let time = setTimeout(() => {
        this.setData({
          show: true,
        });
        clearTimeout(time);
        time = null;
      }, 1000);
      this.changeImg({
        detail: {
          current: 0,
        },
      });
    },
  },
  methods: {
    changeImg(e) {
      let { delayTime, delayTimes } = this.data.data;
      let current = e.detail.current;
      let data = delayTimes.find((item) => item.index == current);
      this.setData({
        delayTime: data?.delayTime || delayTime,
        current,
      });
    },
    // 返回顶部
    goTop() {
      this.triggerEvent("goTop");
    },
    // scroll 锚点滚动跳转
    goAchor(e) {
      this.triggerEvent("goAchor", e.detail);
    },
    // 对弹窗的操作
    goModal(e) {
      this.triggerEvent("goModal", e.detail);
    },
    // 分享
    goShare(e) {
      let { shareTitle, shareImg, sharePath } = e.detail;
      this.triggerEvent("goShare", {
        shareTitle,
        shareImg,
        sharePath,
      });
    },
    // 游客模式隐私协议
    showPrincy() {
      var currentInstance = wx.$mp.getCurrentPage();
      currentInstance.showOveralModal("secret");
    },
  },
});
