package com.dz.ms.basic.strategy;

import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.fegin.product.ShelfFeignClient;
import com.dz.common.core.service.DownloadStrategy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
public class ShelfListDownLoadStrategy implements DownloadStrategy {

    @Resource
    private ShelfFeignClient shelfFeignClient;

    @Override
    public void export(DownloadAddParamDTO downloadAddParamDTO) {
        shelfFeignClient.exportList(downloadAddParamDTO);
    }
}
