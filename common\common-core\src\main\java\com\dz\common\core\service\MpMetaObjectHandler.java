package com.dz.common.core.service;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.threadlocal.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 自动填充配置Handler
 * @author: Handy
 * @date:   2022/6/22 17:14
 */
@Slf4j
@Component
public class MpMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        log.info("start insert fill ....");
        Date date = new Date();
        this.strictInsertFill(metaObject, "created", Date.class, date);
        this.strictUpdateFill(metaObject, "modified", Date.class, date);
        CurrentUserDTO currentUser = SecurityContext.getUser();
        if(null != currentUser && null != currentUser.getUid()) {
            this.strictInsertFill(metaObject, "creator", Long.class, currentUser.getUid());
            this.strictUpdateFill(metaObject, "modifier", Long.class, currentUser.getUid());
        }
        else {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT,"未获取到当前用户ID!");
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        log.info("start update fill ....");
        this.strictUpdateFill(metaObject, "modified", Date.class, new Date());
        CurrentUserDTO currentUser = SecurityContext.getUser();
        if(null != currentUser && null != currentUser.getUid() ) {
            this.strictUpdateFill(metaObject, "modifier", Long.class, currentUser.getUid());
        }
        else {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT,"未获取到当前用户ID!");
        }
    }
}
