package com.dz.ms.basic.strategy;

import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.fegin.sales.SignInDetailClient;
import com.dz.common.core.service.DownloadStrategy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
public class SignInDetailListDownLoadStrategy implements DownloadStrategy {

    @Resource
    private SignInDetailClient signInDetailClient;

    @Override
    public void export(DownloadAddParamDTO downloadAddParamDTO) {
        signInDetailClient.exportList(downloadAddParamDTO);
    }
}
