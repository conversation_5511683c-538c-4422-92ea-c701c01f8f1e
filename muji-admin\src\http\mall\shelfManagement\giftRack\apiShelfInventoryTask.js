import service from '@/utils/request.js'
import { SHELF_STATUS_OBJ, SHELF_SWITCH_STATUS_OBJ } from '@/utils/constants.js'
import { v4 } from 'uuid'

const itemHandler = (v) => {
  v.onTypeTemp = v.onTypeTemp || v.onType || 1
  v.onTimeTemp = v.onTimeTemp || v.onTime
  v.onDaysTemp = v.onDaysTemp || v.onDays
  v.onInventoryTemp = v.onInventoryTemp || v.onInventory
  v.taskDesc = (['编辑', '实时', v.onTimeTemp, `每${v.onDaysTemp}天`])[v.onTypeTemp]
  v.stateDesc = (['待执行', '已执行', '执行中', '待编辑',])[v.state]
}

export const apiShelfInventoryTask = {
  async getPageList(data) {
    return await service({ url: '/crm/product/product_on_task/list', method: 'get', data }).then(res => {
      res.data.list.forEach(v => itemHandler(v))
      return res
    })
  },
  async createPage(data) {
    return await service({ url: '/crm/product/product_on_task/add', method: 'post', data }).then(res => {
      return res
    })
  },
  async updatePage(data) {
    return await service({ url: '/crm/product/product_on_task/update', method: 'post', data }).then(res => {
      return res
    })
  },
  async deletePage(data) {
    return await service({ url: '/crm/product/product_on_task/delete', method: 'post', data }).then(res => {
      return res
    })
  }
}
