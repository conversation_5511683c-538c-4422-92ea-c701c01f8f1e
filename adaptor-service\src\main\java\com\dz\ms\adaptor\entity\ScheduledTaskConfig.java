package com.dz.ms.adaptor.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Table;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@NoArgsConstructor
@Table("定时任务配置类")
@TableName(value = "scheduled_task_config")
public class ScheduledTaskConfig {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String taskName;

    private String cronExpression;

    private String param;

    private String className;

    private String methodName;

    private Date lastRunTime;
    //下一次执行时间
    private Date nextExecutionTime;

    private Integer status;


    private Date createdAt;

    private Date updatedAt;

    private Long tenantId;
}
