package com.dz.ms.user.dto.dkeyam.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 获取手机令牌信息 出参
 * @author: fei
 * @date: 2025/01/22 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MobileTokenDataRes implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("固定为4，代表手机令牌")
    private Integer model;

    @ApiModelProperty("令牌序列号")
    private String serial;

    @ApiModelProperty("令牌算法 1：Oath")
    private Integer crypto;

    @ApiModelProperty("种子")
    private String seed;

    @ApiModelProperty("令牌时间步进，单位秒")
    private Long timeStep;

    @ApiModelProperty("动态口令长度")
    private Integer passwordLength;

    private Long createTime;
    
    @ApiModelProperty("令牌过期时间")
    private Long expireTime;

}
