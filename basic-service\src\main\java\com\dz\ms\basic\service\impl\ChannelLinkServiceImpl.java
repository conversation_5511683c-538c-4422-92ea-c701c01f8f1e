package com.dz.ms.basic.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.enums.WechatApiEnum;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.service.WechatRequestSevice;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.constants.CacheKeys;
import com.dz.ms.basic.dto.ChannelLinkDTO;
import com.dz.ms.basic.entity.ChannelLink;
import com.dz.ms.basic.mapper.ChannelLinkMapper;
import com.dz.ms.basic.service.ChannelLinkService;
import com.dz.ms.basic.utils.BinaryConvertUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * 渠道链接配置
 * @author: Handy
 * @date:   2023/08/26 17:06
 */
@Service
public class ChannelLinkServiceImpl extends ServiceImpl<ChannelLinkMapper,ChannelLink> implements ChannelLinkService {

	@Resource
    private ChannelLinkMapper channelLinkMapper;
    @Resource
    private WechatRequestSevice wechatRequestSevice;
    @Resource
    private RedisService redisService;

    @Value("${service.tenantid:1}")
    public Long tenantId;

    /**
     * 分页查询渠道链接配置
     * @param param
     * @return PageInfo<ChannelLinkDTO>
     */
    @Override
    public PageInfo<ChannelLinkDTO> getChannelLinkList(ChannelLinkDTO param) {
        ChannelLink channelLink = BeanCopierUtils.convertObjectTrim(param,ChannelLink.class);
        channelLink.setTenantId(SecurityContext.getUser().getTenantId());
        IPage<ChannelLink> page = channelLinkMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), new QueryWrapper<>(channelLink));
        return new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),BeanCopierUtils.convertList(page.getRecords(), ChannelLinkDTO.class));
    }

    /**
     * 根据ID查询渠道链接配置
     * @param id
     * @return ChannelLinkDTO
     */
    @Override
    public ChannelLinkDTO getChannelLinkById(Long id) {
        ChannelLink channelLink = channelLinkMapper.selectById(id);
        return BeanCopierUtils.convertObject(channelLink,ChannelLinkDTO.class);
    }

    /**
     * 保存渠道链接配置
     * @param param
     * @return Long
     */
    @Override
    public Long saveChannelLink(ChannelLinkDTO param) {
        ChannelLink channelLink = new ChannelLink(param.getId(), param.getLinkCode(), param.getChannel(), param.getChannelName(), param.getPath(), param.getQuery());
        channelLink.setTenantId(SecurityContext.getUser().getTenantId());
        channelLink.setLinkCode(null);
        if(ParamUtils.isNullOr0Long(channelLink.getId())) {
            channelLinkMapper.insert(channelLink);
            ChannelLink updateChannelLink = new ChannelLink();
            updateChannelLink.setId(channelLink.getId());
            updateChannelLink.setLinkCode(BinaryConvertUtils.convertToStr(channelLink.getId()));
            channelLinkMapper.updateById(updateChannelLink);
        }
        else {
            channelLinkMapper.updateById(channelLink);
            redisService.del(CacheKeys.CHANNEL_LINK+ SecurityContext.getUser().getTenantId() +":"+param.getLinkCode());
        }
        return channelLink.getId();
    }

    /**
     * 根据ID删除渠道链接配置
     * @param param
     */
    @Override
    public void deleteChannelLinkById(IdCodeDTO param) {
        channelLinkMapper.deleteById(param.getId());
    }

    /**
     * 根据短链编码获取用户url scheme
     * @param code
     * @return
     */
    @Override
    public String getUrlSchemeByCode(String code) {
        ChannelLinkDTO channelLink = getChannelLinkByCode(code);
        JSONObject json = new JSONObject();
        JSONObject jumpWxa = new JSONObject();
        jumpWxa.put("path",channelLink.getPath());
        jumpWxa.put("query",channelLink.getQuery());
        json.put("jump_wxa",jumpWxa);
        String urlScheme = wechatRequestSevice.request(WechatApiEnum.API_GET_MINAPP_SCHEME,tenantId,String.class,json);
        return urlScheme;
    }

    /**
     * 根据短链编码获取URL SCHEME 配置
     * @param code
     * @return
     */
    private ChannelLinkDTO getChannelLinkByCode(String code) {
        String key = CacheKeys.CHANNEL_LINK+code;
        Object object = redisService.get(key);
        if(null != object) {
            return (ChannelLinkDTO) object;
        }
        ChannelLink channelLink = channelLinkMapper.selectOne(new LambdaQueryWrapper<ChannelLink>().eq(ChannelLink::getLinkCode,code));
        ChannelLinkDTO urlSchemeConfigDTO = new ChannelLinkDTO();
        urlSchemeConfigDTO.setPath(channelLink.getPath());
        urlSchemeConfigDTO.setQuery(channelLink.getQuery());
        redisService.set(key,urlSchemeConfigDTO);
        return urlSchemeConfigDTO;
    }
}