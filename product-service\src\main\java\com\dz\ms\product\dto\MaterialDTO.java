package com.dz.ms.product.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 商品素材表DTO
 *
 * @author: LiinNs
 * @date: 2024/11/18 13:45
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "商品素材表")
public class MaterialDTO extends BaseDTO {

    @ApiModelProperty(value = "商品素材id")
    private Long id;
    @ApiModelProperty(value = "类型 1图片 2视频")
    private Integer type;
    @ApiModelProperty(value = "商品id 0未绑定")
    private Long productId;
    @ApiModelProperty(value = "商品编号")
    private String productCode;
    @ApiModelProperty(value = "1场景图 2橱窗图 3兑换须知图 4商品详情图 5使用说明图")
    private Integer usedType;
    @ApiModelProperty(value = "图片地址")
    private String imgUrl;
    @ApiModelProperty(value = "图片名称")
    private String imgName;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "修改时间")
    private Date modified;
    @ApiModelProperty(value = "修改人")
    private Long modifier;
    @ApiModelProperty(value = "是否删除 0未删除 1已删除")
    private Integer isDeleted;

}
