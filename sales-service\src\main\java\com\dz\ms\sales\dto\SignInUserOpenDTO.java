package com.dz.ms.sales.dto;


import com.dz.common.core.annotation.Columns;
import com.dz.common.core.enums.ColumnType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/8
 */
@Data
public class SignInUserOpenDTO {

    @ApiModelProperty(value = "活动标识", example = "20250108")
    @NotBlank(message = "活动标识不能为空")
    private String campaignCode;

    @ApiModelProperty(value = "一级渠道ID")
    private String channelOne;
    @ApiModelProperty(value = "二级渠道ID")
    private String channelTwo;


    @ApiModelProperty(value = "素材链接", example = "https://www.baidu.com")
    @NotBlank(message = "素材链接不能为空")
    private String materialUrl;

    @ApiModelProperty(value = "期待 1提升皮肤耐受性，缓解敏感症状 2增加保湿度 3改善肤色不均 4舒缓泛红现象 5改善毛孔粗大 6改善皮肤屏障，增强保护力（多选，逗号分隔）", example = "1,2,3,4,5,6")
    @NotBlank(message = "期待不能为空")
    private String expectation;


}
