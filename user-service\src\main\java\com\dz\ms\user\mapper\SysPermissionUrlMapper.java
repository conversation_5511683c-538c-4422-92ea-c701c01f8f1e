package com.dz.ms.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.user.entity.SysPermissionUrl;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * 权限功能接口Mapper
 * @author: Handy
 * @date:   2022/07/18 18:24
 */
@Repository
public interface SysPermissionUrlMapper extends BaseMapper<SysPermissionUrl> {

    /**
     * 获取角色权限页面列表
     * @param roleId
     * @return
     */
    List<String> getRolePermitUrls(@Param("roleId") Long roleId);

    /**
     * 根据权限ID列表获取权限接口列表
     * @param permitIds
     * @return
     */
    List<String> getPermitUrlsByPermitIds(@Param("permitIds")Set<Long> permitIds);

}
