package com.dz.ms.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.Result;
import com.dz.common.core.constants.OrderCodeConstants;
import com.dz.common.core.dto.order.OrderCreateParamDTO;
import com.dz.common.core.dto.order.OrderCreateProductDTO;
import com.dz.common.core.dto.product.CartProductDTO;
import com.dz.common.core.dto.product.CartResultDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.product.ShoppingCartFeignClient;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.order.entity.ExchangeOrder;
import com.dz.ms.order.entity.OrderDetail;
import com.dz.ms.order.service.OrderCreateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class OrderCreateServiceImpl implements OrderCreateService {

    @Resource
    private ShoppingCartFeignClient shoppingCartClient;

    @Override
    public List<OrderDetail> createProduct(OrderCreateParamDTO dto, ExchangeOrder order) {
        String orderCode = order.getOrderCode();
        CurrentUserDTO user = SecurityContext.getUser();//获取当前登录用户
        Integer productPoint = 0;
        BigDecimal productAmount = BigDecimal.ZERO;
        Integer number = 0;
        List<OrderDetail> allDetailList = new ArrayList<>();//所有商品的订单详情
        //获得满减活动后的商品
        CartResultDTO cartResultDTO;
        if (dto.getSource() == 1) {
            //跳转来源为普通购物车
            Result<CartResultDTO> previewCartOrder = shoppingCartClient.getPreviewCartOrder();
            log.info("订单号:{}调用获取普通购物车订单预览商品列表接口出参:{}", orderCode, JSON.toJSONString(previewCartOrder));
            cartResultDTO = previewCartOrder.getData();
        } else {
            //跳转来源为立即购买
            OrderCreateProductDTO orderCreateProductDTO = dto.getProductList().get(0);
            log.info("订单号:{},调用单商品购买订单预览接口入参shelfProductId:{},number:{}", orderCode, orderCreateProductDTO.getShelfProductId(), orderCreateProductDTO.getNumber());
            Result<CartResultDTO> cartOrderBySpecId = shoppingCartClient.getCartOrderByShelfProductId(orderCreateProductDTO.getShelfProductId(), orderCreateProductDTO.getNumber());
            log.info("订单号:{},调用单商品购买订单预览接口出参:{}", orderCode, JSON.toJSONString(cartOrderBySpecId));
            cartResultDTO = cartOrderBySpecId.getData();
        }
        if (cartResultDTO == null || CollectionUtils.isEmpty(cartResultDTO.getValidCarts())) {
            throw new BusinessException("商品列表异常");
        }
        List<CartProductDTO> validProductList = cartResultDTO.getValidCarts();

//        productPoint = cartResultDTO.getTotalPoint();
//        productAmount = cartResultDTO.getTotalPrice();
        Long shelfId = null;
        for (CartProductDTO cartProductDTO : validProductList) {
            if (cartProductDTO.getChecked() == 0) {
                continue;
            }

            //组装订单详情表数据
            OrderDetail orderDetail = new OrderDetail();
            orderDetail.setOrderCode(orderCode);//订单编号
            Date now = new Date();
            orderDetail.setProductId(cartProductDTO.getProductId());//商品id
            orderDetail.setProductName(cartProductDTO.getProductName());//商品名称
            orderDetail.setPdType(cartProductDTO.getPdType());
            orderDetail.setVenderId(cartProductDTO.getVenderId());
            orderDetail.setImgUrl(cartProductDTO.getImgUrl());
            orderDetail.setCostPoint(cartProductDTO.getCostPoint());
            orderDetail.setCostPrice(cartProductDTO.getCostPrice());
            orderDetail.setPCostPoint(cartProductDTO.getPCostPoint());
            orderDetail.setPCostPrice(cartProductDTO.getPCostPrice());
            orderDetail.setShelfProductId(cartProductDTO.getShelfProductId());
            orderDetail.setShelfId(cartProductDTO.getShelfId());
            if (ParamUtils.isNullOr0Long(shelfId)) {
                shelfId = cartProductDTO.getShelfId();
            }
            orderDetail.setSCostPoint(cartProductDTO.getSCostPoint());
            orderDetail.setSPrePoint(cartProductDTO.getSPrePoint());
            orderDetail.setCampaignId(cartProductDTO.getCampaignId());
            orderDetail.setRuleId(cartProductDTO.getRuleId());
            orderDetail.setRuleType(cartProductDTO.getRuleType());
            orderDetail.setPeriod(cartProductDTO.getPeriod());
            orderDetail.setCampaignOnStartTime(cartProductDTO.getCampaignOnStartTime());
            orderDetail.setCampaignOnEndTime(cartProductDTO.getCampaignOnEndTime());
            orderDetail.setLimitNum(cartProductDTO.getLimitNum());
            orderDetail.setRCreated(cartProductDTO.getRCreated());

            orderDetail.setRCostPoint(cartProductDTO.getRCostPoint());
            orderDetail.setRPrePoint(cartProductDTO.getRPrePoint());
            orderDetail.setREveryoneLimit(cartProductDTO.getREveryoneLimit());
            orderDetail.setRPurchaseLimit(cartProductDTO.getRPurchaseLimit());

            if (cartProductDTO.getNumber() < 1) {
                throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "参数错误");
            }
            orderDetail.setNumber(cartProductDTO.getNumber());
            orderDetail.setRealPoint(cartProductDTO.getCostPoint() * cartProductDTO.getNumber());
            if (Objects.nonNull(cartProductDTO.getCostPrice())) {
                orderDetail.setRealAmount(cartProductDTO.getCostPrice().multiply(BigDecimal.valueOf(cartProductDTO.getNumber())));
            } else {
                orderDetail.setRealAmount(BigDecimal.ZERO);
            }
            orderDetail.setStatus(OrderCodeConstants.AbstractOrderDetailStatus.NORMAL);// 商品状态:1待兑换 2已兑换
            orderDetail.setSendStatus(OrderCodeConstants.AbstractOrderExpressStatus.WAITING);// 商品发货状态：0未发货，1已发货"
            orderDetail.setCreated(now);//创建时间

            orderDetail.setTenantId(user.getTenantId());//租户id
            orderDetail.setModified(now);//修改时间
            allDetailList.add(orderDetail);
            productPoint += orderDetail.getRealPoint();
            productAmount = productAmount.add(orderDetail.getRealAmount());
            number += orderDetail.getNumber();
        }
        order.setProductPoint(productPoint);
        order.setProductAmount(productAmount);
        order.setShelfId(shelfId);
        order.setNumber(number);
        return allDetailList;
    }
}
