package com.dz.ms.basic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.constants.CacheKeys;
import com.dz.ms.basic.dto.NavigationConfigDTO;
import com.dz.ms.basic.dto.StyleConfigDTO;
import com.dz.ms.basic.entity.DefaultData;
import com.dz.ms.basic.entity.NavigationConfig;
import com.dz.ms.basic.entity.StyleConfig;
import com.dz.ms.basic.mapper.DefaultDataMapper;
import com.dz.ms.basic.mapper.StyleConfigMapper;
import com.dz.ms.basic.mapper.UiConfigMapper;
import com.dz.ms.basic.service.NavigationConfigService;
import com.dz.ms.basic.service.StyleConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 小程序UI自定义配置
 * @author: Handy
 * @date:   2022/11/21 14:54
 */
@Service
public class StyleConfigServiceImpl extends ServiceImpl<StyleConfigMapper, StyleConfig> implements StyleConfigService {


    @Resource
    private StyleConfigMapper styleConfigMapper;

    @Override
    public StyleConfigDTO getStyleConfigList(Long tenantId) {
        StyleConfig styleConfigList = styleConfigMapper.selectStyleConfigList(tenantId);
        StyleConfigDTO  list = BeanCopierUtils.convertObject(styleConfigList, StyleConfigDTO.class);
        return list;
    }

    @Override
    public StyleConfigDTO getStyleConfigById(Long id) {
        StyleConfig styleConfig = styleConfigMapper.selectById(id);
        if (styleConfig == null) {
            throw new BusinessException(ErrorCode.INTERNAL_ERROR, "未查询到该样式配置");
        }
        StyleConfigDTO styleConfigDTO = BeanCopierUtils.convertObject(styleConfig, StyleConfigDTO.class);
        return styleConfigDTO;
    }

    @Override
    public Long saveStyleConfig(StyleConfigDTO param) {
        StyleConfig styleConfig = BeanCopierUtils.convertObject(param, StyleConfig.class);
        if (ParamUtils.isNullOr0Long(styleConfig.getId())) {
            styleConfig.setCreator(SecurityContext.getUser().getUid());
            styleConfig.setCreateTime(new Date(System.currentTimeMillis()));
            styleConfigMapper.insert(styleConfig);
        } else {
            styleConfig.setModifier(SecurityContext.getUser().getUid());
            styleConfig.setModified(new Date(System.currentTimeMillis()));
            styleConfigMapper.updateById(styleConfig);
        }
        return styleConfig.getId();
    }
}