package com.dz.common.core.enums;

import java.util.Objects;

/**
 * 核销类型枚举
 * <AUTHOR>
 * @date 2023-10-02 10:51
 */
public enum VerifyRecordsTypeEnum {
    SERVE(0, "服务预约"),
    GRADE_SCORE(1, "积分兑礼"),
    PRODUCT(2, "抽奖商品"),
    COUPON(3, "非预约卡券")
    ;
    private final Integer code;
    private final String value;

    VerifyRecordsTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (VerifyRecordsTypeEnum resultEnum : VerifyRecordsTypeEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum.value;
            }
        }
        return null;
    }
}
