package com.dz.ms.adaptor.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.ms.adaptor.config.RedisLock;
import com.dz.ms.adaptor.dto.ScheduledTaskConfigLogDTO;
import com.dz.ms.adaptor.entity.ScheduledTaskConfig;
import com.dz.ms.adaptor.mapper.ScheduledTaskConfigMapper;
import com.dz.ms.adaptor.service.ScheduledTaskConfigLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.support.CronExpression;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

@Slf4j
//@Service
public class ScheduledTaskService {

    @Autowired
    private RedisLock redisLock;
    @Autowired
    private ScheduledTaskConfigMapper scheduledTaskConfigMapper;
    @Autowired
    @Qualifier("ScheduledTask")
    private ThreadPoolTaskExecutor ScheduledTask;
    @Resource
    private ApplicationContext applicationContext;
    private static final String LOCK_PREFIX = "task_lock:"; // Redis 锁前缀
    @Resource
    private ScheduledTaskConfigLogService scheduledTaskConfigLogService;

    // 执行定时任务
//    @Scheduled(cron = "0 * * * * ?")
    public void executeScheduledTasks() {
        SecurityContext.setUser(new CurrentUserDTO(1L));
        LambdaQueryWrapper<ScheduledTaskConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ScheduledTaskConfig::getStatus, 1);
        List<ScheduledTaskConfig> configs = scheduledTaskConfigMapper.selectList(wrapper);
        if (configs == null || configs.isEmpty()) {
            log.info("没有定时任务需要执行");
            return;
        }
        // 循环
        for (ScheduledTaskConfig config : configs) {
            if (config.getNextExecutionTime() == null) {
                config.setNextExecutionTime(calculateNextExecutionTime(config.getCronExpression()));
                scheduledTaskConfigMapper.updateById(config);
            } else if (isTimeToExecute(config.getNextExecutionTime())) {
                // 尝试获取任务锁
                if (tryLock(config.getTaskName() + config.getId())) {
                    // 执行任务
                    ScheduledTask.execute(() -> {
                        try {
                            executeTask(config);
                        } finally {
                            // 释放任务锁
                            unlock(config.getTaskName() + config.getId());
                        }
                    });
                }
            }
        }
    }

    // 获取任务锁
    private boolean tryLock(String taskName) {
        Boolean lockAcquired = redisLock.tryGetDistributedLock(LOCK_PREFIX + taskName, "100", RedisLock.EXPIRE_TIME);
        return lockAcquired != null && lockAcquired;
    }

    // 释放任务锁
    private void unlock(String taskName) {
        redisLock.releaseDistributedLock(LOCK_PREFIX + taskName, "100");
    }

    // 执行具体的任务逻辑
    private void executeTask(ScheduledTaskConfig task) {
        SecurityContext.setUser(new CurrentUserDTO(1L));
        Date date = new Date();
        ScheduledTaskConfigLogDTO configLogDTO = ScheduledTaskConfigLogDTO.builder()
                .taskConfigId(task.getId()).taskName(task.getTaskName()).cronExpression(task.getCronExpression())
                .param(task.getParam()).createdAt(date).updatedAt(date).tenantId(1L)
                .build();
        Long configLogId = scheduledTaskConfigLogService.saveScheduledTaskConfigLog(configLogDTO);
        configLogDTO.setId(configLogId);
        // 执行任务的逻辑
        log.info("任务名称: " + task.getTaskName() + " 参数 params: " + task.getParam());
        Date startTime = new Date();
        int status;
        try {
            BasicProcessor bean = (BasicProcessor) applicationContext.getBean(task.getClassName());
            TaskContext taskContext = new TaskContext();
            taskContext.setJobParams(task.getParam());
            bean.process(taskContext);
            status = 1;
        } catch (Exception e) {
            log.error("任务执行失败", e);
            status = 2;
        }
        Date endTime = new Date();
        // 更新任务的执行时间
        task.setLastRunTime(new Date());
        task.setNextExecutionTime(calculateNextExecutionTime(task.getCronExpression()));
        scheduledTaskConfigMapper.updateById(task);
        configLogDTO.setRunTime(endTime.getTime() - startTime.getTime());
        configLogDTO.setStatus(status);
        configLogDTO.setUpdatedAt(endTime);
        scheduledTaskConfigLogService.saveScheduledTaskConfigLog(configLogDTO);
    }

    // 更新下一次执行时间
    public void updateNextExecutionTime(ScheduledTaskConfig task) {
        task.setNextExecutionTime(calculateNextExecutionTime(task.getCronExpression()));
        scheduledTaskConfigMapper.updateById(task);
    }

    // 判断cron表达式是否到达可执行时间
    public static boolean isTimeToExecute(Date cronExpression) {
        return cronExpression != null && cronExpression.before(new Date());
    }

    // 判断cron表达式是否到达可执行时间
    public static boolean isTimeToExecute(String cronExpression) {
        CronExpression cron = CronExpression.parse(cronExpression);
        LocalDateTime nextExecutionTime = cron.next(LocalDateTime.now());
        return nextExecutionTime != null && nextExecutionTime.isBefore(LocalDateTime.now());
    }

    // 计算cron表达式的下一次执行时间
    public static Date calculateNextExecutionTime(String cronExpression) {
        CronExpression cron = CronExpression.parse(cronExpression);
        LocalDateTime nextExecutionTime = cron.next(LocalDateTime.now());
        return nextExecutionTime != null ? Date.from(nextExecutionTime.atZone(ZoneId.systemDefault()).toInstant()) : null;
    }
}
