package com.dz.ms.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 账号密码/验证码登录入参DTO
 * @author: Handy
 * @date:   2022/1/30 23:04
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "账号密码/验证码登录入参")
public class AccountLoginDTO {

    @ApiModelProperty(value = "账号")
    private String username;
    @ApiModelProperty(value = "密码")
    private String password;
    @ApiModelProperty(value = "宁盾动态口令")
    private String dkeyAmPassword;
    @ApiModelProperty(value = "图形验证码")
    private String imageCode;
    @ApiModelProperty(value = "图形验证码/短信验证码key")
    private String codeKey;
    @ApiModelProperty(value = "短信验证码")
    private String smsCode;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "平台类型 1会小 2企微 3商城")
    private Integer platform;

}
