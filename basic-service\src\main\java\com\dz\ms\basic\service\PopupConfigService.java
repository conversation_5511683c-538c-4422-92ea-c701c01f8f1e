package com.dz.ms.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.basic.PopupConfigDTO;
import com.dz.ms.basic.entity.PopupConfig;

/**
 * 弹窗配置接口
 * @author: Handy
 * @date:   2023/11/20 20:40
 */
public interface PopupConfigService extends IService<PopupConfig> {

	/**
     * 分页查询弹窗配置
     * @param param
     * @return PageInfo<PopupConfigDTO>
     */
    public PageInfo<PopupConfigDTO> getPopupConfigList(PopupConfigDTO param);

    /**
     * 根据ID查询弹窗配置
     * @param id
     * @return PopupConfigDTO
     */
    public PopupConfigDTO getPopupConfigById(Long id);

    /**
     * 保存弹窗配置
     * @param param
     * @return Long
     */
    public Long savePopupConfig(PopupConfigDTO param);

    /**
     * 根据ID删除弹窗配置
     * @param param
     */
    public void deletePopupConfigById(IdCodeDTO param);

    /**
     * 根据类型查询弹窗配置
     *
     * @param type
     * @param tenantId
     * @return
     */
    PopupConfigDTO getPopupConfigByType(Integer type, Long tenantId);

    /**
     * 根据类型更新弹窗配置
     *
     * @param param
     * @param tenantId
     */
    void savePopupConfigByType(PopupConfigDTO param, Long tenantId);

}
