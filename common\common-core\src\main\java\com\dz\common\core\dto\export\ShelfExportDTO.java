package com.dz.common.core.dto.export;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ShelfExportDTO {

    @ApiModelProperty(value = "货架ID")
    private Long id;
    @ApiModelProperty(value = "货架名称")
    private String name;
    @ApiModelProperty(value = "货架上架时间")
    private String onShelfTimeStr;
    @ApiModelProperty(value = "货架状态 1未开始/2上架中/3已结束")
    private Integer shelfState;
    @ApiModelProperty(value = "优先级 值越大显示越优先")
    private Integer priority;
    @ApiModelProperty(value = "货架营销活动列表")
    private String shelfActivityListStr;
    @ApiModelProperty(value = "上架商品数量")
    private Integer productSum;
    @ApiModelProperty(value = "兑礼人数")
    private Integer exchangePeople;
    @ApiModelProperty(value = "兑礼订单数")
    private Integer exchangeOrder;
    @ApiModelProperty(value = "总兑礼件数")
    private Integer exchangeNum;
    @ApiModelProperty(value = "总兑礼积分")
    private Integer exchangePoint;
    @ApiModelProperty(value = "人均兑礼件数")
    private Integer exchangeAvgNum;
    @ApiModelProperty(value = "货架状态 0禁用 1启用")
    private Integer state;
}
