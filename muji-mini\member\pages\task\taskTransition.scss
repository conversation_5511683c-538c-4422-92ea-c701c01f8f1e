.task-my-page {
  --treeDuration: 0.8s;

  &.on-load {
    --treeDuration: 0s;
  }
}

// 状态3
.page-current-lottery.page-current-lottery.page-current-lottery {

  .task-content {
    transform: none;
  }

  .popup-title-desc .link,
  .popup-title-desc .link:before {
    pointer-events: none;
  }

  .lottery-outer-text {
    transform: translateX(-120rpx);
  }

  .lottery-outer-logo {
    transform: scale(1.2);
  }

  .lottery-tree-wrapper {
    // transition: transform var(--treeDuration);
    transform: scale(2.6);

    .lottery-tree {
      pointer-events: auto;
    }
  }

  .lottery-draw-text {
    opacity: 0;
    transition: opacity var(--treeDuration) calc(-1 * var(--treeDuration) / 2);
  }

  .lottery-footer {
    transition: transform calc(3 * var(--treeDuration) / 4) calc(var(--treeDuration) / 4);
    transform: translateY(-320rpx)
  }

  .popup-window {
    display: none;
  }

}

// 状态4
.page-current-task.page-current-task.page-current-task {

  .popup-title-desc .link,
  .popup-title-desc .link:before {
    pointer-events: none;
  }

  .lottery-header-more {
    pointer-events: all;
    opacity: 1;
  }

  .popup-window {
    display: none;
  }
}

.task-wrapper {
  overflow: hidden;
  height: 100vh;
  // transition: opacity 0.2s;

  .task-content {
    transform: translateY(calc(-100vh + 640rpx));
    transform-origin: 50% 100%;
    transition: transform var(--treeDuration);
    will-change: transform;
  }

  .lottery-tree {
    pointer-events: auto;
  }

  .section-lottery-outer {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    z-index: 10;

    .lottery-outer-text {
      position: absolute;
      left: 50rpx;
      top: 194rpx;
      z-index: 100;
      font-size: 60rpx;

      width: 60rpx;
      font-weight: 900;
      color: #79181E;
      line-height: 67rpx;
      text-align: center;
      font-style: normal;

      pointer-events: none;
      text-transform: none;
      transition: transform var(--treeDuration);
      will-change: transform;
    }

    .lottery-outer-logo {
      position: absolute;
      top: 204rpx;
      right: 50rpx;
      width: 93rpx;
      height: 60rpx;

      pointer-events: none;
      transform-origin: 100% top;
      transition: transform var(--treeDuration);
      will-change: transform;
    }
  }
}

.lottery-header-more {
  margin-left: 20rpx;
  pointer-events: none;
  opacity: 0;

  width: 100%;
  transition: opacity var(--treeDuration);
  will-change: opacity;

  .lottery-header-more-img {
    width: 178rpx;
    height: 60rpx;
  }
}

.section-lottery {
  // pointer-events: none;
  box-sizing: content-box;
  overflow: hidden;
  position: relative;

  height: 100vh;
  background-color: #EEEAE1;
  border-bottom: #94243A 6rpx solid;

  transform-origin: 50% 100%;
  transition: height var(--treeDuration);
  will-change: height;

  .lottery-popup {}

  .lottery-draw-text {
    position: absolute;
    z-index: 100;
    bottom: 0;
    right: 32rpx;
    padding: 18rpx;

    // pointer-events: none;
    transition: opacity var(--treeDuration) calc(var(--treeDuration) / 2);
    will-change: opacity;

    .lottery-draw-text-img {
      display: block;
      width: 125rpx;
      height: 36rpx;
    }
  }

  .lottery-tree-wrapper {
    position: absolute;
    right: 96rpx;
    bottom: -34rpx;
    z-index: 10;
    width: 381rpx;
    height: 421rpx;

    // pointer-events: none;
    transition: transform var(--treeDuration);
    transform-origin: 72% 140%;
    will-change: transform;

    pointer-events: none;

    .lottery-tree {
      display: block;
      width: 100%;
      height: 100%;
    }

    .lottery-tree-box {
      position: absolute;
      z-index: 10;

      will-change: transform;

      &.box11 {
        top: 38rpx;
        left: 114rpx;
        width: 63rpx;
        height: 97rpx;
      }

      &.box12 {
        top: 76rpx;
        left: 278rpx;
        width: 63rpx;
        height: 97rpx;
      }

      &.box2 {
        top: 174rpx;
        left: 70rpx;
        width: 56rpx;
        height: 85rpx;
      }
    }
  }

  .lottery-footer {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 80rpx;
    z-index: 10;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    margin-bottom: -320rpx;

    transform-origin: 50% 100%;
    transition: transform calc(var(--treeDuration) / 2);
    will-change: transform;

    .lottery-title {
      display: flex;
      align-items: center;
      justify-content: center;

      width: 270rpx;
      height: 60rpx;
      background: #94243A;
      border-radius: 5rpx;

      margin-bottom: 30rpx;
      font-weight: 500;
      font-size: 24rpx;
      color: #fff;
      line-height: 43rpx;
      text-align: left;
      font-style: normal;
      text-transform: none;

      opacity: 1;
      will-change: opacity;
      transition: opacity 0.2s;

      &.hide {
        opacity: 0;
      }

      &.disabled {
        background-color: #D8D8D8;
        color: #231815;

        .image-hand {
          animation: none;
        }
      }

      .image-hand {
        width: 32rpx;
        height: 32rpx;
        margin-right: 10rpx;

        animation: animHand 1.4s infinite ease;
      }
    }

    .lottery-desc {
      margin-bottom: 60rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #231815;
      line-height: 35rpx;
      text-align: left;
      font-style: normal;
      text-transform: none;

      opacity: 1;
      transition: opacity 0.2s;
      will-change: opacity;

      &.hide {
        opacity: 0;
      }

      .lottery-desc-num {
        font-weight: 900;
        font-feature-settings: "tnum";
        font-variant-numeric: tabular-nums;
      }
    }

    .lottery-button {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      //background: #231815;
      border-radius: 5rpx 5rpx 5rpx 5rpx;

      font-weight: 500;
      font-size: 24rpx;
      line-height: 35rpx;
      color: #231815;
      font-style: normal;
      text-transform: none;

      text-decoration: underline;
      text-underline-offset: 3rpx;

      &::before {
        content: '';
        position: absolute;
        left: -20rpx;
        top: -20rpx;
        right: -20rpx;
        bottom: -20rpx;
        z-index: 1;
        //background-color: #48C871;
      }
    }
  }
}

@keyframes animWindow {
  0% {
    opacity: 0;
    transform-origin: 44% 35%;
  }

  10% {
    opacity: 1;
    transform: none;
    transform-origin: 44% 35%;
  }

  20% {
    opacity: 1;
    transform: none;
    transform-origin: 44% 35%;
  }

  100% {
    opacity: 1;
    transform: scale(12);
    transform-origin: 44% 35%;
  }
}

@keyframes animButterfly {
  0% {
    opacity: 0;
  }

  1% {
    opacity: 1;
  }

  80% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

@keyframes animHand {
  0% {
    transform: none;
  }

  50% {
    transform: scale(0.8);
  }

  100% {
    transform: none;
  }
}
