package com.dz.ms.user.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.utils.MD5;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.constants.CommonConstants;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.IdNumberDTO;
import com.dz.common.core.dto.user.SysPermissionDTO;
import com.dz.common.core.dto.user.SysUserDTO;
import com.dz.common.core.dto.user.SysUserSimpleDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.user.SysUserFeginClient;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.DesensitizationUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.user.constants.CacheKeys;
import com.dz.ms.user.dto.SysMenuDTO;
import com.dz.ms.user.dto.SysUserRoleDTO;
import com.dz.ms.user.dto.UpdatePasswordDTO;
import com.dz.ms.user.dto.UserRoleDTO;
import com.dz.ms.user.entity.SysUser;
import com.dz.ms.user.entity.UserInfo;
import com.dz.ms.user.service.ISysMenuService;
import com.dz.ms.user.service.SysRoleService;
import com.dz.ms.user.service.SysUserService;
import com.dz.ms.user.utils.BCryptPasswordEncoder;
import com.dz.ms.user.vo.MenuInfoVo;
import com.dz.ms.user.vo.RouterVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Api(tags="系统用户信息")
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
@RestController
public class SysUserController implements SysUserFeginClient {

    @Resource
    private SysUserService sysUserService;
    @Resource
    private SysRoleService sysRoleService;
    @Resource
    private RedisService redisService;
    @Resource
    private ISysMenuService menuService;

    /**
     * 分页查询系统用户信息
     * @param param
     * @return result<PageInfo<SysUserDTO>>
     */
    @ApiOperation("分页查询系统用户信息")
	@GetMapping(value = "/crm/sys_user/list")
    public Result<PageInfo<SysUserDTO>> getSysUserList(@ModelAttribute SysUserDTO param) {
        Result<PageInfo<SysUserDTO>> result = new Result<>();
        SysUser sysUser = BeanCopierUtils.convertObjectTrim(param,SysUser.class);
        sysUser.setRealname(null);
        sysUser.setTenantId(SecurityContext.getUser().getTenantId());
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>(sysUser);
        if(StringUtils.isNotEmpty(param.getUsername())) {
            wrapper.like(SysUser::getUsername,param.getUsername());
        }
        wrapper.ne(SysUser :: getIsAdmin,"1");
        wrapper.orderByDesc(SysUser::getId);
        IPage<SysUser> page = sysUserService.page(new Page<>(param.getPageNum(), param.getPageSize()),wrapper);
        List<SysUserDTO> list = new ArrayList<>();
        if(!CollectionUtils.isEmpty(page.getRecords())) {
            List<Long> creatorIds = new ArrayList<>();
            List<Long> ids = new ArrayList<>();
            page.getRecords().forEach(user -> {
                creatorIds.add(user.getCreator());
                ids.add(user.getId());
            });
            List<SysUser> creatorUsers = sysUserService.listByIds(creatorIds);
            Map<Long,SysUser> creatorMap = creatorUsers.stream().collect(Collectors.toMap(SysUser :: getId,su -> su));
            List<SysUserRoleDTO> roles = sysRoleService.getSysRoleByUserIds(ids);
            Map<Long,SysUserRoleDTO> roleMap = null;
            if(!CollectionUtils.isEmpty(roles)) {
                roleMap = roles.stream().collect(Collectors.toMap(SysUserRoleDTO :: getUid,sr -> sr));
            }
            else {
                roleMap = new HashMap<>();
            }
            Map<Long, SysUserRoleDTO> finalRoleMap = roleMap;
            page.getRecords().forEach(user -> {
                SysUserDTO sysUserDTO = BeanCopierUtils.convertObject(user,SysUserDTO.class);
                sysUserDTO.setRoleId(finalRoleMap.containsKey(user.getId()) ? finalRoleMap.get(user.getId()).getRoleId() : null);
                sysUserDTO.setRoleName(finalRoleMap.containsKey(user.getId()) ? finalRoleMap.get(user.getId()).getRoleName() : "");
                sysUserDTO.setCreatorName(creatorMap.containsKey(user.getCreator()) ? creatorMap.get(user.getCreator()).getRealname() : "");
                sysUserDTO.setMobile(DesensitizationUtils.mobileSecret(user.getMobile()));
                list.add(sysUserDTO);
            });
        }
        result.setData(new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),list));
        return result;
    }

    /**
     * 根据ID查询系统用户信息
     * @param id
     * @return result<SysUserDTO>
     */
    @ApiOperation("根据ID查询系统用户信息")
	@GetMapping(value = "/crm/sys_user/info")
    public Result<SysUserDTO> getSysUserById(@RequestParam("id") Long id) {
        Result<SysUserDTO> result = new Result<>();
        Long tenantId = SecurityContext.getUser().getTenantId();
        SysUserDTO sysUser = sysUserService.getUserContainRootIdByUid(id,tenantId);
        result.setData(sysUser);
        return result;
    }

    /**
     * 根据UID查询系统用户信息
     * @return
     */
    /*private SysUserDTO getUserByUid(Long uid,Long tenantId) {
        SysUserDTO sysUserDTO = (SysUserDTO) redisService.get(CacheKeys.SYS_USER_INFO + tenantId +":"+ uid);
        if(null == sysUserDTO) {
            SysUser sysUser = sysUserService.getById(uid);
            sysUserDTO = BeanCopierUtils.convertObject(sysUser,SysUserDTO.class);
            redisService.set(CacheKeys.SYS_USER_INFO + tenantId +":"+ uid,sysUserDTO, CommonConstants.DAY_SECONDS);
        }
        return sysUserDTO;
    }*/

    /**
     * 根据UID查询系统用户信息
     * @return
     */
    @GetMapping(value = "/sys_user/info")
    public Result<SysUserDTO> getSysUserByUid(@RequestParam("uid")Long uid, @RequestParam("tenantId")Long tenantId) {
        return new Result().data(sysUserService.getUserByUid(uid,tenantId));
    }

    /**
     * 新增系统用户信息
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增系统用户",type = LogType.OPERATELOG)
    @ApiOperation("新增系统用户信息")
    @PostMapping(value = "/crm/sys_user/add")
    public Result<Long> addSysUser(@RequestBody SysUserDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,true);
        Long id = sysUserService.saveSysUser(param);
        result.setData(id);
        return result;
    }

    /**
     * 更新系统用户信息
     * @param param
     * @return result<Object>
     */
    @SysLog(value = "更新系统用户信息",type = LogType.OPERATELOG)
    @ApiOperation("更新系统用户信息")
    @PostMapping(value = "/crm/sys_user/update")
    public Result<Long> updateSysUser(@RequestBody SysUserDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,false);
        sysUserService.saveSysUser(param);
        result.setData(param.getId());
        return result;
    }

    /**
     * 验证保存参数
     * @param param
     */
    private void validationSaveParam(SysUserDTO param,boolean isAdd) {
        if(isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if(!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
        if(StringUtils.isBlank(param.getUsername())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "账号不能为空");
        }
        if(param.getUsername().length() < 4) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "账号太短");
        }
        if(StringUtils.isBlank(param.getMobile())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "手机号不能为空");
        }
        param.setIsAdmin(0);
        param.setTenantId(SecurityContext.getUser().getTenantId());
    }

    @SysLog(value = "创建超级管理员",type = LogType.OPERATELOG)
    @ApiOperation("创建超级管理员")
    @PostMapping(value = "/sys_user/create_super_admin")
    public Result<Long> createSuperAdmin(@RequestBody SysUserDTO param) {
        Result<Long> result = new Result<>();
        if(StringUtils.isBlank(param.getUsername())) {
            return result.paramErroResult("账号不能为空");
        }
        if(StringUtils.isBlank(param.getMobile())) {
            return result.paramErroResult("手机号不能为空");
        }
        if(ParamUtils.isNullOr0Long(param.getTenantId())) {
            return result.paramErroResult("租户不能为空");
        }
        param.setIsAdmin(1);
        Long id = sysUserService.saveSysUser(param);
        result.setData(id);
        return result;
    }
	
	/**
     * 根据ID删除系统用户信息
     * @param param
     * @return result<Boolean>
     */
    @SysLog(value = "根据ID删除系统用户信息",type = LogType.OPERATELOG)
    @ApiOperation("根据ID删除系统用户信息")
	@PostMapping(value = "/crm/sys_user/delete")
    public Result<Boolean> deleteSysUserById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        sysUserService.deleteSysUserById(param.getId());
        result.setData(true);
        return result;
    }

    @ApiOperation("获取用户角色")
    @GetMapping(value = "/crm/sys_user/role")
    public Result<Long> getUserRole(@RequestParam("uid") Long uid) {
        Result<Long> result = new Result<>();
        Long roleId = sysUserService.getUserRole(uid,SecurityContext.getUser().getTenantId());
        result.setData(roleId);
        return result;
    }

    @SysLog(value = "绑定用户角色",type = LogType.OPERATELOG)
    @ApiOperation("绑定用户角色")
    @PostMapping(value = "/crm/sys_user/bind_role")
    public Result<Boolean> bindRole(@RequestBody UserRoleDTO param) {
        Result<Boolean> result = new Result<>();
        sysUserService.bindRole(param);
        result.setData(true);
        return result;
    }

    @ApiOperation("获取当前用户权限列表")
    @GetMapping(value = "/crm/sys_user/identity")
    public Result<SysUserDTO> getCurrentSysUser() {
        Result<SysUserDTO> result = new Result<>();
        Long tenantId = SecurityContext.getUser().getTenantId();
        Long uid = SecurityContext.getUser().getUid();
        SysUserDTO sysUser = sysUserService.getUserByUid(uid,tenantId);
        Long roleId = 0L;
        if(ParamUtils.Integer2int(sysUser.getIsAdmin()) != 1) {
            roleId = sysUserService.getUserRole(uid, tenantId);
        }
        List<String> functionCodes = sysRoleService.getRoleFunctionCodes(roleId,tenantId,null);
        List<SysPermissionDTO> menuList = sysRoleService.getRoleMenuTree(roleId,tenantId,null);
        sysUser.setRoleId(roleId);
        sysUser.setFunctionCodes(functionCodes);
        sysUser.setMenuList(menuList);
        result.setData(sysUser);
        return result;
    }

    /**
     * 根据用户ID列表获取系统用户信息
     * @param ids
     * @param tenantId
     * @return
     */
    @PostMapping(value = "/sys_user/list_byids")
    public Result<List<SysUserSimpleDTO>> getSysUserListByIds(@RequestBody List<Long> ids, @RequestParam("tenantId") Long tenantId) {
        Result<List<SysUserSimpleDTO>> result = new Result<>();
        List<SysUser> list = sysUserService.getByIds(ids,tenantId);
        result.setData(BeanCopierUtils.convertList(list,SysUserSimpleDTO.class));
        return result;
    }

    @SysLog(value = "重置密码",type = LogType.OPERATELOG)
    @ApiOperation("重置密码")
    @GetMapping(value = "/crm/sys_user/password_reset")
    public Result<Boolean> passwordReset(@RequestParam("id") Long uid) {
        Result<Boolean> result = new Result<>();
        sysUserService.passwordReset(uid);
        result.setData(true);
        return result;
    }

    @SysLog(value = "修改密码",type = LogType.OPERATELOG)
    @ApiOperation("CRM登录用户修改密码")
    @PostMapping(value = "/crm/password_update")
    public Result<Boolean> passwordUpdateCrm(@RequestBody UpdatePasswordDTO param) {
        Result<Boolean> result = new Result<>();
        if (StringUtils.isBlank(param.getOldPassword())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "原密码不能为空");
        }
        if (StringUtils.isBlank(param.getNewPassword())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新密码不能为空");
        }
        sysUserService.passwordUpdate(param);
        result.setData(true);
        return result;
    }

    @SysLog(value = "修改系统用户状态",type = LogType.OPERATELOG)
    @ApiOperation("修改系统用户状态")
    @PostMapping(value = "/crm/state_update")
    public Result<Boolean> updateSysUserState(@RequestBody IdNumberDTO param) {
        Result<Boolean> result = new Result<>();
        if (null == param.getId() || null == param.getNumber()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "参数不能为空");
        }
        SysUser sysUser = new SysUser();
        sysUser.setId(param.getId());
        sysUser.setState(param.getNumber());
        sysUser.setTenantId(SecurityContext.getUser().getTenantId());
        sysUserService.updateById(sysUser);
        result.setData(true);
        return result;
    }

    @ApiOperation("用户注销")
    @GetMapping(value = "/crm/logout")
    public Result<Boolean> logout() {
        Result<Boolean> result = new Result<>();
        sysUserService.logout();
        result.setData(true);
        return result;
    }

    @ApiOperation("菜单列表(舍弃不用)")
    @GetMapping(value = "/crm/sys_user/getRouters")
    public Result<List<RouterVo>> getRouters() {
        Result<List<RouterVo>> result = new Result<>();
        List<SysMenuDTO> menus = menuService.selectMenuTreeByUserId();
        result.setData(menuService.buildMenus(menus));
        return result;
    }

    @GetMapping(value = "/crm/sys_user/getInfoByMenuName")
    @ApiOperation("根据菜单名称获取菜单及祖级信息(舍弃不用)")
    public Result<List<MenuInfoVo>> getInfoByMenuName(@RequestParam(value = "menuName") String menuName,
                                                      @RequestParam(value = "menuType") String menuType) {
        Result<List<MenuInfoVo>> result = new Result<>();
        List<MenuInfoVo> menuInfos = menuService.getInfoByMenuName(menuName,menuType);
        result.setData(menuInfos);
        return result;
    }

    @GetMapping(value = "/sys_user/getUserByIds")
    public Result<List<SysUserDTO>> getUserByIds(@RequestParam(value = "ids") List<Long> ids) {
        Result<List<SysUserDTO>> result = new Result<>();
        List<SysUser> creatorUsers = sysUserService.listByIds(ids);
        result.setData(BeanCopierUtils.convertList(creatorUsers,SysUserDTO.class));
        return result;
    }

    public static void main(String[] args) {
        String salt = UUID.randomUUID().toString().trim().replaceAll("-","");
        String password = "muji6688";
        password = MD5.encode(password+salt);
        String encode = new BCryptPasswordEncoder().encode(password);
    }


}
