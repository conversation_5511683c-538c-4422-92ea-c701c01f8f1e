package com.dz.ms.product.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.product.dto.SuperscriptDTO;
import com.dz.ms.product.service.SuperscriptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "商品角标管理")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class SuperscriptController {

    @Resource
    private SuperscriptService superscriptService;

    /**
     * 分页查询商品角标管理
     *
     * @param param
     * @return result<PageInfo < SuperscriptDTO>>
     */
    @ApiOperation("分页查询商品角标管理")
    @GetMapping(value = "/crm/superscript/list")
    public Result<PageInfo<SuperscriptDTO>> getSuperscriptList(@ModelAttribute SuperscriptDTO param) {
        Result<PageInfo<SuperscriptDTO>> result = new Result<>();
        PageInfo<SuperscriptDTO> page = superscriptService.getSuperscriptList(param);
        result.setData(page);
        return result;
    }

    /**
     * 查询商品角标
     *
     * @param param
     * @return result<List < SuperscriptDTO>>
     */
    @ApiOperation("查询商品角标")
    @GetMapping(value = "/crm/superscript/no_page_list")
    public Result<List<SuperscriptDTO>> getSuperscriptNoPageList(@ModelAttribute SuperscriptDTO param) {
        Result<List<SuperscriptDTO>> result = new Result<>();
        List<SuperscriptDTO> page = superscriptService.getSuperscriptNoPageList(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询商品角标管理
     *
     * @param id
     * @return result<SuperscriptDTO>
     */
    @ApiOperation("根据ID查询商品角标管理")
    @GetMapping(value = "/crm/superscript/info")
    public Result<SuperscriptDTO> getSuperscriptById(@RequestParam("id") Long id) {
        Result<SuperscriptDTO> result = new Result<>();
        SuperscriptDTO superscript = superscriptService.getSuperscriptById(id);
        result.setData(superscript);
        return result;
    }

    /**
     * 新增商品角标管理
     *
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增商品角标管理", type = LogType.OPERATELOG)
    @ApiOperation("新增商品角标管理")
    @PostMapping(value = "/crm/superscript/add")
    public Result<Long> addSuperscript(@RequestBody SuperscriptDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param, true);
        Long id = superscriptService.saveSuperscript(param);
        result.setData(id);
        return result;
    }

    /**
     * 更新商品角标管理
     *
     * @param param
     * @return result<Object>
     */
    @SysLog(value = "更新商品角标管理", type = LogType.OPERATELOG)
    @ApiOperation("更新商品角标管理")
    @PostMapping(value = "/crm/superscript/update")
    public Result<Long> updateSuperscript(@RequestBody SuperscriptDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param, false);
        superscriptService.saveSuperscript(param);
        result.setData(param.getId());
        return result;
    }

    /**
     * 验证保存参数
     *
     * @param param
     */
    private void validationSaveParam(SuperscriptDTO param, boolean isAdd) {
        if (isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if (!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
    }

    /**
     * 根据ID删除商品角标管理
     *
     * @param param
     * @return result<Boolean>
     */
    @SysLog(value = "根据ID删除商品角标管理", type = LogType.OPERATELOG)
    @ApiOperation("根据ID删除商品角标管理")
    @PostMapping(value = "/crm/superscript/delete")
    public Result<Boolean> deleteSuperscriptById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        superscriptService.deleteSuperscriptById(param);
        result.setData(true);
        return result;
    }

}
