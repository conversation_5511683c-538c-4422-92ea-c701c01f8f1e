package com.dz.ms.basic.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.core.annotation.CacheEvict;
import com.dz.common.core.annotation.Cacheable;
import com.dz.common.core.dto.basic.TenantConfigDTO;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.ms.basic.constants.CacheKeys;
import com.dz.ms.basic.entity.TenantConfig;
import com.dz.ms.basic.mapper.TenantConfigMapper;
import com.dz.ms.basic.service.TenantConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 租户设置
 * @author: Handy
 * @date:   2022/08/30 23:04
 */
@Service
public class TenantConfigServiceImpl extends ServiceImpl<TenantConfigMapper,TenantConfig> implements TenantConfigService {

    @Resource
    private TenantConfigMapper tenantConfigMapper;

    /**
     * 根据ID查询租户设置
     * @param id
     * @return
     */
    @Override
    @Cacheable(prefix = CacheKeys.TENANT_CONFIG_INFO,key = "'#id'")
    public TenantConfigDTO getTenantConfigById(Long id) {
        TenantConfig tenantConfig = tenantConfigMapper.selectById(id);
        return BeanCopierUtils.convertObject(tenantConfig, TenantConfigDTO.class);
    }

    /**
     * 保存租户设置
     * @param param
     */
    @Override
    @CacheEvict(prefix = CacheKeys.TENANT_CONFIG_INFO,key = "'#param.id'")
    public void saveTenantConfig(TenantConfigDTO param) {
        TenantConfig tenantConfig = new TenantConfig(param.getId(), param.getUserCrm());
        TenantConfig getConfig = tenantConfigMapper.selectById(param.getId());
        if(null == getConfig) {
            tenantConfigMapper.insert(tenantConfig);
        }
        else {
            tenantConfigMapper.updateById(tenantConfig);
        }

    }

}