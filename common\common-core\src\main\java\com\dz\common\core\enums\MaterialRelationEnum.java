package com.dz.common.core.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 素材关联业务字段类型
 * @author: Handy
 * @date:   2023/05/09 16:36
 */
public enum MaterialRelationEnum {

	STORE_VR("门店管理","VR预览图"),
	STORE_HEADER("门店管理","门店头图"),
	MEMBER_BENEFIT("会员管理","权益ICON"),
	MEMBER_CARD("会员管理","会员卡样式"),
	BRAND_MP_QRCODE("品牌管理","公众号二维码"),
	BRAND_HOME_QRCODE("品牌管理","品牌首页二维码"),
	BRAND_LOGO("品牌管理","品牌logo"),
	SERVICE_MAIN("服务项目","服务项目图"),
	MINIAPP_TEMPLATE("自定义模板","自定义模板"),
	TAB_BAR("自定义UI","Tabbar");
	/** 关联模块类型 */
	private String moduleName;
	/** 关联字段名称 */
	private String fieldName;

	MaterialRelationEnum() {

	}

	MaterialRelationEnum(String moduleName, String fieldName) {
		this.moduleName = moduleName;
		this.fieldName = fieldName;
	}

	public String getModuleName() {
		return moduleName;
	}

	public String getFieldName() {
		return fieldName;
	}

	private static Map<String,String> materialRelationMap = null;
	public static String getNameByType(String type) {
		if(null == materialRelationMap) {
			materialRelationMap = new HashMap();
			for (MaterialRelationEnum relation : MaterialRelationEnum.values()) {
				materialRelationMap.put(relation.name(),relation.getFieldName());
			}
		}
		return materialRelationMap.get(type);
	}

}
