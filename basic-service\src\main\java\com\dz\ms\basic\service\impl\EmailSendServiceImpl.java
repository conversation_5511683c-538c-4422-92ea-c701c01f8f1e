package com.dz.ms.basic.service.impl;

import com.alibaba.fastjson.JSON;
import com.dz.common.core.dto.basic.EmailSendDTO;
import com.dz.ms.basic.service.EmailSendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;
import java.util.Date;

/**
 * 发送邮件
 * @author: Handy
 * @date:   2021/12/16 15:44
 */
@Service
@Slf4j
public class EmailSendServiceImpl implements EmailSendService {

    @Resource
    private JavaMailSender javaMailSender;

    @Value("${spring.mail.username:}")
    private String from;

    /**
     * 普通邮件发送
     * @param param
     */
    @Override
    public void sendSimpleMail(EmailSendDTO param) {
        // 构建一个邮件对象
        SimpleMailMessage message = new SimpleMailMessage();
        // 设置邮件主题
        message.setSubject(param.getSubject());
        // 设置邮件发送者，这个跟application.yml中设置的要一致
        message.setFrom(from);
        // 设置邮件接收者，可以有多个接收者，中间用逗号隔开，以下类似
        message.setTo(param.getTo());
        // 设置邮件抄送人，可以有多个抄送人
        message.setCc(param.getCc());
        // 设置隐秘抄送人，可以有多个
        //message.setBcc("7******<EMAIL>");
        // 设置邮件发送日期
        message.setSentDate(new Date());
        // 设置邮件的正文
        message.setText(param.getContent());
        // 发送邮件
        javaMailSender.send(message);
    }

    /**
     * 富文本邮件发送
     * @param param
     */
    @Override
    public void sendMimeMail(EmailSendDTO param) {
        try{
            MimeMessage message = javaMailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            helper.setFrom(from);
            helper.setTo(param.getTo());
            if(null == param.getCc()) {
                helper.setCc(new String[]{});
            }
            else {
                helper.setCc(param.getCc());
            }
            helper.setSubject(param.getSubject());
            helper.setText(param.getContent(), true);
            helper.setSentDate(new Date());
            javaMailSender.send(message);
        }catch (Exception e){
            log.error(JSON.toJSONString(param.getTo())+"发送富文本邮件出错",e);
        }
    }

}
