package com.dz.ms.order.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单详情信息
 *
 * @author: LiinNs
 * @date: 2024/12/09 10:38
 */
@Getter
@Setter
@NoArgsConstructor
@Table("订单详情信息")
@TableName(value = "order_detail")
public class OrderDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR, length = 36, isNull = false, comment = "订单编号")
    private String orderCode;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "商品ID")
    private Long productId;
    @Columns(type = ColumnType.VARCHAR, length = 256, isNull = true, comment = "商品名称")
    private String productName;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = true, comment = "商品类型 1实物商品 2电子券")
    private Integer pdType;
    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = true, comment = "外部数据ID 优惠券ID需兑换")
    private String venderId;
    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = true, comment = "优惠券编码")
    private String couponCode;
    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = true, comment = "优惠券stockId")
    private String stockId;
    @Columns(type = ColumnType.VARCHAR, length = 1024, isNull = true, comment = "商品图片")
    private String imgUrl;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, comment = "兑换积分，商品实际兑换时支付的积分单价")
    private Integer costPoint;
    @Columns(type = ColumnType.DECIMAL, length = 19, scale = 2, isNull = true, comment = "1积分+金额时 仍需支付的金额，商品实际兑换时支付的金额单价")
    private BigDecimal costPrice;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, comment = "兑换积分，来自商品表的价格留存")
    private Integer pCostPoint;
    @Columns(type = ColumnType.DECIMAL, length = 19, scale = 2, isNull = true, comment = "1积分+金额时 仍需支付的金额，来自商品表的价格留存")
    private BigDecimal pCostPrice;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "货架商品ID")
    private Long shelfProductId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "货架ID")
    private Long shelfId;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "兑换积分，来自货架上的价格留存")
    private Integer sCostPoint;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "积分划线价，来自货架上的价格留存")
    private Integer sPrePoint;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "营销活动ID")
    private Long campaignId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "营销规则ID")
    private Long ruleId;

    @TableField(exist = false)
    // 活动规则限购时间设置 1活动时间内 2周期
    private Integer ruleType;

    // 周期天数
    @TableField(exist = false)
    private Integer period;

    // 活动规则创建时间
    @TableField(exist = false)
    private Date rCreated;

    // 活动开始时间
    @TableField(exist = false)
    private Date campaignOnStartTime;

    // 活动结束时间
    @TableField(exist = false)
    private Date campaignOnEndTime;

    // 默认限购数量/月(货架商品限购数量)
    @TableField(exist = false)
    private Integer limitNum;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "兑换积分，来自营销规则的价格留存")
    private Integer rCostPoint;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "积分划线价，来自营销规则的价格留存")
    private Integer rPrePoint;

    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "每人限购数")
    private Integer rEveryoneLimit;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "活动限购数量")
    private Integer rPurchaseLimit;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "商品数量")
    private Integer number;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, comment = "实际支付总积分")
    private Integer realPoint;
    @Columns(type = ColumnType.DECIMAL, length = 19, scale = 2, isNull = true, comment = "实际支付总金额")
    private BigDecimal realAmount;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "发货数量")
    private Integer deliveryNumber;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = true, comment = "商品状态:1待兑换 2已兑换 3已过期")
    private Integer status;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = true, comment = "商品发货状态：0未发货，1已发货")
    private Integer sendStatus;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = true, comment = "否删除 0未删除 1已删除")
    @TableLogic
    private Integer isDeleted;

    public OrderDetail(Long id, String orderCode, Long productId, String productName, Integer pdType, String venderId, String imgUrl, Integer costPoint, BigDecimal costPrice, Integer pCostPoint, BigDecimal pCostPrice, Long shelfProductId, Long shelfId, Integer sCostPoint, Integer sPrePoint, Long campaignId, Long ruleId, Integer rCostPoint, Integer rPrePoint, Integer number, Integer realPoint, BigDecimal realAmount, Integer deliveryNumber, Integer status, Integer sendStatus) {
        this.id = id;
        this.orderCode = orderCode;
        this.productId = productId;
        this.productName = productName;
        this.pdType = pdType;
        this.venderId = venderId;
        this.imgUrl = imgUrl;
        this.costPoint = costPoint;
        this.costPrice = costPrice;
        this.pCostPoint = pCostPoint;
        this.pCostPrice = pCostPrice;
        this.shelfProductId = shelfProductId;
        this.shelfId = shelfId;
        this.sCostPoint = sCostPoint;
        this.sPrePoint = sPrePoint;
        this.campaignId = campaignId;
        this.ruleId = ruleId;
        this.rCostPoint = rCostPoint;
        this.rPrePoint = rPrePoint;
        this.number = number;
        this.realPoint = realPoint;
        this.realAmount = realAmount;
        this.deliveryNumber = deliveryNumber;
        this.status = status;
        this.sendStatus = sendStatus;
    }

}
