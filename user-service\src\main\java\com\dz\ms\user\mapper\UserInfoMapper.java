package com.dz.ms.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.user.UserSimpleDTO;
import com.dz.ms.user.entity.UserInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户信息Mapper
 * @author: Handy
 * @date:   2022/2/4 17:33
 */
@Repository
public interface UserInfoMapper extends BaseMapper<UserInfo> {

    /** 根据openid获取用户信息 */
    UserInfo getUserByOpenid(@Param("openid") String openid,@Param("tenantId") Long tenantId);

    /** 根据unionid获取用户信息 */
    UserInfo getNoOpenidUserByUnionid(@Param("unionid") String unionid,@Param("tenantId") Long tenantId);

    /** 根据用户ID列表获取用户信息 */
    List<UserSimpleDTO> getUserListByIds(@Param("ids")List<Long> ids, @Param("tenantId")Long tenantId);



    /** 根据用户ID列表获取用户openid列表 */
    List<String> getUserOpenidByIds(@Param("ids")List<Long> ids, @Param("tenantId")Long tenantId);

    /** 获取所有员工ID账号列表 */
    List<IdCodeDTO> queryUserIdCodes();

    UserSimpleDTO getUserInfoByUnionId(@Param("unionid") String unionid, @Param("tenantId") Long tenantId);

    List<UserSimpleDTO> getUserInfoListByUnionIds(@Param("unionids") List<String> unionids, @Param("tenantId") Long tenantId);

    List<UserSimpleDTO> getUserInfoListByOpenids(@Param("openids") List<String> openids, @Param("tenantId") Long tenantId);
}
