package com.dz.ms.order.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单详情-优惠券信息
 */
@Getter
@Setter
@NoArgsConstructor
@Table("订单详情-优惠券信息")
@TableName(value = "order_detail_coupon")
public class OrderDetailCoupon implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR, length = 36, isNull = false, comment = "订单编号")
    private String orderCode;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "订单详情ID")
    private Long orderDetailId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "商品ID")
    private Long productId;
    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = true, comment = "外部数据ID 优惠券ID需兑换")
    private String venderId;
    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = true, comment = "优惠券编码")
    private String couponCode;
    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = true, comment = "优惠券stockId")
    private String stockId;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = true, comment = "商品状态:1待兑换 2已兑换 3已过期")
    private Integer status;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = true, comment = "否删除 0未删除 1已删除")
    @TableLogic
    private Integer isDeleted;

    public OrderDetailCoupon(Long id, String orderCode, Long orderDetailId, Long productId, String venderId, String couponCode, String stockId, Integer status) {
        this.id = id;
        this.orderCode = orderCode;
        this.orderDetailId = orderDetailId;
        this.productId = productId;
        this.venderId = venderId;
        this.couponCode = couponCode;
        this.stockId = stockId;
        this.status = status;
    }

}
