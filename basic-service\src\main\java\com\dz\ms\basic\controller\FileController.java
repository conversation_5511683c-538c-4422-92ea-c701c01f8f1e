package com.dz.ms.basic.controller;

import com.alibaba.fastjson.JSON;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.internal.OSSHeaders;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.StorageClass;
import com.dz.common.base.constant.ClientTypeConstant;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.Result;
import com.dz.common.core.constants.CommonConstants;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.dto.user.UserSimpleDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.basic.FileFeignClient;
import com.dz.common.core.fegin.user.UserInfoFeginClient;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.DateUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.constants.CacheKeys;
import com.dz.ms.basic.utils.FileUtil;
import com.microsoft.azure.storage.CloudStorageAccount;
import com.microsoft.azure.storage.blob.CloudBlobClient;
import com.microsoft.azure.storage.blob.CloudBlobContainer;
import com.microsoft.azure.storage.blob.CloudBlockBlob;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.model.PutObjectRequest;
import com.qiniu.http.Response;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.Region;
import com.qiniu.storage.UploadManager;
import com.qiniu.storage.model.DefaultPutRet;
import com.qiniu.util.Auth;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;

/**
 * 文件管理
 * @Author: Handy
 * @Date: 2022/2/4 19:14
 */
@RestController
@Api(tags = "文件管理")
@Slf4j
public class FileController implements FileFeignClient {

    @Value("${file.request.domain:}")
    private String requestDomain;
    @Value("${file.upload.path:}")
    private String filePath;
    @Value("${file.upload.cloud:0}")
    private Integer uploadCloud;
    @Value("${file.upload.sign:}")
    private String sign;

    @Value("${file.upload.access_key:}")
    private String accessKey;
    @Value("${file.upload.secret_key:}")
    private String secretKey;
    @Value("${file.upload.bucket:}")
    private String bucket;
    @Value("${file.upload.region:}")
    private String region;
    @Value("${file.upload.endpoint:}")
    private String endpoint;
    @Value("${file.upload.oss_project:}")
    private String ossProject;

    /** 微软云 */
    @Value("${file.upload.account_name:}")
    private String accountName;
    @Value("${file.upload.account_key:}")
    private String accountKey;
    @Value("${file.upload.endpoint_suffix:}")
    private String endpointSuffix;
    @Value("${file.upload.default_endpoints_protocol:}")
    private String defaultEndpointsProtocol;
    @Value("${file.upload.container:}")
    private String containerName;

    @Resource
    private RedisService redisService;
    @Resource
    private UserInfoFeginClient userInfoFeginClient;

    private final Random random = new Random();

    @PostMapping(value = {"/crm/file/upload","/app/file/upload","/oms/file/upload",})
    @ApiOperation("上传文件")
    public Result<String> uploadFile(@RequestParam("file") MultipartFile file) {
        if (null == file) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"请选择要上传的文件!");
        }
        String fileName = file.getOriginalFilename();
        log.info("上传文件名称为：" + fileName);
        if (StringUtils.isBlank(fileName) || fileName.indexOf(".") < 0) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,fileName+"文件格式错误");
        }
        int fileType = FileUtil.checkFileName(fileName);
        if (fileType == 0) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,fileName+"该文件不支持上传");
        }
        long size = file.getSize();
        if (size == 0) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,fileName+"文件为空");
        }
        double sizeMb = (double)size/1048576;
        if (fileType == 1 && sizeMb > 20) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"图片大小不能超过20M");
        }
        if (fileType == 2 && sizeMb > 50) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"视频大小不能超过50M");
        }
        try {
            byte[] b = new byte[10];
            file.getInputStream().read(b,0,b.length);
            String headerHex = FileUtil.bytesToHexString(b);
            log.info("上传文件headerHex:{}",headerHex);
            boolean isValid = false;
            for (String hex : FileUtil.validFileHex) {
                if(headerHex.startsWith(hex)) {
                    isValid = true;
                    break;
                }
            }
            if(!isValid) {
                throw new BusinessException(ErrorCode.BAD_REQUEST,"该文件格式不支持上传!");
            }
        } catch (IOException e) {
            log.error("上传文件解析文件头失败",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST,"解析文件失败!");
        }
        String datePath = new SimpleDateFormat("yyyyMMdd").format(new Date());
        long times = System.currentTimeMillis();
        long tenantId = ParamUtils.Long2long(SecurityContext.getUser().getTenantId());
        String suffix = fileName.substring(fileName.lastIndexOf(".")).toLowerCase();
        String newFileName = "mall/" + tenantId + "/" + datePath + "/" + times+random.nextInt(10000) + suffix;
        if(uploadCloud.equals(1)) {
            return uploadCos(file,newFileName);
        }
        else if(uploadCloud.equals(2)) {
            return uploadToQiniu(file,newFileName);
        }
        else if(uploadCloud.equals(3)) {
            return uploadAzure(file,newFileName);
        }
        else if(uploadCloud.equals(4)) {
            return uploadOss(file,newFileName);
        }
        return uploadToLocal(file,newFileName);
    }

    @PostMapping(value = {"/app/file/campaign/upload"})
    @ApiOperation("campaign上传文件")
    public Result<List<String>> campaignUploadFiles(@RequestParam("files") List<MultipartFile> files) {
        Result<List<String>> result = new Result<>();
        if (ObjectUtils.isEmpty(files)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"请选择要上传的文件!");
        }

        List<String> list = new ArrayList<>();
        for (MultipartFile file : files) {
            String data = campaignUploadFile(file).getData();
            list.add(data);
        }
        result.setData(list);
        return result;
    }

    public Result<String> campaignUploadFile(MultipartFile file) {
        if (null == file) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"请选择要上传的文件!");
        }
        String fileName = file.getOriginalFilename();
        log.info("上传文件名称为：" + fileName);
        if (StringUtils.isBlank(fileName) || fileName.indexOf(".") < 0) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,fileName+"文件格式错误");
        }
        int fileType = FileUtil.checkFileName(fileName);
        CurrentUserDTO user = SecurityContext.getUser();
        if (ClientTypeConstant.APP == user.getType()) {
            if (fileType == 2 || fileType == 3) {
                throw new BusinessException(ErrorCode.BAD_REQUEST,fileName+"该文件不支持上传");
            }
        }
        if (fileType == 0) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,fileName+"该文件不支持上传");
        }
        long size = file.getSize();
        if (size == 0) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,fileName+"文件为空");
        }
        double sizeMb = (double)size/1048576;
        if (fileType == 1 && sizeMb > 20) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"图片大小不能超过20M");
        }
        if (fileType == 2 && sizeMb > 50) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"视频大小不能超过50M");
        }
        try {
            byte[] b = new byte[10];
            file.getInputStream().read(b,0,b.length);
            String headerHex = FileUtil.bytesToHexString(b);
            log.info("上传文件headerHex:{}",headerHex);
            boolean isValid = false;
            if (ClientTypeConstant.APP == user.getType()) {
                for (String hex : FileUtil.validFileHexApp) {
                    if(headerHex.startsWith(hex)) {
                        isValid = true;
                        break;
                    }
                }
            } else {
                for (String hex : FileUtil.validFileHex) {
                    if (headerHex.startsWith(hex)) {
                        isValid = true;
                        break;
                    }
                }
            }
            if(!isValid) {
                throw new BusinessException(ErrorCode.BAD_REQUEST,"该文件格式不支持上传!");
            }
        } catch (IOException e) {
            log.error("上传文件解析文件头失败",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST,"解析文件失败!");
        }

        if (ClientTypeConstant.APP == user.getType()) {
            UserSimpleDTO data = userInfoFeginClient.getCurrentUserSimpleInfo().getData();
            String redisKey = CacheKeys.UPLOAD_TIMES + user.getTenantId() + ":" + DateUtils.getCurrDate() + ":" + data.getUnionid();

            Object timesObj = redisService.get(redisKey);
            if (timesObj != null) {
                Integer times = (Integer) timesObj;
                if (times >= 100) {
                    log.error("上传文件用户上传次数超限" + times);
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "用户上传次数超限!");
                }
                redisService.set(redisKey, times + 1, CommonConstants.DAY_SECONDS);
            } else {
                redisService.set(redisKey, 1, CommonConstants.DAY_SECONDS);
            }
        }

        String datePath = new SimpleDateFormat("yyyyMMdd").format(new Date());
        long times = System.currentTimeMillis();
        long tenantId = ParamUtils.Long2long(SecurityContext.getUser().getTenantId());
        String suffix = fileName.substring(fileName.lastIndexOf(".")).toLowerCase();
        String newFileName = "mall/" + tenantId + "/" + datePath + "/" + times+random.nextInt(10000) + suffix;
        if(uploadCloud.equals(1)) {
            return uploadCos(file,newFileName);
        }
        else if(uploadCloud.equals(2)) {
            return uploadToQiniu(file,newFileName);
        }
        else if(uploadCloud.equals(3)) {
            return uploadAzure(file,newFileName);
        }
        else if(uploadCloud.equals(4)) {
            return uploadOss(file,newFileName);
        }
        return uploadToLocal(file,newFileName);
    }

    /**
     * 上传文件到本地
     * @param file
     * @param fileName
     * @return/
     */
    private Result<String> uploadToLocal(MultipartFile file, String fileName) {
        Result<String> result = new Result<>();
        try {
            File targetFile = new File(filePath, fileName);
            if(!targetFile.getParentFile().exists()){
                targetFile.getParentFile().mkdirs();
            }
            FileUtil.copyInputStreamToFile(file.getInputStream(), targetFile);
            result.setData(requestDomain+"/"+fileName);
        } catch (IOException e) {
            log.error("上传文件到本地出错"+filePath+fileName,e);
            throw new BusinessException(ErrorCode.REQUEST_SERVICE_ERROR,"读取文件失败");
        } catch (Exception e) {
            log.error("上传文件到本地失败",e);
            throw new BusinessException(ErrorCode.REQUEST_SERVICE_ERROR,"上传失败");
        }
        return result;
    }

    /**
     * 上传文件到七牛
     * @param file
     * @param fileName
     * @return
     */
    private Result<String> uploadToQiniu(MultipartFile file, String fileName) {
        Result<String> result = new Result<String>();
        try {
            /** 构造一个带指定 Region 对象的配置类 */
            Configuration cfg = new Configuration(Region.region0());
            /** 其他参数参考类注释 */
            UploadManager uploadManager = new UploadManager(cfg);
            /** 生成上传凭证，然后准备上传 */
            Auth auth = Auth.create(accessKey, secretKey);
            String token = auth.uploadToken(bucket);
            Response response = uploadManager.put(file.getBytes(), fileName, token);
            //解析上传成功的结果
            DefaultPutRet putRet = JSON.parseObject(response.bodyString(), DefaultPutRet.class);
            log.info("七牛上传文件:{}",putRet.key);
            result.setData(requestDomain+"/"+fileName);
        } catch (Exception e) {
            log.error("七牛上传文件异常",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST,"上传失败");
        }
        return result;
    }

    @ApiOperation("上传文件到腾讯云COS")
    public Result<String> uploadCos(MultipartFile file, String fileName) {
        COSCredentials cred = new BasicCOSCredentials(accessKey, secretKey);
        // 2 设置 bucket 的区域, COS 地域的简称请参阅 https://cloud.tencent.com/document/product/436/6224
        // clientConfig 中包含了设置 region, https(默认 http), 超时, 代理等 set 方法, 使用可参阅源码或者常见问题 Java SDK 部分
        com.qcloud.cos.region.Region r = new com.qcloud.cos.region.Region(region);
        ClientConfig clientConfig = new ClientConfig(r);
        File targetFile = new File(fileName);
        try {
            Files.copy(file.getInputStream(), Paths.get(fileName));
            // 3 生成 cos 客户端
            COSClient cosClient = new COSClient(cred, clientConfig);
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, fileName, targetFile);
            cosClient.putObject(putObjectRequest);
        } catch (IOException e) {
            throw new BusinessException(ErrorCode.REQUEST_SERVICE_ERROR,"上传失败");
        }finally {
            if (!targetFile.delete()) {
                log.error("文件删除失败");
            }
        }
        Result<String> result = new Result<String>();
        result.setData(requestDomain+"/"+fileName);
        return result;
    }

    /**
     * 微软云上传
     * @param file
     * @param fileName
     * @return
     * @throws IOException
     */
    public Result<String> uploadAzure(MultipartFile file, String fileName) {
        CloudBlockBlob blob = initAzure(fileName);
        try {
            // 将文件上传到Azure Container
            byte[] bytes = file.getBytes();
            blob.uploadFromByteArray(bytes, 0, bytes.length);
        } catch (Exception e) {
            log.error("文件上传微软云异常", e);
            throw new BusinessException(ErrorCode.INTERNAL_ERROR, "文件上传异常");
        }
        Result<String> result = new Result<String>();
        result.setData(requestDomain + "/" + containerName + "/" + blob.getName()+"?"+sign);
        return result;
    }

    /** 微软云上传初始化 */
    private CloudBlockBlob initAzure(String blobKey) {
        // 密钥连接字符串
        String storageConnectionString = "AccountName=" + accountName + ";"
                + "AccountKey=" + accountKey + ";"
                + "EndpointSuffix=" + endpointSuffix + ";"
                + "DefaultEndpointsProtocol=" + defaultEndpointsProtocol + ";";
        CloudBlockBlob blob = null;
        try {
            // 获得StorageAccount对象
            CloudStorageAccount storageAccount= CloudStorageAccount.parse(storageConnectionString);
            // 由StorageAccount对象创建BlobClient
            CloudBlobClient blobClient = storageAccount.createCloudBlobClient();
            // 根据传入的containerName, 获得container实例
            CloudBlobContainer container = blobClient.getContainerReference(containerName);
            // 构建目标BlockBlob对象
            blob = container.getBlockBlobReference(blobKey);
        } catch (Exception e) {
            log.error("访问微软云存储异常", e);
            throw new BusinessException(ErrorCode.INTERNAL_ERROR, "文件上传出错");
        }
        return blob;
    }

    @ApiOperation("上传文件到阿里云OSS")
    public Result<String> uploadOss(MultipartFile file, String fileName) {
        OSS ossClient = new OSSClient(endpoint, accessKey, secretKey);
        //文件上传路径
        String objectName = ossProject + "/" + fileName;
        try {
            // 如果需要上传时设置存储类型与访问权限，请参考以下示例代码。
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setHeader(OSSHeaders.OSS_STORAGE_CLASS, StorageClass.Standard.toString());
            metadata.setObjectAcl(CannedAccessControlList.PublicReadWrite);
            // metadata.setContentType(ModelToDtoUtils.getcontentType("application/octet-stream"));
            // <yourObjectName>表示上传文件到OSS时需要指定包含文件后缀在内的完整路径，例如abc/efg/123.jpg。
            ossClient.putObject(bucket, objectName, new ByteArrayInputStream(file.getBytes()), metadata);
            // 关闭OSSClient。
            ossClient.shutdown();
            /*// 设置URL过期时间为1小时。
            Date expiration = new Date(System.currentTimeMillis() + 3600 * 1000);
            // 生成以GET方法访问的签名URL，访客可以直接通过浏览器访问相关内容。
            URL url = ossClient.generatePresignedUrl(bucket, fileName, expiration);
            log.info("阿里云OSS上传完成:{}",url.toString());*/
        } catch (Exception e) {
            log.error("阿里云OSS上传失败",e);
            ossClient.shutdown();
            throw new BusinessException(ErrorCode.REQUEST_SERVICE_ERROR,"上传失败");
        }
        Result<String> result = new Result<String>();
        result.setData(requestDomain+"/"+objectName);
        return result;
    }

    @PostMapping("/crm/file/upload/max")
    @ApiOperation("我的消息历史文件上传")
    public Result<String> uploadFileMax(@RequestParam("file") MultipartFile file) {
        if (null == file) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"请选择要上传的文件!");
        }
        String fileName = file.getOriginalFilename();
        log.info("上传文件名称为：" + fileName);
        if (StringUtils.isBlank(fileName) || fileName.indexOf(".") < 0) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,fileName+"文件格式错误");
        }
        int fileType = FileUtil.checkFileName(fileName);
        if (fileType == 0) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,fileName+"该文件不支持上传");
        }
        long size = file.getSize();
        if (size == 0) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,fileName+"文件为空");
        }
        double sizeMb = (double)size/1048576;
        if (fileType == 1) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,fileName+"文件格式错误");
        }
        if (fileType == 2) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,fileName+"文件格式错误");
        }
        String newFileName = "my-msg/" + fileName;
        if(uploadCloud.equals(1)) {
            return uploadCos(file,newFileName);
        }
        else if(uploadCloud.equals(2)) {
            return uploadToQiniu(file,newFileName);
        }
        else if(uploadCloud.equals(3)) {
            return uploadAzure(file,newFileName);
        }
        else if(uploadCloud.equals(4)) {
            return uploadOss(file,newFileName);
        }
        return uploadToLocal(file,newFileName);
    }

    public static void main(String[] args) throws Exception {
        /*OSS ossClient = new OSSClient("https://oss-cn-shanghai.aliyuncs.com", "LTAI5tMKuygq3aa4DxNzWRs9", "******************************");
        // 如果需要上传时设置存储类型与访问权限，请参考以下示例代码。
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setHeader(OSSHeaders.OSS_STORAGE_CLASS, StorageClass.Standard.toString());
        metadata.setObjectAcl(CannedAccessControlList.PublicReadWrite);
        // metadata.setContentType(ModelToDtoUtils.getcontentType("application/octet-stream"));
        // <yourObjectName>表示上传文件到OSS时需要指定包含文件后缀在内的完整路径，例如abc/efg/123.jpg。
        ossClient.putObject("tf-hmmp", "member/2309/1234.jpg", new File("D:\\opt\\data\\20230111.png"));
        // 关闭OSSClient。
        ossClient.shutdown();
            /*access_key: LTAI5tMKuygq3aa4DxNzWRs9
    secret_key: ******************************
    bucket: tf-hmmp
    endpoint: https://oss-cn-shanghai.aliyuncs.com*/
    }
}
