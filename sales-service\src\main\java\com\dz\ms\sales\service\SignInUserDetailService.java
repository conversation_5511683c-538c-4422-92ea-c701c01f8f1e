package com.dz.ms.sales.service;


import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.dto.basic.AliCheckRequestDTO;
import com.dz.ms.sales.dto.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/8
 */
public interface SignInUserDetailService {
    /**
     * 打卡活动数据汇总
     *
     * @return
     */
    CrmSignInUserInfoDTO getSignInUserInfo();

    /**
     * 打卡活动列表
     *
     * @return
     */
    PageInfo<CrmSignInUserDTO> getSignInUserList(CrmSignInUserParamDTO param);

    void exportSignInDetailList(DownloadAddParamDTO exportParam);

    /**
     * 查询用户是否开启打卡
     *
     * @param uid
     * @return
     */
    OpenSignInDTO isOpenSignIn(String campaignCode, Long uid);

    /**
     * 开启打卡
     *
     * @param uid
     * @param param
     * @return
     */
    Long openSignIn(Long uid, SignInUserOpenDTO param);


    /**
     * 查询当前打卡进度
     *
     * @param uid
     * @return
     */
    SignInRecordDTO querySignInSpeed(String campaignCode, Long uid);


    /**
     * 打卡
     *
     * @param uid
     * @param param
     * @return
     */
    Long signIn(Long uid, SignInUserDetailDTO param);

    /**
     * 补卡
     *
     * @param uid
     * @param param
     * @return
     */
    SignInSupplementDTO supplement(Long uid, SignInUserDetailDTO param);

    /**
     * 获取第7天的报告
     *
     * @param uid
     * @param days
     * @return
     */
    SignInUserDetailDTO getReport(Long uid, int days);

    /**
     * 获取第七天的报告v2
     *
     * @param campaignCode
     * @param uid
     * @return
     */
    SignInReportDTO getReportDays(String campaignCode, Long uid);

    /**
     * 签到提醒任务执行完成
     *
     * @param campaignCode
     */
    void signInPushMsgJob(String campaignCode);

    /**
     * 用户签到失败任务执行完成
     *
     * @param campaignCode
     */
    void signInUserFailJob(String campaignCode);

    /**
     * 重启打卡
     *
     * @param campaignCode
     * @param uid
     * @return
     */
    Boolean restartSignIn(String campaignCode, Long uid);

    void verifyImageSafety2(String materialUrl, AliCheckRequestDTO aliCheckRequestDTO);

    void verifyTextSafety2(String text, AliCheckRequestDTO aliCheckRequestDTO);
}
