<template>
  <a-form-item :label="props.label" :name="props.name" :rules="props.rules" class="ParentWrap">
    <a-select
      v-model:value="thisFields.value"
      :filterOption="true"
      :allowClear="true"
      :maxTagCount="1"
      placeholder="请选择"
      @change="thisMethods.change"
      :options="thisFields.options"
      v-bind="$attrs"
    />
  </a-form-item>
</template>

<script setup>
import { onMounted, reactive, watch } from 'vue'
import { PRODUCT_DELIVERY_METHODS_ARR } from '@/utils/constants.js'

const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  name: {
    type: String,
    default: ''
  },
  rules: {
    type: Object,
    default: () => undefined
  },
  modelValue: {
    type: [Array, String, Number],
    default: () => []
  }
})
const emits = defineEmits(['change', 'update:modelValue'])

const attrs = useAttrs()
const thisFields = reactive({
  value: attrs.mode === 'multiple' ? [] : '',
  options: PRODUCT_DELIVERY_METHODS_ARR
})
const thisMethods = {
  setValue () {
    thisFields.value = props.modelValue
  },
  change (e) {
    emits('update:modelValue', e)
    emits('change', e)
  }
}

onMounted(() => thisMethods.setValue())
watch(() => props.modelValue, () => thisMethods.setValue())
</script>

<style lang="scss" scoped>
.ParentWrap {
  .ant-select {
    width: 202px;
  }
}
</style>
