.clockPopup {
  position: relative;
  background: transparent;
  display: flex;
  flex-direction: column;
  align-items: center;

  &-content {
    overflow: hidden;
    width: 100%;
    border-radius: var(--radius);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 46rpx;
    padding-bottom: 63rpx;
    padding-right: 60rpx;
    padding-left: 60rpx;
    box-sizing: border-box;
    border-radius: 16rpx 16rpx 0 0;

    .clock {
      max-height: 1070rpx;

      .content {
        position: relative;
        padding-bottom: 1rpx;

        .title {
          padding-top: 8rpx;
          margin-bottom: 48rpx;
          font-family: 'MUJIFont2020',
            'SourceHanSansCN';
          font-weight: 700;
          font-size: 40rpx;
          color: #3C3C43;
          line-height: 52rpx;
          // letter-spacing: 1rpx;
          text-align: center;
          font-style: normal;
          text-transform: none;
        }

        .title1 {
          // margin-left: 84rpx;
          font-family: 'MUJIFont2020',
            'SourceHanSansCN';
          font-weight: 700;
          font-size: 28rpx;
          line-height: 52rpx;
          text-align: center;
          margin-bottom: 24rpx;
          display: flex;
          justify-content: center;

          text {
            background-color: #3C3C43;
            color: #FFFFFF;
            padding: 2rpx 22rpx;
          }
        }

        .title-content {
          margin-bottom: 10rpx;
          font-family: 'MUJIFont2020',
            'SourceHanSansCN';
          font-weight: 400;
          font-size: 24rpx;
          color: #3C3C43;
          line-height: 52rpx;
          font-style: normal;
          text-transform: none;
        }

        .title-content1 {
          margin-bottom: 4rpx;
          font-weight: 700;
        }

        .tips {
          // margin: 0 88rpx;
          padding: 0 3rpx;
          margin-bottom: 36rpx;
          font-family: 'MUJIFont2020', 'SourceHanSansCN';
          font-weight: 500;
          font-size: 20rpx;
          color: #3C3C43;
          line-height: 36rpx;
          text-align: justify;
          font-style: normal;
          text-transform: none;
        }

        .table {
          // width: 100%;
          // margin: 0 78rpx;
          height: auto;
          box-sizing: border-box;
          position: relative;

          // display: inline-block;

          .head {
            position: sticky;
            top: 0;
            z-index: 999;
            display: grid;

            /* display: grid; 网格布局 */
            /* grid-auto-flow 属性控制自动放置的项目如何插入网格中 */
            /* column	通过填充每一列来放置项目 */
            grid-auto-flow: column;

            .table_th {
              display: flex;
              justify-content: center;

              .table_td {
                display: flex;
                align-items: center;
                justify-content: center;
                font-family: 'MUJIFont2020', 'SourceHanSansCN';
                // min-width: 145rpx;
                min-width: 236rpx;
                height: 61rpx;
                // padding: 0rpx 20rpx;
                // width: 200rpx;
                border-top: 1rpx solid #999999;
                border-bottom: 1rpx solid #999999;
                border-left: 1rpx solid #999999;
                background: #FBF8F3;
                // box-sizing: border-box;
                font-weight: 700;
                font-size: 20rpx;
                color: #3C3C43;
                // line-height: 37rpx;

                // ,&:nth-last-child(2)
                &:nth-last-child(1) {
                  width: 334rpx;
                  // width: 202rpx;
                  border-right: 1rpx solid #999999;
                }

              }
            }
          }

          .tbale_body {

            .table_tr {
              display: flex;
              justify-content: center;

              .table_body_td {
                // padding: 10rpx 20rpx;
                // min-width: 145rpx;
                font-family: 'MUJIFont2020', 'SourceHanSansCN';
                min-width: 236rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                // box-sizing: border-box;
                height: 61rpx;
                border-bottom: 1rpx solid #999999;
                border-left: 1rpx solid #999999;
                font-weight: 400;
                font-size: 20rpx;
                color: #3C3C43;
                // line-height: 37rpx;

                // ,&:nth-last-child(2)
                &:nth-last-child(1) {
                  width: 334rpx;
                  // width: 202rpx;
                  border-right: 1rpx solid #999999;
                }
              }
            }
          }
        }
      }
    }
  }

  &-close {
    position: absolute;
    top: 50rpx;
    right: 40rpx;
    padding-top: 10rpx;
    height: 48rpx;
    width: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-closeBox {
    height: 80rpx;
    width: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

}

// .van-popup__close-icon {
//   color: #3C3C43;
// }
