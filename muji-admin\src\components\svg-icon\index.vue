<template>
  <svg class="svg-icon" :style="{
 borderRadius
  }" :aria-hidden="true">
    <use :xlink:href="symbolId" :fill="color" />
  </svg>
</template>

<script>
import { defineComponent, computed } from 'vue'

export default defineComponent({
  name: 'SvgIcon',
  props: {
    prefix: {
      type: String,
      default: 'icon',
    },
    name: {
      type: String,
      required: true,
    },
    color: {
      type: String,
      default: '#333',
    },
    borderRadius: {
      type: String,
      default: '',
    }
  },
  setup(props) {
    const symbolId = computed(() => `#${props.prefix}-${props.name}`)
    return { symbolId }
  },
})
</script>
<style scoped  lang="scss">
.svg-icon {
  vertical-align: top;
  fill: currentColor;
  overflow: hidden;
}
</style>