const app = getApp()
import {
  throttle
} from '../../../../utils/util';
Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    userInfo: {
      type: Object,
      value: {},
      observer: (val) => {

      }
    },
    expandState: {
      type: Boolean,
      value: false,
    }
  },
  /**
   * 组件的初始数据
   */
  data: {
    width: 50,
  },
  attached() {
    // console.log(this.data.legendData); // 等同于页面的 onLoad

  },


  /**
   * 组件的方法列表
   */
  methods: {
    toLegend: throttle(async function (e) {
      wx.$mp.track({
        event: 'member_legend_click'
      })
      if (app.ifRegister()) {
        wx.$mp.navigateTo({
          url: '/pages/myLegend/myLegend',
          success: (result) => { },
          fail: (res) => {
            console.log(res);
          },
          complete: (res) => { },
        })
      }
    }),
  }
})
