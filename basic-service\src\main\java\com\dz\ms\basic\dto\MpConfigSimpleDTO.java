package com.dz.ms.basic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 公众号/小程序配置简要信息DTO
 * @author: Handy
 * @date:   2022/01/28 15:35
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "公众号/小程序配置简要信息")
public class MpConfigSimpleDTO {

    @ApiModelProperty(value = "公众号/小程序appID")
    private String appId;
    @ApiModelProperty(value = "公众号/小程序秘钥")
    private String appSecret;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;

}
