// pages/myCoupon/myCoupon.ts
const app = getApp()
import {
  couponexlistpire
} from '../../api/index'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    tabList: [{
      label: '已使用',
      value: '2',
    }, {
      label: '已过期',
      value: '3',
    },],
    activeTab: 0,
    currentList: [],
    loading: false,
    pageSize: 10,
    pageNum: 1,
    count: 1,
    couponStatus: 2,
    scrollTop: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.getList()
  },

  getList() {
    let {
      count,
      currentList,
      loading
    } = this.data;
    if (loading) return
    if (count <= currentList.length) return
    this.setData({
      loading: true
    })
    couponexlistpire({
      pageSize: this.data.pageSize,
      pageNum: this.data.pageNum,
      couponStatus: this.data.couponStatus
    }).then(res => {
      const newData = res.data.list
      let currentListArr = []
      if (this.data.pageNum == 1) {
        currentListArr = newData;
      } else {
        currentListArr = this.data.currentList.concat(newData);
      }
      this.setData({
        currentList: currentListArr,
        count: res.data.count,
        pageSize: 10 // 必写 不能省略
      })
      // 用数据计算下一页的页码
      this.data.pageNum = Math.ceil(this.data.currentList.length / this.data.pageSize) + 1
    }).finally(() => {
      this.setData({
        loading: false
      })
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  onReachBottom() {
    this.getList()
  },

  onChangeTab(e) {
    const {
      index,
      value
    } = e.detail;
    this.data.currentList = []
    this.setData({
      activeTab: index,
      couponStatus: value,
      pageNum: 1,
      scrollTop: 0,
      loading: false,
      count: 1,
    })
    this.getList()
  },
})
