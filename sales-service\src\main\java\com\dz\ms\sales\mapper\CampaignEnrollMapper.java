package com.dz.ms.sales.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.sales.entity.CampaignEnroll;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/19
 */
@Repository
public interface CampaignEnrollMapper extends BaseMapper<CampaignEnroll> {

    List<CampaignEnroll> siftCampaignEnrollByChannel(@Param("campaignCode") String campaignCode,  @Param("channelOne") String channelOne, @Param("channelTwo") String channelTwo, @Param("num") Integer num);

    int verifyCampaignEnroll(List<Long> idList);

    void verifyCampaignEnroll1(String campaignCode);

}
