package com.dz.ms.adaptor.mqreceiver;

import com.alibaba.fastjson.JSONObject;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.DateUtils;
import com.dz.ms.adaptor.dto.CrmPointsSyncParamDTO;
import com.dz.ms.adaptor.dto.MqCrmPointsSyncMessageDTO;
import com.dz.ms.adaptor.service.PointsSubscriptionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 即将过期积分用户记录MQ消息接收
 */
@Component
@Slf4j
public class CrmPointsSyncMessageReceiver {

	@Resource
	private PointsSubscriptionService pointsSubscriptionService;
	/**
	 * 消费者接收消息并消费消息
	 * 交换机、队列不存在的话，以下注解可以自动创建交换机和队列
	 */
	@RabbitListener(bindings = @QueueBinding(value = @Queue(value = "adaptor.message.queue", durable = "true"), exchange = @Exchange(value = "adaptor.message", durable = "true", type = "topic"), key = "#"))
	@RabbitHandler
	public void onReadMessage(@Payload String messageStr) {
		try {
			MqCrmPointsSyncMessageDTO message = JSONObject.parseObject(messageStr,MqCrmPointsSyncMessageDTO.class);
			long timeStr = message.getCreateTime().getTime();
			//log.info("日期:{},即将过期积分用户记录mq消费,开始消费.[MqCrmPointsSyncMessageDTO={}]", timeStr, messageStr);
			CurrentUserDTO currentUser = new CurrentUserDTO();
			currentUser.setTenantId(message.getTenantId());
			SecurityContext.setUser(currentUser);
			if(CollectionUtils.isEmpty(message.getList())) {
				log.error("日期:{},即将过期积分用户记录mq消费error，参数为空", timeStr);
			}
			List<CrmPointsSyncParamDTO> paramDTOList = message.getList();
			String nowDay = pointsSubscriptionService.subTableList(LocalDate.now().format(DateUtils.Y_M_DTF1));
			if(StringUtils.isBlank(nowDay)){
				log.error("日期:{},即将过期积分用户记录mq消费error，subTableList执行异常，messageStr:{}", timeStr, messageStr);
			} else {
				Integer insertNum = pointsSubscriptionService.insertBatchSomeColumn(paramDTOList, nowDay);
				if(!Objects.equals(paramDTOList.size(),insertNum)){
					log.error("日期:{},即将过期积分用户记录mq消费error，insertNum执行异常，paramDTOList长度:{}，insertNum长度:{}", timeStr, paramDTOList.size(), insertNum);
				}
			}
		} catch (Exception e){
			log.error("即将过期积分用户记录mq消费Exception:" +  messageStr, e);
		}
	}

}
