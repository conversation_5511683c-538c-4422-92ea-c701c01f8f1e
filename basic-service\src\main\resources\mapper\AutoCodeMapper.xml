<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.basic.mapper.AutoCodeMapper" >

	<select id="getAllDatabase" resultType="java.lang.String">
  	SHOW DATABASES;
  </select>

	<select id="getTableNameBykeyword" resultType="com.dz.ms.basic.dto.AutoCodeTableDTO">
  	SELECT TABLE_NAME name,TABLE_COMMENT comment FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = #{database} AND TABLE_NAME LIKE concat('%',#{keyword},'%')
  </select>

	<select id="getTableInfoByTableName" resultType="com.dz.ms.basic.dto.AutoCodeColumnDTO" >
  	SELECT
  		COLUMN_NAME columName,
  		DATA_TYPE dataType,
  		COLUMN_COMMENT columnComment,
  		CASE COLUMN_KEY WHEN 'PRI' THEN 1 ELSE 0 END AS isPrimark,
  		CASE EXTRA WHEN 'auto_increment' THEN 1 ELSE 0 END AS isAuto
  	FROM INFORMATION_SCHEMA.COLUMNS
  		WHERE TABLE_SCHEMA = #{database}
  		AND TABLE_NAME = #{tableName}
  		ORDER BY ORDINAL_POSITION
  </select>

</mapper>
