// signUp/pages/activityRules/activityRules.js
const app = getApp();
Page({
    /**
     * 页面的初始数据
     */
    data: {
        couponObj: {
            ruleDesc:
                "1111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111",
        },
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {},
    bindscrolltolower: app.debounce(async function () {
        console.log("滚动到底部");
    }),

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {},

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {},

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {},

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {},

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {},

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {},

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {
        // 页面分享
        return {
            title: "和我一起体验「米糠护肤」开启五天打卡活动",
            imageUrl: wx.$config.ossImg + "/MiKangCampaign/mk-share-card-1.png",
            path: "/MiKangCampaign/pages/index/index",
        };
    },
});
