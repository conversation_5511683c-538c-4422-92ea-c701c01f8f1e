package com.dz.ms.basic.strategy;

import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.fegin.sales.InteractionTaskFeginClient;
import com.dz.common.core.service.DownloadStrategy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
public class InteractionTaskListDownLoadStrategy implements DownloadStrategy {

    @Resource
    private InteractionTaskFeginClient interactionTaskFeginClient;

    @Override
    public void export(DownloadAddParamDTO downloadAddParamDTO) {
        interactionTaskFeginClient.exportTaskList(downloadAddParamDTO);
    }
}
