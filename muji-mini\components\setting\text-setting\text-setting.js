// components/setting/text-setting/text-setting.js
const app = getApp()
Component({
  externalClasses: ['textStyle'],

  properties: {
    data: {
      type: Object
    },
    content: {
      type: String,
      observer(val) {
        this.handleContent()
      }
    }
  },
  data: {
    showInfo: '',
    rpx: app.globalData.rpx
  },
  lifetimes: {
    attached() {
      this.handleContent()
    }
  },

  methods: {
    handleContent() {
      this.setData({
        showInfo: app.handleContent(this.data.content)
      })
    }
  }
})
