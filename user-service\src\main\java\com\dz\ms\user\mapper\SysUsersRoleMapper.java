package com.dz.ms.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.user.dto.SysRoleDTO;
import com.dz.ms.user.entity.SysUsersRole;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户角色关系Mapper
 * @author: Handy
 * @date:   2022/2/4 23:04
 */
@Repository
public interface SysUsersRoleMapper extends BaseMapper<SysUsersRole> {

    /**
     * 根据角色Id列表获取角色对应绑定用户数量
     * @param roleIds
     * @return
     */
    List<SysRoleDTO> getRoleUserCountByRoleIds(@Param("roleIds") List<Long> roleIds);

}
