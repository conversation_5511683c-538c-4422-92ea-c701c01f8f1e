<template>
  <div class="header-title">轮播图片</div>
  <a-form-item label="高度">
    <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.height" addon-after="px"></a-input-number>
  </a-form-item>
  <a-form-item label="圆角">
    <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.borderRadius" addon-after="px"></a-input-number>
  </a-form-item>
  <a-form-item>
    <a-button @click="addList" block>+ 添加图片</a-button>
  </a-form-item>
  <VueDraggable :list="data.list" item-key="id" :animation="300">
    <template #item="{ element: item, index:i }">
      <div class="scroll">
        <div class="scroll-title">图{{ i+1 }}</div>
        <a-space>
          <HolderOutlined />
          <uploadImg :max="10" :width="100" :height="100" :imgUrl="item.imgUrl" :form="data" :path="i" :disabled="disabled" @success="uploadSuccess" />
          <div style="margin-left:20px;">
            <div style="margin-bottom:10px;font-weight:bold">跳转热区</div>
            <addLink type="2" :showType="[1,3,7]" :links="item.imgLinks" :components="components" @ok="(link)=>item.imgLinks=link">
              <a-button type="primary" style="width:200px;">设置热区</a-button>
            </addLink>
          </div>
        </a-space>
        <a-form-item label="主标题" style="margin-top:20px;">
          <a-textarea placeholder="请输入" v-model:value="item.mainTitle" maxlength="1000"></a-textarea>
        </a-form-item>
        <a-form-item label="副标题">
          <a-textarea placeholder="请输入" v-model:value="item.smallTitle" maxlength="1000"></a-textarea>
        </a-form-item>
        <delete-outlined class="scroll-delete" size="mini" @click.stop="deleteList(i)" />
      </div>
    </template>
  </VueDraggable>
  <div class="header-title">背景设置</div>
  <bgSet :addParams="data"></bgSet>
  <div class="header-title">组件样式</div>
  <a-form-item label="边距" :labelCol="{width:'50px'}">
    <a-space>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingTop" addon-before="上" addon-after="px"></a-input-number>
      </a-form-item>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingBottom" addon-before="下" addon-after="px"></a-input-number>
      </a-form-item>
    </a-space>
    <a-space>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingLeft" addon-before="左" addon-after="px"></a-input-number>
      </a-form-item>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingRight" addon-before="右" addon-after="px"></a-input-number>
      </a-form-item>
    </a-space>
  </a-form-item>
  <div class="header-title">自动轮播设置</div>
  <a-form-item label="自动轮播">
    <a-switch :unCheckedValue="0" :checkedValue="1" v-model:checked="data.autoplay" />
  </a-form-item>
  <template>
    <a-form-item label="统一停留时间" :labelCol="{width:'150px'}">
      <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.delayTime" addon-after="ms"></a-input-number>
    </a-form-item>
    <a-form-item label="特殊停留时间" :labelCol="{width:'150px'}">
      <a-button @click="addSpeicalTime" block>+ 添加一组特殊间隔</a-button>
      <a-form-item class="speical">
        <div class="speical-item" v-for="item in data.delayTimes" :key="item.id">
          <a-space>
            <a-select style="width:80px" v-model:value="item.index" :getPopupContainer="triggerNode => triggerNode.parentNode">
              <a-select-option v-for="(t,i) in data.list" :key="i" :value="i">图{{ i+1 }}</a-select-option>
            </a-select>
            <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="item.delayTime" addon-after="ms"></a-input-number>
          </a-space>
          <delete-outlined class="speical-delete" size="mini" @click.stop="data.delayTimes.splice(i,1)" />
        </div>
      </a-form-item>
    </a-form-item>
    <a-form-item label="滑动过渡时间" :labelCol="{width:'150px'}">
      <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.durationTime" addon-after="ms"></a-input-number>
    </a-form-item>
  </template>
  <div class="header-title">指示点设置</div>
  <a-form-item label="指示点">
    <a-switch :unCheckedValue="0" :checkedValue="1" v-model:checked="data.pointOpen" />
  </a-form-item>
  <template v-if="data.pointOpen">
    <a-form-item label="样式">
      <a-form-item label="尺寸(直径)" :colon="false">
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.pointSize" addon-after="px"></a-input-number>
      </a-form-item>
      <a-form-item label="间距" :colon="false">
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.pointSpace" addon-after="px"></a-input-number>
      </a-form-item>
      <a-form-item label="选中" :colon="false">
        <Color :show="false" :value="data.pointSelectColor" @changeColor="(color)=>changeColor(color,'pointSelectColor')"></Color>
      </a-form-item>
      <a-form-item label="未选中" :colon="false">
        <Color :show="false" :value="data.pointUnSelectColor" @changeColor="(color)=>changeColor(color,'pointUnSelectColor')"></Color>
      </a-form-item>
    </a-form-item>
    <a-form-item label="位置">
      <a-radio-group v-model:value="data.pointPostion">
        <a-radio :value="1">图内</a-radio>
        <a-radio :value="2">图外</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item label="垂直方位">
      <a-radio-group v-model:value="data.pointVertical">
        <a-radio :value="1">上方</a-radio>
        <a-radio :value="2">下方</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item label="水平方位">
      <a-radio-group v-model:value="data.pointHorizonal">
        <a-radio :value="1">靠左</a-radio>
        <a-radio :value="2">居中</a-radio>
        <a-radio :value="3">靠右</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item label="与图片边缘间距" :labelCol="{width:'200px'}">
      <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.pointOutSpace" addon-after="px"></a-input-number>
    </a-form-item>
  </template>
  <div class="header-title">标题设置</div>
  <div class="subject">
    <a-form-item label="主题">
      <a-switch :unCheckedValue="0" :checkedValue="1" v-model:checked="data.subject.open" />
    </a-form-item>
    <template v-if="data.subject.open">
      <a-form-item label="文本内容">
        <a-textarea placeholder="请输入" v-model:value="data.subject.content"></a-textarea>
      </a-form-item>
      <a-form-item label="文本设置">
        <textModal :data="data.subject" @ok="val=>data.subject=val">
          <a-button type="link">样式设置</a-button>
        </textModal>
      </a-form-item>
    </template>
  </div>
  <div class="subject">
    <a-form-item label="主标题">
      <a-switch :unCheckedValue="0" :checkedValue="1" v-model:checked="data.mainTitle.open" />
    </a-form-item>
    <template v-if="data.mainTitle.open">
      <a-form-item label="文本设置">
        <textModal :data="data.mainTitle" @ok="val=>data.mainTitle=val">
          <a-button type="link">样式设置</a-button>
        </textModal>
      </a-form-item>
    </template>
  </div>
  <div class="subject">
    <a-form-item label="副标题">
      <a-switch :unCheckedValue="0" :checkedValue="1" v-model:checked="data.smallTitle.open" />
    </a-form-item>
    <template v-if="data.smallTitle.open">
      <a-form-item label="文本设置">
        <textModal :data="data.smallTitle" @ok="val=>data.smallTitle=val">
          <a-button type="link">样式设置</a-button>
        </textModal>
      </a-form-item>
    </template>
  </div>
  <div class="header-title">滑动提示设置</div>
  <a-form-item label="滑动提示">
    <a-switch :unCheckedValue="0" :checkedValue="1" v-model:checked="data.slideShow" />
  </a-form-item>
  <!-- 悬浮窗 -->
  <div class="header-title">悬浮窗</div>
  <floatSetting :disabled="disabled" :addParams="data.floatSetting" :components="components" @ok="(val)=>data.floatSetting=val"></floatSetting>

</template>
<script setup>
const emit = defineEmits(['ok', 'cancel'])
import { v4 as uuidv4 } from "uuid";

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  // 所有组件数组
  components: {
    type: Array,
    default() {
      return []
    }
  },
  // 当前组件数据
  data: {
    type: Object,
    default() {
      return {}
    }
  },
  // 组件展示的宽度
  width: {
    type: Number,
    default: 750,
  },

})


// 添加图片
const addList = () => {
  props.data.list.push({
    id: uuidv4(),
    imgUrl: '', // 图片url
    mainTitle: '',
    smallTitle: '',
    // 图片热区
    imgLinks: [],
  })
}

// 删除图片
const deleteList = (i) => {
  props.data.list.splice(i, 1)
}


const addSpeicalTime = () => {
  props.data.delayTimes.push({
    id: uuidv4(),
    index: null,
    delayTime: '',
  })
}
// 上传图片 视频
const uploadSuccess = async (data) => {
  let { form, path, imgUrl } = data;
  form.list[path].imgUrl = imgUrl
}


// 修改颜色
const changeColor = async (color, path) => {
  props.data[path] = color
}
</script>


<style  scoped lang="scss">
.scroll {
  position: relative;
  margin-bottom: 10px;
  background: #efefef;
  padding: 10px;
  &-delete {
    position: absolute;
    top: 0;
    right: 0;
    padding: 10px;
    color: red;
  }
  &-title {
    margin-bottom: 10px;
  }
}
.subject {
  border: 1px solid #efefef;
  padding: 10px 10px 0;
  margin-bottom: 10px;
}
.speical {
  &-item {
    position: relative;
    margin-top: 10px;
    background: #efefef;
    padding: 10px;
    padding-right: 40px;
  }
  &-delete {
    position: absolute;
    top: 8px;
    right: 0;
    padding: 10px;
    color: red;
  }
}
</style>
