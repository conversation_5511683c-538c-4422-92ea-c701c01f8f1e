package com.dz.ms.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.product.dto.ShelfPeopleDTO;
import com.dz.ms.product.entity.ShelfPeople;
import com.dz.ms.product.mapper.ShelfPeopleMapper;
import com.dz.ms.product.service.ShelfPeopleService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 货架人群包条件
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:27
 */
@Service
public class ShelfPeopleServiceImpl extends ServiceImpl<ShelfPeopleMapper, ShelfPeople> implements ShelfPeopleService {

    @Resource
    private ShelfPeopleMapper shelfPeopleMapper;

    /**
     * 分页查询货架人群包条件
     *
     * @param param
     * @return PageInfo<ShelfPeopleDTO>
     */
    @Override
    public PageInfo<ShelfPeopleDTO> getShelfPeopleList(ShelfPeopleDTO param) {
        ShelfPeople shelfPeople = BeanCopierUtils.convertObjectTrim(param, ShelfPeople.class);
        IPage<ShelfPeople> page = shelfPeopleMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), new QueryWrapper<>(shelfPeople));
        return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), BeanCopierUtils.convertList(page.getRecords(), ShelfPeopleDTO.class));
    }

    /**
     * 根据ID查询货架人群包条件
     *
     * @param id
     * @return ShelfPeopleDTO
     */
    @Override
    public ShelfPeopleDTO getShelfPeopleById(Long id) {
        ShelfPeople shelfPeople = shelfPeopleMapper.selectById(id);
        return BeanCopierUtils.convertObject(shelfPeople, ShelfPeopleDTO.class);
    }

    /**
     * 保存货架人群包条件
     *
     * @param param
     * @return Long
     */
    @Override
    public Long saveShelfPeople(ShelfPeopleDTO param) {
        ShelfPeople shelfPeople = new ShelfPeople(param.getId(), param.getShelfId(), param.getShelfName(), param.getGroupId(), param.getGroupName(), param.getConditionType(), param.getConditionLogic(), param.getConditionStr(), param.getState());
        if (ParamUtils.isNullOr0Long(shelfPeople.getId())) {
            shelfPeopleMapper.insert(shelfPeople);
        } else {
            shelfPeopleMapper.updateById(shelfPeople);
        }
        return shelfPeople.getId();
    }

    /**
     * 根据ID删除货架人群包条件
     *
     * @param param
     */
    @Override
    public void deleteShelfPeopleById(IdCodeDTO param) {
        shelfPeopleMapper.deleteById(param.getId());
    }

}