package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 渠道链接配置
 * @author: Handy
 * @date:   2023/04/03 17:30
 */
@Getter
@Setter
@NoArgsConstructor
@Table("渠道链接配置")
@TableName(value = "channel_link")
public class ChannelLink implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "配置ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR,length = 20,isNull = true,comment = "链接自增编码")
    private String linkCode;
    @Columns(type = ColumnType.VARCHAR,length = 20,isNull = true,comment = "渠道编码")
    private String channel;
    @Columns(type = ColumnType.VARCHAR,length = 64,isNull = true,comment = "渠道名称")
    private String channelName;
    @Columns(type = ColumnType.VARCHAR,length = 128,isNull = true,comment = "小程序页面路径")
    private String path;
    @Columns(type = ColumnType.VARCHAR,length = 256,isNull = true,comment = "小程序页面参数")
    private String query;

    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = true,comment = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = false,defaultValue = "0",comment = "是否删除 0未删除 1已删除")
    @TableLogic
    private Integer isDeleted;

    public ChannelLink(Long id, String linkCode, String channel, String channelName, String path, String query) {
        this.id = id;
        this.linkCode = linkCode;
        this.channel = channel;
        this.channelName = channelName;
        this.path = path;
        this.query = query;
    }
}
