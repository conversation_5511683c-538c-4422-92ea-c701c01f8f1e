package com.dz.ms.user.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import com.dz.common.core.utils.ModelToSql;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 权限功能接口
 * @author: Handy
 * @date:   2022/07/18 18:14
 */
@Getter
@Setter
@NoArgsConstructor
@Table("权限功能接口")
@TableName(value = "sys_permission_url")
public class SysPermissionUrl implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "权限页面ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "权限ID",isIndex = true)
    private Long permitId;
    @Columns(type = ColumnType.VARCHAR,length = 30,isNull = false,comment = "权限名称")
    private String permitName;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "接口地址")
    private String url;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    public SysPermissionUrl(Long id, Long permitId, String permitName, String url) {
        this.id = id;
        this.permitId = permitId;
        this.permitName = permitName;
        this.url = url;
    }

}
