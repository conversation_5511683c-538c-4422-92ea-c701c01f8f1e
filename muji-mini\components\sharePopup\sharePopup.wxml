<!--pages/components/sharePopup/sharePopup.wxml-->
<van-popup style="--popup-background-color: transparent" lock-scroll closeable="{{false}}" zIndex="{{zIndex}}" show="{{ show }}" safe-area-inset-bottom="{{true}}" root-portal="{{true}}" custom-style="background-color: transparent" overlay="{{true}}" close-on-click-overlay="{{true }}" bind:close="close" overlay-style="background-color:rgba(0,0,0,0.5)">
  <view class="selfModal">
    <view class="selfModal-content" style="background:{{background}};--radius:{{borderRadius}}rpx;">
      <slot></slot>
    </view>
    <view class="selfModal-close" wx:if="{{closeable}}" catch:touchmove="touchmove1">
      <view class="selfModal-closeBox" bindtap="close">
        <text class="iconfont icon-a-Turnoff" style="color:#fff;font-size:50rpx;" catch:touchmove="touchmove1"></text>
      </view>
    </view>
  </view>
</van-popup>