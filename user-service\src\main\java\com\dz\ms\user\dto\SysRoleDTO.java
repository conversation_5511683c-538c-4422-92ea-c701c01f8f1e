package com.dz.ms.user.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 系统角色DTO
 * @author: Handy
 * @date:   2022/2/4 19:14
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "系统角色")
public class SysRoleDTO extends BaseDTO {

    @ApiModelProperty(value = "角色ID")
    private Long id;
    @ApiModelProperty(value = "角色名称")
    private String roleName;
    @ApiModelProperty(value = "角色描述")
    private String roleDesc;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "绑定用户数量")
    private Integer userNum;
    @ApiModelProperty(value = "权限ID列表")
    private List<Long> permitIds;

}
