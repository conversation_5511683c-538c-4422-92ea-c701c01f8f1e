package com.dz.ms.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.product.dto.MaterialDTO;
import com.dz.ms.product.entity.Material;
import com.dz.ms.product.mapper.MaterialMapper;
import com.dz.ms.product.service.MaterialService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 商品素材表
 *
 * @author: LiinNs
 * @date: 2024/11/18 13:45
 */
@Service
public class MaterialServiceImpl extends ServiceImpl<MaterialMapper, Material> implements MaterialService {

    @Resource
    private MaterialMapper materialMapper;

    /**
     * 分页查询商品素材表
     *
     * @param param
     * @return PageInfo<MaterialDTO>
     */
    @Override
    public PageInfo<MaterialDTO> getMaterialList(MaterialDTO param) {
        Material material = BeanCopierUtils.convertObjectTrim(param, Material.class);
        IPage<Material> page = materialMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), new QueryWrapper<>(material));
        return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), BeanCopierUtils.convertList(page.getRecords(), MaterialDTO.class));
    }

    /**
     * 根据ID查询商品素材表
     *
     * @param id
     * @return MaterialDTO
     */
    @Override
    public MaterialDTO getMaterialById(Long id) {
        Material material = materialMapper.selectById(id);
        return BeanCopierUtils.convertObject(material, MaterialDTO.class);
    }

    /**
     * 保存商品素材表
     *
     * @param param
     * @return Long
     */
    @Override
    public Long saveMaterial(MaterialDTO param) {
        Material material = new Material(param.getId(), param.getType(), param.getProductId(), param.getProductCode(), param.getUsedType(), param.getImgUrl(), param.getImgName());
        if (ParamUtils.isNullOr0Long(material.getId())) {
            materialMapper.insert(material);
        } else {
            materialMapper.updateById(material);
        }
        return material.getId();
    }

    /**
     * 根据ID删除商品素材表
     *
     * @param param
     */
    @Override
    public void deleteMaterialById(IdCodeDTO param) {
        materialMapper.deleteById(param.getId());
    }

}