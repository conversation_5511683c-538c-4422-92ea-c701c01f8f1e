@import "assets/scss/config";

.store {
  &-unauthorized {
    position: relative;

    &-li {
      font-family: SourceHanSansCN,
        SourceHanSansCN,
        MUJIFont2020,
        PingFangSC,
        PingFang SC;
      font-weight: 700;
      font-size: 32rpx;
      color: #3C3C43;
      line-height: 46rpx;
      letter-spacing: 1px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    &-tips {
      margin-top: 22rpx;
      margin-bottom: 40rpx;
      font-family: SourceHanSansCN,
        SourceHanSansCN,
        MUJIFont2020,
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #3C3C43;
      line-height: 36rpx;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    &-bth {
      position: absolute;
      top: 19rpx;
      right: 10rpx;
      width: 182rpx;
      height: 60rpx;
      background: #3C3C43;
      border-radius: 5rpx 5rpx 5rpx 5rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: SourceHanSansCN,
        <PERSON>HanSansCN,
        MUJIFont2020,
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 24rpx;
      color: #FFFFFF;
      line-height: 36rpx;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
  }

  // width: 100%;
  // border-bottom: 1px solid #eee;
  &-address {

    margin-bottom: 40rpx;
    font-family: SourceHanSansCN, SourceHanSansCN, PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #3C3C43;
    line-height: 36rpx;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  &-name {

    margin-bottom: 22rpx;
    font-family: SourceHanSansCN,
      SourceHanSansCN,
      MUJIFont2020,
      PingFangSC,
      PingFang SC;
    font-weight: 700;
    font-size: 32rpx;
    color: #3C3C43;
    line-height: 46rpx;
    letter-spacing: 1px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30rpx;

    &-left {
      display: flex;
      align-items: center;
      font-family: SourceHanSansCN,
        SourceHanSansCN,
        MUJIFont2020,
        PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 24rpx;
      color: #888888;
      line-height: 35rpx;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }

    &-right {
      display: flex;
      align-items: center;
      height: 36rpx;

      &-title {
        margin-top: 6rpx;
        font-family: SourceHanSansCN,
          SourceHanSansCN,
          MUJIFont2020,
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 20rpx;
        color: #3C3C43;
        line-height: 28rpx;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }
    }
  }

  &-img {
    width: 100%;
  }



  &-title {
    font-family: SourceHanSansCN,
      SourceHanSansCN,
      MUJIFont2020,
      PingFangSC,
      PingFang SC;
    font-weight: bold;
    font-size: 32rpx;
    color: #3C3C43;
    line-height: 44rpx;
    margin-bottom: 36rpx;

  }



}
