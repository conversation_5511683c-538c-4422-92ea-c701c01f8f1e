package com.dz.common.core.exception;


import com.dz.common.base.enums.ErrorCode;
import lombok.extern.slf4j.Slf4j;

/**
 * 业务异常
 */
@Slf4j
public class BusinessException extends RuntimeException {

	/** 状态编码 */
	private Integer code;

	public BusinessException() {
		super(ErrorCode.INTERNAL_ERROR.getMessage());
		this.code = ErrorCode.INTERNAL_ERROR.getCode();
	}

	public BusinessException(Throwable cause) {
		super(ErrorCode.INTERNAL_ERROR.getMessage(),cause);
		this.code = ErrorCode.INTERNAL_ERROR.getCode();
	}

	public BusinessException(String message) {
		super(message);
		this.code = ErrorCode.INTERNAL_ERROR.getCode();
	}

	public BusinessException(String message, Throwable cause) {
		super(message, cause);
		this.code = ErrorCode.INTERNAL_ERROR.getCode();
	}

	public BusinessException(ErrorCode errorCode, String message, Throwable cause) {
		super(message, cause);
		this.code = errorCode.getCode();
	}

	public BusinessException(ErrorCode errorCode, String message) {
		super(message);
		this.code = errorCode.getCode();
	}

	public BusinessException(ErrorCode errorCode, String msgFormat, Object... args) {
		super(String.format(msgFormat, args));
		this.code = errorCode.getCode();
	}

	public BusinessException(ErrorCode errorCode) {
		super(errorCode.getMessage());
		this.code = errorCode.getCode();
	}

	public BusinessException(Integer code, String message) {
		super(message);
		this.code = code;
	}

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}
}
