.level-box {
  width: 650rpx;
  margin: 0 auto;
  position: relative;
  z-index: 1;
  transition: opacity 0.5s;

  .row {
    display: flex;
    margin-bottom: 32rpx;
    align-items: center;
    justify-content: space-between;

    .level-name {
      // width: 152rpx;
      font-family: SourceHanSansSC, SourceHanSansSC;
      font-weight: 700;
      font-size: 36rpx;
      color: #3C3C43;
      line-height: 54rpx;
      letter-spacing: 1px;
      text-align: left;
      font-style: normal;
    }
  }

  .level-line {
    width: 650rpx;
    height: 16rpx;
    background: rgba(60, 60, 67, 0.1);
    border-radius: 2rpx;
    .current-level {
      width: var(--width);
      height: inherit;
      background: #3C3C43;
    }
  }

  .tips-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10rpx;

    .tips-arrow {
      margin-left: 5rpx;
      width: 18rpx;
      height: 18rpx;
    }

    .txt {
      display: flex;
      align-items: center;
      height: 28rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 20rpx;
      color: #3C3C43;
      line-height: 28rpx;
      text-align: right;
      font-style: normal;
    }
  }
}
