package com.dz.common.core.enums;

import java.util.Objects;

/**
 * Describe：状态类型枚举
 * Created by 吴蜀黍 on 2023/8/20 8:36
 **/
public enum StateEnum {

    ENABLE(1, "启用，上架"),
    DISABLE(0, "禁用、下架"),
    ;

    private final Integer code;
    private final String value;

    StateEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (StateEnum resultEnum : StateEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum.value;
            }
        }
        return null;
    }
}
