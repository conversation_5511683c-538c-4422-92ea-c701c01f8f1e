import service from '@/utils/request.js'
import { SHELF_STATUS_OBJ, SHELF_SWITCH_STATUS_OBJ, PRODUCT_TYPE_OBJ } from '@/utils/constants.js'

import { v4 } from 'uuid'
import { cloneDeep } from 'lodash'
import { number } from 'echarts'

const itemHandler = (v) => {
  if (v.onType === 1) {
    v.onStartTime = ''
    v.onEndTime = '永久上架'
  }
  v.productSum = v.productSum || 0
  v.shelfStateDesc = SHELF_STATUS_OBJ[v.shelfState]
  v.stateDesc = SHELF_SWITCH_STATUS_OBJ[v.state]
  v.shelfActivityList = v.shelfActivityList || []
  v.saveShelfProductList = v.saveShelfProductList || []
  v.saveShelfProductList.forEach(v2 => {
    v2.superscriptIdList = v2.superscriptIdList || []
    v2.superscriptIdList = v2.superscriptIdList.filter(v => v).map(v => +v)
  })
}
const itemHandlerShelfProduct = (v) => {
  console.log("🚀 ~ itemHandlerShelfProduct ~ v:", v)
  v.uuid = v4()
  v.tagList = v.tagList || []
  v.superscriptIdList = v.superscriptIdList || []
  v.superscriptIdList = v?.superscriptIdList?.filter(v => v)?.map(v => +v)
  v.superscriptCampaign = v?.superscriptCampaignNameList?.join('、') || []
  if (typeof v.pdType == 'number') {
    v.pdTypeDesc = PRODUCT_TYPE_OBJ[v.pdType]
  }
}

export const apiGiftRack = {
  async getAllPriority(data) {
    return await service({ url: '/crm/product/shelf/all_priority', method: 'get', data }).then(res => {
      res.data = res.data || []
      return res
    })
  },
  async getAllPageList(data) {
    return await service({ url: '/crm/product/shelf/list_by_name', method: 'get', data }).then(res => {
      res.data.forEach(v => itemHandler(v))
      return res
    })
  },
  async getPageList(data) {
    data = cloneDeep(data)
    delete data.dateTimeRange
    return await service({ url: '/crm/product/shelf/list', method: 'get', data }).then(res => {
      res.data.list.forEach(v => itemHandler(v))
      return res
    })
  },
  async getShelfProductPageList(data) {
    return await service({ url: '/crm/product/shelf_product/list', method: 'post', data }).then(res => {
      res.data.list.forEach(v => itemHandlerShelfProduct(v))
      if (data.hiddenProductIdList) {
        res.data.list.forEach(item => {
          item.disabled = data.hiddenProductIdList.includes(item.productId)
        })
      }

      // res.data.list = res.data.list.filter(v => !data.hiddenProductIdList.includes(v.id))
      // debugger
      return res
    })
  },
  async getShelfProductPageListShop(data) {//商品列表
    return await service({ url: '/crm/product/shelf_product/list', method: 'post', data }).then(res => {
      res.data.list.forEach(v => itemHandlerShelfProduct(v))

      // res.data.list = res.data.list.filter(v => !data.hiddenProductIdList.includes(v.id))
      // debugger
      return res
    })
  },
  async getShelfProductAllList(data) {
    return await service({ url: '/crm/product/shelf_product/product_by_shelf_id', method: 'get', data }).then(res => {

      res.data.forEach(v => itemHandlerShelfProduct(v))
      return res
    })
  },
  async getPageDetail(data) {
    return await service({ url: '/crm/product/shelf/info', method: 'get', data }).then(async res => {
      if (res.data.onType !== 1 && res.data.onStartTime && res.data.onEndTime) {
        res.data.dateTimeRange = [res.data.onStartTime, res.data.onEndTime]
      }
      // if (res.data.onStartTime && res.data.onEndTime) {
      //   res.data.dateTimeRange = [res.data.onStartTime, res.data.onEndTime]
      // }
      const res2 = await apiGiftRack.getShelfProductAllList({ shelfId: data.id })
      res.data.saveShelfProductList = res2.data
      return res
    })
  },
  async createPage(data) {
    data.onStartTime = data.dateTimeRange[0]
    data.onEndTime = data.dateTimeRange[1]
    data.saveShelfProductList.forEach(v2 => {
      v2.superscriptIdList = v2.superscriptIdList || []
    })
    return await service({ url: '/crm/product/shelf/add', method: 'post', data }).then(res => {
      return res
    })
  },
  async updatePage(data) {
    data.onStartTime = data.dateTimeRange[0]
    data.onEndTime = data.dateTimeRange[1]
    data.saveShelfProductList.forEach(v2 => {
      v2.superscriptIdList = v2.superscriptIdList || []
    })
    return await service({ url: '/crm/product/shelf/update', method: 'post', data }).then(res => {
      return res
    })
  },
  async updateShelfProductList(data) {
    data.saveShelfProductList.forEach(v2 => {
      v2.superscriptIdList = v2.superscriptIdList || []
    })
    return await service({ url: '/crm/product/shelf_product/save', method: 'post', data }).then(res => {
      return res
    })
  },
  async updateState(data) {
    data.number = data.state
    return await service({ url: '/crm/product/shelf/update_state', method: 'post', data }).then(res => {
      return res
    })
  },
  async deletePage(data) {
    return await service({ url: '/crm/product/shelf/delete', method: 'post', data }).then(res => {
      return res
    })
  },
  async exportPage(data) {
  }
}
