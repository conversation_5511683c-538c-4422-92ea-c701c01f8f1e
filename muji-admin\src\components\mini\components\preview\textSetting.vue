<template>
  <div class="textStyle textSetting" :style="{paddingLeft:data.paddingLeft/2+'px',paddingRight:data.paddingRight/2+'px',paddingBottom:data.paddingBottom/2+'px',paddingTop:data.paddingTop/2+'px',}">
    <div :class="['textSetting-content','lineType'+data.lineType]" :style="{fontFamily:data.fontFamily,fontSize:data.fontSize/2+'px',lineHeight:data.lineHeight/2+'px',color:data.color,fontStyle:data.fontItalic?'italic':'',textDecoration:data.underLine?'underline':data.deleteLine?'line-through':'',textAlign:data.textAlign,fontWeight:data.fontWeight?'bold':'normal','--lines':data.lines,'--height':data.lineHeight/2+'px'}">
      <div class="textSetting-bg">
        <slot></slot>
      </div>
      <div class="textSetting-text" v-html="content">
      </div>
    </div>
  </div>
</template>
<script setup>
const emit = defineEmits(['ok', 'cancel', 'changeActive'])
import { cloneDeep } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { computed } from 'vue';
const props = defineProps({
  data: {
    type: Object,
    default() {
      return {}
    }
  },
  content: {
    type: String,

  },
})



</script>

<style scoped lang="scss">
.textSetting {
  box-sizing: border-box;
  position: relative;

  &-content {
    text-underline-offset: 8/2px;

    &.lineType1 {
      height: auto;
    }

    // 超出省略  不超出不省略
    &.lineType2 {
      height: calc(var(--lines) * var(--height));
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: var(--lines);
    }

    // 超出省略  不超出不省略
    &.lineType3 {
      max-height: calc(var(--lines) * var(--height));
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: var(--lines);
    }
  }

  &-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  &-text {
    position: relative;
    z-index: 100;
    word-break: break-all;
  }
}
</style>
