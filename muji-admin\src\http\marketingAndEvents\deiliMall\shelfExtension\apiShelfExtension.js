import dayjs from 'dayjs'
import service from '@/utils/request.js'
import { SHELF_EXTENSION_STATUS_OBJ, SHELF_EXTENSION_SWITCH_STATUS_OBJ } from '@/utils/constants.js'
import { cloneDeep } from 'lodash'

export const apiShelfExtension = {
  async getPageList(data) {
    data = cloneDeep(data)
    delete data.dateTimeRange
    return await service({ url: '/crm/product/shelf_promotion/list', method: 'get', data }).then(res => {
      res.data.list.forEach(v => {
        if (v.onType === 1) {
          v.onStartTime = ''
          v.onEndTime = '永久上架'
        }
        v.num = v.num || 0
        v.promotionStateDesc = SHELF_EXTENSION_STATUS_OBJ[v.promotionState]
        v.stateDesc = SHELF_EXTENSION_SWITCH_STATUS_OBJ[v.state]
      })
      return res
    })
  },
  async getPageDetail(data) {
    return await service({ url: '/crm/product/shelf_promotion/info', method: 'get', data }).then(res => {
      if (res.data.onStartTime && res.data.onEndTime) {
        res.data.dateTimeRange = [res.data.onStartTime, res.data.onEndTime]
      }
      try {
        res.data.content = JSON.parse(res.data.content || JSON.stringify({}))

      } catch (e) {
        res.data.content = {}
      }
      return res
    })
  },
  async createPage(data) {
    data.onStartTime = data.dateTimeRange[0]
    data.onEndTime = data.dateTimeRange[1]
    data.num = data.content.list.length
    data.content = JSON.stringify(data.content)
    return await service({ url: '/crm/product/shelf_promotion/add', method: 'post', data }).then(res => {
      return res
    })
  },
  async updatePage(data) {
    data.onStartTime = data.dateTimeRange[0]
    data.onEndTime = data.dateTimeRange[1]
    data.num = data.content.list.length
    data.content = JSON.stringify(data.content)
    return await service({ url: '/crm/product/shelf_promotion/update', method: 'post', data }).then(res => {
      return res
    })
  },
  async updateState(data) {
    data.number = data.state
    return await service({ url: '/crm/product/shelf_promotion/update_state', method: 'post', data }).then(res => {
      return res
    })
  },
  async deletePage(data) {
    return await service({ url: '/crm/product/shelf_promotion/delete', method: 'post', data }).then(res => {
      return res
    })
  },
  async exportPage(data) {
  }
}
