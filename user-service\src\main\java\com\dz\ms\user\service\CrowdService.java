package com.dz.ms.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.user.CrowdAllDTO;
import com.dz.common.core.dto.user.CrowdDTO;
import com.dz.ms.user.entity.Crowd;

import java.util.List;

/**
 * 人群包接口
 * @author: yibo
 * @date:   2024/11/18 13:53
 */
public interface CrowdService extends IService<Crowd> {

	/**
     * 分页查询人群包
     * @param param
     * @return PageInfo<CrowdDTO>
     */
    public PageInfo<CrowdDTO> getCrowdList(CrowdDTO param);

    /**
     * 根据ID查询人群包
     * @param id
     * @return CrowdDTO
     */
    public CrowdDTO getCrowdById(Long id);

    /**
     * 保存人群包
     * @param param
     * @return Long
     */
    public Long saveCrowd(CrowdDTO param);

    /**
     * 根据ID删除人群包
     * @param param
     */
    public void deleteCrowdById(IdCodeDTO param);

    void updateCrowd(CrowdDTO param);

    void updateStatus(CrowdDTO param);

    List<CrowdDTO> getList(CrowdDTO param);

    List<Long> userHaveCrowd();


    void updateCrowdCrm(CrowdAllDTO crowdAllDTO);

    Long saveCrowdCrm(CrowdAllDTO crowdAllDTO);

    boolean crowdIsHave(Long id);
}
