package com.dz.ms.product.dto.req;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 商品搜索字段
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "商品搜索字段")
public class CrmProductListParamDTO extends BaseDTO {

    @ApiModelProperty(value = "状态 0禁用 1启用")
    private Integer state;

    @ApiModelProperty(value = "商品CP号")
    private String venderId;

    @ApiModelProperty(value = "商品ID列表")
    private List<Long> productIdList;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "商品类型 1实物商品 2电子券")
    private Integer pdType;

    @ApiModelProperty(value = "货架ID列表")
    private List<Long> shelfIdList;

    @ApiModelProperty(value = "隐藏已添加商品ID列表")
    private List<Long> hiddenProductIdList;
}
