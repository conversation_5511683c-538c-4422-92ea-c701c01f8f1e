package com.dz.ms.user.controller;

import com.alibaba.excel.EasyExcel;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.user.CrowdAllDTO;
import com.dz.common.core.dto.user.CrowdDTO;
import com.dz.common.core.dto.user.CrowdImportResultDTO;
import com.dz.common.core.dto.user.CrowdTemplateExcelDTO;
import com.dz.common.core.utils.ExcelUtils;
import com.dz.ms.user.service.CrowdService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@Api(tags="人群包")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class CrowdController  {

    @Resource
    private CrowdService crowdService;

    /**
     * 分页查询人群包
     * @param param
     * @return result<PageInfo<CrowdDTO>>
     */
    @ApiOperation("分页查询人群包")
	@GetMapping(value = "/crm/crowd/list")
    public Result<PageInfo<CrowdDTO>> getCrowdList(@ModelAttribute CrowdDTO param) {
        Result<PageInfo<CrowdDTO>> result = new Result<>();
		PageInfo<CrowdDTO> page = crowdService.getCrowdList(param);
        result.setData(page);
        return result;
    }
    @ApiOperation("人群包列表")
    @GetMapping(value ={"/crm/crowd/noPageList","/crowd/noPageList"} )
    public Result<List<CrowdDTO>> getList(@ModelAttribute CrowdDTO param) {
        Result<List<CrowdDTO>> result = new Result<>();
        List<CrowdDTO> page = crowdService.getList(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询人群包
     * @param id
     * @return result<CrowdDTO>
     */
    @ApiOperation("根据ID查询人群包")
	@GetMapping(value = {"/crm/crowd/info","/crowd/info"})
    public Result<CrowdDTO> getCrowdById(@RequestParam("id") Long id) {
        Result<CrowdDTO> result = new Result<>();
        CrowdDTO crowd = crowdService.getCrowdById(id);
        result.setData(crowd);
        return result;
    }

    /**
     * 保存人群包
     * @param param
     * @return result<Long>
     */
    @ApiOperation("保存人群包")
	@PostMapping(value = "/crowd/save")
    public Result<Long> saveCrowd(@RequestBody CrowdDTO param) {
        Result<Long> result = new Result<>();
        Long id = crowdService.saveCrowd(param);
        result.setData(id);
        return result;
    }

    @ApiOperation("后台保存人群包")
    @PostMapping(value = "/crm/crowd/save")
    public Result<Long> saveCrowdCrm(@RequestBody CrowdAllDTO crowdAllDTO) {
        Result<Long> result = new Result<>();
        Long id = crowdService.saveCrowdCrm(crowdAllDTO);
        result.setData(id);
        return result;
    }

    @ApiOperation("编辑人群包")
    @PostMapping(value = "/crowd/update")
    public Result<Boolean> updateCrowd(@RequestBody CrowdDTO param) {
        Result<Boolean> result = new Result<>();
        crowdService.updateCrowd(param);
        result.setData(true);
        return result;
    }

    @ApiOperation("后台编辑人群包")
    @PostMapping(value = "/crm/crowd/update")
    public Result<Boolean> updateCrowdCrm(@RequestBody CrowdAllDTO crowdAllDTO) {
        Result<Boolean> result = new Result<>();
        crowdService.updateCrowdCrm(crowdAllDTO);
        result.setData(true);
        return result;
    }

    @ApiOperation("编辑人群包状态")
    @PostMapping(value = "/crm/crowd/updateStatus")
    public Result<Boolean> updateStatus(@RequestBody CrowdDTO param) {
        Result<Boolean> result = new Result<>();
        crowdService.updateStatus(param);
        result.setData(true);
        return result;
    }
	
	/**
     * 根据ID删除人群包
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除人群包")
	@PostMapping(value = "/crm/crowd/delete")
    public Result<Boolean> deleteCrowdById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        crowdService.deleteCrowdById(param);
        result.setData(true);
        return result;
    }

    @PostMapping("/crm/crowd/import")
    @ApiOperation("导入人群包")
    public Result<CrowdImportResultDTO> employeeImport(@RequestParam(value = "file") MultipartFile file) {
        Result<CrowdImportResultDTO> result = new Result<>();
        CrowdImportResultDTO crowdImportResultDTO = new CrowdImportResultDTO();
        String name = file.getOriginalFilename();
        crowdImportResultDTO.setFileName(name);
        List<CrowdTemplateExcelDTO> excelList = ExcelUtils.excelImport(file, CrowdTemplateExcelDTO.class);
        List<String> list = new ArrayList<>();
        for (CrowdTemplateExcelDTO crowdTemplateExcelDTO : excelList) {
            list.add(crowdTemplateExcelDTO.getMemberCode());
        }
        crowdImportResultDTO.setMemberCodeList(list);
        result.setData(crowdImportResultDTO);
        return result;
    }

    @ApiOperation("下载人群包模板")
    @GetMapping(value = "/crm/crowd/template")
    public void outputList( HttpServletResponse response) throws IOException {
        List<CrowdTemplateExcelDTO> excelDTOS = new ArrayList<>();
        String fileName = URLEncoder.encode("人群包导入模板", StandardCharsets.UTF_8);
        response.setHeader("Content-disposition", "attachment;filename="+fileName+".xlsx");
        response.setHeader("Access-Control-Expose-Headers","Content-disposition");
        EasyExcel.write(response.getOutputStream(), CrowdTemplateExcelDTO.class)
                .sheet("人群包导入模板")
                .doWrite(excelDTOS);
    }
    @ApiOperation("小程序查询用户适用人群包")
    @GetMapping(value = {"/app/crowd/check","/crowd/check"})
    public Result<List<Long>> userHaveCrowd() {
        Result<List<Long>> result = new Result<>();
        List<Long> list = crowdService.userHaveCrowd();
        result.setData(list);
        return result;
    }

    @ApiOperation("根据人群包id校验是否存在")
    @GetMapping(value = "/crowd/isHave")
    public Result<Boolean> crowdIsHave(@RequestParam Long id) {
        Result<Boolean> result = new Result<>();
        boolean isHave = crowdService.crowdIsHave(id);
        result.setData(isHave);
        return result;
    }
}
