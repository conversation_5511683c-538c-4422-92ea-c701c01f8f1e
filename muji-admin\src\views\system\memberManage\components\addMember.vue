<template>
  <a-drawer :title="title" width="800" placement="right" :closable="true" :maskClosable="false" :open="open" @close="onClose">
    <a-spin :spinning="loading">
      <a-form class="form" ref="addForm" :model="addParams" :rules="rules" :labelCol="{ style: 'width:120px' }">
        <a-form-item label="用户姓名" name="realname">
          <a-input v-model:value="addParams.realname" style="width:400px" placeholder="请输入用户姓名" allow-clear show-count :maxlength="12" />
        </a-form-item>
        <a-form-item label="登录账号" name="username">
          <a-input placeholder="请输入账号" :disabled="disabled" style="width:400px" v-model:value="addParams.username" allow-clear show-count :maxlength="20" />
        </a-form-item>
        <a-form-item label="联系电话 " name="mobile" v-if="!addParams.id">
          <a-input placeholder="请输入联系电话" style="width:400px" v-model:value="addParams.mobile" allow-clear />
        </a-form-item>
        <a-form-item label="角色操作权限" name="roleId">
          <a-radio-group style="width:100%" v-model:value="addParams.roleId">
            <a-row>
              <a-col :span="11" class="member-role" :key="index" v-for="(item, index) in roleList">
                <a-radio :value="item.id">
                  {{ item.roleName }}
                </a-radio>
                <a-button type="link" @click="lookDetail(item)">
                  点击查看详情
                </a-button>
              </a-col>
            </a-row>
          </a-radio-group>
        </a-form-item>

      </a-form>
    </a-spin>
    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="ok" :loading="loading">确定</a-button>
      </a-space>
    </template>

  </a-drawer>
  <addRole :visible="RoleShow" @ok="updateList" @cancel="RoleShow = false" :id="RoleId" :type="RoleType" />
</template>
<script setup>
import { roleListNopage, addUser, updateUser } from '@/http/index.js'
import { useGlobalStore } from '@/store'
import { message, Modal } from "ant-design-vue";
import addRole from '../../roleManage/components/addRole.vue'
const $router = useRouter();
const global = useGlobalStore()
const addForm = ref(null)
import { cloneDeep } from 'lodash'
import { checkPassword, checkUserName } from '@/utils/validate.js'
import { onMounted } from 'vue';
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  // 操作抽屉打开关闭
  visible: {
    type: Boolean,
    default: false
  },
  // 数据ID
  id: {
    type: String || Number,
    default: ''
  },
  // 操作类型
  type: {
    type: Number,
    default: 0, // 0-新增 1-编辑  2-查看
  }

})
// 置灰
const disabled = computed(() => {
  return props.type == 1
})

// 标题
const title = computed(() => {
  return ['新建', '编辑', '查看'][props.type] + '成员'
})

const { open, addParams, rules, loading, roleList, RoleShow, RoleId, RoleType } = toRefs(reactive({
  open: props.visible,
  RoleShow: false,
  roleList: [],
  loading: false,
  RoleId: '',
  RoleType: 1,
  addParams: {
    // id: '',
    realname: '',
    username: '',
    mobile: '',
    roleId: ''
  },
  rules: {
    realname: [{ required: true, message: '请输入用户姓名', trigger: ['blur', 'change'] }],
    mobile: [{ required: true, message: '请输入联系电话', trigger: ['blur', 'change'] }],
    username: [{ required: true, message: '请输入账号', trigger: ['blur', 'change'] }, { validator: checkUserName, trigger: ['blur', 'change'] }],
    roleId: [{ required: true, message: '请选择角色', trigger: ['blur', 'change'] }],
  }
})
);

// 暴露给父组件的属性和方法
defineExpose({
  addParams
})

watch(() => props.visible, (value) => {
  // 弹窗操作
  open.value = props.visible
  addForm.value?.resetFields()
  if (value) {

    initData()
  }
})
//所有接口调取出
const initData = async () => {
  const promiseArr = []
  promiseArr.push(roleListNopage())
  // 如果有新的接口就在这里push
  loading.value = true
  try {
    const [roleRes] = await Promise.all(promiseArr)
    roleList.value = roleRes.data
    loading.value = false
  } catch (error) {

    console.error('获取数据失败:', error)
  }
}


const lookDetail = (item) => {
  console.log(item);
  RoleShow.value = true
  RoleId.value = item.id

}

function updateList() {
  RoleShow.value = false
}

// 关闭弹窗
const onClose = () => {
  emit('cancel')
}

// 确定
const ok = () => {

  addForm.value.validate().then(res => {

    let params = cloneDeep(addParams.value)
    loading.value = true
    if (props.id) {
      // console.log('编辑');
      updateUser(params).then(res => {
        message.success(res.msg);
        emit('ok', props.id)
      }).finally(() => {
        loading.value = false
      })
    } else {
      addUser(params).then(res => {
        message.success(res.msg);
        emit('ok', props.id)
      }).finally(() => {
        loading.value = false
      })
    }

  })
}



</script>

<style scoped lang="scss">
:deep(.searchForm .ant-form-item) {
  margin-bottom: 16px;
}

:deep(.anticon-eye-invisible) {
  display: none;
}

.member-role {
  background: #f2f2f2;
  margin-bottom: 20px;
  padding: 5px 10px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &:nth-child(odd) {
    margin-right: 35px;
  }
}
</style>
