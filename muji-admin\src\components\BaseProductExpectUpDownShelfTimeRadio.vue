<template>
  <a-form-item :label="props.label" :name="props.name" :rules="props.rules" class="ParentWrap">
    <a-radio-group v-model:value="thisFields.value" @change="thisMethods.change" v-bind="$attrs">
      <template v-for="(item,index) in thisFields.options" :key="index">
        <a-radio-button :value="item.value">{{ item.label }}</a-radio-button>
      </template>
    </a-radio-group>
  </a-form-item>
</template>

<script setup>
import { onMounted, reactive, watch } from 'vue'
import { PRODUCT_EXPECT_UP_DOWN_SHELF_TIME_ARR } from '@/utils/constants.js'

const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  name: {
    type: String,
    default: ''
  },
  rules: {
    type: Object,
    default: () => undefined
  },
  modelValue: {
    type: [String, Number],
    default: () => ''
  }
})
const emits = defineEmits(['change', 'update:modelValue'])

const attrs = useAttrs()
const thisFields = reactive({
  value: '',
  options: PRODUCT_EXPECT_UP_DOWN_SHELF_TIME_ARR
})
const thisMethods = {
  setValue() {
    thisFields.value = props.modelValue
  },
  change(e) {
    // console.log(e, thisFields.value)
    emits('update:modelValue', thisFields.value)
    emits('change', thisFields.value)
  }
}

onMounted(() => thisMethods.setValue())
watch(() => props.modelValue, () => thisMethods.setValue())
</script>

<style lang="scss" scoped>
.ParentWrap {
}
</style>
