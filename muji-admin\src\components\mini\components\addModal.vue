<template>
  <a-form-item label="开启自动弹窗" :labelCol="{ width:'130px' }">
    <a-switch :unCheckedValue="0" :checkedValue="1" v-model:checked="addParams.autoModal" />
  </a-form-item>
  <template v-if="addParams.autoModal">
    <a-form-item label="展示频次">
      <a-radio-group v-model:value="addParams.openModalType" button-style="solid" size="small">
        <a-radio-button :value="1">每次展示</a-radio-button>
        <a-radio-button :value="3">每天1次</a-radio-button>
        <a-radio-button :value="2">永久1次</a-radio-button>
      </a-radio-group>
    </a-form-item>
    <addLink type="2" :maxCondition="5" :isEvent="false" :links="addParams.modalLinks" :showType="[3]" @ok="(link)=>addParams.modalLinks=link">
      <a-button block>+ 弹窗设置</a-button>
    </addLink>
  </template>

</template>
<script setup>
const emit = defineEmits(['ok', 'cancel'])
import { v4 as uuidv4 } from 'uuid'

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  addParams: {
    type: Object,
    default() {
      return {}
    }
  },
})
const { activeKey, activeKey1 } = toRefs(reactive({
  activeKey: ['1'],
  activeKey1: ['1-1']
}))


// 添加弹窗设置
const addModalSet = () => {
  props.addParams.modalList.push({
    id: uuidv4(),
    openType: 1,
    imgLinks: []
  })
}

// 删除弹窗
const deleteModal = (i) => {
  props.addParams.modalList.splice(i, 1)
}



</script>

<style scoped lang="scss">
.modal {
  &-item {
    padding: 10px;
    margin-bottom: 10px;
    background: #efefef;
    position: relative;
  }
  &-title {
    font-weight: bold;
    margin-bottom: 10px;
  }
  &-delete {
    position: absolute;
    padding: 10px;
    right: 0;
    top: 0;
  }
}
</style>
