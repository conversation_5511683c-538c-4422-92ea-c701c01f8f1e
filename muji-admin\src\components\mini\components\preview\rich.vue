<template>
  <div class="rich" :style="{width:width/2+'px',height:auto,background:data.bgColor,fontColor:data.fontColor}">
    <div class="rich-content" :style="{
      borderRadius:data.borderRadius/2+'px',
      paddingLeft:data.paddingLeft/2+'px',
      paddingRight:data.paddingRight/2+'px',
      paddingTop:data.paddingTop/2+'px',
      paddingBottom:data.paddingBottom/2+'px',
    }" v-html="handlePx(data.content)">
    </div>
  </div>
</template>
<script setup>
const emit = defineEmits(['ok', 'cancel', 'changeActive'])
import { cloneDeep } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { computed } from 'vue';
import customBg from './customBg.vue'
const props = defineProps({
  width: {
    type: Number,
    default: 750,
  },
  data: {
    type: Object,
    default() {
      return {}
    }
  },
})


// 处理富文本中的像素
const handlePx = (content) => {
  // 处理字体大小
  var regex = /(?<=font-size:\s*)\d+(?=px)/g;
  content = content.replace(regex, fontSize => {
    return (Number(fontSize) / 2).toFixed(2)
  });
  // 处理pt单位
  var regex = /(?<=\s*)\d+pt/g;
  content = content.replace(regex, pt => {
    return (Number(pt.slice(0, -2)) / 2).toFixed(2) + 'px'
  });

  // 处理 ul 边距
  var regex = /<ul>/g;
  content = content.replace(regex, pt => {
    return '<ul style="padding-left:20px">'
  });

  // 处理图片的宽和高 width="233"
  var width = /(?<=width=")\d+(?=")/g;
  content = content.replace(width, fontSize => {
    return (Number(fontSize) / 2).toFixed(2)
  });
  var height = /(?<=height=")\d+(?=")/g;
  content = content.replace(height, fontSize => {
    return (Number(fontSize) / 2).toFixed(2)
  });
  return content;
}


</script>

<style scoped lang="scss">
.rich {
  position: relative;
}
</style>
