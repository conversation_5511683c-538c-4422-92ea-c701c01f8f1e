package com.dz.ms.user.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.enums.ColumnType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 类注释
 *
 * @AUTHOR 倗诚
 * @DATE 2023/7/16 17:26
 * @VERSION 1.0.0
 */
@Data
public class EmployeeImportExcelDTO {

    @ExcelProperty("姓名")
    private String empName;
    @ExcelProperty("导购编码")
    private String empCode;
    @ExcelProperty("手机号")
    private String mobile;
    @ExcelProperty("企微号")
    private String qwCode;
    @ExcelProperty("是否店长")
    private Integer isManager;

    @ExcelProperty("专柜")
    private String storeNames;

}
