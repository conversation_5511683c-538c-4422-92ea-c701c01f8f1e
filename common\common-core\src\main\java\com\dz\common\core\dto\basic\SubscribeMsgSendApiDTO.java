package com.dz.common.core.dto.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 发送小程序订阅消息参数
 * <AUTHOR>
 * @date 2022/2/3 11:27
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
@ApiModel(value = "小程序订阅消息发送入参")
public class SubscribeMsgSendApiDTO implements Serializable {

    private static final long serialVersionUID = 1824399519809149429L;

    @ApiModelProperty(value = "消息编码")
    private String msgCode;

    @ApiModelProperty(value = "接收者openid")
    private List<String> openids;

    @ApiModelProperty(value = "跳转页面路径+参数，仅限本小程序内的页面")
    private String pageUrl;

    @ApiModelProperty(value = "模板内容数组 数组中值可为null但顺序一定要和模板字段顺序一致")
    private String[] content;

}
