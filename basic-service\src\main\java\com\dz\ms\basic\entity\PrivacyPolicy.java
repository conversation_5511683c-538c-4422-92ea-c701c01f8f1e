package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 隐私条款
 * @author: Handy
 * @date:   2023/05/17 18:05
 */
@Getter
@Setter
@NoArgsConstructor
@Table("隐私条款")
@TableName(value = "privacy_policy")
public class PrivacyPolicy implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 11, isNull = false, comment = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = true, comment = "隐私条款名称")
    private String name;
    @Columns(type = ColumnType.VARCHAR, length = 32, isNull = true, comment = "隐私条款标题")
    private String policyTitle;
    @Columns(type = ColumnType.VARCHAR, length = 32, isNull = true, comment = "更新标题")
    private String newTitle;
    @Columns(type = ColumnType.VARCHAR, length = 32, isNull = true, comment = "隐私条款版本")
    private String policyVersion;
    @Columns(type = ColumnType.TEXT, length = 0, isNull = true, comment = "隐私条款内容")
    private String content;
    @Columns(type = ColumnType.INT, length = 11, isNull = true, comment = "关联自定义")
    private Integer urlSum;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "发布时间")
    private Date pushTime;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = false, defaultValue = "0", comment = "启停用状态 0启用 1停用")
    private Integer status;
    @Columns(type = ColumnType.BIGINT, length = 11, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT, length = 11, isNull = false, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT, length = 11, isNull = true, comment = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;


    public PrivacyPolicy(Long id, String name, String policyTitle, String newTitle, String policyVersion, String content, Integer urlSum, Date pushTime, Integer status) {
        this.id = id;
        this.name = name;
        this.policyTitle = policyTitle;
        this.newTitle = newTitle;
        this.policyVersion = policyVersion;
        this.content = content;
        this.urlSum = urlSum;
        this.pushTime = pushTime;
        this.status = status;
    }
}
