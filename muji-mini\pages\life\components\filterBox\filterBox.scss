.filter-box {
  // border-bottom: 1px solid #eee;
  position: relative;
  // z-index: 999999;
  background-color: #fff;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;

  .filter-tab {
    margin-top: 43rpx;
    position: relative;

    .tab-list {
      overflow-x: scroll;
      white-space: nowrap;

      .tab-item {
        padding: 0 35rpx;
        box-sizing: border-box;
        height: 60rpx;
        line-height: 60rpx;
        border-radius: 5rpx;
        background-color: #F6F6F6;
        display: inline-flex;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #3C3C43;
        font-style: normal;
        margin-right: 20rpx;
        position: relative;

        .tab-text {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .iconfont {
          position: relative;
        }

      }

      .active {
        background-color: #3C3C43;
        color: #fff;
        font-weight: 500;
      }
    }
  }

  .filter-other {
    // height: 113rpx;
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;
    margin-top: 40rpx;
    margin-bottom: 30rpx;
    height: 33rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #3C3C43;
    line-height: 33rpx;
    text-align: left;
    font-style: normal;

    .filter-btn {
      display: flex;
      position: relative;


      .btn-item {
        display: flex;
        align-items: center;
        gap: 10rpx;

        .checked-box {
          width: 24rpx;
          height: 24rpx;
          border: 2rpx solid #888888;
          border-radius: 50%;
          display: inline-block;
          font-size: 16rpx;
          line-height: 24rpx;
          text-align: center;
        }

        .active {
          color: #fff;
          background-color: black;
          border: 0;
        }
      }
    }

  }

  .dialog-content {
    padding: 0 40rpx 40rpx 40rpx;
    position: sticky;

    .filter-diaglog-box {
      border-top: 1px solid #eee;
      padding-top: 40rpx;
      box-sizing: border-box;
      position: relative;

      .dialog-title {
        height: 40rpx;
        font-family: PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 28rpx;
        color: #3C3C43;
        line-height: 40rpx;
        letter-spacing: 1px;
        text-align: left;
        font-style: normal;
        margin-bottom: 30rpx;
      }
    }

    .filter-list-box {
      display: flex;
      align-items: center;
      gap: 20rpx;
      flex-wrap: wrap;
      width: 670rpx;
    }

    .btn-box {
      display: flex;
      justify-content: space-between;
      margin-top: 60rpx;
    }

    .dialog-item {
      padding: 0 35rpx;
      height: 60rpx;
      line-height: 60rpx;
      color: #3C3C43;
      background: #F6F6F6;
      border-radius: 5rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 24rpx;
      line-height: 60rpx;
      text-align: center;
      font-style: normal;
    }

    .active {
      background-color: #3C3C43;
      color: #fff;
    }
  }

  // .red-font {
  //   color: red;
  // }
}
