package com.dz.ms.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.order.dto.BookingRecordDTO;
import com.dz.ms.order.entity.BookingRecord;
import com.dz.ms.order.mapper.BookingRecordMapper;
import com.dz.ms.order.service.BookingRecordService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * 预约记录
 * @author: Handy
 * @date:   2024/06/06 14:47
 */
@Service
public class BookingRecordServiceImpl extends ServiceImpl<BookingRecordMapper,BookingRecord> implements BookingRecordService {

	@Resource
    private BookingRecordMapper bookingRecordMapper;

	/**
     * 分页查询预约记录
     * @param param
     * @return PageInfo<BookingRecordDTO>
     */
    @Override
    public PageInfo<BookingRecordDTO> getBookingRecordList(BookingRecordDTO param) {
        BookingRecord bookingRecord = BeanCopierUtils.convertObjectTrim(param,BookingRecord.class);
        IPage<BookingRecord> page = bookingRecordMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), new QueryWrapper<>(bookingRecord));
        return new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),BeanCopierUtils.convertList(page.getRecords(), BookingRecordDTO.class));
    }

    /**
     * 根据ID查询预约记录
     * @param id
     * @return BookingRecordDTO
     */
    @Override
    public BookingRecordDTO getBookingRecordById(Long id) {
        BookingRecord bookingRecord = bookingRecordMapper.selectById(id);
        return BeanCopierUtils.convertObject(bookingRecord,BookingRecordDTO.class);
    }

    /**
     * 保存预约记录
     * @param param
     * @return Long
     */
    @Override
    public Long saveBookingRecord(BookingRecordDTO param) {
        BookingRecord bookingRecord = new BookingRecord(param.getId(), param.getRecordCode(), param.getUid(), param.getUserName(), param.getGender(), param.getMobile(), param.getCardLevel(), param.getBookingId(), param.getBookingName(), param.getBookingImg(), param.getBookingDate(), param.getTimeSlot(), param.getBookingTimeStart(), param.getBookingTimeEnd(), param.getVerifyTimeStart(), param.getVerifyTimeEnd(), param.getItemType(), param.getItemId(), param.getItemName(), param.getItemIntroduction(), param.getStoreCode(), param.getStoreName(), param.getEmpCode(), param.getVerifyStoreCode(), param.getVerifyStoreName(), param.getVerifierType(), param.getVerifierCode(), param.getVerifierName(), param.getState(), param.getMsgState(), param.getStateTime(), param.getUpdateNum(), param.getEmpBooking(), param.getConsumeType());
        if(ParamUtils.isNullOr0Long(bookingRecord.getId())) {
            bookingRecordMapper.insert(bookingRecord);
        }
        else {
            bookingRecordMapper.updateById(bookingRecord);
        }
        return bookingRecord.getId();
    }

    /**
     * 根据ID删除预约记录
     * @param param
     */
    @Override
    public void deleteBookingRecordById(IdCodeDTO param) {
        bookingRecordMapper.deleteById(param.getId());
    }
	
}