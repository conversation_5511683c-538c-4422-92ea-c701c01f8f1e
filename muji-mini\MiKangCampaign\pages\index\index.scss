/* MiKangCampaign/pages/index/index.wxss */
.page-container {
  background-color: #F8F6ED;
  .page-rule {
    //position: absolute;
    z-index: 10;

    position: fixed;
    right: 0rpx;
    top: 342rpx;
    width: 58rpx;
    height: 160rpx;
    background: #ECD8BA66;
    font-family: MUJIFont2020;
    font-weight: 700;
    font-size: 28rpx;
    color: #FFFFFF;
    line-height: 32rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    writing-mode: vertical-rl;
    letter-spacing: 4rpx;
    border-radius: 16rpx 0rpx 0rpx 16rpx;
  }

  .page-zhongjiang {
    z-index: 10;

    position: fixed;
    right: 0rpx;
    top: 392rpx;
    width: 58rpx;
    height: 160rpx;
    background: #ECD8BA66;
    font-family: MUJIFont2020;
    font-weight: 700;
    font-size: 28rpx;
    color: #FFFFFF;
    line-height: 32rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    writing-mode: vertical-rl;
    letter-spacing: 4rpx;
    border-radius: 16rpx 0rpx 0rpx 16rpx;
  }

  .page-header {
    display: block;
    width: 100vw;
    height: 706rpx;
  }

  .page-all {
    display: block;
    width: 100vw;
    // height: 100vh;
    position: absolute;
    left: 0rpx;
    top: 0rpx;
  }

  .page-content {
    position: relative;
    // pointer-events: none;
    // margin-top: 71rpx;
    // margin-left: 86rpx;
    width: 100vw;
    // height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    &-img {
      width: 750rpx;
      height: auto;
    }

    &-tip {
      position: absolute;
      left: 54rpx;
      top: -30rpx;
      line-height: 29rpx;
      padding: 0 20rpx;
      background: #C8B49A;
      font-family: MUJIFont2020;
      font-weight: 500;
      font-size: 20rpx;
      color: #FFFFFF;
      border-radius: 23rpx;
      letter-spacing: 2rpx;

      &::after {
        content: '';
        position: absolute;
        bottom: -14rpx;
        left: 30rpx;
        width: 0;
        height: 0;
        border-right: 10rpx solid transparent;
        border-left: 10rpx solid transparent;
        border-top: 16rpx solid #C8B49A;
        /* 指向左边的颜色 */
      }
    }
  }

  .bg-image {
    width: 100%;
    height: auto; /* mode="widthFix" 自动调整高度 */
    display: block; /* 确保图片块级显示 */
  }

  .card-box {
    position: absolute; /* 绝对定位，相对于 .page-content */
    left: 0;
    right: 0;
    bottom: 0; /* 固定在底部 */
    top: 60%; /* 从容器高度的50%开始，覆盖下半部分 */
    display: flex;
    flex-direction: column;
    justify-content: flex-start; /* 内容从顶部开始排列 */
    align-items: center;
    z-index: 10; /* 确保在背景图片之上 */
  }

  .bottom-box {
    z-index: 100;
    position: fixed;
    bottom: 92rpx;
    display: flex;
    justify-content: center;
  }

  .registrationSuccessful {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    z-index: 1;
    height: 100vh;
    //flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
  }
}
