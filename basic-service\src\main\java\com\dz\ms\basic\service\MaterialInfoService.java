package com.dz.ms.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.dto.BaseDTO;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.basic.MaterialRelationParamDTO;
import com.dz.ms.basic.dto.MaterialInfoDTO;
import com.dz.ms.basic.dto.MaterialSimpleDTO;
import com.dz.ms.basic.entity.MaterialInfo;

import java.util.List;

/**
 * 素材信息接口
 * @author: Handy
 * @date:   2022/2/4 17:39
 */
public interface MaterialInfoService extends IService<MaterialInfo> {

    /**
     * 根据ID列表删除素材信息
     * @param ids
     */
    void deleteMaterialByIds(List<Long> ids);

    /**
     * 根据ID列表更新素材分类
     * @param param
     */
    void updateMaterialGroupByIds(MaterialInfoDTO param);

    /**
     * 根据ID列表更新素材过期时间
     * @param param
     */
    void updateMaterialTimeByIds(MaterialInfoDTO param);

    /**
     * 根据素材id列表获取素材地址列表
     * @param ids
     * @return
     */
    List<MaterialSimpleDTO> getMaterialUrlByIds(List<Long> ids,Long tenantId);

    /**
     * 保存素材关联业务信息
     * @param param
     */
    void saveMaterialRelation(MaterialRelationParamDTO param);

    /**
     * 批量保存素材关联业务信息
     * @param list
     */
    void saveBatchMaterialRelation(List<MaterialRelationParamDTO> list);

    /**
     * 分页查询即将过期和已过期素材信息
     * @param param
     * @return
     */
    PageInfo<MaterialInfoDTO> getMaterialExpireList(BaseDTO param);

    /**
     * 生效达到生效日期的替换素材
     */
    void effectiveReplaceUrl(Long tenantId);

    /**
     * 批量新增素材信息
     * @param list
     */
    void addBatch(List<MaterialInfoDTO> list);

    /**
     * 批量更新素材信息
     * @param list
     */
    void updateBatch(List<MaterialInfoDTO> list);

}
