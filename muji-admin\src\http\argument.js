// 所有的命名必须全局唯一
import service from '@/utils/request.js'

//



// 列表
export function promotion_channelList(data) {
    return service({
        url: '/crm/basic/promotion_channel/list',
        method: 'get',
        data
    })
}


// 列表编辑
export function promotion_channelUpdate(data) {
    return service({
        url: '/crm/basic/promotion_channel/update',
        method: 'post',
        data
    })
}
// 列表新增
export function promotion_channelAdd(data) {
    return service({
        url: '/crm/basic/promotion_channel/add',
        method: 'post',
        data
    })
}

// 列表新增
export function promotion_channelDel(data) {
    return service({
        url: '/crm/basic/promotion_channel/delete',
        method: 'post',
        data
    })
}

// 推广 装修

export function basicpromotion_pagelist(data) {
    return service({
        url: '/crm/basic/promotion_page/list',
        method: 'get',
        data
    })
}
// /crm/basic/promotion_channel/channelList  不分页渠道列表
export function promotion_channelchannelList(data) {
    return service({
        url: '/crm/basic/promotion_channel/channelList',
        method: 'get',
        data
    })
}

//  装修推广新增
export function promotion_pageAdd(data) {
    return service({
        url: '/crm/basic/promotion_page/add',
        method: 'post',
        data
    })
}
// 
export function promotion_pageupdate(data) {
    return service({
        url: '/crm/basic/promotion_page/update',
        method: 'post',
        data
    })
}
// 

export function promotion_pagegetChannelList(data) {
    return service({
        url: '/crm/basic/promotion_page/getChannelList',
        method: 'get',
        data
    })
}
// 查询一级渠道
export function promotion_channeloneChannelList(data) {
    return service({
        url: '/crm/basic/promotion_channel/oneChannelList',
        method: 'get',
        data
    })
}