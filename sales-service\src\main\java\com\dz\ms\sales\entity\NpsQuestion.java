package com.dz.ms.sales.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/16
 */
@Getter
@Setter
@NoArgsConstructor
@Table("NPS问卷问题")
@TableName(value = "nps_question")
public class NpsQuestion implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "问题ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "问卷ID",isIndex = true)
    private Long npsId;
    @Columns(type = ColumnType.VARCHAR,length = 200,isNull = true,comment = "问题名称")
    private String title;
    @Columns(type = ColumnType.VARCHAR,length = 200,isNull = true,comment = "副标题")
    private String subTitle;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = false, comment = "问题类型 1单选题 2多选题 3问答题 4打分题 5拍照题")
    private Integer questionType;
    @Columns(type = ColumnType.VARCHAR,length = 200,isNull = true,comment = "问答题提示")
    private String questionTips;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "最高分值")
    private Integer maxScore;
    @Columns(type = ColumnType.VARCHAR,length = 10,isNull = true,comment = "最小分值文案")
    private String minScoreText;
    @Columns(type = ColumnType.VARCHAR,length = 10,isNull = true,comment = "最大分值文案")
    private String maxScoreText;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "拍照题最小照片数量")
    private Integer minPhotoNum;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "拍照题最大照片数量")
    private Integer maxPhotoNum;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = false, comment = "是否必填 0否 1是",defaultValue = "1")
    private Integer isMust;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, comment = "问题排序，正序",defaultValue = "0")
    private Integer sort;

    @Columns(type = ColumnType.VARCHAR,length = 200,isNull = true,comment = "别名")
    private String alias;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = false,defaultValue = "0",comment = "是否删除 0未删除 1已删除")
    @TableLogic
    private Integer isDeleted;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = true,comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    public NpsQuestion(Long id, Long npsId, String title, Integer questionType, String questionTips, Integer maxScore, String minScoreText, String maxScoreText, Integer minPhotoNum, Integer maxPhotoNum, Integer isMust, Integer sort) {
        this.id = id;
        this.npsId = npsId;
        this.title = title;
        this.questionType = questionType;
        this.questionTips = questionTips;
        this.maxScore = maxScore;
        this.minScoreText = minScoreText;
        this.maxScoreText = maxScoreText;
        this.minPhotoNum = minPhotoNum;
        this.maxPhotoNum = maxPhotoNum;
        this.isMust = isMust;
        this.sort = sort;
    }
}
