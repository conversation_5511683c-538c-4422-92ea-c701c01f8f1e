package com.dz.common.core.utils;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.encrypt.api.encrypt.standard.StandardEncryptAlgorithm;
import org.apache.shardingsphere.encrypt.spi.EncryptAlgorithm;
import org.apache.shardingsphere.encrypt.spi.context.EncryptContext;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Properties;

@Data
@Slf4j
@Service
public class AesMysqlEncryptor implements StandardEncryptAlgorithm<Object, String> {

    private Properties properties = new Properties();

    @Override
    public void init(Properties properties) {
        if (properties != null) {
            this.properties = properties;  // 初始化加密需要的属性
        }
    }

    /**
     * 加密
     *
     * @param o
     * @return
     */
    public String encrypt(Object o, EncryptContext encryptContext) {
        log.info("数据加密原始数据:" + o);
        String encryptStr = null;
        try {
            encryptStr = AesMysqlUtils.encrypt(o.toString());
        } catch (Exception e) {
            log.error("数据加密失败", o, e);
            encryptStr = o.toString();
        }
        log.info("数据加密后数据:" + encryptStr);
        return encryptStr;
    }

    /**
     * 解密
     *
     * @param decryptStr
     * @return
     */
    public Object decrypt(String decryptStr, EncryptContext encryptContext) {
//        log.info("数据解密前数据:" + decryptStr);
        try {
            decryptStr = AesMysqlUtils.decrypt(decryptStr);
        } catch (Exception e) {
            log.error("数据解密失败", decryptStr, e);
        }
//        log.info("数据解密后数据:" + decryptStr);
        return decryptStr;
    }

    /**
     * 类型
     *
     * @return
     */
    @Override
    public String getType() {
        return "AES_MYSQL";
    }

    public static void main(String[] args) {
        jiami();
        jiemi();
//        for (int i = 0; i < 32; i++) {
//            String str = "select count(1) from coupon_user_" + i + "  where unionid in ('omryFtyjPdPv0zlpZrOU7L2CdPIY');";
//        }
    }

    public static void jiami(){
        List<String> list = Arrays.asList("13506411355");
        AesMysqlEncryptor aesMysqlEncryptor = new AesMysqlEncryptor();
        list.forEach(e -> aesMysqlEncryptor.encrypt(e, null));
    }

    public static void jiemi(){

        AesMysqlEncryptor aesMysqlEncryptor = new AesMysqlEncryptor();
        aesMysqlEncryptor.decrypt("95PHcs2sNv5GjTC16+t+YQ==", null);
    }



}
