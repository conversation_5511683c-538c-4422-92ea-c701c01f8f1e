package com.dz.ms.product.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 货架商品保存入参
 *
 * @author: fei
 * @date: 2024/11/25 16:25
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@ApiModel(value = "货架商品保存入参")
public class ShelfProductSaveDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "货架名称")
    private String shelfName;
    @ApiModelProperty(value = "商品ID")
    private Long productId;
    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "商品在货架上的位置 越大越靠后")
    private Integer onShelfIndex;
    @ApiModelProperty(value = "发货方式 1线下使用 2邮寄")
    private Integer deliveryType;
    @ApiModelProperty(value = "是否展示 0不展示 1展示")
    private Integer beShow;
    @ApiModelProperty(value = "展示方式 1积分加价购 2商品兑换")
    private Integer showType;
    @ApiModelProperty(value = "上架库存")
    private Integer onInventory;
    @ApiModelProperty(value = "目前库存")
    private Integer currentInventory;
    @ApiModelProperty(value = "兑换积分")
    private Integer costPoint;
    @ApiModelProperty(value = "积分划线价")
    private Integer prePoint;
    @ApiModelProperty(value = "默认限购数量/月")
    private Integer limitNum;
    @ApiModelProperty(value = "角标名称列表")
    private List<Long> superscriptIdList;

    private Integer pdType;

}
