package com.dz.ms.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.IdNumberDTO;
import com.dz.ms.product.dto.ShelfCampaignDTO;
import com.dz.ms.product.dto.req.ShelfCampaignParamDTO;
import com.dz.ms.product.dto.req.ShelfCampaignSaveParamDTO;
import com.dz.ms.product.entity.ShelfCampaign;

import java.util.List;
import java.util.Map;

/**
 * 货架营销活动接口
 *
 * @author: LiinNs
 * @date: 2024/11/20 11:39
 */
public interface ShelfCampaignService extends IService<ShelfCampaign> {

    /**
     * 分页查询货架营销活动
     *
     * @param param
     * @return PageInfo<ShelfCampaignDTO>
     */
    PageInfo<ShelfCampaignDTO> getShelfCampaignList(ShelfCampaignParamDTO param);

    /**
     * 根据ID查询货架营销活动
     *
     * @param id        id
     * @param isThrow   1:抛异常
     * @return ShelfCampaignDTO
     */
    ShelfCampaignDTO getShelfCampaignById(Long id, Integer isThrow);

    /**
     * 根据货架ID列表查询货架营销活动列表
     * @param shelfIds   shelfIds
     * @param isQryProductNum 是否查询商品数量
     * @return List<ShelfCampaignDTO>
     */
    List<ShelfCampaignDTO> getShelfCampaignByShelfIds(List<Long> shelfIds, boolean isQryProductNum);

    /**
     * 根据货架ID列表查询货架营销活动列表
     * @param shelfIds   shelfIds
     * @return Map<Long, List<ShelfCampaignDTO>>
     */
    Map<Long, List<ShelfCampaignDTO>> getShelfCampaignMap(List<Long> shelfIds);

    /**
     * 保存货架营销活动
     *
     * @param param
     * @return Long
     */
    Long saveShelfCampaign(ShelfCampaignSaveParamDTO param, boolean isAdd);

    /**
     * 根据ID删除货架营销活动
     *
     * @param param
     */
    void deleteShelfCampaignById(IdCodeDTO param);

    /**
     * 根据ID修改启停状态
     *
     * @param param ID NUMBER 通用DTO
     */
    void updateStateById(IdNumberDTO param);

    /**
     * 根据货架id修改关联货架id为null
     *
     * @param shelfId 货架id
     */
    void updShelfIdIntoNull(Long shelfId);

    void exportList(DownloadAddParamDTO exportParam);
}
