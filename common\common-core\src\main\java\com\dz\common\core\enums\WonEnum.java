package com.dz.common.core.enums;

import java.util.Objects;

/**
 * Describe：状态类型枚举
 * Created by 吴蜀黍 on 2023/8/20 8:36
 **/
public enum WonEnum {
    NOT_WON(0, "未抽中"),
    WON(1, "抽中"),
    ;

    private final Integer code;
    private final String value;

    WonEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (WonEnum resultEnum : WonEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum.value;
            }
        }
        return null;
    }
}
