package com.dz.ms.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.core.constants.CommonConstants;
import com.dz.common.core.service.RedisService;
import com.dz.ms.user.constants.CacheKeys;
import com.dz.ms.user.dto.RolePermissionDTO;
import com.dz.ms.user.entity.OmsPermission;
import com.dz.ms.user.entity.OmsRole;
import com.dz.ms.user.entity.OmsRolePermission;
import com.dz.ms.user.mapper.OmsPermissionMapper;
import com.dz.ms.user.mapper.OmsRoleMapper;
import com.dz.ms.user.mapper.OmsRolePermissionMapper;
import com.dz.ms.user.service.OmsPermissionService;
import com.dz.ms.user.service.OmsRoleService;
import com.dz.common.core.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * OMS-系统角色
 * @author: Handy
 * @date:   2020/01/29 19:15
 */
@Service
public class OmsRoleServiceImpl extends ServiceImpl<OmsRoleMapper,OmsRole> implements OmsRoleService {

    @Resource
    private OmsPermissionService omsPermissionService;

    @Resource
    private OmsPermissionMapper omsPermissionMapper;

    @Resource
    private OmsRolePermissionMapper omsRolePermissionMapper;

    @Resource
    private RedisService redisService;

    /**
     * 获取角色权限列表
     * @param roleId
     * @return
     */
    @Override
    public List<String> getRolePermitCodes(Long roleId) {
        if(null == roleId) {
            return null;
        }
        if(roleId.equals(0L)) {
            return omsPermissionService.getAllPermissionCodes();
        }
        String key = CacheKeys.OMS_ROLE_PERMIT_CODES + roleId;
        Object cachList = redisService.get(key);
        if(null != cachList) {
            return (List<String>)cachList;
        }
        List<OmsPermission> rolePermits = omsPermissionMapper.getRolePermitCodes(roleId);
        Set<String> permitCodes = new HashSet<>();
        LambdaQueryWrapper<OmsPermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OmsPermission :: getHasChild,1);
        List<OmsPermission> parentPermits = omsPermissionMapper.selectList(wrapper);
        Map<Long,OmsPermission> parentMap = parentPermits.stream().collect(Collectors.toMap(OmsPermission :: getId, omsPermission -> omsPermission));
        rolePermits.forEach(omsPermission -> {
            permitCodes.add(omsPermission.getCode());
            handelPrentPermitCode(permitCodes,parentMap,omsPermission);
        });
        List<String> list = new ArrayList<>(permitCodes);
        redisService.set(key,list,CommonConstants.DAY_SECONDS);
        return list;
    }

    /**
     * 递归添加父级权限编码
     * @param permitCodes
     * @param parentMap
     * @param omsPermission
     */
    private void handelPrentPermitCode(Set<String> permitCodes,Map<Long,OmsPermission> parentMap,OmsPermission omsPermission) {
        if(omsPermission.getParentId() > 0 && parentMap.containsKey(omsPermission.getParentId())) {
            OmsPermission parentPermission = parentMap.get(omsPermission.getParentId());
            permitCodes.add(parentPermission.getCode());
            handelPrentPermitCode(permitCodes,parentMap,parentPermission);
        }
    }

    /**
     * 获取角色权限ID列表
     * @param roleId
     * @return
     */
    @Override
    public List<Long> getRolePermitIds(Long roleId) {
        return omsPermissionMapper.getRolePermitIds(roleId);
    }

    /**
     * 绑定角色权限
     * @param param
     * @param uid
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(prefix = CacheKeys.OMS_ROLE_PERMIT_CODES, key = "'#param.roleId'")
    public void bindPermit(RolePermissionDTO param, Long uid) {
        List<Long> origIds = new ArrayList<>();
        List<Long> addIds = new ArrayList<>();
        List<Long> deleteIds = new ArrayList<>();
        List<Long> newIds = param.getPermitIds();
        LambdaQueryWrapper<OmsRolePermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OmsRolePermission :: getRoleId,param.getRoleId());
        List<OmsRolePermission> getList = omsRolePermissionMapper.selectList(wrapper);
        if(null != getList) {
            for (OmsRolePermission rolePermission : getList) {
                origIds.add(rolePermission.getPermitId());
            }
        }
        if(CollectionUtils.isEmpty(newIds)) {
            deleteIds = origIds;
        }
        else {
            for (Long id : newIds) {
                if(!origIds.contains(id)) {
                    addIds.add(id);
                }
            }
            for (OmsRolePermission rolePermission : getList) {
                if(!newIds.contains(rolePermission.getPermitId())) {
                    deleteIds.add(rolePermission.getId());
                }
            }
        }
        Date date = new Date();
        for (Long id : addIds) {
            OmsRolePermission rolePermission = new OmsRolePermission();
            rolePermission.setRoleId(param.getRoleId());
            rolePermission.setPermitId(id);
            omsRolePermissionMapper.insert(rolePermission);
        }
        for (Long id : deleteIds) {
            omsRolePermissionMapper.deleteById(id);
        }
    }

}
