package com.dz.ms.sales.dto;


import com.dz.common.core.annotation.Columns;
import com.dz.common.core.enums.ColumnType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/8
 */
@Data
public class SignInRecordDTO {

    @ApiModelProperty("打卡记录")
    private List<SignInUserDetailDTO> list;

    @ApiModelProperty(value = "今日打卡天数")
    private Integer days = 1;

    @ApiModelProperty("今日是否打卡")
    private Boolean isSignIn;

    @ApiModelProperty("剩余补卡次数")
    private Long surplusRepairSignInTimes;

    @ApiModelProperty("完成的次数")
    private Long completeTimes;

    @ApiModelProperty("剩余的次数")
    private Long surplusTimes;

    @ApiModelProperty("打卡活动开始时间")
    private Date startActivityTime;
    @ApiModelProperty("打卡活动结束时间")
    private Date endActivityTime;


//    @ApiModelProperty("打卡开始时间")
//    private Date signInStartTime;
//    @ApiModelProperty("打卡结束时间")
//    private Date signInEndTime;

}
