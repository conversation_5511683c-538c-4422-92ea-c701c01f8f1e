// import dayjs from '../../utils/dayjs.min'
const dayjs = require('../../utils/dayjs.min')
const app = getApp()
import {
	apiPrivacyAgreement,
	updatePolicyVersion
} from '../../api/index'
Component({
	options: {
		// multipleSlots: true,
		addGlobalClass: true,
	},
	properties: {
		loadingTime: {//loading 延迟时间
			type: Number,
			value: 1000,
		},
		// 全局loading
		loading: {
			type: Boolean,
			value: false,
			observer(val) {
				if (val) {
					this.setData({
						loadingInfo: {
							loadingWidth: app.globalData.styleSetting.pageStyle.loadingWidth,
							loadingHeight: app.globalData.styleSetting.pageStyle.loadingHeight,
							loading: app.globalData.styleSetting.pageStyle.loading,
						}
					})
					// 延迟1s之后如果接口还在加载  才展示loading
					if (this.time) {
						clearTimeout(this.time)
						this.time = null
					}
					this.time = setTimeout(() => {
						clearTimeout(this.time)
						this.time = null
						if (this.data.loading) {
							this.setData({
								realLoading: true
							})
						}
					}, this.data.loadingTime);
				} else {
					this.setData({
						realLoading: false,
					})
				}
			}
		},
		// 全局弹窗
		overallModal: {
			type: Object,
			value() {
				return {}
			},
			observer(val) {
				this.getData(val)
			}
		},
		expirePointsNum: {
			type: [String, Number],
			value: null,
			observer(val) {
				this.setData({
					tipsShow: val ? false : true
				})
			}
		}
	},
	data: {
		userInfo: {},
		loadingInfo: {}, // loading展示信息
		realLoading: false,
		selectAll: false,
		ageChecked: false, // 隐私协议——年龄
		allAgreements: false, //所有隐私协议
		isAgreeBrand: false, // 隐私协议——信息
		personalPrivacyPolicy: false, //个人信息隐私
		isShowAgreeBrand: true, // 是否显示隐私协议的品牌信息勾选

		agree1: false,// 服务使用协议
		agree2: false,// 隐私保护指引
		agree3: false,// 个人信息保护
		isTabBarPage: false, // 是否是一级页面
		title: "", // 隐私协议标题
		content: '', // 隐私协议内容
		firstContent: '',// 隐私协议内容
		tipsShow: false,
		showModal: false, // 显示屏蔽层 只允许滑动浏览 不能点击
		move: false,
	},
	attached() {
		this.setData({
			isTabBarPage: wx.$mp.isTabBarPage(),
			autoPrincy: app.globalData.autoPrincy
		})
	},
	pageLifetimes: {
		show() {
			const app = getApp()
			var currentInstance = wx.$mp.getCurrentPage();
			let userInfo = app.globalData.userInfo;
			console.log(userInfo)
			let privacyData = app.globalData.privacyData;
			let needAuthorization = app.globalData.needAuthorization;
			this.setData({
				userInfo,
				privacyData,
				showModal: false
			})
			// 需要弹隐私协议的页面
			if (!wx.$config.noShowPrivicy.some(item => item.includes(currentInstance.path)) && ((userInfo?.policyVersion != privacyData?.policyVersion) || needAuthorization)) {
				this.setData({
					showModal: true
				})
			} else if (!wx.$config.noShowPhone.some(item => item.includes(currentInstance.path))) { // 需要展示绑定手机号的页面
				if (app.globalData.userInfo.isFreeze && !currentInstance.data.overallModal.freeze) { // 用户冻结
					this.setData({
						showModal: true
					})
				} else if (app.globalData.userInfo.isMember > 0 && !app.globalData.userInfo.mobile && !currentInstance.data.overallModal.phone) { // 判断是否会员注册 但是未绑定手机号
					this.setData({
						showModal: true
					})
				}
			}
			this.setData({
				autoPrincy: app.globalData.autoPrincy
			})
		},
	},

	methods: {
		Privacy(e) {
			let {
				type
			} = e.currentTarget.dataset;
			switch (type) {
				case 1:
					wx.$mp.navigateTo({
						url: '/pages/userAgreement/userAgreement',
					})
					break
				case 2:
					wx.$mp.navigateTo({
						url: '/pages/userPrivicy/userPrivicy',
					})
					break
				case 3:
					wx.$mp.openPrivacyContract()
					break
				case 4:
					break
			}
		},
		closeTips() {
			this.setData({
				tipsShow: true
			})
		},
		reveal() {
			var currentInstance = wx.$mp.getCurrentPage();
			let userInfo = app.globalData.userInfo;
			let privacyData = app.globalData.privacyData;
			let needAuthorization = app.globalData.needAuthorization;
			// // 获取当前页面 更新全局弹窗状态
			if (!wx.$config.noShowPrivicy.some(item => item.includes(currentInstance.path)) && ((userInfo?.policyVersion != privacyData?.policyVersion) || needAuthorization)) {
				currentInstance.showOveralModal('secret')
			} else if (!wx.$config.noShowPhone.some(item => item.includes(currentInstance.path))) { // 需要展示绑定手机号的页面
				if (app.globalData.userInfo.isFreeze) { // 用户冻结
					currentInstance.showOveralModal('freeze')
				} else if (app.globalData.userInfo.isMember > 0 && !app.globalData.userInfo.mobile) { // 判断是否会员注册 但是未绑定手机号
					currentInstance.showOveralModal('phone')
				}
			}
			// 关闭遮罩层
			this.setData({
				showModal: false,
			})
		},
		start() {
			this.setData({
				move: true
			})
		},
		end() {
			this.setData({
				move: false
			})
		},
		// 单选框选项更新
		onCheckboxChange(e) {
			let {
				key
			} = e.currentTarget.dataset;
			if (key === 'selectAll') {
				// 全选时，更新所有复选框状态
				const newValue = !this.data.selectAll;
				this.setData({
					selectAll: newValue,
					ageChecked: newValue,
					allAgreements: newValue,
					isAgreeBrand: newValue,
					personalPrivacyPolicy: newValue,
					agree1: newValue,
					agree2: newValue,
					agree3: newValue,
				})
			} else {
				// 其他复选框更新
				const newValue = !this.data[key];
				this.setData({
					[key]: newValue
				})
				console.log(this.data)
				this.getAllC()
			}
		},
		getAllC() {
			let { ageChecked, allAgreements, isShowAgreeBrand, isAgreeBrand, personalPrivacyPolicy, agree1, agree2, agree3, userInfo: { policyVersion }, privacyData: { versionUpdContent } } = this.data;
			// 检查是否所有必选项都选中，更新全选状态
			let allChecked
			if (!policyVersion) {
				allChecked = ageChecked && allAgreements && (!isShowAgreeBrand || isAgreeBrand) && personalPrivacyPolicy
			} else {
				allChecked = ageChecked && (!isShowAgreeBrand || isAgreeBrand) && (versionUpdContent.indexOf('1') > -1 ? agree1 : true) && (versionUpdContent.indexOf('2') > -1 ? agree2 : true) && (versionUpdContent.indexOf('3') > -1 ? agree3 : true)
			}

			this.setData({
				selectAll: allChecked
			})
		},
		// 关闭全局弹窗
		close(e) {
			let {
				key
			} = e.currentTarget.dataset;
			// 获取当前页面 更新全局弹窗状态
			var currentInstance = wx.$mp.getCurrentPage();
			// 隐私协议
			if (key == 'secret') {
				currentInstance.hiddenOveralModal(key) // 关闭弹窗
				this.setData({ showModal: true })
			} else if (key == 'freeze') { // 冻结
				currentInstance.hiddenOveralModal(key) // 关闭弹窗
				this.setData({ showModal: true })
			} else if (key == 'phone') { // 绑定手机号
				currentInstance.hiddenOveralModal(key) // 关闭弹窗
				this.setData({ showModal: true })
			} else { //其他
				currentInstance.hiddenOveralModal(key) // 关闭弹窗
			}
		},
		// 全局弹窗确认按钮
		async confirm(e) {
			let openSetting
			let {
				key
			} = e.currentTarget.dataset;
			let {
				ageChecked,
				isAgreeBrand,
				isShowAgreeBrand,
				allAgreements,
				personalPrivacyPolicy,
				agree1, agree2, agree3,
			} = this.data;
			let { userInfo: { policyVersion }, privacyData: { versionUpdContent } } = app.globalData

			// 获取当前页面 更新全局弹窗状态
			var currentInstance = wx.$mp.getCurrentPage();
			// 隐私协议
			if (key == 'secret') {
				wx.$mp.track({
					event: 'modal_princy_agree'
				})
				// 判断选项是否选中
				// 第一次的判断条件 !policyVersion &&
				if ((!policyVersion && (!ageChecked || !allAgreements || !personalPrivacyPolicy))) {
					wx.showToast({
						title: '请勾选相应政策\n以便我们为您提供服务',
						icon: 'none'
					})
					return
				}
				// 非第一次的判断条件
				let aggres = []
				if (versionUpdContent.indexOf('1') > -1) {
					aggres.push(agree1)
				}
				if (versionUpdContent.indexOf('2') > -1) {
					aggres.push(agree2)
				}
				if (versionUpdContent.indexOf('3') > -1) {
					aggres.push(agree3)
				}
				if (policyVersion && (!ageChecked || aggres.some(item => !item))) {
					wx.showToast({
						title: '请勾选相应政策\n以便我们为您提供服务',
						icon: 'none'
					})
					return
				}
				// 授权小程序api权限
				if (app.globalData.needAuthorization && app.globalData.resolvePrivacyAuthorization) {
					app.globalData.needAuthorization = false
					app.globalData.resolvePrivacyAuthorization({
						buttonId: 'agree-btn',
						event: 'agree'
					})
				}
				// 接口调用更新隐私协议版本 to do
				await updatePolicyVersion({
					policyVersion: app.globalData.privacyData.policyVersion,
					isAgreeBrand: isShowAgreeBrand ? isAgreeBrand * 1 : app.globalData.userInfo.isAgreeBrand
				})
				await app.getUserInfo() // 更新用户隐私协议版本号

				this.setData({
					showModal: false
				})
				app.globalData.autoPrincy = true

				// 跳转到第一次的页面
				if (app.globalData.firstData) {
					let options = app.globalData.firstData.options
					let params = Object.keys(options).reduce((total, key) => {
						total += `${key}=${options[key]}&`
						return total
					}, '')
					console.log('参数', params)
					wx.$mp.redirectTo({
						url: app.globalData.firstData.path + '?' + params,
						success: () => {
							app.globalData.firstData = null
							let time = setTimeout(() => {
								clearTimeout(time)
								currentInstance.hiddenOveralModal(key) // 关闭弹窗
								currentInstance.hiddenOveralModal('yellow') // 关闭弹窗
							}, 5000);
						}
					})
				} else { // 首页判断后续是否有全局弹窗
					currentInstance.hiddenOveralModal(key) // 关闭弹窗
					currentInstance.hiddenOveralModal('yellow') // 关闭弹窗

					if (!wx.$config.noShowPhone.some(item => item.includes(currentInstance.path))) { // 需要展示绑定手机号的页面
						if (app.globalData.userInfo.isFreeze) { // 用户冻结
							currentInstance.showOveralModal('freeze')
						} else if (app.globalData.userInfo.isMember > 0 && !app.globalData.userInfo.mobile) { // 判断是否会员注册 但是未绑定手机号
							currentInstance.showOveralModal('phone')
						}
					}
				}
				let info = currentInstance.data.info;
				if (info && info.pageSetting && info.navSetting && info.componentSetting) {
					currentInstance.setData({
						info: {
							...info
						},
						pageSetting: {
							...info.pageSetting
						}
					})
				}
			} else if (key == 'fail') { // 接口报错
				currentInstance.hiddenOveralModal(key) // 关闭弹窗
			} else if (key == 'store') { // 门店定位
				openSetting = await wx.$mp.openSetting()
				currentInstance.hiddenOveralModal(key) // 关闭弹窗
			} else if (key == 'subscribe') { // 订阅弹窗
				openSetting = await wx.$mp.openSetting()
				currentInstance.hiddenOveralModal(key) // 关闭弹窗
			} else if (key === 'phone') { // 需要绑定手机号的页面
				this.setData({
					showModal: true,
				})
				currentInstance.hiddenOveralModal(key) // 关闭弹窗
				wx.$mp.navigateTo({
					url: '/pages/bindPhone/bindPhone'
				})
			} else if (key === 'freeze') { // 绑定手机号
				this.setData({
					showModal: true,
				})
				currentInstance.hiddenOveralModal(key) // 关闭弹窗
			}
			else {
				currentInstance.hiddenOveralModal(key) // 其他
			}
			this.triggerEvent('overallModalConfirm', {
				key,
				openSetting
			})
		},
		// 全局弹窗取消按钮
		cancel(e) {
			let {
				key
			} = e.currentTarget.dataset;
			// 获取当前页面 更新全局弹窗状态
			var currentInstance = wx.$mp.getCurrentPage();

			if (key == 'secret') {
				// 点击不同意之后  后续弹窗颜色都是透明色
				app.globalData.autoPrincy = true
				wx.$mp.track({
					event: 'modal_princy_disagree'
				})
				app.globalData.firstData = null
				// 如果不是首页 点击不同意跳转到首页  点击首页空白再次展示隐私协议
				if (!'/pages/index/index'.includes(currentInstance.path)) {
					wx.$mp.switchTab({
						url: '/pages/index/index',
					});
					let time = setTimeout(() => {
						clearTimeout(time)
						this.setData({
							showModal: true,
							autoPrincy: true
						})
						currentInstance.hiddenOveralModal('yellow') // 关闭弹窗
						currentInstance.hiddenOveralModal(key) // 关闭弹窗
					}, 5000);
				} else {
					this.setData({
						showModal: true,
						autoPrincy: true
					})
					currentInstance.hiddenOveralModal('yellow') // 关闭弹窗
					currentInstance.hiddenOveralModal(key) // 关闭弹窗
				}
			} else {
				currentInstance.hiddenOveralModal(key) // 关闭弹窗
			}
		},
		// 获取隐私协议数据
		getData(val) {
			if (val.secret) {
				// 获取隐私协议内容
				this.setData({
					isAgreeBrand: this.data.isAgreeBrand || !!app.globalData.userInfo.isAgreeBrand,
					isShowAgreeBrand: !app.globalData.userInfo.isAgreeBrand,
					title: app.globalData.userInfo.policyVersion ? app.globalData.privacyData.newTitle : app.globalData.privacyData.policyTitle,
					content: app.globalData.privacyData.content,
					firstContent: app.globalData.privacyData.firstContent,
				})
				this.getAllC()
			}
		},
		// 禁止滑动
		noTouch() {
			return false
		}
	}
})
