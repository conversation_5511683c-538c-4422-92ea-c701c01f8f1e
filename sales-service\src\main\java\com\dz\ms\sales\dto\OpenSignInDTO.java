package com.dz.ms.sales.dto;


import com.dz.common.core.annotation.Columns;
import com.dz.common.core.enums.ColumnType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/30
 */
@Data
public class OpenSignInDTO {

    @ApiModelProperty("是否开启打卡")
    private Boolean isOpen;

    @ApiModelProperty("状态 0未完成 1已完成 2打卡失败（缺卡次数大于等于允许最大的补签次数减去已补签次数）")
    private Integer state;

    @ApiModelProperty("用户类型 1购买指定商品的用户 2其他导入的用户 3报名中奖的用户")
    private Integer couponUserType;

    @ApiModelProperty("重启次数")
    private Integer restartCount;

    @ApiModelProperty("打卡活动开始时间")
    private Date startActivityTime;
    @ApiModelProperty("打卡活动结束时间")
    private Date endActivityTime;


    @ApiModelProperty("打卡开始时间")
    private Date signInStartTime;
    @ApiModelProperty("打卡结束时间")
    private Date signInEndTime;



}
