<template>
  <div @click="showModal">
    <slot name="default"></slot>
  </div>
  <a-modal :title="title" width="600px" v-if="open" placement="right" :maskClosable="false" :closable="true" :open="open" @cancel="onClose">
    <a-form class="form" ref="addForm" :model="addParams" :rules="rules" :labelCol="{ style: 'width:160px' }">

      <a-form-item label="请上传弹窗图片" name="popupImg">
        <uploadImg :max="10" :width="100" :height="100" :imgUrl="addParams.popupImg" :form="addParams" path="popupImg" :disabled="disabled" @success="uploadSuccess" />
        <div class="global-tip">
          建议尺寸
        </div>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="ok" :loading="loading">确定</a-button>
      </a-space>
    </template>

  </a-modal>
</template>

<script setup >

const addForm = ref(null)
import { message, Modal } from "ant-design-vue";
import _ from "lodash"
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({

  title: {
    type: String || Number,
    default: '配置弹窗图片'
  },


})

const { open, addParams, rules, loading, isCheckedOptions, } = toRefs(reactive({
  open: false,
  loading: false,
  isCheckedOptions: [],
  addParams: {
    popupImg: ''
  },
  rules: {
    popupImg: [{ required: true, message: '请上传', trigger: ['blur', 'change'] }],


  }
})
);
// 确定
const ok = () => {
  addForm.value.validate().then(res => {
    emit('ok', addParams.value.popupImg)
  })

}
function showModal() {
  open.value = true
}
const onClose = () => {
  open.value = false
  emit('cancel')
}

watch(() => open.value, (value) => {
  // 弹窗操作
  addParams.value = {
    popupImg: ''
  }

  addForm.value?.resetFields()

})
const uploadSuccess = async (data) => {
  let { form, path: key, imgUrl } = data;
  console.log("🚀 ~ uploadSuccess ~ form, path: key, imgUrl:", form, key, imgUrl)
  _.set(form, key.split('.'), imgUrl)
  // 清除校验
  await nextTick()
  addForm.value.validateFields([key.split('.')])
}

</script>
