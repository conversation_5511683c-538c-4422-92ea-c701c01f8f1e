package com.dz.ms.sales.mapper;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.core.mapper.NormalBaseMapper;
import com.dz.ms.sales.dto.CrmSignInUserDTO;
import com.dz.ms.sales.dto.CrmSignInUserParamDTO;
import com.dz.ms.sales.dto.SignInUserDetailDTO;
import com.dz.ms.sales.entity.SignInUserDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/8
 */
@Repository
public interface SignInUserDetailMapper extends NormalBaseMapper<SignInUserDetail> {

    IPage<CrmSignInUserDTO> selectByMany(@Param("page") Page<CrmSignInUserDTO> page, @Param("param") CrmSignInUserParamDTO param);


    List<SignInUserDetailDTO> selectSignInCountByDays();

    List<SignInUserDetail> missingCardSignInUserDetail(@Param("campaignCode") String campaignCode, @Param("signInTimes") Integer signInTimes, @Param("repairSignInTimes") Integer repairSignInTimes);

    void signInUserDetailFail(@Param("ids") List<Long> ids, @Param("userId") Long userId);
}
