.cart-page {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;

  .cart-container {
    box-sizing: border-box;
    //margin: 0 var(--page-margin);

    .card-wrapper {
      padding: 20rpx 0;
    }

    .van-swipe-cell__right {
      background-color: var(--primary-color);
      height: 260rpx;
      line-height: 260rpx;
      width: 180rpx;
      text-align: center;
      color: #fff;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      font-style: normal;
    }

    .select-box {
      padding: 0 var(--page-margin);
      display: flex;
      align-items: center;

      .icon-Mark {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16rpx;
        color: #ffffff;
      }

      &.expire {
        opacity: 0.6;
      }

      .select-region {
        height: 80rpx;
        width: 120rpx;
        position: absolute;
        top: 30%;
        left: 0rpx;
      }

      .select-btn {
        width: 24rpx;
        height: 24rpx;
        border: 2rpx solid #000000;
        margin-right: 20rpx;

        &.disabled {
          pointer-events: none;
          background: #F6F6F6;
          border: 1rpx solid #EEEEEE;
        }

      }

      .select-item {
        transform: translateY(-50%);
      }
    }

    .active {
      background-color: black;
    }

    .select-all {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #3C3C43;
      line-height: 40rpx;
      letter-spacing: 1px;
      text-align: center;
      font-style: normal;
      margin-top: 60rpx;
      margin-bottom: 20rpx;
    }

    .expire-btn {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 var(--page-margin);
      // margin-bottom: var(--page-margin);
      // margin-top: 60rpx;
      height: 104rpx;

      .left {
        line-height: 28rpx;
        font-size: 28rpx;
      }

      .right {
        font-size: 24rpx;
        height: 100%;
        line-height: 104rpx;
        padding-left: 40rpx;
        box-sizing: border-box;
      }
    }
  }
}

.bottom-box {
  padding: var(--page-margin);
  //padding-bottom: env(safe-area-inset-bottom);
  padding-bottom: 60rpx;
  /* 避免被系统底部遮挡 */
  background-color: #fff;
  display: flex;
  justify-content: space-between;
  box-shadow: 0rpx 0rpx 15rpx 0rpx rgba(0, 0, 0, 0.04);

  .total-box {
    .total-num {
      height: 33rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #888888;
      line-height: 33rpx;
      text-align: left;
      font-style: normal;
      margin-bottom: 10rpx;
    }

    .total-price {
      height: 40rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28rpx;
      color: #3C3C43;
      line-height: 40rpx;
      letter-spacing: 1px;
      text-align: left;
      font-style: normal;
    }
  }
}
