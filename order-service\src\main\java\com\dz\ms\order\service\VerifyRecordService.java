package com.dz.ms.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.ms.order.dto.VerifyRecordDTO;
import com.dz.ms.order.entity.VerifyRecord;

/**
 * 核销记录接口
 * @author: Handy
 * @date:   2024/06/06 14:47
 */
public interface VerifyRecordService extends IService<VerifyRecord> {

	/**
     * 分页查询核销记录
     * @param param
     * @return PageInfo<VerifyRecordDTO>
     */
    public PageInfo<VerifyRecordDTO> getVerifyRecordList(VerifyRecordDTO param);

    /**
     * 根据ID查询核销记录
     * @param id
     * @return VerifyRecordDTO
     */
    public VerifyRecordDTO getVerifyRecordById(Long id);

    /**
     * 保存核销记录
     * @param param
     * @return Long
     */
    public Long saveVerifyRecord(VerifyRecordDTO param);

    /**
     * 根据ID删除核销记录
     * @param param
     */
    public void deleteVerifyRecordById(IdCodeDTO param);

}
