package com.dz.ms.adaptor.controller;

import com.dz.ms.adaptor.dto.CrmPointsSyncWrapperParamDTO;
import com.dz.ms.adaptor.service.PointsSubscriptionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags="第三方CRM接口请求记录")
@Slf4j
@RestController
public class ThirdCrmController {

    @Resource
    private PointsSubscriptionService pointsSubscriptionService;

    /**
     * 即将过期积分用户记录
     *
     * @return
     */
    @ApiOperation("用户即将过期积分推送")
    @PostMapping(value = "/openapi/third_crm/points_sync")
    public String crmPointsSync(@RequestBody CrmPointsSyncWrapperParamDTO param) {
        try {
            //log.info("用户即将过期积分推送,param:{}", param.toString());
            pointsSubscriptionService.crmPointsSync(param.getItems());
        } catch (Exception e) {
            log.error("用户即将过期积分推送失败", e);
            return "error";
        }
        return "success";
    }

}
