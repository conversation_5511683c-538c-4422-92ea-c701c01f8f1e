package com.dz.ms.sales.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.user.InteractionTaskDTO;
import com.dz.ms.sales.entity.InteractionTask;
import com.dz.ms.sales.vo.InteractionTaskSelectVo;
import com.dz.ms.sales.vo.InteractionTaskVo;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;
import java.sql.SQLException;
import java.text.ParseException;
import java.util.List;

/**
 * 用户信息接口
 *
 * @author: Handy
 * @date: 2022/2/4 17:33
 */
public interface InteractionTaskService extends IService<InteractionTask> {

    PageInfo<InteractionTaskDTO> getInteractionTaskList(InteractionTaskSelectVo param) throws ParseException;

    /**
     * 导出任务列表
     */
    void exportTaskList(String jsonParam, String reportCode, String fileName, String fileExt, Long downloadCenterId) throws ParseException;

    InteractionTaskDTO getInteractionTaskInfo(Long id);

    void addInteractionTaskInfo(InteractionTaskVo param);

    void updateInteractionTaskInfo(InteractionTaskVo param,Integer type);

    void deleteInteractionTaskInfo(InteractionTaskVo param);

    List<InteractionTaskDTO> getInteractionTaskListApp(Long userId) throws ParseException;

    void taskSuccessRecordCard(String longitude,String latitude) throws ParseException;

    void taskSuccessRecordByOrder() throws ParseException;

    void taskSuccessRecordByProduct() throws ParseException;

    void taskSuccessRecordFriend(Long inviteUserId) throws ParseException;

    void threeFirstOrder() throws ParseException;

    void taskExpireThree();

    void addUserTask(Long taskId, Long uid, Long tenantId) throws SQLException;
}
