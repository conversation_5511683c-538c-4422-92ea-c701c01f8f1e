<template>
  <a-modal
    v-model:open="open"
    width="1200px"
    ok-text="保存"
    :confirm-loading="confirmLoading"
    @ok="handleOk"
  >
    <a-form :model="searchFields" layout="inline">
      <a-form-item label="奖品名称" name="prizesName">
        <a-input
          placeholder="请输入"
          allow-clear
          v-model:value="searchFields.prizesName"
          allowClear
          @keyup.enter="whenClickSearch"
        ></a-input>
      </a-form-item>
      <a-form-item label="奖品类型" name="prizesType">
        <a-select
          v-model:value="searchFields.prizesType"
          :options="PRIZES_TYPE_ARR"
          allow-clear
          placeholder="请选择"
        />
      </a-form-item>
      <a-form-item label="启用状态" name="state">
        <a-select
          v-model:value="searchFields.state"
          :options="PRIZES_STATUS_ARR"
          allow-clear
          placeholder="请选择"
        />
      </a-form-item>
      <div style="flex: 1"></div>
      <a-space>
        <a-button type="primary" @click="whenClickSearch"> 查询 </a-button>
        <a-button @click="whenClickReset"> 重置 </a-button>
        <a-button type="primary" :disabled="!lotteryId" @click="stockManageModal = true">
          库存任务管理
        </a-button>
      </a-space>
    </a-form>
    <a-table
      class="shelf-product-management-add-table"
      row-key="_id"
      :scroll="{ scrollToFirstRowOnChange: true, x: '100%', y: 500 }"
      :dataSource="dataSource"
      :columns="tableHeader"
      :pagination="pagination"
      @change="whenPaginationChange"
    >
      <template v-slot:headerCell="{ column }">
        <span v-if="column.dataIndex == 'crowdId'">
          <span style="margin-right: 4px">人群限制</span>
          <a-tooltip placement="top" title="只有人群包内的用户，才有可能抽中该奖品">
            <QuestionCircleOutlined />
          </a-tooltip>
        </span>
        <span v-else-if="column.dataIndex == 'countLimit'">
          <span style="margin-right: 4px">{{ column.title }}</span>
          <a-tooltip placement="top" title="为0时，不限制">
            <QuestionCircleOutlined />
          </a-tooltip>
        </span>
      </template>
      <template #bodyCell="{ text, record, index, column }">
        <template v-if="column.dataIndex === '_index'">{{ index + 1 }}</template>
        <template v-if="column.dataIndex === 'crowdId'">
          <a-select
            v-model:value="record.crowdId"
            :options="crowdAllList"
            show-search
            :filterOption="crowdFilterOption"
          />
        </template>
        <template v-else-if="column.dataIndex === 'totalStock'">
          <a-input-number
            v-model:value="record.totalStock"
            :min="0"
            :max="9999999"
            :parser="(v) => inputNumberParserInteger(v, 0)"
          />
        </template>
        <template v-else-if="column.dataIndex === 'countLimit'">
          <a-input-number
            v-model:value="record.countLimit"
            :min="0"
            :max="99999"
            :parser="(v) => inputNumberParserInteger(v, 0)"
          />
        </template>
        <template v-else-if="column.dataIndex === 'operate'">
          <a-space>
            <a-button type="link" size="small" @click="handleState(record)">
              {{ record._enable ? '停用' : '启用' }}
            </a-button>
            <a-popconfirm
              title="确定删除该奖品/商品？"
              @confirm="handleDelete(record)"
              :disabled="record._enable"
            >
              <a-button :disabled="record._enable" type="link" size="small">删除 </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>
  </a-modal>

  <InventoryTaskModal
    v-model:visible="stockManageModal"
    :lottery-id="props.lotteryId"
    @changeInventory="handleChangeInventory"
  />
</template>

<script setup>
import InventoryTaskModal from './InventoryTaskModal.vue'

import {
  PRIZES_TYPE_OBJ,
  PRIZES_TYPE_ARR,
  PRIZES_STATUS_ARR,
  PRIZES_STATUS_OBJ
} from '../utils/constant'
import { inputNumberParserInteger } from '../utils/utils'

const confirmLoading = ref(false)
const open = defineModel('open')
const emit = defineEmits(['ok'])
const props = defineProps({
  // 抽奖id
  lotteryId: { type: [String, Number], required: true },
  // 人群列表
  crowdAllList: { type: Array, required: true },
  // 已选择商品的列表
  goodsList: { type: Array, required: true },
  // 配置抽奖弹窗中更新数据方法
  updateDataFun: { type: Function, required: true }
})
// 所有商品的列表
let allData = []
// 根据当前搜索条件筛选后的商品列表
let filteredData = []
const filteredDataLength = ref(0)

watch(
  () => open.value,
  (val) => {
    if (val) {
      allData = props.goodsList.map(formatDataSource)
      whenClickReset()
    } else {
      allData = []
      whenClickReset()
    }
  }
)

// 确认操作
function handleOk() {
  const list = allData.map((item) => {
    const { _prizesType, _tagList, _state, _enable, _, remainStock, _consumeStock, ...rest } = item
    return rest
  })
  open.value = false
  emit('ok', { list, deleteIds })
  deleteIds = []
}

// 搜索
const searchFields = reactive(getDefaultSearchFields())
const pagination = computed(() => {
  return {
    total: filteredDataLength.value,
    current: searchFields.pageNum,
    pageSize: searchFields.pageSize,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ['bottomLeft']
  }
})
function whenPaginationChange(pag) {
  searchFields.pageNum = pag.current
  searchFields.pageSize = pag.pageSize
  getCurrentPageData()
}
function whenClickSearch(reset = true) {
  if (reset) {
    Object.assign(searchFields, { pageNum: 1, pageSize: 10 })
  }
  const { prizesName, prizesType, state } = searchFields
  filteredData = allData.filter((item) => {
    return (
      (prizesName !== '' ? item.prizesName.includes(prizesName) : true) &&
      (prizesType !== undefined ? item.prizesType === prizesType : true) &&
      (state !== undefined ? item.state === state : true)
    )
  })
  filteredDataLength.value = filteredData.length
  getCurrentPageData()
}
function whenClickReset() {
  Object.assign(searchFields, getDefaultSearchFields())
  whenClickSearch()
}
// 获取当前页数据
function getCurrentPageData() {
  dataSource.value = filteredData.slice(
    (searchFields.pageNum - 1) * searchFields.pageSize,
    searchFields.pageNum * searchFields.pageSize
  )
}
function getDefaultSearchFields() {
  return {
    prizesName: '',
    prizesType: undefined,
    state: undefined,
    pageNum: 1,
    pageSize: 10
  }
}
function formatDataSource(item) {
  return {
    ...item,
    // countLimit 单奖品中奖限制/人 -1时为不限制 但输入框中显示为0
    _: '-',
    _enable: item.state === 1,
    countLimit: item.countLimit <= 0 ? 0 : item.countLimit,
    _prizesType: PRIZES_TYPE_OBJ[item.prizesType],
    remainStock: item.remainStock || '-',
    _consumeStock: item.totalStock ? item.totalStock - (item.remainStock || 0) : '-',
    _state: PRIZES_STATUS_OBJ[item.state] + '中'
  }
}

const dataSource = ref([])
const tableHeader = [
  { title: '序', dataIndex: '_index', align: 'center', width: 40 },
  { title: '奖品/商品 名称', dataIndex: 'prizesName', align: 'center', ellipsis: true, width: 120 },
  { title: '奖品类型', dataIndex: '_prizesType', align: 'center', width: 80 },
  { title: '该抽奖活动上架库存', dataIndex: 'totalStock', align: 'center', width: 80 },
  { title: '该抽奖活动目前库存', dataIndex: 'remainStock', align: 'center', width: 80 },
  { title: '该抽奖活动消耗量', dataIndex: '_consumeStock', align: 'center', width: 80 },
  { title: '启用状态', dataIndex: '_state', align: 'center', width: 80 },
  { title: '单奖品中奖限制/人', dataIndex: 'countLimit', align: 'center', width: 80 },
  { title: '人群限制', dataIndex: 'crowdId', align: 'center', width: 140 },
  { title: '操作', dataIndex: 'operate', align: 'center', width: 100 }
]

// 删除
let deleteIds = [] // 本次删除的ids
function handleDelete(record) {
  deleteIds.push(record._id)
  allData = allData.filter((item) => item._id !== record._id)
  whenClickReset()
}
// 选择人群包单选选择框通用筛选
function crowdFilterOption(input, option) {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 库存管理弹窗
const stockManageModal = ref(false)
// 处理库存变更 更新抽奖详情数据,更新当前弹窗
async function handleChangeInventory() {
  // fullLoading.value = true
  // confirmLoading.value = true
  // const parentData = await props.updateDataFun()
  // console.log('parentData: ', parentData)
  // allData = parentData.lotteryPrizeList.map(formatDataSource)
  // whenClickReset()
  // fullLoading.value = false
  // confirmLoading.value = false
}
// 启用/停用
function handleState(record) {
  allData = allData.map((item) => {
    if (item._id === record._id) {
      item.state = record.state === 1 ? 0 : 1
      return formatDataSource(item)
    }
    return item
  })
  whenClickSearch(false)
}
</script>

<style scoped>
.ant-select {
  width: 160px;
}
.ant-form {
  padding: 20px 20px 20px 0px;
}
</style>
