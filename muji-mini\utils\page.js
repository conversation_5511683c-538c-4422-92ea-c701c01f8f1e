// 该页面功能为拦截小程序的一些方法做一些封装
import "../utils/config";
import mixinsFunction from "../mixins/index";
import {
  loginWechat,
  getStyle,
  getPrivacyPolicy,
  addUserMessage,
  firstPurchaseTask,
  subscribeConfig,
  addAutoSubscribe,
  offLineStoreTask,
  taskProduct,
  getChannelId,
  cdpAgreeSelect
} from '../api/index'
import c from './lf-wx-miniapp.min';

const cssVarPicker = () => ({
  "--primary-color": wx.$cssVar["--primary-color"],
  "--checkboxColor": wx.$cssVar["--checkboxColor"],
});

// token获取中
async function getToken() {
  const app = getApp();
  const currentInstance = wx.$mp.getCurrentPage();
  if (!app.globalData.tokenPromise) {
    currentInstance.showOveralModal("white");
    app.globalData.tokenPromise = new Promise(async (resolve) => {
      // 优先获取隐私协议
      if (!app.globalData.privacyData?.id) {
        await app.getPrincy();
      }
      // 获取token
      const token = wx.getStorageSync("token");
      if (token) {
        app.globalData.token = token;
        // token失效有可能是用户注销 需要重新获取用户信息
        if (!app.globalData.userInfo.id) {
          resolve(app.getUserInfo());
        }
        resolve();
      } else {
        wx.login({
          success: (res) => {
            const { code } = res;
            loginWechat({
              code,
              appId: wx.$config.appId,
            }).then(async (value) => {
              const { token } = value.data;
              app.globalData.token = token;
              wx.setStorageSync("token", token);
              // token失效有可能是用户注销 需要重新获取用户信息
              // if (app.globalData.userInfo?.id) {
              //   resolve()
              // } else {
              resolve(app.getUserInfo());
              // }
            });
          },
        });
      }
    });
  }

  return app.globalData.tokenPromise.then((res) => {
    // 隐私协议判断
    // 首次进入非隐私协议的时候判断  符合条件的页面才弹隐私协议弹窗
    // 没有自动弹过隐私协议
    let executeFlag = true;
    if (
      !wx.$config.noShowPrivicy.some((item) =>
        item.includes(currentInstance.path)
      ) &&
      !app.globalData.autoPrincy
    ) {
      // 这里的逻辑一进小程序执行 只执行一次
      if (
        app.globalData.userInfo?.policyVersion !=
        app.globalData.privacyData?.policyVersion ||
        app.globalData.needAuthorization
      ) {
        // 首页直接弹隐私协议
        if ("/pages/index/index".includes(currentInstance.path)) {
          currentInstance.showOveralModal("secret");
        } else if (
          "/pages/princyModal/princyModal".includes(currentInstance.path)
        ) {
          console.log("什么也不做");
        } else {
          // 非首页 跳转页面  不执行 生命周期函数
          currentInstance.showOveralModal("yellow");
          executeFlag = false;
        }
      }
    } else {
    }
    currentInstance.hiddenOveralModal("white");
    console.log("返回", executeFlag);
    return executeFlag;
  });
}

const miniPage = Page;
// 拦截 Page
Page = function (pageData) {
  const app = getApp();

  // 埋点
  // 只有正式环境上传
  if (wx.$config.env === "prod") {
    app.muji_sdk.setIdentities(app.globalData.identities);
    app.muji_sdk.setProfile(app.globalData.profile);
  }

  // 通过函数的形式可以拿到app的获取
  const mixins = mixinsFunction();

  // 混入data合并
  pageData.data = {
    popupShowNum: 0,
    disablePageScroll: false,
    scrollTop: 0,
    cssVar: cssVarPicker(),
    ...(mixins.data || {}),
    ...(pageData.data || {}),
  };

  // 混入方法调用  页面方法覆盖混入方法
  pageData = {
    ...mixins.methods,
    ...pageData,
  };

  pageData.onPopupShowDisablePageScroll = function (force = true) {
    if (force) {
      this.setData({
        disablePageScroll: true,
      });
    } else {
      this.data.popupShowNum++;
      this.setData({
        disablePageScroll: true,
      });
    }
  };
  pageData.onPopupHideEnablePageScroll = function (force = true) {
    if (force) {
      this.setData({
        disablePageScroll: false,
      });
    } else {
      this.data.popupShowNum--;
      if (this.data.popupShowNum === 0) {
        this.setData({
          disablePageScroll: false,
        });
      }
    }
  };

  // onLoad
  const _onLoad = pageData.onLoad;
  pageData.onLoad = async function (options = {}) {
    // 获取全局风格
    if (!app.globalData.styleSetting?.id) {
      getStyle().then((res) => {
        let data = res.data;
        let content = JSON.parse(data.content);
        data.pageStyle = content.pageStyle;
        data.pageStyle.font = data.pageStyle.font
          ? data.pageStyle.font.join(" ")
          : "";
        data.openStyle = content.openStyle;
        app.globalData.styleSetting = data;
      });
    }

    // 检查token有效性
    let executeFlag = await getToken();

    if (!this.optionsNoSwitchTabQuery) this.optionsNoSwitchTabQuery = {};
    console.log("页面onload----------------");
    const currentInstance = wx.$mp.getCurrentPage();
    // 解析二维码options进入参数
    let { scene } = options;
    if (scene) {
      scene = decodeURIComponent(scene);
      console.log("scene", scene);
      let obj = scene.split("&").reduce((obj, item) => {
        let [key, value] = item.split("=");
        obj[key] = value;
        return obj;
      }, {});
      options = {
        ...options,
        ...obj,
      };
    }
    this.optionsNoSwitchTabQuery = options;
    options = {
      ...this.optionsNoSwitchTabQuery,
      ...app.globalData.switchTabQuery,
    };
    // 通过二维码进来  paramid 换取 chid cpid
    if (options.paramid) {
      const res = await getChannelId({
        paramId: options.paramid,
      });
      if (res.data) {
        const channelData = res.data.split("&").reduce((obj, item) => {
          let [key, value] = item.split("=");
          obj[key] = value;
          return obj;
        }, {});
        if (channelData.chid) {
          app.globalData.channelOne = channelData.chid;
        }
        if (channelData.cpid) {
          app.globalData.channelTwo = channelData.cpid;
        }
      }
    }
    // 通过链接直接进来直接带 chid cpid
    // 全局参数保存 ———— 渠道参数
    if (options.chid) {
      // 一级
      app.globalData.channelOne = options.chid;
    }
    if (options.cpid) {
      // 二级渠道参数
      app.globalData.channelTwo = options.cpid;
    }
    if (options.inviteUserId) {
      app.globalData.inviteUserId = options.inviteUserId;
    }

    // 设置入参
    this.setData({
      options,
    });

    // 打印页面入口信息
    this.path = `/${this.__route__}`;
    console.log("\n\n");
    console.log(
      "%c 进入页面",
      "color: #ff0000;",
      this.path,
      "options:",
      options
    );
    console.log("\n\n");

    console.log("onload---------------------------", executeFlag);
    if (wx.$mp.getOneVarType(_onLoad) === "Function" && executeFlag) {
      _onLoad.bind(this)(this.data.options);
    }

    if (
      !wx.$config.noShowPrivicy.some((item) =>
        item.includes(currentInstance.path)
      ) &&
      !app.globalData.autoPrincy
    ) {
      // 这里的逻辑一进小程序执行 只执行一次
      if (
        app.globalData.userInfo?.policyVersion !=
        app.globalData.privacyData?.policyVersion ||
        app.globalData.needAuthorization
      ) {
        // 首页直接弹隐私协议
        if ("/pages/index/index".includes(currentInstance.path)) {
          currentInstance.showOveralModal("secret");
        } else if (
          "/pages/princyModal/princyModal".includes(currentInstance.path)
        ) {
          // 隐私弹窗直接弹
          currentInstance.showOveralModal("secret");
        } else {
          // 非首页 跳转页面  不执行 生命周期函数
          currentInstance.showOveralModal("yellow");
          app.globalData.firstData = {
            path: currentInstance.path,
            options: this.optionsNoSwitchTabQuery,
          };
          wx.$mp.redirectTo({
            url: `/pages/princyModal/princyModal`,
          });
        }
      } else if (
        !wx.$config.noShowPhone.some((item) =>
          item.includes(currentInstance.path)
        )
      ) {
        // 需要展示绑定手机号的页面
        if (app.globalData.userInfo.isFreeze) {
          // 用户冻结
          currentInstance.showOveralModal("freeze");
        } else if (
          app.globalData.userInfo.isMember > 0 &&
          !app.globalData.userInfo.mobile
        ) {
          // 判断是否会员注册 但是未绑定手机号
          currentInstance.showOveralModal("phone");
        }
      }
    } else if (
      !wx.$config.noShowPhone.some((item) =>
        item.includes(currentInstance.path)
      )
    ) {
      // 需要展示绑定手机号的页面
      if (app.globalData.userInfo.isFreeze) {
        // 用户冻结
        currentInstance.showOveralModal("freeze");
      } else if (
        app.globalData.userInfo.isMember > 0 &&
        !app.globalData.userInfo.mobile
      ) {
        // 判断是否会员注册 但是未绑定手机号
        currentInstance.showOveralModal("phone");
      }
    }

    // 进入小程序只执行一次的内容
    if (!app.globalData.autoOperate) {
      app.globalData.autoOperate = true;
      // 订阅消息获取
      subscribeConfig().then((res) => {
        app.globalData.subscribeConfig = {
          ...(JSON.parse(res.data.content || "") || {}),
          id: res.data.id,
        };
      });

      // 自动触发订阅消息
      addAutoSubscribe({
        msgCode: ["expireCoupon", "memberReceive"],
      });

      // 用户信息推送
      addUserMessage({
        msgCode: ["expireCoupon", "couponAccount", "receiveCard"],
      });

      // 首购任务
      firstPurchaseTask();

      //完成线下消费任务奖励
      taskProduct();

      // cdp接受营销短信
      cdpAgreeSelect()

      // 自动打卡  需要token 获取定位  所以加延迟时间
      // let time = setTimeout(() => {
      //   clearTimeout(time)
      //   time = null
      //   if (app.globalData.userLatitude) {
      //     offLineStoreTask({
      //       latitude: app.globalData.userLatitude,
      //       longitude: app.globalData.userLongitude,
      //     })
      //   }
      // }, 5000);
    }
  };

  // onShow
  const _onShow = pageData.onShow;
  pageData.onShow = async function () {
    // 检查token有效性
    let executeFlag = await getToken();
    console.log("onShow---------------------------", executeFlag);

    // 其他地方同意后  本页面隐藏
    const currentInstance = wx.$mp.getCurrentPage();
    if (
      app.globalData.userInfo?.policyVersion ==
      app.globalData.privacyData?.policyVersion &&
      !app.globalData.needAuthorization
    ) {
      currentInstance.hiddenOveralModal("secret");
    }
    // 同一个tabBar，多次switchTab的时候，不会触发onLoad，所以需要在onShow中赋值最新的switchTabQuery。
    this.setData({
      options: {
        ...this.optionsNoSwitchTabQuery,
        ...app.globalData.switchTabQuery,
      },
      visitor: app.globalData.visitor,
      userInfo: app.globalData.userInfo,
    });

    // 页面pv uv传参
    wx.$mp.track({
      event: "pageShow",
    });
    if (wx.$mp.getOneVarType(_onShow) === "Function" && executeFlag) {
      _onShow.bind(this)(this.data.options);
    }
  };

  // onReady
  const _onReady = pageData.onReady;
  pageData.onReady = async function () {
    // 检查token有效性
    let executeFlag = await getToken();
    console.log("onReady---------------------------", executeFlag);
    if (wx.$mp.getOneVarType(_onReady) === "Function" && executeFlag) {
      _onReady.bind(this)(this.data.options);
    }
  };

  const _onShareAppMessage = pageData.onShareAppMessage;
  if (wx.$mp.getOneVarType(_onShareAppMessage) === "Function") {
    pageData.onShareAppMessage = async function (options) {
      const currentPage = wx.$mp.getCurrentPage();
      if (
        app.ifRegister() &&
        options.from === "button" &&
        options.target.dataset.type === "task"
      )
        await app.subscribe("task");
      const shareData =
        (await _onShareAppMessage.bind(currentPage)(options)) || {};
      shareData.imageUrl =
        shareData.imageUrl || `${wx.$config.ossImg}/share/normal.jpg`;
      shareData.title =
        shareData.title || wx.$contants.SHARE_DATA_DEFAULT_TITLE;
      shareData.path = shareData.path || "/pages/index/index";
      // 所有分享链接都需要加上分享人和渠道参数
      shareData.query = {
        ...(shareData.query || {}),
        inviteUserId: app.globalData.userInfo.id, // 分享用户人ID
        chid: app.globalData.channelOne, // 渠道1
        cpid: app.globalData.channelTwo, // 渠道2
      };
      const query = shareData.query;
      if (query) {
        const symbol = shareData.path.includes("?") ? "&" : "?";
        const keys = Object.keys(query).sort();
        if (keys.length) {
          shareData.path += `${symbol}${keys
            .map((key) => `${key}=${query[key]}`)
            .join("&")}`;
        }
      }
      console.log("分享数据shareData：", shareData);
      return shareData;
    };
  }

  const _onPageScroll = pageData.onPageScroll;
  pageData.onPageScroll = function (e) {
    this.setData({
      scrollTop: e.scrollTop,
    });
    if (wx.$mp.getOneVarType(_onPageScroll) === "Function") {
      _onPageScroll.bind(this)(e);
    }
  };

  miniPage(pageData);
};

// 拦截组件 注入图片前缀
const component = Component;
Component = function (componentData) {
  const app = getApp();

  componentData.data = {
    cssVar: cssVarPicker(),
    ...(componentData.data || {}),
    $cdn: wx.$config.ossImg,
  };

  component(componentData);
};
