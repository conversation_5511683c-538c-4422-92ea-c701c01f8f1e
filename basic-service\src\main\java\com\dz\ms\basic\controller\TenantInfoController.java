package com.dz.ms.basic.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.KeyValueDTO;
import com.dz.common.core.dto.basic.QywxConfigDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.fegin.basic.TenantInfoFeginClient;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.ms.basic.dto.TenantConfigAppDTO;
import com.dz.ms.basic.dto.TenantInfoDTO;
import com.dz.ms.basic.dto.NavigationConfigDTO;
import com.dz.ms.basic.entity.TenantInfo;
import com.dz.ms.basic.service.QywxConfigService;
import com.dz.ms.basic.service.TenantInfoService;
import com.dz.ms.basic.service.NavigationConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 租户信息
 * <AUTHOR>
 */
@Api(tags="租户信息")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class TenantInfoController implements TenantInfoFeginClient {

    @Resource
    private TenantInfoService tenantInfoService;
    @Resource
    private QywxConfigService qywxConfigService;
    @Resource
    private NavigationConfigService navigationConfigService;

    /**
     * 分页查询租户信息
     * @param param
     * @return result<PageInfo<TenantInfoDTO>>
     */
    @ApiOperation("分页查询租户信息")
    @GetMapping(value = "/oms/tenant/list")
    public Result<PageInfo<TenantInfoDTO>> getTenantInfoList(@ModelAttribute TenantInfoDTO param) {
        Result<PageInfo<TenantInfoDTO>> result = new Result<>();
        TenantInfo tenantInfo = BeanCopierUtils.convertObjectTrim(param,TenantInfo.class);
        IPage<TenantInfo> page = tenantInfoService.page(new Page<>(param.getPageNum(), param.getPageSize()), new QueryWrapper<>(tenantInfo));
        result.setData(new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),BeanCopierUtils.convertList(page.getRecords(), TenantInfoDTO.class)));
        return result;
    }

    /**
     * 根据ID查询租户信息
     * @param id
     * @return result<TenantInfoDTO>
     */
    @ApiOperation("根据ID查询租户信息")
    @GetMapping(value = "/oms/tenant/info")
    public Result<TenantInfoDTO> getTenantInfoById(@RequestParam("id") Long id) {
        Result<TenantInfoDTO> result = new Result<>();
        TenantInfo tenantInfo = tenantInfoService.getById(id);
        result.setData(BeanCopierUtils.convertObject(tenantInfo,TenantInfoDTO.class));
        return result;
    }

    @ApiOperation("获取当前租户信息")
    @GetMapping(value = "/crm/tenant/current_info")
    public Result<TenantInfoDTO> getCurrentTenantInfo() {
        Result<TenantInfoDTO> result = new Result<>();
        TenantInfo tenantInfo = tenantInfoService.getById(SecurityContext.getUser().getTenantId());
        result.setData(BeanCopierUtils.convertObject(tenantInfo,TenantInfoDTO.class));
        return result;
    }

    /**
     * 保存租户信息
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "保存租户信息",type = LogType.OPERATELOG)
    @ApiOperation("保存租户信息")
    @PostMapping(value = {"/oms/tenant/save","/crm/tenant/save"})
    public Result<Long> save(@RequestBody TenantInfoDTO param) {
        Result<Long> result = new Result<>();
        if(StringUtils.isBlank(param.getCnName())) {
            return result.paramErroResult("品牌名称不能为空");
        }
        if(StringUtils.isBlank(param.getMobile())) {
            return result.paramErroResult("手机号不能为空");
        }
        Long id = tenantInfoService.saveTenant(param);
        result.setData(id);
        return result;
    }

    /**
     * 根据ID删除租户信息
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除租户信息")
    @PostMapping(value = "/oms/tenant/delete")
    public Result<Boolean> deleteTenantInfoById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        tenantInfoService.removeById(param.getId());
        result.setData(true);
        return result;
    }

    @ApiOperation("根据租户ID列表获取租户信息")
	@PostMapping(value = "/tenant/list_by_ids")
    public Result<List<KeyValueDTO>> getTenantInfoByIds(@RequestBody List<Long> ids) {
        Result<List<KeyValueDTO>> result = new Result<>();
        List<TenantInfo> list = tenantInfoService.listByIds(ids);
        List<KeyValueDTO> resList = new ArrayList<>();
        if(CollectionUtils.isEmpty(list)) {
            return result;
        }
        for (TenantInfo tenantInfo : list) {
            resList.add(new KeyValueDTO(tenantInfo.getId(),tenantInfo.getTenantCode(),tenantInfo.getCnName(),tenantInfo.getLogoUrl()));
        }
        result.setData(resList);
        return result;
    }

    @ApiOperation("获取所有租户列表")
    @GetMapping(value = "/tenant/list_all")
    public Result<List<KeyValueDTO>> getAllTenantList() {
        Result<List<KeyValueDTO>> result = new Result<>();
        LambdaQueryWrapper<TenantInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TenantInfo :: getState,1);
        List<TenantInfo> list = tenantInfoService.list(wrapper);
        if(CollectionUtils.isEmpty(list)) {
            return result;
        }
        List<KeyValueDTO> kvList = list.stream().map(tenant -> {
            return new KeyValueDTO(tenant.getId(),tenant.getTenantCode(),tenant.getCnName(),tenant.getLogoUrl());
        }).collect(Collectors.toList());
        result.setData(kvList);
        return result;
    }

    @ApiOperation("根据租户ID查询租户信息")
    @GetMapping(value = "/tenant/get_by_id")
    public Result<KeyValueDTO> getTenantById(@RequestParam("id") Long id) {
        Result<KeyValueDTO> result = new Result<>();
        KeyValueDTO tenantInfo = tenantInfoService.getTenantById(id);
        result.setData(tenantInfo);
        return result;
    }

    @ApiOperation("根据租户Code查询租户信息")
    @GetMapping(value = "/tenant/get_by_code")
    public Result<KeyValueDTO> getTenantByCode(@RequestParam("code") String code) {
        Result<KeyValueDTO> result = new Result<>();
        KeyValueDTO tenant = tenantInfoService.getTenantByCode(code);
        result.setData(tenant);
        return result;
    }

    @ApiOperation("根据租户编码查询租户配置")
    @GetMapping("/app/tenant_config/by_code")
    public Result<TenantConfigAppDTO> getQywxConfigByTenantCode(@RequestParam(value = "tenantCode",required = false) String tenantCode) {
        Result<TenantConfigAppDTO> result = new Result<>();
        if(StringUtils.isEmpty(tenantCode)) {
            return result.paramErroResult("未获取到品牌标识，请在wesocial打开");
        }
        KeyValueDTO tenant = tenantInfoService.getTenantByCode(tenantCode);
        if(null == tenant) {
            return result.paramErroResult("品牌编码错误");
        }
        QywxConfigDTO qywxConfig = qywxConfigService.getQywxConfigByTenantId(tenant.getId());
        SecurityContext.setUser(new CurrentUserDTO(tenant.getId()));
        NavigationConfigDTO uiConfig = navigationConfigService.getCheckedUiConfig();
        result.setData(new TenantConfigAppDTO(qywxConfig.getState(),tenant.getValue() ,null == uiConfig ? null : uiConfig.getContent(),qywxConfig.getTenantId()));
        return result;
    }

    @ApiOperation("初始化品牌信息")
    @PostMapping(value = "/tenant/init")
    public Result<Boolean> initTenant(@RequestParam("tenantId") Long tenantId) {
        Result<Boolean> result = new Result<>();
        tenantInfoService.initTenant(tenantId);
        result.setData(true);
        return result;
    }

}
