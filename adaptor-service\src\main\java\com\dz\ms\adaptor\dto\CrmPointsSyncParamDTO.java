package com.dz.ms.adaptor.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * 即将过期积分用户记录按月分表
 */
@Data
@NoArgsConstructor
@ToString
public class CrmPointsSyncParamDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 用户会员code
     */
    @JsonProperty("member_code")
    private String memberCode;

    /**
     * 当前可用积分数量
     */
    @JsonProperty("bonus_amount")
    private Integer bonusAmount;

    /**
     * 即将过期积分
     */
    @JsonProperty("expire_bonus_amount")
    private Integer expirePoints;

    /**
     * 创建时间
     */
    private Date createTime;

    private Long tenantId;

    @Override
    public String toString() {
        return "{" +
                "member_code='" + memberCode + '\'' +
                ", bonus_amount=" + bonusAmount +
                ", expire_bonus_amount=" + expirePoints +
                '}';
    }
}
