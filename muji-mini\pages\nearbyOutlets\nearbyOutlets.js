// pages/nearbyOutlets/nearbyOutlets.js
import {
  getStoreList,
  getCurrentCity,
} from '../../api/index.js'
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    menuButtonTop: app.globalData.menuButtonTop,
    menuButtonBottom: app.globalData.menuButtonBottom,
    isBackHidden: false,
    pageSize: 20,
    pageNum: 1,
    outletsList: [], // 门店列表
    // showCategoryDialog: false, // 显示品类筛选
    showPositionDialog: false, // 显示位置授权弹窗
    showCityDialog: false, // 显示城市列表弹窗
    isLocationAuthorized: false, // 判断用户是否开启位置授权
    outletsTopHight: app.globalData.navBarHeight,
    navHeight: app.globalData.navBarHeight + app.globalData.statusBarHeight,
    activeTab: 1, // 默认是门店列表
    filters: { // 筛选数据
      city: '全国',
      // serveIds: [],  // 服务选项
      storeName: '',
      type: 'all'
    },
    latitude: undefined,
    longitude: undefined,
    count: 0,
    loading: true,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    const {
      userLatitude
    } = app.globalData;
    if (userLatitude && userLatitude !== 0) {
      this.setData({
        isLocationAuthorized: true,
        latitude: app.globalData.userLatitude,
        longitude: app.globalData.userLongitude,
      })
    } else {
      this.setLocation()
    }
    // 获取顶部高度，动态计算滚动区域高度
    const that = this; // 保存当前页面实例
    wx.createSelectorQuery().select('#outletsTop').boundingClientRect(function (rect) {
      that.setData({
        outletsTopHight: rect.bottom,
      })
    }).exec();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow(options) {
    const {
      city
    } = options;
    const {
      filters
    } = this.data;
    this.data.outletsList = [];
    this.data.pageNum = 1;
    const updateFilters = async () => {
      if (city) { // 如果 options 中包含城市信息，则直接更新 filters
        this.setData({
          filters: {
            ...filters,
            city,
          }
        });
      } else {
        if (filters.city === '全国') {
          await this.getCurrentCityData();
        }
      }
      await this.getList(); // 在获取城市信息后再获取列表
    };
    updateFilters();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  getList() {
    const {
      pageNum,
      pageSize,
      filters,
      latitude,
      longitude,
      outletsList,
      isLocationAuthorized
    } = this.data;
    const params = {
      ...filters
    };
    const {
      city,
      type,
    } = filters;

    if (type === "all") {
      params.type = undefined
    }
    if (city === '全国') {
      params.city = undefined
    }
    this.setData({
      loading: true,
    })
    getStoreList({
      pageSize,
      pageNum,
      latitude: 0 ? undefined : latitude,
      longitude: 0 ? undefined : longitude,
      tag: !isLocationAuthorized ? 1 : undefined,
      ...params,
    }).then((res) => {
      const {
        data,
      } = res;
      this.setData({
        outletsList: pageNum === 1 ? data.list : outletsList.concat(data.list),
        hasMore: data.pageNum * data.pageSize < data.count,
        count: data.count,
        loading: false,
      })
    })
  },
  // 展示品类筛选
  handleShowCategory() {
    this.handleCloseDialog();
    // this.setData({
    //   showCategoryDialog: true,
    // })
  },
  handleShowCity() {
    const {
      filters
    } = this.data;
    if (filters.city !== '全国') {
      wx.$mp.navigateTo({
        url: `/pages/citySearch/citySearch?city=${filters.city}`,
      })
    } else {
      wx.$mp.navigateTo({
        url: `/pages/citySearch/citySearch`,
      })
    }
    //  wx.$mp.navigateTo({
    //   url: '/pages/citySearch/citySearch',
    // })
    // this.handleCloseDialog();
    // this.setData({
    //   showCityDialog: true,
    // })
  },
  handleCloseDialog() {
    this.setData({
      // showCategoryDialog: false,
      showCityDialog: false,
    })
  },
  overallModalConfirm(e) {
    const eDetail = e.detail
    if (eDetail?.openSetting?.valueOfLocationAuthorized) {
      this.setLocation()
    }
  },
  handleShowPostiton() {
    wx.$mp.track({
      event: 'nearbyOutlets_open_click'
    })
    this.showOveralModal('store')
  },
  handleClosePostiton() {
    this.setData({
      showPositionDialog: false,
    })
  },
  setLocation() {
    app.autoAuthLocation().then(async (res) => {
      this.setData({
        isLocationAuthorized: true,
        latitude: res.latitude,
        longitude: res.longitude,
        outletsList: [],
        pageNum: 1,
      });
      await this.getCurrentCityData();
      await this.getList();
    }).catch((error) => {
      console.log('error', error);
    });
  },

  changeTab(e) {
    const {
      activeTab
    } = e.detail;
    this.setData({
      activeTab,
    })
  },
  changLocation(e) {
    const {
      latitude,
      longitude
    } = e.detail;
    this.setData({
      latitude,
      longitude,
    })
  },
  handleFilter: app.debounce(async function (e) {
    const {
      filters
    } = this.data;
    const {
      detail
    } = e;
    this.setData({
      pageNum: 1,
      filters: {
        ...filters,
        ...detail
      },
      outletsList: [],
      count: 0,
    })
    await this.getList()
  }),
  getMore() {
    const {
      hasMore
    } = this.data;
    if (hasMore) {
      const {
        pageNum
      } = this.data;
      this.setData({
        pageNum: pageNum + 1,
      })
      this.getList()
    }

  },
  async getCurrentCityData() {
    const {
      latitude,
      longitude,
    } = this.data;
    if (latitude) {
      const res = await getCurrentCity({
        latitude,
        longitude,
      });
      this.setData({
        filters: {
          city: res.data,
        }
      })
    }

  }
})
