package com.dz.gateway.swagger;

import com.dz.common.base.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 公共Controller
 * @author: Handy
 * @date: 2022/02/06 15:29
 */
@Slf4j
@RestController
public class GatewayController {

    @GetMapping(value = "/actuator/health")
    public String healthcheck() {
        return "ok";
    }


}
