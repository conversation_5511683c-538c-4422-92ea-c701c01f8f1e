package com.dz.common.core.constants;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;

public class ApplicationConstant {

    public static final String PATH = "muji/";
    @Value("${spring.application.name}")
    public String applicationName;
    @Value("${spring.profiles.active}")
    public String profile;
    @Value("${database.sharding.enable:false}")
    public Boolean openSharding;
    @Value("${service.single:false}")
    public Boolean singleService;
    @Value("${service.swagger:true}")
    public Boolean openSwagger;

    public ApplicationConstant() {
    }

    public boolean isDevProfile() {
        return StringUtils.isBlank(this.profile) || "DEV".equalsIgnoreCase(this.profile);
    }

    public boolean isUatProfile() {
        return "UAT".equalsIgnoreCase(this.profile);
    }

    public boolean isPreProfile() {
        return "PREV".equalsIgnoreCase(this.profile);
    }

    public boolean isProProfile() {
        return "PROD".equalsIgnoreCase(this.profile);
    }

}