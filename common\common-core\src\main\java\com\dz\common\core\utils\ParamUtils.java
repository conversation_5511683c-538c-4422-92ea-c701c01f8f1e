package com.dz.common.core.utils;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.core.constants.NumConstants;
import com.dz.common.core.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * 参数处理判断工具类
 * @author: Handy
 * @date: 2017/9/16
 */
public class ParamUtils {

    private static Pattern NUMBER_PATTERN = Pattern.compile("[0-9]*");

    /**
     * 判断INTEGER 是否为空或者 0
     * @return
     */
    public static boolean isNullOr0Integer(Integer num) {
        return null == num || num.intValue() < 1 ? true : false;
    }

    /**
     * 判断Long 是否为空或者 0
     * @return
     */
    public static boolean isNullOr0Long(Long num) {
        return null == num || num.intValue() < 1 ? true : false;
    }

    /**
     * Integer 转 int
     * @param num
     * @return
     */
    public static int Integer2int(Integer num) {
        if(null == num) {
            return 0;
        }else {
            return num.intValue();
        }
    }

    /**
     * Long 转 long
     * @param num
     * @return
     */
    public static long Long2long(Long num) {
        if(null == num) {
            return 0;
        }else {
            return num.longValue();
        }
    }

    /**
     * @Description: 将 1,2,3, 返回成一个 [1,2,3]的集合
     * @param strs
     * @return: List<Integer>
     */
    public static List<Integer> strs2List(String strs) {
        List<Integer> result = new ArrayList<Integer>();
        if (strs != null) {
            String arr[] = strs.split(",");
            for (String a : arr) {
                if (StringUtils.isNotBlank(a)) {
                    result.add(Integer.parseInt(a));
                }
            }
        }
        return result;
    }

    /**
     * @Description: 返回N个对象.toString的追加之和（忽略其中的null对象）
     * @param objects
     * @return: String
     */
    public static String append(Object... objects) {
        StringBuffer sb = new StringBuffer();
        for (Object object : objects) {
            if (object != null) {
                sb.append(object.toString());
            }
        }
        return sb.toString();
    }

    /**
     * @Description: 拼接参数
     * @param args
     *            a=11,b=22,c=,d=null
     * @return: String ?a=11&b=22
     */
    public static String queryString(String... args) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < args.length; i++) {
            String temp = args[i];
            if (StringUtils.isNotBlank(temp)) {
                String right = temp.split("=")[1];
                if (StringUtils.isNotBlank(right)) {
                    // 以上处理，是避免追加null参数
                    if (i == 0) {
                        sb.append("?").append(args[i]);
                    } else {
                        sb.append("&").append(args[i]);
                    }
                }
            }
        }
        return sb.toString();
    }

    /**
     * @Description: 去重list
     * @param list
     * @return: List<Integer>
     */
    public static List<Integer> reconnectedList(List<Integer> list) {
        List<Integer> result = new ArrayList<Integer>();
        if (null == list || list.isEmpty()) {
            return result;
        }
        Set<Integer> set = new HashSet<Integer>();
        for (Integer i : list) {
            set.add(i);
        }
        for (Integer i : set) {
            result.add(i);
        }
        return result;
    }

    /**
     * 替换emoji表情字符
     * @param val
     * @return
     */
    public static String replaceEmoji(String val){
        if(StringUtils.isNotBlank(val)){
            val = val.replaceAll("[\\ud800\\udc00-\\udbff\\udfff\\ud800-\\udfff]", "");
        }
        return val;
    }

    /**
     * 数字转汉字
     * @Date:2017/12/31 23:48
     * @Author:Handy
     * @param num
     */
    public static String numToCnChart(int num) {
        if(num < 0 || num > 9) {
            return "";
        }
        String[] array = new String[] {"零","一","二","三","四","五","六","七","八","九"};
        return array[num];
    }

    /**
     * 根据pageNo和pageSize 获取 offset limit 方式分页
     * @param pageNo
     * @param pageSize
     * @return
     */
    public static Integer[] getPageParam(Integer pageNo, Integer pageSize) {
        pageNo = null == pageNo || pageNo < 1 ? 1 : pageNo;
        pageSize = null == pageSize || pageSize < 1 ? 20 : pageSize;
        return new Integer[]{(pageNo - 1) * pageSize,pageSize};
    }

    /**
     * 校验启停用传参
     * @param state 启用:1,停用0
     */
    public static void checkStateParam(Integer state) {
        if(Objects.isNull(state) || !(Objects.equals(state, NumConstants.ZERO) || Objects.equals(state, NumConstants.ONE))){
            throw new BusinessException(ErrorCode.INTERNAL_ERROR);
        }
    }

}
