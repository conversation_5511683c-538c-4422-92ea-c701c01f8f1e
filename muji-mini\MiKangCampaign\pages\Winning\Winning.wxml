<!--signUp/pages/Winning/Winning.wxml 中奖页面-->
<my-page overallModal="{{overallModal}}" loading="{{loading1}}">
  <view class="page-container" style="background-image:url({{$cdn}}/MiKangCampaign/mk-prize-draw-bg.png);">
    <custom-header background=" transparent" type="{{1}}" isBackHidden="{{ isBackHidden }}" color="black" />
    <view class="prizeDraw1">
      <view class="activeRules" bindtap="activeRules">活动规则</view>
      <!-- 剩余抽奖次数为0 不显示去抽奖按钮 -->
      <!-- <view class="goLottery" wx:if="{{isShare && surplusCount!=0}}" bindtap="goLottery">
        <image class="lottery-img" src="{{$cdn}}/MiKangCampaign/winningIcon.png" mode="" />
        <view class="lottery-title">去抽奖</view>
      </view> -->
      <!-- <view class="MyCoupon" bind:tap="goMyCoupon">查看礼券</view> -->
      <!-- <view class="prizeDraw-title1 title"><text>您获得了1张健康美容品类85折券</text></view>
      <view class="prizeDraw-title2 title"><text>在【我的】-【我的礼券】中查看</text></view> -->
      <view class="swiper-wrap">
        <!-- indicator-color="#d8d8d8" indicator-active-color="#c7b49a"  -->
        <swiper current="{{current}}" style="height: 1080rpx;" indicator-dots="{{false}}" autoplay="{{false}}" bindchange="onSwiperChange" circular duration="{{duration}}">
          <block wx:for="{{list}}" wx:key="index">
            <swiper-item>
              <view class="item-wrap">
                <view class="tips">
                  <!-- <view class="tips-img">
                    <image src="{{$cdn}}/MiKangCampaign/Winning1.png" mode="" />
                  </view> -->
                  <view class="tips-title">
                    <!-- 健康美容
                    <view class="prizeDraw-title3" wx:if="{{true}}"> <text>恭喜您获得</text> </view> -->
                    <!-- 抽取的礼品 -->
                    <view class="prizeDraw-title3">
                      <text wx:if="{{item.prizesLevelCode!='6'}}">恭喜您抽到</text>
                      <text wx:else>恭喜您获得</text>
                    </view>
                    <view class="prizeDraw-title4"><text>{{item.prizesName||""}}</text></view>
                  </view>
                </view>
                <view class="picture">
                  <image lazy-load src="{{$cdn}}/MiKangCampaign/{{item.detailImageUrl}}" mode="" />
                  <view class="slider-point-Left" bind:tap="goToPrev" data-type='left' wx:if="{{list.length>1}}">
                    <view class="point_left"></view>
                  </view>
                  <view class="slider-point-Right" data-type='right' bind:tap="goToNext" wx:if="{{list.length>1}}">
                    <view class="point_right"></view>
                  </view>
                </view>
                <view class="bottom-tips" wx:if="{{item.description =='50积分'}}">
                  可前往
                  <text class="height-light">「积分商城」</text>
                  使用
                  <view class="text1-MyCoupon" bind:tap="goMylife"><text>前往积分商城</text></view>
                  <!-- pages/life/life -->
                </view>
                <view class="bottom-tips" wx:else>
                  <view>奖品已发放至<text class="height-light">「我的礼券」</text>中</view>
                  <view class="text1">*兑换详情详见卡券规则</view>
                  <view class="text1-MyCoupon" bind:tap="goMyCoupon"><text>查看礼券</text></view>
                </view>
              </view>
            </swiper-item>
          </block>
        </swiper>
        <view class="slider-point-wrap" wx:if="{{list.length>1}}">
          <!--  -->
          <view class="slider-point" style="top:auto;--left:auto;--right:auto;">
            <view class="slider-point-item {{current == index?'active':''}}" wx:for="{{list}}" wx:key="index"></view>
          </view>
        </view>
        <view class="prize-once-again" bind:tap="clickPopu">
          <image src="{{$cdn}}/MiKangCampaign/mk-to-report-icon.png" mode="aspectFit" />
        </view>
      </view>
      <view class="bottom-box">
        <basic-button wx:if="{{surplusCount>0}}" width="{{650}}" size="large" bind:click="click">
          抽取惊喜礼品
        </basic-button>

        <basic-button wx:if="{{isShare && surplusCount < 1}}" width="{{650}}" size="large" bind:click="goIndex">
          返回活动首页
        </basic-button>

        <view class="isShare" wx:if="{{!isShare && surplusCount < 1}}">
          分享体验报告可再次抽奖
        </view>

        <!-- <basic-button width="{{650}}" size="large" bind:click="clickPopu">
          查看我的体验报告
        </basic-button> -->
        <!-- 判断是否分享过海报 没有（false）有（true) -->
        <!-- <view wx:if="{{!isShare}}" class="isShare">分享获得抽奖机会</view> -->
        <!-- <view wx:if="{{isShare && surplusCount>0}}" class="isShare">您拥有一次抽奖机会</view> -->
      </view>

    </view>
    <!-- 海报弹框 -->
    <!-- <canvas class="page-canvasone" id="myCanvasOne" type="2d"></canvas>
    <sharePopup show="{{isShow}}" background="{{'#00000000'}}" closeable="{{false}}" borderRadius="{{0}}" bindclose="closePopup">
      <view class="share-popup" wx:if="{{isShow}}">
        <image class="image" src="{{srcOne}}" alt="" />
        <view class="clock-btn tab-bar">
          <basic-button width="{{305}}" size="large" bindtap="shareImg">
            保存海报
          </basic-button>
          <basic-button width="{{305}}" size="large" bindtap="share">
            {{!isShare?'分享获得抽奖机会':'分享好友'}}
          </basic-button>
        </view>
      </view>
    </sharePopup> -->
    <!-- 生成海报 -->
    <!-- <canvas class="page-canvas" id="myCanvas" type="2d"></canvas>
    <share-poster ImgStyle="{{'v2'}}" src="{{src}}" show="{{show}}" bindsuccess="success" bindclose="close"></share-poster>
    <view wx:if="{{show}}" class="share_mask"></view> -->
    <!-- 第一次分享 恭喜或得一次抽奖机会 -->
    <!-- <Popup isShow="{{showPopup}}" info="{{infoLottery}}" bindclose="closeShowPopup"></Popup> -->
  </view>
</my-page>