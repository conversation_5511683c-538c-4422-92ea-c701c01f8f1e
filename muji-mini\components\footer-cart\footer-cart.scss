@import "assets/scss/config";

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 180rpx;
  background: #fff;
  display: flex;
  justify-content: space-between;
  // align-items: center;
  padding: 40rpx 40rpx 0 20rpx;
  box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.04);
    box-sizing: border-box;
    // margin-top: 30rpx;
  .cart-btn {
    display: flex;
    flex-direction: column;
    align-items: center;

    margin-top: -4rpx;
    padding-top: 10rpx;
    width: 94rpx;

    font-feature-settings: "tnum";
    font-variant-numeric: tabular-nums;

    .iconfont {
      position: relative;
      font-size: 48rpx;
      color: var(--text-black-color);

      .count {
        box-sizing: border-box;
        position: absolute;
        top: -6rpx;
        left: 44rpx;
        color: #fff;
        background-color: var(--primary-color);
        border-radius: 100rpx;
        padding: 0 6rpx;
        min-width: 24rpx;
        height: 24rpx;
        line-height: 24rpx;
        font-size: 16rpx;
        text-align: center;
        white-space: nowrap;
      }
    }

    .cart-icon {
      display: none;
      width: 48rpx;
      height: 48rpx;
      background-color: red;
      // margin-bottom: 6rpx;
    }

    text {
      //width: 54rpx;
      height: 25rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 18rpx;
      color: #666666;
      line-height: 25rpx;
      text-align: center;
      font-style: normal;
      white-space: nowrap;
    }
  }

  .action-btns {
    //flex: 1;
    display: flex;
    justify-content: space-between;
    width: 550rpx;

    button {
      width: 240rpx;
      height: 80rpx;
      //border-radius: 40rpx;
      font-size: 28rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .add-cart-btn {
      background: #FFE4E4;
      color: var(--primary-color);
    }

    .buy-btn {
      background: var(--primary-color);
      color: #fff;
    }
  }
}
