package com.dz.ms.basic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.basic.SubscribeMsgSendDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.enums.SubscribeMsgEnum;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.dto.MpMsgDTO;
import com.dz.ms.basic.dto.MpMsgPushDTO;
import com.dz.common.core.dto.basic.MpMsgSubscribeUserDTO;
import com.dz.ms.basic.entity.ChannelLink;
import com.dz.ms.basic.entity.MpMsgCrowd;
import com.dz.ms.basic.entity.MpMsgPush;
import com.dz.ms.basic.entity.MpMsgSubscribe;
import com.dz.ms.basic.mapper.MpMsgCrowdMapper;
import com.dz.ms.basic.mapper.MpMsgPushMapper;
import com.dz.ms.basic.mapper.MpMsgSubscribeLogMapper;
import com.dz.ms.basic.mapper.MpMsgSubscribeMapper;
import com.dz.ms.basic.service.MpMsgPushService;
import com.dz.ms.basic.service.MpMsgService;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 小程序/公众号模板消息推送任务
 * @author: Handy
 * @date:   2023/07/07 14:22
 */
@Service
public class MpMsgPushServiceImpl extends ServiceImpl<MpMsgPushMapper,MpMsgPush> implements MpMsgPushService {

	@Resource
    private MpMsgPushMapper mpMsgPushMapper;
    @Resource
    private MpMsgSubscribeMapper mpMsgSubscribeMapper;
    @Resource
    private MpMsgService mpMsgService;
    @Autowired
    private MpMsgSubscribeLogMapper mpMsgSubscribeLogMapper;
    @Autowired
    private MpMsgCrowdMapper mpMsgCrowdMapper;

    /**
     * 分页查询小程序/公众号模板消息推送任务
     * @param param
     * @return PageInfo<MpMsgPushDTO>
     */
    @Override
    public PageInfo<MpMsgPushDTO> getMpMsgPushList(MpMsgPushDTO param) {
        MpMsgPush mpMsgPush = BeanCopierUtils.convertObjectTrim(param,MpMsgPush.class);
        IPage<MpMsgPush> page = mpMsgPushMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), new QueryWrapper<>(mpMsgPush));
        return new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),BeanCopierUtils.convertList(page.getRecords(), MpMsgPushDTO.class));
    }

    /**
     * 根据ID查询小程序/公众号模板消息推送任务
     * @param id
     * @return MpMsgPushDTO
     */
    @Override
    public MpMsgPushDTO getMpMsgPushById(Long id) {
        MpMsgPush mpMsgPush = mpMsgPushMapper.selectById(id);
        return BeanCopierUtils.convertObject(mpMsgPush,MpMsgPushDTO.class);
    }

    /**
     * 保存小程序/公众号模板消息推送任务
     * @param param
     * @return Long
     */
    @Override
    public Long saveMpMsgPush(MpMsgPushDTO param) {
        MpMsgPush mpMsgPush = new MpMsgPush(param.getId(), param.getPushName(), param.getMsgId(), param.getTemplateId(), param.getTemplateName(), param.getMsgContent(), param.getPagePath(), param.getPushType(), param.getPushTime(), param.getPushNumber(), param.getState());
        if(ParamUtils.isNullOr0Long(mpMsgPush.getId())) {
            mpMsgPushMapper.insert(mpMsgPush);
        }
        else {
            mpMsgPushMapper.updateById(mpMsgPush);
        }
        return mpMsgPush.getId();
    }

    /**
     * 根据ID删除小程序/公众号模板消息推送任务
     * @param param
     */
    @Override
    public void deleteMpMsgPushById(IdCodeDTO param) {
        mpMsgPushMapper.deleteById(param.getId());
    }

    /**
     * 活动开始提醒消息发送
     * @param tenantId
     * @param content
     * @param path
     * @param uids
     */
    @Override
    public void activityStartMsgPush(SubscribeMsgEnum enums, Long tenantId,String[] content,String path) {
        if(null == SecurityContext.getUser() || null == SecurityContext.getUser().getTenantId() || 0 == SecurityContext.getUser().getTenantId()) {
            SecurityContext.setUser(new CurrentUserDTO(tenantId,0L));
        }
        //分页查询订阅用户
//        Integer pageSize = 900;
//        Integer pageNum = 1;
//        while (true){
            //分页查询mpmsgcrowd中的人群
            List<MpMsgCrowd> page = mpMsgCrowdMapper.selectList(new LambdaQueryWrapper<>());
            if(CollectionUtils.isEmpty(page)){
//                break;
                return;
            }
            SubscribeMsgSendDTO msgSend = new SubscribeMsgSendDTO(enums, path ,content);
            List<String> openids = new ArrayList<>();
            for (MpMsgCrowd msgCrowd : page) {
                openids.add(msgCrowd.getOpenid());
                msgCrowd.setIsPush(1);
                mpMsgCrowdMapper.updateById(msgCrowd);
            }
            msgSend.setOpenids(openids);
            msgSend.setMsgCode(enums.getMsgCode());
            mpMsgService.sendSubscribeMsgCatch(msgSend,tenantId);
            //批量修改推送次数
            mpMsgSubscribeMapper.updatePushNumberByIds(openids);
//            pageNum++;
//        }
    }


    /**
     * 第二轮报名开始提醒消息发送
     *
     * @param tenantId
     * @param content
     * @param path
     * @param uids
     */
    @Override
    public void msgPushBySubscribeLog(SubscribeMsgEnum enums, Long tenantId,String[] content,String path, List<Long> uids) {
        if(null == SecurityContext.getUser() || null == SecurityContext.getUser().getTenantId() || 0 == SecurityContext.getUser().getTenantId()) {
            SecurityContext.setUser(new CurrentUserDTO(tenantId,0L));
        }
        MpMsgDTO mpMsg = mpMsgService.getSubscribeMsgByCode(enums.getMsgCode());

        List<MpMsgSubscribeUserDTO> list = mpMsgSubscribeLogMapper.selectUserSubscribeList(uids, mpMsg.getTemplateId());
        if(CollectionUtils.isEmpty(list)) {
            return;
        }
        SubscribeMsgSendDTO msgSend = new SubscribeMsgSendDTO(enums, path ,content);
//        msgSend.setOpenids(openids);
//        msgSend.setMsgCode(enums.getMsgCode());
//        mpMsgService.sendSubscribeMsgCatch(msgSend,tenantId);
//        //批量修改推送次数
//        mpMsgSubscribeMapper.updatePushNumberByIds(openids);


        List<Long> ids = new ArrayList<>();
        for (MpMsgSubscribeUserDTO subscribeUser : list) {
            List<String> openids = new ArrayList<>();
            openids.add(subscribeUser.getOpenid());
            msgSend.setOpenids(openids);
            msgSend.setMsgCode(enums.getMsgCode());
            mpMsgService.sendSubscribeMsgCatch(msgSend,tenantId);
            ids.add(subscribeUser.getId());
        }
        mpMsgSubscribeLogMapper.updateStateByIds(ids, mpMsg.getTemplateId());
    }

    @Override
    public void sendSubscribeMsg(SubscribeMsgSendDTO param, Long tenantId) {
        List<String> openids = param.getOpenids();
        if(CollectionUtils.isEmpty(openids)) {
            return;
        }
        mpMsgService.sendSubscribeMsgCatch(param,tenantId);
    }
}