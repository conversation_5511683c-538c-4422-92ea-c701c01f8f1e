package com.dz.ms.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.ms.product.dto.TaskLogDTO;
import com.dz.ms.product.entity.TaskLog;

/**
 * 日志-货架商品库存任务接口
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:53
 */
public interface TaskLogService extends IService<TaskLog> {

    /**
     * 分页查询日志-货架商品库存任务
     *
     * @param param
     * @return PageInfo<TaskLogDTO>
     */
    public PageInfo<TaskLogDTO> getTaskLogList(TaskLogDTO param);

    /**
     * 根据ID查询日志-货架商品库存任务
     *
     * @param id
     * @return TaskLogDTO
     */
    public TaskLogDTO getTaskLogById(Long id);

    /**
     * 保存日志-货架商品库存任务
     *
     * @param param
     * @return Long
     */
    public Long saveTaskLog(TaskLogDTO param);

    /**
     * 根据ID删除日志-货架商品库存任务
     *
     * @param param
     */
    public void deleteTaskLogById(IdCodeDTO param);

}
