package com.dz.ms.basic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "小程序常规页面模板分组更新")
public class MiniappTemplateGroupDTO {
    @ApiModelProperty(value = "页面组ID")
    private Long id;
    @ApiModelProperty(value = "模板ID列表")
    private List<Long> templateIds;
}
