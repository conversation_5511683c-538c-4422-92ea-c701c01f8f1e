package com.dz.ms.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.user.entity.SysUsersStore;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 系统用户可用门店Mapper
 * @author: Handy
 * @date:   2023/05/15 21:48
 */
@Repository
public interface SysUsersStoreMapper extends BaseMapper<SysUsersStore> {

    /** 批量新增 */
    int insertBatch(@Param("uid") Long uid,@Param("tenantId") Long tenantId, @Param("storeIds")List<Long> storeIds);

}
