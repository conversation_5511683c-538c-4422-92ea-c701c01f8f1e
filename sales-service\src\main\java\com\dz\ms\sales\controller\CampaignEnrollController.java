package com.dz.ms.sales.controller;


import com.dz.common.base.dto.BaseDTO;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.adaptor.SiftEnrollDTO;
import com.dz.common.core.fegin.sales.CampaignEnrollFeignClient;
import com.dz.ms.sales.dto.CampaignEnrollDTO;
import com.dz.ms.sales.dto.CampaignEnrollParamDTO;
import com.dz.ms.sales.dto.CampaignEnrollRosterListDTO;
import com.dz.ms.sales.service.CampaignEnrollService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/19
 */
@Api(tags = "报名信息接口")
@RestController
public class CampaignEnrollController implements CampaignEnrollFeignClient {

    @Resource
    private CampaignEnrollService campaignEnrollService;


    @ApiOperation("获取报名信息列表")
    @GetMapping(value = "/app/enroll/list")
    public Result<List<CampaignEnrollRosterListDTO>> getCampaignEnrollPage() {
        Result<List<CampaignEnrollRosterListDTO>> result = new Result<>();
        List<CampaignEnrollRosterListDTO> pageInfo = campaignEnrollService.getCampaignEnrollPage();
        result.setData(pageInfo);
        return result;
    }

    @ApiOperation("获取报名信息详情")
    @GetMapping(value = "/app/enroll/info")
    public Result<CampaignEnrollDTO> getCampaignEnrollInfo(@RequestParam("campaignCode") String campaignCode) {
        Result<CampaignEnrollDTO> result = new Result<>();
        CampaignEnrollDTO dto = campaignEnrollService.getCampaignEnrollInfo(campaignCode);
        result.setData(dto);
        return result;
    }

    @ApiOperation("填写报名信息")
    @PostMapping(value = "/app/enroll")
    public Result<Long> addCampaignEnroll(@RequestBody @Validated CampaignEnrollParamDTO param) {
        Result<Long> result = new Result<>();
        Long id = campaignEnrollService.addCampaignEnroll(param);
        result.setData(id);
        return result;
    }

    @ApiOperation("根据渠道筛选报名信息")
    @PostMapping(value = "/remote/enroll/channel/sift")
    public Result<Boolean> siftCampaignEnrollByChannel(@RequestBody SiftEnrollDTO param) {
        Result<Boolean> result = new Result<>();
        Boolean b = campaignEnrollService.siftCampaignEnrollByChannel(param.getCampaignCode(), param.getJobParams());
        result.setData(b);
        return result;
    }

    @ApiOperation("报名成功推送消息")
    @PostMapping(value = "/remote/enroll/success/pushMsg")
    public Result<Boolean> enrollSuccessPushMsgJob(@RequestParam("campaignCode") String campaignCode) {
        Result<Boolean> result = new Result<>();
        campaignEnrollService.enrollSuccessPushMsgJob(campaignCode);
        result.setData(true);
        return result;
    }

    @ApiOperation("活动报名失败后推送第二轮报名订阅消息")
    @PostMapping(value = "/remote/enroll/fail/pushMsg")
    public Result<Boolean> enrollFailPushMsgJob(@RequestParam("campaignCode") String campaignCode) {
        Result<Boolean> result = new Result<>();
        campaignEnrollService.enrollFailPushMsgJob(campaignCode);
        result.setData(true);
        return result;
    }
}
