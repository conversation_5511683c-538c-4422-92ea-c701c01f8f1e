package com.dz.ms.order.dto;
import java.util.Date;
import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 核销记录DTO
 * @author: Handy
 * @date:   2024/06/06 14:47
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "核销记录")
public class VerifyRecordDTO extends BaseDTO {

    @ApiModelProperty(value = "ID")
    private Long id;
    @ApiModelProperty(value = "核销编号（预约编号/订单编号/卡券编号）")
    private String verifyCode;
    @ApiModelProperty(value = "核销ID（预约/订单/卡券ID）")
    private Long verifyId;
    @ApiModelProperty(value = "核销类型(1兑礼订单 2预约 3卡券)")
    private Integer verifyType;
    @ApiModelProperty(value = "客户ID")
    private Long uid;
    @ApiModelProperty(value = "客户姓名")
    private String userName;
    @ApiModelProperty(value = "客户性别(0未知 1男 2女)")
    private Integer gender;
    @ApiModelProperty(value = "客户手机号")
    private String mobile;
    @ApiModelProperty(value = "客户等级")
    private String cardLevel;
    @ApiModelProperty(value = "核销记录名称")
    private String recordName;
    @ApiModelProperty(value = "核销项目名称")
    private String itemName;
    @ApiModelProperty(value = "预约日期(格式yyyy-MM-dd)")
    private String bookingDate;
    @ApiModelProperty(value = "预约时间段")
    private String timeSlot;
    @ApiModelProperty(value = "可核销开始时间")
    private Date verifyStartTime;
    @ApiModelProperty(value = "可核销结束时间")
    private Date verifyEndTime;
    @ApiModelProperty(value = "预约门店编码")
    private String storeCode;
    @ApiModelProperty(value = "预约门店名称")
    private String storeName;
    @ApiModelProperty(value = "预约/分配员工编号")
    private String empCode;
    @ApiModelProperty(value = "核销门店编码")
    private String verifyStoreCode;
    @ApiModelProperty(value = "核销门店名称")
    private String verifyStoreName;
    @ApiModelProperty(value = "核销人类型 1后台用户 2导购")
    private Integer verifierType;
    @ApiModelProperty(value = "核销员工编号")
    private String verifierCode;
    @ApiModelProperty(value = "核销员工姓名")
    private String verifierName;
    @ApiModelProperty(value = "取消人类型 1后台用户 2导购")
    private Integer cancelerType;
    @ApiModelProperty(value = "取消人编码")
    private String cancelerCode;
    @ApiModelProperty(value = "取消人名称")
    private String cancelerName;
    @ApiModelProperty(value = "修改人类型 1后台用户 2导购")
    private Integer updaterType;
    @ApiModelProperty(value = "修改人编码")
    private String updaterCode;
    @ApiModelProperty(value = "修改人名称")
    private String updaterName;
    @ApiModelProperty(value = "预约消耗类型 0无消耗 1积分 2卡券 3权益 4pin_code 5付费")
    private Integer consumeType;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "更新人")
    private Long modifier;
    @ApiModelProperty(value = "更新时间")
    private Date modified;

}
