package com.dz.common.core.enums;

/**
 * 定时任务枚举
 * @author: Handy
 * @date:   2022/07/07 16:36
 */
public enum JobEnum {

	EMPLOYEE_JOB("员工启用停用定时任务","员工启用停用定时任务","EmployeeJobProcessor",1),
	EMPLOYEE_SYN_JOB("定时任务同步企业微信员工","定时任务同步企业微信员工","EmployeeSynJobProcessor",1),
	POINTS_JOB("员工导入积分定时任务","员工导入积分定时任务","PointsJobProcessor",1),
	PRODUCT_SYN_JOB("同步中台商品定时任务","同步中台商品定时任务","ProductSynInfoProcessor",1);

	/** 任务名称 */
    private String jobName;
	/** 任务描述 */
	private String jobDescription;
	/** 处理器类名 */
	private String processorClass;
	/** 执行器类型 1单机执行 2广播执行 3MapReduce 4Map */
	private Integer executeType;

	JobEnum(String jobName, String jobDescription, String processorClass, Integer executeType) {
		this.jobName = jobName;
		this.jobDescription = jobDescription;
		this.processorClass = processorClass;
		this.executeType = executeType;
	}

	public String getJobName() {
		return jobName;
	}

	public String getJobDescription() {
		return jobDescription;
	}

	public String getProcessorClass() {
		return processorClass;
	}

	public Integer getExecuteType() {
		return executeType;
	}
}
