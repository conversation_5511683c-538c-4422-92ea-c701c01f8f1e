@import "assets/scss/common";
@import "assets/scss/config";

@keyframes show {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.selfModal {
  position: relative;
  background: transparent;
  display: flex;
  flex-direction: column;
  align-items: center;
  // padding: 90rpx 0;
  box-sizing: border-box;

  &-content {
    // overflow: hidden;
    width: 610rpx;
    border-radius: var(--radius);
    animation: show 0.6s forwards;
    box-sizing: border-box;
    box-shadow: 0rpx 22rpx 74rpx 0rpx rgba(143, 119, 85, 0.35);
  }

  &-close {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 80rpx;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-closeBox {
    height: 80rpx;
    width: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
