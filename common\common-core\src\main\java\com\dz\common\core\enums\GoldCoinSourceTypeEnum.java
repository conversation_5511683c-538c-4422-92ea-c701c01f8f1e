package com.dz.common.core.enums;

import java.util.Objects;

/**
 * 金币对账单来源类行枚举
 * <AUTHOR>
 **/
public enum GoldCoinSourceTypeEnum {

    LOTTERY_EXPENSE(0, GoldCoinStatementTypeEnum.EXPENSE, "抽奖消耗"),
    POINTS_EXPENSE(1, GoldCoinStatementTypeEnum.EXPENSE, "积分兑换"),
    SIGN_IN_INCOME(10, GoldCoinStatementTypeEnum.INCOME, "签到奖励"),
    LOTTERY_INCOME(11, GoldCoinStatementTypeEnum.INCOME, "抽奖奖品"),
    TASK_REGISTER(12, GoldCoinStatementTypeEnum.INCOME, "新用户注册"),
    TASK_INVITE_NEW_USER(13, GoldCoinStatementTypeEnum.INCOME, "邀请新人注册"),
    TASK_INVITER(14, GoldCoinStatementTypeEnum.INCOME, "邀请新人注册奖励"),
    TASK_IMPROVE_INFO(16, Gold<PERSON>oinStatementTypeEnum.INCOME, "完善信息"),
    POINTS_INCOME(17, GoldCoinStatementTypeEnum.INCOME, "积分兑换"),
    DRAW_EXPENSE(18, GoldCoinStatementTypeEnum.EXPENSE, "抽签消耗"),
    BUY_PRODUCT_TASK(19, GoldCoinStatementTypeEnum.INCOME, "完成购买商品任务"),
    BOOKING_TASK(20, GoldCoinStatementTypeEnum.INCOME, "完成预约任务"),
    ;

    private final Integer code;
    private final Integer type;
    private final String value;

    GoldCoinSourceTypeEnum(Integer code, GoldCoinStatementTypeEnum type, String value) {
        this.code = code;
        this.type = type.getCode();
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public Integer getType() {
        return type;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByCode(Integer code) {
        GoldCoinSourceTypeEnum sourceTypeEnum = getByCode(code);
        if (sourceTypeEnum == null) {
            return null;
        }
        return sourceTypeEnum.value;
    }
    public static GoldCoinSourceTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (GoldCoinSourceTypeEnum resultEnum : GoldCoinSourceTypeEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum;
            }
        }
        return null;
    }
}
