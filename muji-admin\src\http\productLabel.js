// 所有的命名必须全局唯一
import service from '@/utils/request.js'


// 列表
export function tag_infoList(data) {
    return service({
        url: '/crm/product/tag_info/list',
        method: 'get',
        data
    })
}
// 列表 新增
export function tag_infoAdd(data) {
    return service({
        url: '/crm/product/tag_info/add',
        method: 'post',
        data
    })
}

// 列表编辑
export function tag_infoUpdate(data) {
    return service({
        url: '/crm/product/tag_info/update',
        method: 'post',
        data
    })
}
// 列表 删除
export function tag_infoDelete(data) {
    return service({
        url: '/crm/product/tag_info/delete',
        method: 'post',
        data
    })
}

export function tag_info(data) {
    return service({
        url: '/crm/product/tag_info/info',
        method: 'get',
        data
    })
}
//标签不带分页的查询 

export function tag_info_noPage(data) {
    return service({
        url: '/crm/product/tag_info/no_page_list',
        method: 'get',
        data
    })
}

// 订单列表  /crm/order/exchange_order/list
export function orderexchange_orderlist(data) {
    return service({
        url: '/crm/order/exchange_order/list',
        method: 'get',
        data
    })
}
//   下载发货模板

export function exchange_ordertemplate(data) {
    return service({
        url: '/crm/order/exchange_order/template',
        method: 'get',
        data,
        responseType: 'blob',
    })
}
// 批量发货

export function exchange_orderimport_delivery(data) {
    return service({
        url: '/crm/order/exchange_order/import_delivery',
        method: 'post',
        headers: { 'Content-Type': 'multipart/form-data' },
        data,

    })
}
//  
export function exchange_orderdelivery(data) {
    return service({
        url: '/crm/order/exchange_order/delivery',
        method: 'post',
        data,

    })
}