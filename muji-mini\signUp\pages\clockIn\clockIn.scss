/* signUp/pages/clockIn/clockIn.wxss */
.page-container {
  // background-color: rgba(255, 235, 205, 0.534);
  background-size: 100% 100%;
  overflow: hidden;

  .page-content {
    padding-top: 34rpx;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
  }

  .clockIn {
    flex-shrink: 0;
    height: 100%;
    position: relative;

    .clockIn-wrap {
      flex: 1;
      height: 100%;
      // padding-top: 34rpx;
    }

    &-title {
      margin: 0 44rpx 0 40rpx;
      height: 151rpx;
      background-color: #F8F6ED;
      display: flex;
      font-family: MUJIFont2020;
      font-weight: 300;
      font-size: 33rpx;
      color: #3D3D3D;
      line-height: 47rpx;
      text-align: center;
      font-style: normal;
      text-transform: none;
      padding-left: 50rpx;
      align-items: center;

      &-wrap {
        height: 79rpx;
        display: flex;
        align-items: flex-end;
        margin-left: 30rpx;
      }

      .num {
        width: 91rpx;
        height: 79rpx;
        font-family: MUJIFont2020,
          SourceHanSansCN;
        font-weight: 300;
        font-size: 60rpx;
        color: #3C3C43;
        line-height: 86rpx;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }

    }

    &-icon {
      width: 14rpx;
      height: 14rpx;
      background: #C8B49A;
      border-radius: 50%;
      margin-top: 40rpx;

    }

    .score {
      // min-height: calc(100% - 173rpx);
      margin: 0 44rpx 0 40rpx;
      background-color: white;
      padding-top: 41rpx;
      padding-bottom: 72rpx;

      .van-uploader {
        .van-uploader__wrapper {
          .van-uploader__preview {
            &:nth-child(3n) {
              margin-right: 0rpx;
            }
          }
        }
      }

      .picture {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 172rpx;
        height: 172rpx;
        background: #D8D8D8;
        font-weight: 700;
        font-size: 22rpx;
        color: #FFFFFF;
        line-height: 25rpx;
        text-align: center;

        .img {
          width: 43rpx;
          height: 43rpx;
        }

        .img-text {
          font-weight: 700;
          font-size: 22rpx;
          margin-top: 14rpx;
        }
      }

      .textarea {
        width: 556rpx;
        position: relative;
        // padding-bottom: 41rpx;
        box-sizing: border-box;
        margin-bottom: 60rpx;

        textarea {
          width: 100%;
          font-size: 20rpx;
          color: #979797;
          line-height: 29rpx;
          letter-spacing: 2rpx;
          text-align: left;
        }

        .textNum {
          position: absolute;
          right: 12rpx;
          bottom: 0rpx;
          font-weight: 400;
          font-size: 20rpx;
          color: #3C3C43;
          line-height: 29rpx;
          letter-spacing: 3rpx;
          text-align: right;
        }
      }

      .prompt {
        font-family: SourceHanSansCN;
        font-weight: 700;
        font-size: 20rpx;
        color: #979797;
        line-height: 29rpx;
        letter-spacing: 4rpx;
        margin-bottom: 40rpx;
        margin-left: 60rpx;
        // margin-top: 22rpx;
      }

      .skin {
        display: flex;
        flex-wrap: wrap;

        .skin-item {
          padding: 22rpx 44rpx;
          background: #D8D8D8;
          font-family: MUJIFont2020,
            SourceHanSansCN;
          font-weight: 300;
          font-size: 24rpx;
          color: var(--text-black-color);
          line-height: 43rpx;
          letter-spacing: 2rpx;
          text-align: left;
          margin-right: 20rpx;
          margin-bottom: 20rpx;
          box-sizing: border-box;

          &:nth-last-child(2) {
            margin-right: 0rpx;
          }
        }

        .active {
          background: #C8B49A;
          color: #FFFFFF;
        }
      }

      .answer {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding-left: 14rpx;
        padding-left: 60rpx;
        padding-right: 60rpx;
        margin-bottom: 40rpx;
        box-sizing: border-box;

        .answer-title {
          // height: 41rpx;
          font-family: MUJIFont2020,
            SourceHanSansCN;
          font-weight: 400;
          font-size: 28rpx;
          color: #2E2E2E;
          line-height: 40rpx;
          letter-spacing: 2rpx;
          margin-bottom: 20rpx;
        }

        .answer-text {
          height: 32rpx;
          font-family: MUJIFont2020,
            SourceHanSansCN;
          font-weight: 400;
          font-size: 22rpx;
          color: #979797;
          line-height: 32rpx;
          letter-spacing: 3rpx;
          text-align: center;
        }

        .answer-star {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;

          // margin-top: 20rpx;
          // margin-bottom: 20rpx;


          .num {
            font-family: MUJIFont2020,
              SourceHanSansCN;
            font-weight: 400;
            font-size: 28rpx;
            color: #2E2E2E;
            line-height: 40rpx;
            letter-spacing: 6rpx;
            text-align: center;
            // margin-left: 40rpx;

            .key {
              font-family: MUJIFont2020,
                SourceHanSansCN;
              font-weight: 400;
              font-size: 36rpx;
              color: #3C3C43;
              letter-spacing: 8rpx;
            }
          }
        }
      }

      .answer1 {
        border-bottom: 1rpx solid #D8D8D8;
      }
    }
  }

  .bottom-box {
    margin-top: 172rpx;
    margin-bottom: env(safe-area-inset-bottom);
    /* 避免被系统底部遮挡 */
    display: flex;
    justify-content: center;
  }
}
