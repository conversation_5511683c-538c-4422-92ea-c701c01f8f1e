.page-container {
  width: 100vw;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow-x: hidden;

  .page-coupon {
    flex: 1;
    box-sizing: border-box;
    margin: 30rpx 40rpx 0 40rpx;

    &-top {
      padding: 47rpx 40rpx 54rpx 40rpx;
    }

    .coupon-title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 32rpx;
      color: #3c3c43;
      line-height: 45rpx;
      letter-spacing: 1px;
      text-align: left;
      font-style: normal;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .coupon-des {
      height: 36rpx;
      margin-top: 7rpx;
      font-family: NotoSansHans;
      font-weight: 400;
      font-size: 24rpx;
      color: #888888;
      // line-height: 36rpx;
      letter-spacing: 1px;
      text-align: left;
      font-style: normal;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .coupon-date {
      margin-top: 40rpx;
      margin-bottom: 40rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #3c3c43;
      // line-height: 30rpx;
      text-align: left;
      font-style: normal;
    }

    .coupon-image {
      // padding-top: 40rpx;
      position: relative;
      // margin: 0 auto;
      // width: 270rpx;
      // height: 270rpx;
      background: #fafafa;
      display: flex;
      align-items: center;
      justify-content: center;

      image {
        overflow: visible !important;
      }

      // image {
      //   width: 100%;
      //   height: 100%;
      // }

      .coupon-qrCode {
        position: absolute;
        left: 99999rpx;
        //  margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 250rpx;
        height: 250rpx;
      }
    }

    .coupon-tips {
      margin-top: 20rpx;
      font-family: NotoSansHans;
      font-weight: 400;
      font-size: 24rpx;
      color: #888888;
      // line-height: 36rpx;
      text-align: center;
      font-style: normal;
    }

    .coupon-code {
      display: flex;
      // align-items: center;
      justify-content: center;
      margin-top: 37rpx;

      .coupon-code-txt {
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1;
        font-family: MUJIFont2020, MUJIFont2020;
        font-weight: 300;
        font-size: 22rpx;
        color: #3c3c43;
        // line-height: 48rpx;
        text-align: center;
        font-style: normal;
        // overflow: hidden;
        // white-space: nowrap;
        // text-overflow: ellipsis;
      }

      .copy {
        width: 48rpx;
        margin-left: 10rpx;
        font-family: NotoSansHans;
        font-weight: 400;
        font-size: 22rpx;
        color: #3c3c43;
        // line-height: 36rpx;
        text-align: left;
        font-style: normal;
      }
    }
    .coupon-time {
      text-align: center;
      font-weight: 300;
      font-size: 20rpx;
      color: #000;
      line-height: 36rpx;
      font-family: "MUJIFont2020", SourceHanSansCN;
      text {
        color: #cebb94;
      }
    }
    .coupon-BarCode {
      margin-top: 30rpx;
      // width: 670rpx;
      // height: 288rpx;
      background: #fafafa;
      padding: 60rpx 40rpx 60rpx 40rpx;

      &-hearder {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .coupon-BarCode-text {
        font-family: NotoSansHans;
        font-weight: 500;
        font-size: 28rpx;
        color: #3c3c43;
        line-height: 32rpx;
        letter-spacing: 1px;
        text-align: left;
        font-style: normal;
      }

      .coupon-BarCode-ti {
        display: flex;
        align-items: center;
        font-family: NotoSansHans;
        font-weight: 400;
        font-size: 24rpx;
        color: #888888;
        line-height: 32rpx;
        text-align: right;
        font-style: normal;

        .toggle-icon {
          margin-left: 10rpx;
          width: 32rpx;
          height: 32rpx;
        }
      }

      .coupon-BarCode-code {
        margin: 0 auto;
        display: flex;
        justify-content: center;
        align-items: center;
        // width: 550rpx;
        // height: 96rpx;
        padding-top: 40rpx;

        .barcode-img {
          height: 48px;
          width: 550px;
        }

        .code {
          position: fixed;
          right: 9000px;
          z-index: -1 !important;
        }
      }
    }

    .coupon-rule {
      margin-top: 30rpx;
    }
  }

  .active {
    z-index: 99999;
    opacity: 0.3 !important;
    background: #fafafa;
  }
}
