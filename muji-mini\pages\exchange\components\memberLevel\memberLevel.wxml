<view class="level-box" style="opacity: {{ expandState ? 1 : 0}};">
  <view class="row">
    <view class="level-name">{{userInfo.cardLevelName}}</view>
    <view class="level-name" wx:if="{{userInfo.nextLevelName}}">{{userInfo.nextLevelName}}</view>
  </view>
  <!-- currentMileage -->
  <view class="level-line">
    <view class="current-level"
      style="--width: {{(userInfo.currentMileage - userInfo.currentLeveLMileage) / (userInfo.nextLeveLMileage - userInfo.currentLeveLMileage) * 650}}rpx" />
  </view>
  <view class="tips-box">
    <view class="txt" catchtap="toLegend"  wx:if="{{userInfo.cardLevel!=4}}">距{{userInfo.nextLevelName}}还需{{userInfo.nextMileage}}里程<image class="tips-arrow" src="{{$cdn}}/right-arrow.png" mode="" /></view>
    <view class="txt" catchtap="toLegend" wx:else>已达到最高等级<image class="tips-arrow" src="{{$cdn}}/right-arrow.png" mode="" /></view>
    <view class="txt">{{userInfo.memberExpireTime}}到期</view>
  </view>
</view>
