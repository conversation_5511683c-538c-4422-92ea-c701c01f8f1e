package com.dz.ms.user.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.UnsupportedJwtException;
import io.jsonwebtoken.security.Keys;
import io.jsonwebtoken.security.SignatureException;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Map;

/**
 * JwtToken 工具类
 */
@Slf4j
public class JwtTokenUtils {

    private static final String KEY = "ByzPymTfpLsSrA8BsvtMiwfNJ5CpT2Flv2FAAjVBxDXMdi6B2B5gi1o3JIE9YdaURk1+MIIBIjMAPDSh0RY5HkiG9wL0ZaU0AGCruYcdd606lhadDhK5HKq4JDI9Nukjv6FcsGyxqc2EwBtyX2rm5OZvJyJ8" +
            "AjZh0gOrarT9a6IDO7ydffdXrPymTfpLsSrA8BsvBPOCNcwDOkQ8AMoKCAQEAGmTVGhwRJnT2Z1ltscRMq6GxzLC1WFevRZnBX10eE0Vcutg+KOMZHCiL9a5ANB6gkqhA+*****************************";

    /**
     * 生成token
     * @param claim
     * @param hours
     * @return
     */
//    public static String generatorToken(Map<String,Object> claim, String secret, int hours){
//        long times = hours*60*60*1000;
//        times += System.currentTimeMillis();
//        String token = Jwts.builder().setClaims(claim)
//                .setExpiration(new Date(times))
//                .signWith(SignatureAlgorithm.HS256,KEY+secret).compact();
//        return token;
//    }
    /**
     * 生成 Token（符合 JJWT 0.12.x 新 API）
     * @param claims  Token载荷（Claims）
     * @param secret  业务密钥（动态部分）
     * @param hours   有效小时数
     * @return JWT 字符串
     */
    public static String generatorToken(Map<String, Object> claims, String secret, int hours) {
        // 1. 合并 KEY 前缀与动态 Secret，生成密钥的字节数组
        byte[] keyBytes = (KEY + secret).getBytes(StandardCharsets.UTF_8);
        // 2. 创建安全密钥（JJWT 会自动检测密钥长度是否符合 HMAC-SHA 要求）
        SecretKey secureKey = Keys.hmacShaKeyFor(keyBytes);

        // 3. 计算过期时间
        long expirationMillis = System.currentTimeMillis() + hours * 60 * 60 * 1000L;

        // 4. 生成 Token
        return Jwts.builder()
                .claims(claims)                // 设置 Claims（新API流畅写法）
                .expiration(new Date(expirationMillis))    // 过期时间
                .signWith(secureKey)            // 签名密钥（不需要显式指定算法，密钥隐含算法类型）
                .compact();
    }

    /**
     * 根据token获取token中的信息
     * @param token
     * @return
     */
//    public static Map<String, Object> getTokenInfo(String token, String secret){
//        Jws<Claims> claimsJws = Jwts.parser().setSigningKey(KEY+secret).parseClaimsJws(token);
//        return claimsJws.getBody();
//    }
    public static Map<String, Object> getTokenInfo(String token, String secret) {
        secret=secret.replace("\"", "");
        // 1. **生成安全密钥**（从字符串转换为 SecretKey）
        byte[] secretBytes = (KEY + secret).getBytes(StandardCharsets.UTF_8);
        SecretKey secureKey = Keys.hmacShaKeyFor(secretBytes); // 👈 确保密钥符合 HMAC-SHA 算法要求
        Jws<Claims> claimsJws= null;
        try {
            // 2. **构建解析器并验证签名**
            claimsJws = Jwts.parser()
                    .verifyWith(secureKey) // 👈 替换废弃的 setSigningKey(key)
                    .build()
                    .parseClaimsJws(token);
            // 使用claims进行后续操作
        } catch (SignatureException ex) {
            // 签名验证失败
            log.info("Invalid signature", ex);
        } catch (MalformedJwtException ex) {
            // Token格式错误
            log.info("Invalid token", ex);
        } catch (ExpiredJwtException ex) {
            // Token已过期
            log.info("Token expired", ex);
        } catch (UnsupportedJwtException ex) {
            // 不支持的Token类型
            log.info("Unsupported token", ex);
        } catch (IllegalArgumentException ex) {
            // Token为空或null
            log.info("Token is either empty or null", ex);
        }

        return claimsJws.getBody();
    }

    public static void main(String[] args) {
        // 1. **生成安全密钥**（从字符串转换为 SecretKey）
        String secret = "19e3fd0b-71b8-455e-b799-9194d1848b23";
        String token="eyJhbGciOiJIUzUxMiJ9.eyJyb2xlIjo0LCJwdCI6MSwiaWQiOjM5LCJ0eXBlIjo1LCJ0aWQiOjEsImV4cCI6MTc0MDAyMTI0Nn0.QkfQofwkbN1lt_u8GwVSyTA_lQ4Vw5e5fZko4u-6oD7hdI3oHDjI5n53AkT0yWTbw3LgpENzyWeQ802-qrEUog";
        byte[] secretBytes = (KEY + secret).getBytes(StandardCharsets.UTF_8);
        SecretKey secureKey = Keys.hmacShaKeyFor(secretBytes); // 👈 确保密钥符合 HMAC-SHA 算法要求

        // 2. **构建解析器并验证签名**
        Jws<Claims> claimsJws = Jwts.parser()
                .verifyWith(secureKey) // 👈 替换废弃的 setSigningKey(key)
                .build()
                .parseClaimsJws(token);
        System.out.println(JSONObject.toJSONString(claimsJws.getBody()));
    }
}