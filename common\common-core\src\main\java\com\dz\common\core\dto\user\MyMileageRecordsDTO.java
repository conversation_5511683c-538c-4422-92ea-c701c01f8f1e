package com.dz.common.core.dto.user;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 我的里程流水DTO
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "我的里程流水")
public class MyMileageRecordsDTO extends BaseDTO {

    @ApiModelProperty(value = "里程名称（消费100.00元）")
    private String mileageName;
    @ApiModelProperty(value = "获取里程时间")
    private String obtainTime;
    @ApiModelProperty(value = "消费渠道")
    private String channel;
    @ApiModelProperty(value = "里程增加数量")
    private String pointsNum;
    @ApiModelProperty(value = "里程流程编号")
    private String mileageSn;
    @ApiModelProperty(value = "操作方式 1增加 2减少")
    private Integer changeType;
}
