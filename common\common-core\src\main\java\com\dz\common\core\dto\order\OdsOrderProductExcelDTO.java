package com.dz.common.core.dto.order;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

/**

 */
@Data
public class OdsOrderProductExcelDTO {
    @ExcelProperty("id")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ExcelProperty("member_code")
    private String memberCode;

    @ExcelProperty("shop_id_str")
    private String shopIdStr;

    @ExcelProperty("shop_name_str")
    private String shopNameStr;

    @ExcelProperty("sale_id_str")
    private String saleIdStr;

    @ExcelProperty("sale_ty_str")
    private String saleTyStr;

    @ExcelProperty("checkout_dt_str")
    private String checkoutDtStr;

    @ExcelProperty("pos_id_str")
    private String posIdStr;

    @ExcelProperty("staff_id_str")
    private String staffIdStr;

    @ExcelProperty("saleid_fr_str")
    private String saleidFrStr;

    @ExcelProperty("total_qty_str")
    private String totalQtyStr;

    @ExcelProperty("total_amt_str")
    private String totalAmtStr;

    @ExcelProperty("item_id_str")
    private String itemIdStr;

    @ExcelProperty("item_name_str")
    private String itemNameStr;

    @ExcelProperty("qty_str")
    private String qtyStr;

    @ExcelProperty("up_serial_no_str")
    private String upSerialNoStr;

    @ExcelProperty("sale_amt_str")
    private String saleAmtStr;

    @ExcelProperty("real_amt_str")
    private String realAmtStr;

    @ExcelProperty("promname1_str")
    private String promname1Str;

    @ExcelProperty("promname2_str")
    private String promname2Str;

    @ExcelProperty("promname5_str")
    private String promname5Str;

    @ExcelProperty("promdisname_str")
    private String promdisnameStr;

    @ExcelProperty("couponname_str")
    private String couponnameStr;

    @ExcelProperty("create_time")
    private Date createTime;

    @ExcelProperty("biz_date")
    private String bizDate;

    @ExcelProperty("tenant_id")
    private Long tenantId = 1L;
}
