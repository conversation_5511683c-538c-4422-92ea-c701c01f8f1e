.popup {
  max-height: 1070rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  // padding: 60rpx 30rpx 60rpx 60rpx;
  overflow: hidden;


  &-title {
    width: 100%;
    box-sizing: border-box;
    text-align: center;
    padding: 60rpx 60rpx 40rpx 60rpx;
    font-family: SourceHanSansCN;
    font-weight: 500;
    font-size: 36rpx;
    color: #231815;
    line-height: 54rpx;
    letter-spacing: 1rpx;
  }

  &-content {
    flex: 1;
    overflow: hidden;
    transition: all .5s;
    width: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;

    .normal-content {
      display: block;
      width: 510rpx;
      font-family: MUJIFont2020, SourceHanSansCN;
      font-weight: 400;
      font-size: 28rpx;
      color: #888888;
      line-height: 42rpx;
      font-style: normal;
      box-sizing: border-box;
    }
  }



  &-scroll {
    height: 100%;
    width: 100%;
    overflow: auto;
    box-sizing: border-box;
    padding-right: 30rpx;
  }



  &-bottom {
    width: 100%;
    padding-left: 60rpx;
    padding-right: 60rpx;
    width: 100%;
    box-sizing: border-box;
  }

  &-other {}

  &-button {
    flex-shrink: 0;
    box-sizing: border-box;
    margin: 0 auto;

    &.confirm {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 510rpx;
      height: 80rpx;
      background: #231815;
      border-radius: 5rpx;
      font-family: SourceHanSansCN, PingFang SC;
      font-weight: 500;
      font-size: 28rpx;
      color: #FFFFFF;
      margin-top: 60rpx;
    }

    &.cancel {
      font-family: SourceHanSansCN, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #231815;
      padding-top: 30rpx;
      line-height: 58rpx;
      text-align: center;
    }
  }
}
