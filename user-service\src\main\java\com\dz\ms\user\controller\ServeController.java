package com.dz.ms.user.controller;

import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.user.ServeDTO;
import com.dz.ms.user.service.ServeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import java.util.List;

@Api(tags="门店服务")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class ServeController  {

    @Resource
    private ServeService serveService;

    /**
     * 分页查询
     * @return result<PageInfo<ServeDTO>>
     */
    @ApiOperation("查询")
	@GetMapping(value = "/crm/serve/list")
    public Result<List<ServeDTO>> getServeList(ServeDTO param) {
        Result<List<ServeDTO>> result = new Result<>();
        List<ServeDTO> list = serveService.getServeList(param);
        result.setData(list);
        return result;
    }

    /**
     * 根据ID查询
     * @param id
     * @return result<ServeDTO>
     */
    @ApiOperation("根据ID查询")
	@GetMapping(value = "/serve/info")
    public Result<ServeDTO> getServeById(@RequestParam("id") Long id) {
        Result<ServeDTO> result = new Result<>();
        ServeDTO serve = serveService.getServeById(id);
        result.setData(serve);
        return result;
    }

    /**
     * 保存
     * @param param
     * @return result<Long>
     */
    @ApiOperation("保存")
	@PostMapping(value = "/crm/serve/save")
    public Result<Long> saveServe(@RequestBody ServeDTO param) {
        Result<Long> result = new Result<>();
        Long id = serveService.saveServe(param);
        result.setData(id);
        return result;
    }

    @ApiOperation("排序")
    @PostMapping(value = "/crm/serve/sort")
    public Result<Boolean> updateSort(@RequestBody List<ServeDTO> param) {
        Result<Boolean> result = new Result<>();
        serveService.updateSort(param);
        result.setData(true);
        return result;
    }
	
	/**
     * 根据ID删除
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除")
	@PostMapping(value = "/crm/serve/delete")
    public Result<Boolean> deleteServeById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        serveService.deleteServeById(param);
        result.setData(true);
        return result;
    }

//------------------------------------------------------app-------------------------------------------------------------

    @ApiOperation("小程序服务抽屉查询")
    @GetMapping(value = "/app/serve/list")
    public Result<List<ServeDTO>> getAppServeList() {
        Result<List<ServeDTO>> result = new Result<>();
        List<ServeDTO> list = serveService.getAppServeList();
        result.setData(list);
        return result;
    }
}
