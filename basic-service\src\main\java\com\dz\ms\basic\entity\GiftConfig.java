package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 核心礼遇配置
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table("核心礼遇配置")
@TableName(value = "t_gift_config")
public class GiftConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    private String name;
    /**
     * 礼遇类型：1新人礼配置，2二回礼配置，3生日礼配置，4升级礼配置，5无以上礼遇可领取且券列表有有效券时，6无礼遇、无可用券时，7无礼遇、有可使用的商品券时
     */
    private Integer giftType;
    /**
     * 卡片样式
     */
    private String headImg;
    /**
     *弹窗配置
     */
    private String ballImg;
    /**
     *弹窗跳转链接
     */
    private String ballJumpUrl;
    /**
     *跳转链接
     */
    private String jumpUrl;
    /**
     *关联优惠券id
     */
    private String couponId;
    /**
     *创建人
     */
    private String createAt;
    /**
     *创建时间
     */
    private Date createTime;
    /**
     *修改人
     */
    private String updateAt;
    /**
     *修改时间
     */
    private Date updateTime;
    /**
     *渠道
     */
    private Long tenantId;

    /**
     * 领取弹窗
     */
    private String reveiveBallImg;
}
