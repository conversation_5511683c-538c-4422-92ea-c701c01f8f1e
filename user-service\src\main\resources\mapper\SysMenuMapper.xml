<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dz.ms.user.mapper.SysMenuMapper">


	<select id="selectMenuTreeAll" resultType="com.dz.ms.user.entity.SysMenu">
		select distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.visible, m.status, ifnull(m.perms,'') as perms, 
						m.is_frame, m.menu_type, m.icon, m.order_num, m.created
		from sys_menu m 
		where m.menu_type in ('M', 'C') 
		and m.status = 0 and m.type = 2 
		and m.tenant_id = #{tenantId}
		order by m.parent_id, m.order_num
	</select>
	
    <select id="selectMenuTreeByUserId" resultType="com.dz.ms.user.entity.SysMenu">
		select distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.visible, m.status, ifnull(m.perms,'') as perms, 
						m.is_frame, m.menu_type, m.icon, m.order_num, m.created
		from sys_menu m
			 left join sys_role_menu rm on m.menu_id = rm.menu_id
			 left join sys_users_role sur on rm.role_id = sur.role_id
			 left join sys_role ro on sur.role_id = ro.id
			 left join sys_user u on sur.uid = u.id
		where u.id = #{userId} 
		and m.menu_type in ('M', 'C') 
		and m.status = 0 
		and m.type = 2
		and m.tenant_id = #{tenantId}
		order by m.parent_id, m.order_num
	</select>
	
    <select id="selectMenuTreeByRoleId" resultType="com.dz.ms.user.entity.SysMenu">
		select distinct m.menu_id, m.parent_id, m.menu_name, m.path, m.component, m.visible, m.status, ifnull(m.perms,'') as perms, 
						m.is_frame, m.menu_type, m.icon, m.order_num, m.created
		from sys_menu m
			 left join sys_role_menu rm on m.menu_id = rm.menu_id
		where rm.role_id = #{roleId} 
		and m.menu_type in ('M', 'C') 
		and m.status = 0 
		and m.type = 2
		and m.tenant_id = #{tenantId}
		order by m.parent_id, m.order_num
	</select>

	<select id="getInfoByMenuName" resultType="com.dz.ms.user.vo.MenuInfoVo">
		SELECT
			menu_id,
			menu_name,
			parent_id,
			order_num,
			path
		FROM
			sys_menu
		WHERE status = 0 AND
		      menu_type = #{menuType} AND
		      menu_name like concat('%', #{menuName}, '%')
	</select>

	<select id="selectByParentId" resultType="com.dz.ms.user.entity.SysMenu">
		select menu_id, menu_name, parent_id, order_num, path, component, is_frame, menu_type, visible, status, ifnull(perms,'') as perms, icon, created 
		from sys_menu
		where menu_id = #{menuId} and status = 0
	</select>

</mapper> 
