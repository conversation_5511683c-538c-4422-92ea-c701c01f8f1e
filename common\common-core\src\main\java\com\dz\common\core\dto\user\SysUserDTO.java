package com.dz.common.core.dto.user;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 系统用户信息DTO
 * @author: Handy
 * @date:   2022/2/4 23:04
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "系统用户信息")
public class SysUserDTO extends BaseDTO {

    @ApiModelProperty(value = "系统用户ID")
    private Long id;
    @ApiModelProperty(value = "账号")
    private String username;
    @ApiModelProperty(value = "真实姓名")
    private String realname;
    @ApiModelProperty(value = "手机号码")
    private String mobile;
    @ApiModelProperty(value = "用户状态 0离职 1正常")
    private Integer state;
    @ApiModelProperty(value = "是否超管 1是 0否")
    private Integer isAdmin;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "创建人姓名")
    private String creatorName;
    //@ApiModelProperty(value = "权限码")
    //private List<String> permits;
    @ApiModelProperty(value = "功能权限码")
    private List<String> functionCodes;
    @ApiModelProperty(value = "功能权限码")
    List<SysPermissionDTO> menuList;
    @ApiModelProperty(value = "角色ID")
    private Long roleId;
    @ApiModelProperty(value = "角色名")
    private String roleName;
    @ApiModelProperty(value = "token")
    private String token;

}
