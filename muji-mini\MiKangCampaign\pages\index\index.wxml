<!--MiKangCampaign/pages/index/index.wxml-->
<my-page overallModal="{{overallModal}}" loading="{{loading}}">
  <view wx:if="{{!loading}}" class="page-container">
    <!-- black -->
    <custom-header background="transparent" type="{{1}}" color="{{HomepageStatus==1?'white':'black'}}" />
    <scroll-view enhanced="{{ true }}" bounces="{{ false }}" show-scrollbar="{{ false }}" scroll-y class="registrationSuccessful">
      <view bind:tap="onTapRule" class="page-rule">活动规则</view>
      <!-- <view wx:if="{{isShowTime}}" bind:tap="onTapRule1" class="page-zhongjiang">招募结果</view> -->
      <!-- <block wx:if="{{HomepageStatus==1}}">
        <view class="page-content">
          <image wx:if="{{HomepageStatus==1}}" class="page-content-img" mode="widthFix" style="width: 750rpx;flex-shrink:0;height: auto;" src="{{$cdn}}/MiKangCampaign/mk-signup-content1.png" />
        </view>
      </block> -->
      <!-- <block wx:elif="{{HomepageStatus==2}}">
        <image class="page-all" mode="aspectFit" style="width: 750rpx;flex-shrink:0;height: auto;" src="{{$cdn}}/signUp/signup-content2-2.jpg" />
        <image class="page-header" src="" />
        <view class="page-content">
          <image class="page-content-img" src="" />
        </view>
      </block> -->
      <view wx:if="{{HomepageStatus}}" class="page-content">
        <image class="bg-image" mode="widthFix" src="{{$cdn}}/MiKangCampaign/mk-signup-content1.png"></image>
        <view class="card-box">
          <!-- 状态1：未报名  -->
          <image wx:if="{{HomepageStatus < 8}}" class="page-content-img" mode="widthFix" src="{{$cdn}}/MiKangCampaign/mk-index-card-{{HomepageStatus}}.png" />
          <image wx:elif="{{HomepageStatus==8}}" class="page-content-img" src="{{$cdn}}/MiKangCampaign/mk-index-card-6.png" />

          <view class="bottom-box">
            <basic-button width="{{670}}" disabled="{{disabled}}" loading="{{loading}}" size="large" bind:click="submit">
              {{btnTitle}}
            </basic-button>
          </view>
        </view>
      </view>
    </scroll-view>
    <result isShow="{{showAnnouncement}}" bindclose="close" />
  </view>
</my-page>