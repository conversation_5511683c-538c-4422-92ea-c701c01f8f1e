package com.dz.ms.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.product.dto.ShelfProductSuperscriptDTO;
import com.dz.ms.product.entity.ShelfProductSuperscript;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 货架商品角标Mapper
 *
 * @author: fei
 * @date: 2024/12/09 11:32
 */
@Repository
public interface ShelfProductSuperscriptMapper extends BaseMapper<ShelfProductSuperscript> {

    List<ShelfProductSuperscriptDTO> selectNoPageList(@Param("shelfProductIdList") List<Long> shelfProductIdList, @Param("shelfId") Long shelfId);
    
}
