package com.dz.ms.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.ms.basic.dto.MpMsgConfigDTO;
import com.dz.ms.basic.dto.StyleConfigDTO;
import com.dz.ms.basic.entity.MpMsgConfig;
import com.dz.ms.basic.entity.StyleConfig;


public interface MpMsgConfigService extends IService<MpMsgConfig> {


    MpMsgConfigDTO getMpMsgConfigList(Long tenantId);

    Long saveMpMsgConfig(MpMsgConfigDTO param);

    MpMsgConfigDTO getMpMsgConfigById(Long id);
}
