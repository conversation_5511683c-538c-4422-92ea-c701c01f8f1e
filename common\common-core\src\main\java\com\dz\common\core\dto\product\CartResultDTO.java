package com.dz.common.core.dto.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 购物车返回结果DTO
 */
@Setter
@Getter
@ToString
public class CartResultDTO implements Serializable {

    private static final long serialVersionUID = -560313253497221405L;
    @ApiModelProperty(value = "应付总积分")
    private Integer totalPoint = 0;
    @ApiModelProperty(value = "应付总金额 某些商品设置了积分+金额兑换")
    private BigDecimal totalPrice = new BigDecimal(0);
    @ApiModelProperty(value = "购物车商品条数")
    private Integer totalNum = 0;
    @ApiModelProperty(value = "已选择商品件数")
    private Integer checkNum = 0;
    @ApiModelProperty(value = "是否全选")
    private Integer checkAll;
    @ApiModelProperty(value = "购物车商品列表")
    private List<CartProductDTO> carts;
    @ApiModelProperty(value = "可用商品列表")
    private List<CartProductDTO> validCarts = new ArrayList<>();
    @ApiModelProperty(value = "失效商品列表")
    private List<CartProductDTO> invalidCarts = new ArrayList<>();

}