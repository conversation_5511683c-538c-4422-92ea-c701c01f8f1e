package com.dz.common.core.enums;

import java.util.Objects;

/**
 * Describe：删除类型枚举
 * Created by 吴蜀黍 on 2023/8/20 8:36
 **/
public enum DelEnum {

    NOT_DELETE(0, "未删除"),
    DELETE(1, "已删除"),
    ;

    private final Integer code;
    private final String value;

    DelEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DelEnum resultEnum : DelEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum.value;
            }
        }
        return null;
    }
}
