package com.dz.common.core.exception;


import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 全局异常处理
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

	/**
	 * 业务异常
	 */
	@ExceptionHandler(BusinessException.class)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Result businessException(BusinessException e) {
		log.error("业务异常:{}", e.getMessage(),e);
		return new Result().errorResult(e.getCode(),e.getMessage());
	}


	/**
	 * 其他系统异常
	 */
	@ExceptionHandler(Exception.class)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Result exception(Exception e) {
		log.error("系统异常:",e);
		StringWriter sw = new StringWriter();
		PrintWriter pw = new PrintWriter(sw);
		try {
			e.printStackTrace(pw);
			sw.close();
			pw.close();
		} catch (IOException ioException) {
			ioException.printStackTrace();
		}
		log.error("系统异常ExceptionTrim:"+sw.toString().replaceAll("[\\t\\n\\r]", " "));
		return new Result().errorResult(ErrorCode.INTERNAL_ERROR);
	}

	/**
	 * 参数异常
	 * */
	@ExceptionHandler(MethodArgumentNotValidException.class)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Result methodArgumentNotValidException(MethodArgumentNotValidException e) {
		List<ObjectError> allErrors = e.getBindingResult().getAllErrors();
		String message = allErrors.stream().map(s -> s.getDefaultMessage()).collect(Collectors.joining(";"));
		log.error("参数异常:{}",message);
		return new Result().errorResult(ErrorCode.BAD_REQUEST,message);
	}
}
