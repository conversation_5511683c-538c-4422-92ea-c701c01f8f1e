<template>
  <div class="bg">
    <!-- 背景色 -->
    <div class="bg-item bg-color" :style="{background:bgSetting.bgColor}" v-if="bgSetting.bgType==1&&bgSetting.bgColor"></div>
    <!-- 图片 -->
    <img :src="bgSetting.bgImg" class="bg-item bg-img" v-else-if="bgSetting.bgType==2&&bgSetting.bgImg" />
    <!-- 视频 -->
    <video :src="bgSetting.bgVideo" class="bg-item bg-video" v-else-if="bgSetting.bgType==3&&bgSetting.bgVideo"></video>
  </div>
</template>
<script setup>
const emit = defineEmits(['ok', 'cancel', 'changeActive'])
import { cloneDeep } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { computed } from 'vue';
const props = defineProps({
  bgSetting: {
    type: Object,
    default() {
      return {}
    }
  },
})



</script>

<style scoped lang="scss">
.bg {
  height: 100%;
  width: 100%;

  &-item {
    width: 100%;
    height: 100%;
    display: block;
  }
  &-video {
    object-fit: cover;
  }
}
</style>
