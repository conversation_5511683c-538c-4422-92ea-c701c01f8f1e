package com.dz.common.core.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 单ID/CODE POST请求通用DTO
 * @author: Handy
 * @date:   2022/2/3 16:33
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
@ApiModel(value = "ID CODE 通用DTO")
public class IdCodeDTO {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "CODE")
    private String code;

    public IdCodeDTO(Long id, String code) {
        this.id = id;
        this.code = code;
    }

}
