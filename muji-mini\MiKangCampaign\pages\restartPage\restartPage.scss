/* MiKangCampaign/pages/restartPage/restartPage.wxss */
.page-container {
    // background-color: #a0a2a3;
    background-size: 100% auto;
    background-repeat: no-repeat;

    .overPage-wrap {
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        z-index: 1;
        height: 100vh;
        overflow-y: auto;
        overflow-x: hidden;

        .overPage-top {
            position: relative;
            // border-bottom: 1rpx solid #D8D8D8;
            box-sizing: border-box;

            .enpageBack {
                position: relative;
                top: 0;
                left: 0;
                z-index: 1;
                width: 100%;
                display: block;
                vertical-align: top;
            }

            .page-content {
                width: 100%;
                height: 100%;
                box-sizing: border-box;
                position: absolute;
                top: 0;
                left: 0;
                z-index: 2;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
            }

            .image,
            .image1 {
                display: flex;
                flex-direction: column;
                width: 100%;
                padding-bottom: 15rpx;
                box-sizing: border-box;

                .text {
                    margin-top: 24rpx;

                    .text1 {
                        // height: 54rpx;
                        display: inline-block;
                        font-family: MUJIFont2020;
                        font-weight: 700;
                        font-size: 32rpx;
                        color: #ffffff;
                        line-height: 48rpx;
                        letter-spacing: 1rpx;
                        text-align: left;
                        margin-left: 49rpx;
                    }
                }

                .h1 {
                    font-family: MUJIFont2020;
                    font-weight: 900;
                    font-size: 64rpx;
                    color: #ffffff;
                    line-height: 72rpx;
                    text-align: left;
                    margin-left: 48rpx;
                    margin-top: 48rpx;

                    .h1-text {
                        margin-top: 18rpx;
                    }
                }

                .bubble {
                    position: relative;
                    margin-left: 49rpx;
                    font-weight: 500;
                    line-height: 40rpx;
                    font-size: 24rpx;
                    color: #756453;

                    margin-top: 24rpx;

                    .bubble-img {
                        position: absolute;
                        left: 48rpx;
                        top: -14rpx;
                        width: 24rpx;
                        height: 15rpx;
                    }

                    text {
                        padding: 8rpx 20rpx;
                        background-color: #ffffff;
                        border-radius: 8rpx;
                    }
                }

                .p1 {
                    font-family: MUJIFont2020;
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #ffffff;
                    line-height: 32rpx;
                    letter-spacing: 2rpx;
                    text-align: left;
                    margin-left: 42rpx;
                    margin-top: 4rpx;
                    // display: flex;
                    // align-items: center;
                }

                .p2 {
                    margin-left: 42rpx;
                    display: flex;
                }

                .subscribe-text {
                    font-weight: 900;
                    font-size: 22rpx;
                    color: #ffffff;
                    line-height: 32rpx;
                    letter-spacing: 2rpx;
                    text-decoration: underline;
                    padding: 20rpx 5rpx 20rpx 0;
                }

                .subscribe {
                    display: inline-block;
                    width: 60rpx;
                    height: 60rpx;
                    background-size: 100% 100%;
                    margin-top: 31rpx;
                }
            }

            .overPage {
                margin-top: 242rpx;

                // overflow-y: auto;
                // overflow-x: hidden;
                .signUpOver-box {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-left: 79rpx;

                    .signUpOver_title {
                        font-family: MUJIFont2020;
                        font-weight: 900;
                        font-size: 39rpx;
                        color: #3c3c43;
                        line-height: 59rpx;
                        letter-spacing: 2rpx;
                        text-align: left;
                    }

                    .signUpOver_title1 {
                        font-weight: 900;
                        font-size: 39rpx;
                        color: #3c3c43;
                        line-height: 45rpx;
                        letter-spacing: 2rpx;
                        text-align: left;
                    }

                    .pic2 {
                        width: 170rpx;
                        height: 240rpx;
                        margin-right: 105rpx;
                    }

                    .pic3 {
                        width: 189rpx;
                        height: 110rpx;
                        margin-right: 85rpx;
                    }
                }

                .signUpOver-box1 {
                    margin-top: 60rpx;
                }

                .signUpOver-bottom-box {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-top: 243rpx;
                }
            }

            .otherType {
                display: flex;
                align-items: flex-start;
                justify-content: center;
                margin-top: 157rpx;

                .other-box {
                    // padding: 0 50rpx;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: start;

                    .Other-img {
                        width: 257rpx;
                        height: 257rpx;
                        margin-left: 50rpx;
                        margin-right: 50rpx;
                    }

                    .other-title {
                        font-family: MUJIFont2020;
                        font-weight: 900;
                        font-size: 34rpx;
                        color: #3c3c43;
                        line-height: 51rpx;
                        letter-spacing: 1px;
                        text-align: center;
                        margin-top: 70rpx;
                    }

                    .othet-text {
                        font-family: MUJIFont2020;
                        font-weight: 400;
                        font-size: 24rpx;
                        color: #979797;
                        line-height: 42rpx;
                        letter-spacing: 1px;
                        text-align: center;
                        width: 316rpx;
                        margin-top: 17rpx;
                    }

                    .othet-text1 {
                        font-family: MUJIFont2020;
                        font-weight: 400;
                        font-size: 16rpx;
                        color: #979797;
                        line-height: 28rpx;
                        letter-spacing: 1rpx;
                        text-align: center;
                    }
                }

                .other-box2 {
                    position: relative;

                    .line {
                        position: absolute;
                        left: 0rpx;
                        top: 18rpx;
                        height: 221rpx;
                        width: 0rpx;
                        border-left: 1rpx solid rgba(151, 151, 151, 0.5);
                    }
                }
            }

            .otherType-bottom-box {
                display: flex;
                align-items: center;
                justify-content: center;
                // margin-top: 211rpx;
            }

            .pullDownView {
                margin-top: 54rpx;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                margin-bottom: 28rpx;
                font-size: 24rpx;
                line-height: 36rpx;

                .viewIconfont {
                    transform: scaleY(-1);
                    line-height: 48rpx;
                    // margin-bottom: 18rpx;
                }
                .viewText {
                    line-height: 68rpx;
                }
            }
        }

        .overPage-bottom {
            .bottom-title {
                margin-left: 45rpx;
                margin-top: 40rpx;

                .bottom-title1 {
                    font-family: MUJIFont2020;
                    font-weight: 700;
                    font-size: 50rpx;
                    color: #2e2e2e;
                    line-height: 57rpx;
                    letter-spacing: 6rpx;
                    text-align: left;
                }

                .bottom-title2 {
                    width: 169rpx;
                    height: 54rpx;
                    margin-top: 30rpx;
                    // box-sizing: border-box;
                    padding-bottom: 3rpx;
                    font-family: MUJIFont2020;
                    font-weight: 500;
                    font-size: 29rpx;
                    line-height: 54rpx;
                    letter-spacing: 1rpx;
                    text-align: center;
                    color: #ffffff;
                    background: #3c3c43;
                    border-radius: 27rpx 27rpx 27rpx 27rpx;
                }
            }
        }

        .picture {
            width: 100%;
            height: auto;

            .img {
                width: 100%;
                height: 100%;
                display: block;
                vertical-align: top;
            }
        }
    }

    .page-rule {
        //position: absolute;
        z-index: 10;
        position: fixed;
        right: 0rpx;
        top: 216rpx;
        width: 58rpx;
        height: 160rpx;
        background: #ab8e6166;
        font-family: MUJIFont2020;
        font-weight: 700;
        font-size: 28rpx;
        color: #ffffff;
        line-height: 24rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        writing-mode: vertical-rl;
        letter-spacing: 4rpx;
        border-radius: 16rpx 0rpx 0rpx 16rpx;
        margin: 10rpx 0rpx 10rpx 20rpx;
    }

    .page-zhongjiang {
        z-index: 10;
        position: fixed;
        right: 0rpx;
        top: 400rpx;
        width: 58rpx;
        height: 160rpx;
        background: #ab8e6166;
        font-family: MUJIFont2020;
        font-weight: 700;
        font-size: 28rpx;
        color: #ffffff;
        line-height: 24rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        writing-mode: vertical-rl;
        letter-spacing: 4rpx;
        border-radius: 16rpx 0rpx 0rpx 16rpx;
        margin: 10rpx 0rpx 10rpx 20rpx;
    }
}
