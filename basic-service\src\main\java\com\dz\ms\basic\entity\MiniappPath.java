package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@Table("小程序页面模板")
@TableName(value = "miniapp_path")
public class MiniappPath implements Serializable {
    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.INT,length = 0,isNull = true,comment = "页面类型")
    private Integer pageType;
    @Columns(type = ColumnType.VARCHAR,length = 32,isNull = true,comment = "页面类型")
    private String name;
    @Columns(type = ColumnType.TEXT,length = 0,isNull = true,comment = "页面路径")
    private String path;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
}
