package com.dz.ms.basic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "更新小程序模版路径")
public class MiniappTemplateUpdatePathDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "模板类型 1常规页面 2系统页面 3定制页面 4弹窗页面 5开屏页面")
    private Integer templateType;
    @ApiModelProperty(value = "页面类型 0默认 1首页 2会员中心 3会员码 4生活圈 5更多 6活动开屏 7首页开屏 ")
    private Integer pageType;
    @ApiModelProperty(value = "是否发布 0否1是")
    private Integer publish;
}
