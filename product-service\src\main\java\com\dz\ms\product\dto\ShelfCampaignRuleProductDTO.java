package com.dz.ms.product.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 营销活动规则关联的货架商品DTO
 *
 * @author: LiinNs
 * @date: 2024/11/20 11:39
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "营销活动规则关联的货架商品")
public class ShelfCampaignRuleProductDTO extends BaseDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "营销活动ID")
    private Long campaignId;
    @ApiModelProperty(value = "营销活动规则ID")
    private Long ruleId;
    @ApiModelProperty(value = "营销活动规则名称(规则角标)")
    private String ruleName;
    @ApiModelProperty(value = "人群包ID")
    private Long groupId;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "货架商品ID")
    private Long shelfProductId;
    @ApiModelProperty(value = "商品ID")
    private Long productId;
    @ApiModelProperty(value = "是否展示 0不展示 1展示")
    private Integer beShow;
    @ApiModelProperty(value = "上架库存")
    private Integer onInventory;
    @ApiModelProperty(value = "目前库存")
    private Integer currentInventory;
    @ApiModelProperty(value = "1同当前库存 2活动规则库存")
    private Integer inventoryType;
    @ApiModelProperty(value = "活动规则库存")
    private Integer ruleInventory;
    @ApiModelProperty(value = "兑换积分")
    private Integer costPoint;
    @ApiModelProperty(value = "积分划线价")
    private Integer prePoint;
    @ApiModelProperty(value = "1积分+金额时 仍需支付的金额")
    private BigDecimal costPrice;
    @ApiModelProperty(value = "每人限购数")
    private Integer everyoneLimit;
    @ApiModelProperty(value = "规则开始时间")
    private Date ruleCreated;
    @ApiModelProperty(value = "规则组件配置")
    private String content;
    
    
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "创建时间")
    private Date created;

    @ApiModelProperty(value = "活动开始时间")
    private Date campaignOnStartTime;
    @ApiModelProperty(value = "活动结束时间")
    private Date campaignOnEndTime;

}
