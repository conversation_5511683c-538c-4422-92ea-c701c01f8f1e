.floatStyle {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 100;
  pointer-events: none;
}

.customPage {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;

  &.first {
    height: calc(100vh - 80px - env(safe-area-inset-bottom));
  }

  &-box {
    position: relative;
    flex: 1;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  &-content {
    position: relative;
    flex: 1;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }


  &-scroll {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }
}

.openGo {
  position: fixed;
  // right: 50rpx;
  // top: 250rpx;
  right: 50rpx;
  bottom: 78rpx;
}

.tab-bar {
  height: 80px;
  padding-bottom: constant(safe-area-inset-bottom) !important;
  padding-bottom: env(safe-area-inset-bottom) !important;
}

.bgStyle {
  height: 100%;
  width: 100%;
}
