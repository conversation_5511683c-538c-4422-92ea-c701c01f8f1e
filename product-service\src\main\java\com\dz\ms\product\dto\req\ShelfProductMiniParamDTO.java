package com.dz.ms.product.dto.req;

import com.dz.common.base.dto.BaseDTO;
import com.dz.common.core.dto.ScoreRangeDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 货架商品小程序查询入参
 *
 * @author: fei
 * @date: 2024/12/06 16:25
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@ApiModel(value = "货架商品小程序查询入参")
public class ShelfProductMiniParamDTO extends BaseDTO {


    @ApiModelProperty(value = "货架ID 前端传参为空")
    private Long shelfId;
    @ApiModelProperty(value = "一级标签ID 查询二级标签时值为空")
    private Long oneTagId;
    @ApiModelProperty(value = "二级标签ID列表 查询一级标签时值为空")
    private List<Long> twoTagIdList;
    @ApiModelProperty(value = "标签ID列表 前端传参为空")
    private List<Long> tagIdList;
    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "排序方式 1按积分由低到高 2按积分由高到低 默认排序不用传")
    private Integer sortType;
    @ApiModelProperty(value = "兑换方式筛选 1按纯积分 2按积分加价购")
    private Integer exchangeType;
    @ApiModelProperty(value = "是否勾选只看我能换的 1勾选")
    private Integer isMyExchange;
    @ApiModelProperty(value = "我的积分")
    private Integer myPoint;
    @ApiModelProperty(value = "积分区间列表")
    private List<ScoreRangeDTO> scoreRangeList;
    @ApiModelProperty(value = "登录用户适用人群包id列表")
    private List<Long> groupIdList;
    

}
