package com.dz.ms.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.ms.order.dto.BookingRecordDTO;
import com.dz.ms.order.entity.BookingRecord;

/**
 * 预约记录接口
 * @author: Handy
 * @date:   2024/06/06 14:47
 */
public interface BookingRecordService extends IService<BookingRecord> {

	/**
     * 分页查询预约记录
     * @param param
     * @return PageInfo<BookingRecordDTO>
     */
    public PageInfo<BookingRecordDTO> getBookingRecordList(BookingRecordDTO param);

    /**
     * 根据ID查询预约记录
     * @param id
     * @return BookingRecordDTO
     */
    public BookingRecordDTO getBookingRecordById(Long id);

    /**
     * 保存预约记录
     * @param param
     * @return Long
     */
    public Long saveBookingRecord(BookingRecordDTO param);

    /**
     * 根据ID删除预约记录
     * @param param
     */
    public void deleteBookingRecordById(IdCodeDTO param);

}
