<van-dialog use-slot width="630rpx" height="{{height}}rpx" show="{{isShow}}" closeOnClickOverlay="true" show-confirm-button="{{ false }}" custom-style="--overlay-background-color:rgba(0, 0, 0, .7); border-radius: 0rpx; background-color: transparent">
  <view class=" basic-dialog" style="padding-bottom: 80rpx;position: relative;">
    <view class="dialog-box">
      <block wx:if="{{type==='primary'}}">
        <!-- 弹窗标题 -->
        <view class="dialog-title">
          {{title}}
        </view>
        <!-- 副标题 -->
        <view class="dialog-sub-title">
          {{subTitle}}
        </view>
      </block>
      <!-- 报错弹窗 -->
      <block wx:elif="{{type==='errorTips'}}">
        <view>
          errorMsg
        </view>
      </block>
      <view class="content-box">
        <slot name="content"></slot>
      </view>
      <view class="btn-box">
        <block wx:if="{{showConfirm}}">
          <basic-button bindtap="confirm" width="{{510}}" btnState="primary" size="large">
            {{confirmTxt}}
          </basic-button>
        </block>
        <block wx:if="{{showCancel}}">
          <view bindtap="cancel" class="cancel-btn">{{cancelTxt}}</view>
        </block>
      </view>
    </view>
    <image bindtap="close" class="close-popup" src="{{$cdn}}/close.png" mode="widthFix" />
  </view>
</van-dialog>