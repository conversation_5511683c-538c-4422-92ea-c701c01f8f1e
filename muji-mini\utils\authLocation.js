// 统一处理获取用户位置信息
import {
  offLineStoreTask
} from '../api/index';

const authLocation = () => {
  const app = getApp();
  return new Promise((resolve, reject) => {
    wx.getSetting({
      success: (res) => {
        // 判断用户是否授权了位置信息
        const isAuthorized = res.authSetting['scope.userLocation'];
        if (isAuthorized === undefined) {
          // 用户从未开启过授权
          resolve({
            success: false,
            status: 'never_requested', // 用户从未请求过授权
          });
        } else if (isAuthorized === false) {
          // 一天只弹一次弹窗
          if (!app.getTodaySub('authLocation')) {
            app.setTodaySub('authLocation', 1)
            wx.$mp.getCurrentPage().showOveralModal('store')
          }
          // 用户曾经开启过但拒绝了授权
          resolve({
            success: false,
            status: 'denied', // 用户拒绝授权
          });
        } else if (isAuthorized === true) {
          wx.getLocation({
            type: 'gcj02', // 返回经纬度（gcj02坐标系）
            success: (v) => {
              // 全局保存最新的用户所在经纬度
              app.globalData.userLatitude = v.latitude
              app.globalData.userLongitude = v.longitude
              // 打卡线下门店任务
              // offLineStoreTask({
              //   latitude: v.latitude,
              //   longitude: v.longitude,
              // })
              resolve({
                data: {
                  latitude: v.latitude,
                  longitude: v.longitude,
                },
                success: true,
                status: 'authorized', // 用户已授权
              });
            },
            fail: (error) => {
              console.error('获取位置失败:', error); // 打印错误信息
              reject({
                success: false,
                error: error, // 返回错误信息
              });
            }
          });
        }
      },
      fail: (error) => {
        reject({
          success: false,
          error: error, // 返回错误信息
        });
      }
    });
  });
};




export default wx.$authLocation = authLocation;
