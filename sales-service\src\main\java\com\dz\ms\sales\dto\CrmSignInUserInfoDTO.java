package com.dz.ms.sales.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/22
 */
@Data
public class CrmSignInUserInfoDTO {

    @ApiModelProperty(value = "报名体验官人数")
    private Integer enrollNum = 0;

    @ApiModelProperty(value = "招募人数")
    private Integer recruitNum = 0;

    @ApiModelProperty(value = "购买人数")
    private Integer purchaseNum = 0;

    @ApiModelProperty(value = "导入人数")
    private Integer importNum = 0;


    @ApiModelProperty(value = "参与打卡人数")
    private Integer signInNum = 0;

    @ApiModelProperty(value = "完成7次打卡人数")
    private Integer signIn7DaysNum = 0;

    @ApiModelProperty(value = "完成1次打卡人数")
    private Integer signIn1DaysNum = 0;

    @ApiModelProperty(value = "完成2次打卡人数")
    private Integer signIn2DaysNum = 0;

    @ApiModelProperty(value = "完成3次打卡人数")
    private Integer signIn3DaysNum = 0;

    @ApiModelProperty(value = "完成4次打卡人数")
    private Integer signIn4DaysNum = 0;

    @ApiModelProperty(value = "完成5次打卡人数")
    private Integer signIn5DaysNum = 0;

    @ApiModelProperty(value = "完成6次打卡人数")
    private Integer signIn6DaysNum = 0;

}
