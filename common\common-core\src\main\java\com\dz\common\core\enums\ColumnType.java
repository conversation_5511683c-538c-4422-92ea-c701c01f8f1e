package com.dz.common.core.enums;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据库字段类型枚举
 * @author: Handy
 * @date:   2022/07/07 16:36
 */
public enum ColumnType {

	VARCHAR,
	CHAR,
	INT,
	TINYINT,
	BIGINT,
	DATE,
	TIME,
	DATETIME,
	DECIMAL,
	FLOAT,
	DOUBLE,
	TEXT,
	TINYTEXT,
	MEDIUMTEXT,
	LONGTEXT,
	BLOB;

	public static List<String> noLengthTypes = getNoLengthTypes();
	private static List<String> getNoLengthTypes() {
		if(CollectionUtils.isEmpty(noLengthTypes)) {
			noLengthTypes = new ArrayList<>();
			noLengthTypes.add(DATE.name());
			noLengthTypes.add(TIME.name());
			noLengthTypes.add(DATETIME.name());
			noLengthTypes.add(TEXT.name());
			noLengthTypes.add(TINYTEXT.name());
			noLengthTypes.add(MEDIUMTEXT.name());
			noLengthTypes.add(LONGTEXT.name());
			noLengthTypes.add(BLOB.name());
		}
		return noLengthTypes;
	}

	public static List<String> decimalTypes = getDecimalTypes();
	private static List<String> getDecimalTypes() {
		if(CollectionUtils.isEmpty(decimalTypes)) {
			decimalTypes = new ArrayList<>();
			decimalTypes.add(DECIMAL.name());
			decimalTypes.add(FLOAT.name());
			decimalTypes.add(DOUBLE.name());
		}
		return decimalTypes;
	}
}
