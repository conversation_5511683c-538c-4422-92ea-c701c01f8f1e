// components/cell-list/cell-list.js
Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true,
  },

  /**
   * 组件的属性列表
   */
  properties: {
    list: {
      type: Array,
      value: [],
      observer(val) {
        this.handleData(val);
      }
    },
    labelKey: {
      type: String,
      value: 'label',
    },
    contentKey: {
      type: String,
      value: 'content',
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    listData: [],
  },
  lifetimes: {},
  /**
   * 组件的方法列表
   */
  methods: {
    unfold(e) {
      const index = e.currentTarget.dataset.index;
      const list = this.data.listData;
      const item = list[index];


      // 判断内容是否为图片链接
      // if (item[this.data.contentKey]) {
      //   const content = item[this.data.contentKey];
      //   item.isImage = content.startsWith('http') &&
      //     (content.endsWith('.png') || content.endsWith('.jpg') || content.endsWith('.jpeg'));
      // }

      item.isUnfold = !item.isUnfold;

      this.setData({
        listData: list
      })
    },
    handleData(v) {
      const data = v.filter(item => !!item.content);
      // const value = data.map(item => ({
      //   ...item,
      //   isUnfold: item.label === '兑换须知' ? true : false,
      // }))
      this.setData({
        listData: data,
      })
    }
  }
})
