package com.dz.common.core.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 日期工具
 *
 * @Author: Handy
 * @Date: 2022/2/4 19:14
 */
public class DateUtils {

    public static final DateTimeFormatter Y_MD_HMS_DTF1 = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    public static final DateTimeFormatter Y_MD_HMS_DTF2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter Y_MD_HMS_DTF3 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
    public static final DateTimeFormatter Y_MD_HMS_DTF4 = DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm");
    public static final DateTimeFormatter Y_MD_HMS_DTF5 = DateTimeFormatter.ofPattern("yyyy.MM.dd HH:mm:ss");
    public static final DateTimeFormatter Y_MD_HMS_DTF6 = DateTimeFormatter.ofPattern("yyyy.MM.dd HH:mm");
    public static final DateTimeFormatter Y_MD_HM_DTF1 = DateTimeFormatter.ofPattern("yyyyMMddHHmm");
    public static final DateTimeFormatter Y_MD_DTF1 = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final DateTimeFormatter Y_MD_DTF2 = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
    public static final DateTimeFormatter Y_MD_DTF3 = DateTimeFormatter.ofPattern("yyyy/MM/dd");
    public static final DateTimeFormatter Y_MD_DTF4 = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final DateTimeFormatter Y_MD_DTF5 = DateTimeFormatter.ofPattern("yyyy/dd/MM");
    public static final DateTimeFormatter Y_M_DTF1 = DateTimeFormatter.ofPattern("yyyyMM");

    /**
     * Date转cron表达式
     *
     * @param date
     * @return
     */
    public static String dateToCron(Date date) {
        if (null == date) {
            return null;
        }
        String dateFormat = "ss mm HH dd MM ? yyyy";
        SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
        return sdf.format(date);
    }

    /**
     * 获取当月1号零晨时间
     *
     * @return
     */
    public static Date getMonthZero() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取指定年 月 最后一天
     *
     * @param year
     * @param month
     * @return
     */
    public static Date getLastDayOfMonth(int year, int month) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, year);
        //设置月份
        cal.set(Calendar.MONTH, month - 1);
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        //格式化日期
        return cal.getTime();
    }

    /**
     * 获取n秒前的时间
     *
     * @return
     */
    public static Date getBeforeSecondDate(int second) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.SECOND, -second);
        return calendar.getTime();
    }

    /**
     * 加n天
     *
     * @param plus 正数加指定天、负数减指定天
     */
    public static Date getPlusDays(Date startDate, Integer plus) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        calendar.add(Calendar.DAY_OF_YEAR, plus);
        return calendar.getTime();
    }


    /**
     * 加n天
     *
     * @param plus 加指定单位的时间
     */
    public static Date add(Date date, int type, int plus) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(type, plus);
        return calendar.getTime();
    }

    /**
     * 加n天后的23:59:59
     */
    public static Instant getPlusDaysEndTimeInstant(Date startDate, Integer plus) {
        LocalDate localDate = LocalDateTime.ofInstant(startDate.toInstant(), ZoneId.systemDefault()).plusDays(plus).toLocalDate();
        Instant instant = localDate.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant();
        return instant;
    }

    /**
     * 获取两个日期直接的相差天数
     *
     * @param startDate 开始时间(更早)
     * @param endDate   结束时间(更晚)
     * @return endDate - startDate 相差天数，eg：2023-08-22 - 2023-08-21 = 1
     */
    public static Long getBetween(Date startDate, Date endDate) {
        LocalDate start = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate end = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return ChronoUnit.DAYS.between(start, end);
    }

    /**
     * 转换成 yyyy-MM-dd 格式的日期
     *
     * @param date 日期
     * @return yyyy-MM-dd 格式的字符串
     */
    public static String toDayString(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return simpleDateFormat.format(date);
    }

    /**
     * 转换成 yyyy-MM-dd 格式的日期
     *
     * @param date 日期
     * @return yyyy-MM-dd 格式的字符串
     */
    public static String toDayString(Date date, String pattern) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        return simpleDateFormat.format(date);
    }

    /**
     * 转换成 yyyy-MM-dd 格式的日期
     *
     * @param date    日期
     * @param pattern 格式字符串
     */
    public static String toDayString(LocalDate date, String pattern) {
        return date.format(DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 转换成 yyyy-MM-dd 格式的日期
     *
     * @param date 日期
     * @return yyyy-MM-dd 格式的字符串
     */
    public static String toDayString(LocalDate date) {
        return toDayString(date, "yyyy-MM-dd");
    }

    /**
     * 转换成 yyyy-MM-dd 格式的日期
     *
     * @param date    日期
     * @param pattern 格式字符串
     */
    public static String toDayString(LocalDateTime date, String pattern) {
        return date.format(DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 转换成 yyyy-MM-dd 格式的日期
     *
     * @param date 日期
     * @return yyyy-MM-dd 格式的字符串
     */
    public static String toDayString(LocalDateTime date) {
        return toDayString(date, "yyyy-MM-dd");
    }

    /**
     * 将日期设置到0点
     *
     * @param date 日期
     * @return 清空time部分的日期
     */
    public static Date timeClear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取当前月份加n月后的最后一天的23:59:00
     *
     * @param plus 月份
     * @return 当前月份加n月后的最后一天的23:59:00
     */
    public static Date getPlusMonthLastDay(int plus) {
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, plus);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 判断当前时间是否在10点 - 20点 之间
     */
    public static Boolean betweenByNow(int before, int after) {
        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, before);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date begin = calendar.getTime();

        calendar.set(Calendar.HOUR_OF_DAY, after);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date end = calendar.getTime();

        if (now.after(begin) && now.before(end)) {
            return true;
        }
        return false;
    }


    public static String getCurrDate() {
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(date);
    }

    /**
     * 将日期设置到0点
     *
     * @param date 日期
     * @return 清空time部分的日期
     */
    public static Date timeClear(String date) {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            return sdf.parse(date + " 00:00:00");
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 将日期设置到23点59分59秒
     *
     * @param date 日期
     * @return 清空time部分的日期
     */
    public static Date timeLast(String date) {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            return sdf.parse(date + " 23:59:59");
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static LocalDate toLocalDate(Date date) {
        return java.time.LocalDate.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    public static LocalDateTime toLocalDateTime(Date date) {
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    public static Date toDate(LocalDate localDate) {
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    public static Date toDateLocalTime(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static LocalTime toLocalDateTimeStr(String timeString) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        LocalTime localTime = LocalTime.parse(timeString, formatter);
        return localTime;
    }

    /**
     * 解析日期区间字符串为LocalDate数组
     *
     * @param dateRange 日期区间字符串，格式为"yyyy-MM-dd~yyyy-MM-dd"
     * @return 日期区间的LocalDate数组，索引0为开始日期，索引1为结束日期
     */
    public static LocalDate[] parseDateRange(String dateRange) {
        String[] dates = dateRange.split("~");
        return new LocalDate[]{LocalDate.parse(dates[0]), LocalDate.parse(dates[1])};
    }

    public static Date strToDate(String time) {
        if (time == null || time.isEmpty()) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date date = sdf.parse(time);
            return date;
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String toDayStr(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return simpleDateFormat.format(date);
    }

    /**
     * 获取当前日期+时间 无毫秒
     *
     * @return
     */
    @NotNull
    public static Date getCurrentDateNoMillisecond() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static Date strToDateYMD(String time) {
        if (time == null || time.isEmpty()) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date date = sdf.parse(time);
            return date;
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取排班时间段
     *
     * @param timeSlot
     * @return
     */
    public static String timeSlotStr(String timeSlot) {
        if (StringUtils.isBlank(timeSlot)) {
            return null;
        }
        //10:20-10:50
        String[] timeArray = timeSlot.split("-");
        //10:20
        String[] startTimes = timeArray[0].split(":");
        //10:20-> 10
        int startHour = NumberUtils.toInt(startTimes[0]);
        int nextHours = startHour == 23 ? 0 : startHour + 1;
        String time = startTimes[0] + ":00-" + (nextHours < 10 ? "0" + nextHours + ":00" : nextHours + ":00");
        return time;
    }

    public static String toDayDateStr(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(date);
    }

    /**
     * 日期字符串转日期
     * @param dateString
     * @return
     */
    @Nullable
    public static Date stringToDate(String dateString) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return dateFormat.parse(dateString);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 加n月后的最后一天的23:59:00
     *
     * @param plus 月份
     * @return 加n月后的最后一天的23:59:00
     */
    public static Date getPlusMonthLastDay(Date date,int plus) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, plus);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }


    /**
     * 获取当前时间的0点0分0秒
     * @return 当前日期的0点0分0秒，Date类型
     */
    public static Date getStartOfDayAsDate(int days) {
        LocalDateTime startOfDay = LocalDateTime.now().plusDays(days).with(LocalTime.MIN).withNano(0);
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取当前时间加N天后的23点59分59秒
     * @param days 增加的天数
     * @return 增加N天后的日期的23点59分59秒，Date类型
     */
    public static Date getEndOfDayPlusDaysAsDate(int days) {
        LocalDateTime endOfDayPlusDays = LocalDateTime.now().plusDays(days).with(LocalTime.MAX).withNano(0);
        return Date.from(endOfDayPlusDays.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static String getEndOfDayPlusDaysAsStr(Date date, int days) {
        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime endOfDayPlusDays = localDateTime.plusDays(days).with(LocalTime.MAX).withNano(0);
        Date from = Date.from(endOfDayPlusDays.atZone(ZoneId.systemDefault()).toInstant());
        return toDayString(from, "yyyyMMdd");
    }

    /**
     * 获取当月1号零晨时间
     *
     * @return
     */
    public static Date getMonthStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取当月1号零晨时间
     *
     * @return
     */
    public static Date getMonthEnd() {
        // 获取当前月
        YearMonth currentMonth = YearMonth.now();
        // 计算下个月的第一天
        LocalDate nextMonthFirstDay = currentMonth.plusMonths(1).atDay(1);
        // 获取当前月的最后一天的23:59:59
        LocalDateTime lastDayEnd = nextMonthFirstDay.minusDays(1)
                .atTime(23, 59, 59);

        // 转换为 Instant
        Instant instant = lastDayEnd.atZone(ZoneId.systemDefault()).toInstant();
        return Date.from(instant);
    }

    // 计算两个日期之间的天数差
    public static Long daysBetween(Date date1, Date date2) {
        LocalDate localDate1 = date1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localDate2 = date2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return ChronoUnit.DAYS.between(localDate1, localDate2);
    }

    public static void main(String[] args) {
    }
}
