package com.dz.ms.basic.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.dto.ChannelLinkDTO;
import com.dz.ms.basic.service.ChannelLinkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

@Api(tags="渠道链接配置")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class ChannelLinkController  {

    @Resource
    private ChannelLinkService channelLinkService;
    @Value("${service.shortlink.domain:*}")
    public String shortlinkDomain;

    /**
     * 分页查询渠道链接配置
     * @param param
     * @return result<PageInfo<ChannelLinkDTO>>
     */
    @ApiOperation("分页查询渠道链接配置")
	@GetMapping(value = "/crm/channel_link/list")
    public Result<PageInfo<ChannelLinkDTO>> getChannelLinkList(@ModelAttribute ChannelLinkDTO param) {
        Result<PageInfo<ChannelLinkDTO>> result = new Result<>();
		PageInfo<ChannelLinkDTO> page = channelLinkService.getChannelLinkList(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询渠道链接配置
     * @param id
     * @return result<ChannelLinkDTO>
     */
    @ApiOperation("根据ID查询渠道链接配置")
	@GetMapping(value = "/crm/channel_link/info")
    public Result<ChannelLinkDTO> getChannelLinkById(@RequestParam("id") Long id) {
        Result<ChannelLinkDTO> result = new Result<>();
        ChannelLinkDTO channelLink = channelLinkService.getChannelLinkById(id);
        result.setData(channelLink);
        return result;
    }

    /**
     * 新增渠道链接配置
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增渠道链接配置",type = LogType.OPERATELOG)
    @ApiOperation("新增渠道链接配置")
	@PostMapping(value = "/crm/channel_link/add")
    public Result<Long> addChannelLink(@RequestBody ChannelLinkDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,true);
        Long id = channelLinkService.saveChannelLink(param);
        result.setData(id);
        return result;
    }

    /**
    * 更新渠道链接配置
    * @param param
    * @return result<Object>
    */
    @SysLog(value = "更新渠道链接配置",type = LogType.OPERATELOG)
    @ApiOperation("更新渠道链接配置")
    @PostMapping(value = "/crm/channel_link/update")
    public Result<Long> updateChannelLink(@RequestBody ChannelLinkDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,false);
        channelLinkService.saveChannelLink(param);
        result.setData(param.getId());
        return result;
    }

    /**
    * 验证保存参数
    * @param param
    */
    private void validationSaveParam(ChannelLinkDTO param,boolean isAdd) {
        if(isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if(!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
        if(StringUtils.isEmpty(param.getChannel())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "渠道编码不能为空");
        }
        if(StringUtils.isEmpty(param.getChannelName())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "渠道名称不能为空");
        }
        if(StringUtils.isEmpty(param.getPath())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "小程序路径不能为空");
        }
    }

	/**
     * 根据ID删除渠道链接配置
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除渠道链接配置")
	@PostMapping(value = "/crm/channel_link/delete")
    public Result<Boolean> deleteChannelLinkById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        channelLinkService.deleteChannelLinkById(param);
        result.setData(true);
        return result;
    }

    @ApiOperation("获取用户url scheme")
    @GetMapping(value = "/app/white/url_scheme")
    public Result<String> getUrlSchemeByCode(HttpServletResponse response, @RequestParam("code") String code) {
        response.setHeader("Access-Control-Allow-Origin",shortlinkDomain);
        response.setHeader("Access-Control-Allow-Credentials", "true");
        response.setHeader("Access-Control-Allow-Methods", "GET,OPTIONS");
        Result<String> result = new Result<>();
        String urlScheme = channelLinkService.getUrlSchemeByCode(code);
        result.setData(urlScheme);
        return result;
    }

}
