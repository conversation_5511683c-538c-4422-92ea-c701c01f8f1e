package com.dz.ms.${typeName}.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.${typeName}.dto.${objectName}DTO;
import com.dz.ms.${typeName}.entity.${objectName};
import com.dz.ms.${typeName}.mapper.${objectName}Mapper;
import com.dz.ms.${typeName}.service.${objectName}Service;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * ${tableComment}
 * @author: ${author}
 * @date:   ${nowDate}
 */
@Service
public class ${objectName}ServiceImpl extends ServiceImpl<${objectName}Mapper,${objectName}> implements ${objectName}Service {

	@Resource
    private ${objectName}Mapper ${varObjectName}Mapper;

	/**
     * 分页查询${tableComment}
     * @param param
     * @return PageInfo<${objectName}DTO>
     */
    @Override
    public PageInfo<${objectName}DTO> get${objectName}List(${objectName}DTO param) {
        ${objectName} ${varObjectName} = BeanCopierUtils.convertObjectTrim(param,${objectName}.class);
        IPage<${objectName}> page = ${varObjectName}Mapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), new QueryWrapper<>(${varObjectName}));
        return new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),BeanCopierUtils.convertList(page.getRecords(), ${objectName}DTO.class));
    }

    /**
     * 根据ID查询${tableComment}
     * @param ${primarkUpper}
     * @return ${objectName}DTO
     */
    @Override
    public ${objectName}DTO get${objectName}ById(${primarkMType} ${primarkUpper}) {
        ${objectName} ${varObjectName} = ${varObjectName}Mapper.selectById(${primarkUpper});
        return BeanCopierUtils.convertObject(${varObjectName},${objectName}DTO.class);
    }

    /**
     * 保存${tableComment}
     * @param param
     * @return Long
     */
    @Override
    public Long save${objectName}(${objectName}DTO param) {
        ${objectName} ${varObjectName} = new ${objectName}(<#list pureList as var>param.get${var.firstUpperCaseColum}()<#if var_index < pureList?size-1>, </#if></#list>);
        if(ParamUtils.isNullOr0Long(${varObjectName}.getId())) {
            ${varObjectName}Mapper.insert(${varObjectName});
        }
        else {
            ${varObjectName}Mapper.updateById(${varObjectName});
        }
        return ${varObjectName}.getId();
    }

    /**
     * 根据ID删除${tableComment}
     * @param param
     */
    @Override
    public void delete${objectName}ById(IdCodeDTO param) {
        ${varObjectName}Mapper.deleteById(param.getId());
    }
	
}