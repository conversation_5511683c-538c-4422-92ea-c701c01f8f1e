package com.dz.ms.user.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.user.SysUserDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.user.dto.RolePermissionDTO;
import com.dz.ms.user.dto.SysRoleDTO;
import com.dz.ms.user.entity.SysRole;
import com.dz.ms.user.service.SysRoleService;
import com.dz.ms.user.service.SysUserService;
import com.dz.ms.user.vo.RouterVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Api(tags="系统角色")
@RequestMapping(value="/crm")
@RestController
public class SysRoleController  {

    @Resource
    private SysRoleService sysRoleService;
    @Resource
    private SysUserService sysUserService;

    /**
     * 分页查询系统角色
     * @param param
     * @return result<PageInfo<SysRoleDTO>>
     */
    @ApiOperation("分页查询系统角色")
	@GetMapping(value = "/sys_role/list")
    public Result<PageInfo<SysRoleDTO>> getSysRoleList(@ModelAttribute SysRoleDTO param) {
        Result<PageInfo<SysRoleDTO>> result = new Result<>();
        SysRole sysRole = BeanCopierUtils.convertObjectTrim(param,SysRole.class);
        sysRole.setPlatform(SecurityContext.getUser().getPlatform());
        String name = sysRole.getRoleName();
        sysRole.setRoleName(null);
        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>(sysRole);
        if(StringUtils.isNotEmpty(name)) {
            wrapper.like(SysRole::getRoleName,name);
        }
        wrapper.orderByDesc(SysRole::getId);
        IPage<SysRole> page = sysRoleService.page(new Page<>(param.getPageNum(), param.getPageSize()), wrapper);
        List<SysRoleDTO> list = new ArrayList<>();
        if(!CollectionUtils.isEmpty(page.getRecords())) {
            List<Long> roleIds = page.getRecords().stream().map(SysRole :: getId).collect(Collectors.toList());
            Map<Long,Integer> map = sysUserService.getRoleUserCountByRoleIds(roleIds);
            page.getRecords().forEach(role -> {
                SysRoleDTO sysRoleDTO = BeanCopierUtils.convertObject(role,SysRoleDTO.class);
                sysRoleDTO.setUserNum(map.get(role.getId()));
                list.add(sysRoleDTO);
            });
        }
        result.setData(new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),list));
        return result;
    }

    @ApiOperation("查询系统角色列表不分页")
    @GetMapping(value = "/sys_role/list_nopage")
    public Result<List<SysRoleDTO>> getSysRoleListNopage(@ModelAttribute SysRoleDTO param) {
        Result<List<SysRoleDTO>> result = new Result<>();
        SysRole sysRole = BeanCopierUtils.convertObjectTrim(param,SysRole.class);
        sysRole.setPlatform(SecurityContext.getUser().getPlatform());
        List<SysRole> list = sysRoleService.list(new LambdaQueryWrapper<>(sysRole));
        result.setData(BeanCopierUtils.convertList(list,SysRoleDTO.class));
        return result;
    }

    /**
     * 根据ID查询系统角色
     * @param id
     * @return result<SysRoleDTO>
     */
    @ApiOperation("根据ID查询系统角色")
	@GetMapping(value = "/sys_role/info")
    public Result<SysRoleDTO> getSysRoleById(@RequestParam("id") Long id) {
        Result<SysRoleDTO> result = new Result<>();
        SysRole sysRole = sysRoleService.getById(id);
        SysRoleDTO role = BeanCopierUtils.convertObject(sysRole,SysRoleDTO.class);
        List<Long> permitIds = sysRoleService.getRolePermitIds(id);
        role.setPermitIds(permitIds);
        result.setData(role);
        return result;
    }

    /**
     * 新增系统角色
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增系统角色",type = LogType.OPERATELOG)
    @ApiOperation("新增系统角色")
    @PostMapping(value = "/sys_role/add")
    public Result<Long> addSysRole(@RequestBody SysRoleDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,true);
        SysRole sysRole = new SysRole(param.getId(), param.getRoleName(), param.getRoleDesc(), SecurityContext.getUser().getPlatform());
        sysRoleService.save(sysRole);
        if(!CollectionUtils.isEmpty(param.getPermitIds())) {
            RolePermissionDTO rolePermission = new RolePermissionDTO();
            rolePermission.setRoleId(sysRole.getId());
            rolePermission.setPermitIds(param.getPermitIds());
            sysRoleService.bindPermit(rolePermission);
        }
        result.setData(sysRole.getId());
        return result;
    }

    /**
     * 更新系统角色
     * @param param
     * @return result<Object>
     */
    @SysLog(value = "更新系统角色",type = LogType.OPERATELOG)
    @ApiOperation("更新系统角色")
    @PostMapping(value = "/sys_role/update")
    public Result<Long> updateSysRole(@RequestBody SysRoleDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,false);
        SysRole sysRole = new SysRole(param.getId(), param.getRoleName(), param.getRoleDesc(),SecurityContext.getUser().getPlatform());
        sysRoleService.updateById(sysRole);
        if(!CollectionUtils.isEmpty(param.getPermitIds())) {
            RolePermissionDTO rolePermission = new RolePermissionDTO();
            rolePermission.setRoleId(sysRole.getId());
            rolePermission.setPermitIds(param.getPermitIds());
            sysRoleService.bindPermit(rolePermission);
        }
        result.setData(param.getId());
        return result;
    }

    /**
     * 验证保存参数
     * @param param
     */
    private void validationSaveParam(SysRoleDTO param,boolean isAdd) {
        if(isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if(!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper();
        wrapper.eq(SysRole::getRoleName,param.getRoleName());
        boolean isNew = ParamUtils.isNullOr0Long(param.getId());
        if(!isNew) {
            wrapper.ne(SysRole::getId,param.getId());
        }
        long count = sysRoleService.count(wrapper);
        if(count > 0) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "角色名称不能重复");
        }
    }
	
	/**
     * 根据ID删除系统角色
     * @param param
     * @return result<Boolean>
     */
    @SysLog(value = "根据ID删除系统角色",type = LogType.OPERATELOG)
    @ApiOperation("根据ID删除系统角色")
	@PostMapping(value = "/sys_role/delete")
    public Result<Boolean> deleteSysRoleById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        Map<Long,Integer> map = sysUserService.getRoleUserCountByRoleIds(Arrays.asList(new Long[] {param.getId()}));
        if(null != map) {
            Integer count = map.get(param.getId());
            if(null != count && count > 0) {
                return result.paramErroResult("该角色已被员工使用");
            }
        }
        sysRoleService.removeById(param.getId());
        result.setData(true);
        return result;
    }

    @ApiOperation("获取角色权限列表")
    @GetMapping(value = "/sys_role/permits")
    public Result<List<Long>> getRolePermitCodes(@RequestParam("roleId") Long roleId) {
        Result<List<Long>> result = new Result<>();
        List<Long> list = sysRoleService.getRolePermitIds(roleId);
        result.setData(list);
        return result;
    }

    @SysLog(value = "绑定角色权限",type = LogType.OPERATELOG)
    @ApiOperation("绑定角色权限")
    @PostMapping(value = "/sys_role/bind_permit")
    public Result<Boolean> bindPermit(@RequestBody RolePermissionDTO param) {
        Result<Boolean> result = new Result<>();
        sysRoleService.bindPermit(param);
        result.setData(true);
        return result;
    }

    @ApiOperation("菜单列表")
    @GetMapping(value = "/sys_role/getRouterList")
    public Result<List<RouterVo>> getRouterList() {
        Result<List<RouterVo>> result = new Result<>();
        SysUserDTO sysUser = sysUserService.getLoginUserContainRootId();
        result.setData(sysRoleService.selectMenuTree(sysUser));
        return result;
    }

}
