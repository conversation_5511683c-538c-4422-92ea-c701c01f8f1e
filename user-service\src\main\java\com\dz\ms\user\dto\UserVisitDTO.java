package com.dz.ms.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 用户访问记录DTO
 * @author: Handy
 * @date:   2023/08/26 01:48
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "用户访问记录")
public class UserVisitDTO {

    @ApiModelProperty(value = "访问类型 1页面访问 2按钮点击")
    private Integer visitType;
    @ApiModelProperty(value = "小程序openid")
    private String openid;
    @ApiModelProperty(value = "微信unionid")
    private String unionid;
    @ApiModelProperty(value = "会员等级")
    private String cardLevel;
    @ApiModelProperty(value = "来源渠道")
    private String channel;
    @ApiModelProperty(value = "广告ID")
    private String gdtVid;
    @ApiModelProperty(value = "页面类型编码")
    private String pageCode;
    @ApiModelProperty(value = "详情页唯一标识")
    private String detailsId;
    @ApiModelProperty(value = "按钮名称")
    private String button;
    @ApiModelProperty(value = "页面地址")
    private String path;
    @ApiModelProperty(value = "页面参数json")
    private String param;
    @ApiModelProperty(value = "场景值")
    private String scene;
    @ApiModelProperty(value = "来源页面编码")
    private String referer;

}
