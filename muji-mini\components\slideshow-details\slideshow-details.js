// components/slideshow-details/slideshow-details.js
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    list: {
      type: Array,
      value: [],
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    indicatorDots: false,
    autoplay: false,
    interval: 5000,
    duration: 500,
    current: 0
  },

  /**
   * 组件的方法列表
   */
  methods: {
    changeImg(current, source) {

      if (current.detail.source == 'touch') {
        this.setData({
          current: current.detail.current
        })
      }
      console.log(current, source, this.data.current);
    }

  }
})
