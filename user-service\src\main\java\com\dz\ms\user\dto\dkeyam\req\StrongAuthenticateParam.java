package com.dz.ms.user.dto.dkeyam.req;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 认证入参
 * @author: fei
 * @date: 2025/01/22 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StrongAuthenticateParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("站点名称")
    private String tenantName;

    @ApiModelProperty("认证设备名称 需要在认证系统上创建认证设备，设备类型为：API调用")
    private String accessServerName;

    @ApiModelProperty("认证设备共享秘钥")
    private String sharedSecret;

    @ApiModelProperty("用户登录名")
    private String loginName;
    
    @ApiModelProperty("密码，多种情况【不要加号】：(1) 用户静态密码(2) 令牌动态密码(3) 用户静态密码+令牌动态密码")
    private String password;

    @ApiModelProperty("是否推送认证，默认为非推送")
    private Boolean pushAuth;
    
}
