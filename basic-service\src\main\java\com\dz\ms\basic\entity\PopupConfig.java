package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 弹窗配置
 * @author: Handy
 * @date:   2023/11/20 19:20
 */
@Getter
@Setter
@NoArgsConstructor
@Table("弹窗配置")
@TableName(value = "popup_config")
public class PopupConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "弹窗配置ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = false,comment = "弹窗类型 1预约黑名单",isUnique = true)
    private Integer popupType;
    @Columns(type = ColumnType.VARCHAR,length = 256,isNull = true,comment = "弹窗图片")
    private String popupImg;
    @Columns(type = ColumnType.VARCHAR,length = 256,isNull = true,comment = "跳转链接配置JSON")
    private String jumpLink;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = true,comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    public PopupConfig(Long id, Integer popupType, String popupImg, String jumpLink) {
        this.id = id;
        this.popupType = popupType;
        this.popupImg = popupImg;
        this.jumpLink = jumpLink;
    }

}
