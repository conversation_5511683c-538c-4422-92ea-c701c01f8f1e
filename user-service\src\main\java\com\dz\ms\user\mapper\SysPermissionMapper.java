package com.dz.ms.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.user.entity.SysPermission;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 权限功能Mapper
 * @author: Handy
 * @date:   2022/2/4 23:04
 */
@Repository
public interface SysPermissionMapper extends BaseMapper<SysPermission> {

    /**
     * 获取角色权限列表
     * @param roleId
     * @return
     */
    List<SysPermission> getRolePermitCodes(@Param("roleId") Long roleId,@Param("permitType") Integer permitType,@Param("platform") Integer platform);

    /**
     * 获取角色权限ID列表
     * @param roleId
     * @return
     */
    List<Long> getRolePermitIds(@Param("roleId") Long roleId,@Param("platform") Integer platform);

}
