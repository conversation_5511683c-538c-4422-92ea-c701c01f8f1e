<template>
  <!-- 获取省市区 地址 -->
  <a-form-item label="门店地址" required>
    <a-form-item name="location">
      <a-cascader :disabled="disabled" v-model:value="form.location" :show-search="{ filter }" :field-names="{ label: 'name', value: 'id', children: 'children' }" :options="options" expand-trigger="hover" placeholder="请选择省市区" @change="changeArea" :getPopupContainer="triggerNode => triggerNode.parentNode" />
    </a-form-item>
    <a-form-item name="address">
      <a-textarea :disabled="disabled" v-model:value="form.address" placeholder="请输入详细地址" show-count :maxlength="100" />
    </a-form-item>
  </a-form-item>
</template>
<script setup>
import { getAreaList, } from '@/http/index.js'
import { message } from 'ant-design-vue';
import { useGlobalStore } from '@/store'
const global = useGlobalStore()
import { reactive, toRefs, ref, onMounted, watch } from 'vue';
const emit = defineEmits(['success'])
const props = defineProps({
  // 是否可以修改
  disabled: {
    type: Boolean,
    default: false
  },
  // 省市区
  location: {
    type: Array,
    default() {
      return []
    }
  },
  // 详细地址
  address: {
    type: String,
    default: ''
  },
  // 表单
  form: {
    type: Object,
    default() { return {} }
  }
})



let { options, selectedOptions } = toRefs(reactive({
  options: global.areaList,
  selectedOptions: []
}))

const filter = (inputValue, path) => {
  return path.some(option => option.name.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);
};

// 详情数据更新
// watch(() => props.provinceId, (value) => {
//   if (value) {
//     area.value = [props.provinceId, props.cityId, props.areaId]
//   }
// })



// 获取省市区数据
if (global.areaList.length) {
  options.value = global.areaList;
} else {
  getAreaList().then(res => {
    global.setAreaList(res.data)
    options.value = res.data
  })
}

// 保存数据
const ok = () => {
  let area = selectedOptions.value
  emit('ok', {
    province: area[0].name,
    provinceId: area[0].id,
    city: area[1].name,
    cityId: area[1].id,
    area: area[2].name,
    areaId: area[2].id,
  })
}

// 修改省市区
const changeArea = (value, data) => {
  selectedOptions.value = data
  ok()
}
</script>
<style lang="scss" scoped>
.upload {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  font-size: 10px;
  color: #999;
  outline: 1px dashed #d9d9d9;
  background: rgb(250, 250, 250);
  &-eye {
    font-size: 14px;
    position: absolute;
    padding: 5px;
    top: 0;
    right: 0;
    color: red;
  }
  &-delete {
    font-size: 14px;
    position: absolute;
    padding: 0;
    bottom: 0;
    right: 0;
    color: red;
  }
  &.show {
    background: rgb(250, 250, 250) url(../assets/images/bg.png) center repeat;
  }

  &:hover {
    border-color: #1890ff;
    // cursor: pointer;
  }
  &-loading {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
  }
}
</style>