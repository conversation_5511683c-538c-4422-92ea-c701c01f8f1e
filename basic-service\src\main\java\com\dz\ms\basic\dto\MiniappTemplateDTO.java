package com.dz.ms.basic.dto;
import java.util.Date;
import java.util.List;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 小程序页面模板DTO
 * @author: Handy
 * @date:   2022/02/04 22:59
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "小程序常规页面模板")
public class MiniappTemplateDTO extends BaseDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "模板类型 1常规页面 2系统页面 3定制页面 4弹窗页面 5开屏页面")
    private Integer templateType;
    @ApiModelProperty(value = "页面类型 0默认 1首页 2会员中心 3会员码 4生活圈 5更多 6活动开屏 7首页开屏 ")
    private Integer pageType;
    @ApiModelProperty(value = "同类型是否只存在一个已发布 0否 1是")
    private Integer isOnly;
    @ApiModelProperty(value = "模板名称")
    private String templateName;
    @ApiModelProperty(value = "模板内容json")
    private String content;
    @ApiModelProperty(value = "模板内容预览json")
    private String pageJson;
    @ApiModelProperty(value = "页面路径")
    private String pagePath;
    @ApiModelProperty(value = "是否发布 0否1是")
    private Integer publish;
    @ApiModelProperty(value = "关联素材ID列表")
    private List<Long> materialIds;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
}
