// 所有的命名必须全局唯一
import service from '@/utils/request.js'


// 问卷列表
export function npsList(data = {}) {
    return service({
        url: '/crm/sales/nps/list',
        method: 'get',
        data
    })
}

// 
// 问卷状态更新
export function npsUpdateState(data = {}) {
    return service({
        url: '/crm/sales/nps/update_state',
        method: 'post',
        data
    })
}

//新增优惠券基础信息
export function addNps(data = {}) {
  return service({
    url: '/crm/sales/nps/add',
    method: 'post',
    data
  })
}

//更新优惠券基础信息
export function updateNps(data = {}) {
  return service({
    url: '/crm/sales/nps/update',
    method: 'post',
    data
  })
}

//根据ID查询优惠券基础信息
export function npsInfo(data = {}) {
  return service({
    url: '/crm/sales/nps/info',
    method: 'get',
    data
  })
}