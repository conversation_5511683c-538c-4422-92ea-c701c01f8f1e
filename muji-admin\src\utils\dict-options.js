export const equityStatus = [
    {
        value: '',
        label: '全部'
    },
    {
        value: 0,
        label: '停用'
    },
    {
        value: 1,
        label: '启用'
    }
]

export const exchangeDescTypeOptions = [
    {
        value: 1,
        label: '使用图片配置'
    },
    {
        value: 2,
        label: '使用文字编辑器'
    }
]

export const CrowdPackTypeOptions = [
    {
        value: 0,
        label: '时间段'
    },
    {
        value: 1,
        label: '永久可用'
    }
]

export const CrowdPackTypeOptions1 = [
    {
        value: 0,
        label: '新建规则'
    },
    {
        value: 1,
        label: '导入人群条件'
    }
]

export const pdTypeOptions = [
    {
        value: 1,
        label: '实物商品'
    },
    {
        value: 2,
        label: '电子券'
    }
]
// 0条件规则 1使用人群包 2导入人群包
export const crowdTypeOptions = [
    {
        value: 0,
        label: '条件规则'
    },
    {
        value: 1,
        label: '使用人群包'
    },
    {
        value: 2,
        label: '导入人群包'
    }
]


export const isStoreServeOptions = [
    {
        value: null,
        label: '全部'
    },
    {
        value: 1,
        label: '未关联'
    },
    {
        value: 2,
        label: '已关联'
    }
]

export const isStoreImageOptions = [
    {
        value: null,
        label: '全部'
    },
    {
        value: 1,
        label: '未关联'
    },
    {
        value: 2,
        label: '已关联'
    }
]

export const isWeWorkImageOptions = [
    {
        value: null,
        label: '全部'
    },
    {
        value: 1,
        label: '未配置'
    },
    {
        value: 2,
        label: '已配置'
    }
]

export const statusOptions = [
    {
        value: '',
        label: '全部'
    },
    {
        value: 0,
        label: '启用'
    },
    {
        value: 1,
        label: '停用'
    }
]

export const taskStatusOptions = [
    {
        value: 0,
        label: '待开始'
    },
    {
        value: 1,
        label: '进行中'
    },
    {
        value: 2,
        label: '已结束'
    }
]

export const taskTypeOptions = [
    {
        value: 1,
        label: '限时'
    },
    {
        value: 2,
        label: '购物'
    },
    {
        value: 3,
        label: '互动'
    }
]

export const showTimeTypeTypeOptions = [
    {
        value: 1,
        label: '时间段'
    },
    {
        value: 2,
        label: '永久可用'
    },
    {
        value: 3,
        label: '根据活动时间展示'
    }
]

export const isTimeRestrictTypeOptions = [
    {
        value: 1,
        label: '限时'
    },
    {
        value: 2,
        label: '不限时'
    }
]
export const taskContentOptions = [
    {
        value: 1,
        label: '线下打卡'
    },
    {
        value: 2,
        label: '兑礼任务'
    },
    {
        value: 3,
        label: '线下消费'
    },
    {
        value: 4,
        label: '邀请好友'
    },
    {
        value: 5,
        label: '购物任务'
    }, {
        value: 6,
        label: '分享任务'
    }
]


export let completeTaskOptions = ref([
    {
        value: 1,
        label: '一次性',
        disabled: false
    },
    {
        value: 2,
        label: '周期',
        disabled: false
    },
    {
        value: 3,
        label: '周期+阶梯',
        disabled: false
    }
])
export const rewardTypeOptions = [
    {
        value: 1,
        label: '优惠券'
    },
    {
        value: 2,
        label: '积分'
    },
    {
        value: 3,
        label: '抽奖次数'
    }
]

export const expressStatusOptions = [
    {
        value: 0,
        label: '待发货'
    },
    {
        value: 1,
        label: '已发货'
    },
    {
        value: 2,
        label: '部分发货'
    },

]
export const orderStatusOptions = [
    // {
    //     value: 0,
    //     label: '已完成'
    // },
    // {
    //     value: 1,
    //     label: '待支付'
    // },
    // {
    //     value: 2,
    //     label: '待发货'
    // },
    // {
    //     value: 3,
    //     label: '已发货'
    // },
    {
        value: 4,
        label: '待兑换'
    },
    // {
    //     value: 5,
    //     label: '部分兑换'
    // },
    {
        value: 6,
        label: '已兑换'
    },
    // {
    //     value: 7,
    //     label: '已取消'
    // },
]

export const storetypeOptions = [
    {
        value: 1,
        label: '普通店'
    },
    {
        value: 2,
        label: '旗舰店'
    },
]

export const isCloseOptions = [
    {
        value: 0,
        label: '根据开业日期判断'
    },
    {
        value: 1,
        label: '闭店'
    },
]
export const isUpdateServeOptions = [
    {
        value: 0,
        label: '否'
    },
    {
        value: 1,
        label: '是'
    },
]

export const isOpenServeOptions = [
    {
        value: 1,
        label: '待开业'
    },
    {
        value: 2,
        label: '开业中'
    }, {
        value: 3,
        label: '闭店'
    },

    // {
    //     value: 4,
    //     label: '改装中'

    // }
]