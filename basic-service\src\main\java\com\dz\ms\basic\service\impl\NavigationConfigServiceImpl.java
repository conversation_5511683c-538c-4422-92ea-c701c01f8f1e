package com.dz.ms.basic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.constants.CacheKeys;
import com.dz.ms.basic.dto.NavigationConfigDTO;
import com.dz.ms.basic.entity.DefaultData;
import com.dz.ms.basic.entity.NavigationConfig;
import com.dz.ms.basic.mapper.DefaultDataMapper;
import com.dz.ms.basic.mapper.UiConfigMapper;
import com.dz.ms.basic.service.NavigationConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 小程序UI自定义配置
 * @author: Handy
 * @date:   2022/11/21 14:54
 */
@Service
public class NavigationConfigServiceImpl extends ServiceImpl<UiConfigMapper, NavigationConfig> implements NavigationConfigService {

	@Resource
    private UiConfigMapper uiConfigMapper;
    @Resource
    private DefaultDataMapper defaultDataMapper;
    @Resource
    private RedisService redisService;

	/**
     * 分页查询小程序UI自定义配置
     * @param param
     * @return PageInfo<UiConfigDTO>
     */
	@Override
    public PageInfo<NavigationConfigDTO> getUiConfigList(NavigationConfigDTO param) {
        NavigationConfig navigationConfig = BeanCopierUtils.convertObjectTrim(param, NavigationConfig.class);
        IPage<NavigationConfig> page = uiConfigMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), new QueryWrapper<>(navigationConfig));
        return new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),BeanCopierUtils.convertList(page.getRecords(), NavigationConfigDTO.class));
    }

    /**
     * 根据ID查询小程序UI自定义配置
     * @param id
     * @return UiConfigDTO
     */
    @Override
    public NavigationConfigDTO getUiConfigById(Long id) {
        NavigationConfig navigationConfig = uiConfigMapper.selectById(id);
        return BeanCopierUtils.convertObject(navigationConfig, NavigationConfigDTO.class);
    }

    /**
     * 保存小程序UI自定义配置
     * @param param
     * @return Long
     */
    @Override
    public Long saveUiConfig(NavigationConfigDTO param) {
        NavigationConfig navigationConfig = new NavigationConfig(param.getId(), param.getColor(), param.getSelectedColor(), param.getContent(), param.getChecked());
        if(null != param.getChecked() && param.getChecked().equals(1)) {
            uiConfigMapper.uncheckedUiConfig();
        }
        if (null != param.getBgColor()) {
            navigationConfig.setBgColor(param.getBgColor());
        }
        if (null != param.getFontSize()) {
            navigationConfig.setFontSize(param.getFontSize());
        }
        if(ParamUtils.isNullOr0Long(navigationConfig.getId())) {
            uiConfigMapper.insert(navigationConfig);
        }
        else {
            uiConfigMapper.updateById(navigationConfig);
        }
        redisService.del(CacheKeys.NAVIGATION_CONFIG_DEFAULT+ SecurityContext.getUser().getTenantId());
        return navigationConfig.getId();
    }

    /**
     * 根据ID删除小程序UI自定义配置
     * @param param
     */
    @Override
    public void deleteUiConfigById(IdCodeDTO param) {
        NavigationConfig navigationConfig = uiConfigMapper.selectById(param.getId());
        if(null == navigationConfig) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT);
        }
        if(navigationConfig.getChecked().equals(1)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"该配置正在使用中，请先取消选中");
        }
        uiConfigMapper.deleteById(param.getId());
    }

    /**
     * 获取所有小程序UI自定义配置列表
     * @return
     */
    @Override
    public List<NavigationConfigDTO> getAllUiConfig() {
        return uiConfigMapper.getAllUiConfig();
    }

    /**
     * 获取当前选中的UI配置
     * @return
     */
    @Override
    public NavigationConfigDTO getCheckedUiConfig() {
        String key = CacheKeys.NAVIGATION_CONFIG_DEFAULT+SecurityContext.getUser().getTenantId();
        Object object = redisService.get(key);
        if(null != object) {
            return (NavigationConfigDTO) object;
        }
        NavigationConfigDTO config = uiConfigMapper.getCheckedUiConfig();
        if(null == config) {
            String content = getDefaultUiConfig();
            config = new NavigationConfigDTO();
            config.setContent(content);
//            config.setUiName("默认配置");
            return config;
        }
        redisService.set(key,config);
        return config;
    }

    /**
     * 选中UI配置
     * @param param
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkedUiConfigById(IdCodeDTO param) {
        uiConfigMapper.uncheckedUiConfig();
        NavigationConfig navigationConfig = new NavigationConfig();
        navigationConfig.setId(param.getId());
        navigationConfig.setChecked(1);
        uiConfigMapper.updateById(navigationConfig);
        redisService.del(CacheKeys.NAVIGATION_CONFIG_DEFAULT+ SecurityContext.getUser().getTenantId());
    }

    private String getDefaultUiConfig() {
        String key = CacheKeys.NAVIGATION_CONFIG_DEFAULT+0;
        String config = redisService.getString(key);
        if(null != config) {
            return config;
        }
        DefaultData defaultData = defaultDataMapper.selectOne(new LambdaQueryWrapper<DefaultData>().eq(DefaultData::getType,1));
        if(null == defaultData) {
            return null;
        }
        redisService.setString(key,defaultData.getContent());
        return defaultData.getContent();
    }

}