package com.dz.ms.user.service;

import com.dz.common.base.dto.BaseDTO;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.user.MyMsgDTO;
import com.dz.common.core.vo.MyMsgVo;

import javax.servlet.http.HttpServletResponse;
import java.net.MalformedURLException;
import java.text.ParseException;
import java.util.List;

/**
 * 我的消息
 */
public interface MyMsgService {

    PageInfo<MyMsgDTO> msgList(BaseDTO param);

    MyMsgDTO unReadMsgList();

    Integer msgNum();

    void add(MyMsgVo msgCode) throws ParseException;

    void read(Long id);

    void sync(MyMsgVo msgCode, HttpServletResponse response) throws MalformedURLException;
}
