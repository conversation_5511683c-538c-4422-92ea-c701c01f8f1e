package com.dz.common.core.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 日志类型
 * @author: Handy
 * @date:   2022/08/04 16:36
 */
public enum LogType {

	OPERATELOG(1,"操作日志"),
	SCRIPT(2,"脚本同步"),
	INTERFACE(3,"接口调用"),
	IMPORTDATA(4,"数据导入");

	private int type;
	private String name;

	LogType() {

	}

	LogType(int type,String name) {
		this.type = type;
		this.name = name;
	}

	public int getType() {
		return type;
	}

	public String getName() {
		return name;
	}

	public static String getNameByType(Integer type) {
		Map<Integer,String> map = new HashMap();
		map.put(OPERATELOG.getType(),OPERATELOG.getName());
		map.put(SCRIPT.getType(),SCRIPT.getName());
		map.put(INTERFACE.getType(),INTERFACE.getName());
		map.put(IMPORTDATA.getType(),IMPORTDATA.getName());
		return map.get(type);
	}

}
