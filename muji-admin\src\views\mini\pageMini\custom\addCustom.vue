<template>
  <a-modal title="代码级页面" :width="500" centered :maskClosable="false" :closable="true" :open="visible" @cancel="cancel">
    <a-form class="form" ref="formRef" :model="addParams" labelAlign="left" :labelCol="{ style: 'width:80px' }">
      <a-form-item label="页面名称" name="templateName" :rules="[{  required: true, message: '请输入', trigger: ['blur', 'change'] }]">
        <a-input placeholder="请输入" v-model:value="addParams.templateName" allowClear></a-input>
      </a-form-item>
      <a-form-item label="页面路径" name="pagePath" :rules="[{  required: true, message: '请输入', trigger: ['blur', 'change'] }]">
        <a-textarea size="large" :disabled="!!addParams.id" placeholder="请输入" v-model:value="addParams.pagePath" allowClear></a-textarea>
      </a-form-item>
    </a-form>
    <template #footer v-if="!disabled">
      <a-space>
        <a-button @click="cancel">取消</a-button>
        <a-button type="primary" :loading="loading" @click="ok">确定</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup>
import { message } from "ant-design-vue";
import { addTemplate, updateTemplate } from '@/http/index.js'
import { cloneDeep, template } from 'lodash'
import { v4 as uuidv4 } from "uuid";
const formRef = ref()
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  // 页面配置
  visible: {
    type: Boolean,
    default: false,
  },
  // 数据
  data: {
    type: Object,
    default() {
      return {}
    }
  }
})



watch(() => props.visible, (value) => {
  // 弹窗操作
  if (value) {
    addParams.value = cloneDeep(props.data || {
      id: '',
      templateCode: '',// 模板编号
      //  templateType	模板类型 1常规页面 2系统页面 3定制页面 4弹窗页面
      templateType: 3,// 模板类型
      templateName: '',// 模板名称
      groupId: '',// 页面组ID
      groupName: '',// 页面组
      pagePath: '',// 页面地址
      isOnly: 1,//	同类型是否只存在一个已发布 0否 1是
      publish: 1, //是否发布 0否1是
      pageJson: '',//	模板内容预览json
      content: '', //模板内容json
    })
  }
  formRef.value?.resetFields()

})

// templateType	模板类型 1常规页面 2系统页面 3定制页面 4弹窗页面  5开屏页
// pageType	页面类型 0二级自定义页面 1首页 2兑换 3会员码 4生活圈 5更多

const { addParams, loading } = toRefs(reactive({
  addParams: {},
  loading: false,
}))



const cancel = () => {
  emit('cancel')
}



// 保存设置
const ok = () => {
  formRef.value.validate().then(res => {
    let params = cloneDeep(addParams.value)
    params.content = JSON.stringify({
      templateName: params.templateName,
    })
    let api = params.id ? updateTemplate : addTemplate;
    loading.value = true
    api(params).then(() => {
      message.success('保存成功')
      emit('cancel')
      emit('ok')
    }).finally(() => {
      loading.value = false
    })
  })
}
</script>

<style  scoped lang="scss">
</style>
