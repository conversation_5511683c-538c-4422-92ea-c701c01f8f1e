package com.dz.ms.basic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "推广渠道查询")
public class PromotionChannelListDTO {

    @ApiModelProperty(value = "一级渠道ID")
    private Long oneId;
    @ApiModelProperty(value = "二级渠道ID")
    private Long twoId;
    @ApiModelProperty(value = "一级渠道名称")
    private String oneChannelName;
    @ApiModelProperty(value = "一级渠道参数")
    private String oneChannelParam;
    @ApiModelProperty(value = "二级渠道名称")
    private String twoChannelName;
    @ApiModelProperty(value = "二级渠道参数")
    private String twoChannelParam;;
    @ApiModelProperty(value = "0正常 1删除")
    private Integer isDeleted;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "创建人姓名")
    private String creatorName;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "父类ID")
    private Long parentId;
}
