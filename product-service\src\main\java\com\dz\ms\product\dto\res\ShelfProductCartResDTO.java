package com.dz.ms.product.dto.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 货架商品购物车相关DTO
 *
 * @author: fei
 * @date: 2024/12/10 16:25
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@ApiModel(value = "货架商品购物车相关DTO")
public class ShelfProductCartResDTO {

    @ApiModelProperty(value = "货架id")
    private Long shelfId;
    @ApiModelProperty(value = "货架商品id")
    private Long shelfProductId;
    @ApiModelProperty(value = "商品id")
    private Long productId;
    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "兑换积分，加购时积分")
    private Integer costPoint;
    @ApiModelProperty(value = "兑换金额，加购时单价")
    private BigDecimal costPrice;
    @ApiModelProperty(value = "商品类型 1实物商品 2电子券")
    private Integer pdType;
    @ApiModelProperty(value = "外部数据ID 优惠券ID需兑换")
    private String venderId;
    @ApiModelProperty(value = "商品图片")
    private String imgUrl;
    @ApiModelProperty(value = "商品状态(0禁用 1启用)")
    private Integer state;
    @ApiModelProperty(value = "金额是否展示在货架列表")
    private Integer costPriceOnShelf;
    @ApiModelProperty(value = "兑换积分，来自商品表的价格留存")
    private Integer pCostPoint;
    @ApiModelProperty(value = "1积分+金额时 仍需支付的金额，来自商品表的价格留存")
    private BigDecimal pCostPrice;
    @ApiModelProperty(value = "兑换积分，来自货架上的价格留存")
    private Integer sCostPoint;
    @ApiModelProperty(value = "积分划线价，来自货架上的价格留存")
    private Integer sPrePoint;
    @ApiModelProperty(value = "货架当前库存")
    private Integer sCurrentInventory;
    @ApiModelProperty(value = "角标ID列表")
    private List<Long> superscriptIdList;
    @ApiModelProperty(value = "角标名称列表")
    private List<String> superscriptNameList;
    @ApiModelProperty(value = "活动角标字符串")
    private List<String> superscriptCampaignNameList;
    @ApiModelProperty(value = "营销活动ID")
    private Long campaignId;
    @ApiModelProperty(value = "营销规则ID")
    private Long ruleId;
    @ApiModelProperty(value = "营销规则人群包ID")
    private Long ruleGroupId;
    @ApiModelProperty(value = "活动规则限购时间设置 1活动时间内 2周期")
    private Integer ruleType;
    @ApiModelProperty(value = "周期天数")
    private Integer period;
    @ApiModelProperty(value = "活动规则创建时间")
    private Date rCreated;
    @ApiModelProperty(value = "兑换积分，来自营销规则的价格留存")
    private Integer rCostPoint;
    @ApiModelProperty(value = "积分划线价，来自营销规则的价格留存")
    private Integer rPrePoint;
    @ApiModelProperty(value = "每人限购数")
    private Integer rEveryoneLimit;
    @ApiModelProperty(value = "活动限购剩余库存")
    private Integer restInventory;
    @ApiModelProperty(value = "活动限购数量")
    private Integer rPurchaseLimit;
    @ApiModelProperty(value = "是否展示 0不展示 1展示")
    private Integer beShow;
    @ApiModelProperty(value = "展示方式 1积分加价购 2商品兑换")
    private Integer showType;
    @ApiModelProperty(value = "活动开始时间")
    private Date campaignOnStartTime;
    @ApiModelProperty(value = "活动结束时间")
    private Date campaignOnEndTime;
    @ApiModelProperty(value = "默认限购数量/月(货架商品限购数量)")
    private Integer limitNum;

}
