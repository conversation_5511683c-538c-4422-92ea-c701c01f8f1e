
import { createApp, ref } from 'vue';
import App from './App.vue'
import '@/assets/scss/index.scss';// 全局全局样式
// 对分页请求的全局控制
import { setGlobalOptions } from 'vue-request';
setGlobalOptions({
  // 设定一个延迟值，当等待时间大于延迟值时 loading 才会被设置成 true
  loadingDelay: 400,
  loadingKeep: 1000,//  可以让 loading 持续指定的时间
  // 如果请求时间少于指定的时间，则最终时间为指定的时间
  // 如果请求时间大于指定的时间，则最终时间为请求的时间
});
const app = createApp(App);
import { createPinia } from 'pinia'
const pinia = createPinia()
app.use(pinia)
import global from '@/utils/global'



import Antd from 'ant-design-vue';
import 'ant-design-vue/dist/reset.css';
import 'virtual:svg-icons-register' //svg 需要用到的组件
import * as antIcons from '@ant-design/icons-vue';// 全局引入icon
// 全局引入icon
Object.keys(antIcons).forEach(key => {
  app.component(key, antIcons[key])
})
import SvgIcon from './components/svg-icon/index.vue'//svg 组件
import router from './router/index'
import './permission' // 路由拦截器

// 视频播放组件
import videoPlay from 'vue3-video-play' // 引入组件
import 'vue3-video-play/dist/style.css' // 引入css

// 引入全局组件
import layout from '@/components/layout/index.vue';
import searchForm from '@/components/searchForm.vue'
import uploadImg from '@/components/uploadImg.vue'
import uploadMoreImg from '@/components/uploadMoreImg.vue'
import uploadVideo from '@/components/uploadVideo.vue'
import areaList from '@/components/areaList.vue'
import downloadFile from '@/components/downloadFile.vue'
import uploadFile from '@/components/uploadFile.vue'
import VueDraggable from 'vuedraggable'
import BaseAndOrSelect from '@/components/BaseAndOrSelect.vue'
import BaseBelongingShelfSelect from '@/components/BaseBelongingShelfSelect.vue'
import BaseDateTime from '@/components/BaseDateTime.vue'
import BaseDateTimeRange from '@/components/BaseDateTimeRange.vue'
import BaseProductCornerSelect from '@/components/BaseProductCornerSelect.vue'
import BaseProductDeliveryMethodsSelect from '@/components/BaseProductDeliveryMethodsSelect.vue'
import BaseProductDisplayMethodsSelect from '@/components/BaseProductDisplayMethodsSelect.vue'
import BaseProductDisplayStatusSelect from '@/components/BaseProductDisplayStatusSelect.vue'
import BaseProductExpectUpDownShelfTimeRadio from '@/components/BaseProductExpectUpDownShelfTimeRadio.vue'
import BaseProductTypeSelect from '@/components/BaseProductTypeSelect.vue'
import WidgetsCarouselConfig from '@/components/WidgetsConfig/WidgetCarouselConfig.vue'
import WidgetsCarouselPreview from '@/components/WidgetsPreview/WidgetCarouselPreview.vue'
import jumpModal from '@/components/jumpModal.vue' // 添加小程序装弹窗组件
import addMini from '@/components/mini/addMini.vue' // 添加小程序装饰页面组件
import selectPage from '@/components/selectPage.vue' // 选择页面弹窗
import addLink from '@/components/addLink.vue' // 热区组件
import textSetting from '@/components/textSetting.vue' // 文本设置
import textModal from '@/components/textModal.vue' // 文本设置弹窗
import floatSetting from '@/components/floatSetting.vue' // 悬浮窗设置
import pageShare from '@/components/pageShare.vue' // 分享组件
import bgSet from '@/components/bgSet.vue' // 添加背景色组件
import Color from '@/components/color/index.vue' // 添加颜色组件
import miniExtendDialog from '@/components/miniExtendDialog.vue'//推广组件


// 注册全局组件
app.component("layout", layout)
  .component('searchForm', searchForm)
  .component('SvgIcon', SvgIcon)
  .component('uploadImg', uploadImg)
  .component('uploadMoreImg', uploadMoreImg)
  .component('uploadVideo', uploadVideo)
  .component('areaList', areaList)
  .component("videoPlay", videoPlay)
  .component("downloadFile", downloadFile)
  .component("uploadFile", uploadFile)
  .component("VueDraggable", VueDraggable)
  .component("BaseAndOrSelect", BaseAndOrSelect)
  .component("BaseBelongingShelfSelect", BaseBelongingShelfSelect)
  .component("BaseDateTime", BaseDateTime)
  .component("BaseDateTimeRange", BaseDateTimeRange)
  .component("BaseProductCornerSelect", BaseProductCornerSelect)
  .component("BaseProductDeliveryMethodsSelect", BaseProductDeliveryMethodsSelect)
  .component("BaseProductDisplayMethodsSelect", BaseProductDisplayMethodsSelect)
  .component("BaseProductDisplayStatusSelect", BaseProductDisplayStatusSelect)
  .component("BaseProductExpectUpDownShelfTimeRadio", BaseProductExpectUpDownShelfTimeRadio)
  .component("BaseProductTypeSelect", BaseProductTypeSelect)
  .component('WidgetsCarouselConfig', WidgetsCarouselConfig)
  .component('WidgetsCarouselPreview', WidgetsCarouselPreview)
  .component("jumpModal", jumpModal)
  .component("addMini", addMini)
  .component("addLink", addLink)
  .component("selectPage", selectPage)
  .component("floatSetting", floatSetting)
  .component("textSetting", textSetting)
  .component("textModal", textModal)
  .component("pageShare", pageShare)
  .component("bgSet", bgSet)
  .component("Color", Color)
  .component('miniExtendDialog', miniExtendDialog)


app
  .use(Antd)
  .use(router)
  .mount('#app');



// 添加全局的方法和属性
Object.keys(global).map(key => {
  app.config.globalProperties[key] = global[key]
})
