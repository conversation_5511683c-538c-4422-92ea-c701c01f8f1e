package com.dz.common.core.dto.wechat.mpmsg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 小程序订阅消息模板库信息
 * @author: Handy
 * @date:   2023/07/07 14:22
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "小程序订阅消息模板库信息")
public class SubscribeMsgTemplateDTO {

    @ApiModelProperty(value = "模版标题ID")
    private Long tid;
    @ApiModelProperty(value = "模版标题")
    private String title;
    @ApiModelProperty(value = "模版类型 1一次性订阅 2长期订阅")
    private Integer type;
    @ApiModelProperty(value = "模版所属类目ID")
    private Integer categoryId;

}
