package com.dz.ms.user.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.core.dto.adaptor.ThirdPartyRecordVo;
import com.dz.common.core.dto.user.SysUserDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.adaptor.ThirdPartyReocrdFeginClient;
import com.dz.common.core.utils.CommonUtils;
import com.dz.ms.user.constants.DKeyAmAPIEnum;
import com.dz.ms.user.dto.AccountLoginDTO;
import com.dz.ms.user.dto.dkeyam.req.BindingListParam;
import com.dz.ms.user.dto.dkeyam.req.IdentityAddParam;
import com.dz.ms.user.dto.dkeyam.req.IdentityReplaceParam;
import com.dz.ms.user.dto.dkeyam.req.MobileTokenBindTokenQrCodeParam;
import com.dz.ms.user.dto.dkeyam.req.MobileTokenCompleteTokenQrCodeParam;
import com.dz.ms.user.dto.dkeyam.req.MobileTokenDataParam;
import com.dz.ms.user.dto.dkeyam.req.MobileTokenDeliverParam;
import com.dz.ms.user.dto.dkeyam.req.MobileTokenUserQrCodeParam;
import com.dz.ms.user.dto.dkeyam.req.PortalIncWeiXinSyncUserParam;
import com.dz.ms.user.dto.dkeyam.req.PortalQrCodeGrantParam;
import com.dz.ms.user.dto.dkeyam.req.TenantListParam;
import com.dz.ms.user.dto.dkeyam.req.TokenBindParam;
import com.dz.ms.user.dto.dkeyam.req.TokenUnbindAllParam;
import com.dz.ms.user.dto.dkeyam.req.TokenUnbindParam;
import com.dz.ms.user.dto.dkeyam.req.disconnectParam;
import com.dz.ms.user.dto.dkeyam.res.CallbackAuthenticateRes;
import com.dz.ms.user.dto.dkeyam.res.CallbackGetAllUserRes;
import com.dz.ms.user.dto.dkeyam.res.CallbackGetOneUserRes;
import com.dz.ms.user.dto.dkeyam.res.DKeyAmPageInfo;
import com.dz.ms.user.dto.dkeyam.res.DKeyAmResult;
import com.dz.ms.user.dto.dkeyam.res.MobileTokenDataRes;
import com.dz.ms.user.dto.dkeyam.res.StrongPasswordRequirementRes;
import com.dz.ms.user.dto.dkeyam.res.TenantListRes;
import com.dz.ms.user.dto.dkeyam.res.TokenBindingRes;
import com.dz.ms.user.dto.dkeyam.res.TokenInfoRes;
import com.dz.ms.user.dto.dkeyam.res.UserInfoRes;
import com.dz.ms.user.dto.dkeyam.req.CallbackAuthenticateParam;
import com.dz.ms.user.dto.dkeyam.req.CallbackGetAllUserParam;
import com.dz.ms.user.dto.dkeyam.req.CallbackGetOneUserParam;
import com.dz.ms.user.dto.dkeyam.req.StrongAuthenticateParam;
import com.dz.ms.user.dto.dkeyam.req.StrongGetDynamicPasswordParam;
import com.dz.ms.user.dto.dkeyam.req.StrongPasswordRequirementParam;
import com.dz.ms.user.dto.dkeyam.req.UserDeleteParam;
import com.dz.ms.user.dto.dkeyam.req.UserIncSyncParam;
import com.dz.ms.user.dto.dkeyam.req.UserInfoParam;
import com.dz.ms.user.dto.dkeyam.req.UserInfosParam;
import com.dz.ms.user.dto.dkeyam.req.UserUnbindUserTerminalsParam;
import com.dz.ms.user.service.DKeyAmOpenApiService;
import com.dz.ms.user.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpUtils;
import java.util.*;

/**
 * 宁盾Api接口
 *
 * @author: fei
 * @date: 2025/01/22 15:15
 */
@Service
@Slf4j
public class DKeyAmOpenApiServiceImpl implements DKeyAmOpenApiService {

    @Value("${dkeyam.api.domain:}")
    private String domain;
    @Value("${dkeyam.api.tenantName:}")
    private String tenantName;
    @Value("${dkeyam.api.accessServerName:}")
    private String accessServerName;
    @Value("${dkeyam.api.sharedSecret:}")
    private String sharedSecret;
    @Resource
    @Qualifier("HTTPS_REST_TEMPLATE")
    private RestTemplate restTemplate;
    @Resource
    private ThirdPartyReocrdFeginClient thirdPartyReocrdFeginClient;
    @Resource
    private SysUserService sysUserService;

    /**
     * 认证
     * @param param 认证入参
     * @return DKeyAmResult<String>
     */
    @Override
    public DKeyAmResult<String> strongAuthenticateParam(StrongAuthenticateParam param) {
        param.setTenantName(tenantName);
        param.setAccessServerName(accessServerName);
        param.setSharedSecret(sharedSecret);
        DKeyAmResult<String> result;
        try {
            result = request(DKeyAmAPIEnum.STRONG_AUTHENTICATE, JSONUtil.toJsonStr(param));
        } catch (Exception e) {
            log.error("认证失败",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST, "认证失败");
        }
        return result;
    }

    /**
     * 用户认证策略
     * @param param 用户认证策略入参
     * @return DKeyAmResult<StrongPasswordRequirementRes>
     */
    @Override
    public DKeyAmResult<StrongPasswordRequirementRes> strongPasswordRequirementParam(StrongPasswordRequirementParam param) {
        param.setTenantName(tenantName);
        param.setAccessServerName(accessServerName);
        param.setSharedSecret(sharedSecret);
        DKeyAmResult<StrongPasswordRequirementRes> result;
        try {
            DKeyAmResult<String> res = request(DKeyAmAPIEnum.STRONG_PASSWORD_REQUIREMENT, JSONObject.parseObject(CommonUtils.jsonStr(param)));
            result = new DKeyAmResult<>(res, StrongPasswordRequirementRes.class);
        } catch (Exception e) {
            log.error("用户认证策略失败",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST, "用户认证策略失败");
        }
        return result;
    }

    /**
     * 发送动态密码
     * @param param 发送动态密码入参
     * @return DKeyAmResult<String>
     */
    @Override
    public DKeyAmResult<String> strongGetDynamicPassword(StrongGetDynamicPasswordParam param) {
        param.setTenantName(tenantName);
        param.setAccessServerName(accessServerName);
        param.setSharedSecret(sharedSecret);
        DKeyAmResult<String> result;
        try {
            result = request(DKeyAmAPIEnum.STRONG_GET_DYNAMIC_PASSWORD, JSONObject.parseObject(CommonUtils.jsonStr(param)));
        } catch (Exception e) {
            log.error("发送动态密码失败",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST, "发送动态密码失败");
        }
        return result;
    }

    /**
     * 外部用户认证
     * @param param 入参
     * @return CallbackAuthenticateReq
     */
    @Override
    public CallbackAuthenticateRes callbackAuthenticate(CallbackAuthenticateParam param) {
        CallbackAuthenticateRes req = null;
        AccountLoginDTO dto = new AccountLoginDTO();
        dto.setUsername(param.getLoginName());
        dto.setPassword(param.getPassword());
        SysUserDTO sysUserDTO = sysUserService.dKeyAmCallbackAuthenticate(dto);
        if(Objects.nonNull(sysUserDTO)){
            req = new CallbackAuthenticateRes();
            req.setPersonalName(sysUserDTO.getUsername());
            //req.setMobile(sysUserDTO.getMobile());
        }
        return req;
    }

    /**
     * 获取单个用户的信息
     * @param param 入参
     * @return CallbackGetOneUserReq
     */
    @Override
    public CallbackGetOneUserRes callbackGetOneUser(CallbackGetOneUserParam param) {
        CallbackGetOneUserRes req = null;
        AccountLoginDTO dto = new AccountLoginDTO();
        dto.setUsername(param.getLoginName());
        SysUserDTO sysUserDTO = sysUserService.dKeyAmCallbackGetOneUser(dto);
        if(Objects.nonNull(sysUserDTO)){
            req = new CallbackGetOneUserRes();
            req.setPersonalName(sysUserDTO.getUsername());
            //req.setMobile(sysUserDTO.getMobile());
        }
        return req;
    }

    /**
     * 同步所有用户的信息
     * @param param 入参
     * @return DKeyAmPageInfo<CallbackGetAllUserReq>
     */
    @Override
    public DKeyAmPageInfo<CallbackGetAllUserRes> callbackGetAllUserReq(CallbackGetAllUserParam param) {
        List<CallbackGetAllUserRes> resList = new ArrayList<>();
        IPage<SysUserDTO> page = sysUserService.dKeyAmCallbackGetAllUserReq(param);
        List<SysUserDTO> records = page.getRecords();
        if(!CollectionUtils.isEmpty(records)){
            for (SysUserDTO dto : records) {
                resList.add(CallbackGetAllUserRes.builder().loginName(dto.getUsername()).build());
            }
        }
        return new DKeyAmPageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), resList);
    }

    /**
     * 增量同步用户
     * @param param 入参
     * @return JSONObject
     */
    @Override
    public DKeyAmResult<String> userIncSync(UserIncSyncParam param) {
        DKeyAmResult<String> result;
        try {
            result = request(DKeyAmAPIEnum.USER_INC_SYNC, JSONObject.parseObject(CommonUtils.jsonStr(param)));
        } catch (Exception e) {
            log.error("增量同步用户失败",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST, "增量同步用户失败");
        }
        return result;
    }

    /**
     * 删除用户
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    @Override
    public DKeyAmResult<String> userIncSync(UserDeleteParam param) {
        DKeyAmResult<String> result;
        try {
            result = request(DKeyAmAPIEnum.USER_DELETE, JSONObject.parseObject(CommonUtils.jsonStr(param)));
        } catch (Exception e) {
            log.error("删除用户失败",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST, "删除用户失败");
        }
        return result;
    }

    /**
     * 获取单个用户的信息
     * @param param 入参
     * @return DKeyAmResult<UserInfoRes>
     */
    @Override
    public DKeyAmResult<UserInfoRes> userInfo(UserInfoParam param) {
        DKeyAmResult<UserInfoRes> result;
        try {
            DKeyAmResult<String> res = request(DKeyAmAPIEnum.USER_INFO, JSONObject.parseObject(CommonUtils.jsonStr(param)));
            result = new DKeyAmResult<>(res, UserInfoRes.class);
        } catch (Exception e) {
            log.error("获取单个用户的信息失败",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST, "获取单个用户的信息失败");
        }
        return result;
    }

    /**
     * 获取所有用户的信息
     * @param param 入参
     * @return DKeyAmResult<DKeyAmPageInfo<UserInfoRes>>
     */
    @Override
    public DKeyAmResult<DKeyAmPageInfo<UserInfoRes>> userInfos(UserInfosParam param) {
        DKeyAmResult<DKeyAmPageInfo<UserInfoRes>> result;
        try {
            DKeyAmResult<String> res = request(DKeyAmAPIEnum.USER_INFOS, JSONObject.parseObject(CommonUtils.jsonStr(param)));
            result = new DKeyAmResult<>(res);
            result.setData(new DKeyAmPageInfo<>(res.getData(), UserInfoRes.class));
        } catch (Exception e) {
            log.error("获取所有用户的信息失败",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST, "获取所有用户的信息失败");
        }
        return result;
    }

    /**
     * 解绑用户终端
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    @Override
    public DKeyAmResult<String> userUnbindUserTerminals(UserUnbindUserTerminalsParam param) {
        DKeyAmResult<String> result;
        try {
            result = request(DKeyAmAPIEnum.USER_UNBIND_USER_TERMINALS, JSONObject.parseObject(CommonUtils.jsonStr(param)));
        } catch (Exception e) {
            log.error("解绑用户终端失败",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST, "解绑用户终端失败");
        }
        return result;
    }

    /**
     * 用户和令牌的绑定关系
     * @param param 入参
     * @return DKeyAmResult<List<TokenBindingRes>>
     */
    @Override
    public DKeyAmResult<List<TokenBindingRes>> bindingList(BindingListParam param) {
        DKeyAmResult<List<TokenBindingRes>> result;
        try {
            DKeyAmResult<String> res = request(DKeyAmAPIEnum.BINDING_LIST, JSONObject.parseObject(CommonUtils.jsonStr(param)));
            result = new DKeyAmResult<>(res);
            result.setData(JSONObject.parseArray(res.getData(),TokenBindingRes.class));
        } catch (Exception e) {
            log.error("用户和令牌的绑定关系失败",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST, "用户和令牌的绑定关系失败");
        }
        return result;
    }

    /**
     * 派发手机令牌
     * @param param 入参
     * @return DKeyAmResult<TokenInfoRes>
     */
    @Override
    public DKeyAmResult<TokenInfoRes> mobileTokenDeliver(MobileTokenDeliverParam param) {
        DKeyAmResult<TokenInfoRes> result;
        try {
            DKeyAmResult<String> res = request(DKeyAmAPIEnum.MOBILE_TOKEN_DELIVER, JSONObject.parseObject(CommonUtils.jsonStr(param)));
            result = new DKeyAmResult<>(res,TokenInfoRes.class);
        } catch (Exception e) {
            log.error("派发手机令牌息失败",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST, "派发手机令牌失败");
        }
        return result;
    }

    /**
     * 获取手机令牌二维码
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    @Override
    public DKeyAmResult<String> mobileTokenUserQrCode(MobileTokenUserQrCodeParam param) {
        DKeyAmResult<String> result;
        try {
            result = request(DKeyAmAPIEnum.MOBILE_TOKEN_USER_QR_CODE, JSONObject.parseObject(CommonUtils.jsonStr(param)));
        } catch (Exception e) {
            log.error("获取手机令牌二维码失败",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST, "获取手机令牌二维码息失败");
        }
        return result;
    }

    /**
     * 绑定并获取手机令牌二维码
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    @Override
    public DKeyAmResult<String> bindAndWriteMobileTokenQrCode(MobileTokenBindTokenQrCodeParam param) {
        DKeyAmResult<String> result;
        try {
            result = request(DKeyAmAPIEnum.MOBILE_TOKEN_BIND_AND_WRITE_MOBILE_TOKEN_QR_CODE, JSONObject.parseObject(CommonUtils.jsonStr(param)));
        } catch (Exception e) {
            log.error("绑定并获取手机令牌二维码失败",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST, "绑定并获取手机令牌二维码失败");
        }
        return result;
    }

    /**
     * 完成手机令牌绑定
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    @Override
    public DKeyAmResult<String> completeMobileTokenBinding(MobileTokenCompleteTokenQrCodeParam param) {
        DKeyAmResult<String> result;
        try {
            result = request(DKeyAmAPIEnum.MOBILE_TOKEN_COMPLETE_MOBILE_TOKEN_BINDING, JSONObject.parseObject(CommonUtils.jsonStr(param)));
        } catch (Exception e) {
            log.error("完成手机令牌绑定失败",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST, "完成手机令牌绑定失败");
        }
        return result;
    }

    /**
     * 绑定时间型令牌
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    @Override
    public DKeyAmResult<String> tokenBind(TokenBindParam param) {
        DKeyAmResult<String> result;
        try {
            result = request(DKeyAmAPIEnum.TOKEN_BIND, JSONObject.parseObject(CommonUtils.jsonStr(param)));
        } catch (Exception e) {
            log.error("绑定时间型令牌失败",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST, "绑定时间型令牌失败");
        }
        return result;
    }

    /**
     * 解绑时间型令牌
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    @Override
    public DKeyAmResult<String> tokenUnbind(TokenUnbindParam param) {
        DKeyAmResult<String> result;
        try {
            result = request(DKeyAmAPIEnum.TOKEN_UNBIND, JSONObject.parseObject(CommonUtils.jsonStr(param)));
        } catch (Exception e) {
            log.error("解绑时间型令牌失败",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST, "解绑时间型令牌失败");
        }
        return result;
    }

    /**
     * 解绑所有时间型令牌
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    @Override
    public DKeyAmResult<String> tokenUnbindAll(TokenUnbindAllParam param) {
        DKeyAmResult<String> result;
        try {
            result = request(DKeyAmAPIEnum.TOKEN_UNBIND_ALL, JSONObject.parseObject(CommonUtils.jsonStr(param)));
        } catch (Exception e) {
            log.error("解绑所有时间型令牌失败",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST, "解绑所有时间型令牌失败");
        }
        return result;
    }

    /**
     * 获取手机令牌信息
     * @param param 入参
     * @return DKeyAmResult<MobileTokenDataRes>
     */
    @Override
    public DKeyAmResult<MobileTokenDataRes> mobileTokenData(MobileTokenDataParam param) {
        DKeyAmResult<MobileTokenDataRes> result;
        try {
            DKeyAmResult<String> res = request(DKeyAmAPIEnum.MOBILE_TOKEN_DATA, JSONObject.parseObject(CommonUtils.jsonStr(param)));
            result = new DKeyAmResult<>(res,MobileTokenDataRes.class);
        } catch (Exception e) {
            log.error("获取手机令牌信息失败",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST, "获取手机令牌信息失败");
        }
        return result;
    }
    
    /**
     * 断开在线用户
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    @Override
    public DKeyAmResult<String> disconnect(disconnectParam param) {
        DKeyAmResult<String> result;
        try {
            result = request(DKeyAmAPIEnum.DISCONNECT, JSONObject.parseObject(CommonUtils.jsonStr(param)));
        } catch (Exception e) {
            log.error("断开在线用户失败",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST, "断开在线用户失败");
        }
        return result;
    }

    /**
     * 扫码授权
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    @Override
    public DKeyAmResult<String> portalQrCodeGrant(PortalQrCodeGrantParam param) {
        DKeyAmResult<String> result;
        try {
            result = request(DKeyAmAPIEnum.PORTAL_QR_CODE_GRANT, JSONObject.parseObject(CommonUtils.jsonStr(param)));
        } catch (Exception e) {
            log.error("扫码授权失败",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST, "扫码授权失败");
        }
        return result;
    }
    
    /**
     * 增量更新用户信息（微信）
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    @Override
    public DKeyAmResult<String> portalIncWeiXinSyncUser(PortalIncWeiXinSyncUserParam param) {
        DKeyAmResult<String> result;
        try {
            result = request(DKeyAmAPIEnum.PORTAL_WEI_XIN_INC_SYNC_USER, JSONObject.parseObject(CommonUtils.jsonStr(param)));
        } catch (Exception e) {
            log.error("增量更新用户信息（微信）失败",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST, "增量更新用户信息（微信）失败");
        }
        return result;
    }

    /**
     * 站点列表
     * @param param 入参
     * @return DKeyAmResult<List<TenantListRes>>
     */
    @Override
    public DKeyAmResult<List<TenantListRes>> tenantList(TenantListParam param) {
        DKeyAmResult<List<TenantListRes>> result;
        try {
            DKeyAmResult<String> res = request(DKeyAmAPIEnum.TENANT_LIST, JSONObject.parseObject(CommonUtils.jsonStr(param)));
            result = new DKeyAmResult<>(res);
            result.setData(JSONObject.parseArray(res.getData(),TenantListRes.class));
        } catch (Exception e) {
            log.error("站点列表失败",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST, "站点列表失败");
        }
        return result;
    }

    /**
     * 新增Mac地址黑白名单
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    @Override
    public DKeyAmResult<String> identityAdd(IdentityAddParam param) {
        DKeyAmResult<String> result;
        try {
            result = request(DKeyAmAPIEnum.BLACK_WHITE_MAC_ADDRESS_LIST_ADD, JSONObject.parseObject(CommonUtils.jsonStr(param)));
        } catch (Exception e) {
            log.error("新增Mac地址黑白名单失败",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增Mac地址黑白名单失败");
        }
        return result;
    }

    /**
     * Mac地址黑白名单
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    @Override
    public DKeyAmResult<String> identityReplace(IdentityReplaceParam param) {
        DKeyAmResult<String> result;
        try {
            result = request(DKeyAmAPIEnum.BLACK_WHITE_MAC_ADDRESS_LIST_LIST_REPLACE, JSONObject.parseObject(CommonUtils.jsonStr(param)));
        } catch (Exception e) {
            log.error("Mac地址黑白名单失败",e);
            throw new BusinessException(ErrorCode.BAD_REQUEST, "Mac地址黑白名单失败");
        }
        return result;
    }
    
    /**
     * CRM接口统一请求方法
     *
     * @param postBody
     */
    public DKeyAmResult<String> request(DKeyAmAPIEnum apiEnum, JSONObject postBody) {
        boolean isPost = HttpMethod.POST.equals(apiEnum.getMethod());
        return request(apiEnum.getUri(), JSONObject.toJSONString(postBody),apiEnum.getDesc(),isPost);
    }

    public DKeyAmResult<String> request(DKeyAmAPIEnum apiEnum, String postBody) {
        boolean isPost = HttpMethod.POST.equals(apiEnum.getMethod());
        return request(apiEnum.getUri(), postBody,apiEnum.getDesc(),isPost);
    }

    /***
     * 计算签名
     * @param address HTTP请求地址：如: /user/query
     * @param body HTTP请求数据
     * @return JSON
     */
    public DKeyAmResult<String> request(String address, String body, String desc, boolean isPost) {
        String requestUrl = domain + address;
        log.info("DKeyAm请求 {} postBody:{} url:{}", desc, body, requestUrl);
        cn.hutool.json.JSONObject json = null;
        Date startTime;
        Date endTime;
        try {
            startTime = new Date();
            if (isPost) {
                Map<String, Object> formMap = JSONUtil.parseObj(body);
                String postResStr = HttpUtil.createPost(requestUrl)
                        .form(formMap)
                        .execute()
                        .body();
                log.info("DKeyAm请求 {}回参 postBody:{} url:{} postResStr:{}", desc, body, requestUrl, postResStr);
                json = JSONUtil.parseObj(postResStr);
            } else {
                json = JSONUtil.parseObj(HttpUtil.get(requestUrl));
            }
            endTime = new Date();
            //计算请求耗时
            long time = endTime.getTime() - startTime.getTime();
            ThirdPartyRecordVo recordVo = new ThirdPartyRecordVo();
            recordVo.setApiDesc(desc);
            recordVo.setApiUrl(requestUrl);
            recordVo.setParam(body);
            recordVo.setResult(CommonUtils.jsonStr(json));
            recordVo.setCreateTime(new Date());
            recordVo.setStatus(1);
            recordVo.setRequestTime(time);
            thirdPartyReocrdFeginClient.thirdDKeyAmRecord(recordVo);
        } catch (Exception e) {
            String message = e.getMessage();
            log.error("DKeyAm请求 {} 异常", desc, e);
            ThirdPartyRecordVo recordVo = new ThirdPartyRecordVo();
            recordVo.setApiDesc(desc);
            recordVo.setApiUrl(requestUrl);
            recordVo.setParam(body);
            recordVo.setFailDesc("请求异常,异常原因:" + message);
            recordVo.setCreateTime(new Date());
            recordVo.setStatus(0);
            thirdPartyReocrdFeginClient.thirdDKeyAmRecord(recordVo);
            throw new BusinessException(ErrorCode.INTERNAL_ERROR);
        }
        log.info("DKeyAm请求 {} result:{}", desc, null == json ? "[null相]" : json.toString());
        DKeyAmResult<String> result = new DKeyAmResult<>();
        if (Objects.nonNull(json)) {
            result.setData(json.getStr("data"));
            result.setErrorCode(StringUtils.isBlank(json.getStr("errorCode")) ? -666 : Integer.parseInt(json.getStr("errorCode")));
            result.setErrorId(StringUtils.isBlank(json.getStr("errorId")) ? null : Long.parseLong(json.getStr("errorId")));
            result.setSuccess(StringUtils.isNoneBlank(json.getStr("success")) && Boolean.parseBoolean(json.getStr("success")));
            result.setMessage(json.getStr("message"));
            result.setRequestId(StringUtils.isBlank(json.getStr("requestId")) ? null : Long.parseLong(json.getStr("requestId")));
        } else {
            throw new BusinessException(ErrorCode.INTERNAL_ERROR);
        }
        return result;
    }

//    public DKeyAmResult<String> request(String address, String body,String desc, boolean isPost) {
//        HttpHeaders headers = new HttpHeaders();
//        headers.add("Content-Type", "application/x-www-form-urlencoded");
//        String requestUrl = domain + address;
//        log.info("DKeyAm请求 {} postBody:{} url:{}", desc, body, address);
//        JSONObject json = null;
//        Date startTime;
//        Date endTime;
//        try {
//            if (isPost) {
//                HttpEntity<String> requestEntity = new HttpEntity<String>(body, headers);
//                startTime= new Date();
//
//                json = restTemplate.postForObject(requestUrl, requestEntity, JSONObject.class);
//                endTime= new Date();
//            } else {
//                headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
//                HttpEntity<String> requestEntity = new HttpEntity<String>(headers);
//                startTime= new Date();
//                json = restTemplate.exchange(requestUrl, HttpMethod.GET, requestEntity, JSONObject.class).getBody();
//                endTime= new Date();
//            }
//            //计算请求耗时
//            long time = endTime.getTime() - startTime.getTime();
//            ThirdPartyRecordVo recordVo = new ThirdPartyRecordVo();
//            recordVo.setApiDesc(desc);
//            recordVo.setApiUrl(requestUrl);
//            recordVo.setParam(body);
//            recordVo.setResult(CommonUtils.jsonStr(json));
//            recordVo.setCreateTime(new Date());
//            recordVo.setStatus(1);
//            recordVo.setRequestTime(time);
//            thirdPartyReocrdFeginClient.thirdDKeyAmRecord(recordVo);
//        } catch (RestClientException e) {
//            String message = e.getMessage();
//            log.error("DKeyAm请求 {} 异常", desc, e);
//            ThirdPartyRecordVo recordVo = new ThirdPartyRecordVo();
//            recordVo.setApiDesc(desc);
//            recordVo.setApiUrl(requestUrl);
//            recordVo.setParam(body);
//            recordVo.setFailDesc("请求异常,异常原因:"+message);
//            recordVo.setCreateTime(new Date());
//            recordVo.setStatus(0);
//            thirdPartyReocrdFeginClient.thirdDKeyAmRecord(recordVo);
//            throw new BusinessException(ErrorCode.INTERNAL_ERROR);
//        }
//        log.info("DKeyAm请求 {} result:{}", desc, null == json ? "[null相]" : json.toJSONString());
//        DKeyAmResult<String> result = new DKeyAmResult<>();
//        if(Objects.nonNull(json)){
//            result.setData(json.getString("data"));
//            result.setErrorCode(StringUtils.isBlank(json.getString("errorCode")) ? -666 : Integer.parseInt(json.getString("errorCode")));
//            result.setErrorId(StringUtils.isBlank(json.getString("errorId")) ? null : Long.parseLong(json.getString("errorId")));
//            result.setSuccess(StringUtils.isNoneBlank(json.getString("success")) && Boolean.parseBoolean(json.getString("success")));
//            result.setMessage(json.getString("message"));
//            result.setRequestId(StringUtils.isBlank(json.getString("requestId")) ? null : Long.parseLong(json.getString("requestId")));
//        } else {
//            throw new BusinessException(ErrorCode.INTERNAL_ERROR);
//        }
//        return result;
//    }

    public static void main(String[] args) {

    }
}
