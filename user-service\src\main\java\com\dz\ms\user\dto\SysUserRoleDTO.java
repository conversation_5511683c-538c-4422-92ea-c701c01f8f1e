package com.dz.ms.user.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 系统用户角色DTO
 * @author: Handy
 * @date:   2022/7/15 19:04
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "系统用户角色")
public class SysUserRoleDTO extends BaseDTO {

    @ApiModelProperty(value = "系统用户ID")
    private Long uid;
    @ApiModelProperty(value = "角色ID")
    private Long roleId;
    @ApiModelProperty(value = "角色名")
    private String roleName;

}
