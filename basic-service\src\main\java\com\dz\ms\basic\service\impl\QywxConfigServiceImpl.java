package com.dz.ms.basic.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.core.annotation.Cacheable;
import com.dz.common.core.dto.basic.QywxConfigDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.dto.wechat.DecryptEnterpriseUserDTO;
import com.dz.common.core.enums.WechatApiEnum;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.constants.CacheKeys;
import com.dz.ms.basic.entity.QywxConfig;
import com.dz.ms.basic.mapper.QywxConfigMapper;
import com.dz.ms.basic.service.QywxConfigService;
import com.dz.ms.basic.utils.WxDataDecryptUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * 企业微信配置
 * @author: Handy
 * @date:   2022/07/20 21:18
 */
@Slf4j
@Service
public class QywxConfigServiceImpl extends ServiceImpl<QywxConfigMapper,QywxConfig> implements QywxConfigService {

    @Resource
    private QywxConfigMapper qywxConfigMapper;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private RedisService redisService;
    @Value("${wx.config.cipher}")
    private String cipher;

    /**
     * 根据租户ID获取企业微信access_token
     * @param type
     * @param tenantId
     * @param cleanCach
     * @return
     */
    @Override
    public String getQywxAccessToken(Integer type, Long tenantId, Boolean cleanCach) {
        String key = CacheKeys.QYWX_ACCESSTOKEN+tenantId+":"+type;
        String accesstoken = null;
        if(cleanCach) {
            redisService.del(key);
        }
        else {
            accesstoken = redisService.getString(key);
            if(null != accesstoken) {
                return accesstoken;
            }
        }
        CurrentUserDTO current = SecurityContext.getUser();
        if(null == current || ParamUtils.isNullOr0Long(current.getTenantId())) {
            current = new CurrentUserDTO(tenantId);
            SecurityContext.setUser(current);
        }
        LambdaQueryWrapper<QywxConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(QywxConfig :: getType,type);
        wrapper.eq(QywxConfig :: getTenantId,tenantId);
        QywxConfig qywxConfig = qywxConfigMapper.selectOne(wrapper);
        if(null == qywxConfig) {
            throw new BusinessException(ErrorCode.INTERNAL_ERROR,"未获取到企微配置");
        }
        JSONObject json = restTemplate.getForObject(WechatApiEnum.API_GET_WXCP_APPTOKEN_BY_SECRET.getUrl() + "?corpid="+qywxConfig.getCorpId()+"&corpsecret="+qywxConfig.getCorpSecret(), JSONObject.class);
        log.info("获取企业微信access_token result:"+ (null == json ? "emp" : json.toJSONString()));
        if(null != json && json.containsKey("access_token")) {
            accesstoken = json.getString("access_token");
            int expires = json.getIntValue("expires_in");
            redisService.setString(key,accesstoken,expires-600);
        }
        else {
            log.info("获取企业微信access_token失败corpid:{}corpsecret:{}",qywxConfig.getCorpId(),qywxConfig.getCorpSecret());
            throw new BusinessException(ErrorCode.INTERNAL_ERROR,"获取access_token失败");
        }
        return accesstoken;
    }

    /**
     * 根据租户ID获取企业微信配置
     * @param tenantId
     * @return
     */
    @Override
    @Cacheable(prefix = CacheKeys.QYWX_CONFIG_BYTENANT,key = "'#tenantId'")
    public QywxConfigDTO getQywxConfigByTenantId(Long tenantId) {
        LambdaQueryWrapper<QywxConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(QywxConfig :: getTenantId,tenantId);
        wrapper.eq(QywxConfig :: getType,1);
        QywxConfig qywxConfig = qywxConfigMapper.selectOne(wrapper);
        return BeanCopierUtils.convertObject(qywxConfig,QywxConfigDTO.class);
    }

    @Override
    public DecryptEnterpriseUserDTO decryptEnterpriseUserDTO(String encryptedData, String iv, String sessionKey) {

        if (null == sessionKey) {
            throw new BusinessException(ErrorCode.UNAUTHORIZED, "解密数据失败,请重新登录小程序");
        }
        try {
            byte[] resultByte = WxDataDecryptUtil.decrypt(Base64.decodeBase64(encryptedData), Base64.decodeBase64(sessionKey), Base64.decodeBase64(iv),cipher);
            log.info("解密企业微信用户信息完成");
            if (null != resultByte && resultByte.length > 0) {
                String resultJson = new String(resultByte, StandardCharsets.UTF_8);
                log.info("解密微信用户信息结果:{}", resultJson);
                return JSON.parseObject(resultJson, DecryptEnterpriseUserDTO.class);
            }
        } catch (Exception e) {
            log.error("解密微信用户信息失败", e);
        }
        log.info("解密微信用户信息结果为空");
        throw new BusinessException(ErrorCode.BAD_REQUEST, "解密数据失败");
    }
}