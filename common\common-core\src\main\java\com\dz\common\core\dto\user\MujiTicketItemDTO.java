package com.dz.common.core.dto.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MujiTicketItemDTO {

    @ApiModelProperty(value = "打折率")
    private String discountrate;
    @ApiModelProperty(value = "商品ID")
    private String itemId;
    @ApiModelProperty(value = "商品名称")
    private String itemName;
    @ApiModelProperty(value = "购入件数")
    private String qty;
    @ApiModelProperty(value = "实收金额")
    private String realPrice;
    @ApiModelProperty(value = "销售价格")
    private String salePrice;
}
