package com.dz.ms.order.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 核销记录
 * @author: Handy
 * @date:   2024/05/31 16:36
 */
@Getter
@Setter
@NoArgsConstructor
@Table("核销记录")
@TableName(value = "verify_record")
public class VerifyRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 11, isNull = false, comment = "ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = true, comment = "核销编号（预约编号/订单编号/卡券编号）")
    private String verifyCode;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "核销ID（预约/订单/卡券ID）")
    private Long verifyId;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, defaultValue = "0", comment = "核销类型(1兑礼订单 2预约 3卡券)")
    private Integer verifyType;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "客户ID")
    private Long uid;
    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = true, comment = "客户姓名")
    private String userName;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, defaultValue = "0", comment = "客户性别(0未知 1男 2女)")
    private Integer gender;
    @Columns(type = ColumnType.VARCHAR, length = 36, isNull = true, comment = "客户手机号")
    private String mobile;
    @Columns(type = ColumnType.VARCHAR, length = 20, isNull = true, comment = "客户等级")
    private String cardLevel;
    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = true, comment = "核销记录名称")
    private String recordName;
    @Columns(type = ColumnType.VARCHAR,length = 64,isNull = true,comment = "核销项目名称")
    private String itemName;
    @Columns(type = ColumnType.VARCHAR,length = 24,isNull = true,comment = "预约日期(格式yyyy-MM-dd)",isIndex = true)
    private String bookingDate;
    @Columns(type = ColumnType.VARCHAR,length = 32,isNull = true,comment = "预约时间段")
    private String timeSlot;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "可核销开始时间")
    private Date verifyStartTime;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "可核销结束时间")
    private Date verifyEndTime;
    @Columns(type = ColumnType.VARCHAR,length = 36,isNull = false,comment = "预约门店编码")
    private String storeCode;
    @Columns(type = ColumnType.VARCHAR,length = 36,isNull = false,comment = "预约门店名称")
    private String storeName;
    @Columns(type = ColumnType.VARCHAR,length = 36,isNull = true,comment = "预约/分配员工编号")
    private String empCode;
    @Columns(type = ColumnType.VARCHAR,length = 36,isNull = true,comment = "核销门店编码")
    private String verifyStoreCode;
    @Columns(type = ColumnType.VARCHAR,length = 36,isNull = true,comment = "核销门店名称")
    private String verifyStoreName;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, comment = "核销人类型 1后台用户 2导购")
    private Integer verifierType;
    @Columns(type = ColumnType.VARCHAR,length = 36,isNull = true,comment = "核销员工编号")
    private String verifierCode;
    @Columns(type = ColumnType.VARCHAR,length = 50,isNull = true,comment = "核销员工姓名")
    private String verifierName;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, comment = "取消人类型 1后台用户 2导购")
    private Integer cancelerType;
    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = true, comment = "取消人编码")
    private String cancelerCode;
    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = true, comment = "取消人名称")
    private String cancelerName;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, comment = "修改人类型 1后台用户 2导购")
    private Integer updaterType;
    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = true, comment = "修改人编码")
    private String updaterCode;
    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = true, comment = "修改人名称")
    private String updaterName;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = true, comment = "预约消耗类型 0无消耗 1积分 2卡券 3权益 4pin_code 5付费",defaultValue = "0")
    private Integer consumeType;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;

    public VerifyRecord(Long id, String verifyCode, Long verifyId, Integer verifyType, Long uid, String userName, Integer gender, String mobile, String cardLevel, String recordName, String itemName, String bookingDate, String timeSlot, Date verifyStartTime, Date verifyEndTime, String storeCode, String storeName, String empCode, String verifyStoreCode, String verifyStoreName, Integer verifierType, String verifierCode, String verifierName, Integer cancelerType, String cancelerCode, String cancelerName, Integer updaterType, String updaterCode, String updaterName, Integer consumeType) {
        this.id = id;
        this.verifyCode = verifyCode;
        this.verifyId = verifyId;
        this.verifyType = verifyType;
        this.uid = uid;
        this.userName = userName;
        this.gender = gender;
        this.mobile = mobile;
        this.cardLevel = cardLevel;
        this.recordName = recordName;
        this.itemName = itemName;
        this.bookingDate = bookingDate;
        this.timeSlot = timeSlot;
        this.verifyStartTime = verifyStartTime;
        this.verifyEndTime = verifyEndTime;
        this.storeCode = storeCode;
        this.storeName = storeName;
        this.empCode = empCode;
        this.verifyStoreCode = verifyStoreCode;
        this.verifyStoreName = verifyStoreName;
        this.verifierType = verifierType;
        this.verifierCode = verifierCode;
        this.verifierName = verifierName;
        this.cancelerType = cancelerType;
        this.cancelerCode = cancelerCode;
        this.cancelerName = cancelerName;
        this.updaterType = updaterType;
        this.updaterCode = updaterCode;
        this.updaterName = updaterName;
        this.consumeType = consumeType;
    }

}
