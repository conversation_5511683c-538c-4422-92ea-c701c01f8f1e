<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.product.mapper.ShelfTagMapper">

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
        id,
  	    shelf_id,
  	    shelf_name,
  	    tag_id,
  	    tag_name,
  	    cate,
  	    parent_id,
  	    tenant_id,
  	    creator,
  	    created,
  	    modified,
  	    modifier
    </sql>

    <!-- 查询示例 -->
    <select id="selectDemo" parameterType="java.lang.Long" resultType="com.dz.ms.product.entity.ShelfTag">
        select
        <include refid="Base_Column_List"/>
        from shelf_tag
        where id = #{id,jdbcType=BIGINT}
        AND tenant_id = #{tenantId}
    </select>
    
    <select id="selectAllList" resultType="com.dz.ms.product.entity.ShelfTag">
        select
            st.id,st.shelf_id,st.tag_id,st.cate,st.parent_id,
            ti.name tagName
        from shelf_tag st
        left join tag_info ti on st.tag_id = ti.id
        <where>
            st.is_deleted = 0
            and ti.is_deleted = 0
            <if test="param.shelfId != null">
                AND st.shelf_id = #{param.shelfId}
            </if>
            <if test="param.parentId != null">
                AND st.parent_id = #{param.parentId}
            </if>
            <if test="param.cate != null">
                AND st.cate = #{param.cate}
            </if>
        </where>
        order by id asc
    </select>

</mapper>
