.toast-container {
  position: fixed;
  top: 800rpx; // Adjusted from 50% to 20% to change the vertical position
  left: 50%;
  transform: translate(-50%, 0); // Adjusted from translate(-50%, -50%) to translate(-50%, 0) to change the vertical position
  opacity: 0;
  z-index: 9999;
  transition: opacity 0.3s;
  width: 100%;
  height: 100%;
  display: none; // Changed from flex to none to hide the toast container when not visible
  align-items: flex-start; // Adjusted from center to flex-start to change the vertical alignment
  justify-content: center;
}

.toast {
  &-content {
    width: auto;
    padding: 30rpx 40rpx;
    text-align: center;
    border-radius: 5px;
    background: rgba(0, 0, 0, 0.6);
    box-sizing: border-box;
    /* 白色 */
    border: 1px solid #FFFFFF;
    backdrop-filter: blur(4px);

  }

  &-title {

    font-family: Source Han Sans CN;
    font-size: 28rpx;
    font-weight: 500;
    line-height: normal;
    text-align: center;
    letter-spacing: normal;
    color: #FFFFFF;
  }

  &-tips {
    font-family: Source <PERSON>;
    font-size: 20rpx;
    font-weight: bold;
    line-height: normal;
    text-align: center;
    letter-spacing: normal;
    color: #FFFFFF;
  }
}

.toast-container.show {
  opacity: 1;
  display: flex; // Changed from none to flex to show the toast container when visible
}

.toast-icon {
  width: 60rpx;
  height: 60rpx;
  display: block;
  margin: 0 auto 10rpx;
}