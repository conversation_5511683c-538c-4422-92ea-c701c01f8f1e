package com.dz.ms.product.dto.res;

import com.dz.ms.product.dto.TagInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商品列表信息DTO
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "商品列表信息")
public class ProductChiefInfoDTO {

    @ApiModelProperty(value = "商品ID")
    private Long id;
    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "商品类型 1实物商品 2电子券")
    private Integer pdType;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "首张商品场景图")
    private String sceneImg;
    @ApiModelProperty(value = "商品主图")
    private String shelfImg;
    @ApiModelProperty(value = "首张商品橱窗图")
    private String shopWindowImg;
    @ApiModelProperty(value = "商品标签列表")
    private List<TagInfoDTO> tagList;
    @ApiModelProperty(value = "兑换积分")
    private Integer costPoint;
    @ApiModelProperty(value = "兑换金额")
    private BigDecimal costPrice;
    @ApiModelProperty(value = "外部数据ID")
    private String venderId;
    @ApiModelProperty(value = "商品累计兑换量")
    private Integer exchangeNum;
    @ApiModelProperty(value = "状态 0禁用 1启用")
    private Integer state;
    @ApiModelProperty(value = "金额是否展示在货架列表")
    private Integer costPriceOnShelf;

}
