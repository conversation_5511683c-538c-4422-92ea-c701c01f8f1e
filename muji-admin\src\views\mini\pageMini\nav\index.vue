<template>
  <layout>
    <div style="height:100%;overflow-y:auto">
      <a-form class="form" ref="formRef" :model="addParams" labelAlign="left" :labelCol="{ style: 'width:120px' }">
        <div class="header-title">基本配置</div>
        <a-form-item label="导航背景色" name="bgColor" :rules="[{  required: true, message: '请选择', trigger: ['blur', 'change'] }]">
          <Color color="rgba(0,0,0,1)" :value="addParams.bgColor" @changeColor="(color)=>changeColor(color,'bgColor')"></Color>
        </a-form-item>
        <a-form-item label="展现方式" :rules="[{  required: true, message: '请选择', trigger: ['blur', 'change'] }]">
          <a-radio-group v-model:value="addParams.showType" @change="changeShowType">
            <a-radio :value="1">仅图</a-radio>
            <a-radio :value="2">图+菜单名</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="字体颜色" :rules="[{  required: true, message: '请选择', trigger: ['blur', 'change'] }]">
          <a-form-item label="选中" name="selectedColor" :colon="false">
            <Color :value="addParams.selectedColor" @changeColor="(color)=>changeColor(color,'selectedColor')"></Color>
          </a-form-item>
          <a-form-item label="未选中" name="color" :colon="false">
            <Color :value="addParams.color" @changeColor="(color)=>changeColor(color,'color')"></Color>
          </a-form-item>
        </a-form-item>
        <a-form-item label="字体大小" name="fontSize" :rules="[{  required: true, message: '请输入', trigger: ['blur', 'change'] }]">
          <a-input-number placeholder="请输入" :min="12" :precision="0" v-model:value="addParams.fontSize" addon-after="px"></a-input-number>
        </a-form-item>
        <div class="header-title">菜单设置</div>
        <div class="content">
          <div class="right">
            <a-card v-for="(item,index) in addParams.list" :key="item.id" :bordered="false" class="card">
              <div class="title">菜单{{ index + 1 }}</div>
              <a-form-item label="是否启用">
                <a-switch v-model:checked="item.show" />
              </a-form-item>
              <a-form-item label="点击是否需要注册">
                <a-switch v-model:checked="item.register" />
              </a-form-item>
              <a-form-item label="点击触发订阅消息">
                <a-select v-model:value="item.subscribeScene" mode="multiple" placeholder="请选择订阅消息" :getPopupContainer="triggerNode => triggerNode.parentNode" allowClear>
                  <!-- 订阅消息模板ID  接口获取 -->
                  <a-select-option v-for="t in subscribeMsgList" :value="t.templateId" :key="t.templateId" :disabled="item.subscribeScene?.length>=3&&!item.subscribeScene.includes(t.templateId)">{{t.templateName}} （{{t.keyword}}）</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="点击埋点">
                <a-input placeholder="请输入" style="width:300px;" v-model:value="item.code" show-count :maxlength="50" />
              </a-form-item>
              <a-form-item label="菜单名称" :name="['list',index,'text']" :rules="[{  required: true, message: '请输入', trigger: ['blur', 'change'] }]">
                <a-input placeholder="请输入" style="width:300px;" v-model:value="item.text" show-count :maxlength="4" />
              </a-form-item>
              <!-- <a-form-item label="展示位置" :name="['list',index,'sort']" :rules="[{  required: true, message: '请输入', trigger: ['blur', 'change'] }]">
                    <a-input-number :precision="0" placeholder="请输入" v-model:value="item.sort" :min="1" disabled />
                  </a-form-item> -->
              <a-form-item label="展现方式" :name="['list',index,'showType']" :rules="[{  required: true, message: '请选择', trigger: ['blur', 'change'] }]">
                <a-radio-group v-model:value="item.showType" name="radioGroup" style="width:100%;">
                  <a-radio :value="1">仅图</a-radio>
                  <a-radio :value="2">图+菜单名</a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item label="菜单图标" required>
                <a-space>
                  <a-form-item label="选中" :colon="false" :name="['list',index,'selectedIconPath']" :rules="[{required:true,   message: '请上传', trigger: ['blur', 'change'] }]">
                    <uploadImg :max="20" :width="80" :height="80" :imgUrl="item.selectedIconPath" :form="addParams" :path="'list.'+index+'.selectedIconPath'" @success="uploadSuccess"></uploadImg>
                  </a-form-item>
                  <a-form-item label="未选中" :colon="false" :name="['list',index,'iconPath']" :rules="[{required:true,   message: '请上传', trigger: ['blur', 'change'] }]">
                    <uploadImg :max="20" :width="80" :height="80" :imgUrl="item.iconPath" :form="addParams" :path="'list.'+index+'.iconPath'" @success="uploadSuccess"></uploadImg>
                  </a-form-item>
                </a-space>
              </a-form-item>
              <a-form-item label="图标大小" :name="['list',index,'iconType']" :rules="[{  required: true, message: '请选择', trigger: ['blur', 'change'] }]">
                <a-radio-group v-model:value="item.iconType" name="radioGroup" style="width:100%;">
                  <a-radio :value="1">小</a-radio>
                  <a-radio :value="2">大</a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item label="链接方式" :name="['list',index,'type']" :rules="[{  required: true, message: '请选择', trigger: ['blur', 'change'] }]">
                <a-radio-group v-model:value="item.type" name="radioGroup" style="width:100%;">
                  <a-radio :value="1">展示原有页面 ({{item.pagePath}})</a-radio>
                  <a-radio :value="2">自定义</a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item label="跳转链接" v-if="item.type==2" style="margin-top:20px;" class="item-link" :name="['list',index,'linkName']" :rules="[{  required: true, message: '请添加', trigger: ['blur', 'change'] }]">
                <selectPage :link="item" :params="{index}" @ok="changeLink">
                </selectPage>
              </a-form-item>
            </a-card>
          </div>
          <div class="left">
            <div class="left-tabbar">
              <div class="tabbar" :style="{backgroundColor:addParams.bgColor}">
                <template v-for="(item,index) in addParams.list" :key="item.id">
                  <div class="tabbar-item" v-if="item.show" :style="{order:item.sort}" @click="tabbarIndex=index">
                    <img :src="tabbarIndex==index?item.selectedIconPath:item.iconPath" class="tabbar-item-img small" v-if="item.iconType==1">
                    <img :src="tabbarIndex==index?item.selectedIconPath:item.iconPath" class="tabbar-item-img big" v-else>
                    <div v-if="item.showType==2" class="tabbar-item-name" :style="{color:tabbarIndex==index?addParams.selectedColor:addParams.color,fontSize:addParams.fontSize/2+'px'}">{{item.text}}</div>
                  </div>
                </template>
              </div>
            </div>
          </div>
        </div>
      </a-form>
    </div>

    <template v-slot:footer>
      <a-flex justify="center" align="center">
        <a-button type="primary" :disabled="!$hasPermission('nav:save')" @click="saveData()">保存配置</a-button>
      </a-flex>
    </template>
  </layout>
</template>
<script setup>
import { reactive, toRefs, ref, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { useGlobalStore } from '@/store'
import { useRoute, useRouter } from 'vue-router'
import { navSave, getNav } from '@/http/index.js'
import _, { cloneDeep } from 'lodash';
import { v4 as uuidv4 } from 'uuid'


// 固定应用
const global = useGlobalStore()
const $router = useRouter()
const { query } = useRoute()
const formRef = ref()
const tableRef = ref()
const subscribeMsgList = ref([])
const tabbarPage = [// 一级页面地址
  '/pages/index/index',
  '/pages/exchange/exchange',
  '/pages/qrCode/qrCode',
  '/pages/life/life',
  '/pages/more/more',

]

// 获取订阅消息模板列表
global.getSubscribeMsgList().then(data => {
  subscribeMsgList.value = data
  console.log(data)
})
const list = Array(5).fill('').map((item, index) => ({
  id: uuidv4(),
  show: true, // 是否启用
  text: '', // 菜单名称
  sort: index + 1, // 位置
  showType: 1,// 1-图 2-图加文字
  pagePath: tabbarPage[index],
  iconType: 1, // 图标大小 1-小 2-大
  iconPath: '', // 未选中图标
  selectedIconPath: '', // 选中图标
  type: 1, // 跳转链接方式  1-原有  2-自定义
  linkType: '',
  linkUrl: '',
  linkName: '',
  appid: '',
  path: '',
  url: '',
  code: '',
  register: false,
  subscribeScene: undefined, // 订阅场景
}))
// 定义数据
const { addParams, tabbarIndex } = toRefs(reactive({
  tabbarIndex: 0,
  addParams: {
    bgColor: 'rgba(255,255,255,1)',
    showType: 1,// 1-图 2-图加文字
    color: 'rgba(255,255,255,1)',// 未选中颜色
    selectedColor: 'rgba(255,255,255,0.4)',// 选中颜色
    fontSize: 20,// 字体大小
    list: [...list],
  },
}))

// 修改颜色
const changeColor = async (color, path) => {
  addParams.value[path] = color
  // 清除校验
  await nextTick()
  formRef.value.validateFields([[path]])
}

//上传图片
const uploadSuccess = async (data) => {
  let { form, path, imgUrl } = data;
  _.set(form, path, imgUrl)
  // 清除校验
  await nextTick()
  formRef.value.validateFields([path.split('.').map(item => {
    if (isNaN(item * 1)) {
      return item
    } else {
      return item * 1
    }
  })], { recursive: true })
}

// 修改类型
const changeShowType = (e) => {
  addParams.value.list.forEach(item => {
    item.showType = e.target.value
  })
}

// 获取导航信息
getNav().then(res => {
  if (res.data?.id) {
    res.data.showType = 1
    let content = JSON.parse(res.data.content)
    res.data.list = content.list
    addParams.value = res.data
    console.log(addParams.value)
    console.log(JSON.stringify(addParams.value.list))
  }
})


// 切换链接
const changeLink = ({ data, params: { index } }) => {
  addParams.value.list[index] = data
}

// 保存数据
const saveData = () => {
  formRef.value.validate().then(res => {
    let params = cloneDeep(addParams.value)
    params.checked = 1 // 是否选中 0未选中 1选中
    params.list.forEach(item => {
      if (item.type == 1) {
        item.linkType = '';
        item.linkUrl = '';
        item.linkName = '';
        item.appid = '';
        item.path = '';
        item.url = '';
      }
    })
    params.content = JSON.stringify({
      list: params.list,
    })
    delete params.list
    navSave(params).then(res => {
      message.success('保存成功')
    })
  })
}


</script>
<style lang="scss" scoped>
.link-wrap {
  line-height: 28px;
  padding-right: 10px;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: normal;
  white-space: nowrap;
  color: inherit;
  display: inline-block;
}
.link-delete {
  margin-left: 50px;
  padding: 10px;
  color: red;
}
.link-address-wrap {
  width: 375px;
  padding: 10px;
  border: 1px solid #ebeef5;
  .item-link {
    font-size: 12px;
    display: flex;
    align-items: center;
    color: #606266;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px dashed #ebeef5;
    position: relative;
    .link-delete {
      position: absolute;
      right: 0;
      top: 0;
      padding: 10px;
    }
    &:last-child {
      margin-bottom: 0;
    }

    &.active-item-link {
      color: #e75c45;
    }
  }
}
.tabbar {
  width: 375px;
  height: 68px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  &-item {
    flex: 1;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    &-img {
      width: 24px;
      height: 24px;
      &.big {
        width: 60px;
        height: 60px;
        margin-top: -50px;
      }
    }
    &-name {
      margin-top: 4px;
      font-size: 12px;
    }
  }
}
.content {
  display: flex;
  position: relative;
  overflow: hidden;

  &-tip {
    position: absolute;
    left: 0;
    top: 0;
    padding: 5px;
  }
  .left {
    position: relative;
    width: 375px;
    // height: 812px;
    height: 118px;
    margin-right: 30px;
    display: flex;
    flex-direction: column;
    background: gray;
    outline: 1px solid #999;
    padding-top: 50px;

    &-header {
      height: 94px;
      width: 100%;
    }
    &-content {
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden;
      &::-webkit-scrollbar {
        width: 0px;
        border: 1px solid transparent;
      }
      &::-webkit-scrollbar-button {
        display: none;
      }
      &::-webkit-scrollbar-thumb {
        background: #dddddd;
        border-radius: 3px;
      }
    }
    &-tarbar {
      height: 52px;
    }
  }
  .right {
    flex: 1;
    padding-right: 30px;
  }
  .card {
    padding-top: 20px;
    margin-left: 20px;
    margin-top: 20px;
    margin-bottom: 30px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    position: relative;
    &:first-child {
      margin-top: 0;
    }
    &-drag {
      position: absolute;
      left: 0;
      padding: 10px;
      top: 50%;
      transform: translateY(-50%);
      width: 50px;
    }
    .title {
      position: absolute;
      top: 0;
      left: 0;
      background: #6a6bbf;
      color: #fff;
      font-size: 12px;
      padding: 3px 8px;
    }
  }
  .delete-content {
    position: absolute;
    color: #dc412d;
    font-size: 18px;
    top: 30px;
    right: 30px;
  }
}
</style>

