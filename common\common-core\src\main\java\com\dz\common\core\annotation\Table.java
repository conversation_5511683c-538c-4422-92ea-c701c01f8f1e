package com.dz.common.core.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 表字段注解
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
public @interface Table {
    /** 字段描述 */
    String value();
    /** 分表方式 1固定表数量 2按月分 */
    int splitType() default 0;
    /** 字段描述 */
    int splitCount() default 0;
}