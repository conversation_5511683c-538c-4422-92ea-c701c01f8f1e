{"compileType": "miniprogram", "libVersion": "trial", "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useCompilerPlugins": ["sass"], "ignoreUploadUnusedFiles": true, "urlCheck": true, "compileHotReLoad": false, "swc": false, "disableSWC": true}, "editorSetting": {"tabIndent": "auto", "tabSize": 2}, "simulatorPluginLibVersion": {}, "packOptions": {"ignore": [{"value": "images", "type": "folder"}], "include": []}, "appid": "wx38d029d79dea452e", "projectname": "muji-mini", "condition": {}}