<!--MiKangCampaign/pages/overPage/overPage.wxml-->
<my-page overallModal="{{overallModal}}">
  <!-- background:#F8F6ED; -->
  <view class="page-container">
    <custom-header isShare="{{true}}" background="transparent" type="{{1}}" color="white" />
    <scroll-view class="overPage-wrap" enhanced="{{ true }}" bounces="{{ false }}" show-scrollbar="{{ false }}" scroll-y>
      <view class="overPage">
        <view class="overPage-top">
          <image class="enpageBack" src="{{$cdn}}/MiKangCampaign/enpageBack.jpg" mode="widthFix" />
          <view bind:tap="onTapRule" class="page-rule">活动规则</view>
          <view bind:tap="onTapRule1" class="page-zhongjiang">招募结果</view>
          <view class="page-content" style="padding-top: {{navBarHeight+statusBarHeight+'px'}};">
            <!-- 报名未中奖 或者报名时间已结束 未获得体验资格 -->
            <view class="image1">
              <view class="h1">
                <view><text>{{'很遗憾'}}</text></view>
                <view class="h1-text"><text>{{'您未成为体验官'}}</text></view>
              </view>
              <view class="bubble">
                <image class="bubble-img" src="{{$cdn}}/MiKangCampaign/triangle.png" mode="" />
                <text>您还可以通过以下方式参与打卡活动</text>
              </view>
            </view>
            <view class="otherType-bottom">
              <view class="content-type">
                <image src="{{$cdn}}/MiKangCampaign/PurchaseGoods.png" mode="widthFix" />
                <view class="goStore" bind:tap="goStore"></view>
              </view>
              <view class="otherType-bottom-box">
                <!-- <basic-button width="{{670}}" disabled="{{!disabled}}" loading="{{loading}}" size="large" bind:click="submit">
                  {{!disabled?'已订阅活动消息':"订阅活动消息"}}
                </basic-button> -->
              </view>
              <view class="pullDownView {{type}}">
                <view class="viewIconfont iconfont icon-Pull-down"></view>
                <view class="viewText">下拉查看商品介绍</view>
              </view>
            </view>

          </view>
        </view>
        <view class="picture">
          <image class="img" style="width:750rpx;flex-shrink:0;" src="{{$cdn}}/MiKangCampaign/overpage2.png" mode="widthFix" />
          <!-- <image class="img" style="width:750rpx;flex-shrink:0;" src="{{$cdn}}/MiKangCampaign/overpage3.jpg" mode="widthFix" /> -->
        </view>
      </view>
    </scroll-view>
    <!-- 拒绝订阅 提示框 -->
    <guidePopup showGuide="{{showContact1}}" bind:finish="closeGuide" />
    <result isShow="{{showAnnouncement}}" bindclose="close" />
  </view>
</my-page>