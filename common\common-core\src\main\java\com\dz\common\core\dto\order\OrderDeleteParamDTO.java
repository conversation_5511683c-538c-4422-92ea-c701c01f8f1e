package com.dz.common.core.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 订单信息DTO
 */
@Data
@ApiModel(value = "订单删除入参")
public class OrderDeleteParamDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单编号")
    @NotNull(message = "orderCode不能为空")
    private String orderCode;

}