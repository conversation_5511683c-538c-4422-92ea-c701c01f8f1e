package com.dz.common.core.dto.user;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 券列表
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "已使用券列表")
public class CouponsListDTO extends BaseDTO {

    @ApiModelProperty(value = "券ID")
    private String couponId;
    @ApiModelProperty(value = "活动ID")
    private String activityId;
    @ApiModelProperty(value = "券码")
    private String couponCode;
    @ApiModelProperty(value = "券名称")
    private String couponName;
    @ApiModelProperty(value = "券描述")
    private String couponDesc;
    @ApiModelProperty(value = "优惠券图标")
    private String icon;
    @ApiModelProperty(value = "券类型（1商品券，2优惠券）")
    private Integer couponType;
    @ApiModelProperty(value = "是否已领取（1是，2否）")
    private Integer receiveStatus;
    @ApiModelProperty(value = "1已使用，2已过期")
    private Integer isUsed;
    @ApiModelProperty(value = "优惠券过期时间（yyyy-MM-dd HH:mm:ss）")
    private String couponEffectiveTime;
    @ApiModelProperty(value = "券剩余领取时间yyyy-MM-dd HH:mm:ss")
    private String receiveTime;
    @ApiModelProperty(value = "领券类型，1活动券，2普通券")
    private Integer receiveType;
}
