package com.dz.ms.basic.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.base.dto.BaseDTO;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.CacheEvict;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.basic.TenantConfigDTO;
import com.dz.common.core.fegin.basic.TenantConfigFeignClient;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.ms.basic.constants.CacheKeys;
import com.dz.ms.basic.entity.TenantConfig;
import com.dz.ms.basic.service.TenantConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags="租户设置")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class TenantConfigController implements TenantConfigFeignClient {

    @Resource
    private TenantConfigService tenantConfigService;

    /**
     * 分页查询租户设置
     * @param param
     * @return result<PageInfo<TenantConfigDTO>>
     */
    @ApiOperation("分页查询租户设置")
	@GetMapping(value = "/tenant_config/list")
    public Result<PageInfo<TenantConfigDTO>> getTenantConfigList(@ModelAttribute BaseDTO param) {
        Result<PageInfo<TenantConfigDTO>> result = new Result<>();
        TenantConfig tenantConfig = BeanCopierUtils.convertObjectTrim(param,TenantConfig.class);
        IPage<TenantConfig> page = tenantConfigService.page(new Page<>(param.getPageNum(), param.getPageSize()), new QueryWrapper<>(tenantConfig));
        result.setData(new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),BeanCopierUtils.convertList(page.getRecords(), TenantConfigDTO.class)));
        return result;
    }

    /**
     * 根据ID查询租户设置
     * @param id
     * @return result<TenantConfigDTO>
     */
    @ApiOperation("根据ID查询租户设置")
	@GetMapping(value = "/tenant_config/info")
    public Result<TenantConfigDTO> getTenantConfigById(@RequestParam("id") Long id) {
        Result<TenantConfigDTO> result = new Result<>();
        TenantConfigDTO tenantConfig = tenantConfigService.getTenantConfigById(id);
        result.setData(tenantConfig);
        return result;
    }

    /**
     * 保存租户设置
     * @param param
     * @return result<Long>
     */
    @ApiOperation("保存租户设置")
	@PostMapping(value = "/tenant_config/save")
    public Result<Object> save(@RequestBody TenantConfigDTO param) {
        Result<Object> result = new Result<>();
        tenantConfigService.saveTenantConfig(param);
        return result;
    }
	
	/**
     * 根据ID删除租户设置
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除租户设置")
    @CacheEvict(prefix = CacheKeys.TENANT_CONFIG_INFO,key = "'#param.id'")
	@PostMapping(value = "/tenant_config/delete")
    public Result<Boolean> deleteTenantConfigById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        tenantConfigService.removeById(param.getId());
        result.setData(true);
        return result;
    }

    @ApiOperation("获取当前小程序配置")
    @GetMapping(value = "/app/config/current_info")
    public Result<TenantConfigDTO> getCurrentTenantConfig() {
        Result<TenantConfigDTO> result = new Result<>();
        TenantConfigDTO tenantConfig = tenantConfigService.getTenantConfigById(SecurityContext.getUser().getTenantId());
        result.setData(tenantConfig);
        return result;
    }

}
