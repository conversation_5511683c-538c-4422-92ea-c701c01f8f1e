<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.user.mapper.MyMsgMapper" >

    <select id="selectByMonth" resultType="com.dz.ms.user.entity.MyMsg">
        select * from t_my_msg_${nowDay} where user_id=#{userId} and msg_code=#{msgCode} and DATE_FORMAT(NOW(),'%Y-%m')= DATE_FORMAT(create_time,'%Y-%m')
    </select>
    <select id="selectByMsgCode" resultType="com.dz.ms.user.entity.MyMsg">
        select * from t_my_msg_${nowDay} where user_id=#{userId} and msg_code=#{msgCode} and DATE_FORMAT(NOW(),'%Y-%m-%D')= DATE_FORMAT(create_time,'%Y-%m-%D')
    </select>
    <select id="selectSubscriptionByMsgCode" resultType="com.dz.ms.user.entity.SubscriptionMsg">
        select * from t_subscription_msg_${nowDay} where user_id=#{userId} and msg_code=#{msgCode} and send_desc=#{sendDesc} and DATE_FORMAT(NOW(),'%Y-%m-%D')= DATE_FORMAT(create_time,'%Y-%m-%D')
    </select>
    <select id="listByUserId" resultType="com.dz.ms.user.entity.MyMsg">
        select * from t_my_msg_${nowDay} where user_id=#{userId} order by create_time desc
    </select>
    <update id="updateByUserId">
        update t_my_msg_${nowDay} set is_read=1 where user_id=#{userId} and is_read=2
    </update>
    <select id="listByUnRead" resultType="com.dz.ms.user.entity.MyMsg">
        select * from t_my_msg_${nowDay} where user_id=#{userId} and is_read=2 and DATE_FORMAT(NOW(),'%Y-%m-%D')= DATE_FORMAT(create_time,'%Y-%m-%D') order by sort_num desc
    </select>
    <select id="listByUnReadNum" resultType="com.dz.ms.user.entity.MyMsg">
        select * from t_my_msg_${nowDay} where user_id=#{userId} and is_read=2
    </select>
    <update id="updateReadById">
        update t_my_msg_${nowDay} set is_read=1 where id=#{id}
    </update>

    <insert id="inserByTable">
        insert into t_my_msg_${nowDay}
        (title,
        msg_desc,
        jump_url,
        create_at,
        create_time,
        tenant_id,
        user_id,
        is_read,
        msg_code,
        home_title,
        sort_num,
        msg_type
        )
        values
            (#{myMsg.title},
            #{myMsg.msgDesc},
            #{myMsg.jumpUrl},
            #{myMsg.createAt},
            #{myMsg.createTime},
            #{myMsg.tenantId},
            #{myMsg.userId},
            #{myMsg.isRead},
            #{myMsg.msgCode},
            #{myMsg.homeTitle},
            #{myMsg.sortNum},
            #{myMsg.msgType})
    </insert>
    <!-- 批量插入 -->
    <insert id="inserListByTable">
        insert into t_my_msg_${nowDay}
        (title,
        msg_desc,
        jump_url,
        create_at,
        create_time,
        tenant_id,
        user_id,
        is_read,
        msg_code,
        home_title,
        sort_num,
        msg_type
        )
        values
        <foreach collection="list" item="myMsg" separator=",">
            (#{myMsg.title},
            #{myMsg.msgDesc},
            #{myMsg.jumpUrl},
            #{myMsg.createAt},
            #{myMsg.createTime},
            #{myMsg.tenantId},
            #{myMsg.userId},
            #{myMsg.isRead},
            #{myMsg.msgCode},
            #{myMsg.homeTitle},
            #{myMsg.sortNum},
            #{myMsg.msgType})
        </foreach>
    </insert>

    <!--获取分表列表-->
    <select id="querySubTableList" resultType="java.lang.String">
        SHOW TABLES LIKE 't_my_msg_%'
    </select>


    <!--创建分表-->
    <update id="createSubTable" parameterType="java.lang.String">
        CREATE TABLE muji_user.t_my_msg_${nowDay}  (
                                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                        `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '标题',
                                        `msg_desc` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '内容',
                                        `jump_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '跳转链接',
                                        `create_at` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
                                        `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                        `tenant_id` bigint NOT NULL COMMENT '租户ID',
                                        `user_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'memberCode',
                                        `is_read` tinyint NULL DEFAULT NULL COMMENT '是否已读，1是2否',
                                        `msg_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '消息code',
                                        `home_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '首页通知标题',
                                        `sort_num` int NULL DEFAULT NULL COMMENT '排序',
                                        `msg_type` tinyint NULL DEFAULT 1 COMMENT '1会小2历史迁移',
                                        PRIMARY KEY (`id`) USING BTREE,
                                        INDEX `idx_userid_msgcode_createtime`(`user_id`, `msg_code`, `create_time`) USING BTREE,
                                        INDEX `idx_userid`(`user_id`) USING BTREE,
                                        INDEX `idx_userid_isread`(`user_id`, `is_read`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 24807 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '我的消息' ROW_FORMAT = DYNAMIC;
    </update>
<!--下面是订阅消息的-->
    <!--获取分表列表-->
    <select id="querySubscriptionTableList" resultType="java.lang.String">
        SHOW TABLES LIKE 't_subscription_msg_%'
    </select>


    <!--创建分表-->
    <update id="createSubscriptionTable" parameterType="java.lang.String">
        CREATE TABLE muji_user.t_subscription_msg_${nowDay}  (
                                                      `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                      `create_at` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
                                                      `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
                                                      `tenant_id` bigint NOT NULL COMMENT '租户ID',
                                                      `user_id` bigint NULL DEFAULT NULL COMMENT 'userId',
                                                      `msg_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '消息code',
                                                      `send_desc` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发送描述',
                                                      PRIMARY KEY (`id`) USING BTREE,
                                                      INDEX `idx_uid_code_time`(`create_time`, `user_id`, `msg_code`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '发送订阅消息记录' ROW_FORMAT = DYNAMIC;
    </update>

    <insert id="inserSubscriptionByTable">
        insert into t_subscription_msg_${nowDay}
        (create_at,
         create_time,
         tenant_id,
         user_id,
         msg_code,
         send_desc
        )
        values
            (#{myMsg.createAt},
             #{myMsg.createTime},
             #{myMsg.tenantId},
             #{myMsg.userId},
             #{myMsg.msgCode},
             #{myMsg.sendDesc})
    </insert>
    <select id="selectSubscriptionByActivityId" resultType="com.dz.ms.user.entity.SubscriptionMsg">
        select * from t_subscription_msg_activity where user_id=#{userId} and msg_code=#{msgCode}
    </select>

    <insert id="inserSubscriptionByActivityId">
        insert into t_subscription_msg_activity
        (create_at,
         create_time,
         tenant_id,
         user_id,
         msg_code
        )
        values
            (#{myMsg.createAt},
             #{myMsg.createTime},
             #{myMsg.tenantId},
             #{myMsg.userId},
             #{myMsg.msgCode})
    </insert>
</mapper>
