<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.adaptor.mapper.ScheduledTaskConfigLogMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    id,
  	    task_config_id,
  	    task_name,
  	    cron_expression,
  	    param,
  	    run_time,
  	    status,
  	    created_at,
  	    updated_at
    </sql>
	<insert id="insertLog" useGeneratedKeys="true" keyProperty="param.id">
		insert into scheduled_task_config_log_${month} (
			task_config_id,
			task_name,
			cron_expression,
			param,
			run_time,
			status,
			created_at,
			updated_at,
		    tenant_id
		)
		values (
			#{param.taskConfigId},
			#{param.taskName},
			#{param.cronExpression},
			#{param.param},
			#{param.runTime},
			#{param.status},
			#{param.createdAt},
			#{param.updatedAt},
		    #{param.tenantId}
		)
	</insert>
	<update id="updateStatus">
		update scheduled_task_config_log_${month}
		<set>
			<if test="param.runTime">
				run_time = #{param.runTime},
			</if>
			<if test="param.status">
				status = #{param.status},
			</if>
			<if test="param.updatedAt">
				updated_at = #{param.updatedAt},
			</if>
		</set>
		where id = #{param.id}
	</update>

	<select id="queryTableList" resultType="java.lang.String">
		SHOW TABLES LIKE 'scheduled_task_config_log_%'
	</select>

	<select id="createTable" parameterType="java.lang.String" statementType="STATEMENT">
		CREATE TABLE IF NOT EXISTS scheduled_task_config_log_${month} (
													 `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
													 `task_config_id` bigint NOT NULL COMMENT 'task配置表id',
													 `task_name` varchar(255) NOT NULL COMMENT '任务名称',
													 `cron_expression` varchar(255) NOT NULL COMMENT 'Cron表达式',
													 `param` varchar(2000) DEFAULT NULL COMMENT '参数',
													 `run_time` bigint DEFAULT NULL COMMENT '执行时间',
													 `status` int NOT NULL DEFAULT '0' COMMENT '状态(0进行中 1成功 2失败)',
													 `created_at` datetime NOT NULL COMMENT '创建时间',
													 `updated_at` datetime NOT NULL COMMENT '更新时间',
													 `tenant_id` bigint DEFAULT NULL,
													 PRIMARY KEY (`id`)
		) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='定时任务日志表';
	</select>
	
	


</mapper>
