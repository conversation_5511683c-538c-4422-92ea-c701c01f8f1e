package com.dz.common.core.dto.wechat;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 企微部门员工信息
 * <AUTHOR>
 * @date 2022/07/21 18:10
 */
@Setter
@Getter
@ToString
public class QyEmployeeDTO {

    @ApiModelProperty(value = "成员UserID")
    private String userid;
    @ApiModelProperty(value = "成员名称")
    private String name;
    @ApiModelProperty(value = "成员所属部门列表")
    private List<Integer> department;
    @ApiModelProperty(value = "部门内的排序值默认为0。数量必须和department一致，数值越大排序越前面")
    private List<Integer> order;
    @ApiModelProperty(value = "职务信息")
    private String position;
    @ApiModelProperty(value = "手机号码")
    private String mobile;
    @ApiModelProperty(value = "性别0表示未定义，1表示男性，2表示女性")
    private String gender;
    @ApiModelProperty(value = "邮箱")
    private String email;
    @ApiModelProperty(value = "企业邮箱")
    @JSONField(name = "biz_mail")
    private String bizMail;
    @ApiModelProperty(value = "表示在所在的部门内是否为部门负责人")
    @JSONField(name = "is_leader_in_dept")
    private List<Integer> isLeaderInDept;
    @ApiModelProperty(value = "直属上级UserID")
    @JSONField(name = "direct_leader")
    private List<String> directLeader;
    @ApiModelProperty(value = "头像url")
    private String avatar;
    @ApiModelProperty(value = "头像缩略图url")
    @JSONField(name = "thumb_avatar")
    private String thumbAvatar;
    @ApiModelProperty(value = "座机")
    private String telephone;
    @ApiModelProperty(value = "别名")
    private String alias;
    @ApiModelProperty(value = "激活状态: 1=已激活，2=已禁用，4=未激活，5=退出企业")
    private String status;
    @ApiModelProperty(value = "地址")
    private String address;
    @ApiModelProperty(value = "英文名称")
    @JSONField(name = "english_name")
    private String englishName;
    @ApiModelProperty(value = "全局唯一")
    @JSONField(name = "openUserid")
    private String open_userid;
    @ApiModelProperty(value = "主部门")
    @JSONField(name = "main_department")
    private Integer mainDepartment;
    @ApiModelProperty(value = "员工个人二维码")
    @JSONField(name = "qr_code")
    private String qrCode;
    @ApiModelProperty(value = "对外职务")
    @JSONField(name = "external_position")
    private String externalPosition;

}
