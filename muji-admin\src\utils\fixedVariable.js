
import { v4 as uuidv4 } from 'uuid'


// 装饰页面变量
export const pageData = {
  id: '',
  templateCode: '',// 模板编号
  templateType: '',// 模板类型
  pageType: '',// 页面类型
  templateName: '',// 模板名称
  groupId: '',// 页面组ID
  groupName: '',// 页面组
  pagePath: '',// 页面地址
  isOnly: 0,//	同类型是否只存在一个已发布 0否 1是
  publish: 0, //是否发布 0否1是
  pageJson: '',//	模板内容预览json
  content: '', //模板内容json
  pageSetting: { // 页面设置
    templateName: '',// 模板名称
    pageType: '1',//1-常规页 2-加载页 3-开屏页  4-弹窗
    // 弹窗设置
    pageWidth: 750,// 弹窗宽度 单位 rpx
    pageHeight: 800,// 弹窗高度
    modalPos: 1,// 弹窗位置 1-居中 2-居下
    modalTitle: 0, // 是否显示标题内容
    modalRadius: '',// 圆角
    modalCloseImg: '',// 关闭图片
    modalMaskColor: 'rgba(0,0,0,.7)',// 遮罩层颜色
    modalMaskClose: false,// 遮罩层是否可以关闭
    // 开屏设置
    openFrequency: 1,//开屏频次  1-每次打开都展示  2-仅展示1次 3-自定义
    openDays: '',// 天数
    openNumer: '',//次数
    autoGo: false, // 是否自动跳转
    imgLinks: [], // 自动跳过配置
    autoTime: '', // 自动跳转时间
    openGo: false, // 是否有跳过按钮
    openLinks: [],// 跳过配置
    // 背景设置
    bgType: 1,//背景设置 1-纯色  2-图片  3-视频
    bgColor: 'rgba(0,0,0,0)',
    bgImg: '',
    bgVideo: '',
    // 分享设置
    isShare: 1,
    shareTitle: '',//分享标题
    shareImg: '',//分享图片
    sharePath: '',//分享路径

    // 弹窗设置
    autoModal: false, // 自动弹窗
    openModalType: 1, // 弹窗频率
    modalLinks: [], // 弹窗设置
    // 悬浮窗
    floatSetting: {
      openFloat: false, // 显示悬浮窗
      width: 20,
      height: 20,
      bottom: 20,
      horizontalType: 1,//1-右下 2-左下
      left: 20,
      right: 20,
      space: 20, // 图之间的距离
      drag: false, // 拖动
      showType: 1, // 1-仅图 2-图+名称
      floatList: []
    }
  },
  // 导航设置
  navSetting: {
    navType: 1,//1-固定标题  2-完全沉浸  3-滑动恢复 4-滑动恢复
    moveType: 1, // 1-滑动前 2-滑动后
    navColor: 1,// 状态栏的颜色
    navColor1: 1,// 状态栏滑动后的颜色
    isTitle: 1, // 1-文字 2-图片
    titleUrl: '',
    titlePos: 1,// 标题位置  1-居中 2-居左
    back: 1,
    titleColor: 'rgba(0,0,0,1)',// 标题文字颜色
    bgColorStyle: 0,// 颜色风格设置 0-无 1-全局设置 2-自定义
    // 背景设置
    bgType: 1,//背景设置 1-纯色  2-图片  3-视频
    bgColor: 'rgba(0,0,0,0)',
    bgImg: '',
    bgVideo: '',
    imgLinks: [
      {
        id: uuidv4(),
        width: 10,
        height: 10,
        position: [0, 0], // 位置
        code: '',// 埋点参数
        events: [],// 事件
        actions: [],// 行为
      }
    ],// 图片热区
    move: {
      navColor: 1,// 状态栏的颜色
      isTitle: 1, // 1-文字 2-图片
      titleUrl: '',
      titlePos: 1,// 标题位置  1-居中 2-居左
      back: 1,
      titleColor: 'rgba(0,0,0,1)',// 标题文字颜色
      bgColorStyle: 0,// 颜色风格设置 0-无 1-全局设置 2-自定义
      // 背景设置
      bgType: 1,//背景设置 1-纯色  2-图片  3-视频
      bgColor: 'rgba(0,0,0,0)',
      bgImg: '',
      bgVideo: '',
      imgLinks: [
        {
          id: uuidv4(),
          width: 10,
          height: 10,
          position: [0, 0], // 位置
          code: '',// 埋点参数
          events: [],// 事件
          actions: [],// 行为
        }
      ],// 图片热区
    }
  },
  // 逻辑行为
  actionLinks: [
    {
      id: uuidv4(),
      width: 10,
      height: 10,
      position: [0, 0], // 位置
      code: '',// 埋点参数
      events: [],// 事件
      actions: [],// 行为
    }
  ],// 图片热区
  componentSetting: [] // 组件配置
}

// 热区行为
export const operateTypes = [
  { value: 1, label: '页面跳转' },
  { value: 2, label: '关闭弹窗' },
  { value: 3, label: '打开弹窗' },
  { value: 4, label: '长按识别二维码' },
  { value: 5, label: '点击复制' },
  { value: 6, label: '点击分享' },
  { value: 7, label: '锚点跳转' },
  { value: 8, label: '置顶' },
  { value: 9, label: '返回上一页' },
]

// 组件配置
export const components = [
  {
    id: uuidv4(),
    type: '基础组件',
    components: [ // 通过type 区分组件
      { type: 1, text: '富文本', icon: 'rich.png' },
      { type: 2, text: '图片', icon: 'image.png' },
      { type: 3, text: '视频', icon: 'video.png' },
      { type: 4, text: '普通轮播', icon: 'slider.png' },
      { type: 6, text: '滚动区域', icon: 'scroll.png' },
      { type: 9, text: '普通文本', icon: 'text.png' },
      { type: 10, text: '分割线', icon: 'line.png' },
    ]
  },
  {
    id: uuidv4(),
    type: '首页组件',
    components: [ // 通过type 区分组件
      { type: 7, text: '门店', icon: 'store.png' },
      { type: 8, text: '任务', icon: 'task.png' },
    ]
  }
]


export const fontFamily = [
  { id: 2, value: 'MUJIFont2020', label: '品牌英文字体' },
  { id: 1, value: 'SourceHanSansCN', label: '品牌中文字体' },
  { id: 3, value: 'PingFang SC', label: '苹果字体' },
  { id: 4, value: '思源黑体', label: '思源黑体' },
  { id: 5, value: 'Helvetica Neue', label: 'Helvetica Neue' },
  { id: 6, value: 'Helvetica', label: 'Helvetica' },
  { id: 7, value: 'Hiragino Sans GB', label: '苹果丽黑' },
  { id: 8, value: 'Microsoft YaHei', label: '微软雅黑' },
  { id: 9, value: 'Arial', label: 'Arial无衬线字体' },
  { id: 10, value: 'sans-serif', label: 'sans-serif无衬线字体' },
]
// 组件配置变量
export const typeData = {
  // 富文本
  "1": {
    type: 1,
    text: '富文本',
    content: '', // 内容
    bgColor: 'rgba(0,0,0,0)', // 背景色
    fontColor: 'rgba(0,0,0,0)', // 字体色
    paddingTop: 0,// 上边距
    paddingBottom: 0,// 下边距
    paddingLeft: 0,// 左边距
    paddingRight: 0,// 右边距
  },
  // 图片组件
  "2": {
    type: 2, // 类型 需要和components中保持一致
    text: '图片', // 名字  需要和components中保持一致
    heightSet: 0,// 0-自适应  1-一屏高度
    imgUrl: '', // 图片url
    imgWidth: '',// 图片原始高度
    imgHeight: '',// 图片原始高度
    bottom: '',// 底部margin
    // 背景设置
    bgType: 1,//背景设置 1-纯色  2-图片  3-视频
    bgColor: 'rgba(0,0,0,0)',
    bgImg: '',
    bgVideo: '',
    // 图片热区
    imgLinks: [
      {
        id: uuidv4(),
        width: 10,
        height: 10,
        position: [0, 0], // 位置
        code: '',// 埋点参数
        events: [],// 事件
        actions: [],// 行为
      }
    ],
    borderRadius: 0,// 圆角
    paddingTop: 0,// 上边距
    paddingBottom: 0,// 下边距
    paddingLeft: 0,// 左边距
    paddingRight: 0,// 右边距
  },
  // 视频
  "3": {
    type: 3, // 类型 需要和components中保持一致
    text: '视频', // 名字  需要和components中保持一致
    heightSet: 0,// 0-自适应  1-一屏高度
    imgUrl: '', // 视频url
    coverUrl: '', // 封面url
    imgWidth: '',// 视频原始高度
    imgHeight: '',// 视频原始高度
    bottom: '',// 底部margin
    // 背景设置
    bgType: 1,//背景设置 1-纯色  2-图片  3-视频
    bgColor: 'rgba(0,0,0,0)',
    bgImg: '',
    bgVideo: '',
    autoplay: false, // 自定播放
    loop: false, // 循环播放
    playBtn: false,// 播放按钮
    muted: false,// 播放时静音
    controls: false,// 控制条
    muteBtn: false,// 静音按钮
    // 图片热区
    imgLinks: [
      // {
      //   id: uuidv4(),
      //   width: 10,
      //   height: 10,
      //   position: [0, 0], // 位置
      //   code: '',// 埋点参数
      //   events: [],// 事件
      //   actions: [],// 行为
      // }
    ],
    borderRadius: 0,// 圆角
    paddingTop: 0,// 上边距
    paddingBottom: 0,// 下边距
    paddingLeft: 0,// 左边距
    paddingRight: 0,// 右边距
  },
  // 普通轮播
  "4": {
    type: 4, // 类型 需要和components中保持一致
    text: '普通轮播', // 名字  需要和components中保持一致
    height: 30,
    list: [],
    // 背景设置
    bgType: 1,//背景设置 1-纯色  2-图片  3-视频
    bgColor: 'rgba(0,0,0,0)',
    bgImg: '',
    bgVideo: '',
    borderRadius: 0,// 圆角
    paddingTop: 0,// 上边距
    paddingBottom: 0,// 下边距
    paddingLeft: 0,// 左边距
    paddingRight: 0,// 右边距
    autoplay: false, // 自动轮播
    delayTime: 3000, // 停留时间
    delayTimes: [], // 特殊停留时间
    durationTime: 2000, // 过渡时间
    pointOpen: false, // 指点打开
    pointSpace: 20, // 指示点间距
    pointSize: 20, // 指示点直径
    pointSelectColor: '',// 选中颜色
    pointUnSelectColor: '',// 未选中颜色
    pointPostion: 1, // 1-图内 2-图外
    pointHorizonal: 1, // 1-左 2-居中 3-居右
    pointVertical: 1, // 1-上  2-下
    pointOutSpace: 20, // 外边距
    subject: {
      open: false,
      content: '',//
      fontFamily: ['思源黑体'],//字体类型
      fontSize: 36,// 字体大小
      lineHeight: 54, // 行高
      color: 'rgba(0,0,0,1)',// 字体颜色
      fontWeight: '',//字体粗细
      fontItalic: false, // 字体斜体
      underLine: false, // 字体下划线
      deleteLine: false, // 字体删除线
      textAlign: 'left', // 居左
      lineType: 2, //显示行数
      lines: 1,// 行数
      paddingTop: 20,
      paddingBottom: 20,
      paddingLeft: 20,
      paddingRight: 20,
    },
    mainTitle: {
      open: false,
      fontFamily: ['思源黑体'],//字体类型
      fontSize: 28,// 字体大小
      lineHeight: 42, // 行高
      color: 'rgba(0,0,0,1)',// 字体颜色
      fontWeight: '',//字体粗细
      fontItalic: false, // 字体斜体
      underLine: false, // 字体下划线
      deleteLine: false, // 字体删除线
      textAlign: 'left', // 居左
      lineType: 2, //显示行数
      lines: 1,// 行数
      paddingTop: 20,
      paddingBottom: 20,
      paddingLeft: 20,
      paddingRight: 20,
    },
    smallTitle: {
      open: false,
      fontFamily: ['思源黑体'],//字体类型
      fontSize: 24,// 字体大小
      lineHeight: 36, // 行高
      color: 'rgba(0,0,0,1)',// 字体颜色
      fontWeight: '',//字体粗细
      fontItalic: false, // 字体斜体
      underLine: false, // 字体下划线
      deleteLine: false, // 字体删除线
      textAlign: 'left', // 居左
      lineType: 2, //显示行数
      lines: 2,// 行数
      paddingTop: 20,
      paddingBottom: 20,
      paddingLeft: 20,
      paddingRight: 20,
    },
    slideShow: false, //滑动提示
    // 悬浮窗
    floatSetting: {
      openFloat: false, // 显示悬浮窗
      width: 20,
      height: 20,
      bottom: 20,
      horizontalType: 1,//1-右下 2-左下
      left: 20,
      right: 20,
      space: 20, // 图之间的距离
      drag: false, // 拖动
      showType: 1, // 1-仅图 2-图+名称
      floatList: []
    }

  },
  // 滚动区域
  "6": {
    type: 6, // 类型 需要和components中保持一致
    text: '滚动区域', // 名字  需要和components中保持一致
    height: 300,// 内容高度
    width: 750, // 内容宽度
    list: [
      // {
      //   id: uuidv4(),
      //   imgUrl: '', // 图片url
      //   imgWidth: '',// 图片原始高度
      //   imgHeight: '',// 图片原始高度
      //   // 图片热区
      //   imgLinks: [
      //     {
      //       id: uuidv4(),
      //       width: 10,
      //       height: 10,
      //       position: [0, 0], // 位置
      //       code: '',// 埋点参数
      //       events: [],// 事件
      //       actions: [],// 行为
      //     }
      //   ],
      // }
    ],
    // 背景设置
    bgType: 1,//背景设置 1-纯色  2-图片  3-视频
    bgColor: 'rgba(0,0,0,0)',
    bgImg: '',
    bgVideo: '',
    borderRadius: 0,// 圆角
    paddingTop: 0,// 上边距
    paddingBottom: 0,// 下边距
    paddingLeft: 0,// 左边距
    paddingRight: 0,// 右边距
  },
  // 门店
  "7": {
    type: 7,
    text: '门店',
    title: '',// 标题配置
    imgUrl: '', // 门店图片
    bgColor: 'rgba(0,0,0,0)', // 背景色
    paddingTop: 0,// 上边距
    paddingBottom: 0,// 下边距
    paddingLeft: 0,// 左边距
    paddingRight: 0,// 右边距
  },
  "8": {
    type: 8,
    text: '任务',
    title: '',// 标题配置
    bgColor: 'rgba(0,0,0,0)', // 背景色
    paddingTop: 0,// 上边距
    paddingBottom: 0,// 下边距
    paddingLeft: 0,// 左边距
    paddingRight: 0,// 右边距

  },
  "9": {
    type: 9,
    text: '普通文本',
    paddingTop: 0,// 上边距
    paddingBottom: 0,// 下边距
    paddingLeft: 0,// 左边距
    paddingRight: 0,// 右边距
    // 背景设置
    bgType: 1,//背景设置 1-纯色  2-图片  3-视频
    bgColor: 'rgba(0,0,0,0)',
    bgImg: '',
    bgVideo: '',
    imgLinks: [],
    fontFamily: undefined,//字体类型
    fontSize: 20,// 字体大小
    lineHeight: 30, // 行高
    color: '',// 字体颜色
    fontWeight: '',//字体粗细
    fontItalic: false, // 字体斜体
    underLine: false, // 字体下划线
    deleteLine: false, // 字体删除线
    textAlign: 'left', // 居左
    lineType: 1, //显示行数
    lines: '',// 行数

  },
  "10": {
    type: 10,
    text: '分割线',
    paddingTop: 0,// 上边距
    paddingBottom: 0,// 下边距
    paddingLeft: 0,// 左边距
    paddingRight: 0,// 右边距
    lineType: 'solid', // 直线类型
    height: 1, // 高度
    color: 'rgba(0,0,0,0)',
    // 背景设置
    bgType: 1,//背景设置 1-纯色  2-图片  3-视频
    bgColor: 'rgba(0,0,0,0)',
    bgImg: '',
    bgVideo: '',
  }

};
// 组件类型变量
export const selectType = [
  // { value: 1, name: '标题导航栏' },
  { value: 2, name: '海报切图组件' },
  // { value: 3, name: '梯形组件(大小图切换)' },
  // { value: 5, name: '梯形组件(一个图缩放)' },
  { value: 4, name: '多图轮播组件' },
  { value: 6, name: '视频组件' },
  { value: 7, name: '锚点轮播组件' },

]


// 变量
export const miniVariable = [
  {
    name: '会员相关',
    key: 'userInfo',
    children: [
      { id: 1, name: '微信昵称', key: 'username' },
      { id: 2, name: '注册时间', key: 'registerTime' },
      { id: 3, name: '手机号', key: 'mobile' },
      { id: 4, name: '会员等级', key: 'cardLevelName' },
      { id: 5, name: '生日', key: 'birthday' },
      { id: 6, name: '会员卡号', key: 'cardNo' },
      { id: 7, name: '所在省', key: 'province' },
      { id: 8, name: '所在市', key: 'city' },
      { id: 9, name: '称谓', key: 'gender' },
    ]
  }
]

// 变量列表
export const userListVariable = miniVariable[0].children

// 组件的名字
export const componentName = ['', 'Nav', 'Poster', 'FloorChange', 'Slider', 'FloorScale', 'videoCmp', 'Anchor']

// 预览组件名字
export const componentPreviewName = ['', 'NavPreview', 'PosterPreview', 'FloorChangePreview', 'SliderPreview', 'FloorScalePreview', 'VideoCmpPreview', 'AnchorPreview']
