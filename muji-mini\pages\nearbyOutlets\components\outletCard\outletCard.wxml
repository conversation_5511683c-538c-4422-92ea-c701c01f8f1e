<view class="outlet-card">
  <view class="card-box">
    <view class="box-left">
      <view class="name">
        {{outletData.storeName}}
        <view wx:if="{{outletData.type===2}}" class="tag">
          <view>{{storeTag}}</view>
        </view>
      </view>
      <view class="info">
        <view class="info-item">
          <view class="iconfont icon-Place1" />
          <view class="address-name">{{outletData.storeAddress}}</view>
        </view>
        <view class="info-item"
          wx:if="{{(outletData.openingHourOne!==null&&outletData.openingHourOne!=='')||(outletData.openingHourTwo!==null && outletData.openingHourTwo!=='')}}">
          <view class="iconfont icon-Time" />
          <view class="open-time">
            <view wx:if="{{outletData.openingHourOne!==null && outletData.openingHourOne!==''}}" class="time-item">
              {{outletData.openingHourOne}}</view>
            <view wx:if="{{outletData.openingHourTwo!==null && outletData.openingHourTwo!==''}}" class="time-item"
              style="margin-top: 22rpx;">{{outletData.openingHourTwo}}</view>
          </view>
          <!-- {{outletData.openDate}} -->
        </view>
      </view>
    </view>
    <view class="box-right">
      <view class="distance-box">
        <view class="distance-icon" catchtap="openLocation" data-store="{{outletData}}">
          <image src="{{$cdn}}/guide.png" />
        </view>
        <view wx:if="{{outletData.distance}}" class="num">
          {{outletData.distance}}
        </view>
      </view>
    </view>
  </view>
  <!-- <view class="service-box">
    在此门店
    <view class="service-list">
      <view class="service-item" wx:for="{{outletData.serveDTOList}}" wx:key="id">
        <image class="service-icon" src="{{item.image}}" />
        <view class="service-name ellipsis">{{item.name}}</view>
      </view>
    </view>
  </view> -->
  <view class="btn-box">
    <view class="btn-item" bindtap="callTel" data-num="{{outletData.phone}}">
      电话咨询
    </view>
    <block wx:if="{{(outletData.weworkImages&&outletData.weworkImagesType === 1 ) || (outletData.weworkOneImages&&outletData.weworkImagesType === 2)}}">
      <view class="btn-item  black-bg" bindtap="goGuide" data-value="{{outletData.id}}"
        data-distance="{{outletData.distance}}">
        服务顾问
      </view>
    </block>
  </view>
</view>
