<!--MiKangCampaign/pages/announcement/announcement.wxml-->
<my-page overallModal="{{overallModal}}">
  <view class="page-container" style="background:#FBF8F3;">
    <custom-header type="{{1}}" background="transparent" isBackHidden="{{ isBackHidden }}" color="black">
    </custom-header>
    <scroll-view class="announcement" enhanced="{{ true }}" bounces="{{ false }}" show-scrollbar="{{ false }}" scroll-y>
      <view class="content">
        <view class="title">体验官公示结果</view>
        <block wx:for="{{listData}}" wx:for-item="person">
          <block wx:if="{{person.enrollRosterList.length>0}}">
            <view class="title1"><text>第{{person.tabsObj}}轮体验官招募结果：</text></view>
            <view class="box">
              <view class="title-content">第{{person.tabsObj}}轮报名时间：</view>
              <view class="title-Time">{{person.campaignStartTime}}-{{person.campaignEndTime}}</view>
              <view class="title-content">第{{person.tabsObj}}轮报名结果公示时间：</view>
              <view class="title-Time">{{person.campaignShowTime}}</view>
              <view class="title-content">极简护肤礼：</view>
              <view class="tips"><text>报名中选的会员可获得极简护肤礼，包含：【无印良品敏感肌用基础补水化妆水•高保湿型50ml】与【无印良品敏感肌用基础补水乳液•高保湿型50ml】各1份。</text></view>
              <view class="title-content">极简护肤礼发货时间：</view>
              <view class="tips"><text>您的极简护肤礼将在公示期满后7个工作日内完成发放，寄往体验官报名时所填写的收货地址，请各位体验官注意查收（部分地区可能因物流原因稍有延迟）。</text></view>
              <view class="title-content">第{{person.tabsObj}}轮报名中奖名单：</view>
              <view class="table">
                <view class="head">
                  <view class="table_th">
                    <view class="table_td" wx:for="{{option}}" wx:key="index" style="text-align: {{item.align||'center'}};">{{item.label}}</view>
                  </view>
                </view>
                <!-- 超出10个滚动 -->
                <scroll-view class="table_scroll" scroll-y style="max-height:600rpx;" bindscrolltolower="handleScrollToLower">
                  <view class="tbale_body">
                    <view class="table_tr" wx:for="{{person.enrollRosterList}}" wx:key="rowIndex" wx:for-index="rowIndex" wx:for-item="rowItem">
                      <view class="table_body_td" style="text-align: center;" wx:for="{{option}}" wx:key="colIndex" wx:for-index="colIndex" wx:for-item="colItem">{{rowItem[colItem.value]}}</view>
                    </view>
                  </view>
                </scroll-view>
              </view>
            </view>
          </block>

        </block>

      </view>
      <view class="bottom"></view>
    </scroll-view>
  </view>
</my-page>