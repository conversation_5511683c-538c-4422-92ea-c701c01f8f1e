package com.dz.ms.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.ms.basic.dto.ChannelLinkDTO;
import com.dz.ms.basic.entity.ChannelLink;

/**
 * 渠道链接配置接口
 * @author: Handy
 * @date:   2023/08/26 17:06
 */
public interface ChannelLinkService extends IService<ChannelLink> {

	/**
     * 分页查询渠道链接配置
     * @param param
     * @return PageInfo<ChannelLinkDTO>
     */
    public PageInfo<ChannelLinkDTO> getChannelLinkList(ChannelLinkDTO param);

    /**
     * 根据ID查询渠道链接配置
     * @param id
     * @return ChannelLinkDTO
     */
    public ChannelLinkDTO getChannelLinkById(Long id);

    /**
     * 保存渠道链接配置
     * @param param
     * @return Long
     */
    public Long saveChannelLink(ChannelLinkDTO param);

    /**
     * 根据ID删除渠道链接配置
     * @param param
     */
    public void deleteChannelLinkById(IdCodeDTO param);

    /**
     * 根据短链编码获取用户url scheme
     * @param code
     * @return
     */
    String getUrlSchemeByCode(String code);

}
