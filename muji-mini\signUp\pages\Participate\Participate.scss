/* signUp/pages/Participate/Participate.wxss */
.page-container {
  background: linear-gradient(rgba(255, 255, 255, 0) 1%, #F8F6ED 100%);
  background-size: 100% 100%;

  .page-main {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    z-index: 1;
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .page-header {
    display: block;
    width: 100vw;
    height: 861rpx;
  }

  .page-content {
    pointer-events: none;
    margin-top: 105rpx;
    margin-left: 79rpx;
    margin-bottom: 143rpx;
    width: 586rpx;
    height: 375rpx;
  }

  .Participate-wrap {
    display: flex;
    flex-direction: column;
    position: relative;

    .activeRules {
      position: fixed;
      right: 0rpx;
      top: 243rpx;
      width: 70rpx;
      height: 146rpx;
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .activeRules-in {
        width: 44rpx;
        height: 146rpx;
        background: #C7B397;
        font-family: MUJIFont2020,
          SourceHanSansCN;
        font-weight: 400;
        font-size: 22rpx;
        color: #FFFFFF;
        line-height: 24rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        writing-mode: vertical-rl;
        letter-spacing: 4rpx;
      }

    }

    .activeRules2 {
      position: fixed;
      right: 0rpx;
      top: 420rpx;
      width: 70rpx;
      height: 146rpx;
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .activeRules2-in {
        width: 44rpx;
        height: 146rpx;
        background: #C7B397;
        font-family: MUJIFont2020,
          SourceHanSansCN;
        font-weight: 400;
        font-size: 22rpx;
        color: #FFFFFF;
        line-height: 24rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        writing-mode: vertical-rl;
        letter-spacing: 4rpx;
      }
    }

    .title1 {
      font-family: MUJIFont2020,
        SourceHanSansCN;
      font-weight: 500;
      font-size: 48rpx;
      color: var(--text-black-color);
      line-height: 65rpx;
      letter-spacing: 1rpx;
      text-align: left;
      padding-left: 57rpx;
      padding-top: 75rpx;
    }

    .title2 {
      font-family: MUJIFont2020,
        SourceHanSansCN;
      font-weight: 300;
      font-size: 28rpx;
      color: var(--text-black-color);
      line-height: 56rpx;
      letter-spacing: 2rpx;
      text-align: left;
      margin-left: 57rpx;
      margin-top: 7rpx;
    }

    .step {
      display: flex;
      margin-left: 75rpx;
      margin-top: 40rpx;
      align-items: center;

      .step-img {
        width: 100rpx;
        height: 100rpx;
        // background-color: rgb(73, 71, 71);
        background-size: 100% 100%;
        margin-right: 60rpx;
      }

      .step-text {
        font-family: MUJIFont2020,
          SourceHanSansCN;
        font-weight: 400;
        font-size: 22rpx;
        color: var(--text-black-color);
        line-height: 40rpx;
        letter-spacing: 2rpx;
        text-align: left;

        .b {
          font-family: MUJIFont2020, SourceHanSansCN;
          font-weight: 700;
          font-size: 28rpx;
        }

        .text3 {
          width: 368rpx;
          font-weight: 400;
          font-size: 14rpx;
          color: #979797;
          line-height: 18rpx;
          letter-spacing: 1rpx;
          text-align: left;
        }
      }

    }
  }

  .bottom-box {
    // margin-top: var(--page-margin);
    margin-bottom: 60rpx;
    display: flex;
    justify-content: center;
  }
}
