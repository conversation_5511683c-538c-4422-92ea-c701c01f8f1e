package com.dz.ms.product.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.core.constants.NumConstants;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.ms.product.dto.ShelfCampaignRuleProductDTO;
import com.dz.ms.product.dto.ShelfProductSuperscriptDTO;
import com.dz.ms.product.dto.res.ShelfProductAllSuperscriptResDTO;
import com.dz.ms.product.entity.ShelfProductSuperscript;
import com.dz.ms.product.mapper.ShelfProductSuperscriptMapper;
import com.dz.ms.product.service.ShelfCampaignRuleProductService;
import com.dz.ms.product.service.ShelfProductSuperscriptService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 货架商品角标接口
 *
 * @author: fei
 * @date: 2024/12/09 11:32
 */
@Service
public class ShelfProductSuperscriptServiceImpl extends ServiceImpl<ShelfProductSuperscriptMapper, ShelfProductSuperscript> implements ShelfProductSuperscriptService {

    @Resource
    private ShelfProductSuperscriptMapper shelfProductSuperscriptMapper;
    @Resource
    private ShelfCampaignRuleProductService shelfCampaignRuleProductService;

    /**
     * 根据货架商品ID列表/货架id查询货架展示角标列表
     * @param shelfProductIdList 货架商品ID列表
     * @param shelfId 货架ID
     * @return List<ShelfProductSuperscriptDTO>
     */
    @Override
    public List<ShelfProductSuperscriptDTO> getShowSuperscriptList(List<Long> shelfProductIdList, Long shelfId) {
        List<ShelfProductSuperscriptDTO> shelfProductSuperscriptList = new ArrayList<>();
        List<ShelfProductSuperscriptDTO> productSuperscriptList = shelfProductSuperscriptMapper.selectNoPageList(shelfProductIdList,shelfId);
        if(!CollectionUtils.isEmpty(productSuperscriptList)){
            shelfProductSuperscriptList = productSuperscriptList;
        }
        return shelfProductSuperscriptList;
    }

    /**
     * 根据货架商品ID列表/货架id查询货架所有角标列表
     * @param shelfProductIdList 货架商品ID列表
     * @param shelfId 货架ID
     * @return List<ShelfProductAllSuperscriptResDTO>
     */
    @Override
    public List<ShelfProductAllSuperscriptResDTO> getAllProductSuperscriptList(List<Long> shelfProductIdList, Long shelfId) {
        List<ShelfProductAllSuperscriptResDTO> miniShelfProductSuperscriptList = new ArrayList<>();
        List<ShelfProductSuperscriptDTO> allShowSuperscriptList = this.getShowSuperscriptList(null,shelfId);
        //活动角标暂时去除不展示
        //List<ShelfCampaignRuleProductDTO> allRuleSuperscriptList = shelfCampaignRuleProductService.getShelfCampaignRuleProductByShelfId(shelfId, null, NumConstants.ONE);
        List<ShelfCampaignRuleProductDTO> allRuleSuperscriptList = new ArrayList<>();
        for (Long shelfProductId : shelfProductIdList) {
            ShelfProductAllSuperscriptResDTO superscriptMiniResDTO = ShelfProductAllSuperscriptResDTO.builder().shelfProductId(shelfProductId).build();
            List<ShelfProductSuperscriptDTO> showSuperscriptList = allShowSuperscriptList.stream().filter(s -> s.getShelfProductId().equals(shelfProductId)).collect(Collectors.toList());
            List<ShelfCampaignRuleProductDTO> ruleSuperscriptList = allRuleSuperscriptList.stream().filter(s -> s.getShelfProductId().equals(shelfProductId)).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(showSuperscriptList)){
                superscriptMiniResDTO.setShelfProductSuperscriptList(showSuperscriptList);
            }
            if(!CollectionUtils.isEmpty(ruleSuperscriptList)){
                superscriptMiniResDTO.setRuleName(ruleSuperscriptList.get(0).getRuleName());
            }
            miniShelfProductSuperscriptList.add(superscriptMiniResDTO);
        }
        return miniShelfProductSuperscriptList;
    }

    /**
     * 保存货架角标列表
     * @param shelfProductSuperscriptList 入参
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveProductSuperscriptList(List<ShelfProductSuperscriptDTO> shelfProductSuperscriptList,Long shelfId) {
        //根据货架id 删除货架商品角标
        shelfProductSuperscriptMapper.delete(new LambdaQueryWrapper<>(ShelfProductSuperscript.builder().shelfId(shelfId).build()));
        if(!CollectionUtils.isEmpty(shelfProductSuperscriptList)){
            List<ShelfProductSuperscript> list = BeanCopierUtils.convertList(shelfProductSuperscriptList, ShelfProductSuperscript.class);
            this.saveBatch(list);
        }
    }

    /**
     * 根据角标ID删除货架商品角标
     * @param superscriptId 角标ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delProductSuperscriptBySuperscriptId(Long superscriptId) {
        shelfProductSuperscriptMapper.delete(new LambdaQueryWrapper<>(ShelfProductSuperscript.builder().superscriptId(superscriptId).build()));
    }


}
