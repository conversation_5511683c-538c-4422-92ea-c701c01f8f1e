package com.dz.ms.user.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.core.dto.adaptor.ThirdPartyRecordVo;
import com.dz.common.core.dto.basic.SubscribeMsgSendDTO;
import com.dz.common.core.enums.SubscribeMsgEnum;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.adaptor.ThirdPartyReocrdFeginClient;
import com.dz.common.core.fegin.basic.MpMsgFeignClient;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.vo.SubscriptionMsgVo;
import com.dz.ms.user.constants.MJAPIEnum;
import com.dz.ms.user.dto.UserMobileUpdDTO;
import com.dz.ms.user.service.MUJIOpenApiService;
import com.dz.ms.user.service.SubscriptionMsgService;
import com.dz.ms.user.utils.MJSignUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 用户信息
 *
 * @author: Handy
 * @date: 2022/2/4 17:33
 */
@Service
@Slf4j
public class MUJIOpenApiServiceImpl implements MUJIOpenApiService {

    // 接口重试次数
    private static final Integer TRY_TIMES = 5;
    @Value("${muji.api.domain}")
    private String domain;
    @Value("${muji.api.appKey}")
    private String appKey;
    @Value("${muji.api.appSecret}")
    private String appSecret;
    @Value("${card.create.appkey}")
    private String appId;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private ThirdPartyReocrdFeginClient thirdPartyReocrdFeginClient;
    @Autowired
    private MpMsgFeignClient mpMsgFeignClient;
    @Autowired
    private SubscriptionMsgService subscriptionMsgService;

    @Override
    public JSONObject register(String mobile, String openId, String unionId,String channel) {
        JSONObject params = new JSONObject();
        JSONArray bindsAry=new JSONArray();
        params.put("mobile", mobile);
        params.put("platform", "WECHAT");
        if (openId != null){
            JSONObject openIds =  new JSONObject();
            openIds.put("thirdparty_type", "OPENID");
            openIds.put("thirdparty_id", openId);
            openIds.put("thirdparty_appid", appId);
            bindsAry.add(openIds);
        }
        JSONObject unionIds =   new JSONObject();
        unionIds.put("thirdparty_type", "UNIONID");
        unionIds.put("thirdparty_id", unionId);
        unionIds.put("thirdparty_appid", appId);
        bindsAry.add(unionIds);
        params.put("binds", bindsAry);
        if (StringUtils.isNotBlank(channel)){
            params.put("channel_id", channel);
        }
        JSONObject result=null;
        try {
           result = request(MJAPIEnum.CRM_MEMBER_BIND, params);
        } catch (Exception e) {
            log.info("注册业务繁忙，请稍后重新注册:"+e.getMessage());
            throw new BusinessException(ErrorCode.BAD_REQUEST, "注册业务繁忙，请稍后重新注册");
        }
        return result;
    }

    /**
     * @param mobile
     * @return
     */
    @Override
    public JSONObject memberCheck(String mobile) {
        JSONObject params = new JSONObject();
        params.put("mobile", mobile);
        JSONObject result=null;
        try {
           result = request(MJAPIEnum.CRM_MEMBER_CHECK, params);
        } catch (Exception e) {
            log.info("校验手机号是否已注册失败:"+e.getMessage());
            throw new BusinessException(ErrorCode.USER_REGISTER_ERROR, "校验手机号是否已注册失败");
        }
        return result;
    }

    /**
     * @param cardNo
     * @return
     */
    @Override
    public JSONObject memberDetailByCardNo(String cardNo) {
        if (StringUtils.isBlank(cardNo)){
            return null;
        }
        JSONObject params = new JSONObject();
        params.put("member_code", cardNo);
        JSONObject result=null;
        try {
           result = request(MJAPIEnum.CRM_MEMBER_DETAIL, params);
        } catch (Exception e) {
            log.info("会员信息查询失败:"+e.getMessage());
        }
        return result;
    }

    /**
     * @param unionId
     * @return
     */
    @Override
    public JSONObject memberDetailByUnionId(String unionId) {
        JSONObject params = new JSONObject();
        params.put("platform", "WECHAT");
        params.put("thirdparty_type", "UNIONID");
        params.put("thirdparty_id", unionId);
        JSONObject result=null;
        try {
            result = request(MJAPIEnum.CRM_MEMBER_DETAIL, params);
        } catch (Exception e) {
            log.info("会员信息查询失败:"+e.getMessage());
        }
        return result;
    }

    @Override
    public JSONObject sendMobileCode(String mobile) {
        JSONObject params = new JSONObject();
        params.put("mobile", mobile);
        JSONObject result=null;
        try {
           result = request(MJAPIEnum.SEND_MOBILE_CODE, params);
        } catch (Exception e) {
            log.info("发送短信验证码失败:"+e.getMessage());
            throw new BusinessException(ErrorCode.BAD_REQUEST, "发送短信验证码失败");
        }
        return result;
    }

    @Override
    public JSONObject mobileUpdate(UserMobileUpdDTO userMobileUpdDTO) {
        JSONObject params = new JSONObject();
        params.put("member_code", userMobileUpdDTO.getMemberCode());
        params.put("old_mobile", userMobileUpdDTO.getOleMobile());
        params.put("new_mobile", userMobileUpdDTO.getNewMobile());
        params.put("sms_code", userMobileUpdDTO.getSmsCode());
        JSONObject result=null;
        try {
            result = request(MJAPIEnum.CRM_MEMBER_CHANGE_MOBILE, params);
        } catch (Exception e) {
            log.info("会员手机号变更失败:"+e.getMessage());
            throw new BusinessException(ErrorCode.USER_REGISTER_ERROR, "会员手机号变更失败");
        }
        return result;
    }

    @Override
    public JSONObject updateMemberInfo(String memberCode, String nickName, String birthday, Integer gender, String avatar, String province, String city) {
        JSONObject params = new JSONObject();
        params.put("member_code", memberCode);
        if (StringUtils.isNotBlank(nickName)){
            params.put("nickname", nickName);
        }
        if (StringUtils.isNotBlank(birthday)){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
            SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
            String bir="";
            try {
                if (birthday.contains("/")){
                    Date date = sdf.parse(birthday);
                    bir = sdf1.format(date);
                }else{
                    bir = birthday;
                }
            } catch (Exception e) {
                log.info("生日格式错误:"+e.getMessage());
                throw new BusinessException(ErrorCode.BAD_REQUEST, "生日格式错误");
            }
            params.put("birthday", bir);
        }
        if (gender != null){
            params.put("gender", gender);
        }
        if (StringUtils.isNotBlank(avatar)){
            params.put("avatar", avatar);
        }
        if (StringUtils.isNotBlank(province)){
            params.put("province", province);
        }
        if (StringUtils.isNotBlank(city)){
            params.put("city", city);
        }
        JSONObject result=null;
        try {
           result = request(MJAPIEnum.UPDATE_MEMBER_INFO, params);
        } catch (Exception e) {
            log.info("会员信息更新失败:"+e.getMessage());
            throw new BusinessException(ErrorCode.BAD_REQUEST, "会员信息更新失败");
        }
        return result;
    }

    @Override
    public JSONObject cancelMemberInfo(String memberCode, String smsCode, Integer[] cancelReason, String remark) {
        JSONObject params = new JSONObject();
        params.put("member_code", memberCode);
        params.put("sms_code", smsCode);
        params.put("cancel_reason", cancelReason);
        if (StringUtils.isNotBlank(remark)){
            params.put("remark", remark);
        }else{
            params.put("remark", "无");
        }
        JSONObject result=null;
        try {
           result = request(MJAPIEnum.CANCEL_MEMBER_INFO, params);
        } catch (Exception e) {
            log.info("会员账号注销失败:"+e.getMessage());
            throw new BusinessException(ErrorCode.USER_LOGOFF_ERROR, "会员账号注销失败");
        }
        return result;
    }

    @Override
    public JSONObject getMemberPrCode(String memberCode) {
        if (StringUtils.isBlank(memberCode)){
            return null;
        }
        JSONObject params = new JSONObject();
        params.put("member_code", memberCode);
        JSONObject result=null;
        try {
           result = request(MJAPIEnum.GET_MEMBER_PR_CODE, params);
        } catch (Exception e) {
            log.info("获取会员动态码失败:"+e.getMessage());
            throw new BusinessException(ErrorCode.BAD_REQUEST, "获取会员动态码失败");
        }
        return result;
    }

    @Override
    public JSONObject addMemberPoints(Long userId,String memberCode,String channel,Integer bonusAmount, String reason) {
        //六位随机数字
        String code = String.valueOf((int)((Math.random()*9+1)*100000));
        //当前时间+365天
        Date expiredAt=new Date(new Date().getTime() + 365L * 24 * 60 * 60 * 1000);
        SimpleDateFormat sdfd = new SimpleDateFormat("yyyy-MM-dd");
        JSONObject params = new JSONObject();
        params.put("member_code", memberCode);
        params.put("channel", channel);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        params.put("out_sn", sdf.format(new Date())+code);
        params.put("bonus_amount", bonusAmount);
        params.put("expired_at", sdfd.format(expiredAt));
        params.put("reason", reason);
        JSONObject result = null;
        try {
            result = request(MJAPIEnum.ADD_MEMBER_POINTS, params);
        } catch (Exception e) {
            log.info("会员积分增加失败:" + e.getMessage());
            throw new BusinessException(ErrorCode.BAD_REQUEST, "会员积分增加失败");

        }
        if (null != result && result.containsKey("member_code")){
            SimpleDateFormat sdfdDateFormat =  new SimpleDateFormat("yyyy年MM月dd日 HH:mm");
            //发送订阅消息
            SubscribeMsgSendDTO subscribeMsgSendDTO = new SubscribeMsgSendDTO();
            subscribeMsgSendDTO.setMsgCode(SubscribeMsgEnum.POINTS_GET.getMsgCode());
            List<Long> uids = new ArrayList<>();
            if (null != userId){
                uids.add(userId);
            }else{
                uids.add(SecurityContext.getUser().getUid());
            }
            subscribeMsgSendDTO.setUids(uids);
            String[] content = new String[3];
            content[0]=sdfdDateFormat.format(new Date());
//            if (channel.equals("MINI_INTERACT_TASK")){
//                if (StringUtils.isNotBlank(reason)){
//                    content[1]=reason;
//                }else{
//                    content[1]="互动任务";
//                }
//            }
            content[1]=bonusAmount+"";
            //查询我的可用积分
//            JSONObject memberPoints=memberPointsCount(memberCode);
//            if (null != memberPoints && memberPoints.containsKey("bonus_amount")){
//                content[3]=""+memberPoints.getInteger("bonus_amount");
//            }else{
//                content[3]="0";
//            }
            if (StringUtils.isNotBlank(reason)){
                    content[2]=reason+"任务完成获得积分";
                }else{
                    content[2]="任务完成获得积分";
                }
            subscribeMsgSendDTO.setContent(content);
            mpMsgFeignClient.sendMiniappSubscribeMsg(subscribeMsgSendDTO, SecurityContext.getUser().getTenantId());
            //记录订阅消息
            SubscriptionMsgVo subscriptionMsgVo = new SubscriptionMsgVo();
            List<String> msgCode = new ArrayList<>();
            msgCode.add("getPoints");
            subscriptionMsgVo.setMsgCode(msgCode);
            subscriptionMsgVo.setSendDesc("互动任务积分到账提醒："+bonusAmount+"积分");
            try {
                subscriptionMsgService.add(subscriptionMsgVo);
            }catch (Exception e){
                log.info("记录订阅消息失败:"+e.getMessage());
            }

        }
        return result;
    }

    @Override
    public JSONObject addMemberPoints(String memberCode, String channel, Integer bonusAmount, String reason, String outSn) {
        //当前时间+365天
        Date expiredAt = new Date(new Date().getTime() + 365L * 24 * 60 * 60 * 1000);
        SimpleDateFormat sdfd = new SimpleDateFormat("yyyy-MM-dd");
        JSONObject params = new JSONObject();
        params.put("member_code", memberCode);
        params.put("channel", channel);
        params.put("out_sn", outSn);
        params.put("bonus_amount", bonusAmount);
        params.put("expired_at", sdfd.format(expiredAt));
        params.put("reason", reason);
        JSONObject result = null;
        try {
            result = request(MJAPIEnum.ADD_MEMBER_POINTS, params);
        } catch (Exception e) {
            log.info("会员积分增加失败:" + e.getMessage());
            throw new BusinessException(ErrorCode.BAD_REQUEST, "会员积分增加失败");

        }
        return result;
    }

    @Override
    public JSONObject deductMemberPoints(String memberCode, String channel, Integer bonusAmount, String reason) {
        //六位随机数字
        String code = String.valueOf((int) ((Math.random() * 9 + 1) * 100000));
        //当前时间+365天
        Date expiredAt = new Date(new Date().getTime() + 365L * 24 * 60 * 60 * 1000);
        SimpleDateFormat sdfd = new SimpleDateFormat("yyyy-MM-dd");
        JSONObject params = new JSONObject();
        params.put("member_code", memberCode);
        params.put("channel", channel);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        params.put("out_sn", sdf.format(new Date()) + code);
        params.put("bonus_amount", bonusAmount);
        params.put("reason", reason);
        JSONObject result = null;
        try {
            result = request(MJAPIEnum.DEDUCT_MEMBER_POINTS, params);
        } catch (Exception e) {
            log.info("会员积分扣减失败:" + e.getMessage());
            throw new BusinessException(ErrorCode.BAD_REQUEST, "会员积分扣减失败");

        }
        return result;
    }

    @Override
    public JSONObject deductMemberPoints(String memberCode, String channel, Integer bonusAmount, String reason, String outSn) {
        JSONObject params = new JSONObject();
        params.put("member_code", memberCode);
        params.put("channel", channel);
        params.put("out_sn", outSn);
        params.put("bonus_amount", bonusAmount);
        params.put("reason", reason);
        JSONObject result = null;
        try {
            result = request(MJAPIEnum.DEDUCT_MEMBER_POINTS, params);
        } catch (Exception e) {
            log.info("会员积分扣减失败:" + e.getMessage());
            throw new BusinessException(ErrorCode.BAD_REQUEST, "会员积分扣减失败");

        }
        return result;
    }

    @Override
    public JSONObject memberPointsList(String memberCode, String start_time, String end_time, Integer page, Integer pageSize) {
        if (StringUtils.isBlank(memberCode)){
            return null;
        }
        JSONObject params = new JSONObject();
        params.put("member_code", memberCode);
        params.put("start_time", start_time);
        params.put("end_time", end_time);
        params.put("page", page);
        params.put("page_size", pageSize);
        JSONObject result=null;
        try {
           result = request(MJAPIEnum.MEMBER_POINTS_LIST, params);
        } catch (Exception e) {
            log.info("会员积分流水列表失败:"+e.getMessage());
            throw new BusinessException(ErrorCode.BAD_REQUEST, "会员积分流水列表失败");
        }
        return result;
    }

    @Override
    public JSONObject memberPointsDetail(String memberCode, String bonusSn) {
        if (StringUtils.isBlank(memberCode)){
            return null;
        }
        JSONObject params = new JSONObject();
        params.put("member_code", memberCode);
        params.put("bonus_sn", bonusSn);
        JSONObject result=null;
        try {
            result = request(MJAPIEnum.MEMBER_POINTS_DETAIL, params);
            if(Objects.isNull(result)){
                throw new BusinessException(ErrorCode.INTERNAL_ERROR, "数据加载中，请稍后再试");
            }
        } catch (Exception e) {
            log.info("会员积分流水详情查询失败:"+e.getMessage());
            throw new BusinessException(ErrorCode.INTERNAL_ERROR, "数据加载中，请稍后再试");
        }
        return result;
    }

    @Override
    public JSONObject memberPointsCount(String memberCode) {
        if (StringUtils.isBlank(memberCode)){
            return null;
        }
        JSONObject params = new JSONObject();
        params.put("member_code", memberCode);
        JSONObject result=null;
        try {
           result = request(MJAPIEnum.MEMBER_POINTS_COUNT, params);
        } catch (Exception e) {
            log.info("会员积分数据查询失败:"+e.getMessage());
            throw new BusinessException(ErrorCode.BAD_REQUEST, "会员积分数据查询失败");
        }
        return result;
    }

    @Override
    public JSONObject memberMileageList(String memberCode, String start_time, String end_time, Integer page, Integer pageSize) {
        if (StringUtils.isBlank(memberCode)){
            return null;
        }
        JSONObject params = new JSONObject();
        params.put("member_code", memberCode);
        params.put("start_time", start_time);
        params.put("end_time", end_time);
        params.put("page", page);
        params.put("page_size", pageSize);
        JSONObject result=null;
        try {
           result = request(MJAPIEnum.MEMBER_MILEAGE_LIST, params);
        } catch (Exception e) {
            log.info("会员里程流水列表失败:"+e.getMessage());
            throw new BusinessException(ErrorCode.BAD_REQUEST, "会员里程流水列表失败");
        }
        return result;
    }
    
    @Override
    public JSONObject memberMileageDetail(String memberCode, String mileageSn) {
        if (StringUtils.isBlank(memberCode)){
            return null;
        }
        JSONObject params = new JSONObject();
        params.put("member_code", memberCode);
        params.put("mileage_sn", mileageSn);
        JSONObject result=null;
        try {
           result = request(MJAPIEnum.MEMBER_MILEAGE_DETAIL, params);
            if(Objects.isNull(result)){
                throw new BusinessException(ErrorCode.INTERNAL_ERROR, "数据加载中，请稍后再试");
            }
        } catch (Exception e) {
            log.info("会员里程流水详情查询失败:"+e.getMessage());
            throw new BusinessException(ErrorCode.INTERNAL_ERROR, "数据加载中，请稍后再试");
        }
        return result;
    }

    @Override
    public JSONObject activityCouponList(String memberCode) {
        if (StringUtils.isBlank(memberCode)){
            return null;
        }
        JSONObject params = new JSONObject();
        params.put("member_code", memberCode);
        params.put("platform", "WECHAT");
        JSONObject result=null;
        try {
           result = request(MJAPIEnum.ACTIVITY_COUPON_LIST, params);
        } catch (Exception e) {
            log.info("【领券活动】活动列表失败:"+e.getMessage());
            throw new BusinessException(ErrorCode.BAD_REQUEST, "【领券活动】活动列表失败");
        }
        return result;
    }

    @Override
    public JSONObject activityCouponReceive(String memberCode,String activityId) {
        if (StringUtils.isBlank(memberCode)){
            return null;
        }
        JSONObject params = new JSONObject();
        params.put("member_code", memberCode);
        params.put("activity_id", activityId);
        params.put("platform", "WECHAT");
        JSONObject result = null;
        try {
            result = request(MJAPIEnum.ACTIVITY_COUPON_RECEIVE, params);
        } catch (Exception e) {
            log.info("【领券活动】活动领取失败:" + e.getMessage());
            throw new BusinessException(ErrorCode.BAD_REQUEST, "【领券活动】活动领取失败");
        }
        return result;
    }

    @Override
    public JSONObject activityQrCodeReceive(String memberCode, String qrcodeId, String qrcodeSn) {
        if (StringUtils.isBlank(memberCode)){
            return null;
        }
        JSONObject params = new JSONObject();
        params.put("member_code", memberCode);
        params.put("qrcode_id", qrcodeId);
        params.put("qrcode_sn", qrcodeSn);
        params.put("platform", "WECHAT");
        JSONObject result = null;
        try {
            result = request(MJAPIEnum.ACTIVITY_QRCODE_RECEIVE, params);
        } catch (Exception e) {
            log.info("【扫码活动】扫码上报人群:" + e.getMessage());
            throw new BusinessException(ErrorCode.BAD_REQUEST, "【扫码活动】扫码上报人群");
        }
        return result;
    }

    @Override
    public JSONObject memberCouponList(String memberCode, Integer type, Integer status, Integer page, Integer pageSize) {
        if (StringUtils.isBlank(memberCode)){
            return null;
        }
        JSONObject params = new JSONObject();
        params.put("member_code", memberCode);
        if (type != null) {
            params.put("type", type);
        }
        params.put("status", status);
        params.put("page", page);
        params.put("page_size", pageSize);
        JSONObject result=null;
        try {
           result = request(MJAPIEnum.MEMBER_COUPON_LIST, params);
        } catch (Exception e) {
            log.info("会员优惠券列表失败:"+e.getMessage());
            throw new BusinessException(ErrorCode.BAD_REQUEST, "会员优惠券列表失败");
        }
        return result;
    }

    @Override
    public JSONObject memberCouponDetails(String stockId) {
        JSONObject params = new JSONObject();
        params.put("stock_id", stockId);
        JSONObject result=null;
        try {
           result = request(MJAPIEnum.MEMBER_COUPON_STOCK_DETAILS, params);
        } catch (Exception e) {
            log.info("优惠券批次详情失败:"+e.getMessage());
            throw new BusinessException(ErrorCode.BAD_REQUEST, "优惠券批次详情失败");
        }
        return result;
    }

    @Override
    public JSONObject memberCouponReceive(String memberCode, String stockId) {
        if (StringUtils.isBlank(memberCode)){
            return null;
        }
        JSONObject params = new JSONObject();
        params.put("member_code", memberCode);
        params.put("stock_id", stockId);
        JSONObject result=null;
        try {
           result = request(MJAPIEnum.MEMBER_COUPON_RECEIVE, params);
        } catch (Exception e) {
            log.info("会员优惠券领取失败:"+e.getMessage());
            throw new BusinessException(ErrorCode.BAD_REQUEST, "会员优惠券领取失败");
        }
        return result;
    }

    @Override
    public JSONObject memberCouponCodeDetails(String memberCode, String stockId, String couponCode) {
        if (StringUtils.isBlank(memberCode)){
            return null;
        }
        JSONObject params = new JSONObject();
        params.put("member_code", memberCode);
        params.put("stock_id", stockId);
        params.put("coupon_code", couponCode);
        JSONObject result=null;
        try {
           result = request(MJAPIEnum.MEMBER_COUPON_CODE_DETAILS, params);
        } catch (Exception e) {
            log.info("会员券码详情失败:"+e.getMessage());
            throw new BusinessException(ErrorCode.BAD_REQUEST, "会员券码详情失败");
        }
        return result;
    }

    @Override
    public JSONObject memberOrderList(String memberCode, String start_time, String end_time, Integer type, Integer page, Integer pageSize) {
        if (StringUtils.isBlank(memberCode)){
            return null;
        }
        JSONObject params = new JSONObject();
        params.put("member_code", memberCode);
        params.put("start_time", start_time);
        params.put("end_time", end_time);
        params.put("type", type);
        params.put("page", page);
        params.put("page_size", pageSize);
        JSONObject result=new JSONObject();
        try {
           result = request(MJAPIEnum.MEMBER_ORDER_LIST, params);
        } catch (Exception e) {
            log.info("会员订单列表失败:"+e.getMessage());
            throw new BusinessException(ErrorCode.BAD_REQUEST, "会员订单列表失败");
        }
        return result;
    }

    @Override
    public JSONObject memberOrderTicketDetails(String memberCode, String orderSn) {
        if (StringUtils.isBlank(memberCode)){
            return null;
        }
        JSONObject params = new JSONObject();
        params.put("member_code", memberCode);
        params.put("order_sn", orderSn);
        JSONObject result=null;
        try {
            result = request(MJAPIEnum.MEMBER_ORDER_TICKET_DETAILS, params);
        } catch (Exception e) {
            log.info("会员订单-小票详情失败:"+e.getMessage());
            throw new BusinessException(ErrorCode.BAD_REQUEST, "会员订单-小票详情失败");
        }
        return result;
    }

    @Override
    public JSONObject memberOrderDetails(String memberCode, String orderSn) {
        if (StringUtils.isBlank(memberCode)){
            return null;
        }
        JSONObject params = new JSONObject();
        params.put("member_code", memberCode);
        params.put("order_sn", orderSn);
        JSONObject result=null;
        try {
           result = request(MJAPIEnum.MEMBER_ORDER_DETAILS, params);
        } catch (Exception e) {
            log.info("会员订单详情失败:"+e.getMessage());
            throw new BusinessException(ErrorCode.BAD_REQUEST, "会员订单详情失败");
        }
        return result;
    }

    /**
     * 查看用户开卡状态
     *
     * @param memberCode
     * @return
     */
    @Override
    public JSONObject openCardStatus(String memberCode) {
        if (StringUtils.isBlank(memberCode)){
            return null;
        }
        JSONObject params = new JSONObject();
        params.put("member_code", memberCode);
        JSONObject result=null;
        try {
           result = request(MJAPIEnum.OPEN_CARD_STATUS, params);
        } catch (Exception e) {
            log.info("查看用户开卡状态失败:"+e.getMessage());
            throw new BusinessException(ErrorCode.BAD_REQUEST, "查看用户开卡状态失败");
        }
        return result;
    }

    /**
     * 即将过期积分列表
     *
     * @param memberCode
     * @param page
     * @param pageSize
     * @return
     */
    @Override
    public JSONObject recentExpirePoints(String memberCode, Integer page, Integer pageSize) {
        if (StringUtils.isBlank(memberCode)){
            return null;
        }
        JSONObject params = new JSONObject();
        params.put("member_code", memberCode);
        params.put("page", page);
        params.put("page_size", pageSize);
        JSONObject result=new JSONObject();
        try {
            result = request(MJAPIEnum.RECENT_EXPIRE_POINTS, params);
        } catch (Exception e) {
            log.info("会员订单列表失败:"+e.getMessage());
            throw new BusinessException(ErrorCode.BAD_REQUEST, "会员订单列表失败");
        }
        return result;
    }

    /**
     * 历史过期积分列表
     *
     * @param memberCode
     * @param startTime
     * @param endTime
     * @param page
     * @param pageSize
     * @return
     */
    @Override
    public JSONObject historyExpirePoints(String memberCode, String startTime, String endTime, Integer page, Integer pageSize) {
        if (StringUtils.isBlank(memberCode)){
            return null;
        }
        JSONObject params = new JSONObject();
        params.put("member_code", memberCode);
        params.put("start_time", startTime);
        params.put("end_time", endTime);
        params.put("page", page);
        params.put("page_size", pageSize);
        JSONObject result=new JSONObject();
        try {
            result = request(MJAPIEnum.HISTORY_EXPIRE_POINTS, params);
        } catch (Exception e) {
            log.info("会员订单列表失败:"+e.getMessage());
            throw new BusinessException(ErrorCode.BAD_REQUEST, "会员订单列表失败");
        }
        return result;
    }

    /**
     * CRM接口统一请求方法
     *
     * @param postBody
     */
    public JSONObject request(MJAPIEnum apiEnum, JSONObject postBody) {
        boolean isPost = HttpMethod.POST.equals(apiEnum.getMethod());
        return request(apiEnum.getMethod().toString(), apiEnum.getUri(), JSONObject.toJSONString(postBody),apiEnum.getDesc(),isPost,true,1,apiEnum.getNum());
    }

    /***
     * 计算签名
     * @param method HTTP请求方法，如：POST GET
     * @param address HTTP请求地址：如: /user/query
     * @param body HTTP请求数据
     * @return JSON
     */
    public JSONObject request(String method, String address, String body,String desc, boolean isPost, boolean isRetry, Integer tryTime,Integer num) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        HttpHeaders headers = new HttpHeaders();
        Date date = new Date();
        headers.add("x-very-timestamp", sdf.format(date));
        headers.add("x-very-app-id", appKey);
        Map<String, Object> header=new HashMap<>();
        header.put("x-very-timestamp", sdf.format(date));
        header.put("x-very-app-id", appKey);
        //获取签名
        String sign= MJSignUtils.getSign(method, address, header, body, appSecret, MJSignUtils.Type.md5);
        headers.add("Authorization", sign);
        String requestUrl = domain + address;
        log.info("CRM请求 {} postBody:{} url:{}", desc, body, address);
        JSONObject json = null;
        Date startTime = null;
        Date endTime = null;
        try {
            if (isPost) {
                headers.setContentType(MediaType.APPLICATION_JSON);
                HttpEntity<String> requestEntity = new HttpEntity<String>(body, headers);
                startTime= new Date();
                json = restTemplate.postForObject(requestUrl, requestEntity, JSONObject.class);
                endTime= new Date();
            } else {
                headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
                HttpEntity<String> requestEntity = new HttpEntity<String>(headers);
                startTime= new Date();
                json = restTemplate.exchange(requestUrl, HttpMethod.GET, requestEntity, JSONObject.class).getBody();
                endTime= new Date();
            }
            //计算请求耗时
            long time = endTime.getTime() - startTime.getTime();
            ThirdPartyRecordVo recordVo = new ThirdPartyRecordVo();
            recordVo.setApiDesc(desc);
            recordVo.setApiUrl(requestUrl);
            recordVo.setParam(body);
            recordVo.setResult(json.toJSONString());
            recordVo.setCreateTime(new Date());
            recordVo.setStatus(1);
            recordVo.setRequestTime(time);
            recordVo.setNum(num);
            try {
                thirdPartyReocrdFeginClient.thridPartRecord(recordVo);
            }catch (Exception eq){
                log.error("thirdPartyReocrdFeginClient请求异常:"+eq.getMessage());
            }
        } catch (RestClientException e) {
            String message = e.getMessage();
            if (null != message) {
                //第三方注销单独做异常处理
                if (message.contains("MemberStatusError") || message.contains("MemberNotExist")){
                    log.error("CRM请求 {} 异常，重试5次均失败", desc, e);
                    ThirdPartyRecordVo recordVo = new ThirdPartyRecordVo();
                    recordVo.setApiDesc(desc);
                    recordVo.setApiUrl(requestUrl);
                    recordVo.setParam(body);
                    recordVo.setFailDesc("重试5次均失败,失败原因:"+message);
                    recordVo.setCreateTime(new Date());
                    recordVo.setStatus(0);
                    recordVo.setNum(num);
                    try {
                        thirdPartyReocrdFeginClient.thridPartRecord(recordVo);
                    }catch (Exception eq){
                        log.error("thirdPartyReocrdFeginClient请求异常:"+eq.getMessage());
                    }
                    json = new JSONObject();
                    if (message.contains("MemberStatusError")){
                        json.put("code", "MemberStatusError");
                    }
                    if (message.contains("MemberNotExist")){
                        json.put("code", "MemberNotExist");
                    }
                    return json;
                }
                //注册单独做异常处理
                if (message.contains("UnionidBindsError")){
                    log.error("CRM请求 {} 异常，重试5次均失败", desc, e);
                    ThirdPartyRecordVo recordVo = new ThirdPartyRecordVo();
                    recordVo.setApiDesc(desc);
                    recordVo.setApiUrl(requestUrl);
                    recordVo.setParam(body);
                    recordVo.setFailDesc("重试5次均失败,失败原因:"+message);
                    recordVo.setCreateTime(new Date());
                    recordVo.setStatus(0);
                    recordVo.setNum(num);
                    try {
                        thirdPartyReocrdFeginClient.thridPartRecord(recordVo);
                    }catch (Exception eq){
                        log.error("thirdPartyReocrdFeginClient请求异常:"+eq.getMessage());
                    }
                    json = new JSONObject();
                    json.put("code", "UnionidBindsError");
                    return json;
                }
                if(message.contains("MobileBindsError")){
                    log.error("CRM请求 {} 异常，重试5次均失败", desc, e);
                    ThirdPartyRecordVo recordVo = new ThirdPartyRecordVo();
                    recordVo.setApiDesc(desc);
                    recordVo.setApiUrl(requestUrl);
                    recordVo.setParam(body);
                    recordVo.setFailDesc("重试5次均失败,失败原因:"+message);
                    recordVo.setCreateTime(new Date());
                    recordVo.setStatus(0);
                    recordVo.setNum(num);
                    try {
                        thirdPartyReocrdFeginClient.thridPartRecord(recordVo);
                    }catch (Exception eq){
                        log.error("thirdPartyReocrdFeginClient请求异常:"+eq.getMessage());
                    }
                    json = new JSONObject();
                    json.put("code", "MobileBindsError");
                    return json;
                }
                if (isRetry) {
                    if (tryTime <= TRY_TIMES) {
                        log.error("CRM请求 {} 异常，重试中 第: {} 次", desc, tryTime, e);
                        return request(method, address, body, desc, isPost, true, ++tryTime,num);
                    } else {
                        log.error("CRM请求 {} 异常，重试5次均失败", desc, e);
                        ThirdPartyRecordVo recordVo = new ThirdPartyRecordVo();
                        recordVo.setApiDesc(desc);
                        recordVo.setApiUrl(requestUrl);
                        recordVo.setParam(body);
                        recordVo.setFailDesc("重试5次均失败,失败原因:"+message);
                        recordVo.setCreateTime(new Date());
                        recordVo.setStatus(0);
                        recordVo.setNum(num);
                        try {
                            thirdPartyReocrdFeginClient.thridPartRecord(recordVo);
                        }catch (Exception eq){
                            log.error("thirdPartyReocrdFeginClient请求异常:"+eq.getMessage());
                        }
                        try {
                            if(StringUtils.equals(MJAPIEnum.SEND_MOBILE_CODE.getDesc(),desc)){
                                json = new JSONObject();
                                if(message.contains("InvalidParameter") || message.contains("MobileNotExist")){
                                    json.put("code", "InvalidParameter");
                                }
                            }
                        } catch (Exception descE){
                            log.info("CRM请求 {} message转换异常:{}", desc, message);
                        }
                    }
                }
            } else {
                log.error("CRM请求 {} 异常", desc, message);
                ThirdPartyRecordVo recordVo = new ThirdPartyRecordVo();
                recordVo.setApiDesc(desc);
                recordVo.setApiUrl(requestUrl);
                recordVo.setParam(body);
                recordVo.setFailDesc("请求异常,异常原因:"+message);
                recordVo.setCreateTime(new Date());
                recordVo.setStatus(0);
                recordVo.setNum(num);
                try {
                    thirdPartyReocrdFeginClient.thridPartRecord(recordVo);
                }catch (Exception eq){
                    log.error("thirdPartyReocrdFeginClient请求异常:"+eq.getMessage());
                }
            }
        }
        log.info("CRM请求 {} result:{}", desc, null == json ? "无数据" : json.toJSONString());
        return json;
    }
}
