package com.dz.ms.adaptor.mqreceiver;

import com.alibaba.fastjson.JSONObject;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.DateUtils;
import com.dz.ms.adaptor.dto.CrmPointsSyncParamDTO;
import com.dz.ms.adaptor.dto.MqCrmPointsSyncMessageDTO;
import com.dz.ms.adaptor.dto.ThirdPartRecordMqDTO;
import com.dz.ms.adaptor.entity.ThirdPartyRecord;
import com.dz.ms.adaptor.mapper.ThirdPartyRecordMapper;
import com.dz.ms.adaptor.service.PointsSubscriptionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * crm接口记录
 */
@Component
@Slf4j
public class ThirdPartRecordReceiver {

	@Resource
	private ThirdPartyRecordMapper thirdPartyRecordMapper;

	/**
	 * 消费者接收消息并消费消息
	 * 交换机、队列不存在的话，以下注解可以自动创建交换机和队列
	 */
	@RabbitListener(bindings = @QueueBinding(value = @Queue(value = "adaptor.crmapi.queue", durable = "true"), exchange = @Exchange(value = "adaptor.crmapi", durable = "true", type = "topic"), key = "adaptor_crmapi"))
	@RabbitHandler
	public void onReadMessage(@Payload String messageStr) {
		try {
			ThirdPartRecordMqDTO message = JSONObject.parseObject(messageStr,ThirdPartRecordMqDTO.class);
			Date now = message.getCreateTime();
			log.info("日期:{},crm接口记录,开始消费.[ThirdPartRecordMqDTO={}]", now, messageStr);
			CurrentUserDTO currentUser = new CurrentUserDTO();
			currentUser.setTenantId(message.getTenantId());
			SecurityContext.setUser(currentUser);
			if(null == message.getThirdPartyRecord()) {
				log.error("日期:{},crm接口记录error，参数为空", now);
			}
			ThirdPartyRecord thirdPartyRecord = message.getThirdPartyRecord();
			thirdPartyRecord.setTenantId(message.getTenantId());
			for (int i = 0; i < 5; i++) {
				try {
					int a= thirdPartyRecordMapper.inserByTable(message.getTaleName(),thirdPartyRecord);
					if (a>0){
						break;
					}
				}catch (Exception e){
					log.info("第三方接口:"+thirdPartyRecord.getApiDesc()+",请求记录插入失败:"+e.getMessage());
				}
			}
		} catch (Exception e){
			log.error("crm接口记录mq消费Exception:" +  messageStr, e);
		}
	}

}
