package com.dz.common.core.fegin.sales;


import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.adaptor.SiftEnrollDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/7
 */
@FeignClient(name = ServiceConstant.SALES_SERVICE_NAME, contextId = "CampaignEnrollFeignClient")
public interface CampaignEnrollFeignClient {

    /**
     * 定时任务 根据渠道筛选报名信息
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/remote/enroll/channel/sift")
    public Result<Boolean> siftCampaignEnrollByChannel(@RequestBody SiftEnrollDTO param);


    /**
     * 定时任务 活动报名成功后推送订阅消息
     *
     * @param
     * @return
     */
    @PostMapping(value = "/remote/enroll/success/pushMsg")
    public Result<Boolean> enrollSuccessPushMsgJob(@RequestParam("campaignCode") String campaignCode);


    /**
     * 定时任务 活动报名失败后推送第二轮报名订阅消息
     *
     * @param
     * @return
     */
    @PostMapping(value = "/remote/enroll/fail/pushMsg")
    public Result<Boolean> enrollFailPushMsgJob(@RequestParam("campaignCode") String campaignCode);
}
