package com.dz.ms.sales.controller;


import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.dto.DownloadCenterDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.fegin.basic.ReportDownloadFeignClient;
import com.dz.common.core.fegin.sales.SignInDetailClient;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.ms.sales.config.Globals;
import com.dz.ms.sales.dto.*;
import com.dz.ms.sales.service.SignInUserDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/9
 */
@Slf4j
@Api(tags = "活动签到接口")
@RestController
public class SignInUserController implements SignInDetailClient {

    @Resource
    private SignInUserDetailService signInUserDetailService;
    @Resource
    private ReportDownloadFeignClient reportDownloadFeignClient;


    @ApiOperation("打卡活动数据汇总")
    @GetMapping(value = "/crm/signIn/user/info")
    public Result<CrmSignInUserInfoDTO> getSignInUserInfo() {
        Result<CrmSignInUserInfoDTO> result = new Result<>();
        CrmSignInUserInfoDTO dto = signInUserDetailService.getSignInUserInfo();
        result.setData(dto);
        return result;
    }

    @ApiOperation("打卡活动列表")
    @GetMapping(value = "/crm/signIn/user/list")
    public Result<PageInfo<CrmSignInUserDTO>> getSignInUserList(@ModelAttribute CrmSignInUserParamDTO param) {
        Result<PageInfo<CrmSignInUserDTO>> result = new Result<>();
        PageInfo<CrmSignInUserDTO> page = signInUserDetailService.getSignInUserList(param);
        result.setData(page);
        return result;
    }


    /**
     * 导出订单列表
     *
     * @return
     */
    @PostMapping(value = "/sales/sign_in_detail/export_list")
    public Result<Void> exportList(@RequestBody DownloadAddParamDTO exportParam) {
        CurrentUserDTO user = SecurityContext.getUser();
        Globals.downloadThreadPool.execute(() -> {
            try {
                SecurityContext.setUser(user);
                signInUserDetailService.exportSignInDetailList(exportParam);
            } catch (Exception e) {
                log.error("导出错误", e);
                DownloadCenterDTO downloadCenterDTO = new DownloadCenterDTO();
                downloadCenterDTO.setId(exportParam.getDownloadCenterId());
                downloadCenterDTO.setErrorDesc(e.getMessage());
                downloadCenterDTO.setState(2);
                reportDownloadFeignClient.updateDownloadCenter(downloadCenterDTO);
            }
        });
        return new Result<>();
    }

    @ApiOperation("签到提醒任务执行完成")
    @PostMapping(value = "/sales/sign_in_detail/pushMsg")
    public Result<Void> signInPushMsgJob(@RequestParam("campaignCode") String campaignCode) {
        signInUserDetailService.signInPushMsgJob(campaignCode);
        return new Result<>();
    }

    @ApiOperation("用户签到失败任务执行完成")
    @PostMapping(value = "/sales/sign_in_detail/fail")
    public Result<Void> signInUserFailJob(@RequestParam("campaignCode") String campaignCode) {
        signInUserDetailService.signInUserFailJob(campaignCode);
        return new Result<>();
    }


    @ApiOperation("查询用户是否开启打卡")
    @GetMapping(value = "/app/signIn/user/open")
    public Result<OpenSignInDTO> isOpenSignIn(@RequestParam("campaignCode") String campaignCode) {
        Result<OpenSignInDTO> result = new Result<>();
        OpenSignInDTO openSignIn = signInUserDetailService.isOpenSignIn(campaignCode, SecurityContext.getUser().getUid());
        result.setData(openSignIn);
        return result;
    }

    @ApiOperation("开启打卡")
    @PostMapping(value = "/app/signIn/user/open/signIn")
    public Result<Long> openSignIn(@RequestBody @Validated SignInUserOpenDTO param) {
        Result<Long> result = new Result<>();
        Long openSignIn = signInUserDetailService.openSignIn(SecurityContext.getUser().getUid(), param);
        result.setData(openSignIn);
        return result;
    }

    @ApiOperation("查询当前打卡进度")
    @GetMapping(value = "/app/signIn/user/speed")
    public Result<SignInRecordDTO> querySignInSpeed(@RequestParam("campaignCode") String campaignCode) {
        Result<SignInRecordDTO> result = new Result<>();
        SignInRecordDTO signIn = signInUserDetailService.querySignInSpeed(campaignCode, SecurityContext.getUser().getUid());
        result.setData(signIn);
        return result;
    }

    @ApiOperation("用户打卡")
    @PostMapping(value = "/app/signIn/user/signIn")
    public Result<Long> signIn(@RequestBody SignInUserDetailDTO param) {
        Result<Long> result = new Result<>();
        Long signIn = signInUserDetailService.signIn(SecurityContext.getUser().getUid(), param);
        result.setData(signIn);
        return result;
    }

    @ApiOperation("补卡")
    @PostMapping(value = "/app/signIn/user/supplement")
    public Result<SignInSupplementDTO> supplement(@RequestBody SignInUserDetailDTO param) {
        Result<SignInSupplementDTO> result = new Result<>();
        SignInSupplementDTO signIn = signInUserDetailService.supplement(SecurityContext.getUser().getUid(), param);
        result.setData(signIn);
        return result;
    }

    @ApiOperation("获取第七天的报告")
    @GetMapping(value = "/app/signIn/user/report/day7")
    public Result<SignInUserDetailDTO> getReport() {
        Result<SignInUserDetailDTO> result = new Result<>();
        SignInUserDetailDTO signInUserDetailDTO = signInUserDetailService.getReport(SecurityContext.getUser().getUid(), 7);
        result.setData(signInUserDetailDTO);
        return result;
    }

    @ApiOperation("获取第七天的报告v2")
    @GetMapping(value = "/app/signIn/user/reportDays")
    public Result<SignInReportDTO> getReportDays(@RequestParam("campaignCode") String campaignCode) {
        Result<SignInReportDTO> result = new Result<>();
        SignInReportDTO dto = signInUserDetailService.getReportDays(campaignCode, SecurityContext.getUser().getUid());
        result.setData(dto);
        return result;
    }

    @ApiOperation("重启打卡")
    @GetMapping(value = "/app/signIn/user/restartSignIn")
    public Result<Boolean> restartSignIn(@RequestParam("campaignCode") String campaignCode) {
        Result<Boolean> result = new Result<>();
        Boolean dto = signInUserDetailService.restartSignIn(campaignCode, SecurityContext.getUser().getUid());
        result.setData(dto);
        return result;
    }
}
