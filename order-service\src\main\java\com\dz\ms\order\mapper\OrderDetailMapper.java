package com.dz.ms.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.core.dto.MujiOrder;
import com.dz.common.core.dto.order.OdsOrderProductDTO;
import com.dz.common.core.dto.order.PurchaseStaticDTO;
import com.dz.common.core.dto.order.PurchaseStaticParamDTO;
import com.dz.common.core.dto.product.CpStaticDTO;
import com.dz.common.core.dto.product.CpStaticParamDTO;
import com.dz.ms.order.entity.OdsOrderProduct;
import com.dz.ms.order.entity.OrderDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 订单详情信息Mapper
 *
 * @author: LiinNs
 * @date: 2024/12/09 10:38
 */
@Repository
public interface OrderDetailMapper extends BaseMapper<OrderDetail> {

    /**
     * 采购统计
     *
     * @param param 采购统计参数
     * @return 采购统计结果
     */
    List<PurchaseStaticDTO> selectPurchaseStatic(PurchaseStaticParamDTO param);

    /**
     * 优惠券兑换统计
     */
    IPage<CpStaticDTO> selectCpStatic(Page<CpStaticDTO> page, @Param("param") CpStaticParamDTO param);

    List<MujiOrder> selectMujiOrder(@Param("beginTime") Date beginTime,
                                    @Param("endTime") Date endTime);

    void insertBatchSomeColumn(List<OdsOrderProduct> odsOrderProductList);

    List<OdsOrderProduct> sftpOrderList(@Param("mod") String mod,
                                        @Param("memberCode") String memberCode,
                                        @Param("beginTime") String beginTime,
                                        @Param("endTime") String endTime);

    void removeOrder(@Param("tableName") String tableName, @Param("ninetyDaysAgo") Date ninetyDaysAgo);

    int removeOrderBatch(@Param("tableName") String tableName, @Param("ninetyDaysAgo") Date ninetyDaysAgo, @Param("offset") int offset, @Param("batchSize") int batchSize);
}
