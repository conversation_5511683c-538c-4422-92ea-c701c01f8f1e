package com.dz.ms.user.dto.dkeyam.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.io.Serializable;

/**
 * 增量同步用户入参
 * @author: fei
 * @date: 2025/01/22 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserIncSyncParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("管理员登录名")
    private String adminLoginName;

    @ApiModelProperty("管理员密码")
    private String adminPassword;

    @ApiModelProperty("站点名称")
    private String tenantName;

    @ApiModelProperty("用户源名称 本地用户源：06bc758e-04f2-40b3-9d6c-562b115aeb3c 外部用户源：和创建的外部用户源名称一致")
    private String identityStoreName;

    @ApiModelProperty("用户登录名")
    private String loginName;

    @ApiModelProperty("用户组（组与组之间使用/分隔） 示例：aa/bb/cc")
    private String groupPath;

    @ApiModelProperty("用户姓名")
    private String personalName;

    @ApiModelProperty("用户静态密码")
    private String password;

    @ApiModelProperty("是否受<密码复杂度>影响，默认：true 密码复杂度：系统后台配置的策略，检查密码是否过于简单")
    private Boolean needPasswordPolicy;

    @ApiModelProperty("是否下次登录更改密码")
    private Boolean changePasswordAtNextLogin;

    @ApiModelProperty("用户手机号（会先清除以前的手机号）")
    private String mobile;

    @ApiModelProperty("用户角色（每次更新一个角色，不会解绑以前的角色）")
    private String roleName;

    @ApiModelProperty("用户角色是否为易失(true代表不会被外部用户源角色信息覆盖)")
    private Boolean roleIsNonVolatile;

    @ApiModelProperty("用户角色（每次可以更新多个角色，会将以前的角色解绑）")
    private String[] roleNames;

    @ApiModelProperty("用户是否启用，默认值true：启用")
    private Boolean enabled;

    @ApiModelProperty("用户有效期，起始时间，时间戳毫秒")
    private Long startTime;

    @ApiModelProperty("用户有效期，结束时间，时间戳毫秒")
    private Long endTime;
}
