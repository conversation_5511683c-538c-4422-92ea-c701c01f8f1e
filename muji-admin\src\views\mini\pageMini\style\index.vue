<template>
  <layout>
    <template v-slot:headerTop>
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="1" tab="页面风格"></a-tab-pane>
        <a-tab-pane key="2" tab="启动页设置"></a-tab-pane>
      </a-tabs>
    </template>
    <template v-slot="{ height }">
      <a-form class="form" :style="{height:height+'px'}" ref="formRef" :model="addParams" :rules="rules" labelAlign="left" :labelCol="{ style: 'width:150px' }">
        <!-- 页面风格 -->
        <template v-if="activeKey==1">
          <div class="header-title">文字字体</div>
          <a-form-item label="默认字体" :name="['pageStyle','font']" :rules="[{required: true, message: '请选择', trigger: ['blur', 'change'] }]">
            <a-select placeholder="请选择" v-model:value="addParams.pageStyle.font" mode="multiple" :options="fontFamily" style="width:300px" allowClear :getPopupContainer="triggerNode => triggerNode.parentNode">
            </a-select>
            <div class="global-tip">请按照顺序选择</div>
          </a-form-item>
          <div class="header-title">页面和顶栏</div>
          <a-form-item label="页面背景色" :name="['pageStyle','pageColor']" :rules="[{required: true, message: '请选择', trigger: ['blur', 'change'] }]">
            <Color color="rgba(0,0,0,1)" :value="addParams.pageStyle.pageColor" @changeColor="(color)=>changeColor(['pageStyle','pageColor'],color)"></Color>
          </a-form-item>
          <a-form-item label="默认顶栏自定义背景">
            <bgSet :disabled="disabled" :addParams="addParams.pageStyle"></bgSet>
          </a-form-item>
          <!-- <a-form-item label="默认顶栏前景色" :name="['pageStyle','fontColor']" :rules="[{required: true, message: '请选择', trigger: ['blur', 'change'] }]">
            <a-radio-group v-model:value="addParams.pageStyle.fontColor">
              <a-radio value="#000000">黑色</a-radio>
              <a-radio value="#ffffff">白色</a-radio>
            </a-radio-group>
            <div class="global-tip">前景颜色值，包括按钮、标题、状态栏的颜色</div>
          </a-form-item>
          <a-form-item label="默认顶栏背景色" :name="['pageStyle','contentColor']" :rules="[{required: true, message: '请选择', trigger: ['blur', 'change'] }]">
            <Color color="rgba(0,0,0,1)" keyValue="hex" :value="addParams.pageStyle.contentColor" @changeColor="(color)=>changeColor(['pageStyle','contentColor'],color)"></Color>
          </a-form-item> -->
          <div class="header-title">loading设置</div>
          <a-form-item label="loading图片">
            <uploadImg :max="10" :width="100" :height="100" :imgUrl="addParams.pageStyle.loading" :form="addParams" path="pageStyle.loading" :disabled="disabled" @success="uploadSuccess" />
          </a-form-item>
          <a-form-item label="loading大小" :labelCol="{width:'50px'}">
            <a-space>
              <a-form-item>
                <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="addParams.pageStyle.loadingWidth" addon-before="宽" addon-after="px"></a-input-number>
              </a-form-item>
              <a-form-item>
                <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="addParams.pageStyle.loadingHeight" addon-before="高" addon-after="px"></a-input-number>
              </a-form-item>
            </a-space>
          </a-form-item>
        </template>
        <!-- 启动页 -->
        <template v-else-if="activeKey==2">
          <a-form-item label="开启启动页" :name="['openStyle','state']" :rules="[{required: true, message: '请选择', trigger: ['blur', 'change'] }]">
            <a-switch :unCheckedValue="0" :checkedValue="1" v-model:checked="addParams.openStyle.state" />
          </a-form-item>
          <a-form-item label="展示频次" v-if="addParams.openStyle.state">
            <a-radio-group v-model:value="addParams.openStyle.openFrequency">
              <a-radio :value="1">每次打开都展示</a-radio>
              <a-radio :value="2">每天仅展示1次</a-radio>
              <a-radio :value="3" style="display:block;margin-top:15px;">
                <a-space>
                  <a-space-compact block>
                    <a-form-item :name="['openStyle','openDays']" :rules="addParams.openStyle.state==3?[{required: true, message: '请输入', trigger: ['blur', 'change'] }]:[]">
                      <a-input-number placeholder="请输入" :min="1" :precision="0" v-model:value="addParams.openStyle.openDays" addonBefore="每" addonAfter="天"></a-input-number>
                    </a-form-item>
                    <a-form-item :name="['openStyle','openNumer']" :rules="addParams.openStyle.state==3?[{required: true, message: '请输入', trigger: ['blur', 'change'] }]:[]">
                      <a-input-number placeholder="请输入" :min="1" :precision="0" v-model:value="addParams.openStyle.openNumer" addonAfter="次"></a-input-number>
                    </a-form-item>
                  </a-space-compact>
                </a-space>
              </a-radio>
            </a-radio-group>
          </a-form-item>
        </template>
      </a-form>
    </template>
    <template v-slot:footer>
      <a-flex justify="center" align="center">
        <a-button type="primary" @click="saveData" :disabled="!$hasPermission('style:save')" :loading="loading">保存配置</a-button>
      </a-flex>
    </template>
  </layout>
</template>
<script setup>
import { message } from 'ant-design-vue'
import { getStyle, styleSave } from '@/http/index.js'
import { fontFamily } from "@/utils/fixedVariable.js";
import { usePagination } from 'vue-request'
import { cloneDeep, set } from 'lodash'
import { nextTick } from 'vue';
const formRef = ref()
const keys = ['', 'pageStyle', 'openStyle']
const data = {
  id: '',
  content: '',
  pageStyle: {
    font: null,
    pageColor: '',
    contentColor: '',
    // 背景设置
    bgType: 1,//背景设置 1-纯色  2-图片  3-视频
    bgColor: 'rgba(0,0,0,0)',
    bgImg: '',
    bgVideo: '',
    // loading设置
    loadingHeight: 0,
    loadingWidth: 0,
    loading: ''
  },
  // // 开屏设置
  openStyle: {
    state: 0,
    openFrequency: 1,//开屏频次  1-每次打开都展示  2-仅展示1次 3-自定义
    openDays: 1,// 天数
    openNumer: 1,//次数
  },
}
const { addParams, origin, loading, rules, activeKey } = toRefs(reactive({
  // 操作数据
  addParams: cloneDeep(data),
  // 原始数据备份
  origin: cloneDeep(data),
  loading: false,
  activeKey: '1',
  rules: {

  }
}))


// 上传图片 视频
const uploadSuccess = async (data) => {
  let { form, path, imgUrl } = data;
  set(form, path, imgUrl)
  await nextTick()
  formRef.value.validateFields([path.split('.')])
}


// 修改颜色
const changeColor = async (key, color) => {
  set(addParams.value, key.join('.'), color)
  // console.log(addParams.value)
  await nextTick()
  formRef.value.validateFields([key])
}

// 获取信息
const getInfo = () => {
  getStyle().then(res => {
    if (res.data?.id) {
      let params = res.data;
      let content = JSON.parse(params.content)
      params.pageStyle = content.pageStyle
      params.openStyle = content.openStyle
      addParams.value = params
      origin.value = cloneDeep(params)
    }
  })
}
getInfo()
// 保存设置
const saveData = () => {
  formRef.value.validate().then(res => {
    let params = cloneDeep(origin.value)
    params.content = JSON.stringify({
      pageStyle: activeKey.value == 1 ? addParams.value.pageStyle : params.pageStyle,
      openStyle: activeKey.value == 2 ? addParams.value.openStyle : params.openStyle,
    })
    delete params.pageStyle
    delete params.openStyle
    loading.value = true
    styleSave(params).then(res => {
      message.success('保存成功')
      getInfo()
    }).finally(() => {
      loading.value = false
    })
  })
}

</script>
<style  scoped lang="scss">
.form {
  overflow-y: auto;
}
</style>
