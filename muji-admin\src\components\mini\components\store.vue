<template>
  <div class="header-title">门店信息</div>
  <a-form-item label="门店标题">
    <a-input placeholder="请输入" v-model:value="data.title" maxlength="8" showCount></a-input>
  </a-form-item>
  <a-form-item label="门店图片">
    <uploadImg :max="10" :width="300" :height="100" :imgUrl="data.imgUrl" :form="data" path="imgUrl" :disabled="disabled" @success="uploadSuccess" />
    <div class="global-tip">建议长宽670*370</div>
  </a-form-item>
  <div class="header-title">颜色设置</div>
  <a-form-item label="背景色">
    <Color color="rgba(0,0,0,1)" :value="data.bgColor" @changeColor="changeColor"></Color>
  </a-form-item>
  <div class="header-title">组件样式</div>
  <a-form-item label="边距" :labelCol="{width:'50px'}">
    <a-space>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingTop" addon-before="上" addon-after="px"></a-input-number>
      </a-form-item>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingBottom" addon-before="下" addon-after="px"></a-input-number>
      </a-form-item>
    </a-space>
    <a-space>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingLeft" addon-before="左" addon-after="px"></a-input-number>
      </a-form-item>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingRight" addon-before="右" addon-after="px"></a-input-number>
      </a-form-item>
    </a-space>
  </a-form-item>
</template>
<script setup>
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  // 所有组件数组
  components: {
    type: Array,
    default() {
      return []
    }
  },
  // 当前组件数据
  data: {
    type: Object,
    default() {
      return {}
    }
  },
  // 组件展示的宽度
  width: {
    type: Number,
    default: 750,
  },

})

// 修改颜色
const changeColor = async (color) => {
  props.data.bgColor = color
}

// 上传图片 视频
const uploadSuccess = async (data) => {
  let { form, path, imgUrl } = data;
  form[path] = imgUrl
}
</script>

<style>
</style>
