package com.dz.ms.user.controller;

import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.user.openapi.param.OpenApiTokenParamDTO;
import com.dz.common.core.dto.user.openapi.response.OpenApiDataDTO;
import com.dz.common.core.enums.LogType;
import com.dz.ms.user.service.OpenApiAccountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * OpenApi-UserController
 *
 */
@Api(tags = "OpenApi-User")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class OpenApiUserSupplyController {

    @Resource
    private OpenApiAccountService openApiAccountService;

    @SysLog(value = "openapi获取token",type = LogType.INTERFACE)
    @ApiOperation("获取token")
    @PostMapping(value = "/openapi/auth/get_token")
    public Result<OpenApiDataDTO> getToken(@RequestBody @Valid OpenApiTokenParamDTO param) {
        Result<OpenApiDataDTO> result = new Result<>();
        OpenApiDataDTO returnDTO = openApiAccountService.getToken(param);
        result.setData(returnDTO);
        return result;
    }

}
