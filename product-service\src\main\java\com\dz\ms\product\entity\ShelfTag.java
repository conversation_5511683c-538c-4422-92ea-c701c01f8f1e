package com.dz.ms.product.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 货架标签表
 *
 * @author: LiinNs
 * @date: 2024/11/27 15:15
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table("货架标签表")
@TableName(value = "shelf_tag")
public class ShelfTag implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "货架ID", isIndex = true)
    private Long shelfId;
    @Columns(type = ColumnType.VARCHAR, length = 256, isNull = true, comment = "货架名称", isIndex = true)
    private String shelfName;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "标签ID", isIndex = true)
    private Long tagId;
    @Columns(type = ColumnType.VARCHAR, length = 256, isNull = true, comment = "标签名称", isIndex = true)
    private String tagName;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, comment = "1一级标签 2二级标签 ...")
    private Integer cate;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "2级标签所关联的1级标签ID")
    private Long parentId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "是否删除 0未删除 1已删除")
    @TableLogic
    private Integer isDeleted;

    public ShelfTag(Long id, Long shelfId, String shelfName, Long tagId, String tagName, Integer cate, Long parentId) {
        this.id = id;
        this.shelfId = shelfId;
        this.shelfName = shelfName;
        this.tagId = tagId;
        this.tagName = tagName;
        this.cate = cate;
        this.parentId = parentId;
    }

}
