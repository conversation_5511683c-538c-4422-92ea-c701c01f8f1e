<van-popup style="--popup-background-color: transparent;" lock-scroll closeable="{{false}}" show="{{ isShow }}" safe-area-inset-bottom="{{true}}" bindclose="onClose" custom-style="background-color: transparent" position='bottom'>
  <view class="clockPopup">
    <view class="clockPopup-content" style="background:url({{$cdn}}/MiKangCampaign/mk-add-form-bg.png) 100% 100%;--radius:0rpx;">
      <scroll-view class="clock" enhanced="{{ true }}" bounces="{{ false }}" show-scrollbar="{{ false }}" catch:touchmove="touchmove" scroll-y>
        <view class="content">
          <view class="title">体验公示结果</view>
          <block wx:for="{{listData}}" wx:for-item="person">
            <block wx:if="{{person.enrollRosterList.length>0}}">
              <!-- <view class="title1"><text>第{{person.tabsObj}}期体验官招募结果</text></view> -->
              <view class="box">
                <view class="title-content">报名时间为: {{person.campaignStartTime}}-{{person.campaignEndTime}}</view>
                <!-- <view class="title-Time">{{person.campaignStartTime}}-{{person.campaignEndTime}}</view> -->
                <!-- <view class="title-content">第{{person.tabsObj}}轮报名结果公示时间：</view> -->
                <!-- <view class="title-Time">{{person.campaignShowTime}}</view> -->
                <view class="title-content title-content1">体验官护肤礼:</view>
                <view class="tips"><text>体验官护肤礼，将于2025年9月12日内寄出，请各位体验官注意查收护肤礼包含:【无印良品米糠发酵精华液5ml】与【无印良品米糠发酵精华水5ml】 各2份。</text></view>
                <!-- <view class="title-content">极简护肤礼发货时间：</view> -->
                <!-- <view class="tips"><text>您的极简护肤礼将在公示期满后7个工作日内完成发放，寄往体验官报名时所填写的收货地址，请各位体验官注意查收（部分地区可能因物流原因稍有延迟）。</text></view> -->
                <!-- <view class="title-content">第{{person.tabsObj}}轮报名中奖名单：</view> -->
                <view class="table">
                  <view class="head">
                    <view class="table_th">
                      <view class="table_td" wx:for="{{option}}" wx:key="index" style="text-align: {{item.align||'center'}};">{{item.label}}</view>
                    </view>
                  </view>
                  <!-- 超出10个滚动 -->
                  <scroll-view class="table_scroll" scroll-y style="max-height:600rpx;" bindscrolltolower="handleScrollToLower">
                    <view class="tbale_body">
                      <view class="table_tr" wx:for="{{person.enrollRosterList}}" wx:key="rowIndex" wx:for-index="rowIndex" wx:for-item="rowItem">
                        <view class="table_body_td" style="text-align: center;" wx:for="{{option}}" wx:key="colIndex" wx:for-index="colIndex" wx:for-item="colItem">{{rowItem[colItem.value]}}</view>
                      </view>
                    </view>
                  </scroll-view>
                </view>
              </view>
            </block>
          </block>
        </view>
      </scroll-view>
    </view>
    <view class="clockPopup-close" catch:touchmove="touchmove1">
      <view class="clockPopup-closeBox" bindtap="onClose">
        <text class="iconfont icon-a-Turnoff" style="color:#3C3C43;font-size:46rpx;" catch:touchmove="touchmove1"></text>
      </view>
    </view>
  </view>
</van-popup>