package com.dz.common.core.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExchangeStaticParamDTO {

    @ApiModelProperty(value = "订单号")
    private String orderCode;
    @ApiModelProperty(value = "订单积分")
    private Integer orderPoint;
    @ApiModelProperty(value = "订单件数")
    private Integer orderNum;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "货架兑礼人数")
    private Integer exchangePeople;
    @ApiModelProperty(value = "货架商品")
    private List<StaticItemParamDTO> itemList;

}
