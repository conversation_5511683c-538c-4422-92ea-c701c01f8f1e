package com.dz.ms.basic.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.enums.SubscribeMsgEnum;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.basic.MpMsgPushFeignClient;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.dto.MpMsgPushDTO;
import com.dz.ms.basic.service.MpMsgPushService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import java.util.List;

@Api(tags="小程序及公众号模板消息推送任务")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class MpMsgPushController implements MpMsgPushFeignClient {

    @Resource
    private MpMsgPushService mpMsgPushService;

    /**
     * 分页查询小程序/公众号模板消息推送任务
     * @param param
     * @return result<PageInfo<MpMsgPushDTO>>
     */
    @ApiOperation("分页查询小程序/公众号模板消息推送任务")
	@GetMapping(value = "/mp_msg_push/list")
    public Result<PageInfo<MpMsgPushDTO>> getMpMsgPushList(@ModelAttribute MpMsgPushDTO param) {
        Result<PageInfo<MpMsgPushDTO>> result = new Result<>();
		PageInfo<MpMsgPushDTO> page = mpMsgPushService.getMpMsgPushList(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询小程序/公众号模板消息推送任务
     * @param id
     * @return result<MpMsgPushDTO>
     */
    @ApiOperation("根据ID查询小程序/公众号模板消息推送任务")
	@GetMapping(value = "/mp_msg_push/info")
    public Result<MpMsgPushDTO> getMpMsgPushById(@RequestParam("id") Long id) {
        Result<MpMsgPushDTO> result = new Result<>();
        MpMsgPushDTO mpMsgPush = mpMsgPushService.getMpMsgPushById(id);
        result.setData(mpMsgPush);
        return result;
    }

    /**
     * 新增小程序/公众号模板消息推送任务
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增小程序/公众号模板消息推送任务",type = LogType.OPERATELOG)
    @ApiOperation("新增小程序/公众号模板消息推送任务")
	@PostMapping(value = "/mp_msg_push/add")
    public Result<Long> addMpMsgPush(@RequestBody MpMsgPushDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,true);
        Long id = mpMsgPushService.saveMpMsgPush(param);
        result.setData(id);
        return result;
    }

    /**
    * 更新小程序/公众号模板消息推送任务
    * @param param
    * @return result<Object>
    */
    @SysLog(value = "更新小程序/公众号模板消息推送任务",type = LogType.OPERATELOG)
    @ApiOperation("更新小程序/公众号模板消息推送任务")
    @PostMapping(value = "/mp_msg_push/update")
    public Result<Long> updateMpMsgPush(@RequestBody MpMsgPushDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,false);
        mpMsgPushService.saveMpMsgPush(param);
        result.setData(param.getId());
        return result;
    }

    /**
    * 验证保存参数
    * @param param
    */
    private void validationSaveParam(MpMsgPushDTO param,boolean isAdd) {
        if(isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if(!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
    }

	/**
     * 根据ID删除小程序/公众号模板消息推送任务
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除小程序/公众号模板消息推送任务")
	@PostMapping(value = "/mp_msg_push/delete")
    public Result<Boolean> deleteMpMsgPushById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        mpMsgPushService.deleteMpMsgPushById(param);
        result.setData(true);
        return result;
    }

    @ApiOperation("活动开始提醒消息发送（会小）")
    @PostMapping(value = "/mp_msg_push/activity_start")
    public Result<Boolean> activityStartMsgPush(@RequestParam("tenantId") Long tenantId,@RequestParam("content")String[] content,
                                                @RequestParam("path")String path) {
        Result<Boolean> result = new Result<>();
        mpMsgPushService.activityStartMsgPush(SubscribeMsgEnum.ACTIVITY_START, tenantId,content,path);
        result.setData(true);
        return result;
    }

    @ApiOperation("活动开始提醒消息发送（campaign）")
    @PostMapping(value = "/mp_msg_push/activity_start_campaign")
    public Result<Boolean> activityStartCampaignMsgPush(@RequestParam("tenantId") Long tenantId,@RequestParam("content")String[] content,
                                                        @RequestParam("path")String path, @RequestParam(value = "uids",required = false) List<Long> uids) {
        Result<Boolean> result = new Result<>();
        mpMsgPushService.msgPushBySubscribeLog(SubscribeMsgEnum.ACTIVITY_START, tenantId,content,path, uids);
        result.setData(true);
        return result;
    }


    @ApiOperation("第二轮报名开始提醒消息发送")
    @PostMapping(value = "/mp_msg_push/enroll_start")
    public Result<Boolean> activityEnrollMsgPush(@RequestParam("tenantId") Long tenantId,@RequestParam("content")String[] content,
                                                @RequestParam("path")String path,@RequestParam(value = "uids",required = false) List<Long> uids) {
        Result<Boolean> result = new Result<>();
        mpMsgPushService.msgPushBySubscribeLog(SubscribeMsgEnum.ENROLL_FAIL, tenantId,content,path,uids);
        result.setData(true);
        return result;
    }


}
