package com.dz.ms.user.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.exception.BusinessException;
import com.dz.ms.user.dto.AppointmentRequestDTO;
import com.dz.ms.user.dto.AppointmentStatsDTO;
import com.dz.ms.user.entity.AppointmentRecord;
import com.dz.ms.user.service.AppointmentService;
import com.dz.ms.user.vo.AppointmentDetailsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "预约活动")
@RestController
public class AppointmentController {

    @Autowired
    private AppointmentService appointmentService;

    /**
     * 获取预约活动详情
     *
     * @return
     */
    @GetMapping("/app/appointments/details")
    public Result<AppointmentDetailsVO> getAppointmentDetails() {
        Result<AppointmentDetailsVO> result = new Result<>();
        result.setData(appointmentService.getAppointmentDetails());
        return result;
    }

    /**
     * 预约活动
     *
     * @param request 包含预约信息的请求体
     */
    @ApiOperation("预约活动")
    @PostMapping("/app/appointments/book")
    public Result<Void> bookAppointment(@RequestBody AppointmentRequestDTO request) {
        // 校验姓名 非空且不能超过20位
        if (StringUtils.isEmpty(request.getName()) || request.getName().length() > 20) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "姓名不能超过20位");
        }
        // 校验手机号 长度 7~15位，且首位可以是+开头
        if (request.getPhone().length() < 7 || request.getPhone().length() > 15 || !request.getPhone().matches("^[+]?[0-9]{7,15}$")) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "手机号格式不正确， 长度 7~15位");
        }
        // 校验预约日期格式
        if (!request.getAppointmentDate().matches("^\\d{4}/\\d{2}/\\d{2}$")) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "预约日期格式不正确，如：2025/03/12");
        }
        // 校验预约场次格式
        if (!request.getAppointmentSlot().matches("^\\d{2}:\\d{2}-\\d{2}:\\d{2}$")) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "预约场次格式不正确，如：14:00-16:00");
        }
        appointmentService.bookAppointment(request.getName(), request.getPhone(), request.getAppointmentDate(), request.getAppointmentSlot());
        return new Result<>();
    }

    /**
     * 获取预约日期配置列表
     *
     * @return 获取预约日期配置列表
     */
    @ApiOperation("获取预约日期配置列表")
    @GetMapping("/app/appointments/dates")
    public Result<List<String>> getAppointmentDates() {
        Result<List<String>> result = new Result<>();
        result.setData(appointmentService.getAppointmentDates());
        return result;
    }

    /**
     * 获取预约日期列表
     *
     * @return 获取预约日期列表
     */
    @ApiOperation("获取预约日期配置列表")
    @GetMapping("/app/appointments/dates_stats")
    public Result<List<AppointmentStatsDTO>> getAppointmentDatesStats() {
        Result<List<AppointmentStatsDTO>> result = new Result<>();
        result.setData(appointmentService.getAppointmentDatesStats());
        return result;
    }


    /**
     * 获取预约场次配置
     *
     * @param appointmentDate
     * @return 预约场次配置列表
     */
    @ApiOperation("获取预约场次配置")
    @GetMapping("/app/appointments/slots")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appointmentDate", value = "预约日期", example = "2025/03/01", required = true, dataType = "String"),
    })
    public Result<List<String>> getAppointmentSlots(@RequestParam String appointmentDate) {
        // 校验预约日期格式
        if (!appointmentDate.matches("^\\d{4}/\\d{2}/\\d{2}$")) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "预约日期格式不正确，如：2025/03/12");
        }
        Result<List<String>> result = new Result<>();
        result.setData(appointmentService.getAppointmentSlots(appointmentDate));
        return result;
    }

    /**
     * 获取预约场次配置
     *
     * @param appointmentDate
     * @return 预约场次配置列表
     */
    @ApiOperation("获取预约场次配置")
    @GetMapping("/app/appointments/slots_stats")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appointmentDate", value = "预约日期", example = "2025/03/01", required = true, dataType = "String"),
    })
    public Result<List<AppointmentStatsDTO>> getAppointmentSlotsStats(@RequestParam String appointmentDate) {
        // 校验预约日期格式
        if (!appointmentDate.matches("^\\d{4}/\\d{2}/\\d{2}$")) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "预约日期格式不正确，如：2025/03/12");
        }
        Result<List<AppointmentStatsDTO>> result = new Result<>();
        result.setData(appointmentService.getAppointmentSlotsStats(appointmentDate));
        return result;
    }

    /**
     * 判断场次是否已约满
     *
     * @param appointmentDate 预约日期
     * @param appointmentSlot 预约场次
     * @return 是否已约满
     */
    @ApiOperation("判断场次是否已约满")
    @GetMapping("/app/appointments/isFull")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appointmentDate", value = "预约日期", example = "2025/03/12", required = true, dataType = "String"),
            @ApiImplicitParam(name = "appointmentSlot", value = "预约场次", example = "14:00-16:00", required = true, dataType = "String"),
    })
    public Result<Boolean> isAppointmentSlotFull(@RequestParam String appointmentDate, @RequestParam String appointmentSlot) {
        // 校验预约日期格式
        if (!appointmentDate.matches("^\\d{4}/\\d{2}/\\d{2}$")) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "预约日期格式不正确，如：2025/03/12");
        }
        // 校验预约场次格式
        if (!appointmentSlot.matches("^\\d{2}:\\d{2}-\\d{2}:\\d{2}$")) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "预约场次格式不正确，如：14:00-16:00");
        }
        Result<Boolean> result = new Result<>();
        result.setData(appointmentService.isAppointmentSlotFull(appointmentDate, appointmentSlot));
        return result;
    }

    /**
     * 根据日期查询预约记录
     *
     * @param appointmentDate
     * @return 预约记录列表
     */
    @ApiOperation("根据日期查询预约记录")
    @GetMapping("/crm/appointments/list")
    public Result<PageInfo<AppointmentRecord>> listAppointmentsByDate(@RequestParam(required = false) String appointmentDate,
                                                                      @RequestParam Integer pageNum,
                                                                      @RequestParam Integer pageSize) {
        Result<PageInfo<AppointmentRecord>> result = new Result<>();
        result.setData(appointmentService.listAppointmentsByDate(pageNum, pageSize, appointmentDate));
        return result;
    }

    /**
     * 根据日期导出所有预约记录
     *
     * @param appointmentDate
     * @return 预约记录列表
     */
    @ApiOperation("根据日期导出所有预约记录")
    @GetMapping("/crm/appointments/export")
    public List<AppointmentRecord> exportAppointmentsByDate(@RequestParam(required = false) String appointmentDate) {
        return appointmentService.exportAppointmentsByDate(appointmentDate);
    }
}