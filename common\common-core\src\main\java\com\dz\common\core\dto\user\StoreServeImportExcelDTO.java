package com.dz.common.core.dto.user;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 */
@Data
public class StoreServeImportExcelDTO {

    @ExcelProperty("门店编号")
    @ColumnWidth(19)
    private String storeSn;

    /*@ExcelProperty("服务ID")
    private String serveIds;*/

    @ExcelProperty("门店类型 1普通店  2旗舰店")
    @ColumnWidth(57)
    private Integer type;

}
