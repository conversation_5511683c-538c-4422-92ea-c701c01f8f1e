<template>
  <div class="poster" :style="{width:width/2+'px',height:height+'px'}">
    <customBg :bgSetting="data" class="bgStyle"></customBg>
    <div class="poster-content" :style="{
      borderRadius:data.borderRadius/2+'px',
      left:data.paddingLeft/2+'px',
      right:data.paddingRight/2+'px',
      top:data.paddingTop/2+'px',
      bottom:data.paddingBottom/2+'px',
    }">
      <img :src="data.imgUrl" class="poster-img" :style="{width:posterWidth+'px',height:posterHeight+'px'}" />
    </div>
  </div>
</template>
<script setup>
const emit = defineEmits(['ok', 'cancel', 'changeActive'])
import { cloneDeep } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { computed } from 'vue';
import customBg from './customBg.vue'
const props = defineProps({
  width: {
    type: Number,
    default: 750,
  },
  data: {
    type: Object,
    default() {
      return {}
    }
  },
})
watch(() => props.data, (val) => {
  calcHeight()
}, {
  deep: true
})
const { posterWidth, posterHeight, height } = toRefs(reactive({
  posterWidth: '',
  posterHeight: '',
  height: '',
}))

const calcHeight = () => {
  let width = props.width;
  let { imgWidth,
    imgHeight,
    paddingBottom,
    paddingLeft,
    paddingRight,
    paddingTop } = props.data;
  let posterWidth1 = (width - paddingLeft - paddingRight)
  let posterHeight1 = parseInt(posterWidth1 * imgHeight / imgWidth)
  let height1 = posterHeight1 + paddingBottom + paddingTop
  posterWidth.value = posterWidth1 / 2
  posterHeight.value = posterHeight1 / 2
  height.value = height1 / 2
}
calcHeight()

</script>

<style scoped lang="scss">
.poster {
  position: relative;

  &-content {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    overflow: hidden;
  }

  &-img {
    display: block;
  }
}
</style>
