package com.dz.ms.basic.service.impl;

import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.*;
import com.dz.common.core.dto.user.SysUserDTO;
import com.dz.common.core.enums.ReportDownloadEnum;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.user.SysUserFeginClient;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.ms.basic.entity.DownloadCenter;
import com.dz.ms.basic.entity.DownloadHeader;
import com.dz.ms.basic.mapper.DownloadCenterMapper;
import com.dz.ms.basic.mapper.DownloadHeaderMapper;
import com.dz.ms.basic.service.DownloadCenterService;
import com.dz.ms.basic.service.ReportDownloadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 类注释
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/2/8 16:49
 */
@Service
@Slf4j
public class ReportDownloadServiceImpl implements ReportDownloadService {

    private static final String DOWNLOAD_LOCK_PREFIX = "download_lock:";
    private static final String DOWNLOAD_SCARP_DAY = "system:download:scarp:day";
    @Resource
    private DownloadCenterService downloadCenterService;
    @Resource
    private SysUserFeginClient sysUserClient;

    @Resource
    private RedisService redisService;
    @Resource
    private DownloadHeaderMapper downloadHeaderMapper;
    @Resource
    private DownloadCenterMapper downloadCenterMapper;

    @Override
    public void downloadTask(ReportDownloadParamDTO reportDownloadParamDTO) {

        boolean lock = redisService.lock(DOWNLOAD_LOCK_PREFIX + reportDownloadParamDTO.getType() + ":" + SecurityContext.getUser().getUid(), 10);

        if (lock) {
            // 获取用户信息

            SysUserDTO sysUserDTO = sysUserClient.getSysUserByUid(SecurityContext.getUser().getUid(), SecurityContext.getUser().getTenantId()).getData();
            // 添加记录
            ReportDownloadEnum reportDownloadEnum = ReportDownloadEnum.typeOf(reportDownloadParamDTO.getType());
            DownloadAddParamDTO downloadAddParamDTO = new DownloadAddParamDTO();
            downloadAddParamDTO.setJsonParam(reportDownloadParamDTO.getParam().toJSONString());
            downloadAddParamDTO.setBeanName(reportDownloadEnum.getBeanName());
            downloadAddParamDTO.setCreateAt(sysUserDTO.getId().toString());
            // 创建员工工号以及姓名
            downloadAddParamDTO.setHeader(sysUserDTO.getUsername());
            downloadAddParamDTO.setReportCode(reportDownloadEnum.getReportCode());
            downloadAddParamDTO.setFileName(StringUtils.isEmpty(reportDownloadParamDTO.getFileName()) ? reportDownloadEnum.getFileName() : reportDownloadParamDTO.getFileName());
            downloadAddParamDTO.setMenuName(reportDownloadEnum.getMenuName());
            downloadAddParamDTO.setModuleName(reportDownloadEnum.getModuleName());
            downloadAddParamDTO.setTenantId(SecurityContext.getUser().getTenantId());
            String name = downloadAddParamDTO.getFileName();
            final String fileName = name + System.currentTimeMillis() + "-" + downloadAddParamDTO.getCreateAt();
            String fileExt = ".xlsx";
            downloadAddParamDTO.setFileName(fileName);
            downloadAddParamDTO.setFileExt(fileExt);
            downloadCenterService.save(downloadAddParamDTO, SecurityContext.getUser());
        } else {
            throw new BusinessException("10秒内不能重复导出同类型报表");
        }
    }

    @Override
    public PageInfo<ReportDownloadTaskDTO> taskList(ReportDownloadTaskParamDTO reportDownloadTaskParamDTO) {
        String menuName = null;
        String moduleName = null;
        if (null != reportDownloadTaskParamDTO.getType()) {
            ReportDownloadEnum reportDownloadEnum = ReportDownloadEnum.typeOf(reportDownloadTaskParamDTO.getType());
            menuName = reportDownloadEnum.getMenuName();
            moduleName = reportDownloadEnum.getModuleName();
        }
        DownloadQueryParamDTO paramDTO = new DownloadQueryParamDTO();
        paramDTO.setMenuName(menuName);
        paramDTO.setModuleName(moduleName);
        paramDTO.setTenantId(SecurityContext.getUser().getTenantId());
        paramDTO.setPageNum(reportDownloadTaskParamDTO.getPageNum());
        paramDTO.setPageSize(reportDownloadTaskParamDTO.getPageSize());
        paramDTO.setBeginTime(reportDownloadTaskParamDTO.getBeginTime());
        paramDTO.setEndTime(reportDownloadTaskParamDTO.getEndTime());
        paramDTO.setFileName(reportDownloadTaskParamDTO.getFileName());
        paramDTO.setCreator(SecurityContext.getUser().getUid());
        PageInfo<DownloadDTO> pageInfo = downloadCenterService.page(paramDTO);
        return new PageInfo<>(pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getCount(), BeanCopierUtils.convertList(pageInfo.getList(), ReportDownloadTaskDTO.class));
    }

    @Override
    public List<ReportDownloadTypeDTO> category() {
        return Arrays.stream(ReportDownloadEnum.values()).map(e -> {
            ReportDownloadTypeDTO dto = new ReportDownloadTypeDTO();
            dto.setType(e.getType());
            dto.setBizModuleName(e.getModuleName() + "-" + e.getMenuName());
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public void delete(Long id) {
        downloadCenterService.delete(id);
    }

    @Override
    public void mark(Long id) {
        downloadCenterService.mark(id);
    }

    @Override
    public void scarp() {
        // 首先作废超过3天未下载的数据
        String dayTime = redisService.getString(DOWNLOAD_SCARP_DAY);
        if (StringUtils.isEmpty(dayTime)) {
            dayTime = "3";
        }
        // 处理数据，更改状态
        downloadCenterService.scarp(Integer.valueOf(dayTime));
        // 清除oss文件
        List<DownloadDTO> list = downloadCenterService.listScarp(Integer.valueOf(dayTime));
        list.forEach(downloadDTO -> {
            // 删除oss文件
            downloadCenterService.updateClear(downloadDTO);
        });
    }

    @Override
    public void downloadTemplate(ReportDownloadParamDTO reportDownloadParamDTO) {
        if (reportDownloadParamDTO.getType() == null) {
            throw new BusinessException("下载类型不能为空");
        }
        // 获取用户信息
        Result<SysUserDTO> Result = sysUserClient.getSysUserByUid(SecurityContext.getUser().getUid(), SecurityContext.getUser().getTenantId());
        SysUserDTO sysUserInfoDTO = Result.getData();
        // 添加记录
        ReportDownloadEnum reportDownloadEnum = ReportDownloadEnum.typeOf(reportDownloadParamDTO.getType());
        DownloadCenter downloadCenter = new DownloadCenter();
        downloadCenter.setCreator(sysUserInfoDTO.getId());
        // 创建员工工号以及姓名
        downloadCenter.setHeader(sysUserInfoDTO.getUsername());
        downloadCenter.setSourceUrl(reportDownloadEnum.getReportCode());
        downloadCenter.setFileName(reportDownloadEnum.getFileName());
        downloadCenter.setMenuName(reportDownloadEnum.getMenuName());
        downloadCenter.setModuleName(reportDownloadEnum.getModuleName());
        downloadCenter.setTenantId(SecurityContext.getUser().getTenantId());
        downloadCenter.setDownloadNum(0L);
        downloadCenter.setState(1);
        downloadCenter.setIsDeleted(0);
        downloadCenter.setCreator(SecurityContext.getUser().getUid());
        downloadCenter.setCreated(new Date());
        downloadCenterService.save(downloadCenter);
    }

    @Override
    public List<DownloadHeaderDTO> getHeader(String reportCode, Long tenantId) {
        List<DownloadHeader> headerList = downloadHeaderMapper.selectByReportCode(reportCode, tenantId);
        return BeanCopierUtils.convertList(headerList, DownloadHeaderDTO.class);
    }

    @Override
    public void updateHeader(Long id, String errorMessage, Integer status, String url) {
        DownloadCenter downloadCenter = new DownloadCenter();
        downloadCenter.setId(id);
        downloadCenter.setErrorDesc(errorMessage);
        downloadCenter.setState(status);
        downloadCenter.setSourceUrl(url);
        downloadCenterMapper.updateByPrimaryKeySelective(downloadCenter);
    }

    @Override
    public int updateDownloadCenter(DownloadCenterDTO downloadCenterDTO) {
        return downloadCenterMapper.updateByPrimaryKeySelective(BeanCopierUtils.convertObject(downloadCenterDTO, DownloadCenter.class));
    }

}
