package com.dz.ms.order.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单主表DTO
 *
 * @author: LiinNs
 * @date: 2024/12/09 10:38
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "订单主表")
public class ExchangeOrderDTO extends BaseDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "订单编号")
    private String orderCode;
    @ApiModelProperty(value = "订单类型 1普通订单")
    private Integer orderType;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "货架名称")
    private String shelfName;
    @ApiModelProperty(value = "营销活动ID")
    private Long campaignId;
    @ApiModelProperty(value = "营销活动名称")
    private String campaignName;
    @ApiModelProperty(value = "商品总金额")
    private Integer productPoint;
    @ApiModelProperty(value = "商品金额")
    private BigDecimal productAmount;
    @ApiModelProperty(value = "订单总积分")
    private Integer orderPoint;
    @ApiModelProperty(value = "订单总金额")
    private BigDecimal orderAmount;
    @ApiModelProperty(value = "商品件数")
    private Integer number;
    @ApiModelProperty(value = "用户id")
    private Long userId;
    @ApiModelProperty(value = "用户手机号")
    private String userPhone;
    @ApiModelProperty(value = "用户CRM编码")
    private String userCrmCode;
    @ApiModelProperty(value = "会员卡等级")
    private String userCardLevel;
    @ApiModelProperty(value = "收货人id")
    private Long receiverId;
    @ApiModelProperty(value = "收货人姓名")
    private String receiverName;
    @ApiModelProperty(value = "收货人手机号")
    private String receiverPhone;
    @ApiModelProperty(value = "收货人地址")
    private String receiverAddress;
    @ApiModelProperty(value = "收货人邮编")
    private String receiverPostcode;
    @ApiModelProperty(value = "配送费")
    private BigDecimal expressAmount;
    @ApiModelProperty(value = "订单状态 0已完成 1待支付 2待发货 3已发货 4待兑换 5部分兑换 6已兑换 7已取消")
    private Integer orderStatus;
    @ApiModelProperty(value = "支付状态 1未支付 2已支付 3已退款 4部分支付")
    private Integer payStatus;
    @ApiModelProperty(value = "支付方式 1微信")
    private Integer payType;
    @ApiModelProperty(value = "支付时间")
    private Date payTime;
    @ApiModelProperty(value = "支付编号")
    private String payCode;
    @ApiModelProperty(value = "第三方订单号")
    private String tradeId;
    @ApiModelProperty(value = "发货状态 0待发货 1已发货 2部分发货")
    private Integer expressStatus;
    @ApiModelProperty(value = "物流ID")
    private Long expressId;
    @ApiModelProperty(value = "物流code")
    private String expressCode;
    @ApiModelProperty(value = "微信form id")
    private String formId;
    @ApiModelProperty(value = "订单备注")
    private String remark;
    @ApiModelProperty(value = "完成时间")
    private Date finishTime;
    @ApiModelProperty(value = "同步状态 0未同步 1已同步")
    private Integer syncStatus;
    @ApiModelProperty(value = "是否删除 0未删除 1已删除")
    private Integer isDeleted;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "修改时间")
    private Date modified;

}
