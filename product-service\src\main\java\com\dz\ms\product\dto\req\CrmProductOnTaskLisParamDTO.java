package com.dz.ms.product.dto.req;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 货架商品库存任务查询参数DTO
 *
 * @author: LiinNs
 * @date: 2024/11/22
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "货架商品库存任务查询参数D")
public class CrmProductOnTaskLisParamDTO extends BaseDTO {

    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "是否隐藏已完成任务 0不隐藏 1隐藏")
    private Integer hideDone = 0;
    @ApiModelProperty(value = "状态 0待完成 1已完成 2进行中")
    private Integer state;
}
