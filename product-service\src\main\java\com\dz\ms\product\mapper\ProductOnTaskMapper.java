package com.dz.ms.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.product.entity.ProductOnTask;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 货架商品库存任务Mapper
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:25
 */
@Repository
public interface ProductOnTaskMapper extends BaseMapper<ProductOnTask> {

    int insertBatch(@Param("list") List<ProductOnTask> addList);
}
