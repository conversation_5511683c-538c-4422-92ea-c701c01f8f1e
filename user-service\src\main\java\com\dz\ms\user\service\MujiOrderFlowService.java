package com.dz.ms.user.service;

import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.user.MujiOrderFlowDTO;
import com.dz.common.core.dto.user.MujiOrderFlowParamDTO;
import com.dz.common.core.dto.user.MujiOrderInfoDTO;
import com.dz.common.core.dto.user.MujiTicketInfoDTO;

/**
 * Muji 我的消费记录
 */
public interface MujiOrderFlowService {

    /**
     * 我的消费记录列表
     *
     * @param param
     * @return
     */
    PageInfo<MujiOrderFlowDTO> getMujiOrderFlowList(MujiOrderFlowParamDTO param);

    /**
     * 订单详情
     *
     * @param orderSn
     * @return
     */
    MujiOrderInfoDTO getMujiOrderInfo(String orderSn);

    /**
     * 订单小票详情
     *
     * @param orderSn
     * @return
     */
    MujiTicketInfoDTO getMujiOrderTicketInfo(String orderSn);
}
