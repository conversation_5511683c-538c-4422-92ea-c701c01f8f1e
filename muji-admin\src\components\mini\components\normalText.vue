<template>
  <div class="header-title">文本内容</div>
  <a-form-item>
    <div class="welcome-input">
      <a-textarea placeholder="请输入" v-model:value="data.content" ref="textArea" :rows="8" show-count :maxlength="1000"></a-textarea>
      <div class="welcome-btn">
        <a-select style="width:100%" placeholder="插入变量" v-model:value="insert" @change="insertContent" :getPopupContainer="triggerNode => triggerNode.parentNode">
          <a-select-option v-for="item in userListVariable" :value="'##'+item.name+'##'" :key="item.key">{{  item.name}}</a-select-option>
        </a-select>
      </div>
    </div>
  </a-form-item>
  <div class="header-title">跳转热区</div>
  <addLink type="2" :links="data.imgLinks" :components="components" @ok="(link)=>data.imgLinks=link">
    <a-button block>设置热区</a-button>
  </addLink>
  <div class="header-title">文本样式</div>
  <textSetting :data="data" :disabled="disabled"></textSetting>
  <div class="header-title">背景设置</div>
  <bgSet :addParams="data"></bgSet>

</template>
<script setup>
const emit = defineEmits(['ok', 'cancel'])
import { userListVariable } from "@/utils/fixedVariable.js";
const textArea = ref(null)
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  // 所有组件数组
  components: {
    type: Array,
    default() {
      return []
    }
  },
  // 当前组件数据
  data: {
    type: Object,
    default() {
      return {}
    }
  },
})

const { insert } = toRefs(reactive({
  insert: null
}))

// 在光标位置插入特定字符
const insertContent = (val, options) => {
  let content = props.data.content || ''
  let pos = textArea.value.resizableTextArea.textArea.selectionStart || content?.length || 0;
  props.data.content = content.substring(0, pos) + options.value + content.substring(pos, content.length)
  insert.value = null
}
</script>

<style lang="scss" scoped>
.welcome {
  &-input {
    border-radius: 10px;
    padding: 10px;
    border: 1px solid #ccc;
    margin-bottom: 10px;
  }
  &-btn {
    padding-top: 10px;
    border-top: 1px solid #ccc;
  }
}
</style>
