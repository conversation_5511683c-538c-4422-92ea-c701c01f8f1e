<view class="cart-card">
  <view class="cart-img" bind:tap="onTapDetail">
    <!-- 角标 -->
    <view wx:if="{{tagData}}" style="position: absolute; z-index: 2; {{tagData.position === 'top' ? 'top: 0; left: 0;' : 'bottom: 5rpx; left: 8rpx;'}}">
      <image src="{{tagData.imgUrl}}" style="width: {{tagData.width * 0.79}}rpx; height: {{tagData.height * 0.79}}rpx;" />
    </view>
    <image class="img" mode="aspectFill" src="{{skuInfo.imgUrl}}" />
  </view>
  <view class="cart-info">
    <view class="cart-title" bind:tap="onTapDetail">
      {{skuInfo.productName}}
    </view>
    <view class="cart-price" bind:tap="onTapDetail">
      <!--<block wx:if="{{skuInfo.rprePoint && skuInfo.costPoint}}"><text class="red">{{skuInfo.costPoint}}积分</text><text class="line">{{skuInfo.rprePoint}}积分</text></block>-->
      <!--<block wx:else>{{skuInfo.costPoint}}积分<block wx:if="{{skuInfo.costPrice}}">+{{skuInfo.costPrice}}元</block>-->
      <!--</block>-->
      <view class="{{skuInfo.showType===2 && skuInfo.prePoint ? 'red' :''}}">{{skuInfo.costPoint}}积分</view>
      <view wx:if="{{skuInfo.showType===1 && skuInfo.costPrice && skuInfo.costPriceOnShelf!==0}}">+{{skuInfo.costPrice}}元</view>
      <view wx:if="{{skuInfo.showType===2 && skuInfo.prePoint !== null}}" class="line">{{skuInfo.prePoint}}积分</view>
    </view>

    <cart-input wx:if="{{type!=='expire'}}" skuInfo="{{skuInfo}}" bind:confirm="onConfirm" bind:tap-del="onTapCartDel" bind:tap-add="onTapCartAdd" />

  </view>
</view>