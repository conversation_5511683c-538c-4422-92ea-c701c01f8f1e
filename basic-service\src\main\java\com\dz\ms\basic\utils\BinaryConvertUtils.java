package com.dz.ms.basic.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * 进制转换
 */
public class BinaryConvertUtils {
    private static String _arrChars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    private static Map<Character, Integer> ArrBitNum;

    static {
        ArrBitNum = initArr();
    }

    static Map<Character, Integer> initArr() {
        Map<Character, Integer> ret = new HashMap<>();
        for (int i = 0, j = _arrChars.length(); i < j; i++) {
            ret.put(_arrChars.charAt(i), i);
        }

        return ret;
    }

    /**
     * 把62进制转换为十进制
     *
     * @param str 62进制的字符串
     * @return 十进制数
     */
    public static long convertToNum(String str) throws Exception {
        return convertToNum(str, _arrChars.length());
    }

    /**
     * 把n进制转换为十进制
     *
     * @param str n进制的字符串
     * @param n   n进制
     * @return 十进制数
     */
    public static long convertToNum(String str, int n) throws Exception {
        if (n <= 0) {
            n = _arrChars.length();
        }

        long ret = 0L;
        if (str == null || (str = str.trim()).length() <= 0) {
            return ret;
        }
        //1234 = 1* 62^(4-1-0) + 1* 62^(4-1-1)
        for (int i = 0, j = str.length(); i < j; i++) {
            Integer num = ArrBitNum.get(str.charAt(i));
//            if (num == null)
//                throw new Exception("Contains unknown char: " + str.charAt(i));
            if (num != null && num > 0) {
                ret += (long) (num * Math.pow(n, j - Double.valueOf(1) - i));
            }
        }

        return ret;
    }

    /**
     * 把十进制数转换为62进制
     *
     * @param num 十进制数
     * @return 62进制字符串
     */
    public static String convertToStr(long num) {
        try {
            return convertToStr(num, _arrChars.length());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 把十进制数转换为n进制
     *
     * @param num 十进制数
     * @param n   要转换为几进制
     * @return n进制字符串
     */
    public static String convertToStr(long num, int n) throws Exception {
        if (num < 0) {
            throw new Exception("不支持负数转换");
        }

        if (n <= 0) {
            n = _arrChars.length();
        }

        StringBuilder ret = new StringBuilder();
        do {
            int perNum = (int) (num % n);
            num = num / n;
            ret.insert(0, _arrChars.charAt(perNum));
        } while (num > 0);

        return ret.toString();
    }

    public static void main(String[] args) throws Exception {
    }
}