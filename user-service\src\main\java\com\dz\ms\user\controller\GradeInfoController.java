package com.dz.ms.user.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.IsEnableDTO;
import com.dz.common.core.dto.user.GradeListDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.user.dto.GradeInfoDTO;
import com.dz.ms.user.service.GradeInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Api(tags="会员等级")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class GradeInfoController {

    @Resource
    private GradeInfoService gradeInfoService;

    @ApiOperation("查询会员等级列表")
	@GetMapping("/crm/grade_info/list")
    public Result<List<GradeInfoDTO>> getGradeInfoList(@ModelAttribute GradeInfoDTO param) {
        Result<List<GradeInfoDTO>> result = new Result<>();
        List<GradeInfoDTO> list = gradeInfoService.getGradeInfoList(param);
        result.setData(list);
        return result;
    }

    @ApiOperation("查询APP端会员等级列表")
    @GetMapping("/app/grade_info/list")
    public Result<List<GradeInfoDTO>> getAppGradeInfoList(@ModelAttribute GradeInfoDTO param) {
        Result<List<GradeInfoDTO>> result = new Result<>();
        param.setState(1);
        List<GradeInfoDTO> list = gradeInfoService.getGradeInfoList(param);
        result.setData(list);
        return result;
    }

    /**
     * 根据ID/CODE查询会员等级
     * @param param
     * @return result<GradeInfoDTO>
     */
    @ApiOperation("根据ID/CODE查询会员等级")
	@GetMapping(value = {"/crm/grade_info/info","/app/grade_info/info"})
    public Result<GradeInfoDTO> getGradeInfo(@ModelAttribute IdCodeDTO param) {
        Result<GradeInfoDTO> result = new Result<>();
        GradeInfoDTO gradeInfo = gradeInfoService.getGradeInfo(param, SecurityContext.getUser().getTenantId());
        result.setData(gradeInfo);
        return result;
    }

    /**
     * 新增会员等级
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增会员等级",type = LogType.OPERATELOG)
    @ApiOperation("新增会员等级")
    @PostMapping(value = "/crm/grade_info/add")
    public Result<Long> addGradeInfo(@RequestBody GradeInfoDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,true);
        Long id = gradeInfoService.saveGradeInfo(param);
        result.setData(id);
        return result;
    }

    /**
     * 更新会员等级
     * @param param
     * @return result<Object>
     */
    @SysLog(value = "更新会员等级",type = LogType.OPERATELOG)
    @ApiOperation("更新会员等级")
    @PostMapping(value = "/crm/grade_info/update")
    public Result<Long> updateGradeInfo(@RequestBody GradeInfoDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,false);
        gradeInfoService.saveGradeInfo(param);
        result.setData(param.getId());
        return result;
    }

    /**
     * 验证保存参数
     * @param param
     */
    private void validationSaveParam(GradeInfoDTO param,boolean isAdd) {
        if(isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if(!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
        if(StringUtils.isBlank(param.getGradeName())){
            throw new BusinessException(ErrorCode.BAD_REQUEST, "会员等级名称不能为空");
        }
    }

    @SysLog(value = "批量保存会员等级",type = LogType.OPERATELOG)
    @ApiOperation("批量保存会员等级")
    @PostMapping(value = "/crm/grade_info/save_batch")
    public Result<Object> saveGradeInfo(@RequestBody List<GradeInfoDTO> list) {
        Result<Object> result = new Result<>();
        gradeInfoService.updateGradeBatch(list);
        return result;
    }
	
	/**
     * 根据ID删除会员等级
     * @param param 单ID/CODE POST请求通用DTO
     * @return result<Boolean>
     */
    @SysLog(value = "删除会员等级",type = LogType.OPERATELOG)
    @ApiOperation("根据ID删除会员等级")
	@PostMapping(value = "/crm/grade_info/delete")
    public Result<Boolean> deleteGradeInfoById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        gradeInfoService.deleteGradeInfoById(param);
        result.setData(true);
        return result;
    }

    @SysLog(value = "修改会员等级启停状态",type = LogType.OPERATELOG)
    @ApiOperation("修改会员等级启停状态")
    @PostMapping(value = "/crm/grade_info/update_state")
    public Result<Boolean> updateGradeInfoStateById(@RequestBody IsEnableDTO param) {
        Result<Boolean> result = new Result<>();
        gradeInfoService.updateGradeInfoStateById(param);
        result.setData(true);
        return result;
    }

    @ApiOperation("获取会员等级MAP")
    @GetMapping(value = "/grade_info/code_name_map")
    public Result<List<GradeListDTO>> getCodeToNameGradeMap(@RequestParam("tenantId")Long tenantId) {
        Result<List<GradeListDTO>> result = new Result<>();
        result.setData(gradeInfoService.getCodeToNameGradeMap(tenantId));
        return result;
    }


}
