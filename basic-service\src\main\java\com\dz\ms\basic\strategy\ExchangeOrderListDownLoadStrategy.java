package com.dz.ms.basic.strategy;

import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.fegin.order.ExchangeOrderFeignClient;
import com.dz.common.core.service.DownloadStrategy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
public class ExchangeOrderListDownLoadStrategy implements DownloadStrategy {

    @Resource
    private ExchangeOrderFeignClient exchangeOrderFeignClient;

    @Override
    public void export(DownloadAddParamDTO downloadAddParamDTO) {
        exchangeOrderFeignClient.exportOrderList(downloadAddParamDTO);
    }
}
