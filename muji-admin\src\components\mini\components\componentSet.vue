<template>
  <a-collapse expandIconPosition="end" v-model:activeKey="activeKey" :bordered="false" style="background: rgb(255, 255, 255)">
    <template #expandIcon="{ activeKey }">
      <UpOutlined :rotate="activeKey ? 90 : 0" />
    </template>
    <a-collapse-panel key="1" header="组件设置">
      <template v-if="components[index]">
        <!-- 富文本 -->
        <rich v-if="components[index].type==1" :disabled="disabled" :width="width" :components="components" :data="components[index]"></rich>
        <!-- 组件数据 -->
        <poster v-if="components[index].type==2" :disabled="disabled" :width="width" :components="components" :data="components[index]"></poster>
        <!-- 视频组件 -->
        <myVideo v-if="components[index].type==3" :disabled="disabled" :width="width" :components="components" :data="components[index]"></myVideo>
        <!-- 普通轮播组件 -->
        <normalSlider v-if="components[index].type==4" :disabled="disabled" :width="width" :components="components" :data="components[index]"></normalSlider>
        <!-- 滚动组件 -->
        <scroll v-if="components[index].type==6" :disabled="disabled" :width="width" :components="components" :data="components[index]"></scroll>
        <!-- 门店组件 -->
        <store v-if="components[index].type==7" :disabled="disabled" :width="width" :components="components" :data="components[index]"></store>
        <!-- 任务组件 -->
        <task v-if="components[index].type==8" :disabled="disabled" :width="width" :components="components" :data="components[index]"></task>
        <!-- 普通文本 -->
        <normalText v-if="components[index].type==9" :disabled="disabled" :width="width" :components="components" :data="components[index]"></normalText>
        <!-- 分割线 -->
        <division v-if="components[index].type==10" :disabled="disabled" :width="width" :components="components" :data="components[index]"></division>
      </template>
    </a-collapse-panel>

  </a-collapse>
</template>
<script setup>
import poster from './poster.vue'
import normalText from './normalText.vue'
import myVideo from './myVideo.vue'
import rich from './rich.vue'
import store from './store.vue'
import task from './task.vue'
import scroll from './scroll.vue'
import normalSlider from './normalSlider.vue'
import division from './division.vue'
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  // 当下激活的下标
  index: {
    type: Number,
    default: 0,
  },
  // 组件展示的宽度
  width: {
    type: Number,
    default: 750,
  },
  // 组件数组
  components: {
    type: Array,
    default() {
      return []
    }
  },
})


const { activeKey } = toRefs(reactive({
  activeKey: [1]
}))


</script>

<style scoped lang="scss">
</style>
