package com.dz.ms.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.core.dto.user.SysUserDTO;
import com.dz.ms.user.entity.SysUser;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 系统用户信息Mapper
 * @author: Handy
 * @date:   2022/2/4 23:04
 */
@Repository
public interface SysUserMapper extends BaseMapper<SysUser> {

    SysUser getUserByUsername(@Param("username") String username, @Param("tenantId") Long tenantId);

    /** 根据userIdList查询系统用户信息 */
    List<SysUser> selectByIds(@Param("uids")List<Long> uids, @Param("tenantId") Long tenantId);

    IPage<SysUserDTO> selPageList(Page<Object> objectPage, @Param("tenantId") Long tenantId);
}
