package com.dz.ms.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.basic.dto.NavigationConfigDTO;
import com.dz.ms.basic.entity.NavigationConfig;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 小程序UI自定义配置Mapper
 * @author: Handy
 * @date:   2022/11/21 11:17
 */
@Repository
public interface UiConfigMapper extends BaseMapper<NavigationConfig> {

    /** 获取所有小程序UI自定义配置列表 */
    List<NavigationConfigDTO> getAllUiConfig();

    /** 获取当前选中的UI配置 */
    NavigationConfigDTO getCheckedUiConfig();

    /** 取消选中UI */
    int uncheckedUiConfig();

}
