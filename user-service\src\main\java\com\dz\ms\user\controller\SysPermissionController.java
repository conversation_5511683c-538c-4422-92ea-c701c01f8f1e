package com.dz.ms.user.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.user.SysPermissionDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.ms.user.entity.SysPermission;
import com.dz.ms.user.service.SysPermissionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags="权限功能")
@RestController
public class SysPermissionController  {

    @Resource
    private SysPermissionService sysPermissionService;

    /**
     * 分页查询权限功能
     * @param param
     * @return result<PageInfo<SysPermissionDTO>>
     */
    @ApiOperation("分页查询权限功能")
	@GetMapping(value = "/oms/sys_permission/list")
    public Result<PageInfo<SysPermissionDTO>> getSysPermissionList(@ModelAttribute SysPermissionDTO param) {
        Result<PageInfo<SysPermissionDTO>> result = new Result<>();
        SysPermission sysPermission = BeanCopierUtils.convertObjectTrim(param,SysPermission.class);
        IPage<SysPermission> page = sysPermissionService.page(new Page<>(param.getPageNum(), param.getPageSize()),new LambdaQueryWrapper<>(sysPermission));
        result.setData(new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),BeanCopierUtils.convertList(page.getRecords(), SysPermissionDTO.class)));
        return result;
    }

    @ApiOperation("查询权限功能不分页")
    @GetMapping(value = {"/oms/sys_permission/list_nopage","/crm/sys_permission/list_nopage"})
    public Result<List<SysPermissionDTO>> getSysPermissionListNopage(@ModelAttribute SysPermissionDTO param) {
        Result<List<SysPermissionDTO>> result = new Result<>();
        param.setPlatform(SecurityContext.getUser().getPlatform());
        SysPermission sysPermission = BeanCopierUtils.convertObjectTrim(param,SysPermission.class);
        List<SysPermission> list = sysPermissionService.list(new LambdaQueryWrapper<>(sysPermission));
        result.setData(BeanCopierUtils.convertList(list,SysPermissionDTO.class));
        return result;
    }

    @ApiOperation("查询权限功能树形结构")
    @GetMapping(value = {"/crm/sys_permission/tree"})
    public Result<List<SysPermissionDTO>> getSysPermissionTree() {
        Result<List<SysPermissionDTO>> result = new Result<>();
        List<SysPermissionDTO> list = sysPermissionService.getSysPermissionTree();
        result.setData(list);
        return result;
    }

    /**
     * 根据ID查询权限功能
     * @param id
     * @return result<SysPermissionDTO>
     */
    @ApiOperation("根据ID查询权限功能")
	@GetMapping(value = "/oms/sys_permission/info")
    public Result<SysPermissionDTO> getSysPermissionById(@RequestParam("id") Long id) {
        Result<SysPermissionDTO> result = new Result<>();
        SysPermission sysPermission = sysPermissionService.getById(id);
        result.setData(BeanCopierUtils.convertObject(sysPermission,SysPermissionDTO.class));
        return result;
    }

    /**
     * 保存权限功能
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "保存权限功能",type = LogType.OPERATELOG)
    @ApiOperation("保存权限功能")
	@PostMapping(value = "/oms/sys_permission/save")
    public Result<Long> save(@RequestBody SysPermissionDTO param) {
        Result<Long> result = new Result<>();
        SysPermission permission = sysPermissionService.getPermissionByCode(param.getCode());
        if(null != permission && null != permission.getId() && (null == param.getId() || !permission.getId().equals(param.getId()))) {
            return result.errorResult(ErrorCode.BAD_REQUEST,"权限编号已存在");
        }
        Long id = sysPermissionService.saveSysPermission(param);
        result.setData(id);
        return result;
    }
	
	/**
     * 根据ID删除权限功能
     * @param param
     * @return result<Boolean>
     */
    @SysLog(value = "根据ID删除权限功能",type = LogType.OPERATELOG)
    @ApiOperation("根据ID删除权限功能")
	@PostMapping(value = "/oms/sys_permission/delete")
    public Result<Boolean> deleteSysPermissionById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        sysPermissionService.deleteSysPermissionById(param.getId());
        result.setData(true);
        return result;
    }

}
