package com.dz.ms.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.config.RedisDistributedLock;
import com.dz.common.core.dto.user.UserSimpleDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.ms.user.dto.AppointmentStatsDTO;
import com.dz.ms.user.entity.AppointmentBanner;
import com.dz.ms.user.entity.AppointmentConfig;
import com.dz.ms.user.entity.AppointmentRecord;
import com.dz.ms.user.entity.AppointmentSlotConfig;
import com.dz.ms.user.mapper.*;
import com.dz.ms.user.service.AppointmentService;
import com.dz.ms.user.service.UserInfoService;
import com.dz.ms.user.vo.AppointmentDetailsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AppointmentServiceImpl implements AppointmentService {

    @Autowired
    private AppointmentConfigMapper appointmentConfigMapper;
    @Autowired
    private AppointmentBannerMapper appointmentBannerMapper;
    @Autowired
    private AppointmentRecordMapper appointmentRecordMapper;
    @Autowired
    private AppointmentSlotConfigMapper appointmentSlotConfigMapper;
    @Autowired
    private WhiteListMapper whiteListMapper;
    @Autowired
    private UserInfoService userInfoService;
    @Resource
    private RedisService redisService;
    @Resource
    private RedisDistributedLock redisDistributedLock;

    private static final String APPOINTMENT_DATE_FORMAT = "yyyy/MM/dd";

    private static final String APPOINTMENT_DATE_VIEW_FORMAT = "yyyy年MM月dd日";

    @Override
    public AppointmentDetailsVO getAppointmentDetails() {

        Long userId = SecurityContext.getUser().getUid();
        UserSimpleDTO simpleInfo = userInfoService.getCurrentUserSimpleInfo();
        AppointmentDetailsVO detailsVO = new AppointmentDetailsVO();
        // 默认只有一个预约活动
        AppointmentConfig config = appointmentConfigMapper.findById(1L);
        if (config != null) {
            String maxDate = appointmentSlotConfigMapper.findMaxAppointmentDate(config.getId());
            if (maxDate != null) {
                try {
                    LocalDate maxAppointmentDate = LocalDate.parse(maxDate, DateTimeFormatter.ofPattern("yyyy/MM/dd"));
                    LocalDate today = LocalDate.now();
                    detailsVO.setExpired(maxAppointmentDate.isBefore(today));
                } catch (Exception e) {
                    throw new BusinessException("日期解析错误");
                }
            } else {
                throw new BusinessException("无活动日期");
            }
        } else {
            throw new BusinessException("无预约活动");
        }
        detailsVO.setName(config.getName());

        List<AppointmentBanner> banners = appointmentBannerMapper.findByAppointmentId(config.getId());
        if (!CollectionUtils.isEmpty(banners)) {
            detailsVO.setBannerUrl(banners.stream().map(AppointmentBanner::getBannerUrl).reduce((a, b) -> a + "," + b).orElse(""));
        }

        // 根据memberCode判断用户是否在白名单内
        String memberCode = simpleInfo.getCardNo();
        detailsVO.setInWhitelist(whiteListMapper.isMemberInWhiteList(memberCode));

        // 判断用户是否已经预约
        AppointmentRecord existingRecord = this.getUserAppointments(config.getId(), userId);
        detailsVO.setAlreadyBooked(existingRecord != null);

        // 设置用户手机号
        detailsVO.setPhone(simpleInfo.getMobile());
        if (existingRecord != null) {// 如果用户有预约，返回用户预约信息
            AppointmentDetailsVO.UserAppointmentVO userAppointmentVO = new AppointmentDetailsVO.UserAppointmentVO();
            userAppointmentVO.setUserName(existingRecord.getName());
            userAppointmentVO.setUserId(userId);
            userAppointmentVO.setMemberCode(memberCode);
            userAppointmentVO.setSessionInfo(convert(existingRecord.getAppointmentSession()));
            userAppointmentVO.setAppointmentDate(convertViewDate(existingRecord.getAppointmentDate()));
            userAppointmentVO.setAppointmentTime(existingRecord.getAppointmentSlot());
            detailsVO.setUserAppointment(userAppointmentVO);
        }

        return detailsVO;
    }

    @Override
    @Transactional
    public void bookAppointment(String name, String phone, String appointmentDate, String appointmentSlot) {
        Long userId = SecurityContext.getUser().getUid();
        String redisKey = "USER_APPOINTMENT_LOCK_" + userId;
        boolean lock = redisService.lock(redisKey, 3);
        if (!lock) {
            throw new BusinessException("预约中请稍后重试");
        }
        UserSimpleDTO simpleInfo = userInfoService.getCurrentUserSimpleInfo();

        // 默认只有一个预约活动
        AppointmentConfig config = appointmentConfigMapper.findById(1L);
        if (null != this.getUserAppointments(config.getId(), userId)) {
            throw new BusinessException("已经预约");
        }

        if (!whiteListMapper.isMemberInWhiteList(simpleInfo.getCardNo())) {
            throw new BusinessException("不满足预约条件");
        }
        // 解析appointmentSlot获取startTime和endTime
        String[] timeParts = appointmentSlot.split("-");
        String startTime = timeParts[0].trim();
        String endTime = timeParts[1].trim();

        // 查询指定日期和时间段的库存
        Integer remainingStock = appointmentSlotConfigMapper.findRemainingStockByDateAndTime(config.getId(), appointmentDate, startTime, endTime);
        if (remainingStock == null) {
            throw new BusinessException("该场次不存在");
        }
        if (remainingStock <= 0) {
            throw new BusinessException("该场次已约满");
        }

        String lockKey = "redis_distributed_lock:bookAppointment:" + config.getId() + ":" + appointmentDate + ":" + appointmentSlot;
        String requestId = UUID.randomUUID().toString();
        if (redisDistributedLock.tryGetDistributedLockWithRetry(lockKey, requestId, RedisDistributedLock.EXPIRE_TIME, RedisDistributedLock.RETRY_TIMES, RedisDistributedLock.SLEEP_TIME)) {
            try {
                // 更新库存
                int updated = appointmentSlotConfigMapper.updateAppointmentSlotConfig(config.getId(), appointmentDate, startTime, endTime, remainingStock - 1);
                if (updated == 0) {
                    throw new BusinessException("该场次已约满");
                }
                AppointmentRecord record = new AppointmentRecord();
                record.setUserId(userId);
                record.setAppointmentId(config.getId());
                record.setName(name);
                record.setPhone(phone);
                // appointmentSlot 是 appointmentDate 这天的第几场次
                Integer appointmentSession = getAppointmentSessionByDateAndStartTime(appointmentDate, appointmentSlot.split("-")[0].trim());
                record.setAppointmentSession(appointmentSession);
                record.setAppointmentDate(appointmentDate);
                record.setAppointmentSlot(appointmentSlot);
                record.setMemberCode(simpleInfo.getCardNo());
                record.setCreatedTime(new Date());
                record.setStatus("BOOKED");
                appointmentRecordMapper.insert(record);
            } finally {
                redisDistributedLock.releaseDistributedLock(lockKey, requestId);
            }
        } else {
            // 处理获取锁失败的情况
            throw new BusinessException("获取锁失败");
        }
    }

    // 新增方法：获取指定日期和开始时间的场次序号
    private Integer getAppointmentSessionByDateAndStartTime(String date, String startTime) {
        // 默认只有一个预约活动
        AppointmentConfig config = appointmentConfigMapper.findById(1L);
        List<AppointmentSlotConfig> slots = appointmentSlotConfigMapper.findByAppointmentIdAndAppointmentDateOrderByStartTime(config.getId(), date);
        for (int i = 0; i < slots.size(); i++) {
            if (slots.get(i).getStartTime().equals(startTime)) {
                return i + 1;
            }
        }
        return null; // 如果未找到对应的startTime，返回null
    }

    @Override
    public AppointmentRecord getUserAppointments(Long appointmentId, Long userId) {
        return appointmentRecordMapper.selectOne(new QueryWrapper<AppointmentRecord>().eq("user_id", userId).eq("appointment_id", appointmentId));
    }

    @Override
    public List<String> getAppointmentDates() {
        // 默认只有一个预约活动
        AppointmentConfig config = appointmentConfigMapper.findById(1L);
        if (config != null) {
            List<AppointmentSlotConfig> slots = appointmentSlotConfigMapper.findByAppointmentIdAndDateAfterToday(config.getId());
            if (!CollectionUtils.isEmpty(slots)) {
                return slots.stream().map(AppointmentSlotConfig::getDate).distinct().sorted().collect(Collectors.toList());
            }
        }
        return Collections.emptyList();
    }

    @Override
    public List<AppointmentStatsDTO> getAppointmentDatesStats() {
        // 默认只有一个预约活动
        AppointmentConfig config = appointmentConfigMapper.findById(1L);
        if (config != null) {
            List<AppointmentSlotConfig> slots = appointmentSlotConfigMapper.findByAppointmentIdAndDateAfterToday(config.getId());
            if (!CollectionUtils.isEmpty(slots)) {
                Map<String, List<AppointmentSlotConfig>> groupByDate = slots.stream()
                        .sorted()
                        .collect(Collectors.groupingBy(AppointmentSlotConfig::getDate));
                List<AppointmentStatsDTO> statsList = new ArrayList<>();
                groupByDate.forEach((date, slotList) -> {
                    AppointmentStatsDTO stats = new AppointmentStatsDTO();
                    stats.setId(date);
                    stats.setName(convertViewDate(date));
                    boolean idFull = true;
                    Integer remainingStock = 0;
                    for (AppointmentSlotConfig slot : slotList) {
                        if (slot.getRemainingStock() > 0) {
                            idFull = false;
                            remainingStock += slot.getRemainingStock();
                        }
                    }
                    stats.setFull(idFull);
                    stats.setRemainingStock(remainingStock);
                    statsList.add(stats);
                });
                return statsList.stream().sorted().collect(Collectors.toList());
            }
        }
        return Collections.emptyList();
    }

    @Override
    public List<String> getAppointmentSlots(String appointmentDate) {
        // 默认只有一个预约活动
        AppointmentConfig config = appointmentConfigMapper.findById(1L);
        if (config != null) {
            List<AppointmentSlotConfig> slots = appointmentSlotConfigMapper.findByAppointmentIdAndAppointmentDate(config.getId(), appointmentDate);
            if (!CollectionUtils.isEmpty(slots)) {
                return slots.stream().map(AppointmentSlotConfig::getSlot).collect(Collectors.toList());
            }
        }
        return Collections.emptyList();
    }

    @Override
    public List<AppointmentStatsDTO> getAppointmentSlotsStats(String appointmentDate) {
        // 默认只有一个预约活动
        AppointmentConfig config = appointmentConfigMapper.findById(1L);
        if (config != null) {
            List<AppointmentSlotConfig> slots = appointmentSlotConfigMapper.findByAppointmentIdAndAppointmentDate(config.getId(), appointmentDate);
            if (!CollectionUtils.isEmpty(slots)) {
                List<AppointmentStatsDTO> statsList = new ArrayList<>();
                for (AppointmentSlotConfig slot : slots) {
                    AppointmentStatsDTO stats = new AppointmentStatsDTO();
                    stats.setId(slot.getSlot());
                    stats.setName(slot.getSlot());
                    stats.setFull(slot.getRemainingStock() <= 0);
                    stats.setRemainingStock(slot.getRemainingStock());
                    statsList.add(stats);
                }
                return statsList.stream().sorted().collect(Collectors.toList());
            }
        }
        return Collections.emptyList();
    }

    @Override
    public PageInfo<AppointmentRecord> listAppointmentsByDate(Integer pageNum, Integer pageSize, String appointmentDate) {
        // 默认只有一个预约活动
        AppointmentConfig config = appointmentConfigMapper.findById(1L);
        if (config != null) {
            QueryWrapper<AppointmentRecord> queryWrapper = new QueryWrapper<AppointmentRecord>()
                    .eq(StringUtils.isNotBlank(appointmentDate), "appointment_date", appointmentDate);
            IPage<AppointmentRecord> page = appointmentRecordMapper.selectPage(new Page<>(pageNum, pageSize), queryWrapper);
            return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), page.getRecords());
        }
        return new PageInfo<>();
    }

    @Override
    public List<AppointmentRecord> exportAppointmentsByDate(String appointmentDate) {
        return appointmentRecordMapper.findByAppointmentDate(appointmentDate);
    }

    @Override
    public boolean isAppointmentSlotFull(String appointmentDate, String appointmentSlot) {
        // 默认只有一个预约活动
        AppointmentConfig config = appointmentConfigMapper.findById(1L);
        if (config == null) {
            return true; // 如果活动不存在，认为场次已约满
        }

        // 解析appointmentSlot获取startTime和endTime
        String[] timeParts = appointmentSlot.split("-");
        String startTime = timeParts[0].trim();
        String endTime = timeParts[1].trim();

        // 查询指定日期和时间段的库存
        Integer remainingStock = appointmentSlotConfigMapper.findRemainingStockByDateAndTime(config.getId(), appointmentDate, startTime, endTime);
        // 如果库存不存在或已约满，返回true
        return remainingStock == null || remainingStock <= 0;
    }

    private static final Map<Integer, String> numberMap = new HashMap<>();

    static {
        numberMap.put(1, "一");
        numberMap.put(2, "二");
        numberMap.put(3, "三");
        numberMap.put(4, "四");
        numberMap.put(5, "五");
        numberMap.put(6, "六");
        numberMap.put(7, "七");
        numberMap.put(8, "八");
        numberMap.put(9, "九");
    }

    public static String convert(int number) {
        return numberMap.getOrDefault(number, number + "");
    }

    private String convertViewDate(String date) {
        // 日期格式转换
        String appointmentDate = "";
        if (StringUtils.isNotBlank(date)) {
            try {
                LocalDate localDate = LocalDate.parse(date, DateTimeFormatter.ofPattern(APPOINTMENT_DATE_FORMAT));
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(APPOINTMENT_DATE_VIEW_FORMAT);
                appointmentDate = localDate.format(formatter);
            } catch (Exception e) {
                log.error("日期解析错误, appointmentDate: {}, error: {}", appointmentDate, e.getMessage(), e);
                throw new BusinessException("日期解析错误");
            }
        } else {
            appointmentDate = null;
        }
        return appointmentDate;
    }
}