package com.dz.ms.sales.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;


@Getter
@Setter
@NoArgsConstructor
@Table("用户抽奖记录")
@TableName(value = "user_lottery_prizes")
public class UserLotteryPrizes implements Serializable {
    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "id")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = true, comment = "活动标识", isIndex = true)
    private String campaignCode;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, comment = "用户类型 0全部用户 1购买指定商品的用户 2其他导入的用户 3报名中奖的用户")
    private Integer targetType;

    @Columns(type = ColumnType.BIGINT, length = 11, isNull = false, comment = "用户ID", isIndex = true)
    private Long uid;
    @Columns(type = ColumnType.VARCHAR, length = 50, isNull = false, comment = "openid")
    private String openid;
    @Columns(type = ColumnType.VARCHAR, length = 255, isNull = true, comment = "unionid", isIndex = true)
    private String unionid;
    @Columns(type = ColumnType.VARCHAR,length = 32,isNull = true,comment = "会员名")
    private String username;
    @Columns(type = ColumnType.VARCHAR, length = 36, isNull = false, comment = "手机号码")
    private String mobile;
    @Columns(type = ColumnType.VARCHAR, length = 50, isNull = false, comment = "会员卡号")
    private String cardNo;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "绑定奖品id")
    private Long prizesId;
    @Columns(type = ColumnType.VARCHAR, length = 255, isNull = true, comment = "奖品名称")
    private String prizesName;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "奖品类型 0空 1积分 2券")
    private Integer prizesType;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "奖品等级编码 0周年特别奖 1一等奖 2二等奖 3三等奖 4四等奖 5鼓励奖")
    private Integer prizesLevelCode;
    @Columns(type = ColumnType.VARCHAR, length = 255, isNull = true, comment = "奖品等级")
    private String prizesLevel;
    @Columns(type = ColumnType.VARCHAR, length = 500, isNull = true, comment = "素材")
    private String imageUrl;
    @Columns(type = ColumnType.VARCHAR, length = 500, isNull = true, comment = "详情图片")
    private String detailImageUrl;
    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = true, comment = "描述")
    private String description;

    @Columns(type = ColumnType.TEXT, length = 0, isNull = true, comment = "抽奖描述信息")
    private String contentJson;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, defaultValue = "0", comment = "积分数量")
    private Integer pointsNum;
    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = true, defaultValue = "", comment = "券code")
    private String couponCode;


    @Columns(type = ColumnType.VARCHAR, length = 255, isNull = true, comment = "备注")
    private String remark;

    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "是否删除 0未删除 1已删除")
    @TableLogic
    private Integer isDeleted;
    @Columns(type = ColumnType.BIGINT, length = 11, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT, length = 11, isNull = false, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT, length = 11, isNull = true, comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    public UserLotteryPrizes(String campaignCode, Integer targetType, Long uid, String openid,
                             String unionid, String username, String mobile, String cardNo, Long prizesId, String prizesName,
                             Integer prizesType, Integer prizesLevelCode, String prizesLevel, String imageUrl, String detailImageUrl,
                             String description, String contentJson, Integer pointsNum, String couponCode) {
        this.campaignCode = campaignCode;
        this.targetType = targetType;
        this.uid = uid;
        this.openid = openid;
        this.unionid = unionid;
        this.username = username;
        this.mobile = mobile;
        this.cardNo = cardNo;
        this.prizesId = prizesId;
        this.prizesName = prizesName;
        this.prizesType = prizesType;
        this.prizesLevelCode = prizesLevelCode;
        this.prizesLevel = prizesLevel;
        this.imageUrl = imageUrl;
        this.detailImageUrl = detailImageUrl;
        this.description = description;
        this.contentJson = contentJson;
        this.pointsNum = pointsNum;
        this.couponCode = couponCode;
    }
}
