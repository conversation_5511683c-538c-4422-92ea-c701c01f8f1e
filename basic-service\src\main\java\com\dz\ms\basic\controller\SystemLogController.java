package com.dz.ms.basic.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.basic.SystemLogDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.fegin.basic.SystemLogFeignClient;
import com.dz.common.core.fegin.user.SysUserFeginClient;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.ms.basic.dto.AlarmConfigDTO;
import com.dz.ms.basic.entity.SystemLog;
import com.dz.ms.basic.service.SystemLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Api(tags="系统日志")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class SystemLogController implements SystemLogFeignClient {

    @Resource
    private SystemLogService systemLogService;
    @Resource
    private SysUserFeginClient sysUserFeginClient;

    /**
     * 分页查询系统日志
     * @param param
     * @return result<PageInfo<SystemLogDTO>>
     */
    @ApiOperation("分页查询系统日志")
	@GetMapping(value = "/crm/system_log/list")
    public Result<PageInfo<SystemLogDTO>> getSystemLogList(@ModelAttribute SystemLogDTO param) {
        Result<PageInfo<SystemLogDTO>> result = new Result<>();
        SystemLog systemLog = BeanCopierUtils.convertObjectTrim(param,SystemLog.class);
        LambdaQueryWrapper<SystemLog> wrapper = new LambdaQueryWrapper<>(systemLog);
        if(null != param.getCreatedStart()) {
            wrapper.ge(SystemLog::getEndTime,param.getCreatedStart());
        }
        if(null != param.getCreatedEnd()) {
            wrapper.le(SystemLog::getEndTime,param.getCreatedEnd());
        }
        wrapper.orderByDesc(SystemLog::getId);
        IPage<SystemLog> page = systemLogService.page(new Page<>(param.getPageNum(), param.getPageSize()), wrapper);
        result.setData(new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),BeanCopierUtils.convertList(page.getRecords(),SystemLogDTO.class)));
        return result;
    }

    /**
     * 根据ID查询系统日志
     * @param id
     * @return result<SystemLogDTO>
     */
    @ApiOperation("根据ID查询系统日志")
	@GetMapping(value = "/crm/system_log/info")
    public Result<SystemLogDTO> getSystemLogById(@RequestParam("id") Long id) {
        Result<SystemLogDTO> result = new Result<>();
        SystemLog systemLog = systemLogService.getById(id);
        result.setData(BeanCopierUtils.convertObject(systemLog,SystemLogDTO.class));
        return result;
    }

    /**
     * 添加系统日志
     * @param param
     * @return result<Long>
     */
    @ApiOperation("添加系统日志")
    @PostMapping(value = "/system_log/add")
    public Result<Object> addLog(@RequestBody SystemLogDTO param) {
        Result<Object> result = new Result<>();
        systemLogService.addSystemLog(param);
        return result;
    }

    @ApiOperation("添加接口内部错误日志")
    @PostMapping(value = "/system_log/add_error")
    public Result<Object> addErrorLog(@RequestParam("name")String name,@RequestParam(value = "url",required = false)String url,@RequestParam(value = "params",required = false)String params,
                                      @RequestParam(value = "exception",required = false)String exception,@RequestParam(value = "operator",required = false)Long operator,@RequestParam("tenantId")Long tenantId) {
        Result<Object> result = new Result<>();
        SystemLogDTO param = new SystemLogDTO();
        param.setLogType(LogType.INTERFACE.getType());
        param.setLogName(name);
        param.setRequestUrl(url);
        param.setParams(params);
        param.setEndTime(new Date());
        param.setState(0);
        param.setAlarmState(0);
        param.setException(exception);
        param.setOperator(operator);
        param.setTenantId(tenantId);
        systemLogService.addSystemLog(param);
        return result;
    }
	
	/**
     * 根据ID删除系统日志
     * @param id
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除系统日志")
	@PostMapping(value = "/system_log/delete")
    public Result<Boolean> deleteSystemLogById(@RequestParam("id") Long id) {
        Result<Boolean> result = new Result<>();
        systemLogService.removeById(id);
        result.setData(true);
        return result;
    }

    @ApiOperation("获取告警配置列表")
    @GetMapping(value = "/oms/system_alarm/list")
    public Result<List<AlarmConfigDTO>> getAlarmConfigList() {
        Result<List<AlarmConfigDTO>> result = new Result<>();
        List<AlarmConfigDTO> list = systemLogService.getAlarmConfigList();
        result.setData(list);
        return result;
    }

    @ApiOperation("保存告警配置")
    @PostMapping(value = "/oms/system_alarm/save")
    public Result<Long> saveAlarmConfig(@RequestBody AlarmConfigDTO param) {
        Result<Long> result = new Result<>();
        Long id = systemLogService.saveAlarmConfig(param);
        result.setData(id);
        return result;
    }

    @ApiOperation("删除告警配置")
    @PostMapping(value = "/oms/system_alarm/delete")
    public Result<Boolean> deleteAlarmConfig(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        systemLogService.deleteAlarmConfig(param.getId());
        result.setData(true);
        return result;
    }

}
