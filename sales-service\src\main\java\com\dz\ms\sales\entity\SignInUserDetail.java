package com.dz.ms.sales.entity;


import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/4
 */
@Getter
@Setter
@NoArgsConstructor
@Table(value = "用户打卡记录明细表")
@TableName(value = "sign_in_user_detail")
public class SignInUserDetail implements Serializable {


    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "用户打卡记录ID", isIndex = true)
    private Long signInUserId;

    @Columns(type = ColumnType.VARCHAR, length = 10, isNull = false, comment = "日期(yyyyMMdd)")
    private String signInDate;

    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = false, comment = "活动标识", isIndex = true)
    private String campaignCode;
    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = true, comment = "一级渠道")
    private String channelOne;
    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = true, comment = "二级渠道")
    private String channelTwo;

    @Columns(type = ColumnType.BIGINT, length = 11, isNull = false, comment = "用户ID", isIndex = true)
    private Long uid;
    @Columns(type = ColumnType.VARCHAR, length = 50, isNull = false, comment = "openid")
    private String openid;
    @Columns(type = ColumnType.VARCHAR, length = 50, isNull = false, comment = "unionid", isIndex = true)
    private String unionid;
    @Columns(type = ColumnType.VARCHAR, length = 32, isNull = true, comment = "会员名")
    private String username;
    @Columns(type = ColumnType.VARCHAR, length = 36, isNull = false, comment = "手机号码", isIndex = true)
    private String mobile;
    @Columns(type = ColumnType.VARCHAR, length = 50, isNull = false, comment = "会员卡号")
    private String cardNo;

    @Columns(type = ColumnType.INT, length = 10, isNull = false, defaultValue = "0", comment = "打卡天数，第几天，开启打卡为第0天")
    private Integer days;

    @Columns(type = ColumnType.VARCHAR, length = 1000, isNull = true, comment = "素材链接")
    private String materialUrl;
    @Columns(type = ColumnType.VARCHAR, length = 20, isNull = true, comment = "期待 1提升皮肤耐受性，缓解敏感症状 2增加保湿度 3改善肤色不均 4舒缓泛红现象 5改善毛孔粗大 6改善皮肤屏障，增强保护力（多选，逗号分隔）")
    private String expectation;

    // 第1-6天
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "舒适度")
    private Integer comfort;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "保湿效果")
    private Integer moisturize;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "吸收速度")
    private Integer absorption;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "持久效果")
    private Integer persistence;
//    @Columns(type = ColumnType.TINYINT, length = 3, isNull = true, defaultValue = "1", comment = "年龄段 1、18~26岁 2、27~36岁")
//    private Integer ageBracket;
    @Columns(type = ColumnType.VARCHAR, length = 200, isNull = true, comment = "改善")
    private String improve;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "整体满意度")
    private Integer satisfaction;

    // 第7天
    @Columns(type = ColumnType.VARCHAR, length = 200, isNull = true, comment = "使用感受")
    private String feeling;
//    @Columns(type = ColumnType.VARCHAR, length = 20, isNull = true, comment = "提升空间（可多选，逗号分隔）")
//    private String suggestion;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "评分")
    private Integer score;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "是否购买")
    private Integer purchase;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "推荐")
    private Integer recommend;


    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "打卡状态 0未打卡 1已打卡")
    private Integer state;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "是否补卡 0正常打卡 1补卡")
    private Integer supplementFlag;

    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "打卡时间")
    private Date signInTime;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "补卡时间")
    private Date supplementTime;

    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "失败状态 0正常 1失败")
    private Integer failFlag;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "是否删除 0未删除 1已删除")
    @TableLogic
    private Integer isDeleted;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;

    // 初始化数据构造
    public SignInUserDetail(Long signInUserId, String signInDate, String campaignCode, String channelOne, String channelTwo, Long uid, String openid, String unionid, String username, String mobile, String cardNo, Integer days) {
        this.signInUserId = signInUserId;
        this.signInDate = signInDate;
        this.campaignCode = campaignCode;
        this.channelOne = channelOne;
        this.channelTwo = channelTwo;
        this.uid = uid;
        this.openid = openid;
        this.unionid = unionid;
        this.username = username;
        this.mobile = mobile;
        this.cardNo = cardNo;
        this.days = days;
    }

    // 开启打卡构造
    public SignInUserDetail(Long signInUserId, String signInDate, String campaignCode, String channelOne, String channelTwo, Long uid, String openid, String unionid, String username, String mobile, String cardNo, Integer days, String materialUrl, String expectation) {
        this.signInUserId = signInUserId;
        this.signInDate = signInDate;
        this.campaignCode = campaignCode;
        this.channelOne = channelOne;
        this.channelTwo = channelTwo;
        this.uid = uid;
        this.openid = openid;
        this.unionid = unionid;
        this.username = username;
        this.mobile = mobile;
        this.cardNo = cardNo;
        this.days = days;
        this.materialUrl = materialUrl;
        this.expectation = expectation;
    }

    // 1-6天构造

    public SignInUserDetail(Long signInUserId, String signInDate, String campaignCode, String channelOne, String channelTwo, Long uid, String openid,
                            String unionid, String username, String mobile, String cardNo, Integer days, Integer comfort,
                            Integer moisturize, Integer absorption, Integer persistence,
//                            Integer ageBracket, String improve,
                            Integer satisfaction, Integer state, Integer supplementFlag, Date signInTime) {
        this.signInUserId = signInUserId;
        this.signInDate = signInDate;
        this.campaignCode = campaignCode;
        this.channelOne = channelOne;
        this.channelTwo = channelTwo;
        this.uid = uid;
        this.openid = openid;
        this.unionid = unionid;
        this.username = username;
        this.mobile = mobile;
        this.cardNo = cardNo;
        this.days = days;
        this.comfort = comfort;
        this.moisturize = moisturize;
        this.absorption = absorption;
        this.persistence = persistence;
//        this.ageBracket = ageBracket;
//        this.improve = improve;
        this.satisfaction = satisfaction;
        this.state = state;
        this.supplementFlag = supplementFlag;
        this.signInTime = signInTime;

    }

    // 第7天构造
    public SignInUserDetail(Long signInUserId, String signInDate, String campaignCode, String channelOne, String channelTwo,
                            Long uid, String openid, String unionid, String username, String mobile, String cardNo, Integer days,
                            String materialUrl, String feeling, Integer score,
                            String improve, Integer purchase,
                            Integer recommend,Integer satisfaction, Integer state, Integer supplementFlag, Date signInTime) {
        this.signInUserId = signInUserId;
        this.signInDate = signInDate;
        this.campaignCode = campaignCode;
        this.channelOne = channelOne;
        this.channelTwo = channelTwo;
        this.uid = uid;
        this.openid = openid;
        this.unionid = unionid;
        this.username = username;
        this.mobile = mobile;
        this.cardNo = cardNo;
        this.days = days;
        this.materialUrl = materialUrl;
        this.feeling = feeling;
        this.score = score;
        this.improve = improve;
        this.purchase = purchase;
        this.recommend = recommend;
        this.satisfaction = satisfaction;
        this.state = state;
        this.supplementFlag = supplementFlag;
        this.signInTime = signInTime;

    }

}
