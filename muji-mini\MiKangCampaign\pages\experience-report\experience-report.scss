/* MiKangCampaign/pages/experience-report/experience-report.wxss */
.page-container {
  position: relative;
}

.report-container {
  background-size: 100% 100%;
  min-height: 1620rpx;
  height: 100vh;
  width: 750rpx; /* 考虑左右内边距 */
  position: absolute;
  top: 0;
  padding-left: 66rpx;
  padding-right: 66rpx;
  padding-top: 176rpx;
  box-sizing: border-box; /* 确保内边距包含在宽度内 */
  overflow-x: visible; /* 防止裁剪 */
}

.header-img {
  // position: relative;
  // top: 176rpx;
  // left: 74rpx;
  width: 600rpx;
  height: 132rpx;
}
.report-image-box {
  width: 600rpx;
  height: 488rpx;
  margin-top: 18rpx;
  display: flex;
}

// .report-image-left {
//   width: 390rpx;
//   height: 488rpx;
// }

.right-box {
  height: 228rpx;
  width: 206rpx;
  position: relative;
}

.after-box {
  margin-top: 33rpx;
}

.report-image-right-box {
  margin-left: 22rpx;
}

.report-icon {
  position: absolute;
  width: 80rpx;
  bottom: 5rpx;
  left: 10rpx;
}

.user-photo-box {
  overflow: hidden;
  width: 174rpx;
  height: 174rpx;
  top: 15rpx;
  left: 17rpx;
  position: absolute;
}

.user-photo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.rating-box {
  // margin-top: 18rpx;
  margin-top: 34rpx;
  display: flex;
  align-items: center;
  .rating {
    font-family: MUJIFont2020;
    font-weight: 900;
    font-size: 52rpx;
    line-height: 64rpx;
    height: 72rpx;
    color: white;
    display: flex;
    align-items: flex-end;
  }
}
.mk-icon-box {
  margin-top: 10rpx;
}

.rating-unit {
  font-family: MUJIFont2020;
  font-size: 16rpx;
  line-height: unset;
  margin-left: 4rpx;
  position: relative;
  top: 13rpx;
}

.rating-icon {
  width: 32rpx;
  margin-left: 16rpx;
}

.comment-box {
  display: flex;
  // flex-direction: column;
  // align-items: flex-start; /* 默认左对齐 */
  margin-top: 44rpx;
  padding-bottom: 48rpx;
  // border-bottom: 1px dashed white;
  width: 340rpx;
  position: relative;
  align-self: flex-start;
}

.comment-box image:last-child {
  align-self: flex-end; /* 最后一个元素右对齐 */
}

.comment-icon {
  width: 24rpx;
  height: 22rpx;
  &:first-child {
    margin-right: 10rpx;
  }

  &:last-child {
    position: absolute;
    right: 0;
    bottom: 30rpx;
  }
}

.comment {
  font-family: MUJIFont2020;
  font-size: 18rpx;
  line-height: 28rpx;
  letter-spacing: 0%;
  // text-align: center;
  vertical-align:middle;
  color: white;
  width: 300rpx;
  // margin: 20rpx 0;

  // display: -webkit-box;
  // -webkit-box-orient: vertical;
  // -webkit-line-clamp: 2; /* 限制为两行 */
  // overflow: hidden;
  // text-overflow: ellipsis;
  // word-break: break-all; /* 确保长单词换行 */
}

.improve-box {
  margin-top: 6rpx;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(183rpx, 1fr));
  width: 100%; /* 容器占满宽度 */
  box-sizing: border-box;
}

.improve-card {
  margin-top: 40rpx;
  width: 183rpx;
  height: 60rpx;
  background-size: 100% 100%;
  font-family: MUJIFont2020;
  font-weight: 500;
  font-size: 24rpx;
  line-height: 40rpx;
  text-align: center;
  vertical-align: middle;
  display: flex;
  align-items: center;
  justify-content: center;
}

// .improve-text {
  
// }

.qr-code {
  margin-top: 30rpx;
  text-align: center;
  image {
    width: 96rpx;
    height: 96rpx;
    background-color: white;
    border-radius: 50%;
    padding: 4rpx;
  }
}

.btn-box {
  position: absolute;
  bottom: 100rpx;
  z-index: 9999;
  /* padding: 0rpx 50rpx ; */
  // margin-left: 50rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  width: calc(750rpx - 50rpx * 2);
  justify-content: space-between;
  button {
    width: 309rpx;
    height: 76rpx;
    font-weight: 500;
    line-height: 76rpx;
    font-size: 28rpx;
    color: white;
    border: 2rpx solid white;
    border-radius: 10rpx;
  }
}

.page-canvas {
  position: fixed;
  left: 0;
  // top: 20px;
  top: 200vh;
  width: 560rpx; // canvas按照750的宽度绘制
  height: 1128rpx;
}

.rader-canvas {
  position: fixed;
  left: 0;
  top: 200vh;
  // top: 300px;
  // left: 200rpx;
  width: 326rpx;
  height: 280rpx;
}

.share_mask {
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  position: fixed;
  top: 0;
  left: 0;
}

.user-common-box {
  display: flex;
  min-height: 448rpx;
  justify-content: center; /* 水平居中整个容器内容 */
  align-items: center; /* 垂直居中整个容器内容 */
}

.user-common-title {
  position: relative;
}

.user-common-img {
  width: 172rpx;
  position: absolute;
  right: -285rpx;
  top: -40rpx;
}

.rader-img {
  width: 326rpx;
  height: 265rpx;
  margin-left: -30rpx;
}

