package com.dz.ms.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.IsEnableDTO;
import com.dz.common.core.dto.user.GradeListDTO;
import com.dz.ms.user.dto.GradeInfoDTO;
import com.dz.ms.user.entity.GradeInfo;

import java.util.List;
import java.util.Map;

/**
 * 会员等级接口
 * @author: zlf
 * @date:   2023/02/14 17:44
 */
public interface GradeInfoService extends IService<GradeInfo> {

	/**
     * 查询会员等级列表
     * @param param 查询条件入参
     * @return PageInfo<GradeInfoDTO>
     */
    List<GradeInfoDTO> getGradeInfoList(GradeInfoDTO param);

    /**
     * 根据ID/CODE查询会员等级
     * @param param
     * @return GradeInfoDTO
     */
    GradeInfoDTO getGradeInfo(IdCodeDTO param,Long tenantId);

    /**
     * 保存会员等级
     * @param param 新增或更新入参
     * @return Long
     */
    Long saveGradeInfo(GradeInfoDTO param);

    /**
     * 根据ID删除会员等级
     * @param param 单ID/CODE POST请求通用DTO
     */
    void deleteGradeInfoById(IdCodeDTO param);
    
    /**
     * 根据ID修改会员启停状态
     * @param param ID NUMBER 通用DTO
     */
    void updateGradeInfoStateById(IsEnableDTO param);

    /**
     * 获取会员等级CodeToName MAP
     * @return
     */
    List<GradeListDTO> getCodeToNameGradeMap(Long tenantId);

    /**
     * 获取会员等级NameToCode MAP
     * @return
     */
    public Map<String,String> getNameToCodeGradeMap(Long tenantId);

    /**
     * 批量更新会员等级
     * @param list
     */
    void updateGradeBatch(List<GradeInfoDTO> list);
}
