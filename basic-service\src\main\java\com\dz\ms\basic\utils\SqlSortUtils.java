package com.dz.ms.basic.utils;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;

public class SqlSortUtils {


   public static <T> SFunction<T, ?> caseWhen(SFunction<T, ?> column, Object matchValue, Object resultValue) {
    return new SFunction<T, Object>() {
        @Override
        public Object apply(T t) {
            return null; // 实现 apply 方法
        }

        public String getSqlSegment() {
            return String.format("CASE WHEN %s = %s THEN %s ELSE %s END",
                    getColumn(), matchValue, resultValue, getColumn());
        }

        public String getColumn() {
            // 实现 getColumn 方法
            return column.toString();
        }
    };
}
}
