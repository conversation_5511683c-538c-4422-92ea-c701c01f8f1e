package com.dz.ms.sales.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 任务奖励列表
 */
@Getter
@Setter
@NoArgsConstructor
@Table("任务奖励")
@TableName(value = "t_task_reward")
public class TaskReward implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 奖励ID
     */
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "主键id")
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     *任务id
     */
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "任务id")
    private Long taskId;
    /**
     *奖励类型：1优惠券，2积分
     */
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "奖励类型：1优惠券，2积分")
    private Integer rewardType;
    /**
     *奖励券列表，多个用英文逗号隔开
     */
    @Columns(type = ColumnType.VARCHAR, length = 255, isNull = true, comment = "奖励券列表，多个用英文逗号隔开")
    private String couponIds;
    /**
     *奖励积分数量
     */
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "奖励积分数量")
    private Integer pointsNum;
    /**
     *奖励顺序
     */
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "奖励顺序")
    private Integer sortNum;
    /**
     *创建人
     */
    @Columns(type = ColumnType.VARCHAR, length = 255, isNull = true, comment = "创建人")
    private String createAt;
    /**
     *创建时间
     */
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "创建时间")
    private Date createTime;
    /**
     *修改人
     */
    @Columns(type = ColumnType.VARCHAR, length = 255, isNull = true, comment = "修改人")
    private String updateAt;
    /**
     *修改时间
     */
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "修改时间")
    private Date updateTime;
    /**
     *渠道
     */
    @Columns(type = ColumnType.BIGINT, length = 11, isNull = false, comment = "租户id")
    private Long tenantId;
}
