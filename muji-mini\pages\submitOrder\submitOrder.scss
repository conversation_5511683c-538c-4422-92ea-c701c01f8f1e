.pages-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;

  .cart-container {
    box-sizing: border-box;
    padding: 20rpx var(--page-margin);
  }

  .total-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20rpx;
    padding-top: 40rpx;
    border-top: 1px solid var(--background-gray-color);
    height: 42rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 28rpx;
    color: #3C3C43;
    line-height: 42rpx;
    letter-spacing: 1px;
    text-align: left;
    font-style: normal;

    view:first-child {
      //width: 58rpx;
      height: 42rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28rpx;
      color: #888888;
      line-height: 42rpx;
      letter-spacing: 1px;
      text-align: left;
      font-style: normal;
    }
  }
}

.bottom-box {
  padding: var(--page-margin);
  padding-bottom: 60rpx;
  /* 避免被系统底部遮挡 */
  background-color: #fff;
  display: flex;
  justify-content: center;
  box-shadow: 0rpx 0rpx 15rpx 0rpx rgba(0, 0, 0, 0.04);
}
