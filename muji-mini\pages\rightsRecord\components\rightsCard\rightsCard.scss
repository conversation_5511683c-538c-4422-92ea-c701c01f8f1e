.rights-card {
  position: relative;
  display: flex;
  // border-bottom: 1rpx solid #eee;
  box-sizing: border-box;
  padding: 40rpx;
  width: 670rpx;
  height: 240rpx;
  background: #FFFFFF;
  border-radius: 10rpx;
  margin-bottom: 30rpx;

  .icon-box {
    // flex: 1;
    width: 160rpx;
    height: 160rpx;
    background: #F6F6F6;
    margin-right: 20rpx;

    image {
      width: 100%;
      height: 100%;
    }
  }


  .box-info {
    width: 410rpx;
    .box-title {
      height: 40rpx;
      width: 100%;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28rpx;
      color: #3C3C43;
      line-height: 40rpx;
      text-align: left;
      font-style: normal;
      margin-bottom: 8rpx;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .box-detail {
      width: 100%;
      height: 28rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 20rpx;
      color: #3C3C43;
      line-height: 28rpx;
      text-align: left;
      font-style: normal;
      margin-bottom: 46rpx;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .box-time {
      height: 28rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 20rpx;
      color: #888888;
      line-height: 28rpx;
      text-align: left;
      font-style: normal;
    }
  }
}
