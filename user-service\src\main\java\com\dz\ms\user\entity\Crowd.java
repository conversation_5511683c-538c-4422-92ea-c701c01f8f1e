package com.dz.ms.user.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.io.Serializable;
import java.util.Date;

/**
 * 人群包
 * @author: yibo
 * @date:   2024/11/18 13:53
 */
@Getter
@Setter
@NoArgsConstructor
@Table("人群包")
@TableName(value = "crowd")
public class Crowd implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "主键id")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "人群包名称")
    private String crowdName;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = true,comment = "人群包类型 0条件规则 2导入人群包")
    private Integer crowdType;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = true,defaultValue = "0",comment = "启停状态 0启用 1停用")
    private Integer crowdStatus;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = false,defaultValue = "0",comment = "类型 0时间段 1永久启用")
    private Integer timeType;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = true,comment = "人群包可用开始时间  (为空则永久可用)")
    private Date startTime;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = true,comment = "人群包可用结束时间  (为空则永久可用)")
    private Date endTime;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "导入文件名")
    private String crowdFile;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    private Date createTime;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "修改时间")
    private Date updateTime;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = true,comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "租户id")
    private Long tenantId;

}
