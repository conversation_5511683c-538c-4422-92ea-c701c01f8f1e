package com.dz.ms.basic.dto;

import com.dz.common.base.dto.BaseDTO;
import com.dz.common.core.dto.basic.TenantConfigDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 小程序UI自定义配置DTO
 * @author: Handy
 * @date:   2022/11/21 11:17
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "小程序UI自定义配置")
public class NavigationConfigDTO extends BaseDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
//    @ApiModelProperty(value = "UI名称")
//    private String uiName;
    @ApiModelProperty(value = "背景色")
    private String bgColor;
    @ApiModelProperty(value = "主色值")
    private String color;
    @ApiModelProperty(value = "选中后色值")
    private String selectedColor;
    @ApiModelProperty(value = "导航配置内容json")
    private String content;
    @ApiModelProperty(value = "是否选中 0未选中 1选中")
    private Integer checked;
    @ApiModelProperty(value = "字体大小")
    private Integer fontSize;
    @ApiModelProperty(value = "基础配置")
    private TenantConfigDTO tenantConfig;

}
