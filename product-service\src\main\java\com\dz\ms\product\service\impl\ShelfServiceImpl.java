package com.dz.ms.product.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.CacheEvict;
import com.dz.common.core.annotation.Cacheable;
import com.dz.common.core.annotation.Lock;
import com.dz.common.core.config.RedisDistributedLock;
import com.dz.common.core.constants.CommonConstants;
import com.dz.common.core.constants.NumConstants;
import com.dz.common.core.constants.ProductConstants;
import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.dto.ExchangeStaticParamDTO;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.IdNumberDTO;
import com.dz.common.core.dto.export.ShelfExportDTO;
import com.dz.common.core.dto.product.ShelfActivityDTO;
import com.dz.common.core.dto.user.CrowdDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.user.CrowdFeignClient;
import com.dz.common.core.service.ExportService;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.CommonUtils;
import com.dz.common.core.utils.DateUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.product.CacheKeys;
import com.dz.ms.product.dto.ShelfCampaignDTO;
import com.dz.ms.product.dto.ShelfDTO;
import com.dz.ms.product.dto.ShelfProductDTO;
import com.dz.ms.product.dto.req.ShelfParamDTO;
import com.dz.ms.product.dto.req.ShelfProductSaveDTO;
import com.dz.ms.product.dto.req.ShelfProductSaveParamDTO;
import com.dz.ms.product.dto.req.ShelfSaveParamDTO;
import com.dz.ms.product.entity.Shelf;
import com.dz.ms.product.mapper.ShelfMapper;
import com.dz.ms.product.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品货架
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:27
 */
@Service
@Slf4j
public class ShelfServiceImpl extends ServiceImpl<ShelfMapper, Shelf> implements ShelfService {

    @Resource
    private ShelfMapper shelfMapper;
    @Resource
    private ShelfProductService shelfProductService;
    @Resource
    private CrowdFeignClient crowdFeignClient;
    @Resource
    private ExportService exportService;
    @Resource
    private RedisService redisService;
    @Resource
    private ShelfCampaignService shelfCampaignService;
    @Resource
    private ShelfCampaignRuleService shelfCampaignRuleService;
    @Resource
    private ShelfProductSuperscriptService shelfProductSuperscriptService;
    @Resource
    private RedisDistributedLock redisDistributedLock;

    /**
     * 分页查询商品货架
     *
     * @param param
     * @return PageInfo<ShelfDTO>
     */
    @Override
    public PageInfo<ShelfDTO> getShelfList(ShelfParamDTO param) {
        List<ShelfDTO> list = new ArrayList<>();
        if(param.getOnStartTime() != null || param.getOnEndTime() != null){
            if(param.getOnStartTime() == null || param.getOnEndTime() == null){
                throw new BusinessException(ErrorCode.BAD_REQUEST, "上架开始/结束时间都不能为空");
            }
            if(!param.getOnStartTime().before(param.getOnEndTime())){
                throw new BusinessException(ErrorCode.BAD_REQUEST, "上架开始时间不得晚于结束时间，同时，两者不可完全重合");
            }
        }
        IPage<Shelf> page = shelfMapper.selPageList(new Page<>(param.getPageNum(), param.getPageSize()), param);
        if(!CollectionUtils.isEmpty(page.getRecords())){
            Date date = new Date();
            list = BeanCopierUtils.convertList(page.getRecords(), ShelfDTO.class);
            //根据货架ID列表查询货架上架商品列表
            Map<Long,List<ShelfProductDTO>> productMap = shelfProductService.getProductMapByShelfIds(list.stream().map(ShelfDTO::getId).collect(Collectors.toList()),NumConstants.ONE);
            Map<Long, List<ShelfCampaignDTO>> shelfCampaignMap = shelfCampaignService.getShelfCampaignMap(list.stream().map(ShelfDTO::getId).collect(Collectors.toList()));
            for (ShelfDTO shelfDTO : list) {
                //货架状态
                if (Objects.nonNull(param.getShelfState())) {
                    shelfDTO.setShelfState(param.getShelfState());
                }
                if (Objects.isNull(param.getShelfState())){
                    if (shelfDTO.getOnStartTime().after(date)) {
                        shelfDTO.setShelfState(ProductConstants.Shelf.STATE_NO_START);
                    }
                    if (shelfDTO.getOnStartTime().before(date) && shelfDTO.getOnEndTime().after(date)) {
                        shelfDTO.setShelfState(ProductConstants.Shelf.STATE_IS_UP);
                    }
                    if (shelfDTO.getOnEndTime().before(date)) {
                        shelfDTO.setShelfState(ProductConstants.Shelf.STATE_IS_END);
                    }
                }
                //商品数量
                if (productMap != null && !productMap.isEmpty()) {
                    List<ShelfProductDTO> shelfProductDTOList = productMap.get(shelfDTO.getId());
                    if (!CollectionUtils.isEmpty(shelfProductDTOList)) {
                        shelfDTO.setProductSum(shelfProductDTOList.size());
                    }
                } else {
                    shelfDTO.setProductSum(NumConstants.ZERO);
                }
                //活动列表
                if(!CollectionUtils.isEmpty(shelfCampaignMap)){
                    List<ShelfCampaignDTO> shelfCampaignDTOList = shelfCampaignMap.get(shelfDTO.getId());
                    if(!CollectionUtils.isEmpty(shelfCampaignDTOList)){
                        shelfDTO.setShelfActivityList(BeanCopierUtils.convertList(shelfCampaignDTOList, ShelfActivityDTO.class));
                    }
                }
            }
        }
        return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), list);
    }

    /**
     * 根据ID查询商品货架
     * @param id id
     * @param isThrow 1:抛异常
     * @return ShelfDTO
     */
    @Override
    public ShelfDTO getShelfById(Long id,Integer isThrow) {
        Shelf shelf = shelfMapper.selectById(id);
        if(shelf == null && Objects.equals(isThrow,NumConstants.ONE)){
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[货架]未查询到此货架");
        }
        return BeanCopierUtils.convertObject(shelf, ShelfDTO.class);
    }
    /**
     * 获取所有货架信息
     * 本方法通过查询数据库中的货架表，获取所有货架的相关信息，并将其转换为DTO列表返回
     * 此方法有缓存,对货架进行修改时切记清除对应缓存
     * @return 货架DTO列表，包含所有货架的简化信息
     */
    @Override
    @Cacheable(prefix = CacheKeys.shelf.SHELF_QUERY_ALL, expire = CommonConstants.DAY_SECONDS)
    public List<ShelfDTO> getAllShelf() {
        List<ShelfDTO> list = new ArrayList<>();
        LambdaQueryWrapper<Shelf> selectWrapper = new LambdaQueryWrapper<Shelf>().select(Shelf::getId, Shelf::getName, Shelf::getOnType, Shelf::getOnStartTime, Shelf::getOnEndTime,
                Shelf::getPriority, Shelf::getLimitShow, Shelf::getGroupId, Shelf::getState, Shelf::getContent);
        List<Shelf> allPriorityList = shelfMapper.selectList(selectWrapper);
        if(!CollectionUtils.isEmpty(allPriorityList)){
            list = BeanCopierUtils.convertList(allPriorityList, ShelfDTO.class);
        }
        return list;
    }

    /**
     * 根据人群包id列表查询优先级排序后的货架列表
     * @param num 限制返回书架的数量
     * @return 返回根据优先级排序并限制数量的书架列表
     */
    @Override
    public List<ShelfDTO> getPrioritySortedShelf(Integer num) {
        //获取用户人群包信息
        List<Long> groupIdList = new ArrayList<>();
        Result<List<Long>> crowdListResult = crowdFeignClient.userHaveCrowd();
        //校验获取用户人群包信息接口是否成功
        if(crowdListResult.isSuccess() && !CollectionUtils.isEmpty(crowdListResult.getData())){
            groupIdList = crowdListResult.getData();
        }
        log.info("=========================getPrioritySortedShelf,uid:{},groupIdList:{}",SecurityContext.getUser().getUid(),CommonUtils.jsonStr(groupIdList));
        List<ShelfDTO> shelfList = getAllShelf();
        List<Long> finalGroupIdList = groupIdList;
        Date date = new Date();
        List<ShelfDTO> resList = shelfList.stream()
                .filter(shelfDTO -> (Objects.equals(shelfDTO.getLimitShow(), NumConstants.ZERO) || finalGroupIdList.contains(shelfDTO.getGroupId()))
                        && (shelfDTO.getOnStartTime().before(date) && shelfDTO.getOnEndTime().after(date))
                        && Objects.equals(shelfDTO.getState(), NumConstants.ONE))
                .sorted(Comparator.comparing(ShelfDTO::getPriority).reversed())
                .limit(num)
                .collect(Collectors.toList());
        resList.forEach(s -> s.setGroupIdList(finalGroupIdList));
        return resList;
    }

    /**
     * 根据人群包id列表查询优先级排序后的货架
     * @return 返回根据优先级排序的货架
     */
    @Override
    public ShelfDTO getPrioritySortedShelfOne() {
        ShelfDTO dto = null;
        Long uid = SecurityContext.getUser().getUid();
        List<ShelfDTO> shelfDTOList = this.getPrioritySortedShelf(NumConstants.ONE);
        if(!CollectionUtils.isEmpty(shelfDTOList)){
            dto = shelfDTOList.get(NumConstants.ZERO);
            redisService.set(CacheKeys.shelf.SHELF_UID + uid,dto,NumConstants.SIXTY);
        }
        return dto;
    }

    /**
     * 查询所有商品货架优先级数据
     * @return List<Integer>
     */
    @Override
    public List<ShelfDTO> getAllPriority() {
        List<ShelfDTO> list = new ArrayList<>();
        List<Shelf> allPriorityList = shelfMapper.selectList(new LambdaQueryWrapper<Shelf>().select(Shelf::getId,Shelf::getPriority));
        if(!CollectionUtils.isEmpty(allPriorityList)){
            list = BeanCopierUtils.convertList(allPriorityList, ShelfDTO.class);
        }
        return list;
    }

    /**
     * 保存商品货架
     *
     * @param param 入参
     * @return Long
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(prefix = CacheKeys.shelf.SHELF_QUERY_ALL)
    public Long saveShelf(ShelfSaveParamDTO param, boolean isAdd) {
        checkSaveParam(param,isAdd);
        Long uid = SecurityContext.getUser().getUid();
        Shelf shelf = BeanCopierUtils.convertObject(param,Shelf.class);
        String lockKey = CacheKeys.Locks.SHELF_ADD + SecurityContext.getUser().getUid();
        if(!isAdd){
            lockKey = CacheKeys.Locks.SHELF_UPDATE + param.getId();
        }
        boolean lock = redisService.lock(lockKey, NumConstants.THIRTY);
        if (lock) {
            try {
                if(Objects.equals(shelf.getOnType(),ProductConstants.Shelf.ON_TYPE_ONE)){
                    shelf.setOnStartTime(DateUtils.timeClear("2000-01-01"));
                    shelf.setOnEndTime(DateUtils.timeClear("3000-01-01"));
                }
                if(Objects.equals(param.getLimitShow(),ProductConstants.Shelf.IS_LIMIT_SHOW)) {
                    if(!Objects.equals(param.getGroupType(),NumConstants.ONE)){
                        Result<Long> saveCrowdRes = crowdFeignClient.saveCrowd(param.getCrowdDTO());
                        if(saveCrowdRes.isSuccess() && Objects.nonNull(saveCrowdRes.getData())){
                            shelf.setGroupId(saveCrowdRes.getData());
                        } else {
                            throw new BusinessException("人群包添加失败,请检查人群包条件");
                        }
                    }
                } else {
                    shelf.setGroupId(0L);
                }
                if (isAdd) {
                    shelfMapper.insert(shelf);
                } else {
                    ShelfDTO tableShelfDTO = this.getShelfById(param.getId(),NumConstants.ONE);
                    shelfMapper.updateById(shelf);
                    if(!StringUtils.equals(shelf.getName(),tableShelfDTO.getName())){
                        shelfProductService.updShelfProductName(ShelfProductDTO.builder().shelfId(shelf.getId()).shelfName(shelf.getName()).build());
                    }
                }
                List<ShelfProductSaveDTO> saveShelfProductList = param.getSaveShelfProductList();
                if (!CollectionUtils.isEmpty(saveShelfProductList)) {
                    for (ShelfProductSaveDTO shelfProductSaveDTO : saveShelfProductList) {
                        shelfProductSaveDTO.setShelfId(shelf.getId());
                        shelfProductSaveDTO.setShelfName(shelf.getName());
                    }
                }
                shelfProductService.saveShelfProduct(ShelfProductSaveParamDTO.builder().shelfId(shelf.getId()).shelfName(shelf.getName()).saveShelfProductList(saveShelfProductList).build(), isAdd, NumConstants.ONE);
            } catch (BusinessException e) {
                log.error("=================【货架 saveShelf接口】,uid:{},BusinessException报错:{},入参:{}",uid,e.getMessage(),CommonUtils.jsonStr(param),e);
                throw new BusinessException(e.getCode(),e.getMessage());
            } catch (Exception e) {
                log.error("=================【货架 saveShelf接口】,uid:{},Exception报错:{},入参:{}",uid,e.getMessage(),CommonUtils.jsonStr(param),e);
                throw new BusinessException(ErrorCode.INTERNAL_ERROR);
            } finally {
                redisService.unlock(lockKey);
            }
        } else {
            if(StringUtils.equals(lockKey,CacheKeys.Locks.SHELF_ADD + SecurityContext.getUser().getUid())){
                throw new BusinessException(ErrorCode.CONFLICT,"请求中，请稍后");
            }
            throw new BusinessException(ErrorCode.CONFLICT, "请求中，请稍后");
        }
        return shelf.getId();
    }

    private void checkSaveParam(ShelfSaveParamDTO param, boolean isAdd) {
        if (isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "业务繁忙，请稍后重试");
        }
        if (!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "业务繁忙，请稍后重试");
        }
        if(StringUtils.isBlank(param.getName()) || Objects.isNull(param.getPriority()) || Objects.isNull(param.getOnType())){
            throw new BusinessException(ErrorCode.BAD_REQUEST, "货架名称,优先级,货架上架时间必填");
        }
        if(Objects.equals(param.getOnType(), ProductConstants.Shelf.ON_TYPE_TWO)){
            if(param.getOnStartTime() == null || param.getOnEndTime() == null){
                throw new BusinessException(ErrorCode.BAD_REQUEST, "上架开始/结束时间都不能为空");
            }
            if(param.getOnStartTime().after(param.getOnEndTime())){
                throw new BusinessException(ErrorCode.BAD_REQUEST, "上架开始时间不能在结束时间之后");
            }
        }
        if(Objects.equals(param.getLimitShow(),ProductConstants.Shelf.IS_LIMIT_SHOW)){
            CrowdDTO crowdDTO = param.getCrowdDTO();
            if(Objects.isNull(crowdDTO)){
                throw new BusinessException(ErrorCode.BAD_REQUEST, "人群包不能为空");
            }
            param.setGroupId(crowdDTO.getCrowdId());
            param.setGroupType(crowdDTO.getCrowdType());
            if(Objects.isNull(param.getGroupType())){
                throw new BusinessException(ErrorCode.BAD_REQUEST, "人群包类型不能为空");
            }
            if(param.getGroupType() < NumConstants.ZERO || param.getGroupType() > NumConstants.TWO){
                throw new BusinessException(ErrorCode.BAD_REQUEST, "人群包类型不符合要求");
            }
            if(param.getGroupType() == NumConstants.ONE && Objects.isNull(param.getGroupId())){
                throw new BusinessException(ErrorCode.BAD_REQUEST, "请选择适用人群包");
            }
        }
        //校验优先级的值
        List<ShelfDTO> allPriorityList = this.getAllPriority();
        if(!CollectionUtils.isEmpty(allPriorityList)){
            List<Integer> allPriority = allPriorityList.stream().map(ShelfDTO::getPriority).collect(Collectors.toList());
            if(isAdd && allPriority.contains(param.getPriority())){
                throw new BusinessException(ErrorCode.BAD_REQUEST, "货架优先级不能重复");
            }
            if(!isAdd && allPriority.contains(param.getPriority())){
                Optional<ShelfDTO> first = allPriorityList.stream().filter(s -> Objects.equals(s.getId(), param.getId())).findFirst();
                if(first.isPresent()){
                    if(!Objects.equals(first.get().getPriority(),param.getPriority())){
                        throw new BusinessException(ErrorCode.BAD_REQUEST, "货架优先级不能重复");
                    }
                }
            }
        }
    }

    /**
     * 根据ID删除商品货架
     *
     * @param param 入参
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock(prefix = CacheKeys.Locks.SHELF_DELETE, key = "'#param.id'")
    @CacheEvict(prefix = CacheKeys.shelf.SHELF_QUERY_ALL)
    public void deleteShelfById(IdCodeDTO param) {
        ShelfDTO shelfDTO = this.getShelfById(param.getId(), NumConstants.ONE);
        Date date = new Date();
        if(Objects.equals(shelfDTO.getState(),NumConstants.ONE) || (shelfDTO.getOnStartTime().before(date) && shelfDTO.getOnEndTime().after(date))){
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[货架]启用中/上架中货架无法删除");
        }
        shelfMapper.deleteById(param.getId());
        shelfProductService.deleteByShelfId(param);
        shelfProductSuperscriptService.saveProductSuperscriptList(null,param.getId());
        shelfCampaignService.updShelfIdIntoNull(param.getId());
    }

    /**
     * 根据ID修改启停状态
     * @param param ID NUMBER 通用DTO
     */
    @Override
    @CacheEvict(prefix = CacheKeys.shelf.SHELF_QUERY_ALL)
    public void updateStateById(IdNumberDTO param) {
        Shelf info = shelfMapper.selectById(param.getId());
        if(info == null){
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[货架]未查询到此货架");
        }
        ParamUtils.checkStateParam(param.getNumber());
        shelfMapper.updateById(Shelf.builder().id(param.getId()).state(param.getNumber()).build());
    }

    /**
     * 不分页查询货架列表
     * @param param 查询条件
     * @param num num 1:查询少量字段, 2:查询所有字段, 3:查询时无is_deleted条件
     * @return List<ShelfDTO>
     */
    @Override
    public List<ShelfDTO> getNoPageShelf(ShelfParamDTO param, Integer num) {
        List<Shelf> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(param.getNoLimitShelfIds())) {
            List<Long> noLimitShelfIds = new ArrayList<>(param.getNoLimitShelfIds());
            param = new ShelfParamDTO();
            param.setNoLimitShelfIds(noLimitShelfIds);
            num = NumConstants.THREE;
        }
        if (Objects.equals(num, NumConstants.ONE)) {
            list = shelfMapper.selLessList(param);
        }
        if (Objects.equals(num, NumConstants.TWO)) {
            list = shelfMapper.selAllList(param);
        }
        if (Objects.equals(num, NumConstants.THREE)) {
            list = shelfMapper.selNoLimitList(param);
        }
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return BeanCopierUtils.convertList(list, ShelfDTO.class);
    }

    /**
     * 不分页查询货架列表
     *
     * @param param 查询条件
     * @param num   num 1:查询少量字段, 2:查询所有字段
     * @return Map<Long, ShelfDTO>
     */
    @Override
    public Map<Long, ShelfDTO> getNoPageShelfMap(ShelfParamDTO param, Integer num) {
        Map<Long, ShelfDTO> map = new HashMap<>();
        List<ShelfDTO> list = this.getNoPageShelf(param, num);
        if (!CollectionUtils.isEmpty(list)) {
            map = list.stream().collect(Collectors.toMap(ShelfDTO::getId, s -> s));
        }
        return map;
    }

    @Override
    public void exportList(DownloadAddParamDTO exportParam) {
        String jsonParam = exportParam.getJsonParam();
        String fileName = exportParam.getFileName();
        String fileExt = exportParam.getFileExt();
        String reportCode = exportParam.getReportCode();
        Long downloadCenterId = exportParam.getDownloadCenterId();
        CurrentUserDTO commonLoginDTO = SecurityContext.getUser();
        ShelfParamDTO param = JSON.parseObject(jsonParam, ShelfParamDTO.class);
        List<JSONObject> reList = getShelfExportDTO(param);
        exportService.writeExportList(fileName, fileExt, reportCode, downloadCenterId, commonLoginDTO, reList);
    }

    private List<JSONObject> getShelfExportDTO(ShelfParamDTO param) {
        if (param.getOnStartTime() != null || param.getOnEndTime() != null) {
            if (param.getOnStartTime() == null || param.getOnEndTime() == null) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "上架开始/结束时间都不能为空");
            }
            if (param.getOnStartTime().after(param.getOnEndTime())) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "上架开始时间不能在结束时间之后");
            }
        }
        List<Shelf> shelfList = shelfMapper.selectListByParam(param);
        if (CollectionUtils.isEmpty(shelfList)) {
            return new ArrayList<>();
        }
        List<ShelfDTO> shelfDTOList = BeanCopierUtils.convertList(shelfList, ShelfDTO.class);
        Map<Long, List<ShelfProductDTO>> productMap = shelfProductService.getProductMapByShelfIds(shelfDTOList.stream().map(ShelfDTO::getId).collect(Collectors.toList()), NumConstants.ONE);

        List<ShelfExportDTO> exportList = new ArrayList<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        Map<Long, List<ShelfCampaignDTO>> shelfCampaignMap = shelfCampaignService.getShelfCampaignMap(shelfDTOList.stream().map(ShelfDTO::getId).collect(Collectors.toList()));
        for (ShelfDTO shelfDTO : shelfDTOList) {
            Date date = new Date();
            if (Objects.nonNull(param.getShelfState())) {
                shelfDTO.setShelfState(param.getShelfState());
            }
            if (Objects.isNull(param.getShelfState())) {
                if (shelfDTO.getOnStartTime().after(date)) {
                    shelfDTO.setShelfState(ProductConstants.Shelf.STATE_NO_START);
                }
                if (shelfDTO.getOnStartTime().before(date) && shelfDTO.getOnEndTime().after(date)) {
                    shelfDTO.setShelfState(ProductConstants.Shelf.STATE_IS_UP);
                }
                if (shelfDTO.getOnEndTime().before(date)) {
                    shelfDTO.setShelfState(ProductConstants.Shelf.STATE_IS_END);
                }
            }

            shelfDTO.setProductSum(Optional.ofNullable(productMap.get(shelfDTO.getId())).orElse(Collections.emptyList()).size());
            ShelfExportDTO exportItem = BeanCopierUtils.convertObject(shelfDTO, ShelfExportDTO.class);
            if (Objects.equals(ProductConstants.Shelf.ON_TYPE_ONE, shelfDTO.getOnType())) {
                exportItem.setOnShelfTimeStr("永久上架");
                exportItem.setShelfState(ProductConstants.Shelf.STATE_IS_UP);
            } else {
                String onShelfTimeStr = simpleDateFormat.format(shelfDTO.getOnStartTime()) + "-" + simpleDateFormat.format(shelfDTO.getOnEndTime());
                exportItem.setOnShelfTimeStr(onShelfTimeStr);
            }
            //活动列表
            if (!CollectionUtils.isEmpty(shelfCampaignMap)) {
                List<ShelfCampaignDTO> shelfCampaignDTOList = shelfCampaignMap.get(shelfDTO.getId());
                if (!CollectionUtils.isEmpty(shelfCampaignDTOList)) {
                    String nameStr = shelfCampaignDTOList.stream().map(ShelfCampaignDTO::getName).collect(Collectors.joining(","));
                    exportItem.setShelfActivityListStr(nameStr);
                }
            }
            exportList.add(exportItem);
        }
        return new ArrayList<>(JSON.parseArray(JSON.toJSONString(exportList), JSONObject.class));
    }

    /**
     * 根据人群包id,更新货架,货架营销活动关联人群包id为null
     * @param groupId 人群包id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(prefix = CacheKeys.shelf.SHELF_QUERY_ALL)
    public void updGroupIdIntoNull(Long groupId) {
        if (Objects.isNull(groupId)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "[货架]人群包id不能是空");
        }
        shelfMapper.updGroupIdIntoNull(groupId);
        //根据人群包id,更新货架营销活动关联人群包id为null
        shelfCampaignRuleService.updGroupIdIntoNull(groupId);
    }

    @Override
    public int updateStatic(ExchangeStaticParamDTO orderStaticParam) {
        String lockKey = "redis_distributed_lock:shelf:" + orderStaticParam.getShelfId();
        String requestId = UUID.randomUUID().toString();
        if (redisDistributedLock.tryGetDistributedLockWithRetry(lockKey, requestId, RedisDistributedLock.EXPIRE_TIME, RedisDistributedLock.RETRY_TIMES, RedisDistributedLock.SLEEP_TIME)) {
            try {
                return shelfMapper.updateStatic(orderStaticParam);
            } finally {
                redisDistributedLock.releaseDistributedLock(lockKey, requestId);
            }
        } else {
            // 处理获取锁失败的情况
            throw new BusinessException("获取锁失败");
        }
    }

}
