package com.dz.common.core.fegin.user;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;


@FeignClient(name = ServiceConstant.USER_SERVICE_NAME, contextId = "StoreFeignClient")
public interface StoreFeignClient {
    /**
     * SFTP获取门店数据
     */
    @PostMapping(value = "/store/getSftpFile")
    public Result<String> getSftpFile();

    /**
     * 根据storeSn查询门店
     *
     * @return result<String>
     */
    @ApiOperation("根据ID查询门店")
    @GetMapping(value = "/store/info/store/sn")
    public Result<String> getStoreByStoreSn(@RequestParam("storeSn") String storeSn);
}

