package com.dz.ms.user.vo;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 注销入参
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LogOffVo
{
    @ApiModelProperty(value = "1不想/不再需要使用，2优惠活动少，3账户出现异常，4担忧隐私及安全问题，5其他（多个英文逗号隔开）")
    private String logOffType;
    @ApiModelProperty(value = "其他注销原因")
    private String logOffDesc;
    @ApiModelProperty(value = "短信验证码")
    private String smsCode;
}
