package com.dz.ms.basic.dto;
import java.util.Date;
import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 小程序订阅消息关联场景DTO
 * @author: Handy
 * @date:   2023/07/13 17:51
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "小程序订阅消息关联场景")
public class MpMsgSceneRelationDTO {

    @ApiModelProperty(value = "消息场景关系ID")
    private Long id;
    @ApiModelProperty(value = "场景编号")
    private String scene;
    @ApiModelProperty(value = "消息配置ID")
    private Long msgId;
    @ApiModelProperty(value = "模板名称")
    private String templateName;


}
