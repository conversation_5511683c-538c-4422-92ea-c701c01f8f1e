package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * 表头字段
 *
 * @author: LiinNs
 * @date: 2024/11/27 15:10
 */
@Getter
@Setter
@NoArgsConstructor
@Table("表头字段")
@TableName(value = "download_header")
public class DownloadHeader implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.INT, length = 0, isNull = false, comment = "")
    @TableId(type = IdType.AUTO)
    private Integer id;
    @Columns(type = ColumnType.VARCHAR, length = 128, isNull = false, comment = "报表编码")
    private String reportCode;
    @Columns(type = ColumnType.VARCHAR, length = 128, isNull = true, comment = "表头编码")
    private String code;
    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = true, comment = "表头名")
    private String name;
    @Columns(type = ColumnType.VARCHAR, length = 255, isNull = true, comment = "表头替换 eg: 未使用_0,已使用_1")
    private String replace;
    @Columns(type = ColumnType.VARCHAR, length = 24, isNull = true, comment = "日期格式化方式")
    private String format;
    @Columns(type = ColumnType.INT, length = 0, isNull = true, comment = "排序")
    private Integer sort;
    @Columns(type = ColumnType.BIGINT, length = 0, isNull = false, comment = "租户ID")
    private Long tenantId;

}
