package com.dz.ms.product.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dz.common.base.constant.ClientTypeConstant;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.Result;
import com.dz.common.core.constants.NumConstants;
import com.dz.common.core.dto.order.PurchaseStaticDTO;
import com.dz.common.core.dto.order.PurchaseStaticParamDTO;
import com.dz.common.core.dto.product.CartDTO;
import com.dz.common.core.dto.product.CartProductDTO;
import com.dz.common.core.dto.product.CartResultDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.dto.user.MyPointsDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.order.ExchangeOrderFeignClient;
import com.dz.common.core.fegin.user.UserInfoFeginClient;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.DateUtils;
import com.dz.ms.product.dto.CartCacheDTO;
import com.dz.ms.product.dto.ShelfDTO;
import com.dz.ms.product.dto.res.ShelfProductCartResDTO;
import com.dz.ms.product.entity.Cart;
import com.dz.ms.product.entity.Product;
import com.dz.ms.product.mapper.CartMapper;
import com.dz.ms.product.mapper.ProductMapper;
import com.dz.ms.product.service.CartService;
import com.dz.ms.product.service.ShelfProductService;
import com.dz.ms.product.service.ShelfService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;


/**
 * 购物车
 */
@Slf4j
@Service
public class CartServiceImpl implements CartService {

    private static final String CART_INFO = "cartinfo";
    private static final String LIST_SUFFIX = ":list";
    private static final String COUNT_SUFFIX = ":count";
    @Resource
    private CartMapper cartMapper;
    @Resource
    private ProductMapper productMapper;
    @Resource
    private RedisService redisService;
    @Resource
    private ShelfService shelfService;
    @Resource
    private ShelfProductService shelfProductService;
    @Resource
    private UserInfoFeginClient userInfoFeginClient;
    @Resource
    private ExchangeOrderFeignClient exchangeOrderFeignClient;

    /**
     * 获取用户购物车列表
     *
     * @return
     */
    @Override
    public CartResultDTO getUserCartList(boolean fromCart) {
        CartCacheDTO cartCache = getUserCartCache();//获取缓存信息
        log.info("获取用户购物车列表缓存cartCache:" + JSONObject.toJSONString(cartCache));
        return handleCart(cartCache, true, true);//构造购物车返回结果
    }

    @Override
    public List<CartProductDTO> getUserCartList(Long userId, Long cartId) {
        return cartMapper.getUserCartList(userId, cartId);
    }

    /**
     * 购物车缓存信息
     */
    @Override
    public CartCacheDTO getUserCartCache() {
        CurrentUserDTO user = SecurityContext.getUser();
        long userId = user.getUid();
        int currentUserType = getCurrentUserType();
        String key = CART_INFO + ":" + userId + ":" + currentUserType + LIST_SUFFIX;
        Object object = redisService.get(key);
        //有缓存走缓存
        if (null != object) {
            CartCacheDTO cartCache = (CartCacheDTO) object;
            if (!CollectionUtils.isEmpty(cartCache.getProductList())) {
                cartCache.setIsCache(1);//标识是否是走的缓存数据
                return cartCache;
            }
        }
        //无缓存走DB,保存到缓存
        List<CartProductDTO> productList = this.getCartDbList(userId);
        CartCacheDTO cartCacheDTO = new CartCacheDTO();
        cartCacheDTO.setProductList(productList);
        redisService.set(key, cartCacheDTO);
        return cartCacheDTO;
    }

    /**
     * 购物车删除缓存走DB
     *
     * @param userId
     * @return
     */
    public List<CartProductDTO> getCartDbList(Long userId) {
        // 走DB
        List<CartProductDTO> list = this.getUserCartList(userId, null);
        if (null == list) {
            list = new ArrayList<>();
        }
        return list;
    }

    /**
     * 根据购物车商品列表生成缓存信息
     */
    private CartCacheDTO getCartCacheByProducts(List<CartProductDTO> list) {
        CartCacheDTO cartCacheDTO = new CartCacheDTO();
        cartCacheDTO.setProductList(list);
        return cartCacheDTO;
    }

    /**
     * 根据缓存构造购物车返回结果
     */
    private CartResultDTO handleCart(CartCacheDTO cartCache, boolean fromCart, boolean cach) {
        CartResultDTO cartResult = new CartResultDTO();
        List<CartProductDTO> productList = cartCache.getProductList();
        if (null == productList) {
            return cartResult;
        }
        CurrentUserDTO user = SecurityContext.getUser();
        List<CartProductDTO> invalidCarts = new ArrayList<>();// 失效商品
        List<CartProductDTO> validCarts = new ArrayList<>();// 有效商品
        List<CartProductDTO> allCarts = new ArrayList<>();// 全部商品

        boolean haveFirstPriorityShelf = true;
        List<ShelfDTO> prioritySortedShelf = shelfService.getPrioritySortedShelf(NumConstants.ONE);
        if (CollectionUtils.isEmpty(prioritySortedShelf)) {
            haveFirstPriorityShelf = false;
        }
        log.info("handleCart prioritySortedShelf:" + JSONObject.toJSONString(prioritySortedShelf));
        List<ShelfProductCartResDTO> validShelfProductList = new ArrayList<>();
        List<Long> groupIdList = new ArrayList<>();
        if (haveFirstPriorityShelf) {
            // 当前货架商品ID列表
            ShelfDTO firstPriorityShelf = prioritySortedShelf.get(0);
            // 当前用户适用的人群包列表
            groupIdList = firstPriorityShelf.getGroupIdList();
            List<Long> validShelfProductIdList = productList.stream()
                    .filter(cart -> Objects.equals(cart.getShelfId(), firstPriorityShelf.getId()))
                    .map(CartProductDTO::getShelfProductId)
                    .collect(toList());
            // 当前货架商品信息列表
            validShelfProductList = shelfProductService.getShelfProductCartList(firstPriorityShelf.getId(), validShelfProductIdList);
        }
        Map<Long, ShelfProductCartResDTO> validSpId2SpInfo = validShelfProductList.stream().collect((Collectors.toMap(ShelfProductCartResDTO::getShelfProductId, sp -> sp)));
        log.info("handleCart validSpId2SpInfo:" + JSONObject.toJSONString(validSpId2SpInfo));
        for (CartProductDTO cart : productList) {
            boolean valid = true;
            if (!haveFirstPriorityShelf) {// 用户没有货架则所有商品都是无效
                valid = false;
            } else {
                //
                ShelfProductCartResDTO validShelfProductInfo = null;
                if (validSpId2SpInfo.containsKey(cart.getShelfProductId())) {
                    validShelfProductInfo = validSpId2SpInfo.get(cart.getShelfProductId());
                }
                // 初始化是否售罄 1售罄 0未售罄
                cart.setIsSellout(NumConstants.ZERO);
                // 当前商品不可用
                if (Objects.isNull(validShelfProductInfo)) {
                    valid = false;
                } else {
                    if (Objects.nonNull(validShelfProductInfo.getRuleGroupId())
                            && !groupIdList.contains(validShelfProductInfo.getRuleGroupId())) {
                        valid = false;
                    } else {
                        log.info("---***---购物车商品信息复制---***---\n" +
                                "---当前购物车的商品: {}\n" +
                                "---当前货架商品信息: {}", cart, validShelfProductInfo);
                        BeanCopierUtils.copyProperties(validShelfProductInfo, cart);
                        if (Objects.nonNull(validShelfProductInfo.getRCostPoint())) {
                            // 当前积分价格
                            cart.setCostPoint(validShelfProductInfo.getRCostPoint());
                            cart.setPrePoint(validShelfProductInfo.getRPrePoint());
                            // 活动剩余库存
                            if (Objects.nonNull(validShelfProductInfo.getRestInventory())
                                    && validShelfProductInfo.getRestInventory() <= 0) {
                                cart.setIsSellout(NumConstants.ONE);
                            }
                        }
                        // 货架剩余库存
                        if (Objects.isNull(validShelfProductInfo.getRCostPoint())
                                && Objects.nonNull(validShelfProductInfo.getSCostPoint())) {
                            // 当前积分价格
                            cart.setCostPoint(validShelfProductInfo.getCostPoint());
                            cart.setPrePoint(validShelfProductInfo.getSPrePoint());
                            if (Objects.nonNull(validShelfProductInfo.getSCurrentInventory())
                                    && validShelfProductInfo.getSCurrentInventory() <= 0) {
                                cart.setIsSellout(NumConstants.ONE);
                            }
                        }
                    }
                }
                // 商品禁用
                if ((Objects.nonNull(cart.getState()) && cart.getState() == 0)) {
                    valid = false;
                } else if (fromCart && cart.getIsSellout() == 1) { // 售罄
                    valid = false;
                }
            }

            log.info("商品信息，cart:{}", cart);
            allCarts.add(cart);
            Cart toUpdate = new Cart();
            toUpdate.setId(cart.getId());
            if (valid) {
                validCarts.add(cart);
                toUpdate.setStatus(1);
                toUpdate.setRuleId(cart.getRuleId());
                toUpdate.setCampaignId(cart.getCampaignId());
                toUpdate.setRPurchaseLimit(cart.getRPurchaseLimit());
                toUpdate.setREveryoneLimit(cart.getREveryoneLimit());
            } else {
                //将失效商品置为未选择
                cart.setChecked(0);
                toUpdate.setChecked(0);
                toUpdate.setStatus(0);
                //添加到无效商品列表
                invalidCarts.add(cart);
            }
            if (fromCart && Objects.nonNull(toUpdate.getId())) {
                cartMapper.updateById(toUpdate);
            }
        }
        cartResult.setInvalidCarts(invalidCarts);
        cartResult.setValidCarts(validCarts);
        cartResult.setCarts(allCarts);
        int totalPoint = 0;
        BigDecimal totalPrice = new BigDecimal(0);
        //购物车商品数量
        int totalNum = 0;
        int checkNum = 0;
        int checkAll = 1;
        for (CartProductDTO cart : validCarts) {
            if (null == cart.getChecked() || !cart.getChecked().equals(1)) {
                checkAll = 0;
                continue;
            }
            if (null != cart.getChecked() && cart.getChecked().equals(1)) {
                checkNum += cart.getNumber();
                totalPoint += cart.getCostPoint() * cart.getNumber();
                if (Objects.nonNull(cart.getCostPrice())) {
                    totalPrice = totalPrice.add(cart.getCostPrice().multiply(BigDecimal.valueOf(cart.getNumber())));
                }
            }
        }
        //购物车商品数量
        if (!CollectionUtils.isEmpty(validCarts)) {
            totalNum += validCarts.stream().mapToInt(CartProductDTO::getNumber).sum();
        }
        cartResult.setCheckAll(checkAll);
        cartResult.setTotalNum(totalNum);
        cartResult.setCheckNum(checkNum);
        cartResult.setTotalPoint(totalPoint);
        cartResult.setTotalPrice(totalPrice);

        //购物车需要缓存 立即购买也走该方法但不缓存
        if (cach) {
            redisService.set(CART_INFO + ":" + user.getUid() + ":" + getCurrentUserType() + COUNT_SUFFIX, totalNum, 3600);
            changeCach(cartCache, user.getUid(), false);
        }
        return cartResult;
    }

    /**
     * 获取订单预览商品列表
     *
     * @return
     */
    @Override
    public CartResultDTO getPreviewCartOrder() {
        CartResultDTO cartResult = getUserCartList(false);
        log.info("购物车商品信息：{}", cartResult);
        return cartResult;
    }

    /**
     * 单商品购买订单预览
     *
     * @return
     */
    @Override
    public CartResultDTO getCartOrderByShelfProductId(Long shelfProductId, Integer num) {
        //立即购买时不走购物车 需要查询活动构造和购物车同样结构的数据
        CartProductDTO cartProduct = cartMapper.getProductCartByShelfProductId(shelfProductId);
        if (Objects.isNull(cartProduct)) {
            throw new BusinessException("没有该商品");
        }
        cartProduct.setChecked(1);
        cartProduct.setNumber(num);
        List<CartProductDTO> list = new ArrayList<>();
        list.add(cartProduct);
        CartCacheDTO cartCache = getCartCacheByProducts(list);
        return handleCart(cartCache, false, false);
    }

    /**
     * 获取用户购物车商品总数
     *
     * @return
     */
    @Override
    public Integer getUserCartCount() {
        CurrentUserDTO user = SecurityContext.getUser();
        long userId = user.getUid();
        Integer currentUserType = getCurrentUserType();
        String key = CART_INFO + ":" + userId + ":" + currentUserType + COUNT_SUFFIX;
        Object obj = redisService.get(key);
        if (null != obj) {
            return (Integer) obj;
        }
        boolean haveFirstPriorityShelf = true;
        List<ShelfDTO> prioritySortedShelf = shelfService.getPrioritySortedShelf(NumConstants.ONE);
        if (CollectionUtils.isEmpty(prioritySortedShelf)) {
            haveFirstPriorityShelf = false;
        }
        if (haveFirstPriorityShelf) {
            ShelfDTO shelfDTO = prioritySortedShelf.get(0);
            int num = cartMapper.getUserCartCount(userId, shelfDTO.getId());
            redisService.set(key, num, 3600);
            return num;
        } else {
            return 0;
        }
    }

    /**
     * 添加或删除购物车缓存
     */
    private void changeCach(CartCacheDTO cartCache, Long userId, boolean isDelete) {
        String key = CART_INFO + ":" + userId + ":" + getCurrentUserType();
        if (isDelete) {
            redisService.del(key + LIST_SUFFIX);
            redisService.del(key + COUNT_SUFFIX);
        } else {
            redisService.set(key + LIST_SUFFIX, cartCache, 3600);
        }
    }

    private Integer getCurrentUserType() {
        int userType;
        if (SecurityContext.getUser().getType().equals(ClientTypeConstant.APP)) {
            userType = 0;
        } else if (SecurityContext.getUser().getType().equals(ClientTypeConstant.EMP)) {
            userType = 1;
        } else {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "用户类型错误");
        }
        return userType;
    }

    /**
     * 添加购物车
     *
     * @param cartDTO
     */
    @Override
    @Transactional
    public void addUserCart(CartDTO cartDTO) {
        CurrentUserDTO user = SecurityContext.getUser();
        long tenantId = user.getTenantId();
        long userId = user.getUid();
        String cartLock = "cartLock:" + userId + ":" + cartDTO.getShelfProductId();
        boolean lock = redisService.lock(cartLock, 3);
        if (!lock) {
            throw new BusinessException(ErrorCode.INTERNAL_ERROR, "添加购物车中请稍后");
        }
        Integer currentUserType = getCurrentUserType();
        //校验库存 校验货架
        List<ShelfDTO> sortedShelf = shelfService.getPrioritySortedShelf(NumConstants.ONE);
        if (CollectionUtils.isEmpty(sortedShelf)) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "该商品已失效，请返回重试");
        }
        ShelfDTO firstPriorityShelf = sortedShelf.get(0);
        if (!Objects.equals(firstPriorityShelf.getId(), cartDTO.getShelfId())) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "该商品已失效，请返回重试");
        }
        Product product = productMapper.selectById(cartDTO.getProductId());
        if (Objects.isNull(product)) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "该商品已失效，请返回重试");
        }
        if (Objects.equals(NumConstants.ZERO, product.getState())) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "该商品已失效，请返回重试");
        }
        List<ShelfProductCartResDTO> shelfProductCartList = shelfProductService.getShelfProductCartList(cartDTO.getShelfId(), Collections.singletonList(cartDTO.getShelfProductId()));
        if (CollectionUtils.isEmpty(shelfProductCartList)) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "该商品已失效，请返回重试");
        }
        List<Long> groupIdList = firstPriorityShelf.getGroupIdList();
        ShelfProductCartResDTO toAddShelfProduct = shelfProductCartList.get(0);
        if (Objects.nonNull(toAddShelfProduct.getRuleGroupId())
                && !groupIdList.contains(toAddShelfProduct.getRuleGroupId())) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "该商品已失效，请返回重试");
        }
        //验证用户是否已加入过同样的商品
        LambdaQueryWrapper<Cart> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Cart::getProductId, cartDTO.getProductId())
                .eq(Cart::getShelfProductId, cartDTO.getShelfProductId())
                .eq(Cart::getUserId, userId);
        Cart getCart = cartMapper.selectOne(queryWrapper);
        CartCacheDTO cartCache;
        int number;
        if (null == getCart) {//没加过 加入购物车
            number = cartDTO.getNumber();
            validateMonthPurchaseLimit(userId, toAddShelfProduct, number);
            Cart cart = new Cart();
            mutableValidateInventoryAndPoint(toAddShelfProduct, number, cart, true);
            cart.setUserId(userId);
            cart.setProductId(toAddShelfProduct.getProductId());
            cart.setImgUrl(toAddShelfProduct.getImgUrl());
            cart.setShelfId(toAddShelfProduct.getShelfId());
            cart.setShelfProductId(toAddShelfProduct.getShelfProductId());
            cart.setCampaignId(toAddShelfProduct.getCampaignId());
            cart.setRuleId(toAddShelfProduct.getRuleId());
            cart.setRPurchaseLimit(toAddShelfProduct.getRPurchaseLimit());
            cart.setREveryoneLimit(toAddShelfProduct.getREveryoneLimit());
            cart.setChecked(1);
            cart.setTenantId(tenantId);
            cartMapper.insert(cart);
            cartCache = getUserCartCache();
            redisService.del(CART_INFO + ":" + userId + ":" + currentUserType + COUNT_SUFFIX);
            redisService.del(CART_INFO + ":" + userId + ":" + currentUserType + LIST_SUFFIX);
            if (null != cartCache.getIsCache() && cartCache.getIsCache().equals(1)) {
                List<CartProductDTO> list = this.getUserCartList(userId, cart.getId());
                if (null == list || list.size() != 1) {
                    throw new BusinessException(ErrorCode.INTERNAL_ERROR, "该商品已失效，请返回重试");
                }
                CartProductDTO cartProduct = list.get(0);
                if (null == cartCache.getProductList()) {
                    cartCache.setProductList(list);
                } else {
                    cartCache.getProductList().add(0, cartProduct);
                }
            }
        } else {// 加过
            number = getCart.getNumber() + cartDTO.getNumber();
            Cart cart = new Cart();
            validateMonthPurchaseLimit(userId, toAddShelfProduct, number);
            mutableValidateInventoryAndPoint(toAddShelfProduct, number, cart, false);
            cart.setId(getCart.getId());
            cart.setChecked(1);
            cart.setTenantId(tenantId);
            cart.setModified(new Date());
            cart.setCampaignId(toAddShelfProduct.getCampaignId());
            cart.setRuleId(toAddShelfProduct.getRuleId());
            cart.setRPurchaseLimit(toAddShelfProduct.getRPurchaseLimit());
            cart.setREveryoneLimit(toAddShelfProduct.getREveryoneLimit());
            cartMapper.updateById(cart);
            cartCache = getUserCartCache();
            redisService.del(CART_INFO + ":" + userId + ":" + currentUserType + COUNT_SUFFIX);
            redisService.del(CART_INFO + ":" + userId + ":" + currentUserType + LIST_SUFFIX);
            //遍历购物车找到该商品 更新数量并选中
            for (CartProductDTO cartProduct : cartCache.getProductList()) {
                if (cartProduct.getId().equals(cart.getId())) {
                    cartProduct.setNumber(cart.getNumber());
                    cartProduct.setChecked(1);
                    break;
                }
            }
        }
        log.info("用户:{} 购物车商品:{}", userId, cartCache);
        // 判断购物车积分
        Result<MyPointsDTO> myPointsDTOResult = userInfoFeginClient.myPoints();
        if (Objects.isNull(myPointsDTOResult)
                || !myPointsDTOResult.isSuccess()
                || Objects.isNull(myPointsDTOResult.getData())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "该商品已失效，请返回重试");
        }
        if (!enoughPoint(cartCache.getProductList(), toAddShelfProduct.getShelfId(), myPointsDTOResult.getData().getPointsNum())) {
            throw new BusinessException(ErrorCode.ADD_CART_INSUFFICIENT_POINT, "您的积分不足以兑换购物车的全部商品");
        }
        if (Objects.nonNull(toAddShelfProduct.getRuleId())) {// 活动限购
            List<CartProductDTO> productList = cartCache.getProductList();
            if (!CollectionUtils.isEmpty(productList)) {
                Map<Long, List<CartProductDTO>> rules = productList.stream()
                        .filter(c -> Objects.nonNull(c.getRuleId()))
                        .filter(c -> c.getChecked().equals(1))
                        .collect(Collectors.groupingBy(CartProductDTO::getRuleId));
                if (!CollectionUtils.isEmpty(rules)) {
                    for (Map.Entry<Long, List<CartProductDTO>> entry : rules.entrySet()) {
                        List<CartProductDTO> rulesProductList = entry.getValue();
                        if (Objects.equals(entry.getKey(), toAddShelfProduct.getRuleId())) {
                            if ((Objects.nonNull(toAddShelfProduct.getRPurchaseLimit()) && toAddShelfProduct.getRPurchaseLimit() > 0)
                                    || (Objects.nonNull(toAddShelfProduct.getREveryoneLimit()) && toAddShelfProduct.getREveryoneLimit() > 0)) {
                                PurchaseStaticParamDTO param = new PurchaseStaticParamDTO();
                                param.setShelfId(toAddShelfProduct.getShelfId());
                                param.setUserId(userId);
                                param.setRuleId(toAddShelfProduct.getRuleId());
                                param.setRuleType(toAddShelfProduct.getRuleType());
                                param.setPeriod(toAddShelfProduct.getPeriod());
                                param.setRCreated(toAddShelfProduct.getRCreated());
                                param.setCampaignOnStartTime(toAddShelfProduct.getCampaignOnStartTime());
                                param.setCampaignOnEndTime(toAddShelfProduct.getCampaignOnEndTime());
                                Result<List<PurchaseStaticDTO>> purchaseStaticDTORet = exchangeOrderFeignClient.purchaseStatic(param);
                                // 总体限购
                                int myPurchaseNumber = 0;
                                // 单商品限购
                                int myShelfProductIdPurchaseNumber = 0;
                                if (Objects.nonNull(purchaseStaticDTORet) && purchaseStaticDTORet.isSuccess() && Objects.nonNull(purchaseStaticDTORet.getData())) {
                                    List<PurchaseStaticDTO> purchaseStaticDTOS = purchaseStaticDTORet.getData();
                                    if (!CollectionUtils.isEmpty(purchaseStaticDTOS)) {
                                        myPurchaseNumber = purchaseStaticDTOS.stream().mapToInt(PurchaseStaticDTO::getNumber).sum();
                                        myShelfProductIdPurchaseNumber = purchaseStaticDTOS.stream()
                                                .filter(d -> Objects.equals(d.getShelfProductId(), toAddShelfProduct.getShelfProductId()))
                                                .mapToInt(PurchaseStaticDTO::getNumber)
                                                .sum();
                                    }
                                }

                                Integer rPurchaseLimit = toAddShelfProduct.getRPurchaseLimit();
                                if (Objects.nonNull(rPurchaseLimit) && rPurchaseLimit > 0) {
                                    int sum = rulesProductList.stream().mapToInt(CartProductDTO::getNumber).sum();

                                    if (sum + myPurchaseNumber > rPurchaseLimit) {
                                        throw new BusinessException(ErrorCode.BAD_REQUEST, "该活动中每人最多兑换" + rPurchaseLimit + "件");
                                    }

                                }
                                // 有每人限购
                                if (Objects.nonNull(toAddShelfProduct.getREveryoneLimit())
                                        && toAddShelfProduct.getREveryoneLimit() > 0
                                        && number + myShelfProductIdPurchaseNumber > toAddShelfProduct.getREveryoneLimit()) {
                                    throw new BusinessException(ErrorCode.BAD_REQUEST, "该商品目前每人限购" + toAddShelfProduct.getREveryoneLimit() + "件");
                                }
                            }
                        }
                    }
                }
            }
        }
        changeCach(cartCache, userId, false);
        redisService.unlock(cartLock);
    }

    private void validateMonthPurchaseLimit(long userId, ShelfProductCartResDTO toAddShelfProduct, Integer number) {
        if ((Objects.isNull(toAddShelfProduct.getREveryoneLimit()) || toAddShelfProduct.getREveryoneLimit() == 0)
                && (Objects.isNull(toAddShelfProduct.getRPurchaseLimit()) || toAddShelfProduct.getRPurchaseLimit() == 0)) {
            if (Objects.nonNull(toAddShelfProduct.getLimitNum()) && toAddShelfProduct.getLimitNum() > 0) {// 货架限购
                int myShelfProductIdPurchaseNumber = 0;
                PurchaseStaticParamDTO purchaseStaticParamDTO = new PurchaseStaticParamDTO();
                purchaseStaticParamDTO.setShelfId(toAddShelfProduct.getShelfId());
                purchaseStaticParamDTO.setUserId(userId);
                Date monthZero = DateUtils.getMonthZero();
                purchaseStaticParamDTO.setCampaignOnStartTime(monthZero);
                purchaseStaticParamDTO.setCampaignOnEndTime(new Date());
                Result<List<PurchaseStaticDTO>> listResult = exchangeOrderFeignClient.purchaseStatic(purchaseStaticParamDTO);
                if (Objects.nonNull(listResult) && listResult.isSuccess() && Objects.nonNull(listResult.getData())) {
                    List<PurchaseStaticDTO> shelfPurchaseStatic = listResult.getData();
                    if (!CollectionUtils.isEmpty(shelfPurchaseStatic)) {
                        myShelfProductIdPurchaseNumber = shelfPurchaseStatic.stream()
                                .filter(d -> Objects.equals(d.getShelfProductId(), toAddShelfProduct.getShelfProductId()))
                                .mapToInt(PurchaseStaticDTO::getNumber)
                                .sum();
                    }
                    if (myShelfProductIdPurchaseNumber + number > toAddShelfProduct.getLimitNum()) {
                        throw new BusinessException(ErrorCode.BAD_REQUEST, "该商品当月每人限购" + toAddShelfProduct.getLimitNum() + "件");
                    }
                }
            }
        }
    }

    private void mutableValidateInventoryAndPoint(ShelfProductCartResDTO currentShelfProductInfo, int number, Cart cart, boolean isAdd) {
        cart.setNumber(number);
        if (Objects.isNull(currentShelfProductInfo.getSCurrentInventory())
                || (Objects.nonNull(currentShelfProductInfo.getSCurrentInventory()) && currentShelfProductInfo.getSCurrentInventory() <= 0)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "商品已售罄");
        }
        if (number > currentShelfProductInfo.getSCurrentInventory()) {
            // cart.setNumber(currentShelfProductInfo.getSCurrentInventory());
            throw new BusinessException(ErrorCode.BAD_REQUEST, "商品库存不足");
        }
        if ((Objects.isNull(currentShelfProductInfo.getREveryoneLimit()) || currentShelfProductInfo.getREveryoneLimit() == 0)
                && (Objects.isNull(currentShelfProductInfo.getRPurchaseLimit()) || currentShelfProductInfo.getRPurchaseLimit() == 0)) {
            if (Objects.nonNull(currentShelfProductInfo.getLimitNum()) && currentShelfProductInfo.getLimitNum() > 0 && number > currentShelfProductInfo.getLimitNum()) {// 货架限购
                throw new BusinessException(ErrorCode.BAD_REQUEST, "该商品当月每人限购" + currentShelfProductInfo.getLimitNum() + "件");
            }
        }
        if (Objects.nonNull(currentShelfProductInfo.getRCostPoint())) {
            // 活动剩余库存
            if (Objects.isNull(currentShelfProductInfo.getRestInventory())
                    || (Objects.nonNull(currentShelfProductInfo.getRestInventory()) && currentShelfProductInfo.getRestInventory() <= 0)) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "商品已售罄");
            }
            if (number > currentShelfProductInfo.getRestInventory()) {
                //cart.setNumber(currentShelfProductInfo.getRestInventory());
                throw new BusinessException(ErrorCode.BAD_REQUEST, "商品库存不足");
            }
            // 有每人限购
            if (Objects.nonNull(currentShelfProductInfo.getREveryoneLimit())
                    && currentShelfProductInfo.getREveryoneLimit() > 0
                    && number > currentShelfProductInfo.getREveryoneLimit()) {
                //cart.setNumber(currentShelfProductInfo.getREveryoneLimit());
                throw new BusinessException(ErrorCode.BAD_REQUEST, "该商品目前每人限购" + currentShelfProductInfo.getREveryoneLimit() + "件");
            }

            if (isAdd) {
                cart.setCostPoint(currentShelfProductInfo.getRCostPoint());
                cart.setCostPrice(currentShelfProductInfo.getCostPrice());
            }
            return;
        }
        if (Objects.isNull(currentShelfProductInfo.getRCostPoint()) && Objects.nonNull(currentShelfProductInfo.getSCostPoint())) {
            if (isAdd) {
                cart.setCostPoint(currentShelfProductInfo.getSCostPoint());
                cart.setCostPrice(currentShelfProductInfo.getCostPrice());
            }
        }
    }

    private boolean enoughPoint(List<CartProductDTO> productList, Long shelfId, Integer myPoint) {
        Integer nullAblePoint = Optional.ofNullable(myPoint).orElse(0);
        int pointAmount = Integer.MAX_VALUE;
        if (!CollectionUtils.isEmpty(productList)) {
            // 选中商品总积分
            pointAmount = productList.stream()
                    .filter(cp -> Objects.equals(cp.getChecked(), 1))
                    .filter(cp -> Objects.equals(cp.getShelfId(), shelfId))
                    .mapToInt(cp -> cp.getCostPoint() * cp.getNumber())
                    .sum();
        }
        return nullAblePoint >= pointAmount;
    }

    /**
     * 修改购物车信息
     *
     * @param cartDTO
     */
    @Override
    @Transactional
    public void editUserCart(CartDTO cartDTO) {
        long tenantId;
        long userId;
        Integer currentUserType;
        CurrentUserDTO user = SecurityContext.getUser();
        tenantId = user.getTenantId();
        userId = user.getUid();
        String cartLock = "cartLock:" + userId + ":" + cartDTO.getShelfProductId();
        boolean lock = redisService.lock(cartLock, 3);
        if (!lock) {
            throw new BusinessException(ErrorCode.INTERNAL_ERROR, "编辑购物车中请稍后");
        }
        currentUserType = getCurrentUserType();
        Cart getCart = cartMapper.selectById(cartDTO.getId());
        if (null == getCart) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "该商品已失效，请返回重试");
        }
        // 获取购物车商品信息
        CartCacheDTO cartCache = getUserCartCache();
        redisService.del(CART_INFO + ":" + userId + ":" + currentUserType + COUNT_SUFFIX);
        redisService.del(CART_INFO + ":" + userId + ":" + currentUserType + LIST_SUFFIX);
        log.info("修改购物车后的缓存信息为：" + JSONObject.toJSONString(cartCache));
        CartProductDTO cartProductDTO = null;
        Long changeId = cartDTO.getId();
        Cart cart = new Cart();
        cart.setId(cartDTO.getId());
        cart.setTenantId(tenantId);
        if (null != cartDTO.getNumber() && cartDTO.getNumber() > 0) {
            cart.setNumber(cartDTO.getNumber());
        } else if (null != cartDTO.getNumber() && cartDTO.getNumber() == 0) {
            Cart deleted = new Cart();
            deleted.setId(cartDTO.getId());
            deleted.setUserId(userId);
            cartMapper.deleteById(deleted);
            List<CartProductDTO> cartList = cartCache.getProductList();
            List<CartProductDTO> newList = new ArrayList<>(cartList);
            for (CartProductDTO cartProduct : newList) {
                if (Objects.equals(cartDTO.getId(), cartProduct.getId())) {
                    cartList.remove(cartProduct);
                }
            }
            if (!CollectionUtils.isEmpty(cartList)) {
                changeCach(cartCache, userId, false);
                redisService.del(CART_INFO + ":" + userId + ":" + currentUserType + COUNT_SUFFIX);
                redisService.del(CART_INFO + ":" + userId + ":" + currentUserType + LIST_SUFFIX);
            } else {
                changeCach(cartCache, userId, true);
            }
            return;
        }
        List<ShelfProductCartResDTO> shelfProductCartList = shelfProductService.getShelfProductCartList(cartDTO.getShelfId(), Collections.singletonList(cartDTO.getShelfProductId()));
        if (CollectionUtils.isEmpty(shelfProductCartList)) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "该商品已失效，请返回重试");
        }
        ShelfProductCartResDTO toEditShelfProduct = shelfProductCartList.get(0);
        validateMonthPurchaseLimit(userId, toEditShelfProduct, cart.getNumber());
        mutableValidateInventoryAndPoint(toEditShelfProduct, cart.getNumber(), cart, false);
        cart.setShelfId(cartDTO.getShelfId());
        cart.setShelfProductId(cartDTO.getShelfProductId());
        cart.setCampaignId(toEditShelfProduct.getCampaignId());
        cart.setRuleId(toEditShelfProduct.getRuleId());
        cart.setRPurchaseLimit(toEditShelfProduct.getRPurchaseLimit());
        cart.setREveryoneLimit(toEditShelfProduct.getREveryoneLimit());
        cart.setChecked(cartDTO.getChecked());
        cartMapper.updateById(cart);
        List<CartProductDTO> list = this.getUserCartList(userId, cart.getId());
        cartProductDTO = list.get(0);
        for (CartProductDTO cartProduct : cartCache.getProductList()) {
            if (cartProduct.getId().equals(changeId)) {
                BeanCopierUtils.copyProperties(cartProductDTO, cartProduct);
                break;
            }
        }
        if (Objects.nonNull(toEditShelfProduct.getRuleId())) {
            List<CartProductDTO> productList = cartCache.getProductList();
            if (!CollectionUtils.isEmpty(productList)) {
                Map<Long, List<CartProductDTO>> rules = productList.stream()
                        .filter(c -> Objects.nonNull(c.getRuleId()))
                        .filter(c -> c.getChecked().equals(1))
                        .collect(Collectors.groupingBy(CartProductDTO::getRuleId));
                if (!CollectionUtils.isEmpty(rules)) {
                    for (Map.Entry<Long, List<CartProductDTO>> entry : rules.entrySet()) {
                        Long ruleId = entry.getKey();
                        List<CartProductDTO> rulesProductList = entry.getValue();
                        if (Objects.equals(ruleId, toEditShelfProduct.getRuleId())) {
                            if ((Objects.nonNull(toEditShelfProduct.getRPurchaseLimit()) && toEditShelfProduct.getRPurchaseLimit() > 0)
                                    || (Objects.nonNull(toEditShelfProduct.getREveryoneLimit()) && toEditShelfProduct.getREveryoneLimit() > 0)) {
                                Integer rPurchaseLimit = toEditShelfProduct.getRPurchaseLimit();
                                PurchaseStaticParamDTO param = new PurchaseStaticParamDTO();
                                param.setShelfId(toEditShelfProduct.getShelfId());
                                param.setUserId(userId);
                                param.setRuleId(toEditShelfProduct.getRuleId());
                                param.setRuleType(toEditShelfProduct.getRuleType());
                                param.setPeriod(toEditShelfProduct.getPeriod());
                                param.setRCreated(toEditShelfProduct.getRCreated());
                                param.setCampaignOnStartTime(toEditShelfProduct.getCampaignOnStartTime());
                                param.setCampaignOnEndTime(toEditShelfProduct.getCampaignOnEndTime());
                                Result<List<PurchaseStaticDTO>> purchaseStaticDTORet = exchangeOrderFeignClient.purchaseStatic(param);
                                // 总体限购
                                int myPurchaseNumber = 0;
                                // 单商品限购
                                int myShelfProductIdPurchaseNumber = 0;
                                if (Objects.nonNull(purchaseStaticDTORet) && purchaseStaticDTORet.isSuccess() && Objects.nonNull(purchaseStaticDTORet.getData())) {
                                    List<PurchaseStaticDTO> purchaseStaticDTOS = purchaseStaticDTORet.getData();
                                    if (!CollectionUtils.isEmpty(purchaseStaticDTOS)) {
                                        myPurchaseNumber = purchaseStaticDTOS.stream().mapToInt(PurchaseStaticDTO::getNumber).sum();
                                        myShelfProductIdPurchaseNumber = purchaseStaticDTOS.stream()
                                                .filter(d -> Objects.equals(d.getShelfProductId(), toEditShelfProduct.getShelfProductId()))
                                                .mapToInt(PurchaseStaticDTO::getNumber)
                                                .sum();
                                    }
                                }
                                if (Objects.nonNull(rPurchaseLimit) && rPurchaseLimit > 0) {
                                    int sum = rulesProductList.stream().mapToInt(CartProductDTO::getNumber).sum();
                                    if (sum + myPurchaseNumber > rPurchaseLimit) {
                                        throw new BusinessException(ErrorCode.BAD_REQUEST, "该活动中每人最多兑换" + rPurchaseLimit + "件");
                                    }
                                }
                                // 有每人限购
                                if (Objects.nonNull(toEditShelfProduct.getREveryoneLimit())
                                        && toEditShelfProduct.getREveryoneLimit() > 0
                                        && cart.getNumber() + myShelfProductIdPurchaseNumber > toEditShelfProduct.getREveryoneLimit()) {
                                    throw new BusinessException(ErrorCode.BAD_REQUEST, "该商品目前每人限购" + toEditShelfProduct.getREveryoneLimit() + "件");
                                }
                            }

                        }

                    }
                }
            }
        }
        changeCach(cartCache, userId, false);
        redisService.unlock(cartLock);
    }

    /**
     * 购物车全选
     */
    @Override
    @Transactional
    public void checkedAllUserCart(Integer status) {
        CurrentUserDTO user = SecurityContext.getUser();
        long userId = user.getUid();
        //将该用户购物车商品DB数据更新为 全选或不选
        cartMapper.checkedAllUserCart(userId, status);
        CartCacheDTO cartCache = getUserCartCache();
        //遍历购物车缓存更新选择状态 重新构造放入缓存
        if (null != cartCache.getProductList()) {
            for (CartProductDTO cartProduct : cartCache.getProductList()) {
                cartProduct.setChecked(status);
            }
            changeCach(cartCache, userId, false);
        }
    }

    /**
     * 删除购物车商品
     *
     * @param ids
     */
    @Override
    @Transactional
    public void deleteUserCart(String ids) {
        long userId;
        Integer currentUserType;
        CurrentUserDTO user = SecurityContext.getUser();
        userId = user.getUid();
        currentUserType = getCurrentUserType();
        String[] array = ids.split(",");
        List<Long> idList = new ArrayList<>();
        for (String str : array) {
            long id = NumberUtils.toLong(str);
            idList.add(id);
            Cart cart = cartMapper.selectById(id);
            if (null == cart) {
                continue;
            }
            Cart deleted = new Cart();
            deleted.setId(id);
            deleted.setUserId(userId);
            cartMapper.deleteById(deleted);
        }
        CartCacheDTO cartCache;
        cartCache = getUserCartCache();
        List<CartProductDTO> cartList = cartCache.getProductList();
        List<CartProductDTO> newList = new ArrayList<>(cartList);
        for (CartProductDTO cartProduct : newList) {
            if (idList.contains(cartProduct.getId())) {
                cartList.remove(cartProduct);
            }
        }
        if (!CollectionUtils.isEmpty(cartList)) {
            changeCach(cartCache, userId, false);
            redisService.del(CART_INFO + ":" + userId + ":" + currentUserType + COUNT_SUFFIX);
            redisService.del(CART_INFO + ":" + userId + ":" + currentUserType + LIST_SUFFIX);
        } else {
            changeCach(cartCache, userId, true);
        }
    }

    /**
     * 清空已下单购物车商品
     */
    @Override
    @Transactional
    public void cleanUserOrderCart() {
        CurrentUserDTO user = SecurityContext.getUser();
        CartCacheDTO cartCache = getUserCartCache();
        if (null == cartCache) {
            return;
        }
        long userId;
        Integer currentUserType;
        userId = user.getUid();
        currentUserType = getCurrentUserType();
        List<Long> idList = new ArrayList<>();
        List<CartProductDTO> cartList = cartCache.getProductList();
        List<CartProductDTO> newList = new ArrayList<>(cartList);
        for (CartProductDTO cartProduct : newList) {
            if (null != cartProduct.getChecked() && cartProduct.getChecked().equals(1)) {
                idList.add(cartProduct.getId());
                cartList.remove(cartProduct);
            }
        }
        if (!CollectionUtils.isEmpty(idList)) {
            for (Long id : idList) {
                Cart cart = cartMapper.selectById(id);
                if (null == cart) {
                    throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "购物车信息不存在");
                }
                Cart deleted = new Cart();
                deleted.setId(id);
                deleted.setUserId(userId);
                cartMapper.deleteById(deleted);

            }
            changeCach(cartCache, userId, false);
            redisService.del(CART_INFO + ":" + userId + ":" + currentUserType + COUNT_SUFFIX);
            redisService.del(CART_INFO + ":" + userId + ":" + currentUserType + LIST_SUFFIX);
        }
    }

    /**
     * 清空购物车失效商品
     */
    @Override
    public void cleanDisabledProduct() {
        // 获取用户购物车列表中的失效商品列表
        List<Long> invalidCartIdList = new ArrayList<>();
        CartResultDTO userCartListData = new CartResultDTO();
        try {
            userCartListData = this.getUserCartList(true);
        } catch (Exception e) {
            throw new BusinessException(ErrorCode.INTERNAL_ERROR, "购物车列表获取失败");
        }
        if (!ObjectUtils.isEmpty(userCartListData)) {
            if (!CollectionUtils.isEmpty(userCartListData.getInvalidCarts())) {
                invalidCartIdList.addAll(userCartListData.getInvalidCarts().stream().map(CartProductDTO::getId).collect(toList()));
            }
        } else {
            throw new BusinessException(ErrorCode.INTERNAL_ERROR, "购物车列表获取失败");
        }

        // 删除失效购物车中的失效商品
        if (!CollectionUtils.isEmpty(invalidCartIdList)) {
            try {
                this.deleteUserCart(StringUtils.join(invalidCartIdList, ","));
            } catch (Exception e) {
                throw new BusinessException(ErrorCode.INTERNAL_ERROR, "删除购物车商品失败");
            }
        }

    }

}
