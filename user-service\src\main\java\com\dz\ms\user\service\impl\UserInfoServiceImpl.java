package com.dz.ms.user.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.constant.ClientTypeConstant;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.utils.MD5;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.Cacheable;
import com.dz.common.core.annotation.Lock;
import com.dz.common.core.constants.CommonConstants;
import com.dz.common.core.dto.basic.GiftConfigDTO;
import com.dz.common.core.dto.basic.TenantConfigDTO;
import com.dz.common.core.dto.user.*;
import com.dz.common.core.dto.wechat.CodeSessionDTO;
import com.dz.common.core.dto.wechat.DecryptMobileDTO;
import com.dz.common.core.dto.wechat.DecryptUserDTO;
import com.dz.common.core.enums.WechatApiEnum;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.basic.CityInfoFeginClient;
import com.dz.common.core.fegin.basic.DefaultDataFeginClient;
import com.dz.common.core.fegin.basic.MpConfigFeignClient;
import com.dz.common.core.fegin.basic.TenantConfigFeignClient;
import com.dz.common.core.fegin.sales.CampaignFeignClient;
import com.dz.common.core.fegin.sales.InteractionTaskFeginClient;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.service.WechatRequestSevice;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.*;
import com.dz.ms.user.constants.CacheKeys;
import com.dz.ms.user.constants.LevelEnum;
import com.dz.ms.user.dto.UserMobileUpdDTO;
import com.dz.ms.user.dto.UserRegisterDTO;
import com.dz.ms.user.dto.UserUpdParamDTO;
import com.dz.ms.user.dto.UserUpdateDTO;
import com.dz.ms.user.dto.WechatLoginDTO;
import com.dz.ms.user.entity.UserInfo;
import com.dz.ms.user.mapper.UserInfoMapper;
import com.dz.ms.user.service.MUJIOpenApiService;
import com.dz.ms.user.service.UserInfoService;
import com.dz.ms.user.utils.JwtTokenUtils;
import com.dz.ms.user.vo.LogOffVo;
import com.dz.ms.user.vo.MyMileageDetailsVo;
import com.dz.ms.user.vo.UserMobileBindVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 用户信息
 *
 * @author: Handy
 * @date: 2022/2/4 17:33
 */
@Service
@Slf4j
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfo> implements UserInfoService {

    @Resource
    private UserInfoMapper userInfoMapper;
    @Resource
    private MpConfigFeignClient mpConfigFeignClient;
    @Resource
    private TenantConfigFeignClient tenantConfigFeignClient;
    @Resource
    private WechatRequestSevice wechatRequestSevice;
    @Resource
    private RedisService redisService;
    @Resource
    private MUJIOpenApiService mujiApiService;
    @Resource
    private InteractionTaskFeginClient interactionTaskFeginClient;

    @Resource
    private CityInfoFeginClient cityInfoFeginClient;

    @Resource
    private DefaultDataFeginClient defaultDataFeginClient;
    @Resource
    private CampaignFeignClient campaignFeignClient;

    @Value("${card.create.appid}")
    private String createAppId;
    @Value("${card.create.cardid}")
    private String createCardId;
    @Value("${card.create.type}")
    private String createType;

    /**
     * 小程序端微信登录
     * @param param
     * @return
     */
    @Override
    public UserSimpleDTO wechatLogin(WechatLoginDTO param) {
        CodeSessionDTO codeSession = mpConfigFeignClient.getMinappSessionByCode(param.getAppId(), param.getCode()).getData();
        if (null == codeSession) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "获取openid异常");
        }
        String key = "user:loginlock:openid:" + codeSession.getOpenid();
        boolean lock = redisService.lock(key, 60);
        if (!lock) {
            throw new BusinessException(ErrorCode.INTERNAL_ERROR, "登录中请稍后");
        }
        try {
            Long tenantId = codeSession.getTenantId();
            SecurityContext.setUser(new CurrentUserDTO(null, 0L, tenantId, null));
            UserInfo userInfo = userInfoMapper.getUserByOpenid(codeSession.getOpenid(), tenantId);
            if(null == userInfo) {
                userInfo = userInfoMapper.getNoOpenidUserByUnionid(codeSession.getUnionid(), tenantId);
                SecurityContext.setUser(new CurrentUserDTO(ClientTypeConstant.APP, 0L, codeSession.getTenantId(), null));
                /** 首次登录 */
                if (null == userInfo) {
                    userInfo = new UserInfo();
                    userInfo.setOpenid(codeSession.getOpenid());
                    userInfo.setUnionid(codeSession.getUnionid());
                    /*userInfo.setChannel(param.getChannel());
                    userInfo.setGdtVid(param.getGdtVid());*/
                    userInfo.setTenantId(codeSession.getTenantId());
                    userInfoMapper.insert(userInfo);
                }
                else {
                    UserInfo updateUser = new UserInfo();
                    updateUser.setId(userInfo.getId());
                    updateUser.setOpenid(codeSession.getOpenid());
                    userInfoMapper.updateById(updateUser);
                }
            }
            String uuid = UUID.randomUUID().toString();
            Map<String, Object> claims = new HashMap<>();
            claims.put("id", userInfo.getId());
            claims.put("type", ClientTypeConstant.APP);
            claims.put("tid", userInfo.getTenantId());
            String token = JwtTokenUtils.generatorToken(claims, uuid, 7 * 24);
            redisService.setString(CacheKeys.USER_SECRET + userInfo.getTenantId() + ":" + userInfo.getId(), uuid, CommonConstants.DAY_SECONDS);
            UserSimpleDTO userSimple = new UserSimpleDTO();
            userSimple.setId(userInfo.getId());
            userSimple.setOpenid(codeSession.getOpenid());
            userSimple.setUnionid(codeSession.getUnionid());
            userSimple.setToken(token);
            return userSimple;
        } catch (Exception e) {
            log.error("微信登录失败"+e.getMessage());
            throw new BusinessException(ErrorCode.INTERNAL_ERROR, e.getMessage());
        } finally {
            redisService.unlock(key);
        }
    }

    /**
     * 获取当前用户简要信息
     *
     * @return
     */
    @Override
    public UserSimpleDTO getUserSimpleInfo(Long uid) {
        UserInfoDTO userInfo = getUserInfo(uid);
        return BeanCopierUtils.convertObject(userInfo, UserSimpleDTO.class);
    }

    /**
     * 获取用户信息
     * @param uid
     * @return
     */
    @Override
    public UserInfoDTO getUserInfo(Long uid) {
        CurrentUserDTO currentUser = SecurityContext.getUser();
        if(ParamUtils.isNullOr0Long(uid)) {
            uid = currentUser.getUid();
        }
        String key = CacheKeys.USER_INFO + currentUser.getTenantId() + ":" + uid;
        UserInfoDTO userInfoDTO = (UserInfoDTO) redisService.get(key);
        if (null != userInfoDTO) {
            if (StringUtils.isNotBlank(userInfoDTO.getMobile())) {
                if (!userInfoDTO.getMobile().contains("*")){
                    //手机号脱敏
                    userInfoDTO.setMobile(userInfoDTO.getMobile().substring(0, 3) + "****" + userInfoDTO.getMobile().substring(7,  userInfoDTO.getMobile().length()));
                }
            }
            return userInfoDTO;
        }
        UserInfo userInfo = userInfoMapper.selectById(uid);
        if (null == userInfo) {
            return null;
        }
        userInfoDTO = BeanCopierUtils.convertObject(userInfo, UserInfoDTO.class);
        if (StringUtils.isNotBlank(userInfoDTO.getMobile())) {
            //手机号脱敏
            userInfoDTO.setMobile(userInfoDTO.getMobile().substring(0, 3) + "****" + userInfoDTO.getMobile().substring(7,  userInfoDTO.getMobile().length()));
        }
        redisService.set(key, userInfoDTO, CommonConstants.DAY_SECONDS);
        return userInfoDTO;
    }

    /**
     * 获取用户会员信息
     *
     * @return
     */
    @Override
    public MemberInfoDTO getUserMemberInfo(Long uid) {
        Long tenantId = SecurityContext.getUser().getTenantId();
        MemberInfoDTO memberInfo = null;
        if (null != uid){
            memberInfo = (MemberInfoDTO) redisService.get(CacheKeys.MEMBER_INFO + ":" + tenantId + ":" + uid);
            if (null != memberInfo){
                return memberInfo;
            }
        }
        UserInfoDTO userInfo = getUserInfo(uid);
        memberInfo = BeanCopierUtils.convertObject(userInfo,MemberInfoDTO.class);
        if (null != userInfo.getState() && userInfo.getState()==2){
            memberInfo.setIsMember(0);
            memberInfo.setIsFreeze(1);
            return memberInfo;
        }
        memberInfo.setIsFreeze(0);
        //查询会员信息
        JSONObject memberInfoJson = mujiApiService.memberDetailByUnionId(userInfo.getUnionid());
        if (!StringUtils.isEmpty(userInfo.getCardNo())){
            log.info("查询会员信息json数据："+com.alibaba.fastjson2.JSONObject.toJSONString(memberInfoJson));
            if (null != memberInfoJson){
                if (memberInfoJson.containsKey("code") && memberInfoJson.getString("code").contains("MemberNotExist")){
                    UserInfo userInfologOff = new UserInfo();
                    userInfologOff.setId(userInfo.getId());
                    userInfologOff.setLogOffType("5");
                    userInfologOff.setLogOffDesc("第三方注销");
                    userInfologOff.setLogOffTime(new Date());
                    userInfologOff.setState(0);
                    userInfoMapper.updateById(userInfologOff);
                    redisService.del(CacheKeys.USER_INFO + tenantId + ":" + userInfo.getId());
                    redisService.del(CacheKeys.MEMBER_INFO + ":" + tenantId + ":" + userInfo.getId());
                    redisService.del(CacheKeys.SIMPLE_USER_INFO + tenantId + ":" + userInfo.getId());
                    redisService.del(CacheKeys.USER_SECRET + tenantId + ":" + userInfo.getId());
                    return null;
                }
                if (memberInfoJson.containsKey("code") && memberInfoJson.getString("code").contains("MemberStatusError")){
                    UserInfo userInfologOff = new UserInfo();
                    userInfologOff.setId(userInfo.getId());
                    userInfologOff.setState(2);
                    userInfoMapper.updateById(userInfologOff);
                    memberInfo.setIsMember(0);
                    memberInfo.setIsFreeze(1);
                    return memberInfo;
                }
                if (memberInfoJson.containsKey("gender")) {
                    memberInfo.setGender(memberInfoJson.getInteger("gender"));
                }
                if (memberInfoJson.containsKey("level_id")) {
                    memberInfo.setCardLevel(memberInfoJson.getString("level_id"));
                }
                if (memberInfoJson.containsKey("level")) {
                    memberInfo.setCardLevelName(memberInfoJson.getString("level"));
                }
                if (memberInfoJson.containsKey("bonus_available")) {
                    memberInfo.setPoints(memberInfoJson.getLong("bonus_available"));
                }
                if (memberInfoJson.containsKey("mileage")) {
                    memberInfo.setCurrentMileage(memberInfoJson.getInteger("mileage"));
                }
                if (memberInfoJson.containsKey("avatar")) {
                    memberInfo.setAvatar(memberInfoJson.getString("avatar"));
                }
                if (memberInfoJson.containsKey("platform_code")) {
                    memberInfo.setPlatformCode(memberInfoJson.getString("platform_code"));
                }
                if (memberInfoJson.containsKey("nickname")) {
                    memberInfo.setUsername(memberInfoJson.getString("nickname"));
                }
                if (memberInfoJson.containsKey("birthday")) {
                    memberInfo.setBirthday(memberInfoJson.getString("birthday"));
                }
                if (memberInfoJson.containsKey("register_time")) {
                    memberInfo.setRegisterTime(memberInfoJson.getString("register_time"));
                }
                if (memberInfoJson.containsKey("mileage_end_date")) {
                    memberInfo.setMemberExpireTime(memberInfoJson.getString("mileage_end_date"));
                }
                if (memberInfoJson.containsKey("mobile")) {
                    String mobile = memberInfoJson.getString("mobile");
                    if (StringUtils.isNotBlank(mobile)){
                        //手机号脱敏
                        memberInfo.setMobile(mobile.substring(0, 3) + "****" + mobile.substring(7,  mobile.length()));
                    }else{
                        memberInfo.setMobile("");
                    }
                }
                if (StringUtils.isNotBlank(memberInfo.getCardLevelName())){
                    memberInfo.setCurrentLeveLMileage(LevelEnum.valueOf(memberInfo.getCardLevelName()).getLevelNum());
                    if (memberInfo.getCardLevel().equals("4")){
                        memberInfo.setNextMileage(0);
                        memberInfo.setNextLeveLMileage(LevelEnum.valueOf(memberInfo.getCardLevelName()).getLevelNum());
                    }else{
                        String nextLeveName=memberInfo.getCardLevel().equals("1")?"铜级会员":memberInfo.getCardLevel().equals("2")?"银级会员": "金级会员";
                        memberInfo.setNextLevelName(nextLeveName);
                        memberInfo.setNextLeveLMileage(LevelEnum.valueOf(nextLeveName).getLevelNum());
                        memberInfo.setNextMileage(memberInfo.getNextLeveLMileage()-memberInfo.getCurrentMileage());
                    }
                }
                memberInfo.setIsMember(1);
            }else{
                memberInfo.setIsMember(0);
                return memberInfo;
            }
        }else{
            if (null != memberInfoJson){
                if (memberInfoJson.containsKey("code") && memberInfoJson.getString("code").contains("MemberStatusError")){
                    UserInfo userInfologOff = new UserInfo();
                    userInfologOff.setId(userInfo.getId());
                    userInfologOff.setState(2);
                    userInfoMapper.updateById(userInfologOff);
                    memberInfo.setIsMember(0);
                    memberInfo.setIsFreeze(1);
                    return memberInfo;
                }
            }
            memberInfo.setIsMember(0);
            return memberInfo;
        }
        memberInfo.setCreateAppId(createAppId);
        memberInfo.setCreateCardId(createCardId);
        memberInfo.setCreateType(createType);

        redisService.set(CacheKeys.MEMBER_INFO + ":" + tenantId + ":" + userInfo.getId(), memberInfo,600);
        return memberInfo;
    }

    /**
     * 获取当前用户会员接口信息带缓存
     *
     * @return
     */
    @Cacheable(prefix = CacheKeys.MEMBER_INFO, key = "'#tenantId'+'#uid'", expire = 60)
    public MemberInfoDTO getUserMemberInfoCach(Long tenantId, Long uid) {
        return getUserMemberInfo(uid);
    }

    /**
     * 根据授权code获取微信手机号
     *
     * @param code
     * @return
     */
    @Override
    public DecryptUserDTO getWxPhoneByCode(String code) {
        Long tenantId = SecurityContext.getUser().getTenantId();
        Long uid = SecurityContext.getUser().getUid();
        Map<String, String> param = new HashMap<>();
        param.put("code", code);
        DecryptUserDTO request = wechatRequestSevice.request(WechatApiEnum.API_GET_USER_PHONE, tenantId, DecryptUserDTO.class, param);
        if (StringUtils.isNotBlank(request.getPhoneNumber())) {
            DecryptMobileDTO decryptMobile = new DecryptMobileDTO(request.getPhoneNumber(), request.getCountryCode());
            redisService.set(CacheKeys.USER_AUTHMOBILE + tenantId + ":" + uid, decryptMobile, CommonConstants.DAY_SECONDS);
        }
        return request;
    }

    /**
     * 根据授权code获取微信手机号对应会员信息
     *
     * @param code
     * @return
     */
    @Override
    public MemberInfoDTO getWxPhoneMemberByCode(String code){
        UserSimpleDTO userSimpleInfo = getCurrentUserSimpleInfo();
        MemberInfoDTO memberInfo = BeanCopierUtils.convertObject(userSimpleInfo, MemberInfoDTO.class);
        //查询CRM会员信息
        JSONObject memberDetail = mujiApiService.memberDetailByUnionId(userSimpleInfo.getUnionid());
        if (null != memberDetail && memberDetail.containsKey("member_code")){
            //绑定unionId和手机号
            /*JSONObject resultObj = mujiApiService.register(memberDetail.getString("mobile"),userSimpleInfo.getOpenid(), userSimpleInfo.getUnionid(),null);
            if (null != resultObj && resultObj.containsKey("code") && (resultObj.getString("code").equals("UnionidBindsError")
                    || resultObj.getString("code").equals("MobileBindsError"))){
                throw new BusinessException(ErrorCode.USER_REGISTER_ERROR, "绑定手机号unionId失败");
            }*/
            //先根据手机号查询是否存在用户记录
            LambdaQueryWrapper<UserInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserInfo::getUnionid, userSimpleInfo.getUnionid());
            wrapper.eq(UserInfo::getState, 1);
            UserInfo userInfo = userInfoMapper.selectOne(wrapper);
            if (null != userInfo) {
                if (StringUtils.isBlank(userInfo.getCardNo())){
                    if (memberDetail.containsKey("nickname")){
                        userInfo.setUsername(memberDetail.getString("nickname"));
                    }
                    if (memberDetail.containsKey("member_code")){
                        userInfo.setCardNo(memberDetail.getString("member_code"));
                    }
                    if (memberDetail.containsKey("level_id")){
                        userInfo.setCardLevel(memberDetail.getString("level_id"));
                    }
                    if (memberDetail.containsKey("gender")){
                        userInfo.setGender(memberDetail.getInteger("gender"));
                    }
                    if (memberDetail.containsKey("birthday")){
                        userInfo.setBirthday(memberDetail.getString("birthday"));
                    }
                    if (memberDetail.containsKey("mobile")){
                        userInfo.setMobile(memberDetail.getString("mobile"));
                    }
                    userInfo.setBindTime(new Date());
                    userInfoMapper.updateById(userInfo);
                }
                memberInfo.setIsMember(1);
                String key = CacheKeys.USER_SECRET + userInfo.getTenantId() + ":" + userInfo.getId();
                String secret = redisService.getString(key);
                redisService.setString(key, secret + "," + userInfo.getId(), 1800);
                redisService.del(CacheKeys.USER_INFO + userInfo.getTenantId() + ":" + userInfo.getId());
                redisService.del(CacheKeys.SIMPLE_USER_INFO + userInfo.getTenantId() + ":" + userInfo.getId());
            }else{
                //把已有会员信息注册状态改为绑定
                UserInfo updateUserOld = new UserInfo();
                updateUserOld.setId(userSimpleInfo.getId());
                updateUserOld.setState(1);
                if (memberDetail.containsKey("nickname")){
                    updateUserOld.setUsername(memberDetail.getString("nickname"));
                }
                if (memberDetail.containsKey("member_code")){
                    updateUserOld.setCardNo(memberDetail.getString("member_code"));
                }
                if (memberDetail.containsKey("level_id")){
                    updateUserOld.setCardLevel(memberDetail.getString("level_id"));
                }
                if (memberDetail.containsKey("gender")){
                    updateUserOld.setGender(memberDetail.getInteger("gender"));
                }
                if (memberDetail.containsKey("birthday")){
                    updateUserOld.setBirthday(memberDetail.getString("birthday"));
                }
                if (memberDetail.containsKey("mobile")){
                    updateUserOld.setMobile(memberDetail.getString("mobile"));
                }
                updateUserOld.setBindTime(new Date());
                userInfoMapper.updateById(updateUserOld);
                memberInfo.setIsMember(1);
                String key = CacheKeys.USER_SECRET + userSimpleInfo.getTenantId() + ":" + userSimpleInfo.getId();
                String secret = redisService.getString(key);
                redisService.setString(key, secret + "," + userSimpleInfo.getId(), 1800);
                redisService.del(CacheKeys.USER_INFO + userSimpleInfo.getTenantId() + ":" + userSimpleInfo.getId());
                redisService.del(CacheKeys.SIMPLE_USER_INFO + userSimpleInfo.getTenantId() + ":" + userSimpleInfo.getId());
            }
        }else{
            memberInfo.setIsMember(0);
        }
        return memberInfo;
    }

    /**
     * 用户注册
     *
     * @param param
     * @param uid
     * @param tenantId
     */
    @Override
    @Lock(prefix = "user:registerlock", key = "'#tenantId'+'#uid'")
    public void userRegister(UserRegisterDTO param, Long uid, Long tenantId) {
        Object object = redisService.get(CacheKeys.USER_AUTHMOBILE + tenantId + ":" + uid);
        if (null == object) {
            throw new BusinessException(ErrorCode.INTERNAL_ERROR, "手机号未授权");
        }
        DecryptMobileDTO decryptMobile = (DecryptMobileDTO) object;
        if (!param.getMobile().equals(decryptMobile.getPhoneNumber())) {
            throw new BusinessException(ErrorCode.INTERNAL_ERROR, "当前手机号非最新授权手机号");
        }
        UserInfoDTO userSimple = getUserInfo(uid);
        log.info("注册用户参数：{}", param);
        JSONObject resultObj = mujiApiService.register(param.getMobile(),userSimple.getOpenid(), userSimple.getUnionid(),param.getChannelTwo());
        if (null != resultObj && resultObj.containsKey("member_code") && !resultObj.getString("member_code").isEmpty()){
            UserInfo userInfo = new UserInfo();
            UserInfo getUser = userInfoMapper.selectOne(new LambdaQueryWrapper<UserInfo>().eq(UserInfo::getMobile, param.getMobile()).last("limit 1"));
            if(null != getUser && null != getUser.getId() && !getUser.getId().equals(uid) && StringUtils.isEmpty(getUser.getOpenid())) {
                userInfo = BeanCopierUtils.convertObject(userSimple,UserInfo.class);
                userInfoMapper.deleteById(uid);
                String key = CacheKeys.USER_SECRET + tenantId +":"+ uid;
                String secret = redisService.getString(key);
                redisService.setString(key,secret+","+getUser.getId(),1800);
                uid = getUser.getId();
            }
            userInfo.setId(uid);
            userInfo.setPolicyVersion(param.getPersonalAccessVersion());
            userInfo.setMobile(decryptMobile.getPhoneNumber());
            if (StringUtils.isNotEmpty(param.getAvatar())) {
                userInfo.setAvatar(param.getAvatar());
            }
            userInfo.setOpenid(userSimple.getOpenid());
            userInfo.setUnionid(userSimple.getUnionid());
            userInfo.setCardNo(resultObj.getString("member_code"));
            userInfo.setGender(param.getGender());
            userInfo.setProvince(param.getProvince());
            userInfo.setCity(param.getCity());
            userInfo.setRegisterTime(new Date());
            userInfo.setChannel(param.getChannelTwo());
            userInfo.setGdtVid(param.getGdtVid());
            userInfo.setCampaignCode(param.getCampaignCode());
            userInfo.setInviteUid(param.getInviteUserId());
            userInfo.setAddress(param.getAddress());
            userInfo.setUsername(param.getUsername());
            userInfo.setArea(param.getArea());
            if (StringUtils.isNotBlank(param.getChannelTwo())){
                userInfo.setChannel(param.getChannelTwo());
            }
            userInfoMapper.updateById(userInfo);
            redisService.del(CacheKeys.USER_INFO + tenantId + ":" + uid);
            redisService.del(CacheKeys.MEMBER_INFO + ":" + tenantId + ":" + uid);
            redisService.del(CacheKeys.SIMPLE_USER_INFO + tenantId + ":" + uid);
            if (param.getInviteUserId() != null){
                interactionTaskFeginClient.taskSuccessRecordFriend(param.getInviteUserId());
            }
            //注册完成以后修改用户信息
            String proCode="";
            if (StringUtils.isNotBlank(param.getProvince())){
                proCode= cityInfoFeginClient.getCodeByName(param.getProvince());
                if (StringUtils.isNotBlank(proCode)){
                    if (proCode.contains("业务繁忙")){
                        proCode="";
                    }
                }
            }
            String cityCode="";
            if (StringUtils.isNotBlank(param.getCity())){
                cityCode= cityInfoFeginClient.getCodeByName(param.getCity());
                if (StringUtils.isNotBlank(cityCode)){
                    if (cityCode.contains("业务繁忙")){
                        cityCode="";
                    }
                }
            }
            try {
                JSONObject result=mujiApiService.updateMemberInfo(userInfo.getCardNo(), param.getUsername(), param.getBirthday(), param.getGender(), param.getAvatar(), proCode, cityCode);
                if (null == result || !result.containsKey("member_code")){
                    log.error("修改会员信息失败");
                    throw new BusinessException(ErrorCode.INTERNAL_ERROR, "修改会员信息失败");
                }
            }catch (Exception e){
                log.error("修改会员信息失败"+e.getMessage());
                throw new BusinessException(ErrorCode.INTERNAL_ERROR, "修改会员信息失败");
            }
        }else{
            if (null != resultObj && resultObj.containsKey("code") && (resultObj.getString("code").equals("UnionidBindsError")
                    || resultObj.getString("code").equals("MobileBindsError"))){
                throw new BusinessException(ErrorCode.USER_REGISTER_ERROR, "绑定手机号unionId失败");
            }
            throw new BusinessException(ErrorCode.INTERNAL_ERROR, "注册失败");
        }
    }

    /**
     * 更新当前用户信息
     *
     * @param param
     */
    @Override
    @Lock(prefix = "user:updatelock", key = "'#tenantId'+'#uid'")
    public void updateUserInfo(UserUpdateDTO param, Long uid, Long tenantId) {
//        TenantConfigDTO tenantConfig = tenantConfigFeignClient.getTenantConfigById(tenantId).getData();
//        if(null != tenantConfig && null != tenantConfig.getUserCrm()  && tenantConfig.getUserCrm().equals(1)) {
//            MemberInfoDTO getMember = getUserMemberInfoCach(tenantId, null);
//            MemberInfoDTO member = new MemberInfoDTO();
//            member.setUnionid(getMember.getUnionid());
//            member.setGender(param.getGender());
//            if (StringUtils.isNotEmpty(param.getBirthday()) && !param.getBirthday().equals(getMember.getBirthday())) {
//                member.setBirthday(param.getBirthday());
//            }
//            member.setProvince(param.getProvince());
//            member.setCity(param.getCity());
//            member.setUsername(param.getUsername());
//            member.setAvatar(param.getAvatar());
//            member.setPersonalAccessVersion(param.getPolicyVersion());
//            member.setAgreeClauseAndPrivacy(param.getAgreeClauseAndPrivacy());
//            member.setAgreeBrandCommunicate(param.getAgreeBrandCommunicate());
//            member.setAgreeCorpCommunicate(param.getAgreeCorpCommunicate());
            /*Result<Object> result = crmApiFeignClient.updateMember(member, tenantId);
            if (!result.isSuccess()) {
                throw new BusinessException(ErrorCode.INTERNAL_ERROR, result.getMsg());
            }*/
//        }
        //修改用户信息同步crm

        UserInfo userInfo = BeanCopierUtils.convertObjectTrim(param,UserInfo.class);
        userInfo.setId(uid);
        userInfoMapper.updateById(userInfo);
        redisService.del(CacheKeys.USER_INFO + tenantId + ":" + uid);
        redisService.del(CacheKeys.MEMBER_INFO + ":" + tenantId + ":" + uid);
        redisService.del(CacheKeys.SIMPLE_USER_INFO + tenantId + ":" + uid);
        UserInfoDTO userInfoDTO = getUserInfo(uid);
        //crm修改会员信息接口
        String proCode= cityInfoFeginClient.getCodeByName(param.getProvince());
        if (StringUtils.isNotBlank(proCode)){
            if (proCode.contains("业务繁忙")){
                proCode="";
            }
        }
        String cityCode= cityInfoFeginClient.getCodeByName(param.getCity());
        if (StringUtils.isNotBlank(cityCode)){
            if (cityCode.contains("业务繁忙")){
                cityCode="";
            }
        }
        try {
            JSONObject result=mujiApiService.updateMemberInfo(userInfoDTO.getCardNo(), param.getUsername(), param.getBirthday(), param.getGender(), param.getAvatar(), proCode, cityCode);
            if (null == result || !result.containsKey("member_code")){
                log.error("修改会员信息失败");
                throw new BusinessException(ErrorCode.INTERNAL_ERROR, "修改会员信息失败");
            }
        }catch (Exception e){
            log.error("修改会员信息失败"+e.getMessage());
            throw new BusinessException(ErrorCode.INTERNAL_ERROR, "修改会员信息失败");
        }
    }

    /**
     * 当前用户新增clientId
     *
     */
    @Override
    public String addUserClientId(Long uid, Long tenantId) {
        String clientId = null;
        UserInfo userInfo = userInfoMapper.selectById(uid);
        if(Objects.nonNull(userInfo) && StringUtils.isBlank(userInfo.getClientId())){
            UserInfo updUser = new UserInfo();
            updUser.setClientId(MD5.encodeLowerCase(userInfo.getUnionid()));
            updUser.setId(uid);
            clientId = updUser.getClientId();
            userInfoMapper.updateById(updUser);
            redisService.del(CacheKeys.USER_INFO + tenantId + ":" + uid);
            redisService.del(CacheKeys.MEMBER_INFO + ":" + tenantId + ":" + uid);
        }
        return clientId;
    }

    /**
     * 注销
     *
     * @param param
     */
    @Override
    @Lock(prefix = "user:logofflock", key = "'#tenantId'+'#uid'")
    public void logOff(LogOffVo param, Long uid, Long tenantId) {
        UserSimpleDTO userSimple = getUserSimpleInfo(uid);
        String[] type = param.getLogOffType().split(",");
        Integer[] typeInt = new Integer[type.length];
        for (int i = 0; i < type.length; i++) {
            typeInt[i] = Integer.parseInt(type[i]);
        }
        JSONObject result =mujiApiService.cancelMemberInfo(userSimple.getCardNo(), param.getSmsCode(), typeInt, param.getLogOffDesc());
        if (null != result && result.containsKey("code") && result.getString("code").equals("InvalidParameter")){
            throw new BusinessException(ErrorCode.BAD_REQUEST, "验证码错误");
        }
        if (null == result || !result.containsKey("member_code")){
            log.error("注销会员失败");
            throw new BusinessException(ErrorCode.BAD_REQUEST, "注销会员失败");
        }

        // campaign数据删除
        Result<Boolean> result1 = campaignFeignClient.delCurrentUserCampaignData(uid);
        if (!result1.isSuccess()) {
            // TODO 暂不抛出异常
            log.error("注销会员，campaign数据删除失败，{}", result1.getMsg());
        }


        UserInfo userInfo = new UserInfo();
        userInfo.setId(uid);
        userInfo.setLogOffType(param.getLogOffType());
        userInfo.setLogOffDesc(param.getLogOffDesc());
        userInfo.setLogOffTime(new Date());
        userInfo.setState(0);
        userInfoMapper.updateById(userInfo);
        redisService.del(CacheKeys.USER_INFO + tenantId + ":" + uid);
        redisService.del(CacheKeys.MEMBER_INFO + ":" + tenantId + ":" + uid);
        redisService.del(CacheKeys.SIMPLE_USER_INFO + tenantId + ":" + uid);
        redisService.del(CacheKeys.USER_SECRET + tenantId + ":" + uid);
    }

    /**
     * 根据用户ID列表获取用户openid列表
     *
     * @param ids
     * @param tenantId
     * @return
     */
    @Override
    public List<String> getUserOpenidByIds(List<Long> ids, Long tenantId) {
        return userInfoMapper.getUserOpenidByIds(ids, tenantId);
    }

    /**
     * 更新授权隐私条款版本号
     *
     * @param  param
     */
    @Override
    public void updateUserPolicyVersion(UserUpdateDTO param) {
        CurrentUserDTO user = SecurityContext.getUser();
        TenantConfigDTO tenantConfig = tenantConfigFeignClient.getTenantConfigById(user.getTenantId()).getData();
        if(null != tenantConfig && null != tenantConfig.getUserCrm()  && tenantConfig.getUserCrm().equals(1)) {
            UserInfoDTO userSimple = getUserInfo(user.getUid());
            /*Result<Object> result = crmApiFeignClient.policyVersionAuth(userSimple.getUnionid(), policyVersion, user.getTenantId());
            if (!result.isSuccess()) {
                throw new BusinessException(result.getCode(), result.getMsg());
            }*/
        }
        UserInfo userInfo = new UserInfo();
        userInfo.setId(user.getUid());
        userInfo.setPolicyVersion(param.getPolicyVersion());
        userInfo.setIsAgreeBrand(param.getIsAgreeBrand());
        userInfoMapper.updateById(userInfo);
        redisService.del(CacheKeys.USER_INFO + user.getTenantId() + ":" + user.getUid());
    }

        /**
     * 获取当前用户简要信息
     *
     * @return
     */
    @Override
    public UserSimpleDTO getCurrentUserSimpleInfo() {
        CurrentUserDTO user = SecurityContext.getUser();
        String key = CacheKeys.SIMPLE_USER_INFO + user.getTenantId() + ":" + user.getUid();
        UserSimpleDTO userSimpleInfo = (UserSimpleDTO) redisService.get(key);
        if (null != userSimpleInfo) {
            if (StringUtils.isNotBlank(userSimpleInfo.getMobile())){
                return userSimpleInfo;
            }else{
                UserInfo userInfo = userInfoMapper.selectById(user.getUid());
                if (null == userInfo) {
                    return null;
                }
                userSimpleInfo = BeanCopierUtils.convertObject(userInfo, UserSimpleDTO.class);
                redisService.set(key, userSimpleInfo, CommonConstants.DAY_SECONDS);
                return userSimpleInfo;
            }
        }
        UserInfo userInfo = userInfoMapper.selectById(user.getUid());
        if (null == userInfo) {
            return null;
        }
        userSimpleInfo = BeanCopierUtils.convertObject(userInfo, UserSimpleDTO.class);
        redisService.set(key, userSimpleInfo, CommonConstants.DAY_SECONDS);
        return userSimpleInfo;
    }

    /**
     * 我的积分
     *
     * @return
     */
    @Override
    public MyPointsDTO myPoints() {
        CurrentUserDTO user = SecurityContext.getUser();
        UserSimpleDTO userSimpleInfo = getUserSimpleInfo(user.getUid());
        JSONObject jsonObject = mujiApiService.memberPointsCount(userSimpleInfo.getCardNo());
        MyPointsDTO myPointsDTO = new MyPointsDTO();
        if (null != jsonObject && jsonObject.containsKey("member_code")){
            if (jsonObject.containsKey("bonus_amount") && null != jsonObject.get("bonus_amount")){
                myPointsDTO.setPointsNum(jsonObject.getInteger("bonus_amount"));
            }
            if (jsonObject.containsKey("expire_bonus_amount") && null != jsonObject.get("expire_bonus_amount")){
                myPointsDTO.setExpirePointsNum(jsonObject.getInteger("expire_bonus_amount"));
            }
        }
        return myPointsDTO;
    }

    /**
     * 我的积分流水
     *
     * @param param
     * @return
     */
    @Override
    public PageInfo<MyPointsRecordsDTO> myPointsRecords(MyMileageDetailsVo param) {
        CurrentUserDTO user = SecurityContext.getUser();
        //查询会员信息
        UserInfoDTO userInfoDTO = getUserInfo(user.getUid());
        Page<MyPointsRecordsDTO> page = new Page<>(param.getPageNum(), param.getPageSize());
        String startTime = param.getYear()+"-01-01 00:00:00";
        String endTime = param.getYear()+"-12-31 23:59:59";
        List<MyPointsRecordsDTO> myPointsRecordsDTOList=new ArrayList<>();
        //查询所有任务名称
        List<String> taskNameList = interactionTaskFeginClient.getInteractionTaskInfo().getData();
//        //查询积分流水
        JSONObject memberPointsList = mujiApiService.memberPointsList(userInfoDTO.getCardNo(), startTime, endTime, param.getPageNum(), param.getPageSize());
        boolean hasMore = false;
        if (null != memberPointsList) {
            JSONArray list = memberPointsList.getJSONArray("items");
            if (!CollectionUtils.isEmpty(list)) {
                for (Object account : list) {
                    //将jsonObject转为map
                    JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(account));
                    MyPointsRecordsDTO myPointsRecordsDTO=new MyPointsRecordsDTO();
                    myPointsRecordsDTO.setExpireTime(jsonObject.getString("expired_at"));
                    myPointsRecordsDTO.setPointsName(jsonObject.getString("reason"));
                    myPointsRecordsDTO.setObtainTime(jsonObject.getString("created_at"));
                    myPointsRecordsDTO.setPointsNum(jsonObject.getString("bonus_amount"));
                    myPointsRecordsDTO.setChangeType(jsonObject.getInteger("change_type"));
                    myPointsRecordsDTO.setBonusSn(jsonObject.getString("bonus_sn"));
                    myPointsRecordsDTOList.add(myPointsRecordsDTO);
                    if (!CollectionUtils.isEmpty(taskNameList) && taskNameList.contains(jsonObject.getString("reason"))){
                        myPointsRecordsDTO.setPointsType(1);
                    }
                }
            }
            if (memberPointsList.containsKey("has_more") && memberPointsList.getString("has_more").equals("Y")) {
                hasMore = true;
            }
        }
        return new PageInfo<>(param.getPageNum(), param.getPageSize(), hasMore, myPointsRecordsDTOList);
    }


    /**
     * 即将过期积分列表
     * @return
     */
    @Override
    public PageInfo<HistoryPointsRecordsDTO> recentExpirePoints(MyMileageDetailsVo param) {
        CurrentUserDTO user = SecurityContext.getUser();
        //查询会员信息
        UserInfoDTO userInfoDTO = getUserInfo(user.getUid());
        List<HistoryPointsRecordsDTO> historyPointsRecordsDTO=new ArrayList<>();
//        //查询积分流水
        JSONObject memberPointsList = mujiApiService.recentExpirePoints(userInfoDTO.getCardNo(), param.getPageNum(), param.getPageSize());
        boolean hasMore = false;
        if (null != memberPointsList) {
            JSONArray list = memberPointsList.getJSONArray("items");
            if (!CollectionUtils.isEmpty(list)) {
                for (Object account : list) {
                    //将jsonObject转为map
                    JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(account));
                    HistoryPointsRecordsDTO myPointsRecordsDTO=new HistoryPointsRecordsDTO();
                    myPointsRecordsDTO.setExpireTime(jsonObject.getString("expired_at"));
                    myPointsRecordsDTO.setPointsNum(jsonObject.getString("bonus_amount"));
                    historyPointsRecordsDTO.add(myPointsRecordsDTO);
                }
            }
            if (memberPointsList.containsKey("has_more") && memberPointsList.getString("has_more").equals("Y")) {
                hasMore = true;
            }
        }
        return new PageInfo<>(param.getPageNum(), param.getPageSize(), hasMore, historyPointsRecordsDTO);
    }

    /**
     *历史过期积分列表
     * @return
     */
    @Override
    public PageInfo<HistoryPointsRecordsDTO> historyExpirePoints(MyMileageDetailsVo param) {
        // 定义线程安全的日期时间格式化器，并指定时区
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").withZone(ZoneId.systemDefault());
        CurrentUserDTO user = SecurityContext.getUser();
        //查询会员信息
        UserInfoDTO userInfoDTO = getUserInfo(user.getUid());
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 当前时间减一年
        LocalDateTime oneYearAgo = now.minusYears(1);
        LocalDateTime oneYearDayAgo = oneYearAgo.plusDays(6);
        // 格式化时间字符串
        String startTime = formatter.format(oneYearDayAgo.atZone(ZoneId.systemDefault()));
        String endTime = formatter.format(now.atZone(ZoneId.systemDefault()));
        List<HistoryPointsRecordsDTO> historyPointsRecordsDTO=new ArrayList<>();
//        //查询积分流水
        JSONObject memberPointsList = mujiApiService.historyExpirePoints(userInfoDTO.getCardNo(), startTime, endTime, param.getPageNum(), param.getPageSize());
        boolean hasMore = false;
        if (null != memberPointsList) {
            JSONArray list = memberPointsList.getJSONArray("items");
            if (!CollectionUtils.isEmpty(list)) {
                for (Object account : list) {
                    //将jsonObject转为map
                    JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(account));
                    HistoryPointsRecordsDTO myPointsRecordsDTO=new HistoryPointsRecordsDTO();
                    myPointsRecordsDTO.setExpireTime(jsonObject.getString("expired_at"));
                    myPointsRecordsDTO.setPointsNum(jsonObject.getString("bonus_amount"));
                    historyPointsRecordsDTO.add(myPointsRecordsDTO);
                }
            }
            if (memberPointsList.containsKey("has_more") && memberPointsList.getString("has_more").equals("Y")) {
                hasMore = true;
            }
        }
        return new PageInfo<>(param.getPageNum(), param.getPageSize(), hasMore, historyPointsRecordsDTO);
    }
    /**
     * 我的积分详情
     *
     * @param bonusSn
     * @return
     */
    @Override
    public MyPointsDetailsDTO myPointsDetails(String bonusSn) {
        UserInfoDTO userInfoDTO = getUserInfo(SecurityContext.getUser().getUid());
        MyPointsDetailsDTO myPointsDetailsDTO = null;
        JSONObject detail = mujiApiService.memberPointsDetail(userInfoDTO.getCardNo(), bonusSn);
        if (null != detail) {
            myPointsDetailsDTO = new MyPointsDetailsDTO();
            myPointsDetailsDTO.setPointsNum(detail.getString("bonus"));
            myPointsDetailsDTO.setObtainTime(getObtainTime(detail.getString("created_at")));
            myPointsDetailsDTO.setObtainDesc(detail.getString("reason"));
            myPointsDetailsDTO.setConsumptionTime(getConsumptionTime(detail.getString("pay_time")));
            myPointsDetailsDTO.setConsumptionAmount(fenToYuan(detail.getString("buyer_fee")));
            myPointsDetailsDTO.setCumulativeAmount(fenToYuan(detail.getString("calculate_fee")));
            myPointsDetailsDTO.setChannelStore(detail.getString("store_name"));
            myPointsDetailsDTO.setChangeType(Integer.parseInt(detail.getString("change_type")));
        }
        return myPointsDetailsDTO;
    }

    /**
     * 我的里程流水
     *
     * @param param
     * @return
     */
    @Override
    public PageInfo<MyMileageRecordsDTO> myMileageRecords(MyMileageDetailsVo param) {
        CurrentUserDTO user = SecurityContext.getUser();
        //查询会员信息
        UserInfoDTO userInfoDTO = getUserInfo(user.getUid());
        Page<MyMileageRecordsDTO> page = new Page<>(param.getPageNum(), param.getPageSize());
        String startTime = param.getYear()+"-01-01 00:00:00";
        String endTime = param.getYear()+"-12-31 23:59:59";
        List<MyMileageRecordsDTO> myMileageRecordsDTOList=new ArrayList<>();
//        //查询里程流水
        JSONObject memberMileageList = mujiApiService.memberMileageList(userInfoDTO.getCardNo(), startTime, endTime, param.getPageNum(), param.getPageSize());
        boolean hasMore = false;
        if (null != memberMileageList) {
            JSONArray list = memberMileageList.getJSONArray("items");
            if (!CollectionUtils.isEmpty(list)) {
                for (Object account : list) {
                    //将jsonObject转为map
                    JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(account));
                    MyMileageRecordsDTO myMileageRecordsDTO=new MyMileageRecordsDTO();
                    myMileageRecordsDTO.setMileageName(jsonObject.getString("reason"));
                    myMileageRecordsDTO.setObtainTime(jsonObject.getString("created_at"));
                    myMileageRecordsDTO.setChannel(jsonObject.getString("store_name"));
                    myMileageRecordsDTO.setPointsNum(jsonObject.getString("mileage"));
                    myMileageRecordsDTO.setMileageSn(jsonObject.getString("mileage_sn"));
                    myMileageRecordsDTO.setChangeType(jsonObject.getInteger("change_type"));
                    myMileageRecordsDTOList.add(myMileageRecordsDTO);
                }
            }
            if (memberMileageList.containsKey("has_more") && memberMileageList.getString("has_more").equals("Y")) {
                hasMore = true;
            }
        }
        return new PageInfo<>(param.getPageNum(), param.getPageSize(), hasMore, myMileageRecordsDTOList);
    }

    /**
     * 我的里程详情
     *
     * @param mileageSn
     * @return
     */
    @Override
    public MyMileageDetailsDTO myMileageDetails(String mileageSn) {
        UserInfoDTO userInfoDTO = getUserInfo(SecurityContext.getUser().getUid());
        MyMileageDetailsDTO myMileageDetailsDTO = null;
        JSONObject detail = mujiApiService.memberMileageDetail(userInfoDTO.getCardNo(), mileageSn);
        if (null != detail) {
            myMileageDetailsDTO = new MyMileageDetailsDTO();
            myMileageDetailsDTO.setPointsNum(detail.getString("mileage"));
            myMileageDetailsDTO.setObtainTime(getObtainTime(detail.getString("created_at")));
            myMileageDetailsDTO.setObtainDesc(detail.getString("reason"));
            myMileageDetailsDTO.setConsumptionTime(getConsumptionTime(detail.getString("pay_time")));
            myMileageDetailsDTO.setConsumptionAmount(fenToYuan(detail.getString("buyer_fee")));
            myMileageDetailsDTO.setCumulativeAmount(fenToYuan(detail.getString("calculate_fee")));
            myMileageDetailsDTO.setChannelStore(detail.getString("store_name"));
            myMileageDetailsDTO.setChangeType(Integer.parseInt(detail.getString("change_type")));
        }
        return myMileageDetailsDTO;
    }

    private static String getObtainTime(String obtainTime) {
        if(StringUtils.isNotBlank(obtainTime)){
            return LocalDateTime.parse(obtainTime, DateUtils.Y_MD_HMS_DTF2).format(DateUtils.Y_MD_DTF1);
        }
        return obtainTime;
    }

    private static String getConsumptionTime(String consumptionTime) {
        if(StringUtils.isNotBlank(consumptionTime)){
            return LocalDateTime.parse(consumptionTime, DateUtils.Y_MD_HMS_DTF2).format(DateUtils.Y_MD_HMS_DTF6);
        }
        return consumptionTime;
    }

    private String fenToYuan(String fen) {
        if(StringUtils.isNotBlank(fen)){
            return new BigDecimal(fen).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).toString();
        }
        return fen;
    }

    @Override
    public PageInfo<UserSimpleDTO> getUserSimpleInfoList(Integer pageNum, Integer pageSize) {
        //分页查询UserInfo用户信息列表
        Page<UserInfo> page = new Page<>(pageNum, pageSize);
        Page<UserInfo> userInfoPage = userInfoMapper.selectPage(page, null);
        List<UserInfo> userInfoList = userInfoPage.getRecords();
        List<UserSimpleDTO> userSimpleDTOList = BeanCopierUtils.convertList(userInfoList, UserSimpleDTO.class);
        return new PageInfo<>(userInfoPage.getCurrent(), userInfoPage.getSize(), userInfoPage.getTotal(), userSimpleDTOList);
    }

    @Override
    public void sendSmsCode() {
        UserSimpleDTO currentUserInfo = getCurrentUserSimpleInfo();
        //查询会员信息
        JSONObject memberInfoJson = mujiApiService.memberDetailByUnionId(currentUserInfo.getUnionid());
        String mobile="";
        if (null != memberInfoJson){
            if (memberInfoJson.containsKey("mobile")) {
                mobile = memberInfoJson.getString("mobile");
                if (StringUtils.isBlank(mobile)){
                    throw new BusinessException(ErrorCode.INTERNAL_ERROR, "请先绑定手机号");
                }
            }
        }else{
            throw new BusinessException(ErrorCode.INTERNAL_ERROR, "请先绑定手机号");
        }
        mujiApiService.sendMobileCode(mobile);
    }

    @Override
    public String sendSmsCode(String mobile) {
        Long tenantId = SecurityContext.getUser().getTenantId();
        String time = redisService.getString(CacheKeys.MOBILE_CODE_INTERVAL_TIME + tenantId + ":" + mobile);
        if (StringUtils.isNotBlank(time)) {
            throw new BusinessException(ErrorCode.INTERNAL_ERROR, "请60秒后重新获取");
        }
        JSONObject checkRes = mujiApiService.memberCheck(mobile);
        if (null == checkRes || !checkRes.containsKey("is_member") || !StringUtils.equals("N",checkRes.getString("is_member"))){
            throw new BusinessException(ErrorCode.USER_REGISTER_ERROR, "校验手机号非未注册");
        }
        JSONObject codeRes = mujiApiService.sendMobileCode(mobile);
        if (null != codeRes && (StringUtils.equalsIgnoreCase(codeRes.getString("code"),"InvalidParameter") || StringUtils.equalsIgnoreCase(codeRes.getString("code"),"MobileNotExist"))){
            throw new BusinessException(ErrorCode.BAD_REQUEST, "请填写正确的手机号");
        }
        if (null == codeRes || !codeRes.containsKey("request_id")){
            throw new BusinessException(ErrorCode.INTERNAL_ERROR);
        }
        redisService.setString(CacheKeys.MOBILE_CODE_INTERVAL_TIME + tenantId + ":" + mobile,"y",60);
        return codeRes.getString("request_id");
    }

    private boolean validMobile(String mobile) {
        Pattern pattern = Pattern.compile("^[1][0-9]{10}$");
        Matcher matcher = pattern.matcher(mobile);
        return matcher.matches();
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String mobileUpdate(UserMobileUpdDTO userMobileUpdDTO) {
        Long uid = SecurityContext.getUser().getUid();
        Long tenantId = SecurityContext.getUser().getTenantId();
        UserInfo userInfo = new UserInfo();
        userInfo.setId(uid);
        userInfo.setMobile(userMobileUpdDTO.getNewMobile());
        userInfoMapper.updateById(userInfo);
        UserSimpleDTO currentUserInfo = getCurrentUserSimpleInfo();
        userMobileUpdDTO.setMemberCode(currentUserInfo.getCardNo());
        JSONObject result = mujiApiService.mobileUpdate(userMobileUpdDTO);
        if (null == result || !result.containsKey("member_code")){
            log.error("会员手机号变更失败");
            throw new BusinessException(ErrorCode.USER_REGISTER_ERROR, "会员手机号变更失败");
        }
        try{
            redisService.del(CacheKeys.USER_INFO + tenantId + ":" + uid);
            redisService.del(CacheKeys.MEMBER_INFO + ":" + tenantId + ":" + uid);
            redisService.del(CacheKeys.SIMPLE_USER_INFO + tenantId + ":" + uid);
            this.getUserInfo(uid);
            this.getUserMemberInfo(uid);
            this.getCurrentUserSimpleInfo();
        } catch (Exception e){
            log.error("会员手机号变更成功,清理缓存失败",e);
        }
        return result.getString("member_code");
    }

    @Override
    public String mobileBind(UserMobileBindVo userMobileBindVo) {
        Long uid = SecurityContext.getUser().getUid();
        Long tenantId = SecurityContext.getUser().getTenantId();
        UserSimpleDTO currentUserInfo = getCurrentUserSimpleInfo();
        JSONObject result = mujiApiService.register(userMobileBindVo.getNewMobile(),currentUserInfo.getOpenid(), currentUserInfo.getUnionid(),userMobileBindVo.getChannelTwo());
        if (null == result || !result.containsKey("member_code")){
            log.error("会员手机号绑定失败");
            throw new BusinessException(ErrorCode.USER_REGISTER_ERROR, "会员手机号绑定失败");
        }
        try{
            UserInfo userInfo = new UserInfo();
            userInfo.setId(uid);
            userInfo.setMobile(userMobileBindVo.getNewMobile());
            userInfoMapper.updateById(userInfo);
            redisService.del(CacheKeys.USER_INFO + tenantId + ":" + uid);
            redisService.del(CacheKeys.MEMBER_INFO + ":" + tenantId + ":" + uid);
            redisService.del(CacheKeys.SIMPLE_USER_INFO + tenantId + ":" + uid);
            this.getUserInfo(uid);
            this.getUserMemberInfo(uid);
            this.getCurrentUserSimpleInfo();
        } catch (Exception e){
            log.error("会员手机号变更成功,清理缓存失败",e);
        }
        return result.getString("member_code");
    }

    @Override
    public UserSimpleDTO getDbUserSimpleInfo(Long uid) {
        UserInfo userInfo = userInfoMapper.selectById(uid);
        if (null == userInfo) {
            return null;
        }
        UserSimpleDTO userInfoDTO = BeanCopierUtils.convertObject(userInfo, UserSimpleDTO.class);
        if (StringUtils.isNotBlank(userInfoDTO.getMobile())) {
            //手机号脱敏
            userInfoDTO.setMobile(userInfoDTO.getMobile().substring(0, 3) + "****" + userInfoDTO.getMobile().substring(7, userInfoDTO.getMobile().length()));
        }
        return userInfoDTO;
    }

    @Override
    public List<UserSimpleDTO> getDbUserSimpleInfoList(List<Long> uidList) {
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(UserInfo::getId, uidList);
        List<UserInfo> userInfos = userInfoMapper.selectList(queryWrapper);
        return BeanCopierUtils.convertList(userInfos, UserSimpleDTO.class);
    }

    @Override
    public List<Long> getUserIdListByName(String userName) {
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(UserInfo::getUsername, userName);
        List<UserInfo> userInfos = userInfoMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(userInfos)) {
            return Collections.emptyList();
        }
        return userInfos.stream().map(UserInfo::getId).collect(Collectors.toList());
    }

    @Override
    public List<UserSimpleDTO> getDbUserByMemberCode(List<String> memberCode) {
        LambdaQueryWrapper<UserInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(UserInfo::getUsername,UserInfo::getId,UserInfo::getCardNo);
        queryWrapper.in(UserInfo::getCardNo, memberCode);
        queryWrapper.eq(UserInfo::getState,1);
        queryWrapper.orderByDesc(UserInfo::getCreated);
        List<UserInfo> userInfo = userInfoMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(userInfo)) {
            return null;
        }
        List<UserSimpleDTO> userInfoDTO = BeanCopierUtils.convertList(userInfo, UserSimpleDTO.class);
        //将userInfoDTO按cardNo去重
        userInfoDTO = userInfoDTO.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(UserSimpleDTO::getCardNo))), ArrayList::new));
        return userInfoDTO;
    }

    @Override
    public List<UserSimpleDTO> getUserIdListByOpenids(List<String> openids, Long tenantId) {
        List<UserSimpleDTO> userInfoListByOpenids = userInfoMapper.getUserInfoListByOpenids(openids, tenantId);
        return BeanCopierUtils.convertList(userInfoListByOpenids, UserSimpleDTO.class);
    }



    @Override
    public void exportToken (HttpServletResponse response){
        CurrentUserDTO currentUserDTO = new CurrentUserDTO();
        currentUserDTO.setTenantId(1L);
        SecurityContext.setUser(currentUserDTO);
        List<UserSimpleDTO> list = new ArrayList<>();
        List<UserInfo> userInfos = userInfoMapper.selectList(new LambdaQueryWrapper<UserInfo>().like(UserInfo::getOpenid, "DZ"));
//        List<UserInfo> userInfos = userInfoMapper.selectList(new LambdaQueryWrapper<UserInfo>().last("limit 10000"));
        for (UserInfo userInfo : userInfos) {
            String uuid = UUID.randomUUID().toString();
            Map<String, Object> claims = new HashMap<>();
            claims.put("id",userInfo.getId());
            claims.put("type", ClientTypeConstant.APP);
            claims.put("tid", userInfo.getTenantId());
            String token = JwtTokenUtils.generatorToken(claims,uuid, 7*24);
            redisService.setString(CacheKeys.USER_SECRET + userInfo.getTenantId() +":"+ userInfo.getId(),uuid,CommonConstants.WEEK_SECONDS);
            UserSimpleDTO userSimple = new UserSimpleDTO();
            userSimple.setId(userInfo.getId());
            userSimple.setUnionid(userInfo.getUnionid());
            userSimple.setToken(token);
            list.add(userSimple);
        }
        ExcelUtils.excelExport(list, UserSimpleDTO.class, "tokens", response);
    }

}
