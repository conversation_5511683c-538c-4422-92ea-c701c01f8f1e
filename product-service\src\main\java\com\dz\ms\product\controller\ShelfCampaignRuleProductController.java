package com.dz.ms.product.controller;

import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.constants.NumConstants;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.ms.product.dto.ShelfCampaignRuleProductDTO;
import com.dz.ms.product.dto.req.ShelfCampaignRuleProductParamDTO;
import com.dz.ms.product.dto.res.ShelfCampaignRuleProductEnhanceDTO;
import com.dz.ms.product.dto.res.ShelfCampaignRuleProductResDTO;
import com.dz.ms.product.service.ShelfCampaignRuleProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Api(tags = "营销活动规则关联的货架商品")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class ShelfCampaignRuleProductController {

    @Resource
    private ShelfCampaignRuleProductService shelfCampaignRuleProductService;

    /**
     * 分页查询营销活动规则关联的货架商品
     * @param param 入参
     * @return result<PageInfo < ShelfCampaignRuleProductResDTO>>
     */
    @ApiOperation("分页查询营销活动规则关联的货架商品")
    @GetMapping(value = "/crm/shelf_campaign_rule_product/list")
    public Result<PageInfo<ShelfCampaignRuleProductResDTO>> getShelfCampaignRuleProductList(@ModelAttribute ShelfCampaignRuleProductParamDTO param) {
        Result<PageInfo<ShelfCampaignRuleProductResDTO>> result = new Result<>();
        PageInfo<ShelfCampaignRuleProductResDTO> page = shelfCampaignRuleProductService.getShelfCampaignRuleProductList(param);
        result.setData(page);
        return result;
    }
    
    @ApiOperation("根据营销活动id查询规则上架商品")
    @GetMapping(value = "/crm/shelf_campaign_rule_product/list_by_campaignId")
    public Result<List<ShelfCampaignRuleProductEnhanceDTO>> getRuleProductByCampaignIds(@RequestParam("campaignId") Long campaignId) {
        Result<List<ShelfCampaignRuleProductEnhanceDTO>> result = new Result<>();
        List<ShelfCampaignRuleProductEnhanceDTO> list = shelfCampaignRuleProductService.getProductEnhanceByManyIds(Collections.singletonList(campaignId), null, NumConstants.TWO);
        result.setData(list);
        return result;
    }

    /**
     * 根据ID查询营销活动规则关联的货架商品
     *
     * @param id
     * @return result<ShelfCampaignRuleProductDTO>
     */
    @ApiOperation("根据ID查询营销活动规则关联的货架商品")
    @GetMapping(value = "/shelf_campaign_rule_product/info")
    public Result<ShelfCampaignRuleProductDTO> getShelfCampaignRuleProductById(@RequestParam("id") Long id) {
        Result<ShelfCampaignRuleProductDTO> result = new Result<>();
        ShelfCampaignRuleProductDTO shelfCampaignRuleProduct = shelfCampaignRuleProductService.getShelfCampaignRuleProductById(id);
        result.setData(shelfCampaignRuleProduct);
        return result;
    }

    /**
     * 根据ID删除营销活动规则关联的货架商品
     *
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除营销活动规则关联的货架商品")
    @PostMapping(value = "/shelf_campaign_rule_product/delete")
    public Result<Boolean> deleteShelfCampaignRuleProductById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        shelfCampaignRuleProductService.deleteShelfCampaignRuleProductById(param);
        result.setData(true);
        return result;
    }

}
