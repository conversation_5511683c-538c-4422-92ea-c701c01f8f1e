package com.dz.ms.user.controller;

import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.user.MujiOrderFlowDTO;
import com.dz.common.core.dto.user.MujiOrderFlowParamDTO;
import com.dz.common.core.dto.user.MujiOrderInfoDTO;
import com.dz.common.core.dto.user.MujiTicketInfoDTO;
import com.dz.ms.user.service.MujiOrderFlowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Api(tags = "我的-消费记录")
@RestController
@RequiredArgsConstructor
public class MujiOrderFlowController {

    private final MujiOrderFlowService mujiOrderFlowService;

    @ApiOperation("我的消费记录列表")
    @GetMapping(value = "/app/muji_order/list")
    public Result<PageInfo<MujiOrderFlowDTO>> getMujiOrderFlowList(@ModelAttribute MujiOrderFlowParamDTO param) {
        PageInfo<MujiOrderFlowDTO> page = mujiOrderFlowService.getMujiOrderFlowList(param);
        Result<PageInfo<MujiOrderFlowDTO>> ret = new Result<>();
        ret.setData(page);
        return ret;
    }

    @ApiOperation("我的消费订单详情")
    @GetMapping(value = "/app/muji_order/info")
    public Result<MujiOrderInfoDTO> getMujiOrderInfo(@RequestParam("orderSn") String orderSn) {
        MujiOrderInfoDTO orderInfo = mujiOrderFlowService.getMujiOrderInfo(orderSn);
        Result<MujiOrderInfoDTO> ret = new Result<>();
        ret.setData(orderInfo);
        return ret;
    }

    @ApiOperation("我的消费订单小票详情")
    @GetMapping(value = "/app/muji_order/ticket_info")
    public Result<MujiTicketInfoDTO> getMujiOrderTicketInfo(@RequestParam("orderSn") String orderSn) {
        MujiTicketInfoDTO orderInfo = mujiOrderFlowService.getMujiOrderTicketInfo(orderSn);
        Result<MujiTicketInfoDTO> ret = new Result<>();
        ret.setData(orderInfo);
        return ret;
    }
}
