package com.dz.ms.order.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSONObject;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.dto.DownloadCenterDTO;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.MujiOrder;
import com.dz.common.core.dto.order.*;
import com.dz.common.core.dto.product.CpStaticDTO;
import com.dz.common.core.dto.product.CpStaticParamDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.basic.ReportDownloadFeignClient;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.ExcelUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.order.config.Globals;
import com.dz.ms.order.dto.CrmExchangeOrderListDTO;
import com.dz.ms.order.dto.ExchangeOrderDTO;
import com.dz.ms.order.dto.req.CrmExchangeOrderParamDTO;
import com.dz.ms.order.service.ExchangeOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

@Api(tags = "订单主表")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
@Slf4j
public class ExchangeOrderController {

    @Resource
    private ExchangeOrderService exchangeOrderService;
    @Resource
    private ReportDownloadFeignClient reportDownloadFeignClient;

    @PostMapping(value = {"/app/order/list",}, produces = {MediaType.APPLICATION_JSON_VALUE})
    @ApiOperation(value = "订单列表")
    public Result<PageInfo<OrderListAppDTO>> listApp(@RequestBody @Valid OrderListAppParamDTO param) {
        //业务处理
        PageInfo<OrderListAppDTO> dto = exchangeOrderService.listApp(param);
        //组装返回参数
        Result<PageInfo<OrderListAppDTO>> result = new Result<>();
        result.setData(dto);
        return result;
    }

    @PostMapping("/app/order/create")
    @ApiOperation(value = "订单创建")
    public Result<CreateOrderResultDTO> create(@RequestBody @Valid OrderCreateParamDTO paramDto) {
        if (paramDto.getSource() == null) {
            paramDto.setSource(0);
        }
        CreateOrderResultDTO dto = exchangeOrderService.create(paramDto);
        //组装返回参数
        Result<CreateOrderResultDTO> result = new Result<>();
        result.setData(dto);
        return result;
    }

    @PostMapping(value = "/app/order/preview")
    @ApiOperation(value = "订单预览")
    public Result<PreviewResultDTO> preview(@RequestBody OrderPreviewParamDTO paramDto) {
        PreviewResultDTO preview = exchangeOrderService.preview(paramDto);
        log.info("订单预览出参：" + JSONObject.toJSONString(preview));
        Result<PreviewResultDTO> result = new Result<>();
        result.setData(preview);
        return result;
    }

    @GetMapping("/app/order/info")
    @ApiOperation(value = "订单详情")
    public Result<OrderInfoAppDTO> infoApp(@RequestParam("orderCode") String orderCode) {
        //参数校验
        if (StringUtils.isBlank(orderCode)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST);
        }
        //业务处理
        OrderInfoAppDTO dto = exchangeOrderService.infoApp(orderCode);

        //组装返回参数
        Result<OrderInfoAppDTO> result = new Result<>();
        result.setData(dto);
        return result;
    }

    @PostMapping("/app/order/delete")
    @ApiOperation(value = "订单删除")
    public Result<Void> delete(@RequestBody @Valid OrderDeleteParamDTO dto) {
        exchangeOrderService.delete(dto);
        return new Result<>();
    }

    /**
     * 订单购买统计
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/order/purchase_static")
    public Result<List<PurchaseStaticDTO>> purchaseStatic(@RequestBody PurchaseStaticParamDTO param) {
        Result<List<PurchaseStaticDTO>> result = new Result<>();
        result.setData(exchangeOrderService.purchaseStatic(param));
        return result;
    }

    /**
     * 分页查询订单主表
     *
     * @param param
     * @return result<PageInfo < ExchangeOrderDTO>>
     */
    @ApiOperation("分页查询订单主表")
    @GetMapping(value = "/crm/exchange_order/list")
    public Result<PageInfo<CrmExchangeOrderListDTO>> getExchangeOrderList(@ModelAttribute CrmExchangeOrderParamDTO param) {
        Result<PageInfo<CrmExchangeOrderListDTO>> result = new Result<>();
        PageInfo<CrmExchangeOrderListDTO> page = exchangeOrderService.getExchangeOrderList(param);
        result.setData(page);
        return result;
    }

    /**
     * 订单发货
     *
     * @param param
     * @return result<Object>
     */
    @SysLog(value = "订单发货", type = LogType.OPERATELOG)
    @ApiOperation("订单发货")
    @PostMapping(value = "/crm/exchange_order/delivery")
    public Result<Integer> delivery(@Valid @RequestBody OrderDeliveryParamDTO param) {
        Result<Integer> result = new Result<>();
        result.setData(exchangeOrderService.delivery(param));
        return result;
    }

    @ApiOperation("下载发货模板")
    @GetMapping(value = "/crm/exchange_order/template")
    public void outputList(HttpServletResponse response) throws IOException {
        List<OrderDeliveryParamDTO> excelDTOS = new ArrayList<>();
        String fileName = URLEncoder.encode("发货导入模板", StandardCharsets.UTF_8);
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        response.setHeader("Access-Control-Expose-Headers", "Content-disposition");
        EasyExcel.write(response.getOutputStream(), OrderDeliveryParamDTO.class)
                .excelType(ExcelTypeEnum.XLSX)
                .sheet("发货导入模板")
                .doWrite(excelDTOS);
    }

    @PostMapping("/crm/exchange_order/import_delivery")
    @ApiOperation("导入订单发货")
    public Result<Boolean> importDelivery(@RequestParam(value = "file") MultipartFile file) {
        Result<Boolean> result = new Result<>();
        List<OrderDeliveryParamDTO> list = new ArrayList<>(ExcelUtils.excelImport(file, OrderDeliveryParamDTO.class));
        result.setData(exchangeOrderService.importDelivery(list));
        return result;
    }


    /**
     * 验证保存参数
     *
     * @param param
     */
    private void validationSaveParam(ExchangeOrderDTO param, boolean isAdd) {
        if (isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if (!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
    }

    /**
     * 根据ID删除订单主表
     *
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除订单主表")
    @PostMapping(value = "/exchange_order/delete")
    public Result<Boolean> deleteExchangeOrderById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        exchangeOrderService.deleteExchangeOrderById(param);
        result.setData(true);
        return result;
    }

    /**
     * 导出订单列表
     *
     * @return
     */
    @PostMapping(value = "/exchange_order/export_order_list")
    public Result<Void> exportOrderList(@RequestBody DownloadAddParamDTO exportParam) {
        CurrentUserDTO user = SecurityContext.getUser();
        Globals.downloadThreadPool.execute(() -> {
            try {
                SecurityContext.setUser(user);
                exchangeOrderService.exportOrderList(exportParam);
            } catch (Exception e) {
                log.error("导出错误", e);
                DownloadCenterDTO downloadCenterDTO = new DownloadCenterDTO();
                downloadCenterDTO.setId(exportParam.getDownloadCenterId());
                downloadCenterDTO.setErrorDesc(e.getMessage());
                downloadCenterDTO.setState(2);
                reportDownloadFeignClient.updateDownloadCenter(downloadCenterDTO);
            }
        });
        return new Result<>();
    }

    /**
     * 拉取优惠券信息修改订单兑换状态
     *
     * @param orderCode
     * @return
     */
    @PostMapping(value = "/exchange_order/change_order_status_by_order_code")
    public Result<Void> changeOrderStatusByOrderCode(@RequestParam(value = "orderCode") String orderCode) {
        exchangeOrderService.changeOrderStatusByOrderCode(orderCode);
        return new Result<>();
    }

    /**
     * 拉取优惠券信息修改订单兑换状态
     */
    @PostMapping(value = "/exchange_order/change_order_status")
    public Result<Void> changeOrderStatus() {
        exchangeOrderService.changeOrderStatus();
        return new Result<>();
    }

    /**
     * 拉取优惠券信息修改订单兑换状态
     */
    @PostMapping(value = {"/exchange_order/all_change_order_status", "/crm/exchange_order/all_change_order_status"})
    public Result<Void> changeAllOrderStatus() {
        exchangeOrderService.changeAllOrderStatus();
        return new Result<>();
    }

    /**
     * 统计优惠券兑换量
     */
    @PostMapping(value = "/exchange_order/cp_static")
    public PageInfo<CpStaticDTO> cpStatic(@RequestBody CpStaticParamDTO param) {
        return exchangeOrderService.cpStatic(param);
    }

    /**
     * 统计货架兑换人数
     *
     * @param shelfId
     * @return
     */
    @GetMapping(value = "/exchange_order/select_exchange_people")
    public Result<Integer> selectExchangePeople(@RequestParam("shelfId") Long shelfId) {
        Result<Integer> result = new Result<>();
        result.setData(exchangeOrderService.selectExchangePeople(shelfId));
        return result;
    }

    /**
     * 订单查询
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    @GetMapping(value = "/exchange_order/select_muji_order")
    public List<MujiOrder> selectMujiOrder(@RequestParam("beginTime") String beginTime,
                                           @RequestParam("endTime") String endTime) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return exchangeOrderService.selectMujiOrder(sdf.parse(beginTime), sdf.parse(endTime));
    }

    /**
     * 用户订单优惠券查询
     */
    @GetMapping(value = "/exchange_order/get_unused_stock_id_list")
    public Result<List<String>> getUnusedStockIdList() {
        Result<List<String>> result = new Result<>();
        result.setData(exchangeOrderService.getUnusedStockId());
        return result;
    }


    /**
     * 订单sftp查询
     *
     * @param memberCode
     * @return
     */
    @GetMapping(value = "/exchange_order/sftp/order/list")
    List<String> sftpOrderList(@RequestParam("memberCode") String memberCode,
                               @RequestParam(value = "startTime", required = false) String startTime,
                               @RequestParam(value = "endTime", required = false) String endTime,
                               @RequestParam(value = "deptId", required = false) String deptId,
                               @RequestParam(value = "depaId", required = false) String depaId,
                               @RequestParam(value = "lineId", required = false) String lineId,
                               @RequestParam(value = "classId", required = false) String classId,
                               @RequestParam(value = "janId", required = false) String janId) {
        return exchangeOrderService.sftpOrderList(memberCode, startTime, endTime, deptId, depaId, lineId, classId, janId);
    }

}
