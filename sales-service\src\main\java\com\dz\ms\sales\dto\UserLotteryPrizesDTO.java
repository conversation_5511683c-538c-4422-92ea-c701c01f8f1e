package com.dz.ms.sales.dto;

import com.dz.common.core.annotation.Columns;
import com.dz.common.core.enums.ColumnType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
public class UserLotteryPrizesDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;


    @ApiModelProperty(value = "绑定奖品id")
    private Long prizesId;
    @ApiModelProperty(value = "奖品名称")
    private String prizesName;
    @ApiModelProperty(value = "奖品类型 0空 1积分 2券")
    private Integer prizesType;
    @ApiModelProperty(value = "奖品等级编码 0周年特别奖 1一等奖 2二等奖 3三等奖 4四等奖 5鼓励奖")
    private Integer prizesLevelCode;
    @ApiModelProperty(value = "奖品等级")
    private String prizesLevel;
    @ApiModelProperty(value = "列表图片")
    private String imageUrl;
    @ApiModelProperty(value = "详情图片")
    private String detailImageUrl;
    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "抽奖描述信息")
    private String contentJson;
    @ApiModelProperty(value = "积分数量")
    private Integer pointsNum;
    @ApiModelProperty(value = "券code")
    private String couponCode;


}
