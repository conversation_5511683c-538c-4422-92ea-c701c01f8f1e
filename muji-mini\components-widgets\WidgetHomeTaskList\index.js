const app = getApp();
import { getTaskList } from "../../api/index.js";
Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true,
  },
  properties: {
    item: {
      type: null,
      value() {
        return {};
      },
    },
    // 游客模式
    visitor: {
      type: <PERSON><PERSON><PERSON>,
    },
  },
  pageLifetimes: {
    show() {
      this.getList();
    },
  },
  data: {
    taskList: [],
    showRuleDialog: false,
    currentRuleImg: "",
  },
  attached() {
    // 等待打开成功后  再获取任务列表
    let time = setTimeout(() => {
      clearTimeout(time);
      time = null;
      this.getList();
    }, 1000);
  },
  methods: {
    // 获取任务列表
    async getList() {
      const res = await getTaskList({
        type: 1, // type为1请求的是未完成数据
      });
      let data = res.data?.data;
      this.setData({
        taskList: data && data.length ? data.slice(0, 3) : [],
      });
    },
    // 跳转任务列表
    goTask: app.debounce(async function () {
      wx.$mp.track({
        event: "home_task_more",
      });
      if (app.ifRegister()) {
        app.subscribe("task").then(() => {
          wx.$mp.navigateTo({
            url: "/pages/interactiveTask/interactiveTask",
          });
        });
      }
    }, 1500),
    showRuleDialog(e) {
      const { img } = e.detail;
      this.setData({
        showRuleDialog: true,
        currentRuleImg: img,
      });
    },
    handleClose() {
      this.setData({
        showRuleDialog: false,
        // currentRuleImg: '',
      });
    },
    handleInvite() {},
  },
});
