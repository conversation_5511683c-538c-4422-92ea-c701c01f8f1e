package com.dz.ms.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.user.*;
import com.dz.common.core.fegin.product.ProductFeignClient;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.ms.user.entity.*;
import com.dz.ms.user.mapper.*;
import com.dz.ms.user.service.CrowdService;
import com.dz.ms.user.service.UserInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * 人群包
 * @author: yibo
 * @date:   2024/11/18 13:53
 */
@Service
@Slf4j
public class CrowdServiceImpl extends ServiceImpl<CrowdMapper,Crowd> implements CrowdService {

	@Resource
    private CrowdMapper crowdMapper;
    @Resource
    private CrowdConditionMapper crowdConditionMapper;
    @Resource
    private CrowdConditionRowMapper crowdConditionRowMapper;
    @Resource
    private CrowdUserMapper crowdUserMapper;
    @Resource
    private ProductFeignClient productFeignClient;
    @Resource
    private UserInfoMapper userInfoMapper;

    @Resource
    private UserInfoService userInfoService;


	/**
     * 分页查询人群包
     * @param param
     * @return PageInfo<CrowdDTO>
     */
    @Override
    public PageInfo<CrowdDTO> getCrowdList(CrowdDTO param) {
        QueryWrapper<Crowd> crowdQueryWrapper = new QueryWrapper<>();
        if (param.getCrowdName() != null && !"".equals(param.getCrowdName())){
            crowdQueryWrapper.like("crowd_name",param.getCrowdName());
        }
        crowdQueryWrapper.orderByDesc("create_time");
        IPage<Crowd> page = crowdMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()),crowdQueryWrapper);
        return new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),BeanCopierUtils.convertList(page.getRecords(), CrowdDTO.class));
    }

    /**
     * 根据ID查询人群包
     * @param id
     * @return CrowdDTO
     */
    @Override
    public CrowdDTO getCrowdById(Long id) {
        Crowd crowd = crowdMapper.selectById(id);
        CrowdDTO crowdDTO = new CrowdDTO();
        BeanCopierUtils.copyProperties(crowd,crowdDTO);
        if (crowd.getCrowdType() == 2){
            CrowdImportResultDTO crowdImportResultDTO = new CrowdImportResultDTO();
            crowdImportResultDTO.setFileName(crowd.getCrowdFile());
            QueryWrapper<CrowdUser> crowdUserQueryWrapper = new QueryWrapper<>();
            crowdUserQueryWrapper.eq("crowd_id",id);
            List<CrowdUser> crowdUsers = crowdUserMapper.selectList(crowdUserQueryWrapper);
            if (!CollectionUtils.isEmpty(crowdUsers)){
                List<String> memberCodeList = new ArrayList<>();
                for (CrowdUser crowdUser : crowdUsers){
                    memberCodeList.add(crowdUser.getMemberCode());
                }
                crowdImportResultDTO.setMemberCodeList(memberCodeList);
            }
            crowdDTO.setCrowdImportResultDTO(crowdImportResultDTO);
        }
        List<CrowdConditionDTO> crowdConditionDTOS = new ArrayList<>();
        QueryWrapper<CrowdCondition> crowdConditionQueryWrapper = new QueryWrapper<>();
        crowdConditionQueryWrapper.eq("crowd_id",id);
        List<CrowdCondition> crowdConditions = crowdConditionMapper.selectList(crowdConditionQueryWrapper);
        if (!CollectionUtils.isEmpty(crowdConditions)){
            for (CrowdCondition crowdCondition : crowdConditions){
                CrowdConditionDTO crowdConditionDTO = new CrowdConditionDTO();
                BeanCopierUtils.copyProperties(crowdCondition,crowdConditionDTO);
                QueryWrapper<CrowdConditionRow> crowdConditionRowQueryWrapper = new QueryWrapper<>();
                crowdConditionRowQueryWrapper.eq("condition_id",crowdCondition.getId());
                List<CrowdConditionRow> crowdConditionRows = crowdConditionRowMapper.selectList(crowdConditionRowQueryWrapper);
                if (!CollectionUtils.isEmpty(crowdConditionRows)){
                    List<CrowdConditionRowDTO> crowdConditionRowDTOS = BeanCopierUtils.convertList(crowdConditionRows, CrowdConditionRowDTO.class);
                    crowdConditionRowDTOS.sort(Comparator.comparing(CrowdConditionRowDTO::getSort));
                    for (CrowdConditionRowDTO crowdConditionRowDTO : crowdConditionRowDTOS){
                        List<Integer> integers = new ArrayList<>();
                        integers.add(crowdConditionRowDTO.getConditionType());
                        integers.add(crowdConditionRowDTO.getConditionValue());
                        crowdConditionRowDTO.setConditions(integers);
                    }
                    crowdConditionDTO.setCrowdConditionRowList(crowdConditionRowDTOS);
                    crowdConditionDTOS.add(crowdConditionDTO);
                }
            }
            crowdConditionDTOS.sort(Comparator.comparing(CrowdConditionDTO::getSort));
            crowdDTO.setCrowdConditionList(crowdConditionDTOS);
        }
        return crowdDTO;
    }

    /**
     * 保存人群包
     * @param param
     * @return Long
     */
    @Override
    @Transactional
    public Long saveCrowd(CrowdDTO param) {
        CurrentUserDTO currentUser = SecurityContext.getUser();
        Long uid = currentUser.getUid();
        Long tenantId = currentUser.getTenantId();
        Date date = new Date();
        Crowd crowd = new Crowd();
        BeanCopierUtils.copyProperties(param,crowd);
        crowd.setCreator(uid);
        crowd.setTenantId(tenantId);
        crowd.setCreateTime(date);
        crowd.setUpdateTime(date);
        if (param.getCrowdType() == 2){
            crowd.setCrowdFile(param.getCrowdImportResultDTO().getFileName());
        }
        crowdMapper.insert(crowd);
        if (param.getCrowdType() == 0){
            for (CrowdConditionDTO crowdConditionDTO : param.getCrowdConditionList()){
                CrowdCondition crowdCondition = new CrowdCondition();
                BeanCopierUtils.copyProperties(crowdConditionDTO,crowdCondition);
                crowdCondition.setCrowdId(crowd.getId());
                crowdCondition.setCreator(uid);
                crowdCondition.setTenantId(tenantId);
                crowdCondition.setCreateTime(date);
                crowdCondition.setUpdateTime(date);
                crowdConditionMapper.insert(crowdCondition);
                if (!CollectionUtils.isEmpty(crowdConditionDTO.getCrowdConditionRowList())){
                    for (CrowdConditionRowDTO crowdConditionRowDTO : crowdConditionDTO.getCrowdConditionRowList()){
                        CrowdConditionRow crowdConditionRow = new CrowdConditionRow();
                        BeanCopierUtils.copyProperties(crowdConditionRowDTO,crowdConditionRow);
                        crowdConditionRow.setConditionId(crowdCondition.getId());
                        crowdConditionRow.setCrowdId(crowd.getId());
                        crowdConditionRow.setCreator(uid);
                        crowdConditionRow.setTenantId(tenantId);
                        crowdConditionRow.setConditionType(crowdConditionRowDTO.getConditions().get(0));
                        crowdConditionRow.setConditionValue(crowdConditionRowDTO.getConditions().get(1));
                        crowdConditionRow.setCreateTime(date);
                        crowdConditionRow.setUpdateTime(date);
                        crowdConditionRowMapper.insert(crowdConditionRow);
                    }
                }
            }
        }
        if (param.getCrowdType() == 2){
            for (String user : param.getCrowdImportResultDTO().getMemberCodeList()){
                CrowdUser crowdUser = new CrowdUser();
                crowdUser.setCrowdId(crowd.getId());
                crowdUser.setMemberCode(user);
                crowdUser.setCreator(uid);
                crowdUser.setTenantId(tenantId);
                crowdUser.setCreateTime(date);
                crowdUser.setUpdateTime(date);
                crowdUserMapper.insert(crowdUser);
            }
        }
        return crowd.getId();
    }

    /**
     * 根据ID删除人群包
     * @param param
     */
    @Override
    @Transactional
    public void deleteCrowdById(IdCodeDTO param) {
        crowdMapper.deleteById(param.getId());
        crowdConditionMapper.delete(new QueryWrapper<CrowdCondition>().eq("crowd_id",param.getId()));
        crowdUserMapper.delete(new QueryWrapper<CrowdUser>().eq("crowd_id",param.getId()));
        crowdConditionRowMapper.delete(new QueryWrapper<CrowdConditionRow>().eq("crowd_id",param.getId()));
        productFeignClient.updGroupIdIntoNull(param);
    }

    @Override
    @Transactional
    public void updateCrowd(CrowdDTO param) {
        CurrentUserDTO currentUser = SecurityContext.getUser();
        Long uid = currentUser.getUid();
        Long tenantId = currentUser.getTenantId();
        Date date = new Date();
        Crowd crowd = new Crowd();
        BeanCopierUtils.copyProperties(param,crowd);
        crowd.setModifier(uid);
        crowd.setUpdateTime(date);
        if (param.getCrowdType() == 2){
            crowd.setCrowdFile(param.getCrowdImportResultDTO().getFileName());
        }
        crowdMapper.updateById(crowd);
        if (!CollectionUtils.isEmpty(param.getCrowdConditionList())){
            crowdConditionMapper.delete(new QueryWrapper<CrowdCondition>().eq("crowd_id",crowd.getId()));
            for (CrowdConditionDTO crowdConditionDTO : param.getCrowdConditionList()){
                CrowdCondition crowdCondition = new CrowdCondition();
                BeanCopierUtils.copyProperties(crowdConditionDTO,crowdCondition);
                crowdCondition.setCrowdId(crowd.getId());
                crowdCondition.setCreator(uid);
                crowdCondition.setTenantId(tenantId);
                crowdCondition.setModifier(uid);
                crowdCondition.setCreateTime(date);
                crowdCondition.setUpdateTime(date);
                crowdConditionMapper.insert(crowdCondition);
                if (!CollectionUtils.isEmpty(crowdConditionDTO.getCrowdConditionRowList())){
                    crowdConditionRowMapper.delete(new QueryWrapper<CrowdConditionRow>().eq("condition_id",crowdConditionDTO.getId()));
                    for (CrowdConditionRowDTO crowdConditionRowDTO : crowdConditionDTO.getCrowdConditionRowList()){
                        CrowdConditionRow crowdConditionRow = new CrowdConditionRow();
                        BeanCopierUtils.copyProperties(crowdConditionRowDTO,crowdConditionRow);
                        crowdConditionRow.setConditionId(crowdCondition.getId());
                        crowdConditionRow.setCrowdId(crowd.getId());
                        crowdConditionRow.setCreator(uid);
                        crowdConditionRow.setTenantId(tenantId);
                        crowdConditionRow.setModifier(uid);
                        crowdConditionRow.setConditionType(crowdConditionRowDTO.getConditions().get(0));
                        crowdConditionRow.setConditionValue(crowdConditionRowDTO.getConditions().get(1));
                        crowdConditionRow.setCreateTime(date);
                        crowdConditionRow.setUpdateTime(date);
                        crowdConditionRowMapper.insert(crowdConditionRow);
                    }
                }
            }
        }
        if (param.getCrowdImportResultDTO() != null) {
            if (!CollectionUtils.isEmpty(param.getCrowdImportResultDTO().getMemberCodeList())) {
                crowdUserMapper.delete(new QueryWrapper<CrowdUser>().eq("crowd_id", crowd.getId()));
                for (String user : param.getCrowdImportResultDTO().getMemberCodeList()) {
                    CrowdUser crowdUser = new CrowdUser();
                    crowdUser.setCrowdId(crowd.getId());
                    crowdUser.setMemberCode(user);
                    crowdUser.setCreator(uid);
                    crowdUser.setTenantId(tenantId);
                    crowdUser.setCreateTime(date);
                    crowdUser.setUpdateTime(date);
                    crowdUserMapper.insert(crowdUser);
                }
            }
        }
    }

    @Override
    public void updateStatus(CrowdDTO param) {
        Crowd crowd = new Crowd();
        crowd.setId(param.getId());
        crowd.setCrowdStatus(param.getCrowdStatus());
        crowd.setModifier(SecurityContext.getUser().getUid());
        crowd.setUpdateTime(new Date());
        crowdMapper.updateById(crowd);
    }

    @Override
    public List<CrowdDTO> getList(CrowdDTO param) {
        QueryWrapper<Crowd> crowdQueryWrapper = new QueryWrapper<>();
        if (param.getCrowdName() != null && !"".equals(param.getCrowdName())){
            crowdQueryWrapper.like("crowd_name",param.getCrowdName());
        }
        //AND (time_type = ? OR (start_time >= ? AND end_time <= ?))
        crowdQueryWrapper.and(wrapper -> wrapper.nested(nestedWrapper -> {
            nestedWrapper.eq("time_type", 1);
            nestedWrapper.or(orWrapper -> {
                orWrapper.eq("time_type", 0);
                orWrapper.le("start_time", new Date());
                orWrapper.ge("end_time", new Date());
            });
        }));
        crowdQueryWrapper.eq("crowd_status", 0);
        crowdQueryWrapper.orderByDesc("create_time");
        List<Crowd> list = crowdMapper.selectList(crowdQueryWrapper);
        return BeanCopierUtils.convertList(list, CrowdDTO.class);
    }

    @Override
    public List<Long> userHaveCrowd() {
        CurrentUserDTO currentUser = SecurityContext.getUser();
        Long uid = currentUser.getUid();
        log.info("用户id:{}",uid);
        MemberInfoDTO userMemberInfo = userInfoService.getUserMemberInfo(uid);
        //UserInfo userInfo = userInfoMapper.selectById(uid);
        List<Long> longs = new ArrayList<>();
        if (userMemberInfo != null){
            //查询有效人群包
            QueryWrapper<Crowd> crowdQueryWrapper = new QueryWrapper<>();
            //AND (time_type = ? OR (start_time >= ? AND end_time <= ?))
            crowdQueryWrapper.and(wrapper -> wrapper.nested(nestedWrapper -> {
                nestedWrapper.eq("time_type", 1);
                nestedWrapper.or(orWrapper -> {
                    orWrapper.eq("time_type", 0);
                    orWrapper.le("start_time", new Date());
                    orWrapper.ge("end_time", new Date());
                });
            }));
            crowdQueryWrapper.eq("crowd_status", 0);
            crowdQueryWrapper.orderByDesc("create_time");
            List<Crowd> list = crowdMapper.selectList(crowdQueryWrapper);
            //查询用户已加入人群包
            if (!CollectionUtils.isEmpty(list)){
                if (userMemberInfo.getCardLevel() != null){
                    int cardLevel = Integer.parseInt(userMemberInfo.getCardLevel());
                    for (Crowd crowd : list){
                        //是否已符合
                        //int tag = 1;
                        int andTag = 1;
                        int orTag = 1;
                        //条件规则判断
                        if (crowd.getCrowdType() == 0){
                            QueryWrapper<CrowdCondition> crowdConditionQueryWrapper = new QueryWrapper<>();
                            crowdConditionQueryWrapper.eq("crowd_id",crowd.getId());
                            crowdConditionQueryWrapper.orderByAsc("sort");
                            List<CrowdCondition> conditions = crowdConditionMapper.selectList(crowdConditionQueryWrapper);
                            // 假设条件满足
                            boolean conditionMet = true;
                            for (CrowdCondition condition : conditions) {
                                // 根据条件ID查询子级条件值
                                List<CrowdConditionRow> crowdConditionRows = crowdConditionRowMapper.selectList(new QueryWrapper<CrowdConditionRow>()
                                        .eq("condition_id", condition.getId())
                                        .orderByAsc("sort"));
                                // 首位处理
                                if (condition.getConditionJudge() == 0) { // 默认情况（简单相等判断）
                                    boolean allRowsForConditionMet = false;
                                    for (CrowdConditionRow row : crowdConditionRows) {
                                        if (row.getConditionId().equals(condition.getId())) {
                                            if (row.getConditionJudge() == 0 && row.getConditionValue().equals(cardLevel)) {
                                                allRowsForConditionMet = true;
                                                //tag = 2;
                                            }
                                            if (row.getConditionJudge() == 1 && anyRowMatches(row, cardLevel, crowdConditionRows)) {
                                                allRowsForConditionMet = true;
                                                //tag = 2;
                                            }
                                            if (row.getConditionJudge() == 2 && !allRowsMatch(row, cardLevel, crowdConditionRows)) {
                                                allRowsForConditionMet = false;
                                                //tag = 1;
                                                andTag = 2;
                                                break; // 如果有一个不满足，就认为条件不满足
                                            }
                                        /*if (!(row.getConditionJudge() == 0 && row.getConditionValue().equals(cardLevel))) {
                                            allRowsForConditionMet = false;
                                            break; // 如果有一个不满足，就认为条件不满足
                                        }*/
                                        }
                                    }
                                    conditionMet = allRowsForConditionMet; // 如果所有相关行都满足条件，则认为条件满足
                                    if (conditionMet){
                                        break;
                                    }
                                }
                                if (condition.getConditionJudge() == 1) { // OR逻辑
                                    //boolean anyRowMet = false;
                                    for (CrowdConditionRow row : crowdConditionRows) {
                                        if (row.getConditionId().equals(condition.getId())) {
                                            if ((row.getConditionJudge() == 0 && row.getConditionValue().equals(cardLevel)) || // 首位判断
                                                    (row.getConditionJudge() == 1 && anyRowMatches(row, cardLevel, crowdConditionRows)) || // OR逻辑
                                                    (row.getConditionJudge() == 2 && allRowsMatch(row, cardLevel, crowdConditionRows))) { // AND逻辑
                                                //anyRowMet = true;
                                                //tag = 2;
                                                orTag = 2;
                                                break;
                                            }
                                        }
                                    }
                                    if (orTag == 2){
                                        conditionMet = true;
                                        break;
                                    }
                                }
                                if (condition.getConditionJudge() == 2) { // AND逻辑

                                    boolean allRowsMet = true;
                                    for (CrowdConditionRow row : crowdConditionRows) {
                                        if (row.getConditionId().equals(condition.getId())) {
                                            if (!(row.getConditionJudge() == 0 && row.getConditionValue().equals(cardLevel)) && // 首位判断
                                                    !(row.getConditionJudge() == 1 && anyRowMatches(row, cardLevel, crowdConditionRows)) && // OR逻辑
                                                    !(row.getConditionJudge() == 2 && allRowsMatch(row, cardLevel, crowdConditionRows))) {
                                                allRowsMet = false;
                                                //tag = 1;
                                                andTag = 2;
                                                //break; // 对于AND逻辑，必须满足所有子条件
                                            }
                                        }
                                    }
                                    if (andTag == 2){
                                        allRowsMet = false;
                                    }
                                    conditionMet = allRowsMet;
                                }

                            }
                            // 如果父级条件满足，则添加其crowdId到结果集中
                            if (conditionMet) {
                                longs.add(crowd.getId());
                            }
                        }
                        //导入人群包判断
                        if (crowd.getCrowdType() == 2){
                            QueryWrapper<CrowdUser> crowdUserQueryWrapper = new QueryWrapper<>();
                            crowdUserQueryWrapper.eq("crowd_id",crowd.getId());
                            crowdUserQueryWrapper.eq("member_code",userMemberInfo.getCardNo());
                            List<CrowdUser> crowdUserList = crowdUserMapper.selectList(crowdUserQueryWrapper);
                            if (!CollectionUtils.isEmpty(crowdUserList)){
                                longs.add(crowd.getId());
                            }
                        }
                    }
                }

            }
        }
        return longs;
    }

    @Override
    public void updateCrowdCrm(CrowdAllDTO crowdAllDTO) {
        CrowdDTO param = crowdAllDTO.getCrowdDTO();
        CurrentUserDTO currentUser = SecurityContext.getUser();
        Long uid = currentUser.getUid();
        Long tenantId = currentUser.getTenantId();
        Date date = new Date();
        Crowd crowd = new Crowd();
        BeanCopierUtils.copyProperties(param,crowd);
        crowd.setModifier(uid);
        crowd.setUpdateTime(date);
        if (param.getCrowdType() == 2){
            crowd.setCrowdFile(param.getCrowdImportResultDTO().getFileName());
        }
        crowdMapper.updateById(crowd);
        if (!CollectionUtils.isEmpty(param.getCrowdConditionList())){
            crowdConditionMapper.delete(new QueryWrapper<CrowdCondition>().eq("crowd_id",crowd.getId()));
            for (CrowdConditionDTO crowdConditionDTO : param.getCrowdConditionList()){
                CrowdCondition crowdCondition = new CrowdCondition();
                BeanCopierUtils.copyProperties(crowdConditionDTO,crowdCondition);
                crowdCondition.setCrowdId(crowd.getId());
                crowdCondition.setCreator(uid);
                crowdCondition.setTenantId(tenantId);
                crowdCondition.setModifier(uid);
                crowdCondition.setCreateTime(date);
                crowdCondition.setUpdateTime(date);
                crowdConditionMapper.insert(crowdCondition);
                if (!CollectionUtils.isEmpty(crowdConditionDTO.getCrowdConditionRowList())){
                    crowdConditionRowMapper.delete(new QueryWrapper<CrowdConditionRow>().eq("condition_id",crowdConditionDTO.getId()));
                    for (CrowdConditionRowDTO crowdConditionRowDTO : crowdConditionDTO.getCrowdConditionRowList()){
                        CrowdConditionRow crowdConditionRow = new CrowdConditionRow();
                        BeanCopierUtils.copyProperties(crowdConditionRowDTO,crowdConditionRow);
                        crowdConditionRow.setConditionId(crowdCondition.getId());
                        crowdConditionRow.setCrowdId(crowd.getId());
                        crowdConditionRow.setCreator(uid);
                        crowdConditionRow.setTenantId(tenantId);
                        crowdConditionRow.setModifier(uid);
                        crowdConditionRow.setConditionType(crowdConditionRowDTO.getConditions().get(0));
                        crowdConditionRow.setConditionValue(crowdConditionRowDTO.getConditions().get(1));
                        crowdConditionRow.setCreateTime(date);
                        crowdConditionRow.setUpdateTime(date);
                        crowdConditionRowMapper.insert(crowdConditionRow);
                    }
                }
            }
        }
        if (param.getCrowdImportResultDTO() != null) {
            if (!CollectionUtils.isEmpty(param.getCrowdImportResultDTO().getMemberCodeList())) {
                crowdUserMapper.delete(new QueryWrapper<CrowdUser>().eq("crowd_id", crowd.getId()));
                for (String user : param.getCrowdImportResultDTO().getMemberCodeList()) {
                    CrowdUser crowdUser = new CrowdUser();
                    crowdUser.setCrowdId(crowd.getId());
                    crowdUser.setMemberCode(user);
                    crowdUser.setCreator(uid);
                    crowdUser.setTenantId(tenantId);
                    crowdUser.setCreateTime(date);
                    crowdUser.setUpdateTime(date);
                    crowdUserMapper.insert(crowdUser);
                }
            }
        }
    }

    @Override
    public Long saveCrowdCrm(CrowdAllDTO crowdAllDTO) {
        CrowdDTO param = crowdAllDTO.getCrowdDTO();
        CurrentUserDTO currentUser = SecurityContext.getUser();
        Long uid = currentUser.getUid();
        Long tenantId = currentUser.getTenantId();
        Date date = new Date();
        Crowd crowd = new Crowd();
        BeanCopierUtils.copyProperties(param,crowd);
        crowd.setCreator(uid);
        crowd.setTenantId(tenantId);
        crowd.setCreateTime(date);
        crowd.setUpdateTime(date);
        if (param.getCrowdType() == 2){
            crowd.setCrowdFile(param.getCrowdImportResultDTO().getFileName());
        }
        crowdMapper.insert(crowd);
        if (!CollectionUtils.isEmpty(param.getCrowdConditionList())){
            for (CrowdConditionDTO crowdConditionDTO : param.getCrowdConditionList()){
                CrowdCondition crowdCondition = new CrowdCondition();
                BeanCopierUtils.copyProperties(crowdConditionDTO,crowdCondition);
                crowdCondition.setCrowdId(crowd.getId());
                crowdCondition.setCreator(uid);
                crowdCondition.setTenantId(tenantId);
                crowdCondition.setCreateTime(date);
                crowdCondition.setUpdateTime(date);
                crowdConditionMapper.insert(crowdCondition);
                if (param.getCrowdType() == 0){
                    for (CrowdConditionRowDTO crowdConditionRowDTO : crowdConditionDTO.getCrowdConditionRowList()){
                        CrowdConditionRow crowdConditionRow = new CrowdConditionRow();
                        BeanCopierUtils.copyProperties(crowdConditionRowDTO,crowdConditionRow);
                        crowdConditionRow.setConditionId(crowdCondition.getId());
                        crowdConditionRow.setCrowdId(crowd.getId());
                        crowdConditionRow.setCreator(uid);
                        crowdConditionRow.setTenantId(tenantId);
                        crowdConditionRow.setConditionType(crowdConditionRowDTO.getConditions().get(0));
                        crowdConditionRow.setConditionValue(crowdConditionRowDTO.getConditions().get(1));
                        crowdConditionRow.setCreateTime(date);
                        crowdConditionRow.setUpdateTime(date);
                        crowdConditionRowMapper.insert(crowdConditionRow);
                    }
                }
            }
        }
        if (param.getCrowdType() == 2){
            for (String user : param.getCrowdImportResultDTO().getMemberCodeList()){
                CrowdUser crowdUser = new CrowdUser();
                crowdUser.setCrowdId(crowd.getId());
                crowdUser.setMemberCode(user);
                crowdUser.setCreator(uid);
                crowdUser.setTenantId(tenantId);
                crowdUser.setCreateTime(date);
                crowdUser.setUpdateTime(date);
                crowdUserMapper.insert(crowdUser);
            }
        }
        return crowd.getId();
    }

    @Override
    public boolean crowdIsHave(Long id) {
        QueryWrapper<Crowd> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id",id);
        return crowdMapper.selectCount(queryWrapper) > 0;
    }


    //检查是否存在至少一个匹配的子条件（用于OR逻辑）
    private boolean anyRowMatches(CrowdConditionRow row, Integer cardLevel, List<CrowdConditionRow> rows) {
        return rows.stream()
                .anyMatch(r -> r.getConditionId().equals(row.getConditionId()) &&
                        (r.getConditionJudge() == 1 && r.getConditionValue().equals(cardLevel)));
    }

    //检查是否所有子条件都匹配（用于AND逻辑）
    private boolean allRowsMatch(CrowdConditionRow row, Integer cardLevel, List<CrowdConditionRow> rows) {
        return rows.stream()
                .filter(r -> r.getConditionId().equals(row.getConditionId()) &&
                        (r.getConditionJudge() == 2 && r.getConditionValue().equals(cardLevel)))
                .count() == rows.stream()
                .filter(r -> r.getConditionId().equals(row.getConditionId()))
                .count();
    }

}