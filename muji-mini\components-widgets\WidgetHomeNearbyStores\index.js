const app = getApp();
import {
  getStoreList
} from '../../api/index'
Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    item: {
      type: null,
      value() {
        return {}
      },
    }
  },
  data: {
    isAuth: false,
    storeData: {}, // 门店数据
    userLatitude: app.globalData.userLatitude, // 用户经纬度
    userLongitude: app.globalData.userLongitude, // 用户经纬度
  },
  attached() {
    this.getStoreData()
  },
  pageLifetimes: {
    show() {
      wx.getSetting({
        success: (res) => {
          const isAuthorized = res.authSetting['scope.userLocation'];
          if (isAuthorized === true) {
            wx.getLocation({
              type: 'gcj02', // 返回经纬度（gcj02坐标系）
              success: (v) => {
                app.globalData.userLatitude = v.latitude
                app.globalData.userLongitude = v.longitude
                this.getStoreData()
              },
              fail: (error) => {
                app.globalData.userLatitude = 0
                app.globalData.userLongitude = 0
              }
            });
          }
        }
      })
    }
  },
  methods: {
    // 获取门店数据
    getStoreData() {
      console.log('app.globalData.userLatitude', app.globalData.userLatitude);
      this.setData({
        userLatitude: app.globalData.userLatitude, // 用户经纬度
        userLongitude: app.globalData.userLongitude, // 用户经纬度
      })
      if (app.globalData.userLatitude) {
        // 获取数据
        getStoreList({
          pageSize: 10,
          pageNum: 1,
          latitude: app.globalData.userLatitude, // 用户经纬度
          longitude: app.globalData.userLongitude, // 用户经纬度
        }).then((res) => {

          this.setData({
            storeData: res.data.list[0] || {}
          })
        })
      }

    },
    // 跳转附近门店
    goStore: app.debounce(async function () {
      wx.$mp.track({
        event: 'home_nearByStore_all'
      })
      wx.$mp.navigateTo({
        url: '/pages/nearbyOutlets/nearbyOutlets',
      })
    }),
    // 跳转导购服务
    goGuide: app.debounce(async function (e) {
      const {
        img
      } = e.currentTarget.dataset;
      console.log(e,'eeeeee');
      wx.$mp.track({
        event: 'home_nearByStore_img'
      })
      // 没有配置导购不需要跳转
      if (!img) return;
      wx.$mp.navigateTo({
        url: '/pages/guide/guide?id' + this.data.storeData.id,
      })
    }),
    // 手动开启授权
    goAuth: app.debounce(async function () {
      wx.$mp.track({
        event: 'home_nearByStore_auth'
      })
      app.manualAuthLocation().then(data => {
        console.log('手动授权  是否获取到data', data)
        this.getStoreData()
      })
    }),
    // 跳转导航
    openLocation: app.debounce(async function () {
      wx.$mp.track({
        event: 'home_nearByStore_nav'
      })
      let store = this.data.storeData
      wx.openLocation({
        longitude: Number(store.longitude),
        latitude: Number(store.latitude),
        scale: 18,
        name: store.storeName,
        address: store.storeAddress,
        fail: (e) => {
          console.log('openLocation fail', e)
        }
      })
    }),
  }
})
