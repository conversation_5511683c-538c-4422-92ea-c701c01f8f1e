package com.dz.ms.adaptor.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.IdNumberDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.adaptor.dto.ScheduledTaskConfigDTO;
import com.dz.ms.adaptor.service.ScheduledTaskConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;

@Api(tags="定时任务配置表")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class ScheduledTaskConfigController  {

    @Resource
    private ScheduledTaskConfigService scheduledTaskConfigService;

    /**
     * 分页查询定时任务配置表
     * @param param
     * @return result<PageInfo<ScheduledTaskConfigDTO>>
     */
    @ApiOperation("分页查询定时任务配置表")
	@GetMapping(value = "/crm/scheduled_task_config/list")
    public Result<PageInfo<ScheduledTaskConfigDTO>> getScheduledTaskConfigList(@ModelAttribute ScheduledTaskConfigDTO param) {
        Result<PageInfo<ScheduledTaskConfigDTO>> result = new Result<>();
		PageInfo<ScheduledTaskConfigDTO> page = scheduledTaskConfigService.getScheduledTaskConfigList(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询定时任务配置表
     * @param id
     * @return result<ScheduledTaskConfigDTO>
     */
    @ApiOperation("根据ID查询定时任务配置表")
	@GetMapping(value = "/crm/scheduled_task_config/info")
    public Result<ScheduledTaskConfigDTO> getScheduledTaskConfigById(@RequestParam("id") Long id) {
        Result<ScheduledTaskConfigDTO> result = new Result<>();
        ScheduledTaskConfigDTO scheduledTaskConfig = scheduledTaskConfigService.getScheduledTaskConfigById(id,true);
        result.setData(scheduledTaskConfig);
        return result;
    }

    /**
     * 新增定时任务配置表
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增定时任务配置表",type = LogType.OPERATELOG)
    @ApiOperation("新增定时任务配置表")
	@PostMapping(value = "/crm/scheduled_task_config/add")
    public Result<Long> addScheduledTaskConfig(@RequestBody ScheduledTaskConfigDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,true);
        Long id = scheduledTaskConfigService.saveScheduledTaskConfig(param);
        result.setData(id);
        return result;
    }

    /**
    * 更新定时任务配置表
    * @param param
    * @return result<Object>
    */
    @SysLog(value = "更新定时任务配置表",type = LogType.OPERATELOG)
    @ApiOperation("更新定时任务配置表")
    @PostMapping(value = "/crm/scheduled_task_config/update")
    public Result<Long> updateScheduledTaskConfig(@RequestBody ScheduledTaskConfigDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,false);
        scheduledTaskConfigService.saveScheduledTaskConfig(param);
        result.setData(param.getId());
        return result;
    }

    /**
    * 验证保存参数
    * @param param
    */
    private void validationSaveParam(ScheduledTaskConfigDTO param,boolean isAdd) {
        if(isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if(!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
    }

	/**
     * 根据ID删除定时任务配置表
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除定时任务配置表")
	@PostMapping(value = "/crm/scheduled_task_config/delete")
    public Result<Boolean> deleteScheduledTaskConfigById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        scheduledTaskConfigService.deleteScheduledTaskConfigById(param);
        result.setData(true);
        return result;
    }

    /**
     * 根据ID修改启停状态
     * @param param ID NUMBER 通用DTO
     * @return result<Boolean>
     */
    @SysLog(value = "根据ID修改启停状态", type = LogType.OPERATELOG)
    @ApiOperation("根据ID修改启停状态")
    @PostMapping(value = "/crm/scheduled_task_config/update_state")
    public Result<Boolean> updateStateById(@RequestBody IdNumberDTO param) {
        Result<Boolean> result = new Result<>();
        scheduledTaskConfigService.updateStateById(param);
        result.setData(true);
        return result;
    }
}
