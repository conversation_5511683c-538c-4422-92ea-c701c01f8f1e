// 获取商品列表
exports.getProductList = data => wx.$request({
  url: '/app/product/shelf_product/list_by_param',
  data,
  method: 'post'
})

// 获取货架标签
exports.getShelfTagList = data => wx.$request({
  url: '/app/product/shelf_tag/tag_group_list',
  data,
  method: 'get'
})

// 获取货架推广
exports.getBannerList = data => wx.$request({
  url: '/app/product/shelf_promotion/info_by_shelf_id',
  data,
  method: 'get'
})

// 获取商品详情
exports.getProductDetail = (data) => wx.$request({
  url: '/app/product/shelf_product/shelf_product_id',
  data,
  method: 'get'
})

// 根据当前用户获取货架id
exports.getShelfId = () => wx.$request({
  url: '/app/product/shelf/priority_shelf',
  method: 'get'
})

// 获取推广位下架状态
exports.getPromotionProductState = (data) => wx.$request({
  url: '/app/product/shelf_product/shelf_product_intro_id',
  method: 'get',
  data
})
