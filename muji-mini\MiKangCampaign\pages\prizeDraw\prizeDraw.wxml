<!--signUp/pages/prizeDraw/prizeDraw.wxml 抽奖页面-->
<my-page overallModal="{{overallModal}}">
  <view class="page-container" style="background-image:url({{$cdn}}/MiKangCampaign/mk-prize-draw-bg.png);">
    <custom-header background="transparent" type="{{1}}" isBackHidden="{{ isBackHidden }}" color="black" />
    <view class="prizeDraw">
      <view class="activeRules" bindtap="activeRules">活动规则</view>
      <!-- <view class="prizeDraw-title1 title"><text>{{"您获得了1张健康美容品类85折券"}}</text></view>
      <view class="prizeDraw-title2 title"><text>{{"在【我的】-【我的礼券】中查看"}}</text></view> -->
      <view class="tips">
        <!-- <view class="tips-img">
          <image src="{{$cdn}}/signUp/Winning1.png" mode="" />
        </view> -->
        <view class="tips-title text-wrapper" animation="{{textAnim1}}" wx:if="{{!isAnimateStart}}">
          <view class="prizeDraw-title3" > <text>已完成五日打卡</text> </view>
          <view class="prizeDraw-title4" animation="{{textAnim1}}"> <text>抽取惊喜礼品</text> </view>
        </view>
        <view class="tips-title hide-text-wrapper" animation="{{textAnim2}}" wx:else>
          <view class="prizeDraw-title3" >
            <text wx:if="{{surplusData.prizesLevelCode!='6'}}">恭喜您抽到</text>
            <text wx:else>恭喜您获得</text>
          </view>
          <view class="prizeDraw-title4"><text>{{surplusData.prizesName||""}}</text></view>
        </view>
      </view>
      <view class="gift-wrap">
        <!-- <view class="gift-item " wx:for="{{giftList}}">
          <image class="img" src="{{$cdn}}/signUp/{{item.id == checkIn?'active-':''}}{{item.imageUrl}}" mode="" />
        </view> -->
        <view class="image-container" bindtap="onImageClick">
          <view class="image-inner" animation="{{flipAnim}}">
            <image src="{{$cdn}}/MiKangCampaign/mk-prize-card-2.png" class="front" style="opacity:{{frontOpacity}};"></image>
            <image src="{{$cdn}}/MiKangCampaign/mk-prize-blank.png" class="back" style="opacity:{{backOpacity}};"></image>           
          </view>
          <view class="gift-box" wx:if="{{isAnimateStart}}">
              <image class="muji-logo hide-text-wrapper" src="{{$cdn}}/MiKangCampaign/mk-muji-logo.png" animation="{{textAnim2}}"></image>
              <image class="fireworks hide-text-wrapper" src="{{$cdn}}/MiKangCampaign/mk-gift-fireworks.png" animation="{{textAnim2}}"></image>
              <image class="gift-item hide-text-wrapper" src="{{backImg}}" animation="{{textAnim2}}"></image>
            </view>
        </view>

      </view>
      <view class="gift-desc hide-text-wrapper" animation="{{textAnim2}}" wx:if="{{surplusData && isSurplus && isAnimateStart}}">
        <view class="gift-desc-container">
          <view wx:if="{{surplusData.description !== '50积分'}}" class="gift-desc-content">
            <view class="gift-desc-title">
              奖品已发放至<text class="gift-desc-height-light">「我的礼券」</text>中
            </view>
            <view class="gift-desc-tips">*兑换详情详见卡券规则</view>
            <view class="gift-desc-button" bind:tap="toCoupon">
              查看礼券
            </view>
          </view>
          <view wx:else class="gift-desc-content">
            <view class="gift-desc-title">
              奖品已发放至<text class="gift-desc-height-light">「积分商城」</text>中
            </view>
            <view class="gift-desc-button" bind:tap="toShop">
              前往积分商城
            </view>
          </view>
        </view>
        <!-- <view wx:if="{{isShare && surplusCount > 0}}" class="prize-once-again">
          <image src="{{$cdn}}/MiKangCampaign/mk-prize-again.png" mode="aspectFit" />
        </view> -->
      </view>
      <!-- <view class="bottom-box">
        <basic-button disabled="{{surplusCount==0}}" width="{{670}}" size="large" bind:click="goWinning">
          抽取惊喜礼品
        </basic-button>
      </view> -->
    </view>
  </view>
  <Popup isShow="{{showPopup}}" info="{{infoLottery}}" bindclose="closePopup"></Popup>
</my-page>