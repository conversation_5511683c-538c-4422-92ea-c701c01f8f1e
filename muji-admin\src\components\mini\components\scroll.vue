<template>
  <div class="header-title">滚动区域</div>
  <a-space>
    <!-- <a-form-item>
      <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.width" addon-before="宽" addon-after="px"></a-input-number>
    </a-form-item> -->
    <a-form-item>
      <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.height" addon-before="高" addon-after="px"></a-input-number>
    </a-form-item>
  </a-space>
  <a-form-item>
    <div class="header-title">内容</div>
    <a-form-item>
      <a-button @click="addList" block>+ 添加图片</a-button>
    </a-form-item>
    <VueDraggable :list="data.list" item-key="id" :animation="300">
      <template #item="{ element: item, index:i }">
        <div class="scroll">
          <div class="scroll-title">图{{ i+1 }}</div>
          <a-space>
            <HolderOutlined />
            <uploadImg :max="10" :width="100" :height="100" :imgUrl="item.imgUrl" :form="data" :path="i" :disabled="disabled" @success="uploadSuccess" />
            <div style="margin-left:20px;">
              <div style="margin-bottom:10px;font-weight:bold">跳转热区</div>
              <!-- 宽度是375-左右边距 -->
              <addLink :imgUrl="item.imgUrl" :links="item.imgLinks" :components="components" @ok="(link)=>item.imgLinks=link">
                <a-button type="primary" style="width:200px;">设置热区</a-button>
              </addLink>
            </div>
          </a-space>
          <delete-outlined class="scroll-delete" size="mini" @click.stop="deleteList(i)" />
        </div>
      </template>
    </VueDraggable>
    <div class="global-tip">建议长宽750*200</div>
  </a-form-item>

  <div class="header-title">背景设置</div>
  <bgSet :addParams="data"></bgSet>
  <div class="header-title">组件样式</div>
  <!-- <a-form-item label="底部空白">
    <a-input-number placeholder="请输入" :precision="0" v-model:value="data.bottom" addon-after="px"></a-input-number>
  </a-form-item> -->
  <a-form-item label="圆角" :labelCol="{width:'50px'}">
    <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.borderRadius" addon-after="px"></a-input-number>
  </a-form-item>
  <a-form-item label="边距" :labelCol="{width:'50px'}">
    <a-space>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingTop" addon-before="上" addon-after="px"></a-input-number>
      </a-form-item>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingBottom" addon-before="下" addon-after="px"></a-input-number>
      </a-form-item>
    </a-space>
    <a-space>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingLeft" addon-before="左" addon-after="px"></a-input-number>
      </a-form-item>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingRight" addon-before="右" addon-after="px"></a-input-number>
      </a-form-item>
    </a-space>
  </a-form-item>
</template>
<script setup>
import { v4 as uuidv4 } from "uuid";
import { cloneDeep, set } from 'lodash'
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  // 所有组件数组
  components: {
    type: Array,
    default() {
      return []
    }
  },
  // 当前组件数据
  data: {
    type: Object,
    default() {
      return {}
    }
  },
  // 组件展示的宽度
  width: {
    type: Number,
    default: 750,
  },
})

// 上传图片 视频
const uploadSuccess = async (data) => {
  let { form, path, imgUrl, imgWidth, imgHeight } = data;
  form.list[path].imgUrl = imgUrl
  form.list[path].imgWidth = imgWidth
  form.list[path].imgHeight = imgHeight
}


// 修改颜色
const changeColor = async (key, color) => {
  props.addParams[key] = color
}

// 添加图片
const addList = () => {
  props.data.list.push({
    id: uuidv4(),
    imgUrl: '', // 图片url
    imgWidth: '',// 图片原始高度
    imgHeight: '',// 图片原始高度
    // 图片热区
    imgLinks: [
      // {
      //   id: uuidv4(),
      //   width: 10,
      //   height: 10,
      //   position: [0, 0], // 位置
      //   code: '',// 埋点参数
      //   events: [],// 事件
      //   actions: [],// 行为
      // }
    ],
  })
}

// 删除图片
const deleteList = (i) => {
  props.data.list.splice(i, 1)
}

</script>

<style  scoped lang="scss">
.scroll {
  position: relative;
  margin-bottom: 10px;
  &-delete {
    position: absolute;
    top: 0;
    right: 0;
    padding: 10px;
    color: red;
  }
  &-title {
    margin-bottom: 10px;
  }
}
</style>
