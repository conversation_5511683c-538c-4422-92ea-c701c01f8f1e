<template>
  <a-drawer :title="title" width="1200" placement="right" :closable="true" :maskClosable="false" :open="thisFields.open" @close="onClose">
    <a-spin :spinning="thisFields.loading">
      <a-form class="form" ref="formRef" :model="formFields" :rules="formRules" :labelCol="{ style: 'width:160px' }">
        <div class="form-top-titles-common">推广信息基本配置</div>
        <div class="form-top-line-common"></div>
        <a-form-item label="推广名称" name="name">
          <a-input placeholder="请输入" style="width:300px;" v-model:value="formFields.name" show-count :maxlength="20" />
        </a-form-item>
        <a-form-item label="推广时间" name="onType">
          <a-radio-group v-model:value="formFields.onType" style="margin-bottom: 16px" @change="thisMethods.onTypeChange">
            <a-radio-button :value="2">时间段</a-radio-button>
            <a-radio-button :value="1">永久有效</a-radio-button>
          </a-radio-group>
          <template v-if="formFields.onType === 2">
            <BaseDateTimeRange label="" name="dateTimeRange" v-model="formFields.dateTimeRange" />
          </template>
        </a-form-item>
        <div class="form-top-titles-common">推广货架选择</div>
        <div class="form-top-line-common"></div>
        <BaseBelongingShelfSelect label="推广货架" name="shelfId" v-model="formFields.shelfId" />
        <div class="form-top-titles-common">推广素材配置</div>
        <div class="form-top-line-common"></div>
        <div class="ui-widgets-in-biz" v-if="thisFields.open">
          <div class="_left">
            <WidgetsCarouselPreview :item="formFields.content" :formRules="formRules" />
          </div>
          <div class="_right">
            <WidgetsCarouselConfig :shelfId="formFields.shelfId" :formFields="formFields.content" :formRules="formRules" :formRef="formRef" />
          </div>
        </div>
      </a-form>
    </a-spin>
    <template #footer v-if="!disabled">
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="ok" :loading="thisFields.loading">确定</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>
<script setup>
import { cloneDeep } from 'lodash'
import { useGlobalStore } from '@/store'
import { message } from 'ant-design-vue'
import { apiShelfExtension } from '@/http/index.js'
import { nextTick, reactive } from 'vue'
import { v4 } from 'uuid'
const global = useGlobalStore()
const formRef = ref(null)
const emits = defineEmits(['cancel', 'ok', 'update:visible'])
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  id: {
    type: [String, Number],
    default: ''
  },
  type: {
    type: [String, Number],
    default: 0 // 0-新增 1-编辑 2-查看 3-复制
  }
})
const disabled = computed(() => {
  return +props.type === 2
})
const title = computed(() => {
  return ['新增', '编辑', '查看', '复制'][props.type] + '推广'
})
const getDefaultFormFields = () => ({
  onType: 2,
  dateTimeRange: [],
  shelfId: null,
  content: {
    id: v4(),
    type: "carousel",
    list: [{
      imgSrc: "",
      imgLinks: [
        {
          id: v4(),
          operateType: null,// 行为类型
          modalIndex: null,// 弹窗下标
          anchorIndex: null,// 锚点下标
          copyText: '',// 复制内容
          linkType: 1,// 页面类型
          linkName: '',// 跳转页面标题
          linkUrl: '',// 页面跳转路径组合的
          appid: '',// 小程序id
          path: '',// 页面跳转链接
          url: '',// H5链接
        }
      ],
    },
    ],
    style: {
      height: 225,
      // width: 385
    },
    autoplay: true,
    interval: 5000,
    duration: 500,
    circular: true,
    showIndicator: true
  },
  num: 0,
  name: ''
})
const formFields = reactive(getDefaultFormFields())
const formRules = reactive({
  onType: [{ required: true, message: '本项必选' }],
  dateTimeRange: [{ required: true, message: '本项必选' }],
  shelfId: [{ required: true, message: '本项必选' }],
  name: [{ required: true, message: '本项必填' }]
})

const thisFields = reactive({
  open: props.visible,
  loading: false
})
const thisMethods = {
  onTypeChange() {
    if (formFields.onType === 2) {
      formFields.dateTimeRange = []
    }
  }
}

watch(() => props.visible, () => {
  thisFields.open = props.visible
  Object.keys(formFields).forEach(key => (delete formFields[key]))
  Object.assign(formFields, getDefaultFormFields())
  formRef.value?.clearValidate()
  if (thisFields.open) {
    initData()
  }
})

const initData = async () => {
  if (!props.id) return
  thisFields.loading = true
  const res = await apiShelfExtension.getPageDetail({ id: props.id }).finally(() => thisFields.loading = false)

  if (res.data.content) {
    // console.log("🚀 ~ initData ~ res.data.content:", res.data.content)
    res.data.content.list.forEach((item, index) => {
      // console.log(!item.imgLinks, item);
      if (!item.imgLinks) {
        item['imgLinks'] = [
          {
            id: v4(),
            operateType: null,// 行为类型
            modalIndex: null,// 弹窗下标
            anchorIndex: null,// 锚点下标
            copyText: '',// 复制内容
            linkType: 1,// 页面类型
            linkName: '',// 跳转页面标题
            linkUrl: '',// 页面跳转路径组合的
            appid: '',// 小程序id
            path: '',// 页面跳转链接
            url: '',// H5链接
          }
        ]
      }
    })
  }

  await nextTick()
  Object.assign(formFields, res.data)

  if (props.type === 3) {
    formFields.id = undefined
    formFields.name += '（复制）'
    formFields.dateTimeRange = []
  }
}

const onClose = () => {
  emits('cancel')
  emits('update:visible', false)
}
const ok = async () => {
  // console.log('ok：', formFields)
  await formRef.value.validate()
  let params = cloneDeep(formFields)
  // console.log("🚀 ~ ok ~ params:", params)
  thisFields.loading = true
  const isAdd = [0, 3].includes(props.type)
  const res = await apiShelfExtension[isAdd ? 'createPage' : 'updatePage'](params).finally(() => thisFields.loading = false)
  message.success(res.msg)
  emits('ok', isAdd ? '' : props.id)
}
</script>

<style scoped lang="scss">
:deep(.searchForm .ant-form-item) {
  margin-bottom: 16px;
}
</style>
