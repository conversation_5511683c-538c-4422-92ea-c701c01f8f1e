package com.dz.common.core.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * CODE NUMBER 通用DTO
 * @author: Handy
 * @date:   2024/3/7 16:33
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
@ApiModel(value = "CODE NUMBER 通用DTO")
public class CodeNumberDTO {

    @ApiModelProperty(value = "CODE")
    private String code;

    @ApiModelProperty(value = "NUMBER")
    private Integer num;

    public CodeNumberDTO(String code, Integer num) {
        this.code = code;
        this.num = num;
    }

}
