<template>
  <div>
    <!-- 普通图片上传模式 -->
    <div style="display:flex;flex-wrap:wrap;" v-if="!props.specialPicture">
      <draggable :sort="sort" class="drag" :list="tempList" item-key="index" ghost-class="ghost" animation="300" @end="end" :filter="'.add'">
        <template #item="{ element: file, index }">
          <div>
            <!-- 已上传图片展示区域 -->
            <div class="box" v-if="file">
              <div v-if="!disabled" style="display:flex;align-items:center;">
                <SvgIcon class="icon" name="drag-dot-vertical" width="20px" height="20px" style="cursor:move;" />
              </div>
              <div class="add">
                <uploadImg :api="api" :maxImgPxShow="maxImgPxShow" :deleted="false" :disabled="disabled" :max="limit" :width="width" :height="height" :imgUrl="file" :path="index" @success="uploadSuccess" />
                <!-- 删除按钮 -->
                <delete-outlined class="box-delete" @click.stop="removeFile(index)" v-if="!disabled" />
              </div>
            </div>
            <!-- 添加图片按钮 -->
            <div v-if="!max || (index == imageProfile.length && index != max) && !disabled" class="add">
              <uploadImg :maxImgPxShow="maxImgPxShow" :api="api" :max="limit" :width="104" :height="104" :path="imageProfile.length" @success="uploadSuccess" />
            </div>
          </div>
        </template>
      </draggable>
    </div>

    <!-- 特殊图片上传模式 -->
    <div v-else>
      <div style="display:flex;flex-wrap:wrap;">
        <draggable :sort="sort" class="drag" :list="tempList" item-key="index" ghost-class="ghost" animation="300" @end="end" :filter="'.add'">
          <template #item="{ element: file, index }">
            <div>
              <!-- 已上传图片列表 -->
              <div class="box" v-if="file && file.url">
                <div style="display:flex;align-items:center;" v-if="!disabled">
                  <SvgIcon class="icon" name="drag-dot-vertical" width="20px" height="20px" style="cursor:move;" />
                </div>
                <div class="add">
                  <uploadImg :specialPicture="props.specialPicture" :api="api" :maxImgPxShow="maxImgPxShow" :deleted="false" :disabled="disabled" :multiple="multiple" :max="limit" :width="width" :height="height" :imgUrl="file['url']" :path="index" @success="uploadSuccess" />
                  <!-- 删除按钮 -->
                  <delete-outlined class="box-delete" @click.stop="removeFile(index)" v-if="!disabled && file.url" />
                </div>
              </div>
              <!-- 添加图片按钮 -->
              <div class="add" v-if="imageProfile.length < max && !disabled && !file.url">
                <uploadImg :specialPicture="props.specialPicture" :multiple="multiple" :maxImgPxShow="maxImgPxShow" :api="api" :max="limit" :width="104" :height="104" :path="imageProfile.length" :numTotal="extractProxies(tempList).length" @success="uploadSuccess" />
              </div>
            </div>
          </template>
        </draggable>
      </div>
    </div>
  </div>
</template>

<script setup>
import draggable from "vuedraggable";
import { message } from 'ant-design-vue';
import { fileUpload } from '@/http/index.js'
import { v4 as uuidv4 } from 'uuid'
import { reactive, toRefs, ref, onMounted, watch, computed } from 'vue';

const emit = defineEmits(['success'])

// Props定义
const props = defineProps({
  api: { // 图片上传接口
    type: Function,
    default: fileUpload,
  },
  sort: { // 是否可以拖拽排序
    type: Boolean,
    default: false
  },
  deleted: { // 是否可以删除
    type: Boolean,
    default: true
  },
  disabled: { // 是否禁用
    type: Boolean,
    default: false
  },
  listType: { // 显示列表类型
    type: String,
    default: "picture-card"
  },
  max: { // 最大上传数量,0表示无限制
    type: Number,
    default: 10
  },
  fileFormat: { // 允许的文件格式
    type: String,
    default: "jpg,jpeg,gif,svg,png"
  },
  limit: { // 单个文件大小限制(MB)
    type: Number,
    default: 10
  },
  fileList: { // 默认图片列表
    type: [Array, String],
    default: () => []
  },
  form: { // 表单对象
    type: Object
  },
  path: { // 表单字段路径
    type: String
  },
  width: { // 图片宽度
    type: Number,
    default: 104
  },
  height: { // 图片高度
    type: Number,
    default: 104
  },
  maxImgPxShow: { // 是否开启图片尺寸校验
    type: Boolean,
    default: false
  },
  specialPicture: { // 是否为特殊图片模式
    type: Boolean,
    default: false
  },
  multiple: { // 是否支持多选
    type: Boolean,
    default: false
  }
})

const { proxy } = getCurrentInstance()

// 图片列表数据
const { imageProfile } = toRefs(reactive({
  imageProfile: props.fileList
}))

// 监听fileList变化
watch(() => props.fileList, data => {
  imageProfile.value = data
})

// 计算临时列表,用于展示
const tempList = computed(() => {
  if (props.specialPicture) {
    let temp = []
    if (imageProfile.value.length > 0) {
      temp.push(...imageProfile.value)
    } else {
      temp.push({})
    }
    return [...imageProfile.value, {}]
  } else {
    return [...imageProfile.value, '']
  }
})

// 上传成功回调
const uploadSuccess = async ({ path, imgUrl }) => {
  // console.log("🚀 ~ uploadSuccess ~  path, imgUrl:", path, imgUrl)
  if (String(path)) {
    imageProfile.value[path] = imgUrl
  } else {
    imageProfile.value.push(imgUrl)
  }
  changeResult()
}

// 删除图片
const removeFile = (index) => {
  imageProfile.value.splice(index, 1)
  changeResult()
}

// 拖拽排序完成
const end = () => {
  changeResult()
}

// 提取代理对象中的实际数据
function extractProxies(array) {
  if (array) {
    return array.flatMap(proxy => {
      return Array.isArray(proxy) ? proxy : (proxy._target || proxy)
    }).filter(item => Object.keys(item).length > 0)
  }
  return []
}

// 触发结果更新
const changeResult = () => {
  emit('success', {
    form: props.form,
    path: props.path,
    imgUrl: props.specialPicture ? extractProxies(tempList.value) : tempList.value.filter(item => !!item)
  })
}
</script>

<style lang="scss" scoped>
// 隐藏默认的上传列表样式
:deep(.ant-upload-list-picture-card-container) {
  width: 0;
  height: 0;
  margin: 0;
}

// 自定义上传按钮样式
:deep(.ant-upload) {
  display: flex;
  align-items: flex-start !important;
  justify-content: flex-start !important;
  border: none !important;
  font-size: 10px !important;
}

// 拖拽容器样式
.drag {
  display: inline-flex;
  flex-wrap: wrap;
}

// 图片容器样式
.box {
  position: relative;
  margin-right: 8px;
  margin-bottom: 8px;
  color: #999;
  display: inline-flex;

  &-delete {
    position: absolute;
    padding: 4px;
    bottom: 8px;
    right: 0;
    color: red;
  }
}

// 删除按钮样式
.box-delete {
  position: absolute;
  padding: 4px;
  bottom: 8px;
  right: 0;
  color: red;
}
</style>
