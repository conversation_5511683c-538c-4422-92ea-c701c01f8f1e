package com.dz.gateway.swagger;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.config.GatewayProperties;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.support.NameUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/swagger-resources")
public class SwaggerProvider {

    @Value("${spring.profiles.active}")
    private String profile;
    @Value("${service.swagger:true}")
    private Boolean openSwagger;

    // 当环境为生产环境（例如 profile 为 prod）或配置不允许打开 Swagger 时，不聚合资源
    private static final String SWAGGER_PROFILE = "pro";

    /**
     * Springdoc OpenAPI 的文档入口（默认是 /v3/api-docs）
     */
    public static final String API_URI = "/v3/api-docs";

    private final RouteLocator routeLocator;
    private final GatewayProperties gatewayProperties;

    public SwaggerProvider(RouteLocator routeLocator, GatewayProperties gatewayProperties) {
        this.routeLocator = routeLocator;
        this.gatewayProperties = gatewayProperties;
    }

    @GetMapping("")
    public Mono<List<SwaggerResource>> swaggerResources() {
        List<SwaggerResource> resources = new ArrayList<>();
        // 如果在生产环境或未开启 swagger，则返回空列表
        if (SWAGGER_PROFILE.equalsIgnoreCase(profile) || !openSwagger) {
            return Mono.just(resources);
        }
        List<String> routes = new ArrayList<>();
        // 获取网关中所有路由 ID
        routeLocator.getRoutes().subscribe(route -> routes.add(route.getId()));
        // 从网关配置中取出路由信息，并过滤获取 Path 类型的 Predicate
        gatewayProperties.getRoutes().stream()
                .filter(routeDefinition -> routes.contains(routeDefinition.getId()))
                .forEach(routeDefinition -> routeDefinition.getPredicates().stream()
                        .filter(predicateDefinition -> "Path".equalsIgnoreCase(predicateDefinition.getName()))
                        .forEach(predicateDefinition -> {
                            // 将路径中 "/**" 替换为 API_URI（即 "/v3/api-docs"）
                            String location = predicateDefinition.getArgs()
                                    .get(NameUtils.GENERATED_NAME_PREFIX + "0")
                                    .replace("/**", API_URI);
                            resources.addAll(swaggerResource(routeDefinition.getId(), location));
                        }));

        List<SwaggerResource> sortedResources = resources.stream()
                .sorted(Comparator.comparing(SwaggerResource::getName))
                .collect(Collectors.toList());
        return Mono.just(sortedResources);
    }

    /**
     * 根据路由 ID 和 API 文档地址生成多个分组的 SwaggerResource
     */
    private List<SwaggerResource> swaggerResource(String name, String location) {
        List<SwaggerResource> swaggerResources = new ArrayList<>();
        // 假设你在各个服务中配置了多个 API 分组，例如 app、crm、openapi、oms，
        // 则通过 query 参数区分：group=app 等
        SwaggerResource resourceApp = new SwaggerResource();
        resourceApp.setName("app-" + name);
        resourceApp.setUrl(location + "?group=app");
        resourceApp.setSwaggerVersion("3.0");

        SwaggerResource resourceCrm = new SwaggerResource();
        resourceCrm.setName("crm-" + name);
        resourceCrm.setUrl(location + "?group=crm");
        resourceCrm.setSwaggerVersion("3.0");

        SwaggerResource resourceOpen = new SwaggerResource();
        resourceOpen.setName("openapi-" + name);
        resourceOpen.setUrl(location + "?group=openapi");
        resourceOpen.setSwaggerVersion("3.0");

        SwaggerResource resourceOms = new SwaggerResource();
        resourceOms.setName("oms-" + name);
        resourceOms.setUrl(location + "?group=oms");
        resourceOms.setSwaggerVersion("3.0");

        swaggerResources.add(resourceApp);
        swaggerResources.add(resourceCrm);
        swaggerResources.add(resourceOpen);
        swaggerResources.add(resourceOms);
        return swaggerResources;
    }
}
