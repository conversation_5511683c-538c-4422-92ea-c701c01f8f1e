package com.dz.ms.user.utils;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.client.ResponseErrorHandler;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;

@Component
public class WebServiceUtils {

  public static String postWebService(String mapParas, String query_url) throws IOException {
    HttpHeaders httpHeaders = new HttpHeaders();
    httpHeaders.set("Content-Type", "application/json;charset=utf-8");
    HttpEntity<String> entity = new HttpEntity<>(mapParas, httpHeaders);
    RestTemplate restTemp = new RestTemplate();
    restTemp.setErrorHandler(new ResponseErrorHandler() {
      @Override
      public boolean hasError(ClientHttpResponse response) throws IOException {
        return false;
      }

      @Override
      public void handleError(ClientHttpResponse response) throws IOException {
      }
    });
    ResponseEntity<String> postForEntity = restTemp.postForEntity(query_url, entity, String.class);
    return postForEntity.getBody();
  }

  public static String getWebService(String query_url, String token) throws IOException {
    HttpHeaders httpHeaders = new HttpHeaders();
    httpHeaders.set("Content-Type", "application/json;charset=utf-8");
    httpHeaders.add("token", token);
    HttpEntity<String> entity = new HttpEntity<>(null, httpHeaders);
    RestTemplate restTemp = new RestTemplate();
    restTemp.setErrorHandler(new ResponseErrorHandler() {
      @Override
      public boolean hasError(ClientHttpResponse response) throws IOException {
        return false;
      }

      @Override
      public void handleError(ClientHttpResponse response) throws IOException {
      }
    });
    ResponseEntity<String> resEntity = restTemp.exchange(query_url, HttpMethod.GET, entity, String.class);
    return resEntity.getBody();
  }

  public static String postWebServiceCarryToken(String mapParas, String query_url, String token) throws IOException {
    HttpHeaders httpHeaders = new HttpHeaders();
    httpHeaders.set("Content-Type", "application/json;charset=utf-8");
    httpHeaders.set("token", token);
    HttpEntity<String> entity = new HttpEntity<>(mapParas, httpHeaders);
    RestTemplate restTemp = new RestTemplate();
    restTemp.setErrorHandler(new ResponseErrorHandler() {
      @Override
      public boolean hasError(ClientHttpResponse response) throws IOException {
        return false;
      }

      @Override
      public void handleError(ClientHttpResponse response) throws IOException {
      }
    });
    ResponseEntity<String> postForEntity = restTemp.postForEntity(query_url, entity, String.class);
    return postForEntity.getBody();
  }


}
