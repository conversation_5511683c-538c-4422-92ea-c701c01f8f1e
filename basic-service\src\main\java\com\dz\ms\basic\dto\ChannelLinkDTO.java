package com.dz.ms.basic.dto;
import java.util.Date;
import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 渠道链接配置DTO
 * @author: Handy
 * @date:   2023/08/26 17:06
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "渠道链接配置")
public class ChannelLinkDTO extends BaseDTO {

    @ApiModelProperty(value = "配置ID")
    private Long id;
    @ApiModelProperty(value = "链接自增编码")
    private String linkCode;
    @ApiModelProperty(value = "渠道编码")
    private String channel;
    @ApiModelProperty(value = "渠道名称")
    private String channelName;
    @ApiModelProperty(value = "小程序页面路径")
    private String path;
    @ApiModelProperty(value = "小程序页面参数")
    private String query;
    @ApiModelProperty(value = "更新时间")
    private Date modified;

}
