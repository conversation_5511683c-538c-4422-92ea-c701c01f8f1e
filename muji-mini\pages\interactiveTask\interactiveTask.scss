/* pages/interactiveTask/interactiveTask.wxss */
.page-content {
  width: 100%;
  box-sizing: border-box;
  position: relative;
  padding: 0 40rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .task-list {
    flex: 1;
    width: 100%;
    box-sizing: border-box;
    padding-bottom: 83rpx;
    display: flex;
    flex-direction: column;
    position: relative;

    .task-list-box {
      flex: 1;
      min-height: calc(100% - 80rpx);
      padding-top: 30rpx;
      box-sizing: border-box;
      position: relative;
      width: 100%;
      display: flex; // 防止margin-bottom溢出
      flex-direction: column;
    }


    .bottom-box {
      width: 100%;
      text-align: center;
      padding-top: 40rpx;
    }
  }

  .page-nav {
    border-bottom: 2rpx solid #ededed;
  }

  .nav-wrap {
    height: 90rpx;
  }


  .tab-item {
    font-size: 32rpx;
    line-height: 90rpx;
    position: relative;
  }
}

.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  text-align: center;
  padding-bottom: env(safe-area-inset-bottom);
}


.complate-btn {
  height: 33rpx;
  font-family: PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #888888;
  line-height: 33rpx;
  text-align: center;
  font-style: normal;
  position: relative;
  display: inline-block;

  &::after {
    content: '';
    display: block;
    position: absolute;
    width: 100%;
    height: 1rpx;
    background-color: #888888;
  }
}
