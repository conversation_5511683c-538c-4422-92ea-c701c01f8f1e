package com.dz.ms.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.ms.basic.dto.MiniappTemplateDTO;
import com.dz.ms.basic.dto.MiniappTemplateGroupDTO;
import com.dz.ms.basic.dto.MiniappTemplateListDTO;
import com.dz.ms.basic.dto.MiniappTemplateUpdatePathDTO;
import com.dz.ms.basic.entity.MiniappTemplate;

import java.util.List;

/**
 * 小程序页面模板接口
 * @author: Handy
 * @date:   2022/02/04 22:59
 */
public interface MiniappTemplateService extends IService<MiniappTemplate> {

    /**
     * 保存小程序页面模板
     * @param param
     * @return
     */
    Long saveMiniappTemplate(MiniappTemplateDTO param);

    /**
     * 根据模板ID获取小程序页面模板
     * @param id
     * @return
     */
    MiniappTemplateDTO getMiniappTemplateById(Long id, Long tenantId);

    /**
     * 根据模板类型获取小程序页面模板
     * @param type
     * @return
     */
    MiniappTemplateDTO getMiniappTemplateByType(Integer type,Integer pageType,Long tenantId);

    /**
     * 获取小程序二维码
     * @param page
     * @param scene
     * @param width
     * @return
     */
    public byte[] getMiniappQrcode(String page,String scene, Integer width, Integer trial, Integer isHyaline, String linColor);

    PageInfo<MiniappTemplateListDTO> getMiniappTemplatePath(PageInfo<MiniappTemplateListDTO> miniappTemplateListDTOPageInfo);

    void updsateMiniappTemplateGroup(MiniappTemplateGroupDTO param);

    Long updateMiniappTemplatePath(MiniappTemplateUpdatePathDTO param);

    MiniappTemplate getCrmMiniappTemplateById(Long id);

    void updateMiniappTemplatePublish(MiniappTemplateUpdatePathDTO param);

    List<MiniappTemplate> selectHistory(List<MiniappTemplate> list);
}
