package com.dz.common.core.dto.wechat.qymsg;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 企微模板卡片消息
 * <AUTHOR>
 * @date 2022/07/28 18:10
 */
@Getter
@Setter
public class TemplateCardMsgDTO {

    private String touser;
    private String toparty;
    private String totag;
    private String msgtype;
    private String agentid;
    private TemplateCard template_card;
    private int enable_id_trans;
    private int enable_duplicate_check;
    private int duplicate_check_interval;

    @Getter
    @Setter
    public class TemplateCard {
        private String card_type;
        private Source source;
        private ActionMenu action_menu;
        private String task_id;
        private Title main_title;
        private QuoteArea quote_area;
        private Title emphasis_content;
        private String sub_title_text;
        private List<TemplateMsgContentDTO> horizontal_content_list;
        private List<Jump> jump_list;
        private Jump card_action;
    }

    @Getter
    @Setter
    public class Source {
        private String icon_url;
        private String desc;
        private int desc_color;
    }

    @Getter
    @Setter
    public class ActionList {
        private String text;
        private String key;
    }

    @Getter
    @Setter
    public class ActionMenu {
        private String desc;
        private List<ActionList> action_list;
    }

    @Getter
    @Setter
    public class Title {
        private String title;
        private String desc;
    }

    @Getter
    @Setter
    public class QuoteArea {
        private int type;
        private String url;
        private String title;
        private String quote_text;
    }

    @Getter
    @Setter
    public class Jump {
        private int type;
        private String url;
        private String title;
        private String appid;
        private String pagepath;
    }

    /**
    touser	否	成员ID列表（消息接收者，多个接收者用‘|’分隔，最多支持1000个）。特殊情况：指定为@all，则向关注该企业应用的全部成员发送
    toparty	否	部门ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为@all时忽略本参数
    totag	否	标签ID列表，多个接收者用‘|’分隔，最多支持100个。当touser为@all时忽略本参数
    msgtype	是	消息类型，此时固定为：template_card
    agentid	是	企业应用的id，整型。企业内部开发，可在应用的设置页面查看；第三方服务商，可通过接口 获取企业授权信息 获取该参数值
    card_type	是	模板卡片类型，文本通知型卡片填写 "text_notice"
    source	否	卡片来源样式信息，不需要来源样式可不填写
    source.icon_url	否	来源图片的url，来源图片的尺寸建议为72*72
    source.desc	否	来源图片的描述，建议不超过20个字，（支持id转译）
    source.desc_color	否	来源文字的颜色，目前支持：0(默认) 灰色，1 黑色，2 红色，3 绿色
    action_menu	否	卡片右上角更多操作按钮
    action_menu.desc	否	更多操作界面的描述
    action_menu.action_list	是	操作列表，列表长度取值范围为 [1, 3]
    action_menu.action_list.text	是	操作的描述文案
    action_menu.action_list.key	是	操作key值，用户点击后，会产生回调事件将本参数作为EventKey返回，回调事件会带上该key值，最长支持1024字节，不可重复
    main_title.title	否	一级标题，建议不超过36个字，文本通知型卡片本字段非必填，但不可本字段和sub_title_text都不填，（支持id转译）
    main_title.desc	否	标题辅助信息，建议不超过44个字，（支持id转译）
    quote_area	否	引用文献样式
    quote_area.type	否	引用文献样式区域点击事件，0或不填代表没有点击事件，1 代表跳转url，2 代表跳转小程序
    quote_area.url	否	点击跳转的url，quote_area.type是1时必填
    quote_area.appid	否	点击跳转的小程序的appid，必须是与当前应用关联的小程序，quote_area.type是2时必填
    quote_area.pagepath	否	点击跳转的小程序的pagepath，quote_area.type是2时选填
    quote_area.title	否	引用文献样式的标题
    quote_area.quote_text	否	引用文献样式的引用文案
    emphasis_content	否	关键数据样式
    emphasis_content.title	否	关键数据样式的数据内容，建议不超过14个字
    emphasis_content.desc	否	关键数据样式的数据描述内容，建议不超过22个字
    sub_title_text	否	二级普通文本，建议不超过160个字，（支持id转译）
    horizontal_content_list	否	二级标题+文本列表，该字段可为空数组，但有数据的话需确认对应字段是否必填，列表长度不超过6
    horizontal_content_list.type	否	链接类型，0或不填代表不是链接，1 代表跳转url，2 代表下载附件，3 代表点击跳转成员详情
    horizontal_content_list.keyname	是	二级标题，建议不超过5个字
    horizontal_content_list.value	否	二级文本，如果horizontal_content_list.type是2，该字段代表文件名称（要包含文件类型），建议不超过30个字，（支持id转译）
    horizontal_content_list.url	否	链接跳转的url，horizontal_content_list.type是1时必填
    horizontal_content_list.media_id	否	附件的media_id，horizontal_content_list.type是2时必填
    horizontal_content_list.userid	否	成员详情的userid，horizontal_content_list.type是3时必填
    jump_list	否	跳转指引样式的列表，该字段可为空数组，但有数据的话需确认对应字段是否必填，列表长度不超过3
    jump_list.type	否	跳转链接类型，0或不填代表不是链接，1 代表跳转url，2 代表跳转小程序
    jump_list.title	是	跳转链接样式的文案内容，建议不超过18个字
    jump_list.url	否	跳转链接的url，jump_list.type是1时必填
    jump_list.appid	否	跳转链接的小程序的appid，必须是与当前应用关联的小程序，jump_list.type是2时必填
    jump_list.pagepath	否	跳转链接的小程序的pagepath，jump_list.type是2时选填
    card_action	是	整体卡片的点击跳转事件，text_notice必填本字段
    card_action.type	是	跳转事件类型，1 代表跳转url，2 代表打开小程序。text_notice卡片模版中该字段取值范围为[1,2]
    card_action.url	否	跳转事件的url，card_action.type是1时必填
    card_action.appid	否	跳转事件的小程序的appid，必须是与当前应用关联的小程序，card_action.type是2时必填
    card_action.pagepath	否	跳转事件的小程序的pagepath，card_action.type是2时选填
    task_id	否	任务id，同一个应用任务id不能重复，只能由数字、字母和“_-@”组成，最长128字节，填了action_menu字段的话本字段必填
    enable_id_trans	否	表示是否开启id转译，0表示否，1表示是，默认0
    enable_duplicate_check	否	表示是否开启重复消息检查，0表示否，1表示是，默认0
    duplicate_check_interval	否	表示是否重复消息检查的时间间隔，默认1800s，最大不超过4小时
    */
}