package com.dz.ms.${typeName}.dto;
<#if hasDate>
import java.util.Date;
</#if>
<#if hasDecimal>
import java.math.BigDecimal;
</#if>
import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * ${tableComment}DTO
 * @author: ${author}
 * @date:   ${nowDate}
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "${tableComment}")
public class ${objectName}DTO extends BaseDTO {

    <#list fieldList as var>
    @ApiModelProperty(value = "${var.columnComment}")
    private ${var.modelDataType} ${var.upperCaseColum};
	</#list>

}
