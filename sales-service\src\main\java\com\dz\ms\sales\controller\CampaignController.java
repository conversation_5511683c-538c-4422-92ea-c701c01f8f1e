package com.dz.ms.sales.controller;


import com.dz.common.base.vo.Result;
import com.dz.common.core.fegin.sales.CampaignFeignClient;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.ms.sales.dto.CampaignDTO;
import com.dz.ms.sales.service.CampaignService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/3
 */
@Api(tags = "活动主信息接口")
@RestController
public class CampaignController implements CampaignFeignClient {

    @Resource
    private CampaignService campaignService;


    @ApiOperation("修改campaign数据")
    @PostMapping(value = "/openapi/campaign/edit")
    public Result<Boolean> editCampaign(@RequestBody CampaignDTO param) {
        Result<Boolean> result = new Result<>();
        Boolean dto = campaignService.editCampaign(param);
        result.setData(dto);
        return result;
    }


    @ApiOperation("查询活动主信息")
    @GetMapping(value = "/app/campaign/list")
    public Result<List<CampaignDTO>> getCampaignList() {
        Result<List<CampaignDTO>> result = new Result<>();
        List<CampaignDTO> list = campaignService.getCampaignList();
        result.setData(list);
        return result;
    }


    @ApiOperation("获取当前用户的活动信息")
    @GetMapping(value = "/app/campaign/user/order_match")
    public Result<CampaignDTO> getCurrentCampaignOrderMatch() {
        Result<CampaignDTO> result = new Result<>();
        CampaignDTO dto = campaignService.getCurrentCampaignOrderMatch(SecurityContext.getUser().getUid());
        result.setData(dto);
        return result;
    }

    @ApiOperation("删除当前用户的活动数据（优先调用）")
    @PostMapping(value = "/remote/campaign/current_user/del_data")
    public Result<Boolean> delCurrentUserCampaignData(@RequestParam("uid") Long uid) {
        Result<Boolean> result = new Result<>();
        Boolean dto = campaignService.delCurrentUserCampaignData(uid);
        result.setData(dto);
        return result;
    }

    @PostMapping(value = "/remote/couponUser/buyer")
    public Result<Void> CampaignBuyerJob(@RequestParam("campaignCode") String campaignCode) {
        campaignService.CampaignBuyerJob(campaignCode);
        return new Result<>();
    }

    @PostMapping(value = "/remote/couponUser/buyer/pushMsg")
    public Result<Void> CampaignBuyerPushMsgJob(@RequestParam("campaignCode") String campaignCode) {
        campaignService.CampaignBuyerPushMsgJob(campaignCode);
        return new Result<>();
    }

}
