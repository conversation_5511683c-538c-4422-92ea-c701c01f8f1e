package com.dz.ms.product.dto.res;

import com.dz.common.base.dto.BaseDTO;
import com.dz.ms.product.dto.TagInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 营销活动规则关联的货架商品DTO
 *
 * @author: LiinNs
 * @date: 2024/11/20 11:39
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "营销活动规则关联的货架商品")
public class ShelfCampaignRuleProductResDTO extends BaseDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "营销活动ID")
    private Long campaignId;
    @ApiModelProperty(value = "营销活动规则ID")
    private Long ruleId;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "货架商品ID")
    private Long shelfProductId;
    @ApiModelProperty(value = "商品ID")
    private Long productId;
    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "是否展示 0不展示 1展示")
    private Integer beShow;
    @ApiModelProperty(value = "商品类型 1实物商品 2电子券")
    private Integer pdType;
    @ApiModelProperty(value = "上架库存")
    private Integer onInventory;
    @ApiModelProperty(value = "目前库存")
    private Integer currentInventory;
    @ApiModelProperty(value = "1同当前库存 2活动规则库存")
    private Integer inventoryType;
    @ApiModelProperty(value = "活动规则库存")
    private Integer ruleInventory;
    @ApiModelProperty(value = "兑换积分")
    private Integer costPoint;
    @ApiModelProperty(value = "积分划线价")
    private Integer prePoint;
    @ApiModelProperty(value = "商品主图")
    private String shelfImg;
    @ApiModelProperty(value = "首张商品场景图")
    private String sceneImg;
    @ApiModelProperty(value = "商品标签列表")
    private List<TagInfoDTO> tagList;
    @ApiModelProperty(value = "角标字符串 多角标逗号分隔")
    private String superscript;
    @ApiModelProperty(value = "每人限购数")
    private Integer everyoneLimit;
}
