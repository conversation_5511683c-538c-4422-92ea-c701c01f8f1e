package com.dz.ms.user.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import groovy.transform.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 类注释
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/5/26 19:23
 */
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class EmployeeScheduleExcelDTO {

    @ExcelProperty("姓名")
    private String empName;

    @ExcelProperty("员工编号")
    private String empCode;

    @ExcelProperty(index = 2)
    private String sun;

    @ExcelProperty(index = 3)
    private String mon;

    @ExcelProperty(index = 4)
    private String tue;

    @ExcelProperty(index = 5)
    private String wed;

    @ExcelProperty(index = 6)
    private String thu;

    @ExcelProperty(index = 7)
    private String fri;

    @ExcelProperty(index = 8)
    private String sat;


}
