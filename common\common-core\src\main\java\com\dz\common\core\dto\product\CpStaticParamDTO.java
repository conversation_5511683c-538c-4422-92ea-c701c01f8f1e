package com.dz.common.core.dto.product;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CpStaticParamDTO extends BaseDTO {

    @ApiModelProperty(value = "商品编码")
    private String cpCode;

    @ApiModelProperty(value = "销售时间开始")
    private Date sellDateStart;

    @ApiModelProperty(value = "销售时间结束")
    private Date sellDateEnd;
}
