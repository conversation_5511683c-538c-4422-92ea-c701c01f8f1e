package com.dz.ms.basic.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.basic.MpMsgSubscribeUserDTO;
import com.dz.common.core.dto.basic.SubscribeMsgSendApiDTO;
import com.dz.common.core.dto.basic.SubscribeMsgSendDTO;
import com.dz.common.core.dto.wechat.mpmsg.SubscribeMsgKeywordDTO;
import com.dz.common.core.dto.wechat.mpmsg.SubscribeMsgTemplateDTO;
import com.dz.common.core.dto.wechat.mpmsg.SubscribeMsgTemplateGetDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.basic.MpMsgFeignClient;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.dto.MpMsgDTO;
import com.dz.ms.basic.dto.MpMsgKeywordDTO;
import com.dz.ms.basic.service.MpMsgPushService;
import com.dz.ms.basic.service.MpMsgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags="小程序及公众号模板消息")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class MpMsgController implements MpMsgFeignClient {

    @Resource
    private MpMsgService mpMsgService;
    @Resource
    private MpMsgPushService mpMsgPushService;

    @ApiOperation("获取小程序订阅消息模板库列表")
    @GetMapping(value = "/crm/subscribe_msg/template/list")
    public Result<PageInfo<SubscribeMsgTemplateDTO>> getSubscribeMsgTemplateList(@RequestParam(value = "pageNo",required = false)Integer pageNo, @RequestParam(value = "pageSize",required = false)Integer pageSize) {
        Result<PageInfo<SubscribeMsgTemplateDTO>> result = new Result<>();
        result.setData(mpMsgService.getSubscribeMsgTemplateList(pageNo,pageSize));
        return result;
    }

    @ApiOperation("获取小程序订阅消息模板关键词库")
    @GetMapping(value = "/crm/subscribe_msg/template/keyword")
    public Result<List<SubscribeMsgKeywordDTO>> getSubscribeMsgKeyword(@RequestParam("templateCode")String templateCode) {
        Result<List<SubscribeMsgKeywordDTO>> result = new Result<>();
        result.setData(mpMsgService.getSubscribeMsgKeyword(templateCode));
        return result;
    }

    @ApiOperation("获取帐号下已添加的模板列表")
    @GetMapping(value = "/crm/subscribe_msg/template/add_list")
    public Result<List<SubscribeMsgTemplateGetDTO>> getAddSubscribeMsgTemplateList() {
        Result<List<SubscribeMsgTemplateGetDTO>> result = new Result<>();
        result.setData(mpMsgService.getAddSubscribeMsgTemplateList());
        return result;
    }

    @ApiOperation("分页查询小程序订阅消息模板配置")
	@GetMapping(value = "/crm/subscribe_msg/list")
    public Result<PageInfo<MpMsgDTO>> getSubscribeMsgList(@ModelAttribute MpMsgDTO param) {
        Result<PageInfo<MpMsgDTO>> result = new Result<>();
		PageInfo<MpMsgDTO> page = mpMsgService.getSubscribeMsgList(param);
        result.setData(page);
        return result;
    }

    @ApiOperation("不分页查询小程序订阅消息模板配置")
    @GetMapping(value = "/crm/subscribe_msg/listAll")
    public Result<List<MpMsgDTO>> getSubscribeMsgAllList() {
        Result<List<MpMsgDTO>> result = new Result<>();
        List<MpMsgDTO> page = mpMsgService.getSubscribeMsgListAll();
        result.setData(page);
        return result;
    }

    @SysLog(value = "新增小程序订阅消息模板配置",type = LogType.OPERATELOG)
    @ApiOperation("新增小程序订阅消息模板配置")
    @PostMapping(value = "/crm/subscribe_msg/add")
    public Result<Long> addSubscribeMsg(@RequestBody MpMsgDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,true);
        Long id = mpMsgService.saveSubscribeMsg(param);
        result.setData(id);
        return result;
    }

    @SysLog(value = "更新小程序订阅消息模板配置",type = LogType.OPERATELOG)
    @ApiOperation("更新小程序订阅消息模板配置")
    @PostMapping(value = "/crm/subscribe_msg/update")
    public Result<Long> updateSubscribeMsg(@RequestBody MpMsgDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,false);
        mpMsgService.saveSubscribeMsg(param);
        result.setData(param.getId());
        return result;
    }

    /**
     * 验证保存参数
     * @param param
     * @param isAdd
     */
    private void validationSaveParam(MpMsgDTO param,boolean isAdd) {
        if(isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if(!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
        if(StringUtils.isEmpty(param.getTemplateCode())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "模板编号不能为空");
        }
        if(StringUtils.isEmpty(param.getTemplateName())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "模板编号名称不能为空");
        }
        if(CollectionUtils.isEmpty(param.getKeywordList())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "关键词列表不能为空");
        }
        for (MpMsgKeywordDTO keyword : param.getKeywordList()) {
            if(null == keyword.getKeyId()) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "关键词ID不能为空");
            }
            if(null == keyword.getSort()) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "关键词排序不能为空");
            }
            if(StringUtils.isEmpty(keyword.getKeyName())) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "关键词名称不能为空");
            }
            if(StringUtils.isEmpty(keyword.getKeyType())) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "关键词类型不能为空");
            }
            if(null == keyword.getIsEdit()) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "是否可编辑选项不能为空");
            }
            if(keyword.getIsEdit().equals(1) && StringUtils.isEmpty(keyword.getEditText())) {
                throw new BusinessException(ErrorCode.BAD_REQUEST, "关键词编辑内容不能为空");
            }
        }
    }

    @ApiOperation("根据ID查询小程序订阅消息模板配置")
    @GetMapping(value = "/crm/subscribe_msg/info")
    public Result<MpMsgDTO> getSubscribeMsgById(@RequestParam("id") Long id) {
        Result<MpMsgDTO> result = new Result<>();
        MpMsgDTO mpMsg = mpMsgService.getSubscribeMsgById(id);
        result.setData(mpMsg);
        return result;
    }

    @ApiOperation("根据ID删除小程序订阅消息模板配置")
    @PostMapping(value = "/crm/subscribe_msg/delete")
    public Result<Boolean> deleteSubscribeMsgById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        mpMsgService.deleteSubscribeMsgById(param);
        result.setData(true);
        return result;
    }

    @ApiOperation(value = "发送小程序订阅消息")
    @PostMapping(value = "/subscribe_msg/send")
    public Result<Boolean> sendMiniappSubscribeMsg(@RequestBody SubscribeMsgSendDTO param, @RequestParam("tenantId") Long tenantId) {
        Result<Boolean> result = new Result<Boolean>();
        mpMsgService.sendSubscribeMsg(param,tenantId);
        result.setData(true);
        return result;
    }

    @ApiOperation(value = "发送小程序订阅消息")
    @PostMapping(value = "/subscribe_msg/send/noCatch")
    public Result<Boolean> sendSubscribeMsgCatch(@RequestBody SubscribeMsgSendDTO param, @RequestParam("tenantId") Long tenantId) {
        Result<Boolean> result = new Result<Boolean>();
        mpMsgService.sendSubscribeMsgCatch(param,tenantId);
        result.setData(true);
        return result;
    }

    @ApiOperation(value = "外部接口发送小程序订阅消息")
    @PostMapping(value = "/subscribe_msg/send_out")
    public Result<Boolean> sendMiniappSubscribeMsgOut(@RequestBody SubscribeMsgSendApiDTO param, @RequestParam("tenantId") Long tenantId) {
        Result<Boolean> result = new Result<Boolean>();
        mpMsgService.sendSubscribeMsgOut(param,tenantId);
        result.setData(true);
        return result;
    }

    @ApiOperation(value = "查询已订阅人群")
    @GetMapping(value = "/subscribe_msg/subscribe_user")
    public Result<List<MpMsgSubscribeUserDTO>> getSubscribeUser(@RequestParam("msgCode") String msgCode) {
        Result<List<MpMsgSubscribeUserDTO>> result = new Result<>();
        List<MpMsgSubscribeUserDTO> list = mpMsgService.getSubscribeUser(msgCode);
        result.setData(list);
        return result;
    }

}
