package com.dz.common.core.fegin.sales;


import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/7
 */
@FeignClient(name = ServiceConstant.SALES_SERVICE_NAME, contextId = "CampaignFeignClient")
public interface CampaignFeignClient {

    /**
     * 删除当前用户的活动数据（优先调用）
     *
     * @return
     */
    @PostMapping(value = "/remote/campaign/current_user/del_data")
    public Result<Boolean> delCurrentUserCampaignData(@RequestParam("uid") Long uid);

    @PostMapping(value = "/remote/couponUser/buyer")
    public Result<Void> CampaignBuyerJob(@RequestParam("campaignCode") String jobParams);

    @PostMapping(value = "/remote/couponUser/buyer/pushMsg")
    public Result<Void> CampaignBuyerPushMsgJob(@RequestParam("campaignCode") String jobParams);
}
