package com.dz.ms.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 修改密码入参DTO
 * @author: Handy
 * @date:   2022/1/30 23:04
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "修改密码入参")
public class UpdatePasswordDTO {

    @ApiModelProperty(value = "账号")
    private String username;
    @ApiModelProperty(value = "密码")
    private String oldPassword;
    @ApiModelProperty(value = "密码")
    private String newPassword;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;

}
