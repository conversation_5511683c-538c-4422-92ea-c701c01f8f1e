<template>
  <a-form-item label="开启悬浮窗" name="data.openFloat">
    <a-switch :unCheckedValue="0" :checkedValue="1" v-model:checked="data.openFloat" @change="emit('ok', data)" />
  </a-form-item>
  <a-form-item label="悬浮窗设置" v-if="data.openFloat">
    <a-button @click="showModal" :disabled="disabled">前往设置</a-button>
  </a-form-item>
  <a-modal title="设置悬浮窗" width="1000" centered :maskClosable="false" :closable="true" :open="visible" @cancel="visible=false">
    <div class="box">
      <div class="header-title">样式设置</div>
      <a-form-item label="尺寸" :labelCol="{width:'50px'}">
        <a-space>
          <a-form-item>
            <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.width" addon-before="宽" addon-after="px"></a-input-number>
          </a-form-item>
          <a-form-item>
            <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.height" addon-before="高" addon-after="px"></a-input-number>
          </a-form-item>
        </a-space>
      </a-form-item>
      <a-form-item label="定位" :labelCol="{width:'50px'}">
        <a-radio-group v-model:value="data.horizontalType">
          <a-radio :value="1">右下</a-radio>
          <a-radio :value="2">左下</a-radio>
        </a-radio-group>

      </a-form-item>
      <a-form-item>
        <a-space>
          <a-form-item v-if="data.horizontalType==1">
            <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.right" addon-before="距右" addon-after="px"></a-input-number>
          </a-form-item>
          <a-form-item v-else>
            <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.left" addon-before="距左" addon-after="px"></a-input-number>
          </a-form-item>
          <a-form-item>
            <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.bottom" addon-before="距底" addon-after="px"></a-input-number>
          </a-form-item>
        </a-space>
      </a-form-item>
      <a-form-item label="多个间距">
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.space" addon-after="px"></a-input-number>
      </a-form-item>
      <a-form-item label="支持拖动">
        <a-switch :unCheckedValue="0" :checkedValue="1" v-model:checked="data.drag" />
      </a-form-item>
      <a-form-item label="展现方式">
        <a-radio-group v-model:value="data.showType">
          <a-radio :value="1">仅图</a-radio>
          <a-radio :value="2">图+名称</a-radio>
        </a-radio-group>
      </a-form-item>
      <div class="header-title">内容</div>
      <a-form-item>
        <a-button block :disabled="data.floatList.length>=5" @click="addModalData">+ 添加悬浮窗</a-button>
      </a-form-item>
      <div class="float">
        <VueDraggable :list="data.floatList" item-key="id" :animation="300">
          <template #item="{ element: item, index:i }">
            <div class="float-item">
              <a-space>
                <HolderOutlined />
                <div class="float-content">
                  <a-form-item label="悬浮窗名称">
                    <a-input placeholder="请输入" v-model:value="item.name" maxlength="10" showCount></a-input>
                  </a-form-item>
                  <a-form-item label="悬浮图">
                    <uploadImg :max="10" :width="100" :height="100" :imgUrl="item.imgUrl" :form="data" :path="'floatList.'+i+'.imgUrl'" :disabled="disabled" @success="uploadSuccess" />
                  </a-form-item>
                  <a-form-item label="跳转链接">
                    <addLink type="2" :isEvent="false" :showType="[1,3,7,8,6]" :links="item.imgLinks" :components="components" @ok="(link)=>item.imgLinks=link">
                      <a-button block>热区设置</a-button>
                    </addLink>
                  </a-form-item>
                </div>
              </a-space>
              <delete-outlined class="float-delete" size="mini" @click.stop="data.floatList.splice(i,1)" />
            </div>
          </template>
        </VueDraggable>
      </div>
    </div>

    <template #footer v-if="!disabled">
      <a-space>
        <a-button @click="visible=false">取消</a-button>
        <a-button type="primary" @click="ok">确定</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup>
import { grade_infoLista, crowdList } from '@/http'
import { message } from "ant-design-vue";
import { cloneDeep, set } from 'lodash'
import { v4 as uuidv4 } from "uuid";
// 组件的公共参数
import { operateTypes } from "@/utils/fixedVariable.js";
const img = ref(null)
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  addParams: {
    type: Object,
    default() {
      return {}
    }
  },
  components: {
    type: Array,
    default() {
      return []
    }
  },

})


const { visible, data } = toRefs(reactive({
  visible: false,
  data: cloneDeep(props.addParams)

}))

watch(() => props.addParams, (val) => {
  data.value = cloneDeep(val)
})
// 点击显示弹窗
const showModal = () => {
  if (props.disabled) return
  visible.value = true
}

// 上传图片 视频
const uploadSuccess = async (data) => {
  let { form, path, imgUrl } = data;
  console.log(data)
  set(form, path, imgUrl)
}


// 添加数据
const addModalData = () => {
  data.value.floatList.push({
    id: uuidv4(),
    name: '',
    imgUrl: '',
    imgLinks: []
  })
}
// 保存设置
const ok = () => {
  visible.value = false
  emit('ok', data.value)
}
</script>

<style  scoped lang="scss">
.box {
  max-height: 500px;
  padding-right: 20px;
  overflow-y: auto;
}
.float {
  &-item {
    background: #efefef;
    padding: 10px;
    margin-bottom: 10px;
    position: relative;
  }
  &-delete {
    position: absolute;
    padding: 10px;
    right: 0;
    top: 0;
  }
}
</style>
