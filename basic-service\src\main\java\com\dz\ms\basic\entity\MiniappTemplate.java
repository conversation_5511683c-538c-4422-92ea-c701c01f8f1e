package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 小程序页面模板
 * @author: Handy
 * @date:   2022/07/26 18:05
 */
@Getter
@Setter
@NoArgsConstructor
@Table("小程序页面模板")
@TableName(value = "miniapp_template")
public class MiniappTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = true,comment = "模板类型 1常规页面 2系统页面 3定制页面 4弹窗页面 5开屏页面")
    private Integer templateType;
    @Columns(type = ColumnType.INT,length = 0,isNull = true,comment = "页面类型 0默认 1首页 2会员中心 3会员码 4生活圈 5更多 6活动开屏 7首页开屏")
    private Integer pageType;
    @Columns(type = ColumnType.VARCHAR,length = 32,isNull = true,comment = "模板名称")
    private String templateName;
    @Columns(type = ColumnType.LONGTEXT,length = 0,isNull = true,comment = "模板内容json")
    private String content;
    @Columns(type = ColumnType.LONGTEXT,length = 0,isNull = true,comment = "模板内容预览json")
    private String pageJson;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = true,comment = "是否发布 0否1是")
    private Integer publish;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = true,comment = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "发布时间")
    private Date pushTime;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = true,comment = "页面组ID")
    private Long groupId;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = true,comment = "页面路径id")
    private Long pathId;
    @Columns(type = ColumnType.TEXT,length = 0,isNull = true,comment = "模版编号")
    private String templateCode;

    public MiniappTemplate(Long id, Integer templateType, String templateName, String content, String pageJson, Integer publish) {
        this.id = id;
        this.templateType = templateType;
        this.templateName = templateName;
        this.content = content;
        this.pageJson = pageJson;
        this.publish = publish;
    }

}
