<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dz.ms.user.mapper.AppointmentSlotConfigMapper">


    <!-- 新增SQL：按startTime正序查询指定日期的AppointmentSlotConfig列表 -->
    <select id="findByAppointmentIdAndAppointmentDateOrderByStartTime"
            resultType="com.dz.ms.user.entity.AppointmentSlotConfig">
        SELECT *
        FROM appointment_slot_config
        WHERE appointment_id = #{appointmentId}
          AND date = #{date}
        ORDER BY start_time ASC
    </select>
</mapper>