const app = getApp();
import { taskType, taskBtnType, taskBtnTrack } from "../../utils/contants";
import {
  taskRecords,
  taskSuccessRecordCardButton,
  taskSuccessRecordCardButtonCheck,
} from "../../api/index";
// import dayjs from "../../utils/dayjs.min";
const dayjs = require("../../utils/dayjs.min");

Component({
  options: {
    styleIsolation: "apply-shared",
  },
  properties: {
    taskInfo: {
      type: Object,
      value: {},
      observer(val) {
        this.handleData(val);
      },
    },
    // 游客模式
    visitor: {
      type: Boolean,
      value:false
    },
    type: {
      // 卡片类型  1待完成卡片   2已完成卡片   3过期卡片
      type: String,
      value: undefined,
    },
    // 组件所在页面
    origin: {
      type: String,
      value: "",
    },
  },
  onLoad(options) {},
  onShow() {},
  /**
   * 组件的初始数据
   */
  data: {
    taskType,
    taskBtnType,
    taskData: {},
    totalPoints: 0,
    isShowCoupon: false, // 是否任务列表都是券的场景
    isShowRuleDiaglog: false,
    currentRuleImg: "",
    showModalDialog: false,
    modal: {},
  },
  /**
   * 组件的方法列表
   */
  methods: {
    handleData(val) {
      let total = 0;

      const { taskRewardList, allReadyNum, ...d } = val;
      // 任务列表是否都是券
      // rewardType 1-优惠券  2-积分
      const isShowCoupon = taskRewardList.every(
        (item) => item.rewardType === 1
      );
      // 但是阶梯性任务，需要在数据里标注完成状态
      // readyCycle 1.一次性   2.周期   3.周期-阶梯
      if (val.readyCycle === 3) {
        d.taskRewardList = val.taskRewardList.map((item, index) => {
          const data = item;
          if (index < allReadyNum) {
            data.complated = true;
          } else {
            data.complated = false;
          }
          if (item.rewardType === 2) {
            total += item.pointsNum;
          }
          data.id = index;
          return data;
        });
      } else {
        // 如果是非阶梯任务，需要前端根据readNum数据造相应长度的数组，根据allReadyNum数据判断完成了几个任务
        d.taskRewardList = taskRewardList;
        d.isCycleTask = new Array(d.readyNum)
          .fill(false)
          .map((_, index) => index < allReadyNum);
      }
      // isTimeRestrict 1-限时 2-不限时
      if (val.isTimeRestrict === 1) {
        d.timeTxt = `${dayjs(val.restrictTimeStart).format(
          "YYYY.MM.DD"
        )} - ${dayjs(val.restrictTimeEnd).format("YYYY.MM.DD")}`;
      }
      this.setData({
        taskData: d,
        totalPoints: total,
        isShowCoupon,
      });
    },
    showRuleDialog: app.debounce(async function (e) {
      const { img } = e.currentTarget.dataset;
      if (!img) return;
      this.triggerEvent("showDialog", {
        img,
      });
    }),
    handleGo: app.debounce(async function (e) {
      const { origin } = this.data;
      const { task, id, item } = e.currentTarget.dataset;
      wx.$mp.track({
        event: origin === "home" ? "home_task_btn" : "task_list_click",
        props: {
          taskId: id,
          type: taskBtnTrack[task],
        },
      });
      await taskRecords({
        taskId: id,
      });
      // 需要注册
      if (app.ifRegister()) {
        if (task === 4) {
          this.triggerEvent("share");
          return;
        }
        app.subscribe("task").then(() => {
          switch (task) {
            case 1: // 线下打卡
              // 地理定位授权
              app.autoAuthLocation().then(async ({ latitude, longitude }) => {
                let { data } = await taskSuccessRecordCardButtonCheck({
                  latitude,
                  longitude,
                  taskId: id,
                });
                // cardStatus 1-打卡成功 2-门店不在百米范围内 3-超过门店可打卡次数
                // checkClocNow    当天是否已打卡上限：1是，2否

                // 判断tip提示内容
                // readyDay 每多少天
                // readyMonth 每多少月
                // 最多多少次

                let tip = "";
                // readyCycle 1.一次性   2.周期   3.周期-阶梯
                if (item.readyCycle == 1) {
                  // tip = `*每天只可打卡${item.limitationNum}次\n*任务最多可打卡${item.readyNum}次`;
                  tip = `*每天只可打卡${item.limitationNum}次`;
                } else if (item.readyCycle == 2) {
                  if (item.reayType == "readyDay") {
                    // tip = `*每天只可打卡${item.limitationNum}次\n*每${item.readyDay}天每次任务最多打卡${item.readyNum}次`;
                    tip = `*每天只可打卡${item.limitationNum}次`;
                  } else if (item.reayType == "readyMonth") {
                    // tip = `*每天只可打卡${item.limitationNum}次\n*每${item.readyMonth}月每次任务最多打卡${item.readyNum}次`;
                    tip = `*每天只可打卡${item.limitationNum}次`;
                  }
                } else if (item.readyCycle == 3) {
                  if (item.reayType == "readyDay") {
                    // tip = `*每天只可打卡${item.limitationNum}次\n*每${item.readyDay}天每次任务最多打卡${item.totalReadyNum}次`;
                    tip = `*每天只可打卡${item.limitationNum}次`;
                  } else if (item.reayType == "readyMonth") {
                    // tip = `*每天只可打卡${item.limitationNum}次\n*每${item.readyMonth}月每次任务最多打卡${item.totalReadyNum}次`;
                    tip = `*每天只可打卡${item.limitationNum}次`;
                  }
                }
                // 今天已经打卡
                if (item.checkClocNow == 1) {
                  this.setData({
                    showModalDialog: true,
                    modal: {
                      title: "您今日已完成打卡\n明日再来吧",
                      confirmText: "我知道了",
                      store: item.clocStoreNameNow,
                      icon: "/store/success1.png",
                      tip,
                      taskId: id,
                      item,
                      clocStoreNameNow: item.clocStoreNameNow,
                    },
                  });
                } else if (data.cardStatus == 3) {
                  // 门店上限
                  this.setData({
                    showModalDialog: true,
                    modal: {
                      title: "门店已达到上限",
                      confirmText: "我知道了",
                      store: data.clocStoreNameNow,
                      icon: "/store/success1.png",
                      tip,
                      taskId: id,
                      item,
                      clocStoreNameNow: data.clocStoreNameNow,
                    },
                  });
                } else if (data.cardStatus == 2) {
                  // 不在100m范围内
                  this.setData({
                    showModalDialog: true,
                    modal: {
                      title: "您需要在门店范围100米内\n即可打卡",
                      icon: "/store/distance1.png",
                      store: data.clocStoreNameNow,
                      cancelText: "我知道了",
                      confirmText: "附近门店",
                      tip,
                      taskId: id,
                      item,
                      clocStoreNameNow: data.clocStoreNameNow,
                    },
                  });
                } else {
                  // 可以打卡
                  this.setData({
                    showModalDialog: true,
                    modal: {
                      title: "欢迎您来到MUJI门店\n快去打卡吧",
                      confirmText: "立即打卡",
                      store: data.clocStoreNameNow,
                      icon: "/store/store1.png",
                      tip,
                      taskId: id,
                      item,
                      clocStoreNameNow: data.clocStoreNameNow,
                    },
                  });
                }
              });
              break;
            case 2: // 兑礼任务前往积分商城
              wx.$mp.switchTab({
                url: "/pages/life/life?rightFixedWrapVisible=true",
              });
              break;
            case 3: // 线下消费
              wx.$mp.navigateTo({
                url: "/pages/nearbyOutlets/nearbyOutlets",
              });
              break;
            case 4: //  邀请好友
              // 在页面加载时就打开分享菜单
              this.triggerEvent("share");
              break;
            case 5: // 首次购买
              wx.$mp.navigateTo({
                url: "/pages/nearbyOutlets/nearbyOutlets",
              });
              break;
          }
        });
      }
    }),
    close() {
      this.setData({ showModalDialog: false });
    },
    confirm: app.debounce(async function (e) {
      let { modal } = this.data;
      let { userLatitude: latitude, userLongitude: longitude } = app.globalData;
      console.log(modal);
      if (modal.confirmText == "立即打卡") {
        taskSuccessRecordCardButton({
          latitude,
          longitude,
          taskId: modal.taskId,
        }).then((res) => {
          // readyDay 每多少天
          // readyMonth 每多少月
          // 最多多少次
          let tip = "";
          let item = modal;
          if (item.readyCycle == 1) {
            // tip = `*每天只可打卡${item.limitationNum}次\n*任务最多可打卡${item.readyNum}次`;
            tip = `*每天只可打卡${item.limitationNum}次`;
          } else if (item.readyCycle == 2) {
            if (item.reayType == "readyDay") {
              // tip = `*每天只可打卡${item.limitationNum}次\n*每${item.readyDay}天每次任务最多打卡${item.readyNum}次`;
              tip = `*每天只可打卡${item.limitationNum}次`;
            } else if (item.reayType == "readyMonth") {
              // tip = `*每天只可打卡${item.limitationNum}次\n*每${item.readyMonth}月每次任务最多打卡${item.readyNum}次`;
              tip = `*每天只可打卡${item.limitationNum}次`;
            }
          } else if (item.readyCycle == 3) {
            if (item.reayType == "readyDay") {
              // tip = `*每天只可打卡${item.limitationNum}次\n*每${item.readyDay}天每次任务最多打卡${item.totalReadyNum}次`;
              tip = `*每天只可打卡${item.limitationNum}次`;
            } else if (item.reayType == "readyMonth") {
              // tip = `*每天只可打卡${item.limitationNum}次\n*每${item.readyMonth}月每次任务最多打卡${item.totalReadyNum}次`;
              tip = `*每天只可打卡${item.limitationNum}次`;
            }
          }
          this.setData({
            showModalDialog: true,
            modal: {
              title: "恭喜您\n打卡成功",
              confirmText: "我知道了",
              store: modal.clocStoreNameNow,
              icon: "/store/finish1.png",
              tip,
            },
          });
          app.getUserInfo();
        });
      } else if (modal.confirmText == "附近门店") {
        this.setData({ showModalDialog: false });
        wx.$mp.navigateTo({
          url: "/pages/nearbyOutlets/nearbyOutlets",
        });
      } else {
        this.triggerEvent("update");
        this.setData({ showModalDialog: false });
      }
    }, 2000),
  },
});
