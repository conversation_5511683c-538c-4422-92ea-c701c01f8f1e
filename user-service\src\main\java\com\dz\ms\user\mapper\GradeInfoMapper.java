package com.dz.ms.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.user.dto.GradeInfoDTO;
import com.dz.ms.user.entity.GradeInfo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 会员等级Mapper
 * @author: zlf
 * @date:   2023/02/14 17:44
 */
@Repository
public interface GradeInfoMapper extends BaseMapper<GradeInfo> {

    /**
     * 根据等级code或名称查询会员等级列表
     * @param param 查询条件入参
     * @return List<GradeInfoDTO>
     */
    List<GradeInfoDTO> selectByCodeOrName(GradeInfoDTO param);

}
