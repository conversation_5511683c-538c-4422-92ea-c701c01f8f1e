<template>
  <a-collapse collapsible="disabled" expandIconPosition="end" v-model:activeKey="activeKey" :bordered="false" style="background: rgb(255, 255, 255)">
    <a-collapse-panel :collapsible="false" :showArrow="false" key="1" header="页面布局">
      <!--  @click="active(-1)" -->
      <div class="list-item" :class="index==-1?'active':''" style="padding-left:20px;line-height:32px" v-if="pageType!=4" @click="active(-1)">顶栏设置</div>
      <VueDraggable :list="list" item-key="id" :animation="300">
        <template #item="{ element: item, index:i }">
          <div class="list-item" :class="index==i?'active':''" @click="active(i)">
            <a-space>
              <HolderOutlined />
              <a-input v-model:value="item.text" v-if="currentIndex==i" @blur="ok"></a-input>
              <span v-else>{{ item.text }}</span>
            </a-space>
            <a-dropdown>
              <template #overlay>
                <a-menu @click="active(i)">
                  <a-menu-item key="1">
                    <a-button type="link" @click="copy(i)">复制</a-button>
                  </a-menu-item>
                  <a-menu-item key="2">
                    <a-button type="link" @click="reName(i)">重命名</a-button>
                  </a-menu-item>
                  <a-menu-item key="3">
                    <a-popconfirm title="是否确定删除该组件？" @confirm="handleDelete(i)">
                      <a-button type="link">删除</a-button>
                    </a-popconfirm>
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button :type="index==i?'primary':'link'">
                <SmallDashOutlined />
              </a-button>
            </a-dropdown>
          </div>
        </template>
      </VueDraggable>
    </a-collapse-panel>

  </a-collapse>
</template>
<script setup>
const emit = defineEmits(['ok', 'cancel', 'changeActive'])
import { cloneDeep } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  // 当前激活下标
  index: {
    type: Number,
    default: 0
  },
  // 页面类型  1-常规页 2-加载页 3-开屏页 4-弹窗
  pageType: {
    type: String || Number,
    default: 1,
  },
  // 组件数组
  components: {
    type: Array,
    default() {
      return []
    }
  },
})


const { activeKey, currentIndex } = toRefs(reactive({
  activeKey: [1],
  currentIndex: props.index
}))

watch(() => props.index, (value) => {
  currentIndex.value = value
})


// 操作数组
const list = computed(() => {
  return props.components
})

const active = (i) => {
  emit('changeActive', i)
}
const ok = () => {
  currentIndex.value = -1
  emit('ok', list.value)
}

// 重命名
const reName = (i) => {
  currentIndex.value = i;
}


// 复制
const copy = (i) => {
  list.value.push({
    ...cloneDeep(list.value[i]),
    id: uuidv4(),
  })
  ok()
}

// 删除
const handleDelete = (i) => {
  list.value.splice(i, 1)
  ok()
}

</script>

<style scoped lang="scss">
.list {
  &-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 3px 0;
    &.active {
      background: $colorPrimary;
      color: #fff;
    }
  }
}
</style>
