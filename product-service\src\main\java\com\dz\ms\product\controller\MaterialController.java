package com.dz.ms.product.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.product.dto.MaterialDTO;
import com.dz.ms.product.service.MaterialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "商品素材表")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class MaterialController {

    @Resource
    private MaterialService materialService;

    /**
     * 分页查询商品素材表
     *
     * @param param
     * @return result<PageInfo < MaterialDTO>>
     */
    @ApiOperation("分页查询商品素材表")
    @GetMapping(value = "/material/list")
    public Result<PageInfo<MaterialDTO>> getMaterialList(@ModelAttribute MaterialDTO param) {
        Result<PageInfo<MaterialDTO>> result = new Result<>();
        PageInfo<MaterialDTO> page = materialService.getMaterialList(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询商品素材表
     *
     * @param id
     * @return result<MaterialDTO>
     */
    @ApiOperation("根据ID查询商品素材表")
    @GetMapping(value = "/material/info")
    public Result<MaterialDTO> getMaterialById(@RequestParam("id") Long id) {
        Result<MaterialDTO> result = new Result<>();
        MaterialDTO material = materialService.getMaterialById(id);
        result.setData(material);
        return result;
    }

    /**
     * 新增商品素材表
     *
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增商品素材表", type = LogType.OPERATELOG)
    @ApiOperation("新增商品素材表")
    @PostMapping(value = "/material/add")
    public Result<Long> addMaterial(@RequestBody MaterialDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param, true);
        Long id = materialService.saveMaterial(param);
        result.setData(id);
        return result;
    }

    /**
     * 更新商品素材表
     *
     * @param param
     * @return result<Object>
     */
    @SysLog(value = "更新商品素材表", type = LogType.OPERATELOG)
    @ApiOperation("更新商品素材表")
    @PostMapping(value = "/material/update")
    public Result<Long> updateMaterial(@RequestBody MaterialDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param, false);
        materialService.saveMaterial(param);
        result.setData(param.getId());
        return result;
    }

    /**
     * 验证保存参数
     *
     * @param param
     */
    private void validationSaveParam(MaterialDTO param, boolean isAdd) {
        if (isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if (!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
    }

    /**
     * 根据ID删除商品素材表
     *
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除商品素材表")
    @PostMapping(value = "/material/delete")
    public Result<Boolean> deleteMaterialById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        materialService.deleteMaterialById(param);
        result.setData(true);
        return result;
    }

}
