package com.dz.ms.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 会员等级权益关系
 * @author: Handy
 * @date:   2023/08/07 17:44
 */
@Getter
@Setter
@NoArgsConstructor
@Table("会员等级权益关系")
@TableName(value = "grade_benefit")
public class GradeBenefit implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "关系ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "会员等级ID")
    private Long gradeId;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "会员权益ID")
    private Long benefitId;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = false, comment = "关系类型 1已激活权益 2未激活权益",defaultValue = "1")
    private Integer relationType;
    @Columns(type = ColumnType.INT,length = 10,isNull = true,defaultValue = "0",comment = "权益排序")
    private Integer sort;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.TEXT,length = 0,isNull = true,comment = "跳转链接配置JSON")
    private String jumpLink;
    @Columns(type = ColumnType.VARCHAR,length = 256,isNull = true,comment = "弹窗图片")
    private String popupImg;
    @Columns(type = ColumnType.VARCHAR,length = 50,isNull = true,comment = "埋点编码")
    private String code;
    public GradeBenefit(Long id, Long gradeId, Long benefitId, Integer relationType) {
        this.id = id;
        this.gradeId = gradeId;
        this.benefitId = benefitId;
        this.relationType = relationType;
    }

}
