package com.dz.ms.basic.dto;
import java.util.Date;
import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 公众号/小程序配置DTO
 * @author: Handy
 * @date:   2022/01/28 15:35
 */
@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "公众号/小程序配置")
public class MpConfigDTO extends BaseDTO {

    @ApiModelProperty(value = "公众号/小程序配置ID")
    private Long id;
    @ApiModelProperty(value = "类型 1小程序 2公众号")
    private Integer appType;
    @ApiModelProperty(value = "公众号/小程序名称")
    private String appName;
    @ApiModelProperty(value = "代码版本号")
    private String version;
    @ApiModelProperty(value = "版本简介")
    private String versionDesc;
    @ApiModelProperty(value = "代码上传状态 0未提交 1已提交 2提交失败 3审核成功 4审核失败 5已发布")
    private Integer codeStatus;
    @ApiModelProperty(value = "审核失败原因")
    private String failReason;
    @ApiModelProperty(value = "公众号/小程序appID")
    private String appId;
    @ApiModelProperty(value = "公众号/小程序秘钥")
    private String appSecret;
    @ApiModelProperty(value = "行业标签 最多10个，空格隔开")
    private String label;
    @ApiModelProperty(value = "接口调用凭据刷新令牌")
    private String refreshToken;
    @ApiModelProperty(value = "授权方认证类型,-1代表未认证，0代表微信认证")
    private Integer verifyTypeInfo;
    @ApiModelProperty(value = "小程序的原始ID")
    private String userName;
    @ApiModelProperty(value = "小程序授权给开发者的权限集列表")
    private String funcInfo;
    @ApiModelProperty(value = "授权状态 0未授权 1已授权 2解除授权")
    private Integer authStatus;
    @ApiModelProperty(value = "是否独立发版 0否 1是")
    private Integer alone;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "更新人")
    private Long modifier;
    @ApiModelProperty(value = "更新时间")
    private Date modified;

}
