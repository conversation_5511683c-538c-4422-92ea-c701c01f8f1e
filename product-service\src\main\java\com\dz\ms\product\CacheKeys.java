package com.dz.ms.product;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/7/16
 */
public class CacheKeys {

    /** 礼遇活动MAP */
    public static final String COUPON_MAP = "coupon:map";

    /** 礼遇活动MAP */
    public static final String COUPON_CODE = "coupon:code:";

    /** 礼遇活动MAP */
    public static final String COUPON_CDP_CODE = "coupon:cdp:code:";


    /** 礼遇活动MAP */
    public static final String COUPON_ID = "coupon:id:";

    /** 无需预约的服务项目 */
    public static final String SERVE_ITEM_NO_BOOKING = "serveitem:nobooking:";

    /** 
     * 锁Keys 
     */
    public static class Locks {
        //删除
        public static final String SHELF_DELETE = "shelf_delete:";
        public static final String SHELF_CAMPAIGN_DELETE = "shelf_campaign_delete:";

        //新增
        public static final String SHELF_ADD = "shelf_add:";
        public static final String CAMPAIGN_ADD = "campaign_add:";
        
        //更新
        public static final String SHELF_UPDATE = "shelf_update:";
        public static final String SHELF_PRODUCT_UPDATE = "shelf_product_update:";
        public static final String CAMPAIGN_UPDATE = "campaign_update:";
        
    }

    public static class shelf {
        //查询
        public static final String SHELF_QUERY_ALL = "shelf:query:all";

        public static final String SHELF_UID = "shelf:uid:";
    }

}
