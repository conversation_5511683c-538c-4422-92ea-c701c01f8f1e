package com.dz.common.core.dto.user;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 券列表
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "已领取券列表")
public class CouponsListAllDTO extends BaseDTO {

    @ApiModelProperty(value = "全部")
    private List<CouponsListDTO> allCouponList;
    @ApiModelProperty(value = "实物券")
    private List<CouponsListDTO> physicalCouponList;
    @ApiModelProperty(value = "优惠券")
    private List<CouponsListDTO> couponList;
}
