package com.dz.ms.user.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.constants.NumConstants;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.adaptor.SftpDTO;
import com.dz.common.core.dto.user.*;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.adaptor.SftpFeginClient;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.SftpUtils;
import com.dz.ms.user.dto.AddressComponentDTO;
import com.dz.ms.user.entity.Serve;
import com.dz.ms.user.entity.Store;
import com.dz.ms.user.entity.StoreServe;
import com.dz.ms.user.mapper.ServeMapper;
import com.dz.ms.user.mapper.StoreMapper;
import com.dz.ms.user.mapper.StoreServeMapper;
import com.dz.ms.user.service.StoreService;
import com.dz.ms.user.utils.MapUtils;
import com.dz.ms.user.utils.Pinyin4jUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.input.BOMInputStream;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.Collator;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 门店
 * @author: yibo
 * @date:   2024/11/19 17:19
 */
@Service
@Slf4j
public class StoreServiceImpl extends ServiceImpl<StoreMapper,Store> implements StoreService {

    @Resource
    private StoreMapper storeMapper;
    @Resource
    private ServeMapper serveMapper;
    @Resource
    private StoreServeMapper storeServeMapper;
    @Resource
    private RestTemplate restTemplate;
    @Value("${map.tencent.suggestion:}")
    private String mapSuggestion;
    @Value("${map.tencent.location:}")
    private String mapLocation;
    @Value("${map.tencent.key:}")
    private String mapKey;
    @Value("${map.tencent.mapSecret:}")
    private String mapSecret;

    @Value("${sftp.config.host:}")
    private String host;
    @Value("${sftp.config.port:}")
    private Integer port;
    @Value("${sftp.config.userName:}")
    private String userName;
    @Value("${sftp.config.password:}")
    private String password;
    @Resource
    private SftpFeginClient sftpFeginClient;

    /**
     * 分页查询门店
     *
     * @param param
     * @return PageInfo<StoreDTO>
     */
    @Override
    public PageInfo<StoreDTO> getStoreList(StoreDTO param) {
        Store store = BeanCopierUtils.convertObjectTrim(param, Store.class);
        QueryWrapper<Store> storeQueryWrapper = new QueryWrapper<>();
        if (param.getStoreSn() != null && !"".equals(param.getStoreSn())) {
            storeQueryWrapper.like("store_sn", param.getStoreSn());
        }
        if (param.getStoreName() != null && !"".equals(param.getStoreName())) {
            storeQueryWrapper.like("store_name", param.getStoreName());
        }
        if (param.getIsUpdate() != null) {
            storeQueryWrapper.eq("is_update", param.getIsUpdate());
        }
        if (param.getType() != null) {
            storeQueryWrapper.eq("type", param.getType());
        }
        //是否有服务
        if (param.getIsStoreServe() != null) {
            List<Long> storeIds = storeServeMapper.selectStoreIds();
            if (!CollectionUtils.isEmpty(storeIds)) {
                if (param.getIsStoreServe() == 2) {
                    storeQueryWrapper.in("id", storeIds);
                } else {
                    storeQueryWrapper.notIn("id", storeIds);
                }
            }
        }
        //关联服务
        if (!CollectionUtils.isEmpty(param.getServeIds())) {
            List<Long> storeIds = storeServeMapper.selectStoreIdsByServeIds(param.getServeIds());
            if (!CollectionUtils.isEmpty(storeIds)) {
                storeQueryWrapper.in("id", storeIds);
            } else {
                return new PageInfo<>();
            }
        }
        //门店图片
        if (param.getIsStoreImage() != null) {
            if (param.getIsStoreImage() == 1) {
                storeQueryWrapper.and(wrapper -> wrapper.isNull("images").or().eq("images", ""));
                //storeQueryWrapper.isNull("images");
            } else {
                storeQueryWrapper.and(wrapper -> wrapper.isNotNull("images").and(w2 -> w2.ne("images", "")));
                //storeQueryWrapper.isNotNull("images");
            }
        }
        //门店二维码
        if (param.getIsWeWorkImage() != null) {
            if (param.getIsWeWorkImage() == 1) {
                storeQueryWrapper.and(wrapper -> wrapper.isNull("wework_images").or().eq("wework_images", ""));
                //storeQueryWrapper.isNull("wework_images");
            } else {
                storeQueryWrapper.and(wrapper -> wrapper.isNotNull("wework_images").and(w2 -> w2.ne("wework_images", "")));
                //storeQueryWrapper.isNotNull("wework_images");
            }
        }
        storeQueryWrapper.orderByAsc("store_sn");
        IPage<Store> page = storeMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), storeQueryWrapper);
        //设置门店服务数
        List<StoreDTO> storeDTOList = BeanCopierUtils.convertList(page.getRecords(), StoreDTO.class);

        if (!CollectionUtils.isEmpty(storeDTOList)) {
            List<Long> serves = storeServeMapper.selectOkServe();
            Date date = new Date();
            // 指定日期格式
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            // 格式化为字符串
            String formattedDate = sdf.format(date);
            //判断开业状态
            if (param.getIsOpen() != null) {
                List<Store> storeList = storeMapper.selectList(storeQueryWrapper);
                List<StoreDTO> storeDTOList1 = BeanCopierUtils.convertList(storeList, StoreDTO.class);
                List<StoreDTO> storeDTOS = new ArrayList<>();
                if (param.getIsOpen() == 1) {
                    for (StoreDTO storeIsOpen : storeDTOList1) {
                        if (storeIsOpen.getOpenDate() != null && !Objects.equals(storeIsOpen.getOpenDate(), "")) {
                            if (Integer.parseInt(storeIsOpen.getOpenDate()) > Integer.parseInt(formattedDate) && storeIsOpen.getIsClose() == 0) {
                                //processStoreDTO(storeIsOpen, serves, storeServeMapper);
                                storeDTOS.add(storeIsOpen);
                            }
                        }
                    }
                }
                if (param.getIsOpen() == 2) {
                    for (StoreDTO storeIsOpen : storeDTOList1) {
                        if (storeIsOpen.getOpenDate() != null && !Objects.equals(storeIsOpen.getOpenDate(), "")) {
                            if (Integer.parseInt(storeIsOpen.getOpenDate()) <= Integer.parseInt(formattedDate) && storeIsOpen.getIsClose() == 0) {
                                //processStoreDTO(storeIsOpen, serves, storeServeMapper);
                                storeDTOS.add(storeIsOpen);
                            }
                        }
                    }
                }
                if (param.getIsOpen() == 3) {
                    for (StoreDTO storeIsOpen : storeDTOList1) {
                        if (storeIsOpen.getOpenDate() != null && !Objects.equals(storeIsOpen.getOpenDate(), "")) {
                            if (storeIsOpen.getIsClose() == 1) {
                                //processStoreDTO(storeIsOpen, serves, storeServeMapper);
                                storeDTOS.add(storeIsOpen);
                            }
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(storeDTOS)) {
                    List<Long> collect = storeDTOS.stream()
                            .map(StoreDTO::getId)
                            .collect(Collectors.toList());
                    QueryWrapper<Store> storeQueryWrapper1 = new QueryWrapper<>();
                    storeQueryWrapper1.in("id", collect);
                    IPage<Store> storePage = storeMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), storeQueryWrapper1);
                    storeDTOS = BeanCopierUtils.convertList(storePage.getRecords(), StoreDTO.class);
                    storeDTOS.forEach(storeDTO -> {
                        storeDTO.setIsOpen(param.getIsOpen());
                        if (storeDTO.getImages() != null && !storeDTO.getImages().equals("")) {
                            storeDTO.setIsStoreImage(2);
                        }
                        if (storeDTO.getWeworkImages() != null && !storeDTO.getWeworkImages().equals("")) {
                            storeDTO.setIsWeWorkImage(2);
                        }
                    });
                    return new PageInfo<>(storePage.getCurrent(), storePage.getSize(), storePage.getTotal(), storeDTOS);
                }
                return new PageInfo<>();
            }


            //无需要筛选开业状态
            for (StoreDTO store1 : storeDTOList) {
                if(store1.getImages() != null && !store1.getImages().equals("")){
                    store1.setIsStoreImage(2);
                }
                if(store1.getWeworkImages() != null && !store1.getWeworkImages().equals("")){
                    store1.setIsWeWorkImage(2);
                }
                if (store1.getOpenDate() != null && !Objects.equals(store1.getOpenDate(), "")) {
                    if (Integer.parseInt(store1.getOpenDate()) >= Integer.parseInt(formattedDate) && store1.getIsClose() == 0) {
                        store1.setIsOpen(1);
                    }
                    if (Integer.parseInt(store1.getOpenDate()) <= Integer.parseInt(formattedDate) && store1.getIsClose() == 0) {
                        store1.setIsOpen(2);
                    }
                    if (store1.getIsClose() == 1) {
                        store1.setIsOpen(3);
                    }
                }
                //processStoreDTO(store1, serves, storeServeMapper);
            }
        }
        return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), storeDTOList);
    }

    /**
     * 根据ID查询门店
     *
     * @param id
     * @return StoreDTO
     */
    @Override
    public StoreDTO getStoreById(Long id) throws ParseException {
        Store store = storeMapper.selectById(id);
        if (store != null && store.getOpenDate() != null && !Objects.equals(store.getOpenDate(), "")) {
            SimpleDateFormat originalSdf = new SimpleDateFormat("yyyyMMdd");
            Date date = originalSdf.parse(store.getOpenDate());
            SimpleDateFormat targetSdf = new SimpleDateFormat("yyyy-MM-dd");
            String formattedDate = targetSdf.format(date);
            store.setOpenDate(formattedDate);
        }
        return BeanCopierUtils.convertObject(store, StoreDTO.class);
    }

    /**
     * 保存门店
     *
     * @param param
     * @return Long
     */
    @Override
    public Long saveStore(StoreDTO param) {
        /*Store store = new Store(param.getId(), param.getStoreSn(), param.getStoreName(), param.getType(), param.getStatus(), param.getProvince(), param.getCity(), param.getArea(), param.getLongitude(), param.getLatitude(), param.getEmail(), param.getPhone(), param.getStoreAddress(), param.getImages(), param.getWeworkImages(), param.getOpeningHour(), param.getIsDefault(), param.getCreateTime(), param.getUpdateTime());
        if(ParamUtils.isNullOr0Long(store.getId())) {
            storeMapper.insert(store);
        }
        else {
            storeMapper.updateById(store);
        }
        return store.getId();*/
        return null;
    }

    /**
     * 根据ID删除门店
     *
     * @param param
     */
    @Override
    public void deleteStoreById(IdCodeDTO param) {
        if (param.getId() != null){
            storeMapper.deleteById(param.getId());
        }
    }

    @Override
    public List<ServeDTO> getStoreServeList(Long storeId) {
        QueryWrapper<Serve> serveQueryWrapper = new QueryWrapper<>();
        serveQueryWrapper.eq("status", 0);
        List<Serve> list = serveMapper.selectList(serveQueryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            List<ServeDTO> serveDTOS = BeanCopierUtils.convertList(list, ServeDTO.class);
            for (ServeDTO serveDTO : serveDTOS) {
                QueryWrapper<StoreServe> storeServiceQueryWrapper = new QueryWrapper<>();
                if (storeId != null){
                    storeServiceQueryWrapper.eq("store_id", storeId);
                }
                if (serveDTO.getId() != null){
                    storeServiceQueryWrapper.eq("serve_id", serveDTO.getId());
                }
                StoreServe storeServe = storeServeMapper.selectOne(storeServiceQueryWrapper);
                if (storeServe != null) {
                    serveDTO.setIsChecked(1);
                } else {
                    serveDTO.setIsChecked(0);
                }
            }
            return serveDTOS;
        }
        return null;
    }

    @Override
    public void updateStoreServe(List<StoreServeDTO> param) {
        CurrentUserDTO currentUser = SecurityContext.getUser();
        Long uid = currentUser.getUid();
        Long tenantId = currentUser.getTenantId();
        storeServeMapper.delete(new QueryWrapper<StoreServe>().eq("store_id", param.get(0).getStoreId()));
        for (StoreServeDTO storeServeDTO : param) {
            StoreServe storeServe = new StoreServe();
            BeanCopierUtils.copyProperties(storeServeDTO, storeServe);
            storeServe.setCreator(uid);
            storeServe.setModifier(uid);
            storeServe.setTenantId(tenantId);
            storeServe.setCreated(new Date());
            storeServe.setModified(new Date());
            storeServeMapper.insert(storeServe);
        }
    }

    @Override
    public void updateStore(StoreDTO param) throws ParseException {
        Store store = storeMapper.selectById(param.getId());
        if (store != null) {
            CurrentUserDTO currentUser = SecurityContext.getUser();
            Long uid = currentUser.getUid();


            int i = 0;
            if (!Objects.equals(param.getStoreSn(), store.getStoreSn())) {
                i = 1;
                log.info("修改getStoreSn");
            }
            if (!Objects.equals(param.getStoreName(), store.getStoreName())) {
                i = 1;
                log.info("修改getStoreName");
            }
            if (!Objects.equals(param.getCity(), store.getCity())) {
                i = 1;
                log.info("修改getCity");
            }
            if (!Objects.equals(param.getProvince(), store.getProvince())) {
                i = 1;
                log.info("修改getProvince");
            }
            if (!Objects.equals(param.getStoreAddress(), store.getStoreAddress())) {
                i = 1;
                log.info("修改getStoreAddress");
            }
            if (param.getLongitude() != null || !Objects.equals(store.getLongitude(), "")) {
                if (!Objects.equals(param.getLongitude(), store.getLongitude())) {
                    i = 1;
                    log.info("修改getLongitude");
                }
            }
            if (param.getLatitude() != null || !Objects.equals(store.getLatitude(), "")) {
                if (!Objects.equals(param.getLatitude(), store.getLatitude())) {
                    i = 1;
                    log.info("修改getLatitude");
                }
            }
            if (!Objects.equals(param.getIsClose(), store.getIsClose())) {
                i = 1;
                log.info("修改getIsClose");
            }
            if (param.getOpeningHourOne() != null || !Objects.equals(store.getOpeningHourOne(), "")) {
                if (!Objects.equals(param.getOpeningHourOne(), store.getOpeningHourOne())) {
                    i = 1;
                    log.info("修改getOpeningHourOne");
                }
            }
            if (param.getOpeningHourTwo() != null || !Objects.equals(store.getOpeningHourTwo(), "")){
                if (!Objects.equals(param.getOpeningHourTwo(), store.getOpeningHourTwo())) {
                    i = 1;
                    log.info("修改getOpeningHourTwo");
                }
            }


            Store storeUpdate = new Store();
            BeanCopierUtils.copyProperties(param, storeUpdate);

            /*boolean isTypeModified = !Objects.equals(param.getType(), store.getType());
            boolean isImagesModified = !Objects.equals(param.getImages(), store.getImages());
            boolean isWeworkImagesModified = !Objects.equals(param.getWeworkImages(), store.getWeworkImages());*/

            if (storeUpdate.getOpenDate() != null && !Objects.equals(storeUpdate.getOpenDate(), "")) {
                SimpleDateFormat originalSdf = new SimpleDateFormat("yyyy-MM-dd");
                Date date = originalSdf.parse(storeUpdate.getOpenDate());
                SimpleDateFormat targetSdf = new SimpleDateFormat("yyyyMMdd");
                String formattedDate = targetSdf.format(date);
                storeUpdate.setOpenDate(formattedDate);
                if (!Objects.equals(formattedDate, store.getOpenDate())) {
                    i = 1;
                    log.info("修改门店编号getOpenDate");
                }
            }

            storeUpdate.setId(param.getId());
            storeUpdate.setModifier(uid);
            if (i == 1) {
                storeUpdate.setIsUpdate(1);
            }
            storeMapper.updateById(storeUpdate);
        }
    }

    @Override
    public void importStoreServe(List<StoreServeImportExcelDTO> excelDTOList) {
        CurrentUserDTO currentUser = SecurityContext.getUser();
        Long uid = currentUser.getUid();
        Long tenantId = currentUser.getTenantId();
        Date now = new Date();
        for (StoreServeImportExcelDTO dto : excelDTOList) {
            QueryWrapper<Store> storeQueryWrapper = new QueryWrapper<>();
            storeQueryWrapper.eq("store_sn", dto.getStoreSn());
            Store store = storeMapper.selectOne(storeQueryWrapper);
            // 将serveIds字符串按逗号拆分，并转换为Long类型的集合
            /*List<Long> serveIds = Arrays.stream(dto.getServeIds().split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            if (!serveIds.isEmpty()) {
                for (Long serveId : serveIds) {
                    StoreServe storeServe = new StoreServe();
                    storeServe.setCreator(uid);
                    storeServe.setModifier(uid);
                    storeServe.setTenantId(tenantId);
                    storeServe.setCreated(now);
                    storeServe.setModified(now);
                    storeServe.setStoreId(store.getId());
                    storeServe.setServeId(serveId);
                    storeServeMapper.insert(storeServe);
                }
            }*/
            if (dto.getType() != null) {
                Store store1 = new Store();
                store1.setId(store.getId());
                store1.setType(dto.getType());
                store1.setModifier(uid);
                store1.setUpdateTime(now);
                store1.setIsUpdate(1);
                storeMapper.updateById(store1);
            }
        }
    }

    @Override
    public PageInfo<StoreDTO> getAppStoreList(StoreDTO param) {
        int tag = 0;
        PageInfo<StoreDTO> appStorePageInfo = new PageInfo<>();
        QueryWrapper<Store> storeQueryWrapper = new QueryWrapper<>();
        if (param.getType() != null) {
            storeQueryWrapper.eq("type", param.getType());
        }
        if (param.getId() != null) {
            storeQueryWrapper.eq("id", param.getId());
        }
        if (param.getCity() != null && !"".equals(param.getCity())) {
            storeQueryWrapper.eq("city", param.getCity());
        }
        //storeQueryWrapper.eq("status", 1);
        if (param.getStoreName() != null && !"".equals(param.getStoreName())) {
            storeQueryWrapper.like("store_name", param.getStoreName());
        }
        //关联服务
        if (!CollectionUtils.isEmpty(param.getServeIds())) {
            List<Long> storeIds = storeServeMapper.selectStoreIdsByServeIds(param.getServeIds());
            if (!CollectionUtils.isEmpty(storeIds)) {
                storeQueryWrapper.in("id", storeIds);
            }
        }
        storeQueryWrapper.ne("city", "-");
        storeQueryWrapper.ne("city", "其他");
        storeQueryWrapper.orderByAsc("store_sn");
        List<Store> storeList = storeMapper.selectList(storeQueryWrapper);
        if (CollectionUtils.isEmpty(storeList) && param.getCity() != null && !"".equals(param.getCity()) && param.getStoreName() == null && param.getType() == null) {
            QueryWrapper<Store> storeQueryWrapper1 = new QueryWrapper<>();
            storeQueryWrapper1.ne("city", "-");
            storeQueryWrapper1.ne("city", "其他");
            storeList = storeMapper.selectList(storeQueryWrapper1);
            tag = 1;
        }
        if (CollectionUtils.isEmpty(storeList)) {
            appStorePageInfo.setCount(0L);
            appStorePageInfo.setPageNum(param.getPageNum());
            appStorePageInfo.setPageSize(param.getPageSize());
            appStorePageInfo.setList(new ArrayList<>());
            return appStorePageInfo;
        }
        List<Store> list = new ArrayList<>();
        Date date = new Date();
        // 指定日期格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        // 格式化为字符串
        String formattedDate = sdf.format(date);
        //只展示开业中数据
        for (Store store : storeList) {
            if (store.getOpenDate() != null && store.getIsClose() == 0 && !Objects.equals(store.getOpenDate(), "") && Integer.parseInt(store.getOpenDate()) <= Integer.parseInt(formattedDate)) {
                list.add(store);
            }
        }
        appStorePageInfo.setCount(list.size());
        List<StoreDTO> dataList = new ArrayList<>();
        if (StringUtils.isEmpty(param.getLongitude()) || StringUtils.isEmpty(param.getLatitude())) {

            if (param.getTag() != null && param.getTag() == 1) {
                dataList.addAll(
                        list.stream()
                                .map(store -> {
                                    StoreDTO appStoreListDTO = BeanCopierUtils.convertObject(store, StoreDTO.class);
                                    if (store.getLongitude() != null && store.getLatitude() != null && !Objects.equals(store.getLatitude(), "") && !Objects.equals(store.getLongitude(), "")) {
                                        Double distance = MapUtils.GetDistance("116.397499", "39.908722", store.getLongitude(), store.getLatitude());
                                        appStoreListDTO.setDistance(String.valueOf(distance));
                                    } else {
                                        appStoreListDTO.setDistance(null); //
                                    }
                                    return appStoreListDTO;
                                })
                                .sorted(Comparator.comparingDouble(
                                        value -> {
                                            if (value.getDistance() == null) {
                                                return Double.MAX_VALUE; // 将空值排在最末尾
                                            }
                                            return Double.parseDouble(value.getDistance());
                                        }
                                ))
                                .skip((long) (param.getPageNum() - 1) * param.getPageSize())
                                .limit(param.getPageSize())
                                .collect(Collectors.toList())
                );
                dataList.forEach(appStoreListDTO -> appStoreListDTO.setDistance(null));
            } else {
                //未授权LBS信息 按照门店创建时间正序
                dataList.addAll(list.stream()
                        .sorted(Comparator.comparing(Store::getId))
                        .skip((long) (param.getPageNum() - 1) * param.getPageSize())
                        .limit(param.getPageSize())
                        .map(store -> BeanCopierUtils.convertObject(store, StoreDTO.class))
                        .collect(Collectors.toList()));
            }
        } else {
            //已授权LBS信息
            dataList.addAll(
                    list.stream()
                            .map(store -> {
                                StoreDTO appStoreListDTO = BeanCopierUtils.convertObject(store, StoreDTO.class);
                                if (store.getLongitude() != null && store.getLatitude() != null && !Objects.equals(store.getLatitude(), "") && !Objects.equals(store.getLongitude(), "")) {
                                    Double distance = MapUtils.GetDistance(param.getLongitude(), param.getLatitude(), store.getLongitude(), store.getLatitude());
                                    appStoreListDTO.setDistance(String.valueOf(distance));
                                } else {
                                    appStoreListDTO.setDistance(null); //
                                }
                                return appStoreListDTO;
                            })
                            .sorted(Comparator.comparingDouble(
                                    value -> {
                                        if (value.getDistance() == null) {
                                            return Double.MAX_VALUE; // 将空值排在最末尾
                                        }
                                        return Double.parseDouble(value.getDistance());
                                    }
                            ))
                            .skip((long) (param.getPageNum() - 1) * param.getPageSize())
                            .limit(param.getPageSize())
                            .collect(Collectors.toList())
            );
            dataList = dataList.stream()
                    .peek(appStoreListDTO -> {
                        if (appStoreListDTO.getDistance() != null) {
                            appStoreListDTO.setDistance(distance2Unit(appStoreListDTO.getDistance()));
                        }
                    })
                    .collect(Collectors.toList());
            //dataList = dataList.stream().peek(appStoreListDTO -> appStoreListDTO.setDistance(distance2Unit(appStoreListDTO.getDistance()))).collect(Collectors.toList());

        }
        //处理门店服务
        /*for (StoreDTO appStoreListDTO : dataList){
            QueryWrapper<StoreServe> storeServeQueryWrapper = new QueryWrapper<>();
            storeServeQueryWrapper.eq("store_id", appStoreListDTO.getId());
            List<StoreServe> storeServeList = storeServeMapper.selectList(storeServeQueryWrapper);
            if (!CollectionUtils.isEmpty(storeServeList)) {
                QueryWrapper<Serve> serveQueryWrapper = new QueryWrapper<>();
                serveQueryWrapper.in("id", storeServeList.stream().map(StoreServe::getServeId).collect(Collectors.toList()));
                serveQueryWrapper.eq("status", 0);
                List<Serve> serveList = serveMapper.selectList(serveQueryWrapper);
                appStoreListDTO.setServeDTOList(BeanCopierUtils.convertList(serveList, ServeDTO.class));
            }
        }*/
        if (tag == 1) {
            dataList.forEach(appStoreListDTO -> appStoreListDTO.setCityTag(1));
        }
        appStorePageInfo.setList(dataList);
        appStorePageInfo.setPageNum(param.getPageNum());
        appStorePageInfo.setPageSize(param.getPageSize());

        return appStorePageInfo;
    }

    @Override
    public List<String> cityList() {
        List<String> cities = storeMapper.selectStoreCity();
        // 去掉列表中的 "-"
        cities.removeIf("-"::equals);
        Collator collator = Collator.getInstance(Locale.CHINA);
        collator.setStrength(Collator.PRIMARY); //
        cities.sort(collator);
        // 再次确保 "其他" 在最后
        if (cities.contains("其他")) {
            cities.remove("其他");
            cities.add("其他");
        }
        return cities;
    }

    /**
     * 根据ID查询门店
     *
     * @param storeSn
     * @return String
     */
    @Override
    public String getStoreByStoreSn(String storeSn) {
        //根据门店编号查询门店名称
        Store store = storeMapper.selectOne(new QueryWrapper<Store>().eq("store_sn", storeSn));
        return store.getStoreName();
    }

    @Override
    public StoreCityDTO cityListNew() {
        List<String> cities = storeMapper.selectStoreCity();
        // 去掉列表中的 "-"
        cities.removeIf("-"::equals);
        cities.removeIf("其他"::equals);
        cities.removeIf("OTH"::equals);
        // 创建拼音转换器
        Pinyin4jUtil pinyinUtil = new Pinyin4jUtil();

        StoreCityDTO cityGroups = new StoreCityDTO();

        for (String city : cities) {
            // 获取城市名称的拼音首字母
            String pinyin = pinyinUtil.getFirstLetter(city);
            if (pinyin != null && !pinyin.isEmpty()) {
                char firstLetter = pinyin.toUpperCase().charAt(0);
                // 确保特定城市被正确分组
                if ("重庆市".equals(city) || "长沙市".equals(city) || "长春市".equals(city)) {
                    firstLetter = 'C';
                }
                cityGroups.addCity(firstLetter, city);
            }
        }

        // 对每个字母下的城市列表进行排序
        cityGroups.getCityMap().values().forEach(this::sortCityList);

        return cityGroups;
    }

    private void sortCityList(List<String> cityList) {
        cityList.sort(String::compareTo);
    }

    @Override
    public String cityByLonLat(String longitude, String latitude) {
        AddressComponentDTO data = null;
        JSONObject jsonObject = getAddress(longitude, latitude);
        if (jsonObject != null) {
            data = jsonObject.getObject("address_component", AddressComponentDTO.class);
        }
        if (data != null){
            return data.getCity();
        }
        return null;
    }

    @Override
    public void getSftpFile() throws IOException {
        SecurityContext.setUser(new CurrentUserDTO(1L, 999L));

        //
        Date date = new Date();
        // 指定日期格式
        LocalDate yesterday = LocalDate.now().minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String formattedDate = yesterday.format(formatter);
        String fileName = "shop_" + formattedDate + ".csv";
        SftpDTO sftpDTO = new SftpDTO();
        sftpDTO.setFilePath("/shop");
        //sftpDTO.setFileName("shop_20250101.csv");
        sftpDTO.setFileName(fileName);
        sftpDTO.setHost(host);
        sftpDTO.setPort(port);
        sftpDTO.setUserName(userName);
        sftpDTO.setPass(password);
        SftpDTO sftpFile = SftpUtils.getSftpFile(sftpDTO);
        //获取文件
        if (sftpFile != null) {
            try (InputStream inputStream = sftpFile.getIn()) {
                dealSftpStoreData(inputStream);
            } finally {
                SftpUtils.sessionClose();
                SftpUtils.sftpClose(sftpFile.getSftp());
            }
        }
    }

    @Override
    public List<StoreDTO> getLongitudeLatitude() {
        List<Store> storeList = storeMapper.getLongitudeLatitude();
        List<Store> list = new ArrayList<>();
        Date date = new Date();
        // 指定日期格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        // 格式化为字符串
        String formattedDate = sdf.format(date);
        //只展示开业中数据
        for (Store store : storeList) {
            if (StringUtils.isEmpty(store.getLatitude()) || StringUtils.isEmpty(store.getLongitude())) {
                continue;
            }
            if (!StringUtils.isEmpty(store.getOpenDate())) {
                if (store.getIsClose() == 0 && Integer.parseInt(store.getOpenDate()) <= Integer.parseInt(formattedDate)) {
                    list.add(store);
                }
            }
        }
        return BeanCopierUtils.convertList(list, StoreDTO.class);
    }

    @Override
    public List<String> getStoreProvince() {
        List<String> storeProvinceList = storeMapper.selectStoreProvince();
        List<String> provinceList = storeMapper.selectProvince();
        Set<String> mergedSet = new HashSet<>();
        mergedSet.addAll(storeProvinceList);
        mergedSet.addAll(provinceList);
        return new ArrayList<>(mergedSet);
    }

    @Override
    public List<String> getStoreCity() {
        List<String> storeCityList = storeMapper.selectStoreCity();
        List<String> cityList = storeMapper.selectCity();
        Set<String> mergedSet = new HashSet<>();
        mergedSet.addAll(storeCityList);
        mergedSet.addAll(cityList);
        return new ArrayList<>(mergedSet);
    }


    private static void processStoreDTO(StoreDTO storeDTO, List<Long> serves, StoreServeMapper storeServeMapper) {
        if (storeDTO.getImages() == null || "".equals(storeDTO.getImages())) {
            storeDTO.setIsStoreImage(1);
        } else {
            storeDTO.setIsStoreImage(2);
        }
        if (storeDTO.getWeworkImages() == null || "".equals(storeDTO.getWeworkImages())) {
            storeDTO.setIsWeWorkImage(1);
        } else {
            storeDTO.setIsWeWorkImage(2);
        }
        QueryWrapper<StoreServe> storeServeQueryWrapper = new QueryWrapper<>();
        storeServeQueryWrapper.eq("store_id", storeDTO.getId());
        storeServeQueryWrapper.in("serve_id", serves);
        storeDTO.setServeNum(storeServeMapper.selectCount(storeServeQueryWrapper));
    }

    private static String distance2Unit(String distance) {
        double dis = Double.parseDouble(distance);
        if (dis >= 1000) {
            Double distanceKm = Math.round(dis / 100d) / 10d;
            return distanceKm + "km";
        } else {
            return String.format("%.2f", dis) + "m";
        }
    }

    /**
     * 根据坐标获取地址详细信息
     *
     * @param longitude 经度
     * @param latitude  维度
     * @return JSONObject
     */
//    public JSONObject getAddress(String longitude, String latitude) {
//        String[] keys = mapKey.split(",");
//        int num = (int) (Math.random() * keys.length);
//        String key = keys[num];
//        String signatureKey = "W3o8BVJnwiw7SkjEAGeAooQKUtKqvmm0";
//        String url = mapLocation + "?key=" + key + "&location=" + latitude + "," + longitude;
//        //无域名请求路径拼接，用于生成sig
//        String url1 = "/ws/geocoder/v1" + "?key=" + key + "&location=" + latitude + "," + longitude;
//
//
//        // 生成签名
//        String signature = generateSignature(url1, signatureKey);
//        url += "&sig=" + signature;
//
//        log.info("经度:" + longitude + ",纬度:" + latitude + ",获取地址描述入参:" + url);
//        JSONObject jsonObject = restTemplate.getForObject(url, JSONObject.class);
//        log.info("【调用腾讯地图】接口:{},返回:{}", url, null == jsonObject ? "" : jsonObject.toJSONString());
//
//        if (jsonObject == null) {
//            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "根据经纬度调用腾讯地图异常!返回空");
//        }
//        JSONObject data;
//        if (jsonObject.getInteger("status") == 0) {
//            data = jsonObject.getJSONObject("result");
//        } else {
//            log.error("根据地市,关键字调用腾讯地图失败!原因:" + jsonObject.getString("message"));
//            return null;
//        }
//        return data;
//    }
    public JSONObject getAddress(String longitude, String latitude) {
        try {
            String path = "/ws/geocoder/v1";
            Map<String, String> params = new TreeMap<>();
            params.put("location", latitude + "," + longitude);
            params.put("key", mapKey);
            // 生成签名
            String signature = generateSignature(path,params,mapSecret);
            log.info("签名:" + mapSecret);
            params.put("sig", signature);

            // 拼接完整 URL
            StringBuilder fullUrl = new StringBuilder(mapLocation).append("?");
            for (Map.Entry<String, String> entry : params.entrySet()) {
                log.info("key:" + entry.getKey() + ",value:" + entry.getValue());
                fullUrl.append(URLEncoder.encode(entry.getKey(), "UTF-8"))
                        .append("=")
                        .append(URLEncoder.encode(entry.getValue(), "UTF-8"))
                        .append("&");
                log.info("fullUrl:" + fullUrl.toString());
            }

            // 移除最后一个 '&'
            fullUrl.setLength(fullUrl.length() - 1);
            log.info("经度:" + longitude + ",纬度:" + latitude + ",获取地址描述入参:" + fullUrl.toString());
            // 发送请求
            JSONObject response = restTemplate.getForObject(fullUrl.toString(), JSONObject.class);
            log.info("【调用腾讯地图】接口:{},返回:{}", fullUrl.toString(), null == response ? "" : response.toJSONString());
            if (response == null) {
                throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "根据经纬度调用腾讯地图异常!返回空");
            }
            JSONObject data;
            if (response.getInteger("status") == 0) {
                data = response.getJSONObject("result");
            } else {
                log.error("根据地市,关键字调用腾讯地图失败!原因:" + response.getString("message"));
                return null;
            }
            return data;
        } catch (Exception e) {
            throw new RuntimeException("从腾讯地图API获取地址失败", e);
        }
    }

    // 新增方法：生成签名
//    private String generateSignature(String url, String signatureKey) {
//        String toSign = url + signatureKey;
//        return org.apache.commons.codec.digest.DigestUtils.md5Hex(toSign);
//    }
    public String generateSignature(String path, Map<String, String> params, String secretKey) throws Exception {
        // 1. 参数按键名升序排序
        TreeMap<String, String> sortedParams = new TreeMap<>(params);

        // 2. 拼接参数为 key=value&key=value 的格式
        StringBuilder queryString = new StringBuilder();
        for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
            queryString.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }

        // 去掉最后一个 "&"
        queryString.deleteCharAt(queryString.length() - 1);

        // 3. 拼接请求路径和 Secret Key
        String stringToSign = path + "?" + queryString + secretKey;
        log.info("Signing string: {}", stringToSign);
        // 4. 计算 MD5 并转换为小写的十六进制字符串
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        byte[] digest = md5.digest(stringToSign.getBytes(StandardCharsets.UTF_8));
        StringBuilder md5String = new StringBuilder();
        for (byte b : digest) {
            md5String.append(String.format("%02x", b));
        }

        return md5String.toString();
    }


    private void dealSftpStoreData(InputStream data) {
        SecurityContext.setUser(new CurrentUserDTO(1L, 999L));
        Date date = new Date();

        // 收集需要更新的数据
        List<Store> storesToUpdate = new ArrayList<>();
        List<Store> storesToSave = new ArrayList<>();
        Set<String> storeSnInCSV = new HashSet<>();

        try (InputStream bomInputStream = new BOMInputStream(data);
             Reader reader = new InputStreamReader(bomInputStream);
             CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT.builder().setHeader().build())) {

            // 调试列名
            List<String> headerNames = csvParser.getHeaderNames();
            log.debug("CSV Header Names: {}", headerNames);

            for (CSVRecord csvRecord : csvParser) {
                String storeSn = csvRecord.get("shop_id_str");
                storeSnInCSV.add(storeSn);

                String storeName = csvRecord.get("shop_name_str");
                String storeAddress = csvRecord.get("address_str");
                String phone = csvRecord.get("tel_str");
                String longitude = csvRecord.get("longitude_str");
                String latitude = csvRecord.get("latitude_str");
                String province = csvRecord.get("province_name_str");
                String city = csvRecord.get("city_name_str");
                String openingHourOne = csvRecord.get("business_hours_1");
                String openingHourTwo = csvRecord.get("business_hours_2");
                String openDate = csvRecord.get("open_date_str");

                try {
                    QueryWrapper<Store> storeQueryWrapper = new QueryWrapper<>();
                    storeQueryWrapper.eq("store_sn", storeSn);
                    Store store = storeMapper.selectOne(storeQueryWrapper);
                    if (store != null) {
                        if (store.getIsUpdate() == 0) {
                            store.setStoreName(storeName);
                            store.setStoreAddress(storeAddress);
                            store.setPhone(phone);
                            store.setLongitude(longitude);
                            store.setLatitude(latitude);
                            store.setProvince(province);
                            if (Objects.equals(province, "OTH")) {
                                store.setProvince("其他");
                            }
                            store.setCity(city);
                            if (Objects.equals(city, "OTH")) {
                                store.setCity("其他");
                            }

                            if (Objects.equals(openingHourOne, "") || openingHourOne == null) {
                                store.setOpeningHourOne(null);
                            } else {
                                store.setOpeningHourOne(openingHourOne);
                            }

                            if (Objects.equals(openingHourTwo, "") || openingHourTwo == null) {
                                store.setOpeningHourTwo(null);
                            } else {
                                store.setOpeningHourTwo(openingHourTwo);
                            }
                            store.setOpenDate(openDate);
                            store.setUpdateTime(date);
                            // 这两种情况记录为关店
                            if (Objects.equals(province, "廃止店") || Objects.equals(province, "改造店")) {
                                store.setIsClose(1);
                            }
                            storesToUpdate.add(store);
                        }
                    } else {
                        store = new Store();
                        store.setStoreSn(storeSn);
                        store.setStoreName(storeName);
                        store.setStoreAddress(storeAddress);
                        store.setPhone(phone);
                        store.setLongitude(longitude);
                        store.setLatitude(latitude);
                        store.setProvince(province);
                        if (!Objects.equals(store.getProvince(), province) && Objects.equals(province, "OTH")) {
                            store.setProvince("其他");
                        }
                        store.setCity(city);
                        if (!Objects.equals(store.getCity(), city) && Objects.equals(city, "OTH")) {
                            store.setCity("其他");
                        }
                        if (Objects.equals(openingHourOne, "") || openingHourOne == null) {
                            store.setOpeningHourOne(null);
                        } else {
                            store.setOpeningHourOne(openingHourOne);
                        }

                        if (Objects.equals(openingHourTwo, "") || openingHourTwo == null) {
                            store.setOpeningHourTwo(null);
                        } else {
                            store.setOpeningHourTwo(openingHourTwo);
                        }
                        store.setOpenDate(openDate);
                        store.setCreator(0L);
                        store.setCreateTime(date);
                        store.setUpdateTime(date);
                        store.setTenantId(1L);
                        // 这两种情况记录为关店
                        if (Objects.equals(province, "廃止店") || Objects.equals(province, "改造店")) {
                            store.setIsClose(1);
                        }
                        storesToSave.add(store);
                    }
                } catch (Exception e) {
                    log.error("sftp store 门店错误 &{}&{}", storeSn, e.getMessage());
                }
            }

            // 获取所有数据库中的门店数据
            QueryWrapper<Store> storeQueryWrapper = new QueryWrapper<>();
            storeQueryWrapper.eq("tenant_id", 1L);
            storeQueryWrapper.eq("is_update",0);
            List<Store> allStoresInDB = storeMapper.selectList(storeQueryWrapper);
            List<Store> storesToDelete = new ArrayList<>();
            if (!CollectionUtils.isEmpty(allStoresInDB)) {
                for (Store store : allStoresInDB) {
                    if (!storeSnInCSV.contains(store.getStoreSn())) {
                        storesToDelete.add(store);
                    }
                }
            }

            // 批量更新
            if (!storesToUpdate.isEmpty()) {
                for (Store store : storesToUpdate) {
                    storeMapper.updateById(store);
                }
            }

            // 批量保存
            if (!storesToSave.isEmpty()) {
                storeMapper.insertBatchSomeColumn(storesToSave);
            }

            // 批量删除 关店
            if (!storesToDelete.isEmpty()) {
                for (Store store : storesToDelete) {
                    store.setIsClose(1);
                    store.setUpdateTime(date);
                    storeMapper.updateById(store);
                }
            }

            log.info("sftp user store data processed successfully");
        } catch (Exception e) {
            log.error("Error processing CSV file", e);
        }
    }

    /*private void dealSftpStoreData(InputStream data) {
    Date date = new Date();
    Workbook workbook;
    try {
        workbook = new XSSFWorkbook(data); // 创建 Workbook 对象
        Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表
        log.info("sftp user store get workbook");

        // 读取表头
        Row headerRow = sheet.getRow(0);
        Iterator<Cell> headerCells = headerRow.cellIterator();
        int storeSnIndex = -1;
        int storeNameIndex = -1;
        int storeAddressIndex = -1;
        int phoneIndex = -1;
        int longitudeIndex = -1;
        int latitudeIndex = -1;
        int provinceIndex = -1;
        int cityIndex = -1;
        int openingHourOneIndex = -1;
        int openingHourTwoIndex = -1;
        int openDateIndex = -1;

        while (headerCells.hasNext()) {
            Cell cell = headerCells.next();
            switch (cell.getStringCellValue()) {
                case "shop_id_str":
                    storeSnIndex = cell.getColumnIndex();
                    break;
                case "shop_name_str":
                    storeNameIndex = cell.getColumnIndex();
                    break;
                case "address_str":
                    storeAddressIndex = cell.getColumnIndex();
                    break;
                case "tel_str":
                    phoneIndex = cell.getColumnIndex();
                    break;
                case "longitude_str":
                    longitudeIndex = cell.getColumnIndex();
                    break;
                case "latitude_str":
                    latitudeIndex = cell.getColumnIndex();
                    break;
                case "province_name_str":
                    provinceIndex = cell.getColumnIndex();
                    break;
                case "city_str":
                    cityIndex = cell.getColumnIndex();
                    break;
                case "business_hours_1":
                    openingHourOneIndex = cell.getColumnIndex();
                    break;
                case "business_hours_2":
                    openingHourTwoIndex = cell.getColumnIndex();
                    break;
                case "open_date_str":
                    openDateIndex = cell.getColumnIndex();
                    break;
            }
        }

        // 检查所有必需的列是否都被找到
        if (storeSnIndex < 0 || storeNameIndex < 0 || storeAddressIndex < 0 || phoneIndex < 0 ||
            longitudeIndex < 0 || latitudeIndex < 0 || provinceIndex < 0 || cityIndex < 0 ||
            openingHourOneIndex < 0 || openingHourTwoIndex < 0 || openDateIndex < 0) {
            throw new IllegalArgumentException("One or more required columns are missing in the header row.");
        }

        // 逐条读取记录
        Iterator<Row> rows = sheet.iterator();
        rows.next(); // 跳过表头行
        log.info("sftp user for each record");

        // 收集需要更新的数据
        List<Store> storesToUpdate = new ArrayList<>();
        List<Store> storeToSave = new ArrayList<>();

        while (rows.hasNext()) {
            Row row = rows.next();

            Cell storeSnCell = row.getCell(storeSnIndex);
            if (storeSnCell == null || storeSnCell.getCellType() == CellType.BLANK) {
                break;
            }

            String storeSn = storeSnCell.getStringCellValue();
            String storeName = getStringCellValue(row.getCell(storeNameIndex));
            String storeAddress = getStringCellValue(row.getCell(storeAddressIndex));
            String phone = getStringCellValue(row.getCell(phoneIndex));
            String longitude = getStringCellValue(row.getCell(longitudeIndex));
            String latitude = getStringCellValue(row.getCell(latitudeIndex));
            String province = getStringCellValue(row.getCell(provinceIndex));
            String city = getStringCellValue(row.getCell(cityIndex));
            String openingHourOne = getStringCellValue(row.getCell(openingHourOneIndex));
            String openingHourTwo = getStringCellValue(row.getCell(openingHourTwoIndex));
            String openDate = getStringCellValue(row.getCell(openDateIndex));

            try {
                QueryWrapper<Store> storeQueryWrapper = new QueryWrapper<>();
                storeQueryWrapper.eq("store_sn", storeSn);
                Store store = storeMapper.selectOne(storeQueryWrapper);
                if (store != null) {
                    if (store.getIsUpdate() == 0) {
                        store.setStoreName(storeName);
                        store.setStoreAddress(storeAddress);
                        store.setPhone(phone);
                        store.setLongitude(longitude);
                        store.setLatitude(latitude);
                        store.setProvince(province);
                        store.setCity(city);
                        store.setOpeningHourOne(openingHourOne);
                        store.setOpeningHourTwo(openingHourTwo);
                        store.setOpenDate(openDate);
                        store.setUpdateTime(date);
                        storesToUpdate.add(store);
                    }
                } else {
                    store = new Store();
                    store.setStoreSn(storeSn);
                    store.setStoreName(storeName);
                    store.setStoreAddress(storeAddress);
                    store.setPhone(phone);
                    store.setLongitude(longitude);
                    store.setLatitude(latitude);
                    store.setProvince(province);
                    store.setCity(city);
                    store.setOpeningHourOne(openingHourOne);
                    store.setOpeningHourTwo(openingHourTwo);
                    store.setOpenDate(openDate);
                    store.setCreator(0L);
                    store.setCreateTime(date);
                    store.setUpdateTime(date);
                    store.setTenantId(1L);
                    storeToSave.add(store);
                }
            } catch (Exception e) {
                log.error("sftp store 门店错误 &{}&{}", storeSn, e.getMessage());
            }
        }

        // 批量更新
        if (!storesToUpdate.isEmpty()) {
            storeMapper.updateBatchById(storesToUpdate);
        }
        if (!storeToSave.isEmpty()) {
            storeMapper.insertBatchSomeColumn(storeToSave);
        }
        workbook.close();
    } catch (Exception e) {
        log.error("SFTP门店拉取过程异常", e);
    }*/

    private String getStringCellValue(Cell cell) {
        if (cell == null || cell.getCellType() == CellType.BLANK) {
            return "";
        }
        if (cell.getCellType() == CellType.STRING) {
            return cell.getStringCellValue();
        }
        if (cell.getCellType() == CellType.NUMERIC) {
            return String.valueOf(cell.getNumericCellValue());
        }
        return "";
    }
}
