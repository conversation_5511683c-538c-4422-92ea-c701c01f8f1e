package com.dz.common.core.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.util.Date;

// crm积分商城-商品信息
@Data
@ColumnWidth(19)
public class MujiGoods {

    // 商品唯一标识
    @ExcelProperty(value = "id")
    private Long id;

    // 商品唯一标识
    @ExcelProperty(value = "goods_id")
    private String goodsId;

    // 商品名称
    @ExcelProperty(value = "goods_name")
    private String goodsName;

    // 是否置顶 (1:置顶, 2:非置顶)
    @ExcelProperty(value = "is_top")
    private Integer isTop;

    // 吊牌价/标价/销售价 (分)
    @ExcelProperty(value = "total_fee")
    private Long totalFee;

    // 价值/成本价 (分)
    @ExcelProperty(value = "cost_fee")
    private Long costFee;

    // 用券后还需支付的金额 (即 券后价) (分)
    @ExcelProperty(value = "cash_fee")
    private Long cashFee;

    // 券面额 (分)
    @ExcelProperty(value = "coupon_fee")
    private Integer couponFee;

    // 适用平台 (全平台:ALL, 仅微信:WECHAT)
    @ExcelProperty(value = "platform")
    private String platform;

    // 商品分类 (优惠券:COUPON, 商品:GOODS)
    @ExcelProperty(value = "type")
    private String type;

    // 限制类型 (1:不限制, 2:限频次, 3:限时, 4:限购)
    @ExcelProperty(value = "limit_type")
    private Integer limitType;

    // 显示方式 (1:全积分兑换, 2:券后价, 3:抵用)
    @ExcelProperty(value = "show_type")
    private Integer showType;

    // 状态 (1:待上架, 2:上架中, 3:已下架, 4:已删除)
    @ExcelProperty(value = "status")
    private Integer status;

    // 是否限量 (1:不限量, 2:限量)
    @ExcelProperty(value = "is_limit")
    private Integer isLimit;

    // 总库存
    @ExcelProperty(value = "total_quantity")
    private Long totalQuantity;

    // 已兑换数量
    @ExcelProperty(value = "exchanged_quantity")
    private Long exchangedQuantity;

    // 兑换基数 (默认0)
    @ExcelProperty(value = "exchanged_base")
    private Long exchangedBase;

    // 剩余库存
    @ExcelProperty(value = "remain_quantity")
    private Long remainQuantity;

    // 兑换等级限制 (-1:不限等级)
    @ExcelProperty(value = "limit_level")
    private Long limitLevel;

    // 兑换积分
    @ExcelProperty(value = "limit_bonus")
    private Long limitBonus;

    // 详情展示标签 (例如：每人每日限领1张)
    @ExcelProperty(value = "tags")
    private String tags;

    // 商品详情
    @ExcelProperty(value = "detail")
    private String detail;

    // 商品单价
    @ExcelProperty(value = "sale_fee")
    private Long saleFee;

    // 商品条码
    @ExcelProperty(value = "jan_code")
    private String janCode;

    // 上架开始时间
    @ExcelProperty(value = "begin_time")
    private Date beginTime;

    // 上架结束时间
    @ExcelProperty(value = "end_time")
    private Date endTime;

    // 创建时间
    @ExcelProperty(value = "created_at")
    private Date createdAt;

    // 更新时间
    @ExcelProperty(value = "updated_at")
    private Date updatedAt;

}
