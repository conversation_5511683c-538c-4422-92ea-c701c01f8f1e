package com.dz.ms.user.mapper;

import com.dz.ms.user.entity.WhiteList;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface WhiteListMapper {
    @Select("SELECT * FROM white_list WHERE user_id = #{userId}")
    WhiteList findByUserId(Long userId);

    @Select("SELECT * FROM white_list WHERE member_code = #{memberCode}")
    WhiteList findByMemberCode(String memberCode);

    boolean isMemberInWhiteList(String memberCode);
}