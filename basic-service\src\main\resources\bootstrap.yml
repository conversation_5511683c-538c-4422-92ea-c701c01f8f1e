## 应用配置
server:
  port: 10201
daozhi.version: 11.30 #检测发版是否成功用提交正式版前请更新
spring:
  application:
    name: basic-service
  profiles:
    active: dev
  mvc:
    date-format: yyyy-MM-dd HH:mm:ss
  jackson:
    joda-date-time-format: yyyy-MM-dd HH:mm:ss
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  mail:
    # 配置 SMTP 服务器地址
    host: smtp.163.com
    # 发送者邮箱
    username: xxx
    # 配置密码，注意不是真正的密码，而是刚刚申请到的授权码
    password: xxx
    # 端口号465或587
    port: 465
    # 默认的邮件编码为UTF-8
    default-encoding: UTF-8
    # 配置SSL 加密工厂
    properties:
      mail:
        smtp:
          ssl.enable: true
          socketFactoryClass: javax.net.ssl.SSLSocketFactory
        #表示开启 DEBUG 模式，这样，邮件发送过程的日志会在控制台打印出来，方便排查错误
        debug: true
mybatis:
  typeAliasesPackage: com.dz.ms.basic.entity
  mapperLocations: classpath:mapper/*.xml
  configuration:
    mapUnderscoreToCamelCase: true
mybatis-plus:
  notenant.tables: channel_link, province_info, city_info, tenant_info, job_business_mapping, system_log, t_minapp_msg_config, minapp_msg_example, alarm_config, qywx_config,style_config, mp_config, tenant_config, default_data, TABLES, COLUMNS
ribbon:
  ConnectTimeout: 6000 #请求连接的超时时间 默认的时间为 1 秒
  ReadTimeout: 6000 #请求处理的超时时间
notify:
  qywx:
    agentid: xxx
    corpid: xxx
    corpsecret: xxx
#logging:
#  files: /opt/muji/api/logs
---
spring:
  profiles: dev
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
      config:
        server-addr: 127.0.0.1:8848
        extension-configs[0]:
          data-id: application-dev.yml
          group: BASIC_SERVICE
        extension-configs[1]:
          data-id: sharding-jdbc-dev.yml
          group: BASIC_SERVICE
        username: nacos
        password: nacos
---
spring:
  profiles: uat
  cloud:
    nacos:
      discovery:
        server-addr: nacos-uat.project-70.svc.cluster.local:8848
        group: UAT_GROUP
        username: nacos
        password: nacos
      config:
        server-addr: nacos-uat.project-70.svc.cluster.local:8848
        extension-configs[0]:
          data-id: application-uat.yml
          group: BASIC_SERVICE
        extension-configs[1]:
          data-id: sharding-jdbc-uat.yml
          group: BASIC_SERVICE
        username: nacos
        password: nacos
---
spring:
  profiles: pro
  cloud:
    nacos:
      discovery:
        server-addr: nacos-prod.project-76.svc.cluster.local:8848
        username: nacos
        password: Muji@123
      config:
        server-addr: nacos-prod.project-76.svc.cluster.local:8848
        extension-configs[0]:
          data-id: application-pro.yml
          group: BASIC_SERVICE
        extension-configs[1]:
          data-id: sharding-jdbc-pro.yml
          group: BASIC_SERVICE
        username: nacos
        password: Muji@123
