package com.dz.common.core.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.util.Date;

// crm积分商城-商品对应的券信息
@Data
@ColumnWidth(19)
public class MujiStockInfo {

    // 商品唯一标识
    @ExcelProperty(value = "id")
    private Long id;

    // 商品编号
    @ExcelProperty(value = "goods_id")
    private String goodsId;

    // 批次号
    @ExcelProperty(value = "stock_id")
    private String stockId;

    // 数量 暂不露出默认1
    @ExcelProperty(value = "num")
    private Long num;

    // 创建时间
    @ExcelProperty(value = "created_at")
    private Date createdAt;

    // 更新时间
    @ExcelProperty(value = "updated_at")
    private Date updatedAt;
}
