package com.dz.ms.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.core.dto.basic.CityDTO;
import com.dz.common.core.dto.basic.ProvinceCityDTO;
import com.dz.common.core.dto.basic.ProvinceDTO;
import com.dz.ms.basic.entity.CityInfo;

import java.util.List;

/**
 * 城市信息接口
 * @author: Handy
 * @date:   2023/03/15 15:52
 */
public interface CityInfoService extends IService<CityInfo> {

    /**
     * 获取省份及关联城市列表
     * @param
     * @return List<ProvinceDTO>
     */
    public List<ProvinceDTO> getProvinceCityList();

    /**
     * 获取有门店的省份
     * @return
     */
    List<ProvinceDTO> getProvinceList();

    /**
     * 获取省份下城市
     * @param provinceCode
     * @return
     */
    List<CityDTO> getCityList(String provinceCode);

    /**
     * 根据城市code获取省份
     * @param cityCode
     * @return
     */
    ProvinceCityDTO getInfoByCityCode(String cityCode);
}
