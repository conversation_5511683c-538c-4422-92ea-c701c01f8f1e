package com.dz.common.core.enums;

import java.util.Objects;

/**
 * 预约状态枚举
 * <AUTHOR>
 * @date 2023-10-02 10:51
 */
public enum VerifyRecordsStateEnum {
    TO_MAKE(0, "待预约"),
    PENDING_CANCELED(1, "待核销"),
    ALREADY_CANCELED(2, "已核销"),
    EXPIRED(3, "已过期"),
    CANCELED(4, "已取消")
    ;
    private final Integer code;
    private final String value;

    VerifyRecordsStateEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (VerifyRecordsStateEnum resultEnum : VerifyRecordsStateEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum.value;
            }
        }
        return null;
    }
}
