package com.dz.common.core.enums;

import java.util.Objects;

/**
 * 核销人类型枚举  1、后台用户,2、导购
 *
 * <AUTHOR>
 * @date 2023-10-02 10:51
 */
public enum VerifyRecordsUserTypeEnum {
    SYS_USER(1, "后台用户"),
    SALES(2, "导购"),

    USER(3, "前台用户"),
    SYSTEM(4, "系统")
    ;
    private final Integer code;
    private final String value;

    VerifyRecordsUserTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (VerifyRecordsUserTypeEnum resultEnum : VerifyRecordsUserTypeEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum.value;
            }
        }
        return null;
    }
}
