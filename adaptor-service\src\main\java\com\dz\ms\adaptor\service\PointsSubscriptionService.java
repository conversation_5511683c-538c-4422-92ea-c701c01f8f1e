package com.dz.ms.adaptor.service;

import com.dz.ms.adaptor.dto.CrmPointsSyncParamDTO;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**

 */
public interface PointsSubscriptionService {

    void pointsSubscription(String jobParams) throws ParseException;

    void sendMyMsg(String jobParams) throws ParseException;

    void getSftpFile() throws IOException;

    String subTableList(String cdpDay);

    void crmPointsSync(List<CrmPointsSyncParamDTO> list);

    Integer insertBatchSomeColumn(List<CrmPointsSyncParamDTO> list, String nowDay);
}
