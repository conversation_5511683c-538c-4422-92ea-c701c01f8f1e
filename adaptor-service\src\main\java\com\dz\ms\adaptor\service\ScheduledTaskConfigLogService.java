package com.dz.ms.adaptor.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.ms.adaptor.dto.ScheduledTaskConfigLogDTO;
import com.dz.ms.adaptor.entity.ScheduledTaskConfigLog;

/**
 * 定时任务日志表接口
 * @author: 
 * @date:   2025/03/17 11:26
 */
public interface ScheduledTaskConfigLogService extends IService<ScheduledTaskConfigLog> {

    /**
     * 保存定时任务日志表
     * @param param
     * @return Long
     */
    Long saveScheduledTaskConfigLog(ScheduledTaskConfigLogDTO param);


}
