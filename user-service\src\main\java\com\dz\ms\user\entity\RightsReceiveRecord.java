package com.dz.ms.user.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 权益领取记录
 */
@Getter
@Setter
@NoArgsConstructor
@Table("权益领取记录")
@TableName(value = "t_rights_receive_record")
public class RightsReceiveRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long userId;

    private String couponId;

    private String couponCode;

    private String createAt;

    private Date createTime;

    private Long tenantId;
}
