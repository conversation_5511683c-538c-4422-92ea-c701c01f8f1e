<!--pages/purchaseRecord/purchaseRecord.wxml-->
<my-page loading="{{loading}}" overallModal="{{overallModal}}">
  <view class="page-content">
    <custom-header type="{{1}}" isBackHidden="{{ isBackHidden }}">
      <view slot="content" class="page-title">
        <text>消费记录</text>
      </view>
    </custom-header>
    <view class="top-block">
      <view class="title">消费明细</view>
      <picker value="{{yearData}}" start="{{startDate}}" end="{{ endDate }}" mode="date" fields="year" bindchange="changeYear">
        <view class="filter-btn">
          {{yearData}}年
          <view class="iconfont icon-Pull-down" />
        </view>
      </picker>
    </view>
    <scroll-view class="list-box" scroll-y lower-threshold="50" bindscrolltolower="onScrollToLower" enhanced="{{ true }}" show-scrollbar="{{ false }}" style="flex: 1;height: 0;" scroll-y="{{list.length > 0}}">
      <view wx:for="{{list}}" class="list-item" wx:key="id">
        <purchase-card dataInfo="{{item}}"></purchase-card>
      </view>
      <block wx:if="{{!loading}}">
        <no-data-available top="{{481}}" data="{{list}}" text="暂无消费记录" />
      </block>
    </scroll-view>
  </view>
</my-page>