<template>
  <a-drawer :title="title" width="1200" placement="right" :closable="true" :maskClosable="false" :open="thisFields.open" @close="onClose">
    <a-spin :spinning="thisFields.loading">
      <a-form class="form" ref="formRef" :model="formFields" :rules="formRules" :labelCol="{ style: 'width:160px' }">
        <div class="form-top-titles-common">货架信息基本配置</div>
        <div class="form-top-line-common"></div>
        <a-form-item label="货架名称" name="name">
          <a-input placeholder="请输入" style="width:300px;" v-model:value="formFields.name" show-count :maxlength="20" />
        </a-form-item>
        <a-form-item label="货架上架时间" name="onType">
          <a-radio-group v-model:value="formFields.onType" style="margin-bottom: 16px">
            <a-radio-button :value="2">时间段</a-radio-button>
            <a-radio-button :value="1">永久有效</a-radio-button>
          </a-radio-group>
          <template v-if="formFields.onType === 2">
            <BaseDateTimeRange label="" name="dateTimeRange" v-model="formFields.dateTimeRange" />
          </template>
        </a-form-item>
        <a-form-item label="货架优先级" name="priority">
          <a-input placeholder="请输入" style="width:300px;" v-model:value="formFields.priority" show-count :maxlength="3" @blur="priorityHandler" />
          <span class="ui-c-grey ml5">优先级 0-999 不能重复</span>
          <div class="ui-c-grey">同时生效多个货架时，根据优先级展示，数值越高优先级越高</div>
        </a-form-item>
        <div class="form-top-titles-common">货架人群条件</div>
        <div class="form-top-line-common"></div>
        <a-form-item label="是否根据人群展示" name="limitShow">
          <a-radio-group v-model:value="formFields.limitShow">
            <a-radio-button :value="1">限制</a-radio-button>
            <a-radio-button :value="0">不限制</a-radio-button>
          </a-radio-group>
          <div class="ui-c-grey">限制人群条件后，对应人群才可见到对应货架</div>
        </a-form-item>
        <ShelfCrowdConditionsFormItem v-if="thisFields.open && formFields.limitShow === 1" :timeType="1" :formFields="formFields" :formRules="formRules" :formRef="formRef" />
        <div class="form-top-titles-common">货架商品管理</div>
        <div class="form-top-line-common"></div>
        <div class="ui-shelf-add-product-and-management-product">
          <div class="_left">
            <span style="width: 160px;text-align: right;">目前共上架商品：</span>
            <span>{{ formFields.saveShelfProductList?.length }} 件</span>
          </div>
          <ShelfAddProductAndManagementProduct v-if="thisFields.open" :formFields="formFields" :shelfId="formFields.id" @addOk="ShelfAddProductAndManagementProductHandlerOk" @changeCurrentInventory="changeCurrentInventory" @managementOk="ShelfAddProductAndManagementProductHandlerOk" />
        </div>
        <a-form-item label="库存较少：">{{ thisFields.less }} 件</a-form-item>
        <a-form-item label="库存售罄：">{{ thisFields.zero }} 件</a-form-item>
        <div class="form-top-titles-common">限制弹窗设置</div>
        <div class="form-top-line-common"></div>
        <a-form-item label="库存售罄时">
          <jumpModal :data="formFields.content" @ok="contentChange">
            <a-button>点击配置弹窗</a-button>
          </jumpModal>
        </a-form-item>
      </a-form>
    </a-spin>
    <template #footer v-if="!disabled">
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="ok" :loading="thisFields.loading">确定</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>
<script setup>
import ShelfCrowdConditionsFormItem from '@/views/mall/shelfManagement/giftRack/components/ShelfCrowdConditionsFormItem.vue'
import ShelfAddProductAndManagementProduct from '@/views/mall/shelfManagement/giftRack/components/ShelfAddProductAndManagementProduct.vue'
import { cloneDeep } from 'lodash'
import { useGlobalStore } from '@/store'
import { message, Modal } from 'ant-design-vue'
import { apiGiftRack } from '@/http/index.js'
import { reactive } from 'vue'

import { v4 as uuidv4 } from 'uuid'
const global = useGlobalStore()
const formRef = ref(null)
const emits = defineEmits(['cancel', 'ok', 'update:visible'])
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  id: {
    type: [String, Number],
    default: ''
  },
  type: {
    type: [String, Number],
    default: 0 // 0-新增 1-编辑 2-查看 3-复制
  }
})
const disabled = computed(() => {
  return +props.type === 2
})
const title = computed(() => {
  return ['新增', '编辑', '查看', '复制'][props.type] + '货架'
})
const getDefaultFormFields = () => ({
  onType: 2,
  dateTimeRange: [],
  priority: '',
  limitShow: 1,
  saveShelfProductList: [],
  name: '',
  crowdDTO: {},
  content: {
    imgUrl: '',
    imgLinks: [

    ]
  }
})
const formFields = reactive(getDefaultFormFields())
const formRules = reactive({
  onType: [{ required: true, message: '本项必选' }],
  dateTimeRange: [{ required: true, message: '本项必选' }],
  priority: [{ required: true, message: '本项必填' }],
  limitShow: [{ required: true, message: '本项必选' }],
  name: [{ required: true, message: '本项必填' }],
  content: {
    imgUrl: [{ required: false, message: '请设置库存售罄' }],
  }
})
function contentChange(data) {
  formFields.content.imgUrl = data.imgUrl
  formFields.content.imgLinks = data.imgLinks
  console.log("🚀 ~ contentChange ~  formFields.content:", formFields.content)
  nextTick(() => {
    // formRef.value.validateFields(['content', 'imgUrl'])
    // formRef.value.clearValidate(['content', 'imgUrl'])

  })
}
const ShelfAddProductAndManagementProductHandlerOk = (beSelectedProductIdArrayObjectList) => {
  if (beSelectedProductIdArrayObjectList) formFields.saveShelfProductList = beSelectedProductIdArrayObjectList
  const saveShelfProductList = formFields.saveShelfProductList.map(v => {
    if (v.id) {
      return +v.currentInventory || 0
    } else {
      return +v.onInventory || 0
    }
  })
  thisFields.less = saveShelfProductList.filter(v => (0 < v && v < 10)).length
  thisFields.zero = saveShelfProductList.filter(v => (v === 0)).length
}
const thisFields = reactive({
  less: 0,
  zero: 0,
  allPriority: [],
  open: props.visible,
  loading: false
})

watch(() => props.visible, () => {
  thisFields.open = props.visible
  Object.keys(formFields).forEach(key => (delete formFields[key]))
  Object.assign(formFields, getDefaultFormFields())
  formRef.value?.clearValidate()
  if (thisFields.open) {
    thisFields.less = 0
    thisFields.zero = 0
    initData()
  }
})
const changeCurrentInventory = () => {
  initData()
}
const initData = async () => {

  await getAllPriority().finally(() => thisFields.loading = false)
  if (!props.id) return
  thisFields.loading = true
  const res = await apiGiftRack.getPageDetail({ id: props.id }).finally(() => thisFields.loading = false)
  if (!res.data.crowdDTO) {
    res.data.crowdDTO = {
      crowdType: 0,
      crowdId: null,
      crowdConditionList: [],
      crowdImportResultDTO: {
        memberCodeList: [],
        fileName: ''
      },
      crowdName: '',
      timeType: ''
    }
    if (res.data.groupId > 0) {
      res.data.crowdDTO.crowdType = 1
      res.data.crowdDTO.crowdId = res.data.groupId
    }
  }
  if (res.data.content) {
    res.data.content = JSON.parse(res.data.content)
  } else {
    res.data.content = {
      imgUrl: '',
      imgLinks: [

      ]
    }
  }
  Object.assign(formFields, res.data)
  console.log("🚀 ~ initData ~ formFields:", formFields)
  if (props.type === 3) {
    formFields.id = undefined
    formFields.name += '（复制）'
    formFields.saveShelfProductList.forEach(v => {
      v.id = undefined
    })
  }
  ShelfAddProductAndManagementProductHandlerOk(formFields.saveShelfProductList)
}

const getAllPriority = async () => {
  thisFields.loading = true
  const res = await apiGiftRack.getAllPriority()
  thisFields.allPriority = res.data.sort((v1, v2) => v1.priority - v2.priority)
}

let priorityIsRepeat = false
const priorityHandler = async () => {
  const match = String(formFields.priority).match(/(0)|([1-9]\d*)/ig) || []
  formFields.priority = match[0] || ''
  priorityIsRepeat = thisFields.allPriority.findIndex(v => (v.priority === +formFields.priority && v.id !== +formFields.id)) !== -1
  // priorityIsRepeat = thisFields.allPriority.includes(+formFields.priority)
  await priorityIsRepeatHandler()
}
const priorityIsRepeatHandler = async () => {
  if (priorityIsRepeat) {
    Modal.confirm({
      title: '优先级不能重复，请避开下述优先级',
      content: thisFields.allPriority.map(v => v.priority).join('、')
    })
    throw Error('优先级重复')
  } else {
  }
}
const onClose = () => {
  emits('cancel')
  emits('update:visible', false)
}
const ok = async () => {
  // console.log('ok：', formFields)
  await formRef.value.validate()
  await priorityHandler()
  // if (!formFields.saveShelfProductList.length) {
  //   return message.warning('请添加商品')
  // } else if (formFields.saveShelfProductList.every(v => !v.onInventory)) {
  //   return message.warning('请管理商品')
  // }
  if (formFields.saveShelfProductList.length) {
    if (formFields.saveShelfProductList.every(v => !v.onInventory)) {
      return message.warning('请管理商品')
    }
  }
  let params = cloneDeep(formFields)
  params.content = JSON.stringify(params.content)
  thisFields.loading = true
  const isAdd = [0, 3].includes(props.type)
  const res = await apiGiftRack[isAdd ? 'createPage' : 'updatePage'](params).finally(() => thisFields.loading = false)
  message.success(res.msg)
  emits('ok', isAdd ? '' : props.id)
}
</script>

<style scoped lang="scss">
:deep(.searchForm .ant-form-item) {
  margin-bottom: 16px;
}
</style>
