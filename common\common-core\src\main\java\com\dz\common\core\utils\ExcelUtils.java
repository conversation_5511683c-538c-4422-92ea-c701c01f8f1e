package com.dz.common.core.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.DownloadHeaderDTO;
import com.dz.common.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Excel导出下载工具
 * @Author: Handy
 * @Date: 2022/7/28 13:48
 */
@Slf4j
public class ExcelUtils {

    /**
     * excel导出
     * @param data
     * @param templateClass
     * @param fileName
     * @param response
     * @throws IOException
     */
    public static void excelExport(Collection<?> data,Class<?> templateClass,String fileName,HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + "." + ExcelTypeEnum.XLSX.getValue(), "UTF-8"));
            // 这里需要设置不关闭流
            EasyExcel.write(response.getOutputStream(), templateClass).autoCloseStream(Boolean.FALSE).sheet("sheet1")
                    .doWrite(data);
        } catch (Exception e) {
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            Result result = new Result();
            result.setCode(ErrorCode.INTERNAL_ERROR.getCode());
            result.setMsg("下载文件失败" + e.getMessage());
            try {
                response.getWriter().println(JSON.toJSONString(result));
            } catch (IOException ioException) {
                log.error("下载文件失败",ioException);
            }
        }
    }

    /**
     * excel导入
     * @param excelFile
     * @param targetClass
     * @param <T>
     * @return
     */
    public static <T> List<T> excelImport(MultipartFile excelFile,Class<T> targetClass) {
        try {
            List<T> list = new ArrayList<>();
            EasyExcel.read(excelFile.getInputStream(), targetClass, new PageReadListener<T>(dataList -> {
                list.addAll(dataList);
            })).sheet().doRead();
            return list;
        } catch (IOException e) {
            log.error("解析导入数据失败", e);
            throw new BusinessException("解析导入数据失败");
        }
    }

    /**
     * @param headerList 表头
     * @param dataList   报表数据内容
     * @param fileName   文件名
     * @return byte[] 文件二进制
     */
    public static byte[] write(List<DownloadHeaderDTO> headerList, List<JSONObject> dataList, String fileName) throws IOException {
        // 声明一个工作薄
        SXSSFWorkbook workbook = new SXSSFWorkbook();
        // 生成一个表格
        //sheet只保留文字
        String substring = fileName.replaceAll("[^\\u4e00-\\u9fa5]", "");
        SXSSFSheet sheet = workbook.createSheet(substring);
        // 设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth((int) 25);

        //表头格式
        XSSFCellStyle headerStyle = ExcelUtils.getHeaderStyle(workbook);
        //内容格式
        XSSFCellStyle contentStyle = ExcelUtils.getContentStyle(workbook);
        //表头内容
        SXSSFRow row = sheet.createRow(0);
        int i = 0;
        List<DownloadHeaderDTO> collect = headerList.stream().
                sorted(Comparator.comparing(DownloadHeaderDTO::getSort)).collect(Collectors.toList());
        for (DownloadHeaderDTO hd : collect) {
            SXSSFCell cell = row.createCell(i);
            cell.setCellStyle(headerStyle);
            XSSFRichTextString text = new XSSFRichTextString(hd.getName());
            cell.setCellValue(text);
            i++;
        }
        // 游标
        int begin = 0;
        //数据填充
        for (JSONObject da : dataList) {
            begin++;
            SXSSFRow createRow = sheet.createRow(begin);
            // 重置下标
            i = 0;
            for (DownloadHeaderDTO hd : collect) {
                String value = da.getString(hd.getCode());
                SXSSFCell createCell = createRow.createCell(i);
                createCell.setCellStyle(contentStyle);
                if (value == null) {
                    value = "";
                }
                //表数据值替换
                if (!StringUtils.isEmpty(hd.getReplace())) {
                    for (String s : hd.getReplace().split(",")) {
                        String[] split = s.split("_");
                        if (split[1].equals(value)) {
                            value = split[0];
                        }
                    }
                }
                //日期格式化
                if (!StringUtils.isEmpty(hd.getFormat())) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat(hd.getFormat());
                    if (!StringUtils.isEmpty(value)) {
                        value = simpleDateFormat.format(da.getDate(hd.getCode()));
                    }
                }

                XSSFRichTextString text = new XSSFRichTextString(value);
                createCell.setCellValue(text);
                i++;
            }
        }
        //生成文件
        File targetFile = new File(fileName);

        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            workbook.write(os);
        } catch (IOException e) {
            log.error("生产文件错误", e);
            throw new BusinessException("写入文件出错");
        } finally {
            if (!ObjectUtils.isEmpty(workbook)) {
                workbook.close();
            }
        }
        byte[] content = os.toByteArray();
        InputStream input = new ByteArrayInputStream(content);
        FileUtils.copyInputStreamToFile(input, targetFile);
        byte[] bytes = FileUtils.readFileToByteArray(targetFile);
        //清理临时文件
        boolean deleteB = targetFile.delete();
        if(!deleteB){
            log.error("write文件delete失败,文件名称:{}",fileName);
        }
        return bytes;
    }

    public static byte[] writeDynamic(List<List<String>> headList, List<List<String>> dataList, String fileName) throws IOException {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        File targetFile = new File(fileName);
        EasyExcel.write(os)
                .head(headList)
                .sheet("报表数据")
                .doWrite(dataList);
        byte[] content = os.toByteArray();
        InputStream input = new ByteArrayInputStream(content);
        FileUtils.copyInputStreamToFile(input, targetFile);
        byte[] bytes = FileUtils.readFileToByteArray(targetFile);
        //清理临时文件
        boolean deleteB = targetFile.delete();
        if(!deleteB){
            log.error("writeDynamic方法delete失败,文件名称:{}",fileName);
        }
        return bytes;
    }

    /**
     * 设置标题的style
     *
     * <AUTHOR>
     * @date 2022-08-18
     */
    public static XSSFCellStyle getHeaderStyle(Workbook workbook) {
        // 生成一个样式（用于标题）
        XSSFCellStyle style = (XSSFCellStyle) workbook.createCellStyle();
        //水平居中
        style.setAlignment(HorizontalAlignment.CENTER);
        //垂直居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        //设置背景色
        style.setFillForegroundColor(IndexedColors.TEAL.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);

        // 生成一个字体
        XSSFFont font = (XSSFFont) workbook.createFont();
        font.setColor(IndexedColors.BLACK.getIndex());
        font.setFontHeightInPoints((short) 12);
        font.setBold(false);
        // 把字体应用到当前的样式
        style.setFont(font);

        return style;
    }

    /**
     * 设置内容的style
     *
     * <AUTHOR>
     * @date 2022-08-18
     */
    public static XSSFCellStyle getContentStyle(Workbook workbook) {
        // 生成并设置另一个样式（用于内容）
        XSSFCellStyle style = (XSSFCellStyle) workbook.createCellStyle();
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        // 生成另一个字体
        XSSFFont font = (XSSFFont) workbook.createFont();
        font.setBold(false);
        // 把字体应用到当前的样式
        style.setFont(font);
        return style;
    }

}
