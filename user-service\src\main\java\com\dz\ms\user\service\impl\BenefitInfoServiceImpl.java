package com.dz.ms.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.user.constants.CacheKeys;
import com.dz.ms.user.dto.BenefitInfoDTO;
import com.dz.ms.user.entity.BenefitInfo;
import com.dz.ms.user.mapper.BenefitInfoMapper;
import com.dz.ms.user.service.BenefitInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 会员权益
 * @author: Handy
 * @date:   2023/08/08 01:14
 */
@Service
public class BenefitInfoServiceImpl extends ServiceImpl<BenefitInfoMapper,BenefitInfo> implements BenefitInfoService {

	@Resource
    private BenefitInfoMapper benefitInfoMapper;
    @Resource
    private RedisService redisService;

    /**
     * 分页查询会员权益
     * @param param
     * @return PageInfo<BenefitInfoDTO>
     */
    @Override
    public PageInfo<BenefitInfoDTO> getBenefitInfoList(BenefitInfoDTO param) {
        BenefitInfo benefitInfo = BeanCopierUtils.convertObjectTrim(param,BenefitInfo.class);
        IPage<BenefitInfo> page = benefitInfoMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), new LambdaQueryWrapper<BenefitInfo>(benefitInfo).orderByDesc(BenefitInfo::getCreated));
        return new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),BeanCopierUtils.convertList(page.getRecords(), BenefitInfoDTO.class));
    }

    /**
     * 查询所有会员权益
     * @param param
     * @return
     */
    public List<BenefitInfoDTO> getAllBenefitInfoList(BenefitInfoDTO param) {
        BenefitInfo benefitInfo = BeanCopierUtils.convertObjectTrim(param,BenefitInfo.class);
        List<BenefitInfo> list = benefitInfoMapper.selectList(new LambdaQueryWrapper<BenefitInfo>(benefitInfo).orderByAsc(BenefitInfo::getSort));
        return BeanCopierUtils.convertList(list, BenefitInfoDTO.class);
    }

    /**
     * 根据ID查询会员权益
     * @param id
     * @return BenefitInfoDTO
     */
    @Override
    public BenefitInfoDTO getBenefitInfoById(Long id) {
        BenefitInfo benefitInfo = benefitInfoMapper.selectById(id);
        return BeanCopierUtils.convertObject(benefitInfo,BenefitInfoDTO.class);
    }

    /**
     * 保存会员权益
     * @param param
     * @return Long
     */
    @Override
    public Long saveBenefitInfo(BenefitInfoDTO param) {
        BenefitInfo benefitInfo = new BenefitInfo(param.getId(), param.getBenefitName(), param.getBenefitImg(), param.getUnActivateImg(),param.getPopupImg(), param.getDetails(), param.getSort(), param.getJumpLink(), param.getState());
        if (param.getActivateImg()!= null){
            benefitInfo.setActivateImg(param.getActivateImg());
        }
        if(ParamUtils.isNullOr0Long(benefitInfo.getId())) {
            benefitInfoMapper.insert(benefitInfo);
        }
        else {
            benefitInfoMapper.updateById(benefitInfo);
            redisService.delAll(CacheKeys.GRADE_INFO+":"+ SecurityContext.getUser().getTenantId());
        }
        return benefitInfo.getId();
    }

    /**
     * 根据ID删除会员权益
     * @param param
     */
    @Override
    public void deleteBenefitInfoById(IdCodeDTO param) {
        benefitInfoMapper.deleteById(param.getId());
    }
	
}