package com.dz.ms.product.dto.res;

import com.dz.ms.product.dto.ShelfProductSuperscriptDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 货架商品所有角标
 * @author: fei
 * @date: 2024/12/09 15:18
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@ApiModel(value = "货架商品所有角标")
public class ShelfProductAllSuperscriptResDTO {

    @ApiModelProperty(value = "货架商品ID")
    private Long shelfProductId;
    @ApiModelProperty(value = "展示角标列表")
    private List<ShelfProductSuperscriptDTO> shelfProductSuperscriptList;
    @ApiModelProperty(value = "活动角标")
    private String ruleName;
}
