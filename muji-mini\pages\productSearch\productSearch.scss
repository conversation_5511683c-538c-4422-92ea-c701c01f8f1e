.page {
  display: flex;
  flex-direction: column;
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
  height: 100vh;
}

page {
  overflow: auto;
  /* box-sizing: inherit; */
}

.page-header {
  margin-top: 20rpx;

  .search-box {
    width: 670rpx;
    height: 80rpx;
    background: #F5F5F5;
    border-radius: 5rpx;
    margin: 0 auto;
    box-sizing: border-box;
    padding: 0 20rpx;
  }
}

.page-header-tips {
  margin: 40rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 24rpx;
  color: #3C3C43;
  line-height: 33rpx;
  text-align: left;
  font-style: normal;
}

.shop-list {
  flex: 1;
  height: 0;

}

.page-shop-list {

  /* flex: 1;
  height: 0; */
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 40rpx;
  padding-bottom: 40rpx;
  /* overflow: hidden; */
}
