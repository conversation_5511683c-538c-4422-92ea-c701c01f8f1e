package com.dz.ms.product.dto.req;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 货架推广活动查询入参DTO
 *
 * @author: LiinNs
 * @date: 2024/11/20 11:36
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "货架推广活动查询入参")
public class ShelfPromotionParamDTO extends BaseDTO {

    @ApiModelProperty(value = "名称")
    private String name;
    @ApiModelProperty(value = "推广开始时间")
    private Date onStartTime;
    @ApiModelProperty(value = "推广结束时间")
    private Date onEndTime;
    @ApiModelProperty(value = "推广状态")
    private Integer promotionState;

}
