package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 租户信息
 * @author: Handy
 * @date:   2022/07/20 13:18
 */
@Getter
@Setter
@NoArgsConstructor
@Table("租户信息")
@TableName(value = "tenant_info")
public class TenantInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "租户ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR,length = 36,isNull = true,comment = "品牌编码",isUnique = true)
    private String tenantCode;
    @Columns(type = ColumnType.VARCHAR,length = 20,isNull = true,comment = "品牌中文名称")
    private String cnName;
    @Columns(type = ColumnType.VARCHAR,length = 20,isNull = true,comment = "品牌英文名称")
    private String enName;
    @Columns(type = ColumnType.VARCHAR,length = 300,isNull = true,comment = "品牌介绍")
    private String introduce;
    @Columns(type = ColumnType.VARCHAR,length = 300,isNull = true,comment = "品牌logo URL")
    private String logoUrl;
    @Columns(type = ColumnType.VARCHAR,length = 300,isNull = true,comment = "登录页logo")
    private String bigLogo;
    @Columns(type = ColumnType.VARCHAR,length = 11,isNull = true,comment = "手机号码")
    private String mobile;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = false,defaultValue = "1",comment = "租户状态 0关闭 1正常")
    private Integer state;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = true,comment = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;

    public TenantInfo(Long id, String tenantCode, String cnName, String enName, String introduce, String logoUrl, String bigLogo, String mobile, Integer state) {
        this.id = id;
        this.tenantCode = tenantCode;
        this.cnName = cnName;
        this.enName = enName;
        this.introduce = introduce;
        this.logoUrl = logoUrl;
        this.bigLogo = bigLogo;
        this.mobile = mobile;
        this.state = state;
    }
}
