const fs = require('fs')

const px2rpxFileContent = (files) => {
  if (!files.length) return
  files.forEach(v => {
    let content = fs.readFileSync(v, 'utf-8')
    content = content.replace(/(?<!px)(-?\d+)rpx/ig, (match, firstGroup) => `px2rpx(${firstGroup})`)
    let hasImport = content.includes(`@import "assets/scss/config"`)
    if (!hasImport) {
      content = `@import "assets/scss/config";\n\n${content}`
    }
    fs.writeFileSync(v, content, 'utf-8')
  })
}
