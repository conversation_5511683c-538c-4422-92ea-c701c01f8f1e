package com.dz.common.core.dto.export;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PromotionExportDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "推广名称")
    private String name;
    @ApiModelProperty(value = "推广时间")
    private String promotionTimeStr;
    @ApiModelProperty(value = "关联货架")
    private String shelfName;
    @ApiModelProperty(value = "推广数量")
    private Integer num;
    @ApiModelProperty(value = "推广状态 1待开始/2进行中/3已结束")
    private Integer promotionState;
    @ApiModelProperty(value = "启停状态 0禁用 1启用")
    private Integer state;
}
