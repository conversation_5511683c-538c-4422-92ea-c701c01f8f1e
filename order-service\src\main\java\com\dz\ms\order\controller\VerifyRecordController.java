package com.dz.ms.order.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.order.dto.VerifyRecordDTO;
import com.dz.ms.order.service.VerifyRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;

@Api(tags="核销记录")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class VerifyRecordController  {

    @Resource
    private VerifyRecordService verifyRecordService;

    /**
     * 分页查询核销记录
     * @param param
     * @return result<PageInfo<VerifyRecordDTO>>
     */
    @ApiOperation("分页查询核销记录")
	@GetMapping(value = "/verify_record/list")
    public Result<PageInfo<VerifyRecordDTO>> getVerifyRecordList(@ModelAttribute VerifyRecordDTO param) {
        Result<PageInfo<VerifyRecordDTO>> result = new Result<>();
		PageInfo<VerifyRecordDTO> page = verifyRecordService.getVerifyRecordList(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询核销记录
     * @param id
     * @return result<VerifyRecordDTO>
     */
    @ApiOperation("根据ID查询核销记录")
	@GetMapping(value = "/verify_record/info")
    public Result<VerifyRecordDTO> getVerifyRecordById(@RequestParam("id") Long id) {
        Result<VerifyRecordDTO> result = new Result<>();
        VerifyRecordDTO verifyRecord = verifyRecordService.getVerifyRecordById(id);
        result.setData(verifyRecord);
        return result;
    }

    /**
     * 新增核销记录
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增核销记录",type = LogType.OPERATELOG)
    @ApiOperation("新增核销记录")
	@PostMapping(value = "/verify_record/add")
    public Result<Long> addVerifyRecord(@RequestBody VerifyRecordDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,true);
        Long id = verifyRecordService.saveVerifyRecord(param);
        result.setData(id);
        return result;
    }

    /**
    * 更新核销记录
    * @param param
    * @return result<Object>
    */
    @SysLog(value = "更新核销记录",type = LogType.OPERATELOG)
    @ApiOperation("更新核销记录")
    @PostMapping(value = "/verify_record/update")
    public Result<Long> updateVerifyRecord(@RequestBody VerifyRecordDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,false);
        verifyRecordService.saveVerifyRecord(param);
        result.setData(param.getId());
        return result;
    }

    /**
    * 验证保存参数
    * @param param
    */
    private void validationSaveParam(VerifyRecordDTO param,boolean isAdd) {
        if(isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if(!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
    }

	/**
     * 根据ID删除核销记录
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除核销记录")
	@PostMapping(value = "/verify_record/delete")
    public Result<Boolean> deleteVerifyRecordById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        verifyRecordService.deleteVerifyRecordById(param);
        result.setData(true);
        return result;
    }

}
