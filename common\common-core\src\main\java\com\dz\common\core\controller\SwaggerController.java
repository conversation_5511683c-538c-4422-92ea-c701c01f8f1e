package com.dz.common.core.controller;

import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.beans.factory.InitializingBean;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;

@Slf4j
@RestController
public class SwaggerController implements InitializingBean {

    @Autowired
    private Environment environment;

    private OpenAPI openAPI;

    @Override
    public void afterPropertiesSet() {
        openAPI = new OpenAPI().info(new Info()
                .title("API Documentation")
                .description("Generated API documentation")
                .version("2.0"));
    }

    /**
     * 返回Swagger的OpenAPI文档
     */
    @GetMapping(value = "/api-docs", produces = {"application/json", "application/hal+json"})
    public ResponseEntity<String> getDocumentation(
            @RequestParam(value = "group", required = false) String swaggerGroup,
            HttpServletRequest servletRequest) {

        // 在这里可以通过 OpenAPI 获取所有的文档信息
        // 例如：openAPI 获取所有路径或其他相关信息

        JSONObject json = new JSONObject();
        json.put("info", openAPI.getInfo());

        // 如果有group参数，还可以根据不同group来做一些路由调整
        // 这里简单的返回整个 OpenAPI 信息，可以根据需求做进一步的处理

        return new ResponseEntity<>(json.toJSONString(), HttpStatus.OK);
    }

    /**
     * API 分组配置，根据需求调整不同的API组
     */
    @Bean
    public GroupedOpenApi crmApi() {
        return GroupedOpenApi.builder()
                .group("crm")
                .pathsToMatch("/crm/**")
                .build();
    }

    @Bean
    public GroupedOpenApi appApi() {
        return GroupedOpenApi.builder()
                .group("app")
                .pathsToMatch("/app/**")
                .build();
    }

    @Bean
    public GroupedOpenApi openApi() {
        return GroupedOpenApi.builder()
                .group("openapi")
                .pathsToMatch("/openapi/**")
                .build();
    }

    @Bean
    public GroupedOpenApi omsApi() {
        return GroupedOpenApi.builder()
                .group("oms")
                .pathsToMatch("/oms/**")
                .build();
    }

}
