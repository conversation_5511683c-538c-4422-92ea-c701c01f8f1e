package com.dz.ms.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.core.dto.IdNumberDTO;
import com.dz.common.core.dto.product.CpStaticDTO;
import com.dz.ms.product.dto.ShelfDTO;
import com.dz.ms.product.dto.ShelfProductDTO;
import com.dz.ms.product.dto.req.ShelfProductMiniParamDTO;
import com.dz.ms.product.dto.req.ShelfProductParamDTO;
import com.dz.ms.product.dto.res.ShelfProductMiniResDTO;
import com.dz.ms.product.entity.ShelfProduct;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 货架商品Mapper
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:25
 */
@Repository
public interface ShelfProductMapper extends BaseMapper<ShelfProduct> {

    /**
     * 查看商品所在已启用的货架
     */
    List<ShelfDTO> selectListByProductId(@Param("productId") Long productId);

    /**
     * 查看商品所在已启用的货架名称列表
     */
    List<String> selectShelfNamesByProductId(@Param("productId") Long productId);

    /**
     * 根据货架ID列表查询货架上架商品数量列表
     * @param shelfIds 架ID列表
     * @return List<IdNumberDTO>
     */
    List<IdNumberDTO> selectProductSumByShelfIds(@Param("shelfIds") List<Long> shelfIds);

    /**
     * 查询少量字段
     */
    List<ShelfProductDTO> selLessList(@Param("shelfIds") List<Long> shelfIds, @Param("productIds") List<Long> productIds, @Param("shelfProductIds") List<Long> shelfProductIds);

    /**
     * 查询所有字段
     */
    List<ShelfProductDTO> selAllList(@Param("shelfIds") List<Long> shelfIds, @Param("productIds") List<Long> productIds, @Param("shelfProductIds") List<Long> shelfProductIds);

    /**
     * 查询积分相关字段
     */
    List<ShelfProductDTO> selPointCorrelationsList(@Param("shelfIds") List<Long> shelfIds, @Param("productIds") List<Long> productIds, @Param("shelfProductIds") List<Long> shelfProductIds);

    /**
     * CRM分页查询货架商品
     */
    IPage<ShelfProduct> selPageList(Page<Object> objectPage, @Param("param") ShelfProductParamDTO param);

    /**
     * APP分页查询货架商品
     */
    IPage<ShelfProductMiniResDTO> selMiniPageList(Page<Object> objectPage, @Param("param") ShelfProductMiniParamDTO param);

    /**
     * 根据ids查询少量字段
     */
    List<ShelfProductDTO> selLessListByIds(@Param("ids") List<Long> ids);

    /**
     * 根据货架ID，商品ID更新货架商品库存
     *
     * @param shelfId
     * @param productId
     * @param onInventory
     * @return
     */
    int updateInventoryByShelfProduct(@Param("shelfId") Long shelfId, @Param("productId") Long productId, @Param("onInventory") Integer onInventory);

    int updateInventory(@Param("isAdd") Integer isAdd, @Param("id") Long id, @Param("num") Integer num);

    int updateStatic(@Param("shelfProductId") Long shelfProductId, @Param("number") Integer number);

    int validateInventory(@Param("id") Long id, @Param("num") Integer num);

    List<CpStaticDTO> selectByShelfProductIds(@Param("shelfProductIdList") List<Long> shelfProductIdList);

    void updateShelfProduct(ShelfProduct param);


    int updateAddInventory(ShelfProduct toUpdate);
}
