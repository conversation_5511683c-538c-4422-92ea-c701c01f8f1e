<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.adaptor.mapper.PointsSubscriptionRecordMapper" >

    <select id="selectPageByParam" resultType="com.dz.ms.adaptor.entity.ExpirePointsRecord">
        select
            id,member_code,expire_points,bonus_amount
        from t_expire_points_record_${nowDay} where DATE_FORMAT(create_time, '%Y%m%d') = DATE_FORMAT(now(), '%Y%m%d')
    </select>

    <select id="selectPageByParamMsg" resultType="com.dz.ms.adaptor.entity.ExpirePointsRecord">
        select
            id,member_code
        from t_expire_points_record_${nowDay} where DATE_FORMAT(create_time, '%Y%m%d') = DATE_FORMAT(now(), '%Y%m%d')
    </select>

    <select id="selectPageByParamStatus" resultType="com.dz.ms.adaptor.entity.ExpirePointsRecord">
        select
            id,member_code,expire_points,bonus_amount
        from t_expire_points_record_${nowDay} where DATE_FORMAT(create_time, '%Y%m%d') = DATE_FORMAT(now(), '%Y%m%d') and is_points_subscription=2
    </select>

    <select id="selectPageByParamMsgStatus" resultType="com.dz.ms.adaptor.entity.ExpirePointsRecord">
        select
            id,member_code
        from t_expire_points_record_${nowDay} where DATE_FORMAT(create_time, '%Y%m%d') = DATE_FORMAT(now(), '%Y%m%d') and is_send_my_msg=0
    </select>

    <update id="updateSubscriptionStatusById">
        update t_expire_points_record_${nowDay} set is_points_subscription=1 where id=#{id}
    </update>
    <update id="updateSubscriptionStatusByIdFail">
        update t_expire_points_record_${nowDay} set is_points_subscription=2 where id=#{id}
    </update>
    <update id="updateSendMyMsgStatusById">
        update t_expire_points_record_${nowDay} set is_send_my_msg=1 where id=#{id}
    </update>
    <!--获取分表列表-->
    <select id="queryPointsSubscriptionTableList" resultType="java.lang.String">
        SHOW TABLES LIKE 't_expire_points_record_%'
    </select>

    <insert id="insertBatchSomeColumn">
        INSERT INTO t_expire_points_record_${nowDay} (
        member_code, bonus_amount, expire_points, create_time,tenant_id
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.memberCode}, #{item.bonusAmount}, #{item.expirePoints}, #{item.createTime}, #{item.tenantId}
            )
        </foreach>
    </insert>

    <!--创建分表-->
    <select id="createPointsSubscriptionTable" parameterType="java.lang.String" statementType="STATEMENT">
        CREATE TABLE IF NOT EXISTS t_expire_points_record_${nowDay}  (
                                                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                        `member_code` varchar(255) DEFAULT NULL COMMENT '用户会员code',
                                                        `bonus_amount` int DEFAULT NULL COMMENT '当前可用积分数量',
                                                        `expire_points` int DEFAULT NULL COMMENT '即将过期积分',
                                                        `create_time` datetime NOT NULL COMMENT '创建时间',
                                                        `tenant_id` bigint NOT NULL COMMENT '租户ID',
                                                        `is_points_subscription` tinyint DEFAULT '0' COMMENT '是否已发送订阅消息 0否，1是',
                                                        `is_send_my_msg` tinyint DEFAULT '0' COMMENT '是否已发送我的消息 0否，1是',
                                                       PRIMARY KEY (`id`) USING BTREE,
                                                        KEY `idx_member_code` (`member_code`),
                                                        KEY `idx_create_time` (`create_time`)
        ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='发送订阅消息记录';
    </select>
    
    <delete id="deleteBySevenDayAgo">
        delete from muji_adaptor.t_expire_points_record_${nowDay} where create_time <![CDATA[ < ]]> #{sevenDayAgo}
    </delete>
</mapper>
