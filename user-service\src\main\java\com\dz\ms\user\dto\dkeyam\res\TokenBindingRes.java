package com.dz.ms.user.dto.dkeyam.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 用户信息
 * @author: fei
 * @date: 2025/01/22 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TokenBindingRes implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("绑定关系id")
    private String id;

    @ApiModelProperty("绑定关系是否是有效的")
    private Boolean bindingEnabled;

    @ApiModelProperty("绑定关系生效的开始时间")
    private Long startTime;

    @ApiModelProperty("优先级")
    private Integer priority;

    @ApiModelProperty("用户信息信息")
    private UserInfoRes user;

    @ApiModelProperty("令牌信息信息")
    private TokenInfoRes token;
}
