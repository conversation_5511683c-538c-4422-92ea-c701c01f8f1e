package com.dz.ms.basic.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.user.SysUserDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.fegin.user.SysUserFeginClient;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.ms.basic.dto.*;
import com.dz.ms.basic.entity.MiniappTemplate;
import com.dz.ms.basic.entity.PromotionChannel;
import com.dz.ms.basic.service.PromotionChannelService;
import com.dz.ms.basic.service.PromotionPageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Api(tags="推广渠道")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class PromotionChannelController {
    private static final Logger log = LoggerFactory.getLogger(PromotionChannelController.class);
    @Resource
    private PromotionChannelService promotionChannelService;
    @Resource
    private SysUserFeginClient sysUserFeginClient;

//    public Result<PageInfo<PromotionChannelListDTO>> getPromotionPageList(@RequestParam(value = "pageNum",required = false)Integer pageNum, @RequestParam(value = "pageSize",required = false)Integer pageSize,
//                                                                      @RequestParam(value = "channelName",required = false)String channelName) {
//        Result<PageInfo<PromotionChannelListDTO>> result = new Result<>();
//        PromotionChannel promotionChannel = new PromotionChannel();
//        LambdaQueryWrapper<PromotionChannel> wrapper = new LambdaQueryWrapper<>(promotionChannel);
//        wrapper.eq(PromotionChannel::getIsDeleted,0);
//        if(channelName!= null) {
//            wrapper.like(PromotionChannel::getChannelName,channelName);
//        }
//        List<PromotionChannelListDTO> list = new ArrayList<>();
//        wrapper.orderByDesc(PromotionChannel::getCreateTime);
//        IPage<PromotionChannel> page = promotionChannelService.page(new Page<>(pageNum, pageSize), wrapper);
//        if(!CollectionUtils.isEmpty(page.getRecords())){
//            List<Long> ids = page.getRecords().stream().map(PromotionChannel::getCreator).distinct().collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
//            List<SysUserDTO> creatorUsers = sysUserFeginClient.getUserByIds(ids).getData();
//            log.info("用户信息为:{}", JSON.toJSONString(creatorUsers));
//            Map<Long,SysUserDTO> creatorMap = creatorUsers.stream().collect(Collectors.toMap(SysUserDTO :: getId, su -> su));
//            page.getRecords().forEach(channel->{
//                PromotionChannelDTO dto = BeanCopierUtils.convertObject(channel,PromotionChannelDTO.class);
//                dto.setCreatorName(creatorMap.containsKey(channel.getCreator()) ? creatorMap.get(channel.getCreator()).getRealname() : "");
//                list.add(dto);
//            });
//        }
//        result.setData(new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),list));
//        return result;
//    }
    /**
     * 分页查询页面推广
     * @return result<PageInfo<PromotionPageDTO>>
     */
    @ApiOperation("分页查询推广渠道")
    @GetMapping(value = "/crm/promotion_channel/list")
    public Result<PageInfo<PromotionChannelListDTO>> getPromotionPageList1(@RequestParam(value = "pageNum", required = false) Integer pageNum, @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                          @RequestParam(value = "channelName", required = false) String channelName) {
        Result<PageInfo<PromotionChannelListDTO>> result = new Result<>();
        PromotionChannel promotionChannel = new PromotionChannel();
        LambdaQueryWrapper<PromotionChannel> wrapper = new LambdaQueryWrapper<>(promotionChannel);
        wrapper.eq(PromotionChannel::getIsDeleted, 0);
        if (channelName != null) {
            wrapper.like(PromotionChannel::getChannelName, channelName);
        }
        List<PromotionChannelListDTO> list = new ArrayList<>();
        wrapper.orderByDesc(PromotionChannel::getCreateTime);
        IPage<PromotionChannel> page = promotionChannelService.page(new Page<>(pageNum, pageSize), wrapper);
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            List<Long> ids = page.getRecords().stream().map(PromotionChannel::getCreator).distinct().collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
            List<SysUserDTO> creatorUsers = sysUserFeginClient.getUserByIds(ids).getData();
            log.info("用户信息为:{}", JSON.toJSONString(creatorUsers));
            Map<Long, SysUserDTO> creatorMap = creatorUsers.stream().collect(Collectors.toMap(SysUserDTO::getId, su -> su));
            page.getRecords().forEach(channel -> {
                PromotionChannelListDTO dto = new PromotionChannelListDTO();
                if (channel.getParentId() != 0) {
                    PromotionChannel parentChannel = promotionChannelService.getById(channel.getParentId());
                    dto.setOneId(parentChannel.getId());
                    dto.setOneChannelName(parentChannel.getChannelName());
                    dto.setOneChannelParam(parentChannel.getChannelParam());
                    dto.setTwoId(channel.getId());
                    dto.setTwoChannelName(channel.getChannelName());
                    dto.setTwoChannelParam(channel.getChannelParam());
                    dto.setCreateTime(parentChannel.getCreateTime());
                }else {
                    dto.setOneId(channel.getId());
                    dto.setOneChannelName(channel.getChannelName());
                    dto.setOneChannelParam(channel.getChannelParam());
                    dto.setCreateTime(channel.getCreateTime());
                }
                dto.setCreatorName(creatorMap.containsKey(channel.getCreator()) ? creatorMap.get(channel.getCreator()).getRealname() : "");
                list.add(dto);
            });
        }
        result.setData(new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), list));
        return result;
    }
    @ApiOperation("查询推广渠道")
    @GetMapping(value = "/crm/promotion_channel/channelList")
    public Result<List<PromotionChannelTreeDTO>> getTreeList() {
        Result<List<PromotionChannelTreeDTO>> result = new Result<>();
        PromotionChannel promotionChannel = new PromotionChannel();
        LambdaQueryWrapper<PromotionChannel> oneWrapper = new LambdaQueryWrapper<>(promotionChannel);
        oneWrapper.eq(PromotionChannel::getIsDeleted, 0);
        oneWrapper.eq(PromotionChannel::getParentId, 0);
        List<PromotionChannel> page = promotionChannelService.list(oneWrapper);
        log.info("首次查询结果为:{}", JSON.toJSONString(page));
        List<PromotionChannelTreeDTO> list = new ArrayList<>();
        for (PromotionChannel oneChannel : page) {
            PromotionChannelTreeDTO oneDto = new PromotionChannelTreeDTO();
            oneDto.setId(oneChannel.getId());
            oneDto.setChannelName(oneChannel.getChannelName());
            oneDto.setChannelParam(oneChannel.getChannelParam());
            LambdaQueryWrapper<PromotionChannel> twoWrapper = new LambdaQueryWrapper<>(promotionChannel);
            log.info("二次查询结果为:{}", JSON.toJSONString(twoWrapper));
            twoWrapper.eq(PromotionChannel::getIsDeleted, 0);
            twoWrapper.eq(PromotionChannel::getParentId, oneChannel.getId());
            List<PromotionChannel> twoPage = promotionChannelService.list(twoWrapper);
            log.info("二次查询结果为:{}", JSON.toJSONString(twoPage));
            List<PromotionChannelDTO> twoList = new ArrayList<>();
            for (PromotionChannel twoChannel : twoPage) {
                PromotionChannelDTO twoDto = new PromotionChannelDTO();
                twoDto.setId(twoChannel.getId());
                twoDto.setChannelName(twoChannel.getChannelName());
                twoDto.setChannelParam(twoChannel.getChannelParam());
                twoList.add(twoDto);
            }
            oneDto.setTwoChannel(twoList);
            list.add(oneDto);
        }
        result.setData(list);
        return result;
    }

    @ApiOperation("查询一级渠道列表")
    @GetMapping(value = "/crm/promotion_channel/oneChannelList")
    public Result<List<PromotionChannelDTO>> getOneChannelList() {
        Result<List<PromotionChannelDTO>> result = new Result<>();
        PromotionChannel promotionChannel = new PromotionChannel();
        List<PromotionChannelDTO> list = new ArrayList<>();
        LambdaQueryWrapper<PromotionChannel> wrapper = new LambdaQueryWrapper<>(promotionChannel);
        wrapper.eq(PromotionChannel::getIsDeleted, 0);
        wrapper.eq(PromotionChannel::getParentId, 0);
        List<PromotionChannel> page = promotionChannelService.list(wrapper);
        result.setData(BeanCopierUtils.convertList(page, PromotionChannelDTO.class));
        return result;
    }

    /**
     * 新增小程序页面推广
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增小程序推广渠道",type = LogType.OPERATELOG)
    @ApiOperation("新增小程序推广渠道")
    @PostMapping(value = "/crm/promotion_channel/add")
    public Result<Long> addMiniappTemplate(@RequestBody PromotionChannelDTO param) {
        Result result = new Result<>();
        Long num = promotionChannelService.savePromotionChannel(param);
        result.setData(num);
        return result;
    }
    /**
     * 新增小程序页面推广
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "修改小程序推广渠道",type = LogType.OPERATELOG)
    @ApiOperation("修改小程序推广渠道")
    @PostMapping(value = "/crm/promotion_channel/update")
    public Result<Long> UpdatePromotionPageUrl(@RequestBody PromotionChannelDTO param) {
        Result result = new Result<>();
        Long num = promotionChannelService.savePromotionChannel(param);
        result.setData(num);
        return result;
    }

    @SysLog(value = "删除小程序推广渠道",type = LogType.OPERATELOG)
    @ApiOperation("删除小程序推广渠道")
    @PostMapping(value = "/crm/promotion_channel/delete")
    public Result<Long> UpdatePromotionDelete(@RequestBody PromotionChannelDTO param) {
        Result result = new Result<>();
        promotionChannelService.deletePromotionChannel(param);
        result.setData(true);
        return result;
    }
}
