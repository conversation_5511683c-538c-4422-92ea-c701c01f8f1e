package com.dz.ms.adaptor.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.core.constants.CommonConstants;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.CommonUtils;
import com.dz.common.core.utils.DateUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.adaptor.constants.CacheKeys;
import com.dz.ms.adaptor.dto.ScheduledTaskConfigLogDTO;
import com.dz.ms.adaptor.entity.ScheduledTaskConfigLog;
import com.dz.ms.adaptor.mapper.ScheduledTaskConfigLogMapper;
import com.dz.ms.adaptor.service.ScheduledTaskConfigLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * 定时任务日志表
 * @author: 
 * @date:   2025/03/17 11:26
 */
@Service
@Slf4j
public class ScheduledTaskConfigLogServiceImpl extends ServiceImpl<ScheduledTaskConfigLogMapper,ScheduledTaskConfigLog> implements ScheduledTaskConfigLogService {

	@Resource
    private ScheduledTaskConfigLogMapper scheduledTaskConfigLogMapper;
    @Resource
    private RedisService redisService;

    /**
     * 保存定时任务日志表
     * @param param
     * @return Long
     */
    @Override
    public Long saveScheduledTaskConfigLog(ScheduledTaskConfigLogDTO param) {
        ScheduledTaskConfigLog scheduledTaskConfigLog = BeanCopierUtils.convertObject(param, ScheduledTaskConfigLog.class);
        try {
            String month = this.subTableList(LocalDate.now().format(DateUtils.Y_M_DTF1));
            if(StringUtils.isBlank(month)){
                log.error("日期:{},保存定时任务日志error，subTableList执行异常，param:{}", month, CommonUtils.jsonStr(param));
            } else {
                if(ParamUtils.isNullOr0Long(scheduledTaskConfigLog.getId())) {
                    if(Objects.isNull(scheduledTaskConfigLog.getStatus())){
                        scheduledTaskConfigLog.setStatus(0);
                    }
                    scheduledTaskConfigLogMapper.insertLog(scheduledTaskConfigLog,month);
                } else {
                    scheduledTaskConfigLogMapper.updateStatus(scheduledTaskConfigLog,month);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("保存定时任务日志表失败,入参:{}", CommonUtils.jsonStr(param));
        }
        return scheduledTaskConfigLog.getId();
    }

    public String subTableList(String month) {
        String key = CacheKeys.Checks.SCHEDULED_TASK_CONFIG_LOG_MONTH;
        String checkStr = redisService.getString(key);
        if(StringUtils.isNotBlank(checkStr) && StringUtils.equals(checkStr, month)){
            return month;
        }
        for (int i = 0; i < 3; i++) {
            try {
                List<String> tableList = scheduledTaskConfigLogMapper.queryTableList();
                boolean hasCurrentTable = false;
                if (!CollectionUtils.isEmpty(tableList)) {
                    for (String table : tableList) {
                        String newTableName="scheduled_task_config_log_" + month;
                        if (newTableName.equals(table)) {
                            hasCurrentTable = true;
                            redisService.setString(key,month, CommonConstants.DAY_SECONDS);
                            break;
                        }
                    }
                }
                if (!hasCurrentTable) {
                    scheduledTaskConfigLogMapper.createTable(month);
                    log.info(month + "创建scheduled_task_config_log成功");
                }
                return month;
            } catch (Exception e) {
                log.error(month + "创建scheduled_task_config_log失败,i:" + i, e);
            }
        }
        return null;
    }

	
}
