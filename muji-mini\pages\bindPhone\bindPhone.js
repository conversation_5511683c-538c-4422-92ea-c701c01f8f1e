const app = getApp()
import {
  genderList,
} from './../../utils/contants';
import { bindPhone } from '../../api/index'
Page({
  data: {
    menuButtonTop: app.globalData.menuButtonTop,
    menuButtonBottom: app.globalData.menuButtonBottom,
    info: {
      gender: 0,
    },
    genderList,
    error: false, // 异常报错
    errContent: "您填写的手机号已关联了其他微信账户,\n请确认手机号后重新注册开卡。\n\n如有疑问请致电客服\n400-920-9299[86315]",

  },
  onShow(options) {
    // 编辑页面 如果用户未注册 自动跳转注册页面
    if (app.globalData.userInfo.isMember <= 0) {
      wx.$mp.redirectTo({
        url: '/pages/register/register',
      })
    }
    let info = {
      avatar: app.globalData.userInfo.avatar || '',
      username: app.globalData.userInfo.username || '',
      gender: app.globalData.userInfo.gender || '',
      mobile: '',
      birthday: app.globalData.userInfo.birthday ? app.globalData.userInfo.birthday.replace(/-/gi, '.') : '',
      province: app.globalData.userInfo.province || '',
      city: app.globalData.userInfo.city || '',
      area: app.globalData.userInfo.area || '',
    }
    this.setData({
      info,
    })
  },
  // 授权手机号
  authMobile(e) {
    wx.$authMobile(e).then(({
      success,
      data,
    }) => {
      if (success) {
        this.setData({
          ['info.mobile']: data,
        })
      } else {
        this.setData({
          ['info.mobile']: null,
        })
      }
    })
  },
  // 绑定
  bindPhone() {
    if (!this.data.info.mobile) {
      wx.showToast({
        title: '请授权手机号',
        icon: 'none'
      })
      return
    }
    this.setData({
      loading: true
    })
    bindPhone({
      newMobile: this.data.info.mobile,
      channelOne: app.globalData.channelOne, // 渠道1
      channelTwo: app.globalData.channelTwo, // 渠道2
    }).then(async () => {
      // 绑定手机号
      wx.showToast({
        title: '绑定成功',
      })
      await app.getUserInfo() // 更新用户信息手机号
      let time = setTimeout(async () => {
        clearTimeout(time)
        time = null
        wx.$mp.navigateBack()
        this.setData({
          loading: false
        })
      }, 2000);
    }).catch((res) => {
      this.setData({
        loading: false
      })
      // 加入会员信息异常
      if (res && res.code > 1000) {
        this.setData({
          error: true,
        })
      }
    })
  },
  // 弹窗关闭
  close() {
    this.setData({
      error: false,
    })
    wx.$mp.navigateBack()
  },
})
