// pages/pointsDetails/pointsDetails.js
import {
  pointsDetails
} from '../../api/index'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    loading: false,
    isBackHidden: false,
    details: {},
    bonusSn: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow(options) {

    if (options) {
      this.setData({
        bonusSn: options.bonusSn
      })
    }

    this.getDetail()
  },
  getDetail() {
    this.setData({
      loading: true
    })
    const {
      bonusSn,
      loading
    } = this.data
    pointsDetails({
      bonusSn: bonusSn
    }).then(res => {
      if (res.data.consumptionAmount === '0.00') {
        res.data.consumptionAmount = ''
      }
      if (res.data.cumulativeAmount === '0.00') {
        res.data.cumulativeAmount = ''
      }

      this.setData({
        details: res.data
      })

    }).finally(() => {
      this.setData({
        loading: false
      })
    })
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },
})
