package com.dz.common.core.config;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.PropertyKeyConst;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import com.dz.common.core.constants.ApplicationConstant;
import com.dz.common.core.utils.ResourceUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.driver.api.yaml.YamlShardingSphereDataSourceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.sql.SQLException;
import java.util.Properties;

/**
 * 数据库配置
 * @author: Handy
 * @date:   2022/6/22 17:14
 */
@Slf4j
@Configuration
public class DataSourceConfig {

    @Resource
    private ApplicationConstant applicationConstant;
    @Value("${spring.cloud.nacos.config.server-addr: }")
    private String nacosConfigAddr;
    @Value("${spring.cloud.nacos.config.username: }")
    private String username;
    @Value("${spring.cloud.nacos.config.password: }")
    private String password;
    @Value("${spring.cloud.nacos.config.namespace: }")
    private String namespace;
    @Value("${spring.cloud.nacos.config.extension-configs[1].group: }")
    private String nacosConfigGroup;

    /**
     * 配置读写分离&分片数据源
     * @return
     * @throws FileNotFoundException
     * @throws SQLException
     * @throws IOException
     */
    @Bean
    public DataSource dataSource() throws IOException, NacosException, SQLException {
        byte[] configBytes = null;

        if (applicationConstant.singleService) {
            configBytes = ResourceUtils.resource2byte("config/sharding-jdbc-" + applicationConstant.profile + ".yml");
        } else {
            Properties properties = new Properties();
            properties.put(PropertyKeyConst.SERVER_ADDR, nacosConfigAddr);
            if (StringUtils.isNotEmpty(username)) {
                properties.put(PropertyKeyConst.USERNAME, username);
                properties.put(PropertyKeyConst.PASSWORD, password);
            }
            if (StringUtils.isNotBlank(namespace)) {
                properties.put(PropertyKeyConst.NAMESPACE, namespace);
            }

            ConfigService configService = NacosFactory.createConfigService(properties);
            log.info("Fetching sharding-jdbc-" + applicationConstant.profile + ".yml, group: {}, addr: {}", nacosConfigGroup, nacosConfigAddr);
            String content = configService.getConfig("sharding-jdbc-" + applicationConstant.profile + ".yml", nacosConfigGroup, 5000);
            log.info("sharding-jdbc content: {}", content);
            configBytes = content.getBytes();
        }

        return YamlShardingSphereDataSourceFactory.createDataSource(configBytes);
    }


}
