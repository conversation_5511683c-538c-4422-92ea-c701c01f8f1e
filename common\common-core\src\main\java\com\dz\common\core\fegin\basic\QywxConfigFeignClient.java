package com.dz.common.core.fegin.basic;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.basic.QywxConfigDTO;
import com.dz.common.core.dto.wechat.DecryptEnterpriseUserDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = ServiceConstant.BASIC_SERVICE_NAME, contextId = "QywxConfigFeignClient")
public interface QywxConfigFeignClient {

    /**
     * 根据租户ID查询企业微信配置
     * @param tenantId
     * @return
     */
    @GetMapping(value = "/qywx/info_by_tenant")
    public Result<QywxConfigDTO> getQywxConfigByTenantId(@RequestParam("tenantId") Long tenantId);

    /**
     * 根据租户ID获取企业微信access_token
     * @param type
     * @param tenantId
     * @param cleanCach
     * @return
     */
    @GetMapping( value = "/qywx/access_token")
    public Result<String> getQywxAccessToken(@RequestParam("type") Integer type,@RequestParam("tenantId") Long tenantId,@RequestParam(value = "cleanCach",required = false) Boolean cleanCach);

    @PostMapping(value = "/qywx/wxdata_decode")
    Result<DecryptEnterpriseUserDTO> decryptEnterpriseUserDTO(@RequestParam("encryptedData") String encryptedData, @RequestParam("iv") String iv, @RequestParam("sessionKey") String sessionKey);
}
