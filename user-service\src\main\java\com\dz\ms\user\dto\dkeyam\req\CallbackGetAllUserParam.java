package com.dz.ms.user.dto.dkeyam.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 同步所有用户的信息入参
 * @author: fei
 * @date: 2025/01/22 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CallbackGetAllUserParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("如果通过分页的方式实现同步，startRow表示第几页的数据。从0开始， startRow = 0表示第一页")
    private Integer startRow;
    @ApiModelProperty("如果通过分页的方式实现同步，limit表示每页的数据量。例如，startRow = 0，limit = 200，表示请求第1页的200条数据。必须大于0")
    private Integer limit;

}
