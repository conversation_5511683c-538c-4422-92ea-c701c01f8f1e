package com.dz.ms.sales.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.sales.entity.UserTask;
import com.dz.ms.sales.entity.UserTaskRecord;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 任务记录信息Mapper
 */
@Repository
public interface UserTaskRecordMapper extends BaseMapper<UserTaskRecord> {

    List<UserTaskRecord> onlyOneFinishPersonNumOne(@Param("taskId") Long taskId, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("num") Integer num);
    List<UserTaskRecord> onlyOneFinishPersonNumTwo(@Param("taskId") Long taskId, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("num") Integer num);

    List<UserTaskRecord> getUserIdByTaskIdList(@Param("taskIds") List<Long> taskIds);

    List<String> querySubTableList();

    int createSubTable(@Param("nowDay")String nowDay);

    void inserByTable(@Param("userTask")UserTask userTask, @Param("nowDay")String nowDay);

    List<String> querySubTableListYear();

    int createSubTableYear(@Param("nowDay")String nowDay);

    Long isUserTaskExists(@Param("tableName")String tableName,@Param("taskId") Long taskId,@Param("uid") Long uid);

    void inserByTableYear(@Param("userTask")UserTask userTask,@Param("nowDay") String nowYear);

    List<UserTask> participateTaskCount(@Param("nowDay") String nowYear);
}
