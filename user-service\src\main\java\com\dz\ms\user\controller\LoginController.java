package com.dz.ms.user.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dz.common.base.constant.ClientTypeConstant;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.Lock;
import com.dz.common.core.dto.KeyValueDTO;
import com.dz.common.core.dto.user.SysUserDTO;
import com.dz.common.core.dto.user.UserSimpleDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.user.LoginFeginClient;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.user.constants.CacheKeys;
import com.dz.ms.user.dto.*;
import com.dz.ms.user.service.OmsUserService;
import com.dz.ms.user.service.SysRoleService;
import com.dz.ms.user.service.SysUserService;
import com.dz.ms.user.service.UserInfoService;
import com.dz.ms.user.utils.JwtTokenUtils;
import com.dz.ms.user.utils.RandImageUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Api(tags="用户登录")
@RestController
public class LoginController implements LoginFeginClient {

    @Resource
    private SysUserService sysUserService;
    @Resource
    private SysRoleService sysRoleService;
    @Resource
    private OmsUserService omsUserService;
    @Resource
    private UserInfoService userInfoService;
    @Resource
    private RedisService redisService;

    /**
     * OMS后台用户名密码登录
     * @return
     */
    @ApiOperation("OMS后台用户名密码登录")
    @PostMapping(value = "/oms/login/password")
    public Result<String> passwordLoginOms(@RequestBody AccountLoginDTO param) {
        Result<String> result = new Result<>();
        if (StringUtils.isBlank(param.getUsername())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "账号不能为空");
        }
        if (StringUtils.isBlank(param.getPassword())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "密码不能为空");
        }
        if (ParamUtils.isNullOr0Integer(param.getPlatform())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "平台类型不能为空");
        }
        /*if (StringUtils.isBlank(param.getImageCode())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "图形验证码不能为空");
        }
        if (StringUtils.isBlank(param.getCodeKey())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "验证码KEY不能为空");
        }
        String imageCode = redisService.getString(CacheKeys.IMAGE_CODE + param.getCodeKey());
        if (!param.getImageCode().equalsIgnoreCase(imageCode)) {
            throw new BusinessException("验证码错误");
        }
        redisService.del(CacheKeys.IMAGE_CODE + param.getCodeKey());*/
        String token = omsUserService.passwordLogin(param);
        result.setData(token);
        return result;
    }

    /**
     * OMS后台用户密码确认并发送短信验证码
     * @param param
     * @return
     */
    @Lock(prefix = "oms:lock:password_check",key = "'#param.username'")
    @ApiOperation("OMS后台用户密码确认并发送短信验证码")
    @PostMapping(value = "/oms/login/password_check")
    public Result<Boolean> passwordCheckOms(@RequestBody AccountLoginDTO param) {
        Result<Boolean> result = new Result<>();
        if (StringUtils.isBlank(param.getUsername())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "账号不能为空");
        }
        if (StringUtils.isBlank(param.getPassword())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "密码不能为空");
        }
        if (StringUtils.isBlank(param.getImageCode())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "图形验证码不能为空");
        }
        if (StringUtils.isBlank(param.getCodeKey())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "验证码KEY不能为空");
        }
        boolean isFirst = omsUserService.passwordCheck(param);
        result.setData(isFirst);
        return result;
    }

    /**
     * OMS后台密码加短信验证码登录
     * @param param
     * @return
     */
    @ApiOperation("OMS后台密码加短信验证码登录")
    @PostMapping(value = "/oms/login/password_sms")
    public Result<OmsUserDTO> passwordSmsLoginOms(@RequestBody AccountLoginDTO param) {
        Result<OmsUserDTO> result = new Result<>();
        if (StringUtils.isBlank(param.getUsername())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "账号不能为空");
        }
        if (StringUtils.isBlank(param.getPassword())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "密码不能为空");
        }
        if (StringUtils.isBlank(param.getSmsCode())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "短信验证码不能为空");
        }
        if (ParamUtils.isNullOr0Integer(param.getPlatform())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "平台类型不能为空");
        }
        OmsUserDTO omsUser = omsUserService.passwordSmsLogin(param);
        result.setData(omsUser);
        return result;
    }

    /**
     * CRM后台用户名密码登录
     * @param param
     * @return
     */
    @ApiOperation("CRM后台用户名密码登录")
    @PostMapping(value = "/crm/login/password")
    public Result<SysUserDTO> passwordLoginCrm(@RequestBody AccountLoginDTO param) {
        Result<SysUserDTO> result = new Result<>();
        if (StringUtils.isBlank(param.getUsername())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "账号不能为空");
        }
        if (StringUtils.isBlank(param.getPassword())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "密码不能为空");
        }
        if (StringUtils.isBlank(param.getImageCode())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "图形验证码不能为空");
        }
        if (StringUtils.isBlank(param.getCodeKey())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "验证码KEY不能为空");
        }
        if (ParamUtils.isNullOr0Integer(param.getPlatform())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "平台类型不能为空");
        }
        String imageCode = redisService.getString(CacheKeys.IMAGE_CODE + param.getCodeKey());
        if (!param.getImageCode().equalsIgnoreCase(imageCode)) {
            throw new BusinessException("验证码错误");
        }
        redisService.del(CacheKeys.IMAGE_CODE + param.getCodeKey());
        SysUserDTO sysUser = sysUserService.passwordLogin(param);
        result.setData(sysUser);
        return result;
    }
    
    /**
     * CRM后台宁盾认证
     * @param param 入参
     * @return SysUserDTO
     */
    @ApiOperation("CRM后台宁盾认证")
    @PostMapping(value = "/crm/login/strongAuthenticate")
    public Result<SysUserDTO> strongAuthenticate(@RequestBody AccountLoginDTO param) {
        log.info("==================================CRM后台宁盾认证入参:" + param.toString());
        Result<SysUserDTO> result = new Result<>();
        if (StringUtils.isBlank(param.getUsername())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "账号不能为空");
        }
        if (StringUtils.isBlank(param.getPassword())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "密码不能为空");
        }
        if (ParamUtils.isNullOr0Integer(param.getPlatform())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "平台类型不能为空");
        }
        if (StringUtils.isBlank(param.getDkeyAmPassword())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "宁盾动态口令不能为空");
        }
        SysUserDTO sysUser = sysUserService.strongAuthenticate(param);
        result.setData(sysUser);
        return result;
    }

    /**
     * CRM后台用户密码确认并发送短信验证码
     * @param param
     * @return
     */
    @Lock(prefix = "crm:lock:password_check",key = "'#param.tenantId'+'#param.username'")
    @ApiOperation("CRM后台用户密码确认并发送短信验证码")
    @PostMapping(value = "/crm/login/password_check")
    public Result<Boolean> passwordCheckCrm(@RequestBody AccountLoginDTO param) {
        Result<Boolean> result = new Result<>();
        if (StringUtils.isBlank(param.getUsername())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "账号不能为空");
        }
        if (StringUtils.isBlank(param.getPassword())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "密码不能为空");
        }
        if (StringUtils.isBlank(param.getImageCode())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "图形验证码不能为空");
        }
        if (StringUtils.isBlank(param.getCodeKey())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "验证码KEY不能为空");
        }
        boolean isFirst = sysUserService.passwordCheck(param);
        result.setData(isFirst);
        return result;
    }

    /**
     * CRM后台密码加短信验证码登录
     * @param param
     * @return
     */
    @ApiOperation("CRM后台密码加短信验证码登录")
    @PostMapping(value = "/crm/login/password_sms")
    public Result<SysUserDTO> passwordSmsLoginCrm(@RequestBody AccountLoginDTO param) {
        Result<SysUserDTO> result = new Result<>();
        if (StringUtils.isBlank(param.getUsername())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "账号不能为空");
        }
        if (StringUtils.isBlank(param.getPassword())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "密码不能为空");
        }
        if (StringUtils.isBlank(param.getSmsCode())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "短信验证码不能为空");
        }
        if (ParamUtils.isNullOr0Integer(param.getPlatform())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "平台类型不能为空");
        }
        SysUserDTO sysUser = sysUserService.passwordSmsLogin(param);
        result.setData(sysUser);
        return result;
    }

    @ApiOperation("OMS修改密码")
    @PostMapping(value = "/oms/login/password_update")
    public Result<Boolean> passwordUpdateOms(@RequestBody UpdatePasswordDTO param) {
        Result<Boolean> result = new Result<>();
        if (StringUtils.isBlank(param.getOldPassword())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "原密码不能为空");
        }
        if (StringUtils.isBlank(param.getNewPassword())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新密码不能为空");
        }
        omsUserService.passwordUpdate(param);
        result.setData(true);
        return result;
    }

    @ApiOperation("CRM修改密码")
    @PostMapping(value = "/crm/login/password_update")
    public Result<Boolean> passwordUpdateCrm(@RequestBody UpdatePasswordDTO param) {
        Result<Boolean> result = new Result<>();
        if (StringUtils.isBlank(param.getOldPassword())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "原密码不能为空");
        }
        if (StringUtils.isBlank(param.getNewPassword())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新密码不能为空");
        }
        sysUserService.passwordUpdate(param);
        result.setData(true);
        return result;
    }

    @ApiOperation("根据账号获取租户信息")
    @GetMapping(value = "/crm/login/tenants_by_username")
    public Result<List<KeyValueDTO>> getTenantsByUsername(@RequestParam("username") String username) {
        Result<List<KeyValueDTO>> result = new Result<>();
        List<KeyValueDTO> list = sysUserService.getTenantsByUsername(username);
        result.setData(list);
        return result;
    }

    @PostMapping(value = "/app/login/wechat")
    @ApiOperation("小程序端微信登录")
    public Result<UserSimpleDTO> wechatLoginAppReturnUser(@RequestBody WechatLoginDTO param) {
        Result<UserSimpleDTO> result = new Result<>();
        if (StringUtils.isBlank(param.getCode())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "code不能为空");
        }
        if (StringUtils.isBlank(param.getAppId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "appId不能为空");
        }
        result.setData(userInfoService.wechatLogin(param));
        return result;
    }

    @ApiOperation("APP获取发送登录短信验证码次数")
    @GetMapping(value = "/app/login/sms_code_count")
    public Result<Integer> appLoginSmsCodeCount(@RequestParam("mobile") String mobile) {
        Result<Integer> result = new Result<>();
        if (StringUtils.isBlank(mobile)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "手机号不能为空");
        }
        String codeStr = redisService.getString(CacheKeys.SMS_CODE + ClientTypeConstant.APP + ":count:" + mobile);
        result.setData(NumberUtils.toInt(codeStr));
        return result;
    }

    /*@PostMapping(value = "/app/login_emp/mobile")
    @ApiOperation("员工手机号验证码登录")
    public Result<EmployeeDTO> empMobileLogin(@RequestBody AccountLoginDTO param) {
        Result<EmployeeDTO> result = new Result<>();
        if (StringUtils.isBlank(param.getUsername())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "手机号不能为空");
        }
        if (StringUtils.isBlank(param.getCodeKey())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "验证码key不能为空");
        }
        if (StringUtils.isBlank(param.getSmsCode())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "短信验证码不能为空");
        }
        result.setData(employeeService.empMobileLogin(param));
        return result;
    }*/

    @GetMapping(value = {"/crm/login/imagecode","/oms/login/imagecode","/app/login/imagecode"})
    @ApiOperation("获取图形验证码")
    public Result<ImageCodeDTO> imageCode() {
        Result<ImageCodeDTO> result = new Result<>();
        String verifyCode = RandImageUtil.generateVerifyCode(5);
        String imgBase64 = RandImageUtil.generate(verifyCode);
        String uuid = UUID.randomUUID().toString();
        redisService.setString(CacheKeys.IMAGE_CODE + uuid, verifyCode, 120);
        ImageCodeDTO login = new ImageCodeDTO();
        login.setCodeKey(uuid);
        login.setImageBase64(imgBase64);
        result.setData(login);
        return result;
    }

    /**
     * 检查用户是否登录及是否有权限访问该页面
     * @param token
     * @param url
     * @return
     */
    @ApiOperation("检查用户是否登录及是否有权限")
    @PostMapping(value = "/check/login")
    public Result<JSONObject> checkLogin(@RequestParam("token") String token, @RequestParam("url") String url) {
        Result result = new Result<>();
        String[] array = token.split("[.]");
        if(array.length != 3) {
            return result.result401("无效的token");
        }
        JSONObject object = null;
        try {
            byte[] bytes = Base64.getDecoder().decode(array[1]);
            object = (JSONObject) JSON.parse(bytes);
        } catch (Exception e) {
            return result.result401("无效的token.");
        }
        int type = object.getIntValue("type");
        Long tenantId = object.getLong("tid");
        String uid = object.getString("id");
        String appkey = object.getString("key");
        String secret = null;
        boolean newUid = false;
        if(type == ClientTypeConstant.APP) {
            secret = redisService.getString(CacheKeys.USER_SECRET + tenantId +":"+ uid);
            if(null != secret && secret.length() > 36) {
                String[] strs = secret.split(",");
                secret = strs[0];
                uid = strs[1];
                newUid = true;
            }
        }
        else if(type == ClientTypeConstant.EMP) {
            secret = redisService.getString(CacheKeys.EMPUSER_SECRET + tenantId +":"+ uid);
        }
        else if(type == ClientTypeConstant.WEB) {
            secret = redisService.getString(CacheKeys.MPUSER_SECRET + tenantId +":"+ uid);
        }
        else if(type == ClientTypeConstant.CRM) {
            secret = redisService.getString(CacheKeys.SYSUSER_SECRET + tenantId +":"+ uid);
        }
        else if(type == ClientTypeConstant.OMS) {
            secret = redisService.getString(CacheKeys.OMSUSER_SECRET+uid);
        }
        else if(type == ClientTypeConstant.OPENAPI) {
            secret = redisService.getString(CacheKeys.OPENUSER_SECRET+appkey);
        }
        else {
            return result.result401("token错误");
        }
        if(StringUtils.isBlank(secret)) {
            return result.result401("token已失效");
        }
        try {
            log.info("type:{}uid:{}secret:{}token解析:{}",type,uid,secret,token);
            Map<String, Object> claims = JwtTokenUtils.getTokenInfo(token,secret);
            String roles = claims.containsKey("role") ? claims.get("role").toString() : null;
            Integer platform = claims.containsKey("pt") ? Integer.valueOf(claims.get("pt").toString()) : null;
            /**
             * 验证权限
             */
            if(type == ClientTypeConstant.CRM) {
                Long roleId = NumberUtils.toLong(roles,-1L);
                if(roleId.equals(-1L)) {
                    return result.result401("该账号还未绑定角色请联系管理员");
                }
                boolean checkPermission = sysRoleService.checkPermission(roleId,url,tenantId,platform);
                if(!checkPermission) {
                    return result.result401("没有访问接口权限");
                }
            }
            Map<String, Object> map = new HashMap<>(claims);
            if(newUid) {
                long userId = NumberUtils.toLong(uid);
                if(userId > 0) {
                    log.info("token权限校验为合并用户uid:{}", userId);
                    map.put("id", userId);
                }
            }
            result.setData(map);
            return result;
        } catch (Exception e) {
            log.error("权限校验出错",e);
            return result.result401("token已失效.");
        }
    }

    /*@GetMapping("/oms/login/pointreset")
    public Result<Object> pointReset() throws SQLException {
        Result<Object> result = new Result<>();
        PointsRestUtils.pointReset();
        return result;
    }*/

}
