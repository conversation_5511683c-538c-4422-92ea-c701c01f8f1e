package com.dz.ms.user.dto;

import com.dz.common.base.dto.BaseDTO;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.enums.ColumnType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 会员等级DTO
 * @author: Handy
 * @date:   2023/08/07 17:44
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "会员等级")
public class GradeInfoDTO extends BaseDTO {

    @ApiModelProperty(value = "等级ID")
    private Long id;
    @ApiModelProperty(value = "会员等级编码")
    private String gradeCode;
    @ApiModelProperty(value = "会员等级名称")
    private String gradeName;
    @ApiModelProperty(value = "会员等级样式")
    private String styleJson;
    @ApiModelProperty(value = "会员等级排序")
    private Integer sort;
    @ApiModelProperty(value = "等级消费金额")
    private Integer expenseAmount;
    @ApiModelProperty(value = "会员等级状态 0停用 1启用")
    private Integer state;
    @ApiModelProperty(value = "权益数量")
    private Long benefitNum;

    @ApiModelProperty(value = "关联已激活权益列表")
    private List<BenefitInfoDTO> activateBenefits;
    @ApiModelProperty(value = "关联未激活权益列表")
    private List<BenefitInfoDTO> unActivateBenefits;

}
