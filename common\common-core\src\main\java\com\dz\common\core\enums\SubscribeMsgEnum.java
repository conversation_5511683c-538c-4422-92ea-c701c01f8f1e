package com.dz.common.core.enums;

/**
 * 小程序订阅消息枚举
 *
 * @author: Handy
 * @date: 2022/02/03 23:36
 */
public enum SubscribeMsgEnum {

	ACTIVITY_START("9686", "会员活动开始提醒"),
	FIRST_GIFT_EXPIRE("14368", "礼品即将过期提醒-新客礼"),
	BIRTHDAY_GIFT_EXPIRE("14368-1", "礼品即将过期提醒-生日礼"),
	BENEFIT_RECEIVE("67910", "会员权益领取通知"),
	BIRTHDAY_GIFT_GET("42671", "生日礼物领取提醒"),
	COUPON_RECEIVE("11408", "优惠券到账通知"),
	COUPON_EXPIRE("44214", "优惠券过期提醒"),
	BOOKING_SUCCESS("5378", "预约成功通知"),
	BOOKING_START("5145", "服务开始提醒"),
	ACTIVITY_EXPIRE("37966", "预约提醒"),
	ORDER_RECEIVE("4037", "订单取货通知"),
	GIFT_SUCCESS("2942","商品兑换成功通知-邮递"),
	GIFT_SUCCESS_STORE("2942-1","商品兑换成功通知-柜台领取"),
	GIFT_DELIVERY("1856", "订单发货通知"),
	GIFT_EXPIRE("5929", "预约到期提醒"),
	LOTTERY_WINNING("19228", "中奖结果通知"),
	PRIZE_EXPIRE("11009", "礼包失效提醒"),
	INVITE_SUCCESS("36412", "邀请成功通知"),
	COUPON_USER_RECEIVE("3995", "优惠券领取通知"),
	TASK_BUY("24417", "购买商品任务完成通知"),
	POINTS_GET("52114", "会员积分增加提醒"),
	TASK_EXPIRE("9637", "活动结束提醒"),
	POINTS_EXPIRE("67840", "过期积分提醒"),

	ENROLL_SUCCESS("19229","报名成功通知"),
	SIGN_IN("12911","打卡提醒"),
//	NEW_CAMPAIGN("342","活动开始提醒"),
	ENROLL_FAIL("39110","活动报名进展通知"),
	BUYER_SIGN_IN("6618","活动状态通知"),

	;


	/**
	 * 模板编号
	 */
	private String msgCode;
	/**
	 * 模板名称
	 */
	private String msgName;

	SubscribeMsgEnum(String msgCode, String msgName) {
		this.msgCode = msgCode;
		this.msgName = msgName;
	}

	public String getMsgCode() {
		return msgCode;
	}

	public String getMsgName() {
		return msgName;
	}
}
