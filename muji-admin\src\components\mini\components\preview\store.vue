<template>
  <div class="store" :style="{width:width/2+'px',height:auto,background:data.bgColor,}">
    <div class="store-content" :style="{
      borderRadius:data.borderRadius/2+'px',
      paddingLeft:data.paddingLeft/2+'px',
      paddingRight:data.paddingRight/2+'px',
      paddingTop:data.paddingTop/2+'px',
      paddingBottom:data.paddingBottom/2+'px',
    }">
      <div class="WidgetHomeNearbyStores">
        <div class="_title">
          <span class="_title-name">{{ data.title||'附近门店' }}</span>
          <div class="_title-right-wrap">
            <span class="_title-right-name">全部门店</span>
            <span class="_title-right-icon iconfont icon-wangzhantuijian"></span>
          </div>
        </div>
        <img class="_main-img" :src="data.imgUrl" />
        <span class="_store-name">成都太古里店</span>
        <div class="_desc-item">
          <span class="_desc-item-label">地址：</span>
          <span class="_desc-item-value">四川省成都市锦江区成都太古里大慈寺路19号28栋</span>
        </div>
        <div class="_desc-item">
          <span class="_desc-item-label">时间：</span>
          <span class="_desc-item-value">周一至周四10:00-22:00</span>
        </div>
        <div class="_desc-item">
          <span class="_desc-item-label">电话：</span>
          <span class="_desc-item-value">028-86765437</span>
        </div>
        <div class="_map-wrap">
          <div class="_distance-wrap">
            <span class="_distance-icon iconfont icon-wangzhantuijian"></span>
            <div class="_distance-content">
              <span class="_distance-span">距离你</span>
              <span class="_distance-span">3.5km</span>
            </div>
          </div>
          <div class="_navigation-wrap">
            <span class="_navigation-span">导航</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
const emit = defineEmits(['ok', 'cancel', 'changeActive'])
import { cloneDeep } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { computed } from 'vue';
import customBg from './customBg.vue'
const props = defineProps({
  width: {
    type: Number,
    default: 750,
  },
  data: {
    type: Object,
    default() {
      return {}
    }
  },
})



</script>

<style scoped lang="scss">
.store {
  position: relative;
  .WidgetHomeNearbyStoresWrap {
    box-sizing: border-box;
  }

  .WidgetHomeNearbyStores {
    // width: px2rpx(670);
    width: 100%;
    margin: 0 auto;

    ._title {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    ._title-name {
      color: rgba(0, 0, 0, 1);
      font-size: px2rpx(36);
      font-family: SourceHanSansCN;
      font-weight: 700;
    }

    ._title-right-wrap {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    ._title-right-name {
      color: rgba(136, 136, 136, 1);
      font-size: px2rpx(28);
      font-family: PingFangSC;
      font-weight: 500;
    }

    ._title-right-icon {
      font-size: px2rpx(24);
      margin-left: px2rpx(10);
    }

    ._main-img {
      width: 100%;
      height: px2rpx(370);
      margin-top: px2rpx(38);
      display: block;
    }

    ._store-name {
      display: block;
      color: rgba(60, 60, 67, 1);
      font-size: px2rpx(32);
      font-family: SourceHanSansCN;
      font-weight: 700;
      line-height: px2rpx(44);
      margin: px2rpx(28) 0 0 px2rpx(30);
    }

    ._desc-item {
      color: rgba(136, 136, 136, 1);
      font-size: px2rpx(24);
      font-family: SourceHanSansCN;
      font-weight: 500;
      line-height: px2rpx(32);
      margin: px2rpx(10) 0 0 px2rpx(30);
    }

    ._map-wrap {
      width: px2rpx(620);
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: px2rpx(40) px2rpx(21) 0 px2rpx(30);
    }

    ._distance-wrap {
      display: flex;
      justify-content: space-between;
      margin: px2rpx(12) 0 px2rpx(12) 0;
    }

    ._distance-icon {
      font-size: px2rpx(36);
      margin-right: px2rpx(10);
    }

    ._distance-content {
      color: rgba(60, 60, 67, 1);
      font-size: px2rpx(24);
      line-height: px2rpx(33);
      font-family: PingFangSC;
      font-weight: 500;
      margin: px2rpx(2) 0 px2rpx(1) 0;
    }

    ._distance-text {
    }

    ._navigation-wrap {
      background-color: rgba(245, 245, 245, 1);
      border-radius: px2rpx(5);
      display: flex;
      align-items: center;
      justify-content: center;
      padding: px2rpx(18) px2rpx(44) px2rpx(18) px2rpx(44);
      font-size: px2rpx(24);
      color: rgba(60, 60, 67, 1);
      font-family: SourceHanSansCN;
      font-weight: 700;
    }

    ._navigation-text {
    }
  }
}
</style>
