<template>
  <layout>
    <template v-slot:header>
      <searchForm :formParams="formParams" @cancel="resetData" @ok="refreshData" :disabled="!$hasPermission('memberList:list:search')">
        <a-form-item name="gradeName" v-show="activeKey == '1'">
          <a-input placeholder="等级名称" allow-clear v-model:value="formParams.gradeName" allowClear @keyup.enter="refreshData"></a-input>
        </a-form-item>
      </searchForm>
    </template>
    <template v-slot:topRight>
      <a-space>
        <!-- :disabled="!$hasPermission('open:leaveUser:synchLeaveUser')" 权限标识 -->
        <a-button type="primary" @click="addChang" :disabled="!$hasPermission('memberList:list:add')">{{ '新建等级' }}</a-button>

      </a-space>

    </template>
    <template v-slot="{ height }">
      <a-table :indentSize="20" row-key="id" :scroll="{ scrollToFirstRowOnChange: true, y: height - 88, x: '100%' }" :dataSource="dataSource" :columns="tableHeader" :pagination="false" :loading="loading" @change="loadData">
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'index'">
            {{ (current - 1) * pageSize + 1 + index }}
          </template>
          <template v-if="column.key === 'styleJson'">
            <a-image v-if="record.styleJson" :src="record.styleJson" :width="50" :height="30"></a-image>
            <span v-else>--</span>
          </template>
          <template v-if="column.key === 'state'">
            <a-tag color="red" v-if="record.state == 0">停用中</a-tag>
            <a-tag color="success" v-else>启用中</a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <!-- <a-button type="link" @click="updateState(record, record.state ? 0 : 1)" :disabled="!hasPermission('memberList:list:state')">{{ record.state ? '下架' : '上架' }}</a-button>
            <a-divider type="vertical" /> -->
            <!-- :disabled="!hasPermission('memberList:list:edit')"  :disabled="!hasPermission('memberList:list:state')"-->
            <a-button type="link" @click="edit(record)">编辑</a-button>
            <a-divider type="vertical" v-if="record.state == 1" />
            <a-button type="link" v-if="record.state == 1" @click="updateState(record, record.state ? 0 : 1)">停用</a-button>

            <template v-if="record.state == 0">
              <a-divider type="vertical" />
              <a-button type="link" @click="updateState(record, record.state ? 0 : 1)">启用</a-button>
              <a-divider type="vertical" />

              <!-- :disabled="!hasPermission('memberList:list:del')" :disabled="!hasPermission('memberList:list:del')" -->
              <a-popconfirm title="是否确定删除该数据？" @confirm="handleDelete(record)">
                <a-button type="link">删除</a-button>
              </a-popconfirm>
            </template>
          </template>

        </template>
      </a-table>
    </template>
  </layout>
  <addMember :visible="visible" @ok="updateList" @cancel="visible = false" :id="id" :type="type"></addMember>
</template>



<script setup>
import addMember from './components/addMember.vue'
import { useRoute, useRouter } from 'vue-router'
import { usePagination, useRequest, } from 'vue-request';
import { grade_infoLista, grade_infoDelete, grade_infoupdate_state } from '@/http/index.js'
import _ from "lodash";
import { message, Modal } from "ant-design-vue";
let pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ["bottomLeft"],
  };
});
// 表格分页数据获取
const total = ref(0)


// 分页参数

const { activeKey, formParams, tableHeader, id, type, visible } = toRefs(reactive({

  activeKey: '1',
  id: '',
  type: 0,// 0-新增 1-编辑  2-查看
  visible: false,

  formParams: {
    name: '',

  },
  tableHeader: [
    {
      title: "序号",
      key: "index",
      align: "center",
      width: 80,
    },
    {
      title: '等级名称',
      dataIndex: 'gradeName',
      align: 'center',
      width: 180,
    },
    {
      title: '外部关联code',
      dataIndex: 'gradeCode',
      align: 'center',
      width: 180,
      customRender: (row) => {
        // console.log(row);

        return row.text || '--'; // 如果数据为空，则显示 '----'
      }
    },
    {
      title: '排序',
      dataIndex: 'sort',
      align: 'center',
      width: 100,

    },
    {
      title: '等级卡片',
      key: 'styleJson',
      align: 'center',
      width: 200,
    },
    {
      title: '关联权益',
      dataIndex: 'benefitNum',
      align: 'center',
      width: 200,
    },
    {
      title: '启用状态',
      key: 'state',
      align: 'center',
      width: 100,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      width: 150,
      fixed: 'right'
    }
  ],


})
);
const getParams = () => {
  // 拷贝一份 然后处理数据
  let params = _.cloneDeep(formParams.value);

  return params;
};
const { data: dataSource, run, loading, pageSize, current, refresh } = usePagination((param) => {
  // 这里修改参数
  return grade_infoLista({ ...param, ...getParams() })
},
  {
    manual: false,
    pagination: {
      currentKey: 'pageNum',  // 通过该值指定接口 当前页数 参数的属性值
      pageSizeKey: 'pageSize',// 通过该值指定接口 每页获取条数 参数的属性值
      totalKey: 'data.data.total',  // 指定 data 中 total 属性的路径
    },
    formatResult: res => { // 返回数据格式化
      return res.data
    }
  });

// 加载数据
const loadData = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}

function updateState(row, state) {
  console.log("🚀 ~ updateState ~ row, state:", row, state)
  grade_infoupdate_state({ id: row.id, state: state }).then(res => {
    if (res.code === 0) {
      message.success(`${row.state == '0' ? '启用' : '停用'}成功`)
      refreshData()
    }
  })
}
function handleDelete(record) {
  grade_infoDelete({ id: record.id }).then(res => {
    if (res.code === 0) {
      message.success('删除成功')
      resetData()
    }
  })
}

// 刷新数据
const refreshData = () => {
  run()
};

// 重置数据
const resetData = () => {
  formParams.value = {
    gradeName: '',
  }
  refreshData()
}
function edit(row) {
  visible.value = true
  id.value = row.id
  type.value = 0
}
function addChang() {
  visible.value = true
  id.value = ''
  type.value = 0
}
function updateList(value) {
  visible.value = false;
  // 新增 重置搜索数据
  if (!value) {
    resetData();
  } else {
    // 查看、编辑 不需要清空搜索数据 直接刷新
    refresh();
  }
}
</script>
