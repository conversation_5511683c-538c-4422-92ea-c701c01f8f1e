package com.dz.ms.user.dto.dkeyam.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 解绑用户终端入参
 * @author: fei
 * @date: 2025/01/22 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserUnbindUserTerminalsParam implements Serializable {

    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty("管理员登录名")
    private String adminLoginName;

    @ApiModelProperty("管理员密码")
    private String adminPassword;

    @ApiModelProperty("站点名称")
    private String tenantName;

    @ApiModelProperty("用户源名称 本地用户源：06bc758e-04f2-40b3-9d6c-562b115aeb3c 外部用户源：和创建的外部用户源名称一致")
    private String identityStoreName;

    @ApiModelProperty("用户登录名")
    private String loginName;
    
    @ApiModelProperty("是否删除指纹库的MAC地址默认值false：不删除")
    private String deleteFingerprint;

}
