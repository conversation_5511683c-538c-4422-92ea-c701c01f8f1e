<template>
  <div class="slider" :style="{width:width/2+'px'}">
    <div class="slider-bg">
      <!-- 背景 -->
      <custom-bg :bgSetting="data" class="bgStyle"></custom-bg>
    </div>
    <div class="slider-content" :style="{paddingLeft:data.paddingLeft/2+'px',paddingRight:data.paddingRight/2+'px',paddingBottom:data.paddingBottom/2+'px',paddingTop:data.paddingTop/2+'px',}">
      <!-- 标题 -->
      <div class="slider-subject" v-if="data.subject.open">
        <text-setting :data="data.subject" :content="data.subject.content" class="textStyle">
        </text-setting>
      </div>
      <div class="slider-scroll">
        <!-- 轮播 -->
        <div class="slider-roll">
          <div class="slider-inner" :style="{height:data.height/2+'px'}">
            <!-- <swiper :modules="[Autoplay]" :slides-per-view="1" :space-between="0" :speed="data.duration" :autoplay="true" class="swiperBox" @slideChange="onSlideChange">
        <swiper-slide v-for="(slider, index) in data.sliders[current].slider" :key="index" class="swiper-slide">
          <img :style="{ height: data.sliderHeight *rpx + 'px', width: '400px' }" :src="slider.picture" />
        </swiper-slide>
      </swiper> -->

            <swiper :style="{height:data.height/2+'px'}" :slides-per-view="1" :space-between="0" :speed="data.duration" :autoplay="true" @slideChange="changeImg">
              <swiper-slide v-for="(item,index) in data.list" :key="index" :style="{height:data.height/2+'px'}">
                <img :src="item.imgUrl" class="slider-image" :style="{borderRadius:data.borderRadius/2+'px',height:data.height/2+'px'}" />
              </swiper-slide>
            </swiper>
            <!-- 里面面指示点 -->
            <div :class="['slider-point', ['','left','center','right'][data.pointHorizonal]] " :style="{top:data.pointVertical==1?(data.pointOutSpace/2+'px'):'auto',bottom:data.pointVertical==2?(data.pointOutSpace/2+'px'):'auto','--left':data.pointSpace/4+'px','--right':data.pointSpace/4+'px'}" v-if="data.pointOpen&&data.pointPostion==1">
              <div :class="['slider-point-item', current==index?'active':'']" :style="{background:data.pointUnSelectColor,margin:(0+' '+data.pointSpace/4+'px'),width:data.pointSize/2+'px',height:data.pointSize/2+'px','--selected':data.pointSelectColor}" v-for="(item,index) in data.list" :key="item.id"></div>
            </div>
            <!-- 外面指示点 -->
            <div :class="['slider-point', ['','left','center','right'][data.pointHorizonal]] " :style="{top:data.pointVertical==1?(-data.pointOutSpace/2-data.pointSize/2+'px'):'auto',bottom:data.pointVertical==2?(-data.pointOutSpace/2-data.pointSize/2+'px'):'auto','--left':data.pointSpace/4+'px','--right':data.pointSpace/4+'px'}" v-if="data.pointOpen&&data.pointPostion==2">
              <div :class="['slider-point-item', current==index?'active':'']" :style="{background:data.pointUnSelectColor,margin:(0+' '+data.pointSpace/4+'px'),width:data.pointSize/2+'px',height:data.pointSize/2+'px','--selected':data.pointSelectColor}" v-for="(item,index) in data.list" :key="item.id"></div>
            </div>
          </div>
        </div>
        <!-- 主标题 -->
        <div class="slider-main" v-if="data.mainTitle.open">
          <text-setting :data="data.mainTitle" :content="data.list[current].mainTitle" class="textStyle">
          </text-setting>
        </div>
        <!-- 副标题 -->
        <div class="slider-small" v-if="data.smallTitle.open">
          <text-setting :data="data.smallTitle" :content="data.list[current].smallTitle" class="textStyle">
          </text-setting>
        </div>
      </div>
      <!-- 滑动指示 -->
      <div class="slider-show" v-if="data.slideShow">
        <div class="slider-show-arrow"></div>
        <div class="slider-show-text">滑动查看</div>
        <div class="slider-show-arrow right"></div>
      </div>

    </div>
    <!-- 悬浮窗 -->
    <float :data="data.floatSetting" class="floatStyle"></float>
  </div>
</template>
<script setup>
const emit = defineEmits(['ok', 'cancel', 'changeActive'])
import customBg from './customBg.vue'
import float from './float.vue'
import textSetting from './textSetting.vue'
import { Swiper, SwiperSlide } from "swiper/vue";
import "swiper/css";
const props = defineProps({
  width: {
    type: Number,
    default: 750,
  },
  data: {
    type: Object,
    default() {
      return {}
    }
  },
})
const current = ref(0)
const changeImg = (e) => {
  console.log(e)
  current.value = e.activeIndex
}

</script>

<style scoped lang="scss">
.slider {
  position: relative;
  height: auto;

  &-bg {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
  }

  &-content {
    position: relative;
    z-index: 100;
    width: 100%;
    height: auto;
    box-sizing: border-box;
    position: relative;
  }

  &-roll {
  }

  &-inner {
    position: relative;
  }

  &-point {
    position: absolute;
    display: flex;

    &.left {
      left: var(--left);
    }

    &.right {
      right: var(--right);
    }

    &.center {
      left: 50%;
      transform: translateX(-50%);
    }

    &-item {
      border-radius: 50%;

      &.active {
        background: var(--selected) !important;
      }
    }
  }

  &-image {
    width: 100%;
  }

  &-show {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 9px;
    color: #3c3c43;
    line-height: 12px;
    display: inline-flex;
    align-items: center;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    margin-top: 10px;

    &-text {
      margin: 0 5px;
    }

    &-arrow {
      width: 0;
      height: 0;
      border-top: 3px solid transparent;
      border-right: 3px solid black;
      border-left: 3px solid transparent;
      border-bottom: 3px solid transparent;

      &.right {
        border-top: 3px solid transparent;
        border-right: 3px solid transparent;
        border-left: 3px solid black;
        border-bottom: 3px solid transparent;
      }
    }
  }
}

.bgStyle {
  width: 100%;
  height: 100%;
}

.floatStyle {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 100;
  pointer-events: none;
}
</style>
