/* signUp/pages/prizeDraw/prizeDraw.wxss */
.page-container {
  background: linear-gradient(rgba(255, 255, 255, 0) 1%, #F8F6ED 100%);
  background-size: 100% 100%;

  .prizeDraw {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;

    .activeRules {
      position: absolute;
      right: 0rpx;
      top: 90rpx;
      width: 44rpx;
      height: 146rpx;
      background: #C7B397;
      font-family: MUJIFont2020,
        SourceHanSansCN;
      font-weight: 400;
      font-size: 22rpx;
      color: #FFFFFF;
      line-height: 24rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      writing-mode: vertical-rl;
      letter-spacing: 4rpx;
    }

    .prizeDraw-title1 {
      height: 40rpx;
      font-family: MUJIFont2020,
        SourceHanSansCN;
      font-weight: 500;
      font-size: 24rpx;
      line-height: 40rpx;
      letter-spacing: 1px;
      margin-top: 31rpx;
    }

    .title {
      margin-left: 62rpx;
    }

    .prizeDraw-title2 {
      height: 40rpx;
      font-weight: 300;
      font-size: 24rpx;
      line-height: 40rpx;
      letter-spacing: 1px;

    }

    .tips {
      display: flex;
      align-items: center;
      margin-left: 70rpx;
      margin-top: 139rpx;


      .tips-img {
        width: 74rpx;
        height: 76rpx;
        margin-right: 21rpx;

        image {
          width: 100%;
          height: 100%;
        }
      }

      .prizeDraw-title3 {
        font-weight: 300;
        font-size: 32rpx;
        color: #2E2E2E;
        line-height: 39rpx;
        letter-spacing: 1px;
        text-align: left;

      }

      .prizeDraw-title4 {
        font-weight: 700;
        font-size: 42rpx;
        color: #2E2E2E;
        line-height: 52rpx;
        letter-spacing: 1px;
        text-align: left;
      }
    }

    .gift-wrap {
      display: flex;
      // flex-direction: column-reverse;
      flex-wrap: wrap;
      // justify-content: end;
      // align-items: flex-end;
      height: 733rpx;
      margin-top: 63rpx;
      padding-left: 29rpx;
      padding-right: 19rpx;

      .gift-item {
        width: 214rpx;
        height: 214rpx;
        background: #F8F6EF;
        margin: 10rpx;
        position: relative;

        .active::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: #C8B49A;
          /*蒙版颜色和透明度*/
        }

        .img {
          width: 100%;
          height: 100%;
        }


      }
    }

  }

  .bottom-box {
    margin-top: 150rpx;
    margin-bottom: 154rpx;
    display: flex;
    justify-content: center;
  }
}
