import {
  postOrderList
} from '../../api/index.js'

const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    loading: false,

    detail: {
      list: [],
      count: 0,
      pageNum: 1,
      pageSize: 10,
    },

    // 订单状态 0已完成 1待支付 2待发货 3已发货 4待兑换 5部分兑换 6已兑换 7已取消
    tabList: [{
      label: '待核销',
      id: 4,
      noDataTxt: '暂无线下核销订单',
      trackData: 'exchange_record_uncomplate',
    }, {
      label: '已核销',
      id: 6,
      noDataTxt: '暂无历史核销订单',
      trackData: 'exchange_record_complate',
    },
    {
      label: '已过期',
      id: 8,
      noDataTxt: '暂无历史兑换订单',
      trackData: 'exchange_record_expire',
    }
    ],
    currentTab: 4,
    currentIndex: 0,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  getData() {
    this.setData({
      loading: true
    })
    const data = {
      orderStatus: this.data.currentTab,
      pageSize: this.data.detail.pageSize,
      pageNum: this.data.detail.pageNum,
      // orderCode:
    }
    postOrderList(data).then(res => {
      const detail = res.data
      if (this.data.detail.pageNum > 1) {
        detail.list = this.data.detail.list.concat(detail.list)
      }
      this.setData({
        detail
      })
    }).finally(() => {
      this.setData({
        loading: false
      })
    })
  },

  onReachBottom() {
    if (this.data.detail.list.length < this.data.detail.count) {
      this.setData({
        detail: {
          ...this.data.detail,
          pageNum: this.data.detail.pageNum + 1
        }
      })
      this.getData()
    } else {
      wx.showToast({
        title: '没有更多了',
        icon: 'none'
      })
    }
  },

  onTapItem(e) {
    wx.$mp.track({
      event: 'exchange_record_go_order',
      props: {
        orderCode: e.target.dataset.item.orderCode,
      }
    })
    wx.$mp.navigateTo({
      url: `/pages/exchangeDetail/exchangeDetail?orderCode=${e.target.dataset.item.orderCode}`
    })
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  handleGoto() {
    wx.$mp.track({
      event: 'exchange_record_go_life'
    })
    wx.$mp.switchTab({
      url: '/pages/life/life',
    })
  },
  onChangeTab(e) {
    const {
      name,
      index,
    } = e.detail
    const {
      tabList
    } = this.data;
    wx.$mp.track({
      event: tabList[index].trackData
    });
    this.setData({
      currentTab: name,
      currentIndex: index,
      detail: {
        ...this.data.detail,
        pageNum: 1
      }
    })
    this.getData()
  }
})
