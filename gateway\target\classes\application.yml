server:
  port: 10003
management:
  endpoint:
    web:
      exposure:
        include: "*"
      base-path: /
spring:
  application:
    name: gateway
  profiles:
    active: dev
  cloud:
    gateway:
      discovery:      #是否与服务发现组件进行结合，通过 serviceId(必须设置成大写) 转发到具体的服务实例。默认为false，设为true便开启通过服务中心的自动根据 serviceId 创建路由的功能。
        locator:      #路由访问方式：http://Gateway_HOST:Gateway_PORT/大写的serviceId/**，其中微服务应用名默认大写访问。
          enabled: true
      routes:
        - id: basic-service           #网关路由到基础服务basic-service
          uri: lb://basic-service
          predicates:
            - Path=/basic/**
          filters:
            - StripPrefix=1
        - id: user-service           #网关路由到用户服务user-service
          uri: lb://user-service
          predicates:
            - Path=/user/**
          filters:
            - StripPrefix=1
        - id: product-service           #网关路由到商品服务product-service
          uri: lb://product-service
          predicates:
            - Path=/product/**
          filters:
            - StripPrefix=1
        - id: order-service           #网关路由到订单服务order-service
          uri: lb://order-service
          predicates:
            - Path=/order/**
          filters:
            - StripPrefix=1
        - id: sales-service           #网关路由到营销服务sales-service
          uri: lb://sales-service
          predicates:
            - Path=/sales/**
          filters:
            - StripPrefix=1
        - id: adaptor-service           #网关路由到adaptor-service
          uri: lb://adaptor-service
          predicates:
            - Path=/adaptor/**
          filters:
            - StripPrefix=1
---
spring:
  config.activate.on-profile: dev
  cloud:
    nacos:
      discovery:
        server-addr: **************:8848 #member-nacos.project-67.svc.scm-prod.local:8848
        username: nacos
        password: nacos
        namespace: public           # 明确指定命名空间
        # 增加以下配置
        watch-delay: 30000          # 30秒等待时间
        failure-tolerance-enabled: true  # 启用容错
        register-enabled: true      # 确保注册启用
env.isdev: true
#logging:
#  files: /opt/muji/api/logs
---
spring:
  config.activate.on-profile: uat
  cloud:
    nacos:
      discovery:
        server-addr: nacos-uat.project-70.svc.cluster.local:8848
        group: UAT_GROUP
        username: nacos
        password: nacos
---
spring:
  config.activate.on-profile: pro
  cloud:
    nacos:
      discovery:
        server-addr: nacos-prod.project-76.svc.cluster.local:8848
        username: nacos
        password: Muji@123
