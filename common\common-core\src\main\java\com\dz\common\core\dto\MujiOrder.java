package com.dz.common.core.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.util.Date;

// crm积分商城-兑换明细
@Data
@ColumnWidth(19)
public class MujiOrder {

    // PK
    @ExcelProperty(value = "id")
    private Long id;

    // 兑换编号
    @ExcelProperty(value = "exchange_sn")
    private String exchangeSn;

    // 会员code
    @ExcelProperty(value = "member_code")
    private String memberCode;

    // 商品编号
    @ExcelProperty(value = "goods_id")
    private String goodsId;

    // 商品名称
    @ExcelProperty(value = "goods_name")
    private String goodsName;

    // 券批次id
    @ExcelProperty(value = "stock_id")
    private String stockId;

    // 积分流水
    @ExcelProperty(value = "bonus_sn")
    private String bonusSn;

    // 兑换使用的积分
    @ExcelProperty(value = "bonus")
    private Long bonus;

    // 兑换时会员的等级
    @ExcelProperty(value = "level")
    private Long level;

    // 券码
    @ExcelProperty(value = "coupon_code")
    private String couponCode;

    // 吊牌价 标价 销售价 分
    @ExcelProperty(value = "total_fee")
    private Long totalFee;

    // 用券后还需支付的金额 即 券后价 分
    @ExcelProperty(value = "cash_fee")
    private Long cashFee;

    // 价值 成本价 分
    @ExcelProperty(value = "cost_fee")
    private Long costFee;

    // 券面额 分
    @ExcelProperty(value = "coupon_fee")
    private Long couponFee;

    // 限制类型 1不限制 2限频次 3限时 4限频次+限时
    @ExcelProperty(value = "limit_type")
    private Long limitType;

    // 兑换等级限制
    @ExcelProperty(value = "limit_level")
    private Long limitLevel;

    // 创建时间
    @ExcelProperty(value = "created_at")
    private Date createdAt;

    // 更新时间
    @ExcelProperty(value = "updated_at")
    private Date updatedAt;

    // 分区
    @ExcelProperty(value = "dt")
    private String dt;
}
