// 所有的命名必须全局唯一
import service from '@/utils/request.js'
import router from '../router/index'
import { message, Modal, } from "ant-design-vue";
import { createVNode } from 'vue';
import { useGlobalStore } from '@/store'

// 角色列表
export function roleList(data = {}) {
  return service({
    url: '/crm/user/sys_role/list',
    method: 'get',
    data
  })
}


// 所有页面权限列表
export function permissionTree(data = {}) {
  return service({
    url: '/crm/user/sys_permission/tree',
    method: 'get',
    data
  })
}

// 角色列表 不分页
export function roleListNopage(data = {}) {
  return service({
    url: '/crm/user/sys_role/list_nopage',
    method: 'get',
    data
  })
}



// 角色保存
export function roleSave(data = {}) {
  return service({
    url: '/crm/user/sys_role/add',
    method: 'post',
    data
  })
}

// 角色更新
export function roleUpdate(data = {}) {
  return service({
    url: '/crm/user/sys_role/update',
    method: 'post',
    data
  })
}

// 角色删除
export function roleDelete(data = {}) {
  return service({
    url: '/crm/user/sys_role/delete',
    method: 'post',
    data
  })
}

// 查看角色详情
export function roleInfo(data = {}) {
  return service({
    url: '/crm/user/sys_role/info',
    method: 'get',
    data
  })
}


// 用户账号列表
export function userList(data = {}) {
  return service({
    url: '/crm/user/sys_user/list',
    method: 'get',
    data
  })
}


// 保存账号
export function addUser(data = {}) {
  return service({
    url: '/crm/user/sys_user/add',
    method: 'post',
    data
  })
}


// 更新账号
export function updateUser(data = {}) {
  return service({
    url: '/crm/user/sys_user/update',
    method: 'post',
    data
  })
}




// 账号密码重置
export function passwordReset(data = {}) {
  return service({
    url: '/crm/user/sys_user/password_reset',
    method: 'get',
    data
  })
}

//  删除账号
export function userDelete(data = {}) {
  // 接口请求
  return service({
    url: '/crm/user/sys_user/delete',
    method: 'post',
    data
  })
}


//  导出任务

export function downloadTask(data = {}) {
  // 接口请求
  return service({
    url: '/crm/basic/report/download/task',
    method: 'post',
    data
  }).then(res => {
    const global = useGlobalStore()
    const modalV = Modal.confirm({
      title: '消息',
      width: '500px', // Changed to 'auto' to dynamically adjust width based on content
      // icon: createVNode(),
      content: createVNode('div', null, [
        createVNode('div', null, '报表导出成功 '),
        createVNode('div', {
          style: 'display: flex; align-items: center; margin-top: 10px; flex-wrap: wrap;' // Added flex-wrap: wrap to allow text to wrap to the next line
        }, [
          createVNode('div', null, '请去 '),
          createVNode('div', {
            class: 'global-color',
            onClick() {
              // console.log(modalV);
              modalV.destroy();
              if (global.$hasPermission("downloadCenter:search") || global.$hasPermission("downloadCenter:list")) {
                router.push({ path: '/system/downloadManagement/downloadCenter' }) // Changed to use path instead of name
              } else {
                message.warning('暂无权限')
              }


              // confirm.close(); // 关闭确认框
              // ElMessageBox.close();
              // router.push({ name: 'download' });
            }
          }, '下载中心'),
          createVNode('div', null, '等待报表生成完成并下载报表，请勿重复导出')
        ])
      ]),
      okText: '确认',
      cancelText: '取消',
      onOk() {
        console.log('OK');
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  })
}