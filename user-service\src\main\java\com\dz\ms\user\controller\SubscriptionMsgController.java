package com.dz.ms.user.controller;

import com.dz.common.base.vo.Result;
import com.dz.common.core.vo.SubscriptionMsgVo;
import com.dz.ms.user.service.SubscriptionMsgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.ParseException;

@Api(tags = "订阅消息提醒")
@RestController
public class SubscriptionMsgController {

    @Resource
    private SubscriptionMsgService subscriptionMsgService;


    @ApiOperation("发送订阅消息")
    @PostMapping(value = "/app/subscription/add")
    public Result<Object> add(@RequestBody SubscriptionMsgVo subscriptionMsgVo) throws ParseException {
        Result<Object> result = new Result<>();
        subscriptionMsgService.add(subscriptionMsgVo);
        return result;
    }
}
