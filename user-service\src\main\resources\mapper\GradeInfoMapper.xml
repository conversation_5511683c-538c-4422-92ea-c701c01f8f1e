<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.user.mapper.GradeInfoMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    id,
  	    state,
  	    grade_code,
  	    grade_name,
  	    style_json,
  	    sort,
  	    expense_amount
    </sql>

	<!-- 根据等级code或名称查询会员等级列表 -->
	<select id="selectByCodeOrName" resultType="com.dz.ms.user.dto.GradeInfoDTO">
		select
		<include refid="Base_Column_List" />
		from grade_info 
		where is_deleted = 0
		AND grade_name = #{gradeName}
	</select>

</mapper>
