package com.dz.ms.adaptor;

import com.dz.common.core.config.CommonConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableScheduling
@EnableDiscoveryClient
@SpringBootApplication
@Import(CommonConfig.class)
public class AdaptorServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(AdaptorServiceApplication.class, args);
    }

}
