package com.dz.common.core.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class DownloadHeaderDTO {

    @ApiModelProperty(value = "ID")
    private Integer id;
    @ApiModelProperty(value = "报表编码")
    private String reportCode;
    @ApiModelProperty(value = "表头编码")
    private String code;
    @ApiModelProperty(value = "表头名")
    private String name;
    @ApiModelProperty(value = "表头替换 eg: 未使用_0,已使用_1")
    private String replace;
    @ApiModelProperty(value = "日期格式化方式")
    private String format;
    @ApiModelProperty(value = "排序")
    private Integer sort;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
}
