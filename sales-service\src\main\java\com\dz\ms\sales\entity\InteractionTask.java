package com.dz.ms.sales.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 任务信息
 */
@Getter
@Setter
@NoArgsConstructor
@Table("互动任务表")
@TableName(value = "t_interaction_task")
public class InteractionTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    @TableId(type = IdType.AUTO)
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "ID")
    private Long id;
    /**
     *任务名称
     */
    @Columns(type = ColumnType.VARCHAR, length = 255, isNull = false, comment = "任务名称")
    private String taskName;
    /**
     *启停状态 0启用，1停用
     */
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false,defaultValue = "0",comment = "启停状态 0启用，1停用")
    private Integer status;
    /**
     *任务展示时间类型：1时间段，2永久展示
     */
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, comment = "任务展示时间类型：1时间段，2永久展示")
    private Integer showTimeType;
    /**
     *任务展示开始时间
     */
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "任务展示开始时间")
    private Date showTimeStart;
    /**
     *任务展示结束时间
     */
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "任务展示结束时间")
    private Date showTimeEnd;
    /**
     *任务展示图片
     */
    @Columns(type = ColumnType.VARCHAR, length = 2000, isNull = true, comment = "任务展示图片")
    private String showImg;
    /**
     *任务类型：1限时，2购物，3互动
     */
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = true, comment = "任务类型：1限时，2购物，3互动")
    private Integer taskType;
    /**
     *是否限时：1限时，2不限时
     */
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = true, comment = "是否限时：1限时，2不限时")
    private Integer isTimeRestrict;
    /**
     *限时开始时间
     */
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "限时开始时间")
    private Date restrictTimeStart;
    /**
     *限时结束时间
     */
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "限时结束时间")
    private Date restrictTimeEnd;
    /**
     *任务内容：1线下打卡，2兑礼任务，3线下消费，4邀请好友，5首次购买
     */
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = true, comment = "任务内容：1线下打卡，2兑礼任务，3线下消费，4邀请好友，5首次购买")
    private Integer taskDesc;
    /**
     *任务完成周期：1一次性，2周期，3周期+阶梯
     */
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = true, comment = "任务完成周期：1一次性，2周期，3周期+阶梯")
    private Integer readyCycle;
    /**
     *任务需要完成次数
     */
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "任务需要完成次数")
    private Integer readyNum;
    /**
     *任务总计完成次数
     */
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "任务总计完成次数")
    private Integer totalReadyNum;
    /**
     *readyDay、readyMonth
     */
    @Columns(type = ColumnType.VARCHAR, length = 50, isNull = true, comment = "readyDay、readyMonth")
    private String reayType;
    /**
     *任务完成周期天数
     */
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "任务完成周期天数")
    private Integer readyDay;
    /**
     *任务完成周期月数
     */
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "任务完成周期月数")
    private Integer readyMonth;
    /**
     *创建人
     */
    @Columns(type = ColumnType.VARCHAR, length = 255, isNull = true, comment = "创建人")
    private String createAt;
    /**
     *创建时间
     */
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "创建时间")
    private Date createTime;
    /**
     *修改人
     */
    @Columns(type = ColumnType.VARCHAR, length = 255, isNull = true, comment = "修改人")
    private String updateAt;
    /**
     *修改时间
     */
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "修改时间")
    private Date updateTime;
    /**
     *渠道
     */
    @Columns(type = ColumnType.VARCHAR, length = 255, isNull = true, comment = "渠道")
    private Long tenantId;
    /**
     *线下消费商品品类码部门code，多个用英文逗号隔开
     */
    @Columns(type = ColumnType.VARCHAR, length = 2000, isNull = true, comment = "线下消费商品品类码部门code，多个用英文逗号隔开")
    private String storeProductCodes;
    /**
     *线下消费商品品类码depart code，多个用英文逗号隔开
     */
    @Columns(type = ColumnType.VARCHAR, length = 2000, isNull = true, comment = "线下消费商品品类码depart code，多个用英文逗号隔开")
    private String departProductCodes;
    /**
     *线下消费商品品类码line code，多个用英文逗号隔开
     */
    @Columns(type = ColumnType.VARCHAR, length = 2000, isNull = true, comment = "线下消费商品品类码line code，多个用英文逗号隔开")
    private String lineProductCodes;
    /**
     *线下消费商品品类码class code，多个用英文逗号隔开
     */
    @Columns(type = ColumnType.VARCHAR, length = 2000, isNull = true, comment = "线下消费商品品类码class code，多个用英文逗号隔开")
    private String classProductCodes;
    /**
     * 线下消费商品品类码jan code，多个用英文逗号隔开
     */
    @Columns(type = ColumnType.VARCHAR, length = 2000, isNull = true, comment = "线下消费商品品类码jan code，多个用英文逗号隔开")
    private String janProductCodes;
    /**
     * 任务规则图片
     */
    @Columns(type = ColumnType.VARCHAR, length = 2000, isNull = true, comment = "任务规则图片")
    private String ruleImg;
}
