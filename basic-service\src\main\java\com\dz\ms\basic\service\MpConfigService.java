package com.dz.ms.basic.service;

import com.dz.common.core.dto.wechat.CodeSessionDTO;
import com.dz.common.core.dto.wechat.DecryptUserDTO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.ms.basic.dto.MpConfigSimpleDTO;
import com.dz.ms.basic.entity.MpConfig;

/**
 * 公众号/小程序配置接口
 * @author: Handy
 * @date:   2022/01/28 15:35
 */
public interface MpConfigService extends IService<MpConfig> {

    /**
     * 根据租户ID获取小程序token
     * @param tenantId
     * @param cleanCach
     * @return
     */
    public String getMiniappAccessToken(Long tenantId,boolean cleanCach);

    /**
     * 根据租户ID和类型获取公众号小程序配置信息
     * @param tenantId
     * @param type
     * @return
     */
    public MpConfigSimpleDTO getMpConfigByTenantIdAndType(Long tenantId, Integer type);

    /**
     * 小程序登录code换取session
     * @param appId
     * @param code
     * @return
     */
    public CodeSessionDTO getMinappSessionByCode(String appId,String code);

    /**
     * 微信用户数据解密
     * @param encryptedData
     * @param iv
     * @param openId
     * @return
     */
    public DecryptUserDTO wxDataDecrypt(String encryptedData, String iv, String openId);

}
