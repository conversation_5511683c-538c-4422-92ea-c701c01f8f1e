package com.dz.ms.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @author: yibo
 * @date:   2024/11/19 17:20
 */
@Getter
@Setter
@NoArgsConstructor
@Table("门店服务-serve")
@TableName(value = "serve")
public class Serve implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "主键id")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "门店服务名称")
    private String name;
    @Columns(type = ColumnType.VARCHAR,length = 255,isNull = true,comment = "门店服务图片")
    private String image;
    @Columns(type = ColumnType.INT,length = 11,isNull = true,comment = "排序")
    private Integer sort;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = false,comment = "是否删除 0启用 1停用")
    private Integer status;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = true,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = true,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = true,comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    public Serve(Long id, String name, String image, Integer sort, Integer status) {
        this.id = id;
        this.name = name;
        this.image = image;
        this.sort = sort;
        this.status = status;
    }

}
