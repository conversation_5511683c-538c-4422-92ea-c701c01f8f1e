<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.basic.mapper.MpMsgSceneRelationMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    id,
  	    scene,
  	    msg_id,
  	    tenant_id,
  	    creator,
  	    created
    </sql>

    <!-- 根据场景编号列表获取场景关联订阅消息模板列表 -->
    <select id="selectSceneRelationBySceneList" resultType="com.dz.ms.basic.dto.MpMsgSceneRelationDTO">
        select
            mmsr.scene,
            mmsr.msg_id,
            mm.template_name
        from mp_msg_scene_relation mmsr
        join mp_msg mm on mmsr.msg_id = mm.id
        where mmsr.scene in
        <foreach collection="sceneList" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <!-- 根据场景获取小程序订阅消息模板列表 -->
    <select id="selectSubscribeMsgIdsByScene" resultType="com.dz.ms.basic.dto.MpMsgSubscribeDTO">
        select
            mm.id msgId,
            mm.template_id templateId,
            mm.trigger_type triggerType,
            mmsr.scene
        from mp_msg_scene_relation mmsr
        join mp_msg mm on mmsr.msg_id = mm.id
        where mmsr.scene = #{scene}
        and mm.state = 1
    </select>

</mapper>
