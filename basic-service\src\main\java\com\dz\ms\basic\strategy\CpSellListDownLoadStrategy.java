package com.dz.ms.basic.strategy;

import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.fegin.product.ProductFeignClient;
import com.dz.common.core.service.DownloadStrategy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
public class CpSellListDownLoadStrategy implements DownloadStrategy {

    @Resource
    private ProductFeignClient productFeign;

    @Override
    public void export(DownloadAddParamDTO downloadAddParamDTO) {
        productFeign.exportCpStatic(downloadAddParamDTO);
    }
}
