<template>
  <a-drawer :title="title" width="1200" placement="right" :closable="true" :maskClosable="false" :open="open" @close="onClose">
    <a-spin :spinning="loading">

      <a-form class="form" ref="addForm" :model="addParams" :rules="rules" :labelCol="{ style: 'width:150px' }">
        <div class="form-top-titles-common">问卷基础信息</div>
        <div class="form-top-line-common"></div>
        <a-form-item label="问卷名称" name="title">
          <a-textarea :disabled="disabled" placeholder="输入问卷名称" style="width:500px;" show-count :maxlength="10" v-model:value="addParams.title" />
        </a-form-item>
        <a-form-item label="是否启用" name="state">
          <a-switch :disabled="disabled" v-model:checked="addParams.state" :un-checked-value="0" :checked-value="1" />
        </a-form-item>
        <a-form-item label="问卷开放时间段" name="showTime1">
          <a-range-picker :disabled="disabled" v-model:value="addParams.showTime1" style="width:500px;" :show-time="{ defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')] }" format="YYYY-MM-DD HH:mm:ss" valueFormat="YYYY-MM-DD HH:mm:ss" />
        </a-form-item>
        <div class="form-top-titles-common">问卷设置</div>
        <div class="form-top-line-common"></div>
        <VueDraggable :list="addParams.questions" item-key="tag" handle=".box-title" class="link" ghost-class="ghost" chosen-class="chosenClass" animation="300" @sort="sort" :move="onMove">
        <template #item="{ element: data, index: i }">
          <div class="box">
            <!-- 标题 -->
            <div class="box-title">
              <img :src="drag" class="box-drag">问题{{ i + 1 }}
            </div>
            <!-- 复制 -->
            <!-- <a-button class="box-copy" type="link" @click="copyQuestion(i)">复制</a-button> -->
            <!-- 删除 -->
            <a-popconfirm title="是否确定删除该问题？" @confirm="delQuestion(i)">
              <a-button class="box-del" type="link" :disabled="disabled" :style="{ visibility: i > 0 ? '' : 'hidden',color:disabled?'#d8d8d8':'#dc412d'  }">删除</a-button>
            </a-popconfirm>

            <a-form-item label="选择问题类型" :name="['questions', i, 'questionType']" :rules="[{ required: true, message: '请选择问题类型', trigger: ['blur', 'change'] }]">
              <a-select v-model:value="data.questionType" :disabled="disabled" style="width: 180px" placeholder="请选择问题类型" @change="(e) => changeType(e, data, i)">
                <a-select-option :value="1">单选题</a-select-option>
                <a-select-option :value="2">多选题</a-select-option>
                <a-select-option :value="3">问答题</a-select-option>
                <a-select-option :value="4">打分题</a-select-option>
                <a-select-option :value="5">拍照题</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="问题名称" :name="['questions', i, 'title']" :rules="[{ required: true, message: '请输入问题名称', trigger: ['blur', 'change'] }]" v-if="data.questionType > 0">
              <a-textarea v-model:value="data.title" :autosize='true' :disabled="disabled" :rows="1" style="width:500px" placeholder="请输入问题名称" show-count :maxlength="200" />
            </a-form-item>
            <a-form-item label="副标题"  v-if="data.questionType === 4">
              <a-textarea placeholder="请输入" :autosize='true' style="width:500px" v-model:value="data.subTitle" :disabled="disabled" allow-clear show-count :maxlength="200"></a-textarea>
              
            </a-form-item>
            <a-form-item label="是否必填" :name="['questions', i, 'isMust']" :rules="[{ required: true, message: '请选择是否必填', trigger: ['blur', 'change'] }]" v-if="data.questionType > 0">
              <a-radio-group v-model:value="data.isMust" :disabled="disabled">
                <a-radio :value="1">必填</a-radio>
                <a-radio :value="0">非必填</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-collapse v-model:activeKey="data.activeKey" v-if="data.questionType > 0">
              <a-collapse-panel :key="1" header="题目设置">
                <a-form-item label="提示文案" v-if="data.questionType === 3">
                  <a-textarea v-model:value="data.questionTips" :autosize='true' :disabled="disabled" style="width:500px" placeholder="请输入" show-count :maxlength="200" />
                </a-form-item>
                <a-form-item label="分值量级" :name="['questions', i, 'maxScore']" :rules="[{ required: true, message: '请选择分值量级', trigger: ['blur', 'change'] }]" v-if="data.questionType === 4">
                  <a-select v-model:value="data.maxScore" :disabled="disabled" style="width: 150px" placeholder="请选择分值量级">
                    <a-select-option :value="3">3</a-select-option>
                    <a-select-option :value="4">4</a-select-option>
                    <a-select-option :value="5">5</a-select-option>
                    <a-select-option :value="6">6</a-select-option>
                    <a-select-option :value="7">7</a-select-option>
                    <a-select-option :value="8">8</a-select-option>
                    <a-select-option :value="9">9</a-select-option>
                    <a-select-option :value="10">10</a-select-option>
                  </a-select>
                  最少3分，最高10分

                </a-form-item>
                <a-form-item label="两端极值文案" v-if="data.questionType === 4">
                  <a-form-item label="最小值文案">
                    <a-input placeholder="请输入..." style="width:250px" v-model:value="data.minScoreText" :disabled="disabled" allow-clear show-count :maxlength="10"></a-input>
                  </a-form-item>
                  <a-form-item label="最大值文案">
                    <a-input placeholder="请输入..." style="width:250px" v-model:value="data.maxScoreText" :disabled="disabled" allow-clear show-count :maxlength="10"></a-input>
                  </a-form-item>
                </a-form-item>
                <a-form-item label="最少拍摄数量" :name="['questions', i, 'minPhotoNum']" :rules="[{ required: true, message: '请输入最少拍摄数量', trigger: ['blur', 'change'] }]" v-if="data.questionType === 5">
                  <a-input-number style="width:100px;" :min="1" :max="9" :precision="0" :disabled="disabled" placeholder="请输入" v-model:value="data.minPhotoNum" @blur="(value) => onBlurNumber(value,data)" />
                </a-form-item>
                <a-form-item label="最多拍摄数量" :name="['questions', i, 'maxPhotoNum']" :rules="[{ required: true, message: '请输入最多拍摄数量', trigger: ['blur', 'change'] }]" v-if="data.questionType === 5">
                  <a-input-number style="width:100px;" :min="1" :max="9" :precision="0" :disabled="disabled" placeholder="请输入" v-model:value="data.maxPhotoNum" @blur="(value) => onBlurNumber(value,data)" />
                </a-form-item>
                <VueDraggable :list="data.options" item-key="tag" class="link" ghost-class="ghost" v-if="data.questionType === 1||data.questionType === 2" chosen-class="chosenClass" animation="300" @sort="(e) => sort1(e, i)" :move="onMove">
                  <template #item="{ element: item, index: index1 }">
                    <div class="box-options">
                      <a-form-item :label="numberToLetter(index1 + 1)" :labelCol="{ style:'width:100px' }" :name="['questions', i, 'options', index1, 'title']" :rules="[{ required: true, message: '请输入选项名称', trigger: ['blur', 'change'] }]">
                        <a-textarea v-model:value="item.title"  :autosize='true' style="width:500px" :disabled="disabled" placeholder="请输入选项名称" :maxlength="200" show-count />
                      </a-form-item>
                      <a-button class="delete-content" type="link" :disabled="disabled" :style="{ visibility: data.options.length > 1 ? '' : 'hidden',color:disabled?'#d8d8d8':'#dc412d' }" @click="delOption(data, index1)">删除</a-button>
                    </div>
                  </template>
                </VueDraggable>
                <a-form-item :wrapper-col="{ offset: 3, span: 21 }" v-if="(data.questionType === 1 && data.options.length < 10)||(data.questionType === 2 && data.options.length < 10)">
                  <a-button type="primary" @click="addOption(data)" :disabled="disabled">
                    + 新增选项
                  </a-button>
                </a-form-item>
              </a-collapse-panel>
            </a-collapse>
          </div>
        </template>
      </VueDraggable>
      <a-button type="primary" style="margin:20px 0px" :disabled="disabled" @click="addQuestion(data)">
        + 新增问题
      </a-button>
      </a-form>

    </a-spin>
    <template #footer v-if="!disabled">
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="ok" :loading="loading">确定</a-button>
      </a-space>
    </template>

  </a-drawer>
</template>
<script setup>
import ShelfCrowdConditionsFormItem from '@/views/mall/shelfManagement/giftRack/components/ShelfCrowdConditionsFormItem.vue'
import { addNps, updateNps, npsInfo, crowdsave, crowdUpdate, crowdInfo } from '@/http/index.js'
import { useGlobalStore } from '@/store'
import { message, Modal } from "ant-design-vue";
import Clipboard from 'clipboard';
import _ from "lodash"
import { v4 as uuidv4 } from "uuid";
import drag from '@/assets/drag.png'
import dayjs from "dayjs";
import { CrowdPackTypeOptions1, CrowdPackTypeOptions } from '@/utils/dict-options'
const $router = useRouter();
const global = useGlobalStore()
const addForm = ref(null)
import { cloneDeep } from 'lodash'
import { onMounted } from 'vue';
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  // 操作抽屉打开关闭
  visible: {
    type: Boolean,
    default: false
  },
  // 数据ID
  id: {
    type: String || Number,
    default: ''
  },
  // 操作类型
  type: {
    type: Number,
    default: 0, // 0-新增 1-编辑  2-查看
  }

})
// 置灰
const disabled = computed(() => {
  return props.type == 1
})

// 标题
const title = computed(() => {
  return '问卷' + ['新增', '详情', '编辑', '新增'][props.type]
})

const { open, addParams, rules, loading, } = toRefs(reactive({
  open: props.visible,

  loading: false,

  addParams: {
    title: '',
    showTime1: null, // 问卷开放时间段
    state: 0, // 是否启用
    questions: [{
      questionType: 1,
      title: '',
      isMust: 1,
      tag: uuidv4(),
      sort: 1,
      activeKey: 1,
      options: [{
        title: '',
        tag: uuidv4(),
        sort: 1
      }]
    }]
  },
  rules: {
    title: [
      { required: true, message: "请输入问卷名称", trigger: ["blur", "change"] },
    ],
    showTime1: [
      {
        type: "array",
        required: true,
        message: "请选择问卷开放时间",
        trigger: ["blur", "change"],
      },
    ],


  }
})
);
//选择问题类型
const changeType = (value, item, idx) => {
  item.title = ''
  item.isMust = 1
  item.activeKey = 1
  if (value === 1 || value === 2) {
    item.sort = idx + 1
    item.options = [{
      title: '',
      tag: uuidv4(),
      sort: 1
    }]
  } else if (value === 3) {
    item.questionTips = '' //答案提示
  } else if (value === 4) {
    item.maxScore = null
    item.minScoreText = ''
    item.maxScoreText = ''
  } else if (value === 5) {
    item.minPhotoNum = ''
    item.maxPhotoNum = ''
  }
}

const onBlurNumber = (value, data) => {
  if (data.minPhotoNum && data.maxPhotoNum && data.minPhotoNum > data.maxPhotoNum) {
    let val = data.minPhotoNum
    data.minPhotoNum = data.maxPhotoNum
    data.maxPhotoNum = val
  }
}
//新增问题
const addQuestion = () => {
  addParams.value.questions.push({
    questionType: 1,
    title: '',
    isMust: 1,
    tag: uuidv4(),
    sort: addParams.value.questions.length + 1,
    activeKey: 1,
    options: [{
      title: '',
      tag: uuidv4(),
      sort: 1
    }]
  })
}

//排序
const sort = (data) => {
  let arr = _.cloneDeep(addParams.value.questions)
  arr.map((i, index) => {
    i.sort = index + 1
  })
  addParams.value.questions = arr

}
//选项排序
const sort1 = (data, idx) => {
  let arr1 = _.cloneDeep(addParams.value.questions[idx].options)
  arr1.map((i, index) => {
    i.sort = index + 1
  })
  addParams.value.questions[idx].options = arr1
}
const numberToLetter = (index) => {
  return '选项' + String.fromCharCode(64 + index)
}
// 新增选项
const addOption = (item) => {
  let length = item.options.length
  item.options.push({
    tag: uuidv4(),
    sort: length + 1,
  })
}
//删除选项
const delOption = (data, idx) => {
  data.options.splice(idx, 1)
}

//删除问题
const delQuestion = (idx) => {
  addParams.value.questions.splice(idx, 1)
}

const onMove = () => {
  if (disabled.value) {
    return false
  }
  return true

}

function getDefaultFormFields() {
  return {
    crowdType: 0,
    timeType: 0,
    createTime: []
  }
}
watch(() => props.visible, (value) => {
  // 弹窗操作
  open.value = props.visible
  addParams.value = {
    title: '',
    showTime1: null, // 问卷开放时间段
    state: 0, // 是否启用
    questions: [{
      questionType: 1,
      title: '',
      isMust: 1,
      tag: uuidv4(),
      sort: 1,
      activeKey: 1,
      options: [{
        title: '',
        tag: uuidv4(),
        sort: 1
      }]
    }]
  }
  addForm.value?.resetFields()
  if (open.value) {

    initData()
  }
})


//所有接口调取出
const initData = async () => {
  const promiseArr = []
  if (props.id) {
    promiseArr.push(npsInfo({ id: props.id }))
  }
  // 如果有新的接口就在这里push
  loading.value = true
  try {
    const [res] = await Promise.all(promiseArr)
    // console.log("🚀 ~ initData ~ crowdInfo:", crowdInfoA)

    if (res) {
      res.data.showTime1 =
      res.data.startTime
        ? [res.data.startTime, res.data.endTime]
        : [];
      if (props.type == 3) {
        res.data.title = res.data.title + '复制'
        res.data.questions.map(x => {
          x.id = ''
          if (x.questionType === 1 || x.questionType === 2) {
            x.options.map(y => {
              y.id = ''
            })
          }
        })
        delete res.data.id
      }
      addParams.value = {
        ...res.data,
      }
    }
    loading.value = false
  } catch (error) {
    loading.value = false
    console.error('获取数据失败:', error)
  }
}
function exportEmployee() {

}




// 关闭弹窗
const onClose = () => {
  emit('cancel')
}
// 确定
const ok = (flag) => {
  addForm.value.validate().then((res) => {
    let api = addParams.value.id ? updateNps : addNps;
    let params = cloneDeep(addParams.value);
    params.startTime = params.showTime1 ? params.showTime1[0] : "";
    params.endTime = params.showTime1 ? params.showTime1[1] : "";
    delete params.showTime1;
    params.state = params.state * 1;
    params.questions.map(x => {
      if (x.questionType !== 1 && x.questionType !== 2) {
        delete x.options
      }
    })
    if (flag) {
      loading.value = true;
      api(params)
        .then((res) => {
          if (res.code === 0) {
            message.success("保存成功");
            emit('ok', props.id)
          }
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
}



</script>

<style scoped lang="scss">
:deep(.searchForm .ant-form-item) {
  margin-bottom: 16px;
}
//影藏红的点
.hide-required-mark {
  :deep(.ant-form-item-required) {
    display: none;
  }
}
:deep(.anticon-eye-invisible) {
  display: none;
}

.member-role {
  background: #f2f2f2;
  margin-bottom: 20px;
  padding: 5px 10px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &:nth-child(odd) {
    margin-right: 35px;
  }
}
.flex {
  display: flex;
  align-items: center;
}
.box {
  position: relative;
  width: 1100px;
  padding: 15px 15px 10px;
  margin-top: 70px;
  margin-bottom: 10px;
  border: 2px solid #757575;

  &-title {
    position: absolute;
    top: -37px;
    left: -2px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 90px;
    height: 35px;
    color: #fff;
    background-color: #1890ff;
  }
  &-drag {
    width: 30px;
    padding: 5px;
  }

  &-options {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    .delete-content {
      color: #dc412d;
      font-size: 16px;
      margin-left: 50px;
    }
  }

  &-copy {
    position: absolute;
    top: -30px;
    right: 70px;
    color: #1890ff;
  }

  &-del {
    position: absolute;
    top: -30px;
    right: 30px;
    color: red;
  }
}
</style>
