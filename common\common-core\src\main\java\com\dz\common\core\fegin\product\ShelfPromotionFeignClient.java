package com.dz.common.core.fegin.product;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.DownloadAddParamDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(name = ServiceConstant.PRODUCT_SERVICE_NAME, contextId = "ShelfPromotionFeignClient")
public interface ShelfPromotionFeignClient {

    /**
     * 导出货架推广列表
     *
     * @return
     */
    @PostMapping(value = "/shelf_promotion/export_promotion_list")
    Result<Void> exportPromotionList(@RequestBody DownloadAddParamDTO exportParam);


}

