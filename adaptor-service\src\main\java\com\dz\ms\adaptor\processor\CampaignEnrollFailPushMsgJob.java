package com.dz.ms.adaptor.processor;


import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.fegin.basic.MpMsgPushFeignClient;
import com.dz.common.core.threadlocal.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/7
 */
@Slf4j
@Component
public class CampaignEnrollFailPushMsgJob implements BasicProcessor {

    @Resource
    private MpMsgPushFeignClient mpMsgPushFeignClient;


    @Override
    public ProcessResult process(TaskContext taskContext) throws Exception {
        String jobParams = taskContext.getJobParams();
        if(Objects.isNull(jobParams)){
            return new ProcessResult(true, "param null");
        }
        SecurityContext.setUser(new CurrentUserDTO(1L));
        String[] strs = jobParams.split(",");
        List<Long> uids = null;
        if (strs.length > 4) {
            uids = new ArrayList<>();
            String[] users = strs[6].split("-");
            for (String str : users) {
                Long id = NumberUtils.toLong(str);
                if (id > 0) {
                    uids.add(id);
                }
            }
            if (uids.isEmpty()) {
                uids = null;
            }
        }
        mpMsgPushFeignClient.activityEnrollMsgPush(1L, new String[]{strs[0], strs[1], strs[2]}, strs[3], uids);
        log.info("活动报名失败后推送第二轮报名订阅消息执行完成");
        return new ProcessResult(true, "success");
    }
}
