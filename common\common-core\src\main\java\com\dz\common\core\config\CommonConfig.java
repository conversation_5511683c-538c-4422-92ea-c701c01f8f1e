package com.dz.common.core.config;

import com.dz.common.core.aop.SysLogAspect;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;

/**
 * 公共配置
 * @author: Handy
 * @date:   2022/2/4 19:14
 */
@EnableCaching
@ComponentScan(basePackages = {"com.dz.**.controller", "com.dz.**.service","com.dz.common.core.filter"})
@EnableFeignClients(value = {"com.dz.common.core.fegin","com.dz.ms.**.fegin"})
@MapperScan(basePackages = {"com.dz.ms.**.mapper"})
@Import({BaseConfig.class, DataSourceConfig.class, SwaggerConfig.class, MybatisPlusConfig.class, BeforeStartedConfig.class, SysLogAspect.class, RedisDistributedLock.class})
public class CommonConfig {

}
