<template>
  <div class="header-title">视频内容</div>
  <a-form-item>
    <uploadVideo :width="300" :height="100" :imgUrl="data.imgUrl" :form="data" path="imgUrl" @success="uploadSuccess">
    </uploadVideo>
    <div class="global-tip">视频时长需要保持在15秒内，否则可能小程序审核不通过；支持MP4格式视频，大小限制在10M内，建议长宽比不大于2（视频高度根据宽度自适应）</div>
  </a-form-item>
  <div style="margin:10px 0;font-weight:bold">视频封面</div>
  <a-form-item>
    <uploadImg :max="10" :width="300" :height="100" :imgUrl="data.coverUrl" :form="data" path="coverUrl" :disabled="disabled" @success="uploadSuccess1" />
    <div class="global-tip">封面尺寸建议同视频尺寸保持一致</div>
  </a-form-item>
  <div style="margin:10px 0;font-weight:bold">跳转热区</div>
  <addLink :imgUrl="data.coverUrl" :links="data.imgLinks" :components="components" @ok="(link)=>data.imgLinks=link">
    <a-button block>设置热区</a-button>
  </addLink>
  <a-form-item label="自动播放" style="margin-top:20px">
    <a-space>
      <a-switch v-model:checked="data.autoplay" />
      <div class="global-tip" style="font-size:11px;">(开启后，进入小程序页面时将自动播放视频)</div>
    </a-space>
  </a-form-item>
  <a-form-item label="循环播放">
    <a-space>
      <a-switch v-model:checked="data.loop" />
      <div class="global-tip" style="font-size:11px;">(开启后，进入小程序页面时将循环播放视频)</div>
    </a-space>
  </a-form-item>
  <a-form-item label="静音播放">
    <a-space>
      <a-switch v-model:checked="data.muted" />
      <div class="global-tip" style="font-size:11px;">(开启后，进入小程序页面时将静音播放视频)</div>
    </a-space>
  </a-form-item>
  <a-form-item label="播放按键">
    <a-space>
      <a-switch v-model:checked="data.playBtn" />
      <div class="global-tip" style="font-size:11px;">(开启后，是否显示播放按键，可暂停/播放)</div>
    </a-space>
  </a-form-item>
  <!-- <a-form-item label="显示控制条">
    <a-space>
      <a-switch v-model:checked="data.controls" />
      <div class="global-tip" style="font-size:11px;">(开启后，小程序播放时会显示控制条)</div>
    </a-space>
  </a-form-item> -->

  <a-form-item label="显示静音按钮" :labelCol="{width:'120px'}">
    <a-space>
      <a-switch v-model:checked="data.muteBtn" />
      <div class="global-tip" style="font-size:11px;">(开启后，小程序播放时会显示禁音按钮)</div>
    </a-space>
  </a-form-item>
  <div class="header-title">背景设置</div>
  <bgSet :addParams="data"></bgSet>
  <div class="header-title">组件样式</div>
  <!-- <a-form-item label="底部空白">
    <a-input-number placeholder="请输入" :precision="0" v-model:value="data.bottom" addon-after="px"></a-input-number>
  </a-form-item> -->
  <a-form-item label="高度设置">
    <a-radio-group v-model:value="data.heightSet">
      <a-radio :value="0">自适应高度</a-radio>
      <a-radio :value="1">一屏高度</a-radio>
    </a-radio-group>
  </a-form-item>
  <a-form-item label="圆角" :labelCol="{width:'50px'}">
    <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.borderRadius" addon-after="px"></a-input-number>
  </a-form-item>
  <a-form-item label="边距" :labelCol="{width:'50px'}">
    <a-space>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingTop" addon-before="上" addon-after="px"></a-input-number>
      </a-form-item>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingBottom" addon-before="下" addon-after="px"></a-input-number>
      </a-form-item>
    </a-space>
    <a-space>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingLeft" addon-before="左" addon-after="px"></a-input-number>
      </a-form-item>
      <a-form-item>
        <a-input-number placeholder="请输入" :min="0" :precision="0" v-model:value="data.paddingRight" addon-before="右" addon-after="px"></a-input-number>
      </a-form-item>
    </a-space>
  </a-form-item>

</template>
<script setup>
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  // 所有组件数组
  components: {
    type: Array,
    default() {
      return []
    }
  },
  // 当前组件数据
  data: {
    type: Object,
    default() {
      return {}
    }
  },
  // 组件展示的宽度
  width: {
    type: Number,
    default: 750,
  },

})

// 上 视频
const uploadSuccess = async (data) => {
  let { form, path, imgUrl } = data;
  form[path] = imgUrl
}
// 上传图片 视频
const uploadSuccess1 = async (data) => {
  let { form, path, imgUrl, imgWidth, imgHeight } = data;
  form[path] = imgUrl
  form.imgWidth = imgWidth
  form.imgHeight = imgHeight
}


// 修改颜色
const changeColor = async (key, color) => {
  props.addParams[key] = color
}

</script>

<style>
</style>
