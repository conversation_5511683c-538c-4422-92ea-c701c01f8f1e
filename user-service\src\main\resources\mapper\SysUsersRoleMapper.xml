<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.user.mapper.SysUsersRoleMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    id,
  	    uid,
  	    role_id,
  	    tenant_id,
  	    created,
  	    creator,
  	    modified,
  	    modifier
    </sql>

    <!-- 根据角色Id列表获取角色对应绑定用户数量 -->
    <select id="getRoleUserCountByRoleIds" resultType="com.dz.ms.user.dto.SysRoleDTO">
        select
        role_id id,
        count(id) userNum
        from sys_users_role
        where role_id in
        <foreach collection="roleIds" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by role_id
    </select>

</mapper>
