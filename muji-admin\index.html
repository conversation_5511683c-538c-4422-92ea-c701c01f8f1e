<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" id="favicon" href="" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

</head>

<body>
  <div id="app"></div>
  <script type="module" src="/src/main.js"></script>
  <script type="module">
    // 获取<link>标签的引用
    var link = document.getElementById('favicon');
    // 修改href属性
    if (link) {
      link.href = `${import.meta.env.VITE_BASE}${import.meta.env.VITE_LOGO_NAME}.svg`;
      // console.log(link.href);
      // console.log("🚀 ~ link.href:", link.href)
    }
  </script>
</body>

</html>
<style lang="scss" scoped>
  #app {
    height: 100%;
  }
</style>