package com.dz.ms.basic.controller;

import com.dz.common.base.vo.Result;
import com.dz.common.core.service.RedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 缓存操作
 * <AUTHOR>
 */
@Api(tags="缓存操作")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class RedisController {

    @Resource
    private RedisService redisService;

    /**
     * 缓存获取
     * @param key
     * @return
     */
    @ApiOperation("缓存keys")
    @GetMapping(value = "/oms/redis/keys")
    public Result<Object> keys(@RequestParam("key") String key) {
        Result<Object> result = new Result<>();
        result.setData(redisService.keys(key));
        return result;
    }

    /**
     * 缓存获取
     * @param key
     * @return
     */
    @ApiOperation("缓存获取")
    @GetMapping(value = "/oms/redis/get")
    public Result<Object> get(@RequestParam("key") String key) {
        Result<Object> result = new Result<>();
        result.setData(redisService.get(key));
        return result;
    }

    /**
     * String缓存获取
     * @param key
     * @return
     */
    @ApiOperation("String缓存获取")
    @GetMapping(value = "/oms/redis/get_string")
    public Result<Object> getString(@RequestParam("key") String key) {
        Result<Object> result = new Result<>();
        result.setData(redisService.getString(key));
        return result;
    }

    /**
     * 缓存放入
     * @param key
     * @param object
     * @param seconds
     * @return
     */
    @ApiOperation("缓存放入")
    @PostMapping(value = "/oms/redis/set")
    public Result<Object> set(@RequestParam("key") String key,@RequestBody Object object,@RequestParam("seconds") Long seconds) {
        Result<Object> result = new Result<>();
        redisService.set(key,object,seconds);
        return result;
    }

    /**
     * 删除缓存
     * @param key
     * @return
     */
    @ApiOperation("删除缓存")
    @GetMapping(value = "/oms/redis/delete")
    public Result<Object> delete(@RequestParam("key") String key) {
        Result<Object> result = new Result<>();
        redisService.delAll(key);
        return result;
    }

}
