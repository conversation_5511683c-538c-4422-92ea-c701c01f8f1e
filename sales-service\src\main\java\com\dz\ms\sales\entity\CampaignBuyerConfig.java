package com.dz.ms.sales.entity;


import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/30
 */
@Getter
@Setter
@NoArgsConstructor
@Table("活动购买资格")
@TableName(value = "campaign_buyer_config")
public class CampaignBuyerConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "id")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = true, comment = "活动标识", isIndex = true)
    private String campaignCode;

    @Columns(type = ColumnType.VARCHAR, length = 1000, isNull = true, comment = "商品sku")
    private String sku;

    @Columns(type = ColumnType.VARCHAR, length = 36, isNull = true, comment = "购买渠道 1全部 2会小 3线下 4天猫 5京东 6官小 多个逗号隔开")
    private String buyChannel;

    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "渠道开始时间")
    private Date channelStartTime;

    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "渠道结束时间")
    private Date channelEndTime;

    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "是否删除 0未删除 1已删除")
    @TableLogic
    private Integer isDeleted;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;

}
