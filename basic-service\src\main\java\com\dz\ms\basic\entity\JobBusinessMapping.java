package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 定时任务与业务参数关系
 * @author: Handy
 * @date:   2022/07/07 15:05
 */
@Getter
@Setter
@NoArgsConstructor
@Table("定时任务与业务参数关系")
@TableName(value = "job_business_mapping")
public class JobBusinessMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 任务ID */
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "任务ID")
    private Long jobId;
    /** 业务参数组合(任务类型-业务参数) */
    @Columns(type = ColumnType.VARCHAR,length = 500,isNull = true,comment = "业务参数组合(任务类型-业务参数)")
    private String params;
    /** 创建人 */
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = true,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    /** 创建时间 */
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;

    public JobBusinessMapping(Long id, Long jobId, String params) {
        this.id = id;
        this.jobId = jobId;
        this.params = params;
    }

}
