package com.dz.common.core.dto.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CpStaticDTO {

    @ApiModelProperty(value = "商品在货架的ID")
    private Long shelfProductId;

    @ApiModelProperty(value = "CP编码")
    private String cpCode;

    @ApiModelProperty(value = "商品ID")
    private Long productId;

    @ApiModelProperty(value = "成本价")
    private BigDecimal originPrice;

    @ApiModelProperty(value = "吊牌价")
    private BigDecimal prePrice;

    @ApiModelProperty(value = "积分价 货架积分价")
    private Integer costPoint;

    @ApiModelProperty(value = "上架库存")
    private Integer onInventoryAmount;

    @ApiModelProperty(value = "剩余库存量")
    private Integer restInventoryAmount;

    @ApiModelProperty(value = "销售件数")
    private Integer sellAmount;

    @ApiModelProperty(value = "销售人数")
    private Integer sellPeopleAmount;

    @ApiModelProperty(value = "销售的总积分")
    private Integer totalPointAmount;
}
