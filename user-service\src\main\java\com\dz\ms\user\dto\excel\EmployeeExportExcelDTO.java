package com.dz.ms.user.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.dz.ms.user.utils.EmployeeStateConverter;
import lombok.Data;

import java.util.Date;

/**
 * 类注释
 *
 * @AUTHOR 倗诚
 * @DATE 2023/7/16 17:26
 * @VERSION 1.0.0
 */
@Data
public class EmployeeExportExcelDTO {

    @ExcelProperty("姓名")
    private String empName;
    @ExcelProperty("导购编码")
    private String empCode;
    @ExcelProperty("手机号")
    private String mobile;
    @ExcelProperty("企微号")
    private String qwCode;
    @ExcelProperty("职位")
    private String positions;
    @ExcelProperty("专柜")
    private String storeNames;

    /**
     * 导出字段
     */
    @ExcelProperty(value = "是否离职", converter = EmployeeStateConverter.class)
    private Integer state;
    @ExcelProperty("离职时间")
    private Date leaveTime;




}
