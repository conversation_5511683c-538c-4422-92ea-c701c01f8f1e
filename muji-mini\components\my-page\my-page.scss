.selfModal {
  position: fixed;
  z-index: 10000001;
  left: 0;
  top: 0;
  width: 750rpx;
  height: 100vh;
  font-size: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox-alone {
  align-items: flex-start !important;
  overflow: visible !important;
}

.my-label-class {
  position: relative;
  top: -5rpx;
}

.checkbox {
  padding-top: 20rpx;

  .underline-text {
    text-decoration: underline;
    font-weight: 600;
  }

  .checkbox-quadrate {
    display: flex;
    align-items: center;

    &-box {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30rpx;
      height: 30rpx;
      border-radius: 2rpx;
      border: 1.5rpx solid #000;
      margin-right: 15rpx;
      padding: 1rpx;
      margin-top: 1rpx;
      box-sizing: border-box;
    }

    .all {
      font-family: NotoSansHans, NotoSansHans;
      font-weight: 600;
      font-size: 24rpx;
      color: #3c3c43;
      line-height: 42rpx;
      text-align: left;
      font-style: normal;
    }

    .iconfontColor {
      font-size: 16rpx;
      line-height: 16rpx;
      color: #fff;
      display: flex;
    }

    .active {
      background: #3c3c43;
    }
  }

  .quadrate-text {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 22rpx;
    color: #3c3c43;
    line-height: 36rpx;
    text-align: left;
    font-style: normal;
  }

  &-item {
    margin-top: 24rpx;
    flex-shrink: 0;
  }

  .checkbox-round {
    display: flex;
    align-items: center;

    &-box {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 22rpx;
      height: 22rpx;
      margin-right: 15rpx;
      border-radius: 50%;
      // background: #3C3C43;
      border: 1.25rpx solid #000;
      padding: 1rpx;
    }

    .quadrate-text {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 22rpx;
      color: #3c3c43;
      line-height: 36rpx;
      text-align: left;
      font-style: normal;
    }

    .iconfontColor {
      font-size: 16rpx;
      line-height: 16rpx;
      color: #fff;
      display: flex;
    }

    .active {
      background: #3c3c43;
    }
  }

  &-text {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 22rpx;
    color: #3c3c43;
    line-height: 36rpx;
    text-align: left;
    font-style: normal;
  }
}

/* 底部tabBar安全区域 */
.my-page-safe-tab-bar {
  height: 80px;
}

/* iPhoneX底部安全区域 */
.my-page-safe-bottom {
  height: constant(safe-area-inset-bottom);
  height: env(safe-area-inset-bottom);
}

.tips {
  position: fixed;
  z-index: 1000;
  bottom: calc(160rpx + env(safe-area-inset-bottom));
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 70rpx;
  background: #3c3c43;
  opacity: 0.9;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #ffffff;
  // line-height: 30rpx;
  text-align: center;
  font-style: normal;

  &-image {
    position: absolute;
    right: 46rpx;
    width: 20rpx;
    height: 20rpx;
    padding: 10px;
  }
}

.page {
  &-button {
    flex-shrink: 0;
    box-sizing: border-box;
    margin: 0 auto;

    &.confirm {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 510rpx;
      height: 80rpx;
      background: #3c3c43;
      border-radius: 5rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28rpx;
      color: #ffffff;
      margin-top: 40rpx;
    }

    &.cancel {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #3c3c43;
      padding-top: 28rpx;
      line-height: 1.5;
      text-align: center;
    }
  }
}
