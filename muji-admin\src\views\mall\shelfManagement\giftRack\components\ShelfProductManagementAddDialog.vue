<template>
  <a-modal v-model:open="thisFields.open" title="" @ok="thisMethods.handleOk" @cancel="thisMethods.cancel" :okButtonProps="{disabled:thisFields.loading}" width="1200px">
    <layout>
      <template v-slot:header>
        <!--:disabled="!$hasPermission('mall:permission:searchGiftGoods')"-->
        <searchForm :formParams="searchFields" @cancel="whenClickReset" @ok="whenClickSearch">
          <a-form-item label="商品名称" name="productName">
            <a-input placeholder="请输入" allow-clear v-model:value="searchFields.productName" allowClear @keyup.enter="whenClickSearch"></a-input>
          </a-form-item>
          <BaseProductTypeSelect label="商品类型" name="pdType" v-model="searchFields.pdType" />
          <template v-if="props.bizType === ''">
            <BaseBelongingShelfSelect label="所处货架" name="shelfIdList" v-model="searchFields.shelfIdList" mode="multiple" />
          </template>
        </searchForm>
      </template>
      <template v-slot="{ height }">
        <div>
          <template v-if="props.bizType === ''">
            <div class="checkboxWrap">
              <a-checkbox v-model:checked="thisFields.hideAddedProducts" @change="refresh">
                <span class="ui-c-grey">隐藏已添加商品</span>
              </a-checkbox>
            </div>
          </template>
          <a-table
            class="shelf-product-management-add-table" :indentSize="20" row-key="id"
            :row-selection="{
              preserveSelectedRowKeys: true,
              selectedRowKeys: thisFields.selectedRowKeys,
              onChange: thisMethods.onSelectChange,
              getCheckboxProps: thisMethods.getCheckboxProps
            }"
            :scroll="{ scrollToFirstRowOnChange: true, x: '100%' }"
            :dataSource="dataSource"
            :columns="thisFields.tableHeader"
            :pagination="pagination"
            :loading="loading"
            @change="whenPaginationChange"
          >
            <template #bodyCell="{text, record, index, column}">
              <template v-if="column.dataIndex === 'index'">{{ (current - 1) * pageSize + 1 + index }}</template>
              <template v-if="column.dataIndex === 'sceneImg'">
                <a-image v-if="record.sceneImg" :src="record.sceneImg" :width="50" :height="50"></a-image>
                <div v-else>--</div>
              </template>
              <template v-if="column.dataIndex === 'shopWindowImg'">
                <a-image v-if="record.shopWindowImg" :src="record.shopWindowImg" :width="50" :height="50" />
                <div v-else>--</div>
              </template>
              <template v-if="column.dataIndex === 'shelfImg'">
                <a-image v-if="record.shelfImg" :src="record.shelfImg" :width="50" :height="50" />
                <div v-else>--</div>
              </template>
              <template v-if="column.dataIndex === 'tagList'">
                {{ record.tagList.map(v => v.name).join('、') }}
              </template>
            </template>
          </a-table>
        </div>
      </template>
    </layout>
  </a-modal>
</template>
<script setup>
import { usePagination } from 'vue-request'
import { apiGiftRack, productList } from '@/http/index.js'
import { onMounted, watch } from 'vue'
import { cloneDeep } from 'lodash'
import { v4 } from 'uuid'

const props = defineProps({
  bizType: { // 业务类型：默认-兑礼货架、crowdPurchaseRestriction-人群限购
    type: String,
    default: ''
  },
  shelfId: {
    type: [String, Number],
    default: ''
  },
  beSelectedProductIdArrayObjectList: {
    type: Array,
    default: () => ([])
  },
  allSelectedProductList: { // 所有已选中的商品（人群限购跨组件）
    type: Array,
    default: () => ([])
  },
  modelValue: {
    type: Boolean,
    default: false
  }
})
const emits = defineEmits(['update:modelValue', 'ok'])

const pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ['bottomLeft']
  }
})
const total = ref(0)
const getDefaultSearchFields = () => ({
  pdType: undefined,
  shelfIdList: [],
  productName: undefined
})
const searchFields = reactive(getDefaultSearchFields())
const thisFields = reactive({
  hideAddedProducts: true,
  selectedRowKeys: [],
  selectedRowObjs: [],
  loading: false,
  open: false,
  tableHeader: [
    { title: '序号', dataIndex: 'index', align: 'center', width: 80 },
    { title: '商品名称', dataIndex: 'productName', align: 'center', ellipsis: true, width: 100 },
    { title: '商品类型', dataIndex: 'pdTypeDesc', align: 'center', ellipsis: true, width: 100 },
    { title: '创建时间', dataIndex: 'created', align: 'center', ellipsis: true, width: 140 },
    { title: '商品主图', dataIndex: 'shelfImg', align: 'center', ellipsis: true, width: 100 },
    { title: '商品标签', dataIndex: 'tagList', align: 'center', ellipsis: true, width: 100 },
    { title: '积分价值', dataIndex: 'costPoint', align: 'center', ellipsis: true, width: 100 },
    { title: '累计兑换量', dataIndex: 'exchangeNum', align: 'center', ellipsis: true, width: 100 }
  ]
})
const thisMethods = {
  onSelectChange (e, a) {
    // console.log('onSelectChange：', e, a)
    thisFields.selectedRowKeys = e
    thisFields.selectedRowObjs = a
  },
  getCheckboxProps (record) {
    let disabled = false
    if (props.bizType === 'crowdPurchaseRestriction' && record.disabled) {
      disabled = true
    }
    return { disabled }
  },
  setOpen () {
    thisFields.open = props.modelValue
    if (thisFields.open) {
      whenClickReset()
    }
  },
  clearSelected () {
    thisFields.selectedRowKeys = []
    thisFields.selectedRowObjs = []
  },
  cancel () {
    emits('update:modelValue', false)
    thisMethods.clearSelected()
  },
  handleOk () {
    emits('update:modelValue', false)
    const list = thisFields.selectedRowObjs.map(v => ({
      ...v,
      id: undefined,
      ptType: v.ptType,
      productId: props.bizType === 'crowdPurchaseRestriction' ? v.productId : v.id,
      shelfProductId: v.id, // 人群限购需要
      inventoryType: 2, // 人群限购需要 1同当前库存 2活动规则库存
      ruleInventory: v.currentInventory,
      uuid: v4()
    }))
    emits('ok', list)
    thisMethods.clearSelected()
  }
}

const { data: dataSource, run, loading, pageSize, current, refresh } = usePagination((param) => {
  const data = { ...param, ...searchFields }
  data.state = 1 // 添加商品添加的是启用中的商品
  if (thisFields.hideAddedProducts) {
    data.hiddenProductIdList = props.beSelectedProductIdArrayObjectList.map(v => v.productId)
  }
  if (props.bizType === 'crowdPurchaseRestriction' && props.shelfId) {
    data.hiddenProductIdList = props?.allSelectedProductList?.map(v => v.productId) || []
    return apiGiftRack.getShelfProductPageList({
      ...data,
      shelfId: props.shelfId
    })
  } else {
    return productList(data)
  }
}, {
  manual: true, // 修改为false 让其自动执行
  pagination: {
    currentKey: 'pageNum', // 通过该值指定接口 当前页数 参数的属性值
    pageSizeKey: 'pageSize', // 通过该值指定接口 每页获取条数 参数的属性值
    totalKey: 'data.data.count' // 指定 data 中 total 属性的路径
  },
  formatResult: res => { // 返回数据格式化
    total.value = res.data.count
    return res.data.list
  }
})

const whenPaginationChange = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}
const whenClickSearch = () => {
  run({ pageNum: 1, pageSize: pageSize.value })
}
const whenClickReset = () => {
  Object.assign(searchFields, getDefaultSearchFields())
  whenClickSearch()
}

const setTableHeader = () => {
  if (props.bizType === 'crowdPurchaseRestriction') {
    thisFields.tableHeader = [
      { title: '序号', dataIndex: 'index', align: 'center', width: 80 },
      { title: '商品名称', dataIndex: 'productName', align: 'center', ellipsis: true, width: 100 },
      { title: '商品类型', dataIndex: 'pdTypeDesc', align: 'center', ellipsis: true, width: 100 },
      { title: '创建时间', dataIndex: 'created', align: 'center', ellipsis: true, width: 140 },
      { title: '商品场景图', dataIndex: 'sceneImg', align: 'center', ellipsis: true, width: 100 },
      { title: '商品橱窗图', dataIndex: 'shopWindowImg', align: 'center', ellipsis: true, width: 100 },
      { title: '商品标签', dataIndex: 'tagList', align: 'center', ellipsis: true, width: 100 },
      { title: '积分价值', dataIndex: 'costPoint', align: 'center', ellipsis: true, width: 100 },
      { title: '累计兑换量', dataIndex: 'exchangeNum', align: 'center', ellipsis: true, width: 100 }
    ]
  }
}

onMounted(() => {
  thisMethods.setOpen()
  setTableHeader()
})
watch(() => props.modelValue, () => thisMethods.setOpen())
</script>

<style scoped lang="scss">
:deep(.shelf-product-management-add-table .ant-pagination-options-size-changer) {
  width: auto !important;
}
.checkboxWrap {
  margin-left: 13px;
  margin-bottom: 5px;
}
</style>
