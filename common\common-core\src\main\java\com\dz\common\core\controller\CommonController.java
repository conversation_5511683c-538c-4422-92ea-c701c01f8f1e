package com.dz.common.core.controller;

import com.dz.common.base.vo.Result;
import com.dz.common.core.service.ModelToSqlSevice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.sql.SQLException;

/**
 * 公共Controller
 * @author: Handy
 * @date: 2022/02/06 15:29
 */
@Slf4j
@RestController
public class CommonController {

    @Value("${daozhi.version:no}")
    private String version;
    @Resource
    private ModelToSqlSevice modelToSqlSevice;

    /**
     * 根据model创建或更新数据库
     * @return
     */
    @PostMapping(value = "/model_to_sql")
    public Result<Boolean> modelToSql() {
        Result<Boolean> result = new Result<>();
        modelToSqlSevice.modelToSql(false);
        result.setData(true);
        return result;
    }

    @GetMapping(value = "/healthcheck")
    public String healthcheck() {
        return "ok";
    }

    @GetMapping(value = "/oms/login/version")
    public String getversion() {
        return version;
    }

}
