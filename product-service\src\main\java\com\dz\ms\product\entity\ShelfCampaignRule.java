package com.dz.ms.product.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 营销活动规则
 *
 * @author: LiinNs
 * @date: 2024/11/27 15:15
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table("营销活动规则")
@TableName(value = "shelf_campaign_rule")
public class ShelfCampaignRule implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "营销活动ID", isIndex = true)
    private Long campaignId;
    @Columns(type = ColumnType.VARCHAR, length = 256, isNull = true, comment = "规则名称 展示位活动角标", isIndex = true)
    private String name;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "人群包ID", isIndex = true)
    private Long groupId;
    @Columns(type = ColumnType.VARCHAR, length = 4000, isNull = true, comment = "规则组件配置")
    private String content;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "货架ID", isIndex = true)
    private Long shelfId;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = false, comment = "1活动时间内 2周期")
    private Integer ruleType;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "周期开始时间")
    private Date cycleCreated;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "周期天数")
    private Integer period;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "规则范围内每个用户限购数量")
    private Integer ruleNum;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "是否删除 0未删除 1已删除")
    @TableLogic
    private Integer isDeleted;

    public ShelfCampaignRule(Long id, Long campaignId, String name, Long groupId, String content, Long shelfId, Integer ruleType, Date cycleCreated, Integer period, Integer ruleNum) {
        this.id = id;
        this.campaignId = campaignId;
        this.name = name;
        this.groupId = groupId;
        this.content = content;
        this.shelfId = shelfId;
        this.ruleType = ruleType;
        this.cycleCreated = cycleCreated;
        this.period = period;
        this.ruleNum = ruleNum;
    }

}
