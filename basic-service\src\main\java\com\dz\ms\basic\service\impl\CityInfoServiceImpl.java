package com.dz.ms.basic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.core.annotation.Cacheable;
import com.dz.common.core.dto.basic.CityDTO;
import com.dz.common.core.dto.basic.ProvinceCityDTO;
import com.dz.common.core.dto.basic.ProvinceDTO;
import com.dz.ms.basic.constants.CacheKeys;
import com.dz.ms.basic.entity.CityInfo;
import com.dz.ms.basic.entity.ProvinceInfo;
import com.dz.ms.basic.mapper.CityInfoMapper;
import com.dz.ms.basic.mapper.ProvinceInfoMapper;
import com.dz.ms.basic.service.CityInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 城市信息
 * @author: Handy
 * @date:   2023/03/15 15:52
 */
@Service
public class CityInfoServiceImpl extends ServiceImpl<CityInfoMapper,CityInfo> implements CityInfoService {

    @Resource
    private ProvinceInfoMapper provinceInfoMapper;
    @Resource
    private CityInfoMapper cityInfoMapper;

	/**
     * 获取省份及关联城市列表
     * @param
     * @return List<ProvinceDTO>
     */
    @Cacheable(prefix = CacheKeys.PROVINCE_CITYS)
    @Override
    public List<ProvinceDTO> getProvinceCityList() {
        List<ProvinceInfo> provinceList = provinceInfoMapper.selectList(new QueryWrapper<>());
        List<CityInfo> cityInfoList = cityInfoMapper.selectList(new QueryWrapper<>());
        Map<String,List<CityDTO>> map = new HashMap<>();
        for (CityInfo city : cityInfoList) {
            List<CityDTO> cityList = map.get(city.getProvinceCode());
            if(null == cityList) {
                cityList = new ArrayList<>();
            }
            cityList.add(new CityDTO(city.getCityCode(),city.getCityName()));
            map.put(city.getProvinceCode(),cityList);
        }
        List<ProvinceDTO> list = new ArrayList<>();
        for (ProvinceInfo province : provinceList) {
            list.add(new ProvinceDTO(province.getProvinceCode(),province.getProvinceName(),map.get(province.getProvinceCode())));
        }
        return list;
    }

    @Override
    public List<ProvinceDTO> getProvinceList() {
        LambdaQueryWrapper<ProvinceInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProvinceInfo::getIsStore,1);
        List<ProvinceInfo> provinceList = provinceInfoMapper.selectList(wrapper);

        List<ProvinceDTO> result = new ArrayList<>();
        provinceList.forEach(provinceInfo -> {
            ProvinceDTO provinceDTO = new ProvinceDTO();
            provinceDTO.setCode(provinceInfo.getProvinceCode());
            provinceDTO.setName(provinceInfo.getProvinceName());
            result.add(provinceDTO);
        });
        return result;
    }

    @Override
    public List<CityDTO> getCityList(String provinceCode) {

        LambdaQueryWrapper<CityInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CityInfo::getProvinceCode,provinceCode);
        List<CityInfo> cityInfoList = cityInfoMapper.selectList(wrapper);

        List<CityDTO> result = new ArrayList<>();
        cityInfoList.forEach(cityInfo -> {
            CityDTO cityDTO = new CityDTO();
            cityDTO.setCode(cityInfo.getCityCode());
            cityDTO.setName(cityInfo.getCityName());
            result.add(cityDTO);
        });
        return result;
    }

    @Override
    public ProvinceCityDTO getInfoByCityCode(String cityCode) {
        ProvinceCityDTO provinceCityDTO = new ProvinceCityDTO();
        // 查询城市
        LambdaQueryWrapper<CityInfo> cityWrapper = new LambdaQueryWrapper<>();
        cityWrapper.eq(CityInfo::getCityCode,cityCode);
        CityInfo cityInfo = cityInfoMapper.selectOne(cityWrapper);
        if(cityInfo != null){
            provinceCityDTO.setCityCode(cityInfo.getCityCode());
            provinceCityDTO.setCityName(cityInfo.getCityName());
            // 查询省份
            LambdaQueryWrapper<ProvinceInfo> provinceWrapper = new LambdaQueryWrapper<>();
            provinceWrapper.eq(ProvinceInfo::getProvinceCode,cityInfo.getProvinceCode());
            ProvinceInfo provinceInfo = provinceInfoMapper.selectOne(provinceWrapper);
            if(provinceInfo != null){
                provinceCityDTO.setProvinceCode(provinceInfo.getProvinceCode());
                provinceCityDTO.setProvinceName(provinceInfo.getProvinceName());
            }
        }
        return provinceCityDTO;
    }

}