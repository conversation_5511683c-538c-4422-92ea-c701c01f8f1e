package com.dz.ms.order.service;


import com.dz.common.core.dto.order.OrderCreateParamDTO;
import com.dz.ms.order.entity.ExchangeOrder;
import com.dz.ms.order.entity.OrderDetail;

import java.util.List;

public interface OrderCreateService {

    /**
     * @Description:订单创建查询商品信息
     * <AUTHOR>
     * @date 2019年3月11日
     */
    List<OrderDetail> createProduct(OrderCreateParamDTO dto, ExchangeOrder order);
}
