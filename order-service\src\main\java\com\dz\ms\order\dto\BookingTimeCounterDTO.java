package com.dz.ms.order.dto;
import java.util.Date;
import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 预约时间段计数器DTO
 * @author: Handy
 * @date:   2024/06/06 14:47
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "预约时间段计数器")
public class BookingTimeCounterDTO extends BaseDTO {

    @ApiModelProperty(value = "ID")
    private Long id;
    @ApiModelProperty(value = "预约ID")
    private Long bookingId;
    @ApiModelProperty(value = "门店ID")
    private Long storeId;
    @ApiModelProperty(value = "预约日期数字(格式yyyyMMdd)")
    private Integer bookingDate;
    @ApiModelProperty(value = "预约时间段")
    private String timeSlot;
    @ApiModelProperty(value = "预约数量")
    private Integer bookingNumber;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "修改时间")
    private Date modified;
    @ApiModelProperty(value = "修改人")
    private Long modifier;

}
