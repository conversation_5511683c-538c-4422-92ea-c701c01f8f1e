package com.dz.common.core.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**

 */
@Setter
@Getter
@ToString
public class OdsOrderProductDTO implements Serializable {

    @ApiModelProperty(value = "主键id")
    private String id;
    @ApiModelProperty(value = "会员编号")
    private String memberCode;
    @ApiModelProperty(value = "店铺编号")
    private String shopIdStr;
    @ApiModelProperty(value = "店铺名称")
    private String shopNameStr;
    @ApiModelProperty(value = "订单编号")
    private String saleIdStr;
    @ApiModelProperty(value = "销售类型")
    private String saleTyStr;
    @ApiModelProperty(value = "交易日期")
    private String checkoutDtStr;
    @ApiModelProperty(value = "POS编号")
    private String posIdStr;
    @ApiModelProperty(value = "收银员编号")
    private String staffIdStr;
    @ApiModelProperty(value = "原销售编号")
    private String saleidFrStr;
    @ApiModelProperty(value = "总数量")
    private String totalQtyStr;
    @ApiModelProperty(value = "实收金额")
    private String totalAmtStr;
    @ApiModelProperty(value = "商品编号")
    private String itemIdStr;
    @ApiModelProperty(value = "商品名称")
    private String itemNameStr;
    @ApiModelProperty(value = "商品数量")
    private String qtyStr;
    @ApiModelProperty(value = "对应商品行")
    private String upSerialNoStr;
    @ApiModelProperty(value = "商品金额")
    private String saleAmtStr;
    @ApiModelProperty(value = "实收金额（可能重复，需确认是否两个字段意义不同）")
    private String realAmtStr;
    @ApiModelProperty(value = "自选套餐")
    private String promname1Str;
    @ApiModelProperty(value = "几件几折")
    private String promname2Str;
    @ApiModelProperty(value = "通用套餐")
    private String promname5Str;
    @ApiModelProperty(value = "满减满赠")
    private String promdisnameStr;
    @ApiModelProperty(value = "用卷")
    private String couponnameStr;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "分区日期")
    private String bizDate;
    @ApiModelProperty(value = "租户id")
    private Long tenantId = 1L;
    private String deptIdStr;
    private String depaIdStr;
    private String lineIdStr;
    private String classIdStr;

}
