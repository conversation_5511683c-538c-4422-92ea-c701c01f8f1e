package com.dz.ms.sales.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;


@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
@Table("用户试衣照记录表")
@TableName(value = "fitting")
public class Fitting implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "用户id", isIndex = true)
    private Long userId;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "模板id")
    private Integer templateId;
    @Columns(type = ColumnType.VARCHAR, length = 500, isNull = true, comment = "图片地址")
    private String imgUrl;
    @Columns(type = ColumnType.VARCHAR, length = 500, isNull = true, comment = "图片文字")
    private String imgText;
    
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "是否删除 0未删除 1已删除")
    @TableLogic
    private Integer isDeleted;
    
}
