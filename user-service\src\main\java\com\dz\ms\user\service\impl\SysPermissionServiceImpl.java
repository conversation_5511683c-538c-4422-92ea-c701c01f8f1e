package com.dz.ms.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.core.annotation.Cacheable;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.user.constants.CacheKeys;
import com.dz.common.core.dto.user.SysPermissionDTO;
import com.dz.ms.user.entity.SysPermission;
import com.dz.ms.user.mapper.SysPermissionMapper;
import com.dz.ms.user.service.SysPermissionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 权限功能
 * @author: Handy
 * @date:   2022/2/4 23:04
 */
@Service
public class SysPermissionServiceImpl extends ServiceImpl<SysPermissionMapper,SysPermission> implements SysPermissionService {

    @Resource
    private SysPermissionMapper sysPermissionMapper;
    @Resource
    private RedisService redisService;

    /**
     * 保存权限功能
     * @param param
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveSysPermission(SysPermissionDTO param) {
        SysPermission getPermission = null;
        if(null != param.getId()) {
            getPermission = sysPermissionMapper.selectById(param.getId());
        }
        Integer platform = SecurityContext.getUser().getPlatform();
        SysPermission sysPermission = BeanCopierUtils.convertObject(param,SysPermission.class);
        sysPermission.setPlatform(platform);
        if(ParamUtils.isNullOr0Long(sysPermission.getId())) {
            sysPermissionMapper.insert(sysPermission);
        }
        else {
            sysPermissionMapper.updateById(sysPermission);
        }
        long parentId = sysPermission.getParentId();
        if(parentId > 0) {
            sysPermission = new SysPermission();
            sysPermission.setId(parentId);
            sysPermission.setHasChild(1);
            sysPermissionMapper.updateById(sysPermission);
        }
        if(null != param.getId() && getPermission != null && getPermission.getParentId() != null && !getPermission.getParentId().equals(parentId)) {
            resetParentPermission(getPermission);
        }
        redisService.del(CacheKeys.SYS_ALL_PERMIT_CODES+":"+platform);
        redisService.del(CacheKeys.SYS_ALL_FUNCTION_CODES+":"+platform);
        redisService.delAll(CacheKeys.SYS_ROLE_MENU_TREE);
        redisService.delAll(CacheKeys.SYS_ROLE_FUNCTIONS);
        return sysPermission.getId();
    }

    /**
     * 根据ID删除权限功能
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSysPermissionById(Long id) {
        SysPermission getPermission = sysPermissionMapper.selectById(id);
        if(getPermission.getHasChild().equals(1)) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT,"该权限下有包含子权限请先删除或编辑子权限！");
        }
        sysPermissionMapper.deleteById(id);
        if(!getPermission.getParentId().equals(0L)) {
            resetParentPermission(getPermission);
        }
        redisService.del(CacheKeys.SYS_ALL_PERMIT_CODES+":"+getPermission.getPlatform());
        redisService.del(CacheKeys.SYS_ALL_FUNCTION_CODES+":"+getPermission.getPlatform());
    }

    /**
     * 将无子权限的hasChild设为0
     * @param getPermission
     */
    private void resetParentPermission(SysPermission getPermission) {
        LambdaQueryWrapper<SysPermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysPermission :: getParentId,getPermission.getParentId());
        List<SysPermission> list = sysPermissionMapper.selectList(wrapper);
        if(CollectionUtils.isEmpty(list)) {
            SysPermission sysPermission = new SysPermission();
            sysPermission.setId(getPermission.getParentId());
            sysPermission.setHasChild(0);
            sysPermissionMapper.updateById(sysPermission);
        }
    }

    /**
     * 获取所有权限编码
     * @return
     */
    @Override
    @Cacheable(prefix = CacheKeys.SYS_ALL_PERMIT_CODES,key = "'#platform'")
    public List<String> getAllPermissionCodes(Integer platform) {
        List<SysPermission> list = sysPermissionMapper.selectList(new LambdaQueryWrapper<SysPermission>().eq(SysPermission::getPlatform,platform));
        return list.stream().map(SysPermission::getCode).collect(Collectors.toList());
    }

    /**
     * 获取所有功能权限编码
     * @return
     */
    @Override
    @Cacheable(prefix = CacheKeys.SYS_ALL_FUNCTION_CODES,key = "'#platform'")
    public List<String> getFunctionPermissionCodes(Integer platform) {
        List<SysPermission> list = sysPermissionMapper.selectList(new LambdaQueryWrapper<SysPermission>().eq(SysPermission::getPlatform,platform).eq(SysPermission::getPermitType,3));
        return list.stream().map(SysPermission::getCode).collect(Collectors.toList());
    }

    /**
     * 根据权限编号查询权限
     * @param code
     * @return
     */
    @Override
    public SysPermission getPermissionByCode(String code) {
        LambdaQueryWrapper<SysPermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysPermission :: getCode,code);
        return sysPermissionMapper.selectOne(wrapper);
    }

    /**
     * 获取权限树形结构
     * @return
     */
    @Override
    public List<SysPermissionDTO> getSysPermissionTree() {
        List<SysPermissionDTO> res = new ArrayList<>();
        LambdaQueryWrapper<SysPermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysPermission::getPlatform, SecurityContext.getUser().getPlatform());
        List<SysPermission> list = sysPermissionMapper.selectList(wrapper);
        if(null == list) {
            return res;
        }
        List<SysPermissionDTO> dtoList = BeanCopierUtils.convertList(list,SysPermissionDTO.class);
        for (SysPermissionDTO permission : dtoList) {
            if(permission.getParentId().equals(0L)) {
                handleSubList(permission,dtoList);
                res.add(permission);
            }
        }
        return res;
    }

    /**
     * 获取权限树形结构
     * @return
     */
    @Override
    public List<SysPermissionDTO> getSysMenuTree(Set<Long> ids,Integer platform) {
        List<SysPermissionDTO> res = new ArrayList<>();
        LambdaQueryWrapper<SysPermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.lt(SysPermission::getPermitType,3);
        wrapper.eq(SysPermission::getPlatform, platform);
        wrapper.in(!CollectionUtils.isEmpty(ids),SysPermission::getId,ids);
        List<SysPermission> list = sysPermissionMapper.selectList(wrapper);
        if(null == list) {
            return res;
        }
        List<SysPermissionDTO> dtoList = BeanCopierUtils.convertList(list,SysPermissionDTO.class);
        for (SysPermissionDTO permission : dtoList) {
            if(permission.getParentId().equals(0L)) {
                handleSubList(permission,dtoList);
                res.add(permission);
            }
        }
        return res;
    }

    /**
     * 子权限递归处理
     * @param permission
     * @param list
     */
    private void handleSubList(SysPermissionDTO permission,List<SysPermissionDTO> list) {
        if(permission.getHasChild().equals(1)) {
            List<SysPermissionDTO> subList = new ArrayList<>();
            for (SysPermissionDTO item : list) {
                if (item.getParentId().equals(permission.getId())) {
                    handleSubList(item, list);
                    subList.add(BeanCopierUtils.convertObject(item,SysPermissionDTO.class));
                }
            }
            permission.setSubList(subList);
        }
    }
    
}
