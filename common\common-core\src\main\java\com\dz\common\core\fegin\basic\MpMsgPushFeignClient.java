package com.dz.common.core.fegin.basic;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ServiceConstant.BASIC_SERVICE_NAME, contextId = "MpMsgPushFeignClient")
public interface MpMsgPushFeignClient {

    /**
     * 活动开始提醒消息发送
     *
     * @param
     * @return
     */
    @PostMapping(value = "/mp_msg_push/activity_start")
    public Result<Boolean> activityStartMsgPush(@RequestParam("tenantId") Long tenantId, @RequestParam("content") String[] content,
                                                @RequestParam("path") String path);

    /**
     * 第二轮报名开始提醒消息发送
     *
     * @param
     * @return
     */
    @PostMapping(value = "/mp_msg_push/activity_start_campaign")
    public Result<Boolean> activityStartCampaignMsgPush(@RequestParam("tenantId") Long tenantId, @RequestParam("content") String[] content,
                                                        @RequestParam("path") String path, @RequestParam(value = "uids", required = false) List<Long> uids);

    /**
     * 第二轮报名开始提醒消息发送
     *
     * @param
     * @return
     */
    @PostMapping(value = "/mp_msg_push/enroll_start")
    public Result<Boolean> activityEnrollMsgPush(@RequestParam("tenantId") Long tenantId, @RequestParam("content") String[] content,
                                                 @RequestParam("path") String path, @RequestParam(value = "uids", required = false) List<Long> uids);

}
