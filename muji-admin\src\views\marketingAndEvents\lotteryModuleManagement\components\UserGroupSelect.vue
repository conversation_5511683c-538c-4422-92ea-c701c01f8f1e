<template>
  <div class="m-b-12">
    <a-button type="link" size="small" @click="handleAddUserGroup" :disabled="disabled">
      + {{ addText }}
    </a-button>
    <span class="global-tip"> <slot name="tip" /> </span>
  </div>
  <a-form-item v-for="item in formModel" :key="item._id" class="m-b-12">
    <div class="flex-box flex-center">
      <a-select
        v-model:value="item.crowdId"
        class="input-width-small"
        :options="userGroupOptions"
        show-search
        :filterOption="crowdFilterOption"
        placeholder="请选择"
      />
      <a-input-number v-model:value="item.initNum" :min="0" class="row-space" />
      <a-button type="link" size="small" @click="handleDeleteUserGroup(item._id)">
        - 删除
      </a-button>
    </div>
  </a-form-item>
</template>

<script setup>
import { computed } from 'vue'

const formModel = defineModel('value', { required: true })

const props = defineProps({
  crowdAllList: { type: Array, default: () => [] },
  addText: { type: String, required: true }
})
const disabled = computed(() => {
  return formModel.value.length ? formModel.value.length === props.crowdAllList.length : false
})

// 人群包选项 (过滤掉已添加的)
const userGroupOptions = computed(() => {
  const ids = formModel.value.map((item) => item.crowdId)
  return props.crowdAllList.map((item) => {
    return { ...item, disabled: ids.includes(item.value) }
  })
})
// 删除
function handleDeleteUserGroup(id) {
  formModel.value = formModel.value.filter((item) => item._id !== id)
}
// 添加
function handleAddUserGroup() {
  formModel.value.push({ _id: Date.now(), crowdId: '', initNum: 0 })
}
// 选择人群包单选选择框通用筛选
function crowdFilterOption(input, option) {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}
</script>
