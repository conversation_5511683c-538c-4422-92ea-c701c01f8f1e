package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统日志
 * @author: Handy
 * @date:   2022/08/04 10:05
 */
@Getter
@Setter
@NoArgsConstructor
@Table("系统日志")
@TableName(value = "system_log")
public class SystemLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = false,comment = "日志类型 1操作日志 2脚本同步 3接口调用")
    private Integer logType;
    @Columns(type = ColumnType.VARCHAR,length = 64,isNull = false,comment = "日志名称")
    private String logName;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "接口地址")
    private String requestUrl;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = true,comment = "执行开始时间")
    private Date startTime;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = true,comment = "执行结束时间")
    private Date endTime;
    @Columns(type = ColumnType.VARCHAR,length = 1000,isNull = true,comment = "方法入参")
    private String params;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = true,comment = "执行结果 0异常 1正常")
    private Integer state;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = true,comment = "告警状态 0未告警 1已告警 2已处理")
    private Integer alarmState;
    @Columns(type = ColumnType.VARCHAR,length = 500,isNull = true,comment = "异常信息")
    private String exception;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = true,comment = "操作人")
    private Long operator;
    @Columns(type = ColumnType.VARCHAR,length = 30,isNull = true,comment = "操作人账号")
    private String operatorName;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = true,comment = "租户ID")
    private Long tenantId;

    public SystemLog(Long id, Integer logType, String logName, String requestUrl, Date startTime, Date endTime, String params, Integer state, Integer alarmState, String exception, Long operator, String operatorName, Long tenantId) {
        this.id = id;
        this.logType = logType;
        this.logName = logName;
        this.requestUrl = requestUrl;
        this.startTime = startTime;
        this.endTime = endTime;
        this.params = params;
        this.state = state;
        this.alarmState = alarmState;
        this.exception = exception;
        this.operator = operator;
        this.operatorName = operatorName;
        this.tenantId = tenantId;
    }
}
