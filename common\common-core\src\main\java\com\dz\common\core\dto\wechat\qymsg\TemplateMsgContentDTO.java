package com.dz.common.core.dto.wechat.qymsg;

import lombok.Getter;
import lombok.Setter;

/**
 * 核心消息内容
 * @Author: Handy
 * @Date: 2022/7/29 0:28
 */
@Getter
@Setter
public class TemplateMsgContentDTO {

    /** 二级标题，建议不超过5个字 */
    private String keyname;
    /** 二级文本，如果horizontal_content_list.type是2，该字段代表文件名称（要包含文件类型），建议不超过30个字，（支持id转译） */
    private String value;
    /** 否	链接类型，0或不填代表不是链接，1 代表跳转url，2 代表下载附件，3 代表点击跳转成员详情 */
    private Integer type;
    /** 否	链接跳转的url，horizontal_content_list.type是1时必填 */
    private String url;
    /** 否	附件的media_id，horizontal_content_list.type是2时必填 */
    private String media_id;
    /** 否	成员详情的userid，horizontal_content_list.type是3时必填 */
    private String userid;

    public TemplateMsgContentDTO(String keyname, String value) {
        this.keyname = keyname;
        this.value = value;
    }

}
