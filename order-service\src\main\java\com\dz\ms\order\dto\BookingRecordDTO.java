package com.dz.ms.order.dto;
import java.util.Date;
import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 预约记录DTO
 * @author: Handy
 * @date:   2024/06/06 14:47
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "预约记录")
public class BookingRecordDTO extends BaseDTO {

    @ApiModelProperty(value = "ID")
    private Long id;
    @ApiModelProperty(value = "预约记录编码")
    private String recordCode;
    @ApiModelProperty(value = "预约客户ID")
    private Long uid;
    @ApiModelProperty(value = "预约客户姓名")
    private String userName;
    @ApiModelProperty(value = "预约客户性别(0未知 1男 2女)")
    private Integer gender;
    @ApiModelProperty(value = "预约客户手机号")
    private String mobile;
    @ApiModelProperty(value = "预约客户等级")
    private String cardLevel;
    @ApiModelProperty(value = "预约ID")
    private Long bookingId;
    @ApiModelProperty(value = "预约名称")
    private String bookingName;
    @ApiModelProperty(value = "预约活动主图")
    private String bookingImg;
    @ApiModelProperty(value = "预约日期(格式yyyy-MM-dd)")
    private String bookingDate;
    @ApiModelProperty(value = "预约时间段")
    private String timeSlot;
    @ApiModelProperty(value = "预约时间段开始时间")
    private Date bookingTimeStart;
    @ApiModelProperty(value = "预约时间段结束时间")
    private Date bookingTimeEnd;
    @ApiModelProperty(value = "核销有效期开始时间")
    private Date verifyTimeStart;
    @ApiModelProperty(value = "核销有效期结束时间")
    private Date verifyTimeEnd;
    @ApiModelProperty(value = "预约项目类型 2服务 3卡券")
    private Integer itemType;
    @ApiModelProperty(value = "预约客户ID")
    private Long itemId;
    @ApiModelProperty(value = "预约项目名称")
    private String itemName;
    @ApiModelProperty(value = "预约项目描述")
    private String itemIntroduction;
    @ApiModelProperty(value = "预约门店编码")
    private String storeCode;
    @ApiModelProperty(value = "预约门店名称")
    private String storeName;
    @ApiModelProperty(value = "预约/分配员工编号")
    private String empCode;
    @ApiModelProperty(value = "核销门店编码")
    private String verifyStoreCode;
    @ApiModelProperty(value = "核销门店名称")
    private String verifyStoreName;
    @ApiModelProperty(value = "核销人类型 1后台用户 2导购")
    private Integer verifierType;
    @ApiModelProperty(value = "核销员工编号")
    private String verifierCode;
    @ApiModelProperty(value = "核销员工姓名")
    private String verifierName;
    @ApiModelProperty(value = "预约状态 1、待核销,2、已核销,3、已过期,4、已取消")
    private Integer state;
    @ApiModelProperty(value = "消息发送状态 0预约成功 1预约修改 2预约开始前 3预约取消 4预约过期")
    private Integer msgState;
    @ApiModelProperty(value = "状态变更时间")
    private Date stateTime;
    @ApiModelProperty(value = "已修改次数")
    private Integer updateNum;
    @ApiModelProperty(value = "是否代客预约 0否  1是")
    private Integer empBooking;
    @ApiModelProperty(value = "预约消耗类型 0无消耗 1积分 2卡券 3权益 4pin_code 5付费")
    private Integer consumeType;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "修改时间")
    private Date modified;
    @ApiModelProperty(value = "修改人")
    private Long modifier;

}
