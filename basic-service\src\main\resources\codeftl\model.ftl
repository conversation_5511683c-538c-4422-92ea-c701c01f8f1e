package com.dz.ms.${typeName}.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.io.Serializable;
<#if hasDate>
import java.util.Date;
</#if>
<#if hasDecimal>
import java.math.BigDecimal;
</#if>

/**
 * ${tableComment}
 * @author: ${author}
 * @date:   ${nowDate}
 */
@Getter
@Setter
@NoArgsConstructor
@Table("${tableComment}")
@TableName(value = "${tableName}")
public class ${objectName} implements Serializable {

    private static final long serialVersionUID = 1L;

    <#list fieldList as var>
    @Columns(type = ColumnType.${var.orgDataType ? upper_case},length = ${var.length},isNull = ${var.isNull ? string("true","false")},comment = "${var.columnComment}")
    <#if hasPrimark && var.columName == primarkName>
    @TableId(type = IdType.AUTO)
    </#if>
    <#if var.columName == 'created' || var.columName == 'creator'>
    @TableField(fill = FieldFill.INSERT)
    </#if>
    <#if var.columName == 'modified' || var.columName == 'modifier'>
    @TableField(fill = FieldFill.INSERT_UPDATE)
    </#if>
    <#if var.columName == 'is_deleted'>
    @TableLogic
    </#if>
    private ${var.modelDataType} ${var.upperCaseColum};
	</#list>
    <#if hasCreate>

    public ${objectName}(<#list pureList as var>${var.modelDataType} ${var.upperCaseColum}<#if var_index < pureList?size-1>, </#if></#list>) {
        <#list pureList as var>
        this.${var.upperCaseColum} = ${var.upperCaseColum};
        </#list>
    }
    </#if>

}
