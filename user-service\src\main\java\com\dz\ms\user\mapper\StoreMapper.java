package com.dz.ms.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.user.entity.Store;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 门店Mapper
 * @author: yibo
 * @date: 2024/11/19 17:19
 */
@Repository
public interface StoreMapper extends BaseMapper<Store> {

    List<Store> getLongitudeLatitude();

    void updateBatchById(List<Store> storesToUpdate);

    void insertBatchSomeColumn(List<Store> storeToSave);

    List<String> selectStoreProvince();

    List<String> selectProvince();

    List<String> selectStoreCity();

    List<String> selectCity();

    Store selectOneNew(String storeSn);
}
