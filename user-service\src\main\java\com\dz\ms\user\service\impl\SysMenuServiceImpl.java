package com.dz.ms.user.service.impl;

import com.dz.common.base.constant.UserConstants;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.dto.user.SysUserDTO;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.ms.user.dto.SysMenuDTO;
import com.dz.ms.user.entity.SysMenu;
import com.dz.ms.user.mapper.SysMenuMapper;
import com.dz.ms.user.service.ISysMenuService;
import com.dz.ms.user.service.SysUserService;
import com.dz.ms.user.vo.MenuInfoVo;
import com.dz.ms.user.vo.MetaVo;
import com.dz.ms.user.vo.RouterVo;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 菜单 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysMenuServiceImpl implements ISysMenuService {

    @Resource
    private SysUserService sysUserService;
    
    @Resource
    private SysMenuMapper menuMapper;


    /**
     * 根据用户ID查询菜单
     *
     * @return 菜单列表
     */
    @Override
    public List<SysMenuDTO> selectMenuTreeByUserId() {
        CurrentUserDTO currentUser = SecurityContext.getUser();
        Long tenantId = currentUser.getTenantId();
        SysUserDTO sysUser = sysUserService.getLoginUserContainRootId();
        List<SysMenu> menus;
        if (sysUser.getIsAdmin().equals(1)) {
            menus = menuMapper.selectMenuTreeAll(tenantId);
        } else {
            menus = menuMapper.selectMenuTreeByRoleId(sysUser.getRoleId(),tenantId);
        }
        if(CollectionUtils.isEmpty(menus)){
            menus = new ArrayList<>();
        }
        return getChildPerms(BeanCopierUtils.convertList(menus,SysMenuDTO.class), 0);
    }

    /**
     * 构建前端路由所需要的菜单
     *
     * @param menus 菜单列表
     * @return 路由列表
     */
    @Override
    public List<RouterVo> buildMenus(List<SysMenuDTO> menus) {
        List<RouterVo> routers = new LinkedList<>();
        for (SysMenuDTO menu : menus) {
            RouterVo router = new RouterVo();
            router.setHidden("1".equals(menu.getVisible()));
            router.setName(getRouteName(menu));
            router.setPath(getRouterPath(menu));
            router.setComponent(getComponent(menu));
            router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon()));
            router.setOrderNum(menu.getOrderNum());
            router.setId(menu.getMenuId());
            router.setParentId(menu.getParentId());
            List<SysMenuDTO> cMenus = menu.getChildren();
            if (!CollectionUtils.isEmpty(cMenus) && UserConstants.TYPE_DIR.equals(menu.getMenuType())) {
                router.setAlwaysShow(true);
                router.setRedirect("noRedirect");
                router.setChildren(buildMenus(cMenus));
            } else if (isMeunFrame(menu)) {
                List<RouterVo> childrenList = new ArrayList<>();
                RouterVo children = new RouterVo();
                children.setPath(menu.getPath());
                children.setComponent(menu.getComponent());
                children.setName(StringUtils.capitalize(menu.getPath()));
                children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon()));
                children.setOrderNum(menu.getOrderNum());
                childrenList.add(children);
                router.setChildren(this.sortByOrderNum(
                        childrenList
                ));
            }
            routers.add(router);
        }
        return this.sortByOrderNum(routers);
    }

    private List<RouterVo> sortByOrderNum(List<RouterVo> routerVoList) {
        return routerVoList.stream()
                .sorted(Comparator.comparingInt(RouterVo::getOrderNum))
                .collect(Collectors.toList());
    }


    /**
     * 获取路由名称
     *
     * @param menu 菜单信息
     * @return 路由名称
     */
    public String getRouteName(SysMenuDTO menu) {
        String routerName = StringUtils.capitalize(menu.getPath());
        // 非外链并且是一级目录（类型为目录）
        if (isMeunFrame(menu)) {
            routerName = StringUtils.EMPTY;
        }
        return routerName;
    }

    /**
     * 获取路由地址
     *
     * @param menu 菜单信息
     * @return 路由地址
     */
    public String getRouterPath(SysMenuDTO menu) {
        String routerPath = menu.getPath();
        // 非外链并且是一级目录（类型为目录）
        if (0 == menu.getParentId().intValue() && UserConstants.TYPE_DIR.equals(
                menu.getMenuType()) && UserConstants.NO_FRAME.equals(menu.getIsFrame().toString())) {
            routerPath = "/" + menu.getPath();
        }
        // 非外链并且是一级目录（类型为菜单）
        else if (isMeunFrame(menu)) {
            routerPath = "/";
        }
        return routerPath;
    }

    /**
     * 获取组件信息
     *
     * @param menu 菜单信息
     * @return 组件信息
     */
    public String getComponent(SysMenuDTO menu) {
        String component = UserConstants.LAYOUT;
        if (StringUtils.isNotEmpty(menu.getComponent()) && !isMeunFrame(menu)) {
            component = menu.getComponent();
        }
        return component;
    }

    /**
     * 是否为菜单内部跳转
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isMeunFrame(SysMenuDTO menu) {
        return menu.getParentId().intValue() == 0 && UserConstants.TYPE_MENU.equals(
                menu.getMenuType()) && UserConstants.NO_FRAME.equals(menu.getIsFrame().toString());
    }

    /**
     * 根据父节点的ID获取所有子节点
     *
     * @param list     分类表
     * @param parentId 传入的父节点ID
     * @return List<SysMenu>
     */
    public List<SysMenuDTO> getChildPerms(List<SysMenuDTO> list, int parentId) {
        List<SysMenuDTO> returnList = new ArrayList<>();
        for (Iterator<SysMenuDTO> iterator = list.iterator(); iterator.hasNext(); ) {
            SysMenuDTO t = iterator.next();
            // 一、根据传入的某个父节点ID,遍历该父节点的所有子节点
            if (t.getParentId() == parentId) {
                recursionFn(list, t);
                returnList.add(t);
            }
        }
        return returnList;
    }

    /**
     * 递归列表
     *
     * @param list
     * @param t
     */
    private void recursionFn(List<SysMenuDTO> list, SysMenuDTO t) {
        // 得到子节点列表
        List<SysMenuDTO> childList = getChildList(list, t);
        t.setChildren(childList);
        for (SysMenuDTO tChild : childList) {
            if (hasChild(list, tChild)) {
                // 判断是否有子节点
                Iterator<SysMenuDTO> it = childList.iterator();
                while (it.hasNext()) {
                    SysMenuDTO n = it.next();
                    recursionFn(list, n);
                }
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<SysMenuDTO> getChildList(List<SysMenuDTO> list, SysMenuDTO t) {
        List<SysMenuDTO> tlist = new ArrayList<>();
        Iterator<SysMenuDTO> it = list.iterator();
        while (it.hasNext()) {
            SysMenuDTO n = it.next();
            if (n.getParentId().longValue() == t.getMenuId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<SysMenuDTO> list, SysMenuDTO t) {
        return getChildList(list, t).size() > 0;
    }

    /**
     * 根据菜单名称获取菜单及祖级信息
     * @param menuName 名称
     * @param menuType 类型
     * @return List<MenuInfoVo>
     */
    @Override
    public List<MenuInfoVo> getInfoByMenuName(String menuName, String menuType) {
        List<MenuInfoVo> list = menuMapper.getInfoByMenuName(menuName,menuType);
        for (MenuInfoVo menuInfoVo : list) {
            Long parentId = menuInfoVo.getParentId();
            List<String> parentPathList = Lists.newArrayList();
            menuInfoVo.setParentPath(String.join(",",selectByParentId(parentId, parentPathList)));
        }

        return list;
    }

    /**
     * 获取父级PATH
     * @param menuId menuId
     * @param parentPathList parentPath
     * @return List<String>
     */
    @Override
    public List<String> selectByParentId(Long menuId,List<String> parentPathList) {
        SysMenu sysMenu = menuMapper.selectByParentId(menuId);
        if(sysMenu != null && sysMenu.getParentId() != null) {
            parentPathList.add(sysMenu.getPath());
            selectByParentId(sysMenu.getParentId(),parentPathList);
        }

        return parentPathList;
    }
}
