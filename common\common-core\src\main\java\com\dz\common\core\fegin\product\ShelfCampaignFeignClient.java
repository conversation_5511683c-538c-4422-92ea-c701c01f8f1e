package com.dz.common.core.fegin.product;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.DownloadAddParamDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(name = ServiceConstant.PRODUCT_SERVICE_NAME, contextId = "ShelfCampaignFeignClient")
public interface ShelfCampaignFeignClient {

    /**
     * 导出货架营销活动
     *
     * @return
     */
    @PostMapping(value = "/shelf_campaign/export_list")
    Result<Void> exportList(@RequestBody DownloadAddParamDTO exportParam);


}

