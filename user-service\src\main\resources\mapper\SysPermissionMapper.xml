<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.user.mapper.SysPermissionMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    id,
  	    code,
  	    parent_id,
  	    permit_type,
  	    permit_name,
  	    url,
  	    permit_desc,
  	    display_sort,
  	    has_child,
  	    created,
  	    creator,
  	    modified,
  	    modifier
    </sql>

	<!-- 获取角色权限列表 -->
	<select id="getRolePermitCodes" resultType="com.dz.ms.user.entity.SysPermission">
        select
		p.id,p.code,p.parent_id
        from sys_permission p
		join sys_role_permission rp
		on rp.permit_id = p.id
		where rp.role_id = #{roleId}
		and p.platform = #{platform}
        <if test="permitType != null">
			and p.permit_type = #{permitType}
		</if>
    </select>

	<!-- 获取角色权限列表 -->
	<select id="getRolePermitIds" resultType="java.lang.Long">
        select
		p.id
        from sys_role_permission rp
        join sys_permission p
        on rp.permit_id = p.id
        where rp.role_id = #{roleId}
        and p.has_child = 0
        and p.platform = #{platform}
    </select>

</mapper>
