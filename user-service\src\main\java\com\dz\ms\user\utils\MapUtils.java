package com.dz.ms.user.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

/**
 * 计算两个经纬度之间的距离
 * <AUTHOR>
 * @date 2019/02/22 15:24
 */
public class MapUtils {
	// 地球半径取平均值：6371.393千米
	private static double EARTH_RADIUS = 6371.393;

	// 将角度换算为弧度
	private static double rad(double d) {
		return d * Math.PI / 180.0;
	}

	/**
	 * 计算两个经纬度之间的距离 longitude 经度 latitude 纬度
	 */
	public static Double GetDistance(String longitudeX, String latitudeY, String longitude2X, String latitude2Y) {
		if (StringUtils.isBlank(longitudeX) || StringUtils.isBlank(latitudeY) || StringUtils.isBlank(longitude2X) || StringUtils.isBlank(latitude2Y)) {
			return null;
		}
		try {
			double lon1 = Double.parseDouble(longitudeX);
			double lat1 = Double.parseDouble(latitudeY);
			double lon2 = Double.parseDouble(longitude2X);
			double lat2 = Double.parseDouble(latitude2Y);

			double lambda1 = lon1 * Math.PI / 180;
			double phi1 = lat1 * Math.PI / 180;
			double lambda2 = lon2 * Math.PI / 180;
			double phi2 = lat2 * Math.PI / 180;

			// Haversine公式
			double a = Math.pow(Math.sin((phi2 - phi1) / 2), 2)
					+ Math.cos(phi1) * Math.cos(phi2) * Math.pow(Math.sin((lambda2 - lambda1) / 2), 2);
			double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
			double distance = EARTH_RADIUS * c * 1000;
			return distance;
		} catch (NumberFormatException e) {
			// 处理转换异常
			return null;
		}
	}

	/**
	 * Map根据value排序
	 */
	public static <K, V extends Comparable<? super V>> Map<K, V> sortByValue(Map<K, V> map) {
		List<Entry<K, V>> list = new LinkedList<>(map.entrySet());
		Collections.sort(list, new Comparator<Entry<K, V>>() {
			@Override
			public int compare(Entry<K, V> o1, Entry<K, V> o2) {
				return (o1.getValue()).compareTo(o2.getValue());
			}
		});

		Map<K, V> result = new LinkedHashMap<>();
		for (Entry<K, V> entry : list) {
			result.put(entry.getKey(), entry.getValue());
		}
		return result;
	}

	/**
	 * 获取map中第一个key值
	 */
	public static Long getFirstOrNull(Map<Long, Double> map) {
		Long obj = null;
		for (Entry<Long, Double> entry : map.entrySet()) {
			obj = entry.getKey();
			if (obj != null) {
				break;
			}
		}
		return obj;
	}

	public static void main(String[] args) {

	}
}
