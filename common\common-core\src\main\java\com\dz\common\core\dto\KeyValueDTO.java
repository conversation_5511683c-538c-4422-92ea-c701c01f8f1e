package com.dz.common.core.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * key value 通用DTO
 * @author: Handy
 * @date:   2022/2/3 16:33
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
@ApiModel(value = "key value 通用DTO")
public class KeyValueDTO {

    @ApiModelProperty(value = "ID")
    private Long id;
    @ApiModelProperty(value = "code")
    private String code;
    @ApiModelProperty(value = "name")
    private String name;
    @ApiModelProperty(value = "value")
    private String value;
    @ApiModelProperty(value = "object")
    private Object object;

    public KeyValueDTO(Long id, String code, String name, String value) {
        this.id = id;
        this.code = code;
        this.name = name;
        this.value = value;
    }

}
