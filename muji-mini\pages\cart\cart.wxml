<my-page loading="{{loading}}" overallModal="{{overallModal}}" catch:overallModalConfirm="overallModalConfirm">
  <view class="page-container">
    <custom-header type="{{1}}" isBackHidden="{{ isBackHidden }}">
      <view slot="content" class="page-title">
        购物车
      </view>
    </custom-header>

    <scroll-view class="cart-page" enhanced="{{ true }}" bounces="{{ false }}" show-scrollbar="{{false}}"
      scroll-top="{{ scrollTop }}" wx:if="{{ !isLoading }}"
      style="height: calc(100vh - {{  menuButtonBottom + 20 }}px - {{ isIPX ? '140rpx' : '120rpx' }})" scroll-y>
      <view class="cart-container">
        <my-credits myCredits="{{currentPoint}}" />

        <view style="margin-top: {{ menuButtonBottom }}px">
          <block wx:if="{{cart.validCarts && cart.validCarts.length>0}}">
            <view class="select-box select-all " bind:tap="onTapCheckAll">
              <view class="select-btn {{cart.checkAll && 'active iconfont icon-<PERSON>'}}" />
              全选
            </view>
            <view wx:for="{{cart.validCarts}}" wx:key="id" class="card-wrapper">
              <van-swipe-cell data-item="{{item}}" async-close bind:close="onCloseSwipeCell" right-width="{{ 90 }}">
                <!--<van-cell-group>-->
                <view class="select-box">
                  <view class="select-region" bind:tap="onTapCheck" data-item="{{item}}" />
                  <view class="select-btn select-item {{item.checked && 'active iconfont icon-Mark'}}"
                    bind:tap="onTapCheck" data-item="{{item}}"></view>
                  <cart-card bind:confirm="onCartConfirm" bind:tap-detail="onTapCartDetail"
                    bind:tap-cart-del="onTapCartDel" bind:tap-cart-add="onTapCartAdd" skuInfo="{{item}}" />
                </view>
                <!--</van-cell-group>-->
                <view slot="right" class="van-swipe-cell__right">删除
                </view>
              </van-swipe-cell>
            </view>
          </block>

          <block wx:if="{{cart.invalidCarts && cart.invalidCarts.length>0}}">
            <view class="expire-box">
              <view class="expire-btn">
                <view class="left">失效商品</view>
                <view bindtap="onTapCartClear" class="right">清空全部</view>
              </view>
              <view class="card-wrapper" wx:for="{{cart.invalidCarts}}" wx:key="id">
                <view class="select-box expire">
                  <view class="select-btn select-item disabled"></view>
                  <cart-card type="expire" skuInfo="{{item}}" bind:confirm="onCartConfirm"
                    bind:tap-cart-del="onTapCartDel" bind:tap-cart-add="onTapCartAdd" />
                  <!--bind:tap-detail="onTapCartDetail"-->
                </view>
              </view>
            </view>
          </block>
          <no-data-available top="{{536}}" wx:if="{{!cart.validCarts || !cart.validCarts.length}}" data="{{cart.carts}}"
            text="暂无添加兑礼商品" />
        </view>
      </view>

    </scroll-view>
    <view class="bottom-box">
      <view class="total-box">
        <view class="total-num">
          已选择{{cart.checkNum}}件，合计
        </view>
        <view class="total-price">
          {{cart.totalPoint}}积分<block wx:if="{{cart.totalPrice}}">+{{cart.totalPrice}}元</block>
        </view>
      </view>
      <!--disabled="{{!cart.carts || !cart.carts.length}}"-->
      <basic-button width="{{271}}"
        disabled="{{(!cart.checkNum || !cart.carts || !cart.carts.length || !currentPoint || currentPoint < cart.totalPoint)}}"
        size="large" bind:click="onClickSubmit">
        {{ (!currentPoint || currentPoint < cart.totalPoint)?'积分不足无法兑换':'立即兑换'}}
      </basic-button>
    </view>
  </view>

  <my-popup show="{{ err.show }}" closeable="{{false}}" borderRadius="{{0}}" title="积分不足" confirmText="我知道了"
    cancelText="" content="您的积分不足以兑换选择的商品" bindconfirm="onPopConfirm" bindcancel="" data-key="fail" showType="normal">
  </my-popup>
</my-page>
