package com.dz.ms.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.ms.product.dto.ShelfProductSuperscriptDTO;
import com.dz.ms.product.dto.res.ShelfProductAllSuperscriptResDTO;
import com.dz.ms.product.entity.ShelfProductSuperscript;

import java.util.List;

/**
 * 货架商品角标接口
 *
 * @author: fei
 * @date: 2024/12/09 11:32
 */
public interface ShelfProductSuperscriptService extends IService<ShelfProductSuperscript> {

    /**
     * 根据货架商品ID列表/货架id查询货架展示角标列表
     * @param shelfProductIdList 货架商品ID列表
     * @param shelfId 货架ID
     * @return List<ShelfProductSuperscriptDTO
     */
    List<ShelfProductSuperscriptDTO> getShowSuperscriptList(List<Long> shelfProductIdList,Long shelfId);
    
    /**
     * 根据货架商品ID列表/货架id查询货架所有角标列表
     * @param shelfProductIdList 货架商品ID列表
     * @param shelfId 货架ID
     * @return List<ShelfProductAllSuperscriptResDTO>
     */
    List<ShelfProductAllSuperscriptResDTO> getAllProductSuperscriptList(List<Long> shelfProductIdList, Long shelfId);

    /**
     * 保存货架角标列表
     * @param shelfProductSuperscriptList 入参
     */
    void saveProductSuperscriptList(List<ShelfProductSuperscriptDTO> shelfProductSuperscriptList,Long shelfId);

    /**
     * 根据角标ID删除货架商品角标
     * @param superscriptId 角标ID
     */
    void delProductSuperscriptBySuperscriptId(Long superscriptId);

}
