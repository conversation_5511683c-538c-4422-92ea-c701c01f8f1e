<template>
  <layout>
    <template v-slot:header>
      <searchForm :formParams="formParams" @cancel="resetData" @ok="refreshData" :disabled="!$hasPermission('rightList:list:search')">

        <a-form-item label="">
          <a-input placeholder="请输入权益名称" v-model:value="formParams.benefitName" allow-clear></a-input>
        </a-form-item>

      </searchForm>
    </template>
    <template v-slot:topRight>
      <a-space>
        <!-- :disabled="!$hasPermission('open:leaveUser:synchLeaveUser')" 权限标识 -->
        <a-button type="primary" @click="addChang" :disabled="!$hasPermission('rightList:list:add')">{{'新建权益'}}</a-button>

      </a-space>

    </template>
    <template v-slot="{ height }">
      <a-table :indentSize="20" row-key="id" :scroll="{ scrollToFirstRowOnChange: true, y: height-88, x: '100%' }" :dataSource="dataSource" :columns="tableHeader" :pagination="pagination" :loading="loading" @change="loadData">
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'index'">
            {{ (current - 1) * pageSize + 1 + index }}
          </template>

          <template v-if="column.key === 'activateImg'">
            <a-image :width="50" :height="50" :src="record.activateImg" />
          </template>
          <template v-if="column.key === 'unActivateImg'">
            <a-image :width="50" style="background: #f0f0f0;" :height="50" :src="record.unActivateImg" />
          </template>
          <template v-if="column.key === 'state'">
            <a-tag color="green" v-if="record.state">启用</a-tag>
            <a-tag color="red" v-else>停用</a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-button type="link" @click="editSetting(record)" :disabled="!$hasPermission('rightList:list:edit')">编辑</a-button>
            <a-divider type="vertical" />
            <a-button v-if="record.state == 1" type="link" @click="changeState(record)" :disabled="!$hasPermission('rightList:list:state')">停用</a-button>
            <template v-else>
              <a-button type="link" @click="changeState(record)" :disabled="!$hasPermission('rightList:list:state')">启用</a-button>
              <a-divider type="vertical" />
              <a-popconfirm title="是否确定删除该数据？" @confirm="handleDelete(record)" :disabled="!$hasPermission('rightList:list:del')">
                <a-button type="link" :disabled="!$hasPermission('rightList:list:del')">删除</a-button>
              </a-popconfirm>
            </template>
          </template>
        </template>
      </a-table>
    </template>
  </layout>
  <addRighr :visible="visible" @ok="updateList" @cancel="visible = false" :id="id" :type="type" />
</template>



<script setup >
import { equityStatus } from '@/utils/dict-options'
import { useRoute, useRouter } from 'vue-router'
import { usePagination, useRequest, } from 'vue-request';
import addRighr from './components/addRighr.vue'
import { benefitLista, benefitDelete, benefitUpdate } from '@/http/index.js'
import _ from "lodash";
import { message, Modal } from "ant-design-vue";

// 表格分页数据获取
const total = ref(0)
const { formParams, tableHeader, visible, id, type } = toRefs(reactive({

  id: '',
  type: 0,// 0-新增 1-编辑  2-查看
  visible: false,

  formParams: {
    name: '',

  },

  tableHeader: [
    {
      title: "序号",
      key: "index",
      align: "center",
      width: 80,
    },

    {
      title: "权益名称",
      dataIndex: "benefitName",
      align: "center",
      width: 100,
      ellipsis: true,
    },
    {
      title: "权益说明",
      dataIndex: "details",
      align: "center",
      width: 100,
      ellipsis: true,
    },
    {
      title: "激活样式",
      key: "activateImg",
      dataIndex: "activateImg",
      align: "center",
      width: 120,
    },
    {
      title: "后续等级样式(未激活)",
      key: "unActivateImg",
      dataIndex: "unActivateImg",
      align: "center",
      width: 120,
    },

    {
      title: "启用状态",
      dataIndex: "state",
      key: "state",
      align: "center",
      width: 100,
    },
    {
      title: "操作",
      dataIndex: "action",
      key: "action",
      align: "center",
      width: 180,
      fixed: "right",
    },
  ],


})
);
const getParams = () => {
  // 拷贝一份 然后处理数据
  let params = _.cloneDeep(formParams.value);

  return params;
};
const { data: dataSource, run, loading, pageSize, current, refresh } = usePagination((param) => {
  // 这里修改参数
  return benefitLista({ ...param, ...getParams() })

},
  {
    manual: false,
    pagination: {
      currentKey: 'pageNum',  // 通过该值指定接口 当前页数 参数的属性值
      pageSizeKey: 'pageSize',// 通过该值指定接口 每页获取条数 参数的属性值
      totalKey: 'data.data.count',  // 指定 data 中 total 属性的路径
    },
    formatResult: res => { // 返回数据格式化
      total.value = res.data.count
      return res.data.list
    }
  });

function updateList(value) {
  visible.value = false;
  // 新增 重置搜索数据
  if (!value) {
    resetData();
  } else {
    // 查看、编辑 不需要清空搜索数据 直接刷新
    refresh();
  }
}
function changeState(record) {
  benefitUpdate({ id: record.id, state: record.state == '0' ? 1 : 0 }).then(res => {
    if (res.code === 0) {
      message.success(`${record.state == '0' ? '启用' : '停用'}成功`)
      refresh();
    }
  })

}
const handleDelete = (record) => {
  benefitDelete({ id: record.id }).then(res => {
    if (res.code === 0) {
      message.success('删除成功')
      resetData()
    }
  })
}
// 分页参数
let pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ["bottomLeft"],
  };
});

function addChang() {
  visible.value = true
  id.value = ''
  type.value = 0
}
function editSetting(row) {
  visible.value = true
  id.value = row.id
  type.value = 1
}

// 加载数据
const loadData = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}


// 刷新数据
const refreshData = () => {
  run({ pageNum: 1, pageSize: pageSize.value })
};

// 重置数据
const resetData = () => {
  formParams.value = {
    benefitName: '',
  }
  refreshData()
}
</script>
