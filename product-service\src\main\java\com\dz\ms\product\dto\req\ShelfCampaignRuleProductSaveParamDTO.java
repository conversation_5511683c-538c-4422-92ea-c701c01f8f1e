package com.dz.ms.product.dto.req;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 营销活动规则关联的货架商品保存入参
 * @author: fei
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@ApiModel(value = "营销活动规则关联的货架商品保存入参")
public class ShelfCampaignRuleProductSaveParamDTO {

    @ApiModelProperty(value = "营销活动ID")
    private Long campaignId;
    @ApiModelProperty(value = "营销活动规则ID")
    private Long ruleId;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "货架商品列表")
    private List<ShelfCampaignRuleProductSaveDTO> saveRuleProductList;

}
