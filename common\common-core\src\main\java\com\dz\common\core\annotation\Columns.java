package com.dz.common.core.annotation;

import com.dz.common.core.enums.ColumnType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 表字段注解
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
public @interface Columns {

    /** 字段名 不填则使用实体字段名转下划线 */
    String name() default "";
    /** 字段类型 */
    ColumnType type();
    /** 字段长度 不需要长度的字段填0 */
    int length();
    /** 字段是否可为空 */
    boolean isNull();
    /** 字段描述 */
    String comment();
    /** 默认值 */
    String defaultValue() default "";
    /** 小数点位数 */
    int scale() default 0;
    /** 是否索引 */
    boolean isIndex() default false;
    /** 是否唯一索引 */
    boolean isUnique() default false;
    /** 联合索引 */
    String[] unionKeys() default "";
    /** 联合唯一索引 */
    String[] uniqueKeys() default "";

}