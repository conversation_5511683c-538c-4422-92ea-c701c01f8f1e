<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.basic.mapper.MaterialInfoMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    id,
  	    material_type,
  	    group_id,
  	    material_name,
  	    material_url,
        video_poster,
  	    tenant_id,
  	    creator,
  	    created,
  	    modifier,
  	    modified,
  	    is_deleted
    </sql>

    <!-- 根据素材id列表获取素材地址列表 -->
    <select id="getMaterialUrlByIds" resultType="com.dz.ms.basic.dto.MaterialSimpleDTO">
        select
        id,
        material_url url,
        video_poster poster
        from material_info
        where id in
        <foreach collection="ids" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <!-- 生效达到生效日期的替换素材 -->
    <update id="effectiveReplaceUrl">
        update material_info
        set material_url = replace_url,replace_url = null
        where replace_url is not null
        and effective_time &lt;= #{date}
    </update>

    <!-- 批量新增素材信息 -->
    <insert id="insertBatch">
        insert into material_info(material_type,group_id,material_name,material_url,replace_url,video_poster,effective_time,expire_time,tenant_id,creator,created,modifier,modified)
        values
        <foreach collection="list" item="material" separator=",">
            (#{material.materialType}, #{material.groupId}, #{material.materialName}, #{material.materialUrl}, #{material.replaceUrl}, #{material.videoPoster}, #{material.effectiveTime}, #{material.expireTime},
             #{material.tenantId}, #{material.creator}, #{material.created}, #{material.modifier}, #{material.modified})
        </foreach>
    </insert>

</mapper>
