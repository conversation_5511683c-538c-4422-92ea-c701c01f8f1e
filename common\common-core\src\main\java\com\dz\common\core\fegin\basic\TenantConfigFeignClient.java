package com.dz.common.core.fegin.basic;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.basic.TenantConfigDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = ServiceConstant.BASIC_SERVICE_NAME, contextId = "TenantConfigFeignClient")
public interface TenantConfigFeignClient {

    /**
     * 根据ID查询租户设置
     * @param id
     * @return result<TenantConfigDTO>
     */
    @GetMapping(value = "/tenant_config/info")
    public Result<TenantConfigDTO> getTenantConfigById(@RequestParam("id") Long id);

}
