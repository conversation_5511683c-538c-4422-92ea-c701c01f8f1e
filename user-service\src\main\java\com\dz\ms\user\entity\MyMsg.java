package com.dz.ms.user.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 我的消息记录
 */
@Getter
@Setter
@NoArgsConstructor
@Table("我的消息记录")
@TableName(value = "t_my_msg")
public class MyMsg implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 标题
     */
    private String title;
    /**
     *内容
     */
    private String msgDesc;
    /**
     *跳转链接
     */
    private String jumpUrl;
    /**
     * 是否已读，1是2否
     */
    private Integer isRead;
    /**
     * userId
     */
    private String userId;
    /**
     *创建人
     */
    private String createAt;
    /**
     *创建时间
     */
    private Date createTime;
    /**
     *渠道id
     */
    private Long tenantId;
    /**
     *消息code
     */
    private String msgCode;
    /**
     *首页通知标题
     */
    private String homeTitle;
    /**
     *排序
     */
    private Integer sortNum;
    /**
     *1会小2历史迁移
     */
    private Integer msgType;

}
