<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.basic.mapper.PrivacyPolicyMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    id,
  	    policy_name,
  	    policy_version,
  	    content,
  	    agreement,
  	    tenant_id,
  	    creator,
  	    created,
  	    modifier,
  	    modified,
  	    is_deleted
    </sql>

    <!-- 获取最新隐私条款版本号 -->
    <select id="selectLastPrivacyPolicyVersion" resultType="java.lang.String">
        select policy_version
        from privacy_policy
        WHERE
            push_time le #{currentTime}
          AND status = 0
        ORDER BY
            created DESC
            LIMIT 1;
    </select>

    <!-- 获取最新隐私条款内容 -->
    <select id="selectLastPrivacyPolicyInfo" resultType="com.dz.ms.basic.dto.PrivacyPolicyDTO">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        privacy_policy
        WHERE
        push_time le #{currentTime}
        AND status = 0
        ORDER BY
        created DESC
        LIMIT 1;
    </select>

</mapper>
