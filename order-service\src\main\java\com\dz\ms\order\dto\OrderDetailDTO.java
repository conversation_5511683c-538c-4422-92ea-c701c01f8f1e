package com.dz.ms.order.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单详情信息DTO
 *
 * @author: LiinNs
 * @date: 2024/12/09 10:38
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "订单详情信息")
public class OrderDetailDTO extends BaseDTO {

    @ApiModelProperty(value = "ID")
    private Long id;
    @ApiModelProperty(value = "订单编号")
    private String orderCode;
    @ApiModelProperty(value = "商品ID")
    private Long productId;
    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "商品类型 1实物商品 2电子券")
    private Integer pdType;
    @ApiModelProperty(value = "外部数据ID 优惠券ID需兑换")
    private String venderId;
    @ApiModelProperty(value = "商品图片")
    private String imgUrl;
    @ApiModelProperty(value = "兑换积分，商品实际兑换时支付的积分单价")
    private Integer costPoint;
    @ApiModelProperty(value = "1积分+金额时 仍需支付的金额，商品实际兑换时支付的金额单价")
    private BigDecimal costPrice;
    @ApiModelProperty(value = "兑换积分，来自商品表的价格留存")
    private Integer pCostPoint;
    @ApiModelProperty(value = "1积分+金额时 仍需支付的金额，来自商品表的价格留存")
    private BigDecimal pCostPrice;
    @ApiModelProperty(value = "货架商品ID")
    private Long shelfProductId;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "兑换积分，来自货架上的价格留存")
    private Integer sCostPoint;
    @ApiModelProperty(value = "积分划线价，来自货架上的价格留存")
    private Integer sPrePoint;
    @ApiModelProperty(value = "营销活动ID")
    private Long campaignId;
    @ApiModelProperty(value = "营销规则ID")
    private Long ruleId;
    @ApiModelProperty(value = "兑换积分，来自营销规则的价格留存")
    private Integer rCostPoint;
    @ApiModelProperty(value = "积分划线价，来自营销规则的价格留存")
    private Integer rPrePoint;
    @ApiModelProperty(value = "商品数量")
    private Integer number;
    @ApiModelProperty(value = "实际支付总积分")
    private Integer realPoint;
    @ApiModelProperty(value = "实际支付总金额")
    private BigDecimal realAmount;
    @ApiModelProperty(value = "发货数量")
    private Integer deliveryNumber;
    @ApiModelProperty(value = "商品状态:1待兑换 2已兑换")
    private Integer status;
    @ApiModelProperty(value = "商品发货状态：0未发货，1已发货")
    private Integer sendStatus;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "修改时间")
    private Date modified;
    @ApiModelProperty(value = "否删除 0未删除 1已删除")
    private Integer isDeleted;

}
