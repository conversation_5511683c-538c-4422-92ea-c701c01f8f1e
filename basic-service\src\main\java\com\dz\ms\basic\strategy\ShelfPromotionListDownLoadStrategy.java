package com.dz.ms.basic.strategy;

import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.fegin.product.ShelfPromotionFeignClient;
import com.dz.common.core.service.DownloadStrategy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
public class ShelfPromotionListDownLoadStrategy implements DownloadStrategy {

    @Resource
    private ShelfPromotionFeignClient shelfPromotionFeignClient;

    @Override
    public void export(DownloadAddParamDTO downloadAddParamDTO) {
        shelfPromotionFeignClient.exportPromotionList(downloadAddParamDTO);
    }
}
