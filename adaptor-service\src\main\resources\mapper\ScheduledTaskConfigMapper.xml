<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.adaptor.mapper.ScheduledTaskConfigMapper" >

    <update id="updateConfig">
        update scheduled_task_config
        <set>
            <if test="param.taskName != null and param.taskName != ''">
                task_name = #{param.taskName},
            </if>
            <if test="param.cronExpression != null and param.cronExpression != ''">
                cron_expression = #{param.cronExpression},
            </if>
            <if test="param.className != null and param.className != ''">
                class_name = #{param.className},
            </if>
            <if test="param.methodName != null and param.methodName != ''">
                method_name = #{param.methodName},
            </if>
            <if test="param.updatedAt != null">
                updated_at = #{param.updatedAt},
            </if>
            param = #{param.param}
        </set>
        where id = #{param.id}
    </update>
    
</mapper>
