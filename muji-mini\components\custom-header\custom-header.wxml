<view class="custom-header-wrap {{isFixed?'isFixed':''}}" style="--bg:{{background || '#fff'}}; --status:{{statusBarHeight}}px;height:{{navBarHeight+statusBarHeight}}px">
  <view class="custom-header">
    <view style="height: {{ menuButtonTop }}px"></view>
    <!-- 传统导航 -->
    <block wx:if="{{type==1}}">
      <view class="custom-header-flex">
        <block wx:if="{{!isBackHidden}}">
          <text class="custom-header-back-icon iconfont icon-Home" style="color: {{color}}" bindtap="pageHome" wx:if="{{ isShowHomeIcon }}"></text>
          <text class="custom-header-back-icon iconfont icon-back" style="color: {{color}}" bindtap="pageBack" wx:else></text>
        </block>
        <view class="custom-header-content">
          <slot name="content"></slot>
        </view>
        <block wx:if="{{!isBackHidden}}">
          <text class="custom-header-back-icon iconfont icon-Home" bindtap="pageBack" style="opacity:0;"></text>
        </block>
      </view>
    </block>
    <!-- logo导航-->
    <block wx:elif="{{type==2}}">
      <view class="logo">
        <image class="close-popup" src="{{$cdn}}/logo-muji.svg" mode="widthFix" />
      </view>
    </block>
    <!-- 自定义导航 -->
    <block wx:else>
      <slot name="content"></slot>
    </block>
  </view>
</view>