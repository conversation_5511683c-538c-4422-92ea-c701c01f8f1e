<my-page overallModal="{{overallModal}}" loading="{{loading}}">
  <custom-header background="transparent" type="{{1}}" isBackHidden="{{ isBackHidden }}" isFixed />
  <view wx:if="{{apiLoading===false&&!resData.id}}" class="pageWrap" style="background-image: url({{$cdn}}/booking/new-booking2/treeBg.png?v=1.0.0);">
    <view class="treeText" style="background-image: url({{$cdn}}/booking/new-booking2/treeText.png?v=1.0.0);"></view>
    <view class="treeLogo" style="background-image: url({{$cdn}}/booking/new-booking2/treeLogo.png?v=1.0.0);"></view>
    <view class="treeMujiLogo" style="background-image: url({{$cdn}}/booking/new-booking2/treeMujiLogo.png?v=1.0.0);"></view>
    <view class="treeActivityRulesButton" style="background-image: url({{$cdn}}/booking/new-booking2/treeActivityRulesButton.png?v=1.0.0);" catch:tap="showRules"></view>
    <view class="inputWrap">
      <view class="item">
        <view class="label">姓<text class="gapW"></text>名</view>
        <input
          type="text" class="input" placeholder="" maxlength="10"
          catch:input="onInput" data-form-name="formFields" data-form-fields="{{formFields}}"
          data-field-name="name" value="{{formFields.name}}"
        />
      </view>
      <view class="item">
        <view class="label">电<text class="gapW"></text>话</view>
        <input
          type="number" class="input" placeholder="" maxlength="11"
          catch:input="onInput" data-form-name="formFields" data-form-fields="{{formFields}}"
          data-field-name="phone" value="{{formFields.phone}}"
        />
      </view>
      <view class="item">
        <view class="label">身份证</view>
        <input
          type="idcard" class="input" placeholder="" maxlength="18"
          catch:input="onInput" data-form-name="formFields" data-form-fields="{{formFields}}"
          data-field-name="idCard" value="{{formFields.idCard}}"
        />
      </view>
    </view>
    <view class="submitButton" style="background-image: url({{$cdn}}/booking/new-booking2/submitButton.png?v=1.0.0);" catch:tap="submit"></view>
    <view class="submitHint" style="background-image: url({{$cdn}}/booking/new-booking2/submitHint.png?v=1.0.0);"></view>
  </view>
  <view wx:if="{{apiLoading===false&&resData.id}}" class="pageWrap" style="background-image: url({{$cdn}}/booking/new-booking2/treeResult.jpg?v=1.0.0);">
    <view class="treeActivityRulesButton" style="background-image: url({{$cdn}}/booking/new-booking2/treeActivityRulesButton.png?v=1.0.0);" catch:tap="showRules"></view>
  </view>
  <booking2-active-rules isShow="{{visibleRules}}" bindclose="closeRules" />
</my-page>
