package com.dz.ms.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.ms.product.dto.ShelfTagDTO;
import com.dz.ms.product.dto.req.ShelfTagSaveParamDTO;
import com.dz.ms.product.dto.res.ShelfTagGroupResDTO;
import com.dz.ms.product.entity.ShelfTag;

import java.util.List;

/**
 * 货架标签表接口
 *
 * @author: LiinNs
 * @date: 2024/11/20 11:32
 */
public interface ShelfTagService extends IService<ShelfTag> {

    /**
     * 分页查询货架标签表
     *
     * @param param
     * @return PageInfo<ShelfTagDTO>
     */
    public PageInfo<ShelfTagDTO> getShelfTagList(ShelfTagDTO param);

    /**
     * 根据ID查询货架标签表
     *
     * @param id
     * @return ShelfTagDTO
     */
    public ShelfTagDTO getShelfTagById(Long id);

    /**
     * 根据货架ID查询货架标签组
     * @param shelfId 货架ID
     * @return result<List<ShelfTagGroupResDTO>
     */
    List<ShelfTagGroupResDTO> getTagGroupList(Long shelfId);

    /**
     * 根据标签ID查询货架标签
     * @param tagId 标签ID
     * @return result<List<ShelfTagDTO>
     */
    List<ShelfTagDTO> getTagByTagId(Long tagId);

    /**
     * 根据货架ID查询货架一级标签
     * @param shelfId 货架ID
     * @return result<List<ShelfTagDTO>
     */
    List<ShelfTagDTO> getOneTag(Long shelfId);

    /**
     * 根据货架ID,一级标签ID查询货架二级标签
     * @param shelfId 货架ID
     * @param tagId 一级标签ID
     * @return result<List<ShelfTagDTO>
     */
    List<ShelfTagDTO> getTwoTag(Long shelfId,Long tagId);

    /**
     * 保存货架标签表
     *
     * @param param
     * @return Long
     */
    public Long saveShelfTag(ShelfTagDTO param);

    /**
     * 保存一级货架标签
     * @param param 入参
     */
    void saveOneShelfTag(ShelfTagSaveParamDTO param);
    /**
     * 保存二级货架标签
     * @param param 入参
     */
    void saveTwoShelfTag(ShelfTagSaveParamDTO param);

    /**
     * 根据标签ID更新货架标签名称
     * @param param 入参
     */
    void updShelfTagName(ShelfTagDTO param);

    /**
     * 根据ID删除货架标签表
     *
     * @param param
     */
    public void deleteShelfTagById(IdCodeDTO param);

    /**
     * 根据标签ID删除货架标签
     * @param tagId 标签ID
     */
    void deleteShelfTagByTagId(Long tagId);

}
