package com.dz.common.core.enums;

import java.util.Objects;

/**
 * Describe：是否枚举类
 * Created by 吴蜀黍 on 2023/8/20 8:36
 **/
public enum YesOrNoEnum {

    NO(0, "否"),
    YES(1, "是"),
    ;

    private final Integer code;
    private final String value;

    YesOrNoEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (YesOrNoEnum resultEnum : YesOrNoEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum.value;
            }
        }
        return null;
    }
}
