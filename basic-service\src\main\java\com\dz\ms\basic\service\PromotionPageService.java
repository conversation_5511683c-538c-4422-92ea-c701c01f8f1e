package com.dz.ms.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.ms.basic.dto.PromotionPageAddDTO;
import com.dz.ms.basic.dto.PromotionPageDTO;
import com.dz.ms.basic.dto.PromotionPageUpdateDTO;
import com.dz.ms.basic.entity.PromotionPage;

import java.util.List;

public interface PromotionPageService extends IService<PromotionPage> {

    List<PromotionPageDTO> getPromotionPageList(Long templateId);

    Integer savePromotionPage(PromotionPageAddDTO param);

    Long updatePromotionPageUrl(PromotionPageUpdateDTO param);
    void updatePromotionPage(PromotionPageUpdateDTO param);

    List<Long> getChannelIds(Long templateId);

    String getParamByParamId(Long paramId);
}
