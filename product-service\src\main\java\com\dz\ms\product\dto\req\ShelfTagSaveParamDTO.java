package com.dz.ms.product.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 保存货架标签入参DTO
 * <AUTHOR>
 * @date: 2024/11/21 11:32
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "保存货架标签入参DTO")
public class ShelfTagSaveParamDTO {

    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "一级标签ID列表")
    private List<Long> oneTagIdList;
    @ApiModelProperty(value = "一级标签ID")
    private Long oneTagId;
    @ApiModelProperty(value = "二级标签ID列表")
    private List<Long> twoTagIdList;
    

}
