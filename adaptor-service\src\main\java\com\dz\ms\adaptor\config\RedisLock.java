package com.dz.ms.adaptor.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.ReturnType;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class RedisLock {

    @Resource
    private RedisTemplate<String, String> redisTemplate;
    public static final int EXPIRE_TIME = 60; // 锁的过期时间，单位秒
    public static final int RETRY_TIMES = 3; // 最大重试次数
    public static final long SLEEP_TIME = 3000; // 重试间隔时间，单位毫秒

    /**
     * 尝试获取分布式锁
     *
     * @param lockKey    锁
     * @param requestId  请求标识
     * @param expireTime 超期时间
     * @return 是否获取成功
     */
    public boolean tryGetDistributedLock(String lockKey, String requestId, int expireTime) {
        Boolean result = redisTemplate.opsForValue()
                .setIfAbsent(lockKey, requestId, expireTime, TimeUnit.SECONDS);
        log.info("RedisDistributedLock获取锁结果:" + result);
        return result != null && result;
    }

    /**
     * 释放分布式锁
     *
     * @param lockKey   锁
     * @param requestId 请求标识
     * @return 是否释放成功
     */
    public boolean releaseDistributedLock(String lockKey, String requestId) {
        String requestIdStr = "\"" + requestId + "\"";
        String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
        Object result = redisTemplate.execute(
                (RedisCallback<Object>) (connection) -> connection.eval(script.getBytes(), ReturnType.INTEGER, 1, lockKey.getBytes(), requestIdStr.getBytes())
        );
        log.info("RedisDistributedLock释放锁结果:" + result);
        return RELEASE_SUCCESS.equals(result);
    }

    /**
     * 尝试获取分布式锁并重试
     *
     * @param lockKey    锁
     * @param requestId  请求标识
     * @param expireTime 超期时间
     * @param retryTimes 重试次数
     * @param sleepTime  重试间隔时间（毫秒）
     * @return 是否获取成功
     */
    public boolean tryGetDistributedLockWithRetry(String lockKey, String requestId, int expireTime, int retryTimes, long sleepTime) {
        for (int i = 0; i < retryTimes; i++) {
            if (tryGetDistributedLock(lockKey, requestId, expireTime)) {
                return true;
            }
            try {
                Thread.sleep(sleepTime);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
        return false;
    }

    private static final Long RELEASE_SUCCESS = 1L;
}
