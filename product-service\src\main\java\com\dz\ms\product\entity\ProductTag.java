package com.dz.ms.product.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 商品标签表
 *
 * @author: LiinNs
 * @date: 2024/11/27 15:13
 */
@Getter
@Setter
@NoArgsConstructor
@Table("商品标签表")
@TableName(value = "product_tag")
public class ProductTag implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "商品任务ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "商品ID", isIndex = true)
    private Long productId;
    @Columns(type = ColumnType.VARCHAR, length = 256, isNull = true, comment = "商品名称", isIndex = true)
    private String productName;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "标签ID", isIndex = true)
    private Long tagId;
    @Columns(type = ColumnType.VARCHAR, length = 256, isNull = true, comment = "标签名称", isIndex = true)
    private String tagName;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, comment = "1一级标签 2二级标签 ...")
    private Integer cate;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    public ProductTag(Long id, Long productId, String productName, Long tagId, String tagName, Integer cate) {
        this.id = id;
        this.productId = productId;
        this.productName = productName;
        this.tagId = tagId;
        this.tagName = tagName;
        this.cate = cate;
    }

}
