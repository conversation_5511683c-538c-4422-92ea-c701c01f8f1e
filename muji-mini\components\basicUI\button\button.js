const app = getApp()

Component({
  properties: {
    size: {
      type: String,
      value: 'middle' // 默认值
    },
    loading: {
      type: Boolean,
      value: false
    },
    width: {
      type: Number,
      value: 300,
    },
    btnState: {
      type: String,
      value: 'primary' // plain  gray
    },
    disabled: {
      type: Boolean,
      value: false
    },
    // 按钮置灰的时候 点击的提示文案
    disabledTip: {
      type: String,
      value: ''
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    // 自定义导航栏的高度
    statusBarHeight: app.globalData.statusBarHeight,
    navBarHeight: app.globalData.navBarHeight,
    bg: 'transparent'
  },

  /**
   * 组件的方法列表
   */
  methods: {
    onClick() {
      let {
        disabled,
        loading,
        disabledTip
      } = this.data
      if (disabled) {
        if (disabledTip) {
          wx.showToast({
            title: disabledTip,
            icon: 'none'
          })
        }
      }
      if (disabled || loading) return
      this.triggerEvent('click'); // 触发 click 事件
    }
  }
})
