package com.dz.ms.adaptor.processor;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dz.common.core.dto.adaptor.SiftEnrollDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.fegin.sales.CampaignEnrollFeignClient;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/7
 */
@Slf4j
@Component
public class CampaignEnrollSiftJob implements BasicProcessor {

    @Resource
    private CampaignEnrollFeignClient campaignEnrollFeignClient;


    @Override
    public ProcessResult process(TaskContext taskContext) throws Exception {
        String jobParams = taskContext.getJobParams();
        /**
         * {
         *     "campaignCode": "string",
         *     "channelOne1":{
         *         "channelTwo1": 1,
         *         "channelTwo2": 2,
         *         "channelTwo3": 3
         *     },
         *     "channelOne2":{
         *          "channelTwo1": 1,
         *          "channelTwo2": 2,
         *          "channelTwo3": 3
         *     }
         * }
         *
         */
        SecurityContext.setUser(new CurrentUserDTO(1L));
        JSONObject jsonObject = JSON.parseObject(jobParams);
        SiftEnrollDTO javaObject = jsonObject.toJavaObject(SiftEnrollDTO.class);
        campaignEnrollFeignClient.siftCampaignEnrollByChannel(javaObject);
        log.info("活动报名随机人选审核通过执行完成");
        return new ProcessResult(true, "success");
    }

}
