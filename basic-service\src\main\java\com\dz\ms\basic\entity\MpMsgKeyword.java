package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 小程序订阅消息关键字
 * @author: Handy
 * @date:   2023/07/06 18:05
 */
@Getter
@Setter
@NoArgsConstructor
@Table("小程序/公众号模板消息关键字")
@TableName(value = "mp_msg_keyword")
public class MpMsgKeyword implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "消息配置ID",isIndex = true)
    private Long msgId;
    @Columns(type = ColumnType.INT,length = 10,isNull = true,comment = "关键词排序")
    private Integer sort;
    @Columns(type = ColumnType.VARCHAR,length = 64,isNull = true,comment = "关键词名称")
    private String keyName;
    @Columns(type = ColumnType.VARCHAR,length = 64,isNull = true,comment = "关键词类型")
    private String keyType;
    @Columns(type = ColumnType.INT,length = 10,isNull = true,comment = "关键词ID")
    private Integer keyId;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = true,comment = "是否可编辑内容 0否 1是")
    private Integer isEdit;
    @Columns(type = ColumnType.VARCHAR,length = 64,isNull = true,comment = "关键词编辑内容")
    private String editText;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = true,comment = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;

    public MpMsgKeyword(Long id, Long msgId, Integer sort, String keyName, String keyType, Integer keyId, Integer isEdit, String editText) {
        this.id = id;
        this.msgId = msgId;
        this.sort = sort;
        this.keyName = keyName;
        this.keyType = keyType;
        this.keyId = keyId;
        this.isEdit = isEdit;
        this.editText = editText;
    }
}
