/* dress/pages/index/index.wxss */
.page{
	height:100vh;
	position: relative;
	&-bg{
		position: absolute;
		top:0;
		left:0;
		width:750rpx;
		height:100vh;
	}
	&-rule{
		position: absolute;
		top:11%;
		right:20rpx;
		width:200rpx;
		height:100rpx;
		// background:rgba(255,0,0,.5)
	}
	&-camera{
		position: absolute;
		bottom:13.8%;
		left:50%;
		transform: translateX(-50%);
		width:500rpx;
		height:150rpx;
		// background:rgba(255,0,0,.5)
	}
	&-photo{
		position: absolute;
		bottom:4.3%;
		left:50%;
		transform: translateX(-50%);
		width:500rpx;
		height:150rpx;
		// background:rgba(255,0,0,.5)
	}
	&-modal{
		position: absolute;
		z-index: 10;
		top:0;
		left:0;
		width:750rpx;
		height:100vh;
	}
}