package com.dz.ms.basic.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.annotation.Cacheable;
import com.dz.common.core.dto.basic.MaterialRelationParamDTO;
import com.dz.common.core.enums.MaterialRelationEnum;
import com.dz.common.core.enums.WechatApiEnum;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.common.core.utils.RandomUtils;
import com.dz.ms.basic.constants.CacheKeys;
import com.dz.ms.basic.dto.*;
import com.dz.ms.basic.entity.*;
import com.dz.ms.basic.mapper.*;
import com.dz.ms.basic.service.MaterialInfoService;
import com.dz.ms.basic.service.MiniappTemplateService;
import com.dz.ms.basic.service.MpConfigService;
import com.dz.ms.basic.service.PromotionPageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小程序页面模板
 * @author: Handy
 * @date:   2022/02/04 22:59
 */
@Service
@Slf4j
public class MiniappTemplateServiceImpl extends ServiceImpl<MiniappTemplateMapper,MiniappTemplate> implements MiniappTemplateService {

    @Resource
    private MiniappTemplateMapper miniappTemplateMapper;
    @Resource
    private MiniappTemplateHistoryMapper historyMapper;
    @Resource
    private MpConfigService mpConfigService;
    @Resource
    private MaterialInfoService materialInfoService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private RedisService redisService;
    @Resource
    private MiniappPathMapper miniappPathMapper;
    @Resource
    private PageGroupMapper pageGroupMapper;
    @Resource
    private PromotionPageService promotionPageService;
    /**
     * 保存小程序页面模板
     * @param param
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveMiniappTemplate(MiniappTemplateDTO param) {
        if(null != param.getId()) {
            MiniappTemplate miniappTemplate = miniappTemplateMapper.selectById(param.getId());
            if(null == miniappTemplate) {
                throw new BusinessException(ErrorCode.BAD_REQUEST,"模板ID不存在");
            }
        }
        MiniappTemplate miniappTemplate = new MiniappTemplate(param.getId(), param.getTemplateType(), param.getTemplateName(), null, param.getPageJson(), param.getPublish());
        if(null != param.getPublish() && param.getPublish().equals(1)) {
            if(null != param.getIsOnly() && param.getIsOnly().equals(1)) {
//                miniappTemplateMapper.setUnPublishByType(param.getPublish());
            }
        }

        Long tenantId = SecurityContext.getUser().getTenantId();

        if(ParamUtils.isNullOr0Long(miniappTemplate.getId())) {
            if(null != param.getPublish() && param.getPublish().equals(1)) {
                miniappTemplate.setPushTime(new Date());
            }
            miniappTemplate.setContent(param.getContent());
            miniappTemplate.setTemplateCode("TM"+ RandomUtils.genNumberCode(6));
            if (miniappTemplate.getTemplateType()==3){
                MiniappPath path = new MiniappPath();
                path.setPath(param.getPagePath());
                path.setPageType(101);
                miniappPathMapper.insert(path);
                miniappTemplate.setPathId(path.getId());
            }else if (miniappTemplate.getTemplateType()==1 || miniappTemplate.getTemplateType()==2){
               MiniappPath p =  miniappPathMapper.selectByPageType(param.getPageType());
                if (p!=null){
                     miniappTemplate.setPathId(p.getId());
                }
            }
            //如果是开屏页，则默认全部是活动开屏页，除非重新设置
            if (miniappTemplate.getTemplateType()==5){
                MiniappPath p =  miniappPathMapper.selectByPageType(6);
                if (p!=null){
                    miniappTemplate.setPathId(p.getId());
                    miniappTemplate.setPageType(6);
                }
            }
            MiniappTemplateHistory history = BeanCopierUtils.convertObject(miniappTemplate,MiniappTemplateHistory.class);
            if (param.getPageJson() !=null){
                history.setContent(param.getPageJson());
            }
            historyMapper.insert(history);
            miniappTemplateMapper.insert(miniappTemplate);
        } else {
//            if (param.getPageType()!=null && param.getPageType()!=0 && param.getPageType()!=null && param.getPageType()!=101 && param.getPageType() !=6) {
//                MiniappPath path = miniappPathMapper.selectByPageType(param.getPageType());
////                MiniappTemplate template = miniappTemplateMapper.getTemplateByType(param.getPageType());
////                if (template!=null) {
////                    redisService.del(CacheKeys.MINIAPP_TEMPLATE_BYID + ":" + 1 + ":" + template.getId());
////                    miniappTemplateMapper.setTemplateTypeByType(template.getPageType());
////                    miniappTemplate.setPathId(path.getId());
////                }
//            }
            //根据ID获取要修改的模板
            MiniappTemplate template = miniappTemplateMapper.selectById(param.getId());
            //如果是开屏页，则默认全部是活动开屏页，除非重新设置
            if (miniappTemplate.getTemplateType()==5 && template.getPageType()!=7){
                MiniappPath p =  miniappPathMapper.selectByPageType(6);
                if (p!=null){
                    miniappTemplate.setPathId(p.getId());
                    miniappTemplate.setPageType(6);
                }
            }
            MiniappTemplateHistory history = BeanCopierUtils.convertObject(miniappTemplate,MiniappTemplateHistory.class);
            history.setTemplateId(miniappTemplate.getId());
            if(null != param.getPublish() && param.getPublish().equals(1)) {
                miniappTemplate.setPushTime(new Date());
                miniappTemplate.setModified(new Date());
                miniappTemplate.setModifier(SecurityContext.getUser().getUid());
                miniappTemplate.setContent(param.getContent());
                history.setContent(param.getContent());
                history.setCreated(new Date());
                history.setCreator(SecurityContext.getUser().getUid());
            }else {
                miniappTemplate.setModified(new Date());
                miniappTemplate.setModifier(SecurityContext.getUser().getUid());
                history.setContent(param.getContent());
                history.setCreated(new Date());
                history.setCreator(SecurityContext.getUser().getUid());
            }

            if (param.getTemplateType() == 2 && param.getPublish() == 0){
                miniappTemplate.setPublish(1);
                history.setContent(param.getPageJson());
            }
            history.setId(null);
            historyMapper.insert(history);
            miniappTemplateMapper.updateById(miniappTemplate);
            redisService.del(CacheKeys.MINIAPP_TEMPLATE_BYID+":"+ tenantId +":"+miniappTemplate.getId());
        }
        MaterialRelationParamDTO relationParam = new MaterialRelationParamDTO(MaterialRelationEnum.MINIAPP_TEMPLATE,param.getMaterialIds(),miniappTemplate.getId());
        materialInfoService.saveMaterialRelation(relationParam);
        redisService.del(CacheKeys.MINIAPP_TEMPLATE+":"+ 1 +":"+miniappTemplate.getTemplateType()+":"+param.getPageType());
        return miniappTemplate.getId();
    }

    @Override
    public Long updateMiniappTemplatePath(MiniappTemplateUpdatePathDTO param) {
        if(null == param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"模板ID不存在");
        }
        MiniappTemplate miniappTemplate = miniappTemplateMapper.selectById(param.getId());
        if(null == miniappTemplate) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"模板ID不存在");
        }

        miniappTemplate.setId(param.getId());
        miniappTemplate.setPageType(param.getPageType());

        MiniappPath path = miniappPathMapper.selectByPageType(param.getPageType());


        if (param.getPageType()!=null && param.getPageType()!=0 && param.getPageType()!=101 && param.getPageType() !=6) {
            MiniappTemplate template = miniappTemplateMapper.getTemplateByType(param.getPageType());
            if (template!=null) {
                redisService.del(CacheKeys.MINIAPP_TEMPLATE_BYID + ":" + 1 + ":" + template.getId());
                miniappTemplateMapper.setTemplateTypeByType(template.getPageType());
                PromotionPageUpdateDTO promotionPageUpdateDTO = new PromotionPageUpdateDTO();
                promotionPageUpdateDTO.setId(template.getId());
                promotionPageUpdateDTO.setPath(path.getPath());
                promotionPageService.updatePromotionPage(promotionPageUpdateDTO);
            }
        }

        PromotionPageUpdateDTO promotionPageUpdateDTO = new PromotionPageUpdateDTO();
        promotionPageUpdateDTO.setId(param.getId());
        promotionPageUpdateDTO.setPath(path.getPath());
        promotionPageService.updatePromotionPage(promotionPageUpdateDTO);

        miniappTemplate.setPathId(path.getId());
        miniappTemplateMapper.updateById(miniappTemplate);
        redisService.del(CacheKeys.MINIAPP_TEMPLATE_BYID+":"+ 1 +":"+miniappTemplate.getId());
        redisService.del(CacheKeys.MINIAPP_TEMPLATE+":"+ 1 +":"+miniappTemplate.getTemplateType()+":"+miniappTemplate.getPageType());
        return miniappTemplate.getId();
    }
    @Override
    public void updateMiniappTemplatePublish(MiniappTemplateUpdatePathDTO param) {
        MiniappTemplate miniappTemplate = miniappTemplateMapper.selectById(param.getId());
        if(null == miniappTemplate) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"模板ID不存在");
        }
        if (miniappTemplate.getPageJson()!= null){
            miniappTemplate.setContent(miniappTemplate.getPageJson());
        }
        miniappTemplate.setPublish(param.getPublish());
        miniappTemplate.setPushTime(new Date());
        if (param.getTemplateType()!= null && param.getTemplateType()==2){
            LambdaQueryWrapper<MiniappTemplateHistory> historyWrapper = new LambdaQueryWrapper<>();
            historyWrapper.eq(MiniappTemplateHistory :: getTemplateId,param.getId());
            historyWrapper.orderByDesc(MiniappTemplateHistory :: getCreated);
            historyWrapper.last("limit 1");
            MiniappTemplateHistory history = historyMapper.selectOne(historyWrapper);
            if (history!= null){
                miniappTemplate.setContent(history.getContent());
            }
        }
        MiniappTemplateHistory history = BeanCopierUtils.convertObject(miniappTemplate,MiniappTemplateHistory.class);
        history.setId(null);
        historyMapper.insert(history);
        miniappTemplateMapper.updateById(miniappTemplate);
        redisService.del(CacheKeys.MINIAPP_TEMPLATE_BYID+":"+ 1 +":"+miniappTemplate.getId());
        redisService.del(CacheKeys.MINIAPP_TEMPLATE+":"+ 1 +":"+miniappTemplate.getTemplateType()+":"+miniappTemplate.getPageType());
    }

    @Override
    public List<MiniappTemplate> selectHistory(List<MiniappTemplate> list) {
        // 循环获取系统页面保存记录
        for (MiniappTemplate miniappTemplate : list) {
            LambdaQueryWrapper<MiniappTemplateHistory> historyWrapper = new LambdaQueryWrapper<>();
            historyWrapper.eq(MiniappTemplateHistory :: getTemplateId,miniappTemplate.getId());
            historyWrapper.orderByDesc(MiniappTemplateHistory :: getCreated);
            historyWrapper.last("limit 1");
            MiniappTemplateHistory history = historyMapper.selectOne(historyWrapper);
            if (history!= null) {
                miniappTemplate.setContent(history.getContent());
                miniappTemplate.setPublish(history.getPublish());
            }
        }
        return list;
    }

    @Override
    public PageInfo<MiniappTemplateListDTO> getMiniappTemplatePath(PageInfo<MiniappTemplateListDTO> miniappTemplateListDTOPageInfo) {
        // 循环获取小程序路径及页面分组
        List<MiniappTemplateListDTO> list = miniappTemplateListDTOPageInfo.getList();
        for (MiniappTemplateListDTO dto : list) {
            if (dto.getTemplateType()==4){
                continue;
            }
            if (dto.getTemplateType() == 2){
                LambdaQueryWrapper<MiniappTemplateHistory> historyWrapper = new LambdaQueryWrapper<>();
                historyWrapper.eq(MiniappTemplateHistory :: getTemplateId,dto.getId());
                historyWrapper.orderByDesc(MiniappTemplateHistory :: getCreated);
                historyWrapper.last("limit 1");
                MiniappTemplateHistory history = historyMapper.selectOne(historyWrapper);
                if (history!= null && dto.getPushTime() != null){
                    dto.setModified(history.getCreated());
                }
            }
            if (dto.getPathId() != null){
                MiniappPath paths = miniappPathMapper.selectById(dto.getPathId());
                dto.setPagePath(paths.getPath());
                if (dto.getGroupId()!=null){
                    PageGroup pageGroups = pageGroupMapper.selectById(dto.getGroupId());
                    dto.setGroupName(pageGroups.getGroupName());
                }
            }
        }
        return miniappTemplateListDTOPageInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updsateMiniappTemplateGroup(MiniappTemplateGroupDTO param) {
        PageGroup pageGroup = pageGroupMapper.selectById(param.getId());
        if (pageGroup==null){
            throw new BusinessException(ErrorCode.BAD_REQUEST,"分组ID不存在");
        }
        for (Long id : param.getTemplateIds()) {
            MiniappTemplate miniappTemplate = miniappTemplateMapper.selectById(id);
            if (miniappTemplate==null){
                throw new BusinessException(ErrorCode.BAD_REQUEST,"模板ID不存在");
            }
            miniappTemplate.setGroupId(param.getId());
            miniappTemplateMapper.updateById(miniappTemplate);
        }
    }



    /**
     * 根据模板ID获取小程序页面模板
     * @param id
     * @return
     */
    @Override
    @Cacheable(prefix = CacheKeys.MINIAPP_TEMPLATE_BYID,key = "'#tenantId'+'#id'")
    public MiniappTemplateDTO getMiniappTemplateById(Long id, Long tenantId) {
        MiniappTemplate miniappTemplate = miniappTemplateMapper.selectById(id);
        if (miniappTemplate==null){
            throw new BusinessException(ErrorCode.BAD_REQUEST,"模板ID不存在");
        }
        miniappTemplate.setPageJson(null);
        return BeanCopierUtils.convertObject(miniappTemplate,MiniappTemplateDTO.class);
    }

    @Override
    public MiniappTemplate getCrmMiniappTemplateById(Long id) {
        MiniappTemplate miniappTemplate = miniappTemplateMapper.selectById(id);
        if (miniappTemplate==null){
            throw new BusinessException(ErrorCode.BAD_REQUEST,"模板ID不存在");
        }
        if (miniappTemplate.getTemplateType() == 2){
            LambdaQueryWrapper<MiniappTemplateHistory> historyWrapper = new LambdaQueryWrapper<>();
            historyWrapper.eq(MiniappTemplateHistory :: getTemplateId,miniappTemplate.getId());
            historyWrapper.orderByDesc(MiniappTemplateHistory :: getCreated);
            historyWrapper.last("limit 1");
            MiniappTemplateHistory history = historyMapper.selectOne(historyWrapper);
            if (history!= null && miniappTemplate.getPushTime() != null){
                miniappTemplate.setModified(history.getCreated());
                Integer size = history.getCreated().compareTo(miniappTemplate.getPushTime());
                if (size>0){
                    miniappTemplate.setContent(history.getContent());
                }
            }
        }
        return miniappTemplate;
    }

    /**
     * 根据模板类型获取小程序页面模板
     * @param type
     * @return
     */
    @Override
    @Cacheable(prefix = CacheKeys.MINIAPP_TEMPLATE,key = "'#tenantId'+'#type'+'#pageType'")
    public MiniappTemplateDTO getMiniappTemplateByType(Integer type,Integer pageType,Long tenantId) {
        LambdaQueryWrapper<MiniappTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MiniappTemplate :: getTemplateType,type);
        wrapper.eq(MiniappTemplate :: getPageType,pageType);
        wrapper.eq(MiniappTemplate :: getPublish,1);
        List<MiniappTemplate> miniappTemplateList = miniappTemplateMapper.selectList(wrapper);
        if(CollectionUtils.isEmpty(miniappTemplateList)) {
            return null;
        }
        MiniappTemplate miniappTemplate = miniappTemplateList.get(0);
        miniappTemplate.setPageJson(null);
        return BeanCopierUtils.convertObject(miniappTemplate,MiniappTemplateDTO.class);
    }

    /**
     * 获取小程序二维码
     * @param page
     * @param scene
     * @param width
     * @return
     */
    @Override
    public byte[] getMiniappQrcode(String page,String scene, Integer width, Integer trial, Integer isHyaline, String linColor) {
        long times1 = System.currentTimeMillis();
        log.info("获取小程序二维码方法进入："+times1);
        Map<String, Object> params = new HashMap<String, Object>();
        if(StringUtils.isBlank(scene)) {
            scene = "qrcode";
        }
        if (page.startsWith("/")){
            page = page.substring(1);
        }
        params.put("scene", scene);
        if(StringUtils.isNotBlank(page)) {
            params.put("page", page);
        }
        if(null == width || width < 1) {
            width = 280;
        }

        params.put("width", width);
        if(null != trial) {
            params.put("check_path", false);
            if(trial == 1) {
                params.put("env_version", "trial");
            }
            else if(trial == 2) {
                params.put("env_version", "develop");
            }else if(trial == 3) {
                params.put("env_version", "release");
            }
        }
        if(null != isHyaline && isHyaline == 1) {
            params.put("is_hyaline", true);
        }
        if (StringUtils.isNotBlank(linColor)) {
            params.put("line_color", JSON.parseObject(linColor));
        }
        String paramJson = JSON.toJSONString(params);
        HttpEntity requestEntity = new HttpEntity(paramJson);
        log.info("获取小程序二维码开始入参数："+paramJson);
        String accessToken = mpConfigService.getMiniappAccessToken(SecurityContext.getUser().getTenantId(), false);
        log.info("token："+accessToken);
        ResponseEntity<byte[]> entity = restTemplate.exchange(WechatApiEnum.API_GET_MINAPP_QRCODE.getUrl()+"?access_token="+accessToken, HttpMethod.POST, requestEntity, byte[].class);
        byte[] result = entity.getBody();
        long times2 = System.currentTimeMillis();
        log.info("获取小程序二维码方法完毕,用时 [{}]", times2-times1);
        return result;
    }

}
