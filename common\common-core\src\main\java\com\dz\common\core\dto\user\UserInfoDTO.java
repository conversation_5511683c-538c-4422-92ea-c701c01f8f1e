package com.dz.common.core.dto.user;

import com.dz.common.base.dto.BaseDTO;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.enums.ColumnType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 用户信息DTO
 * @author: Handy
 * @date:   2022/01/30 22:55
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "用户信息")
public class UserInfoDTO extends BaseDTO {

    @ApiModelProperty(value = "用户ID")
    private Long id;
    @ApiModelProperty(value = "手机号")
    private String mobile;
    @ApiModelProperty(value = "小程序openid")
    private String openid;
    @ApiModelProperty(value = "微信unionid")
    private String unionid;
    @ApiModelProperty(value = "会员卡号")
    private String cardNo;
    @ApiModelProperty(value = "会员名")
    private String username;
    @ApiModelProperty(value = "性别 0未知 1男 2女")
    private Integer gender;
    @ApiModelProperty(value = "头像")
    private String avatar;
    @ApiModelProperty(value = "出生日期")
    private String birthday;
    @ApiModelProperty(value = "会员等级")
    private String cardLevel;
    @ApiModelProperty(value = "省份")
    private String province;
    @ApiModelProperty(value = "城市")
    private String city;
    @ApiModelProperty(value = "区")
    private String area;
    @ApiModelProperty(value = "注册时间")
    private Date registerTime;
    @ApiModelProperty(value = "授权隐私条款版本")
    private String policyVersion;

    @ApiModelProperty(value = "是否勾选接收品牌信息")
    private Integer isAgreeBrand;

    @ApiModelProperty(value = "是否勾选已满十四岁 0勾选 1未勾选")
    private Integer isAgreeYear;

    @ApiModelProperty(value = "unionid MD5加密串")
    private String clientId;
    @ApiModelProperty(value = "状态 0删除 1正常 2冻结")
    private Integer state;

}
