<template>
  <layout>
    <template v-slot:header>
      <searchForm :formParams="formParams" @cancel="resetData" @ok="refreshData" :disabled="!$hasPermission('marketingTask:search')">

        <a-form-item label="" name="taskName">
          <a-input placeholder="请输入任务名称" v-model:value="formParams.taskName" allow-clear></a-input>
        </a-form-item>
        <a-form-item name="createTime" label="任务展示时间">
          <a-range-picker v-model:value="formParams.createTime" :placeholder="['开始时间', '结束时间']" :presets="$rangePresets" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
        </a-form-item>
        <a-form-item name="taskStatus" label="">
          <a-select ref="select" v-model:value="formParams.taskStatus" allowClear :disabled="disabled" :options="taskStatusOptions" :fieldNames="{ label: 'label', value: 'value' }" optionFilterProp="label" showSearch placeholder="任务状态"></a-select>
        </a-form-item>
        <a-form-item name="taskType">
          <a-select ref="select" v-model:value="formParams.taskType" allowClear :disabled="disabled" :options="taskTypeOptions" :fieldNames="{ label: 'label', value: 'value' }" optionFilterProp="label" showSearch placeholder="任务类型"></a-select>
        </a-form-item>
      </searchForm>
    </template>
    <template v-slot:topRight>
      <a-space>
        <!-- :disabled="!$hasPermission('marketingTask:add')" 权限标识 -->
        <!-- :disabled="!$hasPermission('marketingTask:derive')" -->
        <a-button type="primary" :disabled="!$hasPermission('marketingTask:add')" @click="addChang">{{'新建任务'}}</a-button>
        <a-button type="primary" @click="exportData">导出</a-button>

      </a-space>

    </template>
    <template v-slot="{ height }">
      <a-table :indentSize="20" row-key="id" :scroll="{ scrollToFirstRowOnChange: true, y: height-88, x: '100%' }" :dataSource="dataSource" :columns="tableHeader" :pagination="pagination" :loading="loading" @change="loadData">
        <template v-slot:headerCell="{ column }">
          <span v-if="column.title == '总参与人数'">
            <a-space>
              <span>总参与人数</span><a-tooltip placement="top">
                <template #title>
                  <span>点击过该任务跳转即算</span>
                </template>
                <QuestionCircleOutlined />
              </a-tooltip>
            </a-space>
          </span>
          <span v-else-if="column.title == '总完成人数'">
            <a-space>
              <span>总完成人数</span><a-tooltip placement="top">
                <template #title>
                  <span>完成过1次该任务即算</span>
                </template>
                <QuestionCircleOutlined />
              </a-tooltip>
            </a-space>
          </span>
          <span v-else-if="column.title == '总完成次数'">
            <a-space>
              <span>总完成次数</span><a-tooltip placement="top">
                <template #title>
                  <span>完成过该任务的总次数</span>
                </template>
                <QuestionCircleOutlined />
              </a-tooltip>
            </a-space>
          </span>
          <span v-else>{{ column.title }}</span>
          <!-- 在这里添加自定义的头部内容 -->
        </template>
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'index'">
            {{ (current - 1) * pageSize + 1 + index }}
          </template>
          <template v-if="column.key === 'showTimeStart'">
            <div v-if="record.showTimeType == '1'">
              <div>{{record.showTimeStart}}</div>
              <div>{{record.showTimeEnd}}</div>

            </div>

            <div v-else-if="record.showTimeType == '2'">永久展示</div>
            <div v-else-if="record.showTimeType == '3'">根据活动时间展示</div>
            <div v-else>--</div>
            <!-- {{ record.showTimeType }} -->
          </template>
          <template v-if="column.key === 'restrictTimeStart'">
            <div v-if="record.isTimeRestrict == '1'">
              <template v-if="record.restrictTimeStart&& record.restrictTimeEnd">
                <div>{{record.restrictTimeStart}}</div>
                <div>{{record.restrictTimeEnd}}</div>
              </template>
              <div v-else>--</div>
            </div>
            <div v-else>永久展示</div>
          </template>
          <template v-if="column.key === 'status'">
            <a-tag color="green" v-if="!record.status">启用</a-tag>
            <a-tag color="red" v-else>停用</a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-button type="link" :disabled="!$hasPermission('marketingTask:edit')" @click="editSetting(record)">编辑</a-button>
            <a-divider type="vertical" />
            <a-button v-if="record.status == 0" :disabled="!$hasPermission('marketingTask:status')" type="link" @click="changeState(record)">停用</a-button>
            <template v-else>
              <!-- :disabled="!$hasPermission('marketingTask:edit')" -->
              <a-button type="link" :disabled="!$hasPermission('marketingTask:status')" @click="changeState(record)">启用</a-button>
              <a-divider type="vertical" />
              <a-popconfirm title="是否确定删除该数据？" :disabled="!$hasPermission('marketingTask:del')" @confirm="handleDelete(record)">
                <a-button type="link" :disabled="!$hasPermission('marketingTask:del')">删除</a-button>
              </a-popconfirm>
            </template>
          </template>
        </template>
      </a-table>
    </template>
  </layout>
  <addTask :visible="visible" @ok="updateList" @cancel="visible = false" :id="id" :type="type" />
</template>



<script setup >
import { cloneDeep } from "lodash";
import { taskTypeOptions, taskStatusOptions, taskContentOptions, rewardTypeOptions } from '@/utils/dict-options'
import { useRoute, useRouter } from 'vue-router'
import { usePagination, useRequest, } from 'vue-request';
import addTask from './components/addTask.vue'
import { taskDelete, taskList, downloadTask, interactiontaskstatus } from '@/http/index'
import { message, Modal } from "ant-design-vue";

// 分页参数
let pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ["bottomLeft"],
  };
});
// 表格分页数据获取
const total = ref(0)
const { formParams, tableHeader, visible, id, type } = toRefs(reactive({

  id: '',
  type: 0,// 0-新增 1-编辑  2-查看
  visible: false,

  formParams: {
    taskName: '',

  },

  tableHeader: [
    {
      title: "序号",
      key: "index",
      align: "center",
      width: 80,
    },
    {
      title: "任务类型",
      dataIndex: "taskType",
      align: "center",
      width: 100,
      ellipsis: true,
      customRender: (row) => {
        // console.log(row);
        let textLabel = null
        if (row.text) {
          textLabel = taskTypeOptions.find(item => item.value == row.text)
        }
        // console.log(textLabel);
        return textLabel && textLabel.label || '--'; // 如果数据为空，则显示 '----' textLabel.label ||
      }
    },
    {
      title: "任务名称",
      dataIndex: "taskName",
      align: "center",
      width: 100,
      ellipsis: true,
    },
    {
      title: "任务内容",
      dataIndex: "taskDesc",
      align: "center",
      width: 100,
      ellipsis: true,
      customRender: (row) => {
        // console.log(row);
        let textLabel = null
        if (row.text) {
          textLabel = taskContentOptions.find(item => item.value == row.text)
        }
        // console.log(textLabel);
        return textLabel && textLabel.label || '--'; // 如果数据为空，则显示 '----' textLabel.label ||
      }
    },
    {
      title: "任务展示时间",
      key: "showTimeStart",
      dataIndex: "showTimeStart",
      align: "center",
      width: 180,
    },
    {
      title: "任务限时时间",
      key: "restrictTimeStart",
      dataIndex: "restrictTimeStart",
      align: "center",
      width: 180,
    },
    {
      title: "任务状态",
      key: "taskStatus",
      dataIndex: "taskStatus",
      align: "center",
      width: 120,
      customRender: (row) => {
        // console.log(row);
        let textLabel = null
        if (typeof row.text == 'number') {
          textLabel = taskStatusOptions.find(item => item.value === row.text)
        }
        console.log(textLabel);
        return textLabel && textLabel.label || '--'; // 如果数据为空，则显示 '----' textLabel.label ||
      }
    },
    {
      title: "完成要求",
      key: "readyCycle",
      dataIndex: "readyCycle",
      align: "center",
      width: 120,
      customRender: (row) => {
        console.log("🚀 ~ row:", row)
        // row.record

        if (row.text == '1') {
          if (row.record.readyNum) {
            return row.record.readyNum + "次"
          } else {
            return '--'
          }

        } else if (row.text == '2') {
          if (row.record.readyNum) {
            let day = row.record.reayType == 'readyDay' ? row.record.readyDay + '天' : row.record.readyMonth + '月'
            return row.record.readyNum + "次" + '/' + day
          } else {
            return '--'
          }

        } else if (row.text == '3') {
          if (row.record.totalReadyNum) {
            let day = row.record.reayType == 'readyDay' ? row.record.readyDay + '天' : row.record.readyMonth + '月'
            return row.record.totalReadyNum + "次" + '/' + day
          } else {
            return '--'
          }

        } else {
          return '--'
        }
      }
    },
    {
      title: "奖励类型",
      key: "rewardType",
      dataIndex: "rewardType",
      align: "center",
      width: 120, //readyCycle
      customRender: (row) => {
        // console.log(row.record.taskRewardList[0]);
        if (row.record.readyCycle != 3) {
          let textLabel = null
          if (row.record.taskRewardList && row.record.taskRewardList.length > 0) {
            if (row.record.taskRewardList[0] && row.record.taskRewardList[0]['rewardType']) {
              textLabel = rewardTypeOptions.find(item => item.value == row.record.taskRewardList[0]['rewardType'])
            } else {
              return '--'
            }
            // console.log(textLabel);
            return textLabel && textLabel.label
          } else {
            return '--'
          }

        } else if (row.record.readyCycle == 3) {
          return '组合';
        } else {
          return '--';
        }

      }

    },
    {
      title: "活动内容/数量",
      // key: "taskRewardList",
      dataIndex: "taskRewardList",
      align: "center",
      ellipsis: true,
      width: 180,
      customRender: (row) => {
        // console.log(row.text);
        if (row.text && row.text.length > 0) {
          let newTxt = row.text.map(item => {
            if (item.rewardType == 1) {
              return item.couponNames
            } else if (item.rewardType == 3) {
              return item.prizeNum
            } else {
              return item.pointsNum
            }
          })
          if (newTxt && newTxt.length > 0) {
            return newTxt.join('/')
          } else {
            return '--'
          }

        } else {
          return '--'
        }
      }
    },
    {
      title: "总参与人数",
      key: "joinInNum",
      dataIndex: "joinInNum",
      align: "center",
      ellipsis: true,
      width: 120,
    },
    {
      title: "总完成人数",
      key: "finishPersonNum",
      dataIndex: "finishPersonNum",
      align: "center",
      ellipsis: true,
      width: 120,
    },
    ,
    {
      title: "总完成次数",
      key: "finishNum",
      dataIndex: "finishNum",
      align: "center",
      ellipsis: true,
      width: 120,
    },
    {
      title: "启用状态",
      dataIndex: "status",
      key: "status",
      align: "center",
      width: 100,
    },
    {
      title: "操作",
      dataIndex: "action",
      key: "action",
      align: "center",
      width: 180,
      fixed: "right",
    },
  ],


})
);
const getParams = () => {
  // 拷贝一份 然后处理数据
  let params = cloneDeep(formParams.value);
  params.showTimeStart = params.createTime
    ? params.createTime[0] + " 00:00:00"
    : null;
  params.showTimeEnd = params.createTime
    ? params.createTime[1] + " 23:59:59"
    : null;
  delete params.createTime;
  return params;
};
const { data: dataSource, run, loading, pageSize, current, refresh } = usePagination((param) => {
  // 这里修改参数
  return taskList({ ...param, ...getParams() })

},
  {
    manual: false,
    pagination: {
      currentKey: 'pageNum',  // 通过该值指定接口 当前页数 参数的属性值
      pageSizeKey: 'pageSize',// 通过该值指定接口 每页获取条数 参数的属性值
      totalKey: 'data.data.count',  // 指定 data 中 total 属性的路径
    },
    formatResult: res => { // 返回数据格式化
      console.log("🚀 ~ res:", res)
      total.value = Number(res.data.count)
      return res.data.list
    }
  });
function editSetting(record) {
  visible.value = true
  id.value = record.id
  type.value = 1
}
function addChang() {
  visible.value = true
  id.value = ''
  type.value = 0
}
function changeState(record) {
  interactiontaskstatus({ id: record.id, status: record.status == '0' ? 1 : 0 }).then(res => {
    if (res.code === 0) {
      message.success(`${record.status == '1' ? '启用' : '停用'}成功`)
      resetData()
    }
  })
}
function updateList(value) {
  visible.value = false;
  // 新增 重置搜索数据
  if (!value) {
    resetData();
  } else {
    // 查看、编辑 不需要清空搜索数据 直接刷新
    refresh();
  }
}
const handleDelete = (record) => {
  taskDelete({ id: record.id }).then(res => {
    if (res.code === 0) {
      message.success('删除成功')
      resetData()
    }
  })
}
// 加载数据
const loadData = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}


// 刷新数据
const refreshData = () => {
  run({ pageNum: 1, pageSize: pageSize.value })
};

// 重置数据
const resetData = () => {
  formParams.value = {
    taskName: '',
  }
  refreshData()
}
function exportData() {
  downloadTask({ type: 5, fileName: '营销任务列表', param: { ...getParams() } })
}
</script>