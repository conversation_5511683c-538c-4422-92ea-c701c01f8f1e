export const apiBooking = {
  async getBookingInfo (data) {
    return wx.$request({
      url: '/app/user/appointments/details',
      data,
      method: 'get'
    }).then(res => {
      res.data.userAppointment = res.data.userAppointment || {}
      // 场景模拟 ↓
      // res.data.expired = true
      // res.data.inWhitelist = true
      // res.data.alreadyBooked = true
      // 场景模拟 ↑
      return res
    })
  },
  async setBookingInfo (data) {//之前的活动提交接口
    return wx.$request({
      url: '/app/user/appointments/book',
      data,
      method: 'post'
    }).then(res => {
      return res
    })
  },
  async getBookingDates (data) {//之前活动的日期列表
    return wx.$request({
      url: '/app/user/appointments/dates_stats',
      data,
      method: 'get'
    }).then(res => {
      res.data.forEach((v, i) => {
        if (v.full) {
          v.disabled = true
          v.name = `${v.name}（已满）`
        }
      })
      return res
    })
  },
  async getBookingSlots (data) {//之前的场次接口
    return wx.$request({
      url: '/app/user/appointments/slots_stats',
      data,
      method: 'get'
    }).then(res => {
      res.data.forEach((v, i) => {
        if (v.full) {
          v.disabled = true
          v.name = `${v.name}（已满）`
        }
      })
      return res
    })
  }
}
