package com.dz.ms.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.core.dto.basic.GiftConfigDTO;
import com.dz.ms.basic.entity.GiftConfig;
import com.dz.ms.basic.vo.GiftConfigVo;

import java.util.List;

/**
 * 企业微信配置接口
 * @author: Handy
 * @date:   2022/07/20 21:18
 */
public interface GiftConfigService extends IService<GiftConfig> {

    GiftConfigDTO getGiftConfigList(String type);
    GiftConfigDTO getGiftConfigInfo(Long id);

    Boolean getGiftLeveShow(Integer type);

    void updateGiftConfig(GiftConfigVo param);
    List<GiftConfigDTO> getGiftList();
}
