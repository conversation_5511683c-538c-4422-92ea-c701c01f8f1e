<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.sales.mapper.CampaignEnrollMapper">


    <select id="siftCampaignEnrollByChannel" resultType="com.dz.ms.sales.entity.CampaignEnroll">
        select * from campaign_enroll
        where is_deleted = 0 and status = 0 and campaign_code = #{campaignCode} and channel_one = #{channelOne} and channel_two = #{channelTwo}
        and mobile is not null and mobile != ''

        ORDER BY RAND() limit #{num}
    </select>

    <update id="verifyCampaignEnroll">
        update campaign_enroll set status = 1 where id in
        <foreach collection="idList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="verifyCampaignEnroll1">
        update campaign_enroll set status = 2 where status = 0 and campaign_code = #{campaignCode}
    </update>

</mapper>
