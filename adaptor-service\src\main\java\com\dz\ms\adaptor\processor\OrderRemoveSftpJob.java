package com.dz.ms.adaptor.processor;

import com.dz.common.core.fegin.order.ExchangeOrderFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import javax.annotation.Resource;

/**
 * 订单sftp删除任务
 */
@Slf4j
@Component
public class OrderRemoveSftpJob implements BasicProcessor {

    @Resource
    private ExchangeOrderFeignClient exchangeOrderFeignClient;

    @Override
    public ProcessResult process(TaskContext context) {
        exchangeOrderFeignClient.removeOrder();
        log.info("删除订单数据 完成");
        return new ProcessResult(true, "success");
    }
}