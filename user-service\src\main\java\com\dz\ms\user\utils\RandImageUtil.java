package com.dz.ms.user.utils;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.Random;

/**
 * 图形验证码
 * @Author: Handy
 * @Date: 2022/07/18 11:25
 */
public class RandImageUtil {
	/**
	 * 定义图形大小
	 */
	private static final int width = 105;
	/**
	 * 定义图形大小
	 */
	private static final int height = 35;
	/**
	 * 定义干扰线数量
	 */
	private static final int count = 350;
	/**
	 * 干扰线的长度=1.414*lineWidth
	 */
	private static final int lineWidth = 2;
	/**
	 * 图片格式
	 */
	private static final String IMG_FORMAT = "JPEG";
	/**
	 * base64 图片前缀
	 */
	private static final String BASE64_PRE = "data:image/jpg;base64,";

	public static final String VERIFY_CODES = "qwertyuipkjhgfdsazxcvbnmQWERTYUPLKJHGFDSAZXCVBNM1234567890";
	private static final Random random = new Random();

	/**
	 * 直接通过response 返回图片
	 *
	 * @param response
	 * @param resultCode
	 * @throws IOException
	 */
	public static void generate(HttpServletResponse response, String resultCode) throws IOException {
		BufferedImage image = getImageBuffer(resultCode);
		// 输出图象到页面
		ImageIO.write(image, IMG_FORMAT, response.getOutputStream());
	}

	/**
	 * 生成base64字符串
	 *
	 * @param resultCode
	 * @return
	 * @throws IOException
	 */
	public static String generate(String resultCode) {
		BufferedImage image = getImageBuffer(resultCode);
		ByteArrayOutputStream byteStream = new ByteArrayOutputStream();
		byte[] bytes = null;
		try {
			//写入流中
			ImageIO.write(image, IMG_FORMAT, byteStream);
			//转换成字节
			bytes = byteStream.toByteArray();
		} catch (IOException e) {
		} finally {
			try {
				byteStream.close();
			} catch (IOException e) {
			}
		}
		//转换成base64串
		String base64 = Base64.getEncoder().encodeToString(bytes);
		base64 = base64.replaceAll("\n", "").replaceAll("\r", "");
		return BASE64_PRE + base64;
	}

	private static BufferedImage getImageBuffer(String resultCode) {
		// 在内存中创建图象
		final BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
		// 获取图形上下文
		final Graphics2D graphics = (Graphics2D) image.getGraphics();
		// 设定背景颜色
		graphics.setColor(Color.WHITE); // ---1
		graphics.fillRect(0, 0, width, height);
		// 设定边框颜色
		// graphics.setColor(getRandColor(100, 200)); // ---2
		graphics.drawRect(0, 0, width - 1, height - 1);
		// 随机产生干扰线，使图象中的认证码不易被其它程序探测到
		for (int i = 0; i < count; i++) {
			graphics.setColor(getRandColor(150, 200)); // ---3
			final int x = random.nextInt(width - lineWidth - 1) + 1; // 保证画在边框之内
			final int y = random.nextInt(height - lineWidth - 1) + 1;
			final int xl = random.nextInt(lineWidth);
			final int yl = random.nextInt(lineWidth);
			graphics.drawLine(x, y, x + xl, y + yl);
		}
		// 取随机产生的认证码
		for (int i = 0; i < resultCode.length(); i++) {
			// 将认证码显示到图象中,调用函数出来的颜色相同，可能是因为种子太接近，所以只能直接生成
			// graphics.setColor(new Color(20 + random.nextInt(130), 20 + random
			// .nextInt(130), 20 + random.nextInt(130)));
			// 设置字体颜色
			graphics.setColor(Color.BLACK);
			// 设置字体样式
			// graphics.setFont(new Font("Arial Black", Font.ITALIC, 18));
			graphics.setFont(new Font("Times New Roman", Font.BOLD, 24));
			// 设置字符，字符间距，上边距
			graphics.drawString(String.valueOf(resultCode.charAt(i)), (19 * i) + 8, 26);
		}
		// 图象生效
		graphics.dispose();
		return image;
	}

	private static Color getRandColor(int fc, int bc) {
		final int f = fc;
		final int b = bc;
		final int red = f + random.nextInt(b - f);
		final int green = f + random.nextInt(b - f);
		final int blue = f + random.nextInt(b - f);
		return new Color(red, green, blue);
	}

	/**
	 * 使用系统默认字符源生成验证码
	 *
	 * @param verifySize
	 *            验证码长度
	 * @return
	 */
	public static String generateVerifyCode(int verifySize) {
		return generateVerifyCode(verifySize, VERIFY_CODES);
	}

	/**
	 * 使用指定源生成验证码
	 *
	 * @param verifySize
	 *            验证码长度
	 * @param sources
	 *            验证码字符源
	 * @return
	 */
	public static String generateVerifyCode(int verifySize, String sources) {
		if (sources == null || sources.length() == 0) {
			sources = VERIFY_CODES;
		}

		int codesLen = sources.length();
		Random rand = new Random(System.currentTimeMillis());
		StringBuilder verifyCode = new StringBuilder(verifySize);
		for (int i = 0; i < verifySize; i++) {
			verifyCode.append(sources.charAt(rand.nextInt(codesLen - 1)));
		}
		return verifyCode.toString();
	}

}