<template>
  <a-form-item :label="props.label" :name="props.name" :rules="props.rules" class="ParentWrap">
    <a-select
      :fieldNames="{label:'name',value:'id'}"
      v-model:value="thisFields.value"
      :filterOption="thisMethods.filterOption"
      show-search
      :allowClear="true"
      :maxTagCount="props.maxTagCount"
      placeholder="请选择"
      @change="thisMethods.change"
      :options="thisFields.options"
      v-bind="$attrs"
    />
  </a-form-item>
</template>

<script setup>
import { onMounted, reactive, watch } from 'vue'
import { apiShelfCrowd } from '@/http/index.js'

const props = defineProps({
  maxTagCount: {
    type: Number,
    default: 1
  },
  multipleMax: {
    type: [String, Number],
    default: ''
  },
  label: {
    type: String,
    default: ''
  },
  name: {
    type: String,
    default: ''
  },
  rules: {
    type: Object,
    default: () => undefined
  },
  modelValue: {
    type: [Array, String, Number],
    default: () => []
  },
  options: {
    type: [Array, undefined],
    default: () => undefined
  }
})
const emits = defineEmits(['change', 'update:modelValue'])

const attrs = useAttrs()
const thisFields = reactive({
  value: attrs.mode === 'multiple' ? [] : '',
  options: []
})
const thisMethods = {
  filterOption (input, option) {
    return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
  },
  async getOptions () {
    if (props.options) {
      thisFields.options = props.options
    } else {
      const res = await apiShelfCrowd.getAllPageList({ state: 1 })
      thisFields.options = res.data
    }
  },
  setValue () {
    thisFields.value = props.modelValue
  },
  change (e) {
    if (attrs.mode === 'multiple') {
      const multipleMax = +props.multipleMax
      if (multipleMax && e.length > multipleMax) {
        if (multipleMax === 1) {
          e = e.slice(e.length - multipleMax, e.length)
        } else {
          e.length = multipleMax
        }
      }
    }
    emits('update:modelValue', e)
    emits('change', e)
  }
}

onMounted(() => {
  thisMethods.setValue()
  thisMethods.getOptions()
})
watch(() => props.modelValue, () => thisMethods.setValue())
watch(() => props.options, () => thisMethods.getOptions())
</script>

<style lang="scss" scoped>
.ParentWrap {
  .ant-select {
    width: 202px;
  }
}
</style>
