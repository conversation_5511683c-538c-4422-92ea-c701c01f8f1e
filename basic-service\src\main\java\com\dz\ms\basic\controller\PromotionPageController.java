package com.dz.ms.basic.controller;

import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.enums.LogType;
import com.dz.ms.basic.dto.MiniappTemplateDTO;
import com.dz.ms.basic.dto.PromotionPageAddDTO;
import com.dz.ms.basic.dto.PromotionPageDTO;
import com.dz.ms.basic.dto.PromotionPageUpdateDTO;
import com.dz.ms.basic.service.PromotionPageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags="页面推广")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class PromotionPageController {

    @Resource
    private PromotionPageService promotionPageService;

    /**
     * 分页查询页面推广
     * @return result<PageInfo<PromotionPageDTO>>
     */
    @ApiOperation("分页查询页面推广")
    @GetMapping(value = "/crm/promotion_page/list")
    public Result<List<PromotionPageDTO>> getPromotionPageList(@RequestParam("templateId") Long templateId) {
        Result<List<PromotionPageDTO>> result = new Result<>();
        List<PromotionPageDTO> page = promotionPageService.getPromotionPageList(templateId);
        result.setData(page);
        return result;
    }

    @ApiOperation("查询已经添加过的推广渠道ID")
    @GetMapping(value = "/crm/promotion_page/getChannelList")
    public Result<List<Long>> getChannelList(@RequestParam("templateId") Long templateId) {
        Result<List<Long>> result = new Result<>();
        List<Long> ids = promotionPageService.getChannelIds(templateId);
        result.setData(ids);
        return result;
    }

    /**
     * 新增小程序页面推广
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增小程序页面推广",type = LogType.OPERATELOG)
    @ApiOperation("新增小程序页面推广")
    @PostMapping(value = "/crm/promotion_page/add")
    public Result addMiniappTemplate(@RequestBody PromotionPageAddDTO param) {
        Result result = new Result<>();
        Integer num = promotionPageService.savePromotionPage(param);
        if (num == 0) {
            result.setCode(500);
            result.setMsg("新增失败");
        }
        return result;
    }
    /**
     * 新增小程序页面推广
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "修改小程序页面推广Url",type = LogType.OPERATELOG)
    @ApiOperation("修改小程序页面推广")
    @PostMapping(value = "/crm/promotion_page/update")
    public Result UpdatePromotionPageUrl(@RequestBody PromotionPageUpdateDTO param) {
        Result result = new Result<>();
        Long num = promotionPageService.updatePromotionPageUrl(param);
        if (num == 0) {
            result.setCode(500);
            result.setMsg("新增失败");
        }
        return result;
    }
    /**
     * 新增小程序页面推广
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "根据二维码携带参数获取实际参数",type = LogType.OPERATELOG)
    @ApiOperation("新增小程序页面推广")
    @GetMapping(value = "/app/promotion_page/getParam")
    public Result<String> getParam(@RequestParam("paramId") Long paramId) {
        Result<String> result = new Result<>();
        String num = promotionPageService.getParamByParamId(paramId);
        result.setData(num);
        return result;
    }
}
