import axios from 'axios'
import { get, isUndefined } from 'lodash'
import { notification } from 'ant-design-vue'
import { useGlobalStore } from '../store'
import router from '../router'
import { getToken, } from '@/utils/auth'
// 不需要带有token的接口白名单
const whiteList = ['/crm/user/login/imagecode']

const service = axios.create({
  baseURL: import.meta.env.VITE_BASE_URL,
  timeout: 0, // 无限制的超时时间
  // 是否跨域请求A
  crossDomain: true
})


// 请求拦截
service.interceptors.request.use(config => {
  // 加上 config.headers 那么每次默认请求都会带上这个header

  if (config.method.toLowerCase() === 'get') {
    config.params = config.data
    delete config.data
    config.headers['Content-Type'] = config.headers['Content-Type'] || 'application/x-www-form-urlencoded'
  } else if (config.method.toLowerCase() === 'delete') {
    config.params = config.data
    delete config.data
    config.headers['Content-Type'] = config.headers['Content-Type'] || 'application/x-www-form-urlencoded'
  } else {
    config.headers['Content-Type'] = config.headers['Content-Type'] || 'application/json'
  }

  // 添加登录信息
  if (!whiteList.includes(config.url)) {
    // Authorization: 'Bearer ' + getToken()
    config.headers['token'] = getToken()
  }
  return config
}, (error) => {
  // 发送失败
  return Promise.reject(error)
})

// 响应拦截
service.interceptors.response.use((response) => {
  // console.log(response,'response');
  const code = response?.data?.code;
  if (code === 0) {  // 响应成功
    return Promise.resolve(response.data)
  } else if (code == 205) {
    return Promise.resolve(response.data)
  } else if (code === 401) {// token失效
    let name = router?.currentRoute?.value?.name
    // 防止多个接口同时调用报401 重复提醒
    if (name !== 'login') {
      notification.warning({
        message: '提示',
        description: '请重新登录',
        duration: 3
      });
      useGlobalStore().logout() // 清除缓存数据
      router.push({ name: 'login' })
    }
    return Promise.reject(response.data)
  } else if (code === 403) {// 接口无权限
    notification.warning({
      message: '提示',
      description: '没有数据权限，请联系管理员配置',
      duration: 3
    });
    return Promise.reject(response.data)
  } else if (response.config.responseType === 'blob') {// 针对导出blob做得判断(比如下载excel)
    return new Promise((resolve, reject) => {
      let reader = new FileReader();
      reader.readAsText(response.data)
      reader.onload = function () {
        let res = reader.result;
        try {
          let data = JSON.parse(res) // 这里可能会报错，报错代表返回的为正确的导出数据，不报错代表后台返回了错误提醒
          notification.error({
            message: '提示',
            description: data.msg,
            duration: 3
          });
          reject(response.data)
        } catch (e) {
          // 文件名字
          const name = decodeURIComponent(response.headers['content-disposition'].split('filename=')[1])
          resolve({ name, data: response.data })
        }
      }
    })
  } else if (response.config.responseType === 'arraybuffer') {// 文件流 小程序二维码
    return new Promise((resolve, reject) => {
      let reader = new FileReader();
      reader.readAsText(new Blob([response.data]))
      reader.onload = function () {
        let res = reader.result;
        try {
          let data = JSON.parse(res) // 这里可能会报错，报错代表返回的为正确的文件流，不报错代表后台返回了错误提醒
          notification.error({
            message: '提示',
            description: data.msg,
            duration: 3
          });
          reject(response.data)
        } catch (e) {
          resolve(response.data)
        }
      }
    })
  } else { // 其他错误提醒
    notification.error({
      message: '提示',
      description: response.data.msg,
      duration: 3
    });
    return Promise.reject(response.data)
  }
}, (error) => {
  const status = get(error, 'response.status')
  if (isUndefined(status)) {
    // 取消了请求
    return Promise.reject()
  }
  switch (status) {
    case 400:
      error.message = '请求错误'
      break
    case 401:
      router.push('/login')
      error.message = '未授权，请登录'
      break
    case 403:
      error.message = '拒绝访问'
      break
    case 404:
      error.message = `请求地址出错: ${error.response.config.url}`
      break
    case 408:
      error.message = '请求超时'
      break
    case 413:
      error.message = '上传文件太大'
      break
    case 500:
      error.message = '服务器内部错误'
      break
    case 501:
      error.message = '服务未实现'
      break
    case 502:
      error.message = '网关错误'
      break
    case 503:
      error.message = '服务不可用'
      break
    case 504:
      error.message = '网关超时'
      break
    case 505:
      error.message = 'HTTP版本不受支持'
      break
    default:
      error.message = '服务器错误，请联系技术人员'
      break
  }
  notification.error({
    message: '提示',
    description: error.message,
    duration: 3
  });
  return Promise.reject(error.response.data)
})

// 导出
export default service
