<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.product.mapper.TaskLogMapper">

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
        id,
  	    task_id,
  	    shelf_id,
  	    shelf_name,
  	    product_id,
  	    product_name,
  	    on_inventory,
  	    current_inventory,
  	    created
    </sql>

    <!-- 查询示例 -->
    <select id="selectDemo" parameterType="java.lang.Long" resultType="com.dz.ms.product.entity.TaskLog">
        select
        <include refid="Base_Column_List"/>
        from task_log
        where id = #{id,jdbcType=BIGINT}
    </select>

</mapper>
