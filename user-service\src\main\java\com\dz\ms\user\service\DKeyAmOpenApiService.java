package com.dz.ms.user.service;

import com.dz.ms.user.dto.dkeyam.req.BindingListParam;
import com.dz.ms.user.dto.dkeyam.req.IdentityAddParam;
import com.dz.ms.user.dto.dkeyam.req.IdentityReplaceParam;
import com.dz.ms.user.dto.dkeyam.req.MobileTokenBindTokenQrCodeParam;
import com.dz.ms.user.dto.dkeyam.req.MobileTokenCompleteTokenQrCodeParam;
import com.dz.ms.user.dto.dkeyam.req.MobileTokenDataParam;
import com.dz.ms.user.dto.dkeyam.req.MobileTokenDeliverParam;
import com.dz.ms.user.dto.dkeyam.req.MobileTokenUserQrCodeParam;
import com.dz.ms.user.dto.dkeyam.req.PortalIncWeiXinSyncUserParam;
import com.dz.ms.user.dto.dkeyam.req.PortalQrCodeGrantParam;
import com.dz.ms.user.dto.dkeyam.req.TenantListParam;
import com.dz.ms.user.dto.dkeyam.req.TokenBindParam;
import com.dz.ms.user.dto.dkeyam.req.TokenUnbindAllParam;
import com.dz.ms.user.dto.dkeyam.req.TokenUnbindParam;
import com.dz.ms.user.dto.dkeyam.req.disconnectParam;
import com.dz.ms.user.dto.dkeyam.res.CallbackAuthenticateRes;
import com.dz.ms.user.dto.dkeyam.res.CallbackGetAllUserRes;
import com.dz.ms.user.dto.dkeyam.res.CallbackGetOneUserRes;
import com.dz.ms.user.dto.dkeyam.res.DKeyAmPageInfo;
import com.dz.ms.user.dto.dkeyam.res.DKeyAmResult;
import com.dz.ms.user.dto.dkeyam.res.MobileTokenDataRes;
import com.dz.ms.user.dto.dkeyam.res.StrongPasswordRequirementRes;
import com.dz.ms.user.dto.dkeyam.res.TenantListRes;
import com.dz.ms.user.dto.dkeyam.res.TokenBindingRes;
import com.dz.ms.user.dto.dkeyam.res.TokenInfoRes;
import com.dz.ms.user.dto.dkeyam.res.UserInfoRes;
import com.dz.ms.user.dto.dkeyam.req.CallbackAuthenticateParam;
import com.dz.ms.user.dto.dkeyam.req.CallbackGetAllUserParam;
import com.dz.ms.user.dto.dkeyam.req.CallbackGetOneUserParam;
import com.dz.ms.user.dto.dkeyam.req.StrongAuthenticateParam;
import com.dz.ms.user.dto.dkeyam.req.StrongGetDynamicPasswordParam;
import com.dz.ms.user.dto.dkeyam.req.StrongPasswordRequirementParam;
import com.dz.ms.user.dto.dkeyam.req.UserDeleteParam;
import com.dz.ms.user.dto.dkeyam.req.UserIncSyncParam;
import com.dz.ms.user.dto.dkeyam.req.UserInfoParam;
import com.dz.ms.user.dto.dkeyam.req.UserInfosParam;
import com.dz.ms.user.dto.dkeyam.req.UserUnbindUserTerminalsParam;

import java.util.List;

/**
 * 宁盾Api接口
 *
 * @author: fei
 * @date: 2025/01/22 15:15
 */
public interface DKeyAmOpenApiService {

    /**
     * 认证
     * @param param 认证入参
     * @return DKeyAmResult<String>
     */
    DKeyAmResult<String> strongAuthenticateParam(StrongAuthenticateParam param);

    /**
     * 用户认证策略
     * @param param 用户认证策略入参
     * @return DKeyAmResult<StrongPasswordRequirementRes>
     */
    DKeyAmResult<StrongPasswordRequirementRes> strongPasswordRequirementParam(StrongPasswordRequirementParam param);

    /**
     * 发送动态密码
     * @param param 发送动态密码入参
     * @return DKeyAmResult<String>
     */
    DKeyAmResult<String> strongGetDynamicPassword(StrongGetDynamicPasswordParam param);

    /**
     * 外部用户认证
     * @param param 入参
     * @return CallbackAuthenticateReq
     */
    CallbackAuthenticateRes callbackAuthenticate(CallbackAuthenticateParam param);
    
    /**
     * 获取单个用户的信息
     * @param param 入参
     * @return CallbackGetOneUserReq
     */
    CallbackGetOneUserRes callbackGetOneUser(CallbackGetOneUserParam param);
    
    /**
     * 同步所有用户的信息
     * @param param 入参
     * @return DKeyAmPageInfo<CallbackGetAllUserReq>
     */
    DKeyAmPageInfo<CallbackGetAllUserRes> callbackGetAllUserReq(CallbackGetAllUserParam param);

    /**
     * 增量同步用户
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    DKeyAmResult<String> userIncSync(UserIncSyncParam param);

    /**
     * 删除用户
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    DKeyAmResult<String> userIncSync(UserDeleteParam param);

    /**
     * 获取单个用户的信息
     * @param param 入参
     * @return DKeyAmResult<UserInfoRes>
     */
    DKeyAmResult<UserInfoRes> userInfo(UserInfoParam param);

    /**
     * 拉取所有用户的信息
     * @param param 入参
     * @return DKeyAmResult<DKeyAmPageInfo<UserInfoRes>>
     */
    DKeyAmResult<DKeyAmPageInfo<UserInfoRes>> userInfos(UserInfosParam param);
    
    /**
     * 解绑用户终端
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    DKeyAmResult<String> userUnbindUserTerminals(UserUnbindUserTerminalsParam param);

    /**
     * 用户和令牌的绑定关系
     * @param param 入参
     * @return DKeyAmResult<List<TokenBindingRes>>
     */
    DKeyAmResult<List<TokenBindingRes>> bindingList(BindingListParam param);

    /**
     * 派发时间型令牌
     * @param param 入参
     * @return DKeyAmResult<TokenInfoRes>
     */
    DKeyAmResult<TokenInfoRes> mobileTokenDeliver(MobileTokenDeliverParam param);

    /**
     * 获取手机令牌二维码
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    DKeyAmResult<String> mobileTokenUserQrCode(MobileTokenUserQrCodeParam param);

    /**
     * 绑定并获取手机令牌二维码
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    DKeyAmResult<String> bindAndWriteMobileTokenQrCode(MobileTokenBindTokenQrCodeParam param);

    /**
     * 完成手机令牌绑定
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    DKeyAmResult<String> completeMobileTokenBinding(MobileTokenCompleteTokenQrCodeParam param);

    /**
     * 绑定时间型令牌
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    DKeyAmResult<String> tokenBind(TokenBindParam param);

    /**
     * 解绑时间型令牌
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    DKeyAmResult<String> tokenUnbind(TokenUnbindParam param);

    /**
     * 解绑所有时间型令牌
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    DKeyAmResult<String> tokenUnbindAll(TokenUnbindAllParam param);

    /**
     * 获取手机令牌信息
     * @param param 入参
     * @return DKeyAmResult<MobileTokenDataRes>
     */
    DKeyAmResult<MobileTokenDataRes> mobileTokenData(MobileTokenDataParam param);

    /**
     * 断开在线用户
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    DKeyAmResult<String> disconnect(disconnectParam param);

    /**
     * 扫码授权
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    DKeyAmResult<String> portalQrCodeGrant(PortalQrCodeGrantParam param);

    /**
     * 增量更新用户信息（微信）
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    DKeyAmResult<String> portalIncWeiXinSyncUser(PortalIncWeiXinSyncUserParam param);

    /**
     * 站点列表
     * @param param 入参
     * @return DKeyAmResult<List<TenantListRes>>
     */
    DKeyAmResult<List<TenantListRes>> tenantList(TenantListParam param);

    /**
     * 新增Mac地址黑白名单
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    DKeyAmResult<String> identityAdd(IdentityAddParam param);

    /**
     * Mac地址黑白名单
     * @param param 入参
     * @return DKeyAmResult<String>
     */
    DKeyAmResult<String> identityReplace(IdentityReplaceParam param);
    
}
