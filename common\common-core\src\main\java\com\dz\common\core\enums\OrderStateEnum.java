package com.dz.common.core.enums;

import java.util.Objects;

/**
 * @Author: liuzixian
 * Describe：订单状态类型枚举
 * @CreateTime: 2023-08-29 13:28
 */
public enum OrderStateEnum {

    PAY_PENDING_STATE(1, "待支付"),
    SHIP_PENDING_STATE(2, "待发货"),
    SHIP_COMPLETED_STATE(3, "已发货"),
    FINISHED_STATE(4, "已完成"),
    CANCELED_STATE(5, "已取消"),
    CLOSED_STATE(6, "已关闭"),
    PICKUP_PENDING_STATE(7, "待领取"),
    OVERDUE_STATE(8, "已过期"),
    ;

    private final Integer code;
    private final String value;

    OrderStateEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OrderStateEnum resultEnum : OrderStateEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum.value;
            }
        }
        return null;
    }
}
