<view class="rule-componets">
	<view class="rule-header">
		<view class="rule-header-left">使用规则</view>
		<view class="rule-header-right" bindtap="toggleContent" wx:if="{{scroll}}">
			{{ !isExpanded ? '展开' : '收起'}}
			<image class="toggle-icon" src="{{$cdn}}/{{!isExpanded?'unfold.png':'pack-up.png'}} " mode=""></image>
		</view>
	</view>
	<view class="rule-content" style="height:{{  !isExpanded ? ( scroll? height * rpx +'px' : textHeight+'px') : 'auto'}};overflow: hidden;transition: height 0.5s;">
		<text id="rule-list" class="rule-content-text {{refresh?isExpanded?'show':'hide':'show'}}">{{ruleDesc}}</text>
	</view>
</view>