import "./utils/config";
import "./utils/wx/index";
import "./utils/authMobile";
import "./utils/page";
import "./utils/request";
import "./utils/uploadFile";
import "./utils/uploadFiles";
import "./utils/authLocation";
import methods from "./utils/methods";

App({
  onLaunch(options) {
    console.log("小程序onLaunch", options);
    // 监听用户是否有未同意过的隐私协议时
    if (wx.onNeedPrivacyAuthorization) {
      // 隐私接口需要用户授权事件的监听函数
      wx.onNeedPrivacyAuthorization((resolve) => {
        // 需要用户同意隐私授权时
        this.globalData.resolvePrivacyAuthorization = resolve;
      });
    }
    this.initData();
    this.fontsAdd();
    this.watchData();
    this.autoGetPosition(); // 授权情况下自动获取经纬度
  },
  onShow() {
    // 检测版本更新
    if (wx.canIUse("getUpdateManager")) {
      const updateManager = wx.getUpdateManager();
      updateManager.onCheckForUpdate(function (res) {
        if (res.hasUpdate) {
          updateManager.onUpdateReady(function () {
            wx.showModal({
              title: "更新提示",
              content: "新版本已经准备好，是否重启应用？",
              success: function (res) {
                if (res.confirm) {
                  updateManager.applyUpdate();
                }
              },
            });
          });
          updateManager.onUpdateFailed(function () {
            wx.showModal({
              title: "已经有新版本了哟~",
              content: "新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~",
            });
          });
        }
      });
    }
  },
  // 页面未找到
  onPageNotFound(res) {
    console.log(res, "not found");
    let { notFound, path, query } = res;
    if (notFound) {
      // 先处理映射关系
      console.log(wx.$config.oldPath.has("/" + path), "是否有");
      if (wx.$config.oldPath.has("/" + path)) {
        let url = wx.$config.oldPath.get("/" + path);
        url += Object.keys(query).reduce(
          (total, key) => {
            return total + `&${key}=${query[key]}`;
          },
          url.includes("?") ? "" : "?"
        );
        console.log("实际跳转", url);
        this.goUrl(url, true);
        return;
      }
      wx.$mp.switchTab({
        url: "/pages/index/index",
      });
    }
  },
  fontsAdd() {
    wx.loadFontFace({
      global: true,
      family: 'MUJIFont2020',
      scopes: ['native'],
      desc: { weight: 900 },
      source: `url('${wx.$config.ossImg}/fonts/MUJIFont2020-Heavy.otf')`,
    })

    wx.loadFontFace({
      global: true,
      family: 'MUJIFont2020',
      scopes: ['native'],
      desc: { weight: 700 },
      source: `url('${wx.$config.ossImg}/fonts/MUJIFont2020-Bold.otf')`,
    })
    wx.loadFontFace({
      global: true,
      family: 'MUJIFont2020',
      scopes: ['native'],
      desc: { weight: 400 },
      source: `url('${wx.$config.ossImg}/fonts/MUJIFont2020-Regular.otf')`,
    })

    wx.loadFontFace({
      global: true,
      family: 'MUJIFont2020',
      scopes: ['native'],
      desc: { weight: 300 },
      source: `url('${wx.$config.ossImg}/fonts/MUJIFont2020-Light.otf')`,
    })

    wx.loadFontFace({
      global: true,
      family: 'SourceHanSansCN',
      scopes: ['native'],
      desc: { weight: 900 },
      source: `url('${wx.$config.ossImg}/fonts/SourceHanSansCN-Heavy_0.otf')`,
    })

    wx.loadFontFace({
      global: true,
      family: 'SourceHanSansCN',
      scopes: ['native'],
      desc: { weight: 700 },
      source: `url('${wx.$config.ossImg}/fonts/SourceHanSansCN-Bold_0.otf')`,
    })

    wx.loadFontFace({
      global: true,
      family: 'SourceHanSansCN',
      scopes: ['native'],
      desc: { weight: 500 },
      source: `url('${wx.$config.ossImg}/fonts/SourceHanSansCN-Medium_0.otf')`,
    })

    wx.loadFontFace({
      global: true,
      family: 'SourceHanSansCN',
      scopes: ['native'],
      desc: { weight: 400 },
      source: `url('${wx.$config.ossImg}/fonts/SourceHanSansCN-Regular_1.otf')`,
    })

    wx.loadFontFace({
      global: true,
      family: 'SourceHanSansCN',
      scopes: ['native'],
      desc: { weight: 300 },
      source: `url('${wx.$config.ossImg}/fonts/SourceHanSansCN-Normal_0.otf')`,
    })


  },
  initData() {
    const info = wx.getSystemInfoSync();
    const { statusBarHeight, windowWidth, model, safeArea } = info;
    let custom = wx.getMenuButtonBoundingClientRect();
    let navBarHeight = custom.height + (custom.top - statusBarHeight) * 2;
    this.globalData = {
      ...this.globalData,
      menuBottom: custom.bottom,
      statusBarHeight,
      navBarHeight,
      systemInfo: info,
      rpx: (1 / 750) * windowWidth,
      ratio: windowWidth / 750,
      isIPX: model.search("iPhone X") != -1 || safeArea.top > 20,
    };
  },
  // 授权情况下自动获取经纬度
  autoGetPosition() {
    return new Promise((resolve, reject) => {
      wx.getSetting({
        success: (res) => {
          // 判断用户是否授权了位置信息
          const isAuthorized = res.authSetting["scope.userLocation"];
          if (isAuthorized === true) {
            wx.getLocation({
              type: "gcj02", // 返回经纬度（gcj02坐标系）
              success: (v) => {
                // 全局保存最新的用户所在经纬度
                this.globalData.userLatitude = v.latitude;
                this.globalData.userLongitude = v.longitude;
                resolve({
                  latitude: v.latitude,
                  longitude: v.longitude,
                });
              },
            });
          }
        },
        fail: (error) => {
          reject({
            latitude: 0,
            longitude: 0,
          });
        },
      });
    }).then((res) => {
      // 全局保存最新的用户所在经纬度
      this.globalData.userLatitude = res.latitude;
      this.globalData.userLongitude = res.longitude;
    });
  },
  ...methods, // 全局方法
  // 监听操作
  // 监听数据的改变 并更新视图
  watchData() {
    let that = this;
    var obj = this.globalData;
    // globalData备份
    this.globalDataBackUps = {
      ...this.globalData,
    };

    // 监听用户信息的变化
    Object.defineProperty(obj, "userInfo", {
      configurable: true,
      enumerable: true,
      set(value) {
        that.globalDataBackUps.userInfo = value;
        // 获取当前页面 更新积分视图
        var currentInstance = wx.$mp.getCurrentPage();
        currentInstance.setData({
          userInfo: value,
          visitor: that.globalData.visitor,
        });

        let pages = getCurrentPages();
        pages.forEach((currentInstance) => {
          console.log(currentInstance, "666666666666666666666666");
          // 修改tabbar的游客状态
          if (typeof currentInstance.getTabBar === "function") {
            console.log("tabbar------------------", that.globalData.visitor);
            currentInstance.getTabBar((tabbar) => {
              tabbar.setData({
                visitor: that.globalData.visitor,
              });
            });
          }
        });
      },
      get() {
        return that.globalDataBackUps.userInfo;
      },
    });
    // 监听风格数据变化
    Object.defineProperty(obj, "styleSetting", {
      configurable: true,
      enumerable: true,
      set(value) {
        that.globalDataBackUps.styleSetting = value;
        // 获取当前页面 更新积分视图
        var currentInstance = wx.$mp.getCurrentPage();
        currentInstance.setData({
          styleSetting: value,
        });
      },
      get() {
        return that.globalDataBackUps.styleSetting;
      },
    });
    // 监听经纬度变化
    Object.defineProperty(obj, "userLatitude", {
      configurable: true,
      enumerable: true,
      set(value) {
        that.globalDataBackUps.userLatitude = value;
        // 获取当前页面 更新积分视图
        var currentInstance = wx.$mp.getCurrentPage();
        currentInstance.setData({
          userLatitude: value,
        });
      },
      get() {
        return that.globalDataBackUps.userLatitude;
      },
    });
    Object.defineProperty(obj, "userLongitude", {
      configurable: true,
      enumerable: true,
      set(value) {
        that.globalDataBackUps.userLongitude = value;
        // 获取当前页面 更新积分视图
        var currentInstance = wx.$mp.getCurrentPage();
        currentInstance.setData({
          userLongitude: value,
        });
      },
      get() {
        return that.globalDataBackUps.userLongitude;
      },
    });
  },
  // 注销清空数据
  clearData() {
    wx.clearStorageSync();
    // this.globalData.autoOperate = false
    this.globalData.isOpen = false
    this.globalData.visitor = false;
    this.globalData.isBirthday = false;
    this.globalData.token = "";
    this.globalData.channelOne = "";
    this.globalData.channelTwo = "";
    this.globalData.userInfo = {};
    this.globalData.tokenPromise = null;
    this.globalData.giftData = "";
    this.globalData.autoPrincy = false;
    this.globalData.firstData = null;
    this.globalData.switchTabQuery = {};
    // 清除埋点
    this.globalData.identities = [];
    this.globalData.profile = {};
    this.muji_sdk.cleanIdentities();
  },
  globalData: {
    isOpen: false,// 本次打开小程序是否已经看过开屏页
    isBirthday: false, // 是否是生日月
    autoOperate: false,
    channelOne: "", // 一级来源渠道参数
    channelTwo: "", // 二级来源渠道参数
    token: "", // token存放
    statusBarHeight: "",
    navBarHeight: "",
    menuButtonTop: 0,
    menuButtonBottom: 0,
    menuButtonHeight: 0,
    tabbarList: "", // 导航数据存放
    styleSetting: {
      pageStyle: {},
      openStyle: {},
    }, // 全局风格设置
    userInfo: {
      id: "",
    }, // 用户信息
    tokenPromise: null, // token获取返回结果
    autoPrincy: false, // 是否自动弹过一次隐私协议
    firstData: null, // 第一次隐私协议页面数据
    privacyData: {}, // 隐私协议内容数据
    userLatitude: 0, // 用户经纬度
    userLongitude: 0, // 用户经纬度
    giftData: "", // 在触发注册的地方需要注册的弹窗提示
    needAuthorization: false, // 是否需要小程序api授权
    resolvePrivacyAuthorization: null, // 监听api授权函数
    subscribeConfig: {}, // 订阅消息配置
    inviteUserId: "", // 邀请人ID
    switchTabQuery: {}, // 一级页面跳转带参
    identities: [
      // {
      //   type: 'wxa.openId', // ⼩程序的openeid
      //   id: ''
      // }, {
      //   type: 'wechat.unionId',
      //   id: '' // 开放平台的unionid
      // }, {
      //   type: 'member_id',
      //   id: '' // 用户的memberCode
      // }
    ],
    profile: {
      // nickname: '昵称',
      // mobilePhone: '13888888888',
      // avatar: '头像地址',
      // gender: 'male', // male:男 female：⼥ unknown：保密
      // props: {// ⾃定义⽤⼾属性
      // }
    },
  },
});
