package com.dz.ms.basic.utils;

import com.alibaba.fastjson.JSONObject;
import com.dz.common.core.dto.KeyValueDTO;
import org.springframework.web.client.RestTemplate;

import java.sql.Connection;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class SwaggerToPermit {

	public static void main(String[] args) throws Exception {
//		List<KeyValueDTO> list = new ArrayList<>();
//		String[] apps = new String[]{"basic","user","product","sales","order","adaptor"};
//		RestTemplate restTemplate = new RestTemplate();
//		for (String app : apps) {
//			JSONObject json = restTemplate.getForObject("https://bb-vip.theatomdata.com/api/"+app+"/api-docs?group=crm",JSONObject.class);
//			JSONObject paths = json.getJSONObject("paths");
//			if(null == paths){
//				continue;
//			}
//			for (String key : paths.keySet()) {
//				JSONObject method = paths.getJSONObject(key);
//				String name = method.getJSONObject(method.keySet().stream().toArray()[0].toString()).getString("summary");
//				list.add(new KeyValueDTO(null,app,name,key.replace("/crm/"+app,"/crm")));
//			}
//		}
//		Connection connection = SqlUtils.getConnnection("***************************************************************************************************************************","daozhi", "Daozhi@123");
//		ResultSet rs = SqlUtils.getResultSet(connection,"SELECT url FROM sys_permission_url");
//		List<String> urls = new ArrayList<>();
//		while(rs.next()){
//			urls.add(rs.getString("url"));
//		}
//		list = list.stream().sorted(Comparator.comparing(KeyValueDTO::getCode).thenComparing(KeyValueDTO::getValue)).collect(Collectors.toList());
//		String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
//		for (KeyValueDTO keyValue : list) {
//			if(urls.contains(keyValue.getValue())) {
//				continue;
//			}
//			com.dz.common.core.utils.SqlUtils.execute(connection,"INSERT INTO `sys_permission_url` (`permit_id`, `permit_name`, `url`, `created`, `creator`, `modified`, `modifier`) VALUES (0, '"+keyValue.getName()+"', '"+keyValue.getValue()+"', '"+date+"', 1, '"+date+"', 1);");
//		}

	}

	
}