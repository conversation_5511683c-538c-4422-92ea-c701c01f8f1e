<template>
  <div class="box" v-if="data.openFloat">
    <!-- horizontalType 水平位置  /1-右下 2-左下 -->
    <div class="box-view" :style="{width:data.width/2+'px',left:(data.horizontalType==1?'auto':data.left/2+'px'),right:(data.horizontalType==2?'auto':data.right/2+'px'),bottom:data.bottom/2+'px',}">
      <div class="box-content">
        <div class="box-item" :style="{marginBottom:data.space/2+'px'}" v-for="(item,index) in data.floatList" :key="index">
          <img class="box-img" :src="item.imgUrl" :style="{width:data.width/2+'px',height:data.height/2+'px'}" />
          <div class="box-text" v-if="data.showType==2&&item.name">{{item.name}}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
const emit = defineEmits(['ok', 'cancel', 'changeActive'])
import { cloneDeep } from 'lodash'
import { v4 as uuidv4 } from 'uuid'
import { computed } from 'vue';
import customBg from './customBg.vue'
const props = defineProps({
  width: {
    type: Number,
    default: 750,
  },
  data: {
    type: Object,
    default() {
      return {}
    }
  },
})


</script>

<style scoped lang="scss">
.box {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  pointer-events: none;
  width: auto;
  height: auto;

  &-view {
    pointer-events: auto;
    position: absolute;
    height: auto;
  }

  &-content {
    display: flex;
    flex-direction: column;
  }

  &-item {
    display: flex;
    flex-direction: column;
    align-items: center;

    &:last-child {
      margin-bottom: 0 !important;
    }
  }

  &-text {
    margin-top: 5px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 8px;
    color: #000000;
    line-height: 11px;
    white-space: nowrap;
  }
}
</style>
