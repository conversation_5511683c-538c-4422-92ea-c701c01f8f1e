package com.dz.ms.sales.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 活动基础信息
 *
 * @author: klc
 * @date: 2024/01/26 21:20
 */
@Getter
@Setter
@NoArgsConstructor
@Table("活动用户领券记录")
@TableName(value = "campaign_receive_coupon")
public class CampaignReceiveCoupon implements Serializable {
    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "id")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Columns(type = ColumnType.VARCHAR, length = 50, isNull = true, comment = "活动标识", unionKeys = {"campaignCode", "cardNo"})
    private String campaignCode;

    @Columns(type = ColumnType.VARCHAR, length = 50, isNull = false, comment = "unionid", isIndex = true)
    private String unionid;
    @Columns(type = ColumnType.VARCHAR, length = 50, isNull = false, comment = "会员卡号")
    private String cardNo;
    @Columns(type = ColumnType.VARCHAR, length = 50, isNull = true, defaultValue = "", comment = "")
    private String receiveType;

    @Columns(type = ColumnType.VARCHAR, length = 255, isNull = true, defaultValue = "", comment = "券code", isIndex = true)
    private String couponCode;
    @Columns(type = ColumnType.VARCHAR, length = 255, isNull = true, comment = "券名称")
    private String couponName;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "是否已弹框 0未弹 1已弹")
    private Integer popFlag;

    @Columns(type = ColumnType.TINYINT, length = 0, isNull = false, defaultValue = "0", comment = "是否删除 0-未删除 1-已删除")
    @TableLogic
    private Integer isDeleted;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;

}
