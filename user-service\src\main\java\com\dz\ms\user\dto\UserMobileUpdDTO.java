package com.dz.ms.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 更新会员手机号
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "更新会员手机号")
public class UserMobileUpdDTO {

    @ApiModelProperty(value = "会员编号")
    private String memberCode;
    @ApiModelProperty(value = "旧手机号")
    private String oleMobile;
    @ApiModelProperty(value = "新手机号")
    private String newMobile;
    @ApiModelProperty(value = "验证码")
    private String smsCode;
    
}
