package com.dz.ms.user.dto.dkeyam.res;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 用户信息
 * @author: fei
 * @date: 2025/01/22 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MacRes implements Serializable {

    private static final long serialVersionUID = 1L;

    private String macAddress;
    private Integer type;
    private String description;
    private Enablement enablement;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Enablement {
        
        private Boolean enabled;
        private Long startTime;
        private Long endTime;
        
    }
}
