<template>
  <!-- :label="props.label" -->
  <a-form-item label="" :name="props.name" :rules="props.rules" class="ShelfStatusSelect">
    <a-select v-model:value="thisFields.value" :filterOption="true" :allowClear="true" :maxTagCount="1" :placeholder="props.label" @change="thisMethods.change" :options="thisFields.options" v-bind="$attrs" />
  </a-form-item>
</template>

<script setup>
import { onMounted, reactive, watch } from 'vue'
import { SHELF_STATUS_ARR } from '@/utils/constants.js'

const props = defineProps({
  label: {
    type: String,
    default: '货架上架状态'
  },
  name: {
    type: String,
    default: 'shelfState'
  },
  rules: {
    type: Object,
    default: () => undefined
  },
  modelValue: {
    type: [Array, Number, String],
    default: () => []
  }
})
const emits = defineEmits(['change', 'update:modelValue'])

const attrs = useAttrs()
const thisFields = reactive({
  value: attrs.mode === 'multiple' ? [] : '',
  options: SHELF_STATUS_ARR
})
const thisMethods = {
  setValue() {
    thisFields.value = props.modelValue
  },
  change(e) {
    emits('update:modelValue', e)
    emits('change', e)
  }
}

onMounted(() => thisMethods.setValue())
watch(() => props.modelValue, () => thisMethods.setValue())
</script>

<style lang="scss" scoped>
.ShelfStatusSelect {
  .ant-select {
    width: 202px;
  }
}
</style>
