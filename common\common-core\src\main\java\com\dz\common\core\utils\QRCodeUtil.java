package com.dz.common.core.utils;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartFile;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/5/22
 */
@Slf4j
public class QRCodeUtil {

    //设置默认参数，可以根据需要进行修改

    /**
     * 默认是黑色
     */
    private static final int QRCOLOR = 0xFF000000;
    /**
     * 背景颜色
     */
    private static final int BGWHITE = 0xFFFFFFFF;
    /**
     * 二维码宽
     */
    private static final int WIDTH = 400;
    /**
     * 二维码高
     */
    private static final int HEIGHT = 400;

    //设置高度常量
    private static final int SCRIPT_HIGH = -70;

    //设置字体宽度
    private static final int strWidth = 130;


    /**
     * 用于设置QR二维码参数
     * com.google.zxing.EncodeHintType：编码提示类型,枚举类型
     * EncodeHintType.CHARACTER_SET：设置字符编码类型
     * EncodeHintType.ERROR_CORRECTION：设置误差校正
     * ErrorCorrectionLevel：误差校正等级，L = ~7% correction、M = ~15% correction、Q = ~25% correction、H = ~30% correction
     * 不设置时，默认为 L 等级，等级不一样，生成的图案不同，但扫描的结果是一样的
     * EncodeHintType.MARGIN：设置二维码边距，单位像素，值越小，二维码距离四周越近
     */
    private static Map<EncodeHintType, Object> hints = new HashMap<EncodeHintType, Object>() {
        private static final long serialVersionUID = 1L;

        {
            put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);// 设置QR二维码的纠错级别（H为最高级别）具体级别信息
            put(EncodeHintType.CHARACTER_SET, "utf-8");// 设置编码方式
            put(EncodeHintType.MARGIN, 0);
        }
    };


    /**
     * 生成二维码和附带字体参数
     */
    private static BufferedImage createQr(String code) throws WriterException {


        String qrurl = code;

        /**
         * MultiFormatWriter:多格式写入，这是一个工厂类，里面重载了两个 encode 方法，用于写入条形码或二维码
         *      encode(String contents,BarcodeFormat format,int width, int height,Map<EncodeHintType,?> hints)
         *      contents:条形码/二维码内容
         *      format：编码类型，如 条形码，二维码 等
         *      width：码的宽度
         *      height：码的高度
         *      hints：码内容的编码类型
         * BarcodeFormat：枚举该程序包已知的条形码格式，即创建何种码，如 1 维的条形码，2 维的二维码 等
         * BitMatrix：位(比特)矩阵或叫2D矩阵，也就是需要的二维码
         */
        MultiFormatWriter multiFormatWriter = new MultiFormatWriter();

        /**参数顺序分别为：编码内容，编码类型，生成图片宽度，生成图片高度，设置参数
         * BitMatrix 的 get(int x, int y) 获取比特矩阵内容，指定位置有值，则返回true，将其设置为前景色，否则设置为背景色
         * BufferedImage 的 setRGB(int x, int y, int rgb) 方法设置图像像素
         *      x：像素位置的横坐标，即列
         *      y：像素位置的纵坐标，即行
         *      rgb：像素的值，采用 16 进制,如 0xFFFFFF 白色
         */
        BitMatrix bm = multiFormatWriter.encode(qrurl, BarcodeFormat.QR_CODE, WIDTH, HEIGHT, hints);
        //创建一个图片缓冲区存放二维码图片
        BufferedImage image = new BufferedImage(WIDTH, HEIGHT, BufferedImage.TYPE_INT_RGB);
        // 开始利用二维码数据创建Bitmap图片，分别设为黑（0xFFFFFFFF）白（0xFF000000）两色
        for (int x = 0; x < WIDTH; x++) {
            for (int y = 0; y < HEIGHT; y++) {
                image.setRGB(x, y, bm.get(x, y) ? QRCOLOR : BGWHITE);
            }
        }
        int height = image.getHeight();

        // ------------------------------------------自定义文本描述-------------------------------------------------

        //在内存创建图片缓冲区  这里设置画板的宽高和类型
        BufferedImage outImage = new BufferedImage(600, 600, BufferedImage.TYPE_4BYTE_ABGR);

        //创建画布
        Graphics2D outg = outImage.createGraphics();

        // 在画布上画上二维码  X轴Y轴，宽度高度
        outg.drawImage(image, 100, 50, image.getWidth(), image.getHeight(), null);

        // 画文字到新的面板
        outg.setColor(Color.BLACK);
        // 字体、字型、字号
//        outg.setFont(font);

        outg.dispose();
        outImage.flush();
        image = outImage;

        image.flush();
        return image;
    }


//    public static MultipartFile drawLogoQRCode(String code) {
//
//        try {
////            Font fontChinese = new Font("方正小标宋", Font.BOLD, 12);
////            BufferedImage image = QRCodeUtil.createQr(code, fontChinese);
//            BufferedImage image = QRCodeUtil.createQr(code);
//
//            ByteArrayOutputStream os = new ByteArrayOutputStream();
//            //把BufferedImage写入ByteArrayOutputStream
//            ImageIO.write(image, "jpg", os);
//            //ByteArrayOutputStream转成InputStream
//            InputStream input = new ByteArrayInputStream(os.toByteArray());
//            //InputStream转成MultipartFile
//            return new MockMultipartFile("file", "file.jpg", "text/plain", input);
//        } catch (Exception e) {
//            log.error("二维码写入IO流异常", e);
//        }
//        return null;
//    }


}
