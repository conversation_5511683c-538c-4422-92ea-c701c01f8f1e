package com.dz.ms.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.user.entity.AppointmentRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface AppointmentRecordMapper extends BaseMapper<AppointmentRecord> {
    @Select("SELECT * FROM appointment_record WHERE appointment_date = #{appointmentDate}")
    List<AppointmentRecord> findByAppointmentDate(String appointmentDate);

    // 新增方法：统计同一场次的已预约人数
    @Select("SELECT COUNT(*) FROM appointment_record WHERE appointment_id = #{appointmentId} AND appointment_date = #{appointmentDate} AND appointment_slot = #{appointmentSlot}")
    int countByAppointmentIdAndDateAndSlot(@Param("appointmentId") Long appointmentId, @Param("appointmentDate") String appointmentDate, @Param("appointmentSlot") String appointmentSlot);

}