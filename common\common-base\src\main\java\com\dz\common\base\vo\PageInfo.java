package com.dz.common.base.vo;

import java.util.List;

/**
 * 分页实体
 * @author: Handy
 * @date: 2019/9/11 23:55
 */
public class PageInfo<E> {

    //当前页
    private long pageNum = 1;
    //每页数量
    private long pageSize = 20;
    //总数量
    private long count;

    //是否有下一页
    private boolean hasNextPage;
    //数据列表
    private List<E> list;

    public PageInfo() {}

    public PageInfo(long pageNum, long pageSize, long count, List<E> list) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.count = count;
        this.list = list;
    }

    public PageInfo(long pageNum, long pageSize, boolean hasNextPage, List<E> list) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.hasNextPage = hasNextPage;
        this.list = list;
    }

    public long getPageNum() {
        return pageNum;
    }

    public void setPageNum(long pageNum) {
        this.pageNum = pageNum;
    }

    public long getPageSize() {
        return pageSize;
    }

    public void setPageSize(long pageSize) {
        this.pageSize = pageSize;
    }

    public long getCount() {
        return count;
    }

    public void setCount(long count) {
        this.count = count;
    }

    public List<E> getList() {
        return list;
    }

    public void setList(List<E> list) {
        this.list = list;
    }

    public boolean isHasNextPage() {
        return hasNextPage;
    }

    public void setHasNextPage(boolean hasNextPage) {
        this.hasNextPage = hasNextPage;
    }
}

