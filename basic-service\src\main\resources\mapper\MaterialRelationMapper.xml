<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.basic.mapper.MaterialRelationMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    material_id,
  	    relation_type,
  	    relation_id,
  	    tenant_id
    </sql>

	<!-- 批量插入 -->
	<insert id="insertBatch">
		insert into material_relation(material_id, relation_type, relation_id, tenant_id)
		values
		<foreach collection="list" item="relation" separator=",">
			(#{relation.materialId}, #{relation.relationType}, #{relation.relationId}, #{relation.tenantId})
		</foreach>
	</insert>

</mapper>
