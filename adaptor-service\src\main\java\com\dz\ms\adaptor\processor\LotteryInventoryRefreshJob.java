package com.dz.ms.adaptor.processor;

import com.alibaba.fastjson.JSON;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.fegin.sales.LotteryFeignClient;
import com.dz.common.core.threadlocal.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import javax.annotation.Resource;

/**
 * 刷新抽奖库存job
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/6/11
 */
@Slf4j
@Component
public class LotteryInventoryRefreshJob implements BasicProcessor {
    @Resource
    private LotteryFeignClient lotteryFeignClient;

    @Override
    public ProcessResult process(TaskContext context) throws Exception {
        log.info(JSON.toJSONString("刷新抽奖库存job，" + context.getJobParams()));
        SecurityContext.setUser(new CurrentUserDTO(1L, 1L));
        lotteryFeignClient.refreshLotteryInventory(context.getJobParams());
        log.info("刷新抽奖库存任务处理完成");
        return new ProcessResult(true, "success");
    }
}