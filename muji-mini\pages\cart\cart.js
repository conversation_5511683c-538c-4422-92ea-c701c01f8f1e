const app = getApp()

import {
  getUserPoint,
  getCartList,
  postCartAdd,
  delCartItem,
  postCartEdit,
  getCartCheckAll,
  delCartDisabled,
  postOrderPreview
} from '../../api/index.js'

Page({
  data: {
    loading: false,

    menuButtonTop: app.globalData.menuButtonTop,
    menuButtonBottom: app.globalData.menuButtonBottom,
    currentPoint: '',
    cart: {
      carts: [],
    },
    allSelectedState: false,

    err: {
      show: false,
    }
  },
  onLoad(options) { },
  onShow() {
    this.handleGetData()
    this.getPoint();
  },

  // 获取购物车列表
  handleGetData() {
    // this.setData({
    //   loading: true
    // })
    getCartList().then(res => {
      // debugger
      this.setData({
        cart: res.data
      })
    }).finally(() => {
      // this.setData({
      //   loading: false
      // })
    })
  },

  // 删除商品
  onCloseSwipeCell(e) {
    const _e = {
      detail: e.target.dataset.item
    }
    wx.$mp.track({
      event: 'cart_delete',
      props: {
        productId: _e.detail.productId,
        productName: _e.detail.productName,
      }
    })
    this.onTapCartDel(_e, true)
  },

  // 删除商品
  onTapCartDel(e, forceDel) {
    const detail = e.detail
    if (detail.number === 1 || forceDel) {
      const ids = detail.id
      delCartItem(ids).then(res => {
        this.handleGetData()
      })
    } else {
      const data = {
        ...detail,
        number: detail.number - 1
      }
      this.handleEditCart(data)
    }
  },

  // 编辑商品
  handleEditCart(data) {
    // this.setData({
    //   loading: true
    // })
    postCartEdit(data).then(res => {
      this.handleGetData()
    }).catch(err => {
      // debugger
      if (err.code === 1001) {
        this.setData({
          err: {
            ...err,
            show: true
          }
        })
      }
    }).finally(() => {
      this.handleGetData()
      // this.setData({
      //   loading: false
      // })
    })
  },

  // 添加商品
  onTapCartAdd: app.debounce(function (e) {
    // this.setData({
    //   loading: true
    // })
    const detail = e.detail
    const data = {
      ...detail,
      number: 1,
    }
    postCartAdd(data).then(res => {
      this.handleGetData()
    }).catch(err => {
      // debugger
      if (err.code === 1001) {
        this.setData({
          err: {
            ...err,
            show: true
          }
        })
      }
    }).finally(() => {
      // this.setData({
      //   loading: false
      // })
    })
  }),

  onCartConfirm(e) {
    const detail = e.detail
    this.handleEditCart(detail)
  },

  onTapCartDetail(e) {
    const detail = e.detail
    const detailId = detail.shelfProductId
    const pages = getCurrentPages()
    const prePage = pages?.[pages.length - 2]
    const prePagePath = prePage?.path
    const prePageId = +prePage?.options?.id
    // debugger
    if (prePagePath?.includes('integralDetails') && prePageId === detailId) {
      wx.$mp.navigateBack()
    } else {
      wx.$mp.navigateTo({
        url: `/pages/integralDetails/integralDetails?productId=${detailId}`
      })
    }
  },

  // 选择商品
  onTapCheck(e) {
    const detail = e.target.dataset.item
    const data = {
      ...detail,
      checked: detail.checked ? 0 : 1,
    }
    this.handleEditCart(data)
  },

  // 全选商品
  onTapCheckAll() {
    const data = {
      status: this.data.cart.checkAll ? 0 : 1,
    }
    wx.$mp.track({
      event: 'cart_check_all'
    })
    getCartCheckAll(data).then(res => {
      this.handleGetData()
    })
  },

  // 清空失效购物车
  onTapCartClear() {
    wx.$mp.track({
      event: 'cart_clear_all'
    })
    delCartDisabled().then(res => {
      this.handleGetData()
    })
  },

  // 立即兑换
  onClickSubmit() {
    const productList = this.data?.cart?.validCarts?.filter(v => v.checked) || []
    if (!productList?.length) {
      return
    }
    wx.$mp.track({
      event: 'cart_exchange'
    })

    // this.setData({
    //   loading: true
    // })

    const data = {
      source: 1, // 跳转来源 0-立即购买 1-购物车
      productList: productList
    }

    postOrderPreview(data).then(res => {
      // todo
      wx.$mp.navigateTo({
        url: '/pages/submitOrder/submitOrder?source=1',
        success: function (result) {
          // 通过eventChannel向被打开页面传送数据
          result.eventChannel.emit('exchangePreview', {
            data: res.data
          })
        }
      })
    }).catch(err => {
      // debugger
      if (err.code === 1003) {
        this.setData({
          err: {
            ...err,
            show: true
          }
        })
      }
    }).finally(() => {
      // this.setData({
      //   loading: false
      // })
    })
  },

  async getPoint() {
    const res = await getUserPoint();
    if (res.code === 0) {
      this.setData({
        currentPoint: res.data.pointsNum,
      })
    }
  },

  onPopConfirm() {
    this.setData({
      err: {
        ...this.data.err,
        show: false
      }
    })
  },
})
