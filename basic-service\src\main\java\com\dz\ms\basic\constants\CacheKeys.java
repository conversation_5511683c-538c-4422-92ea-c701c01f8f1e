package com.dz.ms.basic.constants;

public class CacheKeys {

    /** 根据appid secret获取的accesstoken */
    public static final String ACCESSTOKEN_BY_SECRET = "mp:accesstoken:bysecret:";
    public static final String ACCESSTOKEN_BY_SECRET_POST = "mp:accesstoken:bysecretPOST:";
    /** 根据appid获取小程序/公众号简要配置 */
    public static final String MP_CONFIG_SIMPLE = "mp:simpleconfig:";
    /** session key */
    public static final String MP_SESSION_KEY = "mp:sessionkey:";
    /** 企业微信accesstoken */
    public static final String QYWX_ACCESSTOKEN = "qywx:accesstoken:";
    /** 自定义UI配置 */
    public static final String NAVIGATION_CONFIG_DEFAULT = "navigation:config:default:";
    /** 根据消息编码获取订阅消息详情 */
    public static final String SUBSCRIBE_INFO_BYCODE = "subscribe:info:bycode:";
    /** 渠道链接 */
    public static final String CHANNEL_LINK = "channellink:";


    /** 根据ID获取小程序推广渠道参数 */
    public static final String PROMOTION_PARAM_BYID = "promotion:param:id";

    /** 告警配置 */
    public static final String ALARM_CONFIG_LIST = "alarm:config";
    /** 根据租户code获取租户 */
    public static final String TENANT_BY_CODE = "tenant:bycode";
    /** 根据租户ID获取租户 */
    public static final String TENANT_BY_ID = "tenant:byid";
    /** 根据租户ID获取企微配置 */
    public static final String QYWX_CONFIG_BYTENANT = "qywx:config:tenant";
    /** 租户配置 */
    public static final String TENANT_CONFIG_INFO = "tenant:config:info";
    /** 根据ids获取素材 */
    public static final String MATERIAL_LIST_IDS = "material:list:ids";
    /** 小程序自定义模板 */
    public static final String MINIAPP_TEMPLATE = "miniapp:template";
    /** 根据ID获取小程序自定义模板 */
    public static final String MINIAPP_TEMPLATE_BYID = "miniapp:template:byid";
    /** 根据场景获取小程序订阅消息模板ID列表 */
    public static final String SUBSCRIBE_MSGID_BYSCENE = "subscribe:msgid:byscene";
    /** 省份城市列表 */
    public static final String PROVINCE_CITYS = "province:citys";
    /** 弹窗配置 */
    public static final String POPUP_CONFIG = "popup:config";
    /** 默认数据 */
    public static final String DEFAULT_DATA = "default:data";

    /**
     * 小程序用户限制上传数量/周
     */
    public static final String UPLOAD_TIMES = "upload:times:";
}
