package com.dz.ms.basic.utils;

import com.dz.ms.basic.utils.FileUtil;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.OutputStream;
import java.net.URLEncoder;


/**
 * 下载文件
 * 创建人：FH 创建时间：2014年12月23日
 * @version
 */
public class FileDownload {

	/**
	 * @param response 
	 * @param filePath		//文件完整路径(包括文件名和扩展名)
	 * @param fileName		//下载后看到的文件名
	 * @return  文件名
	 */
	public static void fileDownload(final HttpServletResponse response, String filePath, String fileName) throws Exception{
		OutputStream outputStream=null;
			try {
				byte[] data = FileUtil.toByteArray2(filePath);
				fileName = URLEncoder.encode(fileName, "UTF-8");
				response.reset();
				response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
				response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
				response.addHeader("Content-Length", "" + data.length);
				response.setContentType("application/octet-stream;charset=UTF-8");
				outputStream = new BufferedOutputStream(response.getOutputStream());
				outputStream.write(data);
				outputStream.flush();
				outputStream.close();
				response.flushBuffer();
			}catch (Exception e){
				if (null != outputStream){
					outputStream.close();
				}
			}finally {
				if (null != outputStream){
					outputStream.close();
				}
			}


		}

}
