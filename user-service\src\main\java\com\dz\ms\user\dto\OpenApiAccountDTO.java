package com.dz.ms.user.dto;
import java.util.Date;
import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 开放接口认证账号DTO
 * @author: Handy
 * @date:   2023/08/10 23:32
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "开放接口认证账号")
public class OpenApiAccountDTO extends BaseDTO {

    @ApiModelProperty(value = "账号ID")
    private Long id;
    @ApiModelProperty(value = "账号名称")
    private String accountName;
    @ApiModelProperty(value = "认证账号")
    private String appKey;
    @ApiModelProperty(value = "认证账号密钥")
    private String appSecret;
    @ApiModelProperty(value = "账号户状态 0关闭  1开启")
    private Integer state;

}
