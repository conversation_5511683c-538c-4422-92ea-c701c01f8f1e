@import '@/assets/scss/common/index.scss';


body {
  // 设计字体要求：中文-思源黑体（SourceHanSansCN）、英文和数字-品牌字体（MUJIFont2020）、前两者不支持则使用-苹果字体（PingFang SC）
  font-family: SourceHanSansCN, 'MUJIFont2020', 'PingFang SC', '思源黑体', 'Helvetica Neue', Helvetica, 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}


#app {
  height: 100%;
}

ul,
li,
p {
  margin: 0;
  padding: 0;
}

::-webkit-scrollbar {
  /*滚动条整体样式*/
  /*高宽分别对应横竖滚动条的尺寸*/
  width: 5px;
  height: 5px;
  // background: #fff;

  /* 隐藏滚动条 */
}

::-webkit-scrollbar-thumb {
  visibility: hidden;
  /*滚动条里面小方块*/
  border-radius: 5px;
  /*深色模式*/
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: hsl(0, 0%, 90%);

  /*浅色模式*/
  /*-webkit-box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
    background: rgb(245, 245, 245);
    border: 1px solid hsl(0, 0%, 80%);
    border-bottom-width: 2px;*/
}

:hover::-webkit-scrollbar-thumb {
  visibility: visible;
}

.form-top-titles-common {
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  // margin-top: 32px;
  padding-top: 32px;
  margin-bottom: 25px;
}

.form-top-line-common {
  width: 100%;
  height: 0px;
  border-top: 1px solid #F0F0F0;
  margin-bottom: 30px;
}

.global-tip {
  margin: 10px 0;
  color: gray;
  font-size: 12px;
  line-height: 1.3;
  word-wrap: break-word;
}

.global-color {
  color: $primary-color ;
  cursor: pointer;
}