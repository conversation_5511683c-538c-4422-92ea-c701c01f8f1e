package com.dz.ms.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.basic.SubscribeMsgSendDTO;
import com.dz.common.core.enums.SubscribeMsgEnum;
import com.dz.ms.basic.dto.MpMsgPushDTO;
import com.dz.ms.basic.entity.MpMsgPush;

import java.util.List;

/**
 * 小程序/公众号模板消息推送任务接口
 * @author: Handy
 * @date:   2023/07/07 14:22
 */
public interface MpMsgPushService extends IService<MpMsgPush> {

	/**
     * 分页查询小程序/公众号模板消息推送任务
     * @param param
     * @return PageInfo<MpMsgPushDTO>
     */
    public PageInfo<MpMsgPushDTO> getMpMsgPushList(MpMsgPushDTO param);

    /**
     * 根据ID查询小程序/公众号模板消息推送任务
     * @param id
     * @return MpMsgPushDTO
     */
    public MpMsgPushDTO getMpMsgPushById(Long id);

    /**
     * 保存小程序/公众号模板消息推送任务
     * @param param
     * @return Long
     */
    public Long saveMpMsgPush(MpMsgPushDTO param);

    /**
     * 根据ID删除小程序/公众号模板消息推送任务
     * @param param
     */
    public void deleteMpMsgPushById(IdCodeDTO param);

    /**
     * 活动开始提醒消息发送
     * @param tenantId
     * @param content
     * @param path
     * @param uids
     */
    public void activityStartMsgPush(SubscribeMsgEnum enums, Long tenantId, String[] content, String path);


    /**
     * 根据订阅记录发送模板消息
     *
     * @param enums
     * @param tenantId
     * @param content
     * @param path
     * @param uids
     */
    public void msgPushBySubscribeLog(SubscribeMsgEnum enums, Long tenantId, String[] content, String path,List<Long> uids);

    /**
     * 发送订阅消息
     * @param tenantId
     */
    void sendSubscribeMsg(SubscribeMsgSendDTO param, Long tenantId);

}
