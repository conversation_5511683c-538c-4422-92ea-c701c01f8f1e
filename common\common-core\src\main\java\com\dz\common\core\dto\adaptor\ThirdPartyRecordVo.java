package com.dz.common.core.dto.adaptor;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 请求三方接口的记录
 */
@Data
@NoArgsConstructor
public class ThirdPartyRecordVo {

    @ApiModelProperty(value = "接口名称")
    private String apiDesc;

    @ApiModelProperty(value = "接口路径")
    private String apiUrl;

    @ApiModelProperty(value = "参数")
    private String param;

    @ApiModelProperty(value = "返回结果")
    private String result;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "状态 0失败 1成功")
    private Integer status;

    @ApiModelProperty(value = "失败原因")
    private String failDesc;

    @ApiModelProperty(value = "请求耗时")
    private Long requestTime;

    @ApiModelProperty(value = "分表num")
    private Integer num;
}
