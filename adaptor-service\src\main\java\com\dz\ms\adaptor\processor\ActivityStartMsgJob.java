package com.dz.ms.adaptor.processor;

import com.dz.common.core.fegin.basic.MpMsgPushFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 活动开始提醒JOB
 * @Author: Handy
 * @Date: 2023/8/5 14:11
 */
@Slf4j
@Component
public class ActivityStartMsgJob implements BasicProcessor {

    @Resource
    private MpMsgPushFeignClient mpMsgPushFeignClient;
    @Override
    public ProcessResult process(TaskContext context) {
        String jobParams = context.getJobParams();
        if(Objects.isNull(jobParams)){
            return new ProcessResult(true, "param null");
        }
        log.info("参数是否存在值: {}", jobParams);
        String[] strs = jobParams.split(",");
        if(strs.length < 6) {
            return new ProcessResult(true, "param invalid");
        }
        Long tenantId = NumberUtils.toLong(strs[0]);
        mpMsgPushFeignClient.activityStartMsgPush(tenantId,new String[]{strs[1],strs[2],strs[3],strs[4]},strs[5]);
        log.info("发送活动开始提醒任务处理完成");
        return new ProcessResult(true, "success");
    }
}
