.indexbar-list {
  width: 100%;
  position: relative;
  // border: 10rpx solid green;
  box-sizing: border-box;

  .letter-box {
    position: fixed;
    top: 471rpx;
    right: 40rpx;

    .letter-item {
      width: 30rpx;
      height: 36rpx;
      font-family: MUJIFont2020,
        MUJIFont2020;
      font-weight: 400;
      font-size: 20rpx;
      color: #888888;
      line-height: 30rpx;
      text-align: center;
      font-style: normal;
    }

    .active {
      color: #7F0019;
    }
  }

  .city-list {
    padding-bottom: 60rpx;
    // border: 10rpx solid red;

    .letter-name {
      height: 36rpx;
      font-family: PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 24rpx;
      color: #888888;
      line-height: 36rpx;
      text-align: left;
      font-style: normal;
      margin-top: 40rpx;
    }

    .city-item {
      height: 40rpx;
      width: 620rpx;
      font-family: SourceHanSansCN,
        SourceHanSansCN;
      font-weight: 400;
      font-size: 28rpx;
      color: #3C3C43;
      line-height: 40rpx;
      letter-spacing: 1px;
      text-align: left;
      font-style: normal;
      padding: 30rpx 0;
      border-bottom: 1rpx solid #EEEEEE;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .no-data {
      display: flex;
      align-items: center;
      flex-direction: column;
      margin-top: 238rpx;

      image {
        width: 124rpx;
        height: 124rpx;
      }

      .no-data-txt {
        margin-top: 30rpx;
        height: 24rpx;
        font-family: SourceHanSansCN,
          SourceHanSansCN;
        font-weight: 400;
        font-size: 24rpx;
        color: #888888;
        line-height: 36rpx;
        text-align: center;
        font-style: normal;
      }
    }
  }
}
