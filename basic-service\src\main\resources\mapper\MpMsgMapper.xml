<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.basic.mapper.MpMsgMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
        id,
  	    msg_type,
  	    template_code,
  	    template_id,
  	    template_name,
  	    trigger_type,
  	    keyword,
  	    page_path,
  	    state,
  	    tenant_id,
  	    creator,
  	    created,
  	    modifier,
  	    modified
    </sql>

    <!-- 根据场景获取小程序订阅消息ID列表 -->
    <select id="getSubscribeMsgIds" resultType="java.lang.String">
        select
        template_id
        from mp_msg
        where scene = #{scene}
        AND msg_type = 1
        AND is_deleted = 0
        AND tenant_id = #{tenantId}
    </select>

	<!-- 根据场景获取小程序订阅消息ID列表 -->
	<select id="getSubscribeMsgByCode" resultType="com.dz.ms.basic.entity.MaterialInfo">
		select
		<include refid="Base_Column_List" />
        from mp_msg
		where template_code = #{templateCode}
        AND msg_type = 1
        AND is_deleted = 0
        AND tenant_id = #{tenantId}
    </select>

</mapper>
