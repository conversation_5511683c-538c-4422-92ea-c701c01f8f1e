package com.dz.ms.sales.dto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/16
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "NPS问卷问题选项")
public class NpsQuestionOptionDTO {

    @ApiModelProperty(value = "选项ID")
    private Long id;
    @ApiModelProperty(value = "问卷ID")
    private Long npsId;
    @ApiModelProperty(value = "问卷问题ID")
    private Long npsQuestionId;
    @ApiModelProperty(value = "选项名称")
    private String title;
    @ApiModelProperty(value = "选项排序")
    private Integer sort;

}
