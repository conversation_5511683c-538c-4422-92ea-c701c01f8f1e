package com.dz.ms.product.dto;

import com.dz.common.base.dto.BaseDTO;
import com.dz.common.core.dto.product.ShelfActivityDTO;
import com.dz.common.core.dto.user.CrowdDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 商品货架DTO
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:27
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "商品货架")
public class ShelfDTO extends BaseDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "货架名称")
    private String name;
    @ApiModelProperty(value = "货架上架类型 1永久上架 2时间段")
    private Integer onType;
    @ApiModelProperty(value = "上架开始时间")
    private Date onStartTime;
    @ApiModelProperty(value = "上架结束时间-下架")
    private Date onEndTime;
    @ApiModelProperty(value = "优先级 值越大显示越优先")
    private Integer priority;
    @ApiModelProperty(value = "是否根据展示 0不限制 1限制")
    private Integer limitShow;
    @ApiModelProperty(value = "人群包ID")
    private Long groupId;
    @ApiModelProperty(value = "人群包名称")
    private String groupName;
    @ApiModelProperty(value = "货架状态 0禁用 1启用")
    private Integer state;
    @ApiModelProperty(value = "库存售罄组件配置")
    private String content;
    @ApiModelProperty(value = "兑礼人数")
    private Integer exchangePeople;
    @ApiModelProperty(value = "兑礼订单数")
    private Integer exchangeOrder;
    @ApiModelProperty(value = "总兑礼件数")
    private Integer exchangeNum;
    @ApiModelProperty(value = "总兑礼积分")
    private Integer exchangePoint;
    @ApiModelProperty(value = "人均兑礼件数")
    private Integer exchangeAvgNum;
    @ApiModelProperty(value = "货架商品id列表")
    private List<Long> productIdList;
    @ApiModelProperty(value = "货架状态 1未开始/2上架中/3已结束")
    private Integer shelfState;
    @ApiModelProperty(value = "上架商品数量")
    private Integer productSum;
    @ApiModelProperty(value = "库存较少商品数量")
    private Integer inventoryLessSum;
    @ApiModelProperty(value = "库存售罄商品数量")
    private Integer inventorySellOutSum;
    @ApiModelProperty(value = "货架营销活动列表")
    private List<ShelfActivityDTO> shelfActivityList;
    @ApiModelProperty(value = "人群包")
    private CrowdDTO crowdDTO;
    
    
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "修改时间")
    private Date modified;
    @ApiModelProperty(value = "修改人")
    private Long modifier;
    @ApiModelProperty(value = "0正常 1删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "登录用户适用人群包id列表")
    private List<Long> groupIdList;

}
