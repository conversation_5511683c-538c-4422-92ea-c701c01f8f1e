
// 所有的命名必须全局唯一
import service from '@/utils/request.js'

//



// 列表
export function taskList(data) {
    return service({
        url: '/crm/sales/interaction/task/list',
        method: 'post',
        data
    })
}
// 列表 新增
export function taskAdd(data) {
    return service({
        url: '/crm/sales/interaction/task/add',
        method: 'post',
        data
    })
}

// 列表编辑
export function taskUpdate(data) {
    return service({
        url: '/crm/sales/interaction/task/udpate',
        method: 'post',
        data
    })
}
// 列表 删除
export function taskDelete(data) {
    return service({
        url: '/crm/sales/interaction/task/delete',
        method: 'post',
        data
    })
}

export function taskInfo(data) {
    return service({
        url: '/crm/sales/interaction/task/info',
        method: 'get',
        data
    })
}
//修改状态

export function interactiontaskstatus(data) {
    return service({
        url: '/crm/sales/interaction/task/status',
        method: 'post',
        data
    })
}