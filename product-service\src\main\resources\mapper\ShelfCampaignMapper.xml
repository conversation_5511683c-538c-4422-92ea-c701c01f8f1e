<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.product.mapper.ShelfCampaignMapper">

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
        id,
  	    `name`,
        `type`,
  	    on_type,
  	    on_start_time,
  	    on_end_time,
  	    shelf_id,
  	    `state`,
  	    is_deleted,
  	    tenant_id,
  	    creator,
  	    created,
  	    modified,
  	    modifier
    </sql>

    <!-- 查询示例 -->
    <select id="selectDemo" parameterType="java.lang.Long" resultType="com.dz.ms.product.entity.ShelfCampaign">
        select
        <include refid="Base_Column_List"/>
        from shelf_campaign
        where id = #{id,jdbcType=BIGINT}
        AND tenant_id = #{tenantId}
    </select>

    <select id="selPageList" resultType="com.dz.ms.product.entity.ShelfCampaign">
        select
        <include refid="Base_Column_List"/>
        from shelf_campaign
        <where>
            is_deleted = 0
            <if test=" param.name != null and param.name != ''  ">
                and `name` like CONCAT('%', #{param.name},'%')
            </if>
            <if test="param.onStartTime != null">
                AND on_start_time <![CDATA[ >= ]]> #{param.onStartTime}
            </if>
            <if test="param.onEndTime != null">
                AND on_end_time <![CDATA[ <= ]]> #{param.onEndTime}
            </if>
            <if test="param.campaignState != null and param.campaignState == 1">
                and on_start_time <![CDATA[ > ]]> now()
            </if>
            <if test="param.campaignState != null and param.campaignState == 2">
                and on_start_time <![CDATA[ <= ]]> now() and on_end_time <![CDATA[ >= ]]> now()
            </if>
            <if test="param.campaignState != null and param.campaignState == 3">
                and on_end_time <![CDATA[ < ]]> now()
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectListByParam" resultType="com.dz.ms.product.entity.ShelfCampaign">
        select
        <include refid="Base_Column_List"/>
        from shelf_campaign
        <where>
            is_deleted = 0
            <if test=" param.name != null and param.name != ''  ">
                and `name` like CONCAT('%', #{param.name},'%')
            </if>
            <if test="param.onStartTime != null">
                AND on_start_time <![CDATA[ >= ]]> #{param.onStartTime}
            </if>
            <if test="param.onEndTime != null">
                AND on_end_time <![CDATA[ <= ]]> #{param.onEndTime}
            </if>
            <if test="param.campaignState != null and param.campaignState == 1">
                and on_start_time <![CDATA[ > ]]> now()
            </if>
            <if test="param.campaignState != null and param.campaignState == 2">
                and on_start_time <![CDATA[ <= ]]> now() and on_end_time <![CDATA[ >= ]]> now()
            </if>
            <if test="param.campaignState != null and param.campaignState == 3">
                and on_end_time <![CDATA[ < ]]> now()
            </if>
        </where>
    </select>

    <update id="updShelfIdIntoNull">
        update shelf_campaign
        set shelf_id = null
        where shelf_id = #{shelfId}
    </update>
</mapper>
