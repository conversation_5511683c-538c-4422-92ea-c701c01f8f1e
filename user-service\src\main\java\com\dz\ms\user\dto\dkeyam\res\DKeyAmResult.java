package com.dz.ms.user.dto.dkeyam.res;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;


/**
 * 统一接口返回类型实体
 * @author: fei
 * @date: 2025/01/22 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class DKeyAmResult<T> {

    /** 返回业务数据 */
    private T data;
    /** 返回结果编码 默认0 */
    private Integer errorCode;
    private Long errorId;
    /** 是否成功 */
    private Boolean success;
    /** 返回结果提示 默认 成功 */
    private String message;
    private Long requestId;

    public DKeyAmResult(DKeyAmResult<String> res, Class<T> tClass) {
        this.errorCode = res.errorCode;
        this.errorId = res.errorId;
        this.success = res.success;
        this.message = res.message;
        this.requestId = res.requestId;
        if(res.success && StringUtils.isNotBlank(res.getData())){
            this.data = JSONObject.toJavaObject(JSONObject.parseObject(res.getData()),tClass);
        }
    }
    public DKeyAmResult(DKeyAmResult<String> res) {
        this.errorCode = res.errorCode;
        this.errorId = res.errorId;
        this.success = res.success;
        this.message = res.message;
        this.requestId = res.requestId;
    }

    public boolean isSuccess() {
        return null != this.errorCode && this.errorCode.equals(0) && this.success;
    }

    public boolean isFail() {
        return !isSuccess();
    }

}
