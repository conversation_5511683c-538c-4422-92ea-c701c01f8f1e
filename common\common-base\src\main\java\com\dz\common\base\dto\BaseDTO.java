package com.dz.common.base.dto;

import lombok.ToString;

/**
 * DTO基类
 * @author: Handy
 * @date: 2019/9/11 23:55
 */
@ToString
public class BaseDTO {

    //当前页
    private Integer pageNum = 1;
    //每页数量
    private Integer pageSize = 10;

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public BaseDTO() {}

    public BaseDTO(Integer pageNum, Integer pageSize) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
    }

}
