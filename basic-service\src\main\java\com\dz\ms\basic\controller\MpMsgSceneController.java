package com.dz.ms.basic.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.dto.MpMsgSceneDTO;
import com.dz.ms.basic.dto.MpMsgSceneRelationDTO;
import com.dz.ms.basic.dto.MpMsgSubscribeDTO;
import com.dz.ms.basic.entity.MpMsgSubscribe;
import com.dz.ms.basic.service.MpMsgSceneService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Api(tags="小程序订阅消息场景")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class MpMsgSceneController  {

    @Resource
    private MpMsgSceneService mpMsgSceneService;

    /**
     * 分页查询小程序订阅消息场景
     * @param param
     * @return result<PageInfo<MpMsgSceneDTO>>
     */
    @ApiOperation("分页查询小程序订阅消息场景")
	@GetMapping(value = "/crm/subscribe_msg_scene/list")
    public Result<PageInfo<MpMsgSceneDTO>> getMpMsgSceneList(@ModelAttribute MpMsgSceneDTO param) {
        Result<PageInfo<MpMsgSceneDTO>> result = new Result<>();
		PageInfo<MpMsgSceneDTO> page = mpMsgSceneService.getMpMsgSceneList(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询小程序订阅消息场景
     * @param id
     * @return result<MpMsgSceneDTO>
     */
    @ApiOperation("根据ID查询小程序订阅消息场景")
	@GetMapping(value = "/subscribe_msg_scene/info")
    public Result<MpMsgSceneDTO> getMpMsgSceneById(@RequestParam("id") Long id) {
        Result<MpMsgSceneDTO> result = new Result<>();
        MpMsgSceneDTO mpMsgScene = mpMsgSceneService.getMpMsgSceneById(id);
        result.setData(mpMsgScene);
        return result;
    }

    /**
     * 新增小程序订阅消息场景
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增小程序订阅消息场景",type = LogType.OPERATELOG)
    @ApiOperation("新增小程序订阅消息场景")
	@PostMapping(value = "/crm/subscribe_msg_scene/add")
    public Result<Long> addMpMsgScene(@RequestBody MpMsgSceneDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,true);
        Long id = mpMsgSceneService.saveMpMsgScene(param);
        result.setData(id);
        return result;
    }

    /**
    * 更新小程序订阅消息场景
    * @param param
    * @return result<Object>
    */
    @SysLog(value = "更新小程序订阅消息场景",type = LogType.OPERATELOG)
    @ApiOperation("更新小程序订阅消息场景")
    @PostMapping(value = "/crm/subscribe_msg_scene/update")
    public Result<Long> updateMpMsgScene(@RequestBody MpMsgSceneDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,false);
        mpMsgSceneService.saveMpMsgScene(param);
        result.setData(param.getId());
        return result;
    }

    /**
    * 验证保存参数
    * @param param
    */
    private void validationSaveParam(MpMsgSceneDTO param,boolean isAdd) {
        if(isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if(!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
        if(StringUtils.isEmpty(param.getScene())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "场景编码不能为空");
        }
        if(StringUtils.isEmpty(param.getSceneName())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "场景名称不能为空");
        }
        if(!CollectionUtils.isEmpty(param.getRelationList())) {
            for (MpMsgSceneRelationDTO mpMsgSceneRelation : param.getRelationList()) {
                if(null == mpMsgSceneRelation.getMsgId()) {
                    throw new BusinessException(ErrorCode.BAD_REQUEST, "关联订阅消息模板ID不能为空");
                }
            }
        }
    }

	/**
     * 根据ID删除小程序订阅消息场景
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除小程序订阅消息场景")
	@PostMapping(value = "/crm/subscribe_msg_scene/delete")
    public Result<Boolean> deleteMpMsgSceneById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        mpMsgSceneService.deleteMpMsgSceneById(param);
        result.setData(true);
        return result;
    }

    @ApiOperation("根据场景获取小程序订阅消息模板列表")
    @GetMapping(value = "/app/subscribe_msg/ids_by_scene")
    public Result<List<MpMsgSubscribeDTO>> getSubscribeMsgIdsByScene(@RequestParam("scene")String scene) {
        Result<List<MpMsgSubscribeDTO>> result = new Result<>();
        result.setData(mpMsgSceneService.getSubscribeMsgIdsByScene(scene, SecurityContext.getUser().getTenantId()));
        return result;
    }

    @ApiOperation("根据模版ID获取小程序订阅消息模板列表")
    @GetMapping(value = "/app/subscribe_msg/ids")
    public Result<List<MpMsgSubscribeDTO>> getSubscribeMsgIds(@RequestParam("templateIds")String templateIds) {
        Result<List<MpMsgSubscribeDTO>> result = new Result<>();
        //数组转List
        String[] templateIdsArr = templateIds.split(",");
        List<String> templateIdsList = Arrays.asList(templateIdsArr);
        result.setData(mpMsgSceneService.getSubscribeMsgIds(templateIdsList, SecurityContext.getUser().getTenantId()));
        return result;
    }

    @ApiOperation("添加小程序订阅消息订阅记录")
    @PostMapping(value = "/app/subscribe_msg/record_add")
    public Result<Boolean> addSubscribeMsgRecord(@RequestBody List<String> TemplateIds) {
        Result<Boolean> result = new Result<>();
        if(CollectionUtils.isEmpty(TemplateIds)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "订阅记录不能为空");
        }
        mpMsgSceneService.addSubscribeMsgRecord(TemplateIds);
        result.setData(true);
        return result;
    }

}
