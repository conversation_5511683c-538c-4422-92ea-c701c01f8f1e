package com.dz.ms.user.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 小程序登录
 * @author: Handy
 * @date: 2022/01/30 16:05
 */
@Data
public class WechatLoginDTO {

	@ApiModelProperty(value = "微信授权码code")
	private String code;
	@ApiModelProperty(value = "小程序appId")
	private String appId;
	@ApiModelProperty(value = "品牌ID")
	private Long tenantId;
	@ApiModelProperty(value = "门店ID")
	private Long storeId;
	@ApiModelProperty(value = "来源渠道")
	private String channel;
	@ApiModelProperty(value = "广告ID")
	private String gdtVid;

}