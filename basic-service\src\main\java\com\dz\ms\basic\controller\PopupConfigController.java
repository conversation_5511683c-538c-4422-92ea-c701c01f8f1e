package com.dz.ms.basic.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.basic.PopupConfigDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.service.PopupConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags="弹窗配置")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class PopupConfigController  {

    @Resource
    private PopupConfigService popupConfigService;

    /**
     * 分页查询弹窗配置
     * @param param
     * @return result<PageInfo<PopupConfigDTO>>
     */
    @ApiOperation("分页查询弹窗配置")
	@GetMapping(value = "/popup_config/list")
    public Result<PageInfo<PopupConfigDTO>> getPopupConfigList(@ModelAttribute PopupConfigDTO param) {
        Result<PageInfo<PopupConfigDTO>> result = new Result<>();
		PageInfo<PopupConfigDTO> page = popupConfigService.getPopupConfigList(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询弹窗配置
     * @param id
     * @return result<PopupConfigDTO>
     */
    @ApiOperation("根据ID查询弹窗配置")
	@GetMapping(value = "/popup_config/info")
    public Result<PopupConfigDTO> getPopupConfigById(@RequestParam("id") Long id) {
        Result<PopupConfigDTO> result = new Result<>();
        PopupConfigDTO popupConfig = popupConfigService.getPopupConfigById(id);
        result.setData(popupConfig);
        return result;
    }

    /**
     * 新增弹窗配置
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增弹窗配置",type = LogType.OPERATELOG)
    @ApiOperation("新增弹窗配置")
	@PostMapping(value = "/popup_config/add")
    public Result<Long> addPopupConfig(@RequestBody PopupConfigDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,true);
        Long id = popupConfigService.savePopupConfig(param);
        result.setData(id);
        return result;
    }

    /**
    * 更新弹窗配置
    * @param param
    * @return result<Object>
    */
    @SysLog(value = "更新弹窗配置",type = LogType.OPERATELOG)
    @ApiOperation("更新弹窗配置")
    @PostMapping(value = "/popup_config/update")
    public Result<Long> updatePopupConfig(@RequestBody PopupConfigDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,false);
        popupConfigService.savePopupConfig(param);
        result.setData(param.getId());
        return result;
    }

    /**
    * 验证保存参数
    * @param param
    */
    private void validationSaveParam(PopupConfigDTO param,boolean isAdd) {
        if(isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if(!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
    }

	/**
     * 根据ID删除弹窗配置
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除弹窗配置")
	@PostMapping(value = "/popup_config/delete")
    public Result<Boolean> deletePopupConfigById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        popupConfigService.deletePopupConfigById(param);
        result.setData(true);
        return result;
    }

    @ApiOperation("根据类型查询弹窗配置")
    @GetMapping(value = "/popup_config/info_by_type")
    public Result<PopupConfigDTO> getPopupConfigByType(@RequestParam("type") Integer type) {
        Result<PopupConfigDTO> result = new Result<>();
        PopupConfigDTO popupConfig = popupConfigService.getPopupConfigByType(type, SecurityContext.getUser().getTenantId());
        result.setData(popupConfig);
        return result;
    }

    @ApiOperation("根据类型保存弹窗配置")
    @PostMapping(value = "/popup_config/save_by_type")
    public Result<Long> savePopupConfigByType(@RequestBody PopupConfigDTO param) {
        Result<Long> result = new Result<>();
        if(null == param.getPopupType()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "弹窗类型不能为空");
        }
        popupConfigService.savePopupConfigByType(param, SecurityContext.getUser().getTenantId());
        result.setData(param.getId());
        return result;
    }

}
