package com.dz.ms.basic.controller;

import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.basic.CityDTO;
import com.dz.common.core.dto.basic.ProvinceCityDTO;
import com.dz.common.core.fegin.basic.CityInfoFeginClient;
import com.dz.common.core.dto.basic.ProvinceDTO;
import com.dz.ms.basic.mapper.DistRictMapper;
import com.dz.ms.basic.service.CityInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags="城市信息")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class CityInfoController implements CityInfoFeginClient {

    @Resource
    private CityInfoService cityInfoService;
    @Resource
    private DistRictMapper distRictMapper;

    @ApiOperation("获取省份及关联城市列表")
    @GetMapping(value = "/app/province_city/list")
    public Result<List<ProvinceDTO>> getProvinceCityList() {
        Result<List<ProvinceDTO>> result = new Result<>();
        List<ProvinceDTO> list = cityInfoService.getProvinceCityList();
        result.setData(list);
        return result;
    }

    @ApiOperation("获取有门店的省份")
    @GetMapping(value = "/province_info/list")
    public List<ProvinceDTO> getProvinceList() {
        return cityInfoService.getProvinceList();
    }

    @ApiOperation("获取省份下城市")
    @GetMapping(value = "/city_info/getCityList")
    public List<CityDTO> getCityList(@RequestParam("provinceCode") String provinceCode) {
        return cityInfoService.getCityList(provinceCode);
    }

    @ApiOperation("根据城市code获取省份城市信息")
    @GetMapping(value = "/province_city_info/get_info_by_cityCode")
    public ProvinceCityDTO getInfoByCityCode(@RequestParam("cityCode") String cityCode) {
        return cityInfoService.getInfoByCityCode(cityCode);
    }

    @ApiOperation("根据名称获取省市code")
    @GetMapping(value = "/province_city_info/get_code_by_name")
    public String getCodeByName(@RequestParam("name") String name) {
        String codeStr = "";
        List<String> code =distRictMapper.getCode(name);
        if (!CollectionUtils.isEmpty(code)){
            codeStr=code.get(0);
        }
        return codeStr;
    }

}
