<template>

  <layout>
    <template v-slot:header>
      <searchForm :formParams="formParams" @cancel="resetData" @ok="refreshData" :disabled="!$hasPermission('mall:giftGoods:search')">
        <a-form-item name="productName">
          <a-input placeholder="商品名称" allow-clear v-model:value="formParams.productName" allowClear @keyup.enter="refreshData"></a-input>
        </a-form-item>
        <a-form-item name="pdType">
          <a-select ref="select" v-model:value="formParams.pdType" allowClear :options="pdTypeOptions" :fieldNames="{ label: 'label', value: 'value' }" optionFilterProp="label" showSearch placeholder="商品类型"></a-select>
        </a-form-item>
        <a-form-item name="shelfIdList">
          <a-select ref="select" mode="tags" :max-tag-count="1" :maxTagTextLength="5" v-model:value="formParams.shelfIdList" allowClear :options="shelfIdListOptions" :fieldNames="{ label: 'name', value: 'id' }" optionFilterProp="name" showSearch placeholder="所处货架">
            <template #maxTagPlaceholder="omittedValues">
              <span :title="TreeStr" :max="changeStr(omittedValues)">{{changeStr(omittedValues)}}</span>
            </template>

          </a-select>
        </a-form-item>

      </searchForm>
    </template>
    <template v-slot:topRight>
      <a-space>
        <!--  权限标识 -->
        <a-button type="primary" :disabled="!$hasPermission('mall:giftGoods:add')" @click="addChang">新建商品</a-button>
        <a-button type="primary" :disabled="!$hasPermission('mall:giftGoods:derive')" @click="exportData">批量导出</a-button>

      </a-space>

    </template>
    <template v-slot="{ height }">
      <a-table :indentSize="20" row-key="id" :scroll="{ scrollToFirstRowOnChange: true, y: height - 88, x: '100%' }" :dataSource="dataSource" :columns="tableHeader" :pagination="pagination" :loading="loading" @change="loadData">
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'index'">
            {{ (current - 1) * pageSize + 1 + index }}
          </template>
          <template v-if="column.key === 'scenceImgList'&& record.scenceImgList">
            <a-image :src="record.scenceImgList[0]" :width="50" :height="50"></a-image>
          </template>
          <template v-if="column.key === 'scenceImgList'&& !record.scenceImgList">
            --
          </template>
          <template v-if="column.key === 'shelfImgList' && record.shelfImgList">
            <a-image :src="record.shelfImgList[0]" :width="50" :height="50"></a-image>
          </template>
          <template v-if="column.key === 'state'">
            <a-tag color="red" v-if="record.state == 0">停用中</a-tag>
            <a-tag color="success" v-else>启用中</a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-button type="link" :disabled="!$hasPermission('mall:giftGoods:edit')" @click="EditRole(record)">编辑</a-button>
            <a-divider type="vertical" />
            <a-popconfirm :title="`确定要${record.state == '0'?'启用':'停用'}吗？`" @confirm="handleEnable(record)" :disabled="!$hasPermission('mall:giftGoods:state')">
              <a-button :disabled="!$hasPermission('mall:giftGoods:state')" type="link">{{record.state == '0'?'启用':'停用'}}</a-button>
            </a-popconfirm>
            <!-- <a-divider type="vertical" />
            <a-popconfirm title="确定要启用吗？" @confirm="handleEnable(record)" :disabled="!$hasPermission('mall:giftGoods:state')|| record.state == '1'">
              <a-button :disabled="!$hasPermission('mall:giftGoods:state') ||record.state == '1'" type="link"></a-button>
            </a-popconfirm> -->
            <template v-if="record.state == '0'">
              <a-divider type="vertical" />
              <a-popconfirm title="是否确定删除该商品吗？" :disabled="!$hasPermission('mall:giftGoods:del')" @confirm="handleDelete(record)">
                <a-button :disabled="!$hasPermission('mall:giftGoods:del')" type="link">删除</a-button>
              </a-popconfirm>
            </template>

          </template>

        </template>
      </a-table>
    </template>

  </layout>
  <addGift :visible="visible" @ok="updateList" @cancel="visible = false" :id="id" :type="type" />
</template>
<script setup>
import addGift from './components/addGift.vue';
import { useRoute, useRouter } from 'vue-router'
import { usePagination, useRequest, } from 'vue-request';
import { productList, producDelete, productUpdate_state, shelfList_by_name, downloadTask } from '@/http/index.js'
import { message, Modal } from "ant-design-vue";
import { exchangeDescTypeOptions, pdTypeOptions } from '@/utils/dict-options'
// 分页参数
let pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ["bottomLeft"],
  };
});
// 表格分页数据获取
const total = ref(0)
const TreeStr = ref('')
const changeStr = (data) => {
  console.log(data);
  let newArr = data.map(item => item.label)
  TreeStr.value = newArr.join("、")
  console.log(TreeStr.value);
  return '+' + data.length
}

const { formParams, tableHeader, visible, id, type, shelfIdListOptions } = toRefs(reactive({


  id: '',
  type: 0,// 0-新增 1-编辑  2-查看
  visible: false,
  shelfIdListOptions: [],
  formParams: {
    productName: '',
    shelfIdList: []
  },

  tableHeader: [{
    title: '序号',
    key: 'index',
    align: 'center',
    width: 80
  },

  {
    title: '商品名称',
    dataIndex: 'productName',
    width: 180,
    align: 'center',
  },
  {
    title: '商品ID',
    dataIndex: 'id',
    width: 180,
    align: 'center',
  },
  {
    title: '商品类型',
    dataIndex: 'pdType',
    align: 'center',
    ellipsis: true,
    width: 180,
    customRender: (row) => {
      // console.log(row);
      let textLabel = null
      if (row.text) {
        textLabel = pdTypeOptions.find(item => item.value == row.text)
      }
      console.log(textLabel);
      return textLabel && textLabel.label || '--'; // 如果数据为空，则显示 '----' textLabel.label ||
    }
  },
  {
    title: '创建时间',
    dataIndex: 'created',
    align: 'center',
    width: 180,

  },
  {
    title: '商品场景图',
    key: 'scenceImgList',
    align: 'center',
    ellipsis: true,
    width: 130,
  },
  {
    title: '商品橱窗图',
    key: 'shelfImgList',
    align: 'center',
    ellipsis: true,
    width: 130,
  },
  {
    title: '商品标签',
    dataIndex: 'tagList',
    align: 'center',
    ellipsis: true,
    width: 180,
    customRender: (row) => {
      // console.log(row);
      let textRow = null
      if (row.text && row.text.length) {
        textRow = row.text.map(item => item.name).join(',');
      }

      return textRow || '--'; // 如果数据为空，则显示 '----'
    }
  },
  {
    title: '商品CP号',
    dataIndex: 'venderId',
    align: 'center',
    ellipsis: true,
    width: 280,

    customRender: (row) => {
      // console.log(row);

      return row.text || '--'; // 如果数据为空，则显示 '----'
    }
  },
  {
    title: '成本价',
    dataIndex: 'originPrice',
    align: 'center',
    ellipsis: true,
    width: 130,

    customRender: (row) => {
      // console.log(row);

      return row.text || '--'; // 如果数据为空，则显示 '----'
    }
  },

  {
    title: '到店支付金额',
    dataIndex: 'costPrice',
    align: 'center',
    ellipsis: true,
    width: 130,

    customRender: (row) => {
      // console.log(row);

      return row.text || '--'; // 如果数据为空，则显示 '----'
    }
  },
  {
    title: '积分价值',
    dataIndex: 'costPoint',
    align: 'center',
    ellipsis: true,
    width: 130,
  },
  {
    title: '目前所处货架',
    dataIndex: 'shelfList',
    align: 'center',
    ellipsis: true,
    width: 280,
    customRender: (row) => {
      // console.log(row);
      let textRow = null
      if (row.text && row.text.length) {
        textRow = row.text.map(item => item.name).join(',');
      }

      return textRow || '--'; // 如果数据为空，则显示 '----'
    }
  },
  {
    title: '启用停用状态',
    key: 'state',
    align: 'center',
    ellipsis: true,
    width: 120,
  },
  {
    title: '累计兑换量',
    dataIndex: 'exchangeNum',
    align: 'center',
    ellipsis: true,
    width: 120,
    customRender: (row) => {
      // console.log(row);

      return row.text || '--'; // 如果数据为空，则显示 '----'
    }
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    align: 'center',
    width: 180,
    fixed: 'right'
  }]
})
);
const { data: dataSource, run, loading, pageSize, current, refresh } = usePagination((param) => {
  // 这里修改参数
  // return resignationInheritanceList({ ...param, ...getParams() })
  return productList({ ...param, ...formParams.value })
},
  {
    manual: false, // 修改为false,让其自动执行
    pagination: {
      currentKey: 'pageNum',  // 通过该值指定接口 当前页数 参数的属性值
      pageSizeKey: 'pageSize',// 通过该值指定接口 每页获取条数 参数的属性值
      totalKey: 'data.data.count',  // 指定 data 中 total 属性的路径
    },
    formatResult: res => { // 返回数据格式化
      total.value = res.data.count
      return res.data.list
    }
  });

function updateList(value) {
  visible.value = false;
  // 新增 重置搜索数据
  if (!value) {
    resetData();
  } else {
    // 查看、编辑 不需要清空搜索数据 直接刷新
    refresh();
  }
}
function EditRole(record) {
  visible.value = true
  type.value = 1
  id.value = record.id
  console.log(record, id.value);

}
function handleEnable(record) {
  productUpdate_state({ id: record.id, number: record.state == '0' ? 1 : 0 }).then(res => {
    if (res.code === 0) {
      message.success(`${record.state == '0' ? '启用' : '停用'}成功`)
      resetData(true)
    }
  })
}
//删除角色
const handleDelete = (record) => {
  producDelete({ id: record.id }).then(res => {
    if (res.code === 0) {
      message.success('删除成功')
      resetData()
    }
  })
}
// 加载数据
const loadData = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}
shelfList_by_name().then(res => {
  shelfIdListOptions.value = res.data
})

// 刷新数据
const refreshData = () => {
  run({ pageNum: 1, pageSize: pageSize.value })
};

// 重置数据
const resetData = (stayOnTheCurrentPage = false) => {
  formParams.value = {
    productName: '',
    shelfIdList: []
  }
  if (stayOnTheCurrentPage) {
    refresh()
  } else {
    refreshData()
  }
}
function addChang() {
  visible.value = true
  type.value = 0
  id.value = ''
}

function exportData() {
  downloadTask({ type: 1, fileName: '兑礼商品列表', param: { ...formParams.value } })
}
</script>
