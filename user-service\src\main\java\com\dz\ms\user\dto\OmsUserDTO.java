package com.dz.ms.user.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * OMS-系统用户信息DTO
 * @author: Handy
 * @date:   2020/01/29 19:15
 */
@Data
@ApiModel(value = "OMS-系统用户信息")
public class OmsUserDTO extends BaseDTO {

    @ApiModelProperty(value = "系统用户ID")
    private Long id;
    @ApiModelProperty(value = "账号")
    private String username;
    @ApiModelProperty(value = "真实姓名")
    private String realname;
    @ApiModelProperty(value = "昵称")
    private String nickname;
    @ApiModelProperty(value = "头像地址")
    private String headUrl;
    @ApiModelProperty(value = "手机号码")
    private String mobile;
    @ApiModelProperty(value = "用户状态 0正常 1离职")
    private Integer state;
    @ApiModelProperty(value = "在线状态 0离线 1在线")
    private Integer isOnline;
    @ApiModelProperty(value = "是否超管 1是 0否")
    private Integer isAdmin;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "权限码")
    private List<String> permits;
    @ApiModelProperty(value = "角色ID")
    private Long roleId;
    @ApiModelProperty(value = "token")
    private String token;

}
