package com.dz.ms.adaptor.processor;


import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.fegin.basic.MpMsgPushFeignClient;
import com.dz.common.core.threadlocal.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/7
 */
@Slf4j
@Component
public class ActivityStartCampaignMsgJob implements BasicProcessor {

    @Resource
    private MpMsgPushFeignClient mpMsgPushFeignClient;


    @Override
    public ProcessResult process(TaskContext taskContext) throws Exception {
        String jobParams = taskContext.getJobParams();
        if(Objects.isNull(jobParams)){
            return new ProcessResult(true, "param null");
        }
        log.info("参数是否存在值: {}", jobParams);
        String[] strs = jobParams.split(",");
        if(strs.length < 6) {
            return new ProcessResult(true, "param invalid");
        }

        SecurityContext.setUser(new CurrentUserDTO(1L,0L));
        List<Long> uids = null;
        if (strs.length == 7) {
            uids = new ArrayList<>();
            String[] users = strs[6].split("-");
            for (String str : users) {
                Long id = NumberUtils.toLong(str);
                if (id > 0) {
                    uids.add(id);
                }
            }
            if (uids.isEmpty()) {
                uids = null;
            }
        }
        Long tenantId = NumberUtils.toLong(strs[0]);
        mpMsgPushFeignClient.activityStartCampaignMsgPush(tenantId, new String[]{strs[1], strs[2], strs[3], strs[4]}, strs[5], uids);
        log.info("新活动开始提醒（campaign）执行完成");
        return new ProcessResult(true, "success");
    }
}
