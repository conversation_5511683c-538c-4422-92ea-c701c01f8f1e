package com.dz.ms.adaptor.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.adaptor.entity.ScheduledTaskConfigLog;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 定时任务日志表Mapper
 * @author: 
 * @date:   2025/03/17 11:26
 */
@Repository
public interface ScheduledTaskConfigLogMapper extends BaseMapper<ScheduledTaskConfigLog> {

    int insertLog(@Param("param") ScheduledTaskConfigLog param,@Param("month") String month);

    int updateStatus(@Param("param") ScheduledTaskConfigLog param,@Param("month") String month);

    List<String> queryTableList();

    void createTable(@Param("month") String month);
    
}
