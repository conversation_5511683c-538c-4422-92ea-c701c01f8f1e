package com.dz.ms.sales.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.ms.sales.dto.FittingDTO;
import com.dz.ms.sales.entity.Fitting;

/**
 * 用户试衣照记录表接口
 * @author: 
 * @date:   2025/03/21 11:43
 */
public interface FittingService extends IService<Fitting> {

	/**
     * 分页查询用户试衣照记录表
     * @param param FittingDTO
     * @return PageInfo<FittingDTO>
     */
    PageInfo<FittingDTO> getFittingList(FittingDTO param);

    /**
     * 根据ID查询用户试衣照记录表
     * @param id Long
     * @return FittingDTO
     */
    FittingDTO getFittingById(Long id);

    /**
     * 保存用户试衣照记录表
     * @param param FittingD
     * @return Long
     */
    Long saveFitting(FittingDTO param);

    /**
     * 根据ID删除用户试衣照记录表
     * @param param IdCodeDTO
     */
    void deleteFittingById(IdCodeDTO param);

}
