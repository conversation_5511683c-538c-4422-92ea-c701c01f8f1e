package com.dz.ms.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.ms.product.dto.ProductTagDTO;
import com.dz.ms.product.entity.ProductTag;

import java.util.List;

/**
 * 商品标签表接口
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:25
 */
public interface ProductTagService extends IService<ProductTag> {

    /**
     * 分页查询商品标签表
     *
     * @param param
     * @return PageInfo<ProductTagDTO>
     */
    public PageInfo<ProductTagDTO> getProductTagList(ProductTagDTO param);

    /**
     * 根据ID查询商品标签表
     *
     * @param id
     * @return ProductTagDTO
     */
    public ProductTagDTO getProductTagById(Long id);

    /**
     * 根据标签ID查询商品标签
     *
     * @param tagId 标签ID
     * @return result<List < ProductTagDTO>
     */
    List<ProductTagDTO> getTagByTagId(Long tagId);

    /**
     * 保存商品标签表
     *
     * @param param
     * @return Long
     */
    public Long saveProductTag(ProductTagDTO param);

    /**
     * 根据ID删除商品标签表
     *
     * @param param
     */
    public void deleteProductTagById(IdCodeDTO param);

    /**
     * 根据标签ID删除商品标签
     *
     * @param tagId 标签ID
     */
    int deleteShelfTagByTagId(Long tagId);
}
