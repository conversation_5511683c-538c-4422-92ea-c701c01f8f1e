export function onInput ({ currentTarget: { dataset }, detail: { value } }) {
  const { fieldName, noTrim, formFields, formName } = dataset
  const newValue = noTrim ? value : value.trim ? value.trim() : value
  // 处理form对象上的数据，因表单上的数据一般放在form对象里统一处理。
  if (formName && formFields) {
    formFields[fieldName] = newValue
    this.setData({ [formName]: formFields })
    // console.log(`this.data.${formName}`, this.data[formName])
    return
  }
  // 处理data上的数据
  this.setData({ [fieldName]: newValue })
  // console.log(`this.data.${fieldName}`, this.data[fieldName])
}
