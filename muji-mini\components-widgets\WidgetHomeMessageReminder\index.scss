@import "assets/scss/config";

.WidgetHomeMessageReminderPlaceholder {
  height: px2rpx(10);

  ._fixed {
    height: inherit;
    //position: fixed;
    left: 0;
    z-index: 101;
    width: 100%;

    &._bg1 {
      background: var(--homeVipBg);
    }

    &._bg2 {
      background: #fff;
    }
  }
}

.WidgetHomeMessageReminder {
  height: px2rpx(60);
  font-size: px2rpx(20);
  color: #3c3c3c;

  ._fixed {
    background: #ffffff;
    height: inherit;
    padding: 0 px2rpx(46) 0 px2rpx(53);
    //position: fixed;
    left: 0;
    z-index: 101;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
  }

  ._left {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  ._notice {
    margin-right: px2rpx(10);
    font-size: px2rpx(24);
  }

  ._text {
    line-height: 1.6;
    padding-bottom: px2rpx(2);
    max-width: px2rpx(560);
    @include ellipsis1();
  }

  ._right {
    font-size: px2rpx(28);
    color: #bbbbbb;
    margin-top: px2rpx(-2);
  }
}
