package com.dz.common.core.dto.export;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class OrderExportDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "订单编号")
    private String orderCode;
    @ApiModelProperty(value = "用户id")
    private Long userId;
    @ApiModelProperty(value = "用户昵称")
    private String userName;
    @ApiModelProperty(value = "用户CRM编码")
    private String userCrmCode;
    @ApiModelProperty(value = "兑换时间")
    private Date created;
    @ApiModelProperty(value = "订单兑换状态 0已完成 1待支付 2待发货 3已发货 4待兑换 5部分兑换 6已兑换 7已取消")
    private Integer orderStatus;
    @ApiModelProperty(value = "发货状态 0待发货 1已发货 2部分发货")
    private Integer expressStatus;
    @ApiModelProperty(value = "商品数量")
    private Integer number;
    @ApiModelProperty(value = "订单价值（积分）")
    private Integer orderPoint;
}
