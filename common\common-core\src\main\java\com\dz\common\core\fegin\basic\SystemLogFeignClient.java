package com.dz.common.core.fegin.basic;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.basic.SystemLogDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = ServiceConstant.BASIC_SERVICE_NAME, contextId = "SystemLogFeignClient")
public interface SystemLogFeignClient {

    /**
     * 保存系统日志
     * @param param
     * @return result<Long>
     */
    @PostMapping(value = "/system_log/add")
    public Result<Object> addLog(@RequestBody SystemLogDTO param);

    @ApiOperation("添加接口内部错误日志")
    @PostMapping(value = "/system_log/add_error")
    public Result<Object> addErrorLog(@RequestParam("name")String name, @RequestParam(value = "url",required = false)String url, @RequestParam(value = "params",required = false)String params,
                                      @RequestParam(value = "exception",required = false)String exception, @RequestParam(value = "operator",required = false)Long operator, @RequestParam("tenantId")Long tenantId);

}
