package com.dz.common.core.dto.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 购物车DTO
 */
@Setter
@Getter
@ToString
@ApiModel(value = "购物车参数DTO")
public class CartDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    private Long id;
    @ApiModelProperty(value = "商品id")
    private Long productId;
    @ApiModelProperty(value = "货架商品ID")
    private Long shelfProductId;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "商品数量")
    private Integer number;
    @ApiModelProperty(value = "是否选中")
    private Integer checked;
}