package com.dz.ms.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.ms.product.dto.ShelfCampaignRuleDTO;
import com.dz.ms.product.dto.req.ShelfCampaignRuleDelParamDTO;
import com.dz.ms.product.dto.req.ShelfCampaignRuleSaveParamDTO;
import com.dz.ms.product.entity.ShelfCampaign;
import com.dz.ms.product.entity.ShelfCampaignRule;

import java.util.List;
import java.util.Map;

/**
 * 营销活动规则接口
 *
 * @author: LiinNs
 * @date: 2024/11/20 11:39
 */
public interface ShelfCampaignRuleService extends IService<ShelfCampaignRule> {

    /**
     * 分页查询营销活动规则
     *
     * @param param
     * @return PageInfo<ShelfCampaignRuleDTO>
     */
    public PageInfo<ShelfCampaignRuleDTO> getShelfCampaignRuleList(ShelfCampaignRuleDTO param);

    /**
     * 根据营销活动ID列表查询营销活动规则列表
     * @param campaignIds 营销活动ID列表
     * @param isQryCrowd 是否查询人群包信息
     * @return List<ShelfCampaignRuleDTO>
     */
    List<ShelfCampaignRuleDTO> getRuleListByCampaignIds(List<Long> campaignIds, boolean isQryCrowd);

    /**
     * 根据营销活动ID列表查询营销活动规则列表
     * @param campaignIds 营销活动ID列表
     * @param isQryCrowd 是否查询人群包信息
     * @return Map<Long, ShelfCampaignRuleDTO>
     */
    Map<Long, ShelfCampaignRuleDTO> getRuleMapByCampaignIds(List<Long> campaignIds, boolean isQryCrowd);

    /**
     * 根据ID查询营销活动规则
     * @param id id
     * @param isThrow 1:抛异常
     * @return ShelfCampaignRuleDTO
     */
    ShelfCampaignRuleDTO getShelfCampaignRuleById(Long id,Integer isThrow);

    /**
     * 保存营销活动规则
     * @param param 营销活动规则
     */
    void saveShelfCampaignRule(ShelfCampaign shelfCampaign, ShelfCampaignRuleSaveParamDTO param, boolean isAdd);

    /**
     * 根据ID删除营销活动规则
     *
     * @param param
     */
    public void deleteShelfCampaignRuleById(IdCodeDTO param);

    /**
     * 根据条件删除营销活动规则
     * @param param 删除条件
     */
    void deleteRule(ShelfCampaignRuleDelParamDTO param);

    /**
     * 根据人群包id,更新货架营销活动关联人群包id为null
     * @param groupId 人群包id
     */
    void updGroupIdIntoNull(Long groupId);
    
}
