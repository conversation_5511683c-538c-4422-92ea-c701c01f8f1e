package com.dz.ms.user.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.io.Serializable;
import java.util.Date;

/**
 * OMS-权限功能
 * @author: Handy
 * @date:   2022/07/25 21:43
 */
@Getter
@Setter
@NoArgsConstructor
@Table("OMS-权限功能")
@TableName(value = "oms_permission")
public class OmsPermission implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "权限ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR,length = 30,isNull = false,comment = "权限编号")
    private String code;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,defaultValue = "0",comment = "父节点ID")
    private Long parentId;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = false,comment = "权限类型 1模块 2页面 3功能")
    private Integer permitType;
    @Columns(type = ColumnType.VARCHAR,length = 30,isNull = false,comment = "权限名称")
    private String permitName;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "页面/接口地址")
    private String url;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "权限描述")
    private String permitDesc;
    @Columns(type = ColumnType.INT,length = 10,isNull = true,comment = "菜单显示排序")
    private Integer displaySort;
    @Columns(type = ColumnType.TINYINT,length = 3,isNull = false,defaultValue = "0",comment = "是否有子菜单")
    private Integer hasChild;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    public OmsPermission(Long id, String code, Long parentId, Integer permitType, String permitName, String url, String permitDesc, Integer displaySort, Integer hasChild) {
        this.id = id;
        this.code = code;
        this.parentId = parentId;
        this.permitType = permitType;
        this.permitName = permitName;
        this.url = url;
        this.permitDesc = permitDesc;
        this.displaySort = displaySort;
        this.hasChild = hasChild;
    }

}
