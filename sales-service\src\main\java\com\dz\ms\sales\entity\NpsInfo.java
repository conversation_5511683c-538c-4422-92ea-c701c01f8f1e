package com.dz.ms.sales.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/16
 */
@Getter
@Setter
@NoArgsConstructor
@Table("NPS问卷信息")
@TableName(value = "nps_info")
public class NpsInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 11, isNull = false, comment = "NPS问卷ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR, length = 200, isNull = false, comment = "问卷标题")
    private String title;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "问卷开始时间")
    private Date startTime;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "问卷结束时间")
    private Date endTime;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, comment = "问卷触发场景 0其他 1打卡",defaultValue = "0")
    private Integer scene;
    @Columns(type = ColumnType.BIGINT, length = 20, isNull = true, comment = "问卷关联值（打卡天数）")
    private Integer sceneValue;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "启用状态 0禁用 1启用")
    private Integer state;

    @Columns(type = ColumnType.TINYINT,length = 3,isNull = false,defaultValue = "0",comment = "是否删除 0未删除 1已删除")
    @TableLogic
    private Integer isDeleted;
    @Columns(type = ColumnType.BIGINT, length = 11, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT, length = 11, isNull = true, comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    public NpsInfo(Long id, String title, Date startTime, Date endTime, Integer state) {
        this.id = id;
        this.title = title;
        this.startTime = startTime;
        this.endTime = endTime;
        this.state = state;
    }
}
