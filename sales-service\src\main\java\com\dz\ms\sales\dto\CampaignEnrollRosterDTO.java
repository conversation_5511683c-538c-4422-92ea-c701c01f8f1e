package com.dz.ms.sales.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/19
 */
@Getter
@Setter
@NoArgsConstructor
public class CampaignEnrollRosterDTO {

    @ApiModelProperty(value = "姓名", example = "张三", required = true)
    private String name;

    @ApiModelProperty(value = "会员号", example = "123456789012345678", required = true)
    private String cardNo;

    @ApiModelProperty(value = "手机号码", example = "13800138000", required = true)
    private String mobile;


}