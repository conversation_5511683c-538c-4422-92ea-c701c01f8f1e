package com.dz.ms.basic.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.wechat.CodeSessionDTO;
import com.dz.common.core.dto.wechat.DecryptUserDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.basic.MpConfigFeignClient;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.dto.MpConfigDTO;
import com.dz.ms.basic.dto.MpConfigSimpleDTO;
import com.dz.ms.basic.entity.MpConfig;
import com.dz.ms.basic.service.MpConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags="公众号/小程序配置")
@RestController
public class MpConfigController implements MpConfigFeignClient {

    @Resource
    private MpConfigService mpConfigService;

    /**
     * 分页查询公众号/小程序配置
     * @param param
     * @return result<PageInfo<MpConfigDTO>>
     */
    @ApiOperation("分页查询公众号/小程序配置")
	@GetMapping(value = "/mp_config/list")
    public Result<PageInfo<MpConfigDTO>> getMpConfigList(@ModelAttribute MpConfigDTO param) {
        Result<PageInfo<MpConfigDTO>> result = new Result<>();
        MpConfig mpConfig = BeanCopierUtils.convertObjectTrim(param,MpConfig.class);
        IPage<MpConfig> page = mpConfigService.page(new Page<>(param.getPageNum(), param.getPageSize()), new LambdaQueryWrapper<>(mpConfig));
        result.setData(new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),BeanCopierUtils.convertList(page.getRecords(), MpConfigDTO.class)));
        return result;
    }

    /**
     * 根据ID查询公众号/小程序配置
     * @param id
     * @return result<MpConfigDTO>
     */
    @ApiOperation("根据ID查询公众号/小程序配置")
	@GetMapping(value = "/mp_config/info")
    public Result<MpConfigDTO> getMpConfigById(@RequestParam("id") Long id) {
        Result<MpConfigDTO> result = new Result<>();
        MpConfig mpConfig = mpConfigService.getById(id);
        result.setData(BeanCopierUtils.convertObject(mpConfig,MpConfigDTO.class));
        return result;
    }

    /**
     * 新增公众号/小程序配置
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增公众号/小程序配置",type = LogType.OPERATELOG)
    @ApiOperation("新增公众号/小程序配置")
    @PostMapping(value = "/mp_config/add")
    public Result<Long> addMpConfig(@RequestBody MpConfigDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,true);
        MpConfig mpConfig = new MpConfig(param.getId(), param.getAppType(), param.getAppName(), param.getAppId(), param.getAppSecret(), param.getLabel(), param.getRefreshToken(), param.getVerifyTypeInfo(), param.getUserName(), param.getFuncInfo(), param.getAuthStatus(), param.getAlone(), param.getVersion(), param.getVersionDesc(), param.getCodeStatus(), param.getFailReason());
        mpConfigService.save(mpConfig);
        result.setData(mpConfig.getId());
        return result;
    }

    /**
     * 更新公众号/小程序配置
     * @param param
     * @return result<Object>
     */
    @SysLog(value = "更新公众号/小程序配置",type = LogType.OPERATELOG)
    @ApiOperation("更新公众号/小程序配置")
    @PostMapping(value = "/mp_config/update")
    public Result<Long> updateMpConfig(@RequestBody MpConfigDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param,false);
        MpConfig mpConfig = new MpConfig(param.getId(), param.getAppType(), param.getAppName(), param.getAppId(), param.getAppSecret(), param.getLabel(), param.getRefreshToken(), param.getVerifyTypeInfo(), param.getUserName(), param.getFuncInfo(), param.getAuthStatus(), param.getAlone(), param.getVersion(), param.getVersionDesc(), param.getCodeStatus(), param.getFailReason());
        mpConfigService.updateById(mpConfig);
        result.setData(param.getId());
        return result;
    }

    /**
     * 验证保存参数
     * @param param
     */
    private void validationSaveParam(MpConfigDTO param,boolean isAdd) {
        if(isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if(!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
    }
	
	/**
     * 根据ID删除公众号/小程序配置
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除公众号/小程序配置")
	@PostMapping(value = "/mp_config/delete")
    public Result<Boolean> deleteMpConfigById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        mpConfigService.removeById(param.getId());
        result.setData(true);
        return result;
    }

    @ApiOperation("根据租户ID获取小程序token")
    @GetMapping(value = "/mp_config/miniapp/accesstoken")
    public Result<String> getMiniappAccessToken(@RequestParam("tenantId") Long tenantId,@RequestParam(value = "cleanCach",required = false) Boolean cleanCach) {
        Result<String> result = new Result<>();
        if(null == cleanCach) {
            cleanCach = false;
        }
        String token = mpConfigService.getMiniappAccessToken(tenantId,cleanCach);
        result.setData(token);
        return result;
    }

    @ApiOperation("根据租户ID获取小程序appid")
    @GetMapping(value = "/mp_config/appid/by_tenant")
    public Result<String> getMiniappidByTenantId(@RequestParam("tenantId") Long tenantId) {
        Result<String> result = new Result<>();
        MpConfigSimpleDTO mpConfig = mpConfigService.getMpConfigByTenantIdAndType(tenantId,1);
        result.setData(mpConfig.getAppId());
        return result;
    }

    @ApiOperation("小程序登录code换取session")
    @PostMapping(value = "/mp_config/code_session")
    public Result<CodeSessionDTO> getMinappSessionByCode(@RequestParam("appId") String appId,@RequestParam("code") String code) {
        Result<CodeSessionDTO> result = new Result<>();
        result.setData(mpConfigService.getMinappSessionByCode(appId,code));
        return result;
    }

    @ApiOperation("微信用户数据解密")
    @PostMapping(value = "/mp_config/wxdata_decode")
    public Result<DecryptUserDTO> wxDataDecrypt(@RequestParam("encryptedData")String encryptedData, @RequestParam("iv")String iv, @RequestParam("openId")String openId) {
        Result<DecryptUserDTO> result = new Result<>();
        result.setData(mpConfigService.wxDataDecrypt(encryptedData,iv,openId));
        return result;
    }

}
