package com.dz.common.core.dto.wechat.mpmsg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 小程序订阅消息模板关键词
 * @author: Handy
 * @date:   2023/07/07 14:22
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "小程序订阅消息模板关键词")
public class SubscribeMsgKeywordDTO {

    @ApiModelProperty(value = "关键词ID 选用模板时需要")
    private Long kid;
    @ApiModelProperty(value = "关键词内容")
    private String name;
    @ApiModelProperty(value = "关键词内容对应的示例")
    private String example;
    @ApiModelProperty(value = "参数类型")
    private String rule;

}
