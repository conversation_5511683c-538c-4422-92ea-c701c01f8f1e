// pages/integralDetails/integralDetails.js
import {
  getProductDetail,
  postCartAdd,
  getCartCount,
  postOrderPreview
} from '../../api/index.js'
import {
  productTagArray
} from '../../utils/contants'

const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    detail: {},
    imgList: [],
    name: '',
    userInfo: {},
    cellOptions: [{
      label: '兑换须知',
      content: undefined,
      isImage: false,

    },  {
      label: '使用说明',
      content: undefined,
      isImage: false
    },
    {
      label: '商品详情',
      content: undefined,
      isImage: false
    },
  ],
    costPrice: 0, // 支付金额
    costPoint: 0, // 支付积分
    purchaseType: 1, // 1 纯积分（需支付0.1元） 2. 积分+金额
    prePoint: undefined, // 积分划线价
    prePrice: 0, // 吊牌价
    loading: false,
    cartCount: 0,
    err: {
      show: false,
    },
    // productTagArray: [{
    //     value: '人气新品',
    //     bgColor: '#7F0019',
    //     fontColor: '#FFF',
    //   },
    //   {
    //     value: '活动限定',
    //     bgColor: '#E0CEAA',
    //     fontColor: '#7F0019',
    //   },
    //   {
    //     value: '限时折扣 积分兑礼',
    //     bgColor: '#7F0019',
    //     fontColor: '#FFF',
    //   },
    //   {
    //     value: '金级专享',
    //     bgColor: '#D4B361',
    //     fontColor: '#FFF',
    //   },
    //   {
    //     value: '银级以上专享',
    //     bgColor: '#ACB4BF',
    //     fontColor: '#FFF',
    //   }, {
    //     value: '铜级以上专享',
    //     bgColor: '#BF9A78',
    //     fontColor: '#FFF',
    //   }
    // ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.data.options = options;
    this.setData({
      userInfo: app.globalData.userInfo
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    this.init()
    this.getCartCount()
    await app.getUserInfo()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },
  onShareAppMessage() {
    const {
      productId
    } = this.data.options;
    return {
      title: '每月会员福利上新，点击查看详情',
      path: `/pages/integralDetails/integralDetails?productId=${productId}&inviteUserId=${app.globalData.userInfo.id}`,
      imageUrl: `${wx.$config.ossImg}/share/shop.jpg`
    }
  },

  async init() {
    const {
      productId
    } = this.data.options;
    this.setData({
      loading: true,
    })
    const res = await getProductDetail({
      shelfProductId: productId
    });
    const {
      data: detailData
    } = res;
    if (detailData === null) {
      wx.$mp.navigateBack();
      return;
    }

    // 判断商品售罄、人群限购弹窗
    if (!detailData?.currentInventory) {
      detailData.popType = '商品售罄'
      detailData.popContent = detailData?.inventoryContent || ''
    } else if (detailData?.isCampaign && detailData?.isCampaignAuth != 1) {
      detailData.popType = '人群限购'
      detailData.popContent = detailData?.ruleContent || ''
    }
    if (detailData?.popContent) {
      detailData.popContent = JSON.parse(detailData.popContent)
    }

    function handleDescData(val) {
      let res = undefined;
      if (val && val.length > 0) {
        res = val.split(',');
      }
      return res
    }

    function handleTagData(val) {
      const v = productTagArray.find(item => item.value === val);
      return v;
    }

    const cellOptions = [{
      label: '兑换须知',
      className: 'mp-html-exchange-notice',
      content: detailData.exchangeDescType === 1 ? handleDescData(detailData.exchangeDescUrl) : detailData.exchangeDescContent,
      isUnfold: true,
      isImage: detailData.exchangeDescType === 1,
      position:'left',
      icon:'bookLook.png'
    },  {
      label: '使用说明',
      className: 'mp-html-instructions',
      content: detailData.referType === 1 ? handleDescData(detailData.referUrl) : detailData.referContent,
      isUnfold: true,
      isImage: detailData.referType === 1,
      position:'left',
      icon:'penBook.png'
    },{
      label: '商品详情',
      className: 'mp-html-product-details',
      content: detailData.detailsType === 1 ? handleDescData(detailData.detailsUrl) : detailData.detailsContent,
      isImage: detailData.detailsType === 1,
      position:'IntheMiddle',
      icon:''
    },
  ]

    // scenceImgUrl是场景图，如果场景图为空才取橱窗图shelfImgUrl
    this.setData({
      detail: detailData, // todo 为何不全都用detail
      imgList: detailData.scenceImgUrl && detailData.scenceImgUrl !== '' ? detailData.scenceImgUrl.split(',') : detailData.shelfImgUrl.split(','),
      name: detailData.productName,
      cellOptions,
      costPrice: detailData.costPrice,
      costPoint: detailData.costPoint,
      purchasebType: detailData.purchaseType,
      prePrice: detailData.prePrice,
      prePoint: detailData.prePoint, // 吊牌价
      loading: false,
      currentTagData: (detailData.superscript !== null && detailData.superscript.length > 0) ? handleTagData(detailData.superscript[0]) : undefined,
    })
  },

  getCartCount() {
    getCartCount().then(res => {
      this.setData({
        cartCount: res.data,
      })
    })
  },

  // 点击购物车图标
  onTapCart: app.debounce(function () {
    wx.$mp.track({
      event: 'product_detail_cart',
    })
    if (app.ifRegister()) {
      wx.$mp.navigateTo({
        url: '/pages/cart/cart'
      })
    }
  }),

  // onTapCart() {
  //   wx.$mp.navigateTo({ url: '/pages/cart/cart' })
  // },

  // 点击加入购物车
  onTapCartAdd: app.debounce(function () {
    if (app.ifRegister()) {
      // this.setData({
      //   loading: true
      // })
      const detail = this.data.detail;
      const data = {
        number: 1,
        shelfProductId: detail.shelfProductId,
        shelfId: detail.shelfId,
        productId: detail.productId,
        "checked": 0,
        // "id": 0
      }
      wx.$mp.track({
        event: 'product_detail_add_cart',
        props: {
          productId: detail.productId,
          productName: detail.productName,
        }
      })
      postCartAdd(data).then(res => {
        wx.showToast({
          title: '加入购物车成功',
          icon: 'none'
        })
        this.getCartCount()
      }).catch(err => {
        // debugger
        if (err.code === 1001) {
          this.setData({
            err: {
              ...err,
              show: true
            }
          })
        }
      }).finally(() => {
        // this.setData({
        //   loading: false
        // })
      })
    }

  }),

  onTapCartExchange: app.debounce(function () {
    if (app.ifRegister()) {
      // this.setData({
      //   loading: true
      // })

      const detail = JSON.parse(JSON.stringify(this.data.detail))
      detail.imgUrl = detail.shelfImgUrl
      detail.number = 1

      const data = {
        source: 0, // 跳转来源 0-立即购买 1-购物车
        productList: [detail]
      }

      wx.$mp.track({
        event: 'product_detail_exchange',
        props: {
          productId: detail.productId,
          productName: detail.productName,
        }
      })
      postOrderPreview(data).then(res => {
        // todo
        wx.$mp.navigateTo({
          url: '/pages/submitOrder/submitOrder?source=0',
          success: function (result) {
            // 通过eventChannel向被打开页面传送数据
            result.eventChannel.emit('exchangePreview', {
              data: res.data
            })
          }
        })
      }).catch(err => {
        // debugger
        if (err.code === 1003) {
          this.setData({
            err: {
              ...err,
              show: true
            }
          })
        }
      }).finally(() => {
        // this.setData({
        //   loading: false
        // })
      })
    }

  }),

  onPopConfirm() {
    this.setData({
      err: {
        ...this.data.err,
        show: false
      }
    })
  },

  onPopCancel() {
    this.setData({
      err: {
        ...this.data.err,
        show: false
      }
    })

    wx.$mp.navigateTo({
      url: '/pages/cart/cart'
    })
  },

})
