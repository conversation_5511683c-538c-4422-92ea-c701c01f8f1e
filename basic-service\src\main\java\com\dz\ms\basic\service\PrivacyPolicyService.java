package com.dz.ms.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.ms.basic.dto.PrivacyPolicyDTO;
import com.dz.ms.basic.entity.PrivacyPolicy;

/**
 * 隐私条款接口
 * @author: Handy
 * @date:   2023/05/17 16:50
 */
public interface PrivacyPolicyService extends IService<PrivacyPolicy> {

	/**
     * 分页查询隐私条款
     * @param param
     * @return PageInfo<PrivacyPolicyDTO>
     */
    public PageInfo<PrivacyPolicyDTO> getPrivacyPolicyList(PrivacyPolicyDTO param);

    /**
     * 根据ID查询隐私条款
     * @param id
     * @return PrivacyPolicyDTO
     */
    public PrivacyPolicyDTO getPrivacyPolicyById(Long id);

    /**
     * 保存隐私条款
     * @param param
     * @return Long
     */
    public Long savePrivacyPolicy(PrivacyPolicyDTO param);

    /**
     * 根据ID删除隐私条款
     * @param param
     */
    public void deletePrivacyPolicyById(IdCodeDTO param);

    /**
     * 获取最新隐私条款版本号
     * @return
     */
    String getLastPrivacyPolicyVersion();

    /**
     * 获取最新隐私条款内容
     * @return
     */
    PrivacyPolicyDTO getLastPrivacyPolicyInfo();
}
