// signUp/pages/signUp/signUp.js
const util = require('../../../utils/util.js')
const app = getApp()
import {
  getCampaignType,
  getEnrollInfo,
  getSignInOpen,
  getLotteryUser
} from '../../api/index.js'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    campaignCode: '',
    disabled: false,
    showRules: false,
    activeInfo: {},
    loading: true,
    // 当前落地页状态
    // 状态1：未报名 状态2：报名成功 未审核 状态3：报名成功-已中奖、已购买 状态4：已经开启打卡
    // 状态5：打卡成功 未抽过奖品 状态6：打卡成功 抽取过一次奖 还没有分享 状态7：抽奖只抽取过一次 分享过 还有一次抽奖机会 状态8：打卡成功 并且抽奖次数已用完
    HomepageStatus: '',
    btnTitle: '', // 跳转页面按钮显示文字
    SignInData: '', //打卡状态
    isShowTime: false // 时间判断
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取当前时间
    const currentTime = new Date();
    const showDate = new Date("2025.03.05 10:00")
    this.setData({
      isShowTime: currentTime.getTime() >= showDate.getTime(),
      loading: true
    })
  },
  onShow() {
    this.jump()
  },

  onTapRule: app.debounce(async function () {
    wx.$mp.navigateTo({
      url: '/signUp/pages/activityRules/activityRules',
    })
  }),
  // 跳转公示结果页
  onTapRule1: app.debounce(async function () {
    wx.$mp.navigateTo({
      url: '/signUp/pages/announcement/announcement',
    })
  }),

  jump() {
    let that = this
    // 获取活动信息
    getCampaignType().then(({
      data: CampaignData
    }) => {
      // state	活动状态 0未开始 1进行中 2公示期 3已结束
      CampaignData.startTime = this.getTime(CampaignData.campaignStartTime)
      CampaignData.endTime = this.getTime(CampaignData.campaignEndTime)

      wx.setStorageSync('campaignCode', CampaignData.campaignCode)
      this.setData({
        campaignCode: CampaignData.campaignCode,
        activeInfo: CampaignData
      })
      // activeTime:true 在活动时间内 false 不在活动时间内
      let activeTime = this.isCurrentTimeBetween({
        startTime: CampaignData.campaignStartTime,
        endTime: CampaignData.campaignEndTime
      })
      console.log(activeTime, '活动时间判断 11111');
      // if (!activeTime) { // 整体活动结束
      //   wx.redirectTo({
      //     url: `/signUp/pages/activeEnd/activeEnd`,
      //   })
      // }
      // 活动状态分为三种 1：报名 2：打卡 3：抽奖
      if (CampaignData.campaignCode.indexOf('phase') > -1) { // 报名
        // 查询报名状态
        getEnrollInfo({
          campaignCode: this.data.campaignCode
        }).then(({
          data: EnrollData
        }) => {
          if (CampaignData.state == 1) { // 活动状态是进行中 不考虑没有中奖的情况
            if (!EnrollData) { //没有报名信息 立即报名
              // 未报名 按钮：立即报名
              this.setData({
                HomepageStatus: 1,
                btnTitle: '立即报名',
                loading: false
              })
            } else if (EnrollData && EnrollData.status != 2) { // 有报名信息 报名信息 报名状态不是2 2代表未中奖(status 0 1中奖 )
              // 已报名 未中奖 按钮：查看报名结果
              this.setData({
                HomepageStatus: 2,
                btnTitle: '查看报名结果',
                loading: false
              })
            } else if (EnrollData && EnrollData.status == 2) {
              // 报名 但是未中奖
              wx.redirectTo({
                url: `/signUp/pages/overPage/overPage?type=zhaomuNot&BMNum=${EnrollData.enrollCount || null}&status=${EnrollData.status || null}`,
              })
            }
          } else if (CampaignData.state == 2) { // 公示期 1：报名未中奖 2：报名已中奖 3未报名
            if (EnrollData && EnrollData.status != 2) { // 1||2
              // 公示期 有没有中奖都跳转到报名成功页面
              // 已报名 未中奖 按钮：查看报名结果
              this.setData({
                HomepageStatus: 2,
                btnTitle: '查看报名结果',
                loading: false
              })
            } else { //错过报名
              wx.redirectTo({
                url: `/signUp/pages/overPage/overPage?type=zhaomuNot&BMNum=${EnrollData?.enrollCount || null}&status=${EnrollData?.status || null}`,
              })
            }
          } else if (CampaignData.state == 3 || CampaignData.state == 0) { // 活动结束 有没有报名信息都代表报名失败
            wx.redirectTo({
              url: `/signUp/pages/overPage/overPage?type=zhaomuNot&BMNum=${EnrollData?.enrollCount || null}&status=${EnrollData?.status || null}`,
            })
            // 报名结束 有没有报名信息都跳转到报名结束页面

          }
        })
      } else if (CampaignData.campaignCode == 'sign_in') { // 打卡
        if (!activeTime) { // 整体活动结束
          wx.redirectTo({
            url: `/signUp/pages/activeEnd/activeEnd`,
          })
        } else {
          // 查询用户是否参与打卡
          getSignInOpen({
            campaignCode: this.data.campaignCode
          }).then(({
            data: SignInData
          }) => {
            // startActivityTime	打卡活动开始时间	isOpen	是否开启打卡 endActivityTime	打卡活动结束时间 signInEndTime	打卡结束时间 state	状态 0未完成 1已完成 2打卡失败 signInStartTime	打卡开始时间
            console.log(SignInData, 'SignInData 是否开启打卡');
            this.setData({
              SignInData
            })
            // SignInData.state状态 0未完成 1已完成 2打卡失败
            // 判断距离结束时间是否还有6天
            let newDate = new Date(CampaignData.campaignEndTime); //结束时间
            let day = newDate.getDate();
            newDate.setDate(day - 6) //最后开启打卡时间
            console.log(newDate, '最后打卡时间');
            // 获取当前日期时间
            var currentDate = new Date();
            // 获取待比较的日期
            var compareDate = new Date(newDate);
            if (currentDate > compareDate) { //  还剩6天 完不成打卡的
              // 当前日期大于最后打卡时间 跳到结束页面
              console.log(SignInData,'SignInData');
              if (!SignInData|| SignInData.state == null) {
                // 没有开启打卡
                wx.redirectTo({
                  url: `/signUp/pages/activeEnd/activeEnd?type=SignInEnd`,
                })
              } else if (SignInData && SignInData.state == 2 && SignInData.restartCount == 1) {
                // 有资格打卡 但是打卡失败的
                wx.redirectTo({
                  url: `/signUp/pages/activeEnd/activeEnd?type=SignInNot`,
                })
              } else if (!SignInData.isOpen && SignInData.state == 2 && SignInData.restartCount > 1) {
                // 打卡失败 进入重启页面 或者重启过也打卡失败 打卡时间已结束 未完成打卡 restartPage
                wx.redirectTo({
                  url: `/signUp/pages/restartPage/restartPage`,
                })
              }else if (SignInData.isOpen && SignInData.state === 0) { // 已经开启打卡 然后打卡未完成
                // 开启打卡 打卡未完成 4 去打卡
                this.setData({
                  HomepageStatus: 4,
                  btnTitle: '去打卡',
                  // loading: false
                })
                wx.redirectTo({
                  url: '/signUp/pages/clock/clock',
                })
              } else if (SignInData.isOpen && SignInData.state == 1) { // 开启打卡 打卡已完成 需要去抽奖
                getLotteryUser({ // 是否抽奖
                  campaignCode: this.data.campaignCode
                }).then(({
                  data: LotteryData
                }) => {
                  // 未抽奖 需要去抽奖
                  if (LotteryData && (LotteryData.totalCount - LotteryData.surplusCount == 0)) {
                    // 没有抽过奖 跳抽奖列表
                    this.setData({
                      HomepageStatus: 5,
                      btnTitle: '抽取惊喜礼品',
                      // loading: false
                    })
                    wx.redirectTo({
                      url: '/signUp/pages/prizeDraw/prizeDraw',
                    })
                  } else if (LotteryData.surplusCount == 0 && !LotteryData.isShare) {
                    console.log(LotteryData, 'LotteryData中奖信息');
                    // 抽过奖 跳中奖页面 抽取过一次奖 还没有分享
                    that.setData({
                      HomepageStatus: 6,
                      btnTitle: '抽取惊喜礼品',
                      // loading: false
                    })
                    wx.redirectTo({
                      url: '/signUp/pages/Winning/Winning',
                    })
                  } else if (LotteryData.surplusCount != 0) {
                    console.log(LotteryData, 'LotteryData中奖信息');
                    // 抽过奖 跳中奖页面 抽取过一次奖 分享过 还剩一次抽奖机会
                    that.setData({
                      HomepageStatus: 7,
                      btnTitle: '抽取惊喜礼品',
                      // loading: false
                    })
                    wx.redirectTo({
                      url: '/signUp/pages/Winning/Winning',
                    })
                  } else if (LotteryData.surplusCount == 0 && LotteryData.isShare) {
                    // 两次抽奖机会都用完 查看中奖页面
                    that.setData({
                      HomepageStatus: 8,
                      btnTitle: '查看我的体验报告',
                      // loading: false
                    })
                    wx.redirectTo({
                      url: '/signUp/pages/Winning/Winning',
                    })
                  }
                  console.log(that.data.HomepageStatus, 'HomepageStatus');
                })
              } 
            } else if (SignInData.isOpen && SignInData.state === 0) { // 已经开启打卡 然后打卡未完成
              // 开启打卡 打卡未完成 4 去打卡
              this.setData({
                HomepageStatus: 4,
                btnTitle: '去打卡',
                // loading: false
              })
              wx.redirectTo({
                url: '/signUp/pages/clock/clock',
              })
            } else if (SignInData.isOpen && SignInData.state == 1) { // 开启打卡 打卡已完成 需要去抽奖
              getLotteryUser({ // 是否抽奖
                campaignCode: this.data.campaignCode
              }).then(({
                data: LotteryData
              }) => {
                // 未抽奖 需要去抽奖
                if (LotteryData && (LotteryData.totalCount - LotteryData.surplusCount == 0)) {
                  // 没有抽过奖 跳抽奖列表
                  this.setData({
                    HomepageStatus: 5,
                    btnTitle: '抽取惊喜礼品',
                    // loading: false
                  })
                  wx.redirectTo({
                    url: '/signUp/pages/prizeDraw/prizeDraw',
                  })
                } else if (LotteryData.surplusCount == 0 && !LotteryData.isShare) {
                  console.log(LotteryData, 'LotteryData中奖信息');
                  // 抽过奖 跳中奖页面 抽取过一次奖 还没有分享
                  that.setData({
                    HomepageStatus: 6,
                    btnTitle: '抽取惊喜礼品',
                    // loading: false
                  })
                  wx.redirectTo({
                    url: '/signUp/pages/Winning/Winning',
                  })
                } else if (LotteryData.surplusCount != 0) {
                  console.log(LotteryData, 'LotteryData中奖信息');
                  // 抽过奖 跳中奖页面 抽取过一次奖 分享过 还剩一次抽奖机会
                  that.setData({
                    HomepageStatus: 7,
                    btnTitle: '抽取惊喜礼品',
                    // loading: false
                  })
                  wx.redirectTo({
                    url: '/signUp/pages/Winning/Winning',
                  })
                } else if (LotteryData.surplusCount == 0 && LotteryData.isShare) {
                  // 两次抽奖机会都用完 查看中奖页面
                  that.setData({
                    HomepageStatus: 8,
                    btnTitle: '查看我的体验报告',
                    // loading: false
                  })
                  wx.redirectTo({
                    url: '/signUp/pages/Winning/Winning',
                  })
                }
                console.log(that.data.HomepageStatus, 'HomepageStatus');
              })
            } else if (!SignInData.isOpen && SignInData.state == 2) {
              // 打卡失败 进入重启页面 或者重启过也打卡失败 打卡时间已结束 未完成打卡 restartPage
              wx.redirectTo({
                url: `/signUp/pages/restartPage/restartPage`,
              })
            } else if (!SignInData.isOpen || !SignInData.restartCount) {
              // 报名成功 中奖、已购买 还未开启打卡（没有打卡次数）
              // 3 按钮：开启体验之旅
              this.setData({
                HomepageStatus: 3,
                btnTitle: '开启体验之旅',
                // loading: false
              })
              wx.redirectTo({
                url: '/signUp/pages/Participate/Participate',
              })
            }
          })
        }


      }
    })
  },
  getTime(time) {
    let day = time.split(" ")
    return day[0]
  },
  // 判断当前时间是否在两个时间段之间
  isCurrentTimeBetween(signUpDate) {
    let {
      startTime,
      endTime
    } = signUpDate
    if (typeof startTime === 'string') {
      // ios 解析不出来 年月 2020-05
      if (startTime.length < 8) {
        startTime = `${time}-1`;
      }
      startTime = new Date(startTime.replace(/-/g, '/').replace('T', ' ')).getTime();
    }
    if (typeof endTime === 'string') {
      // ios 解析不出来 年月 2020-05
      if (endTime.length < 8) {
        endTime = `${time}-1`;
      }
      endTime = new Date(endTime.replace(/-/g, '/').replace('T', ' ')).getTime();
    }
    // 获取当前时间
    const currentTime = new Date();
    // 将开始时间和结束时间转换为日期对象
    const start = new Date(startTime);
    const end = new Date(endTime);
    // 比较当前时间是否在开始时间和结束时间之间
    // 注意：这里使用getTime()方法将日期对象转换为时间戳进行比较
    // 因为直接比较日期对象可能会因为时区等问题导致不准确
    return (currentTime.getTime() >= start.getTime()) && (currentTime.getTime() <= end.getTime());
  },
  // 页面跳转
  submit: app.debounce(async function () {
    let {
      HomepageStatus
    } = this.data
    let path = '' // 跳转路径
    switch (HomepageStatus) {
      case 1: // 立即报名
        wx.$mp.track({
          event: 'sensitive_Apply_click',
        })
        path = '/signUp/pages/indexs/indexs'
        break
      case 2: // 查看报名结果
        wx.$mp.track({
          event: 'sensitive_Apply_click_view',
        })
        path = '/signUp/pages/registrationSuccessful/registrationSuccessful'
        break
      case 3: // 开启体验之旅
        path = '/signUp/pages/Participate/Participate'
        break
      case 4: // 去打卡
        path = '/signUp/pages/clock/clock'
        break
      case 5: // 抽取惊喜礼品
        path = '/signUp/pages/prizeDraw/prizeDraw'
        break
      case 6: // 抽取惊喜礼品
        path = '/signUp/pages/Winning/Winning'
        break
      case 7: // 抽取惊喜礼品
        path = '/signUp/pages/Winning/Winning'
        break
      case 8: // 查看我的体验报告
        path = '/signUp/pages/Winning/Winning'
        break
    }

    // 判断是否注册
    if (app.ifRegister()) {
      wx.navigateTo({
        url: path,
      })
    }
  }),

  showRules() {
    this.setData({
      showRules: !this.data.showRules
    })
  },
  // 跳转公示结果页
  activeRules2: app.debounce(async function () {
    wx.$mp.navigateTo({
      url: '/signUp/pages/announcement/announcement',
    })
  }),
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    // 页面分享
    return {
      title: '和我一起体验「极简护肤」开启七天打卡活动',
      imageUrl: wx.$config.ossImg + '/signUp/share.png',
      path: '/signUp/pages/signUp/signUp',
    }
  }
})
