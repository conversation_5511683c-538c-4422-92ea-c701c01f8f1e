<template>
  <layout>
    <template v-slot:header>
      <searchForm :formParams="searchFields" :disabled="!$hasPermission('custom:search')" @cancel="whenClickReset" @ok="whenClickSearch">
        <a-form-item name="templateName">
          <a-input placeholder="页面名称" v-model:value="searchFields.templateName" allowClear></a-input>
        </a-form-item>
        <!-- <a-form-item name="publish">
          <a-select v-model:value="searchFields.publish" placeholder="页面状态" allowClear>
            <a-select-option :value="1">已发布</a-select-option>
            <a-select-option :value="0">未发布</a-select-option>
          </a-select>
        </a-form-item> -->
        <a-form-item name="groupName">
          <a-input placeholder="页面组名称" v-model:value="searchFields.groupName" allowClear></a-input>
        </a-form-item>
      </searchForm>
    </template>
    <template v-slot:topLeft>
      <a-space>
        已选 {{ selectedRowKeys.length }} 条
      </a-space>
    </template>
    <template v-slot:topRight>
      <a-space>
        <!-- <a-button>移动到组</a-button> -->
        <a-button type="primary" @click="addEdit('',0)" :disabled="!$hasPermission('custom:add')">新建页面</a-button>
      </a-space>
    </template>
    <template v-slot="{ height }">
      <a-table :row-selection="rowSelection" :indentSize="20" row-key="id" :scroll="{ scrollToFirstRowOnChange: true, y: height - 88, x: '100%' }" :dataSource="dataSource" :columns="thisFields.tableHeader" :pagination="pagination" :loading="loading" @change="whenPaginationChange">
        <template #bodyCell="{ record, index, column}">
          <template v-if="column.dataIndex === 'index'">{{ (current - 1) * pageSize + 1 + index }}</template>
          <template v-if="column.dataIndex === 'publish'">{{['未发布','已发布'][record.publish]}}</template>
          <template v-if="column.dataIndex === 'action'">
            <a-button type="link" @click="addEdit(record,1)" :disabled="!$hasPermission('custom:edit')">编辑</a-button>
            <a-divider type="vertical" />
            <!-- <a-button type="link"><a-button type="link">数据</a-button></a-button>
            <a-divider type="vertical" /> -->
            <a-button @click="extendChange(record)" :disabled="!$hasPermission('custom:extend')" type="link">推广</a-button>
            <a-divider type="vertical" />
            <a-popconfirm title="是否确定删除该数据？" :disabled="!$hasPermission('custom:del')" @confirm="handleDelete(record)">
              <a-button type="link" :disabled="!$hasPermission('custom:del')">删除</a-button>
            </a-popconfirm>
          </template>
        </template>
      </a-table>
    </template>
  </layout>
  <miniExtendDialog v-model:value="thisFields.extendVisble" :record="thisFields.extendRecord"></miniExtendDialog>
  <!-- templateType	模板类型 1常规页面 2系统页面 3定制页面 4弹窗页面 -->
  <!-- pageType	页面类型 0默认(二级自定义页面) 1首页 2会员中心 3xx 4 -->
  <!-- isOnly	同类型是否只存在一个已发布 0否 1是 -->
  <!--  pageSetting  页面状态的配置数据
      [
        {key:'1',value:'常规页',},
        {key:'2',value:'加载页',},
        {key:'3',value:'开屏页',},
        {key:'4',value:'弹窗',},
      ]
   -->
  <addCustom :data="thisFields.data" :visible="thisFields.addVisible" @cancel="thisFields.addVisible=false" @ok="addSuccess"></addCustom>
  <a-modal title="预览二维码" :open="thisFields.previewVisible" width="700px" @cancel="thisFields.previewVisible=false" :footer="null">
    <div style="display:flex;flex-direction:column;align-items:center">
      <img :src="thisFields.codeUrl" alt="">
      <div style="margin-top:20px;">手机扫码预览 <a-button type="link" @click="donwload">下载</a-button></div>
    </div>
  </a-modal>

</template>
<script setup>
import { message } from 'ant-design-vue'
import { templateList, deleteTemplate, miniCode, publishTemplate } from '@/http/index.js'
import addCustom from './addCustom.vue'
import { usePagination } from 'vue-request'
import { cloneDeep } from 'lodash'


// 分页数据
const total = ref(0)
const pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ['bottomLeft']
  }
})

// 搜索数据
const getDefaultSearchFields = () => ({
  templateName: '',
  publish: null,
  groupName: '',
  templateType: 3,//	模板类型 1常规页面 2系统页面 3定制页面 4弹窗页面 5开屏页面
})
const searchFields = reactive(getDefaultSearchFields())
// 搜索数据格式处理
const getParams = () => {
  let params = cloneDeep(searchFields)
  return params
}
// 页面数据
const thisFields = reactive({
  id: '',
  type: 0, // 0-新增 1-编辑 2-查看 3-复制
  visible: false, // 详情
  extendVisble: false,//推广
  extendRecord: {},
  previewVisible: false,// 预览二维码页面
  templateName: '',// 预览模板名称
  codeUrl: '',// 二维码图片
  tableHeader: [
    // { title: '序号', dataIndex: 'index', align: 'center', width: 80 },
    { title: 'ID', dataIndex: 'id', align: 'center', ellipsis: true, width: 80 },
    { title: '模板编号', dataIndex: 'templateCode', align: 'center', ellipsis: true, width: 100 },
    { title: '页面名称', dataIndex: 'templateName', align: 'center', ellipsis: true, width: 140 },
    {
      title: '页面组', dataIndex: 'groupName', align: 'center', ellipsis: true, width: 140,
      customRender({ text, record }) {
        return text || '--'
      },
    },
    // { title: '页面状态', dataIndex: 'publish', key: 'publish', align: 'center', ellipsis: true, width: 100 },
    // { title: '最近更新时间', dataIndex: 'modified', align: 'center', ellipsis: true, width: 160 },
    {
      title: '近30天浏览人数', dataIndex: 'numberView', align: 'center', ellipsis: true, width: 130,
      customRender({ text, record }) {
        return text || '--'
      },
    },
    {
      title: '近30天访客人数', dataIndex: 'numberVisitors', align: 'center', ellipsis: true, width: 130,
      customRender({ text, record }) {
        return text || '--'
      },
    },
    { title: '操作', dataIndex: 'action', align: 'center', width: 300, fixed: 'right' }
  ],
  addVisible: false,
  data: null
})
// 选中的元素
const selectedRows = ref([])
// 选中数据操作
const rowSelection = computed(() => {
  return {
    type: 'checkbox',
    selectedRowKeys: selectedRowKeys.value,
    // 选中单个
    onSelect(record, selected, rows, e) {
      if (selected) {
        selectedRows.value.push(record)
      } else {
        let index = selectedRowKeys.value.indexOf(record.id)
        if (index > -1) {
          selectedRows.value.splice(index, 1)
        }
      }
    },
    // 全选
    onSelectAll(selected, rows, changeRow) {
      if (selected) {
        changeRow.forEach((item) => {
          selectedRows.value.push(item)
        })
      } else {
        let ids = changeRow.map((item) => item.id)
        selectedRows.value = selectedRows.value.filter((item) => {
          return !ids.includes(item.id)
        })

      }
    },
  }
})
// 选中元素的key值
const selectedRowKeys = computed(() => {
  return selectedRows.value.map(item => item.id)
})


// 表格数据加载
const { data: dataSource, run, loading, pageSize, current, refresh } = usePagination((param) => {
  return templateList({ ...param, ...getParams() })
}, {
  manual: false, // 修改为false 让其自动执行
  pagination: {
    currentKey: 'pageNum', // 通过该值指定接口 当前页数 参数的属性值
    pageSizeKey: 'pageSize', // 通过该值指定接口 每页获取条数 参数的属性值
    totalKey: 'data.data.count' // 指定 data 中 total 属性的路径
  },
  formatResult: res => { // 返回数据格式化
    total.value = res.data.count
    return res.data.list
  }
})
// 分页变化
const whenPaginationChange = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}
// 搜索
const whenClickSearch = () => {
  run({ pageNum: 1, pageSize: pageSize.value })
}
// 重置
const whenClickReset = () => {
  Object.assign(searchFields, getDefaultSearchFields())
  whenClickSearch()
}

// 删除数据
const handleDelete = (record) => {
  deleteTemplate({ id: record.id }).then(res => {
    message.success('删除成功')
    run({ pageNum: 1, pageSize: pageSize.value })
  })
}

// 添加编辑页面
const addEdit = (record, type) => {
  // type: 0, // 0-新增 1-编辑 2-查看 3-复制
  thisFields.addVisible = true
  thisFields.data = record || null
}

// 添加数据成功
const addSuccess = () => {
  current.value = 1;
  refresh()
}

// 预览页面
const preview = (record) => {
  miniCode({
    page: 'pages/customPreview/customPreview',
    scene: `id=${record.id}`,
    trial: 1, // 1-体验版 0-正式版本
    width: 200,
  }).then((data) => {
    const blob = new Blob([data], { type: "image/png" });
    const objectUrl = URL.createObjectURL(blob);
    thisFields.templateName = record.templateName
    thisFields.previewVisible = true
    thisFields.codeUrl = URL.createObjectURL(blob)
  });
}

// 下载
const donwload = () => {
  const a = document.createElement("a");
  document.body.appendChild(a);
  a.setAttribute("style", "display:none");
  a.setAttribute("href", thisFields.codeUrl);
  a.setAttribute("download", thisFields.templateName + "预览.png");
  a.click();
};
const extendChange = (record) => {

  thisFields.extendVisble = true
  thisFields.extendRecord = record
  console.log("🚀 ~ extendChange ~ record:", thisFields.extendRecord)
}
</script>
