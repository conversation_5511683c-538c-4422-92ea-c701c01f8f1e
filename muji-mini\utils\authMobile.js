// 统一处理获取用户手机号
import {
  getPhoneNumber
} from '../api/index';

// 统一处理授权手机号
const authMobile = e => {
  const {
    errMsg,
    code
  } = e.detail
  return new Promise(resolve => {
    if (errMsg === 'getPhoneNumber:ok') {
      getPhoneNumber({
        code
      }).then((res) => {
        const {
          phoneNumber
        } = res.data;
        resolve({
          success: res.success,
          data: phoneNumber
        })
      })
    } else {
      resolve({
        success: false
      })
    }
  })
}


export default wx.$authMobile = authMobile;
