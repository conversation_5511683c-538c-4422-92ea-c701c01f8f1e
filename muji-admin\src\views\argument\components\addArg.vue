<template>

  <a-modal :title="title" width="576px" v-if="open" placement="right" :maskClosable="false" :closable="true" :open="open" @cancel="onClose">
    <!-- <a-spin :spinning="loading"> -->

    <a-form class="form" ref="addForm" :model="addParams" :rules="rules" :labelCol="{ style: 'width:120px' }">
      <a-form-item label="一级渠道名称" name="parentId">
        <div style="display:flex;align-items: center;">
          <a-select ref="select" v-model:value="addParams.parentId" allowClear :disabled="disabled" :options="OnetagsOptions" :getPopupContainer="triggerNode => triggerNode.parentNode" style="width:300px;" :fieldNames="{ label: 'channelName', value: 'id' }" optionFilterProp="name" showSearch placeholder="请选择一级渠道"></a-select>
          <div @click="openOne=true" style="margin-left: 10px;">找不到，<span style="color:#6a6bbf;cursor: pointer;">去创建</span></div>
        </div>

      </a-form-item>
      <a-form-item label="二级渠道名称" name="channelName">
        <a-input placeholder="请输入二级渠道名称" style="width:300px;" v-model:value="addParams.channelName" show-count :maxlength="30" />
      </a-form-item>
      <a-form-item label="二级渠道参数" name="channelParam">
        <a-input :disabled="disabled" placeholder="请输入二级渠道参数" style="width:300px;" v-model:value="addParams.channelParam" show-count :maxlength="30" />
        <div class=" global-tip">

          只能输入大小写字母和数字，渠道参数将用于渠道连接等的生成
        </div>
      </a-form-item>

    </a-form>
    <!--  -->
    <!-- </a-spin> -->
    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="ok" :loading="loading">确定</a-button>
      </a-space>
    </template>

  </a-modal>
  <a-modal title="新建一级渠道" width="576px" v-if="openOne" placement="right" :maskClosable="false" :closable="true" :open="openOne" @cancel="oneonClose">
    <!-- <a-spin :spinning="loading"> -->

    <a-form class="form" ref="addFormOne" :model="addParamsOne" :rules="rulesOne" :labelCol="{ style: 'width:120px' }">

      <a-form-item label="一级渠道名称" name="channelName">
        <a-input placeholder="请输入一级渠道名称" style="width:300px;" v-model:value="addParamsOne.channelName" show-count :maxlength="30" />
      </a-form-item>
      <a-form-item label="一级渠道参数" name="channelParam">
        <a-input placeholder="请输入一级渠道参数" style="width:300px;" v-model:value="addParamsOne.channelParam" show-count :maxlength="30" />
        <div class=" global-tip">

          只能输入大小写字母和数字，渠道参数将用于渠道连接等的生成
        </div>
      </a-form-item>

    </a-form>
    <!--  -->
    <!-- </a-spin> -->
    <template #footer>
      <a-space>
        <a-button @click="oneonClose">取消</a-button>
        <a-button type="primary" @click="oneok" :loading="loadingOne">确定</a-button>
      </a-space>
    </template>

  </a-modal>
</template>
<script setup>
import { promotion_channelAdd, promotion_channelUpdate, promotion_channeloneChannelList } from '@/http/index.js'
import { useGlobalStore } from '@/store'
import { message, Modal } from "ant-design-vue";


const global = useGlobalStore()
const addForm = ref(null)
const addFormOne = ref(null)
import { cloneDeep } from 'lodash'
import { onMounted } from 'vue';
const emit = defineEmits(['ok', 'cancel', 'okAdd'])
const props = defineProps({
  // 操作抽屉打开关闭
  visible: {
    type: Boolean,
    default: false
  },
  // 数据ID
  id: {
    type: String || Number,
    default: ''
  },
  // 操作类型
  type: {
    type: Number,
    default: 0, // 0-新增 1-编辑  2-查看
  },
  item: {
    type: Object,
    default: {}
  }

})
// 置灰
const disabled = computed(() => {


  return props.type == 1
})

// 标题
const title = computed(() => {
  return ['新建', '编辑', '查看'][props.type] + '渠道'
})

const { open, addParams, rules, loading, openOne, OnetagsOptions, addParamsOne, rulesOne, loadingOne } = toRefs(reactive({
  open: props.visible,
  OnetagsOptions: [],
  openOne: false,
  loading: false,
  loadingOne: false,
  addParams: {
    channelName: null,
    channelParam: null,
    parentId: null
  },
  addParamsOne: {
    channelName: null,
    channelParam: null,

  },
  rules: {
    channelName: [{ required: true, message: '请输入二级渠道名称', trigger: ['blur', 'change'] }],
    channelParam: [{ required: true, message: '请输入二级渠道参数', trigger: ['blur', 'change'] }],
    parentId: [{ required: true, message: '请选择一级渠道', trigger: ['blur', 'change'] }],
  },
  rulesOne: {
    channelName: [{ required: true, message: '请输入一级渠道名称', trigger: ['blur', 'change'] }],
    channelParam: [{ required: true, message: '请输入一级渠道参数', trigger: ['blur', 'change'] }],

  }
})
);
// onMounted(() => {
//     initData()
// })
watch(() => props.visible, (value) => {
  // 弹窗操作
  open.value = props.visible
  addParams.value = {
    channelName: null,
    channelParam: null,
    parentId: null

  },
    promotion_channeloneChannelList().then(res => {
      OnetagsOptions.value = res.data

    })
  addForm.value?.resetFields()
  if (props.id) {
    console.log(props.item);
    // console.log("🚀 ~ watch ~ props.addItem:", props.item)
    if (props.id) {
      addParams.value = {
        ...props.item

      }

    }
  }

})
defineExpose({
  addParams
})



const oneonClose = () => {
  addParamsOne.value = {
    channelName: null,
    channelParam: null,

  }
  openOne.value = false
}
const oneok = () => {
  addFormOne.value.validate().then(res => {
    let params = cloneDeep(addParamsOne.value)
    loadingOne.value = true
    promotion_channelAdd(params).then(res => {
      message.success(res.msg);
      emit('okAdd',)
      oneonClose()
      promotion_channeloneChannelList().then(res => {
        OnetagsOptions.value = res.data

      })
    }).finally(() => {
      loadingOne.value = false
    })


  })
}


// 关闭弹窗
const onClose = () => {
  emit('cancel')
}

// 确定
const ok = () => {
  console.log(11111);
  addForm.value.validate().then(res => {

    let params = cloneDeep(addParams.value)
    console.log(props.id);
    loading.value = true
    if (props.id) {
      console.log('编辑', params);
      // params.channelName = params.channelName
      params.id = params.twoId
      delete params.twoId
      delete params.parentId
      // delete params.channelName
      delete params.channelParam

      promotion_channelUpdate(params).then(res => {
        message.success(res.msg);
        emit('ok', props.id)
      }).finally(() => {
        loading.value = false
      })
    } else {
      // console.log(params, 'xin');
      promotion_channelAdd(params).then(res => {
        message.success(res.msg);
        emit('ok', props.id)
      }).finally(() => {
        loading.value = false
      })
    }

  })
}



</script>

<style scoped lang="scss">
.form {
}
:deep(.searchForm .ant-form-item) {
  // margin-bottom: 16px;
}

:deep(.anticon-eye-invisible) {
  display: none;
}

.member-role {
  background: #f2f2f2;
  margin-bottom: 20px;
  padding: 5px 10px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &:nth-child(odd) {
    margin-right: 35px;
  }
}
</style>
