package com.dz.ms.sales.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/30
 */
@Data
public class CampaignBuyerDTO {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("活动标识")
    private String campaignCode;

    @ApiModelProperty("商品sku")
    private String sku;

    @ApiModelProperty("购买渠道 1全部 2会小 3线下 4天猫 5京东 6官小 多个逗号隔开")
    private String buyChannel;

    @ApiModelProperty("渠道开始时间")
    private Date channelStartTime;

    @ApiModelProperty("渠道结束时间")
    private Date channelEndTime;

}
