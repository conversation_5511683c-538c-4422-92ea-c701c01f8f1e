package com.dz.ms.user.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.core.dto.KeyValueDTO;
import com.dz.ms.user.dto.AccountLoginDTO;
import com.dz.common.core.dto.user.SysUserDTO;
import com.dz.ms.user.dto.UpdatePasswordDTO;
import com.dz.ms.user.dto.UserRoleDTO;
import com.dz.ms.user.dto.dkeyam.req.CallbackGetAllUserParam;
import com.dz.ms.user.entity.SysUser;

import java.util.List;
import java.util.Map;

/**
 * 系统用户信息接口
 * @author: Handy
 * @date:   2022/2/4 23:04
 */
public interface SysUserService extends IService<SysUser> {

    /**
     * 保存系统用户
     * @param param
     * @return
     */
    Long saveSysUser(SysUserDTO param);

    /**
     * 账号密码登录
     * @param param
     * @return
     */
    public SysUserDTO passwordLogin(AccountLoginDTO param);

    /**
     * CRM后台宁盾认证
     * @param param 入参
     * @return SysUserDTO
     */
    SysUserDTO strongAuthenticate(AccountLoginDTO param);

    /**
     * 用户密码确认并发送短信验证码
     * @param param
     * @return
     */
    boolean passwordCheck(AccountLoginDTO param);

    /**
     * 密码加短信验证码登录
     * @param param
     * @return
     */
    SysUserDTO passwordSmsLogin(AccountLoginDTO param);

    /**
     * 获取用户角色ID
     *
     * @param uid
     * @param tenantId
     * @return
     */
    Long getUserRole(Long uid, Long tenantId);

    /**
     * 获取用户可用门店ID列表
     * @param uid
     * @param tenantId
     * @return
     */
    List<Long> getUserStore(Long uid, Long tenantId);

    /**
     * 绑定用户角色
     * @param param
     */
    public void bindRole(UserRoleDTO param);

    /**
     * 根据账号获取租户信息
     * @param username
     * @return
     */
    List<KeyValueDTO> getTenantsByUsername(String username);

    /**
     * 根据角色Id列表获取角色对应绑定用户数量
     * @param roleIds
     * @return
     */
    Map<Long,Integer> getRoleUserCountByRoleIds(List<Long> roleIds);

    /**
     * 重置密码
     * @param uid
     */
    void passwordReset(Long uid);

    /**
     * 修改密码
     * @param param
     */
    void passwordUpdate(UpdatePasswordDTO param);

    /**
     * 登出
     */
    void logout();

    /**
     *根据userIdList查询系统用户信息
     * @param userIds
     * @return
     */
    List<SysUser> getByIds(List<Long> userIds,Long tenantId);

    /**
     * 根据ID删除系统用户信息
     * @param id
     */
    void deleteSysUserById(Long id);

    /**
     * 根据UID查询系统用户信息
     * @return
     */
    SysUserDTO getUserByUid(Long id, Long tenantId);
    
    /**
     * 根据UID查询系统用户信息(包含角色id)
     * @return
     */
    SysUserDTO getUserContainRootIdByUid(Long id, Long tenantId);

    /**
     * 查询当前登录系统用户信息(包含角色id)
     * @return
     */
    SysUserDTO getLoginUserContainRootId();

    /**
     * 宁盾外部用户认证
     * @param param 入参
     * @return SysUserDTO
     */
    SysUserDTO dKeyAmCallbackAuthenticate(AccountLoginDTO param);

    /**
     * 宁盾获取单个用户的信息
     * @param param 入参
     * @return SysUserDTO
     */
    SysUserDTO dKeyAmCallbackGetOneUser(AccountLoginDTO param);

    /**
     * 宁盾同步所有用户的信息
     * @param param 入参
     * @return IPage<SysUserDTO>
     */
    IPage<SysUserDTO> dKeyAmCallbackGetAllUserReq(CallbackGetAllUserParam param);
    
}
