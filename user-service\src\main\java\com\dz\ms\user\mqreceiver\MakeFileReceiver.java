/*
package com.dz.ms.user.mqreceiver;

import com.rabbitmq.client.Channel;
import com.xxy.cd.ms.basic.controller.bff.strategy.DownloadStrategy;
import com.xxy.cd.ms.basic.dto.bff.basic.DownloadMqParamDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.Map;

*/
/**
 * @description:
 * @author: hongbo.wang
 * @date: 2020/7/13
 *//*

@Component
@Slf4j
public class MakeFileReceiver {

    @Autowired
    private ApplicationContext applicationContext;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "manager.makefile.type.queue", durable = "true"),
            exchange = @Exchange(value = "manager.makefile", durable = "true", type = "topic"),
            key = "#"
    ))
    @RabbitHandler
    public void makeFileMsg(@Payload DownloadMqParamDTO param, @Headers Map<String, Object> headers,
                            Channel channel){
        log.info("===============listen make file queue msg start");
        String beanClassId = param.getBeanClassId();
        DownloadStrategy strategy = (DownloadStrategy) applicationContext.getBean(beanClassId);
        strategy.makeFile(param);
    }

    rabbitTemplate.convertAndSend("manager.makefile", "manager.makefile.type.queue", mqParam);
}
*/
