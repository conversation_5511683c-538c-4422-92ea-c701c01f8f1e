package com.dz.ms.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.basic.entity.MpMsg;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 小程序/公众号模板消息Mapper
 * @author: Handy
 * @date:   2022/02/03 23:16
 */
@Repository
public interface MpMsgMapper extends BaseMapper<MpMsg> {

    /** 根据场景获取小程序订阅消息ID列表 */
    List<String> getSubscribeMsgIds(@Param("scene") Integer scene, @Param("tenantId")Long tenantId);

    /** 根据模板编号获取小程序订阅消息ID列表 */
    MpMsg getSubscribeMsgByCode(@Param("templateCode")String templateCode, @Param("tenantId")Long tenantId);

}
