package com.dz.ms.basic.service.impl;

import com.alibaba.fastjson.JSON;
import com.dz.common.core.enums.WechatApiEnum;
import com.dz.common.core.service.WechatRequestSevice;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.ms.basic.dto.UrlLinkDto;
import com.dz.ms.basic.service.MaterialInfoService;
import com.dz.ms.basic.service.MpConfigService;
import com.dz.ms.basic.service.WechatApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class WechatApiServiceImpl implements WechatApiService {

    @Resource
    private MpConfigService mpConfigService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private WechatRequestSevice wechatRequestSevice;

    @Override
    public UrlLinkDto getMiniappUrlLink(String path, String query, Integer expireType, String envVersion) {
        long times1 = System.currentTimeMillis();
        Long tenantId = SecurityContext.getUser().getTenantId();
        Map<String, Object> params = new HashMap<String, Object>();
        if(!StringUtils.isBlank(path)) {
            params.put("path", path);
        }
        if(!StringUtils.isBlank(query)) {
            params.put("query", query);
        }
        params.put("env_version", envVersion);
        Long expireTime = times1+30*24*60*60;
        if (expireType != null && expireType == 0) {
            params.put("expire_type", expireType);
            params.put("expire_time",expireTime );
        }else if (expireType != null && expireType == 1) {
            params.put("expire_type", expireType);
            params.put("expire_interval", 30);
        }
        log.info("获取urlLink 短链接入参：{}", JSON.toJSONString(params));
        UrlLinkDto result = wechatRequestSevice.request(WechatApiEnum.API_WX_URL_LINK, SecurityContext.getUser().getTenantId(), UrlLinkDto.class, params);
        log.info("获取urlLink 短链接出参：{}", JSON.toJSONString(result));
        result.setExpire_time(expireTime);
        return result;
    }
}
