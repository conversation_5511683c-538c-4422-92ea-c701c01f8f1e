<template>
  <div>
    <!--
    :slides-per-view="3"
    :space-between="50"
    navigation
    :pagination="{ clickable: true }"
    :scrollbar="{ draggable: true }"
    -->
    <swiper class="my-carousel" :style="[{ height: `${200}px` }]" :modules="modules" :slides-per-view="1" :space-between="0" :navigation="false" :pagination="item.showIndicator" :scrollbar="false" :autoplay="item.autoplay ? ({ delay: item.interval }) : false" :speed="item.duration" :loop="item.circular" @swiper="onSwiper" @slideChange="onSlideChange">
      <template v-for="(value, index) in item.list" :key="index">
        <swiper-slide class="my-carousel-item">
          <img class="my-carousel-img" :src="value.imgSrc ? value.imgSrc : errorImg" alt="">
        </swiper-slide>
      </template>
    </swiper>
    <div style="margin-top:20px">

      <!-- <a-form-item label="轮播宽度" :name="['content','style','width']" :rules="formRules.width">
        <a-input-number style="width: 200px;" v-model:value="item.style.width" :step="1" step-strictly :min="0" @blur="blur">
          <template #suffix>像素（px）</template>
        </a-input-number>
      </a-form-item> -->
      <a-form-item label="轮播高度" :name="['content', 'style', 'height']" :rules="formRules.height">
        <a-input-number style="width: 200px;" v-model:value="item.style.height" :step="1" step-strictly :min="0" @blur="blur">
          <template #suffix>像素（px）</template>
        </a-input-number>
        <!-- <div class="global-tip">建议尺寸：670*400；比例：5：3</div> -->
      </a-form-item>
      <!-- <a-form-item label="是否自动切换">
        <a-radio-group v-model:value="item.autoplay">
          <a-radio-button :value="true">是</a-radio-button>
          <a-radio-button :value="false">否</a-radio-button>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="自动切换间隔">
        <a-input-number style="width: 260px;" v-model:value="item.interval" :step="1" step-strictly :min="0" @blur="blur">
          <template #suffix>毫秒</template>
        </a-input-number>
      </a-form-item>
      <a-form-item label="滑动动画时长">
        <a-input-number style="width: 260px;" v-model:value="item.duration" :step="1" step-strictly :min="0" @blur="blur">
          <template #suffix>毫秒</template>
        </a-input-number>
      </a-form-item>
      <a-form-item label="是否无限滑动">
        <a-radio-group v-model:value="item.circular">
          <a-radio-button :value="true">是</a-radio-button>
          <a-radio-button :value="false">否</a-radio-button>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="轮播指示器">
        <a-radio-group v-model:value="item.showIndicator">
          <a-radio-button :value="true">显示</a-radio-button>
          <a-radio-button :value="false">不显示</a-radio-button>
        </a-radio-group>
      </a-form-item> -->

    </div>
  </div>
</template>

<script setup>
// import Swiper core and required modules
// import { Autoplay, Navigation, Pagination, Scrollbar, A11y } from 'swiper/modules'

// Import Swiper Vue.js components
import { Swiper, SwiperSlide } from 'swiper/vue'

// Import Swiper styles
import "swiper/css";
// import 'swiper/css'
// import 'swiper/css/navigation'
// import 'swiper/css/pagination'
// import 'swiper/css/scrollbar'
import { watch } from 'vue'
const errorImg = ref('data:image/gif;base64,R0lGODlhAQABAIAAAMLCwgAAACH5BAAAAAAALAAAAAABAAEAAAICRAEAOw==')
const onSwiper = (swiper) => {
  console.log(swiper)
}
const onSlideChange = () => {
  console.log('slide change')
}

const props = defineProps({
  item: {
    type: Object,
    default: () => ({})
  },
  formRules: {
    type: Object,
    default: () => ({})
  },
})

const thisMethods = {
  setFormRules() {
    const formRules = props.formRules
    formRules.width = formRules.width || [{ required: true, message: '本项必选' }]
    formRules.height = formRules.height || [{ required: true, message: '本项必选' }]
  }
}
const blur = () => {
  if (props.item.style.height === undefined) props.item.style.height = props.item.style.height
}



thisMethods.setFormRules()
watch(props.item.list, (val) => {
  console.log("🚀 ~ watch ~ val:", val)

})
</script>

<style scoped lang="scss">
.my-carousel {
  height: 200px;

  .my-carousel-item {
    width: 100%;
    height: 100%;

    .my-carousel-img {
      display: block;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}
</style>
