package com.dz.common.core.enums;

/**
 * 商品类型美剧枚举
 * @author: Handy
 * @date:   2022/07/07 16:36
 */
public enum ProductTypeEnum {

	PRODUCT(1,"商品"),
	SERVICE(2,"服务"),
	COUPON(3,"优惠券"),

	PRODUCTKIT(4,"套组"),
	BB(5,"B币"),
	COUPONACTIVITY(6,"券活动")

	;

	/** 商品类型 */
	private Integer type;
	/** 名称 */
	private String name;

	ProductTypeEnum(Integer type, String name) {
		this.type = type;
		this.name = name;
	}

	public Integer getType() {
		return type;
	}

	public String getName() {
		return name;
	}
}
