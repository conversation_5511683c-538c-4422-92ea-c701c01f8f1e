package com.dz.common.core.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
@ToString
public class CreateOrderResultDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单编号")
    private String orderCode;
    @ApiModelProperty(value = "创建时间戳")
    private Long orderCreateTimeStamp;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "订单总积分")
    private Integer orderPoint;
    @ApiModelProperty(value = "订单总金额")
    private BigDecimal orderAmount;

}
