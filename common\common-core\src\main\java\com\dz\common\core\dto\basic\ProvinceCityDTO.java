package com.dz.common.core.dto.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 省份信息DTO
 * @author: zhaomingcong
 * @date:   2023/09/06
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "省份城市信息")
public class ProvinceCityDTO {

    @ApiModelProperty(value = "省份编码")
    private String provinceCode;
    @ApiModelProperty(value = "省份名称")
    private String provinceName;
    @ApiModelProperty(value = "城市编码")
    private String cityCode;
    @ApiModelProperty(value = "省份名称")
    private String cityName;

    public ProvinceCityDTO(String provinceCode, String provinceName, String cityCode, String cityName) {
        this.provinceCode = provinceCode;
        this.provinceName = provinceName;
        this.cityCode = cityCode;
        this.cityName = cityName;
    }
}
