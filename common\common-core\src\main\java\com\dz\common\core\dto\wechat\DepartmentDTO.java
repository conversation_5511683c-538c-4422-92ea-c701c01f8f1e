package com.dz.common.core.dto.wechat;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 企微部门信息
 * <AUTHOR>
 * @date 2022/07/21 18:10
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
public class DepartmentDTO {

    @ApiModelProperty(value = "部门ID")
    private Long id;
    @ApiModelProperty(value = "部门名称")
    private String name;
    @ApiModelProperty(value = "英文名称")
    @JSONField(name = "name_en")
    private String nameEn;
    @ApiModelProperty(value = "部门负责人的UserID")
    @JSONField(name = "department_leader")
    private List<String> departmentLeader;
    @ApiModelProperty(value = "父部门id")
    private Long parentid;
    @ApiModelProperty(value = "在父部门中的次序值")
    private Integer order;

    public DepartmentDTO(Long id, String name, Long parentid) {
        this.id = id;
        this.name = name;
        this.parentid = parentid;
    }

}
