package com.dz.common.core.filter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dz.common.base.constant.ClientTypeConstant;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.fegin.user.LoginFeginClient;
import com.dz.common.core.threadlocal.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.annotation.Resource;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

@Slf4j
@Component
public class SecurityFilter extends OncePerRequestFilter {

	private final static String[] ACCESS_URL = new String[]{"/check/login","/oms/login","/crm/login","/app/login","/openapi/auth","/callback","/inner","/app/white","/actuator/*","/healthcheck",
			"/app/style_config/get","/app/white/navigation_config/default","app/promotion_page/getParam","/app/privacy_policy/last_info"};

	private final static String SWAGGER_DOCS = "/api-docs";

	@Value("${spring.profiles.active}")
	public String profile;

	@Resource
	private LoginFeginClient loginFeginClient;

	@Override
	protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
		String url = request.getRequestURI();
		if(MediaType.APPLICATION_JSON_VALUE.equals(request.getContentType())) {
			request = new RequestWrapper(request);
		}
		Map<String, String[]> parameterMap = request.getParameterMap();
		String rpc = request.getHeader("rpc");
		log.info("service request {}, rpc:{},param:{}",url, rpc, null == parameterMap ? null : JSON.toJSONString(parameterMap));
		/** 内部请求直接放过 */
		if("1".equals(rpc)) {
			CurrentUserDTO currentUser = new CurrentUserDTO();
			currentUser.setType(NumberUtils.toInt(request.getHeader("type")));
			currentUser.setTenantId(NumberUtils.toLong(request.getHeader("tenantId")));
			currentUser.setUid(NumberUtils.toLong(request.getHeader("uid")));
			currentUser.setStoreId(NumberUtils.toLong(request.getHeader("storeId")));
			SecurityContext.setUser(currentUser);
			filterChain.doFilter(request, response);
			log.info("service request {} ,end",url);
			return;
		}
		/** 白名单请求直接放过 */
		for(String s: ACCESS_URL){
			if(url.equals(s) || url.startsWith(s)){
				log.info("service request {} ,is white",url);
				filterChain.doFilter(request, response);
				log.info("service request {} ,end",url);
				return;
			}
		}
		/** swagger api 直接放过 */
		if("uat,dev".contains(profile) && url.contains(SWAGGER_DOCS)) {
			filterChain.doFilter(request, response);
			return;
		}
		String jwtToken = request.getHeader("token");
		if(StringUtils.isBlank(jwtToken)) {
			jwtToken = request.getParameter("token");
		}
		/** openapi token 白名单处理 */
		boolean isopenapi = url.startsWith("/openapi");
		boolean isWhite = false;
		if(isopenapi) {
			if("PROD".equalsIgnoreCase(profile)){
				if("954f85f6-f095-491a-ba3a-c397eb93b6c3".equals(jwtToken)) {
					isWhite = true;
				}
			}
			else {
				if("954f85f6-f095-491a-ba3a-c397eb93b6c5".equals(jwtToken)) {
					isWhite = true;
				}
			}
			if(isWhite) {
				CurrentUserDTO currentUser = new CurrentUserDTO();
				currentUser.setType(ClientTypeConstant.OPENAPI);
				currentUser.setTenantId(1L);
				SecurityContext.setUser(currentUser);
				filterChain.doFilter(request, response);
				return;
			}
		}
		Result<JSONObject> check = null;
		if(StringUtils.isNotEmpty(jwtToken)) {
			check = loginFeginClient.checkLogin(jwtToken,url);
		}
		//校验jwtToken的合法性
		if (null == check || null == check.getData()) {
			writeError(response,null == check ? ErrorCode.UNAUTHORIZED.getCode() : check.getCode(),null == check ? "未登录" : check.getMsg());
			return;
		}
		JSONObject data = check.getData();
		int type = data.getIntValue("type");
		/** APP端用户不能访问非app接口 */
		boolean isApp = type == ClientTypeConstant.APP || type == ClientTypeConstant.WEB;
		if(isApp) {
			if(!url.startsWith("/app")) {
				writeError(response,ErrorCode.NOT_FOUND.getCode(),ErrorCode.NOT_FOUND.getMessage());
				return;
			}
		}
		/** 导购端用户不能访问非导购接口 */
		else if(type == ClientTypeConstant.EMP) {
			if(!url.startsWith("/emp")) {
				writeError(response,ErrorCode.NOT_FOUND.getCode(),ErrorCode.NOT_FOUND.getMessage());
				return;
			}
		}
		/** crm端用户不能访问非crm接口 */
		else if(type == ClientTypeConstant.CRM) {
			if(!url.startsWith("/crm")) {
				writeError(response,ErrorCode.NOT_FOUND.getCode(),ErrorCode.NOT_FOUND.getMessage());
				return;
			}
		}
		/** oms端用户不能访问非oms接口 */
		if(type == ClientTypeConstant.OMS) {
			if(!url.startsWith("/oms")) {
				writeError(response,ErrorCode.NOT_FOUND.getCode(),ErrorCode.NOT_FOUND.getMessage());
				return;
			}
		}
		/** openapi用户不能访问非openapi接口 */
		if(type == ClientTypeConstant.OPENAPI) {
			if(!isopenapi) {
				writeError(response,ErrorCode.NOT_FOUND.getCode(),ErrorCode.NOT_FOUND.getMessage());
				return;
			}
		}
		CurrentUserDTO currentUser = new CurrentUserDTO();
		currentUser.setType(type);
		if(data.containsKey("id")) {
			currentUser.setUid(data.getLong("id"));
		}
		if(data.containsKey("tid")) {
			currentUser.setTenantId(data.getLong("tid"));
		}
		if(data.containsKey("sid")) {
			currentUser.setStoreId(data.getLong("sid"));
		}
		if(data.containsKey("pt")) {
			currentUser.setPlatform(data.getIntValue("pt"));
		}
		SecurityContext.setUser(currentUser);
		filterChain.doFilter(request, response);
		SecurityContext.setUser(null);
		log.info("service request {} ,end",url);
	}

	private void writeError(HttpServletResponse response,Integer code, String message) throws IOException {
		response.setContentType("application/json; charset=utf-8");
		JSONObject result = new JSONObject();
		result.put("code", code);
		result.put("msg",message);
		response.getWriter().write(result.toJSONString());
	}

}
