<view class="filter-bar">
  <view class="btn-box">
    <view class="toggle-btn">
      <view class="btn-item {{activeValue===item.value && 'active'}}" wx:for="{{toggleBtn}}" wx:key="value" bindtap="toggleSearchType" data-value="{{item.value}}">
        {{ item.label }}
      </view>
    </view>
    <view class="dropdown-filter" bindtap="handleDropdown">{{activeDropDownLabel}}<image style="margin-left: 10rpx;" src="{{$cdn}}/dropdown.png" /></view>
  </view>

  <!-- <view wx:if="{{showDropdown}}" class="dropdown-overlay">
    <view class="mask" bindtap="handleDropdown"></view>
    <view class="dropdown-content">
      <view class="dropdown-item {{activeDropdown === item.value ? 'active' : ''}}" wx:for="{{dropdownList}}" wx:key="value" bindtap="selectDropdownItem" data-value="{{item.value}}">
        {{item.label }}
      </view>
    </view>
  </view> -->
  <basic-popup bindclose="handleDropdown" isShow="{{ showDropdown }}" zIndex="{{1}}">
    <view slot="content">
      <view class="dropdown-container">
        <view class="dropdown-content">
          <view class="dropdown-item {{activeDropdown === item.value ? 'active' : ''}}" wx:for="{{dropdownList}}" wx:key="value" bindtap="selectDropdownItem" data-value="{{item.value}}">
            {{item.label }}
          </view>
        </view>
      </view>
    </view>
  </basic-popup>

</view>