package com.dz.ms.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.core.dto.DownloadQueryParamDTO;
import com.dz.ms.user.entity.MyMsg;
import com.dz.ms.user.entity.SubscriptionMsg;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 我的消息
 */
@Repository
public interface MyMsgMapper extends BaseMapper<MyMsg> {

    /**
     * 我的消息
     * @param userId
     * @param msgCode
     * @param nowDay
     * @return
     */
    List<MyMsg> selectByMsgCode(@Param("userId") String userId, @Param("msgCode") String msgCode, @Param("nowDay") String nowDay);

    List<MyMsg> selectByMonth(@Param("userId") String userId, @Param("msgCode") String msgCode, @Param("nowDay") String nowDay);

    /**
     * 订阅消息
     * @param userId
     * @param msgCode
     * @param nowDay
     * @return
     */
    List<SubscriptionMsg> selectSubscriptionByMsgCode(@Param("userId") Long userId, @Param("msgCode") String msgCode, @Param("sendDesc") String sendDesc, @Param("nowDay") String nowDay);

    IPage<MyMsg> listByUserId(Page<MyMsg> page, @Param("userId") String userId, @Param("nowDay") String nowDay);

    void updateByUserId(@Param("userId") String userId, @Param("nowDay") String nowDay);

    List<MyMsg> listByUnRead(@Param("userId") String userId, @Param("nowDay") String nowDay);

    List<MyMsg> listByUnReadNum(@Param("userId") String userId, @Param("nowDay") String nowDay);
    void updateReadById(@Param("id") Long id, @Param("nowDay") String nowDay);
    /**
     * 获取分表列表
     * @return
     */
    List<String> querySubTableList();

    /**
     * 创建分表
     * @return
     */
    int createSubTable(@Param("nowDay")String nowDay);

    void inserByTable(@Param("myMsg") MyMsg myMsg,@Param("nowDay")String nowDay);

    void inserListByTable(@Param("list") List<MyMsg> list,@Param("nowDay")String nowDay);

    /**
     * 获取分表列表
     * @return
     */
    List<String> querySubscriptionTableList();

    /**
     * 创建分表
     * @return
     */
    int createSubscriptionTable(@Param("nowDay")String nowDay);

    void inserSubscriptionByTable(@Param("myMsg") SubscriptionMsg myMsg, @Param("nowDay")String nowDay);

    SubscriptionMsg selectSubscriptionByActivityId(@Param("userId") Long userId, @Param("msgCode") String msgCode);

    void inserSubscriptionByActivityId(@Param("myMsg") SubscriptionMsg myMsg);

}
