package com.dz.ms.basic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.core.exception.BusinessException;
import com.dz.ms.basic.entity.MaterialGroup;
import com.dz.ms.basic.entity.MaterialInfo;
import com.dz.ms.basic.mapper.MaterialGroupMapper;
import com.dz.ms.basic.mapper.MaterialInfoMapper;
import com.dz.ms.basic.service.MaterialGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 素材分组
 * @author: Handy
 * @date:   2022/2/4 17:39
 */
@Service
public class MaterialGroupServiceImpl extends ServiceImpl<MaterialGroupMapper,MaterialGroup> implements MaterialGroupService {

    @Autowired
    private MaterialGroupMapper materialGroupMapper;
    @Autowired
    private MaterialInfoMapper materialInfoMapper;

    /**
     * 删除素材分组
     * @param id
     * @param tenantId
     */
    @Override
    public void deleteMaterialGroup(Long id, Long tenantId) {
        LambdaQueryWrapper<MaterialInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MaterialInfo :: getGroupId,id);
        long count = materialInfoMapper.selectCount(wrapper);
        if(count > 0) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"该分类下有图片不能删除");
        }
        materialGroupMapper.deleteById(id);
        /*MaterialInfo materialInfo = new MaterialInfo();
        materialInfo.setGroupId(0L);
        //该分组下的素材分组置为0
        materialInfoMapper.update(materialInfo,wrapper);*/
    }
}
