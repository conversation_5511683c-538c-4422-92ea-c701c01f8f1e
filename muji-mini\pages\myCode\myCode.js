// import drawQrcode from '../../utils/weapp.qrcode'
const drawQrcode = require('../../utils/weapp.qrcode')
const app = getApp()

// import {
//   CODE128
// } from "wxapp-barcode/";


Page({

  /**
   * 页面的初始数据
   */
  data: {
    codeImg: '',
    registerTime: '', // 注册时间
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    this.drawCode()
    this.setData({
      registerTime: this.format()
    })
  },
  async onShow() {
    await app.getUserInfo()
    this.drawCode()
  },
  handleGoMember() {
    if (app.ifRegister()) {
      this.openCard();
    }
  },
  openCard() { // 打开卡包
    const {
      createAppId,
      createCardId,
      createType,
      cardNo,
    } = this.data.userInfo;
    wx.$mp.navigateToMiniProgram({
      appId: 'wxeb490c6f9b154ef9',
      path: 'pages/card_open/card_open',
      envVersion: 'release',
      extraData: {
        create_card_appid: createAppId,
        card_id: createCardId,
        // card_code: cardNo,
        activate_type: createType, // 指定跳转激活
      },
    })
  },
  // 格式化注册时间
  format() {
    let { registerTime } = this.data.userInfo;
    if (registerTime) {
      return registerTime.slice(0, 10).replaceAll('-', '.')
    }
    return ''
  },
  drawCode() {
    const {
      userInfo
    } = this.data;

    drawQrcode({
      width: 180,
      height: 180,
      canvasId: 'myQrcode',
      text: userInfo.cardNo,
    })
    const that = this
    setTimeout(() => {
      wx.canvasToTempFilePath({
        x: 0,
        y: 0,
        width: 180,
        height: 180,
        quality: 4,
        canvasId: 'myQrcode',
        success: (res) => {
          that.setData({
            codeImg: res.tempFilePath
          })
        },
        fail: (err) => {
          console.error(err)
        }
      })
    }, 500)



  },
  handleGo() {
    const {
      userInfo
    } = this.data;
    wx.$mp.navigateTo({
      url: `/pages/myBarCode/myBarCode?code=${userInfo.cardNo}`,
    })
  },
  copyTxt(e) {
    const {
      copy
    } = e.currentTarget.dataset;
    wx.setClipboardData({
      data: copy,
    })
  },
})
