package com.dz.ms.sales.dto.excel;


import com.dz.common.core.annotation.Columns;
import com.dz.common.core.enums.ColumnType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/8
 */
@Data
public class SignInUserDetailExportDTO {

    @ApiModelProperty(value = "会员昵称")
    private String username;
    @ApiModelProperty(value = "会员卡号")
    private String cardNo;
    @ApiModelProperty(value = "第几天打卡")
    private String days;
    @ApiModelProperty(value = "打卡时间")
    private String signInTime;
    @ApiModelProperty(value = "舒适度")
    private String comfort;
    @ApiModelProperty(value = "保湿效果")
    private String moisturize;
    @ApiModelProperty(value = "吸收速度")
    private String absorption;
    @ApiModelProperty(value = "持久效果")
    private String persistence;
    @ApiModelProperty(value = "整体满意度")
    private String satisfaction;
    @ApiModelProperty(value = "使用照片")
    private String materialUrl;
    @ApiModelProperty(value = "7日使用感受")
    private String feeling;
    @ApiModelProperty(value = "7天后使用皮肤改善")
    private String improve;
    @ApiModelProperty(value = "产品满意度")
    private String score;
    @ApiModelProperty(value = "推荐产品度")
    private String recommend;
    @ApiModelProperty(value = "是否购买度")
    private String purchase;


}
