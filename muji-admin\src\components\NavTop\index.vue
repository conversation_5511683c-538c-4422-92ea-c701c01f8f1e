<template>
  <div class="meun-box">
    <div class="meun-nav" id="meunScorllleft">
      <ul class="meun-nav-ul">
        <li @click="selectedTab($event, pane, index)" :class="[pane.path == currentPath.value.path ? 'meun-nav-li-active' : '', 'meun-nav-li']" :id="`meun-nav-item${index}`" v-for="(pane, index) in GuserInfo.tagList" :key="index">
          {{ pane.meta.title }}
          <!-- onEdit(pane.key, 'del') -->
          <SvgIcon v-if="!pane.closable && pane.path != currentPath.value.path" class="close-del" @click.stop="onDel(pane, 'del')" name="close-button" width="18" height="18">
          </SvgIcon>
        </li>
      </ul>
    </div>
    <div class="menu-right">
      <div class="menu-right-msg" style="margin-right: 3px;margin-left: 34px;">
        <!-- <a-badge :count="100" overflowCount="99"> -->
        <SvgIcon name="menu-msg" width="24" height="24" />
        <!-- </a-badge> -->
      </div>
      <div class="meun-line-box">
        <div class="meun-line"> </div>
      </div>
      <div class="meun-user">
        <!-- <a-avatar :src="GuserInfo.avatar? GuserInfo.avatar:avatar" style="margin-right:6px;" :size="40">
          <template #icon>
            <img width="width:40px;height:40px" :src="avatar" alt="">
          </template>
</a-avatar> -->
        <a-dropdown>
          <a class="ant-dropdown-link" style="color: #161038;" @click.prevent>
            <span style="margin-right:11px;">{{ GuserInfo.name }}</span>
            <DownOutlined style="margin-right:22px;font-size: 12px;" />
          </a>
          <template #overlay>
            <a-menu>
              <a-menu-item @click="forgotPassword">

                <template #icon>
                  <UserOutlined />
                </template>
                修改密码

              </a-menu-item>
              <a-menu-item @click="goOut">
                <!-- <a-button size="small" type="text"> -->
                <template #icon>
                  <PoweroffOutlined />
                </template>
                注销
                <!-- </a-button> -->
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>

    </div>

  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { useGlobalStore } from '@/store'
import { getCurrentInstance } from "vue";
// import avatar from "@/assets/images/avatar.png";
const GuserInfo = useGlobalStore()
const emit = defineEmits(["removeCachePageId", "addCachePageId"]);//定义可调用的父组件方法名

const avatar = ref(null);


// 动态导入图片
const importImage = async (name) => {
  // console.log("🚀 ~ importImage ~ name:", name)
  // const image = await import(`@/assets/images/${name}.png`);  
  avatar.value = new URL(`../../assets/images/${name}.png`, import.meta.url).href;
  // console.log(avatar.value);
};

// 根据需要动态改变图片名称
importImage(import.meta.env.VITE_LOGO_IMG || 'avatar-daoZhi'); // 例如导入avatar.png

const props = defineProps({
  menuCur: {
    type: Object,
  },
  firstIndex: {
    type: String,
  },
  secondIndex: {
    type: String,

  },
  threeIndex: {
    type: String,
  },
})
// watch(() => router, (value) => {
//   if (!value) {
//     formParams.value.link = []
//   }
// })

const activedKeyIdArray = [];//存放曾经激活tab页的key，用于删除时计算应该激活的tab页
const panes = ref([]); //页签数组



emit('addCachePageId', "Welcome"); //缓存首页组件名称
const $router = useRouter(); //全局路由对象

let currentPath = ref('')
currentPath.value = $router.currentRoute
// debugger

// console.log($router.currentRoute.value.path, '当前的路由信息')
// let tagsAllList = ref([])

watch(
  () => $router.currentRoute.value,
  (newValue) => {
    // console.log(newValue, '009988');
    // 检测到路由变化后修改store中的值
    GuserInfo.addTag(newValue)
  },
  { immediate: true }
)

// let pagination = computed(() => GuserInfo.tagList );

// 删除顶部菜单信息
let onDel = (delInfo) => {
  // 如果删除的是当前页面
  // console.log(delInfo, currentPath.value, '删除当前的11111')
  if (isActive(delInfo, currentPath.value)) {
    GuserInfo.closeSelectedTag(delInfo)
    nextTick(() => {
      toLastView(GuserInfo.tagList, currentPath.value)
    });
  } else {
    // 删除的如果不是当前打开的页面
    GuserInfo.closeSelectedTag(delInfo)
  }
}

// 如果关闭的是当前页面的话显示最后一个页面
let isActive = (route, current) => {

  return route.path === current.value.path
}

// 如果还有其他页签默认显示最后一个
let toLastView = (visitedViews) => {
  const latestView = visitedViews.slice(-1)[0]
  $router.push(latestView.fullPath)
  if (latestView) {

  } else {
    if (view.name === 'Dashboard') {
      this.$router.replace({ path: '/redirect' + view.fullPath })
    } else {
      $router.push('/')
    }
  }
}


// const add = (paneObj) => { //添加tab页签，选中侧边栏菜单项时调用
//   $router.push(paneObj.path);
//   let oneTab = getTabByKey(paneObj.key);
//   if (oneTab == null) { //页签不存在，创建页签
//     emit('addCachePageId', paneObj.path.substring(1));//调用父组件函数，加入组件缓存列表
//     panes.value.push(paneObj);
//   } else {

//   }
// }



//选择某页签时激活该页签
const selectedTab = (event, tagItem, index) => {
  $router.push(tagItem.path);

  const target = event.target;
  const container = document.getElementById('meunScorllleft');

  // 获取容器和目标元素的尺寸信息
  const targetRect = target.getBoundingClientRect();
  const containerRect = container.getBoundingClientRect();

  // 计算目标元素的中心点位置
  const targetCenter = targetRect.left + targetRect.width / 2;
  const containerCenter = containerRect.left + containerRect.width / 2;

  // 计算需要滚动的距离，使目标元素居中显示
  const scrollOffset = targetCenter - containerCenter;

  // 计算可视区域的宽度和目标元素宽度
  const visibleWidth = containerRect.width;
  const targetWidth = targetRect.width;

  // 根据滚动方向添加额外偏移量
  const extraOffset = targetWidth * 0.5; // 额外偏移半个目标元素宽度

  if (scrollOffset < 0) {
    // 向左滚动，添加负的额外偏移
    container.scrollLeft += scrollOffset - extraOffset;
  } else {
    // 向右滚动，添加正的额外偏移
    container.scrollLeft += scrollOffset + extraOffset;
  }
};

// const getTabByKey = (keyId) => { //根据key获得页签对象
//   console.log(panes.value.length < 1);
//   let rtnValue = null;
//   for (let index = 0; index < panes.value.length; index++) {
//     if (panes.value[index].key == keyId) {
//       rtnValue = panes.value[index];
//       break;
//     }
//     console.log(rtnValue, 'rtnValue');
//   }
//   return rtnValue;
// };


const goOut = () => {
  GuserInfo.logout()
}
const isModalVisible = ref(false);
const forgotPassword = () => {
  $router.push({ name: 'forgotPasswordModal', query: { typeTitle: 2 } })
}
const handleModalVisibility = (visible) => {
  isModalVisible.value = visible;
};
</script>

<style lang="scss" scoped>
.meun-box {
  display: flex;
  align-items: center;
  height: 56px;
  margin: 24px 24px 0px 24px;

  // padding: 3px 0;
  // -----------------------------------
  .meun-nav {
    flex: 1;
    overflow-x: auto;
    scroll-behavior: smooth;

    .meun-nav-ul {
      margin: 0;
      // margin-top: 8px;
      padding: 0;
      // padding-top: 8px;
      list-style: none;
      display: flex;
      align-items: center;
    }

    .custom-wrap {
      display: flex;
      align-items: center;
    }

    .meun-nav-li {
      position: relative;
      padding: 9px 14px;
      border: none;
      border-radius: 8px !important;
      margin-left: 12px !important;
      color: rgba(0, 0, 0, 0.5);
      background-color: #fff;
      white-space: nowrap;
      line-height: 22px;
      font-family: PingFang SC;
      font-size: 16px;
      font-weight: 400;
      cursor: pointer;
      box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.04);

      .close-del {
        display: none;
      }

      &:hover {
        .close-del {
          display: block;
          position: absolute;
          right: 0px;
          top: -5px;
          cursor: pointer;
          z-index: 99;
        }
      }
    }

    .meun-nav-li-active {
      padding: 14px 16px !important;
      color: rgba(0, 0, 0, 0.85) !important;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 16px;
      line-height: 22px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    .meun-nav-li:nth-child(1) {
      margin-left: 0px !important;
    }
  }

  // -----------------------------------------------------
  .menu-right {
    // padding-top: 8px;
    height: 100% !important;
    min-width: 150px;
    display: flex;
    align-items: center;

    // justify-content: right;
    .menu-right-msg {
      display: flex;
      justify-content: center;
      /* 水平居中 */
      align-items: center;
      /* 垂直居中 */
    }

    .meun-line-box {
      display: flex;
      justify-content: center;
      /* 水平居中 */
      align-items: center;
      height: 100% !important;
      margin: 0 15px;

      .meun-line {
        width: 1px;
        height: 32px;
        opacity: 1;
        background: $theme-nav-line-color;
      }
    }

    .meun-user {
      height: 48px !important;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 100px;
      // background-color: #fff;
      // box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.04);
      padding-left: 4px;
    }
  }
}
</style>