package com.dz.ms.sales.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.IsEnableDTO;
import com.dz.ms.sales.dto.NpsInfoDTO;
import com.dz.ms.sales.dto.NpsQuestionDTO;
import com.dz.ms.sales.entity.NpsInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/16
 */
public interface NpsInfoService extends IService<NpsInfo> {

    /**
     * 分页查询NPS问卷信息
     *
     * @param param
     * @return PageInfo<NpsInfoDTO>
     */
    public PageInfo<NpsInfoDTO> getNpsInfoList(NpsInfoDTO param);

    /**
     * 根据ID查询NPS问卷信息
     *
     * @param id
     * @return NpsInfoDTO
     */
    public NpsInfoDTO getNpsInfoById(Long id);

    /**
     * 保存NPS问卷信息
     *
     * @param param
     * @return Long
     */
    public Long saveNpsInfo(NpsInfoDTO param);

    /**
     * 根据ID删除NPS问卷信息
     *
     * @param param
     */
    public void deleteNpsInfoById(IdCodeDTO param);

    /**
     * 更新NPS问卷状态
     *
     * @param param
     */
    public void updateNpsInfoState(IsEnableDTO param);

    /**
     * 根据打卡天数，获取问卷信息
     *
     * @param days
     * @param scene
     * @return
     */
    List<NpsQuestionDTO> getNpsBySignInDays(Integer days, Integer scene);


}
