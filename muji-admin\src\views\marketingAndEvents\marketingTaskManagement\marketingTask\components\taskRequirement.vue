<template>
  <div>
    <a-form-item label="任务内容" :name="props.conditionsRuleNamePrefix.concat(['taskDesc'])" :rules="formRules.taskDesc">
      <a-radio-group :disabled="disabled" v-model:value="formFields.taskDesc" @change="thisMethods.taskDescChange">
        <template v-for="(item, index) in taskContentOptions" :key="index">
          <a-radio-button :value="item.value">{{ item.label }}</a-radio-button>
        </template>
      </a-radio-group>
    </a-form-item>
    <a-form-item label="任务完成要求" :name="props.conditionsRuleNamePrefix.concat(['readyCycle'])" :rules="formRules.readyCycle">
      <a-radio-group :disabled="disabled" v-model:value="formFields.readyCycle" @change="thisMethods.readyCycleChange">
        <template v-for="(item, index) in completeTaskOptions" :key="index">
          <a-radio-button :disabled="item.disabled" :value="item.value">{{ item.label }}</a-radio-button>
        </template>
      </a-radio-group>
    </a-form-item>
    <!-- 线下打卡类型 -->
    <!-- v-if="formFields.taskDesc == 1"   {
        value: 1,
        label: '线下打卡' value: 2, label: '兑礼任务' value: 3,label: '线下消费'value: 4, label: '邀请好友' value: 5, label: '首购任务'
  , -->
    <!-- 周期加阶梯  readyCycle  {  value: 1,label: '一次性',   disabled: false  }, {   value: 2,
        label: '周期',
        disabled: false
    },
    {
        value: 3,
        label: '周期+阶梯',
        disabled: false
    }-->
    <!-- <template> -->
    <!-- 一次性的 -->
    <a-form-item label="任务完成要求" v-if="formFields.readyCycle == 1 && formFields.taskDesc != 3" :name="props.conditionsRuleNamePrefix.concat(['readyNum'])" :rules="formRules.readyNum">
      <div class="flex-box">
        <p v-if="formFields.taskDesc == 1">打卡</p>
        <p v-if="formFields.taskDesc == 2">完成</p>
        <p v-if="formFields.taskDesc == 4">邀请好友注册会小会员</p>
        <p v-if="formFields.taskDesc == 5">完成线上线下任意</p>
        <p v-if="formFields.taskDesc == 6">分享</p>
        <!-- <a-form-item label="" name="readyNum"> -->
        <a-input-number class="small-input" placeholder="请输入" :min="1" :max="99" v-model:value="formFields.readyNum" />
        <!-- </a-form-item> -->
        <p v-if="formFields.taskDesc == 1">次线下门店打卡</p>
        <p v-if="formFields.taskDesc == 2">次兑礼</p>
        <p v-if="formFields.taskDesc == 4">人</p>
        <p v-if="formFields.taskDesc == 5">次购物</p>
        <p v-if="formFields.taskDesc == 6">次</p>
      </div>
      <div class="global-tip" v-if="formFields.taskDesc == 1">
        用户进入任意门店范围100米内，打开小程序，即打卡成功
      </div>
      <div class="global-tip" v-if="formFields.taskDesc == 2">
        用户兑礼每1个订单算1次，多个订单算多次
      </div>
      <div class="global-tip" v-if="formFields.taskDesc == 4">
        已注册用户，邀请其他好友成为会员指定数量后，达到要求
      </div>
      <div class="global-tip" v-if="formFields.taskDesc == 5">
        在订单流水中，有任意一次购物的订单记录，即便完成任务
        首购任务仅会展示给：3年内，无任何购物记录的用户
      </div>
      <div class="global-tip" v-if="formFields.taskDesc == 6">
        用户在指定页面，点击过分享按钮，进行分享，即算完成1次
      </div>
    </a-form-item>

    <!-- 一次性结束 -->
    <!-- 周期  -->
    <a-form-item label="任务完成要求" class="a-item-mb0" v-if="formFields.readyCycle == 2 && formFields.taskDesc != 3">
      <div class="flex-box" style="align-items: start;">
        <p class="reay-item-form">每</p>
        <a-form-item label="" :name="props.conditionsRuleNamePrefix.concat([formFields.reayType])" :rules="formRules[formFields.reayType]">
          <a-input-number class="small-input" placeholder="请输入" :min="1" :max="99" v-model:value="formFields[formFields.reayType]" />
        </a-form-item>
        <a-form-item label="" :name="props.conditionsRuleNamePrefix.concat(['reayType'])" :rules="formRules.reayType">
          <a-select v-model:value="formFields.reayType" style="width: 60px;" placeholder="" :bordered="false">
            <a-select-option value="readyDay">天</a-select-option>
            <a-select-option value="readyMonth">月</a-select-option>
          </a-select>
        </a-form-item>
        <p class="reay-item-form" v-if="formFields.taskDesc == 1 || formFields.taskDesc == 2">完成</p>
        <p class="reay-item-form" v-if="formFields.taskDesc == 4"> 邀请好友注册会小会员</p>
        <p class="reay-item-form" v-if="formFields.taskDesc == 6">分享</p>
        <a-form-item label="" :name="props.conditionsRuleNamePrefix.concat(['readyNum'])" :rules="formRules.readyNum">
          <a-input-number class="small-input" placeholder="请输入" :min="1" :max="99" v-model:value="formFields.readyNum" />
        </a-form-item>
        <p class="reay-item-form" v-if="formFields.taskDesc == 1">次线下门店打卡</p>
        <p class="reay-item-form" v-if="formFields.taskDesc == 2">次兑礼</p>
        <p class="reay-item-form" v-if="formFields.taskDesc == 4">人</p>
        <p class="reay-item-form" v-if="formFields.taskDesc == 6">次</p>
      </div>
    </a-form-item>
    <a-form-item label="任务完成要求" class="a-item-mb0" v-if="formFields.readyCycle == 3 && formFields.taskDesc != 3">
      <div class="flex-box" style="align-items: start;">
        <p class="reay-item-form">每</p>
        <a-form-item label="" :name="props.conditionsRuleNamePrefix.concat([formFields.reayType])" :rules="formRules[formFields.reayType]">
          <a-input-number class="small-input" placeholder="请输入" v-model:value="formFields[formFields.reayType]" :min="1" :max="5" />
        </a-form-item>
        <a-form-item label="" :name="props.conditionsRuleNamePrefix.concat(['reayType'])" :rules="formRules.reayType">
          <a-select v-model:value="formFields.reayType" placeholder="" style="width: 60px;" :bordered="false">
            <a-select-option value="readyDay">天</a-select-option>
            <a-select-option value="readyMonth">月</a-select-option>
          </a-select>
        </a-form-item>
        <p class="reay-item-form" v-if="formFields.taskDesc == 1 || formFields.taskDesc == 2">总计可完成</p>
        <p class="reay-item-form" v-if="formFields.taskDesc == 4">总计可邀请</p>
        <p class="reay-item-form" v-if="formFields.taskDesc == 6">总计可完成</p>
        <a-form-item label="" :name="props.conditionsRuleNamePrefix.concat(['totalReadyNum'])" :rules="formRules.totalReadyNum">
          <a-input-number placeholder="请输入" @change="thisMethods.totalReadyNumChange" class="small-input" :min="1" :max="5" v-model:value="formFields.totalReadyNum" />
        </a-form-item>
        <p class="reay-item-form" v-if="formFields.taskDesc == 1">次线下门店打卡任务</p>
        <p class="reay-item-form" v-if="formFields.taskDesc == 2">次兑礼</p>
        <p class="reay-item-form" v-if="formFields.taskDesc == 4">人</p>
        <p class="reay-item-form" v-if="formFields.taskDesc == 6">次分享任务</p>
      </div>
    </a-form-item>
    <!-- 周期结束 -->
    <!-- </template> -->
    <!-- 线下消费字段 -->
    <template v-if="formFields.taskDesc == 3">
      <a-form-item label="任务完成要求" class="a-item-mb0" v-if="formFields.readyCycle == 1">
        <div class="flex-box" style="align-items: start;">
          <p class="reay-item-form" style="margin-right:5px">线下购买</p>
          <a-form-item label="部门" :labelCol="{ style: 'width:40px' }" :colon="false" :name="props.conditionsRuleNamePrefix.concat(['storeProductCodes'])" :rules="formRules.storeProductCodes">
            <a-input placeholder="请输入code" class="small-input" style="width:120px!important" v-model:value="formFields.storeProductCodes" />
          </a-form-item>
          <a-form-item label="depart" :labelCol="{ style: 'width:55px' }" style="margin-left: 10px;" :colon="false" :name="props.conditionsRuleNamePrefix.concat(['departProductCodes'])" :rules="formRules.departProductCodes">
            <a-input placeholder="请输入code" style="width:120px!important" class="small-input" v-model:value="formFields.departProductCodes" />
          </a-form-item>
          <a-form-item label="line" :colon="false" style="margin-left: 10px;" :labelCol="{ style: 'width:35px' }" :name="props.conditionsRuleNamePrefix.concat(['lineProductCodes'])" :rules="formRules.lineProductCodes">
            <a-input placeholder="请输入code" style="width:120px!important" class="small-input" v-model:value="formFields.lineProductCodes" />
          </a-form-item>
          <a-form-item label="class" :colon="false" style="margin-left: 10px;" :labelCol="{ style: 'width:40px' }" :name="props.conditionsRuleNamePrefix.concat(['classProductCodes'])" :rules="formRules.classProductCodes">
            <a-input placeholder="请输入code" style="width:120px!important" class="small-input" v-model:value="formFields.classProductCodes" />
          </a-form-item>
          <a-form-item label="jan" :colon="false" style="margin-left: 10px;" :labelCol="{ style: 'width:30px' }" :name="props.conditionsRuleNamePrefix.concat(['janProductCodes'])" :rules="formRules.janProductCodes">
            <a-input placeholder="请输入code" style="width:120px!important" class="small-input" v-model:value="formFields.janProductCodes" />
          </a-form-item>

        </div>
        <div style="margin-left:66px;align-items: start;" class="flex-box">
          <p class="reay-item-form" style="margin-right:5px">购买件数</p>
          <a-form-item label="" :name="props.conditionsRuleNamePrefix.concat(['readyNum'])">
            <a-input-number class="small-input" placeholder="请输入" :min="1" :max="99" v-model:value="formFields.readyNum" />
          </a-form-item>
          <p class="reay-item-form">件</p>
        </div>
        <div class="global-tip">
          可填写各个不同的code，用户购买其中任何一个，都会被任务计数，购买1件计数1，一次购买2件计数2；多个code时，可以通过“，”分割
        </div>
      </a-form-item>
      <a-form-item label="任务完成要求" v-if="formFields.readyCycle == 2">
        <div class="flex-box" style="align-items: start;">
          <p class="reay-item-form" style="margin-right:5px">线下购买</p>
          <a-form-item label="部门" :labelCol="{ style: 'width:40px' }" :colon="false" :name="props.conditionsRuleNamePrefix.concat(['storeProductCodes'])">
            <a-input placeholder="请输入code" class="small-input" style="width:120px!important" v-model:value="formFields.storeProductCodes" />
          </a-form-item>
          <a-form-item label="depart" :colon="false" style="margin-left: 10px;" :labelCol="{ style: 'width:55px' }" :name="props.conditionsRuleNamePrefix.concat(['departProductCodes'])" :rules="formRules.departProductCodes">
            <a-input placeholder="请输入code" style="width:120px!important" class="small-input" v-model:value="formFields.departProductCodes" />
          </a-form-item>
          <a-form-item label="line" :colon="false" style="margin-left: 10px;" :labelCol="{ style: 'width:35px' }" :name="props.conditionsRuleNamePrefix.concat(['lineProductCodes'])" :rules="formRules.lineProductCodes">
            <a-input placeholder="请输入code" style="width:120px!important" class="small-input" v-model:value="formFields.lineProductCodes" />
          </a-form-item>
          <a-form-item label="class" :colon="false" style="margin-left: 10px;" :labelCol="{ style: 'width:40px' }" :name="props.conditionsRuleNamePrefix.concat(['classProductCodes'])" :rules="formRules.classProductCodes">
            <a-input placeholder="请输入code" style="width:120px!important" class="small-input" v-model:value="formFields.classProductCodes" />
          </a-form-item>
          <a-form-item label="jan" :colon="false" style="margin-left: 10px;" :labelCol="{ style: 'width:30px' }" :name="props.conditionsRuleNamePrefix.concat(['janProductCodes'])" :rules="formRules.janProductCodes">
            <a-input placeholder="请输入code" style="width:120px!important" class="small-input" v-model:value="formFields.janProductCodes" />
          </a-form-item>
        </div>
        <div class="flex-box" style="align-items: start;">
          <p class="reay-item-form">每</p>
          <a-form-item label="" :name="props.conditionsRuleNamePrefix.concat([formFields.reayType])" :rules="formRules[formFields.reayType]">
            <a-input-number class="small-input" placeholder="请输入" :min="1" :max="99" v-model:value="formFields[formFields.reayType]" />
          </a-form-item>
          <a-form-item label="" :name="props.conditionsRuleNamePrefix.concat(['reayType'])" :rules="formRules.reayType">
            <a-select v-model:value="formFields.reayType" style="width: 60px;" placeholder="" :bordered="false">
              <a-select-option value="readyDay">天</a-select-option>
              <a-select-option value="readyMonth">月</a-select-option>
            </a-select>
          </a-form-item>
          <p class="reay-item-form">购买</p>
          <a-form-item label="" :name="props.conditionsRuleNamePrefix.concat(['readyNum'])" :rules="formRules.readyNum">
            <a-input-number class="small-input" placeholder="请输入" :min="1" :max="99" v-model:value="formFields.readyNum" />
          </a-form-item>
          <p class="reay-item-form">件</p>
        </div>
      </a-form-item>
      <a-form-item label="任务完成要求" v-if="formFields.readyCycle == 3">
        <div class="flex-box" style="align-items: start;">
          <p class="reay-item-form" style="margin-right:5px">线下购买</p>
          <a-form-item label="部门" :labelCol="{ style: 'width:40px' }" :colon="false" :name="props.conditionsRuleNamePrefix.concat(['storeProductCodes'])" :rules="formRules.storeProductCodes">
            <a-input placeholder="请输入code" class="small-input" style="width:120px!important" v-model:value="formFields.storeProductCodes" />
          </a-form-item>
          <a-form-item label="depart" style="margin-left: 10px;" :labelCol="{ style: 'width:55px' }" :colon="false" :name="props.conditionsRuleNamePrefix.concat(['departProductCodes'])" :rules="formRules.departProductCodes">
            <a-input placeholder="请输入code" style="width:120px!important" class="small-input" v-model:value="formFields.departProductCodes" />
          </a-form-item>
          <a-form-item label="line" style="margin-left: 10px;" :labelCol="{ style: 'width:35px' }" :colon="false" :name="props.conditionsRuleNamePrefix.concat(['lineProductCodes'])" :rules="formRules.lineProductCodes">
            <a-input placeholder="请输入code" style="width:120px!important" class="small-input" v-model:value="formFields.lineProductCodes" />
          </a-form-item>
          <a-form-item label="class" style="margin-left: 10px;" :labelCol="{ style: 'width:40px' }" :colon="false" :name="props.conditionsRuleNamePrefix.concat(['classProductCodes'])" :rules="formRules.classProductCodes">
            <a-input placeholder="请输入code" style="width:120px!important" class="small-input" v-model:value="formFields.classProductCodes" />
          </a-form-item>
          <a-form-item label="jan" style="margin-left: 10px;" :labelCol="{ style: 'width:30px' }" :colon="false" :name="props.conditionsRuleNamePrefix.concat(['janProductCodes'])" :rules="formRules.janProductCodes">
            <a-input placeholder="请输入code" style="width:120px!important" class="small-input" v-model:value="formFields.janProductCodes" />
          </a-form-item>
        </div>
        <div class="flex-box" style="align-items: start;">
          <p class="reay-item-form">每</p>
          <a-form-item label="" :name="props.conditionsRuleNamePrefix.concat([formFields.reayType])" :rules="formRules[formFields.reayType]">
            <a-input-number class="small-input" placeholder="请输入" v-model:value="formFields[formFields.reayType]" />
          </a-form-item>
          <a-form-item label="" :name="props.conditionsRuleNamePrefix.concat(['reayType'])" :rules="formRules.reayType">
            <a-select v-model:value="formFields.reayType" placeholder="请输入" :min="1" :max="99" style="width: 60px;" :bordered="false">
              <a-select-option value="readyDay">天</a-select-option>
              <a-select-option value="readyMonth">月</a-select-option>
            </a-select>
          </a-form-item>
          <p class="reay-item-form">购买</p>
          <a-form-item label="" :name="props.conditionsRuleNamePrefix.concat(['readyNum'])" :rules="formRules.readyNum">
            <a-input-number class="small-input" placeholder="请输入" :min="1" :max="99" v-model:value="formFields.readyNum" />
          </a-form-item>
          <p class="reay-item-form" style="padding-right:5px">件</p>
          <p class="reay-item-form">总计可完成</p>

          <a-form-item label="" :name="props.conditionsRuleNamePrefix.concat(['totalReadyNum'])" :rules="formRules.totalReadyNum">
            <a-input-number placeholder="请输入" @change="thisMethods.totalReadyNumChange" class="small-input" :min="1" :max="5" v-model:value="formFields.totalReadyNum" />
          </a-form-item>
          <p class="reay-item-form">次</p>
        </div>
      </a-form-item>
    </template>
    <!-- 线下消费结束 -->
    <!-- 新增任务限制 -->
    <!-- <a-form-item label=" " :hideRequiredMark="false" :colon="false" v-if="formFields.taskDesc == 1 ||formFields.taskDesc == 5">
      <a-form-item label="" class="a-item-mb0" v-if="formFields.taskDesc == 1 ||formFields.taskDesc == 5 " :name="props.conditionsRuleNamePrefix.concat(['limitationNum'])">
        <div class="flex-box" style="align-items: start;">
          <p class="reay-item-form">每天完成任务限制</p>
          <a-input-number class="small-input" placeholder="请输入" :disabled="formFields.taskDesc == 1" v-model:value="formFields.limitationNum" :min="1" :max="999" />
          <p class="reay-item-form">次</p>
        </div>
        <div class="global-tip" v-if="formFields.taskDesc == 1">
          每天最多限制用户可完成任务的次数，该任务暂不可调整次数
        </div>
        <div class="global-tip" v-if="formFields.taskDesc == 5">
          每天最多限制用户可完成任务的次数
        </div>
      </a-form-item>
      <a-form-item label="" class="a-item-mb0" :name="props.conditionsRuleNamePrefix.concat(['storeCardNum'])" v-if="formFields.taskDesc == 1">
        <div class="flex-box" style="align-items: start;">
          <p class="reay-item-form">每家门店限制打卡次数</p>
          <a-input-number class="small-input" placeholder="请输入" v-model:value="formFields.storeCardNum" :min="1" :max="999" />
          <p class="reay-item-form">次</p>
        </div>
        <div class="global-tip">
          每个周期内，每家门店可以打卡成功N次
        </div>
      </a-form-item>
    </a-form-item> -->
  </div>
</template>
<script setup>
import { taskContentOptions, completeTaskOptions, } from '@/utils/dict-options'

const emits = defineEmits(['taskDescChange', 'readyCycleChange', 'totalReadyNumChange'])
const props = defineProps({
  formType: { // 0-新增 1-编辑 2-查看
    type: Number,
    default: 0
  },
  conditionsRuleNamePrefix: {
    type: Array,
    default: () => ([])
  },
  formFields: {
    type: Object,
    default: () => ({})
  },
  formRules: {
    type: Object,
    default: () => ({})
  },
  formRef: {
    type: Object,
    default: () => ({})
  },
  disabled: {
    type: Boolean,
    default: false
  }
})



const thisMethods = {
  setFormFields() {
    const formFields = props.formFields
    formFields.storeCardNum = formFields.storeCardNum || 1
    formFields.limitationNum = formFields.limitationNum || 1
    formFields.taskDesc = formFields.taskDesc || 1
    formFields.readyCycle = formFields.readyCycle || 1
    formFields.readyNum = formFields.readyNum || null
    formFields.reayType = formFields.reayType || 'readyDay'
    formFields.readyDay = formFields.readyDay || null
    formFields.readyMonth = formFields.readyMonth || null
    formFields.totalReadyNum = formFields.totalReadyNum || null
    formFields.storeProductCodes = formFields.storeProductCodes || null
    formFields.classProductCodes = formFields.classProductCodes || null
    formFields.departProductCodes = formFields.departProductCodes || null
    formFields.janProductCodes = formFields.janProductCodes || null


  },
  setFormRules() {
    const formRules = props.formRules
    formRules.taskDesc = formRules.taskDesc || [{ required: true, message: '请选择任务内容', trigger: ['blur', 'change'] }]
    formRules.readyCycle = formRules.readyCycle || [{ required: true, message: '请选择任务要求', trigger: ['blur', 'change'] }]
    formRules.storeCardNum = formRules.storeCardNum || [{ required: true, message: '请输入', trigger: ['blur', 'change'] }]
    formRules.limitationNum = formRules.limitationNum || [{ required: true, message: '请输入', trigger: ['blur', 'change'] }]
    formRules.readyNum = formRules.readyNum || [{ required: true, message: '请输入', trigger: ['blur', 'change'] }]
    formRules.reayType = formRules.reayType || [{ required: true, message: '请选择', trigger: ['blur', 'change'] }],
      formRules.readyDay = formRules.readyDay || [{ required: true, message: '请输入天数', trigger: ['blur', 'change'] }]
    formRules.readyMonth = formRules.readyMonth || [{ required: true, message: '请输入', trigger: ['blur', 'change'] }]
    formRules.totalReadyNum = formRules.totalReadyNum || [{ required: true, message: '请输入', trigger: ['blur', 'change'] }]
    formRules.storeProductCodes = formRules.storeProductCodes || [{ required: true, message: '请输入code', trigger: ['blur', 'change'] }]
    formRules.classProductCodes = formRules.classProductCodes || [{ required: true, message: '请输入code', trigger: ['blur', 'change'] }]
    formRules.departProductCodes = formRules.departProductCodes || [{ required: true, message: '请输入code', trigger: ['blur', 'change'] }]
    formRules.janProductCodes = formRules.janProductCodes || [{ required: true, message: '请输入code', trigger: ['blur', 'change'] }]
  },
  taskDescChange(e) {
    emits('taskDescChange', e)
  },
  readyCycleChange(e) {
    console.log(e, 'valuevaluevaluevalue');
    emits('readyCycleChange', e)
  },
  totalReadyNumChange(e) {
    console.log(e);

    emits('totalReadyNumChange', e)
  }
}

thisMethods.setFormFields()
thisMethods.setFormRules()
</script>

<style lang="scss" scoped>
.flex-box {
  display: flex;
  // margin-left: 150px;
  align-items: center;
}

.flex-box p {
  margin: 0;
  padding: 0;
  // line-height: 30px;
}

.small-input {
  width: 100px !important;
  margin: 0 5px;
}

.reay-item-form {
  height: 32px;
  line-height: 32px;
  // margin-bottom: 0 !important;
}

.a-item-mb0 {
  margin-bottom: 0 !important;
}
</style>