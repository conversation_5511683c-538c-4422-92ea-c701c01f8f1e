<template>
  <a-form-item label="背景类型">
    <a-radio-group v-model:value="addParams.bgType">
      <a-radio :value="1">纯色</a-radio>
      <a-radio :value="2">图片</a-radio>
      <a-radio :value="3">视频</a-radio>
    </a-radio-group>
  </a-form-item>
  <a-form-item label="背景色" v-if="addParams.bgType==1">
    <Color color="rgba(0,0,0,1)" :value="addParams.bgColor" @changeColor="changeColor"></Color>
  </a-form-item>
  <a-form-item label="背景图" v-else-if="addParams.bgType==2">
    <uploadImg :max="10" :width="200" :height="200" :imgUrl="addParams.bgImg" :form="addParams" path="bgImg" :disabled="disabled" @success="uploadSuccess" />
  </a-form-item>
  <a-form-item label="背景视频" v-else-if="addParams.bgType==3">
    <uploadVideo :width="200" :height="200" :imgUrl="addParams.bgVideo" :form="addParams" path="bgVideo" @success="uploadSuccess">
    </uploadVideo>
  </a-form-item>
</template>

<script setup>
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  addParams: {
    type: Object,
    default() {
      return {}
    }
  },
})

// 上传图片 视频
const uploadSuccess = async (data) => {
  let { form, path, imgUrl } = data;
  form[path] = imgUrl
}


// 修改颜色
const changeColor = async (color) => {
  props.addParams.bgColor = color
}




</script>

<style>
</style>
