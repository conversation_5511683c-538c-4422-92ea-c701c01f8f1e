package com.dz.common.core.enums;

import java.util.Objects;

/**
 * 金币对账单类行枚举
 * <AUTHOR>
 **/
public enum GoldCoinStatementTypeEnum {

    EXPENSE(0, "支出"),
    INCOME(1, "收入"),
    ;

    private final Integer code;
    private final String value;

    GoldCoinStatementTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (GoldCoinStatementTypeEnum resultEnum : GoldCoinStatementTypeEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum.value;
            }
        }
        return null;
    }
}
