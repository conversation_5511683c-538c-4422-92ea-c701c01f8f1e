<view class="map-card" bindtap="handleClick" data-id="{{outletData.id}}">
  <view class="info">
    <view>
      <view class="name">
        {{outletData.storeName}}
        <view wx:if="{{outletData.type===2}}" class="tag">
          <view>{{storeTag}}</view>
        </view>
      </view>
      <view class="info-item">
        <view class="iconfont icon-Place1" />
        <view class="info-text">{{outletData.storeAddress}}</view>
      </view>
      <view class="info-item" style="margin-top: 14rpx; height: 30rpx;">
        <view class="iconfont icon-Time" wx:if="{{(outletData.openingHourOne!==null&&outletData.openingHourOne!=='')||(outletData.openingHourTwo!==null && outletData.openingHourTwo!=='')}}" />
        <view class="info-text">
          <text wx:if="{{outletData.openingHourOne && outletData.openingHourOne!==''}}">{{outletData.openingHourOne}} </text>
          <text wx:if="{{outletData.openingHourTwo && outletData.openingHourTwo!==''}}">...</text>
        </view>
      </view>
    </view>
    <view class="distance-box" catchtap="openLocation" data-store="{{outletData}}">
      <view class="distance-icon">
        <image src="{{$cdn}}/guide.png" />
      </view>
      <view wx:if="{{outletData.distance}}" class="num">
        {{outletData.distance}}
      </view>
    </view>
  </view>
  <view class="btn-box">
    <view class="btn-item" bindtap="callTel" data-num="{{outletData.phone}}">
      电话咨询
    </view>
    <block wx:if="{{outletData.weworkImages}}">
      <view class="btn-item black-bg" bindtap="goGuide" data-value="{{outletData.id}}" data-distance="{{outletData.distance}}">
        服务顾问
      </view>
    </block>
  </view>
</view>