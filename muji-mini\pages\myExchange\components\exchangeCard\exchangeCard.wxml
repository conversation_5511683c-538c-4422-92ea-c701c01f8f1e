<view class="card-container">
  <view class="card-header">兑换时间：{{item.created}}</view>
  <view class="card-content">
    <!-- 单个商品 -->
    <block wx:if="{{item.productList.length===1}}">
      <view class="card-single">
        <view class="card-img">
          <image
            mode="aspectFill"
            class="image"
            src="{{item.productList[0].imgUrl}}"/>
        </view>
        <view class="single-info">
          <view class="title">{{item.productList[0].productName}}</view>
          <view class="single-num">数量：{{item.productList[0].number}}</view>
        </view>
      </view>
    </block>
    <!-- 多个商品 -->
    <block wx:else>
      <scroll-view
        style="width: 100%"
        class="card-multi"
        enhanced="{{true}}"
        enable-flex
        scroll-x>

        <view
          class="card-img"
          wx:for="{{item.productList}}"
          wx:for-item="itemSub"
          wx:key="id">
          <image
            mode="aspectFill"
            class="image"
            src="{{itemSub.imgUrl}}"/>
        </view>

      </scroll-view>
    </block>
  </view>
  <view class="card-footer">
    <view class="num">共{{item.number}}件</view>
    <view class="price">{{item.orderPoint}}积分</view>
  </view>
</view>
