package com.dz.ms.user.dto.dkeyam.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 断开在线用户 入参
 * @author: fei
 * @date: 2025/01/22 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class disconnectParam implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("断开用户API密钥")
    private String secretKey;

    @ApiModelProperty("是否解绑用户终端MAC地址，默认值false：不解绑")
    private boolean unbindTerminal;

    @ApiModelProperty("断开用户的原因")
    private String cause;

    @ApiModelProperty("用户会话id")
    private String sessionId;

    @ApiModelProperty("站点名称")
    private String tenantName;

    @ApiModelProperty("终端MAC地址，格式：C3:55:33:DD:11:02")
    private String terminalMac;

    @ApiModelProperty("用户登录名")
    private String loginName;

}
