package com.dz.ms.product.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 日志-货架商品库存任务DTO
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:53
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "日志-货架商品库存任务")
public class TaskLogDTO extends BaseDTO {

    @ApiModelProperty(value = "商品任务ID")
    private Long id;
    @ApiModelProperty(value = "任务ID")
    private Long taskId;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "货架名称")
    private String shelfName;
    @ApiModelProperty(value = "商品ID")
    private Long productId;
    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "上架库存数量 正+ 负-")
    private Integer onInventory;
    @ApiModelProperty(value = "任务执行后商品当前库存数量")
    private Integer currentInventory;
    @ApiModelProperty(value = "任务执行时间")
    private Date created;

}
