package com.dz.ms.sales.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;


@Getter
@Setter
@NoArgsConstructor
@Table("用户抽奖次数汇总")
@TableName(value = "user_lottery_summary")
public class UserLotterySummary implements Serializable {
    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "id")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = true, comment = "活动标识", isIndex = true)
    private String campaignCode;

    @Columns(type = ColumnType.BIGINT, length = 11, isNull = false, comment = "用户ID", isIndex = true)
    private Long uid;
    @Columns(type = ColumnType.VARCHAR, length = 50, isNull = false, comment = "openid")
    private String openid;
    @Columns(type = ColumnType.VARCHAR, length = 255, isNull = true, comment = "unionid", isIndex = true)
    private String unionid;
    @Columns(type = ColumnType.VARCHAR,length = 32,isNull = true,comment = "会员名")
    private String username;
    @Columns(type = ColumnType.VARCHAR, length = 36, isNull = false, comment = "手机号码")
    private String mobile;

    @Columns(type = ColumnType.INT, length = 10, isNull = false, defaultValue = "0", comment = "获取的抽奖总次数")
    private Integer totalCount;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, defaultValue = "0", comment = "剩余的抽奖次数")
    private Integer surplusCount;

    @Columns(type = ColumnType.VARCHAR, length = 255, isNull = true, comment = "备注")
    private String remark;

    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "是否删除 0未删除 1已删除")
    @TableLogic
    private Integer isDeleted;
    @Columns(type = ColumnType.BIGINT, length = 11, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT, length = 11, isNull = false, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT, length = 11, isNull = true, comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    public UserLotterySummary(String campaignCode, Long uid, String openid, String unionid, String username, String mobile) {
        this.campaignCode = campaignCode;
        this.uid = uid;
        this.openid = openid;
        this.unionid = unionid;
        this.username = username;
        this.mobile = mobile;
    }
}
