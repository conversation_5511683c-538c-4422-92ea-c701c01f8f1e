package com.dz.ms.sales.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.sales.entity.UserLotteryPrizes;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/5/21
 */
@Repository
public interface UserLotteryPrizesMapper extends BaseMapper<UserLotteryPrizes> {

    Long selectUserLotteryPrizesByMany(@Param("unionid") String unionid, @Param("campaignCode") String campaignCode,  @Param("prizesLevelCode") Integer prizesLevelCode, @Param("monthStart") Date monthStart, @Param("monthEnd") Date monthEnd);



}
