Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    isShow: {
      type: Boolean,
      value: false,
      observer: 'observeShow'
    },
    zIndex: {
      type: Number,
      value: 99999
    },
    top: {
      type: Number,
      value: 0,
    }

  },
  data: {
    inited: false,
    display: false,
    type: 'top',
  },
  lifetimes: {
    attached() {
      // 确保初始化时也调用 observeShow
      this.observeShow(this.data.isShow);
    }
  },
  methods: {
    observeShow(value) {
      if (value) {
        this.show()
      } else {
        this.setData({
          type: 'leave',
          inited: false,
          display: false,
        })
      }
    },

    show() {
      this.setData({
        inited: true,
        display: true,
        type: 'enter'
      })
    },

    onAnimationEnd() {
      if (!this.data.isShow) {
        this.setData({
          display: false
        })
      }
    },
    onClickHide() {
      this.triggerEvent('close')
    }
  }

})
