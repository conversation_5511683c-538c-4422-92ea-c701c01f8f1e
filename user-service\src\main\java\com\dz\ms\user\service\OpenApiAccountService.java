package com.dz.ms.user.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.core.dto.user.openapi.param.OpenApiTokenParamDTO;
import com.dz.common.core.dto.user.openapi.response.OpenApiDataDTO;
import com.dz.ms.user.entity.OpenApiAccount;

/**
 * OpenApi账号(OpenApiAccount)表服务接口
 *
 */
public interface OpenApiAccountService extends IService<OpenApiAccount> {

    OpenApiDataDTO getToken(OpenApiTokenParamDTO param);

}
