package com.dz.ms.adaptor.processor;


import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.fegin.sales.CampaignFeignClient;
import com.dz.common.core.threadlocal.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/2/6
 */
@Slf4j
@Component
public class CampaignBuyerJob implements BasicProcessor {

    @Resource
    private CampaignFeignClient campaignFeignClient;


    @Override
    public ProcessResult process(TaskContext taskContext) throws Exception {
        String jobParams = taskContext.getJobParams();
        SecurityContext.setUser(new CurrentUserDTO(1L));
        campaignFeignClient.CampaignBuyerJob(jobParams);
        log.info("定时拉取已订阅的用户，筛选出购买指定产品的用户任务执行完成");
        return new ProcessResult(true, "success");
    }


}
