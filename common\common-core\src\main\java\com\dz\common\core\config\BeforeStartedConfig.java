package com.dz.common.core.config;

import com.dz.common.core.service.ModelToSqlSevice;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;

import javax.annotation.Resource;

public class BeforeStartedConfig implements BeanPostProcessor {

    @Resource
    private ModelToSqlSevice modelToSqlSevice;

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
//        modelToSqlSevice.modelToSql(true);
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        return bean;
    }
}

