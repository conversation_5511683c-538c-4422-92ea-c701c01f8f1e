package com.dz.common.core.fegin.basic;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.basic.MaterialRelationParamDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 素材管理
 * @Author: Handy
 * @Date: 2020/9/14 9:03
 */
@FeignClient(name = ServiceConstant.BASIC_SERVICE_NAME, contextId = "MaterialFeginClient")
public interface MaterialFeginClient {

    /**
     * 保存素材关联业务信息
     * @param param
     * @return
     */
    @PostMapping(value = "/material/relation/save")
    Result<Long> saveMaterialRelation(@RequestBody MaterialRelationParamDTO param);

    /**
     * 批量保存素材关联业务信息
     * @param list
     * @return
     */
    @PostMapping(value = "/material/relation/save_batch")
    public Result<Long> saveBatchMaterialRelation(@RequestBody List<MaterialRelationParamDTO> list);

}
