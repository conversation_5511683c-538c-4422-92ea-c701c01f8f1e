package com.dz.ms.user.utils;

/**
 * <AUTHOR>
 */

public enum PointsOneType {
    WECHATPOINTS(0, "微信积分"),
    COSMETICSPOINTS(1, "彩妆积分"),
    TOILETPOINTS(2, "香水积分"),
    SKINCAREPOINTS(3, "护肤小老师积分"),
    TOILETTEACHERPOINTS(4, "彩妆小老师积分"),
    SHOWPOINTS(5, "作秀积分"),
    INTELLECTPOINTS(6, "智能专员积分");

    private int code;
    private String message;

    public static String getName(int index) {
        for (PointsOneType c : PointsOneType.values()) {
            if (c.getCode() == index) {
                return c.getMessage();
            }
        }
        return null;
    }

    private PointsOneType(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }
}