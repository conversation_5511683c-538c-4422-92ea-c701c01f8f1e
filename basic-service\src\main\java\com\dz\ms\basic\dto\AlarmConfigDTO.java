package com.dz.ms.basic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 系统告警配置DTO
 * @author: Handy
 * @date:   2022/08/05 17:38
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "系统告警配置")
public class AlarmConfigDTO {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "告警渠道 1邮件 2企微消息 3短信")
    private Integer alarmType;
    @ApiModelProperty(value = "接收消息用户列表逗号隔开")
    private String receivers;
    @ApiModelProperty(value = "配置状态 0停用 1启用")
    private Integer state;

}
