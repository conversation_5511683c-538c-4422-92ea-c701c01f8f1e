.selfModal {
  display: flex;
  flex-direction: column;
  align-items: center;
  &-content {
    width: 590rpx;
    height: 922rpx;
    background-position: 0 0;
    background-size: contain;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    position: relative;
  }
  &-title {
    flex-shrink: 0;
    width: 392rpx;
    height: 92rpx;
    padding-top: 40rpx;
    padding-right: 34rpx;
    align-self: flex-end;
    padding-bottom: 64rpx;
  }
  &-box {
    flex: 1;
    overflow-y: auto;
    width: 509rpx;
  }
  &-rule {
    width: 509rpx;
    height: 1290rpx;
    padding-bottom: 150rpx;
  }
  &-opacity {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 110rpx;
  }
  &-btn {
    width: 354rpx;
    height: 63rpx;
    margin-top: 40rpx;
  }
}
