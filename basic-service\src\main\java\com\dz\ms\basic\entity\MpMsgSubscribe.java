package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 小程序订阅消息订阅记录
 * @author: Handy
 * @date:   2023/07/06 18:05
 */
@Getter
@Setter
@NoArgsConstructor
@Table("小程序订阅消息订阅")
@TableName(value = "mp_msg_subscribe")
public class MpMsgSubscribe implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR,length = 20,isNull = true,comment = "场景编号")
    private String scene;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "消息配置ID",isIndex = true)
    private Long msgId;
    @Columns(type = ColumnType.VARCHAR,length = 64,isNull = false,comment = "模板ID")
    private String templateId;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "订阅用户ID")
    private Long uid;
    @Columns(type = ColumnType.VARCHAR,length = 36,isNull = true,comment = "订阅用户openid")
    private String openid;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = false,comment = "状态 0未推送 1已推送",defaultValue = "1")
    private Integer state;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = true,comment = "推送时间")
    private Date pushTime;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.INT,length = 10,isNull = true,comment = "订阅次数")
    private Integer subNumber;

    public MpMsgSubscribe(Long id, String scene, Long msgId, String templateId, Long uid, String openid, Integer state, Date pushTime) {
        this.id = id;
        this.scene = scene;
        this.msgId = msgId;
        this.templateId = templateId;
        this.uid = uid;
        this.openid = openid;
        this.state = state;
        this.pushTime = pushTime;
    }

}
