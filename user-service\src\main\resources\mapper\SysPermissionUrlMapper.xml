<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.user.mapper.SysPermissionUrlMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    id,
  	    permit_id,
  	    permit_name,
  	    url,
  	    created,
  	    creator,
  	    modified,
  	    modifier
    </sql>

    <!-- 获取角色权限页面列表 -->
    <select id="getRolePermitUrls" parameterType="java.lang.Long" resultType="java.lang.String">
		select
		p.url
		from sys_permission_url p
		left join sys_role_permission rp
		on rp.permit_id = p.permit_id
		where rp.role_id = #{roleId} or p.permit_id = 0
	</select>

	<!-- 根据权限ID列表获取权限接口列表 -->
	<select id="getPermitUrlsByPermitIds" resultType="java.lang.String">
		select
		url
		from sys_permission_url
		where permit_id in
		<foreach collection="permitIds" item="permitId" separator="," open="(" close=")">
			#{permitId}
		</foreach>
		or permit_id = 0
	</select>

</mapper>
