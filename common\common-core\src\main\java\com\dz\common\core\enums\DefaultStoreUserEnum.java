package com.dz.common.core.enums;

import java.util.Objects;

/**
 * Describe：默认门店和店员信息
 * Created by 吴蜀黍 on 2023/8/20 8:36
 **/
public enum DefaultStoreUserEnum {

    BACK_USER_STORE_DEFAULT(-1L, "系统后台"),
    BACK_USER_DEFAULT(-1L, "后台管理员"),
    SYSTEM(-2L, "系统"),
    ;

    private final Long code;
    private final String value;

    DefaultStoreUserEnum(Long code, String value) {
        this.code = code;
        this.value = value;
    }

    public Long getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DefaultStoreUserEnum resultEnum : DefaultStoreUserEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum.value;
            }
        }
        return null;
    }
}
