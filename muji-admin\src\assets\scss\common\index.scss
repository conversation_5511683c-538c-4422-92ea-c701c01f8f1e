@import "./tools";
@import "./ui.scss";

.header-title {
  line-height: 1;
  margin-top: 30px;
  margin-bottom: 30px;
  padding-left: 10px;
  font-weight: bold;
  border-left: 4px solid $colorPrimary;
}

// 抽屉弹窗footer样式
.ant-drawer .ant-drawer-footer {
  text-align: right;
}

// 表格分页样式
.ant-table-wrapper {
  height: 100%;

  .ant-table-pagination.ant-pagination {
    margin-bottom: 0;
  }

  .ant-spin-nested-loading {
    height: 100%;
  }

  .ant-spin-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
  }
}

// 抽屉关闭按钮位置
.ant-drawer .ant-drawer-close {
  position: absolute;
  top: 18px;
  right: 10px;
}

// 文字tip提示
.tips {
  margin-top: 5px;
  color: hsl(0, 0%, 70%);
  font-size: 12px;
}

// 表格按钮样式
.ant-table .ant-btn-link {
  padding: 0;
  border: 0;
}

// 省略号样式
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 统计样式
.ant-statistic {
  display: flex;
  flex-direction: column-reverse;

  .ant-statistic-content {
    margin-bottom: 10px;
    text-align: center;
  }
}

.pad20 {
  padding: 20px !important;
}

.titles-common {
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-top: 32px;
  margin-bottom: 25px;
}

.line-common {
  width: 100%;
  height: 0px;
  border-top: 1px solid #F0F0F0;
  margin-top: 48px;
}

.wx-part1-title {
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 16px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.wx-part1-line {
  width: 100%;
  height: 0px;
  border-top: 1px solid #F0F0F0;
}


// 去掉图片预览俩字
.ant-image-mask {
  .ant-image-mask-info {
    // 将div下的所有元素隐藏文字大小为0
    visibility: hidden;
    font-size: 0;
  }

  .ant-image-mask-info span {
    // 再单独给span标签添加样式 让其显示并且给出文字大小
    visibility: visible;
    font-size: 20px;
  }
}


// 统计自定义
.custom-statistics {
  display: flex;
  flex-wrap: wrap;
}

.statistics-li {
  width: 228px;
  height: 136px;
  background: #FFFFFF;
  border-radius: 12px;
  border: 1px solid #D8D8D8;
  padding: 16px 24px;
  margin-right: 8px;
  box-sizing: border-box;

  &:nth-child(4n) {
    margin-right: 0px;
  }

  .static-tit {
    height: 20px;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 20px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-bottom: 16px;
  }

  .static-number {
    margin-bottom: 16px;
    display: flex;

    .number {
      display: block;
      height: 32px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 32px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 32px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      // margin-top: 24px;
      max-width: 204px;
    }

    .unit {
      display: block;
      margin-left: 6px;
      font-size: 14px;
      line-height: 18px;
      margin-top: 13px;
    }
  }

  .no-padding {
    margin-bottom: 0px;

  }

}

.statistics-samll-li {
  width: 228px;
  height: 100px;
  margin-bottom: 10px;
}



.custom-static-small {
  display: flex;

  .custom-static-li {
    width: 228px;
    height: 100px;
    background: #FFFFFF;
    border-radius: 12px 12px 12px 12px;
    border: 1px solid #D8D8D8;
    padding: 16px 24px;
    box-sizing: border-box;
    margin-right: 8px;
    cursor: pointer;

    &.active {
      background-color: #f7f8fa;
      border: 1px solid #6A6BBF;
    }

  }

  .custom-small-tit {
    margin-bottom: 16px;

  }

  .custom-small-num {

    height: 32px;
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 32px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 32px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    // margin-top: 24px;
    max-width: 204px;
  }

}