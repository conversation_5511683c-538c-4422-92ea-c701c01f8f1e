package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 小程序/公众号模板消息推送任务
 * @author: Handy
 * @date:   2023/07/06 18:05
 */
@Getter
@Setter
@NoArgsConstructor
@Table("小程序/公众号模板消息推送任务")
@TableName(value = "mp_msg_push")
public class MpMsgPush implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "推送任务ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR,length = 64,isNull = true,comment = "推送任务名称")
    private String pushName;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "消息配置ID")
    private Long msgId;
    @Columns(type = ColumnType.VARCHAR,length = 64,isNull = false,comment = "模板ID")
    private String templateId;
    @Columns(type = ColumnType.VARCHAR,length = 20,isNull = true,comment = "模板名称")
    private String templateName;
    @Columns(type = ColumnType.VARCHAR,length = 1000,isNull = false,comment = "消息内容")
    private String msgContent;
    @Columns(type = ColumnType.VARCHAR,length = 128,isNull = true,comment = "跳转路径")
    private String pagePath;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = false,comment = "推送类型 1立即推送 2定时推送")
    private Integer pushType;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "定时推送时间")
    private Date pushTime;
    @Columns(type = ColumnType.INT,length = 10,isNull = false,comment = "推送人数",defaultValue = "0")
    private Integer pushNumber;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = false,comment = "状态 1未执行 2已执行 3已撤销",defaultValue = "1")
    private Integer state;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = true,comment = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;

    public MpMsgPush(Long id, String pushName, Long msgId, String templateId, String templateName, String msgContent, String pagePath, Integer pushType, Date pushTime, Integer pushNumber, Integer state) {
        this.id = id;
        this.pushName = pushName;
        this.msgId = msgId;
        this.templateId = templateId;
        this.templateName = templateName;
        this.msgContent = msgContent;
        this.pagePath = pagePath;
        this.pushType = pushType;
        this.pushTime = pushTime;
        this.pushNumber = pushNumber;
        this.state = state;
    }

}
