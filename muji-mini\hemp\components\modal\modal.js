const app = getApp()
Component({
  properties: {
    // 显示弹窗
    show: {
      type: Boolean,
      value: false,
      observer(val) {
        if (val) {
          this.calcHeight()
        }
      }

    },
    // 是否显示关闭按钮
    closeable: {
      type: Boolean,
      value: false
    },
    // 弹窗背景色
    background: {
      type: String,
      value: 'white'
    },
    // 弹窗圆角
    borderRadius: {
      type: Number,
      value: 0
    },
    // 标题
    title: {
      type: String
    },
    rules: {
      type: Array,
      value() {
        return []
      }
    },
    overlayClick: {
      type: Boolean,
      value: false
    },
    // 确认按钮
    confirmText: {
      type: String,
    },
    zIndex: {
      type: Number,
      value: 1000000
    },

  },
  data: {
    disabled: true,
    height: 0
  },
  ready() {
    if (this.data.show) {
      this.calcHeight()
    }
  },
  methods: {
    calcHeight() {
      this.setData({
        height: 1070 - 154 - (this.data.confirmText ? 140 : 0) - 60
      })
    },
    // 禁止穿透
    touchmove() {
      return false
    },
    // 禁止穿透
    touchmove1() {
      return true
    },
    // 关闭按钮
    close() {
      this.triggerEvent('close')
    },
    // 确认按钮
    confirm() {
      if (this.data.disabled) return
      this.triggerEvent('confirm')
    },
    scrollEnd() {
      this.setData({
        disabled: false
      })
    }
  }
})
