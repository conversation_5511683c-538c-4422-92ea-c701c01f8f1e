package com.dz.ms.sales.dto;
import java.util.Date;
import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 用户试衣照记录表DTO
 * @author: 
 * @date:   2025/03/21 11:43
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@ApiModel(value = "用户试衣照记录表")
public class FittingDTO extends BaseDTO {

    @ApiModelProperty(value = "主键id")
    private Long id;
    @ApiModelProperty(value = "模板id")
    private Integer templateId;
    @ApiModelProperty(value = "图片地址")
    private String imgUrl;
    @ApiModelProperty(value = "图片文字")
    private String imgText;
    @ApiModelProperty(value = "创建时间")
    private Date created;

}
