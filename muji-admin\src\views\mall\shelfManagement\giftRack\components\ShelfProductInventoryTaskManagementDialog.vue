<template>
  <!--注意点1（亮点1）：货架商品管理，是货架创建之前处理数据，需要前端拿到所有数据，前端分页和筛选-->
  <!--注意点2（亮点2）：货架库存任务管理，是货架创建之后处理数据，可以先入库后编辑，后端分页和筛选-->
  <a-modal v-model:open="thisFields.open" title="" @ok="thisMethods.handleOk" @cancel="thisMethods.cancel" :okButtonProps="{hidden:true}" :cancelButtonProps="{hidden:true}" width="1200px">
    <layout>
      <template v-slot:header v-if="false">
        <!--:disabled="!$hasPermission('mall:permission:searchGiftRack')"-->
        <searchForm :formParams="searchFields" @cancel="whenClickReset" @ok="whenClickSearch">
          <a-form-item label="商品名称" name="productName">
            <a-input placeholder="请输入" allow-clear v-model:value="searchFields.productName" allowClear @keyup.enter="whenClickSearch"></a-input>
          </a-form-item>
        </searchForm>
      </template>
      <template v-slot:topRight>
        <a-button type="primary" @click="thisMethods.ShelfProductInventoryTaskManagementAddDialogHandler">
          选择需要处理的商品
        </a-button>
      </template>
      <template v-slot="{ height }">
        <div>
          <div class="checkboxWrap">
            <a-checkbox v-model:checked="thisFields.hideDone" @change="thisMethods.changList">
              <span class="ui-c-grey">隐藏已完成任务</span>
            </a-checkbox>
          </div>
          <a-table :indentSize="20" row-key="id" :scroll="{ scrollToFirstRowOnChange: true, x: '100%' }" :dataSource="dataSource" :columns="thisFields.tableHeader" :pagination="pagination" :loading="loading" @change="whenPaginationChange">
            <template #bodyCell="{text, record, index, column}">
              <template v-if="column.dataIndex === 'index'">{{ (current - 1) * pageSize + 1 + index }}</template>
              <template v-if="column.dataIndex === 'taskDesc'">
                <a-button :disabled="!record.isEdit" class="ml5" type="link" @click="thisMethods.ShelfProductExpectUpDownShelfTimeDialogHandler(record,index)">
                  {{ record.taskDesc }}
                </a-button>
              </template>
              <template v-if="column.dataIndex === 'onInventory'">
                <div class="ui-child-form-item-mb0">
                  <a-form-item label="" name="onInventoryTemp">
                    <a-input :disabled="!record.isEdit" placeholder="请输入" :allowClear="true" v-model:value="record.onInventoryTemp" @blur="()=>thisMethods.onInventoryTempHandler(record)" />
                  </a-form-item>
                </div>
              </template>
              <template v-if="column.dataIndex === 'action'">
                <!-- state	状态 0待执行 1已执行 2执行中  3 待编辑-->
                <a-button type="link" @click="thisMethods.handleEdit(record)" :disabled="record.isEdit || (record.state === 1 ) ">编辑</a-button>
                <a-divider type="vertical" />
                <a-button type="link" @click="thisMethods.handleEditOk(record)" :disabled="!record.isEdit">执行任务</a-button>
                <a-divider type="vertical" />
                <a-button type="link" @click="thisMethods.handleEditCancel(record)" :disabled="!record.isEdit">取消</a-button>
                <a-divider type="vertical" />
                <a-button class="ml5" type="link" @click="thisMethods.handleDel(record,index)" :disabled="record.canDelete !== 1">删除</a-button>
              </template>
            </template>
          </a-table>
        </div>
      </template>
    </layout>
  </a-modal>
  <ShelfProductInventoryTaskManagementAddDialog :shelfId="props.shelfId" v-model="thisFields.ShelfProductInventoryTaskManagementAddDialogOpen" @ok="thisMethods.ShelfProductInventoryTaskManagementAddDialogHandlerOk" />
  <ShelfProductExpectUpDownShelfTimeDialog v-model="thisFields.ShelfProductExpectUpDownShelfTimeDialogOpen" @ok="thisMethods.ShelfProductExpectUpDownShelfTimeDialogHandlerOk" :record="thisFields.record" />
</template>
<script setup>
import ShelfProductInventoryTaskManagementAddDialog from '@/views/mall/shelfManagement/giftRack/components/ShelfProductInventoryTaskManagementAddDialog.vue'
import ShelfProductExpectUpDownShelfTimeDialog from '@/views/mall/shelfManagement/giftRack/components/ShelfProductExpectUpDownShelfTimeDialog.vue'
import { usePagination } from 'vue-request'
import { apiShelfInventoryTask } from '@/http/index.js'
import { onMounted, watch } from 'vue'
import { cloneDeep } from 'lodash'
import dayjs from 'dayjs'
import { message } from 'ant-design-vue'

const props = defineProps({
  shelfId: {
    type: [String, Number],
    default: ''
  },
  modelValue: {
    type: Boolean,
    default: false
  }
})
const emits = defineEmits(['update:modelValue', 'ok', 'changeCurrentInventory'])

const pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ['bottomLeft']
  }
})
const total = ref(0)
const getDefaultSearchFields = () => ({
  productName: ''
})
const searchFields = reactive(getDefaultSearchFields())
const thisFields = reactive({
  hideDone: true,
  record: {},
  ShelfProductInventoryTaskManagementAddDialogOpen: false,
  ShelfProductExpectUpDownShelfTimeDialogOpen: false,
  loading: false,
  open: false,
  tableHeader: [
    { title: '任务创建时间', dataIndex: 'created', align: 'center', ellipsis: true, width: 140 },
    { title: '商品名称', dataIndex: 'productName', align: 'center', ellipsis: true, width: 100 },
    { title: '期望上架时间', dataIndex: 'taskDesc', align: 'center', ellipsis: true, width: 120 },
    { title: '上下架库存数量', dataIndex: 'onInventory', align: 'center', ellipsis: true, width: 120 },
    { title: '任务状态', dataIndex: 'stateDesc', align: 'center', ellipsis: true, width: 100 },
    { title: '操作', dataIndex: 'action', align: 'center', width: 140, fixed: 'right' }
  ]
})
const thisMethods = {
  ShelfProductExpectUpDownShelfTimeDialogHandler(record, index) {
    thisFields.record = record
    thisFields.ShelfProductExpectUpDownShelfTimeDialogOpen = true
  },
  ShelfProductExpectUpDownShelfTimeDialogHandlerOk(e) {
    thisFields.record.onTypeTemp = e.onTypeTemp || 1
    thisFields.record.onTimeTemp = e.onTimeTemp
    thisFields.record.onDaysTemp = e.onDaysTemp
    thisFields.record.taskDesc = (['', '实时', e.onTimeTemp, `每${e.onDaysTemp}天`])[e.onTypeTemp]
  },
  ShelfProductInventoryTaskManagementAddDialogHandler() {
    thisFields.ShelfProductInventoryTaskManagementAddDialogOpen = true
  },
  async ShelfProductInventoryTaskManagementAddDialogHandlerOk(e) {
    const onTime = dayjs(new Date().setDate(new Date().getDate() + 5)).format('YYYY-MM-DD')
    const list = e.map(v => ({
      shelfId: props.shelfId,
      productId: v.productId,
      productName: v.productName,
      shelfProductId: v.shelfProductId,
      onType: '',
      onTime: null,
      onInventory: 0
    }))
    await apiShelfInventoryTask.createPage(list)
    refresh()
  },
  onInventoryTempHandler(record) {
    const match = String(record.onInventoryTemp).match(/(0)|(-?[1-9]\d*)/ig) || []
    record.onInventoryTemp = match[0] || '0'
  },
  handleEdit(record) {
    record.isEdit = true
  },
  async handleEditOk(record) {
    const res = await apiShelfInventoryTask.updatePage({
      shelfId: record.shelfId,
      productId: record.productId,
      shelfProductId: record.shelfProductId,
      onType: record.onTypeTemp,
      onTime: record.onTimeTemp,
      onDays: record.onDaysTemp,
      onInventory: record.onInventoryTemp,
      id: record.id
    })
    message.success(res.msg)
    refresh()
    emits('changeCurrentInventory')
  },
  handleEditCancel(record) {
    record.onTypeTemp = record.onType || 1
    record.onTimeTemp = record.onTime
    record.onDaysTemp = record.onDays
    record.onInventoryTemp = record.onInventory
    thisFields.record.taskDesc = (['', '实时', record.onTimeTemp, `每${record.onDaysTemp}天`])[record.onTypeTemp]
    record.isEdit = false
  },
  async handleDel(record, index) {
    const res = await apiShelfInventoryTask.deletePage({ id: record.id })
    message.success(res.msg)
    refresh()
  },
  setOpen() {
    thisFields.open = props.modelValue
    if (thisFields.open) {
      whenClickReset()
    }
  },
  changList() {
    whenClickReset()
  },
  cancel() {
    emits('update:modelValue', false)
  },
  handleOk() {
    emits('update:modelValue', false)
  }
}

const { data: dataSource, run, loading, pageSize, current, refresh } = usePagination((param) => {
  const data = { ...param, ...searchFields }
  data.hideDone = 0
  data.shelfId = props.shelfId
  if (thisFields.hideDone) {
    data.hideDone = 1
  }
  return apiShelfInventoryTask.getPageList(data)
}, {
  manual: true, // 修改为false 让其自动执行
  pagination: {
    currentKey: 'pageNum', // 通过该值指定接口 当前页数 参数的属性值
    pageSizeKey: 'pageSize', // 通过该值指定接口 每页获取条数 参数的属性值
    totalKey: 'data.data.count' // 指定 data 中 total 属性的路径
  },
  formatResult: res => { // 返回数据格式化
    total.value = res.data.count
    if (current.value > 1 && res.data.list.length === 0) {
      run({ pageNum: current.value - 1, pageSize: pageSize.value })
    }
    return res.data.list
  }
})

const whenPaginationChange = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}

const whenClickSearch = () => {
  if (!props.shelfId) return
  run({ pageNum: 1, pageSize: pageSize.value })
}
const whenClickReset = () => {
  Object.assign(searchFields, getDefaultSearchFields())
  whenClickSearch()
}

onMounted(() => thisMethods.setOpen())
watch(() => props.modelValue, () => thisMethods.setOpen())
</script>

<style scoped lang="scss">
.checkboxWrap {
  margin-left: 13px;
  margin-bottom: 5px;
}
</style>
