package com.dz.ms.basic.utils;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

public class SqlUtils {
	
	static {
		try {
			Class.forName("com.mysql.cj.jdbc.Driver");
		} catch (ClassNotFoundException e) {
			e.printStackTrace();
		}
	}
	
	public static Connection getConnnection(String url, String user,String password){
		try {
			return DriverManager.getConnection(url, user, password);
		} catch (SQLException e) {
			return null;
		}
	}
	
	public static void closeConnection(Connection conn) throws SQLException{
		if(conn!=null){
			conn.close();			
		}
	}
	
	public static ResultSet getResultSet(Connection conn ,String sql) throws SQLException {
//		try {
//			Statement stat = conn.createStatement();
//			conn.close();
//			return stat.executeQuery(sql);
//		} catch (SQLException e) {
//			e.printStackTrace();
			return null;
//		}finally {
//			if (null != conn){
//				conn.close();
//			}
//		}
	}
}
