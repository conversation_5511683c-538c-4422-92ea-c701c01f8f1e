<block wx:if="{{show&&!visitor}}">
  <view class="WidgetHomeMessageReminderPlaceholder">
    <view class="_fixed _{{bg}}"></view>
  </view>
  <view class="WidgetHomeMessageReminder">
    <view class="_fixed" catch:tap="jumpUrl">
      <view class="_left">
        <view class="_notice iconfont icon-Notices"></view>
        <view class="_text">{{resData.homeTitle}}</view>
      </view>
      <view class="_right iconfont icon-a-Turnoff" catch:tap="closeNotice"></view>
    </view>
  </view>
</block>
