package com.dz.ms.user.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.constants.CommonConstants;
import com.dz.common.core.dto.basic.GiftConfigDTO;
import com.dz.common.core.dto.basic.GiftConfigOtherDTO;
import com.dz.common.core.dto.product.ProductCouponDTO;
import com.dz.common.core.dto.user.*;
import com.dz.common.core.fegin.basic.DefaultDataFeginClient;
import com.dz.common.core.fegin.order.ExchangeOrderFeignClient;
import com.dz.common.core.fegin.product.ProductFeignClient;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.vo.ReceiveCouponVo;
import com.dz.ms.user.config.Globals;
import com.dz.ms.user.entity.RightsReceiveRecord;
import com.dz.ms.user.mapper.RightsReceiveRecordMapper;
import com.dz.ms.user.service.CouponService;
import com.dz.ms.user.service.MUJIOpenApiService;
import com.dz.ms.user.service.UserInfoService;
import com.dz.ms.user.vo.CouponSelectVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 我的礼券
 *
 * @author: Handy
 * @date: 2022/2/4 17:33
 */
@Service
@Slf4j
public class CouponServiceImpl implements CouponService {

    @Resource
    private MUJIOpenApiService mUJIOpenApiService;
    @Resource
    private UserInfoService userInfoService;
    @Resource
    private ProductFeignClient productFeignClient;
    @Resource
    private RightsReceiveRecordMapper rightsReceiveRecordMapper;
    @Resource
    private DefaultDataFeginClient defaultDataFeginClient;
    @Resource
    private ExchangeOrderFeignClient exchangeOrderFeignClient;
    @Resource
    private RedisService redisService;

    @Override
    public CouponsListAllDTO getCouponList() {
        CouponsListAllDTO couponListAllDTO = new CouponsListAllDTO();
        //查询用户信息
        CurrentUserDTO user = SecurityContext.getUser();
        Globals.fixedThreadPool.execute(() -> {
            SecurityContext.setUser(user);
            // 相同用户 5分钟内只执行一次
//            boolean lock = redisService.lock("changeOrderStatusByUid" + user.getUid(), 60 * 5);
//            if (!lock) {
//                return;
//            }
            exchangeOrderFeignClient.changeOrderStatus();
        });
        UserInfoDTO userInfoDTO = userInfoService.getUserInfo(user.getUid());
        List<CouponsListDTO> allCouponList = new ArrayList<>();
        List<CouponsListDTO> physicalCouponList = new ArrayList<>();
        List<CouponsListDTO> couponList = new ArrayList<>();
        //查询自建商品券
        List<ProductCouponDTO> resultCouponList = productFeignClient.getProductListByPdType().getData();
        //查询会员券列表
        JSONObject memberCouponList = mUJIOpenApiService.memberCouponList(userInfoDTO.getCardNo(), null, 1, 1, 100);
        if (memberCouponList != null) {
            JSONArray list = memberCouponList.getJSONArray("items");
            if (!CollectionUtils.isEmpty(list)) {
                for (Object account :list){
                    //将jsonObject转为map
                    JSONObject jsonObject=JSONObject.parseObject(JSONObject.toJSONString(account));
                    if (!CollectionUtils.isEmpty(resultCouponList)){
                        //取出resultCouponList中venderId字段
                        List<String> venderIdList=new ArrayList<>();
                        for (ProductCouponDTO productCouponDTO:resultCouponList){
                            venderIdList.add(productCouponDTO.getVenderId());
                        }
                        CouponsListDTO couponsListDTO=new CouponsListDTO();
                        couponsListDTO.setCouponId(jsonObject.getString("stock_id"));
                        couponsListDTO.setCouponName(jsonObject.getString("name"));
                        couponsListDTO.setCouponDesc(jsonObject.getString("sub_name"));
                        couponsListDTO.setIcon(jsonObject.getString("icon"));
                        couponsListDTO.setCouponCode(jsonObject.getString("coupon_code"));
                        couponsListDTO.setReceiveStatus(1);
                        couponsListDTO.setCouponEffectiveTime(jsonObject.getString("end_time"));
                        couponsListDTO.setReceiveType(1);
                        if (venderIdList.contains(jsonObject.getString("stock_id"))){
                            boolean isUsed = true;
                            for (ProductCouponDTO productCouponDTO:resultCouponList){
                                if (productCouponDTO.getVenderId().equals(jsonObject.getString("stock_id"))){
                                    if (null != productCouponDTO.getCostPrice() && productCouponDTO.getCostPrice().compareTo(BigDecimal.ZERO) > 0){
                                        couponsListDTO.setCouponType(1);
                                        physicalCouponList.add(couponsListDTO);
                                        isUsed=false;
                                        break;
                                    }
                                }
                            }
                            if (isUsed){
                                couponsListDTO.setCouponType(2);
                                couponList.add(couponsListDTO);
                            }
                        }else{
                            if (jsonObject.getString("sub_name").contains(CommonConstants.PD_0_1)) {
                                couponsListDTO.setCouponType(1);
                                physicalCouponList.add(couponsListDTO);
                            } else {
                                couponsListDTO.setCouponType(2);
                                couponList.add(couponsListDTO);
                            }
                        }
                        allCouponList.add(couponsListDTO);
                    }else{
                        CouponsListDTO couponsListDTO=new CouponsListDTO();
                        couponsListDTO.setCouponId(jsonObject.getString("stock_id"));
                        couponsListDTO.setCouponName(jsonObject.getString("name"));
                        couponsListDTO.setCouponDesc(jsonObject.getString("sub_name"));
                        couponsListDTO.setIcon(jsonObject.getString("icon"));
                        couponsListDTO.setCouponCode(jsonObject.getString("coupon_code"));
                        couponsListDTO.setReceiveStatus(1);
                        couponsListDTO.setCouponEffectiveTime(jsonObject.getString("end_time"));
                        couponsListDTO.setReceiveType(1);
                        if (jsonObject.getString("sub_name").contains(CommonConstants.PD_0_1)) {
                            couponsListDTO.setCouponType(1);
                            physicalCouponList.add(couponsListDTO);
                        } else {
                            couponsListDTO.setCouponType(2);
                            couponList.add(couponsListDTO);
                        }
                        allCouponList.add(couponsListDTO);
                    }
                }
            }
        }
        //查询活动券
        JSONObject activityCouponList=mUJIOpenApiService.activityCouponList(userInfoDTO.getCardNo());
        if (null != activityCouponList){
            JSONArray activityList=activityCouponList.getJSONArray("items");
            if (!CollectionUtils.isEmpty(activityList)){
                for (Object jsonObject:activityList){
                    //将jsonObject转为map
                    JSONObject activityJsonObject=JSONObject.parseObject(JSONObject.toJSONString(jsonObject));
                    CouponsListDTO couponsListDTO=new CouponsListDTO();
                    couponsListDTO.setActivityId(activityJsonObject.getString("activity_id"));
                    couponsListDTO.setCouponName(activityJsonObject.getString("name"));
                    couponsListDTO.setCouponDesc(activityJsonObject.getString("sub_name"));
                    couponsListDTO.setIcon(activityJsonObject.getString("icon"));
                    couponsListDTO.setReceiveTime(activityJsonObject.getString("dateline"));
                    couponsListDTO.setReceiveStatus(2);
                    couponsListDTO.setReceiveType(1);
                    if (activityJsonObject.getString("sub_name").contains(CommonConstants.PD_0_1)) {
                        couponsListDTO.setCouponType(1);
                        physicalCouponList.add(couponsListDTO);
                    } else {
                        couponsListDTO.setCouponType(2);
                        couponList.add(couponsListDTO);
                    }
                    allCouponList.add(couponsListDTO);
                }
            }
        }
        if (!CollectionUtils.isEmpty(allCouponList)){
            //将allCouponList按receiveStatus倒叙排序
            allCouponList.sort(Comparator.comparing(CouponsListDTO::getReceiveStatus).reversed());
        }
        couponListAllDTO.setAllCouponList(allCouponList);
        if (!CollectionUtils.isEmpty(couponList)){
            couponList.sort(Comparator.comparing(CouponsListDTO::getReceiveStatus).reversed());
        }
        couponListAllDTO.setCouponList(couponList);
        if (!CollectionUtils.isEmpty(physicalCouponList)){
            physicalCouponList.sort(Comparator.comparing(CouponsListDTO::getReceiveStatus).reversed());
        }
        couponListAllDTO.setPhysicalCouponList(physicalCouponList);
        return couponListAllDTO;
    }

    @Override
    public PageInfo<CouponsListDTO> getCouponListExpire(CouponSelectVo param) {
        //查询用户信息
        UserInfoDTO userInfoDTO=userInfoService.getUserInfo(SecurityContext.getUser().getUid());
        Page<CouponsListDTO> page=new Page<>();
        List<CouponsListDTO> listDTO=new ArrayList<>();
        //查询会员券列表
        JSONObject memberCouponList=mUJIOpenApiService.memberCouponList(userInfoDTO.getCardNo(),null,param.getCouponStatus(),param.getPageNum(),param.getPageSize());
        if (memberCouponList != null){
            JSONArray list=memberCouponList.getJSONArray("items");            if (!CollectionUtils.isEmpty(list)){
                for (Object object:list){
                    //将jsonObject转为map
                    JSONObject jsonObject=JSONObject.parseObject(JSONObject.toJSONString(object));
                    CouponsListDTO couponsListDTO=new CouponsListDTO();
                    couponsListDTO.setCouponId(jsonObject.getString("stock_id"));
                    couponsListDTO.setCouponName(jsonObject.getString("name"));
                    couponsListDTO.setCouponDesc(jsonObject.getString("sub_name"));
                    couponsListDTO.setIcon(jsonObject.getString("icon"));
                    couponsListDTO.setCouponCode(jsonObject.getString("coupon_code"));
                    couponsListDTO.setCouponEffectiveTime(jsonObject.getString("end_time"));
                    couponsListDTO.setIsUsed(param.getCouponStatus()==2?1:2);
                    listDTO.add(couponsListDTO);
                }
            }
        }
        if  (memberCouponList != null && memberCouponList.containsKey("paginate")) {
            JSONObject pageJson = memberCouponList.getJSONObject("paginate");
            page.setCurrent(pageJson.getLong("page"));
            page.setSize(pageJson.getLong("page_size"));
            page.setTotal(pageJson.getLong("total"));
        } else {
            log.info("memberCouponList does not contain 'paginate' key");
        }
        return new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),listDTO);
    }

    /**
     * @param couponId
     * @return
     */
    @Override
    public CouponsDetailDTO getCouponDetail(String couponId,String couponCode) {
        //查询用户信息
        UserInfoDTO userInfoDTO=userInfoService.getUserInfo(SecurityContext.getUser().getUid());
        //查询券详情
        JSONObject memberCouponDetails=mUJIOpenApiService.memberCouponCodeDetails(userInfoDTO.getCardNo(),couponId,couponCode);
        CouponsDetailDTO couponsDetailDTO=new CouponsDetailDTO();
        if (null != memberCouponDetails){
            couponsDetailDTO.setCouponId(memberCouponDetails.getString("stock_id"));
            couponsDetailDTO.setCouponName(memberCouponDetails.getString("name"));
            couponsDetailDTO.setCouponDesc(memberCouponDetails.getString("sub_name"));
            couponsDetailDTO.setCouponEndTime(memberCouponDetails.getString("expired_at"));
            couponsDetailDTO.setCouponCode(memberCouponDetails.getString("coupon_code"));
            couponsDetailDTO.setCouponStatus(memberCouponDetails.getString("status"));
            couponsDetailDTO.setRuleDesc(memberCouponDetails.getString("detail"));
        }
        return couponsDetailDTO;
    }

    /**
     * @param couponId
     */
    @Override
    public void couponSend(String couponId) {
        //查询用户信息
        UserInfoDTO userInfoDTO=userInfoService.getUserInfo(SecurityContext.getUser().getUid());
    }

    /**
     * @param param
     */
    @Override
    public ReceiveCouponDTO couponReceive(ReceiveCouponVo param) {
        Result<List<GiftConfigDTO>> result=defaultDataFeginClient.getGiftList();
        List<GiftConfigDTO> giftConfigDTOS=result.getData();
//        //查询用户信息
        UserInfoDTO userInfoDTO=userInfoService.getUserInfo(SecurityContext.getUser().getUid());
        ReceiveCouponDTO receiveCouponDTO=new ReceiveCouponDTO();
        try {
            if (param.getReceiveType()==1){
                //活动领券
                JSONObject activityCouponReceive=mUJIOpenApiService.activityCouponReceive(userInfoDTO.getCardNo(),param.getActivityId());
                if (null != activityCouponReceive && activityCouponReceive.containsKey("coupons") && !CollectionUtils.isEmpty(activityCouponReceive.getJSONArray("coupons"))){
                    JSONArray couponList=activityCouponReceive.getJSONArray("coupons");
                    JSONObject jsonObject=JSONObject.parseObject(JSONObject.toJSONString(couponList.get(0)));
                    receiveCouponDTO.setStockId(jsonObject.getString("stock_id"));
                    receiveCouponDTO.setCouponCode(jsonObject.getString("coupon_code"));
                    //记录领券 只有核心礼遇里面的券才入库
                    for (GiftConfigDTO giftConfigDTO:giftConfigDTOS) {
                        if (giftConfigDTO.getGiftType()==4){
                            List<GiftConfigOtherDTO> giftConfigOtherDTO=giftConfigDTO.getGiftConfigOtherDTO();
                            for (GiftConfigOtherDTO giftConfigOtherDTO1:giftConfigOtherDTO){
                                if (!StringUtils.isEmpty(giftConfigOtherDTO1.getActivityId())) {
                                    String[] couponIds = giftConfigOtherDTO1.getActivityId().split(",");
                                    List<String> couponIdList = new ArrayList<>(Arrays.asList(couponIds));
                                    if (couponIdList.contains(param.getActivityId())) {
                                        RightsReceiveRecord rightsReceiveRecord = new RightsReceiveRecord();
                                        rightsReceiveRecord.setUserId(userInfoDTO.getId());
                                        rightsReceiveRecord.setCouponId(receiveCouponDTO.getStockId());
                                        rightsReceiveRecord.setCouponCode(receiveCouponDTO.getCouponCode());
                                        rightsReceiveRecord.setCreateAt(String.valueOf(userInfoDTO.getId()));
                                        rightsReceiveRecord.setCreateTime(new Date());
                                        rightsReceiveRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                        rightsReceiveRecordMapper.insert(rightsReceiveRecord);
                                        break;
                                    }
                                }
                            }
                        }else{
                            if (!StringUtils.isEmpty(giftConfigDTO.getCouponId())) {
                                String[] couponIds = giftConfigDTO.getCouponId().split(",");
                                List<String> couponIdList = new ArrayList<>(Arrays.asList(couponIds));
                                if (couponIdList.contains(param.getActivityId())) {
                                    RightsReceiveRecord rightsReceiveRecord = new RightsReceiveRecord();
                                    rightsReceiveRecord.setUserId(userInfoDTO.getId());
                                    rightsReceiveRecord.setCouponId(receiveCouponDTO.getStockId());
                                    rightsReceiveRecord.setCouponCode(receiveCouponDTO.getCouponCode());
                                    rightsReceiveRecord.setCreateAt(String.valueOf(userInfoDTO.getId()));
                                    rightsReceiveRecord.setCreateTime(new Date());
                                    rightsReceiveRecord.setTenantId(SecurityContext.getUser().getTenantId());
                                    rightsReceiveRecordMapper.insert(rightsReceiveRecord);
                                    break;
                                }
                            }
                        }
                    }
                }else{
                    throw new RuntimeException("领券失败");
                }
            }else{
                //普通领券
                JSONObject memberCouponReceive=mUJIOpenApiService.memberCouponReceive(userInfoDTO.getCardNo(),param.getCouponId());
                if (null != memberCouponReceive && memberCouponReceive.containsKey("stock_id") && memberCouponReceive.containsKey("coupon_code")){
                    receiveCouponDTO.setStockId(memberCouponReceive.getString("stock_id"));
                    receiveCouponDTO.setCouponCode(memberCouponReceive.getString("coupon_code"));
                }else{
                    throw new RuntimeException("领券失败");
                }
            }
        }catch (Exception e){
            log.error("领券失败",e.getMessage());
            throw new RuntimeException("领券失败");
        }
        return receiveCouponDTO;
    }

    @Override
    public List<RightsReceiveRecordDTO> rightsReceiveRecord() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<RightsReceiveRecordDTO> listDTO=new ArrayList<>();
        UserInfoDTO userInfoDTO=userInfoService.getUserInfo(SecurityContext.getUser().getUid());
        LambdaQueryWrapper<RightsReceiveRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RightsReceiveRecord::getUserId, userInfoDTO.getId());
        List<RightsReceiveRecord> rightsReceiveRecordList=rightsReceiveRecordMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(rightsReceiveRecordList)){
            for (RightsReceiveRecord rightsReceiveRecord : rightsReceiveRecordList){
                RightsReceiveRecordDTO rightsReceiveRecordDTO=new RightsReceiveRecordDTO();
                //查询券详情
                JSONObject memberCouponDetails=mUJIOpenApiService.memberCouponCodeDetails(userInfoDTO.getCardNo(),rightsReceiveRecord.getCouponId(),rightsReceiveRecord.getCouponCode());
                rightsReceiveRecordDTO.setCouponName(memberCouponDetails.getString("name"));
                rightsReceiveRecordDTO.setCouponDesc(memberCouponDetails.getString("sub_name"));
                rightsReceiveRecordDTO.setImgUrl(memberCouponDetails.getString("icon"));
                rightsReceiveRecordDTO.setReceiveTime(sdf.format(rightsReceiveRecord.getCreateTime()));
                listDTO.add(rightsReceiveRecordDTO);
            }
        }
        return listDTO;
    }
}
