package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统告警配置
 * @author: Handy
 * @date:   2022/08/04 10:05
 */
@Getter
@Setter
@NoArgsConstructor
@Table("系统告警配置")
@TableName(value = "alarm_config")
public class AlarmConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = false,comment = "告警渠道 1邮件 2企微消息 3短信")
    private Integer alarmType;
    @Columns(type = ColumnType.VARCHAR,length = 255,isNull = false,comment = "接收消息用户列表逗号隔开")
    private String receivers;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = false,defaultValue = "1",comment = "配置状态 0停用 1启用")
    private Integer state;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    public AlarmConfig(Long id, Integer alarmType, String receivers, Integer state) {
        this.id = id;
        this.alarmType = alarmType;
        this.receivers = receivers;
        this.state = state;
    }

}
