<template>
  <a-drawer
    v-model:open="visibleData.visible"
    :title="title"
    id="lottery-add-drawer"
    width="1000"
    placement="right"
    :maskClosable="false"
    @close="handleClose"
  >
    <a-spin :spinning="loading">
      <a-form
        ref="formRef"
        :model="formModel"
        :rules="formRules"
        :labelCol="{ style: 'width:110px' }"
      >
        <a-divider orientation="left">活动基本配置</a-divider>
        <a-form-item label="活动名称" name="lotteryName">
          <a-input
            v-model:value="formModel.lotteryName"
            placeholder="请输入活动名称"
            show-count
            :maxlength="20"
            class="input-width"
          />
        </a-form-item>
        <a-form-item label="活动Code" name="lotteryCode">
          <a-input
            v-model:value="formModel.lotteryCode"
            placeholder="请输入活动Code"
            show-count
            :maxlength="30"
            class="input-width"
          />
        </a-form-item>
        <a-form-item label="活动展示时间" name="showTimeFlag">
          <a-radio-group v-model:value="formModel.showTimeFlag" button-style="solid">
            <a-radio-button :value="1">时间段</a-radio-button>
            <a-radio-button :value="2">不限制</a-radio-button>
          </a-radio-group>
          <a-form-item v-if="formModel.showTimeFlag === 1" name="_showTime" class="m-t-12">
            <a-range-picker
              v-model:value="formModel._showTime"
              :show-time="{ defaultValue: timePickerDefaultValue }"
            />
          </a-form-item>
          <div class="global-tip">活动在前台展示的时间设置</div>
        </a-form-item>
        <a-form-item label="活动有效时间" name="validTimeFlag">
          <a-radio-group v-model:value="formModel.validTimeFlag" button-style="solid">
            <a-radio-button :value="1">时间段</a-radio-button>
            <a-radio-button :value="2">不限制</a-radio-button>
          </a-radio-group>
          <a-form-item v-if="formModel.validTimeFlag === 1" name="_validTime" class="m-t-12">
            <a-range-picker
              v-model:value="formModel._validTime"
              :show-time="{ defaultValue: timePickerDefaultValue }"
            />
          </a-form-item>
          <div class="global-tip">展示中的活动，未到开启时间，无法进行抽奖“活动未开始”</div>
        </a-form-item>
        <a-form-item label="活动优先级" name="priority">
          <a-input-number
            v-model:value="formModel.priority"
            :min="1"
            :max="999"
            :parser="inputNumberParserInteger"
            style="margin-right: 10px"
          />
          <span class="global-tip">排序从小到大，对应前台tab从左至右或从上至下</span>
        </a-form-item>
        <a-form-item label="抽奖规则">
          <!-- 选择规则页面 -->
          <SelectShop
            :showList="[1]"
            :link="ruleLink"
            btnType="link"
            btnTitle="+ 关联自定义规则"
            @ok="handleSelectRule"
          />
        </a-form-item>
        <a-divider orientation="left">活动奖品配置</a-divider>
        <a-form-item label="奖品配置" name="lotteryPrizeList">
          <span style="margin-right: 10px">
            <span>目前共有</span>
            <span class="text-primary">{{ formModel.lotteryPrizeList.length }}</span>
            <span>个奖品</span>
          </span>
          <a-button type="link" size="small" @click="addGoodsModalVisible = true">
            + 添加奖品
          </a-button>
          <a-button type="link" size="small" @click="manageRewardVisible = true">管理奖品</a-button>
        </a-form-item>
        <a-form-item label="默认兜底奖项" name="strategy">
          <a-select
            v-model:value="formModel.strategy"
            class="input-width"
            placeholder="请选择"
            @change="formModel.coverPrizeName = undefined"
          >
            <a-select-option :value="1">请选择一个奖项作为兜底奖项</a-select-option>
            <a-select-option :value="2">重新抽奖-直至抽到有库存的奖品</a-select-option>
          </a-select>
          <a-button type="link" size="small" @click="handleAddAward"> + 添加奖项 </a-button>

          <a-form-item v-if="formModel.strategy === 1" name="coverPrizeName" class="m-t-12 m-b-0">
            <a-select
              v-model:value="formModel.coverPrizeName"
              class="input-width"
              placeholder="请选择"
              :options="coverAwardOptionsList"
              allow-clear
            />
            <div class="global-tip">
              当对应奖品、奖项库存为0以及用户没有任何一个奖项可以中时，会默认为该奖项，可设置为“谢谢参与”或某个库存及多的奖项
            </div>
          </a-form-item>
        </a-form-item>
        <div
          v-for="(item, index) in formModel.lotteryAwardList"
          :key="item._id"
          class="reward-item"
        >
          <a-form-item label="奖项名称" :labelCol="{ style: 'width:105px' }">
            <a-input
              v-model:value.lazy="item.awardName"
              placeholder="请输入"
              show-count
              :maxlength="20"
              class="input-width"
              @blur="handleaAardItemNameBlur(item)"
            />
            <a-popconfirm title="确定删除该奖项？" @confirm="handleDeleteAward(item, index)">
              <a-button style="margin-left: 160px" type="link" size="small">
                - 删除该奖项
              </a-button>
            </a-popconfirm>
          </a-form-item>
          <a-form-item label="限制中奖人群" :labelCol="{ style: 'width:105px' }">
            <a-select
              v-model:value="item.crowdId"
              class="input-width-small"
              :options="crowdAllList"
              show-search
              :filterOption="crowdFilterOption"
              placeholder="请选择"
            />
          </a-form-item>
          <a-form-item label="限制中奖次数" :labelCol="{ style: 'width:105px' }">
            <a-radio-group
              v-model:value="item.limitType"
              button-style="solid"
              @change="handleAwardLimitTypeChange(item._id)"
            >
              <a-radio-button :value="1">单项设置</a-radio-button>
              <a-radio-button :value="2">关联设置</a-radio-button>
            </a-radio-group>
            <div v-if="item.limitType === 1" class="draw-cost-box m-t-12">
              <span>活动内每人</span>
              <a-input-number
                v-model:value="item.countLimit"
                :min="0"
                :parser="inputNumberParserInteger"
                class="row-space"
              />
              <span>次</span>
            </div>
            <div v-else label="关联奖项" class="m-t-12">
              <a-select
                v-model:value="item.relationAwardName"
                class="input-width-small"
                placeholder="请选择奖项，仅单选"
                :options="awardOptionsList"
              />
              <div class="global-tip">
                关联后，同对应奖项共用机会次数，例如A奖项“活动内每人1次”
                B奖项关联A奖项后，A、B奖项共用该“1”次机会次数，用户次数消耗后，无法再抽中该奖项
              </div>
            </div>
          </a-form-item>
          <a-form-item label="添加奖品" :labelCol="{ style: 'width:105px' }" class="m-t-12 m-b-12">
            <a-select
              v-model:value="item._currentSelectPrize"
              class="input-width-small"
              :options="awardPrizeOptionsList"
              allow-clear
              placeholder="请选择奖品"
            />
            <a-button type="link" size="small" @click="handleAddPrize(item)"> + 添加 </a-button>
            <div>
              <div
                v-for="(prize, index) in item.lotteryPrizeList"
                :key="prize._id"
                class="award-prize-box"
                :class="{ disabled: !prize.state }"
              >
                <span>{{ index + 1 }}</span>
                <span class="award-prize-name">{{ prize.prizesName }}</span>
                <a-input-number
                  v-model:value.lazy="prize.prizeProbability"
                  :min="0"
                  :max="100"
                  :step="0.01"
                  :formatter="inputNumberFormatter"
                  :parser="inputNumberParser"
                  :disabled="!prize.state"
                  placeholder="请设置奖项内几率"
                />
                <span class="award-prize-rate">%</span>
                <a-button type="link" size="small" @click="handleDeletePrize(item, prize)">
                  - 删除
                </a-button>
              </div>
            </div>
          </a-form-item>
          <a-form-item label="该奖项中奖率" :labelCol="{ style: 'width:105px' }">
            <div class="draw-cost-box probability">
              <a-input-number
                v-model:value.lazy="item.awardProbability"
                :min="0"
                :max="100"
                :step="0.01"
                :formatter="inputNumberFormatter"
                :parser="inputNumberParser"
                class="row-space"
              />
              <span>%</span>
            </div>
          </a-form-item>
        </div>
        <a-divider orientation="left">抽奖消耗、次数、任务配置</a-divider>
        <a-form-item label="抽奖消耗">
          <div>通过次数消耗（默认）</div>
          <div class="draw-cost-box">
            <span>通过积分消耗</span>
            <a-form-item name="pointNum" class="row-space">
              <a-input-number
                v-model:value="formModel.pointNum"
                :min="1"
                :parser="inputNumberParserInteger"
              />
            </a-form-item>
            <span>积分/次</span>
            <span style="margin-left: 15px">每</span>
            <a-form-item name="readyDay" class="row-space">
              <a-input-number
                v-model:value="formModel.readyDay"
                :min="1"
                :parser="inputNumberParserInteger"
              />
            </a-form-item>
            <a-form-item name="readyType" class="row-space">
              <a-select v-model:value="formModel.readyType" style="width: 60px">
                <a-select-option value="readyDay">天</a-select-option>
                <a-select-option value="readyMonth">月</a-select-option>
              </a-select>
            </a-form-item>
            <span>可兑换</span>
            <a-form-item name="redeemNum" class="row-space">
              <a-input-number
                v-model:value="formModel.redeemNum"
                :min="1"
                :max="5"
                :parser="inputNumberParserInteger"
              />
            </a-form-item>
            <span>次</span>
          </div>
        </a-form-item>
        <a-form-item label="初始抽奖次数" name="lotteryInitNumList">
          <UserGroupSelect
            v-model:value="formModel.lotteryInitNumList"
            :crowdAllList="crowdAllList"
            addText="添加初始规则"
          >
            <template #tip> 活动开始后，对应用户初始拥有的奖励次数，不设置则默认 0 次 </template>
          </UserGroupSelect>
        </a-form-item>
        <a-form-item label="每次打卡获得" name="lotteryClockInList">
          <UserGroupSelect
            v-model:value="formModel.lotteryClockInList"
            :crowdAllList="crowdAllList"
            addText="添加打卡规则"
          >
            <template #tip> 用户每天进入活动后，奖励的抽奖次数 </template>
          </UserGroupSelect>
        </a-form-item>
        <a-form-item label="通过任务获得" name="taskIds">
          <a-select
            v-model:value="formModel.taskIds"
            class="input-width"
            :options="interactionTaskFestivalList"
            show-search
            allow-clear
            mode="multiple"
            :filterOption="crowdFilterOption"
            placeholder="请选择任务"
          />
        </a-form-item>
        <a-divider orientation="left">活动分享内容配置</a-divider>
        <a-form-item style="width: 400px">
          <pageShare :show-share="false" :addParams="pageSetting" />
        </a-form-item>
      </a-form>
    </a-spin>
    <template #footer>
      <a-space>
        <a-button @click="handleClose" :loading="loading">取消</a-button>
        <a-button type="primary" :loading="loading" @click="handleSubmit">确定</a-button>
      </a-space>
    </template>
  </a-drawer>

  <!-- 选择任务 -->
  <SelectTaskModal v-model:open="selectTaskModalVisible" />
  <!-- 添加奖品 -->
  <AddGoodsModal
    v-model:open="addGoodsModalVisible"
    :selectedGoodsList="formModel.lotteryPrizeList"
    @ok="handleAddGoods"
  />
  <!-- 管理奖品 -->
  <ManaGoodsModal
    v-model:open="manageRewardVisible"
    :lotteryId="formModel.id"
    :crowdAllList="crowdAllList"
    :goodsList="formModel.lotteryPrizeList"
    :updateDataFun="getEditData"
    @ok="handleManageOk"
  />
</template>
<script setup>
import UserGroupSelect from './userGroupSelect.vue'
import SelectTaskModal from './SelectTaskModal.vue'
import AddGoodsModal from './AddGoodsModal.vue'
import ManaGoodsModal from './ManaGoodsModal.vue'
import SelectShop from '@/components/WidgetsConfig/selectShop.vue'

import { computed, reactive, ref, watch } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'

import { inputNumberFormatter, inputNumberParser, inputNumberParserInteger } from '../utils/utils'
import {
  crowdAllList as crowdAllListApi,
  getInteractionTaskFestivalList as getInteractionTaskFestivalListApi,
  addLottery,
  editLottery,
  getLotteryInfo
} from '@/http/index.js'
import { rewardTypeOptionsObj, TASK_DESC_OBJ } from '../utils/constant'

const visibleData = defineModel('visible', {
  type: Object,
  default: () => ({ visible: false, id: null })
})
const emit = defineEmits(['ok', 'cancel'])
// 标题
const title = computed(() => `${visibleData.value.id ? '编辑' : '新增'}活动`)

watch(
  () => visibleData.value.visible,
  (value) => {
    if (value) {
      if (crowdAllList.value.length === 0) {
        getCrowdAllList()
      }
      if (visibleData.value.id) getEditData()
      else {
        getInteractionTaskFestivalList()
      }
    }
  }
)

// 奖品原始数据 (编辑奖品时,如果删除的奖品,数据要保留,lotteryAwardId置空)
let originalAwardList = []

// 编辑是获取抽奖数据
async function getEditData() {
  loading.value = true
  const id = visibleData.value.id
  const res = await getLotteryInfo(id)
  const {
    coverPrizeName,
    lotteryName,
    showTimeFlag,
    showStartTime,
    showEndTime,
    validTimeFlag,
    validStartTime,
    validEndTime,
    priority,
    strategy,
    pointNum,
    readyDay,
    readyType,
    redeemNum,
    linkName,
    linkUrl,
    linkType,
    shareTitle,
    shareCover,
    sharePath,
    state,
    taskList,
    lotteryInitNumList,
    lotteryClockInList,
    lotteryAwardList,
    lotteryPrizeList,
    lotteryCode
  } = res.data
  getInteractionTaskFestivalList(taskList)
  originalAwardList = JSON.parse(JSON.stringify(lotteryAwardList))

  // 添加副id,前端用提交时删除
  lotteryInitNumList?.forEach((item) => {
    item._id = item.id
  })
  lotteryClockInList?.forEach((item) => {
    item._id = item.id
  })
  lotteryPrizeList.forEach((item) => {
    item._id = item.id
    // countLimit 单奖品中奖限制/人 -1时为不限制 但输入框中显示为0
    item.countLimit = item.countLimit === -1 ? 0 : item.countLimit
  })
  lotteryAwardList.forEach((item, index, array) => {
    item._id = item.id
    // 如果关联了奖项，要将奖项名称改为对于奖项Id
    if (item.relationAwardName) {
      item.relationAwardName = array.find((f) => f.awardName === item.relationAwardName).id
    }
  })

  const modal = {
    id,
    coverPrizeName: coverPrizeName
      ? lotteryAwardList.find((f) => f.awardName === coverPrizeName).id
      : undefined,
    lotteryName,
    lotteryCode,
    showTimeFlag,
    _showTime: showTimeFlag === 1 ? [dayjs(showStartTime), dayjs(showEndTime)] : undefined,
    showStartTime,
    showEndTime,
    validTimeFlag,
    _validTime: validTimeFlag === 1 ? [dayjs(validStartTime), dayjs(validEndTime)] : undefined,
    validStartTime,
    validEndTime,
    priority,
    strategy,
    pointNum,
    readyDay,
    readyType,
    redeemNum,
    linkName,
    linkUrl,
    linkType,
    state,
    taskIds: taskList ? taskList.map((item) => item.id) : [],
    lotteryInitNumList,
    lotteryClockInList,
    lotteryPrizeList,
    lotteryAwardList
  }
  Object.assign(formModel.value, modal)
  Object.assign(pageSetting, { shareTitle, shareImg: shareCover, sharePath })

  formModel.value.lotteryAwardList.forEach((item) => {
    item.lotteryPrizeList = item.lotteryPrizeList.map((prize) => {
      const refItem = formModel.value.lotteryPrizeList.find((f) => f.id === prize.id)
      refItem._awardId = item.id
      return refItem
    })
  })
  loading.value = false

  return formModel.value
}
// 关闭弹窗
function handleClose() {
  formRef.value.resetFields()
  visibleData.value.visible = false
  visibleData.value.id = null
  originalAwardList = []
  Object.assign(pageSetting, { shareTitle: '', shareImg: '', sharePath: '' })
  Object.assign(formModel.value, {
    id: '',
    lotteryCode: '',
    lotteryName: '',
    showStartTime: '',
    showEndTime: '',
    validStartTime: '',
    validEndTime: '',
    strategy: undefined,
    lotteryAwardList: [],
    lotteryPrizeList: [
      {
        _id: 'xxcy',
        prizesName: '谢谢参与',
        totalStock: 0,
        state: 1,
        crowdId: -1,
        countLimit: 0,
        prizesType: 99,
        prizesClass: 1,
        prizeProbability: '',
        pointsNum: undefined
      }
    ],
    coverPrizeName: undefined,
    lotteryInitNumList: [],
    lotteryClockInList: [],
    taskIds: [],
    linkName: '',
    linkUrl: '',
    linkType: '',
    shareTitle: '',
    shareImg: '',
    sharePath: '',
    state: 1
  })
  document.querySelector('.ant-drawer-body').scroll({ top: 0 })
}
// 确认提交
async function handleSubmit() {
  try {
    console.log('formModel.value: ', formModel.value)
    const res = await formRef.value.validateFields()
    await validateAwardValue()
    await validatePointNum()

    loading.value = true
    const { _showTime, _validTime } = formModel.value
    let {
      coverPrizeName,
      showTimeFlag,
      validTimeFlag,
      lotteryInitNumList,
      lotteryClockInList,
      lotteryPrizeList,
      lotteryAwardList,
      ...reset
    } = JSON.parse(JSON.stringify(formModel.value))
    delete reset._showTime
    delete reset._validTime

    lotteryClockInList.forEach((item) => {
      delete item._id
    })
    lotteryInitNumList.forEach((item) => {
      delete item._id
    })
    // 过滤掉已添加到奖项中的商品
    lotteryPrizeList = lotteryPrizeList.filter((f) => !f._awardId)
    const allLotteryPrizeIds = lotteryPrizeList.map((f) => f.id)
    lotteryPrizeList.forEach((item) => {
      delete item._id
      // countLimit 单奖品中奖限制/人 -1时为不限制 但输入框中显示为0
      item.countLimit = item.countLimit === 0 ? -1 : item.countLimit
    })
    // 设置 coverPrizeName 对应奖项 isCover 为 1
    coverPrizeName = coverPrizeName
      ? lotteryAwardList.find((f) => f._id === coverPrizeName).awardName
      : ''
    let prizeNum = 0 // 奖品数量，只统计奖项内关联的
    lotteryAwardList.forEach((item, _, array) => {
      prizeNum += item.lotteryPrizeList.length
      if (item.relationAwardName) {
        item.relationAwardName = array.find((f) => f.id === item.relationAwardName).awardName
      }
      item.isCover = item.awardName === coverPrizeName ? 1 : 0
      item.lotteryPrizeList.forEach((prize) => {
        prize.lotteryAwardId = item.id
        prize.countLimit = prize.countLimit === 0 ? -1 : prize.countLimit
      })

      // 编辑奖品时,如果删除了奖品并且奖品列表中还存在,数据要保留,lotteryId置空
      const originalData = originalAwardList.find((f) => f.id === item.id)
      if (originalData) {
        const curIds = item.lotteryPrizeList.map((f) => f.id)
        const filterData = originalData.lotteryPrizeList.filter((f) => {
          const flag = !curIds.includes(f.id) && allLotteryPrizeIds.includes(f.id)
          if (flag) f.lotteryAwardId = ''
          return flag
        })
        item.lotteryPrizeList.push(...filterData)
      }
    })
    // 删除副id
    lotteryAwardList.forEach((item) => {
      delete item._id
      delete item._currentSelectPrize
      item.lotteryPrizeList.forEach((prize) => {
        delete prize._id
        delete prize._awardId
      })
    })
    const params = Object.assign(reset, {
      showTimeFlag,
      showStartTime: showTimeFlag === 1 ? _showTime[0].format('YYYY-MM-DD HH:mm:ss') : '',
      showEndTime: showTimeFlag === 1 ? _showTime[1].format('YYYY-MM-DD HH:mm:ss') : '',
      validTimeFlag,
      validStartTime: validTimeFlag === 1 ? _validTime[0].format('YYYY-MM-DD HH:mm:ss') : '',
      validEndTime: validTimeFlag === 1 ? _validTime[1].format('YYYY-MM-DD HH:mm:ss') : '',
      lotteryInitNumList,
      lotteryClockInList,
      lotteryPrizeList,
      lotteryAwardList,
      prizeNum,
      coverPrizeName,
      shareTitle: pageSetting.shareTitle,
      shareCover: pageSetting.shareImg,
      sharePath: pageSetting.sharePath
    })
    console.log('params: ', params)
    // return  loading.value = false
    params.id ? await editLottery(params) : await addLottery(params)
    loading.value = false
    handleClose()
    message.success('保存成功')
    emit('ok')
  } catch (error) {
    loading.value = false
    console.log('error: ', error)
    if (error.errmsg) message.error({ content: error.errmsg, duration: 3 })
  }
}

const loading = ref(false) // 加载中
const formRef = ref() // 表单ref
const selectTaskModalVisible = ref(false) // 选择任务弹窗
const addGoodsModalVisible = ref(false) // 添加奖品弹窗
const manageRewardVisible = ref(false) // 管理奖品弹窗
const timePickerDefaultValue = [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')]

const formModel = ref({
  id: '',
  lotteryName: '',
  lotteryCode: '',
  showTimeFlag: 1,
  _showTime: undefined,
  showStartTime: '',
  showEndTime: '',
  validTimeFlag: 1,
  _validTime: undefined,
  validStartTime: '',
  validEndTime: '',
  priority: '',
  strategy: undefined,
  lotteryAwardList: [],
  // 默认存在谢谢参与奖品
  lotteryPrizeList: [
    {
      _id: 'xxcy',
      prizesName: '谢谢参与',
      totalStock: 0,
      state: 1,
      crowdId: -1,
      countLimit: 0,
      prizesType: 99,
      prizesClass: 1,
      prizeProbability: '',
      pointsNum: undefined
    }
  ],
  pointNum: '',
  readyDay: '',
  readyType: 'readyDay',
  redeemNum: '',
  coverPrizeName: undefined,
  lotteryInitNumList: [],
  lotteryClockInList: [],
  taskIds: [],
  linkName: '',
  linkUrl: '',
  linkType: '',
  shareTitle: '',
  shareImg: '',
  sharePath: '',
  state: 1
})

const formRules = ref({
  lotteryName: [{ required: true, message: '请输入活动名称', trigger: 'blur' }],
  lotteryCode: [{ required: true, message: '请输入活动Code', trigger: 'blur' }],
  showTimeFlag: [{ required: true, message: '请选择活动展示时间', trigger: 'change' }],
  _showTime: [{ required: true, message: '请选择活动展示时间', trigger: 'change' }],
  validTimeFlag: [{ required: true, message: '请选择活动有效时间', trigger: 'change' }],
  priority: [{ required: true, message: '请输入活动优先级', trigger: 'blur' }],
  _validTime: [{ required: true, message: '请选择活动有效时间', trigger: 'change' }],
  lotteryPrizeList: [{ required: true, message: '请添加奖品', trigger: 'change' }],
  strategy: [{ required: true, message: '请选择默认兜底奖项', trigger: 'change' }],
  coverPrizeName: [{ required: true, message: '请选择兜底奖项', trigger: 'blur' }],
  // readyDay: [{ required: true, message: '请输入', trigger: 'blur' }],
  // readyType: [{ required: true, message: '请选择', trigger: 'change' }],
  // redeemNum: [{ required: true, message: '请输入', trigger: 'blur' }],
  // pointNum: [{ required: true, message: '请输入', trigger: 'blur' }],
  lotteryInitNumList: [{ validator: validateLotteryInitNumListRule }],
  lotteryClockInList: [{ validator: validateLotteryClockInRule }]
})
// 验证初始抽奖次数
function validateLotteryInitNumListRule(rule, value, callback) {
  console.log('value: ', formModel.value.lotteryInitNumList)
  const error = formModel.value.lotteryInitNumList.some((item) => {
    if (!item.crowdId) return true
  })
  if (error) return Promise.reject('请选择人群包')
  return Promise.resolve()
}
// 验证打卡获得
function validateLotteryClockInRule(rule, value, callback) {
  const error = formModel.value.lotteryClockInList.some((item) => {
    if (!item.crowdId) return true
  })
  if (error) return Promise.reject('请选择人群包')
  return Promise.resolve()
}
// 验证奖项名称 禁止和lotteryAwardList数组内其他奖项名称重复
function handleaAardItemNameBlur(item) {
  if (!item.awardName) return
  const error = formModel.value.lotteryAwardList.some((s) => {
    if (s._id !== item._id && s.awardName === item.awardName) return true
  })
  if (error) return message.warning('奖项名称已存在，请修改')
}
// 验证奖项
function validateAwardValue() {
  let errmsg = ''
  let awardProbabilitySum = 0
  let names = []
  if (formModel.value.lotteryAwardList.length === 0) {
    return Promise.reject({ errmsg: '请添加至少一个奖项' })
  }
  const error = formModel.value.lotteryAwardList.some((item, index) => {
    const { awardName, awardProbability, lotteryPrizeList } = item
    const zIndex = `第 ${index + 1} 个奖项`
    let prizeProbabilitySum = 0

    // 奖项内奖品列表为空
    if (lotteryPrizeList.length === 0) {
      errmsg = `${zIndex}内没有奖品，请添加至少一个奖品`
      return true
    }
    // 名称必填
    if (!awardName) {
      errmsg = `请输入${zIndex}的奖项名称`
      return true
    }
    // 名称是否重复
    if (names.includes(awardName)) {
      errmsg = `${zIndex}的奖项名称重复，请调整`
      return true
    }
    names.push(awardName)
    // 验证奖项和奖项内奖品完成率是否填写 总和是否超过100%
    if (typeof awardProbability != 'number') {
      errmsg = `请输入${zIndex}的中奖率`
      return true
    }
    awardProbabilitySum += awardProbability
    if (awardProbabilitySum > 100) {
      errmsg = '所有奖项中奖率不能高于100%，请调整'
      return true
    }
    const prizeError = lotteryPrizeList.some((prize, idx) => {
      if (typeof prize.prizeProbability != 'number') {
        errmsg = `请输入${zIndex}，第 ${idx + 1} 个奖品中奖率`
        return true
      }
      prizeProbabilitySum += prize.prizeProbability
      if (prizeProbabilitySum > 100) {
        errmsg = `${zIndex}中所有奖品中奖率高于100%，请调整`
        return true
      }
    })
    if (prizeError) return true
  })
  if (error) return Promise.reject({ errmsg })
  return Promise.resolve()
}
// 验证通过积分消耗
function validatePointNum() {
  const { pointNum, readyDay, redeemNum } = formModel.value
  // pointNum, readyDay, redeemNum 填写了任意一个，其他三项必须补充完整
  if ((pointNum || readyDay || redeemNum) && (!pointNum || !readyDay || !redeemNum)) {
    return Promise.reject({ errmsg: '请将抽奖消耗中通过积分消耗字段补充完整' })
  }
  return Promise.resolve()
}
// 选择规则
const ruleLink = computed(() => {
  return {
    linkName: formModel.value.linkName,
    linkUrl: formModel.value.linkUrl,
    linkType: formModel.value.linkType
  }
})
function handleSelectRule(data) {
  Object.assign(formModel.value, data.data)
}
// 分享组件参数
const pageSetting = reactive({
  isShare: 1,
  shareTitle: '',
  shareImg: '',
  sharePath: ''
})

// 添加奖品弹窗确认
function handleAddGoods(data) {
  formModel.value.lotteryPrizeList.unshift(...data)
  formRef.value.validate(['lotteryPrizeList'])
}

// 管理奖品确认
function handleManageOk({ list, deleteIds }) {
  formModel.value.lotteryPrizeList = list
  // 更新所有已绑定的奖项的奖品列表,删除deleteIds存在中的奖品
  formModel.value.lotteryPrizeList = formModel.value.lotteryPrizeList.filter((item) => {
    return !deleteIds.includes(item._id)
  })
  formModel.value.lotteryAwardList.forEach((item) => {
    item.lotteryPrizeList = item.lotteryPrizeList
      .filter((f) => !deleteIds.includes(f._id))
      .map((prize) => {
        const newPrize = list.find((d) => d._id === prize._id)
        return newPrize || prize
      })
  })
}

// 选择人群包单选选择框通用筛选
function crowdFilterOption(input, option) {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 选择兜底奖项列表 (仅单项设置、过滤掉已选择的)
const awardOptionsList = computed(() => {
  return formModel.value.lotteryAwardList
    .filter((item) => item.limitType === 1)
    .map((item, i) => {
      return { value: item._id, label: item.awardName || `奖项 ${i + 1}` }
    })
})
// 选择兜底奖项列表 (过滤掉已选择的)
const coverAwardOptionsList = computed(() => {
  return formModel.value.lotteryAwardList.map((item, i) => {
    return { value: item._id, label: item.awardName || `奖项 ${i + 1}` }
  })
})
// 奖项中添加奖品的选项
const awardPrizeOptionsList = computed(() => {
  return formModel.value.lotteryPrizeList
    .filter((item) => !item._awardId && item.state === 1)
    .map((item) => {
      return { value: item._id, label: item.prizesName }
    })
})
// 添加奖项
function handleAddAward() {
  formModel.value.lotteryAwardList.push({
    _id: Date.now(),
    awardName: '',
    crowdId: -1,
    limitType: 1,
    countLimit: 1,
    awardProbability: '',
    _currentSelectPrize: undefined,
    relationAwardName: undefined,
    lotteryPrizeList: []
  })
}
// 删除奖项
function handleDeleteAward(_item, index) {
  // 删除兜底奖项关联
  if (formModel.value.strategy === 1 && formModel.value.coverPrizeName === _item._id) {
    formModel.value.coverPrizeName = undefined
  }
  // 删除关联该奖项的关联奖项
  formModel.value.lotteryAwardList.forEach((item) => {
    if (item.relationAwardName === _item.awardName) {
      item.relationAwardName = undefined
    }
  })
  // 从奖项列表删除该奖项
  // 删除奖项下的奖品,并重置他们的_awardId、奖项几率
  const [deleteEl] = formModel.value.lotteryAwardList.splice(index, 1)
  deleteEl.lotteryPrizeList.forEach((item) => {
    item._awardId = undefined
    item.prizeProbability = ''
    item.lotteryAwardId = ''
  })
  console.log('formModel.value: ', formModel.value)
}
// 添加奖项内奖品
function handleAddPrize(item) {
  if (!item._currentSelectPrize) {
    return message.warning('请先选择奖品')
  }
  const prize = formModel.value.lotteryPrizeList.find((f) => f._id === item._currentSelectPrize)
  prize._awardId = item._id
  item.lotteryPrizeList.push(prize)

  formModel.value.lotteryAwardList.forEach((item) => {
    item._currentSelectPrize = undefined
  })
}
// 删除奖项内奖品
function handleDeletePrize(item, prize) {
  prize._awardId = undefined
  prize.lotteryAwardId = ''
  prize.prizeProbability = ''
  item.lotteryPrizeList = item.lotteryPrizeList.filter((item) => item._id !== prize._id)
}
// 更改奖项的限制中奖次数 如果当前奖项被其他奖项关联进行重置
function handleAwardLimitTypeChange(_id) {
  console.log('e: ', _id)
  formModel.value.lotteryAwardList.find((item) => {
    if (item.relationAwardName === _id) {
      item.relationAwardName = undefined
      return true
    }
  })
}
// 人群包数据
const crowdAllList = ref([])
async function getCrowdAllList() {
  const res = await crowdAllListApi({ crowdStatus: 0 })
  const list = res.data.map((item) => ({ value: item.id, label: item.crowdName }))
  list.unshift({ value: -1, label: '不限制' })
  crowdAllList.value = list
}

// 关联任务数据
const interactionTaskFestivalList = ref([])
async function getInteractionTaskFestivalList(editData) {
  const res = await getInteractionTaskFestivalListApi()
  interactionTaskFestivalList.value = res.data.concat(editData || []).map((item) => {
    let { id, readyCycle, taskRewardList, taskName, taskDesc } = item
    let textLabel
    if (readyCycle != 3 && taskRewardList?.length) {
      textLabel = rewardTypeOptionsObj[taskRewardList[0].rewardType]
    } else if (readyCycle == 3) {
      textLabel = '组合'
    } else {
      textLabel = '--'
    }

    return {
      value: id,
      label: `${taskName} ${TASK_DESC_OBJ[taskDesc]} ${textLabel}`
    }
  })
}
</script>

<style scoped lang="scss">
:deep(.flex-box) {
  display: flex;
  &.flex-center {
    align-items: center;
  }
}
.m-b-0 {
  margin-bottom: 0;
}
:deep(.m-t-12) {
  margin-top: 12px;
}
:deep(.m-b-12) {
  margin-bottom: 12px;
}
:deep(.input-width) {
  width: 400px;
}
:deep(.input-width-small) {
  width: 300px;
}
:deep(.ant-input-number) {
  width: 60px;
}
:deep(.row-space) {
  margin: 0 5px;
}
.draw-cost-box {
  margin-top: 10px;
  display: flex;
  & > span {
    line-height: 32px;
  }
  &.probability {
    margin-top: 0;
    .row-space {
      width: 80px;
    }
  }
}
.award-prize-box {
  margin-top: 10px;
  display: flex;
  align-items: center;
  &.disabled {
    .award-prize-name,
    .award-prize-rate {
      color: #999;
    }
  }
  .award-prize-name {
    margin: 0 12px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    width: 180px;
    text-align: center;
  }
  .ant-input-number {
    width: 150px;
    text-align: center;
  }
  .award-prize-rate {
    margin-left: 12px;
    margin-right: 20px;
  }
}
.reward-item {
  margin: 12px 0 12px 110px;
  width: 800px;
  padding: 14px;
  border-radius: 4px;
  border: 1px solid #e3e2e2;
}
</style>
