package com.dz.ms.adaptor.processor;

import com.dz.common.core.fegin.user.MUJIOpenApiFeignClient;
import com.dz.common.core.vo.MyMsgVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.Response;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.net.MalformedURLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 *
 */
@Slf4j
@Component
public class myMsgSyncJob implements BasicProcessor {

    @Resource
    private MUJIOpenApiFeignClient mujiOpenApiFeignClient;
    @Override
    public ProcessResult process(TaskContext context) throws MalformedURLException {
        String jobParams = context.getJobParams();
        if(Objects.isNull(jobParams)){
            return new ProcessResult(true, "param null");
        }
        log.info("参数是否存在值: {}", jobParams);
        List<String> fileNames = new ArrayList<>();
        Long id=null;
        String[] strs = jobParams.split(",");
        for (int i = 0; i < strs.length; i++) {
            if (i==strs.length-1){
                id=Long.parseLong(strs[i]);
            }else{
                fileNames.add(strs[i]);
            }
        }
        log.info("同步站内消息开始");
        MyMsgVo msgVo = new MyMsgVo();
        msgVo.setMsgCode(fileNames);
        msgVo.setUserId(id);
        HttpServletResponse response=new Response();
//        mujiOpenApiFeignClient.sync(msgVo, response);
        log.info("同步站内消息结束");
        return new ProcessResult(true, "success");
    }
}