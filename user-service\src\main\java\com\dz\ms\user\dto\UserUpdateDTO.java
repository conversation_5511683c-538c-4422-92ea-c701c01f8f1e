package com.dz.ms.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 更新用户信息DTO
 * @author: Handy
 * @date:   2022/01/30 22:55
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "更新用户信息")
public class UserUpdateDTO {

    @ApiModelProperty(value = "昵称")
    private String username;
    @ApiModelProperty(value = "手机号")
    private String mobile;
    @ApiModelProperty(value = "出生日期")
    private String birthday;
    @ApiModelProperty(value = "头像")
    private String avatar;
    @ApiModelProperty(value = "性别 0未知 1男 2女")
    private Integer gender;
    @ApiModelProperty(value = "省份")
    private String province;
    @ApiModelProperty(value = "城市")
    private String city;
    @ApiModelProperty(value = "区")
    private String area;
    @ApiModelProperty(value = "详细地址")
    private String address;
    @ApiModelProperty(value = "授权隐私条款版本")
    private String policyVersion;
    @ApiModelProperty(value = "是否勾选接收品牌信息")
    private Integer isAgreeBrand;
    @ApiModelProperty(value = "同意接受条款和隐私政策")
    private Boolean agreeClauseAndPrivacy;
    @ApiModelProperty(value = "是否愿意沟通品牌")
    private Boolean agreeBrandCommunicate;
    @ApiModelProperty(value = "是否愿意沟通集团")
    private Boolean agreeCorpCommunicate;
}
