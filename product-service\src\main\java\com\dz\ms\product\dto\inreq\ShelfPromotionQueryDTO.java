package com.dz.ms.product.dto.inreq;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;


/**
 * 货架推广查询条件DTO
 *
 * @author: fei
 * @date: 2024/12/11 16:25
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@ApiModel(value = "货架推广查询条件DTO")
public class ShelfPromotionQueryDTO {
    
    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "是否抛异常")
    private Boolean isThrow;
    @ApiModelProperty(value = "是否查询货架信息")
    private Boolean isQryShelf;

}
