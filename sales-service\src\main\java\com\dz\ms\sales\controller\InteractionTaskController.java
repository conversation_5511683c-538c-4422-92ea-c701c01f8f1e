package com.dz.ms.sales.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.dto.DownloadCenterDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.dto.user.InteractionTaskDTO;
import com.dz.common.core.dto.user.InteractionTaskResultDTO;
import com.dz.common.core.fegin.basic.ReportDownloadFeignClient;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.ms.sales.config.Globals;
import com.dz.ms.sales.entity.InteractionTask;
import com.dz.ms.sales.entity.UserTaskSuccessRecord;
import com.dz.ms.sales.mapper.UserTaskSuccessRecordMapper;
import com.dz.ms.sales.service.InteractionTaskService;
import com.dz.ms.sales.vo.InteractionTaskSelectVo;
import com.dz.ms.sales.vo.InteractionTaskVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "互动任务")
@Slf4j
@RestController
public class InteractionTaskController {

    @Resource
    private InteractionTaskService interactionTaskService;
    @Resource
    private ReportDownloadFeignClient reportDownloadFeignClient;
    @Resource
    private UserTaskSuccessRecordMapper userTaskSuccessRecordMapper;

    @ApiOperation("获取任务列表")
    @PostMapping(value = "/crm/interaction/task/list")
    public Result<PageInfo<InteractionTaskDTO>> getInteractionTaskList(@RequestBody InteractionTaskSelectVo param) throws ParseException {
        Result<PageInfo<InteractionTaskDTO>> result = new Result<>();
        result.setData(interactionTaskService.getInteractionTaskList(param));
        return result;
    }

    @ApiOperation("获取任务详情")
    @GetMapping(value = "/crm/interaction/task/info")
    public Result<InteractionTaskDTO> getInteractionTaskInfo(@RequestParam("id") Long id) {
        Result<InteractionTaskDTO> result = new Result<>();
        result.setData(interactionTaskService.getInteractionTaskInfo(id));
        return result;
    }

    @ApiOperation("添加任务")
    @PostMapping(value = "/crm/interaction/task/add")
    public Result<Object> addInteractionTaskInfo(@RequestBody InteractionTaskVo param) {
        Result<Object> result = new Result<>();
        interactionTaskService.addInteractionTaskInfo(param);
        return result;
    }

    @ApiOperation("修改任务")
    @PostMapping(value = "/crm/interaction/task/udpate")
    public Result<Object> updateInteractionTaskInfo(@RequestBody InteractionTaskVo param) {
        Result<Object> result = new Result<>();
        interactionTaskService.updateInteractionTaskInfo(param,1);
        return result;
    }

    @ApiOperation("任务启用停用")
    @PostMapping(value = "/crm/interaction/task/status")
    public Result<Object> updateInteractionTaskStatus(@RequestBody InteractionTaskVo param) {
        Result<Object> result = new Result<>();
        interactionTaskService.updateInteractionTaskInfo(param,2);
        return result;
    }

    @ApiOperation("删除任务")
    @PostMapping(value = "/crm/interaction/task/delete")
    public Result<Object> deleteInteractionTaskInfo(@RequestBody InteractionTaskVo param) {
        Result<Object> result = new Result<>();
        interactionTaskService.deleteInteractionTaskInfo(param);
        return result;
    }

    @ApiOperation("获取任务列表type=1未完成2已完成3已过期")
    @GetMapping(value = "/app/interaction/task/list")
    public Result<InteractionTaskResultDTO> getInteractionTaskListApp(@RequestParam("type") Integer type) throws ParseException {
        Long userId=SecurityContext.getUser().getUid();
        InteractionTaskResultDTO resultDTO = new InteractionTaskResultDTO();
        Date now=new Date();
        Result<InteractionTaskResultDTO> result = new Result<>();
        List<InteractionTaskDTO> list =interactionTaskService.getInteractionTaskListApp(userId);
        //未完成列表
        List<InteractionTaskDTO> incompleteList=new ArrayList<>();
        //已完成列表
        List<InteractionTaskDTO> completedList=new ArrayList<>();
        //已过期列表
        List<InteractionTaskDTO> expireList=new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)){
            for (InteractionTaskDTO task : list) {
                //是否是限时任务
                if (null != task.getRestrictTimeStart() && null != task.getRestrictTimeEnd()){
                    //判断now是否在限时开始时间和限时结束时间之间
                    if (now.getTime()<=task.getRestrictTimeEnd().getTime()){
                        if (null != task.getAllReadyNum()){
                            if (null != task.getReadyNum() && null != task.getTotalReadyNum()){//线下消费这俩值不同时为null
                                if (task.getAllReadyNum().equals(task.getTotalReadyNum())){
                                    completedList.add(task);
                                }else{
                                    incompleteList.add(task);
                                }
                            }else{
                                if (null != task.getReadyNum()){
                                    if (task.getAllReadyNum().equals(task.getReadyNum())){
                                        completedList.add(task);
                                    }else{
                                        incompleteList.add(task);
                                    }
                                }else{
                                    if (task.getAllReadyNum().equals(task.getTotalReadyNum())){
                                        completedList.add(task);
                                    }else{
                                        incompleteList.add(task);
                                    }
                                }
                            }
                        }else{
                            incompleteList.add(task);
                        }
                    }else{
                        if (null != task.getAllReadyNum()){
                            if (null != task.getReadyNum() && null != task.getTotalReadyNum()){//线下消费这俩值不同时为null
                                if (task.getAllReadyNum().equals(task.getTotalReadyNum())){
                                    completedList.add(task);
                                }else{
                                    expireList.add(task);
                                }
                             }else{
                                if (null != task.getReadyNum()){
                                    if (task.getAllReadyNum().equals(task.getReadyNum())){
                                        completedList.add(task);
                                    }else{
                                        expireList.add(task);
                                    }
                                }else{
                                    if (task.getAllReadyNum().equals(task.getTotalReadyNum())){
                                        completedList.add(task);
                                    }else{
                                        expireList.add(task);
                                    }
                                }
                            }
                        }else{
                            expireList.add(task);
                        }
                    }
                }else{
                    if (null != task.getAllReadyNum()){
                        if (null != task.getReadyNum() && null != task.getTotalReadyNum()){//线下消费这俩值不同时为null
                            if (task.getAllReadyNum().equals(task.getTotalReadyNum())){
                                completedList.add(task);
                            }else{
                                incompleteList.add(task);
                            }
                         }else{
                            if (null != task.getReadyNum()){
                                if (task.getAllReadyNum().equals(task.getReadyNum())){
                                    completedList.add(task);
                                }else{
                                    incompleteList.add(task);
                                }
                            }else{
                                if (task.getAllReadyNum().equals(task.getTotalReadyNum())){
                                    completedList.add(task);
                                }else{
                                    incompleteList.add(task);
                                }
                            }
                        }
                    }else{
                        incompleteList.add(task);
                    }
                }
            }
        }
        if (type == 1){
            if (!CollectionUtils.isEmpty(completedList)){
                for (InteractionTaskDTO task : completedList){
                    LambdaQueryWrapper<UserTaskSuccessRecord> wrapperSuccess = new LambdaQueryWrapper<>();
                    wrapperSuccess.eq(UserTaskSuccessRecord::getUserId,userId);
                    wrapperSuccess.eq(UserTaskSuccessRecord::getTaskId,task.getId());
                    wrapperSuccess.eq(UserTaskSuccessRecord::getCycleNum,task.getSuccessCycleNum());
                    if (userTaskSuccessRecordMapper.selectOne(wrapperSuccess) == null){
                        UserTaskSuccessRecord userTaskSuccessRecord = new UserTaskSuccessRecord();
                        userTaskSuccessRecord.setUserId(userId);
                        userTaskSuccessRecord.setTaskId(task.getId());
                        userTaskSuccessRecord.setCycleNum(task.getSuccessCycleNum());
                        userTaskSuccessRecord.setCreateTime(now);
                        userTaskSuccessRecord.setCreateAt(String.valueOf(userId));
                        userTaskSuccessRecord.setTenantId(SecurityContext.getUser().getTenantId());
                        userTaskSuccessRecordMapper.insert(userTaskSuccessRecord);
                    }
                }
                LambdaQueryWrapper<UserTaskSuccessRecord> wrapperSuccessSel = new LambdaQueryWrapper<>();
                wrapperSuccessSel.eq(UserTaskSuccessRecord::getUserId,userId);
                wrapperSuccessSel.eq(UserTaskSuccessRecord::getIsRead,1);
                List<UserTaskSuccessRecord> userTaskSuccessRecords =userTaskSuccessRecordMapper.selectList(wrapperSuccessSel);
                if (!CollectionUtils.isEmpty(userTaskSuccessRecords)){
                    resultDTO.setShowNew(1);
                }
            }
            resultDTO.setData(incompleteList);
        }else if (type == 2){
            //将UserTaskSuccessRecord表未读数据改为已读数据
            LambdaQueryWrapper<UserTaskSuccessRecord> wrapperSuccessSel = new LambdaQueryWrapper<>();
            wrapperSuccessSel.eq(UserTaskSuccessRecord::getUserId,userId);
            wrapperSuccessSel.eq(UserTaskSuccessRecord::getIsRead,1);
            UserTaskSuccessRecord userTaskSuccessRecord=new UserTaskSuccessRecord();
            userTaskSuccessRecord.setIsRead(2);
            userTaskSuccessRecordMapper.update(userTaskSuccessRecord, wrapperSuccessSel);
            resultDTO.setData(completedList);
        }else{
            resultDTO.setData(expireList);
        }
        result.setData(resultDTO);
        return result;
    }

    /**
     * 导出任务列表
     *
     * @return
     */
    @PostMapping(value = "/interaction/export_task_list")
    public Result<Void> exportTaskList(@RequestBody DownloadAddParamDTO exportParam) {
        CurrentUserDTO user = SecurityContext.getUser();
        Globals.downloadThreadPool.execute(() -> {
            try {
                SecurityContext.setUser(user);
                interactionTaskService.exportTaskList(exportParam.getJsonParam(), exportParam.getReportCode(), exportParam.getFileName(), exportParam.getFileExt(), exportParam.getDownloadCenterId());
            } catch (Exception e) {
                log.error("导出错误", e);
                DownloadCenterDTO downloadCenterDTO = new DownloadCenterDTO();
                downloadCenterDTO.setId(exportParam.getDownloadCenterId());
                downloadCenterDTO.setErrorDesc(e.getMessage());
                downloadCenterDTO.setState(2);
                reportDownloadFeignClient.updateDownloadCenter(downloadCenterDTO);
            }
        });
        return new Result<>();
    }

    /**
     * 完成线下打卡任务奖励
     * @return
     */
    @ApiOperation("完成线下打卡任务奖励,longitude经度，latitude维度")
    @GetMapping(value = "/app/interaction/task/card")
    public Result<Object> taskSuccessRecordCard(@RequestParam("longitude") String longitude,@RequestParam("latitude") String latitude) throws ParseException {
        Result<Object> result = new Result<>();
        interactionTaskService.taskSuccessRecordCard(longitude,latitude);
        return result;
    }

    /**
     * 完成兑礼任务奖励
     * @return
     */
    @ApiOperation("完成兑礼任务奖励")
    @GetMapping(value = "/interaction/task/order")
    public Result<Object> taskSuccessRecordByOrder() throws ParseException {
        Result<Object> result = new Result<>();
        interactionTaskService.taskSuccessRecordByOrder();
        return result;
    }

    /**
     * 完成线下消费任务奖励
     * @return
     */
    @ApiOperation("完成线下消费任务奖励")
    @GetMapping(value = "/app/interaction/task/product")
    public Result<Object> taskSuccessRecordByProduct() throws ParseException {
        Result<Object> result = new Result<>();
        interactionTaskService.taskSuccessRecordByProduct();
        return result;
    }

    /**
     * 完成邀请好友任务奖励
     * @return
     */
    @ApiOperation("完成邀请好友任务奖励")
    @GetMapping(value = "/interaction/task/friend")
    public Result<Object> taskSuccessRecordFriend(@RequestParam(value = "inviteUserId") Long inviteUserId) throws ParseException {
        Result<Object> result = new Result<>();
        interactionTaskService.taskSuccessRecordFriend(inviteUserId);
        return result;
    }

    /**
     * 首购任务奖励
     * @return
     */
    @ApiOperation("完成首购任务奖励")
    @GetMapping(value = "/app/interaction/three/first/order")
    public Result<Object> threeFirstOrder() throws ParseException {
        Result<Object> result = new Result<>();
        interactionTaskService.threeFirstOrder();
        return result;
    }


    /**
     * 任务三天到期订阅消息提醒
     * @return
     */
    @PostMapping(value = "/subscription/task/expire/three")
    public Result<Object> taskExpireThree(){
        Result<Object> result = new Result<>();
        interactionTaskService.taskExpireThree();
        return result;
    }

    @GetMapping("/app/userTask/add")
    @ApiOperation("任务埋点记录")
    public Result<String> add(@RequestParam(value = "taskId") Long taskId) throws SQLException {
        CurrentUserDTO user = SecurityContext.getUser();
        Long uid = user.getUid();
        Long tenantId = user.getTenantId();
        interactionTaskService.addUserTask(taskId,uid,tenantId);
        Result<String> commonResponse = new Result<>();
        commonResponse.setMsg("添加成功");
        return commonResponse;
    }

    /**
     * 查询所有任务名称
     * @return
     */
    @GetMapping(value = "/interaction/info/by/name")
    public Result<List<String>> getInteractionTaskInfo() {
        Result<List<String>> result = new Result<>();
        List<InteractionTask> list = interactionTaskService.list();
        List<String> taskNameList= new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)){
            // 将任务名称添加到集合中并做去重
            taskNameList = list.stream().map(InteractionTask::getTaskName).distinct().collect(Collectors.toList());
        }
        result.setData(taskNameList);
        return result;
    }
}
