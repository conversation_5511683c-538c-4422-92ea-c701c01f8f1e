package com.dz.common.core.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.cglib.beans.BeanMap;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 对象属性copy工具
 */
public class BeanCopierUtils {

    public static Map<String,BeanCopier> beanCopierMap = new HashMap<String, BeanCopier>();

    /**
     * copy 对象属性
     * @param source
     * @param target
     */
    public static void copyProperties(Object source, Object target){
        if(null == source) {
            return;
        }
        BeanCopier copier = getCopier(source.getClass(),target.getClass());
        copier.copy(source, target, null);
    }

    /**
     * 对象转换
     * @param source
     * @param targetClass
     * @param <T>
     * @return
     */
    public static <T> T convertObject(Object source, Class<T> targetClass) {
        if(null == source) {
            return null;
        }
        BeanCopier copier = getCopier(source.getClass(),targetClass);
        try {
            T target = targetClass.newInstance();
            copier.copy(source, target, null);
            return target;
        } catch (Exception e) {
            throw new RuntimeException("对象转换出错",e);
        }
    }

    /**
     * 对象List转换
     * @param sourceList
     * @param targetClass
     * @param <T>
     * @return
     */
    public static <T> List<T> convertList(List<?> sourceList, Class<T> targetClass) {
        List<T> list = new ArrayList();
        if (null == sourceList || sourceList.size() < 1) {
            return list;
        }
        BeanCopier copier = getCopier(sourceList.get(0).getClass(),targetClass);
        for (Object source : sourceList) {
            try {
                T target = targetClass.newInstance();
                copier.copy(source, target, null);
                list.add(target);
            } catch (Exception e) {
                throw new RuntimeException("对象转换出错",e);
            }
        }
        return list;
    }

    private static BeanCopier getCopier(Class<?> sourceClass, Class<?> targetClass) {
        String beanKey =  generateKey(sourceClass, targetClass);
        BeanCopier copier =  null;
        if(!beanCopierMap.containsKey(beanKey)){
            copier = BeanCopier.create(sourceClass, targetClass, false);
            beanCopierMap.put(beanKey, copier);
        }else{
            copier = beanCopierMap.get(beanKey);
        }
        return copier;
    }

    /**
     * bean转Map
     * @param source
     * @return
     */
    public static Map<String,Object> beanToMap(Object source) {
        if(null == source) {
            return null;
        }
        try {
            return BeanMap.create(source);
        } catch (Exception e) {
            throw new RuntimeException("对象转换出错",e);
        }
    }

    private static String generateKey(Class<?> class1,Class<?>class2){
        return class1.toString() + class2.toString();
    }

    /**
     * 对象复制并将空字符串设为null
     * @param source
     * @param targetClass
     * @param <T>
     * @return
     */
    public static <T> T convertObjectTrim(Object source, Class<T> targetClass) {
        Class objectClass = source.getClass();
        Field[] fields = objectClass.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                Object val = field.get(source);
                String type = field.getType().toString();
                if("class java.lang.String".equals(type) && val != null && StringUtils.isBlank(val.toString())) {
                    field.set(source,null);
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return convertObject(source,targetClass);
    }

    public static void copyPropertiesTrim(Object source, Object target){
        if(null == source || null == target) {
            return;
        }
        Map<String,Object> map = new HashMap<>();
        Class objectClass = source.getClass();
        Field[] fields = objectClass.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                Object val = field.get(source);
                String type = field.getType().toString();
                if("class java.lang.String".equals(type) && val != null && StringUtils.isEmpty(val.toString())) {
                    val = null;
                }
                map.put(field.getName(),val);
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        Field[] targetFields = target.getClass().getDeclaredFields();
        for (Field field : targetFields) {
            field.setAccessible(true);
            try {
                Object val = field.get(target);
                String type = field.getType().toString();
                if(null == val || ("class java.lang.String".equals(type) && StringUtils.isEmpty(val.toString()))) {
                    field.set(target,map.get(field.getName()));
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
    }

}
