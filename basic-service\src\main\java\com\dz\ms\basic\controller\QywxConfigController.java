package com.dz.ms.basic.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.CacheEvict;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.basic.QywxConfigDTO;
import com.dz.common.core.dto.wechat.DecryptEnterpriseUserDTO;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.ms.basic.constants.CacheKeys;
import com.dz.ms.basic.entity.QywxConfig;
import com.dz.ms.basic.service.QywxConfigService;
import com.dz.ms.basic.service.TenantInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags="企业微信配置")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class QywxConfigController  {

    @Resource
    private QywxConfigService qywxConfigService;
    @Resource
    private RedisService redisService;

    /**
     * 分页查询企业微信配置
     * @param param
     * @return result<PageInfo<QywxConfigDTO>>
     */
    @ApiOperation("分页查询企业微信配置")
	@GetMapping(value = "/oms/qywx/list")
    public Result<PageInfo<QywxConfigDTO>> getQywxConfigList(@ModelAttribute QywxConfigDTO param) {
        Result<PageInfo<QywxConfigDTO>> result = new Result<>();
        QywxConfig qywxConfig = BeanCopierUtils.convertObjectTrim(param,QywxConfig.class);
        IPage<QywxConfig> page = qywxConfigService.page(new Page<>(param.getPageNum(), param.getPageSize()), new QueryWrapper<>(qywxConfig));
        result.setData(new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),BeanCopierUtils.convertList(page.getRecords(), QywxConfigDTO.class)));
        return result;
    }

    /**
     * 根据ID查询企业微信配置
     * @param id
     * @return result<QywxConfigDTO>
     */
    @ApiOperation("根据ID查询企业微信配置")
	@GetMapping(value = "/qywx/info")
    public Result<QywxConfigDTO> getQywxConfigById(@RequestParam("id") Long id) {
        Result<QywxConfigDTO> result = new Result<>();
        QywxConfig qywxConfig = qywxConfigService.getById(id);
        result.setData(BeanCopierUtils.convertObject(qywxConfig,QywxConfigDTO.class));
        return result;
    }

    /**
     * 保存企业微信配置
     * @param param
     * @return result<Long>
     */
    @ApiOperation("保存企业微信配置")
	@PostMapping(value = "/oms/qywx/save")
    public Result<Long> save(@RequestBody QywxConfigDTO param) {
        Result<Long> result = new Result<>();
        QywxConfigDTO getQywxConfig = qywxConfigService.getQywxConfigByTenantId(param.getTenantId());
        QywxConfig qywxConfig = new QywxConfig(param.getId(), param.getCorpId(), param.getAgentId(),param.getAppid(), param.getCorpSecret(), param.getToken(), param.getEncodingAesKey(), param.getType(),param.getState(),param.getTenantId());
        if(null == getQywxConfig) {
            qywxConfigService.save(qywxConfig);
        }
        else {
            qywxConfig.setId(getQywxConfig.getId());
            qywxConfigService.updateById(qywxConfig);
            redisService.del(CacheKeys.QYWX_ACCESSTOKEN+param.getTenantId()+":"+param.getType());
            redisService.del(CacheKeys.QYWX_CONFIG_BYTENANT+":"+param.getTenantId());
        }
        result.setData(qywxConfig.getId());
        return result;
    }
	
	/**
     * 根据ID删除企业微信配置
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除企业微信配置")
	@PostMapping(value = "/oms/qywx/delete")
    public Result<Boolean> deleteQywxConfigById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        qywxConfigService.removeById(param.getId());
        result.setData(true);
        return result;
    }

    /**
     * 根据租户ID获取企业微信access_token
     * @param type
     * @param tenantId
     * @param cleanCach
     * @return
     */
    @GetMapping( value = "/qywx/access_token")
    public Result<String> getQywxAccessToken(@RequestParam("type") Integer type,@RequestParam("tenantId") Long tenantId,@RequestParam(value = "cleanCach",required = false) Boolean cleanCach) {
        Result<String> result = new Result<>();
        result.setData(qywxConfigService.getQywxAccessToken(type,tenantId,cleanCach));
        return result;
    }

    @ApiOperation("根据租户ID查询企业微信配置")
    @GetMapping(value = {"/qywx/info_by_tenant","/oms/qywx/info_by_tenant"})
    public Result<QywxConfigDTO> getQywxConfigByTenantId(@RequestParam("tenantId") Long tenantId) {
        Result<QywxConfigDTO> result = new Result<>();
        QywxConfigDTO qywxConfig = qywxConfigService.getQywxConfigByTenantId(tenantId);
        result.setData(qywxConfig);
        return result;
    }

    @ApiOperation("解密企微敏感信息")
    @PostMapping(value = "/qywx/wxdata_decode")
    public Result<DecryptEnterpriseUserDTO> decryptEnterpriseUserDTO(@RequestParam("encryptedData") String encryptedData, @RequestParam("iv") String iv, @RequestParam("sessionKey") String sessionKey){
        Result<DecryptEnterpriseUserDTO> result = new Result<>();
        result.setData(qywxConfigService.decryptEnterpriseUserDTO(encryptedData, iv, sessionKey));
        return result;
    }
}
