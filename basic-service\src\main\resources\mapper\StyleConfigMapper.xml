<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.basic.mapper.StyleConfigMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    id,
  	    content,
  	    style_type,
  	    tenant_id,
  	    creator,
  	    create_time,
  	    modifier,
  	    modified
    </sql>

	<select id="selectStyleConfigList" parameterType="java.lang.Long" resultType="com.dz.ms.basic.entity.StyleConfig">
		select
		<include refid="Base_Column_List"/>
		from style_config
		where tenant_id = #{tenantId}
	</select>

</mapper>
