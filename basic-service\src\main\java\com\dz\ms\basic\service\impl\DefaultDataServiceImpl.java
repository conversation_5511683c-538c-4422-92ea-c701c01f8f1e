package com.dz.ms.basic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.annotation.Cacheable;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.constants.CacheKeys;
import com.dz.ms.basic.dto.DefaultDataDTO;
import com.dz.ms.basic.entity.DefaultData;
import com.dz.ms.basic.mapper.DefaultDataMapper;
import com.dz.ms.basic.service.DefaultDataService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * 默认数据
 * @author: Handy
 * @date:   2023/12/19 17:05
 */
@Service
public class DefaultDataServiceImpl extends ServiceImpl<DefaultDataMapper,DefaultData> implements DefaultDataService {

	@Resource
    private DefaultDataMapper defaultDataMapper;

	/**
     * 分页查询默认数据
     * @param param
     * @return PageInfo<DefaultDataDTO>
     */
    @Override
    public PageInfo<DefaultDataDTO> getDefaultDataList(DefaultDataDTO param) {
        DefaultData defaultData = BeanCopierUtils.convertObjectTrim(param,DefaultData.class);
        IPage<DefaultData> page = defaultDataMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), new QueryWrapper<>(defaultData));
        return new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),BeanCopierUtils.convertList(page.getRecords(), DefaultDataDTO.class));
    }

    /**
     * 根据ID查询默认数据
     * @param id
     * @return DefaultDataDTO
     */
    @Override
    public DefaultDataDTO getDefaultDataById(Long id) {
        DefaultData defaultData = defaultDataMapper.selectById(id);
        return BeanCopierUtils.convertObject(defaultData,DefaultDataDTO.class);
    }

    /**
     * 根据类型获取默认数据
     * @param type
     * @return
     */
    @Override
    @Cacheable(prefix = CacheKeys.DEFAULT_DATA,key = "'#type'")
    public DefaultDataDTO getDefaultDataByType(Integer type) {
        DefaultData defaultData = defaultDataMapper.selectOne(new LambdaQueryWrapper<DefaultData>().eq(DefaultData::getType,type).last("limit 1"));
        return BeanCopierUtils.convertObject(defaultData,DefaultDataDTO.class);
    }

    /**
     * 保存默认数据
     * @param param
     * @return Long
     */
    @Override
    public Long saveDefaultData(DefaultDataDTO param) {
        DefaultData defaultData = new DefaultData(param.getId(), param.getType(), param.getContent());
        if(ParamUtils.isNullOr0Long(defaultData.getId())) {
            defaultDataMapper.insert(defaultData);
        }
        else {
            defaultDataMapper.updateById(defaultData);
        }
        return defaultData.getId();
    }

    /**
     * 根据ID删除默认数据
     * @param param
     */
    @Override
    public void deleteDefaultDataById(IdCodeDTO param) {
        defaultDataMapper.deleteById(param.getId());
    }
	
}