<template>
  <layout>
    <template v-slot:header>
      <searchForm :formParams="formParams" @cancel="resetData" @ok="refreshData" :disabled="!$hasPermission('privacy:policy:search')">

        <a-form-item label="">
          <a-input placeholder="请输入条款名称" v-model:value="formParams.name" allow-clear></a-input>
        </a-form-item>
        <a-form-item name="status">
          <a-select ref="select" v-model:value="formParams.status" allowClear :options="statusOptions" :fieldNames="{ label: 'label', value: 'value' }" optionFilterProp="label" showSearch placeholder="启用状态"></a-select>
        </a-form-item>
      </searchForm>
    </template>
    <template v-slot:topRight>
      <a-space>
        <!-- :disabled="!$hasPermission('open:leaveUser:synchLeaveUser')" 权限标识 -->
        <a-button type="primary" @click="addChang" :disabled="!$hasPermission('privacy:policy:add')">{{'新建隐私条件'}}</a-button>

      </a-space>

    </template>
    <template v-slot="{ height }">
      <a-table :indentSize="20" row-key="id" :scroll="{ scrollToFirstRowOnChange: true, y: height-88, x: '100%' }" :dataSource="dataSource" :columns="tableHeader" :pagination="pagination" :loading="loading" @change="loadData">
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'index'">
            {{ (current - 1) * pageSize + 1 + index }}
          </template>

          <template v-if="column.key === 'pushStatus'">
            {{record.pushStatus=='0'?'待发布':record.pushStatus=='1'?'发布中':'已结束'}}
          </template>
          <template v-if="column.key === 'status'">
            <a-tag color="green" v-if="record.status == '0'">启用中</a-tag>
            <a-tag color="red" v-else>停用中</a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-button type="link" @click="EditRole(record)" :disabled="!$hasPermission('privacy:policy:edit')">编辑</a-button>
            <a-divider type="vertical" />
            <a-popconfirm :title="`确定要${record.status == '1'?'启用':'停用'}吗？`" @confirm="handleEnable(record)" :disabled="!$hasPermission('privacy:policy:status')">
              <a-button :disabled="!$hasPermission('privacy:policy:status')" type="link">{{record.status == '1'?'启用':'停用'}}</a-button>
            </a-popconfirm>
            <template v-if="record.status == '1'">
              <a-divider type="vertical" />
              <a-popconfirm title="是否确定删除该数据？" @confirm="handleDelete(record)" :disabled="!$hasPermission('privacy:policy:del')">
                <a-button type="link" :disabled="!$hasPermission('privacy:policy:del')">删除</a-button>
              </a-popconfirm>
            </template>

          </template>
        </template>
      </a-table>
    </template>
  </layout>
  <addPrivacy :visible="visible" @ok="updateList" @cancel="visible = false" :id="id" :type="type" />
</template>



<script setup >
import { statusOptions } from '@/utils/dict-options'
import { useRoute, useRouter } from 'vue-router'
import { usePagination, useRequest, } from 'vue-request';
import addPrivacy from './components/addPrivacy.vue'
import { privacy_policyList, privacy_policyDelete, privacy_policyUpdate } from '@/http/index.js'
import { message, Modal } from "ant-design-vue";


// 分页参数
let pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ["bottomLeft"],
  };
});
// 表格分页数据获取
const total = ref(0)
const { formParams, tableHeader, visible, id, type } = toRefs(reactive({

  id: '',
  type: 0,// 0-新增 1-编辑  2-查看
  visible: false,

  formParams: {
    name: '',

  },

  tableHeader: [
    {
      title: "序号",
      key: "index",
      align: "center",
      width: 70,
    },

    {
      title: "条款名称",
      dataIndex: "name",
      align: "center",
      width: 120,
      ellipsis: true,
    },
    {
      title: "版本编号",
      dataIndex: "policyVersion",
      align: "center",
      width: 100,
      ellipsis: true,
    },
    {
      title: "创建时间",

      dataIndex: "created",
      align: "center",
      width: 160,
    },
    ,
    {
      title: "发布时间",
      dataIndex: "pushTime",
      align: "center",
      width: 160,
    },
    {
      title: "发布状态",
      key: "pushStatus",
      //   dataIndex: "pushStatus",
      align: "center",
      width: 80,
    },

    {
      title: "启停用状态",
      dataIndex: "status",
      key: "status",
      align: "center",
      width: 100,
    },

    {
      title: "关联自定义页面",
      dataIndex: "urlSum",
      align: "center",
      width: 120,
    },
    {
      title: "操作",
      dataIndex: "action",
      key: "action",
      align: "center",
      width: 180,
      fixed: "right",
    },
  ],


})
);
const { data: dataSource, run, loading, pageSize, current, refresh } = usePagination((param) => {
  // 这里修改参数
  return privacy_policyList({ ...param, ...formParams.value })

},
  {
    manual: false,
    pagination: {
      currentKey: 'pageNum',  // 通过该值指定接口 当前页数 参数的属性值
      pageSizeKey: 'pageSize',// 通过该值指定接口 每页获取条数 参数的属性值
      totalKey: 'data.count',  // 指定 data 中 total 属性的路径
    },
    formatResult: res => { // 返回数据格式化
      // console.log("🚀 ~ res:", res)
      total.value = Number(res.data.count)
      return res.data.list
    }
  });
function EditRole(record) {
  visible.value = true
  type.value = 1
  id.value = record.id
  // console.log(record, id.value);
}
function handleEnable(record) {
  privacy_policyUpdate({ id: record.id, status: record.status == '0' ? 1 : 0 }).then(res => {
    if (res.code === 0) {
      message.success(`${record.status == '1' ? '启用' : '停用'}成功`)
      resetData()
    }
  })
}
function addChang() {
  visible.value = true
  id.value = ''
  type.value = 0
}
//删除
const handleDelete = (record) => {
  privacy_policyDelete({ id: record.id }).then(res => {
    if (res.code === 0) {
      message.success('删除成功')
      resetData()
    }
  })
}
function updateList(value) {
  visible.value = false;
  // 新增 重置搜索数据
  if (!value) {
    resetData();
  } else {
    // 查看、编辑 不需要清空搜索数据 直接刷新
    refresh();
  }
}
// 加载数据
const loadData = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}


// 刷新数据
const refreshData = () => {
  run({ pageNum: 1, pageSize: pageSize.value })
};

// 重置数据
const resetData = () => {
  formParams.value = {
    name: '',
  }
  refreshData()
}
</script>
