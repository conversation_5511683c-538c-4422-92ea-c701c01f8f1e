package com.dz.common.core.fegin.basic;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.basic.PopupConfigDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 弹窗配置
 * @author: Handy
 * @date:   2023/11/20 20:40
 */
@FeignClient(name = ServiceConstant.BASIC_SERVICE_NAME, contextId = "PopupConfigFeginClient")
public interface PopupConfigFeginClient {

    /**
     * 根据类型查询弹窗配置
     * @param type
     * @return
     */
    @GetMapping(value = "/popup_config/info_by_type")
    public Result<PopupConfigDTO> getPopupConfigByType(@RequestParam("type") Integer type);

    /**
     * 根据类型保存弹窗配置
     * @param param
     * @return
     */
    @PostMapping(value = "/popup_config/save_by_type")
    public Result<Long> savePopupConfigByType(@RequestBody PopupConfigDTO param);

}
