package com.dz.ms.adaptor.task;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class TaskExecutor {

    @Bean("ScheduledTask")
    public ThreadPoolTaskExecutor ScheduledTask() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(25);
        executor.setThreadNamePrefix("ScheduledTask-");
        executor.initialize();
        return executor;
    }
    @Bean("pointsExecutor")
    public ThreadPoolTaskExecutor pointsExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(50);
        executor.setQueueCapacity(200);
        executor.setThreadNamePrefix("pointsExecutor-");
        executor.initialize();
        return executor;
    }
}
