package com.dz.ms.basic.controller;

import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.*;
import com.dz.common.core.fegin.basic.ReportDownloadFeignClient;
import com.dz.ms.basic.service.OssService;
import com.dz.ms.basic.service.ReportDownloadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 类注释
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/2/8 15:15
 */
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
@Api(tags = "下载中心")
public class ReportDownloadController implements ReportDownloadFeignClient {

    @Resource
    private ReportDownloadService reportDownloadService;
    @Resource
    private OssService ossService;

    /**
     * ReportDownloadEnum.type
     *
     * @param downloadParamDTO
     * @return
     * @see com.dz.common.core.enums.ReportDownloadEnum
     */
    @ApiOperation("创建模板下载任务")
    @ApiImplicitParam(name = "type", value = "1、2", dataType = "Integer", required = true, paramType = "body")
    @PostMapping(value = "/crm/report/download/template")
    public Result<Void> downloadTemplate(@Parameter(hidden = true)  @RequestBody ReportDownloadParamDTO downloadParamDTO) {
        Result<Void> response = new Result<>();
        reportDownloadService.downloadTemplate(downloadParamDTO);
        return response;
    }

    @ApiOperation("创建报表下载任务")
    @PostMapping(value = {"/download/task", "/crm/report/download/task"})
    @Override
    public Result<Void> downloadTask(@RequestBody ReportDownloadParamDTO downloadParamDTO) {
        Result<Void> response = new Result<>();
        reportDownloadService.downloadTask(downloadParamDTO);
        return response;
    }

    @ApiOperation("下载任务列表")
    @PostMapping(value = {"/download/task/list", "/crm/report/download/task/list"})
    public Result<PageInfo<ReportDownloadTaskDTO>> taskList(@RequestBody ReportDownloadTaskParamDTO reportDownloadTaskParamDTO) {
        Result<PageInfo<ReportDownloadTaskDTO>> response = new Result<>();
        response.setData(reportDownloadService.taskList(reportDownloadTaskParamDTO));
        return response;
    }

    @ApiOperation("下载任务类型")
    @GetMapping(value = {"/download/task/category", "/crm/report/download/task/category"})
    public Result<List<ReportDownloadTypeDTO>> category() {
        Result<List<ReportDownloadTypeDTO>> response = new Result<>();
        response.setData(reportDownloadService.category());
        return response;
    }

    @ApiOperation("删除下载任务")
    @PostMapping(value = {"/download/task/delete", "/crm/report/download/task/delete"})
    public Result<Void> delete(@RequestBody ReportDownloadTaskDeleteParamDTO paramDTO) {
        Result<Void> response = new Result<>();
        reportDownloadService.delete(paramDTO.getId());
        return response;
    }

    @ApiOperation("标记已下载")
    @PostMapping(value = {"/download/task/mark", "/crm/report/download/task/mark"})
    public Result<Void> mark(@RequestBody ReportDownloadTaskDeleteParamDTO paramDTO) {
        Result<Void> response = new Result<>();
        reportDownloadService.mark(paramDTO.getId());
        return response;
    }

    /**
     * 下载中心作废
     *
     * @return
     */
    @Override
    @GetMapping(value = {"/download/task/scarp", "/crm/report/download/task/scarp"})
    public Result<Void> scarp() {
        Result<Void> response = new Result<>();
        reportDownloadService.scarp();
        return response;
    }

    @Override
    @GetMapping(value = "/download/task/getHeader")
    public Result<List<DownloadHeaderDTO>> getHeader(@RequestParam(value = "reportCode") String reportCode, @RequestParam(value = "tenantId") Long tenantId) {
        Result<List<DownloadHeaderDTO>> response = new Result<>();
        List<DownloadHeaderDTO> list = reportDownloadService.getHeader(reportCode, tenantId);
        response.setData(list);
        return response;
    }

    @GetMapping(value = "/download/task/updateHeader")
    public Result<Object> updateHeader(@RequestParam(value = "id") Long id,
                                       @RequestParam(value = "errorMessage", required = false) String errorMessage,
                                       @RequestParam(value = "status") Integer status,
                                       @RequestParam(value = "url", required = false) String url) {
        Result<Object> response = new Result<>();
        reportDownloadService.updateHeader(id, errorMessage, status, url);
        return response;
    }

    /**
     * OSS文件方式上传
     *
     * @param ossDTO oss上传参数
     * @return String
     */
    @Override
    @PostMapping(value = "/download/uploadOss")
    public String uploadOss(OssDTO ossDTO) {
        return ossService.uploadOss(ossDTO);
    }

    /**
     * OSS文件删除
     *
     * @param url oss文件路径
     */
    @Override
    @DeleteMapping(value = "/download/delOSS")
    public void delOSS(String url) {
        ossService.delOSS(url);
    }

    @Override
    @PostMapping(value = "/download/updateDownloadCenter")
    public void updateDownloadCenter(@RequestBody DownloadCenterDTO downloadCenterDTO) {
        reportDownloadService.updateDownloadCenter(downloadCenterDTO);
    }
}
