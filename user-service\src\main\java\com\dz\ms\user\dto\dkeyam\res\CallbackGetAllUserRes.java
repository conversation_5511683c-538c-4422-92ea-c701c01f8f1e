package com.dz.ms.user.dto.dkeyam.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 同步所有用户的信息出参
 * @author: fei
 * @date: 2025/01/22 15:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CallbackGetAllUserRes implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("用户通过认证系统所使用的登陆名称")
    private String loginName;

    @ApiModelProperty("姓名")
    private String personalName;

    @ApiModelProperty("角色")
    private String role;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("用户是否启用，默认true：启用")
    private Boolean enabled;

    @ApiModelProperty("用户启用的时间戳，Unix timestamp, 单位：毫秒。如果enabled的值已经填写，请务必填写该项返回值")
    private Long startTime;

    @ApiModelProperty("用户启用的时间戳，Unix timestamp, 单位：毫秒。如果enabled的值已经填写，请务必填写该项返回值")
    private Long endTime;
}
