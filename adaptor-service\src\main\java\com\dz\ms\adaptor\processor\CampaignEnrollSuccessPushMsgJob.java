package com.dz.ms.adaptor.processor;


import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.fegin.sales.CampaignEnrollFeignClient;
import com.dz.common.core.threadlocal.SecurityContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/7
 */
@Slf4j
@Component
public class CampaignEnrollSuccessPushMsgJob implements BasicProcessor {

    @Resource
    private CampaignEnrollFeignClient campaignEnrollFeignClient;


    @Override
    public ProcessResult process(TaskContext taskContext) throws Exception {
        String jobParams = taskContext.getJobParams();
        SecurityContext.setUser(new CurrentUserDTO(1L));
        campaignEnrollFeignClient.enrollSuccessPushMsgJob(jobParams);
        log.info("活动报名成功后推送订阅消息执行完成");
        return new ProcessResult(true, "success");
    }
}
