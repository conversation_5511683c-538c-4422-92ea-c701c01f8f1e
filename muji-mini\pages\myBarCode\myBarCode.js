// pages/myBarCode/myBarCode.js
import {
  CODE128
} from "wxapp-barcode";
Page({

  /**
   * 页面的初始数据
   */
  data: {
    code: '',
    barcodeImg: null,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const {
      code
    } = options;
    this.setData({
      code,
    })
    this.drawRotatedCanvas()

  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // this.drawRotatedCanvas();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow(options) {


  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  drawRotatedCanvas() {
    const {
      code
    } = this.data
    CODE128('#memberCode', code, {
      canvasType: '2d', //  这里是必填属性，用于声明这是canvas 2D组件
      width: 603, // 声明canvas画布宽度，务必和元素css宽度一致，单位为rpx
      height: 147
    });
    const query = wx.createSelectorQuery()
    query.select('#memberCode')
      .fields({
        node: true,
        size: true
      })
      .exec((res) => {
        const canvas = res[0].node
        // 设置canvas大小
        let time = setTimeout(() => {
          clearTimeout(time)
          time = null
          wx.canvasToTempFilePath({
            canvas: canvas,
            width: 603,
            height: 147,
            fileType: 'png',
            type: '2D',
            quality: 4, // 设置最高质量
            // canvasId: 'memberCode',
            success: (v) => {
              this.setData({
                barcodeImg: v.tempFilePath
              })
            },
            fail: (err) => {
              console.error(err)
            }
          })
        }, 100)
      })
  },
  goBack() {
    wx.$mp.navigateBack();
  }
})
