import Cookies from 'js-cookie'
const TokenKey = 'MUJI-Token'
const CodeKey = 'MUJI-tenant-Code '
import theme from '@/assets/theme.js'
export function getToken() {
  return Cookies.get(TokenKey) || ''
}

export function setToken(token) {
  return Cookies.set(Token<PERSON>ey, token, { expires: theme.CookiesTime })
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}



export function getCode() {
  return Cookies.get(CodeKey) || ''
}
export function setCode(code) {
  return Cookies.set(<PERSON><PERSON><PERSON>, code, { expires: theme.CookiesTime })
}

export function removeCode() {
  return Cookies.remove(CodeKey)
}

