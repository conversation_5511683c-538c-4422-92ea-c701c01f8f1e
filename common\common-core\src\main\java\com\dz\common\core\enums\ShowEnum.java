package com.dz.common.core.enums;

import java.util.Objects;

/**
 * Describe：是否显示类型枚举
 * Created by 吴蜀黍 on 2023/8/20 8:36
 **/
public enum ShowEnum {

    NOT_SHOW(0, "未显示"),
    IS_SHOW(1, "已显示"),
    ;

    private final Integer code;
    private final String value;

    ShowEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ShowEnum resultEnum : ShowEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum.value;
            }
        }
        return null;
    }
}
