package com.dz.ms.user.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * 系统用户可用门店
 * @author: Handy
 * @date:   2023/05/15 21:43
 */
@Getter
@Setter
@NoArgsConstructor
@Table("系统用户可用门店")
@TableName(value = "sys_users_store")
public class SysUsersStore implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "系统用户ID",isIndex = true)
    private Long uid;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "角色ID",isIndex = true)
    private Long storeId;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "租户ID")
    private Long tenantId;

    public SysUsersStore(Long uid, Long storeId) {
        this.uid = uid;
        this.storeId = storeId;
    }

}
