<view class="card-shop" wx:if="{{list.length>0}}">
  <view class="card-shop-item" wx:for="{{list}}">
    <view class="card-shop-li">
      <view class="card-shop-name">兑换商品</view>
      <view class="card-shop-num">{{item.productName}}</view>
    </view>
    <view class="card-shop-li" >
      <view class="card-shop-name">数量</view>
      <view class="card-shop-num" wx:if="{{item.quantity}}">x{{item.quantity}}</view>
    </view>
    <view class="card-shop-li">
      <view class="card-shop-name">积分</view>
      <view class="card-shop-num"  wx:if="{{item.points}}">{{item.points}}</view>
    </view>
    <view class="card-shop-line" wx:if="{{index < list.length - 1}}"></view>
  </view>

</view>
