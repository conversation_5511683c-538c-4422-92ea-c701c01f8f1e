package com.dz.ms.sales.service;


import com.dz.common.base.dto.BaseDTO;
import com.dz.common.base.vo.PageInfo;
import com.dz.ms.sales.dto.CampaignEnrollDTO;
import com.dz.ms.sales.dto.CampaignEnrollParamDTO;
import com.dz.ms.sales.dto.CampaignEnrollRosterListDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/19
 */
public interface CampaignEnrollService {

    List<CampaignEnrollRosterListDTO> getCampaignEnrollPage();

    /**
     * 获取报名信息详情
     *
     * @param campaignCode
     * @return
     */
    CampaignEnrollDTO getCampaignEnrollInfo(String campaignCode);

    /**
     * 填写报名信息
     *
     * @param param
     * @return
     */
    Long addCampaignEnroll(CampaignEnrollParamDTO param);

    /**
     * 根据渠道筛选报名信息
     *
     * @return
     */
    Boolean siftCampaignEnrollByChannel(String campaignCode, Map<String, Map<String, Integer>> jobParams);

    /**
     * 报名成功推送消息
     *
     * @return
     */
    void enrollSuccessPushMsgJob(String campaignCode);

    void enrollFailPushMsgJob(String campaignCode);
}
