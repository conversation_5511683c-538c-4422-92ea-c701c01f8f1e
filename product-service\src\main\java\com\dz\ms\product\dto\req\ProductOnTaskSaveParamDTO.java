package com.dz.ms.product.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 货架商品库存任务DTO
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:25
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "货架商品库存任务")
public class ProductOnTaskSaveParamDTO {

    @ApiModelProperty(value = "商品任务ID")
    private Long id;
    @ApiModelProperty(value = "货架商品ID 因货架可以同时多次添加同一个商品，分开管理库存任务")
    private Long shelfProductId;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "货架名称")
    private String shelfName;
    @ApiModelProperty(value = "商品ID")
    private Long productId;
    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "上架类型 1实时 2单次时间 3周期")
    private Integer onType;
    @ApiModelProperty(value = "上架类型为单次时间时时间点")
    private Date onTime;
    @ApiModelProperty(value = "上架类型为周期时周期天数")
    private Integer onDays;
    @ApiModelProperty(value = "上架库存数量 正+ 负-")
    private Integer onInventory;
}
