package com.dz.ms.user.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 会员权益DTO
 * @author: Handy
 * @date:   2023/08/08 01:14
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "会员权益")
public class BenefitInfoDTO extends BaseDTO {

    @ApiModelProperty(value = "权益ID")
    private Long id;
    @ApiModelProperty(value = "权益名称")
    private String benefitName;
    @ApiModelProperty(value = "权益背景图")
    private String benefitImg;
    @ApiModelProperty(value = "未激活样式图片")
    private String unActivateImg;
    @ApiModelProperty(value = "激活样式图片")
    private String activateImg;
    @ApiModelProperty(value = "弹窗图片")
    private String popupImg;
    @ApiModelProperty(value = "权益简介")
    private String details;
    @ApiModelProperty(value = "权益排序")
    private Integer sort;
    @ApiModelProperty(value = "跳转链接配置JSON")
    private String jumpLink;
    @ApiModelProperty(value = "权益状态 0停用 1启用")
    private Integer state;
    @ApiModelProperty(value = "埋点")
    private String code;

}
