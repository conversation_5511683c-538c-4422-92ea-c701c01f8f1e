package com.dz.ms.adaptor.processor;

import com.dz.common.core.fegin.sales.InteractionTaskFeginClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import javax.annotation.Resource;
import java.text.ParseException;

/**
 * 任务三天到期订阅消息提醒
 */
@Slf4j
@Component
public class TaskExpiredJob implements BasicProcessor {

    @Resource
    private InteractionTaskFeginClient interactionTaskFeginClient;
    @Override
    public ProcessResult process(TaskContext context) {
        log.info("任务三天到期订阅消息提醒开始");
        interactionTaskFeginClient.taskExpireThree();
        log.info("任务三天到期订阅消息提醒结束");
        return new ProcessResult(true, "success");
    }
}