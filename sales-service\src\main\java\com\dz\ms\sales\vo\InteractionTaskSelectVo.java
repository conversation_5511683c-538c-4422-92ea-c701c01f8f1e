package com.dz.ms.sales.vo;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 任务新增
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InteractionTaskSelectVo extends BaseDTO
{
    @ApiModelProperty(value = "任务名称")
    private String taskName;
    @ApiModelProperty(value = "任务状态 0待开始，1进行中，2已结束")
    private Integer taskStatus;
    @ApiModelProperty(value = "任务展示开始时间")
    private Date showTimeStart;
    @ApiModelProperty(value = "任务展示结束时间")
    private Date showTimeEnd;
    @ApiModelProperty(value = "任务类型：1限时，2购物，3互动")
    private Integer taskType;
}
