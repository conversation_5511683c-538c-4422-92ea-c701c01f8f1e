package com.dz.ms.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.constant.ClientTypeConstant;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.utils.MD5;
import com.dz.common.core.constants.CommonConstants;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.IpAdrressUtil;
import com.dz.common.core.utils.ParamUtils;
import com.dz.common.core.utils.RandomUtils;
import com.dz.ms.user.constants.CacheKeys;
import com.dz.ms.user.dto.AccountLoginDTO;
import com.dz.ms.user.dto.OmsUserDTO;
import com.dz.ms.user.dto.UpdatePasswordDTO;
import com.dz.ms.user.dto.UserRoleDTO;
import com.dz.ms.user.entity.OmsUser;
import com.dz.ms.user.entity.OmsUsersRole;
import com.dz.ms.user.mapper.OmsUserMapper;
import com.dz.ms.user.mapper.OmsUsersRoleMapper;
import com.dz.ms.user.service.OmsRoleService;
import com.dz.ms.user.service.OmsUserService;
import com.dz.ms.user.utils.BCryptPasswordEncoder;
import com.dz.ms.user.utils.JwtTokenUtils;
import com.dz.ms.user.utils.PasswordUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * OMS-系统用户信息
 * @author: Handy
 * @date:   2020/01/29 19:15
 */
@Slf4j
@Service
public class OmsUserServiceImpl extends ServiceImpl<OmsUserMapper,OmsUser> implements OmsUserService {

    @Resource
    private OmsUserMapper omsUserMapper;
    @Resource
    private OmsUsersRoleMapper omsUsersRoleMapper;
    @Resource
    private OmsRoleService omsRoleService;
    @Resource
    private RedisService redisService;

    /**
     * 账号密码登录
     * @param param
     * @return
     */
    @Override
    public String passwordLogin(AccountLoginDTO param) {
        OmsUser omsUser = omsUserMapper.getUserByUsername(param.getUsername());
        if(null == omsUser) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"账号不存在");
        }
        String encode = MD5.encode(param.getPassword()+omsUser.getSalt());
        if(!new BCryptPasswordEncoder().matches(encode,omsUser.getPassword())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"用户名密码错误");
        }
        String uuid = UUID.randomUUID().toString();
        Map<String, Object> claims = new HashMap<>();
        claims.put("id",omsUser.getId());
        claims.put("type", ClientTypeConstant.OMS);
        claims.put("pt", param.getPlatform());
        String token = JwtTokenUtils.generatorToken(claims,uuid,2);
        redisService.setString(CacheKeys.OMSUSER_SECRET+omsUser.getId(),uuid, CommonConstants.WEEK_SECONDS);
        return token;
    }

    /**
     * 用户密码确认并发送短信验证码
     * @param param
     * @return
     */
    @Override
    public boolean passwordCheck(AccountLoginDTO param) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String logmsg = "OMS用户密码确认并发送短信验证码,ip:"+ IpAdrressUtil.getIpAdrress(request)+",tenantId:"+param.getTenantId()+",username:"+param.getUsername()+",";
        log.info(logmsg);
        String imageCode = redisService.getString(CacheKeys.IMAGE_CODE + param.getCodeKey());
        if (!param.getImageCode().equalsIgnoreCase(imageCode)) {
            log.info(logmsg+"验证码错误");
            throw new BusinessException("验证码错误");
        }
        redisService.del(CacheKeys.IMAGE_CODE + param.getCodeKey());
        OmsUser omsUser = omsUserMapper.getUserByUsername(param.getUsername());
        if(null == omsUser) {
            log.info(logmsg+"账号不存在");
            throw new BusinessException(ErrorCode.BAD_REQUEST,"账号不存在");
        }
        String password = PasswordUtils.decrypt(param.getPassword());
        String encode = MD5.encode(password+omsUser.getSalt());
        if(!new BCryptPasswordEncoder().matches(encode,omsUser.getPassword())) {
            log.info(logmsg+"用户名或密码错误");
            throw new BusinessException(ErrorCode.BAD_REQUEST,"用户名或密码错误");
        }
        if(omsUser.getState().equals(0)) {
            log.info(logmsg+"该账号已停用");
            throw new BusinessException(ErrorCode.BAD_REQUEST,"该账号已停用");
        }
        if(StringUtils.isBlank(omsUser.getMobile())) {
            log.info(logmsg+"该账号未绑定手机号码");
            throw new BusinessException(ErrorCode.BAD_REQUEST,"该账号未绑定手机号码，请联系管理员绑定");
        }
        if(null != omsUser.getIsOnline() && omsUser.getIsOnline().equals(2)) {
            log.info(logmsg+"首次登录需要修改密码");
            return true;
        }
        String laskKey = CacheKeys.SMS_CODE + ClientTypeConstant.OMS + ":last:" + omsUser.getMobile();
        String lastCode = redisService.getString(laskKey);
        if(StringUtils.isNotBlank(lastCode)) {
            log.info(logmsg+"验证码发送频率过快");
            throw new BusinessException(ErrorCode.BAD_REQUEST,"验证码发送频率过快，请稍后再发");
        }
        String countKey = CacheKeys.SMS_CODE + ClientTypeConstant.OMS + ":count:" + omsUser.getMobile();
        String codeStr = redisService.getString(countKey);
        int count = NumberUtils.toInt(codeStr);
        if(count >= 15) {
            log.info(logmsg+"发送验证码次数超过限制");
            throw new BusinessException(ErrorCode.BAD_REQUEST,"发送验证码次数超过限制");
        }
        count ++;
        String smsCode = RandomUtils.genNumberCode(6);
        redisService.setString(laskKey, smsCode, 60);
        redisService.setString(CacheKeys.SMS_CODE + ClientTypeConstant.OMS + ":code:" + omsUser.getMobile(), smsCode, 300);
        redisService.set(countKey, count, CommonConstants.DAY_SECONDS);
        /** 发送短信验证码 */
        log.info("{}发送短信验证码:{}",omsUser.getMobile(),smsCode);
        /*Result<Boolean> result = smsFeginClient.sendSmsMessage(omsUser.getMobile(), "登录验证码："+smsCode,0L);
        if(!result.isSuccess()) {
            log.info(logmsg+"发送短信验证码失败");
            throw new BusinessException(ErrorCode.BAD_REQUEST,"发送短信验证码失败请稍后再试");
        }*/
        log.info(logmsg+"发送成功");
        return false;
    }

    /**
     * 密码加短信验证码登录
     * @param param
     * @return
     */
    @Override
    public OmsUserDTO passwordSmsLogin(AccountLoginDTO param) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String logmsg = "OMS密码短信验证码登录,ip:"+ IpAdrressUtil.getIpAdrress(request)+",tenantId:"+param.getTenantId()+",username:"+param.getUsername()+",";
        log.info(logmsg);
        OmsUser omsUser = omsUserMapper.getUserByUsername(param.getUsername());
        if(null == omsUser) {
            log.info(logmsg+"账号不存在");
            throw new BusinessException(ErrorCode.BAD_REQUEST,"账号不存在");
        }
        String password = PasswordUtils.decrypt(param.getPassword());
        String encode = MD5.encode(password+omsUser.getSalt());
        if(!new BCryptPasswordEncoder().matches(encode,omsUser.getPassword())) {
            log.info(logmsg+"用户名或密码错误");
            throw new BusinessException(ErrorCode.BAD_REQUEST,"用户名或密码错误");
        }
        String inputKey = CacheKeys.SMS_CODE + ClientTypeConstant.OMS + ":input:" + omsUser.getMobile();
        String countStr = redisService.getString(inputKey);
        int count = NumberUtils.toInt(countStr);
        if(count >= 5) {
            log.info(logmsg+"短信验证码输入次数超限{}",count);
            throw new BusinessException(ErrorCode.BAD_REQUEST,"短信验证码输入次数超限，请重新登录");
        }
        count ++;
        String smsCode = redisService.getString(CacheKeys.SMS_CODE + ClientTypeConstant.OMS + ":code:" + omsUser.getMobile());
        if(null == smsCode) {
            log.info(logmsg+"短信验证码已过期");
            throw new BusinessException(ErrorCode.BAD_REQUEST,"短信验证码已过期");
        }
        if(!smsCode.equals(param.getSmsCode())) {
            redisService.set(inputKey,count,300);
            log.info(logmsg+"短信验证码错误");
            throw new BusinessException(ErrorCode.BAD_REQUEST,"短信验证码错误");
        }
        redisService.del(inputKey);
        Long roleId = 0L;
        if(ParamUtils.Integer2int(omsUser.getIsAdmin()) != 1) {
            roleId = getUserRole(omsUser.getId());
        }
        String uuid = UUID.randomUUID().toString();
        Map<String, Object> claims = new HashMap<>();
        claims.put("id",omsUser.getId());
        claims.put("type", ClientTypeConstant.OMS);
        claims.put("role", roleId);
        String token = JwtTokenUtils.generatorToken(claims,uuid,24);
        redisService.set(CacheKeys.OMSUSER_SECRET + omsUser.getId(),uuid, CommonConstants.DAY_SECONDS);
        OmsUserDTO omsUserDTO = BeanCopierUtils.convertObject(omsUser,OmsUserDTO.class);
        redisService.set(CacheKeys.OMS_USER_INFO + omsUser.getId(),omsUserDTO, CommonConstants.DAY_SECONDS);
        List<String> list = omsRoleService.getRolePermitCodes(roleId);
        omsUserDTO.setRoleId(roleId);
        omsUserDTO.setPermits(list);
        omsUserDTO.setToken(token);
        omsUser.setIsOnline(1);
        SecurityContext.setUser(new CurrentUserDTO(ClientTypeConstant.OMS,omsUser.getId(),0L,null));
        omsUserMapper.updateById(omsUser);
        log.info(logmsg+"登录成功");
        return omsUserDTO;
    }

    /**
     * 获取用户角色ID
     * @param uid
     * @return
     */
    @Override
    public Long getUserRole(Long uid) {
        String key = CacheKeys.OMS_USER_ROLE+uid;
        String id = redisService.getString(key);
        if(null != id) {
            return NumberUtils.toLong(id);
        }
        LambdaQueryWrapper<OmsUsersRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OmsUsersRole :: getUid,uid);
        OmsUsersRole omsUsersRole = omsUsersRoleMapper.selectOne(wrapper);
        if(null == omsUsersRole) {
            return null;
        }
        redisService.set(key,omsUsersRole.getRoleId(),CommonConstants.WEEK_SECONDS);
        return omsUsersRole.getRoleId();
    }

    /**
     * 绑定用户角色
     * @param param
     * @param uid
     */
    @Override
    public void bindRole(UserRoleDTO param, Long uid) {
        LambdaQueryWrapper<OmsUsersRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OmsUsersRole :: getUid,param.getUserId());
        OmsUsersRole omsUsersRole = omsUsersRoleMapper.selectOne(wrapper);
        if(null == omsUsersRole || null == omsUsersRole.getId()) {
            omsUsersRole = new OmsUsersRole();
            omsUsersRole.setUid(param.getUserId());
            omsUsersRole.setRoleId(param.getRoleId());
            omsUsersRoleMapper.insert(omsUsersRole);
        }
        else if(!omsUsersRole.getRoleId().equals(param.getRoleId())) {
            OmsUsersRole updateOmsUsersRole = new OmsUsersRole();
            updateOmsUsersRole.setId(omsUsersRole.getId());
            updateOmsUsersRole.setRoleId(param.getRoleId());
            updateOmsUsersRole.setModifier(uid);
            omsUsersRoleMapper.updateById(updateOmsUsersRole);
            redisService.del(CacheKeys.OMS_USER_ROLE+param.getUserId());
        }
    }

    /**
     * 修改密码
     * @param param
     */
    @Override
    public void passwordUpdate(UpdatePasswordDTO param) {
        OmsUser omsUser = omsUserMapper.getUserByUsername(param.getUsername());
        if(null == omsUser) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"账号不存在");
        }
        String salt = omsUser.getSalt();
        String password = PasswordUtils.decrypt(param.getOldPassword());
        String encode = MD5.encode(password+salt);
        if(!new BCryptPasswordEncoder().matches(encode,omsUser.getPassword())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST,"原密码错误");
        }
        password = PasswordUtils.decrypt(param.getNewPassword());
        encode = MD5.encode(password+salt);
        encode = new BCryptPasswordEncoder().encode(encode);
        omsUser.setPassword(encode);
        omsUser.setIsOnline(1);
        omsUserMapper.updateById(omsUser);
    }

    public static void main(String[] args) {
        String encode = MD5.encode("1234561b7fc2fd5e724a72bd4c988153e26b4e");

    }
}
