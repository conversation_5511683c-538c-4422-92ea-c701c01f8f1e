package com.dz.ms.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.basic.dto.MpMsgSceneRelationDTO;
import com.dz.ms.basic.dto.MpMsgSubscribeDTO;
import com.dz.ms.basic.entity.MpMsgSceneRelation;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 小程序订阅消息关联场景Mapper
 * @author: Handy
 * @date:   2023/07/07 14:22
 */
@Repository
public interface MpMsgSceneRelationMapper extends BaseMapper<MpMsgSceneRelation> {

    /**
     * 根据场景编号列表获取场景关联订阅消息模板列表
     * @param sceneList
     * @return
     */
    List<MpMsgSceneRelationDTO> selectSceneRelationBySceneList(@Param("sceneList") List<String> sceneList);

    /**
     * 根据场景获取小程序订阅消息模板列表
     * @param scene
     * @return
     */
    List<MpMsgSubscribeDTO> selectSubscribeMsgIdsByScene(@Param("scene")String scene);

}
