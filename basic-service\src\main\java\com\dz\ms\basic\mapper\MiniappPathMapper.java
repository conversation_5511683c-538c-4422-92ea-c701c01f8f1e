package com.dz.ms.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.basic.entity.MiniappPath;
import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.stereotype.Repository;

/**
 * 页面路径配置
 * @author: Handy
 * @date:   2022/08/05 17:38
 */
@Repository
public interface MiniappPathMapper extends BaseMapper<MiniappPath> {

    MiniappPath selectByPageType(@Param("pageType") Integer pageType);
}
