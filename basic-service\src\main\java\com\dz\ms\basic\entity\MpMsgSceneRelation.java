package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 小程序订阅消息关联场景
 * @author: Handy
 * @date:   2023/07/06 18:05
 */
@Getter
@Setter
@NoArgsConstructor
@Table("小程序订阅消息关联场景")
@TableName(value = "mp_msg_scene_relation")
public class MpMsgSceneRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "消息场景关系ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR,length = 20,isNull = true,comment = "场景编号",isIndex = true)
    private String scene;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "消息配置ID")
    private Long msgId;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;

    public MpMsgSceneRelation(Long id, String scene, Long msgId) {
        this.id = id;
        this.scene = scene;
        this.msgId = msgId;
    }
}
