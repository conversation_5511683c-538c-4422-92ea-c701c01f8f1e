package com.dz.common.core.fegin.product;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.product.CpStaticDTO;
import com.dz.common.core.dto.product.OdsItemDTO;
import com.dz.common.core.dto.product.ProductCouponDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


@FeignClient(name = ServiceConstant.PRODUCT_SERVICE_NAME, contextId = "ProductFeignClient")
public interface ProductFeignClient {

    /**
     * 导出商品列表
     *
     * @return
     */
    @PostMapping(value = "/product/export_product_list")
    Result<Void> exportProductList(@RequestBody DownloadAddParamDTO exportParam);

    /**
     * 根据人群包id,更新货架,货架营销活动关联人群包id为null
     * @param param 入参id:人群包id
     * @return Result<Boolean>
     */
    @PostMapping(value = "/shelf/upd_groupId")
    Result<Boolean> updGroupIdIntoNull(@RequestBody IdCodeDTO param);

    /**
     * 查询所有电子券商品
     */
    @GetMapping(value = "/product/by/type")
    Result<List<ProductCouponDTO>> getProductListByPdType();

    @GetMapping(value = "/product/sftp_upload")
    void uploadMujiGoodsAndCouponsAndOrdersExcel(@RequestParam(value = "afterDate", required = false) String afterDate);

    /**
     * 导出CP兑换统计
     */
    @PostMapping(value = "/product/export_cp_static")
    Result<List<CpStaticDTO>> exportCpStatic(@RequestBody DownloadAddParamDTO param);

    /**
     * sftp拉商品
     */
    @PostMapping(value = "/product/getSftpFile")
    public Result<String> getSftpFile();

    /**
     * 查询商品详情
     */
    @GetMapping(value = "/product/by/item/str")
    public Result<List<OdsItemDTO>> selectBySftpProductItemId(@RequestParam(value = "itemId") String itemId);

}

