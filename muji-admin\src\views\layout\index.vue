<template>
  <a-spin :spinning="global.loading">
    <div class="container">
      <!--  这里是侧边栏  ,  hasTwoLevel ?  'has-secondary' : 'no-secondary'-->
      <div class="aside" :class="[collapsedType ? 'sider-shouqi' : 'sider-zhankai']">
        <LeftMenu ref="leftmenu" @widthChange="widthChange" @menuInfo="menuInfo" @hasSecondChange="hasSecondChange">
        </LeftMenu>
      </div>
      <div class="right">
        <!-- 右侧顶部区域 -->
        <div class="header">
          <!-- {{showTwoString}} -->
          <NavTop ref="addNavTopRef"></NavTop>
        </div>
        <!-- 内容区域 -->
        <div class="content">
          <a-spin :spinning="global.contentLoading" style="height:100%">
            <router-view></router-view>
          </a-spin>
        </div>
      </div>
    </div>
  </a-spin>
</template>

<script setup>

import { ref } from 'vue'
import NavTop from '@/components/NavTop/index.vue'
import LeftMenu from '@/components/LeftMenu/index.vue'

import { useGlobalStore } from '@/store'
import { reactive, toRefs, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router'
const global = useGlobalStore()
const $router = useRouter()
const { query } = useRoute()


const { collapsedType, hasTwoLevel } = toRefs(reactive({
  collapsedType: false,
  hasTwoLevel: true, //是否有二级菜单
}));

let menuCur = ref({})
let firstIndex = ref(null)

let secondIndex = ref(null)



let threeIndex = ref(null)
//  展开侧边栏
let widthChange = (val) => {
  console.log('0000')
  collapsedType.value = val;


}
const hasTwoPack = ref('')
let hasSecondChange = (val) => {
  hasTwoLevel.value = val

  console.log(hasTwoPack.value, 'hasTwoPack.value')
}

const showTwoString = computed(() => {
  // 
  if (hasTwoLevel.value && collapsedType.value) {
    return "shouqi-hastwo";
  } else if (hasTwoLevel.value && !collapsedType.value) {
    return "zhankai-hastwo";
  } else if (!hasTwoLevel.value && collapsedType.value) {
    return "shouqi-notwo";
  } else {
    return "zhankai-notwo";
  }
});

let menuInfo = (item, index1, index2, index3) => {
  // console.log('9090')
  console.log(item, index1, index2, index3)

  menuCur.value = item

  firstIndex.value = index1

  firstIndex.value = index2


  firstIndex.value = index3


}


// setTimeout(() => {
//   console.log('定时')
//   // global.setLoading(true) // 全局
//   // global.setContentLoading(true) // 内容
// }, 4000);

</script>

<style scoped lass="scss">
.container {
  height: 100vh;
  width: 100vw;
  min-width: 1280px;
  display: flex;
  overflow: hidden;
}

.aside {
  height: 100vh;
  width: 280px;
  flex-shrink: 0;
  /* background: blue; */
  /* overflow-y: scroll; */
}

.right {
  flex: 1;
  height: 100vh;
  overflow: hidden;
  box-sizing: border-box;

  background: #f8f8f8;
  display: flex;
  flex-direction: column;
}

.header {
  /* height: 56px; */
  flex-shrink: 0;
  /* margin-bottom: 12px; */
}

.content {
  padding: 12px 24px 24px 24px;
  flex: 1;
  overflow: hidden;
}

:deep(.ant-spin-nested-loading) {
  height: 100%;
}

:deep(.ant-spin-container) {
  height: 100%;
}

.sider-zhankai {
  width: 400px !important;
  height: 100%;
  min-width: 400px !important;
  max-width: 400px !important;
  flex: 0 0 400px !important;
}

.sider-shouqi {
  width: 280px !important;
  height: 100%;
  min-width: 280px !important;
  max-width: 280px !important;
  flex: 0 0 280px !important;
}

.no-secondary {
  width: 280px !important;
  height: 100%;
  min-width: 280px !important;
  max-width: 280px !important;
  flex: 0 0 280px !important;
}
</style>
