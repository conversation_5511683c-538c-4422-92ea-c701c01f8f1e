package com.dz.ms.user.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.CacheEvict;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.ms.user.constants.CacheKeys;
import com.dz.ms.user.dto.OmsPermissionDTO;
import com.dz.ms.user.entity.OmsPermission;
import com.dz.ms.user.service.OmsPermissionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags="OMS-权限功能")
@RestController
@RequestMapping(value="/oms")
public class OmsPermissionController {

    @Resource
    private OmsPermissionService omsPermissionService;

    /**
     * 分页查询OMS-权限功能
     * @param param
     * @return result<PageInfo<OmsPermissionDTO>>
     */
    @ApiOperation("分页查询OMS-权限功能")
    @GetMapping(value = "/permission/list")
    public Result<PageInfo<OmsPermissionDTO>> getOmsPermissionList(@ModelAttribute OmsPermissionDTO param) {
        Result<PageInfo<OmsPermissionDTO>> result = new Result<>();
        OmsPermission omsPermission = BeanCopierUtils.convertObjectTrim(param,OmsPermission.class);
        IPage<OmsPermission> page = omsPermissionService.page(new Page<>(param.getPageNum(), param.getPageSize()), new LambdaQueryWrapper<>(omsPermission));
        result.setData(new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),BeanCopierUtils.convertList(page.getRecords(),OmsPermissionDTO.class)));
        return result;
    }

    @ApiOperation("查询OMS权限功能不分页")
    @GetMapping(value = "/permission/list_nopage")
    public Result<List<OmsPermissionDTO>> getOmsPermissionListNopage(@ModelAttribute OmsPermissionDTO param) {
        Result<List<OmsPermissionDTO>> result = new Result<>();
        OmsPermission omsPermission = BeanCopierUtils.convertObjectTrim(param,OmsPermission.class);
        List<OmsPermission> list = omsPermissionService.list(new LambdaQueryWrapper<>(omsPermission));
        result.setData(BeanCopierUtils.convertList(list,OmsPermissionDTO.class));
        return result;
    }

    /**
     * 根据ID查询OMS-权限功能
     * @param id
     * @return result<OmsPermissionDTO>
     */
    @ApiOperation("根据ID查询OMS-权限功能")
    @GetMapping(value = "/permission/info")
    public Result<OmsPermissionDTO> getOmsPermissionById(@RequestParam("id") Long id) {
        Result<OmsPermissionDTO> result = new Result<>();
        OmsPermission omsPermission = omsPermissionService.getById(id);
        result.setData(BeanCopierUtils.convertObject(omsPermission,OmsPermissionDTO.class));
        return result;
    }

    /**
     * 保存OMS-权限功能
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "保存OMS-权限功能",type = LogType.OPERATELOG)
    @ApiOperation("保存OMS-权限功能")
    @PostMapping(value = "/permission/save")
    @CacheEvict(prefix = CacheKeys.OMS_PERMIT_URLMAP)
    public Result<Long> save(@RequestBody OmsPermissionDTO param) {
        Result<Long> result = new Result<>();
        OmsPermission permission = omsPermissionService.getPermissionByCode(param.getCode());
        if(null != permission && null != permission.getId() && (null == param.getId() || !permission.getId().equals(param.getId()))) {
            return result.errorResult(ErrorCode.BAD_REQUEST,"权限编号已存在");
        }
        Long id = omsPermissionService.saveOmsPermission(param);
        result.setData(id);
        return result;
    }

    /**
     * 根据ID删除OMS权限功能
     * @param param
     * @return result<Boolean>
     */
    @SysLog(value = "根据ID删除OMS权限功能",type = LogType.OPERATELOG)
    @ApiOperation("根据ID删除OMS权限功能")
    @PostMapping(value = "/permission/delete")
    public Result<Boolean> deleteOmsPermissionById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        omsPermissionService.deleteOmsPermissionById(param.getId());
        result.setData(true);
        return result;
    }

}
