package com.dz.ms.user.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 菜单权限表 sys_menu
 *
 */
@Getter
@Setter
@NoArgsConstructor
@Table("系统角色")
@TableName(value = "sys_menu")
public class SysMenu 
{
    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 20,isNull = false,comment = "菜单ID")
    @TableId(type = IdType.AUTO)
    private Long menuId;
    @Columns(type = ColumnType.VARCHAR,length = 50,isNull = false,comment = "菜单名称")
    private String menuName;
    @Columns(type = ColumnType.BIGINT,length = 20,isNull = true,defaultValue = "0",comment = "父菜单ID")
    private Long parentId;
    @Columns(type = ColumnType.INT,length = 10,isNull = true,defaultValue = "0",comment = "显示顺序")
    private Integer orderNum;
    @Columns(type = ColumnType.VARCHAR,length = 200,isNull = true,defaultValue = "",comment = "路由地址")
    private String path;
    @Columns(type = ColumnType.VARCHAR,length = 255,isNull = true,comment = "组件路径")
    private String component;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = false,defaultValue = "1",comment = "是否为外链（0是 1否）")
    private Integer isFrame;
    @Columns(type = ColumnType.VARCHAR,length = 10,isNull = true,defaultValue = "",comment = "类型（M目录 C菜单 F按钮）")
    private String menuType;
    @Columns(type = ColumnType.VARCHAR,length = 10,isNull = true,defaultValue = "0",comment = "显示状态（0显示 1隐藏）")
    private String visible;
    @Columns(type = ColumnType.VARCHAR,length = 10,isNull = true,defaultValue = "0",comment = "菜单状态（0显示 1隐藏）")
    private String status;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,comment = "权限字符串")
    private String perms;
    @Columns(type = ColumnType.VARCHAR,length = 100,isNull = true,defaultValue = "#",comment = "菜单图标")
    private String icon;
    @Columns(type = ColumnType.VARCHAR,length = 255,isNull = true,comment = "请求地址")
    private String url;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = true,defaultValue = "2",comment = "菜单类型 1:小程序 2:PC")
    private Integer type;

    @Columns(type = ColumnType.BIGINT,length = 19,isNull = true,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = true,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = true,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = true,comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = true,comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("menuId", getMenuId())
            .append("menuName", getMenuName())
            .append("parentId", getParentId())
            .append("orderNum", getOrderNum())
            .append("path", getPath())
            .append("component", getComponent())
            .append("isFrame", getIsFrame())
            .append("menuType", getMenuType())
            .append("visible", getVisible())
            .append("status ", getStatus())
            .append("perms", getPerms())
            .append("icon", getIcon())
            .append("url", getUrl())
            .append("type", getType())
            .toString();
    }
}
