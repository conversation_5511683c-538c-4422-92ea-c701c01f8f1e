<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.basic.mapper.MpMsgSubscribeMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    id,
  	    scene,
  	    msg_id,
  	    template_id,
  	    uid,
  	    openid,
  	    state,
  	    push_time,
  	    tenant_id,
  	    created
    </sql>

    <!-- 获取用户有效订阅记录中最早的一条 -->
    <select id="selectUserSubscribeList" resultType="com.dz.common.core.dto.basic.MpMsgSubscribeUserDTO">
        select
        min(id) id,
        uid,
        openid
        from mp_msg_subscribe
        where state = 0
        <if test="null != uids and uids.size > 0">
            and uid in
            <foreach collection="uids" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        group by uid
    </select>

    <!-- 根据ID列表将订阅记录更新为已发送 -->
    <update id="updateStateByIds">
        update
        mp_msg_subscribe
        set state = 1
        where id in
        <foreach collection="ids" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>
    <update id="updatePushNumberByIds">
        update
        mp_msg_subscribe
        set sub_number = sub_number - 1
        where openid in
        <foreach collection="ids" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>

</mapper>
