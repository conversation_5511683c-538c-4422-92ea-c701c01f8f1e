package com.dz.ms.basic.dto;

import java.io.Serializable;

/**
 * 代码生成-表字段信息
 * @author: Handy
 * @date:   2019/12/5 17:33
 */
public class AutoCodeColumnDTO implements Serializable{

    private static final long serialVersionUID = -935179123973929626L;

    private String columName;
    private String upperCaseColum;
    private String firstUpperCaseColum;
    private String dataType;
    private String orgDataType;
    private String modelDataType;
    private String columnComment;
    private int isPrimark;
    private int isAuto;
    private boolean isNull;
    private int length;
    private Integer scale;

    public String getColumName() {
        return columName;
    }
    public void setColumName(String columName) {
        this.columName = columName;
    }
    public String getDataType() {
        return dataType;
    }
    public void setDataType(String dataType) {
        String dt = "";
        String mdt = "";
        if(dataType.equals("int")) {
            dt = "Integer".toUpperCase();
            mdt = "Integer";
        }
        else if(dataType.equals("bigint")) {
            dt = "bigint".toUpperCase();
            mdt = "Long";
        }
        else if(dataType.equals("boolean")) {
            dt = "boolean".toUpperCase();
            mdt = "Boolean";
        }
        else if(dataType.equals("char")) {
            dt = "char".toUpperCase();
            mdt = "String";
        }
        else if(dataType.equals("date")) {
            dt = "date".toUpperCase();
            mdt = "Date";
        }
        else if(dataType.equals("datetime")) {
            dt = "TIMESTAMP";
            mdt = "Date";
        }
        else if(dataType.equals("time")) {
            dt = "TIME";
            mdt = "Date";
        }
        else if(dataType.equals("decimal")) {
            dt = "decimal".toUpperCase();
            mdt = "BigDecimal";
        }
        else if(dataType.equals("double")) {
            dt = "double".toUpperCase();
            mdt = "Double";
        }
        else if(dataType.equals("float")) {
            dt = "float".toUpperCase();
            mdt = "Float";
        }
        else if(dataType.equals("text") || dataType.equals("mediumtext")) {
            dt = "varchar".toUpperCase();
            mdt = "String";
        }
        else if(dataType.equals("tinyint")) {
            dt = "tinyint".toUpperCase();
            mdt = "Integer";
        }
        else if(dataType.equals("timestamp")) {
            dt = "timestamp".toUpperCase();
            mdt = "Date";
        }
        else if(dataType.equals("varchar")) {
            dt = "varchar".toUpperCase();
            mdt = "String";
        }
        else if(dataType.equals("longtext")) {
            dt = "LONGVARCHAR".toUpperCase();
            mdt = "String";
        }
        else {
            dt = dataType.toUpperCase();
            mdt = "String";
        }
        this.orgDataType = dataType;
        this.dataType = dt;
        this.modelDataType = mdt;
    }
    public String getColumnComment() {
        return columnComment;
    }
    public void setColumnComment(String columnComment) {
        this.columnComment = columnComment;
    }
    public String getUpperCaseColum() {
        return upperCaseColum;
    }
    public void setUpperCaseColum(String upperCaseColum) {
        this.upperCaseColum = upperCaseColum;
    }
    public int getIsPrimark() {
        return isPrimark;
    }
    public void setIsPrimark(int isPrimark) {
        this.isPrimark = isPrimark;
    }
    public int getIsAuto() {
        return isAuto;
    }
    public void setIsAuto(int isAuto) {
        this.isAuto = isAuto;
    }
    public String getModelDataType() {
        return modelDataType;
    }
    public String getFirstUpperCaseColum() {
        return firstUpperCaseColum;
    }
    public void setFirstUpperCaseColum(String firstUpperCaseColum) {
        this.firstUpperCaseColum = firstUpperCaseColum;
    }

    public boolean getIsNull() {
        return isNull;
    }

    public void setIsNull(boolean isNull) {
        this.isNull = isNull;
    }

    public int getLength() {
        return length;
    }

    public void setLength(int length) {
        this.length = length;
    }

    public Integer getScale() {
        return scale;
    }

    public void setScale(Integer scale) {
        this.scale = scale;
    }

    public String getOrgDataType() {
        return orgDataType;
    }

    public void setOrgDataType(String orgDataType) {
        this.orgDataType = orgDataType;
    }
}
