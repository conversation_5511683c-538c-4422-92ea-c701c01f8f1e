package com.dz.ms.product.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Table;
import lombok.Data;
import org.springframework.data.annotation.Id;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**

 */
@Data
@Table("sftp商品")
@TableName(value = "ods_item")
public class OdsItem implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private String id;

    private String itemIdStr;

    private String deptIdStr;

    private String deptNameStr;

    private String depaIdStr;

    private String depaNameStr;

    private String lineIdStr;

    private String lineNameStr;

    private String classIdStr;

    private String classNameStr;

    private String itemNameStr;

    private String taxRateStr;

    private String costPriceStr;

    private String spuIdStr;

    private String spuNameStr;

    private String seriesIdStr;

    private String bizDate;

    private Date createTime;

    private Date updateTime;

    private Long tenantId;
}
