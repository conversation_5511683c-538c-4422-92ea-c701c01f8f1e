const app = getApp()

Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    myCredits: {
      type: Number,
      value: 0,
    },
    isShow: {
      type: Boolean,
    }
  },
  /**
   * 组件的初始数据
   */
  data: {
    btnList: [{
      label: '获取更多积分',
      value: 'getMore',
      icon: '',
      url: '/pages/interactiveTask/interactiveTask',
      trackData: 'shop_more_point',
    }, {
      label: '兑换记录',
      value: 'exchangeRecord',
      icon: '',
      url: '/pages/myExchange/myExchange',
      trackData: 'shop_exchange_record',
    }]
  },

  /**
   * 组件的方法列表
   */
  methods: {
    async handleGoto(e) {
      const {
        url,
        track
      } = e.currentTarget.dataset;
      wx.$mp.track({
        event: track
      })
      app.goUrl(url)
    },
    goPoint: app.debounce(function () {
      wx.$mp.navigateTo({
        url: '/pages/myIntegral/myIntegral',
      })
    })
  }
})
