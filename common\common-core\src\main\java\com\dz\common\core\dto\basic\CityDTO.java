package com.dz.common.core.dto.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 城市信息DTO
 * @author: Handy
 * @date:   2023/03/15 15:52
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "城市信息")
public class CityDTO{

    @ApiModelProperty(value = "城市编码")
    private String code;
    @ApiModelProperty(value = "城市名称")
    private String name;

    public CityDTO(String code, String name) {
        this.code = code;
        this.name = name;
    }

}
