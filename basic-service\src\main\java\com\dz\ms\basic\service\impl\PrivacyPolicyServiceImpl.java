package com.dz.ms.basic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.dto.PrivacyPolicyDTO;
import com.dz.ms.basic.entity.PrivacyPolicy;
import com.dz.ms.basic.mapper.PrivacyPolicyMapper;
import com.dz.ms.basic.service.PrivacyPolicyService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * 隐私条款
 * @author: Handy
 * @date:   2023/05/17 16:50
 */
@Service
public class PrivacyPolicyServiceImpl extends ServiceImpl<PrivacyPolicyMapper,PrivacyPolicy> implements PrivacyPolicyService {

	@Resource
    private PrivacyPolicyMapper privacyPolicyMapper;

	/**
     * 分页查询隐私条款
     * @param param
     * @return PageInfo<PrivacyPolicyDTO>
     */
    @Override
    public PageInfo<PrivacyPolicyDTO> getPrivacyPolicyList(PrivacyPolicyDTO param) {
        PrivacyPolicy privacyPolicy = BeanCopierUtils.convertObjectTrim(param, PrivacyPolicy.class);
        String name = privacyPolicy.getName();
        //privacyPolicy.setPolicyName(null);
        LambdaQueryWrapper<PrivacyPolicy> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(name)) {
            wrapper.like(PrivacyPolicy::getName, name);
        }
        if (param.getStatus() != null){
            wrapper.eq(PrivacyPolicy::getStatus, param.getStatus());
        }
        wrapper.orderByDesc(PrivacyPolicy::getId);
        IPage<PrivacyPolicy> page = privacyPolicyMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), wrapper);
        List<PrivacyPolicyDTO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(page.getRecords())) {
            List<PrivacyPolicyDTO> privacyPolicyDTOS = BeanCopierUtils.convertList(page.getRecords(), PrivacyPolicyDTO.class);
            privacyPolicyDTOS.sort(Comparator.comparing(PrivacyPolicyDTO::getPushTime).reversed());
            LocalDateTime now = LocalDateTime.now();
            //选出发布时间最大并且发布时间小于当前时间，则该条为发布中
            for (PrivacyPolicyDTO privacyPolicyDTO : privacyPolicyDTOS) {
                Date pushTime = privacyPolicyDTO.getPushTime();
                ZonedDateTime zonedDateTime = pushTime.toInstant().atZone(ZoneId.systemDefault());
                LocalDateTime dateTime = zonedDateTime.toLocalDateTime();
                if (dateTime.isBefore(now)){
                    privacyPolicyDTO.setPushStatus(1);
                    break;
                }
            }
            for (PrivacyPolicyDTO privacyPolicyDTO : privacyPolicyDTOS){
                if (privacyPolicyDTO.getPushStatus() == null){
                    Date pushTime = privacyPolicyDTO.getPushTime();
                    ZonedDateTime zonedDateTime = pushTime.toInstant().atZone(ZoneId.systemDefault());
                    LocalDateTime dateTime = zonedDateTime.toLocalDateTime();
                    //发布时间大于当前时间，待发布  反之已结束
                    if (dateTime.isAfter(now)){
                        privacyPolicyDTO.setPushStatus(0);
                    }else {
                        privacyPolicyDTO.setPushStatus(2);
                    }
                }
            }
            return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), privacyPolicyDTOS);
        }
        return new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(),list);
    }

    /**
     * 根据ID查询隐私条款
     * @param id
     * @return PrivacyPolicyDTO
     */
    @Override
    public PrivacyPolicyDTO getPrivacyPolicyById(Long id) {
        PrivacyPolicy privacyPolicy = privacyPolicyMapper.selectById(id);
        return BeanCopierUtils.convertObject(privacyPolicy,PrivacyPolicyDTO.class);
    }

    /**
     * 保存隐私条款
     * @param param
     * @return Long
     */
    @Override
    public Long savePrivacyPolicy(PrivacyPolicyDTO param) {
        CurrentUserDTO currentUser = SecurityContext.getUser();
        Long uid = currentUser.getUid();
        Long tenantId = currentUser.getTenantId();
        PrivacyPolicy privacyPolicy = new PrivacyPolicy(param.getId(), param.getName(), param.getPolicyTitle(), param.getNewTitle(), param.getPolicyVersion(), param.getContent(), param.getUrlSum(), param.getPushTime(), param.getStatus());
        if (ParamUtils.isNullOr0Long(privacyPolicy.getId())) {
            privacyPolicy.setTenantId(tenantId);
            privacyPolicy.setCreator(uid);
            QueryWrapper<PrivacyPolicy> privacyPolicyQueryWrapper = new QueryWrapper<>();
            privacyPolicyQueryWrapper.eq("push_time", param.getPushTime());
            if(privacyPolicyMapper.selectCount(privacyPolicyQueryWrapper) > 0) {
                throw new BusinessException("已有该时段隐私政策");
            }
            privacyPolicyMapper.insert(privacyPolicy);
            Long id = privacyPolicy.getId();
            LocalDate currentDate = LocalDate.now();

            // 定义日期格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            String dateString = currentDate.format(formatter);
            String idString = String.format("%03d", id);
            PrivacyPolicy privacyPolicy1 = new PrivacyPolicy();
            privacyPolicy1.setId(id);
            // 拼接日期和id字符串
            privacyPolicy1.setPolicyVersion(dateString + idString);
            privacyPolicyMapper.updateById(privacyPolicy1);
        }
        else {
            privacyPolicy.setModifier(uid);
            privacyPolicy.setModified(new Date());
            if (param.getPushTime() != null) {
                QueryWrapper<PrivacyPolicy> privacyPolicyQueryWrapper = new QueryWrapper<>();
                privacyPolicyQueryWrapper.eq("push_time", param.getPushTime());
                privacyPolicyQueryWrapper.ne("id", param.getId());
                if (privacyPolicyMapper.selectCount(privacyPolicyQueryWrapper) > 0) {
                    throw new BusinessException("已有该时段隐私政策");
                }
            }
            privacyPolicyMapper.updateById(privacyPolicy);
        }
        return privacyPolicy.getId();
    }

    /**
     * 根据ID删除隐私条款
     * @param param
     */
    @Override
    public void deletePrivacyPolicyById(IdCodeDTO param) {
        privacyPolicyMapper.deleteById(param.getId());
    }

    /**
     * 获取最新隐私条款版本号
     * @return
     */
    @Override
    public String getLastPrivacyPolicyVersion() {
        QueryWrapper<PrivacyPolicy> privacyPolicyQueryWrapper = new QueryWrapper<>();
        privacyPolicyQueryWrapper.lt("push_time", LocalDateTime.now());
        privacyPolicyQueryWrapper.orderByDesc("push_time");
        privacyPolicyQueryWrapper.last("limit 1");
        PrivacyPolicy privacyPolicy = privacyPolicyMapper.selectOne(privacyPolicyQueryWrapper);
        return privacyPolicy.getPolicyVersion();
    }

    /**
     * 获取最新隐私条款内容
     * @return
     */
    @Override
    public PrivacyPolicyDTO getLastPrivacyPolicyInfo() {
        SecurityContext.setUser(new CurrentUserDTO(1L));
        QueryWrapper<PrivacyPolicy> privacyPolicyQueryWrapper = new QueryWrapper<>();
        privacyPolicyQueryWrapper.lt("push_time", LocalDateTime.now());
        privacyPolicyQueryWrapper.orderByDesc("push_time");
        privacyPolicyQueryWrapper.last("limit 1");
        PrivacyPolicy privacyPolicy = privacyPolicyMapper.selectOne(privacyPolicyQueryWrapper);
        return BeanCopierUtils.convertObject(privacyPolicy,PrivacyPolicyDTO.class);
    }
    
}
