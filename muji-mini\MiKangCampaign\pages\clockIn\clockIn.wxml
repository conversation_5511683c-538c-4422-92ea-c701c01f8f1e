<!--MiKangCampaign/pages/clockIn/clockIn.wxml-->
<my-page overallModal="{{overallModal}}" loading="{{loading}}">
  <view class="page-container" style="background-image:url({{$cdn}}/MiKangCampaign/clockInBack1.jpg);">
    <custom-header background="transparent" type="{{1}}" isBackHidden="{{ isBackHidden }}" color="white">
      <view slot="content" class="custom-header-more">
        米糠五天打卡
      </view>
    </custom-header>
    <view class="right-lanyard">
      <image style="height: 286rpx; width: 136rpx;" mode="aspectFit" src="{{$cdn}}/MiKangCampaign/mk-sign-up-lanyard.png"></image>
    </view>
    <view class="page-content">
      <view class="clockIn" style="background-image: url({{$cdn}}/MiKangCampaign/clockInBack.png);">
        <view class="clockIn-title">
          <!-- <text class="clockIn-icon"></text> -->
          <view class="clockIn-title-wrap">Day <text class="num"> {{days}}</text> {{disabled?"":''}}</view>
        </view>
        <scroll-view class="clockIn-scroll" enhanced="{{ true }}" bounces="{{ false }}" show-scrollbar="{{ false }}" scroll-y>
          <view class="clockIn-wrap">
            <view class="score">
              <view class="prompt" wx:if="{{!islastDay&&!disabled}}">请根据你今天的使用感受做出评分</view>
              <view class="answer {{disabled?'answer-detail':''}} {{item.questionType==3||item.questionType==5?'answer1':''}}" wx:for="{{questionList}}">
                <!-- 问答题暂时不展示title 本期先这样用 -->
                <block wx:if="{{item.questionType==5}}">
                  <view wx:if="{{!disabled}}" class="answer-title"><text>{{item.title||''}}</text> <text class="answer-text" wx:if="{{item.subTitle}}">{{item.subTitle}}</text></view>
                  <view>
                    <van-uploader multiple class="picture-wrap" file-list="{{ fileList }}" max-count="1" preview-size="172rpx" image-fit="aspectFill" use-before-read="{{true}}" data-index="{{index}}" bind:after-read="afterRead" bind:before-read="beforeRead" bind:delete="del" disabled="{{disabled}}" deletable="{{!disabled}}">
                      <view class="picture" style="margin-top:{{disabled?'0rpx':'20rpx'}} ;margin-bottom:{{disabled?'0rpx':'20rpx'}};">
                        <image class="img" src="{{$cdn}}/MiKangCampaign/Plus.png" mode="" />
                      </view>
                    </van-uploader>
                  </view>
                </block>
                <block wx:if="{{item.questionType==3}}">
                  <view class="textarea" wx:if="{{!disabled}}">
                    <view class="answer-title {{days==5?'answer-title3':''}}"><text>{{item.title||''}}</text><text class="answer-text" wx:if="{{item.subTitle}}">{{item.subTitle}}</text>
                    </view>
                    <!-- 问答题暂时不展示title 本期先这样用 -->
                    <view class="answer-textarea">
                      <textarea disabled="{{disabled}}" adjust-position="{{true}}" cursor-spacing="100" show-confirm-bar="{{true}}" maxlength="200" class="txt" value="{{ info[item.alias] }}" bindinput="bindinput" data-alias="{{item.alias}}" placeholder-style="font-size:24rpx;color: #3C3C43;" bindkeyboardheightchange="keyboardChange" placeholder="{{item.questionTips||''}}"></textarea>
                      <view class="textNum">{{info[item.alias].length?info[item.alias].length:0}}/200</view>
                    </view>
                  </view>
                  <view wx:else class="textarea disabledFalse">
                    <view class="disabledFalse-textarea">我的评价</view>
                    <text>{{info[item.alias]}}</text>
                  </view>
                </block>
                <block wx:if="{{item.questionType==2}}">
                  <view class="answer-title"><text>{{item.title||''}}</text>
                    <text class="answer-text" wx:if="{{item.subTitle}}">{{item.subTitle}}</text>
                  </view>
                  <view class="skin">
                    <view wx:if="{{(items.isClick&&disabled)||!disabled}}" wx:for="{{item.options}}" wx:for-item="items" wx:key="indexs" wx:for-index="indexs" bind:tap="clickTap" data-index="{{index}}" data-indexs="{{indexs}}" class="skin-item {{items.isClick?'active':''}}">{{items.title}}
                    </view>

                  </view>
                </block>
                <block wx:if="{{item.questionType==4}}">
                  <view class="answer-title {{days==5?'answer-title4':''}}" ><text>{{item.title||''}}</text>
                    <text class="answer-text" wx:if="{{item.subTitle }}">{{item.subTitle}}</text>
                  </view>
                  <view class="answer-star">
                    <view>
                      <van-rate disabled="{{disabled}}" value="{{info[item.alias]}}" bind:change="changeRate1" data-alias="{{item.alias}}" data-index="{{index}}" size="48rpx" gutter="27rpx" count="{{item.maxScore}}" icon="{{$cdn}}/MiKangCampaign/start2.png" void-icon="{{$cdn}}/MiKangCampaign/start1.png"></van-rate>
                      <!--  -->
                    </view>
                    <!-- wx:if="{{info[item.alias]}}" -->
                    <view class="num" wx:if="{{!disabled}}">{{info[item.alias]||0}}<text class="key"> / {{item.maxScore}}</text></view>
                  </view>
                </block>
              </view>
              <!-- disabled="{{!isComplate}}"  -->
              <view class="bottom-box">
                <basic-button wx:if="{{type != 'view'}}" disabled="{{loda}}" width="{{590}}" size="large" bind:click="submit">
                  立即提交完成打卡
                </basic-button>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
    <!-- 拒绝订阅 提示框 -->
    <guidePopup showGuide="{{showContact1}}" bind:finish="closeGuide" />
  </view>
</my-page>