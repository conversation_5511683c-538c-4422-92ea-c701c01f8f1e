package com.dz.ms.product.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品信息
 *
 * @author: LiinNs
 * @date: 2024/11/27 15:13
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table("商品信息")
@TableName(value = "product")
public class Product implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "商品ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR, length = 256, isNull = true, comment = "商品名称", isIndex = true)
    private String productName;
    @Columns(type = ColumnType.VARCHAR, length = 256, isNull = true, comment = "副标题")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String subTitle;
    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = true, comment = "商品编号")
    private String productCode;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, comment = "商品类型 1实物商品 2电子券")
    private Integer pdType;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, comment = "是否赠品 0赠品 1正品商品")
    private Integer beGift;
    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = true, comment = "三方商品ID")
    private String itemId;
    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = true, comment = "外部数据ID")
    private String venderId;
    @Columns(type = ColumnType.VARCHAR, length = 2500, isNull = true, comment = "场景图片地址 逗号分隔")
    private String scenceImgUrl;
    @Columns(type = ColumnType.VARCHAR, length = 2500, isNull = true, comment = "商品橱窗图片展示位 逗号分隔")
    private String shelfImgUrl;
    @Columns(type = ColumnType.VARCHAR, length = 1024, isNull = true, comment = "商品详情")
    private String details;
    @Columns(type = ColumnType.DECIMAL, length = 19, scale = 2, isNull = true, comment = "吊牌价")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal prePrice;
    @Columns(type = ColumnType.DECIMAL, length = 19, scale = 2, isNull = true, comment = "成本价")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal originPrice;
    @Columns(type = ColumnType.DECIMAL, length = 19, scale = 2, isNull = true, comment = "销售价")
    private BigDecimal price;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, comment = "购买方式 0积分 1积分+金额")
    private Integer purchaseType;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, comment = "兑换积分")
    private Integer costPoint;
    @Columns(type = ColumnType.DECIMAL, length = 19, scale = 2, isNull = true, defaultValue = "0", comment = "1积分+金额时 仍需支付的金额")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal costPrice;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, defaultValue = "0", comment = "金额是否展示在货架列表 0不展示 1展示")
    private Integer costPriceOnShelf;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, defaultValue = "0", comment = "商品累计兑换量")
    private Integer exchangeNum;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, comment = "兑换须知类型 1图片 2富文本")
    private Integer exchangeDescType;
    @Columns(type = ColumnType.VARCHAR, length = 1024, isNull = true, comment = "兑换须知图片")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String exchangeDescUrl;
    @Columns(type = ColumnType.TEXT, length = 0, isNull = true, comment = "兑换须知富文本")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String exchangeDescContent;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, comment = "商品详情类型 1图片 2富文本")
    private Integer detailsType;
    @Columns(type = ColumnType.VARCHAR, length = 1024, isNull = true, comment = "商品详情图片")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String detailsUrl;
    @Columns(type = ColumnType.TEXT, length = 0, isNull = true, comment = "商品详情富文本")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String detailsContent;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, comment = "使用说明类型 1图片 2富文本")
    private Integer referType;
    @Columns(type = ColumnType.VARCHAR, length = 1024, isNull = true, comment = "使用说明图片")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String referUrl;
    @Columns(type = ColumnType.TEXT, length = 0, isNull = true, comment = "使用说明富文本")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String referContent;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = false, defaultValue = "1", comment = "状态 0禁用 1启用")
    private Integer state;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, defaultValue = "0", comment = "0正常 1删除")
    @TableLogic
    private Integer isDeleted;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    public Product(Long id, String productName, String subTitle, String productCode, Integer pdType, Integer beGift, String itemId, String venderId, String scenceImgUrl, String shelfImgUrl, String details, BigDecimal prePrice, BigDecimal originPrice, BigDecimal price, Integer purchaseType, Integer costPoint, BigDecimal costPrice, Integer costPriceOnShelf, Integer exchangeNum, Integer exchangeDescType, String exchangeDescUrl, String exchangeDescContent, Integer detailsType, String detailsUrl, String detailsContent, Integer referType, String referUrl, String referContent, Integer state) {
        this.id = id;
        this.productName = productName;
        this.subTitle = subTitle;
        this.productCode = productCode;
        this.pdType = pdType;
        this.beGift = beGift;
        this.itemId = itemId;
        this.venderId = venderId;
        this.scenceImgUrl = scenceImgUrl;
        this.shelfImgUrl = shelfImgUrl;
        this.details = details;
        this.prePrice = prePrice;
        this.originPrice = originPrice;
        this.price = price;
        this.purchaseType = purchaseType;
        this.costPoint = costPoint;
        this.costPrice = costPrice;
        this.costPriceOnShelf = costPriceOnShelf;
        this.exchangeNum = exchangeNum;
        this.exchangeDescType = exchangeDescType;
        this.exchangeDescUrl = exchangeDescUrl;
        this.exchangeDescContent = exchangeDescContent;
        this.detailsType = detailsType;
        this.detailsUrl = detailsUrl;
        this.detailsContent = detailsContent;
        this.referType = referType;
        this.referUrl = referUrl;
        this.referContent = referContent;
        this.state = state;
    }

}
