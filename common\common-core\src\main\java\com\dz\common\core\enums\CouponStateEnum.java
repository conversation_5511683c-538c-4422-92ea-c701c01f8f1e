package com.dz.common.core.enums;

import java.util.Objects;

/**
 * <AUTHOR>

public enum CouponStateEnum {
    //未激活
    PENDING_UN_ACTIVATE(0, "未激活"),
    // 已领取
    PENDING_TAKE(1, "已领取"),
    // 待核销
    BOOKED(2, "待核销"),
    // 已核销
    TAKES(3, "已核销"),
    // 已取消
    EXPIRED(4, "已取消"),
    // 已过期
    EXPIRE(5, "已过期"),
    // 已赠送
    GIFT(6, "已赠送"),
    // 已失效
    LOSE(7, "已失效"),
    ;

    private final Integer code;
    private final String value;

    CouponStateEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    public Integer getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CouponStateEnum resultEnum : CouponStateEnum.values()) {
            if (Objects.equals(resultEnum.getCode(), code)) {
                return resultEnum.value;
            }
        }
        return null;
    }
}
