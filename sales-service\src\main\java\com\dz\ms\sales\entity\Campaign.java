package com.dz.ms.sales.entity;


import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/30
 */
@Getter
@Setter
@NoArgsConstructor
@Table("活动基础信息")
@TableName(value = "campaign")
public class Campaign implements Serializable {
    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "id")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = false, comment = "活动名称")
    private String campaignName;
    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = true, comment = "活动标识", uniqueKeys = {"campaignCode"})
    private String campaignCode;
    @Columns(type = ColumnType.VARCHAR, length = 200, isNull = true, comment = "活动主图")
    private String campaignImg;
    @Columns(type = ColumnType.VARCHAR, length = 500, isNull = true, comment = "活动详情图")
    private String campaignDetailImg;
    @Columns(type = ColumnType.VARCHAR, length = 200, isNull = true, comment = "活动规则图")
    private String campaignRuleImg;
    @Columns(type = ColumnType.VARCHAR, length = 300, isNull = true, comment = "活动描述")
    private String campaignDesc;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "开始时间")
    private Date campaignStartTime;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "结束时间")
    private Date campaignEndTime;
    @Columns(type = ColumnType.VARCHAR, length = 60, isNull = true, comment = "活动公示时间")
    private String campaignShowTime;
    @Columns(type = ColumnType.VARCHAR, length = 60, isNull = true, comment = "发送短信时间")
    private String sendSmsTime;
    @Columns(type = ColumnType.TEXT, length = 0, isNull = true, comment = "campaign内容json")
    private String content;
    @Columns(type = ColumnType.VARCHAR, length = 60, isNull = true, comment = "下期活动时间")
    private String nextCampaignTime;

    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "1", comment = "是否允许报名参加 0不允许 1允许")
    private Integer isAllowSignUp;
    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = true, comment = "关联活动标识")
    private String campaignJoinCode;


    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "完成后是否发券 0不发券 1发券")
    private Integer isSendCoupon;
    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = true, comment = "优惠券编码")
    private String couponCode;

    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "完成后是否抽奖 0不允许抽奖 1抽奖")
    private Integer isDrawLottery;


    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "1", comment = "活动状态 0未开始 1进行中 2公示期 3已结束")
    private Integer state;

    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "是否删除 0未删除 1已删除")
    @TableLogic
    private Integer isDeleted;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
}
