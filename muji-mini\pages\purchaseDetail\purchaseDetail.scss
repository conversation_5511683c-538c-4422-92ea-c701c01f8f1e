.page-list {
  display: flex;
  flex-direction: column;
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
  height: 100vh;
  background: #F6F6F6;
}

.page-content {
  margin: 30rpx 40rpx;

  .page-header-top {
    background: #fff;
    padding: 60rpx 30rpx 0 30rpx;


    .page-header-logo {
      text-align: center;

      image {
        width: 120rpx;
        height: 50rpx;
      }
    }

    .page-header-txt {
      text-align: center;
      margin-top: 21rpx;
      margin-bottom: 61rpx;
      font-family: SourceHanSansCN,
        SourceHanSansCN,
        MUJIFont2020;
      font-weight: 500;
      font-size: 28rpx;
      color: #3C3C43;
      // line-height: 42rpx;
      text-align: center;
      font-style: normal;
    }

    .page-header-line {
      width: 100%;
      height: 1rpx;
      background-color: #EEEEEE;
    }

    .page-header-rule {
      margin: 42rpx 0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .page-header-text {
        display: flex;
        align-items: center;
        font-family: SourceHanSansCN,
          SourceHanSansCN,
          MUJIFont2020;
        font-weight: 400;
        font-size: 24rpx;
        color: #888888;
        // line-height: 36rpx;
        // text-align: left;
        font-style: normal;

        image {
          margin-right: 10rpx;
        }
      }
    }

    .page-header-cell {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 40rpx;

      .page-header-cell-item {
        flex: 1;
        font-family: SourceHanSansCN,
          SourceHanSansCN,
          MUJIFont2020;
        font-weight: 400;
        font-size: 24rpx;
        color: #888888;
        // line-height: 36rpx;
        text-align: left;
        font-style: normal;
      }

      .page-header-cell-value {
        flex: 1;
        font-family: MUJIFont2020, MUJIFont2020;
        font-weight: 300;
        font-size: 24rpx;
        color: #888888;
        // line-height: 48rpx;
        text-align: right;
        font-style: normal;
      }
    }

    .page-header-shop {
      .page-header-shop-name {
        margin-bottom: 20rpx;
        font-family: SourceHanSansCN,
          SourceHanSansCN,
          MUJIFont2020;
        font-weight: 500;
        font-size: 24rpx;
        color: #3C3C43;
        // line-height: 36rpx;
        text-align: left;
        font-style: normal;
      }

      .page-header-shop-order {
        margin-bottom: 20rpx;
        font-family: MUJIFont2020, MUJIFont2020;
        font-weight: 300;
        font-size: 22rpx;
        color: #888888;
        // line-height: 44rpx;
        text-align: left;
        font-style: normal;
      }

      .page-header-shop-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20rpx;

        .page-header-shop-info1 {
          flex: 1;
          font-family: MUJIFont2020, MUJIFont2020;
          font-weight: 300;
          font-size: 22rpx;
          color: #888888;
          // line-height: 44rpx;
          text-align: left;
          font-style: normal;
        }

        .page-header-shop-info2 {
          flex: 1;
          font-family: MUJIFont2020, MUJIFont2020;
          font-weight: 300;
          font-size: 22rpx;
          color: #888888;
          // line-height: 44rpx;
          text-align: right;
          font-style: normal;
        }
      }


    }

    .page-header-code {
      margin: 0 auto;
      display: flex;
      justify-content: center;
      align-items: center;
      padding-bottom: 80rpx;

      // width: 550rpx;
      // height: 95rpx;
      image {
        width: 550rpx;
        height: 95rpx;
      }

      #barcodeCoupon {
        position: fixed;
        left: 9000px;
        z-index: -1 !important;
        // position: absolute;
        // right: 999999;
        width: 550rpx;
        height: 95rpx;
        // width: 435rpx;
        // height: 95rpx;
      }
    }
  }

  .page-equity {
    margin-top: 30rpx;
    background-color: #fff;
    padding: 36rpx 30rpx 41rpx 30rpx;

    .page-equity-title {
      font-family: SourceHanSansCN,
        SourceHanSansCN,
        MUJIFont2020;
      font-weight: 500;
      font-size: 32rpx;
      color: #3C3C43;
      line-height: 48rpx;
      letter-spacing: 1px;
      text-align: left;
      font-style: normal;
    }

    .page-equity-line {
      margin-top: 36rpx;
      margin-bottom: 34rpx;
      width: 100%;
      height: 1rpx;
      background-color: #EEEEEE;
    }

    .page-equity-cell {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 40rpx;

      .page-equity-cell-item {
        flex: 1;
        font-family: SourceHanSansCN,
          SourceHanSansCN,
          MUJIFont2020;
        font-weight: 400;
        font-size: 24rpx;
        color: #888888;
        // line-height: 36rpx;
        text-align: left;
        font-style: normal;
      }

      .page-equity-cell-value {
        flex: 1;
        font-family: MUJIFont2020, MUJIFont2020;
        font-weight: 400;
        font-size: 24rpx;
        color: #888888;
        // line-height: 48rpx;
        text-align: right;
        font-style: normal;

      }

    }

    .page-equity-cell-left-value {

      font-family: MUJIFont2020, MUJIFont2020;
      font-weight: 300;
      font-size: 24rpx;
      color: #888888;
      // line-height: 48rpx;
      text-align: right;
      font-style: normal;
    }
  }

  .page-info {
    margin-top: 30rpx;

    background-color: #fff;
    padding: 60rpx 30rpx 40rpx 30rpx;

    .page-info-title {
      font-family: SourceHanSansCN,
        SourceHanSansCN,
        MUJIFont2020;
      font-weight: bold;
      font-size: 32rpx;
      color: #3C3C43;
      // line-height: 48rpx;
      letter-spacing: 1px;
      text-align: center;
      font-style: normal;
    }

    .page-info-txt {
      margin-top: 20rpx;
      margin-bottom: 40rpx;
      font-family: SourceHanSansCN,
        SourceHanSansCN,
        MUJIFont2020;
      font-weight: 400;
      font-size: 22rpx;
      color: #888888;
      // line-height: 33rpx;
      text-align: center;
      font-style: normal;
    }

    .page-info-line {
      width: 100%;
      height: 1rpx;
      background-color: #EEEEEE;
    }

    .page-info-item {
      display: flex;
      align-items: center;
      margin-bottom: 25rpx;

      .page-info-item-label {
        font-family: SourceHanSansCN,
          SourceHanSansCN,
          MUJIFont2020;
        font-weight: 400;
        font-size: 24rpx;
        color: #3C3C43;
        line-height: 36rpx;
        text-align: left;
        font-style: normal;
      }

      .page-info-item-value {
        flex: 1;
        width: 0;
        font-family: SourceHanSansCN,
          SourceHanSansCN,
          MUJIFont2020;
        font-weight: 400;
        font-size: 24rpx;
        color: #3C3C43;
        line-height: 36rpx;
        text-align: left;
        font-style: normal;
      }
    }

    .page-info-footer {
      margin-bottom: 40rpx;
      font-family: SourceHanSansCN,
        SourceHanSansCN,
        MUJIFont2020;
      font-weight: 500;
      font-size: 24rpx;
      color: #3C3C43;
      // line-height: 36rpx;
      text-align: left;
      font-style: normal;
    }

    .page-info-cell {
      display: flex;
      align-items: center;
      margin-bottom: 40rpx;

      .page-info-cell-item {
        flex: 1;
        font-family: SourceHanSansCN,
          SourceHanSansCN,
          MUJIFont2020;
        font-weight: 400;
        font-size: 24rpx;
        color: #888888;
        // line-height: 36rpx;
        text-align: left;
        font-style: normal;
      }

      .page-info-cell-value {
        flex: 1;
        font-family: SourceHanSansCN,
          SourceHanSansCN,
          MUJIFont2020;
        font-weight: 400;
        font-size: 24rpx;
        color: #3C3C43;
        // line-height: 36rpx;
        text-align: right;
        font-style: normal;
      }
    }
  }
}
