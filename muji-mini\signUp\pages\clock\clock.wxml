<!--signUp/pages/clock/clock.wxml-->
<my-page overallModal="{{overallModal}}" catch:overallModalConfirm="overallModalConfirm">
  <view class="page-container" style="background-image:url({{$cdn}}/signUp/lotteryBack1.png);">
    <custom-header isShare="{{true}}" background=" transparent" type="{{1}}" isBackHidden="{{ isBackHidden }}" color="black" />
    <scroll-view class="clock" enhanced="{{ true }}" bounces="{{ false }}" show-scrollbar="{{ false }}" scroll-y>
      <view style="margin-top: {{ menuButtonBottom }}px" style="position: relative">
        <view class="top_img">
          <view class="title1">
            <text>{{title1}}</text>
          </view>
          <view class="title2" wx:if="{{days<=7&& surplusTimes>0}}">
            <text>您已完成<text class="num">{{completeTimes}}</text>{{title2}}<text class="num">{{surplusTimes}}</text>天即可抽取惊喜礼品</text>
          </view>
          <view class="title2" wx:else>
            <text>您已完成<text class="num">{{completeTimes}}</text>{{title3}}<text class="num">{{1}}</text>次惊喜礼品抽奖机会</text>
          </view>
          <view class="rules" bind:tap="clickRules">
            <view class="rules-in">活动规则</view>
          </view>
        </view>
        <view class="clockList">
          <view wx:for="{{clockList}}" wx:key="{{index}}" class="clockItem {{item.state==1? 'isSingIn':''}} {{item.state==2?'notSingIn':''}} {{(item.days == days || (days>=7&&item.days>=7)) && !isSignIn?'today':''}}" bind:tap="buqian" data-query="{{item}}">
            <!-- item.days==days && !isSignIn 判断item.days 是否是当天 并且未打卡 显示today样式 -->
            <view class="item">
              <view wx:if="{{index===6 && item.state!=1}}">
                <view class="iconfont" style="background-image:url({{$cdn}}/signUp/gift.png);">
                </view>
              </view>
              {{item.days}}
            </view>
            <view wx:if="{{item.state == 0&&item.days != days}}" class="icon-wrap">
              <view class="icon"></view>
            </view>
            <view wx:if="{{item.state == 1}}" class="icon-wrap">
              <view class="iconfont icon-Mark"></view>
            </view>
            <text class="item-notSingIn" wx:if="{{item.state==2}}">补卡</text>
            <text class="item-notSingIn-today" wx:if="{{item.days == days && !isSignIn}}">今日</text>
          </view>
        </view>
        <view class="clock-btn">
          <!-- 打卡 -->
          <basic-button wx:if="{{!prizeDraw}}" width="{{510}}" size="large" bind:click="submit" disabled="{{isSignIn}}">
            {{btnTitle}}
          </basic-button>
          <!-- 抽奖  -->
          <basic-button wx:if="{{prizeDraw}}" width="{{510}}" size="large" bind:click="ClickPrizeDraw" disabled="{{disabled}}">
            <view class="btn" style="display: flex;justify-content: center;align-items: center;">
              <view class="timeicon" style="background-image: url({{$cdn}}/signUp/gifticon.png);"></view>
              <view>领取礼品</view>
            </view>
          </basic-button>
        </view>
        <view class="record_wrap">
          <view class="record-title">
            【打卡记录】
          </view>
          <view class="record" wx:if="{{recordList.length > 0}}">
            <block wx:for="{{recordList}}">
              <view class="record-item" data-index="{{index}}" bind:tap="detail" wx:if="{{!item.supplementTime&& item.state==1 }}">
                <view class="Day">{{item.days}}</view>
                <view wx:if="{{item.days!=7}}" class="record-item-overall">
                  <view class="title"> 整体满意度</view>
                  <view class="start">
                    <van-rate disabled-color="#E2D7BA" disabled value="{{item.satisfaction}}" bind:change="changeRate" data-index="{{index}}" size="39rpx" gutter="22rpx" color="#E2D7BA" void-color="#E2D7BA" count="{{5}}" icon="{{$cdn}}/signUp/start1.png" void-icon="{{$cdn}}/signUp/start2.png"></van-rate>
                    <view class="detail">详情<view class="iconfont icon-back"></view>
                    </view>
                  </view>

                </view>
                <view wx:else class="record-item-overall">
                  <view class="title">产品满意度</view>
                  <view class="start">
                    <van-rate disabled-color="#E2D7BA" disabled value="{{item.score}}" bind:change="changeRate" data-index="{{index}}" size="39rpx" gutter="22rpx" color="#E2D7BA" void-color="#E2D7BA" count="{{5}}"></van-rate>
                    <view class="detail" data-index="{{index}}" bind:tap="detail">详情 <view class="iconfont icon-back">
                      </view>
                    </view>
                  </view>

                </view>
              </view>
            </block>

          </view>
          <view class="noData" wx:if="{{recordList.length ==0}}">暂无打卡记录</view>
        </view>
      </view>
    </scroll-view>
    <clockPopup num="{{surplusRepairSignInTimes}}" isShow="{{showContact}}" bindclose="closeContact" />
    <!-- 活动规则 -->
    <active-rules isShow="{{showRules}}" bindclose="closeRules" />
    <!-- 分享指引 -->
    <guidePopup showGuide="{{showContact1}}" bind:finish="closeGuide" />
    <!-- 打卡完成 提示弹框 -->
    <Popup isShow="{{showPopup}}" info="{{infoLottery}}" bindclose="closePopup"></Popup>
  </view>
</my-page>