<template>
  <a-drawer :title="title" width="1200" placement="right" :closable="true" :maskClosable="false" :open="open" @close="onClose">
    <a-spin :spinning="loading">

      <a-form class="form" ref="addForm" :model="addParams" :rules="rules" :labelCol="{ style: 'width:150px' }">
        <div class="form-top-titles-common">人群包基础信息</div>
        <div class="form-top-line-common"></div>
        <a-form-item label="人群包名称" :name="['crowdDTO','crowdName']">
          <a-input placeholder="请输入" style="width:400px;" v-model:value="addParams.crowdDTO.crowdName" show-count :maxlength="20" />
        </a-form-item>
        <a-form-item label="人群包可用时间" :name="['crowdDTO','timeType']">
          <a-radio-group v-model:value="addParams.crowdDTO.timeType">
            <template v-for="(item,index) in CrowdPackTypeOptions" :key="index">
              <a-radio-button :value="item.value">{{item.label}}</a-radio-button>
            </template>
          </a-radio-group>
        </a-form-item>
        <a-form-item label=" " class="hide-required-mark" v-if="addParams.crowdDTO.timeType === 0" :name="['crowdDTO','createTime']" :hideRequiredMark="false" :colon="false">
          <a-range-picker style="width:400px;" :disabledDate="disabledStartDate" showNow v-model:value="addParams.crowdDTO.createTime" :getPopupContainer="(triggerNode) => triggerNode.parentNode" valueFormat="YYYY-MM-DD HH:mm:ss" format="YYYY-MM-DD HH:mm:ss" :show-time="{ defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')] }" :placeholder="['开始时间', '结束时间']" />
        </a-form-item>
        <div class="form-top-titles-common">圈选人群</div>
        <div class="form-top-line-common"></div>
        <ShelfCrowdConditionsFormItem v-if="open" :formType="props.type" :addType="1" :formFields="addParams" :formRules="rules.crowdDTO" :formRef="addForm" />

      </a-form>

    </a-spin>
    <template #footer v-if="!disabled">
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="ok" :loading="loading">确定</a-button>
      </a-space>
    </template>

  </a-drawer>
</template>
<script setup>
import ShelfCrowdConditionsFormItem from '@/views/mall/shelfManagement/giftRack/components/ShelfCrowdConditionsFormItem.vue'
import { crowdsave, crowdUpdate, crowdInfo } from '@/http/index.js'
import { useGlobalStore } from '@/store'
import { message, Modal } from "ant-design-vue";
import Clipboard from 'clipboard';
import dayjs from "dayjs";
import { CrowdPackTypeOptions1, CrowdPackTypeOptions } from '@/utils/dict-options'
const $router = useRouter();
const global = useGlobalStore()
const addForm = ref(null)
import { cloneDeep } from 'lodash'
import { onMounted } from 'vue';
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  // 操作抽屉打开关闭
  visible: {
    type: Boolean,
    default: false
  },
  // 数据ID
  id: {
    type: String || Number,
    default: ''
  },
  // 操作类型
  type: {
    type: Number,
    default: 0, // 0-新增 1-编辑  2-查看
  }

})
// 置灰
const disabled = computed(() => {
  return props.type == 2
})

// 标题
const title = computed(() => {
  return ['新建', '编辑', '查看'][props.type] + '人群包'
})

const { open, addParams, rules, loading, } = toRefs(reactive({
  open: props.visible,

  loading: false,

  addParams: {
    crowdDTO: {
      crowdType: 0,
      // crowdConditionList: [],
      timeType: 0,
      createTime: [],
      crowdName: '',

    }


  },
  rules: {
    crowdDTO: {
      crowdName: [{ required: true, message: '请输入人群包名称', trigger: ['blur', 'change'] }],
      timeType: [{ required: true, message: '请选择人群包时间类型', trigger: ['blur', 'change'] }],
      createTime: [
        { required: true, message: '请选择人群包可用时间', trigger: ['blur', 'change'] },
        // { validator: validatesendEndTime, trigger: ['blur', 'change'] },
      ]
    }


  }
})
);
// onMounted(() => {
//     initData()
// })
// 禁用日期的逻辑
const disabledStartDate = (date) => {
  const today = dayjs().startOf('day'); // 获取今天的日期

  if (addParams.value.crowdDTO.createTime[1]) {
    return dayjs(date).isBefore(today, 'day')
  } else {
    // 如果结束时间没有值，则只考虑45天后的日期
    return dayjs(date).isBefore(today, 'day')
  }
};

function validatesendEndTime() {
  if (addParams.value.crowdDTO.createTime[0] && addParams.value.crowdDTO.createTime[1]) {
    const DayData = dayjs(new Date())
    const startsendEndTime = dayjs(addParams.value.crowdDTO.createTime[0]);
    const endsendEndTime = dayjs(addParams.value.crowdDTO.createTime[1]);
    if (startsendEndTime.isBefore(DayData)) {

      return Promise.reject("开始时间不能晚于当前时间");

    } else {
      return Promise.resolve();
    }
  } else {
    return Promise.resolve();
  }
}

function getDefaultFormFields() {
  return {
    crowdType: 0,
    // crowdConditionList: [{
    //   conditionJudge: '',
    //   crowdConditionRowList: [
    //     {
    //       conditionJudge: '',
    //       conditions: []
    //     },
    //     {
    //       conditionJudge: 1,
    //       conditions: []
    //     }
    //   ]
    // },],
    timeType: 0,
    createTime: []
  }
}
watch(() => props.visible, (value) => {
  // 弹窗操作
  open.value = props.visible
  Object.keys(addParams.value.crowdDTO).forEach(key => (delete addParams.value.crowdDTO[key]))
  Object.assign(addParams.value.crowdDTO, getDefaultFormFields())
  console.log("🚀 ~ watch ~ addParams.value:", addParams.value)
  addForm.value?.resetFields()
  if (open.value) {

    initData()
  }
})


//所有接口调取出
const initData = async () => {
  const promiseArr = []


  if (props.id) {

    promiseArr.push(crowdInfo({ id: props.id }))
  }

  // 如果有新的接口就在这里push
  loading.value = true
  try {
    const [crowdInfoA] = await Promise.all(promiseArr)
    // console.log("🚀 ~ initData ~ crowdInfo:", crowdInfoA)

    if (crowdInfoA) {
      let newData = crowdInfoA.data
      if (!newData.crowdConditionList) {
        newData.crowdConditionList = [
          {
            conditionJudge: 0,
            crowdConditionRowList: [
              {
                conditionJudge: 0,
                conditions: []
              },
              {
                conditionJudge: 1,
                conditions: []
              }
            ]
          },
        ]
      } else if (!newData.crowdConditionList.length) {
        newData.crowdConditionList = [
          {
            conditionJudge: 0,
            crowdConditionRowList: [
              {
                conditionJudge: 0,
                conditions: []
              },
              {
                conditionJudge: 1,
                conditions: []
              }
            ]
          },
        ]
      }
      if (newData.endTime && newData.startTime) {

        newData.createTime = [newData.startTime, newData.endTime]
      }
      if (!newData.crowdImportResultDTO) {
        newData.crowdImportResultDTO = {
          memberCodeList: [],
          fileName: ""
        }
      } else if (!newData.crowdImportResultDTO.memberCodeList) {
        newData.crowdImportResultDTO['memberCodeList'] = []
      }
      addParams.value.crowdDTO = newData
    }
    loading.value = false
  } catch (error) {
    loading.value = false
    console.error('获取数据失败:', error)
  }
}
function exportEmployee() {

}




// 关闭弹窗
const onClose = () => {
  emit('cancel')
}
function updateSortFields(data) {
  // 遍历 crowdConditionList 数组

  data.crowdDTO.crowdConditionList.forEach((crowdCondition, crowdIndex) => {
    // 设置 crowdCondition 的 sort 字段为当前下标加1
    crowdCondition.sort = crowdIndex + 1;

    // 遍历 crowdConditionRowList 数组
    crowdCondition.crowdConditionRowList.forEach((row, rowIndex) => {
      // 设置 row 的 sort 字段为当前下标加1（基于 crowdConditionRowList 的索引）
      // 注意：这里的加1是基于 crowdConditionRowList 的内部索引，如果你想要全局唯一的sort值，
      // 你可能需要采用其他策略，比如将 crowdIndex 和 rowIndex 结合起来。
      row.sort = rowIndex + 1;

      // 如果需要全局唯一的sort值，可以考虑如下方式：
      // row.sort = (crowdIndex * 1000) + (rowIndex + 1); // 假设每个crowdCondition最多有1000行
    });
  });

  return data;
}
// 确定
const ok = () => {
  // console.log(addParams.value, 'addParams.value)addParams.value)addParams.value)addParams.value)');

  addForm.value.validate().then(res => {

    let params = cloneDeep(addParams.value)
    if (params.crowdDTO.timeType === 0) {
      if (params.crowdDTO.createTime.length > 0) {
        params.crowdDTO.startTime = params.crowdDTO.createTime[0]
        params.crowdDTO.endTime = params.crowdDTO.createTime[1]
      }
    } else {
      params.crowdDTO.startTime = null
      params.crowdDTO.endTime = null
    }
    // console.log("🚀 ~ addForm.value.validate ~ params:", params)
    if (params.crowdDTO.crowdType == 2) {
      params.crowdDTO.crowdConditionList = []
    } else if (params.crowdDTO.crowdType == 1) {
      params.crowdDTO.crowdConditionList = []
    } else {
      params = updateSortFields(params)
    }
    // console.log("🚀 ~ addForm.value.validate ~ params.crowdDTO.timeType:", params)


    delete params.crowdDTO.createTime
    loading.value = true
    if (props.id) {
      // console.log('编辑');
      crowdUpdate(params).then(res => {
        message.success(res.msg);
        emit('ok', props.id)
      }).finally(() => {
        loading.value = false
      })
    } else {
      crowdsave(params).then(res => {
        message.success(res.msg);
        emit('ok', props.id)
      }).finally(() => {
        loading.value = false
      })
    }

  })
}



</script>

<style scoped lang="scss">
:deep(.searchForm .ant-form-item) {
  margin-bottom: 16px;
}
//影藏红的点
.hide-required-mark {
  :deep(.ant-form-item-required) {
    display: none;
  }
}
:deep(.anticon-eye-invisible) {
  display: none;
}

.member-role {
  background: #f2f2f2;
  margin-bottom: 20px;
  padding: 5px 10px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &:nth-child(odd) {
    margin-right: 35px;
  }
}
</style>
