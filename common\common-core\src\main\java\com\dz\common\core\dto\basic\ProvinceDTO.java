package com.dz.common.core.dto.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 省份信息DTO
 * @author: Handy
 * @date:   2023/03/15 15:47
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "省份信息")
public class ProvinceDTO {

    @ApiModelProperty(value = "省份编码")
    private String code;
    @ApiModelProperty(value = "省份名称")
    private String name;
    @ApiModelProperty(value = "城市列表")
    private List<CityDTO> citys;

    public ProvinceDTO(String code, String name, List<CityDTO> citys) {
        this.code = code;
        this.name = name;
        this.citys = citys;
    }
}
