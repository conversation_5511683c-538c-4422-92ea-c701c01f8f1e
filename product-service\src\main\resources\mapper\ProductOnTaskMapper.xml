<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.product.mapper.ProductOnTaskMapper">

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
        id,
		shelf_product_id,
  	    shelf_id,
  	    shelf_name,
  	    product_id,
  	    product_name,
  	    on_type,
  	    on_time,
  	    on_days,
  	    on_inventory,
  	    state,
  	    is_deleted,
  	    shelf_state,
  	    tenant_id,
  	    creator,
  	    created,
  	    modified,
  	    modifier
	</sql>

	<insert id="insertBatch" keyProperty="list.id" useGeneratedKeys="true" keyColumn="id"
			parameterType="java.util.List">
		insert into product_on_task
		(shelf_product_id,shelf_id,shelf_name,product_id,product_name,on_type,on_time,on_days,on_inventory,state,tenant_id,creator,created,modified,modifier)
		values
		<foreach collection="list" item="item" separator=",">
			(#{item.shelfProductId},#{item.shelfId},#{item.shelfName},#{item.productId},#{item.productName},#{item.onType},#{item.onTime},#{item.onDays},#{item.onInventory},#{item.state},#{item.tenantId},#{item.creator},now(),now(),#{item.modifier})
		</foreach>
	</insert>

	<!-- 查询示例 -->
	<select id="selectDemo" parameterType="java.lang.Long" resultType="com.dz.ms.product.entity.ProductOnTask">
		select
		<include refid="Base_Column_List"/>
		from product_on_task
		where id = #{id,jdbcType=BIGINT}
		AND tenant_id = #{tenantId}
	</select>

</mapper>
