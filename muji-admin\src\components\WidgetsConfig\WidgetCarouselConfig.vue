<template>
  <div v-if="formFields.id">
    <div label="轮播">
      <VueDraggable :list="list" item-key="index" :drag-selector="'.icon'" :filter="'.disabled'" animation="300">
        <template #item="{ element, index }">
          <div>
            <div style="" class="disabled">
              <a-button type="link" :disabled="5 == list.length" @click="thisMethods.addImg()">新增</a-button>
              <a-button type="link" :disabled="list.length - 1 < 1" @click="thisMethods.delImg(index)">删除</a-button>
            </div>
            <div class="drag-item">
              <div>
                <SvgIcon name="drag-dot-vertical" class="icon" width="20px" height="20px" />
              </div>
              <div>
                <a-form-item style="margin-top:20px" :rules="formRules.imgSrc" :label="`图${index + 1}`" :name="['content','list', index, 'imgSrc']" class="disabled">
                  <uploadImg :max="10" :width="100" :height="100" :imgUrl="element.imgSrc" :form="list" :path="index + '.' + 'imgSrc'" @success="(data) => uploadSuccess(data, index)" />
                </a-form-item>
                <a-form-item label="链接组件" :name="['content','list', index, 'imgLinks']" class="disabled" style="flex:1">
                  <selectShop :shelfId="props.shelfId" :showList="[8]" :link="element.imgLinks[0]" btnType="link" btnTitle="配置跳转链接" @ok="(data) => element.imgLinks[0] = data.data"></selectShop>
                  <div class="global-tip">建议尺寸：670*400；比例：5：3</div>
                </a-form-item>
              </div>
            </div>
          </div>
        </template>
      </VueDraggable>
    </div>
    <!-- <template v-if="formFields.content.list.length"> -->
    <!-- <a-form-item label="轮播链接"> -->
    <!--<div>
            <BaseUiLinkSelector
              v-for="(listItem,index) in item.list"
              :key="index" :link="listItem"
              :index="index" v-model:currentIndex="thisFields.currentIndex"
            />
          </div>-->
    <!-- </a-form-item> -->

    <!-- </template> -->
    <!-- </a-form> -->
  </div>
</template>

<script setup>
import { v4 } from 'uuid'
import { computed, reactive, ref, nextTick } from 'vue'
import _ from "lodash"
import { cloneDeep } from 'lodash'
import selectShop from './selectShop.vue'
const props = defineProps({
  formFields: {
    type: Object,
    default: () => ({})
  },
  formRules: {
    type: Object,
    default: () => ({})
  },
  formRef: {
    type: Object,
    default: () => ({})
  },
  shelfId: {
    type: [String, Number],
    default: ''
  },
})

const list = computed(() => {
  return props.formFields.list
})

const baseLinkSelectorData = computed(() => {
  const { hrefName, name, data } = props.formFields.list[thisFields.currentIndex]
  return [{ hrefName, name, data }]
})



const confirm = async e => {
  const list = []
  e.map(v => ({ imgSrc: v, hrefName: '', name: '', data: {} })).forEach(v => {
    const obj = props.formFields.list.find(v2 => (v2.imgSrc === v.imgSrc))
    obj ? list.push(obj) : list.push(v)
  })
  props.formFields.list = list
}

const thisFields = reactive({
  height: 120,
  currentIndex: 0,
  mapStyle: {},
})

const thisMethods = {
  addImg() {
    list.value.push({
      imgSrc: ``, imgLinks: [
        {
          id: v4(),
          operateType: null,
          modalIndex: null,
          anchorIndex: null,
          copyText: '',
          linkType: 1,
          linkName: '',
          linkUrl: '',
          appid: '',
          path: '',
          url: '',
        }
      ],
    })
  },
  delImg(index) {
    list.value.splice(index, 1);
  },
  setFormFields() {
    if (!props.formFields.list || !props.formFields.list.length) {
      props.formFields.list = [{
        imgSrc: ``, imgLinks: [
          {
            id: v4(),
            operateType: null,
            modalIndex: null,
            anchorIndex: null,
            copyText: '',
            linkType: 1,
            linkName: '',
            linkUrl: '',
            appid: '',
            path: '',
            url: '',
          }
        ],
      }]
    }
  },
  setFormRules() {
    const formRules = props.formRules
    formRules.imgSrc = formRules.imgSrc || [{ required: true, message: '本项必选' }]
  }
}

// 上传图片 视频
const uploadSuccess = async (data, index) => {
  let { form, path: key, imgUrl } = data;
  _.set(form, key.split('.'), imgUrl)

  // 等待DOM更新后再校验
  await nextTick()
  if (props.formRef) {
    props.formRef.validateFields([['content', 'list', index, 'imgSrc']])
    props.formRef.clearValidate([['content', 'list', index, 'imgSrc']])
  }
}

thisMethods.setFormFields()
thisMethods.setFormRules()

</script>

<style scoped lang="scss">
.reset ::v-deep .ui-image-scale.active {
  border: 1px solid rgba($borderColor, 0.8);
}

.drag-item {
  display: flex;
  align-items: center;
  background-color: #fbfbfb;

  .icon {
    cursor: move;
  }
}
</style>
