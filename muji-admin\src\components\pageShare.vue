<template>
  <a-form-item label="支持分享" name="addParams.isShare" v-if="showShare">
    <a-switch :unCheckedValue="0" :checkedValue="1" v-model:checked="addParams.isShare" />
  </a-form-item>
  <template v-if="addParams.isShare">
    <a-form-item label="分享标题" name="addParams.shareTitle">
      <a-textarea placeholder="请输入" v-model:value="addParams.shareTitle" show-count :maxlength="30" />
    </a-form-item>
    <a-form-item label="分享封面">
      <uploadImg :max="10" :width="100" :height="100" :imgUrl="addParams.shareImg" :form="addParams" path="shareImg" :disabled="disabled" @success="uploadSuccess" />
      <div class="global-tip">建议长宽比例4:5，支持jpg/png格式，大小不超过2M</div>
    </a-form-item>
    <a-form-item label="分享路径">
      <a-textarea placeholder="请输入" v-model:value="addParams.sharePath" :maxlength="100" />
      <div class="global-tip">以/开头，分享需要加上分享人和渠道参数</div>
    </a-form-item>
  </template>

</template>
<script setup>
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  addParams: {
    type: Object,
    default() {
      return {}
    }
  },
  // 是否显示分享切换按钮
  showShare: {
    type: Boolean,
    default: true
  }
})

// 上传图片 视频
const uploadSuccess = async (data) => {
  let { form, path, imgUrl } = data;
  form[path] = imgUrl
}


</script>

<style>
</style>
