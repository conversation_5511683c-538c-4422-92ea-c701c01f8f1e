package com.dz.ms.product.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.IdNumberDTO;
import com.dz.common.core.dto.product.CpStaticDTO;
import com.dz.common.core.dto.product.CpStaticParamDTO;
import com.dz.common.core.dto.product.OdsItemDTO;
import com.dz.common.core.dto.product.ProductCouponDTO;
import com.dz.ms.product.dto.CrmProductListDTO;
import com.dz.ms.product.dto.ProductDTO;
import com.dz.ms.product.dto.req.CrmProductListParamDTO;
import com.dz.ms.product.dto.req.ProductChiefInfoParamDTO;
import com.dz.ms.product.dto.res.ProductChiefInfoDTO;
import com.dz.ms.product.entity.Product;
import org.apache.ibatis.annotations.Param;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * 商品信息接口
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:25
 */
public interface ProductService extends IService<Product> {

    /**
     * 分页查询商品信息
     *
     * @param param
     * @return PageInfo<CrmProductListDTO>
     */
    PageInfo<CrmProductListDTO> getProductList(CrmProductListParamDTO param);

    /**
     * 分页查询商品信息
     *
     * @param param
     * @return
     */
    PageInfo<ProductChiefInfoDTO> getProductChiefInfoList(ProductChiefInfoParamDTO param);

    /**
     * 根据id列表查询商品信息
     * @param param
     * ids id列表
     * @return List<ProductChiefInfoDTO>
     */
    List<ProductChiefInfoDTO> getProductChiefInfoListByIds(ProductChiefInfoParamDTO param);

    /**
     * 根据ID查询商品信息
     *
     * @param id
     * @return ProductDTO
     */
    public ProductDTO getProductById(Long id);

    /**
     * 保存商品信息
     *
     * @param param
     * @return Long
     */
    public Long saveProduct(ProductDTO param);

    /**
     * 根据ID删除商品信息
     *
     * @param param
     */
    public void deleteProductById(IdCodeDTO param);

    /**
     * 根据ID修改启停状态
     */
    Integer updateStateById(IdNumberDTO param);

    /**
     * 导出商品列表
     */
    void exportProductList(String jsonParam, String reportCode, String fileName, String fileExt, Long downloadCenterId);

    /**
     * 根据ids查询少量字段
     */
    List<ProductDTO> selLessListByIds(List<Long> allProductIdList);

    List<ProductCouponDTO> getProductListByPdType();

    int updateStatic(Long productId, Integer number);

    void uploadMujiGoodsAndCouponsAndOrdersExcel(String afterDate) throws ParseException;

    PageInfo<CpStaticDTO> cpStatic(CpStaticParamDTO param);


    void exportCpStatic(DownloadAddParamDTO param);

    void getSftpFile() throws IOException;

    List<OdsItemDTO> selectBySftpProductItemId(String itemId);
}
