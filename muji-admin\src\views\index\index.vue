<template>
  <layout>
    <template v-slot="{ height }">

  
      <div v-for="(item, index) in iconList" :key="index" style="height:height+'px';overflow-y: scroll;display: inline-block;" @click="selectedIcon(item)">

        <div>
          <SvgIcon :name="item" style="height: 30px; width: 16px" />
        </div>
        <span>{{ item }}</span>
      </div>

    </template>
  </layout>
</template>

<script setup>
const icons = import.meta.glob('../../assets/icons/*.svg')
const iconList = []

onMounted(async () => {
  // Check if icons directory exists and has SVG files
  if (Object.keys(icons).length === 0) {
    console.warn('No SVG icons found in assets/icons/svg directory')
    return
  }

  // Try to load icons
  for (const key in icons) {
    if (Object.hasOwnProperty.call(icons, key)) {
      iconList.push(key.slice(key.lastIndexOf('/') + 1, -4))
    }
  }

  if (iconList.length === 0) {
    console.warn('Failed to load any icons from assets/icons/svg')
  } else {
    console.log('Loaded icons:', iconList)
  }
})

</script>

<style lang="scss" scoped></style>
