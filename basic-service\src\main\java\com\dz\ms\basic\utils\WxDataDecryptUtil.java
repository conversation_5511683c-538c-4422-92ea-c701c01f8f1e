package com.dz.ms.basic.utils;

import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.annotation.Value;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.AlgorithmParameters;
import java.security.InvalidAlgorithmParameterException;
import java.security.Key;
import java.security.NoSuchProviderException;
import java.security.Security;

/**
 * 微信数据解密
 * <AUTHOR>
 */
@Slf4j
public class WxDataDecryptUtil {

    public static boolean initialized = false;
    /**  
     * AES解密  
     * @param content 密文  
     * @return  
     * @throws InvalidAlgorithmParameterException   
     * @throws NoSuchProviderException   
     */    
    public static byte[] decrypt(byte[] content, byte[] keyByte, byte[] ivByte,String cipherStr) {
        initialize();    
        try {    
            Cipher cipher = Cipher.getInstance(cipherStr);
            Key sKeySpec = new SecretKeySpec(keyByte, "AES");
            cipher.init(Cipher.DECRYPT_MODE, sKeySpec, generateIV(ivByte));
            byte[] result = cipher.doFinal(content);    
            return result;    
        } catch (Exception e) {
            log.error("微信数据解密失败",e);
        }
        return null;    
    }      
        
    public static void initialize(){      
        if (initialized) {
            return;
        }
        Security.addProvider(new BouncyCastleProvider());
        initialized = true;      
    }

    /**
     * 生成iv
     * @param iv
     * @return
     * @throws Exception
     */
    private static AlgorithmParameters generateIV(byte[] iv) throws Exception{
        AlgorithmParameters params = AlgorithmParameters.getInstance("AES");      
        params.init(new IvParameterSpec(iv));      
        return params;      
    }       
  
  
}  