Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    currentCity: {
      type: String,
      value: '',
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    storeName: '',
  },

  /**
   * 组件的方法列表
   */
  methods: {
    showCategoryDialog() {
      this.triggerEvent('showDialog')
    },
    // 跳转微信地址
    showCity() {
      console.log('点击跳转城市搜索页')
      this.triggerEvent('showCity')
    },
    handleSearchStore(e) {
      const {
        detail
      } = e;
      this.triggerEvent("filter", {
        storeName: detail
      })
    },
    handleChangeStoreName(e) {
      const {
        detail
      } = e;
      this.triggerEvent("filter", {
        storeName: detail
      })
    }
  }
})
