package com.dz.ms.sales.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Data;
import org.springframework.data.annotation.Id;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**

 */
@Data
@Table("任务埋点")
@TableName(value = "t_user_task")
public class UserTask implements Serializable {
    private static final long serialVersionUID = 1L;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "主键id")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "任务id")
    private Long taskId;

    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "用户id")
    private Long userId;

    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    @Columns(type = ColumnType.INT,length = 11,isNull = true,comment = "用户数量")
    private Integer userCount;
    @Columns(type = ColumnType.BIGINT,length = 11,isNull = false,comment = "租户id")
    private Long tenantId = 1L;
}
