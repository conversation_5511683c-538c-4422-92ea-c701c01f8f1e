<template>
  <a-modal v-model:open="visible" :footer="null" width="1200px" @cancel="handleCancel">
    <a-form :model="searchFields" layout="inline">
      <a-form-item label="商品名称" name="productName">
        <a-input
          placeholder="请输入"
          allow-clear
          v-model:value="searchFields.productName"
          allowClear
          @keyup.enter="whenClickSearch"
        ></a-input>
      </a-form-item>
      <a-space>
        <a-button type="primary" @click="whenClickSearch"> 查询 </a-button>
        <a-button @click="whenClickReset"> 重置 </a-button>
        <a-button type="primary" @click="taskAddModalVisible = true"> 选择需要处理的商品 </a-button>
      </a-space>
    </a-form>
    <div style="margin-bottom: 12px">
      <a-checkbox v-model:checked="hideDoneTask" @change="whenClickReset">
        隐藏已完成任务
      </a-checkbox>
    </div>
    <a-table
      :indentSize="20"
      row-key="id"
      :scroll="{ scrollToFirstRowOnChange: true, x: '100%' }"
      :dataSource="dataSource"
      :columns="tableHeader"
      :pagination="pagination"
      :loading="loading"
      @change="whenPaginationChange"
    >
      <template #bodyCell="{ text, record, index, column }">
        <template v-if="column.dataIndex === 'taskDesc'">
          <a-button
            :disabled="!record._isEdit"
            class="ml5"
            type="link"
            @click="openTimeHandler(record)"
          >
            {{ record.taskDesc }}
          </a-button>
        </template>
        <template v-else-if="column.dataIndex === 'onInventory'">
          <div class="ui-child-form-item-mb0">
            <a-form-item label="" name="onInventoryTemp">
              <a-input
                :disabled="!record._isEdit"
                placeholder="请输入"
                :allowClear="true"
                v-model:value="record.onInventoryTemp"
                @blur="onInventoryTempHandler(record)"
              />
            </a-form-item>
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'action'">
          <!-- state	状态 0待执行 1已执行 2执行中  3 待编辑-->
          <a-button
            type="link"
            @click="record._isEdit = true"
            :disabled="record._isEdit || record.state === 1"
            >编辑</a-button
          >
          <a-divider type="vertical" />
          <a-button type="link" @click="handleEditOk(record)" :disabled="!record._isEdit"
            >执行任务</a-button
          >
          <a-divider type="vertical" />
          <a-button type="link" @click="handleEditCancel(record)" :disabled="!record._isEdit"
            >取消</a-button
          >
          <a-divider type="vertical" />
          <a-button
            class="ml5"
            type="link"
            @click="handleDel(record, index)"
            :disabled="record.canDelete !== 1"
            >删除</a-button
          >
        </template>
      </template>
    </a-table>
  </a-modal>
  <TaskAddModal v-model:visible="taskAddModalVisible" :lotteryId="lotteryId" @ok="refresh" />
  <TaskTimePickerModal
    v-model="timeHandlerVisible"
    @ok="timeHandlerOk"
    :record="thisFields.record"
  />
</template>
<script setup>
import TaskAddModal from './TaskAddModal.vue'
import TaskTimePickerModal from './TaskTimePickerModal.vue'

import { usePagination } from 'vue-request'
import { ref, watch } from 'vue'
import { message } from 'ant-design-vue'

import { getLotteryOnTaskList, editLotteryPrize, delLotteryPrize } from '@/http/index.js'
import { INVENTORY_TASK_STATE } from '../utils/constant'

const visible = defineModel('visible', { type: Boolean, required: true })
const props = defineProps(['lotteryId'])
const emits = defineEmits(['changeInventory'])

watch(
  () => visible.value,
  (value) => {
    console.log('value: ', value)
    if (value) {
      whenClickReset()
    }
  }
)

const pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ['bottomLeft']
  }
})
const total = ref(0)

let changedInventory = false // 是否修改了库存
const searchFields = reactive(getDefaultSearchFields())
const hideDoneTask = ref(true) // 隐藏已完成任务
const taskAddModalVisible = ref(false)
const timeHandlerVisible = ref(false)
const thisFields = reactive({
  hideDone: true,
  record: {},
  ShelfProductExpectUpDownShelfTimeDialogOpen: false,
  loading: false,
  open: false
})

const tableHeader = [
  { title: '任务创建时间', dataIndex: 'created', align: 'center', width: 120 },
  { title: '奖品名称', dataIndex: 'lotteryPrizeName', align: 'center', ellipsis: true, width: 100 },
  { title: '期望上架时间', dataIndex: 'taskDesc', align: 'center', width: 120 },
  { title: '上下架库存数量', dataIndex: 'onInventory', align: 'center', width: 120 },
  { title: '任务状态', dataIndex: '_state', align: 'center', width: 100 },
  { title: '操作', dataIndex: 'action', align: 'center', width: 140, fixed: 'right' }
]

function getDefaultSearchFields() {
  return { lotteryPrizeName: '' }
}
function openTimeHandler(record) {
  thisFields.record = record
  timeHandlerVisible.value = true
}
function timeHandlerOk(e) {
  Object.assign(thisFields.record, {
    onTypeTemp: e.onTypeTemp || 1,
    onTimeTemp: e.onTimeTemp,
    onDaysTemp: e.onDaysTemp,
    taskDesc: formatTaskDesc(e)
  })
}

function onInventoryTempHandler(record) {
  const match = String(record.onInventoryTemp).match(/(0)|(-?[1-9]\d*)/gi) || []
  record.onInventoryTemp = match[0] || '0'
}
async function handleEditOk(record) {
  const res = await editLotteryPrize({
    id: record.id,
    lotteryId: props.lotteryId,
    lotteryPrizeId: record.lotteryPrizeId,
    lotteryPrizeName: record.prizesName,
    productId: record.productId,
    onType: record.onTypeTemp,
    onTime: record.onTimeTemp,
    onDays: record.onDaysTemp,
    onInventory: record.onInventoryTemp
  })
  message.success(res.msg)
  refresh()
  changedInventory = true
}
function handleEditCancel(record) {
  record.onTypeTemp = record.onType || 1
  record.onTimeTemp = record.onTime
  record.onDaysTemp = record.onDays
  record.onInventoryTemp = record.onInventory
  record._isEdit = false
}
async function handleDel(record, index) {
  const res = await delLotteryPrize({ id: record.id })
  message.success(res.msg)
  refresh()
}

const {
  data: dataSource,
  run,
  loading,
  pageSize,
  current,
  refresh
} = usePagination(
  (param) => {
    const params = Object.assign(
      { hideDone: Number(hideDoneTask.value), lotteryId: props.lotteryId },
      param,
      searchFields
    )
    return getLotteryOnTaskList(params)
  },
  {
    manual: true, // 修改为false 让其自动执行
    pagination: {
      currentKey: 'pageNum', // 通过该值指定接口 当前页数 参数的属性值
      pageSizeKey: 'pageSize', // 通过该值指定接口 每页获取条数 参数的属性值
      totalKey: 'data.data.count' // 指定 data 中 total 属性的路径
    },
    formatResult: (res) => {
      // 返回数据格式化
      total.value = res.data.count
      if (current.value > 1 && res.data.list.length === 0) {
        run({ pageNum: current.value - 1, pageSize: pageSize.value })
      }
      return res.data.list.map((item) => {
        Object.assign(item, {
          _state: INVENTORY_TASK_STATE[item.state],
          onTypeTemp: item.onType,
          onTimeTemp: item.onTime,
          onDaysTemp: item.onDays,
          onInventoryTemp: item.onInventory
        })
        item.taskDesc = formatTaskDesc(item)
        return item
      })
    }
  }
)

function whenPaginationChange(pag) {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}
function whenClickSearch() {
  if (!props.lotteryId) return
  run({ pageNum: 1, pageSize: pageSize.value })
}
function whenClickReset() {
  Object.assign(searchFields, getDefaultSearchFields())
  whenClickSearch()
}
function formatTaskDesc(item) {
  return ['编辑', '实时', item.onTimeTemp, `每${item.onDaysTemp}天`][item.onTypeTemp]
}

// 弹窗关闭
function handleCancel() {
  if (changedInventory) {
    emits('changeInventory')
  }
  changedInventory = false
}
</script>

<style scoped lang="scss">
.ant-form {
  padding: 20px 20px 20px 0px;
  justify-content: space-between;
}
</style>
