package com.dz.ms.product.controller;

import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.enums.LogType;
import com.dz.ms.product.dto.ShelfTagDTO;
import com.dz.ms.product.dto.req.ShelfTagSaveParamDTO;
import com.dz.ms.product.dto.res.ShelfTagGroupResDTO;
import com.dz.ms.product.service.ShelfTagService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "货架标签表")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class ShelfTagController {

    @Resource
    private ShelfTagService shelfTagService;

    /**
     * 根据货架ID查询货架标签组
     * @param shelfId 货架ID
     * @return result<List<ShelfTagGroupResDTO>
     */
    @ApiOperation("根据货架ID查询货架标签组")
    @GetMapping(value = "/app/shelf_tag/tag_group_list")
    public Result<List<ShelfTagGroupResDTO>> getTagGroupList(@RequestParam("shelfId") Long shelfId) {
        Result<List<ShelfTagGroupResDTO>> result = new Result<>();
        List<ShelfTagGroupResDTO> list = shelfTagService.getTagGroupList(shelfId);
        result.setData(list);
        return result;
    }

    /**
     * 根据货架ID查询货架一级标签
     * @param shelfId 货架ID
     * @return result<List<ShelfTagDTO>
     */
    @ApiOperation("根据货架ID查询货架一级标签")
    @GetMapping(value = "/crm/shelf_tag/one_tag")
    public Result<List<ShelfTagDTO>> getOneTag(@RequestParam("shelfId") Long shelfId) {
        Result<List<ShelfTagDTO>> result = new Result<>();
        List<ShelfTagDTO> list = shelfTagService.getOneTag(shelfId);
        result.setData(list);
        return result;
    }

    /**
     * 根据货架ID,一级标签ID查询货架二级标签
     * @param shelfId 货架ID
     * @param tagId 一级标签ID
     * @return result<List<ShelfTagDTO>
     */
    @ApiOperation("根据货架ID,一级标签ID查询货架二级标签")
    @GetMapping(value = "/crm/shelf_tag/two_tag")
    public Result<List<ShelfTagDTO>> getOneTag(@RequestParam("shelfId") Long shelfId,@RequestParam("tagId") Long tagId) {
        Result<List<ShelfTagDTO>> result = new Result<>();
        List<ShelfTagDTO> list = shelfTagService.getTwoTag(shelfId,tagId);
        result.setData(list);
        return result;
    }

    /**
     * 保存一级货架标签
     * @param param 入参
     * @return result<String>
     */
    @SysLog(value = "保存一级货架标签", type = LogType.OPERATELOG)
    @ApiOperation("保存一级货架标签")
    @PostMapping(value = "/crm/shelf_tag/one_tag_save")
    public Result<String> saveOneShelfTag(@RequestBody ShelfTagSaveParamDTO param) {
        Result<String> result = new Result<>();
        shelfTagService.saveOneShelfTag(param);
        return result;
    }

    /**
     * 保存二级货架标签
     * @param param 入参
     * @return result<String>
     */
    @SysLog(value = "保存二级货架标签", type = LogType.OPERATELOG)
    @ApiOperation("保存二级货架标签")
    @PostMapping(value = "/crm/shelf_tag/two_tag_save")
    public Result<String> saveTwoShelfTag(@RequestBody ShelfTagSaveParamDTO param) {
        Result<String> result = new Result<>();
        shelfTagService.saveTwoShelfTag(param);
        return result;
    }

}
