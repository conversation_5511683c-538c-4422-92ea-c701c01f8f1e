package com.dz.ms.product.dto.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 货架商品小程序回参
 *
 * @author: fei
 * @date: 2024/12/06 16:25
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@ApiModel(value = "货架商品小程序回参")
public class ShelfProductMiniResDTO {

    @ApiModelProperty(value = "主键ID")
    private Long shelfProductId;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "商品ID")
    private Long productId;
    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "商品主图")
    private String shelfImg;
    @ApiModelProperty(value = "商品在货架上的位置 越大越靠后")
    private Integer onShelfIndex;
    @ApiModelProperty(value = "商品类型 1实物商品 2电子券")
    private Integer pdType;
    @ApiModelProperty(value = "发货方式 1线下使用 2邮寄")
    private Integer deliveryType;
    @ApiModelProperty(value = "展示方式 1积分加价购 2商品兑换")
    private Integer showType;
    @ApiModelProperty(value = "兑换积分")
    private Integer costPoint;
    @ApiModelProperty(value = "积分划线价")
    private Integer prePoint;
    @ApiModelProperty(value = "1积分+金额时 仍需支付的金额")
    private BigDecimal costPrice;
    @ApiModelProperty(value = "金额是否展示在货架列表")
    private Integer costPriceOnShelf;
    @ApiModelProperty(value = "当前库存")
    private Integer currentInventory;
    @ApiModelProperty(value = "角标字符串 多角标逗号分隔")
    private List<String> superscript;
    @ApiModelProperty(value = "活动角标字符串")
    private List<String> superscriptCampaign;
    @ApiModelProperty(value = "是否活动产品 1:是 空值或者其他:否")
    private Integer isCampaign;
    @ApiModelProperty(value = "是否有活动产品权限 1:是 空值或者其他:否")
    private Integer isCampaignAuth;
    @ApiModelProperty(value = "库存售罄组件配置")
    private String inventoryContent;
    @ApiModelProperty(value = "规则组件配置(非人群包用户提示)")
    private String ruleContent;
    

}
