package com.dz.ms.adaptor.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.IdNumberDTO;
import com.dz.ms.adaptor.dto.ScheduledTaskConfigDTO;
import com.dz.ms.adaptor.entity.ScheduledTaskConfig;

/**
 * 定时任务配置表接口
 * @author: 
 * @date:   2025/03/11 15:01
 */
public interface ScheduledTaskConfigService extends IService<ScheduledTaskConfig> {

	/**
     * 分页查询定时任务配置表
     * @param param
     * @return PageInfo<ScheduledTaskConfigDTO>
     */
    PageInfo<ScheduledTaskConfigDTO> getScheduledTaskConfigList(ScheduledTaskConfigDTO param);

    /**
     * 根据ID查询定时任务配置表
     * @param id id
     * @param isThrow 是否抛出异常
     * @return ScheduledTaskConfigDTO
     */
    ScheduledTaskConfigDTO getScheduledTaskConfigById(Long id,boolean isThrow);

    /**
     * 保存定时任务配置表
     * @param param
     * @return Long
     */
    Long saveScheduledTaskConfig(ScheduledTaskConfigDTO param);

    /**
     * 根据ID删除定时任务配置表
     * @param param
     */
    void deleteScheduledTaskConfigById(IdCodeDTO param);

    /**
     * 根据ID修改启停状态
     * @param param ID NUMBER 通用DTO
     */
    void updateStateById(IdNumberDTO param);
    
}
