import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import AutoImport from 'unplugin-auto-import/vite';
import vueJsx from "@vitejs/plugin-vue-jsx"; // 配置vue使用jsx
import removeConsole from 'vite-plugin-remove-console'; // Import the plugin
export default ({ mode, command }) => {
  // 环境变量
  const env = loadEnv(mode, __dirname)
  return defineConfig({
    plugins: [
      vueJsx(),
      vue(),
      AutoImport({
        // 自动导入 Vue 相关函数，如：ref, reactive, toRef 等
        imports: ['vue', 'vue-router',],
      }),
      createSvgIconsPlugin({
        // 指定需要缓存的图标文件夹
        iconDirs: [path.resolve(process.cwd(), 'src/assets/icons')],
        // 指定symbolId格式
        symbolId: 'icon-[dir]-[name]',

        /**
         * 自定义插入位置
         *
         */
        // inject?: 'body-last' | 'body-first'

        /**
         * custom dom id
         */
        // customDomId: '__svg__icons__dom__',
      }),
      removeConsole()
    ],
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@import "./src/assets/scss/config/index.scss";`
        },
      },
    },
    // 是否自动在浏览器打开
    open: true,
    // 是否开启 https
    https: false,
    // 服务端渲染
    ssr: false,
    /**
     * 在生产中服务时的基本公共路径。所有路径资源在构建时会被加上base项配置的值
     * @default '/'
     */
    base: env.VITE_BASE,//command == 'serve' ? '/' :
    /**
     * 与“根”相关的目录，构建输出将放在其中。如果目录存在，它将在构建之前被删除。
     * @default 'dist'
     */
    outDir: 'dist',
    resolve: {
      alias: {
        '@': path.resolve(__dirname, '.', 'src'),
      },
    },
    // 反向代理
    server: {
      port: env.VITE_PORT,
      host: "0.0.0.0",
      // 是否自动在浏览器打开
      open: true,
      // 是否开启 https
      https: false,
      proxy: {
        '/api': {
          target: env.VITE_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '/api'),
        },
      },
    },
  })
}
