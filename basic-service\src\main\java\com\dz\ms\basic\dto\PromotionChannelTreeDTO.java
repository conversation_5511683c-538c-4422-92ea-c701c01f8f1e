package com.dz.ms.basic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ApiModel(value = "树状结构")
public class PromotionChannelTreeDTO {
    @ApiModelProperty(value = "渠道ID")
    private Long id;
    @ApiModelProperty(value = "渠道名称")
    private String channelName;
    @ApiModelProperty(value = "渠道参数")
    private String channelParam;
    @ApiModelProperty(value = "二级渠道")
    private List<PromotionChannelDTO> twoChannel;
}
