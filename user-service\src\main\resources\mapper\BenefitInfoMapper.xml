<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.user.mapper.BenefitInfoMapper" >

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
  	    id,
  	    benefit_name,
  	    benefit_img,
  	    un_activate_img,
  	    activate_img,
  	    details,
  	    sort,
  	    jump_link,
  	    state,
  	    tenant_id,
  	    popup_img,
  	    created,
  	    creator,
  	    modified,
  	    modifier,
  	    is_deleted
    </sql>

    <!-- 查询示例 -->
    <select id="selectDemo" parameterType="java.lang.Long" resultType="com.dz.ms.user.entity.BenefitInfo">
        select
        <include refid="Base_Column_List" />
        from benefit_info
        where id = #{id,jdbcType=BIGINT}
        AND tenant_id = #{tenantId}
    </select>
	<select id="selectBatchIdsBySort" resultType="com.dz.ms.user.entity.BenefitInfo">
		select
		bi.id,
		bi.benefit_name,
		bi.benefit_img,
		bi.un_activate_img,
		bi.activate_img,
		bi.details,
		gb.sort,
		gb.`code`,
		gb.jump_link,
		gb.popup_img,
		bi.state,
		bi.tenant_id,
		bi.created,
		bi.creator,
		bi.modified,
		bi.modifier,
		bi.is_deleted
		from benefit_info as bi
		left join grade_benefit as gb on bi.id = gb.benefit_id and gb.grade_id = #{gradeId}
		where bi.id in
		<foreach collection="ids" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		order by gb.sort asc
	</select>

</mapper>
