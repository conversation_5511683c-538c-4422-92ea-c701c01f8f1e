package com.dz.common.core.enums;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据库字段类型枚举
 * @author: Handy
 * @date:   2022/07/07 16:36
 */
public enum OperateTypeEnum {

	STOCKING(1,"入库"),
	STOCK_RETRIEVAL(2,"出库"),
	ENGAGE_LOCK(3,"锁定"),
	Unblock(4,"释放");


	/** 模板编号 */
	private Integer type;
	/** 模板名称 */
	private String name;

	OperateTypeEnum(Integer type, String name) {
		this.type = type;
		this.name = name;
	}

	public Integer getType() {
		return type;
	}

	public String getName() {
		return name;
	}
}
