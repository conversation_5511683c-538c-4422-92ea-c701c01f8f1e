<template>
  <a-drawer :title="title" width="800" placement="right" :closable="true" :maskClosable="false" :open="open" @close="onClose">
    <a-spin :spinning="loading">

      <a-form class="form" ref="addForm" :model="addParams" :rules="rules" :labelCol="{ style: 'width:150px' }">
        <div class="form-top-titles-common">权益基本配置</div>
        <div class="form-top-line-common"></div>
        <a-form-item label="权益名称" name="benefitName">
          <a-input placeholder="请输入权益名称" style="width: 400px" v-model:value="addParams.benefitName" allow-clear show-count :maxlength="8" />
        </a-form-item>
        <a-form-item label="权益说明" name="details">
          <a-input placeholder="请输入权益说明" style="width: 400px" v-model:value="addParams.details" allow-clear show-count :maxlength="20" />
        </a-form-item>
        <div class="form-top-titles-common">权益样式配置</div>
        <div class="form-top-line-common"></div>
        <a-form-item label="激活中样式" name="activateImg">
          <uploadImg :max="10" :width="100" :height="100" :imgUrl="addParams.activateImg" :form="addParams" path="activateImg" :disabled="disabled" @success="uploadSuccess" />
          <div class="global-tip">
            建议尺寸 90 * 90 ，比例 1:1，最大不超过10M，支持jpg、png、gif格式，用于展示该卡级下已经拥有的权益
          </div>
        </a-form-item>
        <a-form-item label="后续激活样式配置" name="unActivateImg">
          <uploadImg :max="10" :width="100" :height="100" :imgUrl="addParams.unActivateImg" :form="addParams" path="unActivateImg" :disabled="disabled" @success="uploadSuccess" />
          <div class="global-tip">
            建议尺寸 90 * 90，比例 1:1，最大不超过10M，支持jpg、png、gif格式，用于展示之后可能会被激活的等级的样式；
          </div>
        </a-form-item>
      </a-form>
      <!--  -->
    </a-spin>
    <template #footer v-if="!disabled">
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="ok" :loading="loading">确定</a-button>
      </a-space>
    </template>

  </a-drawer>
</template>
<script setup>
import { benefitAdd, benefitUpdate, benefitInfo } from '@/http/index.js'
import { useGlobalStore } from '@/store'
import { message, Modal } from "ant-design-vue";
import Clipboard from 'clipboard';
const $router = useRouter();
const global = useGlobalStore()
const addForm = ref(null)
import _ from 'lodash'
import { onMounted } from 'vue';
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  // 操作抽屉打开关闭
  visible: {
    type: Boolean,
    default: false
  },
  // 数据ID
  id: {
    type: String || Number,
    default: ''
  },
  // 操作类型
  type: {
    type: Number,
    default: 0, // 0-新增 1-编辑  2-查看
  }

})
// 置灰
const disabled = computed(() => {
  return props.type == 2
})

// 标题
const title = computed(() => {
  return ['新建', '编辑', '查看'][props.type] + '会员权益'
})

const { open, addParams, rules, loading, permitionList } = toRefs(reactive({
  open: props.visible,
  permitionList: [],

  loading: false,

  addParams: {
    benefitName: '',
    activateImg: '',
    unActivateImg: '',
    details: ''
  },
  rules: {
    benefitName: [{ required: true, message: '请输入权益名称', trigger: ['blur', 'change'] }],
    activateImg: [{ required: true, message: '上传激活样式', trigger: ['blur', 'change'] }],
    unActivateImg: [{ required: true, message: '上传未激活样式', trigger: ['blur', 'change'] }],

  }
})
);
// onMounted(() => {
//     initData()
// })
watch(() => props.visible, (value) => {
  // 弹窗操作
  open.value = props.visible
  addParams.value = {
    benefitName: '',
    activateImg: '',
    unActivateImg: '',
    details: ''
  }
  addForm.value?.resetFields()

  if (open.value) {

    initData()
  }
})
const oncheck = async (selectedKeys) => {
  addParams.value.permitIds = selectedKeys;
  await nextTick();
  addForm.value.validateFields(['permitIds'])
}

//所有接口调取出
const initData = async () => {
  const promiseArr = []

  if (props.id) {

    promiseArr.push(benefitInfo({ id: props.id }))
  }

  // 如果有新的接口就在这里push
  loading.value = true
  try {
    const [benefitInfo] = await Promise.all(promiseArr)

    if (benefitInfo) {
      addParams.value = {
        ...benefitInfo.data
      }
    }
    loading.value = false
  } catch (error) {
    loading.value = false
    console.error('获取数据失败:', error)
  }
}



const uploadSuccess = async (data) => {
  let { form, path: key, imgUrl } = data;
  console.log("🚀 ~ uploadSuccess ~ form, path: key, imgUrl:", form, key, imgUrl)
  _.set(form, key.split('.'), imgUrl)
  // 清除校验
  await nextTick()
  addForm.value.validateFields([key.split('.')])
}


// 关闭弹窗
const onClose = () => {
  emit('cancel')
}

// 确定
const ok = () => {

  addForm.value.validate().then(res => {

    let params = _.cloneDeep(addParams.value)


    loading.value = true
    if (props.id) {
      console.log('编辑');
      benefitUpdate(params).then(res => {
        message.success(res.msg);
        emit('ok', props.id)
      }).finally(() => {
        loading.value = false
      })
    } else {
      benefitAdd(params).then(res => {
        message.success(res.msg);
        emit('ok', props.id)
      }).finally(() => {
        loading.value = false
      })
    }

  })
}



</script>

<style scoped lang="scss">
:deep(.searchForm .ant-form-item) {
  margin-bottom: 16px;
}

:deep(.anticon-eye-invisible) {
  display: none;
}

.member-role {
  background: #f2f2f2;
  margin-bottom: 20px;
  padding: 5px 10px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &:nth-child(odd) {
    margin-right: 35px;
  }
}
</style>
