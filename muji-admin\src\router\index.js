import { createRouter, RouterView, createWebHashHistory } from 'vue-router'

import login from '@/views/login/index.vue'
import forgotPasswordModal from '@/views/login/forgotPasswordModal.vue'
import Layout from '@/views/layout/index.vue' //承载页面
import visitor from '@/components/layout/visitor.vue';
import { useGlobalStore } from '@/store'

/**
 * Note: 路由配置项
 *
 * hidden: true                   // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * name:'router-name'             // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
    title: 'title'               // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'             // 设置该路由的图标，对应路径src/icons/svg
  }
 */
// 不需要权限的路由
export const customeRoutes = [
    {
        path: '/',
        redirect: { name: 'login' },
        hidden: true,

    },
    {
        path: '/login',
        name: 'login',
        component: login,
        hidden: true,

    },
    {
        path: '/forgotPasswordModal',
        name: 'forgotPasswordModal',
        component: forgotPasswordModal,
        hidden: true,

    },
    {
        path: '/layout',
        name: 'Layout',
        component: Layout,
        hidden: true,

    },
    {
        path: '/404',
        component: () => import('@/views/error/404.vue'),
        hidden: true,

    },

    // {
    //     path: '/index',
    //     name: 'Index',
    //     component: () => Layout,
    //     redirect: { name: 'seconedindex' },
    //     meta: { title: '首页', icon: 'menu-index', },
    //     children: [
    //         {
    //             path: 'seconedindex',
    //             name: 'Seconedindex',
    //             component: () => visitor,
    //             meta: { title: '首页', icon: 'menu-index' },
    //             redirect: '/index/seconedindex/threeindex',
    //             children: [
    //                 {
    //                     path: 'threeindex',
    //                     name: 'Threeindex',
    //                     component: () => import('@/views/index/index.vue'),
    //                     meta: { title: '首页', icon: '#' }
    //                 }
    //             ]
    //         },
    //     ]
    // },
    // {
    //     path: '/system',
    //     name: 'System',
    //     component: () => Layout,
    //     redirect: { name: 'SystemConfiguration' },
    //     meta: { title: '系统管理', icon: 'menu-system', },
    //     children: [
    //         {
    //             path: 'systemConfiguration',
    //             name: 'SystemConfiguration',
    //             component: () => visitor,
    //             meta: { title: '系统设置', icon: 'menu-system' },
    //             redirect: '/systemConfiguration/roleList',
    //             children: [
    //                 {
    //                     path: 'roleList',
    //                     name: 'RoleList',
    //                     component: () => import('@/views/system/role/index.vue'),
    //                     meta: { title: '角色', icon: 'menu-system' }
    //                 }
    //             ]
    //         },
    //     ]
    // },


]


// 权限过滤
const router = createRouter({
    history: createWebHashHistory(import.meta.env.VITE_BASE ? import.meta.env.VITE_BASE : ''),
    routes: customeRoutes
})
// 部署后页面没刷新 静态资源找不到
router.onError((error) => {
    console.log("🚀 ~ ", error)
    // 判断是否为静态资源找不到的错误
    // if (error.message.includes('Failed to load resource')) {
    // 检查是否已经尝试过刷新
    if (!document.referrer.includes(window.location.pathname)) {
        window.location.reload();
    } else {
        console.log("已尝试过刷新，停止递归刷新");
    }
    // }
})




export default router
