package com.dz.ms.user.controller;

import com.alibaba.fastjson.JSONObject;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.user.OpenCardStatusDTO;
import com.dz.common.core.dto.user.ReceiveCouponDTO;
import com.dz.common.core.dto.user.UserSimpleDTO;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.vo.ReceiveCouponVo;
import com.dz.ms.user.service.CouponService;
import com.dz.ms.user.service.MUJIOpenApiService;
import com.dz.ms.user.service.UserInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags="muji三方接口")
@RestController
public class MUJIOpenApiController {

    @Resource
    private MUJIOpenApiService mujiOpenApiService;
    @Resource
    private CouponService couponService;
    @Resource
    private UserInfoService userInfoService;

    @GetMapping(value = "/activity/coupon/list")
    public Result<JSONObject> activityCouponList(@RequestParam(value = "memberCode",required = false)String memberCode) {
        Result<JSONObject> result = new Result<>();
        result.setData(mujiOpenApiService.activityCouponList(memberCode));
        return result;
    }

    @GetMapping(value = "/member/coupon/list")
    public Result<JSONObject> memberCouponList(@RequestParam(value = "memberCode",required = false)String memberCode,
                                               @RequestParam(value = "type", required = false)Integer type,
                                               @RequestParam("status")Integer status,
                                               @RequestParam("page")Integer page,
                                               @RequestParam("pageSize")Integer pageSize) {
        Result<JSONObject> result = new Result<>();
        result.setData(mujiOpenApiService.memberCouponList(memberCode,type,status,page,pageSize));
        return result;
    }

    @GetMapping(value = "/member/coupon/stock/id")
    public Result<JSONObject> memberCouponStockId(@RequestParam("stockId") String stockId) {
        Result<JSONObject> result = new Result<>();
        result.setData(mujiOpenApiService.memberCouponDetails(stockId));
        return result;
    }

    @GetMapping(value = "/member/coupon/detail")
    public Result<JSONObject> getCouponDetail(@RequestParam("couponId") String couponId,
                                              @RequestParam(value = "memberCode",required = false) String memberCode,
                                              @RequestParam("couponCode") String couponCode) {
        Result<JSONObject> result = new Result<>();
        result.setData(mujiOpenApiService.memberCouponCodeDetails(memberCode, couponId, couponCode));
        return result;
    }

    @PostMapping(value = "/member/coupon/receive")
    public Result<ReceiveCouponDTO> couponReceive(@RequestBody ReceiveCouponVo param) {
        Result<ReceiveCouponDTO> result = new Result<>();
        ReceiveCouponDTO receiveCouponDTO = couponService.couponReceive(param);
        result.setData(receiveCouponDTO);
        return result;
    }

    @ApiOperation("会员积分增加")
    @PostMapping(value = {"/member/add/points", "/app/member/add/points"})
    public Result<JSONObject> addPoints(@RequestParam(value = "userId", required = false) Long userId,
                                        @RequestParam(value = "memberCode",required = false) String memberCode,
                                        @RequestParam("channel") String channel,
                                        @RequestParam("bonusAmount") Integer bonusAmount,
                                        @RequestParam("reason") String reason) {
        Result<JSONObject> result = new Result<>();
        mujiOpenApiService.addMemberPoints(userId,memberCode, channel, bonusAmount, reason);
        return result;
    }

    @ApiOperation("会员积分增加")
    @PostMapping(value = "/member/add/points_with_sn")
    Result<JSONObject> addPointsWithSn(@RequestParam(value = "memberCode",required = false) String memberCode,
                                       @RequestParam("channel") String channel,
                                       @RequestParam("bonusAmount") Integer bonusAmount,
                                       @RequestParam("reason") String reason,
                                       @RequestParam("outSn") String outSn) {
        Result<JSONObject> result = new Result<>();
        mujiOpenApiService.addMemberPoints(memberCode, channel, bonusAmount, reason, outSn);
        return result;
    }

    @ApiOperation("会员积分扣减")
    @PostMapping(value = {"/member/deduct/points", "/app/member/deduct/points"})
    public Result<JSONObject> deductPoints(@RequestParam(value = "memberCode",required = false) String memberCode,
                                           @RequestParam("channel") String channel,
                                           @RequestParam("bonusAmount") Integer bonusAmount,
                                           @RequestParam("reason") String reason) {
        Result<JSONObject> result = new Result<>();
        result.setData(mujiOpenApiService.deductMemberPoints(memberCode, channel, bonusAmount, reason));
        return result;
    }

    /**
     * 积分扣减
     *
     * @param memberCode
     * @param bonusAmount
     * @param reason
     * @return
     */
    @PostMapping(value = "/member/deduct/points_with_sn")
    @ApiOperation("会员积分扣减")
    Result<JSONObject> deductPointsWithSn(@RequestParam(value = "memberCode",required = false) String memberCode,
                                          @RequestParam("channel") String channel,
                                          @RequestParam("bonusAmount") Integer bonusAmount,
                                          @RequestParam("reason") String reason,
                                          @RequestParam("outSn") String outSn) {
        Result<JSONObject> result = new Result<>();
        result.setData(mujiOpenApiService.deductMemberPoints(memberCode, channel, bonusAmount, reason, outSn));
        return result;
    }

    @ApiOperation("订单列表")
    @PostMapping(value = {"/member/order/list", "/app/member/order/list"})
    public Result<JSONObject> orderList(@RequestParam(value = "memberCode",required = false) String memberCode,
                                        @RequestParam("start_time") String start_time,
                                        @RequestParam("end_time") String end_time,
                                        @RequestParam("type") Integer type,
                                        @RequestParam("page") Integer page,
                                        @RequestParam("pageSize") Integer pageSize) {
        Result<JSONObject> result = new Result<>();
        result.setData(mujiOpenApiService.memberOrderList(memberCode,start_time, end_time,type,page,pageSize));
        return result;
    }

    @ApiOperation("小票详情")
    @PostMapping(value = {"/member/order/ticket/details","/app/member/order/ticket/details"})
    public Result<JSONObject> ticketDetails(@RequestParam(value = "memberCode",required = false)String memberCode,
                                            @RequestParam("orderSn")String orderSn) {
        Result<JSONObject> result = new Result<>();
        result.setData(mujiOpenApiService.memberOrderTicketDetails(memberCode,orderSn));
        return result;
    }

    @ApiOperation("订单详情")
    @PostMapping(value = {"/member/order/details","/app/member/order/details"})
    public Result<JSONObject> memberOrderDetails(@RequestParam(value = "memberCode",required = false)String memberCode,
                                            @RequestParam("orderSn")String orderSn) {
        Result<JSONObject> result = new Result<>();
        result.setData(mujiOpenApiService.memberOrderDetails(memberCode,orderSn));
        return result;
    }

    @ApiOperation("查询是否开卡")
    @PostMapping(value = "/app/member/open/card/status")
    public Result<OpenCardStatusDTO> openCardStatus() {
        Result<OpenCardStatusDTO> result = new Result<>();
        OpenCardStatusDTO openCardStatus = new OpenCardStatusDTO();
        //获取当前登录用户信息
        UserSimpleDTO userInfo = userInfoService.getDbUserSimpleInfo(SecurityContext.getUser().getUid());
        JSONObject openCardJson = mujiOpenApiService.openCardStatus(userInfo.getCardNo());
        if (null != openCardJson){
            if (openCardJson.containsKey("wechat_card_status")){
                openCardStatus.setIsOpenCard(openCardJson.getInteger("wechat_card_status"));
                if (openCardJson.getInteger("wechat_card_status")==1){
                    openCardStatus.setWechatCardId(openCardJson.getString("wechat_card_id"));
                    openCardStatus.setWechatCardAppId(openCardJson.getString("wechat_card_app_id"));
                }
            }
        }
        result.setData(openCardStatus);
        return result;
    }

    /**
     * 会员积分数据查询
     * @param memberCode
     * @return
     */
    @PostMapping(value = "/member/points/count")
    public Result<JSONObject> memberPointsCount(@RequestParam(value = "memberCode",required = false) String memberCode) {
        Result<JSONObject> result = new Result<>();
        result.setData(mujiOpenApiService.memberPointsCount(memberCode));
        return result;
    }

    /**
     * 会员扫码上报
     *
     * @param memberCode
     * @return
     */
    @PostMapping(value = "/app/member/qrcode/report")
    @ApiOperation("会员扫码上报")
    public Result<JSONObject> memberQrCodeReport(@RequestParam(value = "memberCode",required = false) String memberCode,
                                                 @RequestParam("qrcodeId") String qrcodeId,
                                                 @RequestParam("qrcodeSn") String qrcodeSn) {
        Result<JSONObject> result = new Result<>();
        result.setData(mujiOpenApiService.activityQrCodeReceive(memberCode, qrcodeId, qrcodeSn));
        return result;
    }
}
