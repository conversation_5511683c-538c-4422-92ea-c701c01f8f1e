// components/modal/modal.js
const app = getApp()
Component({
  properties: {
    // 显示弹窗
    show: {
      type: Boolean,
      value: false,
    },
    // 是否显示关闭按钮
    closeable: {
      type: Boolean,
      value: true
    },
    // 弹窗背景色
    background: {
      type: String,
      value: 'white'
    },
    // 弹窗圆角
    borderRadius: {
      type: Number,
      value: 30
    },
    // 标题
    title: {
      type: String
    },
    // 内容 可以是富文本
    content: {
      type: String
    },
    // 确认按钮
    confirmText: {
      type: String
    },
    // 取消按钮
    cancelText: {
      type: String
    },
    zIndex: {
      type: Number,
      value: 1000000
    }
  },
  data: {},

  methods: {
    // 禁止穿透
    touchmove() {
      return false
    },
    // 禁止穿透
    touchmove1() {
      return true
    },
    // 关闭按钮
    close() {
      this.triggerEvent('close')
    },
  }
})
