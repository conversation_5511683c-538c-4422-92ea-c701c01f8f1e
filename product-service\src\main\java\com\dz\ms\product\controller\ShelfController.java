package com.dz.ms.product.controller;

import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.constants.NumConstants;
import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.dto.DownloadCenterDTO;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.dto.IdNumberDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.fegin.basic.ReportDownloadFeignClient;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.ms.product.config.Globals;
import com.dz.ms.product.dto.ShelfDTO;
import com.dz.ms.product.dto.req.ShelfParamDTO;
import com.dz.ms.product.dto.req.ShelfSaveParamDTO;
import com.dz.ms.product.service.ShelfService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "商品货架")
@RestController
@Slf4j
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class ShelfController {

    @Resource
    private ShelfService shelfService;
    @Resource
    private ReportDownloadFeignClient reportDownloadFeignClient;

    /**
     * 分页查询商品货架
     *
     * @param param
     * @return result<PageInfo < ShelfDTO>>
     */
    @ApiOperation("分页查询商品货架")
    @GetMapping(value = "/crm/shelf/list")
    public Result<PageInfo<ShelfDTO>> getShelfList(@ModelAttribute ShelfParamDTO param) {
        Result<PageInfo<ShelfDTO>> result = new Result<>();
        PageInfo<ShelfDTO> page = shelfService.getShelfList(param);
        result.setData(page);
        return result;
    }

    /**
     * 查询商品货架
     */
    @ApiOperation("不分页查询商品货架名称")
    @GetMapping(value = "/crm/shelf/list_by_name")
    public Result<List<ShelfDTO>> getNoPageShelfList(@ModelAttribute ShelfParamDTO param) {
        Result<List<ShelfDTO>> result = new Result<>();
        param.setState(NumConstants.ONE);
        List<ShelfDTO> page = shelfService.getNoPageShelf(param, NumConstants.ONE);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询商品货架
     *
     * @param id
     * @return result<ShelfDTO>
     */
    @ApiOperation("根据ID查询商品货架")
    @GetMapping(value = "/crm/shelf/info")
    public Result<ShelfDTO> getShelfById(@RequestParam("id") Long id) {
        Result<ShelfDTO> result = new Result<>();
        ShelfDTO shelf = shelfService.getShelfById(id, NumConstants.ONE);
        result.setData(shelf);
        return result;
    }

    @ApiOperation("查询用户商品货架")
    @GetMapping(value = "/app/shelf/priority_shelf")
    public Result<ShelfDTO> getPrioritySortedShelfOne() {
        Result<ShelfDTO> result = new Result<>();
        ShelfDTO shelf = shelfService.getPrioritySortedShelfOne();
        result.setData(shelf);
        return result;
    }

    /**
     * 查询所有商品货架优先级数据
     * @return result<List<Integer>>
     */
    @ApiOperation("查询所有商品货架优先级数据")
    @GetMapping(value = "/crm/shelf/all_priority")
    public Result<List<ShelfDTO>> getAllPriority() {
        Result<List<ShelfDTO>> result = new Result<>();
        result.setData(shelfService.getAllPriority());
        return result;
    }

    /**
     * 新增商品货架
     *
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增商品货架", type = LogType.OPERATELOG)
    @ApiOperation("新增商品货架")
    @PostMapping(value = "/crm/shelf/add")
    public Result<Long> addShelf(@RequestBody ShelfSaveParamDTO param) {
        Result<Long> result = new Result<>();
        Long id = shelfService.saveShelf(param,true);
        result.setData(id);
        return result;
    }

    /**
     * 更新商品货架
     *
     * @param param
     * @return result<Object>
     */
    @SysLog(value = "更新商品货架", type = LogType.OPERATELOG)
    @ApiOperation("更新商品货架")
    @PostMapping(value = "/crm/shelf/update")
    public Result<Long> updateShelf(@RequestBody ShelfSaveParamDTO param) {
        Result<Long> result = new Result<>();
        shelfService.saveShelf(param,false);
        result.setData(param.getId());
        return result;
    }

    /**
     * 根据ID删除商品货架
     *
     * @param param
     * @return result<Boolean>
     */
    @SysLog(value = "根据ID删除商品货架", type = LogType.OPERATELOG)
    @ApiOperation("根据ID删除商品货架")
    @PostMapping(value = "/crm/shelf/delete")
    public Result<Boolean> deleteShelfById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        shelfService.deleteShelfById(param);
        result.setData(true);
        return result;
    }

    /**
     * 根据ID修改启停状态
     * @param param ID NUMBER 通用DTO
     * @return result<Boolean>
     */
    @SysLog(value = "根据ID修改启停状态", type = LogType.OPERATELOG)
    @ApiOperation("根据ID修改启停状态")
    @PostMapping(value = "/crm/shelf/update_state")
    public Result<Boolean> updateStateById(@RequestBody IdNumberDTO param) {
        Result<Boolean> result = new Result<>();
        shelfService.updateStateById(param);
        result.setData(true);
        return result;
    }

    /**
     * 导出货架列表
     *
     * @return
     */
    @PostMapping(value = "/shelf/export_list")
    public Result<Void> exportList(@RequestBody DownloadAddParamDTO exportParam) {
        CurrentUserDTO user = SecurityContext.getUser();
        Globals.downloadThreadPool.execute(() -> {
            try {
                SecurityContext.setUser(user);
                shelfService.exportList(exportParam);
            } catch (Exception e) {
                log.error("导出错误", e);
                DownloadCenterDTO downloadCenterDTO = new DownloadCenterDTO();
                downloadCenterDTO.setId(exportParam.getDownloadCenterId());
                downloadCenterDTO.setErrorDesc(e.getMessage());
                downloadCenterDTO.setState(2);
                reportDownloadFeignClient.updateDownloadCenter(downloadCenterDTO);
            }
        });
        return new Result<>();
    }

    /**
     * 根据人群包id,更新货架,货架营销活动关联人群包id为null
     * @param param ID NUMBER 通用DTO
     * @return result<Boolean>
     */
    @ApiOperation("根据人群包id,更新货架,货架营销活动关联人群包id为null")
    @PostMapping(value = "/shelf/upd_groupId")
    public Result<Boolean> updGroupIdIntoNull(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        //shelfService.updGroupIdIntoNull(param.getId());
        result.setData(true);
        return result;
    }
}
