package com.dz.ms.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Data;

import java.util.Date;

@Data
@Table("白名单")
@TableName(value = "white_list")
public class WhiteList {
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "白名单ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "用户ID")
    private Long userId;

    @Columns(type = ColumnType.VARCHAR, length = 256, isNull = false, comment = "用户名")
    private String userName;

    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = false, comment = "会员卡号")
    private String memberCode;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;

    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;

    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
}