.clockPopup {
  position: relative;
  background: transparent;
  display: flex;
  flex-direction: column;
  align-items: center;

  .clockPopupContent {
    overflow: hidden;
    width: 594rpx;
    height: 906rpx;
    box-sizing: border-box;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 140%;
    padding: 46rpx 0 70rpx;
    border: 10rpx solid #94243A;

    .h1 {
      text-align: center;
      font-weight: 800;
      font-size: 29rpx;
      color: #000000;
      margin-bottom: 46rpx;
    }

    .scrollWrap {
      box-sizing: border-box;
      width: 496rpx;
      height: 607rpx;
      position: relative;
      margin: 0 auto;
    }

    .scroll {
      box-sizing: border-box;
      width: 100%;
      height: 100%;

      .treeRules {
        width: 100%;
      }
    }

    .footMask {
      position: absolute;
      left: 0;
      bottom: 0;
      z-index: 2;
      width: 100%;
      height: 20rpx;
      backdrop-filter: blur(2px);
      opacity: 0.4;
      display: none;

      &.hide {
        display: none;
      }
    }
  }

  .descWrap {
    margin: 14rpx auto 14rpx;
    //display: flex;
    display: none;

    .icon {
      width: 24rpx;
      height: 24rpx;

      .img {
        width: 100%;
        height: 100%;
      }
    }

    .desc {
      margin-left: 20rpx;
      font-weight: 400;
      font-size: 18rpx;
      color: #3D3B43;
      line-height: 24rpx;

      .privacyPolicyText {
        text-decoration: underline;
      }
    }
  }

  .resetBtn {
    margin: 50rpx auto 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 342rpx;
    height: 60rpx;
    background: #94243A;
    font-weight: 800;
    font-size: 22rpx;
    color: #FFFFFF;
  }
}
