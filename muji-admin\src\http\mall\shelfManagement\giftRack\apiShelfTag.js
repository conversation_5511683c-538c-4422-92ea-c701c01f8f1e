import service from '@/utils/request.js'

export const apiShelfTag = {
  async getOneTag (data) {
    return await service({ url: '/crm/product/shelf_tag/one_tag', method: 'get', data }).then(res => {
      return res
    })
  },
  async getTwoTag (data) {
    return await service({ url: '/crm/product/shelf_tag/two_tag', method: 'get', data }).then(res => {
      return res
    })
  },
  async saveOneTag (data) {
    return await service({ url: '/crm/product/shelf_tag/one_tag_save', method: 'post', data }).then(res => {
      return res
    })
  },
  async saveTwoTag (data) {
    return await service({ url: '/crm/product/shelf_tag/two_tag_save', method: 'post', data }).then(res => {
      return res
    })
  }
}
