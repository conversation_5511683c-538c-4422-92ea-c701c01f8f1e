<template>
  <layout>
    <template v-slot:header>
      <!---->
      <searchForm :disabled="!$hasPermission('crowdPurchaseRestriction:search')" :formParams="searchFields" @cancel="whenClickReset" @ok="whenClickSearch">
        <a-form-item label="" name="name">
          <a-input placeholder="活动名称" allow-clear v-model:value="searchFields.name" allowClear @keyup.enter="whenClickSearch"></a-input>
        </a-form-item>
        <BaseDateTimeRange label="开始时间" name="dateTimeRange" v-model="searchFields.dateTimeRange" />
        <CrowdPurchaseRestrictionStatusSelect label="活动状态" name="campaignState" v-model="searchFields.campaignState" />
      </searchForm>
    </template>
    <template v-slot:topRight>
      <a-space>
        <!--:disabled="!$hasPermission('mall:permission:addShelfExtension')"-->
        <a-button type="primary" :disabled="!$hasPermission('crowdPurchaseRestriction:add')" @click="handleAdd">新建活动</a-button>
        <a-button type="primary" :disabled="!$hasPermission('crowdPurchaseRestriction:export')" @click="handleExport">导出</a-button>
      </a-space>
    </template>
    <template v-slot="{ height }">
      <a-table :indentSize="20" row-key="id" :scroll="{ scrollToFirstRowOnChange: true, y: height - 88, x: '100%' }" :dataSource="dataSource" :columns="thisFields.tableHeader" :pagination="pagination" :loading="loading" @change="whenPaginationChange">
        <template #bodyCell="{text, record, index, column}">
          <template v-if="column.dataIndex === 'index'">{{ (current - 1) * pageSize + 1 + index }}</template>
          <template v-if="column.dataIndex === 'onStartTime'">
            <div>{{ record.onStartTime }}</div>
            <div>{{ record.onEndTime }}</div>
          </template>
          <template v-if="column.dataIndex === 'type'">
            {{ CROWD_PURCHASE_RESTRICTION_TYPE.find(i => i.value==record.type)?.label || '-' }}
          </template>
          <template v-if="column.dataIndex === 'campaignState'">
            {{ CROWD_PURCHASE_RESTRICTION_STATUS_ARR.find(i => i.value==record.campaignState)?.label || '-' }}
          </template>
          <template v-if="column.dataIndex === 'stateDesc'">
            <a-tag color="green" v-if="record.state === 1">启用中</a-tag>
            <a-tag color="red" v-else>停用中</a-tag>
          </template>
          <template v-if="column.dataIndex === 'action'">
            <!--<a-button type="link" @click="handleCopy(record)">复制</a-button>-->
            <!--<a-divider type="vertical" />-->
            <!--:disabled="!$hasPermission('mall:permission:editShelfExtension')"-->
            <a-button type="link" :disabled="!$hasPermission('crowdPurchaseRestriction:edit')" @click="handleEdit(record)">编辑</a-button>
            <a-divider type="vertical" />
            <a-button type="link" :disabled="!$hasPermission('crowdPurchaseRestriction:state')" @click="handleStopUsing(record)" v-if="record.state === 1">停用</a-button>
            <a-button type="link" :disabled="!$hasPermission('crowdPurchaseRestriction:state')" @click="handleStopUsing(record)" v-else>启用</a-button>
            <a-divider type="vertical" />
            <a-popconfirm title="是否确定删除该活动？" :disabled="!$hasPermission('crowdPurchaseRestriction:del')||record.state===1" @confirm="handleDelete(record)">
              <!--:disabled="!$hasPermission('mall:permission:delShelfExtension')"-->
              <a-button type="link" :disabled="!$hasPermission('crowdPurchaseRestriction:del')||record.state===1">删除</a-button>
            </a-popconfirm>
          </template>
        </template>
      </a-table>
    </template>
  </layout>
  <currentForm v-model:visible="thisFields.visible" @ok="whenEditSuccess" :id="thisFields.id" :type="thisFields.type" />
</template>
<script setup>
import currentForm from './components/currentForm.vue'
import CrowdPurchaseRestrictionStatusSelect from '@/views/marketingAndEvents/deiliMall/crowdPurchaseRestriction/components/CrowdPurchaseRestrictionStatusSelect.vue'
import { message } from 'ant-design-vue'
import { usePagination } from 'vue-request'
import { apiCrowdPurchaseRestriction } from '@/http/index.js'
import { CROWD_PURCHASE_RESTRICTION_STATUS_ARR, CROWD_PURCHASE_RESTRICTION_TYPE } from '@/utils/constants.js'
import { downloadExcel } from '@/utils/tools.js'
import { cloneDeep } from 'lodash'
import { downloadTask } from '@/http/index.js'
import { useRoute } from 'vue-router'

const route = useRoute()
const pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ['bottomLeft']
  }
})
const total = ref(0)
const getDefaultSearchFields = () => ({
  name: '',
  dateTimeRange: [],
  campaignState: undefined
})
const searchFields = reactive(getDefaultSearchFields())
const thisFields = reactive({
  id: '',
  type: 0, // 0-新增 1-编辑 2-查看
  visible: false,
  tableHeader: [
    { title: '序号', dataIndex: 'index', align: 'center', width: 80 },
    { title: '活动名称', dataIndex: 'name', align: 'center', ellipsis: true, width: 100 },
    { title: '活动类型', dataIndex: 'type', align: 'center', ellipsis: true, width: 100 },
    { title: '活动时间', dataIndex: 'onStartTime', align: 'center', ellipsis: true, width: 140 },
    { title: '关联货架', dataIndex: 'shelfName', align: 'center', ellipsis: true, width: 100 },
    { title: '活动状态', dataIndex: 'campaignState', align: 'center', ellipsis: true, width: 100 },
    { title: '启停状态', dataIndex: 'stateDesc', align: 'center', ellipsis: true, width: 100 },
    { title: '操作', dataIndex: 'action', align: 'center', width: 200, fixed: 'right' }
  ],
  record: {}
})
const { data: dataSource, run, loading, pageSize, current, refresh } = usePagination((param) => {
  searchFields.onStartTime = searchFields.dateTimeRange[0] || ''
  searchFields.onEndTime = searchFields.dateTimeRange[1] || ''
  const data = { ...param, ...searchFields }
  delete data.dateTimeRange
  return apiCrowdPurchaseRestriction.getPageList(data)
}, {
  manual: false, // 修改为false 让其自动执行
  pagination: {
    currentKey: 'pageNum', // 通过该值指定接口 当前页数 参数的属性值
    pageSizeKey: 'pageSize', // 通过该值指定接口 每页获取条数 参数的属性值
    totalKey: 'data.data.count' // 指定 data 中 total 属性的路径
  },
  formatResult: res => { // 返回数据格式化
    total.value = res.data.count
    return res.data.list
  }
})

const whenPaginationChange = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}
const whenClickSearch = () => {
  run({ pageNum: 1, pageSize: pageSize.value })
}
const whenClickReset = () => {
  Object.assign(searchFields, getDefaultSearchFields())
  whenClickSearch()
}

const handleAdd = () => {
  thisFields.visible = true
  thisFields.type = 0
  thisFields.id = ''
}
const handleEdit = (record) => {
  thisFields.visible = true
  thisFields.type = 1
  thisFields.id = record.id
}
const handleCopy = (record) => {
  thisFields.visible = true
  thisFields.type = 3
  thisFields.id = record.id
}
const whenEditSuccess = (value) => {
  thisFields.visible = false
  if (!value) { // 新增 重置搜索数据
    whenClickReset()
  } else { // 查看|编辑 不需要清空搜索数据 直接刷新
    refresh()
  }
}
const handleDelete = (record) => {

  apiCrowdPurchaseRestriction.deletePage({ id: record.id }).then(res => {
    if (res.code === 0) {
      message.success('删除成功')
      whenClickReset()
    }
  })
}

const handleStopUsing = async (record) => {
  await apiCrowdPurchaseRestriction.updateState({ id: record.id, state: record.state === 1 ? 0 : 1 })
  message.success(record.state === 1 ? '已停用' : '已启用')
  refresh()
}

const handleExport = async () => {
  const param = cloneDeep(searchFields)
  param.onStartTime = param.dateTimeRange[0] || ''
  param.onEndTime = param.dateTimeRange[1] || ''
  delete param.dateTimeRange
  // todo
  downloadTask({ type: 6, fileName: '人群限购列表', param })
}

onMounted(() => {
  if (route.query.id) {
    handleEdit({ id: +route.query.id })
  }
})
</script>
