/* signUp/pages/signUp/signUp.wxss */
.page-container {
  background-color: #F8F6ED;

  .page-rule {
    //position: absolute;
    z-index: 10;

    position: fixed;
    right: 0rpx;
    top: 243rpx;
    width: 44rpx;
    height: 146rpx;
    background: #C7B397;
    font-family: MUJIFont2020,
      SourceHanSansCN;
    font-weight: 400;
    font-size: 22rpx;
    color: #FFFFFF;
    line-height: 24rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    writing-mode: vertical-rl;
    letter-spacing: 4rpx;
  }

  .page-zhongjiang {
    z-index: 10;

    position: fixed;
    right: 0rpx;
    top: 421rpx;
    width: 44rpx;
    height: 146rpx;
    background: #C7B397;
    font-family: MUJIFont2020,
      SourceHanSansCN;
    font-weight: 400;
    font-size: 22rpx;
    color: #FFFFFF;
    line-height: 24rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    writing-mode: vertical-rl;
    letter-spacing: 4rpx;
  }

  .page-header {
    display: block;
    width: 100vw;
    height: 706rpx;
  }

  .page-all {
    display: block;
    width: 100vw;
    // height: 100vh;
    position: absolute;
    left: 0rpx;
    top: 0rpx;
  }

  .page-content {
    position: relative;
    pointer-events: none;
    margin-top: 71rpx;
    margin-left: 86rpx;
    width: 548rpx;
    height: 616rpx;

    &-img {
      width: 548rpx;
      height: 616rpx;
    }

    &-tip {
      position: absolute;
      left: 54rpx;
      top: -30rpx;
      line-height: 29rpx;
      padding: 0 20rpx;
      background: #C8B49A;
      font-family: SourceHanSansCN, SourceHanSansCN;
      font-weight: 500;
      font-size: 20rpx;
      color: #FFFFFF;
      border-radius: 23rpx;
      letter-spacing: 2rpx;

      &::after {
        content: '';
        position: absolute;
        bottom: -14rpx;
        left: 30rpx;
        width: 0;
        height: 0;
        border-right: 10rpx solid transparent;
        border-left: 10rpx solid transparent;
        border-top: 16rpx solid #C8B49A;
        /* 指向左边的颜色 */
      }
    }
  }

  .bottom-box {
    margin-top: 90rpx;
    padding-bottom: 60rpx;
    display: flex;
    justify-content: center;
  }

  .registrationSuccessful {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    z-index: 1;
    height: 100vh;
    //flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
  }
}
