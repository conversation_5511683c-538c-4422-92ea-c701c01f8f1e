<template>
  <a-drawer :title="title" width="800" placement="right" :closable="true" :maskClosable="false" :open="open" @close="onClose">
    <a-spin :spinning="loading">

      <a-form class="form" ref="addForm" :model="addParams" :rules="rules" :labelCol="{ style: 'width:150px' }">
        <div class="form-top-titles-common">隐私条款基本配置</div>
        <div class="form-top-line-common"></div>
        <a-form-item label="条款名称" name="name">
          <a-input placeholder="请输入条款名称" style="width: 400px" v-model:value="addParams.name" allow-clear show-count :maxlength="15" />
        </a-form-item>
        <a-form-item label="版本编号" v-if="props.id" name="policyVersion">
          <!-- <a-input placeholder="请输入权益说明" style="width: 400px" v-model:value="addParams.gradeName" allow-clear show-count :maxlength="20" /> -->
          {{addParams.policyVersion}}
        </a-form-item>

        <a-form-item label="发布时间" name="pushTime">
          <a-date-picker :disabled="disabled" :getPopupContainer="(triggerNode) => triggerNode.parentNode" valueFormat="YYYY-MM-DD HH:mm:ss" style="width: 400px" format="YYYY-MM-DD HH:mm:ss" :disabledDate="disabledDate" :show-time="{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }" placeholder="请选择" v-model:value="addParams.pushTime" />
        </a-form-item>
        <div class="form-top-titles-common">弹窗内容配置</div>
        <div class="form-top-line-common"></div>
        <a-form-item label="首次弹窗标题" name="policyTitle">
          <a-input placeholder="请输入首次弹窗标题" style="width: 400px" v-model:value="addParams.policyTitle" allow-clear show-count :maxlength="15" />
        </a-form-item>
        <a-form-item label="版本更新标题" name="newTitle">
          <a-input placeholder="请输入版本更新标题" style="width: 400px" v-model:value="addParams.newTitle" allow-clear show-count :maxlength="15" />
        </a-form-item>
        <a-form-item label="弹窗内容配置" name="content">
          <richText :toolbar="toolbarList" :plugins="pluginsList" :menubar="menubarList" v-model:value="addParams.content"></richText>
        </a-form-item>
      </a-form>
      <!--  -->
    </a-spin>
    <template #footer v-if="!disabled">
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="ok" :loading="loading">确定</a-button>
      </a-space>
    </template>

  </a-drawer>
</template>
<script setup>
import { privacy_policyAdd, privacy_policyUpdate, privacy_policyInfo } from '@/http/index.js'
// console.log("🚀 ~ privacy_policyUpdate:", privacy_policyUpdate)
import richText from "@/components/richText/index.vue";
import { useGlobalStore } from '@/store'
import { message, Modal } from "ant-design-vue";
import Clipboard from 'clipboard';
const $router = useRouter();
const global = useGlobalStore()
const addForm = ref(null)
import dayjs from "dayjs";
import { cloneDeep } from 'lodash'
import { onMounted } from 'vue';
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  // 操作抽屉打开关闭
  visible: {
    type: Boolean,
    default: false
  },
  // 数据ID
  id: {
    type: String || Number,
    default: ''
  },
  // 操作类型
  type: {
    type: Number,
    default: 0, // 0-新增 1-编辑  2-查看
  }

})
// 置灰
const disabled = computed(() => {
  return props.type == 2
})

// 标题
const title = computed(() => {
  return ['新建', '编辑', '查看'][props.type] + '隐私条款'
})

const { open, addParams, rules, loading, permitionList, toolbarList, menubarList, pluginsList } = toRefs(reactive({
  open: props.visible,
  permitionList: [],
  toolbarList: "undo redo |  accordionremove |  fontfamily  fontsize| bold italic underline strikethrough    | align   | link image |  | lineheight outdent indent| forecolor backcolor removeformat | charmap emoticons |   | preview",
  pluginsList: "autolink  fullscreen image link  preview",
  menubarList: "edit view insert  tools table",
  loading: false,

  addParams: {
    name: "",
    pushTime: "",
    policyTitle: '',
    newTitle: '',
    content: ''

  },
  rules: {
    name: [{ required: true, message: '请输入条款名称', trigger: ['blur', 'change'] }],
    pushTime: [{ required: true, message: '请选择时间', trigger: ['blur', 'change'] }],
    policyTitle: [{ required: true, message: '请输入首次弹窗标题', trigger: ['blur', 'change'] }],
    newTitle: [{ required: true, message: '请输入版本更新标题', trigger: ['blur', 'change'] }],
    content: [{ required: true, message: '请输入弹窗内容配置', trigger: ['blur', 'change'] }],
  }
})
);

const disabledDate = (date) => {
  const today = dayjs().startOf('day'); // 获取今天的日期，从午夜开始
  const targetDate = dayjs(date).startOf('day'); // 同样，将目标日期也调整到天的开始

  return targetDate.isBefore(today); // 比较调整后的日期

};
watch(() => props.visible, (value) => {
  // 弹窗操作
  open.value = props.visible
  addParams.value = {
    name: "",
    pushTime: "",
    policyTitle: '',
    newTitle: '',
    content: ''
  }
  addForm.value?.resetFields()
  if (open.value) {

    initData()
  }
})


//所有接口调取出
const initData = async () => {
  const promiseArr = []


  if (props.id) {

    promiseArr.push(privacy_policyInfo({ id: props.id }))

  }

  // 如果有新的接口就在这里push
  loading.value = true
  try {
    const [privacy_policyInfo] = await Promise.all(promiseArr)
    // console.log("🚀 ~ initData ~ privacy_policyInfo:", privacy_policyInfo)

    if (privacy_policyInfo) {

      addParams.value = {
        ...privacy_policyInfo.data
      }
    }
    loading.value = false
  } catch (error) {
    loading.value = false
    // console.error('获取数据失败:', error)
  }
}



const uploadSuccess = async (data) => {
  let { form, path: key, imgUrl } = data;
  // console.log("🚀 ~ uploadSuccess ~ form, path: key, imgUrl:", form, key, imgUrl)
  _.set(form, key.split('.'), imgUrl)
  // 清除校验
  await nextTick()
  addForm.value.validateFields([key.split('.')])
}


// 关闭弹窗
const onClose = () => {
  emit('cancel')
}

// 确定
const ok = () => {
  // console.log(addParams.value, 'addParams.valueaddParams.valueaddParams.valueaddParams.value');

  addForm.value.validate().then(res => {

    let params = cloneDeep(addParams.value)
    if (params.content.length > 0) {
      var tempDiv = document.createElement('div');
      tempDiv.innerHTML = params.content.trim(); // 移除字符串首尾的空白字符，避免不必要的DOM元素

      // 获取所有的<a>标签
      var aTags = tempDiv.getElementsByTagName('a');

      // <a>标签的数量
      params.urlSum = aTags.length || 0;
      // console.log("Number of <a> tags:", params.urlSum);
    } else {
      params.urlSum = 0
    }



    loading.value = true
    if (props.id) {
      console.log('编辑');
      privacy_policyUpdate(params).then(res => {
        console.log("🚀 ~ privacy_policyUpdate ~ privacy_policyUpdate:", privacy_policyUpdate)
        message.success(res.msg);
        emit('ok', props.id)
      }).finally(() => {
        loading.value = false
      })
    } else {
      privacy_policyAdd(params).then(res => {
        message.success(res.msg);
        emit('ok', props.id)
      }).finally(() => {
        loading.value = false
      })
    }

  })
}



</script>

<style scoped lang="scss">
:deep(.searchForm .ant-form-item) {
  margin-bottom: 16px;
}

:deep(.anticon-eye-invisible) {
  display: none;
}

.member-role {
  background: #f2f2f2;
  margin-bottom: 20px;
  padding: 5px 10px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &:nth-child(odd) {
    margin-right: 35px;
  }
}
</style>
