package com.dz.common.core.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class OrderListAppDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单编号")
    private String orderCode;
    @ApiModelProperty(value = "订单状态 0已完成 1待支付 2待发货 3已发货 4待兑换 5部分兑换 6已兑换 7已取消")
    private Integer orderStatus;
    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderAmount;
    @ApiModelProperty(value = "订单积分")
    private Integer orderPoint;
    @ApiModelProperty(value = "商品列表")
    private List<OrderInfoProductAppDTO> productList;
    @ApiModelProperty(value = "商品数量")
    private Integer number;
    @ApiModelProperty(value = "兑换时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date created;
}
