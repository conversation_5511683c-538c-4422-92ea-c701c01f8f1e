package com.dz.common.core.dto.basic;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Author: txt
 * @Date: 2024-06-25 11:34
 * @Version: 1.0
 */
@Data
public class AliCheckRequestDTO {

    @ApiModelProperty("文字审核必传")
    private String content;

    @ApiModelProperty("图片审核必传")
    private List<String> urlList;

    private Integer modelType;

    private Long modelId;


    public AliCheckRequestDTO(String content, List<String> urlList, Integer modelType, Long modelId) {
        this.content = content;
        this.urlList = urlList;
        this.modelType = modelType;
        this.modelId = modelId;
    }
}
