package com.dz.ms.product.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;

/**
 * 营销活动规则关联的货架商品删除入参
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@ApiModel(value = "营销活动规则关联的货架商品删除入参")
public class ShelfCampaignRuleProductDelParamDTO {

    @ApiModelProperty(value = "营销活动ID")
    private Long campaignId;
    @ApiModelProperty(value = "营销活动规则ID")
    private Long ruleId;
    @ApiModelProperty(value = "营销活动规则ID列表")
    private List<Long> ruleIdList;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "商品ID")
    private Long productId;
    @ApiModelProperty(value = "货架商品ID列表(注意:此字段是排除式删除)")
    private List<Long> shelfProductIdList;

}
