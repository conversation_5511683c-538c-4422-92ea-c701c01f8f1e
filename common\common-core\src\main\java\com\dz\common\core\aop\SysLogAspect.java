package com.dz.common.core.aop;

import com.alibaba.fastjson.JSON;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.basic.SystemLogDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.dto.user.SysUserDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.fegin.basic.SystemLogFeignClient;
import com.dz.common.core.fegin.user.SysUserFeginClient;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.IpAdrressUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.Method;
import java.util.Date;

/**
 * 系统切片实现
 * <AUTHOR>
 * @date 2022/2/4 19:14
 */
@Aspect
@Component
@Slf4j
public class SysLogAspect {
	public SysLogAspect(){}

	@Resource
	private SystemLogFeignClient systemLogFeginClient;
	@Resource
	private SysUserFeginClient sysUserFeginClient;


	/**
	* 所有标注了@ActionLog标签的方法切入点
	*/
	@Pointcut("execution(@com.dz.common.core.annotation.SysLog * com.dz.ms.*.controller.*.*(..))")
	private void sysLogController() {}

	@Pointcut("execution(@com.dz.common.core.annotation.SysLog * com.dz.ms.*.service.impl.*.*(..))")
	private void sysLogService() {}

	@Around("sysLogController() || sysLogService()")
	public Object aroundMethods(ProceedingJoinPoint joinPoint) throws Throwable {
		Date startTime = new Date();
		String methodName = joinPoint.getSignature().getName();
		Method method = null;
		Method[] methods = joinPoint.getTarget().getClass().getMethods();
		for (int i = 0; i < methods.length; i++) {
			if (methods[i].getName().equals(methodName)) {
				method = methods[i];
				break;
			}
		}
		boolean hasAnnotation =false;
		if(method != null){
			hasAnnotation = method.isAnnotationPresent(SysLog.class);
		}
		//处理结果
		Object[] params = joinPoint.getArgs();
		boolean isSuccess = true;
		try{
			String message = null;
			Object proceed = joinPoint.proceed();
			if(proceed instanceof Result) {
				Result result = (Result)proceed;
				isSuccess = result.isSuccess();
				message = result.getMsg();
			}
			//方法是否有注解，有注解了记录日志
			if (hasAnnotation) {
				this.saveSystemLog(method, params, isSuccess,startTime,message);
			}
			return proceed;
		}catch(Exception e){
			isSuccess = false;
			//方法是否有注解，有注解了记录日志
			if (hasAnnotation) {
				StringWriter sw = new StringWriter();
				PrintWriter pw = new PrintWriter(sw);
				try {
					e.printStackTrace(pw);
					sw.close();
					pw.close();
				} catch (IOException ioException) {
					ioException.printStackTrace();
				}
				this.saveSystemLog(method, params, isSuccess,startTime,sw.toString());
			}
			throw e;
		}

	}

	private void saveSystemLog(Method method, Object[] params,boolean isSuccess,Date startTime,String exception){
		try{
			SysLog annotation = method.getAnnotation(SysLog.class);
			ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
			String requestURI = null;
			String sourceIP = null;
			if(null != requestAttributes) {
				HttpServletRequest request = requestAttributes.getRequest();
				if(null != request) {
					requestURI = request.getRequestURI();
					sourceIP = IpAdrressUtil.getIpAdrress(request);
				}
			}
			CurrentUserDTO currentUser = SecurityContext.getUser();
			Long uid = null == currentUser ? null : currentUser.getUid();
			Long tenantId = null == currentUser ? null : currentUser.getTenantId();
			SystemLogDTO systemLog = new SystemLogDTO();
			systemLog.setLogType(annotation.type().getType());
			systemLog.setLogName(annotation.value());
			systemLog.setRequestUrl(requestURI);
			if(annotation.type().getType() != LogType.IMPORTDATA.getType() && params.length > 0){
				String paramStr = JSON.toJSONString(params);
				systemLog.setParams(paramStr);
			}
			systemLog.setStartTime(startTime);
			systemLog.setEndTime(new Date());
			systemLog.setState(isSuccess ? 1 : 0);
			systemLog.setAlarmState(0);
			systemLog.setException(exception);
			systemLog.setOperator(uid);
			systemLog.setTenantId(tenantId);
			systemLog.setSourceIP(sourceIP);
			if(null != uid && uid > 0) {
				SysUserDTO sysUser = sysUserFeginClient.getSysUserByUid(uid,tenantId).getData();
				systemLog.setOperatorName(null == sysUser ? null : sysUser.getUsername());
			}
			log.info("systemlog :{}", systemLog.toString());
			systemLogFeginClient.addLog(systemLog);
		}catch(Exception e){
			log.error("save system log error",e);
		}
	}

}
