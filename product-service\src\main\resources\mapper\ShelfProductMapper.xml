<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.product.mapper.ShelfProductMapper">

	<!-- 查询基础字段 -->
	<sql id="Base_Column_List">
		id,
  	    shelf_id,
  	    shelf_name,
  	    product_id,
  	    product_name,
  	    on_shelf_index,
  	    pd_type,
  	    delivery_type,
  	    be_show,
  	    show_type,
  	    on_inventory,
  	    current_inventory,
  	    exchange_num,
  	    cost_point,
  	    pre_point,
  	    cost_price,
  	    superscript,
  	    superscript_campaign_id,
  	    superscript_campaign,
  	    tenant_id,
  	    creator,
  	    created,
  	    modified,
  	    modifier
	</sql>

	<update id="updateInventoryByShelfProduct">
		update shelf_product
		set current_inventory = current_inventory + #{onInventory}
		where shelf_id = #{shelfId}
		  and product_id = #{productId}
	</update>

	<!-- 查询示例 -->
	<select id="selectDemo" parameterType="java.lang.Long" resultType="com.dz.ms.product.entity.ShelfProduct">
		select
		<include refid="Base_Column_List"/>
		from shelf_product
		where id = #{id,jdbcType=BIGINT}
		AND tenant_id = #{tenantId}
	</select>

	<select id="selectListByProductId" resultType="com.dz.ms.product.dto.ShelfDTO">
		SELECT DISTINCT s.*
		FROM shelf_product sp
				 LEFT JOIN shelf s ON sp.shelf_id = s.id
		WHERE sp.product_id = #{productId}
		  AND s.on_start_time &lt;= now()
		  AND s.on_end_time >= now()
		  AND s.state = 1
		  AND s.is_deleted = 0
		  AND sp.is_deleted = 0
		ORDER BY s.id desc
	</select>

	<select id="selectShelfNamesByProductId" resultType="java.lang.String">
		SELECT s.name
		FROM shelf_product sp
				 LEFT JOIN shelf s ON sp.shelf_id = s.id
		WHERE sp.product_id = #{productId}
		  AND s.on_start_time &lt;= now()
		  AND s.on_end_time >= now()
		  AND s.state = 1
		  AND s.is_deleted = 0
		  AND sp.is_deleted = 0
		ORDER BY s.id desc
	</select>

	<select id="selectProductSumByShelfIds" resultType="com.dz.common.core.dto.IdNumberDTO">
		SELECT sp.shelf_id as id, count(distinct product_id) number
		FROM shelf_product sp
		LEFT JOIN shelf s ON sp.shelf_id = s.id
		WHERE sp.shelf_id in
		<foreach collection="shelfIds" index="index" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		AND s.on_start_time &lt;= now() AND s.on_end_time >= now())
		AND s.state = 1
		AND s.is_deleted = 0
		AND sp.is_deleted = 0
		GROUP BY sp.shelf_id
	</select>

	<select id="selLessList" resultType="com.dz.ms.product.dto.ShelfProductDTO">
		SELECT sp.id, sp.shelf_id, sp.product_id, sp.be_show,sp.show_type,
		p.state productState
		FROM shelf_product sp
		left join product p on sp.product_id = p.id
		<where>
			sp.is_deleted = 0
			and p.is_deleted = 0
			<if test="null != shelfIds and shelfIds.size > 0">
				and sp.shelf_id in
				<foreach collection="shelfIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="null != productIds and productIds.size > 0">
				and sp.product_id in
				<foreach collection="productIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="null != shelfProductIds and shelfProductIds.size > 0">
				and sp.id in
				<foreach collection="shelfProductIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
		</where>
		order by on_shelf_index asc
	</select>

	<select id="selAllList" resultType="com.dz.ms.product.dto.ShelfProductDTO">
		SELECT distinct
			sp.id, sp.shelf_id,  sp.product_id, sp.on_shelf_index, sp.delivery_type, sp.be_show, sp.show_type,
			sp.on_inventory, sp.current_inventory, sp.exchange_num, sp.cost_point, sp.pre_point, sp.tenant_id, sp.created, sp.modified, sp.limit_num,
			s.name shelfName,
			p.product_name, p.state productState, p.pd_type, p.cost_price
		FROM shelf_product sp
		left join shelf s on sp.shelf_id = s.id
		left join product p on sp.product_id = p.id
		<where>
			sp.is_deleted = 0
			and p.is_deleted = 0
			<if test="null != shelfIds and shelfIds.size > 0">
				and sp.shelf_id in
				<foreach collection="shelfIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="null != productIds and productIds.size > 0">
				and sp.product_id in
				<foreach collection="productIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="null != shelfProductIds and shelfProductIds.size > 0">
				and sp.id in
				<foreach collection="shelfProductIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
		</where>
		order by on_shelf_index asc
	</select>

	<select id="selPointCorrelationsList" resultType="com.dz.ms.product.dto.ShelfProductDTO">
		SELECT
			sp.id,
			sp.shelf_id,
			sp.product_id,
			sp.on_shelf_index,
		sp.be_show,
		sp.show_type,
		sp.current_inventory,
			sp.cost_point,
			sp.pre_point,
			sp.limit_num,
			p.state productState,
			p.cost_price
		FROM shelf_product sp
		left join product p on sp.product_id = p.id
		<where>
			sp.is_deleted = 0
			and p.is_deleted = 0
			<if test="null != shelfIds and shelfIds.size > 0">
				and sp.shelf_id in
				<foreach collection="shelfIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="null != productIds and productIds.size > 0">
				and sp.product_id in
				<foreach collection="productIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
			<if test="null != shelfProductIds and shelfProductIds.size > 0">
				and sp.id in
				<foreach collection="shelfProductIds" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
		</where>
		order by on_shelf_index asc
	</select>

	<select id="selPageList" resultType="com.dz.ms.product.entity.ShelfProduct">
		select distinct
		sp.id, sp.shelf_id, sp.product_id, sp.on_shelf_index, sp.delivery_type, sp.be_show, sp.show_type,
		sp.on_inventory, sp.current_inventory, sp.cost_point, sp.pre_point, sp.created,sp.exchange_num,
		p.product_name, p.pd_type, p.cost_price
		FROM shelf_product sp
		left join product p on sp.product_id = p.id
		<where>
			sp.is_deleted = 0
			and p.is_deleted = 0
			<if test="param.shelfId != null">
				AND sp.shelf_id = #{param.shelfId}
			</if>
			<if test=" param.productName != null and param.productName != ''  ">
				and p.product_name like CONCAT('%', #{param.productName},'%')
			</if>
			<if test="param.pdType != null">
				AND p.pd_type = #{param.pdType}
			</if>
			<if test="param.state != null">
				AND p.state = #{param.state}
			</if>
			<if test="param.beShow != null">
				AND sp.be_show = #{param.beShow}
			</if>
			<if test="param.inventoryLess != null">
				and sp.current_inventory <![CDATA[ < ]]> #{param.inventoryLess}
			</if>
			<if test="param.idList != null and param.idList.size > 0">
				and sp.id in
				<foreach collection="param.idList" index="index" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
		</where>
		ORDER BY sp.on_shelf_index ASC
	</select>

	<select id="selMiniPageList" resultType="com.dz.ms.product.dto.res.ShelfProductMiniResDTO">
		SELECT distinct sp.id shelfProductId, sp.shelf_id, sp.product_id, sp.on_shelf_index, sp.pd_type,
		sp.delivery_type, sp.show_type, sp.current_inventory,
		COALESCE(rp.cost_point, sp.cost_point) AS costPoint, COALESCE(rp.pre_point, sp.pre_point) AS prePoint
		FROM shelf_product sp
		left join product p on sp.product_id = p.id
		left join product_tag pt on sp.product_id = pt.product_id
		left join shelf_campaign sc on sc.shelf_id = sp.shelf_id and sc.on_start_time <![CDATA[ <= ]]> now() and sc.on_end_time <![CDATA[ >= ]]> now() and sc.state = 1 and sc.is_deleted = 0
		left join shelf_campaign_rule_product rp on rp.campaign_id = sc.id and rp.shelf_product_id = sp.id and rp.is_deleted = 0 and rp.rule_created <![CDATA[ <= ]]> now()
		left join shelf_campaign_rule cr on cr.id = rp.rule_id and cr.is_deleted = 0
		<where>
			sp.is_deleted = 0
			and sp.be_show = 1
			and p.is_deleted = 0
			and p.state = 1
			and sp.shelf_id = #{param.shelfId}
			<if test=" param.productName != null and param.productName != ''  ">
				and p.product_name like CONCAT('%', #{param.productName},'%')
			</if>
			<if test="param.tagIdList != null and param.tagIdList.size > 0">
				and pt.tag_id in
				<foreach collection="param.tagIdList" index="index" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
			<if test="param.myPoint != null">
				and COALESCE(rp.cost_point, sp.cost_point) <![CDATA[ <= ]]> #{param.myPoint}
			</if>
			<if test="param.groupIdList != null and param.groupIdList.size > 0">
				and (
		    		cr.group_id is null 
					or cr.group_id in
					<foreach collection="param.groupIdList" index="index" item="id" open="(" close=")" separator=",">
						#{id}
					</foreach>
				)
			</if>
			<if test="param.scoreRangeList != null and param.scoreRangeList.size > 0">
				and (
					<foreach collection="param.scoreRangeList" item="range" separator=" OR ">
						(
		    				COALESCE(rp.cost_point, sp.cost_point) <![CDATA[ >= ]]> #{range.startNum} 
							AND COALESCE(rp.cost_point, sp.cost_point) <![CDATA[ <= ]]> #{range.endNum}
						)
					</foreach>
				)
			</if>
			<if test="param.exchangeType != null">
				<choose>
					<when test="param.exchangeType == 1">
						and p.cost_price = 0
					</when>
					<when test="param.exchangeType == 2">
						and p.cost_price <![CDATA[ > ]]> 0
					</when>
				</choose>
			</if>
			<choose>
				<when test="param.sortType != null and param.sortType == 1">
					ORDER BY COALESCE(rp.cost_point, sp.cost_point) asc, shelfProductId asc
				</when>
				<when test="param.sortType != null and param.sortType == 2">
					ORDER BY COALESCE(rp.cost_point, sp.cost_point) desc, shelfProductId desc
				</when>
				<otherwise>
					ORDER BY sp.on_shelf_index asc
				</otherwise>
			</choose>
		</where>
	</select>

	<select id="selLessListByIds" resultType="com.dz.ms.product.dto.ShelfProductDTO">
		SELECT sp.id, sp.shelf_id, sp.product_id, sp.current_inventory
		FROM shelf_product sp
		left join product p on sp.product_id = p.id
		<where>
			sp.is_deleted = 0
			and p.is_deleted = 0
			and sp.id in
			<foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</where>
	</select>

	<select id="validateInventory" resultType="java.lang.Integer">
		select
		count(id)
		from shelf_product
		<where>
			id = #{id}
			and is_deleted = 0
			and current_inventory >= #{num}
		</where>
	</select>

	<select id="selectByShelfProductIds" resultType="com.dz.common.core.dto.product.CpStaticDTO">
		SELECT
		sp.id 'shelfProductId',
		sp.product_id,
		SUM( sp.on_inventory ) 'onInventoryAmount',
		SUM( sp.current_inventory) 'restInventoryAmount'
		FROM
		shelf_product sp
		LEFT JOIN shelf s ON sp.shelf_id = s.id
		<where>
			<if test="shelfProductIdList != null and shelfProductIdList.size > 0">
				sp.id IN
				<foreach collection="shelfProductIdList" index="index" item="item" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
		</where>
		GROUP BY sp.id
	</select>

	<update id="updateInventory">
		update shelf_product
		set
		<if test="isAdd == 1">
			current_inventory = current_inventory + #{num}
		</if>
		<if test="isAdd == 2">
			current_inventory = current_inventory - #{num}
		</if>
		<where>
			id = #{id}
			and is_deleted = 0
			<if test="isAdd == 2">
				and current_inventory <![CDATA[ >= ]]> #{num}
			</if>
		</where>
	</update>
	<update id="updateStatic">
		update shelf_product
		set exchange_num = exchange_num + #{number}
		where id = #{shelfProductId}
	</update>

	<update id="updateShelfProduct" parameterType="com.dz.ms.product.entity.ShelfProduct">
		UPDATE shelf_product
		<set>
			<if test="null != shelfName and '' != shelfName">shelf_name = #{shelfName},</if>
			<if test="null != productName and '' != productName">product_name = #{productName},</if>
			<if test="null != pdType ">pd_type = #{pdType}</if>
		</set>
		<where>
			<if test="shelfId != null">
				AND shelf_id = #{shelfId}
			</if>
			<if test="product_id != null">
				AND product_id = #{productId}
			</if>
		</where>
	</update>

	<update id="updateAddInventory">
		update shelf_product
		set current_inventory = #{currentInventory}
		where id = #{id}
	</update>

</mapper>
