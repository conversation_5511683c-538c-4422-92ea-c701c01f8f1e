// 所有的命名必须全局唯一
import service from '@/utils/request.js'

// 获取抽奖活动列表
export function getLotteryList(data = {}) {
  return service({ url: '/crm/sales/lottery/list', method: 'get', data })
}
// 获取活动可关联任务列表
export function getInteractionTaskFestivalList() {
  return service({ url: '/crm/sales/interaction/task/festival/list', method: 'post' })
}
// 新增抽奖
export function addLottery(data = {}) {
  return service({ url: '/crm/sales/lottery/add', method: 'post', data })
}
// 获取抽奖活动详情
export function getLotteryInfo(id) {
  return service({ url: '/crm/sales/lottery/info?id=' + id, method: 'get' })
}
// 编辑抽奖活动
export function editLottery(data = {}) {
  return service({ url: '/crm/sales/lottery/edit', method: 'post', data })
}
// 删除抽奖活动
export function delLottery(data = {}) {
  return service({ url: '/crm/sales/lottery/del', method: 'post', data })
}
// 抽奖活动启用/停用
export function enableLottery(data = {}) {
  return service({ url: '/crm/sales/lottery/enable', method: 'post', data })
}
// 抽奖活动奖品启用/停用（编辑抽奖活动时调用，新增不调用）
export function prizeEnable(data = {}) {
  return service({ url: '/crm/sales/lottery/prize/enable', method: 'post', data })
}

// 库存管理-根据抽奖id查询商品库存列表
export function getLotteryPrizeList(data = {}) {
  return service({ url: '/crm/sales/lottery/lottery_prize', method: 'post', data })
}
// 库存管理-查询库存任务列表
export function getLotteryOnTaskList(data = {}) {
  return service({ url: '/crm/sales/lottery_on_task/list', method: 'get', data })
}
// 库存管理-根据id删除库存商品
export function delLotteryPrize(data = {}) {
  return service({ url: '/crm/sales/lottery_on_task/delete', method: 'post', data })
}
// 库存管理-编辑库存商品
export function editLotteryPrize(data = {}) {
  return service({ url: '/crm/sales/lottery_on_task/update', method: 'post', data })
}
/**
 * 库存管理-新增库存商品
 * @param data
 */
// public class LotteryOnTaskAddParamDTO {
//     @ApiModelProperty(value = "抽奖活动ID")
//     private Long lotteryId;
//     @ApiModelProperty(value = "奖品ID")
//     private Long lotteryPrizeId;
//     @ApiModelProperty(value = "奖品名称")
//     private String lotteryPrizeName;
//     @ApiModelProperty(value = "商品ID")
//     private Long productId;
//     @ApiModelProperty(value = "上架类型 1实时 2单次时间 3周期")
//     private Integer onType;
//     @ApiModelProperty(value = "上架类型为单次时间时时间点")
//     private Date onTime;
//     @ApiModelProperty(value = "上架类型为周期时周期天数")
//     private Integer onDays;
//     @ApiModelProperty(value = "上架库存数量 正+ 负-")
//     private Integer onInventory;

// }
export function addLotteryPrize(data = {}) {
  return service({ url: '/crm/sales/lottery_on_task/add', method: 'post', data })
}
