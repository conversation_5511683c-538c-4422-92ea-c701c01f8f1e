package com.dz.ms.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Data;
import org.jetbrains.annotations.NotNull;

import java.util.Date;
import java.util.Objects;

@Data
@Table("预约场次配置")
@TableName(value = "appointment_slot_config")
public class AppointmentSlotConfig implements Comparable<AppointmentSlotConfig> {
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "场次配置ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "预约ID")
    private Long appointmentId;

    @Columns(type = ColumnType.VARCHAR, length = 256, isNull = false, comment = "日期 yyyy-MM-dd")
    private String date;

    @Columns(type = ColumnType.VARCHAR, length = 256, isNull = false, comment = "时段")
    @TableField(exist = false)
    private String slot;

    @Columns(type = ColumnType.VARCHAR, length = 256, isNull = false, comment = "开始时间")
    private String startTime;

    @Columns(type = ColumnType.VARCHAR, length = 256, isNull = false, comment = "结束时间")
    private String endTime;

    @Columns(type = ColumnType.INT, length = 10, isNull = false, comment = "初始库存")
    private Integer initialStock;

    @Columns(type = ColumnType.INT, length = 10, isNull = false, comment = "剩余库存")
    private Integer remainingStock;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;

    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;

    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;

    public String getSlot() {
        return startTime + "-" + endTime;
    }

    @Override
    public int compareTo(@NotNull AppointmentSlotConfig other) {
        return Objects.compare(this.date, other.date, String::compareTo);
    }
}