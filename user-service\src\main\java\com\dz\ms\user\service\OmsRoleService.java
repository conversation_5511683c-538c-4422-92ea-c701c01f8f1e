package com.dz.ms.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.ms.user.dto.RolePermissionDTO;
import com.dz.ms.user.entity.OmsRole;

import java.util.List;

/**
 * OMS-系统角色接口
 * @author: Handy
 * @date:   2020/01/29 19:15
 */
public interface OmsRoleService extends IService<OmsRole> {

    /**
     * 获取角色权限列表
     * @param roleId
     * @return
     */
    public List<String> getRolePermitCodes(Long roleId);

    /**
     * 获取角色权限ID列表
     * @param roleId
     * @return
     */
    public List<Long> getRolePermitIds(Long roleId);

    /**
     * 绑定角色权限
     * @param param
     * @param uid
     */
    public void bindPermit(RolePermissionDTO param, Long uid);

}