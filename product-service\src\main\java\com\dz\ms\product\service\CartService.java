package com.dz.ms.product.service;

import com.dz.common.core.dto.product.CartDTO;
import com.dz.common.core.dto.product.CartProductDTO;
import com.dz.common.core.dto.product.CartResultDTO;
import com.dz.ms.product.dto.CartCacheDTO;

import java.util.List;

/**
 * 购物车接口
 */
public interface CartService {

    /**
     * 获取用户购物车列表
     *
     * @return
     */
    CartResultDTO getUserCartList(boolean fromCart);

    /**
     * 获取用户购物车表架构商品列表
     *
     * @param userId
     * @return
     */
    List<CartProductDTO> getUserCartList(Long userId, Long cartId);

    /**
     * 添加购物车
     *
     * @param cartDTO
     */
    void addUserCart(CartDTO cartDTO);

    /**
     * 修改购物车信息
     *
     * @param cartDTO
     */
    void editUserCart(CartDTO cartDTO);

    /**
     * 购物车全选
     */
    void checkedAllUserCart(Integer status);

    /**
     * 删除购物车商品
     *
     * @param ids
     */
    void deleteUserCart(String ids);

    /**
     * 获取订单预览商品列表
     *
     * @return
     */
    CartResultDTO getPreviewCartOrder();

    /**
     * 单商品购买订单预览
     *
     * @param specId
     * @return
     */
    CartResultDTO getCartOrderByShelfProductId(Long specId, Integer num);

    /**
     * 获取用户购物车商品总数
     *
     * @return
     */
    Integer getUserCartCount();

    /**
     * 清空已下单购物车商品
     */
    void cleanUserOrderCart();

    CartCacheDTO getUserCartCache();

    /**
     * 清空购物车失效商品
     */
    void cleanDisabledProduct();
}
