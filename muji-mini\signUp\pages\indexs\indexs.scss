/* signUp/pages/indexs/indexs.wxss */
.page-container {
  background-size: 100% 100%;


  .index {
    flex: 1;
    height: 0;
    // overflow-y: auto;
    // overflow-x: hidden;

    margin: 27rpx 49rpx env(safe-area-inset-bottom);
    // width: calc(100% - 98rpx);
    width: auto;

    // position: relative;
    .content {
      background: #F8F6ED;
      display: flex;
      flex-direction: column;
    }

    .header {
      padding-top: 48rpx;
      font-family: MUJIFont2020,
        SourceHanSansCN;
      font-weight: 700;
      font-size: 36rpx;
      color: var(--text-black-color);
      line-height: 52rpx;
      letter-spacing: 3px;
      margin-left: 47rpx;
    }

    .index-form {
      flex: 1;
      height: 0;
      margin: 0 47rpx;
      position: relative;

      // height: 100%;
      .form-title {
        width: 168rpx;
        height: 50rpx;
        background: #E3D8BB;
        border-radius: 29rpx 29rpx 29rpx 29rpx;
        font-family: MUJIFont2020,
          SourceHanSansCN;
        font-weight: 700;
        font-size: 24rpx;
        color: var(--text-black-color);
        line-height: 50rpx;
        letter-spacing: 2px;
        text-align: center;
        margin-top: 101rpx;
      }

      .form-title1 {
        margin-top: 55rpx;
      }

      .form-title2 {
        margin-top: 104rpx;
      }

      .form-tips-last {
        font-family: MUJIFont2020,
          SourceHanSansCN;
        font-weight: 300;
        font-size: 22rpx;
        line-height: 32rpx;
        letter-spacing: 2rpx;
        text-align: left;
        font-style: normal;
        text-transform: none;
        padding-bottom: 149rpx;
      }

      .form-tips {
        font-family: MUJIFont2020,
          SourceHanSansCN;
        font-weight: 500;
        font-size: 26rpx;
        color: #000000;
        line-height: 38rpx;
        letter-spacing: 2rpx;
        text-align: left;
        padding-left: 5rpx;
        margin-top: 7rpx;
        // text-indent: -0.8em;
        // margin-left: 47rpx;
      }

      .form-tips1 {
        // padding-left: 21rpx;
        padding-bottom: 60rpx;

      }

      .form-item {
        border-bottom: 1rpx solid #D8D8D8;
        margin-bottom: 30rpx;
        padding-bottom: 10rpx;
        // margin-left: 20rpx;
        // margin-right: 19rpx;

        .item-label {
          color: var(--text-gray-color);
          font-family: MUJIFont2020,
            SourceHanSansCN;
          font-weight: 500;
          font-size: 26rpx;
          color: var(--text-black-color);
          line-height: 38rpx;
          letter-spacing: 2rpx;
          color: var(--text-black-color);
          text-align: left;
          font-style: normal;
        }

        .item-content {
          margin-top: 10rpx;
          color: var(--text-black-color);
          display: flex;
          justify-content: space-between;
          font-family: MUJIFont2020,
            SourceHanSansCN;
          font-weight: 350;
          font-size: 26rpx;
          line-height: 38rpx;
          letter-spacing: 2rpx;
          text-align: left;
          font-style: normal;
          font-weight: 350;

          input {
            width: 100%;
          }

          .radio-group {
            display: flex;
            font-size: 28rpx;
            color: #979797;
            right: 30rpx;

            .radio-item {
              display: flex;
              align-items: center;
              font-family: MUJIFont2020,
                MUJIFont2020;
              font-weight: 400;
              font-size: 28rpx;
              line-height: 40rpx;
              letter-spacing: 1px;
              color: #979797;
              text-align: left;
              font-style: normal;
              height: 28rpx;
              position: relative;

              &:first-child {
                margin-right: 60rpx;
              }

              .radio {
                width: 28rpx;
                height: 28rpx;
                border: 1rpx solid var(--border-black-color);
                border-radius: 50%;
                margin-right: 20rpx;
              }

              .active {
                border: 1rpx solid var(--btn-primary);
                position: relative;
              }

              .active::before {
                content: '';
                width: 20rpx;
                height: 20rpx;
                background: var(--btn-primary);
                display: block;
                border-radius: 50%;
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
              }
            }
          }

          .item-text {
            flex: 1;
          }

          .icon-button {
            height: 36rpx;
            width: 36rpx;
          }

          .auth-btn {
            width: 200rpx;
            height: 60rpx;
            background: #F5F5F5;
            border-radius: 5rpx;
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 24rpx;
            color: var(--text-black-color);
            line-height: 60rpx;
            text-align: center;
            font-style: normal;
          }

          .item-input {
            font-family: MUJIFont2020,
              SourceHanSansCN;
            height: 28rpx;
            font-weight: 350;
            font-size: 26rpx;
            color: var(--text-black-color);
            line-height: 38rpx;
            letter-spacing: 2rpx;
            text-align: left;
            font-style: normal;

            &::placeholder {
              color: var(--txt-placeholder-color);
              font-weight: 350;
            }
          }

          .without-data {
            color: var(--text-placeholder-color);
          }
        }

        .address {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          // margin-top: 42rpx;
          margin-bottom: 10rpx;
          align-items: flex-end;

          .getAddress {
            text-decoration: underline;
            text-underline-offset: 3rpx;
            font-weight: 400;
            font-size: 19rpx;
            // line-height: 39rpx;
            letter-spacing: 1px;
            text-align: left;
            display: flex;
            align-items: center;
            // padding: 20rpx;
            // padding-top: 45rpx;
            padding-bottom: 0rpx;
          }
        }
      }

      .red {
        color: red;
      }
    }

    .item-value {
      &.disabled {
        color: #888;
      }
    }

    .check {
      height: 29rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: MUJIFont2020,
        SourceHanSansCN;
      font-weight: 350;
      font-size: 20rpx;
      font-weight: 350;
      color: var(--text-black-color);
      line-height: 29rpx;
      text-align: center;

      .iconfont {
        font-size: 20rpx;
        margin-right: 13rpx;
      }

      .link {
        font-family: MUJIFont2020,
          SourceHanSansCN;
        font-weight: 350;
        font-size: 20rpx;
        color: #BFA37D;
        line-height: 29rpx;
        text-align: center;
        font-style: normal;
        text-transform: none;
      }

      .radio {
        border-radius: 50%;
        color: var(--text-black-color);
      }
    }
  }

  // .form-footer {
  //   left: 50%;
  //   transform: translateX(-50%);
  //   // text-align: center;
  //   position: absolute;
  //   bottom: 46rpx;
  // }

  .bottom-box {
    margin-top: 9rpx;
    margin-bottom: 46rpx;
    display: flex;
    justify-content: center;
  }

}
