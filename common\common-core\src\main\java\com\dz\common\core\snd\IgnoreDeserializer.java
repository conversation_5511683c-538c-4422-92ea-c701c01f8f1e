package com.dz.common.core.snd;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class IgnoreDeserializer extends JsonDeserializer {

    private static final String DELIMITER = ";"; // 与序列化时的分隔符保持一致

    @Override
    public Object deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String input = p.getText();
        if (input == null || input.isEmpty()) {
            return new ArrayList<>();
        }
        String[] elements = input.split(DELIMITER);
        List<String> result = new ArrayList<>();
        result.addAll(Arrays.asList(elements));
        return result;
    }
}
