package com.dz.ms.user.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

@Component
@Slf4j
public class QYWXUtils {


  public static void outPutToFile(String fileName, String response) throws Exception {
    File f = new File(fileName);
    try (FileWriter fw = new FileWriter(f, true);
         BufferedWriter out = new BufferedWriter(fw)) {
      out.write(response, 0, response.length() - 1);
    }
  }
}





