package com.dz.ms.adaptor.controller;

import com.alibaba.fastjson.JSONObject;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.adaptor.ThirdPartyRecordVo;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.CommonUtils;
import com.dz.ms.adaptor.dto.MqCrmPointsSyncMessageDTO;
import com.dz.ms.adaptor.dto.ThirdPartRecordMqDTO;
import com.dz.ms.adaptor.entity.ThirdDKeyAmRecord;
import com.dz.ms.adaptor.entity.ThirdPartyRecord;
import com.dz.ms.adaptor.mapper.ThirdDKeyAmRecordMapper;
import com.dz.ms.adaptor.mapper.ThirdPartyRecordMapper;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Api(tags="第三方接口请求记录")
@Slf4j
@RestController
public class ThirdPartyRecordController {

    @Resource
    private ThirdPartyRecordMapper thirdPartyRecordMapper;
    @Resource
    private ThirdDKeyAmRecordMapper thirdDKeyAmRecordMapper;
    @Autowired
    private RedisService redisService;
    @Autowired
    private RabbitTemplate rabbitTemplate;

    /**
     * 第三方接口请求记录
     * @return
     */
    @PostMapping(value = "/third/party/record")
    public Result<Boolean> thridPartRecord(@RequestBody ThirdPartyRecordVo param) {
        if(Objects.isNull(param)){
            return new Result<Boolean>().errorResult("");
        }
        Date now = new Date();
        SimpleDateFormat sdfTable = new SimpleDateFormat("yyyyMM");
        String nowDay = sdfTable.format(now);
        String tableName = subThirdPartTableList(nowDay,param.getNum());
        if (StringUtils.isEmpty(tableName)){
            return new Result<Boolean>().errorResult("");
        }
        log.info("第三方接口请求记录入参:"+ JSONObject.toJSONString(param));
        Result<Boolean> result = new Result<>();
        ThirdPartyRecord thirdPartyRecord = BeanCopierUtils.convertObject(param, ThirdPartyRecord.class);
        ThirdPartRecordMqDTO mqDTO = new ThirdPartRecordMqDTO();
        mqDTO.setThirdPartyRecord(thirdPartyRecord);
        mqDTO.setTenantId(SecurityContext.getUser().getTenantId());
        mqDTO.setCreateTime(now);
        mqDTO.setTaleName(tableName);
        rabbitTemplate.convertAndSend("adaptor.crmapi", "adaptor_crmapi", CommonUtils.jsonStr(mqDTO));
        result.setData(true);
        return result;
    }

    /**
     * 第三方宁盾接口请求记录
     * @return
     */
    @PostMapping(value = "/third/dKeyAm/record")
    public Result<Boolean> thirdDKeyAmRecord(@RequestBody ThirdPartyRecordVo param) {
        if(Objects.isNull(param)){
            return new Result<Boolean>().errorResult("");
        }
        log.info("第三方宁盾接口请求记录入参:"+ JSONObject.toJSONString(param));
        Result<Boolean> result = new Result<>();
        ThirdDKeyAmRecord thirdDKeyAmRecord = BeanCopierUtils.convertObject(param, ThirdDKeyAmRecord.class);
        for (int i = 0; i < 5; i++) {
            try {
                int a= thirdDKeyAmRecordMapper.insert(thirdDKeyAmRecord);
                if (a>0){
                    break;
                }
            }catch (Exception e){
                log.info("第三方宁盾接口:"+param.getApiDesc()+",请求记录插入失败");
            }
        }
        result.setData(true);
        return result;
    }

    private String subThirdPartTableList(String cdpDay, Integer num) {
        String tableName=num+"_"+cdpDay;
        try {
            String cdpDayRedis=redisService.getString("crm:api:url:"+tableName);
            if (!StringUtils.isEmpty(cdpDayRedis)){
                return cdpDayRedis;
            }
            List<String> tableList = thirdPartyRecordMapper.queryThirdPartyRecordTableList(tableName);
            boolean hasCurrentTable = false;
            if (!CollectionUtils.isEmpty(tableList)) {
                for (String table : tableList) {
                    String newTableName="t_third_party_record_"+tableName;
                    if (newTableName.equals(table)) {
                        hasCurrentTable = true;
                        redisService.setString("crm:api:url:"+tableName,tableName);
                    }
                }
            }
            if (!hasCurrentTable) {
                thirdPartyRecordMapper.createThirdPartyRecordTable(tableName);
                redisService.setString("crm:api:url:"+tableName,tableName);
            }
            return tableName;
        } catch (Exception e) {
            try {
                String cdpDayRedis=redisService.getString("crm:api:url:"+tableName);
                if (!StringUtils.isEmpty(cdpDayRedis)){
                    return cdpDayRedis;
                }
                List<String> tableList = thirdPartyRecordMapper.queryThirdPartyRecordTableList(tableName);
                boolean hasCurrentTable = false;
                if (!CollectionUtils.isEmpty(tableList)) {
                    for (String table : tableList) {
                        String newTableName="t_third_party_record_"+tableName;
                        if (newTableName.equals(table)) {
                            hasCurrentTable = true;
                            redisService.setString("crm:api:url:"+tableName,tableName);
                        }
                    }
                }
                if (!hasCurrentTable) {
                    thirdPartyRecordMapper.createThirdPartyRecordTable(tableName);
                    redisService.setString("crm:api:url:"+tableName,tableName);
                }
                return tableName;
            } catch (Exception eq) {
                log.error("t_third_party_record创建失败", eq);
                return null;
            }
        }
    }
}
