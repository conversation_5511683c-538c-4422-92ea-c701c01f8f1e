
// 所有的命名必须全局唯一
import service from '@/utils/request.js'

//



// 列表
export function grade_infoLista(data) {
    return service({
        url: '/crm/user/grade_info/list',
        method: 'get',
        data
    })
}
// 列表 新增
export function grade_infoAdd(data) {
    return service({
        url: '/crm/user/grade_info/add',
        method: 'post',
        data
    })
}

// 列表编辑
export function grade_infoUpdate(data) {
    return service({
        url: '/crm/user/grade_info/update',
        method: 'post',
        data
    })
}
// 列表 删除
export function grade_infoDelete(data) {
    return service({
        url: '/crm/user/grade_info/delete',
        method: 'post',
        data
    })
}

export function grade_infoInfo(data) {
    return service({
        url: '/crm/user/grade_info/info',
        method: 'get',
        data
    })
}
//修改状态

export function grade_infoupdate_state(data) {
    return service({
        url: '/crm/user/grade_info/update_state',
        method: 'post',
        data
    })
}