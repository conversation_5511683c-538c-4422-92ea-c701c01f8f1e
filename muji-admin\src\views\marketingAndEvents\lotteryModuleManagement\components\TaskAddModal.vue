<template>
  <a-modal
    v-model:open="visible"
    @ok="handleOk"
    @cancel="handleClose"
    :confirmLoading="thisFields.loading"
    width="1200px"
  >
    <a-form :model="searchFields" layout="inline">
      <a-form-item label="奖品名称" name="prizesName">
        <a-input
          placeholder="请输入"
          allow-clear
          v-model:value="searchFields.prizesName"
          allowClear
          @keyup.enter="whenClickSearch"
        ></a-input>
      </a-form-item>
      <a-form-item label="奖品类型" name="prizesType">
        <a-select
          v-model:value="searchFields.prizesType"
          allow-clear
          placeholder="请选择"
          :options="PRIZES_TYPE_ARR"
          style="width: 200px"
        />
      </a-form-item>
      <div style="flex-grow: 1"></div>
      <a-space>
        <a-button type="primary" @click="whenClickSearch"> 查询 </a-button>
        <a-button @click="whenClickReset"> 重置 </a-button>
      </a-space>
    </a-form>
    <a-table
      :row-selection="{
        preserveSelectedRowKeys: true,
        selectedRowKeys: thisFields.selectedRowKeys,
        onChange: onSelectChange
      }"
      row-key="id"
      :scroll="{ scrollToFirstRowOnChange: true, x: '100%' }"
      :dataSource="dataSource"
      :columns="tableHeader"
      :pagination="pagination"
      :loading="loading"
      @change="whenPaginationChange"
    >
      <template #bodyCell="{ text, record, index, column }">
        <template v-if="column.dataIndex === 'imageUrl'">
          <a-image v-if="record.imageUrl" :src="record.imageUrl" :width="50" :height="50" />
          <span v-else>-</span>
        </template>
      </template>
    </a-table>
  </a-modal>
</template>
<script setup>
import { usePagination } from 'vue-request'
import { getLotteryPrizeList, addLotteryPrize } from '@/http/index.js'
import { watch } from 'vue'
import { PRIZES_TYPE_ARR, PRIZES_TYPE_OBJ } from '../utils/constant'
import { message } from 'ant-design-vue'

const visible = defineModel('visible', Boolean)
const props = defineProps(['lotteryId'])
const emits = defineEmits(['ok'])

watch(
  () => visible.value,
  (value) => {
    console.log('value: ', value)
    if (value) {
      whenClickReset()
    }
  }
)

const total = ref(0)
const pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ['bottomLeft']
  }
})

const getDefaultSearchFields = () => ({
  prizesType: undefined,
  prizesClass: undefined,
  prizesName: undefined
})
const searchFields = reactive({})
const thisFields = reactive({
  selectedRowKeys: [],
  selectedRowObjs: [],
  loading: false
})
const tableHeader = [
  { title: '序号', dataIndex: '_index', align: 'center', width: 80 },
  { title: '奖品名称', dataIndex: 'prizesName', align: 'center', ellipsis: true, width: 140 },
  { title: '奖品类型', dataIndex: '_prizesType', align: 'center', ellipsis: true, width: 100 },
  { title: '奖品图片', dataIndex: 'imageUrl', align: 'center', ellipsis: true, width: 80 },
  { title: '上架库存', dataIndex: 'totalStock', align: 'center', ellipsis: true, width: 80 },
  { title: '积分价值', dataIndex: 'pointsNum', align: 'center', ellipsis: true, width: 80 },
  { title: '剩余库存', dataIndex: 'remainStock', align: 'center', ellipsis: true, width: 80 }
]
const {
  data: dataSource,
  run,
  loading,
  pageSize,
  current,
  refresh
} = usePagination(
  (param) => getLotteryPrizeList({ ...param, ...searchFields, lotteryId: props.lotteryId }),
  {
    manual: true, // 修改为false 让其自动执行
    pagination: {
      currentKey: 'pageNum', // 通过该值指定接口 当前页数 参数的属性值
      pageSizeKey: 'pageSize', // 通过该值指定接口 每页获取条数 参数的属性值
      totalKey: 'data.data.count' // 指定 data 中 total 属性的路径
    },
    formatResult: (res) => {
      const { count, list } = res.data
      // 返回数据格式化
      total.value = count

      return list.map((item, index) => {
        Object.assign(item, {
          _index: (current.value - 1) * pageSize.value + 1 + index,
          _prizesType: PRIZES_TYPE_OBJ[item.prizesType]
        })
        return item
      })
    }
  }
)
const whenPaginationChange = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}
const whenClickSearch = () => {
  run({ pageNum: 1, pageSize: pageSize.value })
}
const whenClickReset = () => {
  Object.assign(searchFields, getDefaultSearchFields())
  whenClickSearch()
}
function onSelectChange(e, a) {
  // console.log('onSelectChange：', e, a)
  thisFields.selectedRowKeys = e
  thisFields.selectedRowObjs = a
}
function handleClose() {
  thisFields.selectedRowKeys = []
  thisFields.selectedRowObjs = []
}
async function handleOk() {
  const { selectedRowObjs } = thisFields
  if (!selectedRowObjs.length) return message.error('请至少选择一条数据')
  thisFields.loading = true
  const list = thisFields.selectedRowObjs.map((v) => {
    return {
      lotteryId: props.lotteryId,
      lotteryPrizeId: v.id,
      lotteryPrizeName: v.prizesName,
      productId: v.productId,
      onType: 1,
      onTime: null,
      onDays: 0,
      onInventory: 0
    }
  })
  try {
    await addLotteryPrize(list)
    visible.value = false
    thisFields.loading = false
    handleClose()
    message.success('添加成功')
    emits('ok')
  } catch {
    thisFields.loading = false
  }
}
</script>

<style scoped lang="scss">
.ant-form {
  padding: 20px 20px 20px 0px;
}
</style>
