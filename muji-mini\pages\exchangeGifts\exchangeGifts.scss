/* pages/exchangeGifts/exchangeGifts.wxss */
.nav-btn {
  width: 217rpx;
  height: 62rpx;
  background: #FAFAFA;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  margin-left: var(--page-margin);

  .btn {
    width: 50%;
    text-align: center;
    font-size: 40rpx;
  }

  .divided {
    display: block;
    width: 1rpx;
    height: 20rpx;
    background: #3A3D45;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}


.page-content {
  box-sizing: border-box;
  padding: 0 var(--page-margin);
  position: relative;

  .total-box {
    width: 174rpx;
    height: 33rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 24rpx;
    color: #3C3C43;
    line-height: 33rpx;
    text-align: left;
    font-style: normal;
    margin-bottom: 30rpx;
  }

  .product-list {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    flex: 1;
    overflow-y: auto;
    row-gap: 10rpx;

    .product-item {
      width: 330rpx;
    }
  }
}
