package com.dz.ms.product.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.config.RedisDistributedLock;
import com.dz.common.core.constants.NumConstants;
import com.dz.common.core.dto.*;
import com.dz.common.core.dto.adaptor.SftpDTO;
import com.dz.common.core.dto.export.CpStaticExportDTO;
import com.dz.common.core.dto.export.ProductExportDTO;
import com.dz.common.core.dto.product.CpStaticDTO;
import com.dz.common.core.dto.product.CpStaticParamDTO;
import com.dz.common.core.dto.product.OdsItemDTO;
import com.dz.common.core.dto.product.ProductCouponDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.order.ExchangeOrderFeignClient;
import com.dz.common.core.service.ExportService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.CustomMultipartFile;
import com.dz.common.core.utils.ParamUtils;
import com.dz.common.core.utils.SftpUtils;
import com.dz.ms.product.dto.*;
import com.dz.ms.product.dto.req.CrmProductListParamDTO;
import com.dz.ms.product.dto.req.ProductChiefInfoParamDTO;
import com.dz.ms.product.dto.req.ShelfCampaignRuleProductDelParamDTO;
import com.dz.ms.product.dto.res.ProductChiefInfoDTO;
import com.dz.ms.product.entity.Material;
import com.dz.ms.product.entity.OdsItem;
import com.dz.ms.product.entity.Product;
import com.dz.ms.product.entity.ProductTag;
import com.dz.ms.product.mapper.MaterialMapper;
import com.dz.ms.product.mapper.ProductMapper;
import com.dz.ms.product.mapper.ProductTagMapper;
import com.dz.ms.product.mapper.ShelfProductMapper;
import com.dz.ms.product.service.ProductService;
import com.dz.ms.product.service.ShelfCampaignRuleProductService;
import com.dz.ms.product.service.ShelfProductService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商品信息
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:25
 */
@Service
@Slf4j
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {
    @Value("${sftp.config.host}")
    private String host;
    @Value("${sftp.config.port}")
    private String port;
    @Value("${sftp.config.userName}")
    private String userName;
    @Value("${sftp.config.password}")
    private String password;
    @Resource
    private ProductMapper productMapper;
    @Resource
    private MaterialMapper materialMapper;
    @Resource
    private ProductTagMapper productTagMapper;
    @Resource
    private ShelfProductMapper shelfProductMapper;
    @Resource
    private ExportService exportService;
    @Resource
    private ShelfProductService shelfProductService;
    @Resource
    private ShelfCampaignRuleProductService shelfCampaignRuleProductService;
    @Resource
    private RedisDistributedLock redisDistributedLock;
    @Resource
    private ExchangeOrderFeignClient exchangeOrderFeignClient;

    /**
     * 分页查询商品信息
     *
     * @param param
     * @return PageInfo<ProductDTO>
     */
    @Override
    public PageInfo<CrmProductListDTO> getProductList(CrmProductListParamDTO param) {
        IPage<Product> page = productMapper.selectPageByParam(new Page<>(param.getPageNum(), param.getPageSize()), param);
        List<CrmProductListDTO> productList = BeanCopierUtils.convertList(page.getRecords(), CrmProductListDTO.class);
        for (CrmProductListDTO product : productList) {
            setMaterialTag(product);
            // 所在货架
            List<ShelfDTO> shelfDTOList = shelfProductMapper.selectListByProductId(product.getId());
            product.setShelfList(shelfDTOList);
        }

        return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), productList);
    }

    /**
     * 商品图片,标签赋值
     */
    private void setMaterialTag(CrmProductListDTO product) {
        // 商品图片
        LambdaQueryWrapper<Material> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Material::getProductId, product.getId());
        List<Material> materialList = materialMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(materialList)) {
            Map<Integer, List<Material>> materialGroup = materialList.stream().collect(Collectors.groupingBy(Material::getUsedType));
            // 1场景图 2橱窗图 3兑换须知图 4商品详情图 5使用说明图
            List<Material> scenceMaterials = materialGroup.get(NumConstants.ONE);
            if (!CollectionUtils.isEmpty(scenceMaterials)) {
                List<String> scenceImgList = scenceMaterials.stream().map(Material::getImgUrl).collect(Collectors.toList());
                product.setScenceImgList(scenceImgList);
            }
            List<Material> shelfImgMaterials = materialGroup.get(NumConstants.TWO);
            if (!CollectionUtils.isEmpty(shelfImgMaterials)) {
                List<String> shelfImgList = shelfImgMaterials.stream().map(Material::getImgUrl).collect(Collectors.toList());
                product.setShelfImgList(shelfImgList);
            }
        }
        // 商品标签信息
        LambdaQueryWrapper<ProductTag> productTagLambdaQueryWrapper = new LambdaQueryWrapper<>();
        productTagLambdaQueryWrapper.eq(ProductTag::getProductId, product.getId());
        productTagLambdaQueryWrapper.orderByAsc(ProductTag::getCate);
        productTagLambdaQueryWrapper.orderByDesc(ProductTag::getTagId);
        List<ProductTag> productTagList = productTagMapper.selectList(productTagLambdaQueryWrapper);
        if (!CollectionUtils.isEmpty(productTagList)) {
            List<TagInfoDTO> tagList = new ArrayList<>();
            for (ProductTag tag : productTagList) {
                TagInfoDTO tagInfoDTO = new TagInfoDTO();
                tagInfoDTO.setId(tag.getTagId());
                tagInfoDTO.setName(tag.getTagName());
                tagInfoDTO.setCate(tag.getCate());
                tagList.add(tagInfoDTO);
            }
            product.setTagList(tagList);
        }
    }

    @Override
    public PageInfo<ProductChiefInfoDTO> getProductChiefInfoList(ProductChiefInfoParamDTO param) {
        IPage<Product> page = productMapper.selectPageByParam(new Page<>(param.getPageNum(), param.getPageSize()), param);
        List<ProductChiefInfoDTO> productList = BeanCopierUtils.convertList(page.getRecords(), ProductChiefInfoDTO.class);
        if (CollectionUtils.isEmpty(productList)) {
            return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), productList);
        }
        List<List<ProductChiefInfoDTO>> partition = Lists.partition(productList, 500);
        for (List<ProductChiefInfoDTO> part : partition) {
            List<Long> productIdList = part.stream().map(ProductChiefInfoDTO::getId).collect(Collectors.toList());
            // 批量查询素材
            LambdaQueryWrapper<Material> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(Material::getProductId, productIdList);
            if (Objects.nonNull(param.getOrderBy()) && Objects.equals(param.getOrderBy(), NumConstants.TWO)) {
                queryWrapper.orderByDesc(Material::getId);
            } else {
                queryWrapper.orderByAsc(Material::getId);
            }
            List<Material> materials = materialMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(materials)) {
                materials = new ArrayList<>();
            }
            Map<Long, List<Material>> materialsByProductId = materials.stream().collect(Collectors.groupingBy(Material::getProductId));
            // 批量查询标签
            List<ProductTag> productTagList = null;
            if (Objects.nonNull(param.getShowTags()) && param.getShowTags()) {
                LambdaQueryWrapper<ProductTag> productTagLambdaQueryWrapper = new LambdaQueryWrapper<>();
                productTagLambdaQueryWrapper.in(ProductTag::getProductId, productIdList);
                productTagLambdaQueryWrapper.orderByAsc(ProductTag::getCate);
                productTagLambdaQueryWrapper.orderByDesc(ProductTag::getTagId);
                productTagList = productTagMapper.selectList(productTagLambdaQueryWrapper);
            }
            Map<Long, List<ProductTag>> productTagListByProductId = new HashMap<>();
            if (!CollectionUtils.isEmpty(productTagList)) {
                productTagListByProductId = productTagList.stream().collect(Collectors.groupingBy(ProductTag::getProductId));
            }
            for (ProductChiefInfoDTO product : part) {
                // 商品图片
                List<Material> materialList = materialsByProductId.get(product.getId());
                if (!CollectionUtils.isEmpty(materialList)) {
                    Map<Integer, List<Material>> materialGroup = materialList.stream().collect(Collectors.groupingBy(Material::getUsedType));
                    // 1场景图 2橱窗图 3兑换须知图 4商品详情图 5使用说明图
                    List<Material> sceneImgMaterials = materialGroup.get(NumConstants.ONE);
                    List<Material> shelfImgMaterials = materialGroup.get(NumConstants.TWO);
                    if (!CollectionUtils.isEmpty(sceneImgMaterials)) {
                        List<String> sceneImgList = sceneImgMaterials.stream().map(Material::getImgUrl).collect(Collectors.toList());
                        // 首张商品场景图
                        product.setSceneImg(sceneImgList.get(NumConstants.ZERO));
                        // 商品主图
                        product.setShelfImg(product.getSceneImg());
                    }
                    if (!CollectionUtils.isEmpty(shelfImgMaterials)) {
                        List<String> shelfImgList = shelfImgMaterials.stream().map(Material::getImgUrl).collect(Collectors.toList());
                        // 首张商品橱窗图
                        product.setShopWindowImg(shelfImgList.get(NumConstants.ZERO));
                        if(StringUtils.isBlank(product.getShelfImg())){
                            // 商品主图
                            product.setShelfImg(product.getShopWindowImg());
                        }
                    }
                }
                // 商品标签信息
                if (Objects.nonNull(param.getShowTags()) && param.getShowTags()) {
                    List<ProductTag> productTags = productTagListByProductId.get(product.getId());
                    if (!CollectionUtils.isEmpty(productTags)) {
                        List<TagInfoDTO> tagList = new ArrayList<>();
                        for (ProductTag tag : productTags) {
                            TagInfoDTO tagInfoDTO = new TagInfoDTO();
                            tagInfoDTO.setId(tag.getTagId());
                            tagInfoDTO.setName(tag.getTagName());
                            tagInfoDTO.setCate(tag.getCate());
                            tagList.add(tagInfoDTO);
                        }
                        product.setTagList(tagList);
                    }
                }
            }
        }

        return new PageInfo<>(page.getCurrent(), page.getSize(), page.getTotal(), productList);
    }

    /**
     * 根据id列表查询商品信息
     * @param qryProductParam
     * ids id列表
     * @return List<ProductChiefInfoDTO>
     */
    @Override
    public List<ProductChiefInfoDTO> getProductChiefInfoListByIds(ProductChiefInfoParamDTO qryProductParam) {
        List<ProductChiefInfoDTO> list = new ArrayList<>();
        if (Objects.isNull(qryProductParam.getShowTags())) {
            qryProductParam.setShowTags(false);
        }
        if (Objects.isNull(qryProductParam.getOrderBy())) {
            qryProductParam.setOrderBy(NumConstants.ONE);
        }
        qryProductParam.setPageNum(NumConstants.ONE);
        qryProductParam.setPageSize(qryProductParam.getProductIdList().size());
        PageInfo<ProductChiefInfoDTO> productChiefInfoList = this.getProductChiefInfoList(qryProductParam);
        if (!CollectionUtils.isEmpty(productChiefInfoList.getList())) {
            list = productChiefInfoList.getList();
        }
        return list;
    }

    /**
     * 根据ID查询商品信息
     *
     * @param id
     * @return ProductDTO
     */
    @Override
    public ProductDTO getProductById(Long id) {
        Product product = productMapper.selectById(id);
        ProductDTO productDTO = BeanCopierUtils.convertObject(product, ProductDTO.class);
        LambdaQueryWrapper<ProductTag> productTagLambdaQueryWrapper = new LambdaQueryWrapper<>();
        productTagLambdaQueryWrapper.eq(ProductTag::getProductId, product.getId());
        productTagLambdaQueryWrapper.orderByDesc(ProductTag::getTagId);
        List<ProductTag> productTagList = productTagMapper.selectList(productTagLambdaQueryWrapper);
        if (!CollectionUtils.isEmpty(productTagList)) {
            Map<Integer, List<ProductTag>> tagListMap = productTagList.stream().collect(Collectors.groupingBy(ProductTag::getCate));
            List<ProductTag> tags1 = Optional.ofNullable(tagListMap.get(NumConstants.ONE)).orElse(new ArrayList<>());
            List<ProductTag> tags2 = Optional.ofNullable(tagListMap.get(NumConstants.TWO)).orElse(new ArrayList<>());
            List<TagInfoDTO> tagInfos1 = new ArrayList<>();
            for (ProductTag tag : tags1) {
                TagInfoDTO tagInfo = new TagInfoDTO();
                tagInfo.setId(tag.getTagId());
                tagInfo.setName(tag.getTagName());
                tagInfo.setCate(tag.getCate());
                tagInfos1.add(tagInfo);
            }
            List<TagInfoDTO> tagInfos2 = new ArrayList<>();
            for (ProductTag tag : tags2) {
                TagInfoDTO tagInfo = new TagInfoDTO();
                tagInfo.setId(tag.getTagId());
                tagInfo.setName(tag.getTagName());
                tagInfo.setCate(tag.getCate());
                tagInfos2.add(tagInfo);
            }
            productDTO.setTags1(tagInfos1);
            productDTO.setTags2(tagInfos2);
        }
        LambdaQueryWrapper<Material> materialLambdaQueryWrapper = new LambdaQueryWrapper<>();
        materialLambdaQueryWrapper.eq(Material::getProductId, product.getId());
        List<Material> materialList = materialMapper.selectList(materialLambdaQueryWrapper);
        if (!CollectionUtils.isEmpty(materialList)) {
            Map<Integer, List<Material>> materialGroup = materialList.stream().collect(Collectors.groupingBy(Material::getUsedType));
            // 1场景图 2橱窗图 3兑换须知图 4商品详情图 5使用说明图
            List<Material> scenceMaterials = materialGroup.get(NumConstants.ONE);
            if (!CollectionUtils.isEmpty(scenceMaterials)) {
                List<String> scenceImgList = scenceMaterials.stream().map(Material::getImgUrl).collect(Collectors.toList());
                productDTO.setScenceImgUrl(String.join(",", scenceImgList));
            }
            List<Material> shelfImgMaterials = materialGroup.get(NumConstants.TWO);
            if (!CollectionUtils.isEmpty(shelfImgMaterials)) {
                List<String> shelfImgList = shelfImgMaterials.stream().map(Material::getImgUrl).collect(Collectors.toList());
                productDTO.setShelfImgUrl(String.join(",", shelfImgList));
            }
            List<Material> exchangeDescMaterialList = materialGroup.get(NumConstants.THREE);
            if (!CollectionUtils.isEmpty(exchangeDescMaterialList)) {
                List<String> exchangeDescImgList = exchangeDescMaterialList.stream().map(Material::getImgUrl).collect(Collectors.toList());
                productDTO.setExchangeDescUrl(String.join(",", exchangeDescImgList));
            }
            List<Material> detailMaterials = materialGroup.get(NumConstants.FOUR);
            if (!CollectionUtils.isEmpty(detailMaterials)) {
                List<String> detailImgList = detailMaterials.stream().map(Material::getImgUrl).collect(Collectors.toList());
                productDTO.setDetailsUrl(String.join(",", detailImgList));
            }
            List<Material> referMaterials = materialGroup.get(NumConstants.FIVE);
            if (!CollectionUtils.isEmpty(referMaterials)) {
                List<String> referImgList = referMaterials.stream().map(Material::getImgUrl).collect(Collectors.toList());
                productDTO.setReferUrl(String.join(",", referImgList));
            }
        }
        return productDTO;
    }

    /**
     * 保存商品信息
     *
     * @param param
     * @return Long
     */
    @Override
    @Transactional
    public Long saveProduct(ProductDTO param) {
        Product product = new Product(param.getId(), param.getProductName(), param.getSubTitle(), param.getProductCode(), param.getPdType(), param.getBeGift(), param.getItemId(), param.getVenderId(), param.getScenceImgUrl(), param.getShelfImgUrl(), param.getDetails(), param.getPrePrice(), param.getOriginPrice(), param.getPrice(), param.getPurchaseType(), param.getCostPoint(), param.getCostPrice(), param.getCostPriceOnShelf(), param.getExchangeNum(), param.getExchangeDescType(), param.getExchangeDescUrl(), param.getExchangeDescContent(), param.getDetailsType(), param.getDetailsUrl(), param.getDetailsContent(), param.getReferType(), param.getReferUrl(), param.getReferContent(), param.getState());
        if (ParamUtils.isNullOr0Long(product.getId())) {
//            Integer exist = productMapper.selectExist(null, param.getVenderId());
//            if (exist > 0) {
//                throw new BusinessException(ErrorCode.BAD_REQUEST, "CP编号已存在");
//            }
            productMapper.insert(product);
        } else {
//            Integer exist = productMapper.selectExist(product.getId(), param.getVenderId());
//            if (exist > 0) {
//                throw new BusinessException(ErrorCode.BAD_REQUEST, "CP编号已存在");
//            }
            LambdaUpdateWrapper<Product> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Product::getId, product.getId());
            productMapper.update(product, updateWrapper);
            shelfProductService.updShelfProductName(ShelfProductDTO.builder().productId(product.getId()).productName(product.getProductName()).pdType(product.getPdType()).build());
        }
        // 商品图片 usedType 1场景图 2橱窗图 3兑换须知图 4商品详情图 5使用说明图
        List<Material> materialList = new ArrayList<>();
        CurrentUserDTO user = SecurityContext.getUser();
        if (StringUtils.isNotBlank(param.getScenceImgUrl())) {
            String[] images = param.getScenceImgUrl().split(",");
            for (String img : images) {
                Material material = new Material(null, NumConstants.ONE, product.getId(), product.getProductCode(), NumConstants.ONE, img, "empty");
                material.setTenantId(user.getTenantId());
                material.setCreator(user.getUid());
                material.setModifier(user.getUid());
                materialList.add(material);
            }
        }
        if (StringUtils.isNotBlank(param.getShelfImgUrl())) {
            String[] images = param.getShelfImgUrl().split(",");
            for (String img : images) {
                Material material = new Material(null, NumConstants.ONE, product.getId(), product.getProductCode(), NumConstants.TWO, img, "empty");
                material.setTenantId(user.getTenantId());
                material.setCreator(user.getUid());
                material.setModifier(user.getUid());
                materialList.add(material);
            }
        }
        if (StringUtils.isNotBlank(param.getExchangeDescUrl())) {
            String[] images = param.getExchangeDescUrl().split(",");
            for (String img : images) {
                Material material = new Material(null, NumConstants.ONE, product.getId(), product.getProductCode(), NumConstants.THREE, img, "empty");
                material.setTenantId(user.getTenantId());
                material.setCreator(user.getUid());
                material.setModifier(user.getUid());
                materialList.add(material);
            }
        }
        if (StringUtils.isNotBlank(param.getDetailsUrl())) {
            String[] images = param.getDetailsUrl().split(",");
            for (String img : images) {
                Material material = new Material(null, NumConstants.ONE, product.getId(), product.getProductCode(), NumConstants.FOUR, img, "empty");
                material.setTenantId(user.getTenantId());
                material.setCreator(user.getUid());
                material.setModifier(user.getUid());
                materialList.add(material);
            }
        }
        if (StringUtils.isNotBlank(param.getReferUrl())) {
            String[] images = param.getReferUrl().split(",");
            for (String img : images) {
                Material material = new Material(null, NumConstants.ONE, product.getId(), product.getProductCode(), NumConstants.FIVE, img, "empty");
                material.setTenantId(user.getTenantId());
                material.setCreator(user.getUid());
                material.setModifier(user.getUid());
                materialList.add(material);
            }
        }
        LambdaQueryWrapper<Material> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Material::getProductId, product.getId());
        materialMapper.delete(queryWrapper);
        if (!CollectionUtils.isEmpty(materialList)) {
            materialMapper.insertBatch(materialList);
        }
        // 商品标签
        List<ProductTag> productTagList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(param.getTags1())) {
            for (TagInfoDTO tag : param.getTags1()) {
                ProductTag productTag = new ProductTag(null, product.getId(), product.getProductName(), tag.getId(), tag.getName(), NumConstants.ONE);
                productTag.setTenantId(user.getTenantId());
                productTag.setCreator(user.getUid());
                productTag.setModifier(user.getUid());
                productTagList.add(productTag);
            }
        }
        if (!CollectionUtils.isEmpty(param.getTags2())) {
            for (TagInfoDTO tag : param.getTags2()) {
                ProductTag productTag = new ProductTag(null, product.getId(), product.getProductName(), tag.getId(), tag.getName(), NumConstants.TWO);
                productTag.setTenantId(user.getTenantId());
                productTag.setCreator(user.getUid());
                productTag.setModifier(user.getUid());
                productTagList.add(productTag);
            }
        }
        LambdaQueryWrapper<ProductTag> productTagLambdaQueryWrapper = new LambdaQueryWrapper<>();
        productTagLambdaQueryWrapper.eq(ProductTag::getProductId, product.getId());
        productTagMapper.delete(productTagLambdaQueryWrapper);
        if (!CollectionUtils.isEmpty(productTagList)) {
            productTagMapper.insertBatch(productTagList);
        }
        return product.getId();
    }

    /**
     * 根据ID删除商品信息
     *
     * @param param
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProductById(IdCodeDTO param) {
        Product info = productMapper.selectById(param.getId());
        if (info == null) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[商品]未查询到此商品");
        }
        if (Objects.equals(info.getState(), NumConstants.ONE)) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[商品]启用中的商品不能被删除");
        }
        productMapper.deleteById(param.getId());
        shelfProductService.deleteByProductId(param);
        shelfCampaignRuleProductService.deleteRuleProduct(ShelfCampaignRuleProductDelParamDTO.builder().productId(param.getId()).build());
    }

    @Override
    public Integer updateStateById(IdNumberDTO param) {
        Product info = productMapper.selectById(param.getId());
        if (info == null) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "[商品]未查询到此商品");
        }
        info.setState(param.getNumber());
        productMapper.updateById(info);
        return param.getNumber();
    }

    @Override
    public void exportProductList(String jsonParam, String reportCode, String fileName, String fileExt, Long downloadCenterId) {
        CurrentUserDTO commonLoginDTO = SecurityContext.getUser();
        CrmProductListParamDTO crmProductListParamDTO = JSON.parseObject(jsonParam, CrmProductListParamDTO.class);
        List<JSONObject> reList = getProductExportDTO(crmProductListParamDTO);
        exportService.writeExportList(fileName, fileExt, reportCode, downloadCenterId, commonLoginDTO, reList);
    }

    @Override
    public List<ProductCouponDTO> getProductListByPdType() {
        List<Product> productList = productMapper.selectByPdType();
        List<ProductCouponDTO> productCouponDTOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(productList)) {
            productCouponDTOS = BeanCopierUtils.convertList(productList, ProductCouponDTO.class);
        }
        return productCouponDTOS;
    }

    @Override
    public int updateStatic(Long productId, Integer number) {
        String lockKey = "redis_distributed_lock:product:" + productId;
        String requestId = UUID.randomUUID().toString();
        if (redisDistributedLock.tryGetDistributedLockWithRetry(lockKey, requestId, RedisDistributedLock.EXPIRE_TIME, RedisDistributedLock.RETRY_TIMES, RedisDistributedLock.SLEEP_TIME)) {
            try {
                return productMapper.updateStatic(productId, number);
            } finally {
                redisDistributedLock.releaseDistributedLock(lockKey, requestId);
            }
        } else {
            // 处理获取锁失败的情况
            throw new BusinessException("获取锁失败");
        }
    }

    @Override
    public void uploadMujiGoodsAndCouponsAndOrdersExcel(String afterDate) throws ParseException {
        Calendar startDate = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (StringUtils.isBlank(afterDate)) {
            // 获取昨天的日期
            startDate.add(Calendar.DAY_OF_MONTH, -1);
        } else {
            startDate.setTime(formatter.parse(afterDate));
        }
        // 设置昨天的开始时间（00:00:00）
        startDate.set(Calendar.HOUR_OF_DAY, 0);
        startDate.set(Calendar.MINUTE, 0);
        startDate.set(Calendar.SECOND, 0);
        startDate.set(Calendar.MILLISECOND, 0);
        Date beginTime = startDate.getTime();

        // 获取昨天的日期
        Calendar yesterday = Calendar.getInstance();
        yesterday.add(Calendar.DAY_OF_MONTH, -1);
        // 设置昨天的结束时间（23:59:59）
        yesterday.set(Calendar.HOUR_OF_DAY, 23);
        yesterday.set(Calendar.MINUTE, 59);
        yesterday.set(Calendar.SECOND, 59);
        yesterday.set(Calendar.MILLISECOND, 999);
        Date endTime = yesterday.getTime();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String fileName = "redeem_order" + sdf.format(new Date());
        String originalFilename = fileName + ".xlsx";
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelWriterBuilder excelWriterBuilder = EasyExcel.write(outputStream).excelType(ExcelTypeEnum.XLSX);
        List<MujiGoods> mujiGoodsList = productMapper.selectMujiGoods(beginTime, endTime);
        List<MujiStockInfo> mujiStockInfoList = productMapper.selectMujiStockInfo(beginTime, endTime);
        List<MujiOrder> mujiOrderList = exchangeOrderFeignClient.selectMujiOrder(formatter.format(beginTime), formatter.format(endTime));
        try (ExcelWriter writer = excelWriterBuilder.build()) {
            WriteSheet mujiGoodsSheet = EasyExcel.writerSheet("crm积分商城-商品信息").head(MujiGoods.class).build();
            WriteSheet mujiStocksSheet = EasyExcel.writerSheet("crm积分商城-商品对应的券信息").head(MujiStockInfo.class).build();
            WriteSheet mujiOrderSheet = EasyExcel.writerSheet("crm积分商城-兑换明细").head(MujiOrder.class).build();
            writer.write(mujiGoodsList, mujiGoodsSheet);
            writer.write(mujiStockInfoList, mujiStocksSheet);
            writer.write(mujiOrderList, mujiOrderSheet);
            writer.finish();
        }
        CustomMultipartFile customMultipartFile = new CustomMultipartFile(outputStream.toByteArray(), fileName, originalFilename, "application/vnd.openxmlformats - officedocument.spreadsheetml.sheet");
        SftpUtils.uploadFileForBatchImport(customMultipartFile, "gift_orders", host, userName, password, port);
    }

    @Override
    public PageInfo<CpStaticDTO> cpStatic(CpStaticParamDTO param) {
        PageInfo<CpStaticDTO> cpStaticDTOPage = exchangeOrderFeignClient.cpStatic(param);
        if (CollectionUtils.isEmpty(cpStaticDTOPage.getList())) {
            return new PageInfo<>();
        }
        List<CpStaticDTO> cpStaticDTOList = cpStaticDTOPage.getList();
        List<Long> shelfProductIdList = cpStaticDTOList.stream().map(CpStaticDTO::getShelfProductId).collect(Collectors.toList());
        List<CpStaticDTO> productPriceList = productMapper.selectByShelfProductIds(shelfProductIdList);
        List<CpStaticDTO> productInventoryList = shelfProductMapper.selectByShelfProductIds(shelfProductIdList);
        Map<Long, CpStaticDTO> productMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(productPriceList)) {
            productMap = productPriceList.stream().collect(Collectors.toMap(CpStaticDTO::getShelfProductId, Function.identity()));
        }
        Map<Long, CpStaticDTO> productInventoryMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(productInventoryList)) {
            productInventoryMap = productInventoryList.stream().collect(Collectors.toMap(CpStaticDTO::getShelfProductId, Function.identity()));
        }
        for (CpStaticDTO cpStaticDTO : cpStaticDTOList) {
            CpStaticDTO pd = productMap.get(cpStaticDTO.getShelfProductId());
            CpStaticDTO pdInventory = productInventoryMap.get(cpStaticDTO.getShelfProductId());
            if (Objects.nonNull(pd)) {
                cpStaticDTO.setCostPoint(pd.getCostPoint());
                cpStaticDTO.setOriginPrice(pd.getOriginPrice());
                cpStaticDTO.setPrePrice(pd.getPrePrice());
            }
            if (Objects.nonNull(pdInventory)) {
                cpStaticDTO.setOnInventoryAmount(pdInventory.getOnInventoryAmount());
                cpStaticDTO.setRestInventoryAmount(pdInventory.getRestInventoryAmount());
            }
        }
        return new PageInfo<>(cpStaticDTOPage.getPageNum(), cpStaticDTOPage.getPageSize(), cpStaticDTOPage.getCount(), cpStaticDTOPage.getList());
    }

    @Override
    public void exportCpStatic(DownloadAddParamDTO param) {
        CurrentUserDTO commonLoginDTO = SecurityContext.getUser();
        CpStaticParamDTO cpStaticParam = JSON.parseObject(param.getJsonParam(), CpStaticParamDTO.class);
        if (StringUtils.isEmpty(cpStaticParam.getCpCode())
                && Objects.isNull(cpStaticParam.getSellDateStart())
                && Objects.isNull(cpStaticParam.getSellDateEnd())) {
            throw new BusinessException(ErrorCode.INVALID_ARGUMENT, "参数不能全为空");
        }
        List<JSONObject> reList = getCpStaticDTO(cpStaticParam);
        exportService.writeExportList(param.getFileName(), param.getFileExt(), param.getReportCode(), param.getDownloadCenterId(), commonLoginDTO, reList);
    }

    @Override
    public void getSftpFile() throws IOException {
        //
        Date date = new Date();
        // 指定日期格式
        LocalDate yesterday = LocalDate.now().minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String formattedDate = yesterday.format(formatter);
        String fileName = "item_" + formattedDate + ".csv";
        SftpDTO sftpDTO = new SftpDTO();
        sftpDTO.setFilePath("/item");
        //sftpDTO.setFileName("item_20250108.csv");
        sftpDTO.setFileName(fileName);
        sftpDTO.setHost(host);
        sftpDTO.setPort(Integer.valueOf(port));
        sftpDTO.setUserName(userName);
        sftpDTO.setPass(password);
        SftpDTO sftpFile = SftpUtils.getSftpFile(sftpDTO);
        //获取文件
        if (sftpFile != null) {
            dealSftpItemData(sftpFile.getIn());
            sftpFile.getIn().close();
            SftpUtils.sessionClose();
            SftpUtils.sftpClose(sftpFile.getSftp());
        }
    }

    @Override
    public List<OdsItemDTO> selectBySftpProductItemId(String itemId) {
        return productMapper.selectBySftpProductItemId(itemId);
    }

    private static final int BATCH_SIZE = 1000; // 每批处理的数据量

    private void dealSftpItemData(InputStream in) throws IOException {
        SecurityContext.setUser(new CurrentUserDTO(1L, 999L));
        try (Reader reader = new InputStreamReader(in);
             CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT.withFirstRecordAsHeader())) {

            productMapper.deleteAllOdsItem(); // 删除所有旧数据

            List<OdsItem> odsItems = new ArrayList<>(BATCH_SIZE);

            for (CSVRecord csvRecord : csvParser) {
                OdsItem odsItem = new OdsItem();
                odsItem.setId(UUID.randomUUID().toString());
                odsItem.setItemIdStr(csvRecord.get("item_id_str"));
                odsItem.setDeptIdStr(csvRecord.get("dept_id_str"));
                odsItem.setDeptNameStr(csvRecord.get("dept_name_str"));
                odsItem.setDepaIdStr(csvRecord.get("depa_id_str"));
                odsItem.setDepaNameStr(csvRecord.get("depa_name_str"));
                odsItem.setLineIdStr(csvRecord.get("line_id_str"));
                odsItem.setLineNameStr(csvRecord.get("line_name_str"));
                odsItem.setClassIdStr(csvRecord.get("class_id_str"));
                odsItem.setClassNameStr(csvRecord.get("class_name_str"));
                odsItem.setItemNameStr(csvRecord.get("item_name_str"));
                odsItem.setTaxRateStr(csvRecord.get("tax_rate_str"));
                odsItem.setCostPriceStr(csvRecord.get("cost_price_str"));
                odsItem.setSpuIdStr(csvRecord.get("spu_id_str"));
                odsItem.setSpuNameStr(csvRecord.get("spu_name_str"));
                //odsItem.setSeriesIdStr(csvRecord.get("seriesid_str"));
                odsItem.setBizDate(csvRecord.get("biz_date"));
                odsItem.setCreateTime(new Date());
                odsItem.setUpdateTime(new Date());
                odsItem.setTenantId(1L);
                odsItems.add(odsItem);

                // 每处理 BATCH_SIZE 条数据后，执行一次批量插入操作，并清空列表
                if (odsItems.size() >= BATCH_SIZE) {
                    productMapper.insertBatchOdsItem(odsItems);
                    odsItems.clear();
                }
            }

            // 处理剩余数据
            if (!odsItems.isEmpty()) {
                productMapper.insertBatchOdsItem(odsItems);
            }
        }
    }

    private List<JSONObject> getCpStaticDTO(CpStaticParamDTO cpStaticParam) {
        cpStaticParam.setPageNum(1);
        cpStaticParam.setPageSize(Integer.MAX_VALUE);
        List<CpStaticDTO> cpStaticDTOS = cpStatic(cpStaticParam).getList();
        List<CpStaticExportDTO> cpList = BeanCopierUtils.convertList(cpStaticDTOS, CpStaticExportDTO.class);
        return new ArrayList<>(JSON.parseArray(JSON.toJSONString(cpList), JSONObject.class));
    }

    /**
     * 根据ids查询少量字段
     */
    @Override
    public List<ProductDTO> selLessListByIds(List<Long> ids) {
        List<ProductDTO> list = productMapper.selLessListByIds(ids);
        if (!CollectionUtils.isEmpty(list)) {
            return list;
        }
        return new ArrayList<>();
    }

    private List<JSONObject> getProductExportDTO(CrmProductListParamDTO crmProductListParamDTO) {
        List<ProductExportDTO> productList = productMapper.selectListByParam(crmProductListParamDTO);
        if (CollectionUtils.isEmpty(productList)) {
            return new ArrayList<>();
        }
        for (ProductExportDTO product : productList) {
            // 商品图片
            LambdaQueryWrapper<Material> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Material::getProductId, product.getId());
            List<Material> materialList = materialMapper.selectList(queryWrapper);
            if (!CollectionUtils.isEmpty(materialList)) {
                Map<Integer, List<Material>> materialGroup = materialList.stream().collect(Collectors.groupingBy(Material::getUsedType));
                // 1场景图 2橱窗图 3兑换须知图 4商品详情图 5使用说明图
                List<Material> scenceMaterials = materialGroup.get(NumConstants.ONE);
                if (!CollectionUtils.isEmpty(scenceMaterials)) {
                    List<String> scenceImgList = scenceMaterials.stream().map(Material::getImgUrl).collect(Collectors.toList());
                    product.setScenceImgListStr(StringUtils.join(scenceImgList, ","));
                }
                List<Material> shelfImgMaterials = materialGroup.get(NumConstants.TWO);
                if (!CollectionUtils.isEmpty(shelfImgMaterials)) {
                    List<String> shelfImgList = shelfImgMaterials.stream().map(Material::getImgUrl).collect(Collectors.toList());
                    product.setShelfImgListStr(StringUtils.join(shelfImgList, ","));
                }
            }
            // 商品标签信息
            LambdaQueryWrapper<ProductTag> productTagLambdaQueryWrapper = new LambdaQueryWrapper<>();
            productTagLambdaQueryWrapper.eq(ProductTag::getProductId, product.getId());
            productTagLambdaQueryWrapper.orderByAsc(ProductTag::getCate);
            productTagLambdaQueryWrapper.orderByDesc(ProductTag::getTagId);
            List<ProductTag> productTagList = productTagMapper.selectList(productTagLambdaQueryWrapper);
            if (!CollectionUtils.isEmpty(productTagList)) {
                List<TagInfoDTO> tagList = new ArrayList<>();
                for (ProductTag tag : productTagList) {
                    TagInfoDTO tagInfoDTO = new TagInfoDTO();
                    tagInfoDTO.setId(tag.getTagId());
                    tagInfoDTO.setName(tag.getTagName());
                    tagInfoDTO.setCate(tag.getCate());
                    tagList.add(tagInfoDTO);
                }
                product.setTagListStr(StringUtils.join(tagList, ","));
            }
            // 所在货架
            List<String> shelfDTOList = shelfProductMapper.selectShelfNamesByProductId(product.getId());
            product.setShelfListStr(StringUtils.join(shelfDTOList, ","));
        }
        return new ArrayList<>(JSON.parseArray(JSON.toJSONString(productList), JSONObject.class));
    }

}
