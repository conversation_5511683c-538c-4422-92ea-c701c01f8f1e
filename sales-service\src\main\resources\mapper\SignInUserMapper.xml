<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.sales.mapper.SignInUserMapper">




    <update id="signInUserFail">
        update sign_in_user set state = 2, modified = now(), modifier = #{userId} where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="signInUserFailByEndTime">
        update sign_in_user set state = 2, modified = now(), modifier = #{userId} where state = 0 and end_activity_time &lt; now()
    </update>

    <select id="notSignInByToday" resultType="com.dz.ms.sales.entity.SignInUser">
        SELECT t1.* from sign_in_user t1
        WHERE id NOT in (
        SELECT DISTINCT sign_in_user_id
        FROM sign_in_user_detail
        WHERE sign_in_date = DATE_FORMAT(now(), '%Y%m%d') and days > 0
        )
        and t1.state = 0 and t1.end_activity_time >= now() and t1.is_deleted = 0
        and campaign_code = #{campaignCode}
    </select>
</mapper>
