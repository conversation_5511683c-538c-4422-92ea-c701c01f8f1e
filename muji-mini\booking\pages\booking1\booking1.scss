@import "assets/scss/config";

.pageWrap {
  min-height: 100vh;
  position: relative;

  &.isNotBooked {
    .mainImage {
      width: 100%;
      min-height: 670rpx;
    }

    .mainContent {
      padding: 50rpx 37rpx 0;
    }

    .title {
      font-weight: 700;
      font-size: 40rpx;
      color: #3C3C43;
      line-height: 50rpx;
      margin-bottom: 20rpx;
    }

    .subtitle {
      font-weight: 400;
      font-size: 24rpx;
      color: #3C3C43;
      line-height: 38rpx;
    }

    .form {
      margin-top: 50rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #3C3C43;

      .formItem {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 40rpx;

        &:last-of-type {
          margin-bottom: 0;
        }
      }

      .formSelectWrap {
        border-radius: 5rpx 5rpx 5rpx 5rpx;
        border: 2rpx solid #D8D8D9;
        background: #FFFFFF;
        width: 315rpx;
        height: 90rpx;
        box-sizing: border-box;
        padding: 27rpx 30rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;

        &.isDisabled {
          pointer-events: none;
          color: #d8d8d8;
        }

        .arrow {
          width: 0;
          height: 0;
          border: 13rpx solid transparent;
          border-bottom: none;
          border-top-color: currentColor;
        }
      }

      .formInputWrap {
        display: flex;
        align-items: center;
        border-radius: 5rpx 5rpx 5rpx 5rpx;
        border: 2rpx solid #D8D8D9;
        background: #FFFFFF;
        height: 90rpx;
        flex: 1;
        box-sizing: border-box;
        padding: 27rpx 30rpx;

        .formInputLabel {
          width: 160rpx;
        }

        .formInputValue {
          flex: 1;
          color: inherit;
        }
      }
    }

    .resetBtn {
      text-align: center;
      margin-top: 160rpx;
    }

    .btnDesc {
      margin-top: 20rpx;
      font-weight: 400;
      font-size: 22rpx;
      color: #3D3D3D;
      line-height: 38rpx;
      text-align: center;
    }

    .noPermission {
      text-align: center;

      .icon {
        width: 120rpx;
        height: 120rpx;
        margin-top: 200rpx;
      }

      .text {
        margin-top: 20rpx;
        font-weight: 700;
        font-size: 33rpx;
        color: #888888;
        line-height: 50rpx;
      }
    }
  }

  &.isAlreadyBooked {
    background: #eeeeee;

    .mainWrap {
      padding: 26rpx 40rpx 0;
    }

    .mainContent {
      background: #ffffff;
    }

    .logoWrap {
      padding: 40rpx 40rpx 0;
    }

    .logo {
      width: 590rpx;
      height: 528rpx;
    }

    .item {
      padding: 50rpx 40rpx;
      border-bottom: 1rpx dashed #D8D8D8;
      position: relative;

      &.isGrey {
        background: rgba(245, 245, 245, 0.4);
      }

      &:last-of-type {
        border-bottom: none;
      }

      &:nth-of-type(1) {
        &::before,
        &::after {
          content: '';
          display: block;
          width: 30rpx;
          height: 30rpx;
          background: #eeeeee;
          border-radius: 50%;
          position: absolute;
          bottom: 0;
          z-index: 2;
        }

        &::before {
          left: 0;
          transform: translateX(-50%) translateY(50%);
        }

        &::after {
          right: 0;
          transform: translateX(50%) translateY(50%);
        }
      }
    }

    .title {
      font-weight: 700;
      font-size: 40rpx;
      color: #3C3C43;
      line-height: 66rpx;
    }

    .subtitle {
      margin-top: 5rpx;
      font-weight: 400;
      font-size: 22rpx;
      color: #888888;
      line-height: 28rpx;
    }

    .title2 {
      font-weight: 700;
      font-size: 26rpx;
      color: #3C3C43;
      line-height: 52rpx;
    }

    .subtitle2 {
      margin-top: 5rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #3C3C43;
      line-height: 32rpx;
    }

    .title3 {
      font-weight: 400;
      font-size: 24rpx;
      color: #888888;
      line-height: 31rpx;
      margin-bottom: 5rpx;
    }

    .subtitle3 {
      font-weight: 400;
      font-size: 24rpx;
      color: #3C3C43;
      line-height: 31rpx;
      margin-bottom: 30rpx;

      &:last-of-type {
        margin-bottom: 0;
      }
    }
  }

  &.isHidden {
    display: none;
  }
}
