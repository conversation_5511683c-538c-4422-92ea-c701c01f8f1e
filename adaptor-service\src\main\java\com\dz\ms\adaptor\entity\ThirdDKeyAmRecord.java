package com.dz.ms.adaptor.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Table;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 请求宁盾接口的记录表
 */
@Data
@NoArgsConstructor
@Table("请求宁盾接口的记录表")
@TableName(value = "t_third_d_key_am_record")
public class ThirdDKeyAmRecord {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     * 接口名称
     */
    private String apiDesc;


    /**
     * 接口路径
     */
    private String apiUrl;


    /**
     * 参数
     */
    private String param;


    /**
     * 返回结果
     */
    private String result;


    /**
     * 创建时间
     */
    private Date createTime;


    /**
     * 状态 0失败 1成功
     */
    private Integer status;


    /**
     * 失败原因
     */
    private String failDesc;


    /**
     * 租户ID
     */
    private Long tenantId;


    /**
     * 请求耗时
     */
    private Long requestTime;
}
