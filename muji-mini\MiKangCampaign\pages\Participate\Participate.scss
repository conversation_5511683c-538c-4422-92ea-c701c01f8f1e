/* MiKangCampaign/pages/Participate/Participate.wxss */
.page-container {
    background: linear-gradient(
        rgba(255, 255, 255, 0) 1%,
        rgba(245, 232, 210, 1) 100%
    );
    // background: rgba(245, 232, 210, 1);
    background-size: 100% 100%;

    .page-main {
        position: absolute;
        left: 0;
        top: 0;
        right: 0;
        z-index: 1;
        width: 100%;
        height: 100vh;
        overflow-y: auto;
        overflow-x: hidden;
        font-family: MUJIFont2020;

        // display: flex;
        // flex-direction: column;
        // justify-content: flex-end;
        .page_wrap {
            width: 100%;
            position: relative;

            .page-back {
                width: 100%;
            }

            .page-bottom {
                width: 100%;
                position: absolute;
                bottom: 0rpx;

                .page-content {
                    pointer-events: none;
                    margin: 0 8rpx;
                    margin-bottom: 72rpx;
                    width: 734rpx;
                    // height: 550rpx;
                }
            }
        }
    }

    .activeRules {
        position: fixed;
        right: 0rpx;
        top: 216rpx;
        width: 70rpx;
        height: 146rpx;
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .activeRules-in {
            width: 58rpx;
            height: 160rpx;
            background: #ab8e6166;
            font-family: MUJIFont2020;
            font-weight: 700;
            font-size: 28rpx;
            line-height: 24rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            writing-mode: vertical-rl;
            letter-spacing: 4rpx;
        }
    }

    .activeRules2 {
        position: fixed;
        right: 0rpx;
        top: 400rpx;
        width: 70rpx;
        height: 146rpx;
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .activeRules2-in {
            width: 58rpx;
            height: 160rpx;
            background: #ab8e6166;
            font-family: MUJIFont2020;
            font-weight: 700;
            font-size: 28rpx;
            color: #ffffff;
            line-height: 24rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            writing-mode: vertical-rl;
            letter-spacing: 4rpx;
        }
    }

    .page-rule {
        //position: absolute;
        z-index: 10;

        position: fixed;
        right: 0rpx;
        top: 216rpx;
        width: 58rpx;
        height: 160rpx;
        background: #ab8e6166;
        font-family: MUJIFont2020;
        font-weight: 700;
        font-size: 28rpx;
        color: #ffffff;
        line-height: 24rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        writing-mode: vertical-rl;
        letter-spacing: 4rpx;
        border-radius: 16rpx 0rpx 0rpx 16rpx;
        margin: 10rpx 0rpx 10rpx 20rpx;
    }

    .page-zhongjiang {
        z-index: 10;

        position: fixed;
        right: 0rpx;
        top: 400rpx;
        width: 58rpx;
        height: 160rpx;
        background: #ab8e6166;
        font-family: MUJIFont2020;
        font-weight: 700;
        font-size: 28rpx;
        color: #ffffff;
        line-height: 24rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        writing-mode: vertical-rl;
        letter-spacing: 4rpx;
        border-radius: 16rpx 0rpx 0rpx 16rpx;
        margin: 10rpx 0rpx 10rpx 20rpx;
    }

    .bottom-box {
        margin-bottom: 100rpx;
        display: flex;
        justify-content: center;
        font-family: MUJIFont2020;
    }
}
