.page-container1 {
  display: flex;
  flex-direction: column;
  font-family: MUJIFont2020, SourceHanSansCN;
}

.page-content {
  padding-top: 74rpx;
  width: 100%;
  box-sizing: border-box;
  padding-left: 30rpx;
  padding-right: 30rpx;
  position: relative;

  .card-bg {
    position: relative;
    z-index: 0;

    &::before {
      position: absolute;
      content: "";
      width: 680rpx;
      height: 980rpx;
      background: #882238;
      box-shadow: 0rpx 2rpx 4rpx 0rpx #d6d6d6;
      border-radius: 47rpx;
      z-index: 0;
      left: 5rpx;
      top: -5rpx;
    }

    &::after {
      position: absolute;
      content: "";
      width: 690rpx;
      height: 970rpx;
      background: #fff;
      box-shadow: 0rpx 2rpx 4rpx 0rpx #d6d6d6;
      border-radius: 47rpx;
      z-index: 1;
      left: 0;
      border: 6rpx solid transparent;
      box-sizing: border-box;
    }
  }

  .card-content {
    position: relative;
    z-index: 1;
    width: 100%;
    display: flex;
    flex-direction: column;
    height: 970rpx;
    border-radius: 47rpx;
    overflow: hidden;

    .card-top {
      position: relative;
      width: 682rpx;
      height: 479rpx;
      margin: 0 auto;
      background: linear-gradient(
        180deg,
        rgba(247, 247, 247, 0) 0%,
        rgba(237, 237, 237, 0.58) 44%,
        #dbdbdb 100%
      );
      padding: 60rpx;
      box-sizing: border-box;
      background-size: cover;
      /* 背景图像会覆盖整个元素区域，可能会裁切 */
      background-position: center;
      /* 背景图居中 */
      background-repeat: no-repeat;

      .row {
        width: 100%;
        display: flex;
        text-align: center;
        align-items: center;
        justify-content: space-between;
        position: relative;

        &:first-child {
          margin-bottom: 43rpx;
        }

        .logo-box {
          width: 238rpx;
          height: 42rpx;
          margin: 0 auto;
          font-size: 42rpx;
        }

        .avatar {
          width: 200rpx;
          height: 200rpx;
          border: 1rpx solid #ffffff;
          display: flex;
          align-items: center;

          image {
            margin: 0 auto;
            width: 172rpx;
            height: 172rpx;
            display: block;
          }
        }

        .col {
          text-align: left;

          .welcome-txt {
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 32rpx;
            color: #3c3c43;
            line-height: 42rpx;
            letter-spacing: 2px;
            text-align: left;
            font-style: normal;
          }

          .register-tips {
            height: 36rpx;
            font-family: SourceHanSansCN, SourceHanSansCN;
            font-weight: 400;
            font-size: 18rpx;
            color: #3c3c43;
            line-height: 36rpx;
            text-align: left;
            font-style: normal;
          }

          .btn-box {
            margin-top: 40rpx;
          }

          .item-box {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #3c3c43;
            line-height: 46rpx;
            text-align: left;
            font-style: normal;
            margin-bottom: 20rpx;

            &:last-child {
              margin-bottom: 0;
              margin-top: 40rpx;
            }

            .title {
              font-size: 24rpx;
              color: #888;
            }

            .value {
              font-size: 32rpx;
              font-weight: 500;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              font-family: MUJIFont2020, SourceHanSansCN;
            }

            .level-line {
              width: 320rpx;
              height: 6rpx;
              background: rgba(60, 60, 67, 0.1);
              border-radius: 1rpx;
              position: relative;
              z-index: 0;

              &::after {
                position: absolute;
                content: "";
                display: block;
                top: 0;
                left: 0;
                height: 6rpx;
                width: var(--persent);
                z-index: 1;
                background-color: #3c3c43;
              }
            }

            .level-desc {
              margin-top: 20rpx;
              height: 20rpx;
              font-family: SourceHanSansCN, MUJIFont2020;
              font-weight: 400;
              font-size: 20rpx;
              color: #3c3c43;
              line-height: 30rpx;
              text-align: left;
              font-style: normal;
              display: flex;
              gap: 10rpx;
              align-items: center;

              .iconfont {
                font-size: 16rpx;
              }
            }
          }
        }

        .left-col {
          flex: 1;
          flex-grow: 1;
          overflow: hidden;
        }

        .right-col {
          flex-basis: 172rpx;
          width: 172rpx;
        }
      }
    }

    .card-bottom {
      width: 100%;
      position: relative;
      flex: 1;
      display: flex;
      align-items: center;

      .list-box {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        grid-row-gap: 72rpx;
        position: relative;
        width: 100%;
        box-sizing: border-box;
        padding: 0 20rpx;

        .list-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          position: relative;

          .dot {
            width: 29rpx;
            height: 29rpx;
            border-radius: 50%;
            background: #7f0019;
            font-family: MUJIFont2020, MUJIFont2020;
            font-weight: 400;
            font-size: 16rpx;
            color: #ffffff;
            line-height: 29rpx;
            text-align: center;
            font-style: normal;
            position: absolute;
            right: -8rpx;
            top: 0;
          }

          .icon {
            width: 90rpx;
            height: 90rpx;
            // background: #F4EEDE;
            border-radius: 45rpx;
            margin-bottom: 12rpx;
            position: relative;

            image {
              width: 100%;
              height: 100%;
            }
          }

          .name {
            height: 36rpx;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 24rpx;
            color: #3c3c43;
            line-height: 36rpx;
            text-align: center;
            font-style: normal;
          }
        }
      }
    }
  }
}
