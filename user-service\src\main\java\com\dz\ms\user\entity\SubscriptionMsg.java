package com.dz.ms.user.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 订阅消息发送记录
 */
@Getter
@Setter
@NoArgsConstructor
@Table("订阅消息发送记录")
@TableName(value = "t_subscription_msg")
public class SubscriptionMsg implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * userId
     */
    private Long userId;
    /**
     *创建人
     */
    private String createAt;
    /**
     *创建时间
     */
    private Date createTime;
    /**
     *渠道id
     */
    private Long tenantId;
    /**
     *消息code
     */
    private String msgCode;
    /**
     * 发送描述
     */
    private String sendDesc;

}
