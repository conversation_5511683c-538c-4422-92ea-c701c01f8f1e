package com.dz.ms.product.controller;

import com.dz.common.base.enums.ErrorCode;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.annotation.SysLog;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.common.core.enums.LogType;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.product.dto.ProductTagDTO;
import com.dz.ms.product.service.ProductTagService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(tags = "商品标签表")
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class ProductTagController {

    @Resource
    private ProductTagService productTagService;

    /**
     * 分页查询商品标签表
     *
     * @param param
     * @return result<PageInfo < ProductTagDTO>>
     */
    @ApiOperation("分页查询商品标签表")
    @GetMapping(value = "/product_tag/list")
    public Result<PageInfo<ProductTagDTO>> getProductTagList(@ModelAttribute ProductTagDTO param) {
        Result<PageInfo<ProductTagDTO>> result = new Result<>();
        PageInfo<ProductTagDTO> page = productTagService.getProductTagList(param);
        result.setData(page);
        return result;
    }

    /**
     * 根据ID查询商品标签表
     *
     * @param id
     * @return result<ProductTagDTO>
     */
    @ApiOperation("根据ID查询商品标签表")
    @GetMapping(value = "/product_tag/info")
    public Result<ProductTagDTO> getProductTagById(@RequestParam("id") Long id) {
        Result<ProductTagDTO> result = new Result<>();
        ProductTagDTO productTag = productTagService.getProductTagById(id);
        result.setData(productTag);
        return result;
    }

    /**
     * 新增商品标签表
     *
     * @param param
     * @return result<Long>
     */
    @SysLog(value = "新增商品标签表", type = LogType.OPERATELOG)
    @ApiOperation("新增商品标签表")
    @PostMapping(value = "/product_tag/add")
    public Result<Long> addProductTag(@RequestBody ProductTagDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param, true);
        Long id = productTagService.saveProductTag(param);
        result.setData(id);
        return result;
    }

    /**
     * 更新商品标签表
     *
     * @param param
     * @return result<Object>
     */
    @SysLog(value = "更新商品标签表", type = LogType.OPERATELOG)
    @ApiOperation("更新商品标签表")
    @PostMapping(value = "/product_tag/update")
    public Result<Long> updateProductTag(@RequestBody ProductTagDTO param) {
        Result<Long> result = new Result<>();
        validationSaveParam(param, false);
        productTagService.saveProductTag(param);
        result.setData(param.getId());
        return result;
    }

    /**
     * 验证保存参数
     *
     * @param param
     */
    private void validationSaveParam(ProductTagDTO param, boolean isAdd) {
        if (isAdd && null != param.getId()) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "新增参数ID必须为空");
        }
        if (!isAdd && ParamUtils.isNullOr0Long(param.getId())) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "更新参数ID不能为空");
        }
    }

    /**
     * 根据ID删除商品标签表
     *
     * @param param
     * @return result<Boolean>
     */
    @ApiOperation("根据ID删除商品标签表")
    @PostMapping(value = "/product_tag/delete")
    public Result<Boolean> deleteProductTagById(@RequestBody IdCodeDTO param) {
        Result<Boolean> result = new Result<>();
        productTagService.deleteProductTagById(param);
        result.setData(true);
        return result;
    }

}
