// 获取门店
exports.getStoreList = data => wx.$request({
  url: '/app/user/store/list',
  data,
  method: 'post'
})

// 获取服务
exports.getServeList = () => wx.$request({
  url: '/app/user/serve/list',
  method: 'get'
})

// 获取城市列表
exports.getCityList = () => wx.$request({
  url: '/app/user/store/city',
  method: 'get'
})

// 获取带首字母的城市列表
exports.getIndexBarCityList = () => wx.$request({
  url: '/app/user/store/storeCity',
  method: 'get'
})

// 根据经纬度查询当前城市
exports.getCurrentCity = (data) => wx.$request({
  url: '/app/user/store/cityByLonLat',
  method: 'get',
  data,
})
