package com.dz.common.core.threadlocal;

import com.dz.common.core.dto.user.CurrentUserDTO;

public class SecurityContext {

	private static ThreadLocal<CurrentUserDTO> userCtx = new ThreadLocal<CurrentUserDTO>();

	public static void setUser(CurrentUserDTO user) {
		userCtx.set(user);
	}

	public static CurrentUserDTO getUser() {
		return userCtx.get();
	}
	
	public static void reset() {
		userCtx.remove();
	}

}
