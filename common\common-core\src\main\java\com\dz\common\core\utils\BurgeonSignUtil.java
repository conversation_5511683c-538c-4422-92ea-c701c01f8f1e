package com.dz.common.core.utils;

import com.dz.common.base.utils.MD5;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Objects;

/**
 * 伯俊接口签名工具类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023/4/18 15:31
 */
public class BurgeonSignUtil {

    /**
     * 生成签名
     *
     * @param appKey        应用key
     * @param appSecret     应用密钥
     * @param timestamp     时间戳 yyyy-MM-dd HH:mm:ss
     * @return              签名
     */
    public static String sign(String appKey, String appSecret, String timestamp) {
        // 首先进行MD5加密结果得到(小写)
        String encode = Objects.requireNonNull(MD5.encode(appKey + appSecret + timestamp)).toLowerCase();
        // 其次根据得到的MD5加密结果进行Base64加密(大写)
        String s = Base64.getEncoder().encodeToString(encode.getBytes(StandardCharsets.UTF_8)).toUpperCase();
        // 最后进行URLEncoder转码得到的签名结果
        return URLEncoder.encode(s, StandardCharsets.UTF_8);
    }
}
