package com.dz.ms.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 开放接口认证账号DTO
 * @author: Handy
 * @date:   2023/08/10 23:32
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "开放接口认证登录入参")
public class OpenApiAuthDTO {

    @ApiModelProperty(value = "认证账号")
    private String appKey;
    @ApiModelProperty(value = "认证账号密钥")
    private String secret;

}
