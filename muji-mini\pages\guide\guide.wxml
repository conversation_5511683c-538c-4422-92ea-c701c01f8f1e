<!--pages/guide/guide.wxml-->

<my-page loading="{{loading}}" overallModal="{{overallModal}}" catch:overallModalConfirm="overallModalConfirm">
  <custom-header background="transparent" type="{{1}}" isBackHidden="{{ isBackHidden }}" isFixed />
  <image src="{{ images?images:$cdn+'/emptyStore.jpg'}}" class="" mode="aspectFill" style="width:750rpx;flex-shrink:0;height: 600rpx;" />

  <view class="page-content" style="--top:-94rpx">

    <view class="page-content-item">

      <view class="title">{{miniGuide?miniGuide:'加入MUJI社群'}}</view>
      <view class="qrcode-box">

        <block wx:if="{{(qrCodeUrl && weworkImagesType === 1)||(weworkOneImages && weworkImagesType === 2)}}">
          <view class="qrcode-img">
            <image src="{{weworkImagesType === 1?qrCodeUrl:weworkOneImages}}" show-menu-by-longpress="{{true}}"
              lazy-load />
          </view>
          <view class="txt">
            <!-- <view>长按识别二维码，加入社群</view> -->
            <!-- <view>扫码加入微信社群</view> -->
            <view wx:if="{{weworkImagesType === 1}}">长按保存并扫码加入社群</view>
            <view wx:if="{{weworkImagesType === 2}}">扫码添加企微专属服务顾问</view>
            <!-- <view>领取惊喜福利哦</view> -->
          </view>
        </block>
        <block wx:else>
          <view class="no-data">
            抱歉，暂未配置服务顾问
          </view>
        </block>
      </view>
      <view class="qrcode-line"></view>
      <view class="info-box">
        <view class="store-name">{{storeName}}</view>

        <view class="address-box-item">
          <view class="iconfont icon-Place1" />
          <view class="address-box-item-title">
            {{address}}
          </view>
        </view>

        <view class="address-box-phone" bindtap="callTel">
          <view class="iconfont icon-Phone" style="font-size: 38rpx;" />
          <view class="address-box-phone-num">
            {{phone}}
          </view>

        </view>
        <view class="service-time">
          <view class="service-time-item" wx:if="{{openingHourOne !== null || openingHourOne !== ''}}">
            <view class="iconfont icon-Time" style="font-size: 36rpx;margin-right: 10rpx;" />
            <view class="service-time-li">{{openingHourOne}}</view>
          </view>
          <view class="service-time-item" wx:if="{{openingHourTwo}}"
            style="margin-top:{{openingHourOne !== null || openingHourOne !== ''?'10rpx':''}} ;">
            <view wx:if="{{openingHourOne !== null || openingHourOne !== ''}}"
              style="width:36rpx;height: 36rpx;margin-right: 10rpx;" />
            <view wx:else class="iconfont icon-Time" style="font-size: 38rpx;margin-right: 10rpx;" />
            <view class="service-time-li">{{openingHourTwo}}</view>

          </view>
        </view>
      </view>

    </view>

  </view>
  <view class="fixed-bottom">
    <view wx:if="{{distance!==null}}" class="save-btn address" bindtap="openLocation">
      <image src="{{$cdn}}/position.png" class=""
        style="width:32rpx;flex-shrink:0;height: 32rpx;margin-right: 10rpx;" />导航<text
        style="font-size: 20rpx;line-height: 42rpx;height: 33rpx;">({{distance}})</text>
    </view>
    <view wx:if="{{qrCodeUrl}}" class="save-btn" bindtap="saveImage">
      保存到本地
    </view>
  </view>
</my-page>
