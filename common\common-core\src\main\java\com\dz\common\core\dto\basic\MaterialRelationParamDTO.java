package com.dz.common.core.dto.basic;

import com.dz.common.core.enums.MaterialRelationEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 素材关联信息保存入参DTO
 * @author: Handy
 * @date:   2023/05/10 14:31
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "素材关联信息保存入参")
public class MaterialRelationParamDTO {

    @ApiModelProperty(value = "关联模块字段类型枚举")
    private MaterialRelationEnum relationEnum;
    @ApiModelProperty(value = "素材ID列表")
    private List<Long> idList;
    @ApiModelProperty(value = "素材ID字符串多个逗号隔开")
    private String idStr;
    @ApiModelProperty(value = "关联业务ID")
    private Long relationId;

    public MaterialRelationParamDTO(MaterialRelationEnum relationEnum, List<Long> idList, Long relationId) {
        this.relationEnum = relationEnum;
        this.idList = idList;
        this.relationId = relationId;
    }

    public MaterialRelationParamDTO(MaterialRelationEnum relationEnum, String idStr, Long relationId) {
        this.relationEnum = relationEnum;
        this.idStr = idStr;
        this.relationId = relationId;
    }

}
