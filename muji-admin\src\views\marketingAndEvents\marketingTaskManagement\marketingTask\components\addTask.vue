<template>
  <a-drawer :title="title" width="95%" placement="right" :closable="true" :maskClosable="false" :open="open" @close="onClose">
    <a-spin :spinning="loading">

      <a-form class="form" ref="addForm" :model="addParams" :rules="rules" :labelCol="{ style: 'width:150px' }">
        <div class="form-top-titles-common">任务基础信息</div>
        <div class="form-top-line-common"></div>
        <a-form-item label="任务名称" name="taskName">
          <a-input placeholder="请输入任务名称" style="width: 400px" v-model:value="addParams.taskName" allow-clear show-count :maxlength="8" />
          <div class="global-tip">在小程序会展示该名称</div>
        </a-form-item>
        <a-form-item label="任务展示时间" name="showTimeType">
          <a-radio-group v-model:value="addParams.showTimeType" @change="showTimeTypeChange">
            <template v-for="(item, index) in showTimeTypeTypeOptions" :key="index">
              <a-radio-button :value="item.value">{{ item.label }}</a-radio-button>
            </template>
          </a-radio-group>
          <div class="global-tip">时间段内，任务才会展示，超出时间范围，前台不展示</div>
        </a-form-item>

        <a-form-item label=" " class="hide-required-mark" v-if="addParams.showTimeType == 1" name="showTime" :hideRequiredMark="false" :colon="false">
          <a-range-picker style="width:400px;" :disabledDate="disabledStartDate" v-model:value="addParams.showTime" :getPopupContainer="(triggerNode) => triggerNode.parentNode" valueFormat="YYYY-MM-DD HH:mm:ss" format="YYYY-MM-DD HH:mm:ss" :show-time="{
              hideDisabledOptions: true,
              defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')],
            }" :placeholder="['开始时间', '结束时间']" />

        </a-form-item>
        <a-form-item label="过期展示时间" name="showTimeExtend">
          <div class="flex-box">
            <p>展示时间到期后，继续展示</p>
            <!-- <a-form-item label="" name="readyNum"> -->

            <a-input-number class="small-input" placeholder="请输入" :allowClear="true" @blur="()=>onInventoryTempHandler(addParams.showTimeExtend)" :min="0" :max="999" v-model:value="addParams.showTimeExtend" />
            <!-- </a-form-item> -->
            <p>天</p>
          </div>
          <div class="global-tip">
            该任务过期后，会在过期任务中继续展示一定天数
          </div>
        </a-form-item>
        <a-form-item label="任务展示图片配置" name="showImg">
          <uploadImg :max="10" :width="100" :height="100" :imgUrl="addParams.showImg" :form="addParams" path="showImg" :disabled="disabled" @success="uploadSuccess" />
          <div class="global-tip">
            建议尺寸 80px * 80px ；比例 1:1
          </div>
        </a-form-item>

        <a-form-item label="任务规则图片配置" name="ruleImg">
          <uploadImg :max="10" :width="100" :height="100" :imgUrl="addParams.ruleImg" :form="addParams" path="ruleImg" :disabled="disabled" @success="uploadSuccess" />
          <div class="global-tip">
            建议尺寸 630px * 1000px 需要展示任务规则的，在此上传，配置后，会有“！”，用户点击后，会展示该图片内容
          </div>
        </a-form-item>

        <div class="form-top-titles-common">任务类型及时间机制</div>
        <div class="form-top-line-common"></div>
        <a-form-item label="任务类型" name="taskType">
          <a-radio-group v-model:value="addParams.taskType">
            <template v-for="(item, index) in taskTypeOptions" :key="index">
              <a-radio-button :value="item.value">{{ item.label }}</a-radio-button>
            </template>
          </a-radio-group>
          <div class="global-tip">前台展示顺序 限时任务→购物任务→互动任务；各分类下的任务，根据新建时间倒序，最新发布的在最前</div>
        </a-form-item>
        <a-form-item label="是否限时" name="isTimeRestrict">
          <a-radio-group :disabled="addParams.showTimeType === 3" v-model:value="addParams.isTimeRestrict" @change="isTimeRestrictChange">
            <template v-for="(item, index) in isTimeRestrictTypeOptions" :key="index">
              <a-radio-button :value="item.value">{{ item.label }}</a-radio-button>
            </template>
          </a-radio-group>

        </a-form-item>
        <a-form-item label=" " class="hide-required-mark" v-if="addParams.isTimeRestrict == 1" name="restrictTime" :hideRequiredMark="false" :colon="false">
          <a-range-picker :disabled="addParams.showTimeType === 3" style="width:400px;" :show-time="{
            hideDisabledOptions: true,
            defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')],
          }" :disabledDate="disabledStartDate" v-model:value="addParams.restrictTime" :getPopupContainer="(triggerNode) => triggerNode.parentNode" valueFormat="YYYY-MM-DD HH:mm:ss" format="YYYY-MM-DD HH:mm:ss" :placeholder="['开始时间', '结束时间']" />
          <div class="global-tip">限时结束时间不能大于展示结束时间！</div>
          <div class="global-tip">限时后，该任务只有在该时间段内，才可以进行，超过范围后，虽然展示，但无法继续参与</div>
        </a-form-item>
        <div class="form-top-titles-common">任务完成要求</div>
        <taskRequirement :disabled="disabled" :formFields="addParams" :formRules="rules" :formRef="addForm" @taskDescChange="taskDescChange" @readyCycleChange="readyCycleChange" @totalReadyNumChange="totalReadyNumChange"></taskRequirement>

        <div class="form-top-titles-common">任务奖励</div>
        <div class="form-top-line-common"></div>
        <taskReward :formFields="addParams" :formRules="rules" :formRef="addForm" @removeCouponr="removeCouponr" @addCouponr="addCouponr"></taskReward>

      </a-form>
      <!-- v-if="!disabled" -->
    </a-spin>
    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="ok" :loading="loading">确定</a-button>
      </a-space>
    </template>

  </a-drawer>
</template>
<script setup>
import { showTimeTypeTypeOptions, isTimeRestrictTypeOptions, taskTypeOptions, taskContentOptions, completeTaskOptions, rewardTypeOptions } from '@/utils/dict-options'
import { taskAdd, taskUpdate, taskInfo } from '@/http/index.js'
import taskRequirement from './taskRequirement.vue'
import taskReward from './taskReward.vue'
import { useGlobalStore } from '@/store'
import { message, Modal } from "ant-design-vue";
import { formRulesValidate } from '@/utils/validate.js'
import dayjs from "dayjs";
import _ from "lodash"
const $router = useRouter();
const global = useGlobalStore()
const addForm = ref(null)
import { cloneDeep } from 'lodash'
import { onMounted } from 'vue';
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  // 操作抽屉打开关闭
  visible: {
    type: Boolean,
    default: false
  },
  // 数据ID
  id: {
    type: String || Number,
    default: ''
  },
  // 操作类型
  type: {
    type: Number,
    default: 0, // 0-新增 1-编辑  2-查看
  }

})
// 置灰
const disabled = computed(() => {
  return props.type === 1
})
const disabledStartDate = (date) => {
  const today = dayjs().startOf('day'); // 获取今天的日期

  // if (addParams.value.createTime[1]) {
  //   return dayjs(date).isBefore(today, 'day')
  // } else {
  // 如果结束时间没有值，则只考虑45天后的日期
  return dayjs(date).isBefore(today, 'day')
  // }
};

// 标题
const title = computed(() => {
  return ['新建', '编辑', '查看'][props.type] + '任务'
})

const { open, addParams, rules, loading, permitionList } = toRefs(reactive({
  open: props.visible,
  permitionList: [],

  loading: false,

  addParams: {
    taskName: '',//任务名称
    showTimeType: 1, //任务展示时间类型
    restrictTime: [],
    ruleImg: '',
    showTime: [],//任务展示时间
    isTimeRestrict: 1, //是否限时
    taskType: 1, //任务类型
    reayType: 'readyDay',//月
    taskDesc: 1,
    readyDay: null,
    readyMonth: null,
    readyCycle: 1,
    showTimeExtend: 30,//过期时间
    // storeCardNum: 1,// 每家门店限制打卡次数
    // limitationNum: 1,// 每天完成任务限制次数
    taskRewardList: [
      {
        couponIds: [''],
        rewardType: 1,
        sortNum: 1,
        pointsNum: null
      }
    ],
    storeProductCodes: '',
    classProductCodes: '',
    departProductCodes: '',
    lineProductCodes: '',
    janProductCodes: ''
  },
  rules: {
    taskName: [{ required: true, message: '请输入任务名称', trigger: ['blur', 'change'] }],
    showTimeType: [{ required: true, message: '请选择任务展示时间方式', trigger: ['blur', 'change'] }],
    showTime: [{ required: true, message: '请选择时间', trigger: ['blur', 'change'] }],
    taskType: [{ required: true, message: '请选择任务类型', trigger: ['blur', 'change'] }],
    isTimeRestrict: [{ required: true, message: '请选择是否限时', trigger: ['blur', 'change'] }],
    restrictTime: [{ required: true, message: '请选择是否限时时间', trigger: ['blur', 'change'] }],
    readyCycle: [{ required: true, message: '请选择任务要求', trigger: ['blur', 'change'] }],
    // showTimeExtend: [{ validator: (rule, value, callback) => formRulesValidate.elIntegerInRange(value, callback, { min: 0, max: 999 }), trigger: ['blur', 'change'] }]
    // taskDesc: [{ required: true, message: '请选择任务内容', trigger: ['blur', 'change'] }],
    // readyNum: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
    // readyDay: [{ required: true, message: '请输入天数', trigger: ['blur', 'change'] }],
    // readyMonth: [{ required: true, message: '请输入月数', trigger: ['blur', 'change'] }],
    // totalReadyNum: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
    // storeProductCodes: [{ required: true, message: '请输入code', trigger: ['blur', 'change'] }],
    // classProductCodes: [{ required: true, message: '请输入code', trigger: ['blur', 'change'] }],
    // departProductCodes: [{ required: true, message: '请输入code', trigger: ['blur', 'change'] }],
    // lineProductCodes: [{ required: true, message: '请输入code', trigger: ['blur', 'change'] }],
    // janProductCodes: [{ required: true, message: '请输入code', trigger: ['blur', 'change'] }],
  }
})
);

watch(() => props.visible, (value) => {
  // 弹窗操作
  open.value = props.visible
  addParams.value = {
    taskName: '',//任务名称
    showTimeType: 1, //任务展示时间类型
    restrictTime: [],
    showTime: [],//任务展示时间
    isTimeRestrict: 1, //是否限时
    taskType: 1, //任务类型
    reayType: 'readyDay',//月
    taskDesc: 1,
    readyDay: null,
    readyMonth: null,
    readyCycle: 1,
    readyNum: null,
    totalReadyNum: null,
    showTimeExtend: 30,
    // storeCardNum: 1,
    // limitationNum: 1,
    taskRewardList: [
      {
        couponIds: [''],
        rewardType: 1,
        sortNum: 1,
        pointsNum: null
      }
    ],
    storeProductCodes: '',
    classProductCodes: '',
    departProductCodes: '',
    lineProductCodes: '',
  }
  completeTaskOptions.value.forEach((item, index) => {

    item.disabled = false

  });
  addForm.value?.resetFields()
  if (open.value) {

    initData()
  }
})
const taskDescChange = ({ target: { value } }) => {
  if (value) {
    if (addParams.value.taskDesc == 1 || addParams.value.taskDesc == 5) {
      // if (addParams.value.taskDesc == 1) {

      //   addParams.value.storeCardNum = 1
      // } else {
      //   addParams.value.storeCardNum = null
      // }
      // addParams.value.limitationNum = 1
    } else {
      // addParams.value.storeCardNum = null
      // addParams.value.limitationNum = null
    }
    addParams.value.readyCycle = 1
    addParams.value.readyDay = null
    addParams.value.readyMonth = null
    addParams.value.readyNum = null
    addParams.value.totalReadyNum = null
    addParams.value.storeProductCodes = null
    addParams.value.classProductCodes = null
    addParams.value.departProductCodes = null
    addParams.value.lineProductCodes = null

    addParams.value.taskRewardList = [{
      couponIds: [''],
      rewardType: 1,
      sortNum: 1,
      pointsNum: null
    }];
  }
  if (value == 5) {
    completeTaskOptions.value.forEach((item, index) => {
      if (index > 0) {
        item.disabled = true
      }
    });

  } else {
    completeTaskOptions.value.forEach((item, index) => {
      // if (index > 0) {
      item.disabled = false
      // }
    });
  }
  console.log(completeTaskOptions, 'completeTaskOptions');
}
function showTimeTypeChange({ target: { value } }) {

  if (value == 1) {
    addParams.value.showTime = []
    rules.value.restrictTime[0]['required'] = true
  } else if (value == 3) {
    addParams.value.isTimeRestrict = 1
    addParams.value.restrictTime = []
    console.log(rules.value.restrictTime);

    rules.value.restrictTime[0]['required'] = false
  } else {
    rules.value.restrictTime[0]['required'] = true
  }

  addForm.value.clearValidate('restrictTime')
}
function isTimeRestrictChange({ target: { value } }) {
  if (value == 1) {
    addParams.value.restrictTime = []
  }

}
function totalReadyNumChange(val) {
  console.log(val, addParams.value, 'addParams.valueaddParams.valueaddParams.valueaddParams.value');

  // 假设 addParams 是一个全局对象或者已经通过某种方式传入到函数中
  if (val && addParams.value.readyCycle === 3) {
    const currentList = addParams.value.taskRewardList;
    const currentLength = currentList.length;
    const targetLength = Math.max(0, val); // 确保目标长度是非负的

    // 如果目标长度小于当前长度，移除多余的元素
    if (targetLength < currentLength) {
      currentList.splice(targetLength);
    }
    // 如果目标长度大于当前长度，添加新的元素
    else if (targetLength > currentLength) {
      const newElements = [];
      for (let i = currentLength; i < targetLength; i++) {
        // 注意：这里 sortNum 是基于当前列表的最大 sortNum（如果存在）+1 来设置的，
        // 但由于我们是在末尾添加，且每个新元素的 sortNum 都是递增的，
        // 所以这里直接设置为 i + 1 也是可以的（假设之前的 sortNum 是连续的）。
        newElements.push({
          couponIds: [''],
          rewardType: 1,
          sortNum: i + 1, // 确保 sortNum 是唯一的且递增的
          pointsNum: null
        });
      }
      currentList.push(...newElements);
    }
    // 如果目标长度等于当前长度，则不执行任何操作

    // 注意：由于我们是按顺序添加的，且没有在中间插入或删除元素（除了上面的 splice），
    // 所以 sortNum 不需要重新排序。但如果出于某种原因需要手动验证或调整，
    // 可以在这里添加代码来遍历列表并设置正确的 sortNum（通常不需要）。

  } else {
    // 如果条件不满足，重置 taskRewardList
    addParams.value.taskRewardList = [{
      couponIds: [''],
      rewardType: 1,
      sortNum: 1,
      pointsNum: null
    }];
  }

  // 将 val 和更新后的 taskRewardList 打印到控制台（注意打印顺序，以便先看到 val）
}



const readyCycleChange = async ({ target: { value } }) => {
  if (value) {
    if (addParams.value.taskDesc == 1 || addParams.value.taskDesc == 5) {
      // if (addParams.value.taskDesc == 1) {

      //   addParams.value.storeCardNum = 1
      // } else {
      //   addParams.value.storeCardNum = null
      // }
      // addParams.value.limitationNum = 1
    } else {
      // addParams.value.storeCardNum = null
      // addParams.value.limitationNum = null
    }
    addParams.value.readyDay = null
    addParams.value.readyMonth = null
    addParams.value.readyNum = null
    addParams.value.totalReadyNum = null
    addParams.value.storeProductCodes = null
    addParams.value.classProductCodes = null
    addParams.value.departProductCodes = null
    addParams.value.lineProductCodes = null
    addParams.value.janProductCodes = null
    addParams.value.taskRewardList = [{
      couponIds: [''],
      rewardType: 1,
      sortNum: 1,
      pointsNum: null
    }];
  }
  // addParams.reayType
  if (value != 1) {
    addParams.value.reayType = 'readyDay'
    addParams.value.readyDay = null
  } else {
    addParams.value.reayType = ''
  }

}

//所有接口调取出
const initData = async () => {
  const promiseArr = []

  if (props.id) {

    promiseArr.push(taskInfo({ id: props.id }))
  }

  // 如果有新的接口就在这里push
  loading.value = true
  try {
    const [taskInfo1] = await Promise.all(promiseArr)

    if (taskInfo1) {
      const newArr = taskInfo1.data
      if (!newArr.isTimeRestrict) {
        newArr.isTimeRestrict = 1
      }
      if (newArr.showTimeType === 3) {
        rules.value.restrictTime[0]['required'] = false
      } else {
        rules.value.restrictTime[0]['required'] = true
      }
      if (newArr.showTimeType == 1 && newArr.showTimeStart && newArr.showTimeEnd) {
        newArr.showTime = [newArr.showTimeStart, newArr.showTimeEnd]
      } else {
        newArr.showTime = []
      }
      if (newArr.isTimeRestrict == 1 && newArr.restrictTimeStart && newArr.restrictTimeEnd) {
        newArr.restrictTime = [newArr.restrictTimeStart, newArr.restrictTimeEnd]
      } else {
        newArr.restrictTime = []
      }
      newArr.taskRewardList.forEach(item => {
        if (item.couponIds && item.couponIds.length > 0) {
          item.couponIds = item.couponIds.split(",");
        } else {
          item.couponIds = ['']
        }

      })
      if (newArr.taskDesc == 5) {
        completeTaskOptions.value.forEach((item, index) => {
          if (index > 0) {
            item.disabled = true
          }
        });

      } else {
        completeTaskOptions.value.forEach((item, index) => {
          // if (index > 0) {
          item.disabled = false
          // }
        });
      }
      addParams.value = {
        ...newArr
      }
    }
    loading.value = false
  } catch (error) {
    loading.value = false
    console.error('获取数据失败:', error)
  }
}

const addCouponr = (index1, index2) => {
  if (index2 + 1 <= 10) {

    addParams.value.taskRewardList[index1]['couponIds'][index2] = ''
  } else {
    message.info('最多增加10个');
  }
};

const removeCouponr = (index1, index2) => {

  addParams.value.taskRewardList[index1]['couponIds'].splice(index2, 1);

};

const uploadSuccess = async (data) => {
  let { form, path: key, imgUrl } = data;
  _.set(form, key.split('.'), imgUrl)
  // 清除校验
  await nextTick()
  addForm.value.validateFields([key.split('.')])
}


// 关闭弹窗
const onClose = () => {
  emit('cancel')
}

// 确定
const ok = () => {

  addForm.value.validate().then(res => {

    let params = cloneDeep(addParams.value)

    if (params.showTimeType === 3) {//根据活动时间展示
      params.isTimeRestrict = null
      addParams.value.restrictTime = []
    }
    if (params.showTime.length > 0 && params.showTimeType == 1) {
      params.showTimeStart = params.showTime[0]
      params.showTimeEnd = params.showTime[1]
    } else {
      params.showTimeEnd = null
      params.showTimeStart = null
    }
    if (params.restrictTime.length > 0 && params.isTimeRestrict == 1) {
      params.restrictTimeStart = params.restrictTime[0]
      params.restrictTimeEnd = params.restrictTime[1]
    } else {
      params.restrictTimeEnd = null
      params.restrictTimeStart = null
    }
    if (params.taskRewardList.length > 0) {
      params.taskRewardList.forEach(item => {
        if (item.rewardType == 1 && item.couponIds.length > 0) {
          item.couponIds = item.couponIds.join(',')
          item.pointsNum = null
        } else {
          item.couponIds = ''
        }
      })
    }

    delete params.restrictTime
    delete params.showTime
    // loading.value = true
    if (props.id) {
      console.log('编辑');
      taskUpdate(params).then(res => {
        message.success(res.msg);
        emit('ok', props.id)
      }).finally(() => {
        loading.value = false
      })
    } else {
      taskAdd(params).then(res => {
        message.success(res.msg);
        emit('ok', props.id)
      }).finally(() => {
        loading.value = false
      })
    }

  })
}

function onInventoryTempHandler(record) {
  // console.log(record);
  const match = String(record).match(/(0)|(-?[1-9]\d*)/ig) || []
  // console.log(match);
  addParams.value.showTimeExtend = match[0] || 0
}






</script>

<style scoped lang="scss">
:deep(.searchForm .ant-form-item) {
  margin-bottom: 16px;
}

:deep(.anticon-eye-invisible) {
  display: none;
}

// .member-role {
//   background: #f2f2f2;
//   margin-bottom: 20px;
//   padding: 5px 10px 10px;
//   display: flex;
//   justify-content: space-between;
//   align-items: center;

//   &:nth-child(odd) {
//     margin-right: 35px;
//   }
// }

//影藏红的点
.hide-required-mark {
  :deep(.ant-form-item-required) {
    display: none;
  }
}

.flex-box {
  display: flex;
  // margin-left: 150px;
  align-items: center;
}

.flex-box p {
  margin: 0;
  padding: 0;
  // line-height: 30px;
}

.small-input {
  width: 100px !important;
  margin: 0 5px;
}

// .reay-item-form {
//   height: 32px;
//   line-height: 32px;
//   // margin-bottom: 0 !important;
// }

// .a-item-mb0 {
//   margin-bottom: 0 !important;
// }
</style>
