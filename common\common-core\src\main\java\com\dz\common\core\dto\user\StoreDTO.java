package com.dz.common.core.dto.user;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 门店DTO
 * @author: yibo
 * @date:   2024/11/19 17:19
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "门店")
public class StoreDTO extends BaseDTO {

    @ApiModelProperty(value = "主键id")
    private Long id;
    @ApiModelProperty(value = "门店编号")
    private String storeSn;
    @ApiModelProperty(value = "门店名称")
    private String storeName;
    @ApiModelProperty(value = "门店类型 1普通店  2旗舰店")
    private Integer type;
    @ApiModelProperty(value = "1正常  2删除")
    private Integer status;
    @ApiModelProperty(value = "是否关联门店服务 1未关联 2已关联")
    private Integer isStoreServe;
    @ApiModelProperty(value = "是否关联门店照片 1未关联 2已关联")
    private Integer isStoreImage;
    @ApiModelProperty(value = "是否配置企微二维码 1未配置 2已配置")
    private Integer isWeWorkImage;
    @ApiModelProperty(value = "省份")
    private String province;
    @ApiModelProperty(value = "城市")
    private String city;
    @ApiModelProperty(value = "区域")
    private String area;
    @ApiModelProperty(value = "经度")
    private String longitude;
    @ApiModelProperty(value = "维度")
    private String latitude;
    @ApiModelProperty(value = "联系邮箱")
    private String email;
    @ApiModelProperty(value = "联系电话")
    private String phone;
    @ApiModelProperty(value = "门店地址")
    private String storeAddress;
    @ApiModelProperty(value = "门店图片")
    private String images;
    @ApiModelProperty(value = "企业微信联系人二维码")
    private String weworkImages;
    @ApiModelProperty(value = "营业时间1")
    private String openingHourOne;
    @ApiModelProperty(value = "营业时间2")
    private String openingHourTwo;
    @ApiModelProperty(value = "开业日期")
    private String openDate;
    @ApiModelProperty(value = "是否处理过 0否 1是")
    private Integer isUpdate;
    @ApiModelProperty(value = "是否关店 0根据开业日期判断  1闭店")
    private Integer isClose;
    @ApiModelProperty(value = "开业状态  1代开业 2开业中 3闭店")
    private Integer isOpen;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    @ApiModelProperty(value = "修改人")
    private Long modifier;
    @ApiModelProperty(value = "租户id")
    private Long tenantId;
    @ApiModelProperty(value = "关联服务id")
    private List<Long> serveIds;
    @ApiModelProperty(value = "服务数量")
    private Long serveNum;
    @ApiModelProperty(value = "关联服务完整信息")
    private List<ServeDTO> serveDTOList;

    @ApiModelProperty(value = "距离")
    private String distance;
    @ApiModelProperty(value = "是否未定位地图查询   1是")
    private Integer tag;

    @ApiModelProperty(value = "是否根据城市未查到数据   1是")
    private Integer cityTag;




}
