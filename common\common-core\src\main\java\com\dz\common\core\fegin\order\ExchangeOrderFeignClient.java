package com.dz.common.core.fegin.order;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.dto.MujiOrder;
import com.dz.common.core.dto.order.PurchaseStaticDTO;
import com.dz.common.core.dto.order.PurchaseStaticParamDTO;
import com.dz.common.core.dto.product.CpStaticDTO;
import com.dz.common.core.dto.product.CpStaticParamDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


@FeignClient(name = ServiceConstant.ORDER_SERVICE_NAME, contextId = "ExchangeOrderFeignClient")
public interface ExchangeOrderFeignClient {


    /**
     * 订单购买统计
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/order/purchase_static")
    Result<List<PurchaseStaticDTO>> purchaseStatic(@RequestBody PurchaseStaticParamDTO param);

    /**
     * 导出订单列表
     *
     * @return
     */
    @PostMapping(value = "/exchange_order/export_order_list")
    Result<Void> exportOrderList(@RequestBody DownloadAddParamDTO exportParam);

    /**
     * 拉取优惠券信息修改订单兑换状态
     */
    @PostMapping(value = "/exchange_order/change_order_status")
    Result<Void> changeOrderStatus();

    /**
     * 拉取优惠券信息修改订单兑换状态
     */
    @PostMapping(value = "/exchange_order/all_change_order_status")
    Result<Void> changeAllOrderStatus();

    /**
     * 统计优惠券兑换量
     */
    @PostMapping(value = "/exchange_order/cp_static")
    PageInfo<CpStaticDTO> cpStatic(@RequestBody CpStaticParamDTO param);

    /**
     * 订单查询
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    @GetMapping(value = "/exchange_order/select_muji_order")
    List<MujiOrder> selectMujiOrder(@RequestParam("beginTime") String beginTime,
                                    @RequestParam("endTime") String endTime);

    /**
     * 订单拉取
     *
     * @return
     */
    @PostMapping(value = "/order/getSftpFile")
    Result<String> getSftpFile();

    /**
     * 用户订单优惠券查询
     */
    @GetMapping(value = "/exchange_order/get_unused_stock_id_list")
    Result<List<String>> getUnusedStockIdList();

    /**
     * 订单sftp查询
     *
     * @param memberCode
     * @return
     */
    @GetMapping(value = "/exchange_order/sftp/order/list")
    List<String> sftpOrderList(@RequestParam("memberCode") String memberCode,
                               @RequestParam(value = "startTime", required = false) String startTime,
                               @RequestParam(value = "endTime", required = false) String endTime,
                               @RequestParam(value = "deptId", required = false) String deptId,
                               @RequestParam(value = "depaId", required = false) String depaId,
                               @RequestParam(value = "lineId", required = false) String lineId,
                               @RequestParam(value = "classId", required = false) String classId,
                               @RequestParam(value = "janId", required = false) String janId);

    @PostMapping(value = "/order/removeOrder")
    public Result<String> removeOrder();
}

