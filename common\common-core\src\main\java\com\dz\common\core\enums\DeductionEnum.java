package com.dz.common.core.enums;

public enum DeductionEnum {

    PIN_CODE("PIN_CODE", "PIN_CODE"),
    POINTS("POINT", "积分"),
    EQUITY("EQUITY", "权益"),
    COUPON("COUPON", "卡券"),
    BUY("BUY", "付费"),
    ;

    private final String code;

    private final String desc;

    DeductionEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String code() {
        return this.code;
    }

    public String desc() {
        return this.desc;
    }

    public static DeductionEnum ofCode(String code) {
        for (DeductionEnum deduction : DeductionEnum.values()) {
            if (deduction.code().equals(code)) {
                return deduction;
            }
        }
        return null;
    }
}
