<template>
  <a-form-item label="人群条件" :name="props.conditionsRuleNamePrefix.concat(['crowdDTO', 'crowdType'])">
    <a-radio-group v-model:value="formFields.crowdDTO.crowdType" style="margin-bottom: 16px">
      <a-radio-button :value="0">{{+addType === 1 && props.formType === 1?'编辑':'新建'}}规则</a-radio-button>
      <a-radio-button :value="2">导入人群包</a-radio-button>
      <a-radio-button v-if="+addType !== 1" :value="1">使用人群包</a-radio-button>
    </a-radio-group>
    <template v-if="formFields.crowdDTO.crowdType === 0">
      <a-form-item v-if="+addType !== 1" label="人群包名称" :name="props.conditionsRuleNamePrefix.concat(['crowdDTO','crowdName'])" :rules="formRules.crowdName">
        <a-input placeholder="请输入" style="width:300px;" v-model:value="formFields.crowdDTO.crowdName" show-count :maxlength="20" />
      </a-form-item>
      <a-form-item label="人群包条件">
        <template v-for="(item, index) in formFields.crowdDTO.crowdConditionList" :key="index">
          <BaseAndOrSelect v-if="item.conditionJudge" v-model="item.conditionJudge" :allowClear="false" :bordered="false" style="width: 60px;" />
          <a-form-item :label="`条件${index + 1}`" class="formItemReset">
            <div class="itemWrap">
              <a-popover>
                <template #content>
                  <div class="buttonGroup">
                    <a-button type="link" @click="thisMethods.handleAddItem(item)" :disabled="item.crowdConditionRowList.length >= thisFields.conditionGroupMax">+ 添加条件项</a-button>
                    <a-button type="link" @click="thisMethods.handleDelItem(item)" :disabled="item.crowdConditionRowList.length <= thisFields.conditionGroupMin">- 删除条件项</a-button>
                  </div>
                  <div class="buttonGroup">
                    <a-button type="link" @click="thisMethods.handleAddGroup(index)" :disabled="formFields.crowdDTO.crowdConditionList.length >= thisFields.conditionGroupMax">+
                      添加条件组</a-button>
                    <a-button type="link" @click="thisMethods.handleDelGroup(index)" :disabled="formFields.crowdDTO.crowdConditionList.length <= thisFields.conditionGroupMin">-
                      删除条件组</a-button>
                  </div>
                </template>
                <div class="iconWrap">
                  <SettingOutlined />
                </div>
              </a-popover>
              <div class="inputWrap">
                <template v-for="(item2, index2) in item.crowdConditionRowList" :key="index2">
                  <BaseAndOrSelect v-if="item2.conditionJudge" v-model="item2.conditionJudge" :allowClear="false" :bordered="false" style="width: 60px;" />
                  <ShelfCrowdConditionsSelect v-model="item2.conditions" :name="props.conditionsRuleNamePrefix.concat(['crowdDTO', 'crowdConditionList', index, 'crowdConditionRowList', index2, 'conditions'])" :rules="formRules.conditions" :allowClear="false" style="width: 100px;" />
                </template>
              </div>
            </div>
          </a-form-item>
        </template>
        <div style="margin-left: -17px;">
          <a-button type="link" @click="thisMethods.handleAddGroup(formFields.crowdDTO.crowdConditionList.length - 1)" :disabled="formFields.crowdDTO.crowdConditionList.length >= thisFields.conditionGroupMax">+ 添加条件组</a-button>
          <a-button type="link" @click="thisMethods.handleDelGroup(formFields.crowdDTO.crowdConditionList.length - 1)" :disabled="formFields.crowdDTO.crowdConditionList.length <= thisFields.conditionGroupMin">- 删除条件组</a-button>

        </div>
      </a-form-item>
    </template>
    <template v-if="formFields.crowdDTO.crowdType === 1">
      <ShelfCrowdSelect label="选择人群包" :name="props.conditionsRuleNamePrefix.concat(['crowdDTO', 'crowdId'])" :rules="formRules.crowdId" v-model="formFields.crowdDTO.crowdId" />
    </template>
    <template v-if="formFields.crowdDTO.crowdType === 2">
      <a-form-item v-if="+addType !== 1" label="人群包名称" :name="props.conditionsRuleNamePrefix.concat(['crowdDTO', 'crowdName'])" :rules="formRules.crowdName">
        <a-input placeholder="请输入" style="width:300px;" v-model:value="formFields.crowdDTO.crowdName" show-count :maxlength="20" />
      </a-form-item>
      <a-form-item :rules="formRules.memberCodeList" :name="props.conditionsRuleNamePrefix.concat(['crowdDTO', 'crowdImportResultDTO','memberCodeList'])">
        <div class="crowdTemplateExportAndImportWrap">
          <downloadFile type="link" file-name="人群包导入模板" file-type="excel" :api="crowdtemplate" :disabled="false">下载导入模板
          </downloadFile>
          <uploadFile type="link" :api="crowdimport" @ok="thisMethods.crowdUploadSuccess" :disabled="false">上传人群包
          </uploadFile>
        </div>
        <div class="global-tip" v-if="formFields.crowdDTO.crowdImportResultDTO">{{formFields.crowdDTO.crowdImportResultDTO.fileName}}</div>
        <div class="global-tip" v-if="formFields.crowdDTO.crowdImportResultDTO">
          已导入：{{ formFields.crowdDTO.crowdImportResultDTO.memberCodeList.length }}条人群数据
        </div>
      </a-form-item>
    </template>
  </a-form-item>
</template>

<script setup>
import ShelfCrowdConditionsSelect from '@/views/mall/shelfManagement/giftRack/components/ShelfCrowdConditionsSelect.vue'
import ShelfCrowdSelect from '@/views/mall/shelfManagement/giftRack/components/ShelfCrowdSelect.vue'
import { crowdtemplate, crowdimport } from '@/http/index'
import { nextTick } from 'vue'

const props = defineProps({
  formType: { // 0-新增 1-编辑 2-查看
    type: Number,
    default: 0
  },
  timeType: { // 0时间段 1永久可用（通过货架、人群限购创建的人群包，需要传1，否则创建的人群包缺少可用时间）
    type: Number,
    default: 0
  },
  conditionsRuleNamePrefix: {
    type: Array,
    default: () => ([])
  },
  formFields: {
    type: Object,
    default: () => ({})
  },
  formRules: {
    type: Object,
    default: () => ({})
  },
  formRef: {
    type: Object,
    default: () => ({})
  },
  addType: {
    type: Number || String,
    default: 0
  }
})

const crowdCreateGroupChildItemGet = () => {
  return { conditionJudge: 0, conditions: [] }
}
const crowdCreateGroupItemGet = () => {
  return { conditionJudge: 0, crowdConditionRowList: [crowdCreateGroupChildItemGet()] }
}
const thisFields = reactive({
  conditionGroupMin: 1,
  conditionGroupMax: 5
})
const thisMethods = {
  crowdUploadSuccess({ fileUrl }) {
    console.log("🚀 ~ crowdUploadSuccess ~ obj:", fileUrl)
    const formFields = props.formFields
    formFields.crowdDTO.crowdImportResultDTO['memberCodeList'] = fileUrl.memberCodeList
    formFields.crowdDTO.crowdImportResultDTO['fileName'] = fileUrl.fileName
    nextTick(() => {
      props.formRef.clearValidate(props.conditionsRuleNamePrefix.concat(['crowdDTO', 'crowdImportResultDTO', 'memberCodeList']))
      props.formRef.validateFields([props.conditionsRuleNamePrefix.concat(['crowdDTO', 'crowdImportResultDTO', 'memberCodeList'])])
    })
  },
  handleAddGroup(index) {
    const formFields = props.formFields
    const newItem = crowdCreateGroupItemGet()
    if (index > -1) newItem.conditionJudge = 1
    formFields.crowdDTO.crowdConditionList.splice(index + 1, 0, newItem)
    props.formRef.validateFields([['crowdDTO', 'crowdConditionList']])
  },
  handleDelGroup(index) {
    const formFields = props.formFields
    formFields.crowdDTO.crowdConditionList.splice(index, 1)
    if (formFields.crowdDTO.crowdConditionList[0]) formFields.crowdDTO.crowdConditionList[0].conditionJudge = 0
    props.formRef.validateFields([['crowdDTO', 'crowdConditionList']])
  },
  handleAddItem(item) {
    const index2 = item.crowdConditionRowList.length
    const newItem2 = crowdCreateGroupChildItemGet()
    if (index2 > -1) newItem2.conditionJudge = 1
    item.crowdConditionRowList.splice(index2 + 1, 0, newItem2)
  },
  handleDelItem(item) {
    const index2 = item.crowdConditionRowList.length
    item.crowdConditionRowList.splice(index2 - 1, 1)
    if (item.crowdConditionRowList[0]) item.crowdConditionRowList[0].conditionJudge = 0
  },
  setFormFields() {
    const formFields = props.formFields
    formFields.crowdDTO = formFields.crowdDTO || {}
    // 人群包可用时间
    formFields.crowdDTO.timeType = props.timeType || formFields.crowdDTO.timeType
    // 人群包-类型
    formFields.crowdDTO.crowdType = formFields.crowdDTO.crowdType || 0
    // 人群包-新建规则
    formFields.crowdDTO.crowdName = formFields.crowdDTO.crowdName || ''
    formFields.crowdDTO.crowdConditionList = formFields.crowdDTO.crowdConditionList?.length ? formFields.crowdDTO.crowdConditionList : [
      {
        conditionJudge: 0,
        crowdConditionRowList: [
          {
            conditionJudge: 0,
            conditions: []
          },
          {
            conditionJudge: 1,
            conditions: []
          }
        ]
      },
      {
        conditionJudge: 1,
        crowdConditionRowList: [
          {
            conditionJudge: 0,
            conditions: []
          },
          {
            conditionJudge: 1,
            conditions: []
          }
        ]
      }
    ]
    // 人群包-选择人群包
    formFields.crowdDTO.crowdId = formFields.crowdDTO.crowdId || null
    // 人群包-导入人群包
    formFields.crowdDTO.crowdImportResultDTO = formFields.crowdDTO.crowdImportResultDTO || {
      memberCodeList: [],
      fileName: ''
    }
  },
  setFormRules() {
    const formRules = props.formRules
    // 人群包-类型
    formRules.crowdType = formRules.crowdType || [{ required: true, message: '本项必选' }]
    // 人群包-新建规则
    formRules.crowdName = formRules.crowdName || [{ required: true, message: '请输入人群包名称' }]
    formRules.crowdConditionList = formRules.crowdConditionList || [{ required: true, message: '请选择人群条件' }]
    formRules.conditions = formRules.conditions || [{ required: true, message: '请选择条件' }]
    // 人群包-选择人群包
    formRules.crowdId = formRules.crowdId || [{ required: true, message: '本项必选' }]
    // 人群包-导入人群包
    formRules.memberCodeList = formRules.memberCodeList || [{ required: true, message: '本项必传', }]
  }
}
thisMethods.setFormFields()
thisMethods.setFormRules()
// 修改人群包 crowdConditionList 的时候，需要传 isUpdate
watch(() => props.formFields.crowdDTO, (newVal, oldVal) => {
  if (props.formFields.crowdDTO.timeType === '') {
    props.formFields.crowdDTO.timeType = props.timeType
    console.log("🚀 ~ watch ~ newVal:", props.formFields.crowdDTO, newVal)
  }
}, { deep: true, immediate: true })
// 修改人群包 crowdConditionList 的时候，需要传 isUpdate
watch(() => props.formFields.crowdDTO.crowdConditionList, (newVal, oldVal) => {


  // console.log("🚀 ~ watch ~ props.formType:", props.formType)
  if (props.formType === 1 && props.formFields.crowdDTO.crowdType == 0 && oldVal?.[0]?.id) {
    // debugger
    // 如果用户一直修改，最后又改回去，怎么处理？
    // 回复：无所谓，可以认为已经更改了 isUpdate 为 1
    props.formFields.crowdDTO.isUpdate = 1
  }
}, { deep: false })
</script>

<style lang="scss" scoped>
.formItemReset {
  margin-bottom: 0;

  .itemWrap {
    display: flex;
    align-items: center;

    .inputWrap {
      display: flex;
      align-items: center;
    }

    .iconWrap {
      display: flex;
      align-items: center;
      padding: 0 10px;
      height: 32px;
      margin-bottom: 24px;
    }
  }
}

.crowdTemplateExportAndImportWrap {
  margin-left: -15px;
  display: flex;
  align-items: center;
}
</style>
