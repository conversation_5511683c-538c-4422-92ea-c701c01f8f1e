package com.dz.ms.product.dto.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

/**
 * 货架标签组DTO
 *
 * @author: fei
 * @date: 2024/11/24 11:32
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "货架标签组")
public class ShelfTagGroupResDTO {

    @ApiModelProperty(value = "ID")
    private Long id;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "货架名称")
    private String shelfName;
    @ApiModelProperty(value = "标签ID")
    private Long tagId;
    @ApiModelProperty(value = "标签名称")
    private String tagName;
    @ApiModelProperty(value = "1一级标签 2二级标签 ...")
    private Integer cate;
    @ApiModelProperty(value = "子标签")
    List<ShelfTagGroupResDTO> children = new ArrayList<>();

}
