package com.dz.common.core.utils;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 随机数生成工具
 * @author: Handy
 * @date: 2022/07/18
 */
public class RandomUtils {

    private static int sn = 0;

    /**
     * 生成多位随机数字
     */
    public static String genNumberCode(int size) {
        StringBuffer retStr = new StringBuffer("");
        String strTable = "1234567890";
        int len = strTable.length();
        for (int i = 0; i < size; i++) {
            double dblR = Math.random() * len;
            int intR = (int) Math.floor(dblR);
            retStr.append(strTable.charAt(intR));
        }
        return retStr.toString();
    }

    /**
     * 生成订单号
     * 20位数字，日期 + 当日秒 + 随机数
     */
    public static String generateOrderCode(Long userId) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String date = sdf.format(new Date());
        //String random = RANDOM.nextInt(1000000)+"";
        String random = (userId % 100000) + "";
        int times = 5 - random.length();
        for (int i = 0; i < times; i++) {
            random = "0" + random;
        }
        return date + random;
    }

    /**
     * 生成OutSn
     * 20位数字，随机数 + 日期 + 当日秒
     */
    public static String generateOutSn(Long userId) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String date = sdf.format(new Date());
        //String random = RANDOM.nextInt(1000000)+"";
        String random = (userId % 100000) + "";
        int times = 5 - random.length();
        for (int i = 0; i < times; i++) {
            random = "0" + random;
        }
        return random + date;
    }

    /**
     * 生成多位随机数字符
     */
    public static String genStringCode(int size) {
        StringBuffer retStr = new StringBuffer("");
        String strTable = "1234567890ABCDEFGHIGKLMNOPQRSTUVWXYZ";
        int len = strTable.length();
        for (int i = 0; i < size; i++) {
            double dblR = Math.random() * len;
            int intR = (int) Math.floor(dblR);
            retStr.append(strTable.charAt(intR));
        }
        return retStr.toString();
    }


    public static void main(String[] args) {
//
//        ConcurrentHashMap<Integer, LongAdder> map = new ConcurrentHashMap<>();
//        for(int i=0; i<10000; i++) {
//            int index = rateRandom(Arrays.asList(100, 400, 300, 200));
//            map.computeIfAbsent(index, (old)->new LongAdder()).increment();
//        }
//        map.forEach((index, count)->{
//        });
//        List<String> list = new ArrayList<>();
//        list.add("1");
    }
}
