package com.dz.ms.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.product.entity.ProductTag;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 商品标签表Mapper
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:25
 */
@Repository
public interface ProductTagMapper extends BaseMapper<ProductTag> {

    int insertBatch(@Param("list") List<ProductTag> productTagList);
}
