import {
  getOnLineOrderDetail
} from '../../api/index'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    orderSn: '',
    loading: false,
    isBackHidden: false,
    details: {},
    total: 0,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  async onShow(options) {
    this.setData({
      loading: true
    })
    if (options) {
      await this.setData({
        orderSn: options.orderSn
      })
    }
    await this.getDetail()
  },
  getDetail() {
    const {
      orderSn,
    } = this.data
    getOnLineOrderDetail({
      orderSn: orderSn
    }).then(async res => {
      let v = 0;
      if (res.data?.goods && res.data.goods.length > 0) {
        res.data.goods.forEach(item => v += item.quantity)
      }
      await this.setData({
        details: res.data,
        total: v.toFixed(2)
      })
    }).finally(() => {
      this.setData({
        loading: false
      })
    })
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },
})
