package com.dz.ms.product.dto.res;

import com.dz.ms.product.dto.TagInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * 货架商品小程序回参
 *
 * @author: fei
 * @date: 2024/12/06 16:25
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@ApiModel(value = "货架商品小程序回参")
public class ProductMiniResDTO{

    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "副标题")
    private String subTitle;
    @ApiModelProperty(value = "商品类型 1实物商品 2电子券")
    private Integer pdType;
    @ApiModelProperty(value = "是否赠品 0赠品 1正品商品")
    private Integer beGift;
    @ApiModelProperty(value = "1级标签")
    private List<TagInfoDTO> tags1;
    @ApiModelProperty(value = "2级标签")
    private List<TagInfoDTO> tags2;
    @ApiModelProperty(value = "场景图片地址 逗号分隔")
    private String scenceImgUrl;
    @ApiModelProperty(value = "商品橱窗图片展示位 逗号分隔")
    private String shelfImgUrl;
    @ApiModelProperty(value = "商品详情")
    private String details;
    @ApiModelProperty(value = "购买方式 0积分 1积分+金额")
    private Integer purchaseType;
    @ApiModelProperty(value = "兑换积分")
    private Integer costPoint;
    @ApiModelProperty(value = "1积分+金额时 仍需支付的金额")
    private BigDecimal costPrice;
    @ApiModelProperty(value = "吊牌价")
    private BigDecimal prePrice;
    @ApiModelProperty(value = "金额是否展示在货架列表")
    private Integer costPriceOnShelf;
    @ApiModelProperty(value = "兑换须知类型 1图片 2富文本")
    private Integer exchangeDescType;
    @ApiModelProperty(value = "兑换须知图片")
    private String exchangeDescUrl;
    @ApiModelProperty(value = "兑换须知富文本")
    private String exchangeDescContent;
    @ApiModelProperty(value = "商品详情类型 1图片 2富文本")
    private Integer detailsType;
    @ApiModelProperty(value = "商品详情图片")
    private String detailsUrl;
    @ApiModelProperty(value = "商品详情富文本")
    private String detailsContent;
    @ApiModelProperty(value = "使用说明类型 1图片 2富文本")
    private Integer referType;
    @ApiModelProperty(value = "使用说明图片")
    private String referUrl;
    @ApiModelProperty(value = "使用说明富文本")
    private String referContent;

    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "主键ID")
    private Long shelfProductId;
    @ApiModelProperty(value = "商品ID")
    private Long productId;
    @ApiModelProperty(value = "商品状态 0禁用 1启用")
    private Integer productState;
    @ApiModelProperty(value = "积分划线价")
    private Integer prePoint;
    @ApiModelProperty(value = "发货方式 1线下使用 2邮寄")
    private Integer deliveryType;
    @ApiModelProperty(value = "展示方式 1积分加价购 2商品兑换")
    private Integer showType;
    @ApiModelProperty(value = "当前库存")
    private Integer currentInventory;
    @ApiModelProperty(value = "库存售罄组件配置")
    private String inventoryContent;
    @ApiModelProperty(value = "是否活动产品 1:是 空值或者其他:否")
    private Integer isCampaign;
    @ApiModelProperty(value = "是否有活动产品权限 1:是 空值或者其他:否")
    private Integer isCampaignAuth;
    @ApiModelProperty(value = "规则组件配置(非人群包用户提示)")
    private String ruleContent;
    @ApiModelProperty(value = "角标字符串 多角标逗号分隔")
    private List<String> superscript;
}
