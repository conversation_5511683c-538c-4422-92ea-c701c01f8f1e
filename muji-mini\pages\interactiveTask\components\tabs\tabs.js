import dayjs from '../../../../utils/dayjs.min'
// import {
//   readMessage
// } from '../../../../api/index'
const app = getApp();


Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    tabList: {
      type: Array,
      value: [],
    },
    currentTab: {
      type: String,
      value: '',
      observer(val) {
        console.log('val', val)
      }
    },
    showNew: {
      type: Boolean,
      value: false,
    }
  },
  /**
   * 组件的初始数据
   */
  data: {
    activePosition: 120,
  },
  /**
   * 组件的方法列表
   */
  methods: {
    changeTab(v) {
      const {
        value
      } = v.currentTarget.dataset;
      switch (value) {
        case '1':
          wx.$mp.track({
            event: 'task_list_uncomplate'
          })
          this.setData({
            activePosition: 120
          })
          break;
        case '2':
          wx.$mp.track({
            event: 'task_list_complate'
          })
          this.setData({
            activePosition: 460
          })
          break;
      }
      this.triggerEvent('change', {
        type: value
      })
    }
  }
})
