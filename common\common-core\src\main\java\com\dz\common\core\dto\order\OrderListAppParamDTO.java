package com.dz.common.core.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * app端我的订单列表入参
 */
@Data
public class OrderListAppParamDTO {

	@ApiModelProperty(value = "订单号")
	private String orderCode;
	@ApiModelProperty(value = "订单状态 0已完成 1待支付 2待发货 3已发货 4待兑换 5部分兑换 6已兑换 7已取消")
	private Integer orderStatus;
	@ApiModelProperty(value = "第几页")
	private Integer pageNum;
	@ApiModelProperty(value = "每页条数")
	private Integer pageSize;
	@ApiModelProperty(value = "用户id", hidden = true)
	private Long userId;
}
