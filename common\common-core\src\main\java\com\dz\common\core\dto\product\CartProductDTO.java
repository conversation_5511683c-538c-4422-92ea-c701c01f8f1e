package com.dz.common.core.dto.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 购物车商品信息DTO
 *
 * @author: handongdong
 * @date: 2019/03/20 10:42
 */
@Setter
@Getter
@ToString
@ApiModel(value = "购物车商品信息DTO")
@NoArgsConstructor
public class CartProductDTO implements Serializable {

    private static final long serialVersionUID = 550407949901410199L;

    @ApiModelProperty(value = "ID")
    private Long id;
    @ApiModelProperty(value = "货架ID")
    private Long shelfId;
    @ApiModelProperty(value = "货架商品ID")
    private Long shelfProductId;
    @ApiModelProperty(value = "商品id")
    private Long productId;
    @ApiModelProperty(value = "商品名称")
    private String productName;
    @ApiModelProperty(value = "兑换积分，加购时积分")
    private Integer costPoint;
    @ApiModelProperty(value = "积分划线价")
    private Integer prePoint;
    @ApiModelProperty(value = "兑换金额，加购时单价")
    private BigDecimal costPrice;
    @ApiModelProperty(value = "商品类型 1实物商品 2电子券")
    private Integer pdType;
    @ApiModelProperty(value = "外部数据ID 优惠券ID需兑换")
    private String venderId;
    @ApiModelProperty(value = "商品图片")
    private String imgUrl;
    @ApiModelProperty(value = "商品状态(0禁用 1启用)")
    private Integer state;
    @ApiModelProperty(value = "展示方式 1积分加价购 2商品兑换")
    private Integer showType;
    @ApiModelProperty(value = "金额是否展示在货架列表")
    private Integer costPriceOnShelf;
    @ApiModelProperty(value = "兑换积分，来自商品表的价格留存")
    private Integer pCostPoint;
    @ApiModelProperty(value = "1积分+金额时 仍需支付的金额，来自商品表的价格留存")
    private BigDecimal pCostPrice;
    @ApiModelProperty(value = "兑换积分，来自货架上的价格留存")
    private Integer sCostPoint;
    @ApiModelProperty(value = "积分划线价，来自货架上的价格留存")
    private Integer sPrePoint;
    @ApiModelProperty(value = "角标ID列表")
    private List<Long> superscriptIdList;
    @ApiModelProperty(value = "角标名称列表")
    private List<String> superscriptNameList;
    @ApiModelProperty(value = "活动角标字符串")
    private List<String> superscriptCampaignNameList;
    @ApiModelProperty(value = "营销活动ID")
    private Long campaignId;
    @ApiModelProperty(value = "营销规则ID")
    private Long ruleId;
    @ApiModelProperty(value = "营销规则人群包ID")
    private Long ruleGroupId;
    @ApiModelProperty(value = "活动规则限购时间设置 1活动时间内 2周期")
    private Integer ruleType;
    @ApiModelProperty(value = "周期天数")
    private Integer period;
    @ApiModelProperty(value = "活动规则创建时间")
    private Date rCreated;
    @ApiModelProperty(value = "兑换积分，来自营销规则的价格留存")
    private Integer rCostPoint;
    @ApiModelProperty(value = "积分划线价，来自营销规则的价格留存")
    private Integer rPrePoint;
    @ApiModelProperty(value = "每人限购数")
    private Integer rEveryoneLimit;
    @ApiModelProperty(value = "活动限购数量")
    private Integer rPurchaseLimit;
    @ApiModelProperty(value = "活动开始时间")
    private Date campaignOnStartTime;
    @ApiModelProperty(value = "活动结束时间")
    private Date campaignOnEndTime;
    @ApiModelProperty(value = "默认限购数量/月(货架商品限购数量)")
    private Integer limitNum;
    @ApiModelProperty(value = "商品数量")
    private Integer number;
    @ApiModelProperty(value = "选中标示 0未选中 1选中")
    private Integer checked;
    @ApiModelProperty(value = "是否包邮 0：否 1：是")
    private Integer expressFree;
    @ApiModelProperty(value = "创建时间")
    private Date created;
    @ApiModelProperty(value = "是否售罄 1售罄 0未售罄")
    private Integer isSellout;
    @ApiModelProperty(value = "是否删除")
    private Integer isDeleted;

}