package com.dz.ms.product.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dz.common.core.dto.MujiGoods;
import com.dz.common.core.dto.MujiStockInfo;
import com.dz.common.core.dto.export.ProductExportDTO;
import com.dz.common.core.dto.product.CpStaticDTO;
import com.dz.common.core.dto.product.OdsItemDTO;
import com.dz.ms.product.dto.ProductDTO;
import com.dz.ms.product.dto.req.CrmProductListParamDTO;
import com.dz.ms.product.entity.OdsItem;
import com.dz.ms.product.entity.Product;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 商品信息Mapper
 *
 * @author: LiinNs
 * @date: 2024/11/16 16:25
 */
@Repository
public interface ProductMapper extends BaseMapper<Product> {

    IPage<Product> selectPageByParam(Page<Product> page, @Param("param") CrmProductListParamDTO param);
    IPage<Product> selectPageNoDistinctByParam(Page<Product> page, @Param("param") CrmProductListParamDTO param);

    List<ProductExportDTO> selectListByParam(@Param("param") CrmProductListParamDTO crmProductListParamDTO);

    List<Product> selectNoPageByParam(@Param("param") CrmProductListParamDTO param);

    /**
     * 根据ids查询少量字段
     */
    List<ProductDTO> selLessListByIds(@Param("ids") List<Long> ids);

    List<Product> selectByPdType();

    int updateStatic(@Param("productId") Long productId, @Param("number") Integer number);

    List<MujiGoods> selectMujiGoods(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    List<MujiStockInfo> selectMujiStockInfo(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    /**
     * CP号不能重复使用
     *
     * @param id
     * @param venderId
     * @return
     */
    Integer selectExist(@Param("id") Long id, @Param("venderId") String venderId);

    List<CpStaticDTO> selectByShelfProductIds(@Param("shelfProductIdList") List<Long> shelfProductIdList);

    void insertBatchOdsItem(List<OdsItem> odsItems);

    void deleteAllOdsItem();

    List<OdsItemDTO> selectBySftpProductItemId(@Param("itemId") String itemId);
}
