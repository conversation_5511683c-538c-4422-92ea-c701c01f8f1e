package com.dz.common.core.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;


/**
 * 下载中心
 *
 * @author: LiinNs
 * @date: 2024/11/26 15:48
 */
@Getter
@Setter
@NoArgsConstructor
public class DownloadCenterDTO {


    @ApiModelProperty(value = "ID ")
    private Long id;
    @ApiModelProperty(value = "业务报表接口")
    private String queryUrl;
    @ApiModelProperty(value = "请求参数")
    private String jsonParam;
    @ApiModelProperty(value = "请求方法")
    private String method;
    @ApiModelProperty(value = "表头")
    private String header;
    @ApiModelProperty(value = "菜单名")
    private String menuName;
    @ApiModelProperty(value = "模块名")
    private String moduleName;
    @ApiModelProperty(value = "文件名")
    private String fileName;
    @ApiModelProperty(value = "下载次数")
    private Long downloadNum;
    @ApiModelProperty(value = "拓展名")
    private String fileExt;
    @ApiModelProperty(value = "下载文件路径")
    private String sourceUrl;
    @ApiModelProperty(value = "状态 0-生成中 1-已完成 2-已失败 3-已失效")
    private Integer state;
    @ApiModelProperty(value = "失败原因")
    private String errorDesc;
    @ApiModelProperty(value = "是否删除 0-未删除 1-已删除")
    private Integer isDeleted;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;
    @ApiModelProperty(value = "创建人")
    private Long creator;
    @ApiModelProperty(value = "创建时间")
    private Date created;
}
