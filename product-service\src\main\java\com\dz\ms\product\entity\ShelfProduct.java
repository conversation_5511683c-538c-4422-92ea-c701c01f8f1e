package com.dz.ms.product.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 货架商品
 *
 * @author: LiinNs
 * @date: 2024/11/27 15:15
 * 注意: 此表不受ModelToSql类管理
 */
@Getter
@Setter
@NoArgsConstructor
@Builder
@AllArgsConstructor
@Table("货架商品")
@TableName(value = "shelf_product")
public class ShelfProduct implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "货架ID", isIndex = true)
    private Long shelfId;
    @Columns(type = ColumnType.VARCHAR, length = 256, isNull = true, comment = "货架名称", isIndex = true)
    private String shelfName;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "商品ID", isIndex = true)
    private Long productId;
    @Columns(type = ColumnType.VARCHAR, length = 256, isNull = true, comment = "商品名称", isIndex = true)
    private String productName;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, comment = "商品在货架上的位置 越大越靠后")
    private Integer onShelfIndex;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, comment = "商品类型 1实物商品 2电子券")
    private Integer pdType;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, defaultValue = "1", comment = "发货方式 1线下使用 2邮寄")
    private Integer deliveryType;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = true, defaultValue = "0", comment = "是否展示 0不展示 1展示")
    private Integer beShow;
    @Columns(type = ColumnType.TINYINT, length = 1, isNull = false, comment = "展示方式 1积分加价购 2商品兑换")
    private Integer showType;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, defaultValue = "0", comment = "上架库存")
    private Integer onInventory;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, defaultValue = "0", comment = "目前库存")
    private Integer currentInventory;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, defaultValue = "0", comment = "当前货架商品累计兑换量")
    private Integer exchangeNum;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, comment = "兑换积分", isIndex = true)
    private Integer costPoint;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "积分划线价")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer prePoint;
    @Columns(type = ColumnType.INT, length = 10, isNull = true, comment = "默认限购数量/月")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer limitNum;
    @Columns(type = ColumnType.DECIMAL, length = 19 ,scale = 2, isNull = true, defaultValue = "0", comment = "1积分+金额时 仍需支付的金额")
    private BigDecimal costPrice;
    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = true, comment = "角标字符串 多角标逗号分隔")
    private String superscript;
    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = true, comment = "活动ID")
    private String superscriptCampaignId;
    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = true, comment = "活动角标字符串")
    private String superscriptCampaign;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, defaultValue = "0", comment = "是否删除 0未删除 1已删除")
    @TableLogic
    private Integer isDeleted;

    public ShelfProduct(Long id, Long shelfId, String shelfName, Long productId, String productName, Integer onShelfIndex, Integer pdType, Integer deliveryType, Integer beShow, Integer onInventory, Integer currentInventory, Integer exchangeNum, Integer costPoint, Integer prePoint, BigDecimal costPrice, String superscript, String superscriptCampaignId, String superscriptCampaign) {
        this.id = id;
        this.shelfId = shelfId;
        this.shelfName = shelfName;
        this.productId = productId;
        this.productName = productName;
        this.onShelfIndex = onShelfIndex;
        this.pdType = pdType;
        this.deliveryType = deliveryType;
        this.beShow = beShow;
        this.onInventory = onInventory;
        this.currentInventory = currentInventory;
        this.exchangeNum = exchangeNum;
        this.costPoint = costPoint;
        this.prePoint = prePoint;
        this.costPrice = costPrice;
        this.superscript = superscript;
        this.superscriptCampaignId = superscriptCampaignId;
        this.superscriptCampaign = superscriptCampaign;
    }

}
