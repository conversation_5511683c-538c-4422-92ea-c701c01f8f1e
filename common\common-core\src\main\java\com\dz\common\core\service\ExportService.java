package com.dz.common.core.service;

import com.alibaba.fastjson.JSONObject;
import com.dz.common.core.constants.ApplicationConstant;
import com.dz.common.core.dto.DownloadCenterDTO;
import com.dz.common.core.dto.DownloadHeaderDTO;
import com.dz.common.core.dto.OssDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.fegin.basic.ReportDownloadFeignClient;
import com.dz.common.core.utils.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class ExportService {

    @Resource
    private ReportDownloadFeignClient reportDownloadFeignClient;

    public void writeExportList(String fileName, String fileExt, String reportCode, Long downloadCenterId, CurrentUserDTO commonLoginDTO, List<JSONObject> reList) {
        byte[] content;
        try {
            log.info("业务报表-->{},读取数据总行数-->{}", fileName, reList.size());
            //写入excel
            List<DownloadHeaderDTO> headerList = reportDownloadFeignClient.getHeader(reportCode, commonLoginDTO.getTenantId()).getData();
            content = ExcelUtils.write(headerList, reList, fileName);
        } catch (Exception e) {
            log.error("报表导出失败,exception:", e);
            DownloadCenterDTO downloadCenterDTO = new DownloadCenterDTO();
            downloadCenterDTO.setId(downloadCenterId);
            downloadCenterDTO.setErrorDesc(e.getMessage());
            downloadCenterDTO.setState(2);
            reportDownloadFeignClient.updateDownloadCenter(downloadCenterDTO);
            return;
        }
        //文件存储oss
        OssDTO ossDTO = new OssDTO();
        ossDTO.setKey(ApplicationConstant.PATH + fileName + fileExt);
        ossDTO.setBytes(content);
        String url = reportDownloadFeignClient.uploadOss(ossDTO);
        DownloadCenterDTO downloadCenterDTO = new DownloadCenterDTO();
        downloadCenterDTO.setId(downloadCenterId);
        downloadCenterDTO.setSourceUrl(url);
        downloadCenterDTO.setState(1);
        reportDownloadFeignClient.updateDownloadCenter(downloadCenterDTO);
        log.info("下载业务报表任务结束, 文件名: {}", fileName);
    }
}
