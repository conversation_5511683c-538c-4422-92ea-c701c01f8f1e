package com.dz.ms.user.dto;

import com.dz.common.base.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * OMS-权限功能DTO
 * @author: Handy
 * @date:   2020/01/29 19:15
 */
@Data
@ApiModel(value = "OMS-权限功能")
public class OmsPermissionDTO extends BaseDTO {
    
    @ApiModelProperty(value = "权限ID")
    private Long id;
    @ApiModelProperty(value = "权限编号")
    private String code;
    @ApiModelProperty(value = "父节点ID")
    private Long parentId;
    @ApiModelProperty(value = "是否有子节点")
    private Integer hasChild;
    @ApiModelProperty(value = "权限类型 1模块 2页面 3功能")
    private Integer permitType;
    @ApiModelProperty(value = "权限名称")
    private String permitName;
    @ApiModelProperty(value = "页面地址")
    private String url;
    @ApiModelProperty(value = "权限描述")
    private String permitDesc;
    @ApiModelProperty(value = "菜单显示排序")
    private Integer displaySort;
    @ApiModelProperty(value = "创建时间")
    private Date created;

}