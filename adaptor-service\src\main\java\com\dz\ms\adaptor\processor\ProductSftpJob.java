package com.dz.ms.adaptor.processor;

import com.dz.common.core.fegin.order.ExchangeOrderFeignClient;
import com.dz.common.core.fegin.product.ProductFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import javax.annotation.Resource;

/**
 * 商品item sftp任务
 */
@Slf4j
@Component
public class ProductSftpJob implements BasicProcessor {

    @Resource
    private ProductFeignClient productFeignClient;

    @Override
    public ProcessResult process(TaskContext context) {
        productFeignClient.getSftpFile();
        log.info("拉取商品数据 完成");
        return new ProcessResult(true, "success");
    }
}