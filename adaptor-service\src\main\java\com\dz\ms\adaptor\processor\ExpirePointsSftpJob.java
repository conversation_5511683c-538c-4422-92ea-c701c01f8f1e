package com.dz.ms.adaptor.processor;

import com.dz.ms.adaptor.service.PointsSubscriptionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * SFTP获取即将到期积分用户及积分量
 */
@Slf4j
@Component
public class ExpirePointsSftpJob implements BasicProcessor {

    @Resource
    private PointsSubscriptionService pointsSubscriptionService;
    @Override
    public ProcessResult process(TaskContext context) throws IOException {
        log.info("SFTP获取即将到期积分用户及积分量开始");
        pointsSubscriptionService.getSftpFile();
        log.info("SFTP获取即将到期积分用户及积分量结束");
        return new ProcessResult(true, "success");
    }
}