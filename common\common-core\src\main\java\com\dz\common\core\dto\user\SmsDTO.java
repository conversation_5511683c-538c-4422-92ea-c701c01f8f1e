package com.dz.common.core.dto.user;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@NoArgsConstructor
@ToString
public class SmsDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "手机号码")
	@NotBlank
	private String mobile;
	
}
