<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.product.mapper.ShelfCampaignRuleMapper">

    <!-- 查询基础字段 -->
    <sql id="Base_Column_List">
        id,
  	    campaign_id,
  	    name,
  	    group_id,
  	    content,
  	    shelf_id,
  	    rule_type,
  	    period,
  	    rule_num,
  	    tenant_id,
  	    creator,
  	    created,
  	    modified,
  	    modifier
    </sql>

    <!-- 查询示例 -->
    <select id="selectDemo" parameterType="java.lang.Long" resultType="com.dz.ms.product.entity.ShelfCampaignRule">
        select
        <include refid="Base_Column_List"/>
        from shelf_campaign_rule
        where id = #{id,jdbcType=BIGINT}
        AND tenant_id = #{tenantId}
    </select>

	<update id="updGroupIdIntoNull">
		UPDATE shelf_campaign_rule SET group_id = null WHERE group_id = #{groupId}
	</update>

	<update id="deleteByParam">
		update shelf_campaign_rule
		set is_deleted = 1
		<where>
			<if test="param.campaignId != null">
				AND campaign_id = #{param.campaignId}
			</if>
			<if test="param.ruleId != null">
				AND id = #{param.ruleId}
			</if>
			<if test="param.ruleIdList != null and param.ruleIdList.size > 0">
				AND id in
				<foreach collection="param.ruleIdList" index="index" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
			<if test="param.shelfId != null">
				AND shelf_id = #{param.shelfId}
			</if>
		</where>
	</update>

</mapper>
