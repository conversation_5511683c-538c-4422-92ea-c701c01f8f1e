package com.dz.ms.basic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * APP端租户相关配置
 * @author: Handy
 * @date:   2022/08/25 11:18
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "APP端租户相关配置")
public class TenantConfigAppDTO {

    @ApiModelProperty(value = "企微状态 0关闭 1启用")
    private Integer state;
    @ApiModelProperty(value = "品牌logo URL")
    private String logoUrl;
    @ApiModelProperty(value = "UI自定义配置")
    private String uiConfig;
    @ApiModelProperty(value = "租户ID")
    private Long tenantId;

    public TenantConfigAppDTO(Integer state, String logoUrl, String uiConfig, Long tenantId) {
        this.state = state;
        this.logoUrl = logoUrl;
        this.uiConfig = uiConfig;
        this.tenantId = tenantId;
    }
}
