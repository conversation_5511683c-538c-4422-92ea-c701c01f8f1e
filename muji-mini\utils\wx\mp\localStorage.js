const suffix = ''

const setStorageSync = (key, data, expires) => {
  const myKey = key + suffix
  if (expires !== undefined) { // expires多少秒后失效
    expires = new Date().getTime() + expires * 1000
  }
  return wx.setStorageSync(myKey, { data, expires })
}
const removeStorageSync = (key) => {
  const myKey = key + suffix
  return wx.removeStorageSync(myKey)
}
const clearStorageSync = () => {
  return wx.clearStorageSync()
}
const getStorageSync = (key) => {
  const myKey = key + suffix
  let item = wx.getStorageSync(myKey)
  if (item === '') {
    item = { data: '' }
  }
  let { data, expires } = item
  if (expires !== undefined && new Date().getTime() - expires >= 0) {
    removeStorageSync(key)
    data = getStorageSync(key)
  }
  return data
}

export default {
  setStorageSync,
  removeStorageSync,
  clearStorageSync,
  getStorageSync
}
