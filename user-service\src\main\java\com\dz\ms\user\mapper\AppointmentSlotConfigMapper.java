package com.dz.ms.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dz.ms.user.entity.AppointmentSlotConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface AppointmentSlotConfigMapper extends BaseMapper<AppointmentSlotConfig> {

    @Update("UPDATE appointment_slot_config SET remaining_stock = #{remainingStock} WHERE id = #{id}")
    void update(AppointmentSlotConfig slotConfig);

    @Select("SELECT * FROM appointment_slot_config WHERE appointment_id = #{appointmentId} AND date = #{appointmentDate} AND date >= DATE_FORMAT(now(), '%Y/%m/%d') OR (date = DATE_FORMAT(now(), '%Y/%m/%d') AND start_time >= DATE_FORMAT(now(), '%H:%i')) ORDER BY start_time")
    List<AppointmentSlotConfig> findByAppointmentIdAndAppointmentDate(Long appointmentId, String appointmentDate);

    // 新增方法：按startTime正序查询指定日期的AppointmentSlotConfig列表
    List<AppointmentSlotConfig> findByAppointmentIdAndAppointmentDateOrderByStartTime(Long appointmentId, String date);

    // 新增方法：查询指定日期和时间段的库存
    @Select("SELECT remaining_stock FROM appointment_slot_config WHERE appointment_id = #{appointmentId} AND date = #{date} AND start_time = #{startTime} AND end_time = #{endTime} AND date >= DATE_FORMAT(now(), '%Y/%m/%d')  OR (date = DATE_FORMAT(now(), '%Y/%m/%d') AND start_time >= DATE_FORMAT(now(), '%H:%i'))")
    Integer findRemainingStockByDateAndTime(Long appointmentId, String date, String startTime, String endTime);

    // 新增方法：更新库存
    @Update("UPDATE appointment_slot_config SET remaining_stock = #{remainingStock} WHERE appointment_id = #{appointmentId} AND date = #{appointmentDate} AND start_time = #{startTime} AND end_time = #{endTime} AND remaining_stock > 0")
    int updateAppointmentSlotConfig(@Param("appointmentId") Long appointmentId, @Param("appointmentDate") String appointmentDate, @Param("startTime") String startTime, @Param("endTime") String endTime, @Param("remainingStock") int remainingStock);

    // 新增方法：查询指定appointmentId且日期大于等于今天的AppointmentSlotConfig列表
    @Select("SELECT * FROM appointment_slot_config WHERE appointment_id = #{appointmentId} AND date >= DATE_FORMAT(now(), '%Y/%m/%d')")
    List<AppointmentSlotConfig> findByAppointmentIdAndDateAfterToday(Long appointmentId);

    // 新增方法：查询最大活动日期
    @Select("SELECT MAX(date) FROM appointment_slot_config WHERE appointment_id = #{appointmentId}")
    String findMaxAppointmentDate(Long appointmentId);

}