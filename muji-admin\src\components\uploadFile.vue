<template>
  <div class="uploadList">
    <!-- 
          <uploadFile
          :api="接口"
          @ok="导入成功触发方法"
          :disabled="是否置灰">按钮文案</uploadFile>
     -->
    <!-- {{ fileList }} -->
    <a-upload :disabled="disabled" accept=".xlsx,.xls,.csv" :maxCount="maxCount" v-model:file-list="fileList" :show-upload-list="false" :custom-request="getFile">

      <a-space>
        <a-button :type="type" :disabled="disabled">
          <slot></slot>
        </a-button>
        <slot name="right"></slot>
      </a-space>
    </a-upload>
    <div class="file-box" v-if="fileUrl">
      <div class="file-box-list">
        <div :class="['file-box-name',disabled?'file-box-disabled':'']" @click="downloadChange(fileUrl)" :title=" fileName?fileName: getFileNameFromPath(fileUrl)
        "> {{
         fileName?fileName: getFileNameFromPath(fileUrl) }}</div>
        <delete-outlined class="file-box-delete" @click.stop="removeFile(fileUrl)" v-if="!disabled && deleted && fileUrl" />

      </div>
    </div>
  </div>
</template>
<script setup>
import { fileUpload } from '@/http/index.js'
import { message } from 'ant-design-vue';
import { reactive, toRefs, ref, onMounted, watch } from 'vue';
const emit = defineEmits(['ok'])
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  // 图片上传接口
  api: { // 图片上传接口
    type: Function,
    default: fileUpload,
  },
  maxCount: {
    type: Number,
    default: 1
  },
  // 是否展示删除图标
  deleted: {
    type: Boolean,
    default: true
  },
  form: {
    type: Object
  },  // form的key值
  path: {
    type: [String, Number],
  },
  // 默认文件
  fileUrl: {
    type: String,
  },
  fileName: {
    type: String,
    default: ''
  },
  pathFileName: {
    type: [String, Number],
  },
  type: {
    type: String,
    default: 'primary'
  },
  showName: {
    type: Boolean,
    default: false
  }
})
const fileList = ref([])

let { loading } = toRefs(reactive({
  loading: false,
}))
function getFileNameFromPath(filePath) {
  // 使用 split 方法分割路径
  if (filePath) {
    const parts = filePath.split('/');
    return parts[parts.length - 1];
  }
}
//文件移除
const removeFile = () => {
  emit('ok', {
    form: props.form,
    path: props.path,
    pathFileName: props.pathFileName,
    fileUrl: '',
    fileName: ''
  })
}
const downloadChange = async (fileUrl) => {
  // console.log("🚀 ~ downloadChange ~ fileUrl:", fileUrl)
  if (!props.disabled) {
    let fileUrl1 = fileUrl; // 文件的 URL
    let fileName1 = props.fileName; // 指定下载的文件名称
    // console.log("🚀 ~ downloadChange ~ fileName:", fileName1)

    // Fetch the file from the URL
    const response = await fetch(fileUrl1);
    const blob = await response.blob();

    // Create a temporary anchor element
    const a = document.createElement('a');
    a.href = URL.createObjectURL(blob);
    a.download = fileName1;
    document.body.appendChild(a);
    a.click();

    // Clean up
    setTimeout(() => {
      document.body.removeChild(a);
      URL.revokeObjectURL(a.href);
    }, 100);
  }
}
// 文件上传
const getFile = async ({ file }) => {
  // console.log(file)
  // if (loading.value) return
  // loading.value = true
  const formData = new FormData()
  formData.append('file', file)
  props.api(formData).then(res => {
    message.success('导入成功')
    if (props.showName) {
      emit('ok', {
        form: props.form,
        path: props.path,
        pathFileName: props.pathFileName,
        fileUrl: res.data.url,
        fileName: res.data.name
      })
    } else {
      emit('ok', {
        form: props.form,
        path: props.path,

        fileUrl: res.data,

      })
    }

    // loading.value = false
  }).finally(() => {
    // loading.value = false
  })
}

</script>
<style lang="scss" scoped>
.uploadList {
  // width: 150px;
  // display: flex;
}

.file-box {
  width: 300px;
  margin-top: 5px;

  &-name {
    font-weight: 500;
    flex: 1;
    white-space: nowrap;
    /* 防止文字换行 */
    overflow: hidden;
    /* 隐藏溢出部分 */
    text-overflow: ellipsis;
    /* 使用省略号表示溢出的部分 */
    cursor: pointer;
  }
  &-disabled {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
  }
  &-list {
    display: flex;
    padding: 5px;
    height: 32px;
    // border: 1px solid #cccccc;
    border-radius: 5px;
  }

  &-delete {
    // flex: 1;
    margin-left: 10px;
    font-size: 14px;
    // position: absolute;
    // padding: 0;
    // bottom: 0;
    // left: 0;
    color: red;
  }
}

.upload {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  font-size: 10px;
  color: #999;
  outline: 1px dashed #d9d9d9;
  background: rgb(250, 250, 250);

  &.show {
    background: rgb(250, 250, 250) url(../assets/images/bg.png) center repeat;
  }

  &:hover {
    border-color: #1890ff;
    // cursor: pointer;
  }

  &-loading {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
  }
}
</style>