package com.dz.ms.user.utils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.Map;

public class MJSignUtils {
    // 默认VeryStar接口对外公钥，业务方可以自定义设置
    private static String publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5AmSATdL1gIl9PhtDqP0" +
            "uTUrO5TA7qSyjPwKchKSt5Qg+ho0nKzl56ODbRpqE05y2/QHplWr0jlPggPxEb8B" +
            "pgegKyC81Xv50wcFHmdqKJgu9bAHo7obUB1PK+/slntfPqekB4+gyu6Wc5qMrmux" +
            "Bce/mDcquP30ErIyvZAov28NYCcNlen5OmuSdv6GNDGmkMvAUqwLOxDHGKIVsLEa" +
            "f6IRYfw4NvzzssTXFLHs14rZd1qDIrnZzD2ZMLd99KXzrgQuV6AkPYt/mxZ47N0n" +
            "+cQgbq05VqUJUk0OuCjHuYaaJueH7PtyMD9KKbYKJCeWFvkR1JSZEe3+GkDbZvGV" +
            "+QIDAQAB";
    private static final String headerPrefix = "x-very";
    private static final String HEX_STRING = "0123456789abcdef";
    private static final char[] HEX_CHARS = HEX_STRING.toCharArray();

    /**
     * 统一编码格式 默认使用UTF-8
     */
    private static final Charset DEFAULT_CHARSET = StandardCharsets.UTF_8;

    public enum Type {
        md5("VERY-MD5"),
        sha1("VERY-HMAC-SHA1"),
        sha256("VERY-HMAC-SHA256"),
        rsa2048("VERY-SHA256-RSA2048");

        private final String desc;

        Type(String desc) {
            this.desc = desc;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 获取公钥
     *
     * @return ""
     */
    public static String getPublicKey() {
        return publicKey;
    }

    /***
     * 设置公钥
     * @param key ""
     */
    public static void setPublicKey(String key) {
        publicKey = key;
    }

    /***
     * 计算签名
     * @param method HTTP请求方法，如：POST GET
     * @param address HTTP请求地址：如: /user/query
     * @param headers HTTP请求header
     * @param body HTTP请求数据
     * @param appSecret 计算签名的密钥
     * @param type 计算签名的hash方式
     * @return 返回签名结果
     */
    public static String getSign(String method, String address, Map<String, Object> headers, String body, String appSecret, Type type) {
        StringBuilder builder = new StringBuilder(method + "\n" + address + "\n");
        builder.append(getHeaders(headers));
        builder.append(body);

        String signature;
        try {
            switch (type) {
                case md5:
                    signature = md5(builder + appSecret);
                    break;
                case sha1:
                    signature = hmacSha1(builder.toString(), appSecret);
                    break;
                case sha256:
                    signature = hmacSha256(builder.toString(), appSecret);
                    break;
                case rsa2048:
                    signature = RSA.sign(builder.toString(), appSecret);
                    break;
                default:
                    throw new IllegalStateException("Unexpected value: " + type);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return type.getDesc() + " " + signature;
    }

    /***
     * 验证接口返回中的签名是否正确，返回签名是通过RSA256生成的
     * @param responseData 响应的待签名数据
     * @param signature  响应的签名值
     * @return 返回校验结果
     */
    public static boolean verifySign(String responseData, String signature) {
        return RSA.verifySign(publicKey, responseData, signature);
    }

    /**
     * 解析请求头中自定义签名字段
     *
     * @param headers ""
     * @return 返回自定义header中的字段
     */
    private static String getHeaders(Map<String, Object> headers) {
        ArrayList<String> list = new ArrayList<>();
        // 过滤,key转小写
        for (Map.Entry<String, Object> entry : headers.entrySet()) {
            if (entry.getKey() == null)
                continue;
            String key = entry.getKey().toLowerCase();
            if (!key.startsWith(headerPrefix)) {
                continue;
            }
            if (entry.getValue() == null || entry.getValue() == "") {
                continue;
            }
            list.add(key.toLowerCase() + "=" + entry.getValue());
        }
        // 字典排序
        Collections.sort(list);
        // 拼接
        StringBuilder sb = new StringBuilder();
        for (String s : list) sb.append(s).append("\n");
        return sb.toString();
    }

    // MD5加密
    public static String md5(String str) throws Exception {
        MessageDigest md = MessageDigest.getInstance("MD5");
        return toHexString(md.digest(str.getBytes(DEFAULT_CHARSET)));
    }

    /**
     * hmacSha1
     *
     * @param data 待签名数据
     * @param key  密钥
     * @return 加密结果
     */
    public static String hmacSha1(String data, String key) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA1");
        SecretKeySpec secret_key = new SecretKeySpec(key.getBytes(DEFAULT_CHARSET), "HmacSHA1");
        mac.init(secret_key);
        return toHexString(mac.doFinal(data.getBytes(DEFAULT_CHARSET)));
    }

    /**
     * hmacSha256
     *
     * @param data 待签名数据
     * @param key  密钥
     * @return 加密结果
     */
    public static String hmacSha256(String data, String key) throws Exception {
        Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
        SecretKeySpec secret_key = new SecretKeySpec(key.getBytes(DEFAULT_CHARSET), "HmacSHA256");
        sha256_HMAC.init(secret_key);
        return toHexString(sha256_HMAC.doFinal(data.getBytes(DEFAULT_CHARSET)));
    }

    /**
     * Convert bytes to hex string (all lower-case).
     *
     * @param b Input bytes.
     * @return Hex string.
     */
    public static String toHexString(byte[] b) {
        StringBuilder sb = new StringBuilder(b.length * 2);
        for (byte x : b) {
            int hi = (x & 0xf0) >> 4;
            int lo = x & 0x0f;
            sb.append(HEX_CHARS[hi]);
            sb.append(HEX_CHARS[lo]);
        }
        return sb.toString().trim();
    }

    public static class RSA {
        private static final String KEY_ALGORITHM = "RSA";
        private static final String SIGNATURE_ALGORITHM = "SHA256withRSA";

        // 解码PublicKey
        private static PublicKey getPublicKey(String key) {
            byte[] byteKey = Base64.getDecoder().decode(key);
            X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(byteKey);
            try {
                KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
                return keyFactory.generatePublic(x509EncodedKeySpec);
            } catch (NoSuchAlgorithmException | InvalidKeySpecException e) {
                e.printStackTrace();
            }
            return null;
        }

        // 解码PrivateKey
        private static PrivateKey getPrivateKey(String key) {
            byte[] byteKey = Base64.getDecoder().decode(key);
            PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(byteKey);
            try {
                KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
                return keyFactory.generatePrivate(pkcs8EncodedKeySpec);
            } catch (NoSuchAlgorithmException | InvalidKeySpecException e) {
                e.printStackTrace();
            }
            return null;
        }

        // 签名
        public static String sign(String requestData, String key) {
            String signature = null;
            try {
                PrivateKey privateKey = getPrivateKey(key);

                Signature Sign = Signature.getInstance(SIGNATURE_ALGORITHM);
                Sign.initSign(privateKey);
                Sign.update(requestData.getBytes(DEFAULT_CHARSET));

                signature = Base64.getEncoder().encodeToString(Sign.sign());
            } catch (Exception e) {
                e.printStackTrace();
            }
            return signature;
        }

        // 验签
        public static boolean verifySign(String key, String responseData, String signature) {
            boolean verifySignSuccess = false;
            try {
                PublicKey publicKey = getPublicKey(key);

                Signature verifySign = Signature.getInstance(SIGNATURE_ALGORITHM);
                verifySign.initVerify(publicKey);
                verifySign.update(responseData.getBytes(DEFAULT_CHARSET));

                verifySignSuccess = verifySign.verify(Base64.getDecoder().decode(signature));
            } catch (Exception e) {
                e.printStackTrace();
            }
            return verifySignSuccess;
        }
    }
}
