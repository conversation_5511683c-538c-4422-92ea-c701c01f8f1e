<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.product.mapper.ProductMapper">

	<!-- 查询基础字段 -->
	<sql id="Base_Column_List">
		id,
  	    product_name,
  	    sub_title,
  	    product_code,
  	    pd_type,
  	    be_gift,
  	    item_id,
  	    vender_id,
  	    scence_img_url,
  	    shelf_img_url,
  	    details,
  	    pre_price,
		origin_price,
  	    price,
  	    purchase_type,
  	    cost_point,
  	    cost_price,
		cost_price_on_shelf,
  	    exchange_num,
  	    exchange_desc_type,
  	    exchange_desc_url,
  	    exchange_desc_content,
  	    details_type,
  	    details_url,
  	    details_content,
  	    refer_type,
  	    refer_url,
  	    refer_content,
  	    state,
		is_deleted,
  	    tenant_id,
  	    creator,
  	    created,
  	    modified,
  	    modifier
	</sql>
	<insert id="insertBatchOdsItem" parameterType="java.util.List">
		insert into ods_item (
	    id,
		item_id_str,
		dept_id_str,
		dept_name_str,
		depa_id_str,
		depa_name_str,
		line_id_str,
		line_name_str,
		class_id_str,
		class_name_str,
		item_name_str,
		tax_rate_str,
		cost_price_str,
		spu_id_str,
		spu_name_str,
		seriesid_str,
		biz_date,
		create_time,
		update_time,
		tenant_id
		)
		values
		<foreach collection="odsItems" item="item" separator=",">
			(
			#{item.id},
			#{item.itemIdStr},
			#{item.deptIdStr},
			#{item.deptNameStr},
			#{item.depaIdStr},
			#{item.depaNameStr},
			#{item.lineIdStr},
			#{item.lineNameStr},
			#{item.classIdStr},
			#{item.classNameStr},
			#{item.itemNameStr},
			#{item.taxRateStr},
			#{item.costPriceStr},
			#{item.spuIdStr},
			#{item.spuNameStr},
			#{item.seriesIdStr},
			#{item.bizDate},
			#{item.createTime},
			#{item.updateTime},
			#{item.tenantId}
			)
		</foreach>
	</insert>

    <update id="updateStatic">
		update product
		set exchange_num = exchange_num + #{number}
		where id = #{productId}
	</update>
    <delete id="deleteAllOdsItem">
		delete from ods_item
	</delete>

    <!-- 查询示例 -->
	<select id="selectDemo" parameterType="java.lang.Long" resultType="com.dz.ms.product.entity.Product">
		select
		<include refid="Base_Column_List"/>
		from product
		where id = #{id,jdbcType=BIGINT}
		AND tenant_id = #{tenantId}
	</select>

	<select id="selectPageByParam" resultType="com.dz.ms.product.entity.Product">
		select
		distinct p.*
		from product p
		<where>
			is_deleted = 0
			<if test="param.productName != null and param.productName != ''">
				and p.product_name like CONCAT("%",#{param.productName},"%")
			</if>
			<if test="param.pdType != null">
				and p.pd_type = #{param.pdType}
			</if>
			<if test="param.state != null">
				and p.state = #{param.state}
			</if>
			<if test="param.venderId != null and param.venderId !=''">
				and p.vender_id = #{param.venderId}
			</if>
			<if test="param.shelfIdList != null and !param.shelfIdList.isEmpty">
				and p.id in (select distinct product_id from shelf_product where is_deleted = 0 and shelf_id in
				<foreach collection="param.shelfIdList" item="shelfId" open="(" close=")" separator=",">
					#{shelfId}
				</foreach>)
			</if>
			<if test="param.hiddenProductIdList != null and param.hiddenProductIdList.size > 0">
				and p.id not in
				<foreach collection="param.hiddenProductIdList" index="index" item="id" open="(" close=")"
						 separator=",">
					#{id}
				</foreach>
            </if>
            <if test="param.productIdList != null and param.productIdList.size > 0">
                and p.id in
                <foreach collection="param.productIdList" index="index" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
		ORDER BY id DESC
    </select>

	<select id="selectPageNoDistinctByParam" resultType="com.dz.ms.product.entity.Product">
		select
			p.*
		from product p
		<where>
			is_deleted = 0
			<if test="param.productName != null and param.productName != ''">
				and p.product_name like CONCAT("%",#{param.productName},"%")
			</if>
			<if test="param.pdType != null">
				and p.pd_type = #{param.pdType}
			</if>
			<if test="param.shelfIdList != null and !param.shelfIdList.isEmpty">
				and p.id in (select distinct product_id from shelf_product where is_deleted = 0 and shelf_id in
				<foreach collection="param.shelfIdList" item="shelfId" open="(" close=")" separator=",">
					#{shelfId}
				</foreach>)
			</if>
			<if test="param.hiddenProductIdList != null and param.hiddenProductIdList.size > 0">
				and p.id not in
				<foreach collection="param.hiddenProductIdList" index="index" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
			<if test="param.productIdList != null and param.productIdList.size > 0">
				and p.id in
				<foreach collection="param.productIdList" index="index" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
		</where>
		ORDER BY id DESC
	</select>
	
    <select id="selectListByParam" resultType="com.dz.common.core.dto.export.ProductExportDTO">
		select
		distinct p.id,
		p.product_name,
		p.pd_type,
		p.created,
		p.vender_id,
		p.origin_price,
		p.cost_price,
		p.cost_point,
		p.state,
		p.exchange_num
		from product p
		<where>
			is_deleted = 0
			<if test="param.productName != null and param.productName != ''">
				and p.product_name like CONCAT("%",#{param.productName},"%")
            </if>
            <if test="param.pdType != null">
				and p.pd_type = #{param.pdType}
            </if>
            <if test="param.shelfIdList != null and !param.shelfIdList.isEmpty">
				and p.id in (select distinct product_id from shelf_product where is_deleted = 0 and shelf_id in
                <foreach collection="param.shelfIdList" item="shelfId" open="(" close=")" separator=",">
					#{shelfId}
				</foreach>)
            </if>
		</where>
		ORDER BY id DESC
	</select>

	<select id="selectNoPageByParam" resultType="com.dz.ms.product.entity.Product">
		select
		distinct p.*
		from product p
		<where>
			is_deleted = 0
			<if test="param.productName != null and param.productName != ''">
				and p.product_name like CONCAT("%",#{param.productName},"%")
			</if>
			<if test="param.pdType != null">
				and p.pd_type = #{param.pdType}
			</if>
			<if test="param.shelfIdList != null and !param.shelfIdList.isEmpty">
				and p.id in (select distinct product_id from shelf_product where is_deleted = 0 and shelf_id in
				<foreach collection="param.shelfIdList" item="shelfId" open="(" close=")" separator=",">
					#{shelfId}
				</foreach>)
			</if>
			<if test="param.hiddenProductIdList != null and param.hiddenProductIdList.size > 0">
				and p.id not in
				<foreach collection="param.hiddenProductIdList" index="index" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
			<if test="param.productIdList != null and param.productIdList.size > 0">
				and p.id in
				<foreach collection="param.productIdList" index="index" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
		</where>
		ORDER BY id DESC
	</select>
	
    <select id="selLessListByIds" resultType="com.dz.ms.product.dto.ProductDTO">
		SELECT 
		    p.id,
			p.product_name,
			p.state,
			p.pd_type
		FROM product p
		<where>
			p.is_deleted = 0
			and p.id in
			<foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
				#{item}
			</foreach>
		</where>
	</select>

	<select id="selectByPdType" resultType="com.dz.ms.product.entity.Product">
		SELECT vender_id, cost_price
		FROM `product`
		where pd_type = 1 and is_deleted = 1
	</select>

	<select id="selectMujiGoods" resultType="com.dz.common.core.dto.MujiGoods">
		SELECT p.id,
			   p.id              'goods_id',
			   p.product_name    'goods_name',
			   p.pre_price * 100 'total_fee',
			   CASE
				   p.pd_type
				   WHEN 1 THEN
					   "GOODS"
				   ELSE "COUPON"
				   END           'type',
			   CASE
				   p.state
				   WHEN 0 THEN
					   "1"
				   ELSE "2"
				   END           'status',
			   p.exchange_num    'exchanged_quantity',
			   p.cost_point      'limit_bonus',
			   p.created         'created_at',
			   p.modified        'updated_at'
		FROM product p
		where p.created >= #{beginTime}
		  AND p.created &lt;= #{endTime}
		ORDER BY id ASC
	</select>

	<select id="selectMujiStockInfo" resultType="com.dz.common.core.dto.MujiStockInfo">
		SELECT DISTINCT p.id,
						p.id        'goods_id',
						p.vender_id 'stock_id',
						1           'num',
						p.created   'created_at',
						p.modified  'updated_at'
		FROM product p
		WHERE p.created >= #{beginTime}
		  AND p.created &lt;= #{endTime}
		ORDER BY id ASC
	</select>

	<select id="selectExist" resultType="java.lang.Integer">
		SELECT COUNT(p.id)
		FROM product p
		WHERE p.vender_id = #{venderId}
		AND p.is_deleted = 0
		<if test="id != null">
			AND p.id != #{id}
		</if>
	</select>

	<select id="selectByShelfProductIds" resultType="com.dz.common.core.dto.product.CpStaticDTO">
		SELECT DISTINCT
		sp.id 'shelfProductId',
		p.id 'productId',
		p.vender_id,
		p.origin_price,
		p.pre_price,
		sp.cost_point
		FROM product p
		RIGHT JOIN shelf_product sp ON p.id = sp.product_id
		WHERE sp.id IN
		<foreach collection="shelfProductIdList" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<select id="selectBySftpProductItemId" resultType="com.dz.common.core.dto.product.OdsItemDTO">
		SELECT
		    dept_id_str,
		    depa_id_str,
		    line_id_str,
		    class_id_str
		FROM ods_item
		WHERE item_id_str =#{itemId}
	</select>
</mapper>
