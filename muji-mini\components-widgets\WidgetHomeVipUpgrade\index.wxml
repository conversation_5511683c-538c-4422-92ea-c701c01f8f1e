<view class="WidgetHomeVipUpgrade isCardLevel{{userInfo.cardLevel}}"
  style="--currentScale:{{currentScale}};--lastScale:{{lastScale}};--currentRotate:{{currentRotate}};">
  <view class="_padding-wrap">
    <block wx:if="{{userInfo.id}}">
      <block wx:if="{{userInfo.isMember}}">
        <view class="_vip-progress-wrap">
          <view class="_vip-progress-point-wrap">
            <view class="_vip-progress-point-left"></view>
            <view class="_vip-progress-point-right"></view>
          </view>
          <view class="_vip-progress-line-wrap">
            <view class="_vip-progress-line1-wrap">
              <view class="_vip-progress-line1"></view>
            </view>
            <view class="_vip-progress-line2-wrap">
              <view class="_vip-progress-line2"></view>
            </view>
            <view class="_vip-progress-line3-wrap">
              <view class="_vip-progress-line3">
                <view class="_vip-progress-point-move"></view>
              </view>
            </view>
          </view>
        </view>
        <view class="_vip-type-wrap" wx:if="{{userInfo.cardLevel==='4'}}">
          <view class="_vip-type-left">
            <view class="_vip-type-title">银级会员</view>
            <view class="_vip-type-subtitle">您已高于此等级</view>
          </view>
          <view class="_vip-type-right">
            <view class="_vip-type-title">{{userInfo.cardLevelName}}</view>
            <view class="_vip-type-subtitle">
              <view>当前等级｜已达到最高等级</view>
              <view class="_vip-type-more-icon iconfont icon-View1"></view>
            </view>
          </view>
        </view>
        <view class="_vip-type-wrap" wx:else>
          <view class="_vip-type-left">
            <view class="_vip-type-title">{{userInfo.cardLevelName}}</view>
            <view class="_vip-type-subtitle">当前等级</view>
          </view>
          <view class="_vip-type-right">
            <view class="_vip-type-title">{{userInfo.nextLevelName}}</view>
            <view class="_vip-type-subtitle" catch:tap="handleGoLegend">
              <view>距{{userInfo.nextLevelName}}还需{{userInfo.nextMileage}}里程</view>
              <view class="_vip-type-more-icon iconfont icon-View1" style="color:#3C3C43;"></view>
            </view>
          </view>
        </view>
        <view class="_vip-name-wrap">
          <view class="_vip-name-left">Hi，{{userInfo.username}}</view>
          <view class="_vip-name-right">
            <view class="_vip-score" bindtap="goSubscribe" data-scene="coupon">
              <view class="_vip-score-title">{{couponNumInfo}}</view>
              <view class="_vip-score-subtitle">礼券</view>
              <view class="_vip-score-red" wx:if="{{isRed==1}}"></view>
            </view>
            <view class="_vip-score-gap-line"></view>
            <view class="_vip-score" bindtap="goSubscribe" data-scene="point">
              <view class="_vip-score-title">{{userInfo.points}}</view>
              <view class="_vip-score-subtitle">积分</view>
            </view>
          </view>
        </view>
      </block>
      <view class="_vip-no-login-wrap" style="margin-top:0;margin-bottom:40rpx;" wx:else>
        <view class="_vip-no-login-left">请登录/注册</view>
        <view class="_vip-no-login-right" bindtap="goRegister">登录/注册</view>
      </view>
    </block>
    <view class="_vip-more-info-wrap" bindtap="goStore">
      <view class="_triangle"></view>
      <view class="_vip-more-info-left">
        <text class="_vip-more-info-left-icon iconfont icon-a-MUJIHelper"></text>
        <text class="_vip-more-info-text">添加MUJI助手，获取更多门店信息</text>
      </view>
      <text class="_vip-more-info-right-icon iconfont  icon-View1" style="color:#3C3C43"></text>
    </view>
  </view>
  <view class="_vip-benefit-wrap">
    <image src="{{$cdn}}/home/<USER>" class="_vip-benefit-bottom-bg" mode="aspectFill"></image>
    <!-- 展示福利内容 -->
    <block wx:if="{{!!giftData.id}}">
      <!-- 点击出现弹窗 -->
      <image src="{{giftData.headImg}}" class="_vip-benefit-img" wx:if="{{giftData.ballImg}}" bindtap="showJumpModal">
      </image>
      <!-- 跳转 -->
      <custom-link data="{{giftData.jumpUrl[0]}}" class="linkStyle" wx:else>
        <image src="{{giftData.headImg}}" class="_vip-benefit-img"></image>
      </custom-link>
    </block>
  </view>
  <view class="_placeholder"></view>
  <view class="WidgetHomeVipUpgrade-visitor" wx:if="{{visitor}}"  catchtap="showPrincy"></view>

</view>
<jumpModal show="{{showModal}}" jumpData="{{giftData.jumpData}}" bindclose="closeModal"></jumpModal>