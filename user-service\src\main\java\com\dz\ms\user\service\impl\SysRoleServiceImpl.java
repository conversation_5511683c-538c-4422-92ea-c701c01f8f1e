package com.dz.ms.user.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.constant.UserConstants;
import com.dz.common.core.constants.CommonConstants;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.dto.user.SysUserDTO;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.user.constants.CacheKeys;
import com.dz.ms.user.dto.RolePermissionDTO;
import com.dz.common.core.dto.user.SysPermissionDTO;
import com.dz.ms.user.dto.SysUserRoleDTO;
import com.dz.ms.user.entity.SysPermission;
import com.dz.ms.user.entity.SysRole;
import com.dz.ms.user.entity.SysRolePermission;
import com.dz.ms.user.mapper.SysPermissionMapper;
import com.dz.ms.user.mapper.SysPermissionUrlMapper;
import com.dz.ms.user.mapper.SysRoleMapper;
import com.dz.ms.user.mapper.SysRolePermissionMapper;
import com.dz.ms.user.service.SysPermissionService;
import com.dz.ms.user.service.SysRoleService;
import com.dz.ms.user.vo.MetaVo;
import com.dz.ms.user.vo.RouterVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 系统角色
 * @author: Handy
 * @date:   2022/2/4 23:04
 */
@Service
@Slf4j
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper,SysRole> implements SysRoleService {

    @Resource
    private SysPermissionService sysPermissionService;

    @Resource
    private SysRoleMapper sysRoleMapper;

    @Resource
    private SysPermissionMapper sysPermissionMapper;

    @Resource
    private SysPermissionUrlMapper sysPermissionUrlMapper;

    @Resource
    private SysRolePermissionMapper sysRolePermissionMapper;

    @Resource
    private RedisService redisService;

    /**
     * 获取角色权限列表
     * @param roleId
     * @return
     */
    @Override
    public List<String> getRolePermitCodes(Long roleId,Long tenantId,Integer platform) {
        if(null == roleId) {
            return null;
        }
        if(SecurityContext.getUser().getPlatform() != null){
            platform = SecurityContext.getUser().getPlatform();
        }
        if(roleId.equals(0L)) {
            return sysPermissionService.getAllPermissionCodes(platform);
        }
        String key = CacheKeys.SYS_ROLE_PERMIT_CODES + tenantId + ":" + roleId + ":" + platform;
        Object cachList = redisService.get(key);
        if(null != cachList) {
            return (List<String>) cachList;
        }
        List<SysPermission> rolePermits = sysPermissionMapper.getRolePermitCodes(roleId,null,platform);
        Set<String> permitCodes = new HashSet<>();
        LambdaQueryWrapper<SysPermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysPermission :: getHasChild,1);
        wrapper.eq(SysPermission :: getPlatform, platform);
        List<SysPermission> parentPermits = sysPermissionMapper.selectList(wrapper);
        Map<Long,SysPermission> parentMap = parentPermits.stream().collect(Collectors.toMap(SysPermission :: getId, sysPermission -> sysPermission));
        rolePermits.forEach(sysPermission -> {
            permitCodes.add(sysPermission.getCode());
            handelPrentPermitCode(permitCodes,parentMap,sysPermission);
        });
        List<String> list = new ArrayList<>(permitCodes);
        redisService.set(key,list,CommonConstants.DAY_SECONDS);
        return list;
    }

    /**
     * 获取角色功能权限码列表
     * @param roleId
     * @return
     */
    @Override
    public List<String> getRoleFunctionCodes(Long roleId,Long tenantId,Integer platform) {
        if(null == roleId) {
            return null;
        }
        if(SecurityContext.getUser().getPlatform() != null){
            platform = SecurityContext.getUser().getPlatform();
        }
        if(roleId.equals(0L)) {
            return sysPermissionService.getFunctionPermissionCodes(platform);
        }
        String key = CacheKeys.SYS_ROLE_FUNCTIONS + tenantId + ":" + roleId + ":" + platform;
        Object cachList = redisService.get(key);
        if(null != cachList) {
            return (List<String>) cachList;
        }
        List<SysPermission> rolePermits = sysPermissionMapper.getRolePermitCodes(roleId,3,platform);
        List<String> list = rolePermits.stream().map(SysPermission::getCode).collect(Collectors.toList());
        redisService.set(key,list,CommonConstants.DAY_SECONDS);
        return list;
    }

    /**
     * 递归添加父级权限编码
     * @param permitCodes
     * @param parentMap
     * @param sysPermission
     */
    private void handelPrentPermitCode(Set<String> permitCodes,Map<Long,SysPermission> parentMap,SysPermission sysPermission) {
        if(sysPermission.getParentId() > 0 && parentMap.containsKey(sysPermission.getParentId())) {
            SysPermission parentPermission = parentMap.get(sysPermission.getParentId());
            permitCodes.add(parentPermission.getCode());
            if(!parentPermission.getId().equals(sysPermission.getId())){
                handelPrentPermitCode(permitCodes,parentMap,parentPermission);
            }
        }
    }

    /**
     * 递归添加父级权限ID
     * @param permitIds
     * @param parentMap
     * @param sysPermission
     */
    private void handelPrentPermitIds(Set<Long> permitIds,Map<Long,SysPermission> parentMap,SysPermission sysPermission) {
        if(sysPermission.getParentId() > 0 && parentMap.containsKey(sysPermission.getParentId())) {
            SysPermission parentPermission = parentMap.get(sysPermission.getParentId());
            permitIds.add(parentPermission.getId());
            if(!parentPermission.getId().equals(sysPermission.getId())){
                handelPrentPermitIds(permitIds,parentMap,parentPermission);
            }
        }
    }

    /**
     * 获取角色菜单树形结构
     * @return
     */
    @Override
    public List<SysPermissionDTO> getRoleMenuTree(Long roleId, Long tenantId,Integer platform) {
        if(null == roleId) {
            return null;
        }
        if(SecurityContext.getUser().getPlatform() != null){
            platform = SecurityContext.getUser().getPlatform();
        }
        String key = CacheKeys.SYS_ROLE_MENU_TREE + tenantId + ":" + roleId + ":" + platform;
        Object cachList = redisService.get(key);
        if(null != cachList) {
            return (List<SysPermissionDTO>) cachList;
        }
        List<SysPermission> rolePermits = null;
        if(roleId.equals(0L)) {
            rolePermits = sysPermissionMapper.selectList(new LambdaQueryWrapper<SysPermission>().eq(SysPermission::getPlatform,platform));
        }
        else {
            rolePermits = sysPermissionMapper.getRolePermitCodes(roleId,null,platform);
        }
        Set<Long> permitIds = new HashSet<>();
        LambdaQueryWrapper<SysPermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysPermission :: getHasChild,1);
        wrapper.eq(SysPermission :: getPlatform, platform);
        List<SysPermission> parentPermits = sysPermissionMapper.selectList(wrapper);
        Map<Long,SysPermission> parentMap = parentPermits.stream().collect(Collectors.toMap(SysPermission :: getId, sysPermission -> sysPermission));
        rolePermits.forEach(sysPermission -> {
            permitIds.add(sysPermission.getId());
            handelPrentPermitIds(permitIds,parentMap,sysPermission);
        });
        List<SysPermissionDTO> list = sysPermissionService.getSysMenuTree(permitIds,platform);
        redisService.set(key,list,CommonConstants.DAY_SECONDS);
        return list;
    }

    /**
     * 获取角色权限接口列表
     * @param roleId
     * @return
     */
    @Override
    public List<String> getRolePermitUrls(Long roleId,Integer platform) {
        Long tenantId = SecurityContext.getUser().getTenantId();
        String key = CacheKeys.SYS_ROLE_PERMIT_URLS + tenantId + ":" + roleId + ":" + platform;
        Object cachList = redisService.get(key);
        //log.info("getRolePermitUrls key:{},cachList:{}",key,JSON.toJSONString(cachList));
        if(null != cachList) {
            return (List<String>) cachList;
        }
        List<SysPermission> rolePermits = sysPermissionMapper.getRolePermitCodes(roleId,null,platform);
        Set<Long> permitIds = new HashSet<>();
        LambdaQueryWrapper<SysPermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysPermission :: getHasChild,1);
        wrapper.eq(SysPermission :: getPlatform, platform);
        List<SysPermission> parentPermits = sysPermissionMapper.selectList(wrapper);
        Map<Long,SysPermission> parentMap = parentPermits.stream().collect(Collectors.toMap(SysPermission :: getId, sysPermission -> sysPermission));
        rolePermits.forEach(sysPermission -> {
            permitIds.add(sysPermission.getId());
            handelPrentPermitIds(permitIds,parentMap,sysPermission);
        });
        List<String> list = sysPermissionUrlMapper.getPermitUrlsByPermitIds(permitIds);
        redisService.set(key,list,CommonConstants.DAY_SECONDS);
        return list;
    }

    /**
     * 获取角色权限ID列表
     * @param roleId
     * @return
     */
    @Override
    public List<Long> getRolePermitIds(Long roleId) {
        return sysPermissionMapper.getRolePermitIds(roleId,SecurityContext.getUser().getPlatform());
    }

    /**
     * 绑定角色权限
     * @param param
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindPermit(RolePermissionDTO param) {
        List<Long> origIds = new ArrayList<>();
        List<Long> addIds = new ArrayList<>();
        List<Long> deleteIds = new ArrayList<>();
        List<Long> newIds = param.getPermitIds();
        LambdaQueryWrapper<SysRolePermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRolePermission :: getRoleId,param.getRoleId());
        List<SysRolePermission> getList = sysRolePermissionMapper.selectList(wrapper);
        if(null != getList) {
            for (SysRolePermission rolePermission : getList) {
                origIds.add(rolePermission.getPermitId());
            }
        }
        if(CollectionUtils.isEmpty(newIds)) {
            deleteIds = origIds;
        }
        else {
            for (Long id : newIds) {
                if(!origIds.contains(id)) {
                    addIds.add(id);
                }
            }
            for (SysRolePermission rolePermission : getList) {
                if(!newIds.contains(rolePermission.getPermitId())) {
                    deleteIds.add(rolePermission.getId());
                }
            }
        }
        for (Long id : addIds) {
            SysRolePermission rolePermission = new SysRolePermission();
            rolePermission.setRoleId(param.getRoleId());
            rolePermission.setPermitId(id);
            sysRolePermissionMapper.insert(rolePermission);
        }
        for (Long id : deleteIds) {
            sysRolePermissionMapper.deleteById(id);
        }
        Long tenantId = SecurityContext.getUser().getTenantId();
        Integer platform = SecurityContext.getUser().getPlatform();
        Long roleId = param.getRoleId();
        redisService.del(CacheKeys.SYS_ROLE_PERMIT_CODES + tenantId + ":" + roleId + ":" + platform, CacheKeys.SYS_ROLE_PERMIT_URLS + tenantId + ":" + roleId + ":" + platform);
        redisService.del(CacheKeys.SYS_ROLE_FUNCTIONS + tenantId + ":" + roleId + ":" + platform, CacheKeys.SYS_ROLE_MENU_TREE + tenantId + ":" + roleId + ":" + platform);
        redisService.del(CacheKeys.SYS_ALL_PERMIT_CODES + ":" + platform, CacheKeys.SYS_ALL_FUNCTION_CODES + ":" + platform);
    }

    /**
     * 根据系统用户ID列表获取绑定角色列表
     * @param ids
     * @return
     */
    @Override
    public List<SysUserRoleDTO> getSysRoleByUserIds(List<Long> ids) {
        return sysRoleMapper.getSysRoleByUserIds(ids);
    }

    /**
     * 验证接口权限
     * @param roleId
     * @param url
     * @param tenantId
     */
    @Override
    public boolean checkPermission(Long roleId, String url, Long tenantId,Integer platform) {
        if(roleId.equals(0L)) {
            return true;
        }
        if(null == SecurityContext.getUser() || ParamUtils.isNullOr0Long(SecurityContext.getUser().getTenantId())) {
            SecurityContext.setUser(new CurrentUserDTO(tenantId));
        }
        List<String> list = getRolePermitUrls(roleId, platform);
        if(list.contains(url)) {
            return true;
        }
        log.info("checkPermission url:{},permitUrls:{}",url, JSON.toJSONString(list));
        return false;
    }

    @Override
    public List<RouterVo> selectMenuTree(SysUserDTO sysUser) {
        CurrentUserDTO currentUser = SecurityContext.getUser();
        Long roleId = 0L;
        if(ParamUtils.Integer2int(sysUser.getIsAdmin()) != 1) {
            roleId = sysUser.getRoleId();
        }
        List<SysPermissionDTO> menuList = this.getRoleMenuTree(roleId,currentUser.getTenantId(),currentUser.getPlatform());
        if(CollectionUtils.isEmpty(menuList)){
            menuList = new ArrayList<>();
        }
        return buildMenus(menuList);
    }

    /**
     * 构建前端路由所需要的菜单
     *
     * @param menus 菜单列表
     * @return 路由列表
     */
    public List<RouterVo> buildMenus(List<SysPermissionDTO> menus) {
        List<RouterVo> routers = new LinkedList<>();
        for (SysPermissionDTO menu : menus) {
            if(StringUtils.isBlank(menu.getMenuType()) || !"M,C".contains(menu.getMenuType())){
                continue;
            }
            RouterVo router = new RouterVo();
            router.setHidden("1".equals(menu.getVisible()));
            router.setName(getRouteName(menu));
            router.setPath(getRouterPath(menu));
            router.setComponent(getComponent(menu));
            router.setMeta(new MetaVo(menu.getPermitName(), menu.getIcon()));
            router.setOrderNum(menu.getDisplaySort());
            router.setId(menu.getId());
            router.setParentId(menu.getParentId());
            List<SysPermissionDTO> cMenus = menu.getSubList();
            if (!CollectionUtils.isEmpty(cMenus) && UserConstants.TYPE_DIR.equals(menu.getMenuType())) {
                router.setAlwaysShow(true);
                router.setRedirect("noRedirect");
                router.setChildren(buildMenus(cMenus));
            } else if (isMeunFrame(menu)) {
                List<RouterVo> childrenList = new ArrayList<>();
                RouterVo children = new RouterVo();
                children.setPath(menu.getPath());
                children.setComponent(menu.getComponent());
                children.setName(StringUtils.capitalize(menu.getPath()));
                children.setMeta(new MetaVo(menu.getPermitName(), menu.getIcon()));
                children.setOrderNum(menu.getDisplaySort());
                childrenList.add(children);
                router.setChildren(this.sortByOrderNum(
                        childrenList
                ));
            }
            routers.add(router);
        }
        return this.sortByOrderNum(routers);
    }

    private List<RouterVo> sortByOrderNum(List<RouterVo> routerVoList) {
        return routerVoList.stream()
                .sorted(Comparator.comparingInt(RouterVo::getOrderNum))
                .collect(Collectors.toList());
    }


    /**
     * 获取路由名称
     *
     * @param menu 菜单信息
     * @return 路由名称
     */
    public String getRouteName(SysPermissionDTO menu) {
        String routerName = StringUtils.capitalize(menu.getPath());
        // 非外链并且是一级目录（类型为目录）
        if (isMeunFrame(menu)) {
            routerName = StringUtils.EMPTY;
        }
        return routerName;
    }

    /**
     * 获取路由地址
     *
     * @param menu 菜单信息
     * @return 路由地址
     */
    public String getRouterPath(SysPermissionDTO menu) {
        String routerPath = menu.getPath();
        // 非外链并且是一级目录（类型为目录）
        if (0 == menu.getParentId().intValue() && UserConstants.TYPE_DIR.equals(
                menu.getMenuType()) && UserConstants.NO_FRAME.equals(menu.getIsFrame().toString())) {
            routerPath = "/" + menu.getPath();
        }
        // 非外链并且是一级目录（类型为菜单）
        else if (isMeunFrame(menu)) {
            routerPath = "/";
        }
        return routerPath;
    }

    /**
     * 获取组件信息
     *
     * @param menu 菜单信息
     * @return 组件信息
     */
    public String getComponent(SysPermissionDTO menu) {
        String component = UserConstants.LAYOUT;
        if (StringUtils.isNotEmpty(menu.getComponent()) && !isMeunFrame(menu)) {
            component = menu.getComponent();
        }
        return component;
    }

    /**
     * 是否为菜单内部跳转
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isMeunFrame(SysPermissionDTO menu) {
        return menu.getParentId().intValue() == 0 && UserConstants.TYPE_MENU.equals(
                menu.getMenuType()) && UserConstants.NO_FRAME.equals(menu.getIsFrame().toString());
    }

}
