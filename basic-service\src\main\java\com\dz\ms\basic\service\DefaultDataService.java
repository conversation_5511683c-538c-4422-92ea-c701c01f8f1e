package com.dz.ms.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.ms.basic.dto.DefaultDataDTO;
import com.dz.ms.basic.entity.DefaultData;

/**
 * 默认数据接口
 * @author: Handy
 * @date:   2023/12/19 17:05
 */
public interface DefaultDataService extends IService<DefaultData> {

	/**
     * 分页查询默认数据
     * @param param
     * @return PageInfo<DefaultDataDTO>
     */
    public PageInfo<DefaultDataDTO> getDefaultDataList(DefaultDataDTO param);

    /**
     * 根据ID查询默认数据
     * @param id
     * @return DefaultDataDTO
     */
    public DefaultDataDTO getDefaultDataById(Long id);

    /**
     * 根据类型获取默认数据
     * @param type
     * @return
     */
    public DefaultDataDTO getDefaultDataByType(Integer type);

    /**
     * 保存默认数据
     * @param param
     * @return Long
     */
    public Long saveDefaultData(DefaultDataDTO param);

    /**
     * 根据ID删除默认数据
     * @param param
     */
    public void deleteDefaultDataById(IdCodeDTO param);

}
