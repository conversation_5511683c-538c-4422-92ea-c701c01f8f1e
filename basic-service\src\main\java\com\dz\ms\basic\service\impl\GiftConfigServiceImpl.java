package com.dz.ms.basic.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.vo.Result;
import com.dz.common.core.constants.CommonConstants;
import com.dz.common.core.dto.basic.GiftConfigDTO;
import com.dz.common.core.dto.basic.GiftConfigOtherDTO;
import com.dz.common.core.dto.product.ProductCouponDTO;
import com.dz.common.core.dto.user.MemberInfoDTO;
import com.dz.common.core.fegin.order.ExchangeOrderFeignClient;
import com.dz.common.core.fegin.product.ProductFeignClient;
import com.dz.common.core.fegin.user.MUJIOpenApiFeignClient;
import com.dz.common.core.fegin.user.UserInfoFeginClient;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ParamUtils;
import com.dz.ms.basic.entity.GiftConfig;
import com.dz.ms.basic.entity.GiftConfigOther;
import com.dz.ms.basic.mapper.GiftConfigMapper;
import com.dz.ms.basic.mapper.GiftConfigOtherMapper;
import com.dz.ms.basic.service.GiftConfigService;
import com.dz.ms.basic.vo.GiftConfigOtherVo;
import com.dz.ms.basic.vo.GiftConfigVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 企业微信配置
 * @author: Handy
 * @date:   2022/07/20 21:18
 */
@Slf4j
@Service
public class GiftConfigServiceImpl extends ServiceImpl<GiftConfigMapper, GiftConfig> implements GiftConfigService {

    @Autowired
    private GiftConfigMapper giftConfigMapper;
    @Autowired
    private MUJIOpenApiFeignClient mUJIOpenApiFeignClient;
    @Autowired
    private UserInfoFeginClient userInfoFeginClient;
    @Resource
    private ExchangeOrderFeignClient exchangeOrderFeignClient;
    @Autowired
    private GiftConfigOtherMapper giftConfigOtherMapper;
    @Autowired
    private ProductFeignClient productFeignClient;

    @Override
    public GiftConfigDTO getGiftConfigList(String type) {
        LambdaQueryWrapper<GiftConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByAsc(GiftConfig::getGiftType);
        List<GiftConfig> listDTO = giftConfigMapper.selectList(wrapper);
        List<GiftConfigDTO> list = BeanCopierUtils.convertList(listDTO, GiftConfigDTO.class);
        Map<Integer, GiftConfigDTO> giftMap = list.stream().collect(Collectors.toMap(GiftConfigDTO::getGiftType, Function.identity()));
        //查询我的会员信息
        Result<MemberInfoDTO> userSimpleInfo = userInfoFeginClient.getCurrentUserMemberInfo();
        if (null == userSimpleInfo || null == userSimpleInfo.getData() || userSimpleInfo.getData().getIsMember()==0) {
            //判断是否还有可领券礼遇，如果没有则判断是有有可使用的券
            // 礼遇类型：1新人礼配置
            return giftMap.get(1);
        }
        //查询我的券列表
        Result<JSONObject> jsonObjects =  mUJIOpenApiFeignClient.activityCouponList(userSimpleInfo.getData().getCardNo());
        GiftConfigDTO gift = null;
        if (null != jsonObjects && null != jsonObjects.getData()){
            JSONArray activityList=jsonObjects.getData().getJSONArray("items");
            if (!CollectionUtils.isEmpty(activityList)){
                //依次判断list中配置的券是否包含在我的券列表中，如果包含则直接返回待领取的这个券
                configFirst:for (GiftConfigDTO config : list) {
                    if (StringUtils.isEmpty(type)){
                        // 礼遇类型：1新人礼配置，2二回礼配置，3生日礼配置，4升级礼配置，5无以上礼遇可领取且券列表有有效券时，6无礼遇、无可用券时，7无礼遇、有可使用的商品券时
                        if (config.getGiftType()==1){
                            continue;
                        }
                    }
                    if (config.getGiftType()==4){
                        List<GiftConfigOther> giftConfigOthers = giftConfigOtherMapper.selectList(new LambdaQueryWrapper<GiftConfigOther>()
                                .eq(GiftConfigOther::getGiftType, config.getGiftType()));
                        if (!CollectionUtils.isEmpty(giftConfigOthers)){
                            for (GiftConfigOther giftConfigOther : giftConfigOthers) {
                                if (null != giftConfigOther.getActivityId()){
                                    String[] couponIds = giftConfigOther.getActivityId().split(",");
                                    List<String> couponIdList = new ArrayList<>(Arrays.asList(couponIds));
                                    for (Object jsonObject:activityList) {
                                        //将jsonObject转为map
                                        JSONObject activityJsonObject = JSONObject.parseObject(JSONObject.toJSONString(jsonObject));
                                        if (couponIdList.contains(activityJsonObject.getString("activity_id"))){
                                            gift = config;
                                            gift.setActivityId(giftConfigOther.getActivityId());
                                            gift.setReceiveType(1);
                                            gift.setGiftConfigOtherDTO(BeanCopierUtils.convertList(giftConfigOthers, GiftConfigOtherDTO.class));
                                            break configFirst;
                                        }
                                    }
                                }
                            }
                        }
                    }else{
                        if (null != config.getCouponId()){
                            String[] couponIds = config.getCouponId().split(",");
                            List<String> couponIdList = new ArrayList<>(Arrays.asList(couponIds));
                            for (Object jsonObject:activityList) {
                                //将jsonObject转为map
                                JSONObject activityJsonObject = JSONObject.parseObject(JSONObject.toJSONString(jsonObject));
                                if (couponIdList.contains(activityJsonObject.getString("activity_id"))) {
                                    gift = config;
                                    gift.setActivityId(config.getCouponId());
                                    gift.setReceiveType(1);
                                    break configFirst;
                                }
                            }
                        }
                    }
                }
                if (gift == null) {// 无以上礼遇
                    boolean havePdCoupon = false;// 是否有商品券
                    for (Object jsonObject : activityList) {
                        //将jsonObject转为map
                        JSONObject activityJsonObject = JSONObject.parseObject(JSONObject.toJSONString(jsonObject));
                        String subName = activityJsonObject.getString("sub_name");
                        if (StringUtils.hasText(subName) && subName.contains(CommonConstants.PD_0_1)) {
                            havePdCoupon = true;
                            break;
                        }
                    }
                    if (havePdCoupon) {
                        gift = giftMap.get(7);// 7无礼遇、有可使用的商品券时
                    } else {
                        gift = getGiftConfigDTO(true, giftMap, userSimpleInfo);
                    }
                }
            } else {
                gift = getGiftConfigDTO(false, giftMap, userSimpleInfo);
            }
        } else {
            gift = getGiftConfigDTO(false, giftMap, userSimpleInfo);
        }


        return gift;
    }

    private GiftConfigDTO getGiftConfigDTO(boolean haveActivity, Map<Integer, GiftConfigDTO> giftMap, Result<MemberInfoDTO> userSimpleInfo) {
        GiftConfigDTO gift;
        Result<JSONObject> jsonCouponObjects = mUJIOpenApiFeignClient.memberCouponList(userSimpleInfo.getData().getCardNo(), null, 1, 1, 100);
        if (null != jsonCouponObjects && null != jsonCouponObjects.getData()) {
            JSONArray couponMyList = jsonCouponObjects.getData().getJSONArray("items");
            if (!CollectionUtils.isEmpty(couponMyList)) {
                boolean havePdCoupon = false;// 是否有商品券
                List<String> venderIdList = new ArrayList<>();
                //查询自建商品券
                List<ProductCouponDTO> resultCouponList = productFeignClient.getProductListByPdType().getData();
                if (!CollectionUtils.isEmpty(resultCouponList)) {
                    for (ProductCouponDTO productCouponDTO : resultCouponList) {
                        venderIdList.add(productCouponDTO.getVenderId());
                    }
                }
                for (Object couponObject : couponMyList) {
                    JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(couponObject));
                    if (!CollectionUtils.isEmpty(venderIdList)) {
                        if (venderIdList.contains(jsonObject.getString("stock_id"))) {
                            havePdCoupon = true;
                            break;
                        }
                    }
                    String subName = jsonObject.getString("sub_name");
                    if (StringUtils.hasText(subName) && subName.contains(CommonConstants.PD_0_1)) {
                        havePdCoupon = true;
                        break;
                    }
                }
                if (havePdCoupon) {
                    gift = giftMap.get(7);// 7无礼遇、有可使用的商品券时
                } else {
                    gift = giftMap.get(5);// 5无以上礼遇可领取且券列表有有效券时
                }
            } else {
                if (haveActivity) {
                    gift = giftMap.get(5);// 5无以上礼遇可领取且券列表有有效券时
                } else {
                    gift = giftMap.get(6);// 6无礼遇、无可用券时
                }
            }
        } else {
            if (haveActivity) {
                gift = giftMap.get(5);// 5无以上礼遇可领取且券列表有有效券时
            } else {
                gift = giftMap.get(6);// 6无礼遇、无可用券时
            }
        }
        return gift;
    }

    @Override
    public GiftConfigDTO getGiftConfigInfo(Long id) {
        GiftConfig giftConfig = giftConfigMapper.selectById(id);
        GiftConfigDTO giftConfigDTO = BeanCopierUtils.convertObject(giftConfig, GiftConfigDTO.class);
        List<GiftConfigOther> giftConfigOthers = giftConfigOtherMapper.selectList(new LambdaQueryWrapper<GiftConfigOther>()
                .eq(GiftConfigOther::getGiftType, giftConfigDTO.getGiftType()));
        if (!CollectionUtils.isEmpty(giftConfigOthers)){
            List<GiftConfigOtherDTO> giftConfigOtherDTOS = BeanCopierUtils.convertList(giftConfigOthers, GiftConfigOtherDTO.class);
            giftConfigDTO.setGiftConfigOtherDTO(giftConfigOtherDTOS);
        }
        return giftConfigDTO;
    }

    @Override
    public Boolean getGiftLeveShow(Integer type) {
        boolean isLeveShow = false;
        //查询我的会员信息
        Result<MemberInfoDTO> userSimpleInfo = userInfoFeginClient.getCurrentUserMemberInfo();
        if (userSimpleInfo.getData().getIsMember()!=1) {
            return isLeveShow;
        }
        //查询我的券列表
        Result<JSONObject> jsonObjects =  mUJIOpenApiFeignClient.activityCouponList(userSimpleInfo.getData().getCardNo());
        if (null == jsonObjects || null == jsonObjects.getData()){
            return isLeveShow;
        }
        JSONArray activityList=jsonObjects.getData().getJSONArray("items");
        if (CollectionUtils.isEmpty(activityList)) {
            return isLeveShow;
        }
        if (type==1){
            LambdaQueryWrapper<GiftConfig> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(GiftConfig::getGiftType,3);
            GiftConfig giftConfig = giftConfigMapper.selectOne(wrapper);
            for (Object jsonObject:activityList) {
                //将jsonObject转为map
                JSONObject activityJsonObject = JSONObject.parseObject(JSONObject.toJSONString(jsonObject));
                String[] couponIds = giftConfig.getCouponId().split(",");
                List<String> couponIdList = new ArrayList<>(Arrays.asList(couponIds));
                if (couponIdList.contains(activityJsonObject.getString("activity_id"))){
                    isLeveShow = true;
                    break;
                }
            }
        }else{
            //2铜级会员,3银级会员,4金级会员
            LambdaQueryWrapper<GiftConfigOther> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(GiftConfigOther::getLevelId,type);
            GiftConfigOther giftConfigOther = giftConfigOtherMapper.selectOne(wrapper);
            for (Object jsonObject:activityList) {
                //将jsonObject转为map
                JSONObject activityJsonObject = JSONObject.parseObject(JSONObject.toJSONString(jsonObject));
                String[] couponIds = giftConfigOther.getActivityId().split(",");
                List<String> couponIdList = new ArrayList<>(Arrays.asList(couponIds));
                if (couponIdList.contains(activityJsonObject.getString("activity_id"))){
                    isLeveShow = true;
                    break;
                }
            }
        }
        return isLeveShow;
    }

    @Override
    public void updateGiftConfig(GiftConfigVo param) {
        GiftConfig giftConfig = BeanCopierUtils.convertObject(param, GiftConfig.class);
        if (ParamUtils.isNullOr0Long(giftConfig.getId())) {
            giftConfig.setCreateAt(SecurityContext.getUser().getUid().toString());
            giftConfig.setCreateTime(new Date());
            giftConfigMapper.insert(giftConfig);
            if (!CollectionUtils.isEmpty(param.getGiftConfigOtherVoList())) {
                for (GiftConfigOtherVo giftConfigOtherVo : param.getGiftConfigOtherVoList()) {
                    GiftConfigOther giftConfigOther = BeanCopierUtils.convertObject(giftConfigOtherVo, GiftConfigOther.class);
                    giftConfigOther.setGiftType(giftConfig.getGiftType());
                    giftConfigOther.setCreateAt(SecurityContext.getUser().getUid().toString());
                    giftConfigOther.setCreateTime(new Date());
                    giftConfigOtherMapper.updateById(giftConfigOther);
                }
            }
        } else {
            giftConfig.setUpdateAt(SecurityContext.getUser().getUid().toString());
            giftConfig.setUpdateTime(new Date());
            giftConfigMapper.updateById(giftConfig);
            if (!CollectionUtils.isEmpty(param.getGiftConfigOtherVoList())) {
                for (GiftConfigOtherVo giftConfigOtherVo : param.getGiftConfigOtherVoList()) {
                    GiftConfigOther giftConfigOther = BeanCopierUtils.convertObject(giftConfigOtherVo, GiftConfigOther.class);
                    giftConfigOther.setUpdateAt(SecurityContext.getUser().getUid().toString());
                    giftConfigOther.setUpdateTime(new Date());
                    giftConfigOtherMapper.updateById(giftConfigOther);
                }
            }
        }
    }

    @Override
    public List<GiftConfigDTO> getGiftList() {
        //按giftType正序查询
        List<GiftConfig> list = giftConfigMapper.selectList(new LambdaQueryWrapper<GiftConfig>().orderByAsc(GiftConfig::getGiftType));
        List<GiftConfigDTO> listDTO = BeanCopierUtils.convertList(list, GiftConfigDTO.class);
        for (GiftConfigDTO giftConfigDTO : listDTO) {
            List<GiftConfigOther> giftConfigOthers = giftConfigOtherMapper.selectList(new LambdaQueryWrapper<GiftConfigOther>()
                    .eq(GiftConfigOther::getGiftType, giftConfigDTO.getGiftType()));
            if (!CollectionUtils.isEmpty(giftConfigOthers)){
                List<GiftConfigOtherDTO> giftConfigOtherDTOS = BeanCopierUtils.convertList(giftConfigOthers, GiftConfigOtherDTO.class);
                giftConfigDTO.setGiftConfigOtherDTO(giftConfigOtherDTOS);
            }
        }
        return listDTO;
    }
}