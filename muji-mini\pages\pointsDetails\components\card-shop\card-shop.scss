.card {


  &-shop {
    margin-top: 30rpx;
    background-color: #fff;
    padding: 60rpx 30rpx 20rpx 30rpx;

    &-line {
      width: 100%;
      height: 0rpx;
      border-radius: 0rpx 0rpx 0rpx 0rpx;
      border-top: 1rpx solid #EEEEEE;
      margin-bottom: 40rpx;
    }

    &-item {}

    &-li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30rpx;
    }

    &-name {
      flex: 1;
      font-family: <PERSON> <PERSON>, Source <PERSON>;
      font-weight: 400;
      font-size: 24rpx;
      color: #888888;
      line-height: 35rpx;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }

    &-num {
      flex: 1;
      font-family: Source <PERSON>, Source <PERSON>;
      font-weight: 400;
      font-size: 24rpx;
      color: #3C3C43;
      line-height: 35rpx;
      text-align: right;
      font-style: normal;
      text-transform: none;
    }
  }
}
