package com.dz.common.core.fegin.basic;

import com.dz.common.base.constant.ServiceConstant;
import com.dz.common.base.vo.Result;
import com.dz.common.core.dto.KeyValueDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 租户信息
 * @Author: Handy
 * @Date: 2020/9/14 9:03
 */
@FeignClient(name = ServiceConstant.BASIC_SERVICE_NAME, contextId = "TenantInfoFeginClient")
public interface TenantInfoFeginClient {

    /**
     * 初始化品牌信息
     * @param tenantId
     * @return
     */
    @PostMapping(value = "/tenant/init")
    public Result<Boolean> initTenant(@RequestParam("tenantId") Long tenantId);

    /**
     * 根据租户ID列表获取租户信息
     * @param ids
     * @return
     */
    @PostMapping(value = "/tenant/list_by_ids")
    public Result<List<KeyValueDTO>> getTenantInfoByIds(@RequestBody List<Long> ids);

    /**
     * 获取所有租户列表
     * @return
     */
    @GetMapping(value = "/tenant/list_all")
    public Result<List<KeyValueDTO>> getAllTenantList();

    /**
     * 根据租户ID查询租户信息
     * @param id
     * @return
     */
    @GetMapping(value = "/tenant/get_by_id")
    public Result<KeyValueDTO> getTenantById(@RequestParam("id") Long id);

    /**
     * 根据租户Code查询租户信息
     * @param code
     * @return
     */
    @GetMapping(value = "/tenant/get_by_code")
    public Result<KeyValueDTO> getTenantByCode(@RequestParam("code") String code);

}
