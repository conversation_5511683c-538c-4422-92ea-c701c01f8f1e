package com.dz.ms.basic.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.aliyuncs.green.model.v20180509.ImageSyncScanRequest;
import com.aliyuncs.green.model.v20180509.TextScanRequest;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.http.HttpResponse;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.dz.ms.basic.entity.AliContentSecurityLog;
import com.dz.ms.basic.mapper.AliContentSecurityLogMapper;
import com.dz.ms.basic.service.AliCheckService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.*;

/**
 * @Description:
 * @Author: txt
 * @Date: 2022/3/15 16:48
 * @Version: 1.0
 */
@Slf4j
@Service
public class AliCheckServiceImpl implements AliCheckService {
    @Resource
    private AliContentSecurityLogMapper aliContentSecurityLogMapper;

    @Value("${aliyun.contentSecurity.accessKeyId:}")
    public String aliContentAccessKeyId;
    @Value("${aliyun.contentSecurity.accesskeySecret:}")
    public String aliContentAccesskeySecret;
    @Value("${aliyun.contentSecurity.regionId:}")
    public String aliContentRegionId;
    @Value("${aliyun.contentSecurity.product:}")
    public String aliContentProduct;
    @Value("${aliyun.contentSecurity.endpoint:}")
    public String aliContentEndpoint;
    @Value("${aliyun.contentSecurity.uid:}")
    public String aliContentUid;
    @Value("${aliyun.contentSecurity.seed:}")
    public String aliContentSeed;
    @Value("${aliyun.contentSecurity.bizType:}")
    public String aliContentBizType;


    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public boolean textScanRequest(String content, Integer modelType, Long modelId, boolean flag) {
        IClientProfile profile = DefaultProfile
                .getProfile(aliContentRegionId, aliContentAccessKeyId, aliContentAccesskeySecret);
        DefaultProfile
                .addEndpoint(aliContentRegionId, aliContentProduct, aliContentEndpoint);

        IAcsClient client = new DefaultAcsClient(profile);
        TextScanRequest textScanRequest = new TextScanRequest();
        textScanRequest.setAcceptFormat(FormatType.JSON); // 指定API返回格式。
        textScanRequest.setHttpContentType(FormatType.JSON);
        textScanRequest.setMethod(MethodType.POST); // 指定请求方法。
        textScanRequest.setEncoding("UTF-8");
        textScanRequest.setRegionId(aliContentRegionId);
        List<Map<String, Object>> tasks = new ArrayList<Map<String, Object>>();
        Map<String, Object> task1 = new LinkedHashMap<String, Object>();
        task1.put("dataId", UUID.randomUUID().toString());
        /**
         * 待检测的文本，长度不超过10000个字符。
         */
        task1.put("content", content);
        tasks.add(task1);
        JSONObject data = new JSONObject();

        /**
         * 检测场景。文本垃圾检测请传递antispam。
         **/
        data.put("scenes", Arrays.asList("antispam"));
        data.put("tasks", tasks);
        data.put("bizType", aliContentBizType);
//        log.info(JSON.toJSONString(data, true));
//        log.info("================阿里云文字审核 入参=====================");
//        log.info(JSON.toJSONString(data, true));
        try {
            textScanRequest.setHttpContent(data.toJSONString().getBytes("UTF-8"), "UTF-8", FormatType.JSON);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        // 请务必设置超时时间。
        textScanRequest.setConnectTimeout(3000);
        textScanRequest.setReadTimeout(6000);
        try {
            HttpResponse httpResponse = client.doAction(textScanRequest);
            if (httpResponse.isSuccess()) {
                JSONObject scrResponse = JSON.parseObject(new String(httpResponse.getHttpContent(), "UTF-8"));
//                log.info(JSON.toJSONString(scrResponse, true));
//                log.info("================阿里云文字审核 出参=====================");
//                log.info(JSON.toJSONString(scrResponse, true));
                if (200 == scrResponse.getInteger("code")) {
                    JSONArray taskResults = scrResponse.getJSONArray("data");
                    for (Object taskResult : taskResults) {
                        if (200 == ((JSONObject) taskResult).getInteger("code")) {
                            if (flag) {
                                aliContentSecurityLogMapper.insert(new AliContentSecurityLog(1, modelType, modelId, JSON.toJSONString(data, true), JSON.toJSONString(scrResponse, true)));
                            }
                            JSONArray sceneResults = ((JSONObject) taskResult).getJSONArray("results");
//                            Object sceneResult = sceneResults.get(0);
                            for (Object sceneResult : sceneResults) {
                                String label = ((JSONObject) sceneResult).getString("label");
                                String scene = ((JSONObject) sceneResult).getString("scene");
                                String suggestion = ((JSONObject) sceneResult).getString("suggestion");
                                // 根据scene和suggetion做相关处理。
                                // suggestion为pass表示未命中垃圾。suggestion为block表示命中了垃圾，可以通过label字段查看命中的垃圾分类。
                                if (!"normal".equals(label) || !"pass".equals(suggestion)) {
                                    return true;
                                }
                            }
                        } else {
                            log.info("task process fail:" + ((JSONObject) taskResult).getInteger("code"));
                            throw new RuntimeException("task process fail:" + ((JSONObject) taskResult).getInteger("code"));
                        }
                    }
                } else {
                    log.info("detect not success. code:" + scrResponse.getInteger("code"));
                    throw new RuntimeException("detect not success. code:" + scrResponse.getInteger("code"));
                }
            } else {
                log.info("response not success. status:" + httpResponse.getStatus());
                throw new RuntimeException("response not success. status:" + httpResponse.getStatus());
            }
        } catch (ServerException e) {
            e.printStackTrace();
        } catch (ClientException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }


    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Map<String, Integer> imageSyncScanRequest(List<String> urlList, Integer modelType, Long modelId, boolean flag) {
        Map<String, Integer> map = new HashMap<>();
        IClientProfile profile = DefaultProfile
                .getProfile(aliContentRegionId, aliContentAccessKeyId, aliContentAccesskeySecret);
        DefaultProfile
                .addEndpoint(aliContentRegionId, aliContentProduct, aliContentEndpoint);

        IAcsClient client = new DefaultAcsClient(profile);

        ImageSyncScanRequest imageSyncScanRequest = new ImageSyncScanRequest();
        // 指定API返回格式。
        imageSyncScanRequest.setAcceptFormat(FormatType.JSON);
        // 指定请求方法。
        imageSyncScanRequest.setMethod(MethodType.POST);
        imageSyncScanRequest.setEncoding("utf-8");
        // 支持HTTP和HTTPS。
        imageSyncScanRequest.setProtocol(ProtocolType.HTTP);


        JSONObject httpBody = new JSONObject();
        /**
         * 设置要检测的风险场景。计费依据此处传递的场景计算。
         * 一次请求中可以同时检测多张图片，每张图片可以同时检测多个风险场景，计费按照场景计算。
         * 例如，检测2张图片，场景传递porn和terrorism，计费会按照2张图片鉴黄，2张图片暴恐检测计算。
         * porn：图片智能鉴黄
         * terrorism：图片暴恐涉政
         * ad：图文违规
         * qrcode：图片二维码
         * live：图片不良场景
         * logo：图片logo
         */
        httpBody.put("scenes", Arrays.asList("porn", "terrorism", "ad", "qrcode", "live", "logo"));

        /**
         * 设置待检测图片。一张图片对应一个task。
         * 多张图片同时检测时，处理的时间由最后一个处理完的图片决定。
         * 通常情况下批量检测的平均响应时间比单张检测的要长。一次批量提交的图片数越多，响应时间被拉长的概率越高。
         * 这里以单张图片检测作为示例, 如果是批量图片检测，请自行构建多个task。
         */

        List<JSONObject> list = new ArrayList<>();
        for (String url : urlList) {
            JSONObject task = new JSONObject();
            task.put("dataId", UUID.randomUUID().toString());
            // 设置图片链接。
            task.put("url", url);
            task.put("time", new Date());
            list.add(task);
        }

        httpBody.put("tasks", list);
        httpBody.put("bizType", aliContentBizType);

        log.info("阿里云图片审核 入参");
        log.info(JSON.toJSONString(httpBody, true));
        imageSyncScanRequest.setHttpContent(org.apache.commons.codec.binary.StringUtils.getBytesUtf8(httpBody.toJSONString()),
                "UTF-8", FormatType.JSON);

        /**
         * 请设置超时时间。服务端全链路处理超时时间为10秒，请做相应设置。
         * 如果您设置的ReadTimeout小于服务端处理的时间，程序中会获得一个ReadTimeout异常。
         */
        imageSyncScanRequest.setConnectTimeout(3000);
        imageSyncScanRequest.setReadTimeout(10000);
        HttpResponse httpResponse = null;
        try {
            httpResponse = client.doAction(imageSyncScanRequest);
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 服务端接收到请求，完成处理后返回的结果。
        if (httpResponse != null && httpResponse.isSuccess()) {
            JSONObject scrResponse = JSON.parseObject(org.apache.commons.codec.binary.StringUtils.newStringUtf8(httpResponse.getHttpContent()));
            log.info("阿里云图片审核 出参");
            log.info(JSON.toJSONString(scrResponse, true));
            int requestCode = scrResponse.getIntValue("code");
            // 每一张图片的检测结果。
            JSONArray taskResults = scrResponse.getJSONArray("data");
            if (200 == requestCode) {
                for (Object taskResult : taskResults) {
                    // 单张图片的处理结果。
                    int taskCode = ((JSONObject) taskResult).getIntValue("code");
                    // 图片对应检测场景的处理结果。如果是多个场景，则会有每个场景的结果。
                    JSONArray sceneResults = ((JSONObject) taskResult).getJSONArray("results");

                    if (200 == taskCode) {
                        if (flag) {
                            aliContentSecurityLogMapper.insert(new AliContentSecurityLog(2, modelType, modelId, JSON.toJSONString(httpBody, true), JSON.toJSONString(scrResponse, true)));
                        }
                        map.put(((JSONObject) taskResult).getString("url"), 0);
                        for (Object sceneResult : sceneResults) {
                            String scene = ((JSONObject) sceneResult).getString("scene");
                            String suggestion = ((JSONObject) sceneResult).getString("suggestion");
                            String label = ((JSONObject) sceneResult).getString("label");
                            // 根据scene和suggestion做相关处理。
                            // 根据不同的suggestion结果做业务上的不同处理。例如，将违规数据删除等。
                            log.info("scene = [" + scene + "]");
                            log.info("suggestion = [" + suggestion + "]");
                            if (!"normal".equals(label) || !"pass".equals(suggestion)) {
                                map.put(((JSONObject) taskResult).getString("url"), 1);
                                break;
                            }
                        }

                    } else {
                        // 单张图片处理失败, 原因视具体的情况详细分析。
                        log.info("task process fail. task response:" + JSON.toJSONString(taskResult));
                        throw new RuntimeException("task process fail. task response:" + JSON.toJSONString(taskResult));
                    }
                }
            } else {
                // 表明请求整体处理失败，原因视具体的情况详细分析。
                log.info("the whole image scan request failed. response:" + JSON.toJSONString(scrResponse));
                throw new RuntimeException("the whole image scan request failed. response:" + JSON.toJSONString(scrResponse));
            }
        }
        return map;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void videoAsyncScanRequest(String url, String dataId, Integer modelType, Long modelId) throws Exception {
//        IClientProfile profile = DefaultProfile
//                .getProfile(aliContentRegionId, aliContentAccessKeyId, aliContentAccesskeySecret);
//        DefaultProfile
//                .addEndpoint(aliContentRegionId, aliContentProduct, aliContentEndpoint);
//
//        IAcsClient client = new DefaultAcsClient(profile);
//
//        VideoAsyncScanRequest videoAsyncScanRequest = new VideoAsyncScanRequest();
//        videoAsyncScanRequest.setAcceptFormat(FormatType.JSON); // 指定API返回格式。
//        videoAsyncScanRequest.setMethod(MethodType.POST); // 指定请求方法。
//
//        List<Map<String, Object>> tasks = new ArrayList<Map<String, Object>>();
//        Map<String, Object> task = new LinkedHashMap<String, Object>();
//        task.put("dataId", dataId);
//        task.put("url", url);
//        tasks.add(task);
//        /**
//         * 设置要检测的场景。计费是依据此处传递的场景计算。
//         * 视频默认1秒截取一帧，您可以自行控制截帧频率。收费按照视频的截帧数量以及每一帧的检测场景计算。
//         * 举例：1分钟的视频截帧60张，检测色情（对应场景参数porn）和暴恐涉政（对应场景参数terrorism）2个场景，收费按照60张色情+60张暴恐涉政进行计费。
//         * porn：视频智能鉴黄。
//         * terrorism：视频暴恐涉政。
//         * live：视频不良场景。
//         * logo：视频logo。
//         * ad：视频图文违规。
//         */
//        JSONObject data = new JSONObject();
//        data.put("scenes", Arrays.asList("porn", "terrorism"));
//        data.put("tasks", tasks);
//        data.put("callback", aliContentCallbackUrl);
//        data.put("seed", aliContentSeed);
//        data.put("bizType", aliContentBizType);
//        log.info("================阿里云视频异步审核 入参=====================");
//        log.info(JSON.toJSONString(data, true));
//        videoAsyncScanRequest.setHttpContent(data.toJSONString().getBytes("UTF-8"), "UTF-8", FormatType.JSON);
//
//        /**
//         * 请务必设置超时时间。
//         */
//        videoAsyncScanRequest.setConnectTimeout(3000);
//        videoAsyncScanRequest.setReadTimeout(6000);
//        try {
//            HttpResponse httpResponse = client.doAction(videoAsyncScanRequest);
//
//            if (httpResponse.isSuccess()) {
//                JSONObject scrResponse = JSON.parseObject(new String(httpResponse.getHttpContent(), "UTF-8"));
//                log.info("================阿里云视频异步审核 出参=====================");
//                log.info(JSON.toJSONString(scrResponse, true));
//                int requestCode = scrResponse.getIntValue("code");
//                // 每一张图片的检测结果。
//                JSONArray taskResults = scrResponse.getJSONArray("data");
//                if (200 == requestCode) {
//                    for (Object taskResult : taskResults) {
//                        // 单张图片的处理结果。
//                        int taskCode = ((com.alibaba.fastjson.JSONObject) taskResult).getIntValue("code");
//                        // 图片对应检测场景的处理结果。如果是多个场景，则会有每个场景的结果。
//                        if (200 == taskCode) {
//                            aliContentSecurityLogMapper.insert(new AliContentSecurityLog(3, modelType, modelId, JSON.toJSONString(data, true), JSON.toJSONString(scrResponse, true)));
//                        } else {
//                            // 单张图片处理失败, 原因视具体的情况详细分析。
//                            log.info("task process fail. task response:" + JSON.toJSONString(taskResult));
//                            throw new RuntimeException("task process fail. task response:" + JSON.toJSONString(taskResult));
//                        }
//                    }
//                } else {
//                    // 表明请求整体处理失败，原因视具体的情况详细分析。
//                    log.info("the whole image scan request failed. response:" + JSON.toJSONString(scrResponse));
//                    throw new RuntimeException("the whole image scan request failed. response:" + JSON.toJSONString(scrResponse));
//                }
//            } else {
//                log.info("response not success. status:" + httpResponse.getStatus());
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
    }


    public static String msgSecCheckErrMsg(String label) {
        String msg;
        switch (label) {
            case "normal":
                msg = "正常文本";
                break;
            case "spam":
                msg = "含垃圾信息";
                break;
            case "ad":
                msg = "广告";
                break;
            case "politics":
                msg = "涉政";
                break;
            case "terrorism":
                msg = "暴恐";
                break;
            case "abuse":
                msg = "辱骂";
                break;
            case "porn":
                msg = "色情";
                break;
            case "flood":
                msg = "灌水";
                break;
            case "contraband":
                msg = "违禁";
                break;
            case "meaningless":
                msg = "无意义";
                break;
            case "harmful":
                msg = "不良场景";
                break;
            case "customized":
                msg = "自定义（例如命中自定义关键词）";
                break;
            default:
                msg = "系统错误";
                break;
        }
        return msg;
    }


}
