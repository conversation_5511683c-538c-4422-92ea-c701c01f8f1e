<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dz.ms.user.mapper.OmsUserMapper" >

    <!-- 查询基础字段 -->
	<sql id="Base_Column_List">
  	    id,
  	    username,
  	    password,
  	    salt,
  	    realname,
  	    nickname,
  	    head_url,
  	    mobile,
  	    state,
  	    is_online,
  	    is_admin,
  	    created,
  	    creator,
  	    modified,
  	    modifier
    </sql>

    <!-- 根据账号查询 -->
    <select id="getUserByUsername" resultType="com.dz.ms.user.entity.OmsUser">
        select
        <include refid="Base_Column_List" />
        from oms_user
        where username = #{username}
    </select>

</mapper>
