.map-card {
  width: 620rpx;
  height: 318rpx;
  background: #FFFFFF;
  box-sizing: border-box;
  position: relative;
  margin: 0 auto;
  padding: 40rpx 30rpx;

  .name {
    height: 40rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 28rpx;
    color: #3C3C43;
    line-height: 40rpx;
    text-align: left;
    font-style: normal;
    margin-bottom: 24rpx;

    .tag {
      // width: 86rpx;
      padding: 0 10rpx;
      height: 36rpx;
      background: #F4EEDE;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 22rpx;
      color: #3C3C43;
      text-align: center;
      font-style: normal;
      position: relative;
      top: -2rpx;
      margin-left: 10rpx;
      display: inline-flex;
      align-items: center;
    }
  }

  .info {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #888888;
    line-height: 32rpx;
    text-align: left;
    font-style: normal;
    margin-bottom: 44rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .info-item {
      // margin-bottom: 25rpx;
      display: flex;
      align-items: center;
      position: relative;
      gap: 5rpx;
      height: 30rpx;
      line-height: 30rpx;

      .iconfont {
        font-size: 32rpx;
        display: block;
      }

      .info-text {
        width: 410rpx;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 20rpx;
      }
    }

    .distance-box {
      display: flex;
      flex-direction: column;
      align-items: center;

      .distance-icon {
        width: 72rpx;
        height: 72rpx;
        background: #FFFFFF;
        box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.06);
        border-radius: 36rpx;

        image {
          width: 72rpx;
          height: 72rpx;
        }
      }

      .num {
        height: 32rpx;
        font-family: MUJIFont2020;
        font-weight: 400;
        font-size: 24rpx;
        color: #3C3C43;
        line-height: 32rpx;
        text-align: center;
        font-style: normal;
        margin-top: 12rpx;
      }
    }
  }

  .btn-box {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .btn-item {
      width: 265rpx;
      height: 60rpx;
      border-radius: 5rpx;
      border: 2rpx solid #BBBBBB;
      line-height: 60rpx;
      text-align: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 24rpx;
      color: #3C3C43;
      font-style: normal;
      box-sizing: border-box;
    }

    .black-bg {
      background-color: #3C3C43;
      color: #fff;
      border: none;
    }
  }
}
