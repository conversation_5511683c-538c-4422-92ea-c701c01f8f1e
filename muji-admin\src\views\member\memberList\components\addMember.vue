<template>
  <a-drawer :title="title" width="95%" placement="right" :closable="true" :maskClosable="false" :open="open" @close="onClose">
    <a-spin :spinning="loading">
      <a-form class="form" ref="addForm" :model="addParams" :rules="rules" :labelCol="{ style: 'width:170px' }">
        <div class="form-top-titles-common">基本配置</div>
        <div class="form-top-line-common"></div>

        <a-form-item label="等级名称" name="gradeName">
          <a-input placeholder="请输入等级名称" style="width: 400px" v-model:value="addParams.gradeName" allow-clear show-count :maxlength="8" />
        </a-form-item>
        <a-form-item label="外部关联code" name="gradeCode">
          <a-input placeholder="请输入等级编码" style="width: 400px" v-model:value="addParams.gradeCode" allow-clear show-count :maxlength="20" />
        </a-form-item>
        <!-- <a-form-item label="当前等级消费金额" name="expenseAmount">
          <a-input-number :precision="0" addonAfter="元" style="width:200px" v-model:value="addParams.expenseAmount" :min="0" />
        </a-form-item> -->
        <div class="form-top-titles-common">等级卡片样式配置</div>
        <div class="form-top-line-common"></div>
        <a-form-item label="等级卡片" name="styleJson">
          <uploadImg :max="10" :width="100" :height="100" :imgUrl="addParams.styleJson" :form="addParams" path="styleJson" @success="uploadSuccess"></uploadImg>
          <div class="global-tip">
            推荐大小为670px*320px，最大不超过10M，支持jpg、png、gif格式
          </div>
        </a-form-item>
        <a-form-item label="等级排序" name="sort">
          <a-input-number :min="1" :precision="0" :max="99999" placeholder="请输入" v-model:value="addParams.sort" allow-clear></a-input-number>
          <div class="global-tip">
            排序从小到大，对应前台tab从左至右或从上至下
          </div>
        </a-form-item>
        <div class="form-top-titles-common">权益配置</div>
        <div class="form-top-line-common"></div>
        <a-form-item label="当前可激活权益">
          <a-space>
            <a-select allow-clear style="width: 400px" v-model:value="currentRights" :getPopupContainer="triggerNode => {
              return triggerNode.parentNode
            }
              " placeholder="请选择权益">
              <a-select-option v-for="item in rightsList" :title="item.details" :key="item.id" :value="item.id">{{
                item.benefitName }}（{{ item.details }}）</a-select-option>
            </a-select>
            <a-button type="primary" @click="addTagLevel('activateBenefits')">确认添加</a-button>

          </a-space>
        </a-form-item>
        <a-form-item class="hide-required-mark" label=" " v-if="addParams.activateBenefits.length > 0" :colon="false" name="activateBenefits">
          <a-input v-model:value="addParams.activateBenefits" style="display:none;" />
          <table class="draggble-table">
            <thead>
              <tr class="draggble-tr">
                <th class="draggble-th w200">
                  <div style="width:20px"></div>
                </th>
                <th class="draggble-th">已添加权益</th>
                <th class="draggble-th">点击埋点</th>
                <th class="draggble-th">弹窗配置</th>
                <th class="draggble-th">跳转链接</th>
                <th class="draggble-th">操作</th>
              </tr>
            </thead>
            <tbody class="draggble-tbody">
              <!-- ghost-class="ghost" -->
              <VueDraggable class="link" :list="addParams.activateBenefits" :drag-selector="'.icon'" @choose="handleChoose" :filter="'.move-input, .no-drag'" item-key="id" animation="300">
                <template #item="{ element: item, index }">

                  <tr class="draggble-tr" :key="index">
                    <td class="draggble-td w200">
                      <SvgIcon name="drag-dot-vertical" class="icon" width="20px" height="20px" />
                    </td>
                    <td class="draggble-td move-input">
                      <div class="line-div "> <a-image class="service-picurl" :src="item.activateImg" alt="" />
                        <div class="service-name" :title="item.benefitName">{{ item.benefitName }}</div>
                      </div>

                    </td>
                    <td class="draggble-td no-drag">

                      <a-input style="width: 100%;" placeholder="请输入" v-model:value="item.code" allow-clear show-count :maxlength="50" @pointerdown.stop.native />

                    </td>
                    <td class="draggble-td move-input">
                      <!-- <div v-if="item.popupImg" class="line-div ">
                        <a-image class="service-picurl" :src="item.popupImg" alt="" />
                        <DeleteOutlined @click="deletePopImg('activateBenefits',index)" style="margin-top:8px" />
                      </div>
                      <moduleImg v-else @ok="(img)=>addPopImg(img,'activateBenefits',index) ">
                        <a-button type="link">配置弹窗图片</a-button>
                      </moduleImg> -->
                      <uploadImg :max="10" :width="100" :height="100" :imgUrl="item.popupImg" :form="addParams" :path="'activateBenefits' + '.' + index + '.' + 'popupImg'" :disabled="disabled" @success="uploadSuccess" />
                    </td>
                    <td class="draggble-td move-input" style="display: flex;justify-content: center;">
                      <!-- <selectPage :link="item.jumpLink" btnTitle="配置跳转链接" btnType="link" @ok="(obj)=>changeLink(obj,'activateBenefits',index)"></selectPage> -->
                      <!-- <addLink :disabled="disabled" type="2"  :showType="[1,2]" :links="addParams.actionLinks"  :isEvent="false"> -->
                      <div class="line-div ">

                        <addLink :disabled="disabled" type="2" :showType="[1, 2]" :links="addParams['activateBenefits'][index].jumpLink" @ok="(link) => addParams['activateBenefits'][index].jumpLink = link" :isEvent="false">
                          <a-button type="link">配置跳转链接</a-button>
                        </addLink>
                      </div>

                    </td>
                    <td class="draggble-td move-input">
                      <a-button type="link" @click="deleteTag('activateBenefits', index)">删除</a-button>
                    </td>
                  </tr>

                </template>
              </VueDraggable>
            </tbody>
          </table>
          <!-- <div v-if="!addParams.activateBenefits.length">--</div> -->
        </a-form-item>
        <a-form-item label="后续等级样式(未激活)">
          <a-space>
            <a-select allow-clear style="width: 400px" v-model:value="unCurrentRights" :getPopupContainer="triggerNode => {
              return triggerNode.parentNode
            }
              " placeholder="请选择权益">
              <a-select-option v-for="item in rightsList" :title="item.details" :key="item.id" :value="item.id">{{
                item.benefitName }}（{{ item.details }}）</a-select-option>
            </a-select>
            <a-button type="primary" @click="addTagLevel('unActivateBenefits')">确认添加</a-button>

          </a-space>
        </a-form-item>

        <a-form-item class="hide-required-mark" v-if="addParams.unActivateBenefits.length > 0" label=" " :colon="false" name="unActivateBenefits">
          <a-input v-model:value="addParams.unActivateBenefits" style="display:none;" />
          <table class="draggble-table">
            <thead>
              <tr class="draggble-tr">
                <th class="draggble-th w200">
                  <div style="width:20px"></div>
                </th>
                <th class="draggble-th">已添加权益</th>
                <th class="draggble-th">点击埋点</th>
                <th class="draggble-th">弹窗配置</th>
                <th class="draggble-th">跳转链接</th>
                <th class="draggble-th">操作</th>
              </tr>
            </thead>
            <tbody class="draggble-tbody">
              <VueDraggable class="link" :list="addParams.unActivateBenefits" item-key="id" :drag-selector="'.icon'" @choose="handleChoose" :filter="'.move-input, .no-drag'" animation="300">
                <template #item="{ element: item, index }">

                  <tr class="draggble-tr" :key="index">
                    <td class="draggble-td w200">
                      <SvgIcon name="drag-dot-vertical" class="icon" width="20px" height="20px" />
                    </td>
                    <td class="draggble-td move-input">
                      <div class="line-div move-input"> <a-image style="background: #f0f0f0;" class="service-picurl" :src="item.unActivateImg" alt="" />
                        <div class="service-name" :title="item.benefitName">{{ item.benefitName }}</div>
                      </div>

                    </td>
                    <td class="draggble-td no-drag">

                      <a-input style="width: 100%;" placeholder="请输入" v-model:value="item.code" allow-clear show-count :maxlength="50" @pointerdown.stop.native />

                    </td>
                    <td class="draggble-td move-input">
                      <uploadImg :max="10" :width="100" :height="100" :imgUrl="item.popupImg" :form="addParams" :path="'unActivateBenefits' + '.' + index + '.' + 'popupImg'" :disabled="disabled" @success="uploadSuccess" />
                      <!-- <div v-if="item.popupImg" class="line-div ">
                        <a-image class="service-picurl" :src="item.popupImg" alt="" />
                        <DeleteOutlined @click="deletePopImg('activateBenefits',index)" style="margin-top:8px" />
                      </div>
                      <moduleImg v-else @ok="(img)=>addPopImg(img,'unActivateBenefits',index) ">
                        <a-button type="link">配置弹窗图片</a-button>
                      </moduleImg> -->
                    </td>
                    <td class="draggble-td move-input" style="display: flex;justify-content: center;">
                      <!-- <selectPage :link="item.jumpLink" btnType="link" btnTitle="配置跳转链接" @ok="(obj)=>changeLink(obj,'unActivateBenefits',index)"></selectPage> -->
                      <div class="line-div ">

                        <addLink :disabled="disabled" type="2" :showType="[1, 2]" :links="addParams['unActivateBenefits'][index].jumpLink" @ok="(link) => addParams['unActivateBenefits'][index].jumpLink = link" :isEvent="false">
                          <a-button type="link">配置跳转链接</a-button>
                        </addLink>
                      </div>

                    </td>
                    <td class="draggble-td move-input">
                      <a-button type="link" @click="deleteTag('unActivateBenefits', index)">删除</a-button>
                    </td>
                  </tr>

                </template>
              </VueDraggable>
            </tbody>
          </table>
          <!-- <div v-if="!addParams.unActivateBenefits.length">--</div> -->
        </a-form-item>
      </a-form>
    </a-spin>
    <template #footer v-if="!disabled">
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="ok" :loading="loading">确定</a-button>
      </a-space>
    </template>

  </a-drawer>
</template>
<script setup>
import { benefitList_all, grade_infoAdd, grade_infoUpdate, grade_infoInfo } from '@/http/index.js'
import moduleImg from './modalImg.vue'
import { useGlobalStore } from '@/store'
import { message, Modal } from "ant-design-vue";
import _ from "lodash"
import { v4 as uuidv4 } from "uuid";
const $router = useRouter();
const global = useGlobalStore()
const addForm = ref(null)
import { cloneDeep } from 'lodash'
import { checkPassword, checkUserName } from '@/utils/validate.js'
import { onMounted } from 'vue';
const emit = defineEmits(['ok', 'cancel'])
const props = defineProps({
  // 操作抽屉打开关闭
  visible: {
    type: Boolean,
    default: false
  },
  // 数据ID
  id: {
    type: String || Number,
    default: ''
  },
  // 操作类型
  type: {
    type: Number,
    default: 0, // 0-新增 1-编辑  2-查看
  }

})
// 置灰
const disabled = computed(() => {
  return props.type == 2
})

// 标题
const title = computed(() => {
  return ['新建', '编辑', '查看'][props.type] + '会员等级'
})

const { open, addParams, rules, loading, rightsList, currentRights, unCurrentRights } = toRefs(reactive({
  open: props.visible,
  rightsList: [],
  currentRights: null,
  unCurrentRights: null,
  loading: false,
  addParams: {
    // expenseAmount: 0,//未解锁升级条件
    state: 1, // 是否启用
    gradeName: '', // 等级名称
    gradeCode: '', // 等级编码
    styleJson: "", // 等级卡片
    activateBenefits: [
    ], // 当前等级
    unActivateBenefits: [], // 后续等级
    sort: 1,

  },
  rules: {
    gradeName: [{ required: true, message: '请输入等级名称', trigger: ['blur', 'change'] }],
    // gradeCode: [{ required: true, message: '请输入等级编码', trigger: ['blur', 'change'] }],
    // styleJson: [{ required: true, message: '请上传等级卡片', trigger: ['blur', 'change'] }],
    // activateBenefits: [{ required: true, message: '请选择当前等级激活权益', trigger: ['blur', 'change'] }],
    // expenseAmount: [{ required: true, message: '请输入当前等级消费金额', trigger: ['blur', 'change'] }],
  }
})
);
const uploadSuccess = async (data) => {
  let { form, path: key, imgUrl } = data;
  console.log("🚀 ~ uploadSuccess ~ form, path: key, imgUrl:", form, key, imgUrl)
  _.set(form, key.split('.'), imgUrl)
  // 清除校验
  await nextTick()
  addForm.value.validateFields([key.split('.')])
}

// 暴露给父组件的属性和方法
defineExpose({
  addParams
})


watch(() => props.visible, (value) => {
  // 弹窗操作
  open.value = props.visible
  addParams.value = {
    // expenseAmount: 0,//未解锁升级条件
    state: 1, // 是否启用
    gradeName: '', // 等级名称
    gradeCode: '', // 等级编码
    styleJson: "", // 等级卡片
    activateBenefits: [
    ], // 当前等级
    unActivateBenefits: [], // 后续等级
    sort: 1,

  }
  currentRights.value = null,
    unCurrentRights.value = null,
    addForm.value?.resetFields()
  if (open.value) {
    initData()
  }
})
//所有接口调取出
const initData = async () => {
  const promiseArr = []
  promiseArr.push(benefitList_all({ state: 1 }))
  if (props.id) {
    promiseArr.push(grade_infoInfo({ id: props.id }))
  }
  // 如果有新的接口就在这里push
  loading.value = true
  try {
    const [benefit_all, grade_infoI] = await Promise.all(promiseArr)
    console.log("🚀 ~ initData ~ grade_infoI:", grade_infoI)
    rightsList.value = benefit_all.data
    if (grade_infoI) {
      let newgradeInfo = _.cloneDeep(grade_infoI.data)
      if (newgradeInfo.activateBenefits && newgradeInfo.activateBenefits.length > 0) {
        newgradeInfo.activateBenefits.forEach(item => {
          if (item.jumpLink) {
            item.jumpLink = JSON.parse(item.jumpLink)
          } else {
            item.jumpLink = []
          }
        })
      } else {
        newgradeInfo.activateBenefits = []
      }
      if (newgradeInfo.unActivateBenefits && newgradeInfo.unActivateBenefits.length > 0) {
        newgradeInfo.unActivateBenefits.forEach(item => {

          if (item.jumpLink) {

            item.jumpLink = JSON.parse(item.jumpLink)

          } else {
            item.jumpLink = []
          }

        })
      } else {
        newgradeInfo.unActivateBenefits = []
      }
      console.log(newgradeInfo, 'newgradeInfonewgradeInfonewgradeInfo');
      addParams.value = {
        ...newgradeInfo
      }
    }
    loading.value = false
  } catch (error) {

    console.error('获取数据失败:', error)
  }
}

// 删除标签
const deleteTag = (type, index) => {
  addParams.value[type].splice(index, 1)
}
// 添加卡片等级
const addTagLevel = async (type) => {
  if (!currentRights.value && !unCurrentRights.value) return message.warning('加入前请选择权益')
  const newRight = _.cloneDeep(rightsList.value)
  let currentItem = newRight.filter(res => res.id === currentRights.value)[0]
  let unCurrentItem = newRight.filter(res => res.id === unCurrentRights.value)[0]
  if (type === 'activateBenefits') {
    if (JSON.stringify(addParams.value.unActivateBenefits).indexOf(`"id":${currentItem.id}`) !== -1) {
      return message.warning('该权益已被使用')
    }
    if (JSON.stringify(addParams.value.activateBenefits).indexOf(`"id":${currentItem.id}`) !== -1) {
      return message.warning('该权益当前类型中已存在')
    }



    currentItem['jumpLink'] = []
  } else {
    if (JSON.stringify(addParams.value.activateBenefits).indexOf(`"id":${unCurrentItem.id}`) !== -1) {
      return message.warning('该权益当前类型中已存在')
    }
    if (JSON.stringify(addParams.value.unActivateBenefits).indexOf(`"id":${unCurrentItem.id}`) !== -1) {
      return message.warning('该权益已被使用')
    }


    unCurrentItem['jumpLink'] = []
  }
  // console.log(currentItem, unCurrentItem);






  addParams.value[type].push(type === 'activateBenefits' ? currentItem : unCurrentItem
  )
  console.log(addParams.value);
  type === 'activateBenefits' ? currentRights.value = null : unCurrentRights.value = null

  await nextTick()
  addForm.value.validateFields([type])
}
function changeLink(obj, type, index) {
  // console.log("🚀 ~ changeLink ~ index:", index)
  // console.log(obj);
  addParams.value[type][index]['jumpLink'] = obj.data


}
function addPopImg(img, type, index) {
  addParams.value[type][index]['popupImg'] = img
}
function deletePopImg(type, index) {
  addParams.value[type][index]['popupImg'] = null
}
// 关闭弹窗
const onClose = () => {
  emit('cancel')
}

// 确定
const ok = () => {

  console.log("🚀 ~ addForm.value.validate ~ params:", addParams.value)
  addForm.value.validate().then(res => {

    let params = _.cloneDeep(addParams.value)
    if (params.activateBenefits.length > 0) {
      params.activateBenefits.forEach((item, index) => {
        if (item.jumpLink.length > 0) {

          item.jumpLink = JSON.stringify(item.jumpLink)

        } else {
          item.jumpLink = null
        }
        item.sort = index + 1
      });
    }
    if (params.unActivateBenefits.length > 0) {
      params.unActivateBenefits.forEach((item, index) => {
        if (item.jumpLink.length > 0) {

          item.jumpLink = JSON.stringify(item.jumpLink)

        } else {
          item.jumpLink = null
        }
        item.sort = index + 1
      });
    }

    loading.value = true
    if (props.id) {
      console.log('编辑');
      grade_infoUpdate(params).then(res => {
        message.success(res.msg);
        emit('ok', props.id)
      }).finally(() => {
        loading.value = false
      })
    } else {
      grade_infoAdd(params).then(res => {
        message.success(res.msg);
        emit('ok', props.id)
      }).finally(() => {
        loading.value = false
      })
    }

  })
}

const handleChoose = (event) => {
  if (event.target.tagName === 'INPUT') {
    event.preventDefault(); // 阻止拖拽行为
  }
}

</script>

<style scoped lang="scss">
//影藏红的点
.hide-required-mark {
  :deep(.ant-form-item-required) {
    display: none;
  }
}

:deep(.searchForm .ant-form-item) {
  margin-bottom: 16px;
}

:deep(.anticon-eye-invisible) {
  display: none;
}

.member-role {
  background: #f2f2f2;
  margin-bottom: 20px;
  padding: 5px 10px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  &:nth-child(odd) {
    margin-right: 35px;
  }
}

.line-div {
  // width: 100%;
  display: flex;
  // justify-content: center;
  align-items: center;
  flex-direction: column;
}

.w200 {
  width: 80px !important;
}

.icon {
  cursor: move;
}

.dag-text {
  pointer-events: all;
  position: absolute;
  left: 50%;
}

.draggble {
  &-table {
    width: 100%;
  }

  &-tbody {
    width: 100%;
  }

  &-th {
    // display: flex;
    // justify-content: center;
    // align-items: center;
    width: 100%;
    // padding: 10px;
  }

  &-tr {
    display: flex;
    width: 100%;
    align-items: center;
    // margin-bottom: 10px;

    // justify-content: center;
  }

  &-td {
    width: 100%;
    text-align: center;

    // display: flex;
    // align-items: center;
    // padding: 10px;
    // justify-content: center;
  }
}

:deep(.service-picurl) {
  width: 50px !important;
  height: 50px !important;

  // margin-right: 10px;
}

.service-name {
  flex: 1;
  width: 100%;
  // width: 200px;
  text-align: center;
  white-space: nowrap;
  /* 确保文本在一行内显示 */
  overflow: hidden;
  /* 超出容器的文本将被隐藏 */
  text-overflow: ellipsis;
}
</style>
