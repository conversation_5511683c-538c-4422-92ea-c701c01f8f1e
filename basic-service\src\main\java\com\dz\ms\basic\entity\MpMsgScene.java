package com.dz.ms.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 小程序订阅消息场景
 * @author: Handy
 * @date:   2023/07/06 18:05
 */
@Getter
@Setter
@NoArgsConstructor
@Table("小程序订阅消息场景")
@TableName(value = "mp_msg_scene")
public class MpMsgScene implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "场景ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR,length = 20,isNull = false,comment = "场景编号")
    private String scene;
    @Columns(type = ColumnType.VARCHAR,length = 20,isNull = true,comment = "模板名称")
    private String sceneName;
    @Columns(type = ColumnType.TINYINT,length = 1,isNull = false,comment = "状态 0关闭 1启用",defaultValue = "1")
    private Integer state;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = false,comment = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private Long creator;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.BIGINT,length = 19,isNull = true,comment = "更新人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long modifier;
    @Columns(type = ColumnType.DATETIME,length = 0,isNull = false,comment = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;

    public MpMsgScene(Long id, String scene, String sceneName, Integer state) {
        this.id = id;
        this.scene = scene;
        this.sceneName = sceneName;
        this.state = state;
    }

}
