<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.dz.ms</groupId>
    <artifactId>sa-service</artifactId>
    <version>1.1.0</version>
    <properties>
        <java.version>11</java.version>
        <hutool.version>5.7.21</hutool.version>
    </properties>
    <parent>
        <groupId>com.dz.common</groupId>
        <artifactId>common</artifactId>
        <version>1.1.0</version>
    </parent>

    <dependencies>
        <dependency>
            <groupId>com.dz.common</groupId>
            <artifactId>common-core</artifactId>
        </dependency>

    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
