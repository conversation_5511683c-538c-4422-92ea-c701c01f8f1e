package com.dz.ms.sales.service.impl;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dz.common.base.enums.ErrorCode;
import com.dz.common.core.dto.basic.SubscribeMsgSendDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.dto.user.UserSimpleDTO;
import com.dz.common.core.dto.user.crm.MuJiMemberOrder;
import com.dz.common.core.enums.SubscribeMsgEnum;
import com.dz.common.core.exception.BusinessException;
import com.dz.common.core.fegin.basic.MpMsgFeignClient;
import com.dz.common.core.fegin.user.UserInfoFeginClient;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.common.core.utils.ThreadPoolUtils;
import com.dz.ms.sales.constants.CacheKeys;
import com.dz.ms.sales.constants.CampaignCodeEnum;
import com.dz.ms.sales.dto.*;
import com.dz.ms.sales.entity.CampaignEnroll;
import com.dz.ms.sales.entity.CampaignUser;
import com.dz.ms.sales.mapper.CampaignEnrollMapper;
import com.dz.ms.sales.mapper.CampaignUserMapper;
import com.dz.ms.sales.service.CampaignEnrollService;
import com.dz.ms.sales.service.CampaignService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/19
 */
@Service
@Slf4j
public class CampaignEnrollServiceImpl implements CampaignEnrollService {

    @Resource
    private CampaignEnrollMapper campaignEnrollMapper;
    @Resource
    private UserInfoFeginClient userInfoFeginClient;
    @Resource
    private CampaignService campaignService;
    @Resource
    private CampaignUserMapper campaignUserMapper;
    @Resource
    private RedisService redisService;
    @Resource
    private MpMsgFeignClient mpMsgFeignClient;


    public List<CampaignEnrollRosterListDTO> getCampaignEnrollPage() {
        List<String> campaignCodeList = Arrays.asList(CampaignCodeEnum.CAMPAIGN_ENROLL_1.getCode(), CampaignCodeEnum.CAMPAIGN_ENROLL_2.getCode());
        List<CampaignEnrollRosterListDTO> result = new ArrayList<>();
        for (String campaignCode : campaignCodeList) {
            LambdaQueryWrapper<CampaignEnroll> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CampaignEnroll::getStatus, 1);
            queryWrapper.eq(CampaignEnroll::getCampaignCode, campaignCode);
            queryWrapper.orderByDesc(CampaignEnroll::getId);
            List<CampaignEnroll> page = campaignEnrollMapper.selectList(queryWrapper);
            List<CampaignEnrollRosterDTO> campaignEnrollDTOS = BeanCopierUtils.convertList(page, CampaignEnrollRosterDTO.class);

            for (CampaignEnrollRosterDTO campaignEnrollDTO : campaignEnrollDTOS) {
                if (StringUtils.isNotBlank(campaignEnrollDTO.getName())) {
                    campaignEnrollDTO.setName(campaignEnrollDTO.getName().charAt(0) + "***");
                } else {
                    campaignEnrollDTO.setName("无***");
                }
                campaignEnrollDTO.setMobile(campaignEnrollDTO.getMobile().substring(0, 3) + "****" + campaignEnrollDTO.getMobile().substring(campaignEnrollDTO.getMobile().length() - 4));
                campaignEnrollDTO.setCardNo(campaignEnrollDTO.getCardNo().substring(0, 3) + "****" + campaignEnrollDTO.getCardNo().substring(campaignEnrollDTO.getCardNo().length() - 4));
            }

            CampaignDTO campaignInfoCache = campaignService.getCampaignInfoCache(campaignCode);

            CampaignEnrollRosterListDTO dto = new CampaignEnrollRosterListDTO();
            dto.setCampaignStartTime(campaignInfoCache.getCampaignStartTime());
            dto.setCampaignEndTime(campaignInfoCache.getCampaignEndTime());
            dto.setCampaignShowTime(campaignInfoCache.getCampaignShowTime());
            dto.setEnrollRosterList(campaignEnrollDTOS);

            result.add(dto);
        }
        return result;
    }


    public static void main(String[] args) {
        String password = "muji6688";

        String substring = password.substring(password.length() - 4);
        System.out.println(substring);
    }

    @Override
    public CampaignEnrollDTO getCampaignEnrollInfo(String campaignCode) {
        UserSimpleDTO userInfo = userInfoFeginClient.getCurrentUserSimpleInfo().getData();
        List<CampaignEnroll> campaignEnrollList = campaignEnrollMapper.selectList(new LambdaQueryWrapper<CampaignEnroll>().eq(CampaignEnroll::getCampaignCode, campaignCode).eq(CampaignEnroll::getUnionid, userInfo.getUnionid()).orderByDesc(CampaignEnroll::getId));
        if (ObjectUtils.isNotEmpty(campaignEnrollList)) {
            CampaignEnrollDTO dto = BeanCopierUtils.convertObject(campaignEnrollList.get(0), CampaignEnrollDTO.class);


            if (CampaignCodeEnum.CAMPAIGN_ENROLL_1.getCode().equals(campaignCode)) {
                dto.setEnrollCount(1);
            }
            if (CampaignCodeEnum.CAMPAIGN_ENROLL_2.getCode().equals(campaignCode)) {
                dto.setEnrollCount(2);
            }

            Long l = campaignUserMapper.selectCount(new LambdaQueryWrapper<CampaignUser>().eq(CampaignUser::getCampaignCode, campaignCode).eq(CampaignUser::getMobile, userInfo.getMobile()));
            dto.setEnrollSuccess(l > 0);
            return dto;
        }
        return null;
    }


    @Override
    public Long addCampaignEnroll(CampaignEnrollParamDTO param) {
        CampaignDTO campaignInfoCache = campaignService.getCampaignInfoCache(param.getCampaignCode());
        if (ObjectUtils.isEmpty(campaignInfoCache)) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "活动不存在");
        }
        if (campaignInfoCache.getState() != 1 && campaignInfoCache.getState() != 2) {
            log.info("活动状态: {}, 活动详情: {}", new String[]{"未开始", "进行中", "公示期", "已结束"}[campaignInfoCache.getState()], JSON.toJSONString(campaignInfoCache));
            throw new BusinessException(ErrorCode.BAD_REQUEST, "活动" + new String[]{"未开始", "进行中", "公示期", "已结束"}[campaignInfoCache.getState()]);
        }
        UserSimpleDTO userInfo = userInfoFeginClient.getCurrentUserSimpleInfo().getData();
        if (campaignEnrollMapper.selectCount(new LambdaQueryWrapper<CampaignEnroll>().eq(CampaignEnroll::getCampaignCode, param.getCampaignCode()).in(CampaignEnroll::getStatus, 0, 1).eq(CampaignEnroll::getUnionid, userInfo.getUnionid())) > 0) {
            throw new BusinessException(ErrorCode.BAD_REQUEST, "您已提交过申请，不允许重复提交");
        }

        CampaignEnroll campaignEnroll = BeanCopierUtils.convertObject(param, CampaignEnroll.class);
        campaignEnroll.setUid(userInfo.getId());
        campaignEnroll.setCardNo(userInfo.getCardNo());
//        campaignEnroll.setUsername(userInfo.getUsername());
        campaignEnroll.setUnionid(userInfo.getUnionid());
        campaignEnroll.setOpenid(userInfo.getOpenid());
        campaignEnroll.setMobile(userInfo.getMobile());
        campaignEnrollMapper.insert(campaignEnroll);
        return campaignEnroll.getId();
    }

    @Override
    public Boolean siftCampaignEnrollByChannel(String campaignCode, Map<String, Map<String, Integer>> jobParams) {
        Set<String> channelOneKeys = jobParams.keySet();
//        List<Long> idList = new ArrayList<>();
//        List<Long> allIdList = new ArrayList<>();
//        List<CampaignEnroll> allCampaignEnrollList = new ArrayList<>();

        CampaignDTO campaignInfoDetailCache = campaignService.getCampaignInfoDetailCache(CampaignCodeEnum.CAMPAIGN_SIGN_IN.getCode());
        CampaignBuyerDTO campaignBuyer = campaignInfoDetailCache.getCampaignBuyer();

        for (String channelOneKey : channelOneKeys) {
            Map<String, Integer> channelTwoMap = jobParams.get(channelOneKey);
//            Set<String> channelTwoKeys = channelTwoMap.keySet();
            for (Map.Entry<String, Integer> entry : channelTwoMap.entrySet()) {
                String channelTwoKey = entry.getKey();
                int requiredCount = entry.getValue();

                List<CampaignEnroll> list = new ArrayList<>();
                while (list.size() < requiredCount &&  !Thread.currentThread().isInterrupted()) {
                    List<CampaignEnroll> campaignEnrollList = campaignEnrollMapper.siftCampaignEnrollByChannel(campaignCode, channelOneKey, channelTwoKey, requiredCount - list.size());
                    if (ObjectUtils.isEmpty(campaignEnrollList)) {
                        break;
                    }
                    if (ObjectUtils.isEmpty(campaignBuyer)) {
                        break;
                    }
                    for (CampaignEnroll campaignEnroll : campaignEnrollList) {
                        List<MuJiMemberOrder> muJiMemberOrderList = campaignService.fetchUserOrders(campaignEnroll.getCardNo(), campaignBuyer);
                        // 匹配订单详情
                        boolean isOrderMatch = campaignService.checkIfUserPurchasedRequiredItem(campaignEnroll.getCardNo(), muJiMemberOrderList, campaignBuyer.getSku());
                        // 如果通过其他渠道已参与打卡，则跳过
                        Long count = campaignUserMapper.selectCount(new LambdaQueryWrapper<CampaignUser>().eq(CampaignUser::getCampaignCode, CampaignCodeEnum.CAMPAIGN_SIGN_IN.getCode()).eq(CampaignUser::getMobile, campaignEnroll.getMobile()));
                        if (!isOrderMatch && count <= 0) {
                            // 如果用户没有购买指定商品，则将其添加到筛选列表中
//                            idList.add(campaignEnroll.getId());
                            list.add(campaignEnroll);
//                            allCampaignEnrollList.add(campaignEnroll);

                            campaignEnrollMapper.verifyCampaignEnroll(Collections.singletonList(campaignEnroll.getId()));
                            campaignUserMapper.insert(new CampaignUser(null, CampaignCodeEnum.CAMPAIGN_SIGN_IN.getCode(), 3, campaignEnroll.getMobile()));
                            redisService.del(CacheKeys.CAMPAIGN_ORDER_MATCH + campaignEnroll.getUnionid());
                        } else {
                            CampaignEnroll campaignEnroll1 = new CampaignEnroll();
                            campaignEnroll1.setId(campaignEnroll.getId());
                            campaignEnroll1.setStatus(2);
                            campaignEnrollMapper.updateById(campaignEnroll1);
                        }
//                        allIdList.add(campaignEnroll.getId());
                    }
                }
            }
        }
//        if (ObjectUtils.isNotEmpty(idList)) {
//            campaignEnrollMapper.verifyCampaignEnroll(idList);
//            for (CampaignEnroll campaignEnroll : allCampaignEnrollList) {
//                campaignUserMapper.insert(new CampaignUser(null, CampaignCodeEnum.CAMPAIGN_SIGN_IN.getCode(), 3, campaignEnroll.getMobile()));
//            }
//        }
        campaignEnrollMapper.verifyCampaignEnroll1(campaignCode);
        return true;
    }

    @Override
    public void enrollSuccessPushMsgJob(String campaignCode) {
        List<CampaignEnroll> campaignEnrolls = campaignEnrollMapper.selectList(new LambdaQueryWrapper<CampaignEnroll>().eq(CampaignEnroll::getCampaignCode, campaignCode).eq(CampaignEnroll::getStatus, 1).eq(CampaignEnroll::getPushFlag, 0));
        if (ObjectUtils.isEmpty(campaignEnrolls)) {
            return;
        }
        List<SubscribeMsgSendDTO> list = new ArrayList<>();
        for (CampaignEnroll campaignEnroll : campaignEnrolls) {
            SubscribeMsgSendDTO msgSend = new SubscribeMsgSendDTO(SubscribeMsgEnum.ENROLL_SUCCESS, campaignEnroll.getOpenid(), null,
                    new String[]{null, null});
            msgSend.setMsgCode(SubscribeMsgEnum.ENROLL_SUCCESS.getMsgCode());
            list.add(msgSend);
            campaignEnroll.setPushFlag(1);
            campaignEnrollMapper.updateById(campaignEnroll);
        }

        Long tenantId = SecurityContext.getUser().getTenantId();
        ThreadPoolUtils.pool.execute(() -> {
            SecurityContext.setUser(new CurrentUserDTO(tenantId));
            for (SubscribeMsgSendDTO msgSend : list) {
                mpMsgFeignClient.sendSubscribeMsgCatch(msgSend, tenantId);
            }
        });
    }

    @Override
    public void enrollFailPushMsgJob(String campaignCode) {
        List<CampaignEnroll> campaignEnrolls = campaignEnrollMapper.selectList(new LambdaQueryWrapper<CampaignEnroll>().eq(CampaignEnroll::getCampaignCode, campaignCode).eq(CampaignEnroll::getStatus, 2).eq(CampaignEnroll::getPushFlag, 0));
        if (ObjectUtils.isEmpty(campaignEnrolls)) {
            return;
        }
        List<SubscribeMsgSendDTO> list = new ArrayList<>();
        for (CampaignEnroll campaignEnroll : campaignEnrolls) {
            SubscribeMsgSendDTO msgSend = new SubscribeMsgSendDTO(SubscribeMsgEnum.ENROLL_FAIL, campaignEnroll.getOpenid(), null,
                    new String[]{null, null, null});
            msgSend.setMsgCode(SubscribeMsgEnum.ENROLL_FAIL.getMsgCode());
            list.add(msgSend);
            campaignEnroll.setPushFlag(1);
            campaignEnrollMapper.updateById(campaignEnroll);
        }

        Long tenantId = SecurityContext.getUser().getTenantId();
        ThreadPoolUtils.pool.execute(() -> {
            SecurityContext.setUser(new CurrentUserDTO(tenantId));
            for (SubscribeMsgSendDTO msgSend : list) {
                mpMsgFeignClient.sendSubscribeMsgCatch(msgSend, tenantId);
            }
        });
    }
}
