package com.dz.common.core.dto.wechat.mpmsg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 小程序订阅消息模板添加入参
 * @author: Handy
 * @date:   2023/07/07 14:22
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
@ApiModel(value = "小程序订阅消息模板添加入参")
public class SubscribeMsgTemplateAddDTO {

    @ApiModelProperty(value = "模版标题ID")
    private String tid;
    @ApiModelProperty(value = "添加模板关键词列表")
    private List<Integer> kidList;
    @ApiModelProperty(value = "服务场景描述，15个字以内")
    private String sceneDesc;

}
