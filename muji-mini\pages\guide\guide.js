// pages/guide/guide.js
import {
  getStoreList
} from '../../api/index.js'
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: app.globalData.statusBarHeight,
    navBarHeight: app.globalData.navBarHeight,
    storeName: '',
    address: '',
    phone: '',
    distance: '',
    qrCodeUrl: undefined,
    latitude: undefined, // 用户的经纬度
    longitude: undefined, // 用户的经纬度
    loading: false,
    storeLatitude: undefined, // 门店的经纬度
    storeLongitude: undefined,
    miniGuide:null,
    isBackHidden: false,
    openingHourOne:null,
    openingHourTwo:null,
    images:null,
    weworkImagesType:null,
    weworkOneImages:null
  },


  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const {
      userLatitude
    } = app.globalData;
    if (userLatitude && userLatitude !== 0) {
      this.setData({
        latitude: app.globalData.userLatitude,
        longitude: app.globalData.userLongitude,
      })
    }
    const {
      id
    } = options;
    this.setData({
      id,
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    const {
      userLatitude
    } = app.globalData;
    if (userLatitude && userLatitude !== 0) {
      this.setData({
        latitude: app.globalData.userLatitude,
        longitude: app.globalData.userLongitude,
      })
    }
    const {
      id
    } = this.data;
    this.setData({
      id,
    })
    this.getStore();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  async getStore() {
    this.setData({
      loading: true,
    })
    const {
      id,
      latitude,
      longitude,
    } = this.data;
    const res = await getStoreList({
      id,
      latitude,
      longitude,
    });
    const value = res.data.list[0];
    this.setData({
      storeName: value.storeName,
      address: value.storeAddress,
      phone: value.phone,
      qrCodeUrl: value.weworkImages !== null ? value.weworkImages : undefined,
      distance: value.distance,
      loading: false,
      storeLongitude: value.longitude,
      storeLatitude: value.latitude,
      miniGuide:value.miniGuide,
      openingHourOne:value.openingHourOne,
      openingHourTwo:value.openingHourTwo,
      images:value.images,
      weworkImagesType:value.weworkImagesType,
      weworkOneImages: value.weworkOneImages !== null ? value.weworkOneImages : undefined,
    })
  },
  // 保存图片到相册
  saveImage() {
    const that = this;
    wx.downloadFile({
      url: that.data.qrCodeUrl || '',
      success: (res) => {
        wx.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            wx.showToast({
              title: '保存成功',
            })
            this.onQrClose();
          },
          fail(err) {
            console.log('err', err)
            if (err.errMsg === 'saveImageToPhotosAlbum:fail auth deny') {
              // 用户拒绝了相册权限
              wx.showModal({
                title: '权限提示',
                content: '请授权访问相册，才能保存图片',
                success(res) {
                  if (res.confirm) {
                    wx.openSetting(); // 跳转到设置界面
                  }
                }
              });
            } else {
              // 其他错误处理
              wx.showToast({
                title: '保存失败',
                icon: 'none'
              });
            }
          }
        });
      },
    });
  },
  openLocation() {
    const {
      storeLongitude,
      storeLatitude,
      storeName,
      address
    } = this.data;
    wx.openLocation({
      longitude: Number(storeLongitude),
      latitude: Number(storeLatitude),
      scale: 18,
      name: storeName,
      address: address,
      fail: (e) => {
        console.log('openLocation fail', e)
      }
    })
  },
  callTel: app.debounce(async function (e) {
    const {
      phone
    } = this.data;
    let pattern = /[^u4e00-u9fa5]/g
    const callPhone = phone.replace(pattern, '')
    wx.makePhoneCall({
      phoneNumber: callPhone,
      success: function () {
        console.log("拨打电话成功！")
      },
      fail: function () {
        console.log("拨打电话失败！")
      }
    })
  }),
  onLongPress(e) {
    // 获取图片的路径
    const imagePath = e.target.dataset.src; // 可以是图片的 URL 或本地路径
    console.log('长按图片：', imagePath);

    // 使用 wx.getImageInfo 获取图片信息（如果是本地文件）
    wx.getImageInfo({
      src: imagePath,
      success: (res) => {
        // 使用图片路径识别二维码
        this.scanQRCodeFromImage(res.path);
      },
      fail: (err) => {
        console.error('获取图片信息失败：', err);
      }
    });
  },
  scanQRCodeFromImage(imagePath) {
    wx.scanCode({
      path: imagePath, // 传入图片路径
      success: (res) => {
        console.log('识别结果：', res.result);
        wx.showToast({
          title: '扫描成功',
          icon: 'success',
        });
      },
      fail: (err) => {
        console.error('二维码扫描失败：', err);
        wx.showToast({
          title: '扫描失败',
          icon: 'none',
        });
      }
    });
  }
})
