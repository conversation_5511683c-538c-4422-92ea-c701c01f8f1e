<template>
  <layout>
    <div style="height:100%;overflow-y:auto">
      <a-form class="form" ref="formRef" :model="addParams" labelAlign="right" :labelCol="{ style: 'width:160px' }">
        <div class="header-title">订阅频次设置</div>
        <a-form-item label="订阅频次">
          <a-radio-group v-model:value="addParams.frequencyType">
            <a-radio :value="1" style="display:block;margin-top:3px;">每次点击都订阅</a-radio>
            <a-radio :value="2" style="display:block;margin-top:10px;">
              <a-space>
                <a-space-compact block>
                  <a-form-item name="openDays" :rules="addParams.frequencyType==2?[{required:true,message:'请输入',trigger:['change','blur']}]:[]">
                    <a-input-number placeholder="请输入" :precision="0" :min="1" :max="999" v-model:value="addParams.openDays" addonBefore="每"></a-input-number>
                  </a-form-item>
                  <a-form-item name="openFrequency" :rules="addParams.frequencyType==2?[{required:true,message:'请输入',trigger:['change','blur']}]:[]">
                    <a-input-number placeholder="请输入" :precision="0" :min="1" :max="999" v-model:value="addParams.openFrequency" addonBefore="天" addonAfter="次"></a-input-number>
                  </a-form-item>
                </a-space-compact>
              </a-space>
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="订阅次数限制">
          <a-radio-group v-model:value="addParams.openType">
            <a-radio :value="1" style="display:block;margin-top:3px;">不限制</a-radio>
            <a-radio :value="2" style="display:block;margin-top:10px;">
              <a-space>
                <a-form-item name="openNumbers" :rules="addParams.openType==2?[{required:true,message:'请输入',trigger:['change','blur']}]:[]">
                  <a-input-number placeholder="请输入" :precision="0" :min="1" :max="999" v-model:value="addParams.openNumbers" addonBefore="任意模板订阅次数少于" addonAfter="次才弹窗"></a-input-number>
                </a-form-item>
              </a-space>
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <div class="header-title">未开启订阅设置</div>
        <a-form-item label="未开启订阅">
          <a-radio-group v-model:value="addParams.modalType">
            <a-radio :value="1">弹窗提醒开启</a-radio>
            <a-radio :value="2">直接跳过</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="未开启弹窗频次" v-if="addParams.modalType==1">
          <a-radio-group v-model:value="addParams.noOpenType">
            <a-radio :value="1" style="display:block;margin-top:3px;">每次进行订阅时，若没有开启订阅功能就进行弹窗</a-radio>
            <a-radio :value="2" style="display:block;margin-top:10px;">
              <a-space>
                <a-space-compact block>
                  <a-form-item name="noOopenDays" :rules="addParams.noOpenType==2?[{required:true,message:'请输入',trigger:['change','blur']}]:[]">
                    <a-input-number placeholder="请输入" :precision="0" :min="1" :max="999" v-model:value="addParams.noOopenDays" addonBefore="全局每"></a-input-number>
                  </a-form-item>
                  <a-form-item name="noOpenFrequency" :rules="addParams.noOpenType==2?[{required:true,message:'请输入',trigger:['change','blur']}]:[]">
                    <a-input-number placeholder="请输入" :precision="0" :min="1" :max="999" v-model:value="addParams.noOpenFrequency" addonBefore="天弹窗" addonAfter="次"></a-input-number>
                  </a-form-item>
                </a-space-compact>
              </a-space>
            </a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </div>
    <template v-slot:footer>
      <a-flex justify="center" align="center">
        <a-button type="primary" :disabled="!$hasPermission('subscriptionConfiguration:save')" @click="saveData()">保存配置</a-button>
      </a-flex>
    </template>
  </layout>
</template>
<script setup>
import { reactive, toRefs, ref, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { useGlobalStore } from '@/store'
import { useRoute, useRouter } from 'vue-router'
import { msgConfigSave, msgConfigInfo } from '@/http/index.js'
import _, { cloneDeep } from 'lodash';
import { v4 as uuidv4 } from 'uuid'


// 固定应用
const global = useGlobalStore()
const $router = useRouter()
const { query } = useRoute()
const formRef = ref()
const tableRef = ref()

// 定义数据
const { addParams } = toRefs(reactive({
  addParams: {
    frequencyType: 1,
    openDays: 1,
    openFrequency: 1,
    openType: 1,
    openNumbers: 1,
    modalType: 1,
    noOopenDays: 1,
    noOpenFrequency: 1,
  },
}))


// 获取订阅信息
msgConfigInfo().then(res => {
  if (res.data?.id) {
    let content = JSON.parse(res.data.content)
    addParams.value = { ...content, id: res.data.id }
  }
})



// 保存数据
const saveData = () => {
  formRef.value.validate().then(res => {
    let params = { content: JSON.stringify(addParams.value), id: addParams.value.id }
    // console.log(params)
    msgConfigSave(params).then(res => {
      message.success('保存成功')
    })
  })
}


</script>
<style lang="scss" scoped>
.link-wrap {
  line-height: 28px;
  padding-right: 10px;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: normal;
  white-space: nowrap;
  color: inherit;
  display: inline-block;
}
.link-delete {
  margin-left: 50px;
  padding: 10px;
  color: red;
}
.link-address-wrap {
  width: 375px;
  padding: 10px;
  border: 1px solid #ebeef5;
  .item-link {
    font-size: 12px;
    display: flex;
    align-items: center;
    color: #606266;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px dashed #ebeef5;
    position: relative;
    .link-delete {
      position: absolute;
      right: 0;
      top: 0;
      padding: 10px;
    }
    &:last-child {
      margin-bottom: 0;
    }

    &.active-item-link {
      color: #e75c45;
    }
  }
}
.tabbar {
  width: 375px;
  height: 68px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  &-item {
    flex: 1;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    &-img {
      width: 24px;
      height: 24px;
      &.big {
        width: 60px;
        height: 60px;
        margin-top: -50px;
      }
    }
    &-name {
      margin-top: 4px;
      font-size: 12px;
    }
  }
}
.content {
  display: flex;
  position: relative;
  overflow: hidden;

  &-tip {
    position: absolute;
    left: 0;
    top: 0;
    padding: 5px;
  }
  .left {
    position: relative;
    width: 375px;
    // height: 812px;
    height: 118px;
    margin-right: 30px;
    display: flex;
    flex-direction: column;
    background: gray;
    outline: 1px solid #999;
    padding-top: 50px;

    &-header {
      height: 94px;
      width: 100%;
    }
    &-content {
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden;
      &::-webkit-scrollbar {
        width: 0px;
        border: 1px solid transparent;
      }
      &::-webkit-scrollbar-button {
        display: none;
      }
      &::-webkit-scrollbar-thumb {
        background: #dddddd;
        border-radius: 3px;
      }
    }
    &-tarbar {
      height: 52px;
    }
  }
  .right {
    flex: 1;
    padding-right: 30px;
  }
  .card {
    padding-top: 20px;
    margin-left: 20px;
    margin-top: 20px;
    margin-bottom: 30px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    position: relative;
    &:first-child {
      margin-top: 0;
    }
    &-drag {
      position: absolute;
      left: 0;
      padding: 10px;
      top: 50%;
      transform: translateY(-50%);
      width: 50px;
    }
    .title {
      position: absolute;
      top: 0;
      left: 0;
      background: #1890ff;
      color: #fff;
      font-size: 12px;
      padding: 3px 8px;
    }
  }
  .delete-content {
    position: absolute;
    color: #dc412d;
    font-size: 18px;
    top: 30px;
    right: 30px;
  }
}
</style>

