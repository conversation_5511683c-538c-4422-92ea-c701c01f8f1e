import { validate } from './validate'
import myLocalStorage from './localStorage'

const getSetting = async () => {
  const getSetting = await wx.getSetting()
  // 处理后的值 true表示用户授权了地理位置 false表示用户没授权地理位置（false的场景1：从未调用过授权地理位置接口、false的场景2：调用了授权地理位置接口但是用户拒绝了）
  getSetting.isLocationAuthorized = getSetting.authSetting['scope.userLocation'] === true
  // 原始值 undefined表示从未调用过授权地理位置接口 true表示用户同意授权地理位置 false表示用户拒绝授权地理位置
  getSetting.valueOfLocationAuthorized = getSetting.authSetting['scope.userLocation']
  return getSetting
}
const openSetting = async () => {
  const openSetting = await wx.openSetting()
  // 处理后的值 true表示用户授权了地理位置 false表示用户没授权地理位置（false的场景1：从未调用过授权地理位置接口、false的场景2：调用了授权地理位置接口但是用户拒绝了）
  openSetting.isLocationAuthorized = openSetting.authSetting['scope.userLocation'] === true
  // 原始值 undefined表示从未调用过授权地理位置接口 true表示用户同意授权地理位置 false表示用户拒绝授权地理位置
  openSetting.valueOfLocationAuthorized = openSetting.authSetting['scope.userLocation']
  return openSetting
}
const myGetCurrentPages = () => {
  const pages = getCurrentPages()
  pages.forEach(page => {
    page.path = `/${page.route}`
  })
  return pages
}
const myGetCurrentPage = () => {
  const pages = myGetCurrentPages()
  const len = pages.length
  return pages[len - 1]
}
const isFirstPage = () => {
  const pages = myGetCurrentPages()
  const len = pages.length
  return len <= 1
}
const isTabBarPage = (pageNameOrPath) => {
  let hasTabBar = false
  const currentPage = myGetCurrentPage()
  let currentPagePath = currentPage.path
  if (pageNameOrPath) currentPagePath = pageNameOrPath
  let page = currentPagePath.split('?')[0]
  console.log('一级页面', page)
  if (wx.$contants.TAB_BAR[page]) hasTabBar = true
  return hasTabBar
}
const navigateTo = (options) => {
  const isTabBar = isTabBarPage(options.url)
  if (isTabBar) {
    return wx.$mp.switchTab(options)
  } else {
    // 页面跳转拦截
    let pages = getCurrentPages()
    // samePages 中定义了相同也不需要返回的页面路径
    if (!wx.$config.samePages.some(item => options.url.includes(item))) {
      let index = pages.findIndex(item => {
        return item.path.includes(options.url.split('?')[0])
      })
      // 页面栈 中有路径相同的页面的时候 变为返回
      if (index >= 0) {
        return wx.$mp.navigateBack({
          delta: pages.length - index - 1
        });
      }
    }
    // 页面超过 10 级 自动变为重定向
    if (pages.length >= 10) {
      return wx.$mp.redirectTo(options)
    }
    return wx.navigateTo(options)
  }
}


// 跳转返回
const navigateBack = (options) => {
  let pages = getCurrentPages();
  if (pages.length > 1) {
    return wx.navigateBack(options)
  } else {
    return wx.$mp.switchTab({
      url: '/pages/index/index',
    })
  }
}
// 一级页面跳转  处理参数保存
const switchTab = (options) => {
  const app = getApp()
  let optionsUrL = options.url.split('?')[1] || ''
  let obj = optionsUrL ? optionsUrL.split('&').reduce((obj, item) => {
    let [key, value] = item.split('=')
    obj[key] = value
    return obj
  }, {}) : {}
  app.globalData.switchTabQuery = obj
  return wx.switchTab(options)
}

const redirectTo = (options) => {
  const isTabBar = isTabBarPage(options.url)
  if (isTabBar) {
    return wx.$mp.switchTab(options)
  } else {
    return wx.redirectTo(options)
  }
}

// 半屏小程序
const openEmbeddedMiniProgram = (options) => {
  // 判断是否支持半屏拉起小程序
  if (wx.openEmbeddedMiniProgram) {
    return wx.openEmbeddedMiniProgram(options)
  } else {
    return wx.$mp.navigateToMiniProgram(options)
  }
}
const pagePathToPageFullPath = (item) => {
  const data = item.data || {}
  let pagePath = item.pagePath
  if (pagePath === 'makePhoneCall') {
    wx.$mp.makePhoneCall({ phoneNumber: data.phoneNumber })
  } else if (pagePath === 'returnTop') {
    wx.$mp.pageScrollTo({ scrollTop: 0, duration: 500 })
  } else if (pagePath === 'openPrivacyContract') {
    wx.$mp.openPrivacyContract()
  } else if (pagePath === 'navigateToMiniProgram') {
    wx.$mp.navigateToMiniProgram({ appId: data.appid, path: data.path })
  } else {
    if (!pagePath) return
    const query = Object.entries(data).map((v) => {
      const key = v[0]
      let val = v[1]
      if (pagePath === '/pages/webView/webView') val = encodeURIComponent(val)
      return key + '=' + val
    })
    if (query.length) pagePath = pagePath + '?' + query.join('&')
    return pagePath
  }
}
wx.$mp = {
  // 埋点事件封装
  track: (data) => {
    const app = getApp()
    let pages = getCurrentPages()
    let page = pages[pages.length - 1]
    let props = {
      data: JSON.stringify(data.props || {}), // 埋点的标识参数
      channelOne: app.globalData.channelOne, // 渠道1
      channelTwo: app.globalData.channelTwo, // 渠道2
      pagePath: page.path,
      options: JSON.stringify(page.data.options)
    }
    data.props = props
    console.log(data, '埋点数据')
    // 只有正式环境上传
    if (wx.$config.env === 'prod') {
      app.muji_sdk.track(data);
    }
  },
  validate,
  localStorage: myLocalStorage,
  getSetting,
  openSetting,
  getCurrentPages: myGetCurrentPages,
  getCurrentPage: myGetCurrentPage,
  isFirstPage: isFirstPage,
  isTabBarPage,
  navigateTo,
  switchTab,
  navigateBack,
  redirectTo,
  openEmbeddedMiniProgram,
  pagePathToPageFullPath,
  makePhoneCall(options) {
    return wx.makePhoneCall(options)
  },
  openPrivacyContract(options) {
    return wx.openPrivacyContract(options)
  },
  pageScrollTo(options) {
    return wx.pageScrollTo(options)
  },
  navigateToMiniProgram(options) {
    return wx.navigateToMiniProgram(options)
  },
  getOneVarType(oneVar) {
    return Object.prototype.toString.call(oneVar).slice(8, -1)
  },
  hideShareMenu(options) {
    return wx.hideShareMenu(options)
  },
  showShareMenu(options) {
    return wx.showShareMenu(options)
  },
  exitMiniProgram(options) {
    return wx.exitMiniProgram(options)
  },
  showLoading(options) {
    return wx.showLoading({
      mask: true,
      ...options
    })
  },
  hideLoading(options) {
    return wx.hideLoading({
      noConflict: true,
      ...options
    })
  },
  showToast(options) {
    return wx.showToast({
      icon: 'none',
      ...options
    })
  },
  onAppRoute(fn) {
    return wx.onAppRoute(function (options) {
      fn.bind(this)(options)
    })
  }
}

wx.$mp.onAppRoute(function (options) {
  const app = getApp()
  clearTimeout(app?.globalData?.autoGoTimer)
  // 积分商城页面，点击商品进入商品详情页。
  // 从商品详情页返回积分商城页面，需要保留积分商城页面的滑动位置。
  // 从其他页面进入积分商城页面，需要滚动到顶部。
  // 所以需要记录上一个页面是什么，当前页面是什么。
  const currentPagePath = myLocalStorage.getStorageSync('currentPagePath')
  if (currentPagePath) myLocalStorage.setStorageSync('prevPagePath', currentPagePath)
  myLocalStorage.setStorageSync('currentPagePath', `/${options.path}`)
  console.log('onAppRoute：', options)
})
