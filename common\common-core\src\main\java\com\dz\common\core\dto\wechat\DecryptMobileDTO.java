package com.dz.common.core.dto.wechat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 解密微信手机号
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@NoArgsConstructor
public class DecryptMobileDTO {

	@ApiModelProperty(value = "手机号")
	private String phoneNumber;
	@ApiModelProperty(value = "国家码")
	private String countryCode;

	public DecryptMobileDTO(String phoneNumber, String countryCode) {
		this.phoneNumber = phoneNumber;
		this.countryCode = countryCode;
	}

}
