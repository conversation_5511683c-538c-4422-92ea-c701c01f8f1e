<template>
  <a-popover>
    <template #content>
      <a-table :dataSource="thisFields.dataSource" :columns="thisFields.columns" :pagination="false" style="width: 600px;">
        <template #bodyCell="{text, record, index, column}">
          <template v-if="column.dataIndex === 'name'">
            <a-button type="link" @click="thisMethods.goDetail(record)">{{ record.name }}</a-button>
          </template>
          <template v-if="column.key === 'onStartTime' && record.onType === 2">
            <div>{{ record.onStartTime }}</div>
          </template>
          <template v-if="column.key === 'onStartTime' && record.onType === 1">
            <div>永久有效</div>
          </template>
          <template v-if="column.key === 'onEndTime' && record.onType === 2">
            <div>{{ record.onEndTime }}</div>
          </template>
        </template>
      </a-table>
    </template>
    <slot />
  </a-popover>
</template>

<script setup>
import { onMounted, reactive } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const props = defineProps({
  record: {
    type: Object,
    default: () => ({})
  }
})

const dataSource = props.record.shelfActivityList || []
dataSource.forEach(v => {
  v.campaignStateDesc = ['', '未开始', '进行中', '已结束'][v.campaignState]
})
const thisFields = reactive({
  dataSource,
  columns: [
    { title: '名称', dataIndex: 'name', align: 'center', ellipsis: true, width: 100 },
    { title: '件数', dataIndex: 'campaignProductNum', align: 'center', ellipsis: true, width: 100 },
    { title: '状态', dataIndex: 'campaignStateDesc', align: 'center', ellipsis: true, width: 100 },
    { title: '开始时间', key: 'onStartTime', align: 'center', ellipsis: true, width: 140 },
    { title: '结束时间', key: 'onEndTime', align: 'center', ellipsis: true, width: 140 }
  ]
})
const thisMethods = {
  goDetail(record) {
    router.push({ name: 'CrowdPurchaseRestriction', query: { id: record.id } })
  }
}

onMounted(() => {
})
</script>

<style lang="scss" scoped>
:deep(.ant-table-thead) {
  display: none;
}
</style>
