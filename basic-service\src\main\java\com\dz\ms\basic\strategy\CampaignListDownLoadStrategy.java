package com.dz.ms.basic.strategy;

import com.dz.common.core.dto.DownloadAddParamDTO;
import com.dz.common.core.fegin.product.ShelfCampaignFeignClient;
import com.dz.common.core.service.DownloadStrategy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
public class CampaignListDownLoadStrategy implements DownloadStrategy {

    @Resource
    private ShelfCampaignFeignClient shelfCampaignFeignClient;

    @Override
    public void export(DownloadAddParamDTO downloadAddParamDTO) {
        shelfCampaignFeignClient.exportList(downloadAddParamDTO);
    }
}
