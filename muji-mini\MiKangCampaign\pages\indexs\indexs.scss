/* signUp/pages/indexs/indexs.wxss */
.page-container {
    background-size: 100% 100%;
    position: relative;

    .index {
        flex: 1;
        height: 0;
        // overflow-y: auto;
        // overflow-x: hidden;

        margin: 24rpx 49rpx env(safe-area-inset-bottom);
        // width: calc(100% - 98rpx);
        width: auto;

        // position: relative;
        .content {
            // background: #F8F6ED;
            display: flex;
            flex-direction: column;
        }

        .header {
            padding-top: 48rpx;
            font-family: MUJIFont2020;
            font-weight: 900;
            font-size: 48rpx;
            color: var(--text-black-color);
            line-height: 64rpx;
            letter-spacing: 0;
            margin-left: 47rpx;
        }

        .index-form {
            flex: 1;
            height: 0;
            margin: 0 47rpx;
            position: relative;

            // height: 100%;
            .form-title {
                width: 168rpx;
                height: 50rpx;
                background: #e3d8bb;
                border-radius: 29rpx 29rpx 29rpx 29rpx;
                font-family: MUJIFont2020;
                font-weight: 700;
                font-size: 26rpx;
                color: var(--text-black-color);
                line-height: 50rpx;
                letter-spacing: 2px;
                text-align: center;
                margin-top: 101rpx;
            }

            .form-title1 {
                margin-top: 55rpx;
            }

            .form-title2 {
                margin-top: 104rpx;
            }

            .form-tips-last {
                font-family: MUJIFont2020;
                font-weight: 400;
                font-size: 20rpx;
                line-height: 36rpx;
                letter-spacing: 0rpx;
                text-align: left;
                font-style: normal;
                text-transform: none;
                margin-top: 36rpx;
                padding-bottom: 74rpx;
                color: #888888;
            }

            .form-tips {
                font-family: MUJIFont2020;
                font-weight: 400;
                font-size: 28rpx;
                color: #888888;
                line-height: 42rpx;
                letter-spacing: 0rpx;
                text-align: left;
                // padding-left: 5rpx;
                margin-top: 7rpx;
                // text-indent: -0.8em;
                // margin-left: 47rpx;
            }

            .form-tips1 {
                // padding-left: 21rpx;
                padding-bottom: 40rpx;
            }

            .form-item {
                border-bottom: 2rpx solid #f4eede;
                // margin-bottom: 32rpx;
                padding: 36rpx 0;
                // margin-left: 20rpx;
                // margin-right: 19rpx;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .item-label {
                    font-family: MUJIFont2020;
                    font-weight: 500;
                    font-size: 26rpx;
                    line-height: 38rpx;
                    letter-spacing: 0rpx;
                    color: var(--text-black-color);
                    text-align: left;
                    font-style: normal;
                }

                .item-content {
                    margin-top: 10rpx;
                    color: var(--text-black-color);
                    display: flex;
                    justify-content: space-between;
                    font-family: MUJIFont2020;
                    font-weight: 350;
                    font-size: 26rpx;
                    line-height: 38rpx;
                    letter-spacing: 0rpx;
                    text-align: left;
                    font-style: normal;

                    input {
                        width: 100%;
                        &::placeholder {
                            color: #bbbbbb !important;
                            font-weight: 350;
                        }
                    }

                    .without-data {
                        font-weight: 350;
                    }

                    .radio-group {
                        display: flex;
                        font-size: 28rpx;
                        color: #979797;
                        right: 30rpx;

                        .radio-item {
                            display: flex;
                            align-items: center;
                            font-family: MUJIFont2020;
                            font-weight: 400;
                            font-size: 28rpx;
                            line-height: 40rpx;
                            letter-spacing: 1px;
                            color: #979797;
                            text-align: left;
                            font-style: normal;
                            height: 28rpx;
                            position: relative;

                            &:first-child {
                                margin-right: 60rpx;
                            }

                            .radio {
                                width: 28rpx;
                                height: 28rpx;
                                border: 1rpx solid var(--border-black-color);
                                border-radius: 50%;
                                margin-right: 20rpx;
                            }

                            .active {
                                border: 1rpx solid var(--btn-primary);
                                position: relative;
                            }

                            .active::before {
                                content: "";
                                width: 20rpx;
                                height: 20rpx;
                                background: var(--btn-primary);
                                display: block;
                                border-radius: 50%;
                                position: absolute;
                                left: 50%;
                                top: 50%;
                                transform: translate(-50%, -50%);
                            }
                        }
                    }

                    .item-text {
                        flex: 1;
                    }

                    .icon-button {
                        height: 36rpx;
                        width: 36rpx;
                    }

                    .auth-btn {
                        width: 200rpx;
                        height: 60rpx;
                        background: #f5f5f5;
                        border-radius: 5rpx;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 600;
                        font-size: 26rpx;
                        color: var(--text-black-color);
                        line-height: 60rpx;
                        text-align: center;
                        font-style: normal;
                    }

                    .item-input {
                        font-family: MUJIFont2020;
                        height: 28rpx;
                        font-weight: 350;
                        font-size: 26rpx;
                        color: var(--text-black-color);
                        line-height: 38rpx;
                        letter-spacing: 0rpx;
                        text-align: right;
                        font-style: normal;
                    }

                    .without-data {
                        color: #bbbbbb;
                    }
                }

                .address {
                    display: flex;
                    flex-direction: row;
                    justify-content: space-between;
                    // margin-top: 42rpx;
                    margin-bottom: 10rpx;
                    align-items: flex-end;

                    .getAddress {
                        text-decoration: underline;
                        text-underline-offset: 3rpx;
                        font-weight: 400;
                        font-size: 19rpx;
                        // line-height: 39rpx;
                        letter-spacing: 1px;
                        text-align: left;
                        display: flex;
                        align-items: center;
                        // padding: 20rpx;
                        // padding-top: 45rpx;
                        padding-bottom: 0rpx;
                    }
                }
            }

            .red {
                color: red;
            }
        }

        .item-value {
            &.disabled {
                color: #888;
            }
        }

        .check {
            height: 29rpx;
            display: flex;
            justify-content: flex-start;
            font-family: MUJIFont2020;
            font-weight: 350;
            font-size: 26rpx;
            font-weight: 350;
            color: var(--text-black-color);
            line-height: 29rpx;
            text-align: left;

            .iconfont {
                font-size: 26rpx;
                margin-right: 13rpx;
            }

            .link {
                font-family: MUJIFont2020;
                font-weight: 350;
                font-size: 26rpx;
                // color: #BFA37D;
                line-height: 29rpx;
                text-align: center;
                font-style: normal;
                text-transform: none;
            }

            .radio {
                border-radius: 50%;
                color: var(--text-black-color);
            }
        }
    }

    // .form-footer {
    //   left: 50%;
    //   transform: translateX(-50%);
    //   // text-align: center;
    //   position: absolute;
    //   bottom: 46rpx;
    // }

    .item-button {
        color: #888888;
    }

    .form-footer {
        padding-top: 140rpx;
    }

    .bottom-box {
        z-index: 100;
        width: 100%;
        position: fixed;
        bottom: 92rpx;
        display: flex;
        justify-content: center;
    }

    .right-lanyard {
        position: relative;
        display: inline-block;
        height: 0;
        z-index: 99;
        text-align: right;

        image {
            position: absolute;
            right: -15rpx;
            top: -200rpx;
        }
    }
}

.custom-select-action {
    border-radius: 16rpx 16rpx 0 0 !important;
    .van-action-sheet__header {
        font-family: Source Han Sans CN;
        font-weight: 700;
        font-size: 28rpx;
        text-align: center;
        vertical-align: middle;
        color: #3c3c43;
        padding: 24rpx 0 42rpx;
    }
}

.custom-select-action .van-action-sheet__close {
    font-size: 32rpx !important;
    color: #3c3c43;
    font-weight: 300 !important;
    padding: 24rpx 40rpx 42rpx;
}

.select-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 24rpx;
    padding: 0 40rpx 56rpx;
}

.select-item {
    width: 206rpx;
    height: 68rpx;
    line-height: 68rpx;
    border-radius: 8rpx;
    background-color: #f7f7f7;
    text-align: center;
}

.button-box {
    border-top: 1px solid #eeeff2;
    padding: 24rpx 40rpx;
}

.select-confirm {
    color: white;
    background-color: #3c3c43;
    border-radius: 8rpx;
    font-weight: 500;
    font-size: 28rpx;
    line-height: 92rpx;
}

.select-confirm-active {
    color: white;
    background-color: #3c3c43;
}

.van-icon-arrow {
    color: #bbbbbb;
}
