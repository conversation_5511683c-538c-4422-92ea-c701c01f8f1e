<template>
  <div>
    <!-- 一次性 和周期 的展示 -->
    <template v-if="formFields.readyCycle != 3">
      <template v-for="(item, index) in formFields.taskRewardList" :key="index">
        <a-form-item label="奖励内容" :rules="{ required: true, message: '请选择奖励内容' }" :name="props.conditionsRuleNamePrefix.concat(['taskRewardList', index, 'rewardType'])">
          <a-radio-group v-model:value="item.rewardType">
            <template v-for="(item, index) in rewardTypeOptions" :key="index">
              <a-radio-button :value="item.value">{{ item.label }}</a-radio-button>
            </template>
          </a-radio-group>
        </a-form-item>

        <!-- 积分奖励 -->
        <a-form-item v-if="item.rewardType == '2'" :name="props.conditionsRuleNamePrefix.concat(['taskRewardList', index, 'pointsNum'])" label="积分" :rules="{ required: true, message: '请输入积分' }">
          <a-input-number :min="1" :max="99999999" style="width: 200px" placeholder="请输入积分" v-model:value="item.pointsNum" />
        </a-form-item>
        <a-form-item v-if="item.rewardType == '3'" :name="props.conditionsRuleNamePrefix.concat(['taskRewardList', index, 'prizeNum'])" label="抽奖次数" :rules="{ required: true, message: '请输入次数' }">
          <a-input-number :min="1" :max="30" style="width: 200px" placeholder="请输入次数" v-model:value="item.prizeNum" />
        </a-form-item>
        <!-- 券奖励 -->
        <template v-if="item.rewardType == '1'">
          <template v-for="(itx, idx) in item.couponIds" :key="idx">
            <a-form-item label="券ID" :name="props.conditionsRuleNamePrefix.concat(['taskRewardList', index, 'couponIds', idx])" :rules="{ required: true, message: '请输入券ID' }">
              <!-- align-items: center; -->
              <div style="display: flex;">
                <a-input v-model:value="formFields.taskRewardList[index].couponIds[idx]" allow-clear show-count :maxlength="64" placeholder="请输入券ID" style="width:300px" />
                <a-button type="link" v-if="item.couponIds.length - 1 <= idx && idx != 4" @click="thisMethods.addCouponr(index, item.couponIds.length)">+ 增加一张券</a-button>
                <a-button type="link" v-if="idx < item.couponIds.length - 1 || idx > 0" @click="thisMethods.removeCouponr(index, idx)">- 删除</a-button>
              </div>
            </a-form-item>
            <a-form-item :colon="false" label=" " v-if="item.couponIds.length-1 === idx">
              <div class="global-tip">
                可同时奖励多张券，奖励的券将直接进入券包，无需领取，最多添加5张
              </div>
            </a-form-item>

          </template>
        </template>
      </template>
    </template>

    <!-- 周期 + 阶梯 -->
    <template v-else-if="formFields.readyCycle == 3">
      <template v-if="formFields.totalReadyNum">
        <a-form-item label="奖励内容" name="" :rules="{ required: true, message: '请选择奖励内容' }"></a-form-item>
        <template v-for="(item, index) in formFields.taskRewardList" :key="index">
          <a-form-item :label="`第${index + 1}次`" :rules="{ required: true, message: '请选择奖励内容' }" :name="props.conditionsRuleNamePrefix.concat(['taskRewardList', index, 'rewardType'])">
            <a-radio-group v-model:value="item.rewardType">
              <template v-for="(item, index) in rewardTypeOptions" :key="index">
                <a-radio-button :value="item.value">{{ item.label }}</a-radio-button>
              </template>
            </a-radio-group>
          </a-form-item>

          <!-- 积分奖励 -->
          <a-form-item v-if="item.rewardType == '2'" :name="props.conditionsRuleNamePrefix.concat(['taskRewardList', index, 'pointsNum'])" label="积分" :rules="{ required: true, message: '请输入积分' }">
            <a-input-number :min="1" :max="99999999" style="width: 200px" placeholder="请输入积分" v-model:value="item.pointsNum" />
          </a-form-item>
          <a-form-item v-if="item.rewardType == '3'" :name="props.conditionsRuleNamePrefix.concat(['taskRewardList', index, 'prizeNum'])" label="抽奖次数" :rules="{ required: true, message: '请输入次数' }">
            <a-input-number :min="1" :max="30" style="width: 200px" placeholder="请输入次数" v-model:value="item.prizeNum" />
          </a-form-item>
          <!-- 券奖励 -->
          <template v-if="item.rewardType == '1'">
            <template v-for="(itx, idx) in item.couponIds" :key="idx">
              <a-form-item label="券ID" :name="props.conditionsRuleNamePrefix.concat(['taskRewardList', index, 'couponIds', idx])" :rules="{ required: true, message: '请输入券ID' }">
                <div style="display: flex;align-items: center;">
                  <a-input v-model:value="formFields.taskRewardList[index].couponIds[idx]" allow-clear show-count :maxlength="64" placeholder="请输入券ID" style="width:300px" />
                  <a-button type="link" v-if="item.couponIds.length - 1 <= idx && idx != 4" @click="thisMethods.addCouponr(index, item.couponIds.length)">+ 增加一张券</a-button>
                  <a-button type="link" v-if="idx < item.couponIds.length - 1 || idx > 0" @click="thisMethods.removeCouponr(index, idx)">- 删除</a-button>
                </div>
                <a-form-item :colon="false" label=" " v-if="item.couponIds.length-1 === idx">
                  <div class="global-tip">
                    可同时奖励多张券，奖励的券将直接进入券包，无需领取，最多添加5张
                  </div>
                </a-form-item>
              </a-form-item>
            </template>
          </template>
        </template>
      </template>
      <template v-else>
        <a-form-item label="奖励内容" :rules="{ required: true, message: '请选择奖励内容' }">
          <div class="global-tip">填写任务总次数后，即可配置每次的奖励内容</div>
        </a-form-item>
      </template>
    </template>
  </div>
</template>
<script setup>
import { rewardTypeOptions } from '@/utils/dict-options'
const emits = defineEmits(['addCouponr', 'removeCouponr',])
const props = defineProps({
  formType: { // 0-新增 1-编辑 2-查看
    type: Number,
    default: 0
  },
  conditionsRuleNamePrefix: {
    type: Array,
    default: () => ([])
  },
  formFields: {
    type: Object,
    default: () => ({})
  },
  formRules: {
    type: Object,
    default: () => ({})
  },
  formRef: {
    type: Object,
    default: () => ({})
  },

})


const thisMethods = {
  setFormFields() {
    const formFields = props.formFields

    formFields.taskRewardList = formFields.taskRewardList || [
      {
        couponIds: [''],
        rewardType: 1,
        sortNum: 1,
        pointsNum: null
      }
    ]



  },
  setFormRules() {
    const formRules = props.formRules
  },
  addCouponr(index1, index2) {
    emits('addCouponr', index1, index2)
  },
  removeCouponr(index1, index2) {
    console.log(index1, index2, 'valuevaluevaluevalue');
    emits('removeCouponr', index1, index2)
  },

}

thisMethods.setFormFields()
// thisMethods.setFormRules()

  </script>