// 抽奖
exports.lotteryDraw = data => wx.$request({
  url: '/app/sales/lottery',
  data,
  method: 'post'
})

//任务活动抽奖
exports.lotteryTaskInfo = data => wx.$request({
  url: '/app/sales/lottery/task/info',
  data,
  method: 'get'
})

// 获取用户抽奖记录
exports.lotteryUserGet_prizes = data => wx.$request({
  url: '/app/sales/lottery/user/get_prizes',
  data,
  method: 'get'
})

// 获取抽奖活动信息列表
exports.lotteryList = data => wx.$request({
  url: '/app/sales/lottery/list',
  data,
  method: 'get'
})



// 获取抽奖活动信息
exports.lotteryInfo = data => wx.$request({
  url: '/app/sales/lottery/info',
  data,
  method: 'get'
})
//   初始化抽奖次
exports.initLotteryNum = data => wx.$request({
  url: '/app/sales/lottery/init_lottery_num',
  data,
  method: 'post'
})

//   积分兑换抽奖次
exports.lotteryPointRedeem = data => wx.$request({
  url: '/app/sales/lottery/point_redeem',
  data,
  method: 'post'
})

//   线上打卡
exports.lotteryClockIn = data => wx.$request({
  url: '/app/sales/lottery/clock_in',
  data,
  method: 'post'
})

// 分享任务接口
exports.shareTaskPage = data => wx.$request({
  url: '/app/sales/interaction/task/share/page',
  data,
  method: 'get'
})


// 预约活动列表
exports.membersDayBookingslot_config = data => wx.$request({
  url: '/app/user/membersDayBooking/slot_config',
  data,
  method: 'get'
})


// 预约活动列表提交 /app/membersDayBooking/book
exports.membersDayBookingsbook = data => wx.$request({
  url: '/app/user/membersDayBooking/book',
  data,
  method: 'post'
})

// 获取 活动详情

exports.membersDayBookingsdetails = data => wx.$request({
  url: '/app/user/membersDayBooking/details',
  data,
  method: 'get'
})
// /app/membersDayBooking/isFull  判断当前活动是否有库存

exports.membersDayBookingsisFull = data => wx.$request({
  url: '/app/user/membersDayBooking/isFull',
  data,
  method: 'get'
})