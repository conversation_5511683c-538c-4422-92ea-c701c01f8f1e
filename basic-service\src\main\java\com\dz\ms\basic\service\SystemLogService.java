package com.dz.ms.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.core.dto.basic.SystemLogDTO;
import com.dz.ms.basic.dto.AlarmConfigDTO;
import com.dz.ms.basic.entity.SystemLog;

import java.util.List;

/**
 * 系统日志接口
 * @author: Handy
 * @date:   2022/08/04 20:22
 */
public interface SystemLogService extends IService<SystemLog> {

    /**
     * 保存系统日志
     * @param param
     */
    void addSystemLog(SystemLogDTO param);

    /**
     * 获取告警配置列表
     * @return
     */
    List<AlarmConfigDTO> getAlarmConfigList();

    /**
     * 保存告警配置
     * @return
     */
    public Long saveAlarmConfig(AlarmConfigDTO param);

    /**
     * 删除告警配置
     * @return
     */
    public void deleteAlarmConfig(Long id);

}
