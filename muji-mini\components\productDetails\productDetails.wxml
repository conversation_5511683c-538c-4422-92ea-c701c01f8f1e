<view class="product-list">
  <view class="product-item {{item.position === 'IntheMiddle'?'center':''}}" wx:for="{{listData}}" wx:key="index">
    <view class="product-item-title {{item.position === 'IntheMiddle'?'center':''}}">
      <image class="product-item-img" wx:if="{{item.icon}}" src="{{$cdn}}/product/{{item.icon}}" alt=""/>
      {{item.label}}
    </view>
    <view class="product-item-content">
      <view wx:if="{{!item.isImage}}" class="product-item-content-title">
        <mp-html class="{{item.className}}" content="{{item.content}}" />
      </view>
      <block wx:else>
        <view class="product-item-content-img" wx:for="{{item.content}}" wx:for-item="subItem" wx:key="index">
          <image src="{{subItem}}" mode="widthFix" />
        </view>
      </block>
    </view>
  </view>
</view>
