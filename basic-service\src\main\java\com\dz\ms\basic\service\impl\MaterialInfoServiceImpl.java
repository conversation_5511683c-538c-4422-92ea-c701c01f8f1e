package com.dz.ms.basic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dz.common.base.dto.BaseDTO;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.annotation.Cacheable;
import com.dz.common.core.dto.basic.MaterialRelationParamDTO;
import com.dz.common.core.dto.user.CurrentUserDTO;
import com.dz.common.core.enums.MaterialRelationEnum;
import com.dz.common.core.service.RedisService;
import com.dz.common.core.threadlocal.SecurityContext;
import com.dz.common.core.utils.BeanCopierUtils;
import com.dz.ms.basic.constants.CacheKeys;
import com.dz.ms.basic.dto.MaterialInfoDTO;
import com.dz.ms.basic.dto.MaterialSimpleDTO;
import com.dz.ms.basic.entity.MaterialInfo;
import com.dz.ms.basic.entity.MaterialRelation;
import com.dz.ms.basic.mapper.MaterialInfoMapper;
import com.dz.ms.basic.mapper.MaterialRelationMapper;
import com.dz.ms.basic.service.MaterialInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 素材信息
 * @author: Handy
 * @date:   2022/2/4 17:39
 */
@Service
@Slf4j
public class MaterialInfoServiceImpl extends ServiceImpl<MaterialInfoMapper,MaterialInfo> implements MaterialInfoService {

    @Resource
    private MaterialInfoMapper materialInfoMapper;
    @Resource
    private MaterialRelationMapper materialRelationMapper;
    @Resource
    private RedisService redisService;

    /**
     * 根据ID列表删除素材信息
     * @param ids
     */
    @Override
    public void deleteMaterialByIds(List<Long> ids) {
        materialInfoMapper.deleteBatchIds(ids);
    }

    /**
     * 根据ID列表更新素材分类
     * @param param
     */
    @Override
    public void updateMaterialGroupByIds(MaterialInfoDTO param) {
        LambdaQueryWrapper<MaterialInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MaterialInfo :: getId,param.getIds());
        MaterialInfo materialInfo = new MaterialInfo();
        materialInfo.setGroupId(param.getGroupId());
        materialInfoMapper.update(materialInfo,wrapper);
    }

    /**
     * 根据ID列表更新素材过期时间
     * @param param
     */
    @Override
    public void updateMaterialTimeByIds(MaterialInfoDTO param) {
        LambdaQueryWrapper<MaterialInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MaterialInfo :: getId,param.getIds());
        MaterialInfo materialInfo = new MaterialInfo();
        if(null != param.getEffectiveTime()) {
            materialInfo.setEffectiveTime(param.getEffectiveTime());
        }
        materialInfo.setExpireTime(param.getExpireTime());
        materialInfoMapper.update(materialInfo,wrapper);
    }

    /**
     * 根据素材id列表获取素材地址列表
     * @param ids
     * @return
     */
    @Cacheable(prefix = CacheKeys.MATERIAL_LIST_IDS,key = "'#tenantId'+'#ids'")
    @Override
    public List<MaterialSimpleDTO> getMaterialUrlByIds(List<Long> ids,Long tenantId) {
        return materialInfoMapper.getMaterialUrlByIds(ids);
    }

    /**
     * 保存素材关联业务信息
     * @param param
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMaterialRelation(MaterialRelationParamDTO param) {
        LambdaQueryWrapper<MaterialRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MaterialRelation::getRelationId,param.getRelationId());
        wrapper.eq(MaterialRelation::getRelationType,param.getRelationEnum().name());
        materialRelationMapper.delete(wrapper);
        List<MaterialRelation> list = new ArrayList<>();
        List<Long> idList = param.getIdList();
        Set<Long> idSet = null;
        if(!CollectionUtils.isEmpty(idList)) {
            idSet = new HashSet<>(idList);
        }
        else if(StringUtils.isNotBlank(param.getIdStr())) {
            String[] strs = param.getIdStr().split(",");
            idSet = new HashSet<>();
            for (int i = 0; i < strs.length; i++) {
                long id = NumberUtils.toLong(strs[i]);
                if(id > 0)  {
                    idSet.add(id);
                }
            }
        }
        else {
            return;
        }
        Long tenantId = SecurityContext.getUser().getTenantId();
        for (Long id : idSet) {
            list.add(new MaterialRelation(id,param.getRelationEnum().name(),param.getRelationId(),tenantId));
        }
        materialRelationMapper.insertBatch(list);
    }

    /**
     * 批量保存素材关联业务信息
     * @param list
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchMaterialRelation(List<MaterialRelationParamDTO> list) {
        for (MaterialRelationParamDTO param : list) {
            saveMaterialRelation(param);
        }
    }

    /**
     * 分页查询即将过期和已过期素材信息
     * @param param
     * @return
     */
    @Override
    public PageInfo<MaterialInfoDTO> getMaterialExpireList(BaseDTO param) {
        LambdaQueryWrapper<MaterialInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.isNull(MaterialInfo::getReplaceUrl);
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 3);
        wrapper.le(MaterialInfo::getExpireTime,calendar.getTime());
        wrapper.orderByDesc(MaterialInfo::getExpireTime);
        IPage<MaterialInfo> page = materialInfoMapper.selectPage(new Page<>(param.getPageNum(), param.getPageSize()), wrapper);
        if(CollectionUtils.isEmpty(page.getRecords())) {
            return new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(), null);
        }
        List<Long> ids = page.getRecords().stream().map(MaterialInfo::getId).collect(Collectors.toList());
        LambdaQueryWrapper<MaterialRelation> relationWrapper = new LambdaQueryWrapper<>();
        relationWrapper.in(MaterialRelation::getMaterialId,ids);
        List<MaterialRelation> relationList = materialRelationMapper.selectList(relationWrapper);
        Map<Long,Set<String>> map = new HashMap<>();
        if(!CollectionUtils.isEmpty(relationList)) {
            for (MaterialRelation relation : relationList) {
                String name = MaterialRelationEnum.getNameByType(relation.getRelationType());
                if(null == name) {
                    continue;
                }
                Set<String> relations = map.get(relation.getMaterialId());
                if(null == relations) {
                    relations = new HashSet<>();
                }
                relations.add(name);
                map.put(relation.getMaterialId(),relations);
            }
        }
        Date date = new Date();
        List<MaterialInfoDTO> list = new ArrayList<>();
        for (MaterialInfo materialInfo : page.getRecords()) {
            MaterialInfoDTO material = BeanCopierUtils.convertObject(materialInfo,MaterialInfoDTO.class);
            long expireTime = material.getExpireTime().getTime();
            long curTime = date.getTime();
            long differ = Math.abs((expireTime-curTime));
            differ = differ/1000;
            int differMin = (int) (differ/60);
            int differHour = differMin/60;
            int differDay = differHour/24;
            String timeStr = differ < 60 ? differ+"秒" :
                    differMin < 60 ? differMin+"分钟" :
                    differHour < 24 ? differHour+"小时" : differDay+"天";
            if(material.getExpireTime().after(date)) {
                material.setValidity("还剩"+timeStr);
            }
            else {
                material.setValidity("已过期"+timeStr);
            }
            material.setUseScene(map.get(material.getId()));
            list.add(material);
        }
        return new PageInfo<>(page.getCurrent(),page.getSize(),page.getTotal(), list);
    }

    /**
     * 生效达到生效日期的替换素材
     */
    @Override
    public void effectiveReplaceUrl(Long tenantId) {
        SecurityContext.setUser(new CurrentUserDTO(tenantId));
        int num = materialInfoMapper.effectiveReplaceUrl(new Date());
        log.info("生效达到生效日期的替换素材数量:{}",num);
        if(num > 0) {
            redisService.delAll(CacheKeys.MATERIAL_LIST_IDS+":"+tenantId);
        }

    }

    /**
     * 批量新增素材信息
     * @param list
     */
    @Override
    public void addBatch(List<MaterialInfoDTO> list) {
        List<MaterialInfo> materialInfoList = new ArrayList<>();
        Date date = new Date();
        CurrentUserDTO user = SecurityContext.getUser();
        for (MaterialInfoDTO param : list) {
            MaterialInfo materialInfo = new MaterialInfo(null, param.getMaterialType(), param.getGroupId(), param.getMaterialName(),param.getMaterialUrl(), param.getReplaceUrl(), param.getVideoPoster(),param.getEffectiveTime(),param.getExpireTime());
            if(null == materialInfo.getGroupId()) {
                materialInfo.setGroupId(0L);
            }
            materialInfo.setTenantId(user.getTenantId());
            materialInfo.setCreated(date);
            materialInfo.setCreator(user.getUid());
            materialInfo.setModifier(user.getUid());
            materialInfo.setModified(date);
            materialInfoList.add(materialInfo);
        }
        materialInfoMapper.insertBatch(materialInfoList);
    }

    /**
     * 批量更新素材信息
     * @param list
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBatch(List<MaterialInfoDTO> list) {
        for (MaterialInfoDTO param : list) {
            MaterialInfo materialInfo = new MaterialInfo(param.getId(), null, param.getGroupId(), param.getMaterialName(),null, param.getReplaceUrl(), param.getVideoPoster(),param.getEffectiveTime(),param.getExpireTime());
            materialInfoMapper.updateById(materialInfo);
        }
    }
}
