import { apiNewBooking2 } from '../../../api/index'
import { onInput } from '../../../utils/form'

const app = getApp()
const getDataDefault = () => {
  return {
    visibleRules: false,
    loading: false,
    apiLoading: true,
    formFields: {
      name: '',
      phone: '',
      idCard: ''
    },
    resData: {}
  }
}

Page({
  data: getDataDefault(),

  onLoad () {
    this.init()
  },
  async init () {
    this.setData(getDataDefault())
    const res = await apiNewBooking2.getWhitelist()
    if (!res.data.whiteList.includes(app.globalData.userInfo.cardNo)) {
      return wx.$mp.switchTab({ url: '/pages/index/index' })
    }
    this.getBookingInfo()
  },
  async getBookingInfo () {
    this.setData({ loading: true })
    const res = await apiNewBooking2.getBookingInfo().finally(() => {
      this.setData({ loading: false })
      this.setData({ apiLoading: false })
    })
    const resData = res.data
    const formFields = Object.assign(this.data.formFields, resData)
    this.setData({ resData, formFields })
    if (!resData.id) this.setData({ visibleRules: true })
  },
  onInput,
  async submit () {
    console.log('this.data.formFields：', this.data.formFields)
    if (!app.ifRegister()) return

    const { name, phone, idCard } = this.data.formFields

    if (!name) {
      return wx.$mp.showToast({ title: '请输入姓名' })
    } else if (name.length > 10) {
      return wx.$mp.showToast({ title: '姓名最多十个字' })
    } else if (!phone) {
      return wx.$mp.showToast({ title: '请输入手机号' })
    } else if (!wx.$mp.validate.isPhoneNumber(phone)) {
      return wx.$mp.showToast({ title: '请输入正确的手机号' })
    } else if (!idCard) {
      return wx.$mp.showToast({ title: '请输入身份证号码' })
    } else if (idCard.length !== 18) {
      return wx.$mp.showToast({ title: '身份证号码长度应为18位' })
    }

    this.setData({ loading: true })
    await apiNewBooking2.setBookingInfo(this.data.formFields).finally(() => {
      this.setData({ loading: false })
    })
    wx.$mp.showToast({ title: '提交成功' })
    this.getBookingInfo()
  },
  showRules () {
    this.setData({ visibleRules: true })
  },
  closeRules () {
    this.setData({ visibleRules: false })
  }
})
