package com.dz.ms.order.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.dz.common.core.annotation.Columns;
import com.dz.common.core.annotation.Table;
import com.dz.common.core.enums.ColumnType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单主表
 *
 * @author: LiinNs
 * @date: 2024/12/09 10:38
 */
@Getter
@Setter
@NoArgsConstructor
@Table("订单主表")
@TableName(value = "exchange_order")
public class ExchangeOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;
    @Columns(type = ColumnType.VARCHAR, length = 36, isNull = false, comment = "订单编号")
    private String orderCode;

    @Columns(type = ColumnType.VARCHAR, length = 36, isNull = true, comment = "积分流水编号")
    private String outSn;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, comment = "订单类型 1普通订单")
    private Integer orderType;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "货架ID")
    private Long shelfId;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, comment = "商品总金额")
    private Integer productPoint;
    @Columns(type = ColumnType.DECIMAL, length = 19, scale = 2, isNull = false, comment = "商品金额")
    private BigDecimal productAmount;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, comment = "订单总积分")
    private Integer orderPoint;
    @Columns(type = ColumnType.DECIMAL, length = 19, scale = 2, isNull = false, comment = "订单总金额")
    private BigDecimal orderAmount;
    @Columns(type = ColumnType.INT, length = 10, isNull = false, comment = "商品件数")
    private Integer number;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "用户id")
    private Long userId;
    @Columns(type = ColumnType.VARCHAR, length = 20, isNull = true, comment = "用户手机号")
    private String userPhone;
    @Columns(type = ColumnType.VARCHAR, length = 64, isNull = true, comment = "用户CRM编码")
    private String userCrmCode;
    @Columns(type = ColumnType.VARCHAR, length = 20, isNull = true, comment = "会员卡等级")
    private String userCardLevel;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "收货人id")
    private Long receiverId;
    @Columns(type = ColumnType.VARCHAR, length = 20, isNull = true, comment = "收货人姓名")
    private String receiverName;
    @Columns(type = ColumnType.VARCHAR, length = 128, isNull = true, comment = "收货人手机号")
    private String receiverPhone;
    @Columns(type = ColumnType.VARCHAR, length = 2048, isNull = true, comment = "收货人地址")
    private String receiverAddress;
    @Columns(type = ColumnType.VARCHAR, length = 20, isNull = true, comment = "收货人邮编")
    private String receiverPostcode;
    @Columns(type = ColumnType.DECIMAL, length = 19, scale = 2, isNull = true, comment = "配送费")
    private BigDecimal expressAmount;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, comment = "订单状态 0已完成 1待支付 2待发货 3已发货 4待兑换 5部分兑换 6已兑换 7已取消 8已过期")
    private Integer orderStatus;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = true, comment = "支付状态 1未支付 2已支付 3已退款 4部分支付")
    private Integer payStatus;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = true, comment = "支付方式 1微信")
    private Integer payType;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "支付时间")
    private Date payTime;
    @Columns(type = ColumnType.VARCHAR, length = 36, isNull = true, comment = "支付编号")
    private String payCode;
    @Columns(type = ColumnType.VARCHAR, length = 36, isNull = true, comment = "第三方订单号")
    private String tradeId;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = true, comment = "发货状态 0待发货 1已发货 2部分发货")
    private Integer expressStatus;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = true, comment = "物流ID")
    private Long expressId;
    @Columns(type = ColumnType.VARCHAR, length = 36, isNull = true, comment = "物流code")
    private String expressCode;
    @Columns(type = ColumnType.VARCHAR, length = 100, isNull = false, comment = "微信form id")
    private String formId;
    @Columns(type = ColumnType.VARCHAR, length = 300, isNull = true, comment = "订单备注")
    private String remark;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = true, comment = "完成时间")
    private Date finishTime;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, comment = "同步状态 0未同步 1已同步")
    private Integer syncStatus;
    @Columns(type = ColumnType.TINYINT, length = 3, isNull = false, comment = "是否删除 0未删除 1已删除")
    @TableLogic
    private Integer isDeleted;
    @Columns(type = ColumnType.BIGINT, length = 19, isNull = false, comment = "租户ID")
    private Long tenantId;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private Date created;
    @Columns(type = ColumnType.DATETIME, length = 0, isNull = false, comment = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date modified;

    public ExchangeOrder(Long id, String orderCode, Integer orderType, Long shelfId, Integer productPoint, BigDecimal productAmount, Integer orderPoint, BigDecimal orderAmount, Integer number, Long userId, String userPhone, String userCrmCode, String userCardLevel, Long receiverId, String receiverName, String receiverPhone, String receiverAddress, String receiverPostcode, BigDecimal expressAmount, Integer orderStatus, Integer payStatus, Integer payType, Date payTime, String payCode, String tradeId, Integer expressStatus, Long expressId, String expressCode, String formId, String remark, Date finishTime, Integer syncStatus) {
        this.id = id;
        this.orderCode = orderCode;
        this.orderType = orderType;
        this.shelfId = shelfId;
        this.productPoint = productPoint;
        this.productAmount = productAmount;
        this.orderPoint = orderPoint;
        this.orderAmount = orderAmount;
        this.number = number;
        this.userId = userId;
        this.userPhone = userPhone;
        this.userCrmCode = userCrmCode;
        this.userCardLevel = userCardLevel;
        this.receiverId = receiverId;
        this.receiverName = receiverName;
        this.receiverPhone = receiverPhone;
        this.receiverAddress = receiverAddress;
        this.receiverPostcode = receiverPostcode;
        this.expressAmount = expressAmount;
        this.orderStatus = orderStatus;
        this.payStatus = payStatus;
        this.payType = payType;
        this.payTime = payTime;
        this.payCode = payCode;
        this.tradeId = tradeId;
        this.expressStatus = expressStatus;
        this.expressId = expressId;
        this.expressCode = expressCode;
        this.formId = formId;
        this.remark = remark;
        this.finishTime = finishTime;
        this.syncStatus = syncStatus;
    }

}
