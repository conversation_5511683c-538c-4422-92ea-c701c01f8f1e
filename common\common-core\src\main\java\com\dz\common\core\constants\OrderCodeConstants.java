package com.dz.common.core.constants;

/**
 * <AUTHOR>
 * @Description:order系统编码常量
 * @date 2018年10月24日
 */
public class OrderCodeConstants {

	/**
	 * <AUTHOR>
	 * @Description:订单流水status常量(成功SUCCESS,处理中WAITING)
	 * @date 2018年10月24日
	 */
	public abstract class AbstractOrderExpressStatus {
		/**
		 * 2部分发货
		 */
		public static final int PART = 2;// 2部分发货
		/**
		 * 1已发货
		 */
		public static final int SUCCESS = 1;//1已发货
		/**
		 * 0待发货
		 */
		public static final int WAITING = 0;//0待发货
	}

	/**
	 * 订单type常量 (普通NORMAL)
	 */
	public abstract class AbstractOrderType {
		/**
		 * 1普通
		 */
		public static final int NORMAL = 1;//普通
	}

	/**
	 * <AUTHOR>
	 * @Description:订单status常量(待付款PAYING,待发货SENDOUTING,已发货SENDOUTED,已完成COMPLETE,已取消CANCEL,已关闭CLOSE)
	 * @date 2018年10月24日
	 */
	public abstract class AbstractOrderStatus {
		/**
		 * 0已完成
		 */
		public static final int FINISH = 0;
		/**
		 * 1待支付
		 */
		public static final int PAYING = 1;//待支付
		/**
		 * 2/待发货
		 */
		public static final int SENDOUTING = 2;//待发货
		/**
		 * 3已发货
		 */
		public static final int SENDOUTED = 3;//已发货
		/**
		 * 4待兑换
		 */
		public static final int PENDING = 4;//4待兑换
		/**
		 * 5部分兑换
		 */
		public static final int PART = 5;//5部分兑换
		/**
		 * 6已兑换
		 */
		public static final int SUCCESS = 6;//6已兑换
		/**
		 * 7已取消
		 */
		public static final int CANCEL = 7;//已取消
		/**
		 * 8已过期
		 */
		public static final int EXPIRED = 8;

	}

	/**
	 * <AUTHOR>
	 * @Description:订单pay_status常量(未支付NOT_PAYING,已支付PAID)
	 * @date 2018年10月24日
	 */
	public abstract class AbstractOrderPayStatus {
		/**
		 * 1未支付
		 */
		public static final int NOT_PAYING = 1;//未支付
		/**
		 * 2已支付
		 */
		public static final int PAID = 2;//已支付
		/**
		 * 3已退款
		 */
		public static final int RETURNED_MONEY = 3;//已退款
		/**
		 * 4部分支付
		 */
		public static final int PART_PAYMENT = 4;//部分支付
	}

	/**
	 * 库存扣除状态(
	 */
	public abstract class AbstractInventoryStatus {

		/**
		 * 1成功
		 */
		public static final int SUCCCESS = 1;//成功
		/**
		 * 2失败
		 */
		public static final int FAIL = 2;//失败

	}

	public abstract class AbstractOrderDetailStatus {
		/**
		 * 1待兑换
		 */
		public static final int NORMAL = 1;//1待兑换
		/**
		 * 2换货
		 */
		public static final int SUCCESS = 2;//2已兑换
		/**
		 * 3已过期
		 */
		public static final int EXPIRED = 3;//3已过期

	}

	/**
	 * <AUTHOR>
	 * @Description:订单是否删除常量(可用NORMAL,删除DELETE)
	 * @date 2018年10月24日
	 */
	public abstract class AbstractOrderIsDelete {
		/**
		 * 0正常
		 */
		public static final int NORMAL = 0;//正常
		/**
		 * 1删除
		 */
		public static final int DELETE = 1;//删除
	}

	/**
	 * <AUTHOR>
	 * @Description:同步状态
	 * @date 2020年03月25日
	 */
	public abstract class SyncStatus {
		/**
		 * 1已同步
		 * 0-未同步
		 */
		public static final int ALREADY = 1;
		public static final int UNREADY = 0;
	}

}
