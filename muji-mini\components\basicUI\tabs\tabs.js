const app = getApp()
Component({
  externalClasses: ['tab-parent-class'],
  properties: {
    // 默认显示tab下标
    active: {
      type: Number,
      value: 0,
      observer(val) {
        this.setData({
          value: this.data.active,
        })
      }
    },
    tabs: Array,
    tabsType: {
      type: Boolean,
      value: false
    },
    indexMer: {
      type: Number || String,
      value: 0
    },
    labelKey: {
      type: String,
      value: 'label'
    },
    valueKey: {
      type: String,
      value: 'value'
    },
    lineType:{
      type: Boolean,
      value: true
    },
    buttom:{
      type: Number ,
      value: 40
    },
    Fbuttom:{
      type: Number ,
      value: 40
    },
    fontSize:{
      type: Number ,
      value: 24
    },
    lineHeight:{
      type: Number ,
      value: 34
    },
    fontWeight:{
      type: Number ,
      value: 400
    },
    AfontWeight:{
      type: Number ,
      value: 600
    }
  },
  data: {
    value: 0,
  },
  lifetimes: {
    attached() {
      // 设置初始值

    },
  },
  methods: {
    changeActive(e) {
      let {
        value,
        index
      } = e.currentTarget.dataset;
      if (index === this.data.value) {
        return
      }
      console.log(index);
      this.setData({
        value: index
      })
      this.triggerEvent('change', {
        index,value
      })
    },
  }
})
