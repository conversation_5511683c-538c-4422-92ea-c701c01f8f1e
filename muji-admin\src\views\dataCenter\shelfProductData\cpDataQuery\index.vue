<template>

  <layout>
    <template v-slot:header>
      <searchForm :formParams="formParams" @cancel="resetData" @ok="refreshData" :disabled="!$hasPermission('cpDataQuery:search')">
        <a-form-item name="orderCode">
          <a-input placeholder="请输入CP号" allow-clear v-model:value="formParams.cpCode" allowClear @keyup.enter="refreshData"></a-input>
        </a-form-item>

        <a-form-item name="createTime" label="销售时间">
          <a-range-picker v-model:value="formParams.createTime" :placeholder="['开始时间', '结束时间']" :presets="$rangePresets" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" />
        </a-form-item>

      </searchForm>
    </template>
    <template v-slot:topRight>
      <a-space>

        <a-button type="primary" :disabled="!$hasPermission('cpDataQuery:derive')" @click="excelOrder">批量导出</a-button>
      </a-space>

    </template>
    <template v-slot="{ height }">
      <a-table :indentSize="20" row-key="id" :scroll="{ scrollToFirstRowOnChange: true, y: height - 88, x: '100%' }" :dataSource="dataSource" :columns="tableHeader" :pagination="pagination" :loading="loading" @change="loadData">
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'index'">
            {{ (current - 1) * pageSize + 1 + index }}
          </template>

          <template v-if="column.key === 'action'">
            <!-- <a-button type="link" :disabled="!$hasPermission('giftOrder:goods')||record.expressStatus== 1" @click="EditRole(record)">发货</a-button> -->

          </template>

        </template>
      </a-table>
    </template>

  </layout>

</template>
<script setup>

import { useRoute, useRouter } from 'vue-router'
import { usePagination, useRequest, } from 'vue-request';
import { productCp_static, downloadTask } from '@/http/index.js'
import { message, Modal } from "ant-design-vue";

import { cloneDeep } from "lodash";
// 分页参数
let pagination = computed(() => {
  return {
    total: total.value,
    current: current.value,
    pageSize: pageSize.value,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`,
    position: ["bottomLeft"],
  };
});
// 表格分页数据获取
const total = ref(0)


const { formParams, tableHeader, visible, id, type, } = toRefs(reactive({


  formParams: {

    // cpCode: ''
  },

  tableHeader: [
    //   {
    //   title: '序号',
    //   key: 'index',
    //   align: 'center',
    //   width: 80
    // },
    {
      title: '商品在货架的ID',
      dataIndex: 'shelfProductId',
      width: 180,
      align: 'center',
    },
    {
      title: 'CP',
      dataIndex: 'cpCode',
      align: 'center',
      ellipsis: true,
      width: 280,
    },
    {
      title: '成本价',
      dataIndex: 'originPrice',
      align: 'center',
      ellipsis: true,
      width: 180,
      customRender: (row) => {
        // console.log(row);

        return row.text || '--'; // 如果数据为空，则显示 '----'
      }
    },
    {
      title: '吊牌价',
      dataIndex: 'prePrice',
      align: 'center',
      ellipsis: true,
      width: 180,
      customRender: (row) => {
        // console.log(row);

        return row.text || '--'; // 如果数据为空，则显示 '----'
      }
    },
    {
      title: '积分价',
      dataIndex: 'costPoint',
      align: 'center',
      ellipsis: true,
      width: 180,
      customRender: (row) => {
        // console.log(row);

        return row.text || '--'; // 如果数据为空，则显示 '----'
      }
    },
    {
      title: '上架库存',
      dataIndex: 'onInventoryAmount',
      align: 'center',
      width: 180,
      ellipsis: true,

    },
    {
      title: '兑换件数',
      dataIndex: 'sellAmount',
      align: 'center',
      width: 180,
      ellipsis: true
    },
    {
      title: '兑换人数',
      dataIndex: 'sellPeopleAmount',
      align: 'center',
      ellipsis: true,
      width: 180,
    },
    {
      title: '剩余库存量',
      dataIndex: 'restInventoryAmount',
      align: 'center',
      ellipsis: true,
      width: 180,
    },
    {
      title: '兑换的总积分',
      dataIndex: 'totalPointAmount',
      align: 'center',
      ellipsis: true,
      width: 180,
    },
    // {
    //   title: '操作',
    //   dataIndex: 'action',
    //   key: 'action',
    //   align: 'center',
    //   width: 150,
    //   fixed: 'right'
    // }
  ]
})
);
const getParams = () => {
  // 拷贝一份 然后处理数据
  let params = cloneDeep(formParams.value);
  params.sellDateStart = params.createTime
    ? params.createTime[0] + " 00:00:00"
    : null;
  params.sellDateEnd = params.createTime
    ? params.createTime[1] + " 23:59:59"
    : null;
  delete params.createTime;
  return params;
};
const { data: dataSource, run, loading, pageSize, current, refresh } = usePagination((param) => {
  // 这里修改参数
  return productCp_static({ ...param, ...getParams() })
},
  {
    manual: true, // 修改为false,让其自动执行
    pagination: {
      currentKey: 'pageNum',  // 通过该值指定接口 当前页数 参数的属性值
      pageSizeKey: 'pageSize',// 通过该值指定接口 每页获取条数 参数的属性值
      totalKey: 'data.data.count',  // 指定 data 中 total 属性的路径
    },
    formatResult: res => { // 返回数据格式化
      total.value = res.data.count
      return res.data.list
    }
  });




// 加载数据
const loadData = (pag) => {
  run({ pageNum: pag?.current, pageSize: pag?.pageSize })
}
function excelOrder() {
  if (formParams.value.cpCode || formParams.value.createTime) {
    downloadTask({ type: 7, fileName: '兑礼CP数据列表', param: { ...getParams() } })
  } else {
    message.info('请输入搜索条件')
  }
}

// 刷新数据
const refreshData = () => {
  if (formParams.value.cpCode || formParams.value.createTime) {

    run({ pageNum: 1, pageSize: pageSize.value })
  } else {
    message.info('请输入搜索条件')
  }

};

// 重置数据
const resetData = () => {
  formParams.value = {
    cpCode: '',

  }
  if (formParams.value.cpCode || formParams.value.createTime) {

    refreshData()
  }
}



</script>
