package com.dz.ms.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dz.common.base.vo.PageInfo;
import com.dz.common.core.dto.IdCodeDTO;
import com.dz.ms.order.dto.OrderDetailDTO;
import com.dz.ms.order.entity.OrderDetail;

import java.io.IOException;

/**
 * 订单详情信息接口
 *
 * @author: LiinNs
 * @date: 2024/12/09 10:38
 */
public interface OrderDetailService extends IService<OrderDetail> {

    /**
     * 分页查询订单详情信息
     *
     * @param param
     * @return PageInfo<OrderDetailDTO>
     */
    public PageInfo<OrderDetailDTO> getOrderDetailList(OrderDetailDTO param);

    /**
     * 根据ID查询订单详情信息
     *
     * @param id
     * @return OrderDetailDTO
     */
    public OrderDetailDTO getOrderDetailById(Long id);

    /**
     * 保存订单详情信息
     *
     * @param param
     * @return Long
     */
    public Long saveOrderDetail(OrderDetailDTO param);

    /**
     * 根据ID删除订单详情信息
     *
     * @param param
     */
    public void deleteOrderDetailById(IdCodeDTO param);

    void getSftpFile() throws IOException;

    void removeOrder();
}
