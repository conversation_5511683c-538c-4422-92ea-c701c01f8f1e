package com.dz.common.core.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SwaggerConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Swagger API")
                        .description("swagger documentation")
                        .version("2.0")
                        .contact(new Contact().name("Handy").email("")));
    }

    /**
     * 后台管理系统 API
     */
    @Bean
    public GroupedOpenApi crmApi() {
        return GroupedOpenApi.builder()
                .group("crm")
                .pathsToMatch("/crm/**")
                .build();
    }

    /**
     * APP 前端 API
     */
    @Bean
    public GroupedOpenApi appApi() {
        return GroupedOpenApi.builder()
                .group("app")
                .pathsToMatch("/app/**")
                .build();
    }

    /**
     * 第三方 OpenAPI
     */
    @Bean
    public GroupedOpenApi openApi() {
        return GroupedOpenApi.builder()
                .group("openapi")
                .pathsToMatch("/openapi/**")
                .build();
    }

    /**
     * OMS SAAS 后台 OpenAPI
     */
    @Bean
    public GroupedOpenApi omsApi() {
        return GroupedOpenApi.builder()
                .group("oms")
                .pathsToMatch("/oms/**")
                .build();
    }
}
